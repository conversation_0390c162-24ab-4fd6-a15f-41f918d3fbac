package com.vedeng.erp.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.eventbus.EventBus;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.BizLogEnum;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.crm.admin.api.dto.tuoke.BusinessCluesDetailDTO;
import com.vedeng.erp.business.common.constant.BusinessChanceConstant;
import com.vedeng.erp.business.common.enums.*;
import com.vedeng.erp.business.domain.dto.*;
import com.vedeng.erp.business.domain.entity.BusinessLeadsEntity;
import com.vedeng.erp.business.domain.entity.BussinessChanceEntity;
import com.vedeng.erp.business.dto.BusinessCloseDto;
import com.vedeng.erp.business.dto.CloseAuditDto;
import com.vedeng.erp.business.feign.CommunicateRecordApiService;
import com.vedeng.erp.business.feign.CrmTraderApiService;
import com.vedeng.erp.business.mapper.BusinessChanceSeekHelpMapper;
import com.vedeng.erp.business.mapper.BusinessLeadsMapper;
import com.vedeng.erp.business.mapper.BussinessChanceMapper;
import com.vedeng.erp.business.mapstruct.BusinessChanceConvertor;
import com.vedeng.erp.business.mapstruct.BusinessChanceGoodsDtoToQuoteorderGoodsDtoConvertor;
import com.vedeng.erp.business.mapstruct.BusinessLeadsConvertor;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.system.common.enums.CustomDataLogSaveTypeEnums;
import com.vedeng.erp.system.common.enums.CustomDataOperBizTypeEnums;
import com.vedeng.erp.system.common.enums.CustomDataOperTypeEnums;
import com.vedeng.erp.system.dto.*;
import com.vedeng.erp.system.service.*;
import com.vedeng.erp.trader.common.enums.CommunicateRecordTypeEnum;
import com.vedeng.erp.trader.domain.dto.QuoteorderDto;
import com.vedeng.erp.trader.domain.dto.QuoteorderGoodsDto;
import com.vedeng.erp.trader.domain.entity.QuoteorderEntity;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.RCommunicateTodoJAiDto;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.mapper.QuoteorderMapper;
import com.vedeng.erp.trader.mapper.RSalesJBusinessOrderMapper;
import com.vedeng.erp.trader.mapstruct.QuoteorderFromOtherDataConvertor;
import com.vedeng.erp.trader.mapstruct.RSalesJBusinessOrderConvertor;
import com.vedeng.erp.trader.service.PublicCustomerRecordApiService;
import com.vedeng.erp.trader.service.RCommunicateTodoJAiApiService;
import com.vedeng.erp.trader.service.TraderContactService;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.goods.dto.BusinessOrderCategoryDto;
import com.vedeng.goods.service.BusinessOrderCategoryApiService;
import com.vedeng.goods.service.GoodsApiService;
import com.vedeng.goods.vo.CoreSkuVo;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc;
import com.vedeng.infrastructure.tyc.mapper.TycMapper;
import com.vedeng.uac.api.dto.OrganizationResDTO;
import com.vedeng.uac.api.dto.UserInfoDto;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.vedeng.common.core.domain.CurrentUser.getCurrentUser;

/**
 * <AUTHOR>
 * @description 商机业务逻辑接口实现类
 * @date 2022/7/12 14:06
 **/
@Service
@Slf4j
public class BusinessChanceServiceImpl implements BusinessChanceService {

    @Autowired
    private GoodsApiService goodsApiService;

    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    @Autowired
    private BussinessChanceMapper bussinessChanceMapper;

    @Autowired
    private QuoteorderMapper quoteorderMapper;

    @Autowired
    private BusinessChanceConvertor businessChanceConvertor;

    @Autowired
    private BusinessChanceGoodsDtoToQuoteorderGoodsDtoConvertor businessChanceGoodsDtoToQuoteorderGoodsDtoConvertor;

    @Autowired
    private QuoteorderFromOtherDataConvertor quoteorderFromOtherDataConvertor;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private CustomDataLogApiService customDataLogApiService;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Autowired
    private RegionApiService regionApi;

    @Autowired
    private CommunicateRecordApiService communicateRecordApiService;

    @Autowired
    private BusinessLeadsService businessLeadsService;

    @Autowired
    private CustomTagApiService customTagApiService;

    @Resource
    private CrmTraderApiService crmTraderApiService;

    @Autowired
    private PublicCustomerRecordApiService publicCustomerRecordApiService;

    @Autowired
    private TraderContactService traderContactService;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private RSalesJBusinessOrderMapper rSalesJBusinessOrderMapper;

    @Autowired
    private RSalesJBusinessOrderConvertor  rSalesJBusinessOrderConvertor;

    @Autowired
    private BusinessChanceSeekHelpMapper businessChanceSeekHelpMapper;

    @Autowired
    private CustomDataOperApiService customDataOperApiService;

    @Autowired
    private RCommunicateTodoJAiApiService rCommunicateTodoJAiApiService;

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    @Autowired
    private BusinessLeadsConvertor businessLeadsConvertor;

    @Autowired
    private OrderTerminalApiService orderTerminalApiService;

    @Autowired
    private OperationLogApiService operationLogApiService;

    @Autowired
    private TycMapper tycMapper;
    @Autowired
    private BusinessOrderCategoryApiService businessOrderCategoryService;

    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;

    @Value("${crmApplicationMessageJumpUrl}")
    private String crmApplicationMessageJumpUrl;  //qa.lxcrm.vedeng.com


    @Value("${erpUrl}")
    private String erpUrl;

    private static final  String STATIC_JUMP_URL = "/crm/wx/transfer?targetUrl=";

    @Value("${lxcrmUrl:}")
    private String lxcrmUrl;

    @Value("${erp.domain}")
    private String erpDomain;

    @Value("${html2Pdf.domain}")
    private String html2PdfDomain;

    @Value("${accessAllBusinessChance:1997}")
    private String accessAllBusinessChance;

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;

    @Value("${crmJumpErpUrl}")
    private String crmJumpErpUrl;

    private static final String PRINT_CONTRACT_URL = "/order/quote/printOrder.do?chapter=a&print=a&quoteorderId=";
    private static final String PRINT_CONTRACT_URL_ERROR = "/order/quote/printOrder.do?chapter=a&quoteorderId=";

    private static final String RENDER_URL = "/api/render";

    private static final Set<Integer> targetScores = new HashSet<>(Arrays.asList(ErpConstant.ONE, ErpConstant.TWO));


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public BusinessChanceDto add(BusinessChanceDto businessChanceDto) {
        log.info("CRM商机，新增入参：{}", JSON.toJSONString(businessChanceDto));
        // 基础数据分装
        businessChanceOfSaleOrLeadsBaseInfoSet(businessChanceDto);
        // 赋值归属
        businessChanceSetUserId(businessChanceDto);
        // 客户信息
        confirmTraderData(businessChanceDto);
        // 计算等级
        calc(businessChanceDto);
        // 其他数据封装
        baseDataSet(businessChanceDto);
        // 进行中
        if (Objects.nonNull(businessChanceDto.getUserId()) && !ErpConstant.ZERO.equals(businessChanceDto.getUserId()) && Objects.nonNull(businessChanceDto.getCommunicateRecordDto())) {
            businessChanceDto.setStatus(ErpConstant.SIX);
        }

        // 转换
        BussinessChanceEntity bussinessChanceEntity = businessChanceConvertor.toEntity(businessChanceDto);
        bussinessChanceMapper.insertSelective(bussinessChanceEntity);


        if(StrUtil.isNotEmpty(businessChanceDto.getCategoryIds())){
            String[] categoryIds = businessChanceDto.getCategoryIds().split(",");
            for(String categoryId : categoryIds){
                BusinessOrderCategoryDto businessOrderCategoryDto = new BusinessOrderCategoryDto();
                businessOrderCategoryDto.setBusinessId(bussinessChanceEntity.getBussinessChanceId());
                businessOrderCategoryDto.setBusinessType(ErpConstant.ONE);
                businessOrderCategoryDto.setCategoryId(Integer.parseInt(categoryId));
                businessOrderCategoryDto.setKeywords(businessChanceDto.getKeywords());
                businessOrderCategoryService.save(businessOrderCategoryDto);
            }
        }

        // 解锁客户
        publicCustomerRecordApiService.unLockTrader(bussinessChanceEntity.getTraderId(), bussinessChanceEntity.getBussinessChanceId(), ErpConstant.ONE, getCurrentUser().getId());
        businessChanceDto.setBussinessChanceId(bussinessChanceEntity.getBussinessChanceId());
        // 商机编号
        String code = generateBusinessNo(businessChanceDto.getBussinessChanceId());
        BussinessChanceEntity toNo = new BussinessChanceEntity();
        toNo.setBussinessChanceId(businessChanceDto.getBussinessChanceId());
        toNo.setBussinessChanceNo(code);
        bussinessChanceMapper.updateByPrimaryKeySelective(toNo);
        businessChanceDto.setBussinessChanceNo(code);
        // 处理关联终端信息
        OrderTerminalDto orderTerminalDto = businessChanceDto.getOrderTerminalDto();
        if (Objects.nonNull(orderTerminalDto) && StrUtil.isNotBlank(orderTerminalDto.getTerminalName())) {
            saveTerminal(businessChanceDto);
        }else if(!StrUtil.isEmpty(businessChanceDto.getTerminalTraderName())){
            OrderTerminalDto orderTerminalDtoTemp = new OrderTerminalDto();
            orderTerminalDtoTemp.setTerminalName(businessChanceDto.getTerminalTraderName());
            orderTerminalDtoTemp.setTerminalTraderNature(businessChanceDto.getTerminalTraderNature());
            String terminalTraderRegion = businessChanceDto.getTerminalTraderRegion();
            //将terminalTraderRegion按逗号分割，分别赋值给provinceId,cityId,areaId，并考虑可能只有两个值或一个值的情况
            if (StrUtil.isNotBlank(terminalTraderRegion)) {
                String[] terminalTraderRegions = terminalTraderRegion.split(",");
                if (terminalTraderRegions.length == 3) {
                    orderTerminalDtoTemp.setProvinceId(Integer.parseInt(terminalTraderRegions[0]));
                    orderTerminalDtoTemp.setCityId(Integer.parseInt(terminalTraderRegions[1]));
                    orderTerminalDtoTemp.setAreaId(Integer.parseInt(terminalTraderRegions[2]));
                } else if (terminalTraderRegions.length == 2) {
                    orderTerminalDtoTemp.setProvinceId(Integer.parseInt(terminalTraderRegions[0]));
                    orderTerminalDtoTemp.setCityId(Integer.parseInt(terminalTraderRegions[1]));
                } else if (terminalTraderRegions.length == 1) {
                    orderTerminalDtoTemp.setProvinceId(Integer.parseInt(terminalTraderRegions[0]));
                }
            }
            //将terminalTraderRegionStr，按以上terminalTraderRegion一样的逻辑，按逗号分割，分别赋值给 provinceName,cityName,areaName，并考虑可能只有两个值或一个值的情况
            String terminalTraderRegionStr = businessChanceDto.getTerminalTraderRegionStr();
            if (StrUtil.isNotBlank(terminalTraderRegionStr)) {
                String[] terminalTraderRegionStrs = terminalTraderRegionStr.split(",");
                if (terminalTraderRegionStrs.length == 3) {
                    orderTerminalDtoTemp.setProvinceName(terminalTraderRegionStrs[0]);
                    orderTerminalDtoTemp.setCityName(terminalTraderRegionStrs[1]);
                    orderTerminalDtoTemp.setAreaName(terminalTraderRegionStrs[2]);
                } else if (terminalTraderRegionStrs.length == 2) {
                    orderTerminalDtoTemp.setProvinceName(terminalTraderRegionStrs[0]);
                    orderTerminalDtoTemp.setCityName(terminalTraderRegionStrs[1]);
                } else if (terminalTraderRegionStrs.length == 1) {
                    orderTerminalDtoTemp.setProvinceName(terminalTraderRegionStrs[0]);
                }
            }
            businessChanceDto.setOrderTerminalDto(orderTerminalDtoTemp);
            saveTerminal(businessChanceDto);
        }
        // 线索回写
        if (Objects.nonNull(businessChanceDto.getBusinessLeadsId())) {
            businessLeadsService.updateLeadsFollowStatusOpportunity(businessChanceDto.getBusinessLeadsId(), businessChanceDto.getBussinessChanceId());
        }
        // 通过ai通话助手产生的商机
        if (Objects.nonNull(businessChanceDto.getIsAiAssistant()) && businessChanceDto.getIsAiAssistant().equals(ErpConstant.T)) {
            RCommunicateTodoJAiDto dto = new RCommunicateTodoJAiDto();
            dto.setBusinessId(bussinessChanceEntity.getBussinessChanceId());
            dto.setBusinessNo(code);
            dto.setCommunicateRecordId(businessChanceDto.getAiCommunicateRecordId());
            rCommunicateTodoJAiApiService.createBusinessChange(dto);
        }
        if(StrUtil.isBlank(businessChanceDto.getTerminalTraderName()) &&
                (Objects.isNull(businessChanceDto.getTerminalTraderNature()) || businessChanceDto.getTerminalTraderNature().equals(ErpConstant.ZERO)
                )){
            updateStageAndStageTime(businessChanceDto.getBussinessChanceId(), BusinessChanceStageEnum.PRELIMINARY_NEGOTIATION);
        } else{
            // 更新商机阶段（商机验证）
            updateStageAndStageTime(businessChanceDto.getBussinessChanceId(), BusinessChanceStageEnum.OPPORTUNITY_VERIFICATION);
        }
        // VDERP-17057  【客户档案】ERP客户档案时间轴 线索转商机（新增商机）
        addChanceTrack(businessChanceDto, bussinessChanceEntity, code);
        
        // 计算线下销售和产线关系，发送消息提醒
        sendAutoShareNotifications(bussinessChanceEntity.getBussinessChanceId(), code);
        
        log.info("CRM商机，新增返参：{}", JSON.toJSONString(businessChanceDto));
        return businessChanceDto;
    }
    
    /**
     * 获取跳转链接
     * @param messageUrl 消息URL
     * @param jumpNameEnum 跳转名称枚举
     * @return 完整跳转URL
     */
    private String getJumpUrl(String messageUrl, JumpErpTitleEnum jumpNameEnum) {
        try {
            String url = crmApplicationMessageJumpUrl + STATIC_JUMP_URL +
                    URLEncoder.encode(erpUrl + "/index.do?target=" +
                            URLEncoder.encode(messageUrl, "UTF-8") +
                            "&ct=" + jumpNameEnum.getCode(), "UTF-8");
            log.info("getJumpUrl url:{}", url);
            return url;
        } catch (Exception e) {
            log.error("getjumpUrl error", e);
            return messageUrl; // 出错时返回原始URL
        }
    }
    
    /**
     * 发送自动共享关系的消息通知
     * @param businessChanceId 商机ID
     * @param businessChanceNo 商机编号
     */
    private void sendAutoShareNotifications(Integer businessChanceId, String businessChanceNo) {
        try {
            CurrentUser currentUser = getCurrentUser();
            if (currentUser == null) {
                log.warn("获取当前用户信息失败，无法发送协作人消息提醒");
                return;
            }
            
            // 商机详情页面URI
            String messageUrl = lxcrmUrl + "/crm/businessChance/profile/detail?id=" + businessChanceId;
            // 使用getjumpUrl生成完整跳转链接
            String url = getJumpUrl(messageUrl, JumpErpTitleEnum.BUSSINESS_CHANCE_DETAIL);
            
            // 查询线下销售和产线负责人
            List<RSalesJBusinessOrderDto> shareList = rSalesJBusinessOrderMapper.calculateShareTagsWithoutBusinessJOrderForChance(businessChanceId);
            if (CollUtil.isEmpty(shareList)) {
                log.info("商机{}没有自动计算的协作人关系", businessChanceNo);
                return;
            }
            
            for (RSalesJBusinessOrderDto share : shareList) {
                // 只处理自动共享关系
                if (share.getShareTag() == null || share.getShareTag() == 3) {
                    continue; // 标签3是手动添加，跳过
                }
                
                Integer userId = share.getSaleUserId();
                if (userId == null || userId.equals(currentUser.getId())) {
                    continue; // 跳过空值或当前用户自己
                }
                
                String content;
                String title;
                // 检查当前用户是否为admin，如果是则显示"商城客户"，否则显示用户名
                String displayName = ErpConstant.DEFAULT_USER_ID == currentUser.getId() ? "商城客户" : currentUser.getUsername();

                if (share.getShareTag() == 1) {
                    // 线下销售
                    title = "线下销售协作人提醒";
                    content = String.format("%s 新建商机%s，作为线下销售您可查看。",
                            displayName, businessChanceNo);
                    sendCardMessage(userId, title, content, url, "查看详情");
                    log.info("已向线下销售协作人(ID:{})发送商机{}新建提醒", userId, businessChanceNo);
                } else if (share.getShareTag() == 2) {
                    // 产线负责人
                    title = "产线协作人提醒";
                    content = String.format("%s 新建商机%s中可能存在跟您产线相关的产品，请查看。",
                            displayName, businessChanceNo);
                    sendCardMessage(userId, title, content, url, "查看详情");
                    log.info("已向产线协作人(ID:{})发送商机{}新建提醒", userId, businessChanceNo);
                }
            }
        } catch (Exception e) {
            log.error("发送商机{}自动共享消息提醒失败: {}", businessChanceNo, e.getMessage(), e);
        }
    }


    
    /**
     * 发送卡片消息
     * @param targetUserId 目标用户ID
     * @param title 标题
     * @param content 内容
     * @param url 跳转链接
     * @param btnText 按钮文本
     */
    private void sendCardMessage(Integer targetUserId, String title, String content, String url, String btnText) {
        try {
            if (targetUserId == null) {
                log.warn("目标用户ID为空，无法发送消息");
                return;
            }
            
            // 获取用户工号
            UserDto userDto = userApiService.getUserById(targetUserId);
            if (userDto == null) {
                log.warn("无法获取用户信息，用户ID: {}", targetUserId);
                return;
            }
            
            String userNumber = userDto.getNumber();
            if (StrUtil.isEmpty(userNumber)) {
                log.warn("用户工号为空，用户ID: {}", targetUserId);
                return;
            }
            
            // 创建企业微信卡片消息
            WxCpMessage wxCpMessage = new WxCpMessage();
            wxCpMessage.setToUser(userNumber);
            wxCpMessage.setMsgType("textcard");
            wxCpMessage.setTitle(title);
            wxCpMessage.setDescription(content);
            wxCpMessage.setUrl(url);
            wxCpMessage.setBtnTxt(btnText);
            
            log.info("发送企业微信卡片消息: 用户={}, 标题={}, 内容={}", userNumber, title, content);
            uacWxUserInfoApiService.sendToUser(wxCpMessage);
            
            // 记录日志
            log.info("成功发送企业微信卡片消息: 目标用户ID={}, 工号={}, 标题={}", targetUserId, userNumber, title);
        } catch (Exception e) {
            log.error("发送企业微信卡片消息失败，目标用户ID: {}, 原因: {}", targetUserId, e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public void saveTerminal(BusinessChanceDto businessChanceDto) {
        CurrentUser currentUser = getCurrentUser();
        OrderTerminalDto orderTerminalDto = businessChanceDto.getOrderTerminalDto();
        orderTerminalDto.setBusinessId(businessChanceDto.getBussinessChanceId());
        orderTerminalDto.setBusinessNo(businessChanceDto.getBussinessChanceNo());
        orderTerminalDto.setBusinessType(ErpConstant.ONE);
        orderTerminalDto.setAddTime(new Date());
        orderTerminalDto.setModTime(new Date());
        orderTerminalDto.setCreator(currentUser.getId());
        orderTerminalDto.setUpdater(currentUser.getId());
        orderTerminalDto.setCreatorName(currentUser.getUsername());
        orderTerminalDto.setUpdaterName(currentUser.getUsername());
        //属性字段要单独赋上
        orderTerminalDto.setTerminalTraderNature(businessChanceDto.getTerminalTraderNature());
        orderTerminalDto.setNatureTypeName(getTerminalTraderNatureTypeName(businessChanceDto.getTerminalTraderNature()));


        orderTerminalApiService.save(orderTerminalDto);
        BussinessChanceEntity update = new BussinessChanceEntity();
        update.setBussinessChanceId(orderTerminalDto.getBusinessId());
        update.setTerminalTraderName(orderTerminalDto.getTerminalName());
        update.setTerminalTraderType(ErpConstant.ZERO);
        bussinessChanceMapper.updateByPrimaryKeySelective(update);
    }

    private String getTerminalTraderNatureTypeName(Integer terminalTraderNature){
        SysOptionDefinitionDto dto = sysOptionDefinitionApiService.getOptionDefinitionById(terminalTraderNature);
        if(dto != null && !StrUtil.isEmpty(dto.getTitle())){
            return dto.getTitle();
        }
        return "";
    }


    private static String generateBusinessNo(Integer businessChanceId) {
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.BUSINESS_CHANCE, NoGeneratorBean.builder().id(businessChanceId).numberOfDigits(9).build());
        return new BillNumGenerator().distribution(billGeneratorBean);
    }

    private void addChanceTrack(BusinessChanceDto businessChanceDto, BussinessChanceEntity bussinessChanceEntity,
			String code) {
		try {
			//VDERP-17057  【客户档案】ERP客户档案时间轴 线索转商机（新增商机）
	        //判断商机是否有客户ID,无客户ID跳过，有客户ID判断：
	        if(Objects.nonNull(bussinessChanceEntity.getTraderId()) && bussinessChanceEntity.getTraderId()!=0){
	        	//1:businessLeadsId 线索ID为空，说明新增商机，新增商机埋点
	        	if(Objects.isNull(businessChanceDto.getBusinessLeadsId()) || (Objects.nonNull(businessChanceDto.getBusinessLeadsId()) && businessChanceDto.getBusinessLeadsId() == 0)) {
	        		TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.PRE_SALE_NEW_BUSINESS_CHANCE);
	        		TrackParamsData trackParamsData = new TrackParamsData();
	        		trackParamsData.setEventTrackingEnum(EventTrackingEnum.PRE_SALE_NEW_BUSINESS_CHANCE);
	        		Map<String, Object> trackParams = new HashMap<>();
                    UserDto user = userApiService.getUserById(bussinessChanceEntity.getCreator());
                    trackParams.put("track_user", user);
	        		trackParams.put("traderId", bussinessChanceEntity.getTraderId());
	        		trackParams.put("bussinessChanceNo", code);
	        		trackParamsData.setTrackParams(trackParams);
	        		trackParamsData.setTrackResult(R.success());
	        		trackStrategy.track(trackParamsData);
	        	}
	        	//2:businessLeadsId 线索ID不为空，说明线索转商机，同时判断线索是否有关联客户，无关联客户追溯新建线索,或者有关联客户但与本次商机关联客户不同时追溯新建线索
	        	else {
	        		BusinessLeadsDto oldBusinessLeadsDto = businessLeadsService.getOne(businessChanceDto.getBusinessLeadsId());
	        		BusinessLeadsEntity oldBusinessLeadsEntity = businessLeadsConvertor.toEntity(oldBusinessLeadsDto);
	        		//线索转商机
	        		TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.PRE_SALE_BUSINESS_LEADS_TO_CHANCE_LEADS);
	        		TrackParamsData trackParamsData = new TrackParamsData();
	        		trackParamsData.setEventTrackingEnum(EventTrackingEnum.PRE_SALE_BUSINESS_LEADS_TO_CHANCE_LEADS);
	        		Map<String, Object> trackParams = new HashMap<>();
                    UserDto user = userApiService.getUserById(bussinessChanceEntity.getCreator());
                    trackParams.put("track_user", user);
	        		trackParams.put("traderId", bussinessChanceEntity.getTraderId());
	        		trackParams.put("bussinessChanceNo", code);
                    UserDto belongerUser = userApiService.getUserById(bussinessChanceEntity.getUserId());
	        		trackParams.put("belonger",belongerUser.getUsername());
	        		trackParams.put("belongerNumber", belongerUser.getNumber());
	        		trackParamsData.setTrackParams(trackParams);
	        		trackParamsData.setTrackResult(R.success());
	        		trackStrategy.track(trackParamsData);
	        		if(Objects.isNull(oldBusinessLeadsDto.getTraderId()) ||  oldBusinessLeadsDto.getTraderId() == 0 || !oldBusinessLeadsDto.getTraderId().equals(bussinessChanceEntity.getTraderId())) {
                        //追溯新建线索(使用本次最新的客户ID)
                        oldBusinessLeadsEntity.setTraderId(bussinessChanceEntity.getTraderId());
	        		    if(oldBusinessLeadsEntity.getClueType().equals(ErpConstant.ID_394)) {
                            businessLeadsService.track(oldBusinessLeadsEntity, EventTrackingEnum.PRE_SALE_NEW_BUSINESS_LEADS_FRONT, oldBusinessLeadsDto.getAddTime());
                        }else {
                            businessLeadsService.track(oldBusinessLeadsEntity, EventTrackingEnum.PRE_SALE_NEW_BUSINESS_LEADS, oldBusinessLeadsDto.getAddTime());
                        }
	        		}
	        	}
	        }
		}catch(Exception e) {
			log.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.PRE_SALE_NEW_BUSINESS_CHANCE.getArchivedName(),e);
		}
	}


    /**
     * 设置商机归属 人 以及归属部门
     *
     * @param businessChanceDto 商机对象
     */
    private void businessChanceSetUserId(BusinessChanceDto businessChanceDto) {
        if (Objects.isNull(businessChanceDto.getUserId()) || ErpConstant.ZERO.equals(businessChanceDto.getUserId())) {
            Integer saleId = traderCustomerBaseService.getTraderCustomerUserIdByTraderId(businessChanceDto.getTraderId());
            businessChanceDto.setUserId(saleId);
            if (Objects.isNull(businessChanceDto.getUserId())) {
                businessChanceDto.setUserId(getCurrentUser().getId());
            }
        }
        UserDto userById = userApiService.getUserById(businessChanceDto.getUserId());
        businessChanceDto.setUsername(userById.getUsername());
        businessChanceDto.setOrgId(userById.getOrgId());
    }

    private void updateQuoteOrderByBusinessChance(Integer bussinessChanceId){
        Integer quoteOrderId = quoteorderMapper.selectQuoteorderIdByBusinessChanceId(bussinessChanceId);
        if(quoteOrderId != null && quoteOrderId >0){
            BussinessChanceEntity bussinessChanceNow = bussinessChanceMapper.selectByPrimaryKey(bussinessChanceId);
            if(bussinessChanceNow == null){
                return;
            }

            QuoteorderEntity quoteorderEntity = new QuoteorderEntity() ;
            quoteorderEntity.setQuoteorderId(quoteOrderId);
            if(bussinessChanceNow.getTraderId() != null && bussinessChanceNow.getTraderId()>0){
                TraderCustomerInfoVo traderCustomerInfoVo = traderCustomerBaseService.getTraderCustomerInfoVo(bussinessChanceNow.getTraderId());
                quoteorderEntity.setTraderId(bussinessChanceNow.getTraderId());
                quoteorderEntity.setTraderName(bussinessChanceNow.getTraderName());
                quoteorderEntity.setArea(traderCustomerInfoVo.getAddress());
                quoteorderEntity.setCustomerType(traderCustomerInfoVo.getCustomerType());
                quoteorderEntity.setCustomerNature(traderCustomerInfoVo.getCustomerNature());
            }else{
                quoteorderEntity.setTraderId(0);
                quoteorderEntity.setTraderName("");
                quoteorderEntity.setArea("");
                quoteorderEntity.setCustomerType(0);
                quoteorderEntity.setCustomerNature(0);
            }
            quoteorderEntity.setTraderContactId(bussinessChanceNow.getTraderContactId());
            quoteorderEntity.setTraderContactName(bussinessChanceNow.getTraderContactName());
            quoteorderEntity.setMobile(bussinessChanceNow.getMobile());
            quoteorderEntity.setTelephone(bussinessChanceNow.getTelephone());
            quoteorderMapper.updateByPrimaryKeyForBusinessChance(quoteorderEntity);


        }

    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public BusinessChanceDto update(BusinessChanceDto businessChanceDto) {
        log.info("CRM商机，更新入参：{}", JSON.toJSONString(businessChanceDto));
        // 赋值归属
        businessChanceSetUserId(businessChanceDto);
        // 客户信息
        confirmTraderData(businessChanceDto);
        // 计算等级
        calc(businessChanceDto);
        // 其他数据封装
        baseDataSet(businessChanceDto);
//        if (Objects.nonNull(businessChanceDto.getAmount())) {
//            businessChanceDto.setAmount(businessChanceDto.getAmount().multiply(ErpConstant.TEN_THOUSAND));
//        }

        // 转换
        BussinessChanceEntity bussinessChanceEntity = businessChanceConvertor.toEntity(businessChanceDto);
        // VDERP-17057  【客户档案】ERP客户档案时间轴 编辑商机（用于追溯）
        editChanceTrack(bussinessChanceEntity);
        bussinessChanceMapper.updateByPrimaryKeySelective(bussinessChanceEntity);
        updateQuoteOrderByBusinessChance(businessChanceDto.getBussinessChanceId());

        if(Objects.nonNull(businessChanceDto.getCategoryIds())){
            // 首先删除现有的所有分类关联
            businessOrderCategoryService.deleteByBusinessIdAndType(bussinessChanceEntity.getBussinessChanceId(), ErpConstant.ONE);
            
            // 如果分类IDs不为空，则重新添加所选分类
            if(!StrUtil.isEmpty(businessChanceDto.getCategoryIds())) {
                String[] categoryIds = businessChanceDto.getCategoryIds().split(",");
                for(String categoryId : categoryIds){
                    if(StrUtil.isNotBlank(categoryId)) {
                        BusinessOrderCategoryDto businessOrderCategoryDto = new BusinessOrderCategoryDto();
                        businessOrderCategoryDto.setBusinessId(bussinessChanceEntity.getBussinessChanceId());
                        businessOrderCategoryDto.setBusinessType(ErpConstant.ONE);
                        businessOrderCategoryDto.setCategoryId(Integer.parseInt(categoryId));
                        businessOrderCategoryDto.setKeywords(businessChanceDto.getKeywords());
                        businessOrderCategoryService.save(businessOrderCategoryDto);
                    }
                }
            }
        }else{
            // 如果没有分类信息，删除所有关联的分类
            businessOrderCategoryService.deleteByBusinessIdAndType(bussinessChanceEntity.getBussinessChanceId(),ErpConstant.ONE);
        }

        // 处理关联终端信息
        OrderTerminalDto orderTerminalDto = businessChanceDto.getOrderTerminalDto();
        if (Objects.nonNull(orderTerminalDto) && StrUtil.isNotBlank(orderTerminalDto.getTerminalName())) {
            saveTerminal(businessChanceDto);
        }else if(!StrUtil.isEmpty(businessChanceDto.getTerminalTraderName())){
//            terminalTraderName:"开发P版账号勿动 科研购1"
//            terminalTraderNature:5602
//            terminalTraderRegion:"130000,130300,130303"
//            terminalTraderRegionStr: "河北省,秦皇岛市,山海关区"
            OrderTerminalDto orderTerminalDtoTemp = new OrderTerminalDto();
            orderTerminalDtoTemp.setTerminalName(businessChanceDto.getTerminalTraderName());
            orderTerminalDtoTemp.setTerminalTraderNature(businessChanceDto.getTerminalTraderNature());
            String terminalTraderRegion = businessChanceDto.getTerminalTraderRegion();
            //将terminalTraderRegion按逗号分割，分别赋值给provinceId,cityId,areaId，并考虑可能只有两个值或一个值的情况
            if (StrUtil.isNotBlank(terminalTraderRegion)) {
                String[] terminalTraderRegions = terminalTraderRegion.split(",");
                if (terminalTraderRegions.length == 3) {
                    orderTerminalDtoTemp.setProvinceId(Integer.parseInt(terminalTraderRegions[0]));
                    orderTerminalDtoTemp.setCityId(Integer.parseInt(terminalTraderRegions[1]));
                    orderTerminalDtoTemp.setAreaId(Integer.parseInt(terminalTraderRegions[2]));
                } else if (terminalTraderRegions.length == 2) {
                    orderTerminalDtoTemp.setProvinceId(Integer.parseInt(terminalTraderRegions[0]));
                    orderTerminalDtoTemp.setCityId(Integer.parseInt(terminalTraderRegions[1]));
                } else if (terminalTraderRegions.length == 1) {
                    orderTerminalDtoTemp.setProvinceId(Integer.parseInt(terminalTraderRegions[0]));
                }
            }
            //将terminalTraderRegionStr，按以上terminalTraderRegion一样的逻辑，按逗号分割，分别赋值给 provinceName,cityName,areaName，并考虑可能只有两个值或一个值的情况
            String terminalTraderRegionStr = businessChanceDto.getTerminalTraderRegionStr();
            if (StrUtil.isNotBlank(terminalTraderRegionStr)) {
                String[] terminalTraderRegionStrs = terminalTraderRegionStr.split(",");
                if (terminalTraderRegionStrs.length == 3) {
                    orderTerminalDtoTemp.setProvinceName(terminalTraderRegionStrs[0]);
                    orderTerminalDtoTemp.setCityName(terminalTraderRegionStrs[1]);
                    orderTerminalDtoTemp.setAreaName(terminalTraderRegionStrs[2]);
                } else if (terminalTraderRegionStrs.length == 2) {
                    orderTerminalDtoTemp.setProvinceName(terminalTraderRegionStrs[0]);
                    orderTerminalDtoTemp.setCityName(terminalTraderRegionStrs[1]);
                } else if (terminalTraderRegionStrs.length == 1) {
                    orderTerminalDtoTemp.setProvinceName(terminalTraderRegionStrs[0]);
                }
            }
            businessChanceDto.setOrderTerminalDto(orderTerminalDtoTemp);
            saveTerminal(businessChanceDto);
        }

        if (StrUtil.isNotBlank(businessChanceDto.getTerminalTraderName()) || Objects.nonNull(businessChanceDto.getTerminalTraderNature())) {
            // 更新商机阶段（商机验证）
            updateStageAndStageTime(businessChanceDto.getBussinessChanceId(), BusinessChanceStageEnum.OPPORTUNITY_VERIFICATION);
        }
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        params.put("userName",currentUser.getUsername());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        logDto.setBizId(businessChanceDto.getBussinessChanceId());
        operationLogApiService.save(logDto, BizLogEnum.BUSINESS_CHANE_EDIT);
        return businessChanceDto;
    }

    @Override
    public void addTrackQuotation(CurrentUser currentUser,String quotationNo,Integer traderId) {
        try {
            UserDto user = userApiService.getUserById(currentUser.getId());
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            trackParams.put("traderId",traderId);
            trackParams.put("track_user", user);
            trackParams.put("quotationNo",quotationNo);
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.SALE_NEW_QUOTATION);
            trackParamsData.setEventTrackingEnum(EventTrackingEnum.SALE_NEW_QUOTATION);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(R.success());
            trackStrategy.track(trackParamsData);
        }catch(Exception e) {
            log.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.SALE_NEW_QUOTATION.getArchivedName(),e);
        }
    }

    //VDERP-17057  【客户档案】ERP客户档案时间轴 编辑商机（用于追溯）
    //1：判断本次是否有关联客户ID，无客户ID跳过埋点
    //2：有客户ID
    //2.1:更新前是否关联客户ID，无关联客户ID或者有关联客户ID，并与本次关联的客户ID不同，有线索追溯   1创建线索转商机、2线索创建
    //2.2:更新前是否关联客户ID，无关联客户ID或者有关联客户ID，并与本次关联的客户ID不同，无线索追溯   1创建商机
    private void editChanceTrack(BussinessChanceEntity bussinessChanceEntity) {
    	try {
    		Integer newTraderId = bussinessChanceEntity.getTraderId();
        	//无客户ID跳过埋点
        	if(Objects.isNull(newTraderId) ||  newTraderId.equals(0)) {
        		return;
        	}
    		//获取更新前的商机
    		BussinessChanceEntity oldBussinessChanceEntity  = bussinessChanceMapper.selectByPrimaryKey(bussinessChanceEntity.getBussinessChanceId());
    		Integer oldTraderId = oldBussinessChanceEntity.getTraderId();
    		//无关联客户ID或者有关联客户ID，并与本次关联的客户ID不同
    		if(Objects.isNull(oldTraderId) || !oldTraderId.equals(newTraderId)) {
    			//有线索追溯   1创建线索转商机、2线索创建
    			List<BusinessLeadsDto> oldBusinessLeadsDtoList = businessLeadsService.getLeadsListByParams(bussinessChanceEntity.getBussinessChanceId());
    			if(!CollUtil.isEmpty(oldBusinessLeadsDtoList)) {
    				BusinessLeadsDto oldBusinessLeadsDto = oldBusinessLeadsDtoList.get(0);
            		BusinessLeadsEntity oldBusinessLeadsEntity = businessLeadsConvertor.toEntity(oldBusinessLeadsDtoList.get(0));
            		//线索转商机
            		TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.PRE_SALE_BUSINESS_LEADS_TO_CHANCE_LEADS);
            		TrackParamsData trackParamsData = new TrackParamsData();
            		trackParamsData.setEventTrackingEnum(EventTrackingEnum.PRE_SALE_BUSINESS_LEADS_TO_CHANCE_LEADS);
            		Map<String, Object> trackParams = new HashMap<>();
                    UserDto user = userApiService.getUserById(bussinessChanceEntity.getCreator());
                    trackParams.put("track_user", user);
            		trackParams.put("traderId", newTraderId);
            		trackParams.put("bussinessChanceNo", bussinessChanceEntity.getBussinessChanceNo());
                    UserDto belongerUser = userApiService.getUserById(bussinessChanceEntity.getUserId());
            		trackParams.put("belonger",belongerUser.getUsername());
            		trackParams.put("belongerNumber", belongerUser.getNumber());
            		trackParamsData.setTrackParams(trackParams);
            		trackParamsData.setTrackResult(R.success());
            		trackStrategy.track(trackParamsData);
            		if(Objects.isNull(oldBusinessLeadsDto.getTraderId()) || oldBusinessLeadsDto.getTraderId() == 0 || !oldBusinessLeadsDto.getTraderId().equals(newTraderId)) {
                        //追溯新建线索(使用本次最新的客户ID)
                        oldBusinessLeadsEntity.setTraderId(newTraderId);
            		    if(oldBusinessLeadsEntity.getClueType().equals(ErpConstant.ID_394)) {
                            businessLeadsService.track(oldBusinessLeadsEntity, EventTrackingEnum.PRE_SALE_NEW_BUSINESS_LEADS_FRONT, oldBusinessLeadsDto.getAddTime());
                        }else {
                            businessLeadsService.track(oldBusinessLeadsEntity, EventTrackingEnum.PRE_SALE_NEW_BUSINESS_LEADS, oldBusinessLeadsDto.getAddTime());
                        }
            		}
    			}
    			//无线索追溯   1创建商机
    			else {
    				TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.PRE_SALE_NEW_BUSINESS_CHANCE);
            		TrackParamsData trackParamsData = new TrackParamsData();
            		trackParamsData.setEventTrackingEnum(EventTrackingEnum.PRE_SALE_NEW_BUSINESS_CHANCE);
            		Map<String, Object> trackParams = new HashMap<>();
                    UserDto user = userApiService.getUserById(bussinessChanceEntity.getCreator());
            		trackParams.put("track_user", user);
            		trackParams.put("traderId", bussinessChanceEntity.getTraderId());
            		trackParams.put("bussinessChanceNo", bussinessChanceEntity.getBussinessChanceNo());
            		trackParamsData.setTrackTime(new Date(oldBussinessChanceEntity.getAddTime()));
            		trackParamsData.setTrackParams(trackParams);
            		trackParamsData.setTrackResult(R.success());
            		trackStrategy.track(trackParamsData);
    			}
    		}
    	}catch(Exception e) {
    		log.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.PRE_SALE_BUSINESS_LEADS_TO_CHANCE_LEADS.getArchivedName(),e);
    	}
	}

	private void sendSuperGuidance(BusinessChanceDto businessChanceDto, String preSupervisorGuidance, BussinessChanceEntity bussinessChanceEntity) {
//        String supervisorGuidance = businessChanceDto.getSupervisorGuidance();
//        if (StrUtil.isNotBlank(supervisorGuidance)){
//            if (!supervisorGuidance.equals(preSupervisorGuidance)){
//                List<Integer> userIdList = new ArrayList<Integer>();
//                Map<String, String> mesMap = new HashMap<>();
//                mesMap.put("bussinessChanceNo", businessChanceDto.getBussinessChanceNo());
//
//                if (bussinessChanceEntity.getUserId() == null || bussinessChanceEntity.getUserId() == 0) {
//                    //如果商机没有分配到销售，则给售前总机角色的账户发送站内消息
//                    userIdList = rUserRoleMapper.getUserIdListAndNotDisabled(Collections.singletonList(17));
//                } else {
//                    userIdList.add(bussinessChanceEntity.getUserId());
//                }
//
//                MessageUtil.sendMessage(240, userIdList, mesMap, "/businessChance/details.do?id=" + bussinessChanceEntity.getBussinessChanceId());
//            }
//        }
    }

    /**
     * 基础数据赋值
     *
     * @param businessChanceDto 商机数据
     */
    private void baseDataSet(BusinessChanceDto businessChanceDto) {
        long now = System.currentTimeMillis();
        if (Objects.isNull(businessChanceDto.getBussinessChanceId())) {
            businessChanceDto.setAddTime(now);
            businessChanceDto.setAssignTime(now);
            businessChanceDto.setReceiveTime(now);
            businessChanceDto.setCreator(getCurrentUser().getId());
            businessChanceDto.setCompanyId(ErpConstant.ONE);
        }
        businessChanceDto.setModTime(now);
        businessChanceDto.setUpdater(getCurrentUser().getId());
        if (Objects.nonNull(businessChanceDto.getOrderTimeDate())) {
            businessChanceDto.setOrderTime(businessChanceDto.getOrderTimeDate().getTime());
        }
        if (Objects.isNull(businessChanceDto.getTerminalTraderNature())) {
            businessChanceDto.setTerminalTraderNature(0);
        }
        if (Objects.isNull(businessChanceDto.getPurchasingType())) {
            businessChanceDto.setPurchasingType(0);
        }
        if (Objects.isNull(businessChanceDto.getBiddingPhase())) {
            businessChanceDto.setBiddingPhase(0);
        }
        if (Objects.isNull(businessChanceDto.getBiddingParameter())) {
            businessChanceDto.setBiddingParameter(0);
        }
    }

    @Autowired
    private BusinessLeadsMapper businessLeadsMapper;
    /**
     * 注意 是销售新增的时候  销售新增商机 基础数据分装 在add 的时候
     * 警告别瞎用 其他类型的商机不可调用
     *
     * @param businessChanceDto 商机
     */
    private void businessChanceOfSaleOrLeadsBaseInfoSet(BusinessChanceDto businessChanceDto) {
        if(Objects.isNull(businessChanceDto.getBusinessLeadsId())){
            if(businessChanceDto.getClueType() != null){
                businessChanceDto.setType(businessChanceDto.getClueType());
            }else{
                businessChanceDto.setType(BusinessChanceConstant.TYPE_SALE_ADD);
            }
        }else{
            BusinessLeadsEntity businessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(businessChanceDto.getBusinessLeadsId());
            if(Objects.isNull(businessLeadsEntity)){
                businessChanceDto.setType(BusinessChanceConstant.TYPE_SALE_ADD);
            }else{
                businessChanceDto.setType(businessLeadsEntity.getClueType());
            }
        }
//        businessChanceDto.setType(Objects.isNull(businessChanceDto.getBusinessLeadsId()) ? BusinessChanceConstant.TYPE_SALE_ADD : BusinessChanceConstant.TYPE_LEADS);
    }


    /**
     * 封装 确定了客户的客户信息 无客户默认0
     * 兼容更新 改动为清空客户则将确认客户的置空
     *
     * @param businessChanceDto 客户信息
     */
    private void confirmTraderData(BusinessChanceDto businessChanceDto) {
        if (Objects.nonNull(businessChanceDto.getTraderId()) && !ErpConstant.ZERO.equals(businessChanceDto.getTraderId())) {
            businessChanceDto.setCheckTraderContactName(businessChanceDto.getTraderContactName());
            businessChanceDto.setCheckTraderContactTelephone(businessChanceDto.getTelephone());
            businessChanceDto.setCheckTraderContactMobile(businessChanceDto.getMobile());
            TraderCustomerInfoVo traderCustomerInfoVo = traderCustomerBaseService.getTraderCustomerInfoVo(businessChanceDto.getTraderId());
            if (Objects.nonNull(traderCustomerInfoVo)) {
                businessChanceDto.setAreaId(traderCustomerInfoVo.getAreaId());
                businessChanceDto.setCheckTraderName(traderCustomerInfoVo.getTraderName());
            } else {
                businessChanceDto.setCheckTraderName(businessChanceDto.getTraderName());
            }
        } else {
            businessChanceDto.setTraderId(0);
            businessChanceDto.setCheckTraderContactName("");
            businessChanceDto.setCheckTraderContactTelephone("");
            businessChanceDto.setCheckTraderContactMobile("");
            businessChanceDto.setCheckTraderName("");
        }
    }

    /**
     * 判断是否可以转报价 可以则生成报价单
     *
     * @param businessChanceDto 商机实体
     * @return QuoteorderDto 报价对象
     */
    public boolean tryConvertQuoteOrder(BusinessChanceDto businessChanceDto) {

        // 判断转换条件
//        if (businessChanceDto.getTraderId() != null && businessChanceDto.getTraderId() != 0 && !CollectionUtils.isEmpty(businessChanceDto.getBusinessChanceGoodsDtos())) {
//
//            // 判断商机是否已有报价
//            if (Objects.isNull(businessChanceDto.getBussinessChanceId())) {
//                throw new ServiceException("新增商机失败");
//            }
//            Integer quoteOrderId = quoteOrderService.selectQuoteorderIdByBusinessChanceId(businessChanceDto.getBussinessChanceId());
//
//            if (Objects.nonNull(quoteOrderId)) {
//                log.error("商机id:{}已存在报价单 id:{}", businessChanceDto.getBussinessChanceId(), quoteOrderId);
//                return false;
//            }
//
//            return true;
//        }
        return false;
    }

    /**
     * 商机生成报价单对象
     *
     * @param businessChanceDto 转报价
     * @return 报价单
     */
    private QuoteorderDto convertQuoteOrder(BusinessChanceDto businessChanceDto) {

        QuoteorderDto quoteorderDto = new QuoteorderDto();

        BeanUtil.copyProperties(businessChanceDto, quoteorderDto);
        // 客户信息
        TraderCustomerInfoVo traderCustomerInfoVo = traderCustomerBaseService.getTraderCustomerInfoVo(businessChanceDto.getTraderId());
        if (traderCustomerInfoVo != null) {
            quoteorderFromOtherDataConvertor.quoteOrderBindTraderCustomerInfoVo(traderCustomerInfoVo, quoteorderDto);
        }

        // 联系人信息  总价 商机编号等 商机中有的
        quoteorderFromOtherDataConvertor.quoteOrderBindBussinessChance(businessChanceDto, quoteorderDto);

        // 部门id 销售id //  零碎 属性赋值
        CurrentUser currentUser = getCurrentUser();
        currentUser.setOrgId(businessChanceDto.getOrgId());
        // 用商机的归属对象
        currentUser.setId(businessChanceDto.getUserId());
        quoteorderFromOtherDataConvertor.quoteOrderBindCurrentUser(currentUser, quoteorderDto);

        if (!CollUtil.isEmpty(businessChanceDto.getBusinessChanceGoodsDtos())) {
            List<QuoteorderGoodsDto> quoteorderGoodsDtos = businessChanceGoodsDtoToQuoteorderGoodsDtoConvertor.busChanceGoodsListToQuoteGoodsDtoList(businessChanceDto.getBusinessChanceGoodsDtos());
            quoteorderDto.setQuoteorderGoodsDtos(quoteorderGoodsDtos);
        }

        return quoteorderDto;
    }

    /**
     * 计算规则
     */
    @Override
    public void calc(BusinessChanceDto businessChanceData) {
        log.info("CRM商机，等级计算入参：{}", JSON.toJSONString(businessChanceData));
        AtomicInteger score = new AtomicInteger(0);
        Integer businessType = businessChanceData.getBusinessType();
        switch (businessType) {
            // 小产品、大单品、应急
            case BusinessChanceConstant.ID_5701:
            case BusinessChanceConstant.ID_5702:
            case BusinessChanceConstant.ID_5705:
                traderLevelCalc(score, businessChanceData.getTraderId());
                customerRelationshipCalc(score, businessChanceData.getCustomerRelationship());
                productMatchingCalc(score, businessChanceData.getBussinessChanceId());
                biddingPhaseCalc(score, businessChanceData.getBiddingPhase());
                terminalTypeCalc(score, businessChanceData.getTerminalTraderName(), businessChanceData.getTerminalTraderNature());
                break;
            // 综合项目
            case BusinessChanceConstant.ID_5703:
                traderLevelCalc(score, businessChanceData.getTraderId());
                customerRelationshipCalc(score, businessChanceData.getCustomerRelationship());
                supplyMatchingCalc(score, businessChanceData.getTerminalTraderNature());
                biddingPhaseCalc(score, businessChanceData.getBiddingPhase());
                terminalTypeCalc(score, businessChanceData.getTerminalTraderName(), businessChanceData.getTerminalTraderNature());
                break;
            // AED
            case BusinessChanceConstant.ID_5704:
                aedCalc(businessChanceData);
                break;
            default:
                log.info("CRM商机，等级计算未匹配到商机类型：{}", JSON.toJSONString(businessChanceData));
                break;
        }
        log.info("CRM商机，等级计算总分：{}", score.get());
        scoreConvertLevel(score.get(), businessChanceData);
        log.info("CRM商机，等级计算结果：{}", JSON.toJSONString(businessChanceData));
    }

    private void scoreConvertLevel(int score, BusinessChanceDto businessChanceData) {
        if (businessChanceData.getBusinessType() == BusinessChanceConstant.ID_5704) {
            return;
        }
        if (score >= BusinessChanceConstant.BOUNDARY_80) {
            businessChanceData.setSystemBusinessLevel(BusinessChanceLevelEnum.S.getCode());
        } else if (score >= BusinessChanceConstant.BOUNDARY_60) {
            businessChanceData.setSystemBusinessLevel(BusinessChanceLevelEnum.A.getCode());
        } else if (score >= BusinessChanceConstant.BOUNDARY_40) {
            businessChanceData.setSystemBusinessLevel(BusinessChanceLevelEnum.B.getCode());
        } else {
            businessChanceData.setSystemBusinessLevel(BusinessChanceLevelEnum.C.getCode());
        }
    }

    private void aedCalc(BusinessChanceDto businessChanceData) {
        Integer biddingPhase = businessChanceData.getBiddingPhase();
        Integer biddingParameter = businessChanceData.getBiddingParameter();
        String terminalTraderName = businessChanceData.getTerminalTraderName();
        BusinessChanceLevelEnum biddingPhaseEnum = BusinessChanceLevelEnum.C;
        if (Objects.nonNull(biddingPhase)) {
            switch (biddingPhase) {
                // 提案咨询
                case BusinessChanceConstant.ID_5801:
                    if (ErpConstant.ONE.equals(biddingParameter) && StrUtil.isNotBlank(terminalTraderName)) {
                        biddingPhaseEnum = BusinessChanceLevelEnum.S;
                    }else {
                        biddingPhaseEnum = BusinessChanceLevelEnum.A;
                    }
                    break;
                // 立项论证
                case BusinessChanceConstant.ID_5802:
                    if (ErpConstant.ONE.equals(biddingParameter)) {
                        biddingPhaseEnum = BusinessChanceLevelEnum.A;
                    } else {
                        biddingPhaseEnum = BusinessChanceLevelEnum.B;
                    }
                    break;
                // 意向公示
                case BusinessChanceConstant.ID_5803:
                    if (ErpConstant.ONE.equals(biddingParameter)) {
                        biddingPhaseEnum = BusinessChanceLevelEnum.A;
                    }
                    break;
                // 公开招标、合同签署
                case BusinessChanceConstant.ID_5804:
                case BusinessChanceConstant.ID_5805:
                    if (ErpConstant.ONE.equals(biddingParameter)) {
                        biddingPhaseEnum = BusinessChanceLevelEnum.B;
                    }
                    break;
                default:
                    break;
            }
        }
        businessChanceData.setSystemBusinessLevel(biddingPhaseEnum.getCode());
    }

    private void supplyMatchingCalc(AtomicInteger score, Integer terminalTraderNature) {
        if (Objects.nonNull(terminalTraderNature)) {
            switch (terminalTraderNature) {
                // 公立基层、非公基层
                case BusinessChanceConstant.ID_5602:
                case BusinessChanceConstant.ID_5604:
                    score.addAndGet(20);
                    break;
                // 应急、院外
                case BusinessChanceConstant.ID_5606:
                case BusinessChanceConstant.ID_5607:
                    score.addAndGet(15);
                    break;
                // 公立等级、非公等级、非公集团
                case BusinessChanceConstant.ID_5601:
                case BusinessChanceConstant.ID_5603:
                case BusinessChanceConstant.ID_5605:
                case BusinessChanceConstant.ID_6010:
                    score.addAndGet(10);
                    break;
                default:
                    break;
            }
        }
    }

    private void terminalTypeCalc(AtomicInteger score, String terminalTraderName, Integer terminalTraderNature) {
        if (StrUtil.isNotBlank(StrUtil.trim(terminalTraderName))) {
            score.addAndGet(20);
            return;
        }
        if (Objects.nonNull(terminalTraderNature)) {
            switch (terminalTraderNature) {
                // 公立基层、非公等级、非公基层、非公集团、应急、院外
                case BusinessChanceConstant.ID_5602:
                case BusinessChanceConstant.ID_5603:
                case BusinessChanceConstant.ID_5604:
                case BusinessChanceConstant.ID_5605:
                case BusinessChanceConstant.ID_5606:
                case BusinessChanceConstant.ID_5607:
                    score.addAndGet(20);
                    break;
                // 公立等级
                case BusinessChanceConstant.ID_5601:
                case BusinessChanceConstant.ID_6010:
                    score.addAndGet(10);
                    break;
                default:
                    break;
            }
        }
    }

    private void biddingPhaseCalc(AtomicInteger score, Integer biddingPhase) {
        if (Objects.nonNull(biddingPhase)) {
            switch (biddingPhase) {
                // 提案咨询
                case BusinessChanceConstant.ID_5801:
                    score.addAndGet(20);
                    break;
                // 立项论证
                case BusinessChanceConstant.ID_5802:
                    score.addAndGet(15);
                    break;
                // 意向公示
                case BusinessChanceConstant.ID_5803:
                    score.addAndGet(10);
                    break;
                default:
                    break;
            }
        }
    }

    private void productMatchingCalc(AtomicInteger score, Integer bussinessChanceId) {
        List<Integer> goodsPositionList = bussinessChanceMapper.getGoodsPosition(bussinessChanceId);
        if (goodsPositionList.stream().anyMatch(ErpConstant.FIVE::equals)) {
            score.addAndGet(20);
        } else if (goodsPositionList.stream().anyMatch(ErpConstant.FOUR::equals)) {
            score.addAndGet(10);
        }
    }

    private void customerRelationshipCalc(AtomicInteger score, String customerRelationship) {
        if (StrUtil.isNotBlank(customerRelationship)) {
            StrUtil.split(customerRelationship, ",")
                    .stream()
                    .map(Integer::valueOf)
                    .filter(targetScores::contains)
                    .forEach(s -> score.addAndGet(15));
        }
    }

    private void traderLevelCalc(AtomicInteger score, Integer traderId) {
        if (Objects.nonNull(traderId)) {
            Integer traderScore = traderCustomerBaseService.getTraderScoreByTraderId(traderId);
            if (Objects.nonNull(traderScore)) {
                score.addAndGet(traderScore);
            }
        }
    }

    @Override
    public BusinessChanceDto selectOne(BusinessChanceDto businessChanceDto) {

        BussinessChanceEntity bussinessChanceEntity = bussinessChanceMapper.selectByPrimaryKey(businessChanceDto.getBussinessChanceId());
        if (Objects.isNull(bussinessChanceEntity)){
            log.info("未查询到商机，商机id：{}",businessChanceDto.getBussinessChanceId());
            return null;
        }
        BusinessChanceDto result = businessChanceConvertor.toDto(bussinessChanceEntity);
        result.setCommunicateRecordDto(new CommunicateRecordDto());
        if (Objects.nonNull(result.getUserId()) && result.getUserId() > 0) {
            UserDto userDto = userApiService.getUserById(result.getUserId());
            result.setUsername(userDto.getUsername());
        }
        return result;
    }

    @Override
    public BusinessChanceDto findBussinessByBusinessChanceNo(String businessChanceNo){
        BussinessChanceEntity bussinessChanceEntity = bussinessChanceMapper.findBussinessByBusinessChanceNo(businessChanceNo);
        if(bussinessChanceEntity == null){
            return null;
        }
        return businessChanceConvertor.toDto(bussinessChanceEntity);
    }


    @Override
    public BusinessChanceDto viewDetail(Integer businessChanceId) {
        CurrentUser currentUser = getCurrentUser();
        BusinessChanceDto businessChanceDto = bussinessChanceMapper.getDetailById(businessChanceId, currentUser.getId());
        if (Objects.nonNull(businessChanceDto.getOrderTime()) && businessChanceDto.getOrderTime() > 0) {
            businessChanceDto.setOrderTimeDate(new Date(businessChanceDto.getOrderTime()));
        }
        businessChanceDto.setAttentionState(Objects.nonNull(businessChanceDto.getCustomDataOperDto()) ? ErpConstant.ONE : ErpConstant.ZERO);
        businessChanceDto.setStatusCommentsStr(sysOptionDefinitionApiService.getOptionDefinitionById(businessChanceDto.getStatusComments()).getTitle());
        businessChanceDto.setCurrentUserId(currentUser.getId());
//        if (Objects.nonNull(businessChanceDto.getAmount())) {
//            businessChanceDto.setAmount(businessChanceDto.getAmount().divide(ErpConstant.TEN_THOUSAND));
//        }
        businessChanceDto.setRelatedOrderDtoList(new ArrayList<>());
        // 客情关系
        if (StrUtil.isNotBlank(businessChanceDto.getCustomerRelationship())) {
            String[] tagArr = businessChanceDto.getCustomerRelationship().split(",");
            List<Integer> ids = Arrays.stream(tagArr).map(Integer::valueOf).collect(Collectors.toList());
            List<String> customerRelationshipStr = new ArrayList<>();
            for (Integer id : ids) {
                if (ErpConstant.ONE.equals(id)) {
                    customerRelationshipStr.add("决策人");
                }
                if (ErpConstant.TWO.equals(id)) {
                    customerRelationshipStr.add("使用人");
                }
            }
            businessChanceDto.setCustomerRelationshipStr(customerRelationshipStr);
        }
        if (StrUtil.isNotBlank(businessChanceDto.getTraderName())) {
            TraderInfoTyc tycInfoQuery = new TraderInfoTyc();
            tycInfoQuery.setName(businessChanceDto.getTraderName());
            TraderInfoTyc traderInfoTyc = tycMapper.getTraderInfoTycByTraderName(tycInfoQuery);
            businessChanceDto.setTycFlag(Objects.nonNull(traderInfoTyc) ? "Y" : "N");
        }
        // 关联商机
        BusinessLeadsDto businessLeadsDto = businessLeadsService.findByBusinessChanceId(businessChanceId);
        if (Objects.nonNull(businessLeadsDto)) {
            RelatedOrderDto relatedOrderDto = new RelatedOrderDto();
            relatedOrderDto.setType("线索");
            relatedOrderDto.setRelatedId(businessLeadsDto.getId());
            relatedOrderDto.setRelatedNo(businessLeadsDto.getLeadsNo());
            relatedOrderDto.setAddTimeDate(businessLeadsDto.getAddTime());
            if (Objects.nonNull(businessLeadsDto.getBelongerId())) {
                UserDto belongUser = userApiService.getUserBaseInfo(businessLeadsDto.getBelongerId());
                relatedOrderDto.setBelongUserId(businessLeadsDto.getBelongerId());
                relatedOrderDto.setBelongUserName(belongUser.getUsername());
                relatedOrderDto.setBelongUserPic(belongUser.getAliasHeadPicture());
            }
            try {
                String targetTabUrl = ("/crm/businessLeads/profile/detail?id=" + businessLeadsDto.getId());
                relatedOrderDto.setJumpErpInnerUrl(lxcrmUrl+targetTabUrl);
                relatedOrderDto.setJumpErpUrl(lxcrmUrl+targetTabUrl);
//                if (crmJumpErpUrl.indexOf("sso") > -1) {
//                    String encodetargetTabUrl = URLEncoder.encode("/index.do?target=" + URLEncoder.encode(lxcrmUrl + targetTabUrl, "UTF-8") + "&title=", "UTF-8");
//                    relatedOrderDto.setJumpErpUrl(crmJumpErpUrl + encodetargetTabUrl);
//                } else {
//                    String encodetargetTabUrl = "/index.do?target=" + URLEncoder.encode(lxcrmUrl + targetTabUrl, "UTF-8") + "&title=" + URLEncoder.encode("线索详情", "UTF-8");
//                    relatedOrderDto.setJumpErpUrl(crmJumpErpUrl + encodetargetTabUrl);
//                }
            } catch (Exception e) {
                log.error("商机详情的关联线索链接转换给前端时失败，需要检查", e);
            }
            businessChanceDto.getRelatedOrderDtoList().add(relatedOrderDto);
        }

        // 处理标签
        if (StrUtil.isNotBlank(businessChanceDto.getTagIds())) {
            String[] tagArr = businessChanceDto.getTagIds().split(",");
            List<Integer> ids = Arrays.stream(tagArr).map(Integer::valueOf).collect(Collectors.toList());
            List<CustomTagDto> tags = customTagApiService.getByIdList(ids);
            businessChanceDto.setTags(tags);
        }
        // 处理省市区信息
        terminalTraderRegion(businessChanceDto);
        // 处理客户跳转链接
        getTraderLink(businessChanceDto);

        return businessChanceDto;
    }

    private void getTraderLink(BusinessChanceDto businessChanceDto) {
        if (Objects.nonNull(businessChanceDto.getTraderId()) && businessChanceDto.getTraderId() > 0) {
            try {
                String targetTabUrl = ("/trader/customer/new/portrait.do?traderId=" + businessChanceDto.getTraderId() + "&traderCustomerId=" + businessChanceDto.getTraderCustomerId() + "&customerNature=" + businessChanceDto.getCustomerNature());
                businessChanceDto.setTraderNameInnerLink(targetTabUrl);
                if (crmJumpErpUrl.indexOf("sso") > -1) {
                    String encodetargetTabUrl = URLEncoder.encode("/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=客户360", "UTF-8");
                    businessChanceDto.setTraderNameLink(crmJumpErpUrl + encodetargetTabUrl);
                } else {
                    String encodetargetTabUrl = "/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=" + URLEncoder.encode("客户360", "UTF-8");
                    businessChanceDto.setTraderNameLink(crmJumpErpUrl + encodetargetTabUrl);
                }
            } catch (Exception e) {
                log.error("客户链接转换给前端时失败，需要检查", e);
            }
        }
    }

    private void terminalTraderRegion(BusinessChanceDto businessChanceDto) {
        String terminalTraderRegion = businessChanceDto.getTerminalTraderRegion();
        if (StrUtil.isNotBlank(terminalTraderRegion)) {
            String[] regionArr = terminalTraderRegion.split(",");
            String minRegion = regionArr[regionArr.length - 1].trim();
            if (StrUtil.isNotBlank(minRegion)) {
                businessChanceDto.setTerminalTraderRegionStr(regionApi.getThisRegionToParentRegion(Integer.valueOf(minRegion)).replace("中国", ""));
            }
        }
    }
    @Override
    public List<UserDto> getBussinessChanceBelongers(String name){
        CurrentUser currentUser = getCurrentUser();
        List<Integer> accessAll = Arrays.stream(accessAllBusinessChance.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        if (accessAll.contains(currentUser.getId())) {
            return findAllBelongUser(name);
        } else {
            List<UserDto> userDtoList = userApiService.queryUserSubFromUac(currentUser.getId());
            List<Integer> allSubordinateUserIdList = userDtoList.stream().map(UserDto::getUserId).collect(Collectors.toList());//全部的下属ID
            if(!allSubordinateUserIdList.contains(currentUser.getId())){
                allSubordinateUserIdList.add(currentUser.getId());
            }
            List<Integer> shareTag1 = bussinessChanceMapper.findShareTag1(currentUser.getId());
            if(shareTag1.size()>0){
                allSubordinateUserIdList.addAll(shareTag1);
            }
            List<Integer> belongderIds = bussinessChanceMapper.findAllBelongUserForBussinessStep1(allSubordinateUserIdList);
            List<Integer> shardIds = bussinessChanceMapper.findAllBelongUserForBussinessStep2(currentUser.getId());
            List<Integer> finalBelongderIds = new ArrayList<>();
            if(belongderIds.size()>0){
                finalBelongderIds.addAll(belongderIds);
            }
            if(shardIds.size()>0){
                finalBelongderIds.addAll(shardIds);
            }
            return bussinessChanceMapper.findAllBelongUserForBussinessStep3(name,finalBelongderIds);
//            return bussinessChanceMapper.findAllBelongUserForBussiness(name,allSubordinateUserIdList, currentUser.getId());
        }
    }

    @Override
    public List<UserDto> getBussinessChanceCreators(String name){
        CurrentUser currentUser = getCurrentUser();
        List<Integer> accessAll = Arrays.stream(accessAllBusinessChance.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        if (accessAll.contains(currentUser.getId())) {
            return findAllBelongUser(name);
        } else {
            List<UserDto> userDtoList = userApiService.queryUserSubFromUac(currentUser.getId());
            List<Integer> allSubordinateUserIdList = userDtoList.stream().map(UserDto::getUserId).collect(Collectors.toList());//全部的下属ID
            if(!allSubordinateUserIdList.contains(currentUser.getId())){
                allSubordinateUserIdList.add(currentUser.getId());
            }
            List<Integer> shareTag2 = bussinessChanceMapper.findShareTag2(currentUser.getId());
            if(shareTag2.size()>0){
                allSubordinateUserIdList.addAll(shareTag2);
            }
            List<Integer> belongderIds = bussinessChanceMapper.findAllCreatorForBussinessStep1(allSubordinateUserIdList);
            List<Integer> shardIds = bussinessChanceMapper.findAllCreatorForBussinessStep2(currentUser.getId());
            List<Integer> finalBelongderIds = new ArrayList<>();
            if(belongderIds.size()>0){
                finalBelongderIds.addAll(belongderIds);
            }
            if(shardIds.size()>0){
                finalBelongderIds.addAll(shardIds);
            }
            return bussinessChanceMapper.findAllCreatorForBussinessStep3(name,finalBelongderIds);
//            return bussinessChanceMapper.findAllBelongUserForBussiness(name,allSubordinateUserIdList, currentUser.getId());
        }
    }

    @Override
    public PageInfo<BusinessChanceDto> page(PageParam<BusinessChanceDto> businessChanceDto) {
        log.info("CRM商机，分页列表入参，param：{}", JSON.toJSONString(businessChanceDto));
        // 查询参数封装
        BusinessChanceDto pageParam = getPageParam(businessChanceDto);
        //CRM-834 需求开发
        if(CollectionUtils.isNotEmpty(pageParam.getOrganizationIdList())){
        	List<Integer> belongOrgUserIdList = getUserIdByOrgParamList(pageParam.getOrganizationIdList());
        	if(CollectionUtils.isNotEmpty(belongOrgUserIdList)) {
        		pageParam.setBelongOrgUserIdList(belongOrgUserIdList);
        	}
        }
        // 获取分页结果
        PageInfo<BusinessChanceDto> pageInfo = PageHelper.startPage(businessChanceDto)
                .doSelectPageInfo(() -> bussinessChanceMapper.findByAll(pageParam));

        List<BusinessChanceDto> all = pageInfo.getList();

        // 只有在未指定完整时间范围查询时，才需要补充最新沟通记录信息
        if (pageParam.getLatestFollowUpTimeStartStr() == null || pageParam.getLatestFollowUpTimeEndStr() == null) {
            // 填充最新沟通记录信息
            fillLatestCommunicationInfo(all);
        }

        List<CustomTagDto> customTagDtoList = customTagApiService.getAllTags(CustomDataOperBizTypeEnums.BUSINESS_CHANCE.getType());
        List<Integer> currentUserExistTagIds = customTagDtoList.stream().map(CustomTagDto::getId).collect(Collectors.toList());

        for (BusinessChanceDto item : all) {
            getPageResult(item, currentUserExistTagIds, customTagDtoList);
        }

        return pageInfo;
    }

    //根据部门ID获取部门下的所有用户ID列表
    private List<Integer> getUserIdByOrgParamList(List<Integer> organizationIdList) {
    	List<Integer> belongOrgUserIdList = new ArrayList<>();
    	RestfulResult<List<UserInfoDto>> result = uacWxUserInfoApiService.getUserListByDepartmentIds(organizationIdList);
        if (result.isSuccess()) {
        	List<UserInfoDto> userInfoDtoList = result.getData();
        	belongOrgUserIdList = userInfoDtoList.stream().map(UserInfoDto::getId).distinct().collect(Collectors.toList()); 
        }
		return belongOrgUserIdList;
	}

	private void getPageResult(BusinessChanceDto item, List<Integer> currentUserExistTagIds, List<CustomTagDto> customTagDtoList) {
        item.setBusinessTypeStr(BusinessTypeEnum.getTitleByCode(item.getBusinessType()));
        item.setSystemBusinessLevelStr(BusinessChanceLevelEnum.getTitleByCode(item.getSystemBusinessLevel()));
        item.setTerminalTraderNatureStr(TerminalTraderNatureEnum.getTitleByCode(item.getTerminalTraderNature()));
        item.setPurchasingTypeStr(PurchasingTypeEnum.getTitleByCode(item.getPurchasingType()));
        item.setBiddingPhaseStr(BiddingPhaseEnum.getTitleByCode(item.getBiddingPhase()));
        item.setTypeStr(BusinessChanceTypeEnum.getTitleByCode(item.getType()));
        item.setStageStr(BusinessChanceStageEnum.getDescByCode(item.getStage()));

        if (Objects.nonNull(item.getAddTime())) {
            item.setAddTimeDate(new Date(item.getAddTime()));
        }
        if (Objects.nonNull(item.getOrderTime()) && item.getOrderTime() > 0) {
            item.setOrderTimeDate(new Date(item.getOrderTime()));
        }
        item.setAttentionState(Objects.nonNull(item.getCustomDataOperDto()) ? ErpConstant.ONE : ErpConstant.ZERO);
        // 处理省市区信息
        terminalTraderRegion(item);
        // 处理标签
        if (StrUtil.isNotBlank(item.getTagIds())) {
            String[] tagArr = item.getTagIds().split(",");
            List<Integer> ids = Arrays.stream(tagArr).map(Integer::valueOf).collect(Collectors.toList());
            //取集合的交集
            item.setTagIdList((List<Integer>) CollUtil.intersection(currentUserExistTagIds, ids));
            List<CustomTagDto> tags = customTagDtoList.stream().filter(t -> item.getTagIdList().contains(t.getId())).collect(Collectors.toList());
            item.setTags(tags);
        }
        // 处理客户跳转链接
        getTraderLink(item);
    }

    private BusinessChanceDto getPageParam(PageParam<BusinessChanceDto> businessChanceDto) {
        CurrentUser currentUser = getCurrentUser();
        List<Integer> accessAll = Arrays.stream(accessAllBusinessChance.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        BusinessChanceDto param = businessChanceDto.getParam();
        if (Objects.isNull(param)) {
            businessChanceDto.setParam(new BusinessChanceDto());
        }
        BusinessChanceDto pageParam = businessChanceDto.getParam();
        pageParam.setLimitStart((long) (businessChanceDto.getPageNum() - 1) * businessChanceDto.getPageSize());
        pageParam.setLimitEnd(Long.valueOf(businessChanceDto.getPageSize()));
        if (accessAll.contains(currentUser.getId())) {
            pageParam.setQueryAll(ErpConstant.ONE);
            pageParam.setCurrentUserId(currentUser.getId());
        } else {
            List<UserDto> userDtoList = userApiService.queryUserSubFromUac(currentUser.getId());
            List<Integer> allSubordinateUserIdList = userDtoList.stream().map(UserDto::getUserId).collect(Collectors.toList());
            pageParam.setUserIdList(allSubordinateUserIdList);
            pageParam.setQueryAll(ErpConstant.ZERO);
            pageParam.setCurrentUserId(currentUser.getId());
        }

        if (Objects.nonNull(pageParam.getLatestFollowUpTimeStartStr())) {
            pageParam.setLatestFollowUpTimeStart(pageParam.getLatestFollowUpTimeStartStr().getTime());
        }
        if (Objects.nonNull(pageParam.getLatestFollowUpTimeEndStr())) {
            pageParam.setLatestFollowUpTimeEnd(pageParam.getLatestFollowUpTimeEndStr().getTime());
        }
        if (Objects.nonNull(pageParam.getAddTimeStartStr())) {
            pageParam.setAddTimeStart(pageParam.getAddTimeStartStr().getTime());
        }
        if (Objects.nonNull(pageParam.getAddTimeEndStr())) {
            pageParam.setAddTimeEnd(pageParam.getAddTimeEndStr().getTime());
        }
        if (Objects.nonNull(pageParam.getOrderTimeStartStr())) {
            pageParam.setOrderTimeStart(pageParam.getOrderTimeStartStr().getTime());
        }
        if (Objects.nonNull(pageParam.getOrderTimeEndStr())) {
            pageParam.setOrderTimeEnd(pageParam.getOrderTimeEndStr().getTime());
        }
        if (CollUtil.isNotEmpty(pageParam.getProvinceIdList())) {
            pageParam.setProvinceIdListStr(pageParam.getProvinceIdList().stream().map(String::valueOf).collect(Collectors.toSet()));
        }
        if (CollUtil.isNotEmpty(pageParam.getCityIdList())) {
            pageParam.setCityIdListStr(pageParam.getCityIdList().stream().map(String::valueOf).collect(Collectors.toSet()));
        }
        if (CollUtil.isNotEmpty(pageParam.getCountyIdList())) {
            pageParam.setCountyIdListStr(pageParam.getCountyIdList().stream().map(String::valueOf).collect(Collectors.toSet()));
        }
        return pageParam;
    }

    private void fillLatestCommunicationInfo(List<BusinessChanceDto> businessChanceDtoList) {
        if (CollUtil.isEmpty(businessChanceDtoList)) {
            return;
        }
        
        List<Integer> businessChanceIds = businessChanceDtoList.stream()
                .map(BusinessChanceDto::getBussinessChanceId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
                
        if (CollUtil.isNotEmpty(businessChanceIds)) {
            List<BusinessChanceDto> latestCommunicationList = bussinessChanceMapper.findLatestCommuncationByAllIn(businessChanceIds);
            
            Map<Integer, BusinessChanceDto> latestCommunicationMap = latestCommunicationList.stream()
                    .collect(Collectors.toMap(
                            BusinessChanceDto::getBussinessChanceId,
                            Function.identity(),
                            (existing, replacement) -> replacement
                    ));
                    
            businessChanceDtoList.forEach(dto -> {
                BusinessChanceDto latestInfo = latestCommunicationMap.get(dto.getBussinessChanceId());
                if (latestInfo != null) {
                    dto.setLatestCommunicateRecordContent(latestInfo.getLatestCommunicateRecordContent());
                    dto.setLatestCommunicateRecordId(latestInfo.getLatestCommunicateRecordId());
                }
            });
        }
    }

    @Override
    @Transactional
    public List<Integer> importExcel(MultipartFile file) throws IOException {
        List<BussinessChanceEntity> businessChanceEntities = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), BusinessChanceDto.class, new PageReadListener<BusinessChanceDto>(dataList ->
                dataList.forEach(data ->
                {
                    businessChanceOfSaleOrLeadsBaseInfoSet(data);
                    // 赋值归属
                    businessChanceSetUserId(data);
//                    confirmTraderData(data);
                    calc(data);
                    baseDataSet(data);
                    businessChanceEntities.add(businessChanceConvertor.toEntity(data));
                }))).sheet().doRead();
        // 清除上一次导入记录
        CustomDataLogDto lastEfficientCustomDataLog = this.getLastEfficientCustomDataLog();
        String[] ids = null;
        if (Objects.nonNull(lastEfficientCustomDataLog) && !StrUtil.isEmpty(lastEfficientCustomDataLog.getRelatedIds())) {
            ids = lastEfficientCustomDataLog.getRelatedIds().split(StrUtil.COMMA);
        }
        if (!CollUtil.isEmpty(businessChanceEntities)) {
            businessChanceEntities.forEach(a -> {
                ValidatorUtils.validate(a, AddGroup.class);
            });

            businessChanceEntities.forEach(c -> {
                bussinessChanceMapper.insertSelective(c);
                String code = generateBusinessNo(c.getBussinessChanceId());
                BussinessChanceEntity toNo = new BussinessChanceEntity();
                toNo.setBussinessChanceId(c.getBussinessChanceId());
                toNo.setBussinessChanceNo(code);
                bussinessChanceMapper.updateByPrimaryKeySelective(toNo);
            });
        } else {
            throw new ServiceException("上传文件为空");
        }
        if (ArrayUtil.isNotEmpty(ids)) {
            Arrays.stream(ids).map(Integer::valueOf).forEach(id -> bussinessChanceMapper.deleteByPrimaryKey(id));
        }

        return businessChanceEntities.stream().map(BussinessChanceEntity::getBussinessChanceId).collect(Collectors.toList());
    }


    /**
     * 获取最后一次有效操作日志记录
     *
     * @return CustomDataLogDto
     */
    private CustomDataLogDto getLastEfficientCustomDataLog() {
        CurrentUser currentUser = getCurrentUser();
        CustomDataLogDto customDataLogDto = CustomDataLogDto.builder()
                .belongerId(currentUser.getId())
                .successFlag(true)
                .saveType(CustomDataLogSaveTypeEnums.EXCEL_IMPORT.getType())
                .type(CustomDataOperBizTypeEnums.BUSINESS_CHANCE.getType())
                .build();
        return customDataLogApiService.getByAllOrderByAddTime(customDataLogDto);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getCloseVerifyInfo(Integer businessChanceId,CurrentUser currentUser) {
//        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "closeBussinesschanceVerify_" + businessChanceId);
//        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>) historicInfo.get("historicActivityInstance");
//        Task taskInfo = (Task) historicInfo.get("taskInfo");
//
//        String endStatus = (String) historicInfo.get("endStatus");
//        boolean endStatusFlag = true;
//        if ("主管审核".equals(endStatus)) {
//            endStatusFlag = false;
//        }
//
//        String verifyUsers = null;
//        if (null != taskInfo) {
//            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfo);
//            verifyUsers = (String) taskInfoVariables.get("verifyUsers");
//        }
//        Map<String, Object> commentMap = (Map<String, Object>) historicInfo.get("commentMap");
//        boolean currentUserAudit = StringUtil.isBlank(verifyUsers) ? false : verifyUsers.toLowerCase().contains(currentUser.getUsername().toLowerCase());
//        // 组装审核记录各个节点
//        List<Map<String, Object>> result = new ArrayList<>();
//        for (int i = 0; i < historicActivityInstance.size(); i++) {
//            HistoricActivityInstance temp = historicActivityInstance.get(i);
//            if (temp.getActivityName() != null) {
//                Map<String, Object> node = new HashMap<>(10);
//                if ("startEvent".equals(temp.getActivityType())) {
//                    node.put("operator", historicInfo.get("startUser"));
//                    node.put("operationalMatters", "开始");
//                } else if ("intermediateThrowEvent".equals(temp.getActivityType())) {
//                    node.put("operator", "");
//                    node.put("operationalMatters", "结束");
//                } else {
//                    if (historicActivityInstance.size() == i + 1) {
//                        node.put("operator", verifyUsers);
//                    } else {
//                        node.put("operator", temp.getAssignee());
//                    }
//                    node.put("operationalMatters", temp.getActivityName());
//                }
//                node.put("operateTime", ErpDateUtils.format(temp.getEndTime(), PatternEnum.YYYY_MM_DD_HH_MM_SS));
//                node.put("comments", commentMap.get(temp.getTaskId()));
//                node.put("taskId", taskInfo == null ? "0" : taskInfo.getId());
//                node.put("endStatusFlag", endStatusFlag);
//                node.put("currentUserAudit",currentUserAudit);
//                result.add(node);
//            }
//        }
//        return result;
        return new ArrayList<>();
    }

    @Override
    public List<MergeChanceGoods> getChancesByNoAfterMerged(String businessChanceNo) {

        return bussinessChanceMapper.getChancesByNoAfterMerged(businessChanceNo);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateSingleData(BusinessChanceDto businessChanceDto) {
        log.info("CRM商机,列表页单行编辑,入参:{}", JSON.toJSONString(businessChanceDto));
        if (Objects.nonNull(businessChanceDto.getOrderTimeDate())) {
            businessChanceDto.setOrderTime(businessChanceDto.getOrderTimeDate().getTime());
        }
//        if (Objects.nonNull(businessChanceDto.getAmount())) {
//            businessChanceDto.setAmount(businessChanceDto.getAmount().multiply(ErpConstant.TEN_THOUSAND));
//        }
        BussinessChanceEntity bussinessChanceEntity = businessChanceConvertor.toEntity(businessChanceDto);
        bussinessChanceMapper.updateByPrimaryKeySelective(bussinessChanceEntity);
        if (StrUtil.isNotBlank(businessChanceDto.getTerminalTraderName()) || Objects.nonNull(businessChanceDto.getTerminalTraderNature())) {
            // 更新商机阶段（商机验证）
            updateStageAndStageTime(businessChanceDto.getBussinessChanceId(), BusinessChanceStageEnum.OPPORTUNITY_VERIFICATION);
        }
    }

    @Override
    public RestfulResult<List<BusinessCluesDetailResultDTO>> getBidInfoByTraderId(Integer traderId) {

        RestfulResult<List<BusinessCluesDetailDTO>> result = crmTraderApiService.getBidInfoByTraderId(traderId);

        List<BusinessCluesDetailResultDTO> businessCluesDetailResultDTOList = new ArrayList<>();

        if ("success".equals(result.getCode()) && CollUtil.isNotEmpty(result.getData())) {
            List<BusinessCluesDetailDTO> data = result.getData();

            for (BusinessCluesDetailDTO detailDTO : data) {
                BusinessCluesDetailResultDTO businessCluesDetailResultDTO = new BusinessCluesDetailResultDTO();

                businessCluesDetailResultDTO.setInfoId(detailDTO.getInfoId());

                String areaStr = "{}{}{}";
                String province = StrUtil.isBlank(detailDTO.getZhaobiaoProvinceName()) ? null : StrUtil.DASHED + detailDTO.getZhaobiaoProvinceName();
                String city = StrUtil.isBlank(detailDTO.getZhaobiaoCityName()) ? null : StrUtil.DASHED + detailDTO.getZhaobiaoCityName();
                String county = StrUtil.isBlank(detailDTO.getZhaobiaoCountryName()) ? null : StrUtil.DASHED + detailDTO.getZhaobiaoCountryName();
                businessCluesDetailResultDTO.setZhaobiaoArea(StrUtil.format(areaStr, province, city, county));

                businessCluesDetailResultDTO.setZhaobiaoUnit(detailDTO.getZhaobiaoUnit());
                businessCluesDetailResultDTO.setZhaobiaoXingzhi(detailDTO.getZhaobiaoXingzhi());
                businessCluesDetailResultDTO.setZhaobiaoLevel(detailDTO.getZhaobiaoLevel());
                businessCluesDetailResultDTO.setZhaobiaoType(detailDTO.getZhaobiaoType());

                if (CollUtil.isNotEmpty(detailDTO.getZhongbiaoGoodsList())) {
                    businessCluesDetailResultDTO.setZhongbiaoGoods(detailDTO.getZhongbiaoGoodsList().get(0).getZhongbiaoGoods());
                    businessCluesDetailResultDTO.setZhongbiaoBrand(detailDTO.getZhongbiaoGoodsList().get(0).getZhongbiaoBrand());
                    businessCluesDetailResultDTO.setZhongbiaoModel(detailDTO.getZhongbiaoGoodsList().get(0).getZhongbiaoModel());
                    if (detailDTO.getZhongbiaoGoodsList().size() > 1) {
                        List<BusinessCluesDetailResultDTO> businessCluesDetailResultGoods = new ArrayList<>();
                        for (int i = 1; i < detailDTO.getZhongbiaoGoodsList().size(); i++) {
                            BusinessCluesDetailResultDTO businessCluesDetailResultGoodsDTO = new BusinessCluesDetailResultDTO();
                            businessCluesDetailResultGoodsDTO.setInfoId(detailDTO.getInfoId());
                            businessCluesDetailResultGoodsDTO.setZhongbiaoGoods(detailDTO.getZhongbiaoGoodsList().get(i).getZhongbiaoGoods());
                            businessCluesDetailResultGoodsDTO.setZhongbiaoBrand(detailDTO.getZhongbiaoGoodsList().get(i).getZhongbiaoBrand());
                            businessCluesDetailResultGoodsDTO.setZhongbiaoModel(detailDTO.getZhongbiaoGoodsList().get(i).getZhongbiaoModel());
                            businessCluesDetailResultGoods.add(businessCluesDetailResultGoodsDTO);
                        }
                        businessCluesDetailResultDTO.setOtherGoodsInfo(businessCluesDetailResultGoods);
                    }
                }
                businessCluesDetailResultDTOList.add(businessCluesDetailResultDTO);
            }
        }

        return new RestfulResult<>(businessCluesDetailResultDTOList);
    }

    @Override
    public void updateBusinessChanceStatusByCommunication(Integer bussinessChanceId) {
        log.info("商机库新增沟通记录修改商机状态，商机id：{}", bussinessChanceId);
        if (Objects.isNull(bussinessChanceId)) {
            return;
        }
        BussinessChanceEntity bussinessChanceEntity = bussinessChanceMapper.selectByPrimaryKey(bussinessChanceId);
        if (Objects.isNull(bussinessChanceEntity)) {
            return;
        }

        if (bussinessChanceEntity.getStatus() != null && bussinessChanceEntity.getStatus() == 0) {
            CommunicateRecordDto query = new CommunicateRecordDto();
            query.setRelatedId(bussinessChanceId);
            query.setCommunicateType(244);
//            Integer integer = communicateRecordService.selectByRelatedIdAndCommunicateType(query);
//            if (!Objects.isNull(integer)) {
//                BussinessChanceEntity update = new BussinessChanceEntity();
//                update.setBussinessChanceId(bussinessChanceEntity.getBussinessChanceId());
//                update.setStatus(6);
//                bussinessChanceMapper.updateByPrimaryKeySelective(update);
//            }
        }


    }

    @Override
    public void updateSupervisorGuidance(BusinessChanceDto businessChanceDto) {
//        Integer bussinessChanceId = businessChanceDto.getBussinessChanceId();
//        log.info("商机库新增主管指导，商机id：{}", bussinessChanceId);
//        if (Objects.isNull(bussinessChanceId)) {
//            return;
//        }
//        BussinessChanceEntity bussinessChanceEntity = bussinessChanceMapper.selectByPrimaryKey(bussinessChanceId);
//        String preSupervisorGuidance = bussinessChanceEntity.getSupervisorGuidance();
//        if (Objects.isNull(bussinessChanceEntity)) {
//            return;
//        }
//        if (businessChanceDto.getSupervisorGuidance() != null&&StrUtil.isNotBlank(businessChanceDto.getSupervisorGuidance())){
//            BussinessChanceEntity update = new BussinessChanceEntity();
//            update.setBussinessChanceId(bussinessChanceEntity.getBussinessChanceId());
//            update.setSupervisorGuidance(businessChanceDto.getSupervisorGuidance());
//            // TO DB
//            bussinessChanceMapper.updateByPrimaryKeySelective(update);
//            //发送主管指导消息，如果数据和之前相同则不发送
//            sendSuperGuidance(businessChanceDto, preSupervisorGuidance, bussinessChanceEntity);
//        }
    }

    @Override
    public List<BusinessChanceDto> getAccuracyEnum() {
        List<BusinessChanceDto> list = new ArrayList<>();
//        for (BusinessChanceAccuracyEnum b : BusinessChanceAccuracyEnum.values()) {
//            BusinessChanceDto businessChanceDto = new BusinessChanceDto();
//            businessChanceDto.setBusinessChanceAccuracy(b.getCode());
//            businessChanceDto.setBusinessChanceAccuracyShow(b.getBusinessChanceAccuracy());
//            list.add(businessChanceDto);
//        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public SmartQuoteResultDto smartQuoteAdd(SmartQuoteDto smartQuoteDto) {

        log.info("smartQuoteAdd 入参：{}",JSON.toJSONString(smartQuoteDto));
        SmartQuoteResultDto resultDto = new SmartQuoteResultDto();

        boolean paramError = Objects.isNull(smartQuoteDto.getTraderId()) || Objects.isNull(smartQuoteDto.getUserId()) ||
                CollUtil.isEmpty(smartQuoteDto.getGoodsList());

        if (paramError) {
            throw new ServiceException("参数不可为空");
        }

        BusinessChanceDto data = smartQuoteConvertBusinessChance(smartQuoteDto);
        // 转换
        BussinessChanceEntity bussinessChanceEntity = businessChanceConvertor.toEntity(data);
        // TO DB
        bussinessChanceMapper.insertSelective(bussinessChanceEntity);
        data.setBussinessChanceId(bussinessChanceEntity.getBussinessChanceId());
        publicCustomerRecordApiService.unLockTrader(bussinessChanceEntity.getTraderId(), bussinessChanceEntity.getBussinessChanceId(), 1, data.getUserId());

        // 商机编号
        String code = generateBusinessNo(data.getBussinessChanceId());
        BussinessChanceEntity toNo = new BussinessChanceEntity();
        toNo.setBussinessChanceId(data.getBussinessChanceId());
        toNo.setBussinessChanceNo(code);
        bussinessChanceMapper.updateByPrimaryKeySelective(toNo);
        data.setBussinessChanceNo(code);
        resultDto.setBussinessChanceNo(data.getBussinessChanceNo());

        CommunicateRecordDto communicateRecordDto = new CommunicateRecordDto();
        communicateRecordDto.setCommunicateType(CommunicateRecordTypeEnum.BUSINESS_CHANCE.getCode());
        communicateRecordDto.setRelatedId(data.getBussinessChanceId());
        communicateRecordDto.setBegintime(System.currentTimeMillis());
        communicateRecordDto.setContact(data.getTraderContactName());
        communicateRecordDto.setContentSuffix(StrUtil.format("贝壳助理{}商品询价",data.getContent()));
        communicateRecordDto.setContactMob(data.getMobile());
        communicateRecordDto.setNoneNextDate(ErpConst.ONE);
        data.setCommunicateRecordDto(communicateRecordDto);
        communicateRecordDto.setIsLfasr(0);
        communicateRecordDto.setCompanyId(1);
        // 保存沟通记录
        communicateRecordApiService.add(communicateRecordDto);


//        if (ErpConst.ONE.equals(data.getStatus())) {
//            // 报价
//            QuoteorderDto quoteorderDto = this.convertQuoteOrder(data);
//            // 报价保存
//            quoteOrderService.saveQuoteOrder(quoteorderDto);
//            resultDto.setQuoteorderNo(quoteorderDto.getQuoteorderNo());
//            resultDto.setQuoteOrderId(quoteorderDto.getQuoteorderId());
//            resultDto.setCanPdf(true);
//        }

        return resultDto;
    }

    @Override
    public String getQuotePdf(Integer quoteOrderId,String quoteOrderNo) {
        // 转pdf
        String contractTemplateUrl = erpDomain + PRINT_CONTRACT_URL + quoteOrderId;
        String html2PdfUrl = html2PdfDomain + RENDER_URL;

        com.vedeng.infrastructure.oss.service.domain.UrlToPdfParam urlToPdfParam = new com.vedeng.infrastructure.oss.service.domain.UrlToPdfParam();
        urlToPdfParam.setUrl(contractTemplateUrl);
        com.vedeng.infrastructure.oss.service.domain.UrlToPdfParam.Pdf pdf = new com.vedeng.infrastructure.oss.service.domain.UrlToPdfParam.Pdf();
        com.vedeng.infrastructure.oss.service.domain.UrlToPdfParam.Pdf.PdfMargin margin = new com.vedeng.infrastructure.oss.service.domain.UrlToPdfParam.Pdf.PdfMargin("1cm" , "1cm" , "1cm" , "0cm");
        pdf.setMargin(margin);
        pdf.setScale(1.5);
        urlToPdfParam.setPdf(pdf);
        // 链接
        String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf", "报价单" + quoteOrderNo, urlToPdfParam);
        log.info("智能询价，报价单id[{}]，报价单号[{}]，合同url[{}]", quoteOrderId, quoteOrderNo, ossUrl);
        if (StrUtil.isEmpty(ossUrl)){
            return erpDomain + PRINT_CONTRACT_URL_ERROR + quoteOrderId;
        }
        return ossUrl;
    }

    @Override
    public void shareBusinessChance(RSalesJBusinessOrderDto rSalesJBusinessOrderDto) {
        log.info("分享商机 入参：{}",JSON.toJSONString(rSalesJBusinessOrderDto));
        rSalesJBusinessOrderDto.setBusinessType(ErpConst.ONE);
        rSalesJBusinessOrderMapper.insertSelective(rSalesJBusinessOrderConvertor.toEntity(rSalesJBusinessOrderDto));
    }

    @Override
    public List<RSalesJBusinessOrderDto> getShareBusinessChance(Integer bussinessChanceId) {
        return rSalesJBusinessOrderMapper.findByBusinessId(bussinessChanceId);
    }

    @Override
    public void cancelShareBusinessChance(Integer id) {
        rSalesJBusinessOrderMapper.deleteById(id);
    }

    /**
     * 关注商机
     * @param smartQuoteDto
     * @return
     */
    @Override
    public Boolean attention(List<Integer> ids, CurrentUser currentUser){
        // 关注限制：7 已成单、4 已关闭，无法关注
        for (Integer id : ids) {
            // 判断是否已存在
            try {
                CustomDataOperDto customDataOperDto = CustomDataOperDto.builder()
                        .belongerId(currentUser.getId())
                        .relatedId(id)
                        .bizType(CustomDataOperBizTypeEnums.BUSINESS_CHANCE.getType())
                        .belonger(currentUser.getUsername())
                        .operTime(DateUtil.date())
                        .operType(CustomDataOperTypeEnums.ATTENTION.getType())
                        .build();

                List<CustomDataOperDto> query = customDataOperApiService.query(customDataOperDto);
                if (CollUtil.isNotEmpty(query)){
                    log.info("关注信息已存在:{}",JSON.toJSON(query));
                    continue;
                }
                log.info("关注入参：{}", JSON.toJSONString(customDataOperDto));
                customDataOperDto.setCreator(currentUser.getId());
                customDataOperDto.setUpdater(currentUser.getId());
                customDataOperApiService.save(customDataOperDto);
            }catch (Exception e){
                log.error("关注商机[{}]异常", id, e);
                return Boolean.FALSE;
            }

        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean cancelAttention(Integer id, CurrentUser currentUser) {
        CustomDataOperDto customDataOperDto = CustomDataOperDto.builder()
                .belongerId(currentUser.getId())
                .relatedId(id)
                .bizType(CustomDataOperBizTypeEnums.BUSINESS_CHANCE.getType())
                .belonger(currentUser.getUsername())
                .operType(CustomDataOperTypeEnums.ATTENTION.getType())
                .build();
        customDataOperApiService.delete(customDataOperDto);
        return Boolean.TRUE;
    }

    @Override
    public void closeBusinessAudit(CloseAuditDto closeAuditDto) {
        // 使用eventbus调用
        eventBusCenter.post(closeAuditDto);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void closeBusiness(BusinessCloseDto businessCloseDto) {
        log.info("CRM商机，关闭入参：{}", JSON.toJSONString(businessCloseDto));
        CurrentUser currentUser = getCurrentUser();
        BussinessChanceEntity entity = new BussinessChanceEntity();
        entity.setStatusComments(businessCloseDto.getStatusComments());
        entity.setClosedComments(businessCloseDto.getCloseComments());
        entity.setBussinessChanceId(businessCloseDto.getBusinessChanceId());
        entity.setModTime(DateUtil.current());
        entity.setUpdater(currentUser.getId());
        bussinessChanceMapper.updateByPrimaryKeySelective(entity);
        // 更新商机阶段（关闭）
        updateStageAndStageTime(businessCloseDto.getBusinessChanceId(), BusinessChanceStageEnum.LOSE_ORDER);
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        params.put("userName",currentUser.getUsername());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        logDto.setBizId(businessCloseDto.getBusinessChanceId());
        operationLogApiService.save(logDto, BizLogEnum.BUSINESS_CHANE_CLOSE);


    }

//    @Override
//    public BusinessSupportWorkbenchResponseDto getBusinessSupportWorkbench(User user,BusinessSupportWorkbenchRequestDto requestDto) {
//        BusinessSupportWorkbenchResponseDto responseDto = new BusinessSupportWorkbenchResponseDto();
//        CurrentUser currentUser = getCurrentUser();
//        Integer userId = currentUser.getId();
//        // 咨询待处理
//        Integer countConsultationPending = businessChanceSeekHelpMapper.countConsultationPendingByUserId(userId);
//        responseDto.setConsultationPendingNum(countConsultationPending.toString());
//        // 今日需跟进
//        String currentDate = DateUtil.format(DateUtil.endOfDay(DateUtil.date()), "yyyy-MM-dd HH:mm:ss");
//        Integer countFollowUpToday = businessChanceSupportRecordMapper.countFollowUpTodayByUserIdList(user.getSubUserIdList(), currentDate);
//        responseDto.setFollowUpTodayNum(countFollowUpToday.toString());
//        // 商机转换率
//        BigDecimal businessConversionRate = BigDecimal.ZERO;
//        Integer concernNum = bussinessChanceMapper.getConcernNumByUserList(user.getSubUserIdList(), Collections.emptyList());
//        if (Objects.nonNull(concernNum) && concernNum > 0) {
//            Integer concernOrderNum = bussinessChanceMapper.getConcernNumByUserList(user.getSubUserIdList(), Collections.singletonList(7));
//            if (Objects.nonNull(concernOrderNum)) {
//                businessConversionRate = BigDecimal.valueOf(concernOrderNum)
//                        .multiply(BigDecimal.valueOf(100))
//                        .divide(BigDecimal.valueOf(concernNum), 2, RoundingMode.HALF_UP)
//                        .setScale(2, RoundingMode.HALF_UP);
//            }
//        }
//        responseDto.setBusinessConversionRate(businessConversionRate + "%");
//        // 统计数据
//        BusinessSupportWorkbenchResponseDto.StatisticalData supportedBusinessStatisticalData = new BusinessSupportWorkbenchResponseDto.StatisticalData();
//        supportedBusinessStatisticalData.setTitle("已支持商机数");
//        BusinessSupportWorkbenchResponseDto.StatisticalData estimatedOrderAmountStatisticalData = new BusinessSupportWorkbenchResponseDto.StatisticalData();
//        estimatedOrderAmountStatisticalData.setTitle("预计成单金额");
//        String startTime = "";
//        String endTime = "";
//        if (Objects.nonNull(requestDto) && StrUtil.isNotBlank(requestDto.getTimeFrame())) {
//            startTime = requestDto.getTimeFrame();
//            endTime = DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(startTime)), "yyyy-MM-dd HH:mm:ss");
//        }
//        List<BusinessChanceSupportRecord> statisticalDataList = businessChanceSupportRecordMapper.getStatisticalDataList(userId, startTime, endTime);
//        if (CollUtil.isNotEmpty(statisticalDataList)) {
//            Map<String, List<BusinessChanceSupportRecord>> map = statisticalDataList.stream().collect(Collectors.groupingBy(BusinessChanceSupportRecord::getBusinessLevel));
//            List<BusinessChanceSupportRecord> s = map.get("S");
//            if (CollUtil.isNotEmpty(s)) {
//                supportedBusinessStatisticalData.setSData(String.valueOf(s.size()));
//                estimatedOrderAmountStatisticalData.setSData(String.valueOf(s.stream().map(BusinessChanceSupportRecord::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
//            }
//            List<BusinessChanceSupportRecord> a = map.get("A");
//            if (CollUtil.isNotEmpty(a)) {
//                supportedBusinessStatisticalData.setAData(String.valueOf(a.size()));
//                estimatedOrderAmountStatisticalData.setAData(String.valueOf(a.stream().map(BusinessChanceSupportRecord::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
//            }
//            List<BusinessChanceSupportRecord> b = map.get("B");
//            if (CollUtil.isNotEmpty(b)) {
//                supportedBusinessStatisticalData.setBData(String.valueOf(b.size()));
//                estimatedOrderAmountStatisticalData.setBData(String.valueOf(b.stream().map(BusinessChanceSupportRecord::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
//            }
//            List<BusinessChanceSupportRecord> c = map.get("C");
//            if (CollUtil.isNotEmpty(c)) {
//                supportedBusinessStatisticalData.setCData(String.valueOf(c.size()));
//                estimatedOrderAmountStatisticalData.setCData(String.valueOf(c.stream().map(BusinessChanceSupportRecord::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
//            }
//        }
//        responseDto.setStatisticalDataList(Arrays.asList(supportedBusinessStatisticalData, estimatedOrderAmountStatisticalData));
//        return responseDto;
//    }

    @Override
    public PageInfo<ConsultationPendingResponseDto> getConsultationPendingInfo(PageParam<ConsultationPendingRequestDto> pageParam) {
        log.info("getConsultationPendingInfo:{}", JSON.toJSONString(pageParam));
        CurrentUser currentUser = getCurrentUser();
        ConsultationPendingRequestDto param = pageParam.getParam();
        param.setUserId(currentUser.getId());
        List<String> addTime = param.getAddTime();
        if (CollUtil.isNotEmpty(addTime)) {
            param.setAddTimeStart(addTime.get(0));
            param.setAddTimeEnd(addTime.get(1));
        }
        PageInfo<ConsultationPendingResponseDto> result = PageHelper.startPage(pageParam)
                .doSelectPageInfo(() -> businessChanceSeekHelpMapper.getConsultationPendingInfo(param));
        log.info("getConsultationPendingInfo result:{}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public void processOrCloseSeekHelp(ProcessOrCloseSeekHelpDto requestDto) {
        log.info("processOrCloseSeekHelp:{}", JSON.toJSONString(requestDto));
        businessChanceSeekHelpMapper.updateStatusByBusinessChanceSeekHelpId(requestDto.getStatus(), requestDto.getBusinessChanceSeekHelpId());
        if (ErpConstant.ONE.equals(requestDto.getStatus())) {
            this.attention(Collections.singletonList(requestDto.getBusinessChanceId()), getCurrentUser());
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateStageAndStageTime(Integer businessChanceId, BusinessChanceStageEnum stageEnum) {
        log.info("更新商机阶段,入参businessChanceId:{},stageEnum:{}", businessChanceId, stageEnum.getCode());
        BussinessChanceEntity bussinessChanceEntity = bussinessChanceMapper.selectByPrimaryKey(businessChanceId);
        Integer oldStage = bussinessChanceEntity.getStage();
        int newStage = stageEnum.getCode();
        log.info("更新商机阶段,oldStage:{},newStage:{}", oldStage, newStage);
        if (newStage > oldStage) {
            log.info("更新商机阶段,newStage大于oldStage,进行更新操作");
            bussinessChanceMapper.updateStageByBusinessChanceId(newStage, businessChanceId);
        }
        BussinessChanceEntity updateEntity = new BussinessChanceEntity();
        updateEntity.setBussinessChanceId(businessChanceId);
        Date now = new Date();
        switch (stageEnum) {
            case PRELIMINARY_NEGOTIATION:
                updateEntity.setPreliminaryNegotiationTime(now);
                break;
            case OPPORTUNITY_VERIFICATION:
                updateEntity.setOpportunityVerificationTime(now);
                break;
            case PRELIMINARY_SCHEME:
                updateEntity.setPreliminarySchemeTime(now);
                break;
            case FINAL_SCHEME:
                updateEntity.setFinalSchemeTime(now);
                break;
            case WINNING_ORDER:
                updateEntity.setWinningOrderTime(now);
                break;
            case LOSE_ORDER:
                updateEntity.setLoseOrderTime(now);
                break;
            default:
                log.info("更新商机阶段时间,stageEnum:{}不支持", stageEnum.getCode());
                return;
        }
        log.info("更新商机阶段时间,updateEntity:{}", JSON.toJSONString(updateEntity));
        bussinessChanceMapper.updateStageTimeByBusinessChanceId(updateEntity);
    }

    @Override
    public void updateLevel(Integer businessChanceId) {
        log.info("更新商机等级,入参:{}", businessChanceId);
        CurrentUser currentUser = getCurrentUser();
        BusinessChanceDto businessChanceDto = bussinessChanceMapper.getDetailById(businessChanceId, currentUser.getId());
        if (Objects.isNull(businessChanceDto)) {
            log.info("更新商机等级,businessChanceDto为空,businessChanceId:{}", businessChanceId);
            return;
        }
        calc(businessChanceDto);
        log.info("更新商机等级,result:{}", businessChanceDto.getSystemBusinessLevel());
        bussinessChanceMapper.updateSystemBusinessLevel(businessChanceDto.getSystemBusinessLevel(), businessChanceId);
    }

    @Override
    public List<UserDto> findAllShareUser(String name) {
        return bussinessChanceMapper.findAllShareUser(name);
    }

    @Override
    public List<UserDto> findAllBelongUser(String name) {
        return bussinessChanceMapper.findAllBelongUser(name);
    }

    private BusinessChanceDto smartQuoteConvertBusinessChance(SmartQuoteDto smartQuoteDto) {
        BusinessChanceDto data = new BusinessChanceDto();

        data.setUserId(smartQuoteDto.getUserId());
        smartQuoteTraderBind(smartQuoteDto, data);
        // 判断联系人
        smartQuoteTraderContactBind(smartQuoteDto, data);

        // 其他数据
        data.setOrderTime(System.currentTimeMillis());
        // sku
        AtomicBoolean flag = new AtomicBoolean(true);
        List<BusinessChanceGoodsDto> businessChanceGoods = smartQuoteGoodsBind(smartQuoteDto,flag);
        data.setBusinessChanceGoodsDtos(businessChanceGoods);
        BigDecimal amount = businessChanceGoods.stream().map(x -> x.getPrice().multiply(new BigDecimal(x.getNum()))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        data.setAmount(amount);

        // 基础数据
        businessChanceOfSaleOrLeadsBaseInfoSet(data);
        data.setCommunication(BusinessChanceConstant.SMART_QUOTE_ADD);
        // 赋值归属
        businessChanceSetUserId(data);

        confirmTraderData(data);
        data.setTraderName(data.getCheckTraderName());
        calc(data);
        List<BusinessChanceGoodsDto> goods = data.getBusinessChanceGoodsDtos();
        String collect = goods.stream().map(x -> x.getSku()+"," + x.getGoodsName()).collect(Collectors.joining(","));
        baseDataSet(data);
        data.setProductCommentsSale(collect);
        if (flag.get()) {
            // 报价中
            data.setStatus(ErpConst.ONE);
        } else {
            // 处理中
            data.setStatus(ErpConst.SIX);
        }
        log.info("智能询价，商机数据[{}]", JSON.toJSONString(data));
        return data;
    }

    private List<BusinessChanceGoodsDto> smartQuoteGoodsBind(SmartQuoteDto smartQuoteDto, AtomicBoolean flag) {
        List<SmartQuoteGoodsDto> goodsList = smartQuoteDto.getGoodsList();
        List<String> skus = goodsList.stream().map(SmartQuoteGoodsDto::getSku).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        List<CoreSkuVo> goodsInfoBySkuNos = goodsApiService.getGoodsInfoBySkuNos(skus);
        if (CollUtil.isEmpty(goodsInfoBySkuNos)) {
            throw new ServiceException("商品不存在");
        }
        Map<String, CoreSkuVo> coreSkuVoMap = goodsInfoBySkuNos.stream().collect(Collectors.toMap(CoreSkuVo::getSkuNo, x -> x, (k1, k2) -> k1));

        List<BusinessChanceGoodsDto> businessChanceGoods = goodsList.stream().map(x -> {

            CoreSkuVo coreSkuVo = coreSkuVoMap.get(x.getSku());
            if (Objects.isNull(x)) {
                throw new ServiceException("商品不存在");
            }
            BusinessChanceGoodsDto businessChanceGoodsDto = new BusinessChanceGoodsDto();

            businessChanceGoodsDto.setSku(x.getSku());
            businessChanceGoodsDto.setNum(x.getNum());
            if (Objects.isNull(x.getPrice()) || x.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                flag.set(false);
                x.setPrice(BigDecimal.ZERO);
            }
            businessChanceGoodsDto.setPrice(x.getPrice());
            if (Objects.nonNull(x.getIncludeTuning()) && ErpConst.ONE.equals(x.getIncludeTuning())) {
                businessChanceGoodsDto.setHaveInstallation(true);
            }
            businessChanceGoodsDto.setBrandName(coreSkuVo.getBrandName());
            businessChanceGoodsDto.setGoodsName(coreSkuVo.getShowName());
            businessChanceGoodsDto.setDeliveryCycle(x.getDeliveryCycle());
            businessChanceGoodsDto.setDeliveryDirect(coreSkuVo.getIsDirect().equals(ErpConst.ONE));
            businessChanceGoodsDto.setModel(coreSkuVo.getModel());
            businessChanceGoodsDto.setUnitName(coreSkuVo.getUnitName());
            businessChanceGoodsDto.setGoodsId(coreSkuVo.getSkuId());
            businessChanceGoodsDto.setIsDelete(false);
            return businessChanceGoodsDto;

        }).collect(Collectors.toList());
        return businessChanceGoods;
    }

    private void smartQuoteTraderContactBind(SmartQuoteDto smartQuoteDto, BusinessChanceDto data) {
        TraderContactDto traderContact = smartQuoteDto.getTraderContact();
        if (Objects.isNull(traderContact)) {
            // 取客户的默认联系人
            TraderContactDto traderContactDto = traderContactService.queryTraderContractDefault(smartQuoteDto.getTraderId());
            if (Objects.isNull(traderContactDto)) {

                List<TraderContactDto> traderContactListBySort = traderContactService.getTraderContactListBySort(smartQuoteDto.getTraderId());

                if (CollUtil.isEmpty(traderContactListBySort)) {
                    throw new ServiceException("客户联系人不存在");
                }
                traderContactDto = traderContactListBySort.get(0);
            }

            data.setTraderContactId(traderContactDto.getTraderContactId());
            data.setMobile(traderContactDto.getMobile());
            data.setTelephone(traderContactDto.getTelephone());
            data.setTraderContactName(traderContactDto.getName());

        } else {
            // 新增
            traderContact.setSex(2);
            traderContact.setTraderId(smartQuoteDto.getTraderId());
            traderContact.setIsDefault(0);
            traderContact.setIsEnable(1);
            traderContact.setIsOnJob(1);
            traderContactService.add(traderContact);

            data.setTraderContactId(traderContact.getTraderContactId());
            data.setMobile(traderContact.getMobile());
            data.setTelephone(traderContact.getTelephone());
            data.setTraderContactName(traderContact.getName());
        }
    }

    private void smartQuoteTraderBind(SmartQuoteDto smartQuoteDto, BusinessChanceDto data) {
        // 组装客户信息
        // 判断客户是否存在
        TraderCustomerInfoVo traderCustomerInfoVo = traderCustomerBaseService.getTraderCustomerInfoVo(smartQuoteDto.getTraderId());
        if (Objects.isNull(traderCustomerInfoVo)) {
            throw new ServiceException("客户不存在");
        }
        data.setTraderId(smartQuoteDto.getTraderId());
        data.setAreaIds(traderCustomerInfoVo.getAreaIds());
        data.setAreaId(traderCustomerInfoVo.getAreaId());

        data.setCheckTraderName(traderCustomerInfoVo.getTraderName());
        data.setCheckTraderArea(traderCustomerInfoVo.getAddress());
    }

    @Override
    public Integer selectQuoteorderIdByBusinessChanceId(Integer bussinessChanceId){
        return quoteorderMapper.selectQuoteorderIdByBusinessChanceId(bussinessChanceId);
    }

    @Override
    public void close(Integer quoteorderId) {
        try {
            // 查询商机ID
            QuoteorderEntity quoteorderEntity = quoteorderMapper.selectByPrimaryKey(quoteorderId);
            QuoteorderEntity update1 = new QuoteorderEntity();
            update1.setQuoteorderId(quoteorderId);
            update1.setValidStatus(Boolean.FALSE);
            update1.setFollowOrderStatus(ErpConstant.TWO);
            update1.setFollowOrderTime(System.currentTimeMillis());
            update1.setCloseReasonComment("订单关闭后，自动关闭报价单");
            update1.setCloseReasonId(3204);
            quoteorderMapper.updateByPrimaryKeySelective(quoteorderEntity);

            Integer bussinessChanceId = quoteorderEntity.getBussinessChanceId();
            BussinessChanceEntity update2 = new BussinessChanceEntity();
            update2.setBussinessChanceId(bussinessChanceId);
            update2.setStatus(ErpConstant.FOUR);
            update2.setClosedComments("订单关闭后，自动关闭商机");
            update2.setStatusComments(3202);//联动关闭
            update2.setStage(ErpConstant.SIX);
            bussinessChanceMapper.updateByPrimaryKeySelective(update2);
        }catch (Exception e){
            log.info("{}关闭商机报价单异常：{}",quoteorderId, e);
        }
       
        
    }
}
