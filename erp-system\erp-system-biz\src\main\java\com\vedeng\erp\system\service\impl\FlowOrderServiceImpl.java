package com.vedeng.erp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.eventbus.EventBus;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.SystemSourceEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.http.SystemApiClientProxy;
import com.vedeng.common.core.utils.DiffUtils;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.dto.BuyorderGoodsApiDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.finance.dto.event.CheckGoodAptitudeEvent;
import com.vedeng.erp.finance.dto.event.CheckGoodAptitudeSupplierEvent;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.system.domain.dto.*;
import com.vedeng.erp.system.domain.entity.*;
import com.vedeng.erp.system.mapper.*;
import com.vedeng.erp.system.mapstruct.FlowNodeConvertor;
import com.vedeng.erp.system.mapstruct.FlowNodeOrderDetailPriceConvertor;
import com.vedeng.erp.system.mapstruct.FlowOrderConvertor;
import com.vedeng.erp.system.mapstruct.FlowOrderDetailConvertor;
import com.vedeng.erp.system.service.BaseCompanyInfoService;
import com.vedeng.erp.system.service.FlowOrderService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.oss.service.domain.FileInfo;
import com.vedeng.uac.api.dto.UserInfoDto;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FlowOrderServiceImpl implements FlowOrderService {

    @Autowired
    private FlowOrderMapper flowOrderMapper;
    @Autowired
    private FlowOrderDetailMapper flowOrderDetailMapper;
    @Autowired
    private FlowNodeMapper flowNodeMapper;
    @Autowired
    private FlowNodeOrderDetailPriceMapper flowNodeOrderDetailPriceMapper;
    @Autowired
    private FlowOrderInfoMapper flowOrderInfoMapper;

    @Autowired
    private BuyorderApiService buyorderApiService;
    @Autowired
    private SaleOrderApiService saleorderApiService;

    @Autowired
    private FlowOrderConvertor flowOrderConvertor;
    @Autowired
    private FlowOrderDetailConvertor flowOrderDetailConvertor;
    @Autowired
    private FlowNodeConvertor flowNodeConvertor;
    @Autowired
    private FlowNodeOrderDetailPriceConvertor flowNodeOrderDetailPriceConvertor;

    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;

    @Autowired
    private BaseCompanyInfoService baseCompanyInfoService;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private SystemApiClientProxy systemApiClientProxy;

    @Override
    public String getFlowOrderByBusinessForStandard(String businessNo){
        List<FlowOrderEntity> findbyBaseOrderNo = flowOrderMapper.findbyBaseOrderNo(businessNo);
        if(CollectionUtil.isNotEmpty(findbyBaseOrderNo)){
            return "Y";
        }

        List<FlowOrderInfoEntity> flowOrderInfoEntities = flowOrderInfoMapper.findByFlowOrderInfoNo(businessNo);
        if(CollectionUtil.isNotEmpty(flowOrderInfoEntities)){
            return "Y";
        }
        return "N";
    }


    @Override
    public PageInfo<FlowOrderRespDto> page(PageParam<FlowOrderReqDto> pageParam) {
        PageInfo<FlowOrderRespDto> result = PageHelper.startPage(pageParam).doSelectPageInfo(() -> flowNodeMapper.getPageList(pageParam.getParam()));
        return result;
    }

    @Override
    public FlowOrderDto init(String baseBusinessNo, Integer baseBusinessType, Integer pushDirection, String sourceErp) {
        FlowOrderDto flowOrderDto = FlowOrderDto.builder()
                .baseOrderNo(baseBusinessNo)
                .baseBusinessType(baseBusinessType)
                .pushDirection(pushDirection)
                .sourceErp(sourceErp)
                .build();

        // 通过 sourceErp 获取公司名称并设置到 sourceErpName
        if (StrUtil.isNotBlank(sourceErp)) {
            BaseCompanyInfoEntity companyInfo = baseCompanyInfoService.selectByShortName(sourceErp);
            if (companyInfo != null && StrUtil.isNotBlank(companyInfo.getCompanyName())) {
                flowOrderDto.setSourceErpName(companyInfo.getCompanyName());
            }
        }

        List<FlowOrderEntity> flowOrderEntities = flowOrderMapper.findbyBaseOrderNo(baseBusinessNo);
        if (CollUtil.isNotEmpty(flowOrderEntities)) {
            throw new ServiceException("该单号已关联其他业务流转单");
        }


        // 采购
        if (baseBusinessType == 1) {
            BuyOrderApiDto buyOrderApiDto;
            
            if(pushDirection == 2){
                // 通过远程API获取采购单数据
                buyOrderApiDto = getBuyOrderFromRemoteApi(baseBusinessNo, sourceErp);
            } else {
                // 通过本地服务获取采购单数据
                buyOrderApiDto = buyorderApiService.getBuyorderByBuyorderNo(baseBusinessNo);
            }
            if (buyOrderApiDto == null) {
                throw new ServiceException("采购单不存在");
            }
            if (buyOrderApiDto.getStatus() > 0) {
                throw new ServiceException("关联单号未查询到未生效状态的采购单号");
            }
            if ("浙江贝登鸿瑞供应链管理有限公司".equals(buyOrderApiDto.getTraderName())) {
                throw new ServiceException("鸿瑞采购单暂不支持使用业务流转单");
            }

            FlowNodeDto flowNodeDto = FlowNodeDto.builder().nodeLevel(1)
                    //款票信息
                    .paymentMethod(buyOrderApiDto.getPaymentType() == 419 ? 0 : 1)
                    .openInvoice(1)
                    .creditPayment(buyOrderApiDto.getAccountPeriodAmount())
                    .balance(buyOrderApiDto.getRetainageAmount())
                    .balanceDueDate(buyOrderApiDto.getRetainageAmountMonth())
                    .amount(buyOrderApiDto.getTotalAmount())
                    .invoiceType(buyOrderApiDto.getInvoiceType())
                    .traderId(buyOrderApiDto.getTraderId())
                    .traderName(buyOrderApiDto.getTraderName())
                    .traderAddressId(buyOrderApiDto.getTraderAddressId())
                    .traderContactId(buyOrderApiDto.getTraderContactId())
                    .traderContactName(buyOrderApiDto.getTraderContactName())
                    .traderContactPhone(buyOrderApiDto.getTraderContactMobile())
                    .traderContactAddress(buyOrderApiDto.getTraderArea() + " " + buyOrderApiDto.getTraderAddress())
                    .receiverTraderContactId(buyOrderApiDto.getTakeTraderContactId())
                    .receiverName(buyOrderApiDto.getTakeTraderContactName())
                    .receiverPhone(buyOrderApiDto.getTakeTraderContactMobile())
                    .receiverAddressId(Integer.valueOf(buyOrderApiDto.getTakeTraderAddressId()))
                    .receiverAddress(buyOrderApiDto.getTakeTraderAddress())
                    .build();
            flowOrderDto.setBaseOrderId(buyOrderApiDto.getBuyorderId());
            flowOrderDto.setFlowNodeDto(CollUtil.newArrayList(flowNodeDto));


            List<FlowOrderDetailDto> flowOrderDetailList = new ArrayList<>();
            for (BuyorderGoodsApiDto buyorderGoodsApiDto : buyOrderApiDto.getBuyorderGoodsApiDtos()) {
                FlowOrderDetailDto flowOrderDetailDto = FlowOrderDetailDto.builder().skuId(buyorderGoodsApiDto.getGoodsId()).skuNo(buyorderGoodsApiDto.getSku()).productName(buyorderGoodsApiDto.getGoodsName()).brand(buyorderGoodsApiDto.getBrandName()).model(buyorderGoodsApiDto.getModel()).unit(buyorderGoodsApiDto.getUnitName()).quantity(buyorderGoodsApiDto.getNum()).build();

                List<FlowNodeOrderDetailPriceDto> flowNodeAmountDtoList = new ArrayList<>();
                flowNodeAmountDtoList.add(FlowNodeOrderDetailPriceDto.builder().nodeLevel(1).price(buyorderGoodsApiDto.getPrice()).build());

                flowOrderDetailDto.setFlowNodeOrderDetailPriceDtoList(flowNodeAmountDtoList);

                flowOrderDetailList.add(flowOrderDetailDto);
            }
            flowOrderDto.setFlowOrderDetailList(flowOrderDetailList);
        }

        if (baseBusinessType == 2) {
            // 销售
            SaleorderInfoDto saleorderInfoDto = saleorderApiService.getBySaleOrderNo(baseBusinessNo);

            if (saleorderInfoDto == null) {
                throw new ServiceException("销售单不存在");
            }
            if (saleorderInfoDto.getValidStatus() == 0) {
                throw new ServiceException("关联单号未查询到已生效状态的销售单号");
            }

            FlowNodeDto flowNodeDto = FlowNodeDto.builder().nodeLevel(1).openInvoice(1)
                    //款票信息
                    .paymentMethod(saleorderInfoDto.getPaymentType() == 419 ? 0 : 1)
                    .creditPayment(saleorderInfoDto.getAccountPeriodAmount())
                    .balance(saleorderInfoDto.getRetainageAmount())
                    .balanceDueDate(saleorderInfoDto.getRetainageAmountMonth())
                    .amount(saleorderInfoDto.getTotalAmount())
                    .invoiceType(saleorderInfoDto.getInvoiceType())

                    .traderId(saleorderInfoDto.getTraderId())
                    .traderName(saleorderInfoDto.getTraderName())
                    .traderAddressId(saleorderInfoDto.getTraderAddressId())
                    .traderContactId(saleorderInfoDto.getTraderContactId())
                    .traderContactName(saleorderInfoDto.getTraderContactName())
                    .traderContactPhone(saleorderInfoDto.getTraderContactMobile())
                    .traderContactAddress(saleorderInfoDto.getTraderArea() + " " + saleorderInfoDto.getTraderAddress())

                    .receiverTraderContactId(saleorderInfoDto.getTakeTraderContactId())
                    .receiverName(saleorderInfoDto.getTakeTraderContactName())
                    .receiverPhone(saleorderInfoDto.getTakeTraderContactMobile())
                    .receiverAddressId(saleorderInfoDto.getTakeTraderContactId())
                    .receiverAddress(saleorderInfoDto.getTakeTraderAddress())

                    .invoiceReceiverTraderContactId(saleorderInfoDto.getInvoiceTraderContactId())
                    .invoiceReceiverName(saleorderInfoDto.getInvoiceTraderContactName())
                    .invoiceReceiverPhone(saleorderInfoDto.getInvoiceTraderContactMobile())
                    .invoiceReceiverAddressId(saleorderInfoDto.getInvoiceTraderContactId())
                    .invoiceReceiverAddress(saleorderInfoDto.getInvoiceTraderAddress())

                    .build();
            flowOrderDto.setBaseOrderId(saleorderInfoDto.getSaleorderId());
            flowOrderDto.setFlowNodeDto(CollUtil.newArrayList(flowNodeDto));

            List<FlowOrderDetailDto> flowOrderDetailList = new ArrayList<>();
            for (SaleOrderGoodsDetailDto saleOrderGoodsDetail : saleorderInfoDto.getSaleOrderGoodsDetailDtoList()) {
                FlowOrderDetailDto flowOrderDetailDto = FlowOrderDetailDto.builder().skuId(saleOrderGoodsDetail.getGoodsId()).skuNo(saleOrderGoodsDetail.getSkuNo()).productName(saleOrderGoodsDetail.getGoodsName()).brand(saleOrderGoodsDetail.getBrandName()).model(saleOrderGoodsDetail.getModel()).unit(saleOrderGoodsDetail.getUnitName()).quantity(saleOrderGoodsDetail.getNum()).build();

                List<FlowNodeOrderDetailPriceDto> flowNodeAmountDtoList = new ArrayList<>();
                flowNodeAmountDtoList.add(FlowNodeOrderDetailPriceDto.builder().nodeLevel(1).price(saleOrderGoodsDetail.getPrice()).build());

                flowOrderDetailDto.setFlowNodeOrderDetailPriceDtoList(flowNodeAmountDtoList);
                flowOrderDetailList.add(flowOrderDetailDto);
            }
            flowOrderDto.setFlowOrderDetailList(flowOrderDetailList);
        }

        return flowOrderDto;
    }

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;

    public void checkGoodAptitudeCustomer(List<Integer> traderIds, List<String> skuList) {
        CheckGoodAptitudeEvent event = new CheckGoodAptitudeEvent();
        event.setSkuNos(skuList);
        event.setTraderIds(traderIds);
        eventBusCenter.post(event);
        if (StrUtil.isNotBlank(event.getMessage())) {
            throw new ServiceException(event.getMessage());
        }
    }

    public void checkGoodAptitudeSupplier(List<Integer> traderIds, List<String> skuList) {
        CheckGoodAptitudeSupplierEvent event = new CheckGoodAptitudeSupplierEvent();
        event.setSkuNos(skuList);
        event.setTraderIds(traderIds);
        eventBusCenter.post(event);
        if (StrUtil.isNotBlank(event.getMessage())) {
            throw new ServiceException(event.getMessage());
        }
    }

    @Override
    public void checkGoodAptitude(FlowOrderDto flowOrderDto) {
        try {
            List<FlowOrderDetailDto> detailList = flowOrderDto.getFlowOrderDetailList();
            List<FlowNodeDto> flowNodeDto = flowOrderDto.getFlowNodeDto();
            List<String> skuNos = detailList.stream().map(FlowOrderDetailDto::getSkuNo).collect(Collectors.toList());
            List<Integer> traderIds = flowNodeDto.stream().map(FlowNodeDto::getTraderId).collect(Collectors.toList());
            if (CollUtil.isEmpty(skuNos) || CollUtil.isEmpty(traderIds)) {
                log.info("参数为空，无需校验资质");
                return;
            }
            if (flowOrderDto.getBaseBusinessType() == 1) {
                log.info("校验供应商");
                checkGoodAptitudeSupplier(traderIds, skuNos);
            }
            if (flowOrderDto.getBaseBusinessType() == 2) {
                log.info("校验客户");
                checkGoodAptitudeCustomer(traderIds, skuNos);
            }
        } catch (Exception e) {
            log.info("资质校验失败", e);
            throw new ServiceException(e.getMessage());
        }

    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void add(FlowOrderDto flowOrderDto) {
        log.info("业务流转单进行保存,{}",JSON.toJSONString(flowOrderDto));
        // 验证ERP推送方向的子公司ERP链接
        validateErpSubsidiariesForErpPush(flowOrderDto);
        
        FlowOrderEntity flowOrderEntity = flowOrderConvertor.toEntity(flowOrderDto);
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.FLOW_ORDER);
        String no = new BillNumGenerator().distribution(billGeneratorBean);
        flowOrderEntity.setFlowOrderNo(no);

        // 根据末级节点判断合同状态
        Integer contractStatus = determineContractStatus(flowOrderDto.getFlowNodeDto());
        flowOrderEntity.setContractStatus(contractStatus);

        flowOrderMapper.insertSelective(flowOrderEntity);
        flowOrderDto.setFlowOrderId(flowOrderEntity.getFlowOrderId());

        List<FlowNodeDto> flowNodeDtoList = flowOrderDto.getFlowNodeDto();
        // 创建一个映射表，用于存储 nodeLevel 和 flowNodeId 的对应关系
        Map<Integer, Long> nodeLevelToFlowNodeIdMap = new HashMap<>();

        // 将node中节点设置从1开始依次递增
        flowNodeDtoList.forEach(node -> {
            FlowNodeEntity flowNodeEntity = flowNodeConvertor.toEntity(node);
            flowNodeEntity.setNodeLevel(flowNodeDtoList.indexOf(node) + 1);
            flowNodeEntity.setFlowOrderId(flowOrderEntity.getFlowOrderId());
            flowNodeMapper.insertSelective(flowNodeEntity);
            nodeLevelToFlowNodeIdMap.put(node.getNodeLevel(), flowNodeEntity.getFlowNodeId());
        });

        List<FlowOrderDetailDto> flowOrderDetailList = flowOrderDto.getFlowOrderDetailList();
        if (CollUtil.isNotEmpty(flowOrderDetailList)) {
            flowOrderDetailList.forEach(flowOrderDetailDto -> {
                FlowOrderDetailEntity flowOrderDetailEntity = flowOrderDetailConvertor.toEntity(flowOrderDetailDto);
                flowOrderDetailEntity.setFlowOrderId(flowOrderEntity.getFlowOrderId());
                flowOrderDetailMapper.insertSelective(flowOrderDetailEntity);

                List<FlowNodeOrderDetailPriceEntity> flowNodeOrderDetailPriceEntityList = flowNodeOrderDetailPriceConvertor.toEntity(flowOrderDetailDto.getFlowNodeOrderDetailPriceDtoList());


                flowNodeOrderDetailPriceEntityList.forEach(flowNodeOrderDetailPriceEntity -> {
                    flowNodeOrderDetailPriceEntity.setFlowOrderDetailId(flowOrderDetailEntity.getFlowOrderDetailId());
                    flowNodeOrderDetailPriceEntity.setNodeLevel(flowNodeOrderDetailPriceEntityList.indexOf(flowNodeOrderDetailPriceEntity) + 1);
                    Long flowNodeId = nodeLevelToFlowNodeIdMap.get(flowNodeOrderDetailPriceEntity.getNodeLevel());
                    flowNodeOrderDetailPriceEntity.setFlowNodeId(flowNodeId);
                    flowNodeOrderDetailPriceMapper.insertSelective(flowNodeOrderDetailPriceEntity);
                });
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(FlowOrderDto flowOrderDto) {

        // 验证ERP推送方向的子公司ERP链接
        validateErpSubsidiariesForErpPush(flowOrderDto);

        // 更新 FlowOrderEntity
        FlowOrderEntity flowOrderEntity = flowOrderConvertor.toEntity(flowOrderDto);

        // 根据末级节点判断合同状态（检查是否已上传合同）
        Integer contractStatus = determineContractStatusWithUploadCheck(flowOrderDto.getFlowOrderId(), flowOrderDto.getFlowNodeDto());
        flowOrderEntity.setContractStatus(contractStatus);

        flowOrderMapper.updateByPrimaryKeySelective(flowOrderEntity);

        // 更新 FlowNodeEntity
        List<FlowNodeEntity> flowNodeEntityListFromDb = flowNodeMapper.findByFlowOrderId(flowOrderDto.getFlowOrderId());
        log.info("修改之前数据库中存在的业务流转单节点: {}", JSON.toJSONString(flowNodeEntityListFromDb));

        List<FlowNodeEntity> flowNodeEntityList = flowNodeConvertor.toEntity(flowOrderDto.getFlowNodeDto());
        flowNodeEntityList.forEach(item -> item.setFlowOrderId(flowOrderDto.getFlowOrderId()));
        log.info("修改时传入的业务流转单节点: {}", JSON.toJSONString(flowNodeEntityList));

        List<DiffUtils.ContentValue<Long, FlowNodeEntity>> flowNodeFromDb = flowNodeEntityListFromDb.stream().map(item -> DiffUtils.ContentValue.of(item.getFlowNodeId(), item)).collect(Collectors.toList());

        List<DiffUtils.ContentValue<Long, FlowNodeEntity>> flowNodeFromParams = flowNodeEntityList.stream().map(item -> DiffUtils.ContentValue.of(item.getFlowNodeId(), item)).collect(Collectors.toList());

        Map<DiffUtils.Type, Consumer<List<FlowNodeEntity>>> flowNodeActionMap = new EnumMap<>(DiffUtils.Type.class);
        flowNodeActionMap.put(DiffUtils.Type.INSERT, this::batchInsertFlowNodeEntity);
        flowNodeActionMap.put(DiffUtils.Type.UPDATE, this::batchUpdateFlowNodeEntity);
        flowNodeActionMap.put(DiffUtils.Type.DELETE, this::batchDeleteFlowNodeEntity);
        DiffUtils.doDiff(flowNodeFromDb, flowNodeFromParams, flowNodeActionMap);

        // 创建一个映射表，用于存储 nodeLevel 和 flowNodeId 的对应关系
        Map<Integer, Long> nodeLevelToFlowNodeIdMap = new HashMap<>();
        flowNodeEntityList.forEach(node -> nodeLevelToFlowNodeIdMap.put(node.getNodeLevel(), node.getFlowNodeId()));

        // 更新 FlowOrderDetailEntity
        List<FlowOrderDetailEntity> flowOrderDetailEntityListFromDb = flowOrderDetailMapper.findByFlowOrderId(flowOrderDto.getFlowOrderId());
        log.info("修改之前数据库中存在的业务流转单明细: {}", JSON.toJSONString(flowOrderDetailEntityListFromDb));

        List<FlowOrderDetailEntity> flowOrderDetailEntityList = flowOrderDetailConvertor.toEntity(flowOrderDto.getFlowOrderDetailList());
        flowOrderDetailEntityList.forEach(item -> item.setFlowOrderId(flowOrderDto.getFlowOrderId()));
        log.info("修改时传入的业务流转单明细: {}", JSON.toJSONString(flowOrderDetailEntityList));

        List<DiffUtils.ContentValue<Long, FlowOrderDetailEntity>> flowOrderDetailFromDb = flowOrderDetailEntityListFromDb.stream().map(item -> DiffUtils.ContentValue.of(item.getFlowOrderDetailId(), item)).collect(Collectors.toList());

        List<DiffUtils.ContentValue<Long, FlowOrderDetailEntity>> flowOrderDetailFromParams = flowOrderDetailEntityList.stream().map(item -> DiffUtils.ContentValue.of(item.getFlowOrderDetailId(), item)).collect(Collectors.toList());

        Map<DiffUtils.Type, Consumer<List<FlowOrderDetailEntity>>> flowOrderDetailActionMap = new EnumMap<>(DiffUtils.Type.class);
        flowOrderDetailActionMap.put(DiffUtils.Type.INSERT, this::batchInsertFlowOrderDetailEntity);
        flowOrderDetailActionMap.put(DiffUtils.Type.UPDATE, this::batchUpdateFlowOrderDetailEntity);
        flowOrderDetailActionMap.put(DiffUtils.Type.DELETE, this::batchDeleteFlowOrderDetailEntity);
        DiffUtils.doDiff(flowOrderDetailFromDb, flowOrderDetailFromParams, flowOrderDetailActionMap);

        // 更新 FlowOrderDetailEntity 其关联的 FlowNodeOrderDetailPriceEntity
        List<FlowOrderDetailDto> flowOrderDetailList = flowOrderDto.getFlowOrderDetailList();
        flowOrderDetailList = flowOrderDetailList.stream().filter(item -> item.getFlowOrderDetailId() != null).collect(Collectors.toList());
        flowOrderDetailList.forEach(item -> {
            List<FlowNodeOrderDetailPriceEntity> flowNodeOrderDetailPriceEntityList = flowNodeOrderDetailPriceMapper.findByFlowOrderDetailId(item.getFlowOrderDetailId());
            log.info("修改之前数据库中存在的业务流转单明细价格: {}", JSON.toJSONString(flowNodeOrderDetailPriceEntityList));

            List<FlowNodeOrderDetailPriceEntity> nodeOrderDetailPriceEntityList = flowNodeOrderDetailPriceConvertor.toEntity(item.getFlowNodeOrderDetailPriceDtoList());
            nodeOrderDetailPriceEntityList.forEach(item1 -> item1.setFlowOrderDetailId(item.getFlowOrderDetailId()));

            // 设置 flowNodeId
            nodeOrderDetailPriceEntityList.forEach(item1 -> {
                Long flowNodeId = nodeLevelToFlowNodeIdMap.get(item1.getNodeLevel());
                item1.setFlowNodeId(flowNodeId);
            });

            log.info("修改时传入的业务流转单明细价格: {}", JSON.toJSONString(nodeOrderDetailPriceEntityList));

            List<DiffUtils.ContentValue<Long, FlowNodeOrderDetailPriceEntity>> nodeOrderDetailPriceFromDb = flowNodeOrderDetailPriceEntityList.stream().map(i -> DiffUtils.ContentValue.of(i.getFlowNodeOrderPriceId(), i)).collect(Collectors.toList());

            List<DiffUtils.ContentValue<Long, FlowNodeOrderDetailPriceEntity>> nodeOrderDetailPriceFromParams = nodeOrderDetailPriceEntityList.stream().map(i -> DiffUtils.ContentValue.of(i.getFlowNodeOrderPriceId(), i)).collect(Collectors.toList());

            Map<DiffUtils.Type, Consumer<List<FlowNodeOrderDetailPriceEntity>>> nodeOrderDetailPriceActionMap = new EnumMap<>(DiffUtils.Type.class);
            nodeOrderDetailPriceActionMap.put(DiffUtils.Type.INSERT, this::batchInsertFlowNodeOrderDetailPrice);
            nodeOrderDetailPriceActionMap.put(DiffUtils.Type.UPDATE, this::batchUpdateFlowNodeOrderDetailPrice);
            nodeOrderDetailPriceActionMap.put(DiffUtils.Type.DELETE, this::batchDeleteFlowNodeOrderDetailPrice);
            DiffUtils.doDiff(nodeOrderDetailPriceFromDb, nodeOrderDetailPriceFromParams, nodeOrderDetailPriceActionMap);
        });
    }

    private void batchDeleteFlowNodeOrderDetailPrice(List<FlowNodeOrderDetailPriceEntity> flowNodeOrderDetailPriceEntities) {
        flowNodeOrderDetailPriceEntities.forEach(item -> flowNodeOrderDetailPriceMapper.deleteByPrimaryKey(item.getFlowNodeOrderPriceId()));
    }

    private void batchUpdateFlowNodeOrderDetailPrice(List<FlowNodeOrderDetailPriceEntity> flowNodeOrderDetailPriceEntities) {
        flowNodeOrderDetailPriceEntities.forEach(item -> flowNodeOrderDetailPriceMapper.updateByPrimaryKeySelective(item));
    }

    private void batchInsertFlowNodeOrderDetailPrice(List<FlowNodeOrderDetailPriceEntity> flowNodeOrderDetailPriceEntities) {
        flowNodeOrderDetailPriceEntities.forEach(item -> flowNodeOrderDetailPriceMapper.insertSelective(item));
    }

    private void batchDeleteFlowOrderDetailEntity(List<FlowOrderDetailEntity> flowOrderDetailEntities) {
        flowOrderDetailEntities.forEach(item -> {
            item.setIsDelete(ErpConstant.T);
            flowOrderDetailMapper.updateByPrimaryKeySelective(item);
        });
    }

    private void batchUpdateFlowOrderDetailEntity(List<FlowOrderDetailEntity> flowOrderDetailEntities) {
        flowOrderDetailEntities.forEach(item -> flowOrderDetailMapper.updateByPrimaryKeySelective(item));
    }

    private void batchInsertFlowOrderDetailEntity(List<FlowOrderDetailEntity> flowOrderDetailEntities) {
        flowOrderDetailEntities.forEach(item -> flowOrderDetailMapper.insertSelective(item));
    }

    private void batchDeleteFlowNodeEntity(List<FlowNodeEntity> flowNodeEntities) {
        flowNodeEntities.forEach(item -> {
            item.setIsDelete(ErpConstant.T);
            flowNodeMapper.updateByPrimaryKeySelective(item);
        });
    }

    private void batchUpdateFlowNodeEntity(List<FlowNodeEntity> flowNodeEntities) {
        flowNodeEntities.forEach(item -> flowNodeMapper.updateByPrimaryKeySelective(item));
    }

    private void batchInsertFlowNodeEntity(List<FlowNodeEntity> flowNodeEntities) {
        flowNodeEntities.forEach(item -> flowNodeMapper.insertSelective(item));

    }

    @Override
    public void delete(HashSet<Long> ids) {
        ids.forEach(id -> {
            FlowOrderEntity flowOrder = flowOrderMapper.selectByPrimaryKey(id);
            if (flowOrder == null) {
                throw new ServiceException("流程订单不存在");
            }

            if (flowOrder.getIsDelete() == 1) {
                throw new ServiceException("流程订单已删除");
            }
            FlowOrderEntity flowOrderEntity = new FlowOrderEntity();
            flowOrderEntity.setFlowOrderId(id);
            flowOrderEntity.setIsDelete(1);
            flowOrderMapper.updateByPrimaryKeySelective(flowOrderEntity);

            flowOrderDetailMapper.findByFlowOrderId(flowOrder.getFlowOrderId()).forEach(flowOrderDetailEntity -> {
                flowOrderDetailEntity.setIsDelete(ErpConstant.T);
                flowOrderDetailMapper.updateByPrimaryKeySelective(flowOrderDetailEntity);
            });

            List<FlowNodeEntity> flowNodeEntityList = flowNodeMapper.findByFlowOrderId(flowOrder.getFlowOrderId());
            flowNodeEntityList.forEach(flowNodeEntity -> {
                flowNodeEntity.setIsDelete(ErpConstant.T);
                flowNodeMapper.updateByPrimaryKeySelective(flowNodeEntity);
                List<FlowNodeOrderDetailPriceEntity> flowNodeOrderDetailPriceEntityList = flowNodeOrderDetailPriceMapper.findByFlowNodeId(flowNodeEntity.getFlowNodeId());
                flowNodeOrderDetailPriceEntityList.forEach(flowNodeOrderDetailPriceEntity -> flowNodeOrderDetailPriceMapper.deleteByPrimaryKey(flowNodeOrderDetailPriceEntity.getFlowNodeOrderPriceId()));
            });
        });

    }

    @Override
    public FlowOrderDto get(Long flowOrderId) {
        List<FlowOrderEntity> flowOrderEntityList = flowOrderMapper.findByFlowOrderId(flowOrderId);
        if (CollUtil.isEmpty(flowOrderEntityList)) {
            throw new ServiceException("业务流程订单不存在");
        }
        FlowOrderEntity flowOrderEntity = flowOrderEntityList.get(0);
        Integer baseBusinessType = flowOrderEntity.getBaseBusinessType();
        List<FlowOrderEntity> flowOrderBuyorder = flowOrderEntityList.stream().filter(e -> Objects.equals(e.getBaseBusinessType(), baseBusinessType)).collect(Collectors.toList());
        if (CollUtil.isEmpty(flowOrderBuyorder)) {
            throw new ServiceException("业务流程信息不存在");
        }
        List<FlowOrderDetailEntity> flowOrderDetailEntityList = flowOrderDetailMapper.findByFlowOrderId(flowOrderId);
        if (CollUtil.isEmpty(flowOrderDetailEntityList)) {
            flowOrderDetailEntityList = new ArrayList<>();
        }
        List<FlowNodeEntity> flowNodeEntityList = flowNodeMapper.findByFlowOrderId(flowOrderId);
        List<Long> detailIds = flowOrderDetailEntityList.stream().map(FlowOrderDetailEntity::getFlowOrderDetailId).collect(Collectors.toList());
        List<FlowNodeOrderDetailPriceEntity> flowNodeOrderDetailPriceEntityList = new ArrayList<>();
        if (CollUtil.isNotEmpty(detailIds)) {
            flowNodeOrderDetailPriceEntityList = flowNodeOrderDetailPriceMapper.findByFlowOrderDetailIds(detailIds);
        }

        Map<String, Integer> map;
        if (Objects.equals(baseBusinessType, 1)) {
            List<FlowSkuNumDto> buyGoodsNum = flowOrderMapper.findBuyGoodsNum(flowOrderId);
            map = buyGoodsNum.stream().collect(Collectors.toMap(FlowSkuNumDto::getSku, FlowSkuNumDto::getNum));
        } else {
            List<FlowSkuNumDto> saleGoodsNum = flowOrderMapper.findSaleGoodsNum(flowOrderId);
            map = saleGoodsNum.stream().collect(Collectors.toMap(FlowSkuNumDto::getSku, FlowSkuNumDto::getNum));
        }

        // 组装
        FlowOrderDto flowOrderDtoList = flowOrderConvertor.toDto(flowOrderEntityList.get(0));

        // 通过 sourceErp 获取公司名称并设置到 sourceErpName
        if (StrUtil.isNotBlank(flowOrderDtoList.getSourceErp())) {
            BaseCompanyInfoEntity companyInfo = baseCompanyInfoService.selectByShortName(flowOrderDtoList.getSourceErp());
            if (companyInfo != null && StrUtil.isNotBlank(companyInfo.getCompanyName())) {
                flowOrderDtoList.setSourceErpName(companyInfo.getCompanyName());
            }
        }

        // 商品信息
        List<FlowOrderDetailDto> flowOrderDetailDtoList = flowOrderDetailConvertor.toDto(flowOrderDetailEntityList);
        List<FlowNodeDto> flowNodeDtoList = flowNodeConvertor.toDto(flowNodeEntityList);
        List<FlowNodeOrderDetailPriceDto> flowNodeOrderDetailPriceDtoList = flowNodeOrderDetailPriceConvertor.toDto(flowNodeOrderDetailPriceEntityList);
        Map<Long, List<FlowNodeOrderDetailPriceDto>> flowOrderDetailIdMap = flowNodeOrderDetailPriceDtoList.stream().collect(Collectors.groupingBy(FlowNodeOrderDetailPriceDto::getFlowOrderDetailId));

        if (MapUtil.isNotEmpty(flowOrderDetailIdMap)) {
            for (FlowOrderDetailDto flowOrderDetailDto : flowOrderDetailDtoList) {
                flowOrderDetailDto.setFlowNodeOrderDetailPriceDtoList(flowOrderDetailIdMap.get(flowOrderDetailDto.getFlowOrderDetailId()));

                Integer num = map.get(flowOrderDetailDto.getSkuNo());
                flowOrderDetailDto.setDeliveryQuantity(Objects.isNull(num) ? 0 : num);
            }
        }

        flowOrderDtoList.setFlowOrderDetailList(flowOrderDetailDtoList);
        flowOrderDtoList.setFlowNodeDto(flowNodeDtoList);
        return flowOrderDtoList;
    }

    @Override
    public void audit(Long flowOrderId) {
        FlowOrderEntity flowOrder = flowOrderMapper.selectByPrimaryKey(flowOrderId);
        if (flowOrder == null) {
            throw new ServiceException("流程订单不存在");
        }

        if (flowOrder.getIsDelete() == 1) {
            throw new ServiceException("流程订单已删除");
        }

        if (flowOrder.getAuditStatus() == 1) {
            throw new ServiceException("审核状态为已审核，不能再次审核");
        }
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        // 更新审核状态
        FlowOrderEntity flowOrderEntity = new FlowOrderEntity();
        flowOrderEntity.setFlowOrderId(flowOrderId);
        flowOrderEntity.setAuditStatus(1);
        flowOrderEntity.setAuditTime(new Date());
        flowOrderEntity.setAuditUserId(currentUser.getId());
        flowOrderEntity.setAuditUsername(currentUser.getUsername());
        flowOrderMapper.updateByPrimaryKeySelective(flowOrderEntity);

        // 获取流程订单详情
        FlowOrderDto flowOrderDto = get(flowOrderId);

        // 根据业务类型处理不同的价格更新逻辑
        if (flowOrder.getBaseBusinessType() == 1) {
            // 采购单处理
            updateBuyOrderPrices(flowOrderDto);

            // 发送企业微信消息通知采购单创建人
            sendWeChatMessage(flowOrderDto);
        }
    }


    /**
     * 判断公司是否为贝登子公司
     *
     * @param companyName 公司名称
     * @return 是否为贝登子公司
     */
    private boolean isBeidengSubsidiary(String companyName) {
        if (StrUtil.isEmpty(companyName)) {
            return false;
        }

        BaseCompanyInfoEntity company = baseCompanyInfoService.selectByCompanyName(companyName);
        return company != null;
    }

    /**
     * 根据流转单节点列表，判断末级节点的合同状态
     *
     * @param flowNodeDtoList 流转单节点列表
     * @return 合同状态：0-无需上传（末级节点为贝登子公司），1-未上传（末级节点非贝登子公司且未上传合同），2-已上传（末级节点非贝登子公司且已上传合同）
     */
    private Integer determineContractStatus(List<FlowNodeDto> flowNodeDtoList) {
        if (CollUtil.isEmpty(flowNodeDtoList)) {
            return 1; // 默认为未上传
        }

        // 获取末级节点
        FlowNodeDto lastNode = flowNodeDtoList.stream().max(Comparator.comparing(FlowNodeDto::getNodeLevel)).orElse(null);

        if (lastNode == null) {
            return 1; // 默认为未上传
        }

        // 判断末级节点的交易者是否为贝登子公司
        boolean isSubsidiary = isBeidengSubsidiary(lastNode.getTraderName());

        // 如果是贝登子公司，则无需上传合同
        if (isSubsidiary) {
            return 0; // 无需上传
        }

        // 否则默认为未上传
        return 1; // 未上传
    }

    /**
     * 根据流转单ID和节点列表，判断末级节点的合同状态（用于更新时检查是否已上传合同）
     *
     * @param flowOrderId     流转单ID
     * @param flowNodeDtoList 流转单节点列表
     * @return 合同状态：0-无需上传（末级节点为贝登子公司），1-未上传（末级节点非贝登子公司且未上传合同），2-已上传（末级节点非贝登子公司且已上传合同）
     */
    private Integer determineContractStatusWithUploadCheck(Long flowOrderId, List<FlowNodeDto> flowNodeDtoList) {
        if (CollUtil.isEmpty(flowNodeDtoList)) {
            return 1; // 默认为未上传
        }

        // 获取末级节点
        FlowNodeDto lastNode = flowNodeDtoList.stream().max(Comparator.comparing(FlowNodeDto::getNodeLevel)).orElse(null);

        if (lastNode == null) {
            return 1; // 默认为未上传
        }

        // 判断末级节点的交易者是否为贝登子公司
        boolean isSubsidiary = isBeidengSubsidiary(lastNode.getTraderName());

        // 如果是贝登子公司，则无需上传合同
        if (isSubsidiary) {
            return 0; // 无需上传
        }

        // 如果不是贝登子公司，检查是否已上传合同（检查所有类型的合同）
        if (flowOrderId != null) {
            boolean hasUploadedContract = checkIfContractUploaded(flowOrderId, lastNode.getNodeLevel());
            return hasUploadedContract ? 2 : 1; // 已上传 : 未上传
        }

        // 否则默认为未上传
        return 1; // 未上传
    }

    /**
     * 检查指定流转单的指定节点是否已上传任何类型的合同
     *
     * @param flowOrderId 流转单ID
     * @param nodeLevel   节点级别
     * @return 是否已上传合同
     */
    private boolean checkIfContractUploaded(Long flowOrderId, Integer nodeLevel) {
        try {
            // 根据flowOrderId和nodeLevel查找对应的FlowNodeId
            List<FlowNodeEntity> flowNodes = flowNodeMapper.findByFlowOrderId(flowOrderId);
            Long flowNodeId = null;

            for (FlowNodeEntity node : flowNodes) {
                if (Objects.equals(node.getNodeLevel(), nodeLevel)) {
                    flowNodeId = node.getFlowNodeId();
                    break;
                }
            }

            if (flowNodeId == null) {
                return false;
            }

            // 查找FlowOrderInfo记录，检查是否有合同文件URL
            List<FlowOrderInfoEntity> flowOrderInfos = flowOrderInfoMapper.findByFlowNodeId(flowNodeId);
            for (FlowOrderInfoEntity info : flowOrderInfos) {
                if (Objects.equals(info.getIsDelete(), 0) && StrUtil.isNotBlank(info.getContractFileUrl())) {
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.warn("检查合同上传状态时发生异常，flowOrderId: {}, nodeLevel: {}", flowOrderId, nodeLevel, e);
            return false;
        }
    }

    private void sendWeChatMessage(FlowOrderDto flowOrderDto) {
        try {
            String baseOrderNo = flowOrderDto.getBaseOrderNo();
            if (baseOrderNo == null) {
                log.warn("采购单No为空，无法发送通知");
                return;
            }

            // 获取采购单创建人ID
            BuyOrderApiDto buyOrderApiDto = buyorderApiService.getBuyorderByBuyorderNo(baseOrderNo);
            Integer createUserId = buyOrderApiDto.getCreator();

            // 构建消息内容
            String messageContent = String.format("您的采购单（单号：%s）关联的业务流转单已通过审核，请及时处理。", baseOrderNo);

            RestfulResult<UserInfoDto> user = uacWxUserInfoApiService.getUser(createUserId, 1);
            WxCpMessage message = new WxCpMessage();
            message.setToUser(user.getData().getJobNumber());
            message.setDescription(messageContent);
            message.setTitle("采购单审核通知");
            message.setMsgType("text");
            uacWxUserInfoApiService.sendToUser(message);

            log.info("已发送企业微信通知，采购单号: {}，接收人ID: {}", baseOrderNo, createUserId);
        } catch (Exception e) {
            log.error("发送企业微信通知失败", e);
        }
    }

    /**
     * 更新采购单商品价格
     *
     * @param flowOrderDto 流程订单数据
     */
    private void updateBuyOrderPrices(FlowOrderDto flowOrderDto) {
        if (flowOrderDto == null || CollUtil.isEmpty(flowOrderDto.getFlowOrderDetailList())) {
            return;
        }

        String baseOrderNo = flowOrderDto.getBaseOrderNo();
        if (baseOrderNo == null) {
            log.warn("采购单No为空，无法更新价格");
            return;
        }

        BuyOrderApiDto buyOrderApiDto;
        if (flowOrderDto.getPushDirection() == 2) {
            // 通过远程API获取采购单信息
            buyOrderApiDto = getBuyOrderFromRemoteApi(baseOrderNo, flowOrderDto.getSourceErp());
        } else {
            // 通过本地服务获取采购单信息
            buyOrderApiDto = buyorderApiService.getBuyorderByBuyorderNo(baseOrderNo);
        }
        
        if (buyOrderApiDto == null) {
            log.warn("采购单不存在，No: {}", baseOrderNo);
            return;
        }

        // 构建SKU到价格的映射
        Map<String, BigDecimal> skuPriceMap = new HashMap<>();
        for (FlowOrderDetailDto detail : flowOrderDto.getFlowOrderDetailList()) {
            if (CollUtil.isEmpty(detail.getFlowNodeOrderDetailPriceDtoList())) {
                continue;
            }

            // 获取节点1的价格（采购单原始价格）
            BigDecimal price = detail.getFlowNodeOrderDetailPriceDtoList().stream().filter(p -> p.getNodeLevel() == 1).map(FlowNodeOrderDetailPriceDto::getPrice).findFirst().orElse(null);

            if (price != null) {
                skuPriceMap.put(detail.getSkuNo(), price);
            }
        }

        // 更新采购单商品价格
        if (MapUtil.isNotEmpty(skuPriceMap) && CollUtil.isNotEmpty(buyOrderApiDto.getBuyorderGoodsApiDtos())) {
            List<BuyorderGoodsApiDto> updatedGoods = new ArrayList<>();

            for (BuyorderGoodsApiDto goods : buyOrderApiDto.getBuyorderGoodsApiDtos()) {
                BigDecimal newPrice = skuPriceMap.get(goods.getSku());
                if (newPrice != null && !newPrice.equals(goods.getPrice())) {
                    BuyorderGoodsApiDto updatedGood = new BuyorderGoodsApiDto();
                    updatedGood.setBuyorderGoodsId(goods.getBuyorderGoodsId());
                    updatedGood.setPrice(newPrice);
                    updatedGood.setNum(goods.getNum());
                    updatedGoods.add(updatedGood);
                }
            }

            if (CollUtil.isNotEmpty(updatedGoods)) {
                if (flowOrderDto.getPushDirection() == 2) {
                    // 通过远程API更新价格
                    updateBuyOrderPricesViaRemoteApi(buyOrderApiDto.getBuyorderId(), updatedGoods, flowOrderDto.getSourceErp());
                } else {
                    // 通过本地服务更新价格
                    buyorderApiService.updateBuyorderGoodsPrices(buyOrderApiDto.getBuyorderId(), updatedGoods);
                }
                log.info("已更新采购单商品价格，采购单ID: {}，更新商品数: {}", buyOrderApiDto.getBuyorderId(), updatedGoods.size());
            }
        }
    }

    /**
     * 通过远程API更新采购单商品价格
     * 
     * @param buyorderId 采购单ID
     * @param updatedGoods 需要更新价格的商品列表
     * @param sourceErp 源ERP公司代码
     */
    private void updateBuyOrderPricesViaRemoteApi(Integer buyorderId, List<BuyorderGoodsApiDto> updatedGoods, String sourceErp) {
        try {
            log.info("通过远程API更新采购单价格，采购单ID: {}, 源ERP: {}, 商品数量: {}", buyorderId, sourceErp, updatedGoods.size());
            
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("buyorderId", buyorderId);
            requestData.put("goodsList", updatedGoods);
            
            // 调用远程API
            String response = systemApiClientProxy
                    .withCompany(sourceErp)
                    .postToSystemApi("/api/v1/buyorder/updateBuyOrderPrices.do", requestData, SystemSourceEnum.SYSTEM);
            
            log.info("远程API更新价格响应: {}", response);
            
            // 解析响应
            if (StrUtil.isBlank(response)) {
                log.error("远程API更新价格响应为空，采购单ID: {}", buyorderId);
                throw new ServiceException("更新采购单价格失败：API响应为空");
            }
            
            // 解析JSON响应
            Map<String, Object> responseMap = JSON.parseObject(response, Map.class);
            
            if (responseMap == null) {
                log.error("解析API响应失败，采购单ID: {}", buyorderId);
                throw new ServiceException("更新采购单价格失败：响应解析失败");
            }
            
            // 检查响应状态
            Integer code = (Integer) responseMap.get("code");
            String message = (String) responseMap.get("message");
            
            if (code == null || !Integer.valueOf(0).equals(code)) {
                log.error("远程API更新价格失败，采购单ID: {}, 错误码: {}, 错误信息: {}", 
                    buyorderId, code, message);
                throw new ServiceException("更新采购单价格失败：" + (message != null ? message : "未知错误"));
            }
            
            log.info("成功通过远程API更新采购单价格，采购单ID: {}", buyorderId);
            
        } catch (Exception e) {
            log.error("通过远程API更新采购单价格失败，采购单ID: {}, 源ERP: {}", buyorderId, sourceErp, e);
            if (e instanceof ServiceException) {
                throw e;
            }
            throw new ServiceException("更新采购单价格失败：" + e.getMessage());
        }
    }


    @Override
    public CornerNumDto cornerNum(PageParam<FlowOrderReqDto> pageParam) {
        pageParam.setPageSize(0);
        FlowOrderReqDto param = pageParam.getParam();
        // 采购
        param.setAuditStatus(0);
        param.setBaseBusinessType(1);
        PageInfo<FlowOrderRespDto> buyOrder = PageHelper.startPage(pageParam).doSelectPageInfo(() -> flowNodeMapper.getPageList(pageParam.getParam()));

        // 销售
        param.setAuditStatus(0);
        param.setBaseBusinessType(2);
        PageInfo<FlowOrderRespDto> saleOrder = PageHelper.startPage(pageParam).doSelectPageInfo(() -> flowNodeMapper.getPageList(pageParam.getParam()));

        CornerNumDto cornerNumDto = new CornerNumDto();
        cornerNumDto.setBuyOrderNoAuditNum(buyOrder.getTotal());
        cornerNumDto.setSaleOrderNoAuditNum(saleOrder.getTotal());
        return cornerNumDto;
    }

    @Override
    public List<FlowOrderBuySaleOrderDto> getBuySaleOrderInfo(Long flowOrderId, Integer baseBusinessType) {
        if (Objects.equals(baseBusinessType, 1)) {
            List<FlowOrderBuySaleOrderDto> flowOrderBuySaleOrderDtos = buyOrderList(flowOrderId, baseBusinessType);
            return flowOrderBuySaleOrderDtos;
        }
        if (Objects.equals(baseBusinessType, 2)) {
            List<FlowOrderBuySaleOrderDto> flowOrderBuySaleOrderDtos = saleOrderList(flowOrderId, baseBusinessType);
            return flowOrderBuySaleOrderDtos;
        }

        return null;
    }

    private List<FlowOrderBuySaleOrderDto> buyOrderList(Long flowOrderId, Integer baseBusinessType) {
        List<FlowOrderBuySaleOrderDto> nodeList = flowNodeMapper.findBuySaleOrderInfo(flowOrderId, baseBusinessType);
        for (FlowOrderBuySaleOrderDto flowOrderBuySaleOrderDto : nodeList) {
            // 解析INVOICE_INFO
            if (StrUtil.isNotBlank(flowOrderBuySaleOrderDto.getBuyOrderInvoiceInfo())) {
                List<FlowInvoiceDto> invoiceDtoList = JSONUtil.toList(JSONUtil.parseArray(flowOrderBuySaleOrderDto.getBuyOrderInvoiceInfo()), FlowInvoiceDto.class);
                flowOrderBuySaleOrderDto.setBuyOrderInvoiceDto(invoiceDtoList);
            }
        }
        if (CollUtil.isEmpty(nodeList)) {
            return Collections.emptyList();
        }
        // 获取FlowOrder实体以判断推送方向
        FlowOrderEntity flowOrderEntity = flowOrderMapper.selectByPrimaryKey(flowOrderId);
        FlowOrderBuySaleOrderDto buyOrderInfo = null;
        
        if (Objects.nonNull(flowOrderEntity) && flowOrderEntity.getPushDirection() == 2) {
            // 推送方向为ERP，需要调用远程API获取0节点数据
            String baseOrderNo = flowOrderEntity.getBaseOrderNo();
            String sourceErp = flowOrderEntity.getSourceErp();
            
            try {
                BuyOrderApiDto buyOrderApiDto = getBuyOrderFromRemoteApi(baseOrderNo, sourceErp);
                if (Objects.nonNull(buyOrderApiDto)) {
                    // 将远程API数据转换为FlowOrderBuySaleOrderDto格式，组成0节点
                    buyOrderInfo = new FlowOrderBuySaleOrderDto();
                    buyOrderInfo.setNodeLevel(0);
                    buyOrderInfo.setBuyOrderNo(buyOrderApiDto.getBuyorderNo());
                    buyOrderInfo.setBuyOrderId(buyOrderApiDto.getBuyorderId());
                    
                    // 状态字段映射（转换为字符串格式以匹配数据库字段类型）
                    if (buyOrderApiDto.getPaymentStatus() != null) {
                        buyOrderInfo.setBuyOrderPayStatus(String.valueOf(buyOrderApiDto.getPaymentStatus()));
                    }
                    if (buyOrderApiDto.getArrivalStatus() != null) {
                        buyOrderInfo.setBuyOrderInStatus(String.valueOf(buyOrderApiDto.getArrivalStatus()));
                    }
                    if (buyOrderApiDto.getInvoiceStatus() != null) {
                        buyOrderInfo.setBuyOrderInvoiceStatus(String.valueOf(buyOrderApiDto.getInvoiceStatus()));
                    }
                    if (buyOrderApiDto.getContractUrl() != null) {
                        buyOrderInfo.setContractUrl(buyOrderApiDto.getContractUrl());
                    }
                }
            } catch (Exception e) {
                log.error("调用远程API获取采购单数据失败，flowOrderId: {}, baseOrderNo: {}", flowOrderId, baseOrderNo, e);
            }
        } else {
            // 推送方向为金蝶或其他，使用原有逻辑
            buyOrderInfo = flowNodeMapper.getBuyOrderInfo(flowOrderId);
        }
        
        if (Objects.nonNull(buyOrderInfo)) {
            List<FlowInvoiceDto> buyOrderInvoice = flowNodeMapper.findBuyOrderInvoice(flowOrderId);
            buyOrderInfo.setBuyOrderInvoiceDto(buyOrderInvoice);
            nodeList.add(buyOrderInfo);
        }

        // 获取最大节点交易商名称，判断是否需要上传合同
        String maxNodeTraderName = flowNodeMapper.getMaxNodeTraderName(flowOrderId);
        boolean isMaxNodeBeidengSubsidiary = isBeidengSubsidiary(maxNodeTraderName);

        // 按nodeLevel排序 从小到大
        nodeList = nodeList.stream().sorted(Comparator.comparing(FlowOrderBuySaleOrderDto::getNodeLevel)).collect(Collectors.toList());

        // 找到最大节点级别（末级节点）
        Integer maxNodeLevel = nodeList.stream().mapToInt(FlowOrderBuySaleOrderDto::getNodeLevel).max().orElse(-1);

        for (int i = 0; i < nodeList.size(); i++) {
            FlowOrderBuySaleOrderDto flowOrderBuySaleOrderDto = nodeList.get(i);

            String buyOrderInvoiceInfo = flowOrderBuySaleOrderDto.getBuyOrderInvoiceInfo();
            if (StrUtil.isNotBlank(buyOrderInvoiceInfo)) {
                flowOrderBuySaleOrderDto.setBuyOrderInvoiceDto(JSONUtil.toList(buyOrderInvoiceInfo, FlowInvoiceDto.class));
            }
            String saleOrderInvoiceInfo = flowOrderBuySaleOrderDto.getSaleOrderInvoiceInfo();
            if (StrUtil.isNotBlank(saleOrderInvoiceInfo)) {
                flowOrderBuySaleOrderDto.setSaleOrderInvoiceDto(JSONUtil.toList(saleOrderInvoiceInfo, FlowInvoiceDto.class));
            }
            Integer nodeLevel = flowOrderBuySaleOrderDto.getNodeLevel();

            // 设置是否需要上传合同：仅在末级节点且有采购信息的行显示，且最大节点交易商不是贝登子公司
            boolean hasBuyOrderInfo = StrUtil.isNotBlank(flowOrderBuySaleOrderDto.getBuyOrderNo());
            boolean isMaxLevelNode = Objects.equals(nodeLevel, maxNodeLevel - 1);
            flowOrderBuySaleOrderDto.setNeedUploadContract(hasBuyOrderInfo && isMaxLevelNode && !isMaxNodeBeidengSubsidiary);

            if (Objects.equals(nodeLevel, 0)) {
                flowOrderBuySaleOrderDto.setBuyOrderNode("0-1");
            } else if (i == nodeList.size() - 1) {
                // 处理最后一个节点
                flowOrderBuySaleOrderDto.setSaleOrderNode(nodeLevel + "-" + (nodeLevel - 1));
            } else {
                flowOrderBuySaleOrderDto.setBuyOrderNode(nodeLevel + "-" + (nodeLevel + 1));
                flowOrderBuySaleOrderDto.setSaleOrderNode(nodeLevel + "-" + (nodeLevel - 1));
            }
        }
        return nodeList;
    }

    private List<FlowOrderBuySaleOrderDto> saleOrderList(Long flowOrderId, Integer baseBusinessType) {
        List<FlowOrderBuySaleOrderDto> nodeList = flowNodeMapper.findBuySaleOrderInfo(flowOrderId, baseBusinessType);
        for (FlowOrderBuySaleOrderDto flowOrderBuySaleOrderDto : nodeList) {
            // 解析INVOICE_INFO
            if (StrUtil.isNotBlank(flowOrderBuySaleOrderDto.getBuyOrderInvoiceInfo())) {
                List<FlowInvoiceDto> invoiceDtoList = JSONUtil.toList(JSONUtil.parseArray(flowOrderBuySaleOrderDto.getBuyOrderInvoiceInfo()), FlowInvoiceDto.class);
                flowOrderBuySaleOrderDto.setSaleOrderInvoiceDto(invoiceDtoList);
            }
        }
        if (CollUtil.isEmpty(nodeList)) {
            return Collections.emptyList();
        }
        // 查询原单
        FlowOrderBuySaleOrderDto saleOrderInfo = flowNodeMapper.getSaleOrderInfo(flowOrderId);
        if (Objects.nonNull(saleOrderInfo)) {
            List<FlowInvoiceDto> buyOrderInvoice = flowNodeMapper.findSaleOrderInvoice(flowOrderId);
            saleOrderInfo.setSaleOrderInvoiceDto(buyOrderInvoice);
            nodeList.add(saleOrderInfo);
        }

        // 获取最大节点交易商名称，判断是否需要上传合同
        String maxNodeTraderName = flowNodeMapper.getMaxNodeTraderName(flowOrderId);
        boolean isMaxNodeBeidengSubsidiary = isBeidengSubsidiary(maxNodeTraderName);

        // 按nodeLevel排序 从小到大
        nodeList = nodeList.stream().sorted(Comparator.comparing(FlowOrderBuySaleOrderDto::getNodeLevel)).collect(Collectors.toList());

        // 找到最大节点级别（末级节点）
        Integer maxNodeLevel = nodeList.stream().mapToInt(FlowOrderBuySaleOrderDto::getNodeLevel).max().orElse(-1);

        for (int i = 0; i < nodeList.size(); i++) {
            FlowOrderBuySaleOrderDto flowOrderBuySaleOrderDto = nodeList.get(i);

            String buyOrderInvoiceInfo = flowOrderBuySaleOrderDto.getBuyOrderInvoiceInfo();
            if (StrUtil.isNotBlank(buyOrderInvoiceInfo)) {
                flowOrderBuySaleOrderDto.setBuyOrderInvoiceDto(JSONUtil.toList(buyOrderInvoiceInfo, FlowInvoiceDto.class));
            }
            String saleOrderInvoiceInfo = flowOrderBuySaleOrderDto.getSaleOrderInvoiceInfo();
            if (StrUtil.isNotBlank(saleOrderInvoiceInfo)) {
                flowOrderBuySaleOrderDto.setSaleOrderInvoiceDto(JSONUtil.toList(saleOrderInvoiceInfo, FlowInvoiceDto.class));
            }
            Integer nodeLevel = flowOrderBuySaleOrderDto.getNodeLevel();

            // 设置是否需要上传合同：仅在末级节点且有采购信息的行显示，且最大节点交易商不是贝登子公司
            boolean hasBuyOrderInfo = StrUtil.isNotBlank(flowOrderBuySaleOrderDto.getBuyOrderNo());
            boolean isMaxLevelNode = Objects.equals(nodeLevel, maxNodeLevel - 1);
            flowOrderBuySaleOrderDto.setNeedUploadContract(hasBuyOrderInfo && isMaxLevelNode && !isMaxNodeBeidengSubsidiary);

            if (Objects.equals(nodeLevel, 0)) {
                flowOrderBuySaleOrderDto.setSaleOrderNode("0-1");
            } else if (i == nodeList.size() - 1) {
                // 处理最后一个节点
                flowOrderBuySaleOrderDto.setBuyOrderNode(nodeLevel + "-" + (nodeLevel - 1));
            } else {
                flowOrderBuySaleOrderDto.setBuyOrderNode(nodeLevel + "-" + (nodeLevel - 1));
                flowOrderBuySaleOrderDto.setSaleOrderNode(nodeLevel + "-" + (nodeLevel + 1));
            }
        }
        return nodeList;
    }


    private static final int PRICE_SCALE = 4;
    private static final int RATE_SCALE = 4;

    @Override
    public FlowOrderDto calculateNodeAmount(FlowOrderDto flowOrderDto) {
        boolean isPurchaseOrder = flowOrderDto.getBaseBusinessType() == 1;

        for (FlowOrderDetailDto detail : flowOrderDto.getFlowOrderDetailList()) {
            List<FlowNodeOrderDetailPriceDto> prices = detail.getFlowNodeOrderDetailPriceDtoList();

            if (isPurchaseOrder) {
                processPurchaseOrder(prices);
            } else {
                processSalesOrder(prices);
            }
        }
        return flowOrderDto;
    }

    /**
     * 采购订单处理（下游传播）
     */
    private void processPurchaseOrder(List<FlowNodeOrderDetailPriceDto> prices) {
        prices.sort(Comparator.comparingInt(FlowNodeOrderDetailPriceDto::getNodeLevel));

        for (int i = 0; i < prices.size(); i++) {
            FlowNodeOrderDetailPriceDto current = prices.get(i);
            if (!isNodeModified(current)) {
                continue;
            }

            if (isCalculatePriceFromMarkupRate(current)) {
                calculateDownstreamFromMarkupRate(prices, i);
            } else {
                updateUpstreamMarkupRate(prices, i);
                calculateDownstreamFromMarkupRate(prices, i);
            }
        }
    }


    // ------------------- 采购单专用逻辑 -------------------

    /**
     * 更新上游加价率（采购单反向计算）
     */
    private void updateUpstreamMarkupRate(List<FlowNodeOrderDetailPriceDto> prices, int currentIndex) {
        if (currentIndex >= prices.size() - 1) {
            return;
        }

        FlowNodeOrderDetailPriceDto current = prices.get(currentIndex);
        FlowNodeOrderDetailPriceDto upstream = prices.get(currentIndex + 1);

        upstream.setMarkupRate(calculateMarkupRate(current.getPrice(), upstream.getPrice()));
        calculateGrossProfitMargin(current, upstream.getPrice());
    }

    /**
     * 计算下游价格链（采购单）
     */
    private void calculateDownstreamFromMarkupRate(List<FlowNodeOrderDetailPriceDto> prices, int startIndex) {
        if (startIndex <= 0) {
            return;
        }

        FlowNodeOrderDetailPriceDto upstream = prices.get(startIndex);
        for (int i = startIndex - 1; i >= 0; i--) {
            FlowNodeOrderDetailPriceDto current = prices.get(i);

            current.setPrice(calculatePriceFromMarkup(upstream.getPrice(), upstream.getMarkupRate()));
            if (i > 0) {
                FlowNodeOrderDetailPriceDto downstream = prices.get(i - 1);
                current.setMarkupRate(calculateMarkupRate(downstream.getPrice(), current.getPrice()));
            }
            calculateGrossProfitMargin(current, upstream.getPrice());
            upstream = current;
        }
    }

    // ------------------- 销售单专用逻辑 -------------------

    /**
     * 销售订单处理（上游传播）
     */
    private void processSalesOrder(List<FlowNodeOrderDetailPriceDto> prices) {
        prices.sort(Comparator.comparingInt(FlowNodeOrderDetailPriceDto::getNodeLevel));

        // 预计算前驱节点
        Map<FlowNodeOrderDetailPriceDto, FlowNodeOrderDetailPriceDto> predecessors = buildPredecessorMap(prices);

        for (FlowNodeOrderDetailPriceDto current : prices) {
            if (!isNodeModified(current)) {
                continue;
            }

            FlowNodeOrderDetailPriceDto pred = predecessors.get(current);
            if (isCalculatePriceFromMarkupRate(current)) {
                updatePriceFromMarkup(current, pred);
            } else {
                updateMarkupFromPrice(current, pred);
            }
            propagateChanges(prices, current, predecessors);
        }
    }

    /**
     * 构建前驱节点映射（销售单专用）
     */
    private Map<FlowNodeOrderDetailPriceDto, FlowNodeOrderDetailPriceDto> buildPredecessorMap(List<FlowNodeOrderDetailPriceDto> nodes) {

        Map<FlowNodeOrderDetailPriceDto, FlowNodeOrderDetailPriceDto> map = new HashMap<>();
        Stack<FlowNodeOrderDetailPriceDto> stack = new Stack<>();

        for (FlowNodeOrderDetailPriceDto node : nodes) {
            while (!stack.isEmpty() && stack.peek().getNodeLevel() >= node.getNodeLevel()) {
                stack.pop();
            }
            map.put(node, stack.isEmpty() ? null : stack.peek());
            stack.push(node);
        }
        return map;
    }

    /**
     * 通过加价率更新价格（销售单正向计算）
     */
    private void updatePriceFromMarkup(FlowNodeOrderDetailPriceDto current, FlowNodeOrderDetailPriceDto predecessor) {
        if (predecessor == null) {
            return;
        }

        current.setPrice(calculatePriceFromMarkup(predecessor.getPrice(), current.getMarkupRate()));
        calculateGrossProfitMargin(current, predecessor.getPrice());
    }

    /**
     * 通过价格更新加价率（销售单反向计算）
     */
    private void updateMarkupFromPrice(FlowNodeOrderDetailPriceDto current, FlowNodeOrderDetailPriceDto predecessor) {
        if (predecessor == null) {
            return;
        }

        current.setMarkupRate(calculateMarkupRate(current.getPrice(), predecessor.getPrice()));
        calculateGrossProfitMargin(current, predecessor.getPrice());
    }

    /**
     * 传播变更到下游节点（销售单）
     */
    private void propagateChanges(List<FlowNodeOrderDetailPriceDto> nodes, FlowNodeOrderDetailPriceDto startNode, Map<FlowNodeOrderDetailPriceDto, FlowNodeOrderDetailPriceDto> predecessors) {
        int startIndex = nodes.indexOf(startNode);
        for (int i = startIndex; i < nodes.size(); i++) {
            FlowNodeOrderDetailPriceDto current = nodes.get(i);
            FlowNodeOrderDetailPriceDto pred = predecessors.get(current);

            if (pred != null) {
                current.setPrice(calculatePriceFromMarkup(pred.getPrice(), current.getMarkupRate()));
            }
        }
    }


    /**
     * 计算加价率
     */
    private BigDecimal calculateMarkupRate(BigDecimal currentPrice, BigDecimal basePrice) {
        if (basePrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return currentPrice.subtract(basePrice).divide(basePrice, RATE_SCALE, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(RATE_SCALE, RoundingMode.HALF_UP);
    }

    /**
     * 根据加价率计算价格
     */
    private BigDecimal calculatePriceFromMarkup(BigDecimal basePrice, BigDecimal markupRate) {
        return basePrice.multiply(BigDecimal.ONE.add(markupRate.divide(BigDecimal.valueOf(100), RATE_SCALE, RoundingMode.HALF_UP)).setScale(PRICE_SCALE, RoundingMode.HALF_UP));
    }

    /**
     * 计算毛利率（成本价到销售价）
     */
    private void calculateGrossProfitMargin(FlowNodeOrderDetailPriceDto node, BigDecimal costPrice) {
        BigDecimal salePrice = node.getPrice();
        if (salePrice.compareTo(BigDecimal.ZERO) == 0) {
            node.setGrossProfitRate(BigDecimal.ZERO);
            return;
        }

        BigDecimal margin = salePrice.subtract(costPrice).divide(salePrice, RATE_SCALE, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(RATE_SCALE, RoundingMode.HALF_UP);
        node.setGrossProfitRate(margin);
    }

    private boolean isNodeModified(FlowNodeOrderDetailPriceDto node) {
        String flag = node.getCalculatePriceFromMarkupRate();
        return flag != null && flag.length() >= 2 && flag.charAt(0) == '1';
    }

    private boolean isCalculatePriceFromMarkupRate(FlowNodeOrderDetailPriceDto node) {
        String flag = node.getCalculatePriceFromMarkupRate();
        return flag != null && flag.length() >= 2 && flag.charAt(1) == '1';
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public String uploadContract(HttpServletRequest request, MultipartFile file, Long flowOrderId, Integer nodeLevel, Integer flowOrderInfoType) {
        // 1. 文件验证
        validateContractFile(file);

        // 2. 生成文件名
        String fileName = generateContractFileName(flowOrderId, file.getOriginalFilename());

        // 3. 上传文件到OSS
        FileInfo fileInfo = ossUtilsService.upload2OssByOriginalName(request, file);
        if (fileInfo.getCode() != 0) {
            throw new ServiceException("文件上传失败：" + fileInfo.getMessage());
        }
        String contractUrl = fileInfo.getScheme() + fileInfo.getOssUrl();
        log.info("contractUrl:{}", contractUrl);

        // 4. 更新数据库中的合同URL
        updateContractUrl(flowOrderId, nodeLevel, flowOrderInfoType, contractUrl);

        // 5. 检查并更新流转单的合同状态
        updateFlowOrderContractStatus(flowOrderId);

        log.info("合同上传成功，流转单ID: {}, 节点级别: {}, 合同URL: {}", flowOrderId, nodeLevel, contractUrl);
        return contractUrl;
    }

    /**
     * 验证合同文件
     */
    private void validateContractFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("请选择要上传的文件");
        }

        // 文件大小验证（10MB）
        long maxSize = 10 * 1024 * 1024;
        if (file.getSize() > maxSize) {
            throw new ServiceException("文件大小不能超过10MB");
        }

        // 文件格式验证
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename) || !originalFilename.toLowerCase().endsWith(".pdf")) {
            throw new ServiceException("只支持PDF格式的文件");
        }

    }

    /**
     * 生成合同文件名
     */
    private String generateContractFileName(Long flowOrderId, String originalFilename) {
        // 获取流转单号
        FlowOrderEntity flowOrder = flowOrderMapper.selectByPrimaryKey(flowOrderId);
        if (flowOrder == null) {
            throw new ServiceException("流转单不存在");
        }

        String flowOrderNo = flowOrder.getFlowOrderNo();
        String timestamp = String.valueOf(System.currentTimeMillis());
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));

        return String.format("contract/%s_合同_%s%s", flowOrderNo, timestamp, extension);
    }

    /**
     * 更新合同URL到数据库
     */
    private void updateContractUrl(Long flowOrderId, Integer nodeLevel, Integer flowOrderInfoType, String contractUrl) {
        // 查找对应的FlowOrderInfo记录
        FlowOrderInfoEntity flowOrderInfo = findFlowOrderInfo(flowOrderId, nodeLevel, flowOrderInfoType);

        if (flowOrderInfo == null) {
            // 如果不存在，创建新记录
            flowOrderInfo = createFlowOrderInfo(flowOrderId, nodeLevel, flowOrderInfoType, contractUrl);
            flowOrderInfoMapper.insertSelective(flowOrderInfo);
        } else {
            // 如果存在，更新合同URL
            flowOrderInfo.setContractFileUrl(contractUrl);
            flowOrderInfoMapper.updateByPrimaryKeySelective(flowOrderInfo);
        }
    }

    /**
     * 查找FlowOrderInfo记录
     */
    private FlowOrderInfoEntity findFlowOrderInfo(Long flowOrderId, Integer nodeLevel, Integer flowOrderInfoType) {
        // 根据flowOrderId和nodeLevel查找对应的FlowNodeId
        List<FlowNodeEntity> flowNodes = flowNodeMapper.findByFlowOrderId(flowOrderId);
        Long flowNodeId = null;

        for (FlowNodeEntity node : flowNodes) {
            if (Objects.equals(node.getNodeLevel(), nodeLevel)) {
                flowNodeId = node.getFlowNodeId();
                break;
            }
        }

        if (flowNodeId == null) {
            throw new ServiceException("未找到对应的流转节点");
        }

        // 查找FlowOrderInfo记录
        List<FlowOrderInfoEntity> flowOrderInfos = flowOrderInfoMapper.findByFlowNodeId(flowNodeId);
        for (FlowOrderInfoEntity info : flowOrderInfos) {
            if (Objects.equals(info.getFlowOrderInfoType(), flowOrderInfoType) && Objects.equals(info.getIsDelete(), 0)) {
                return info;
            }
        }

        return null;
    }

    /**
     * 创建新的FlowOrderInfo记录
     */
    private FlowOrderInfoEntity createFlowOrderInfo(Long flowOrderId, Integer nodeLevel, Integer flowOrderInfoType, String contractUrl) {
        // 根据flowOrderId和nodeLevel查找对应的FlowNodeId
        List<FlowNodeEntity> flowNodes = flowNodeMapper.findByFlowOrderId(flowOrderId);
        Long flowNodeId = null;

        for (FlowNodeEntity node : flowNodes) {
            if (Objects.equals(node.getNodeLevel(), nodeLevel)) {
                flowNodeId = node.getFlowNodeId();
                break;
            }
        }

        if (flowNodeId == null) {
            throw new ServiceException("未找到对应的流转节点");
        }

        FlowOrderInfoEntity flowOrderInfo = new FlowOrderInfoEntity();
        flowOrderInfo.setFlowNodeId(flowNodeId);
        flowOrderInfo.setFlowOrderInfoType(flowOrderInfoType);
        flowOrderInfo.setContractFileUrl(contractUrl);
        flowOrderInfo.setIsDelete(0);

        return flowOrderInfo;
    }

    /**
     * 检查并更新流转单的合同状态
     *
     * @param flowOrderId 流转单ID
     */
    private void updateFlowOrderContractStatus(Long flowOrderId) {
        // 获取流转单信息
        FlowOrderEntity flowOrder = flowOrderMapper.selectByPrimaryKey(flowOrderId);
        if (flowOrder == null) {
            log.warn("流转单不存在，无法更新合同状态，flowOrderId: {}", flowOrderId);
            return;
        }
        // 更新流转单的合同状态
        flowOrder.setContractStatus(2);
        flowOrderMapper.updateByPrimaryKeySelective(flowOrder);
        log.info("成功更新流转单合同状态，flowOrderId: {}, 新状态: {}", flowOrderId, 2);
    }

    /**
     * 通过远程API获取采购单数据
     * 
     * @param buyorderNo 采购单号
     * @param sourceErp 源ERP公司代码
     * @return 采购单数据
     */
    private BuyOrderApiDto getBuyOrderFromRemoteApi(String buyorderNo, String sourceErp) {
        try {
            log.info("通过远程API获取采购单数据，采购单号: {}, 源ERP: {}", buyorderNo, sourceErp);
            
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("buyorderNo", buyorderNo);
            
            // 调用远程API
            String response = systemApiClientProxy
                    .withCompany(sourceErp)
                    .postToSystemApi("/api/v1/buyorder/query.do", requestData, SystemSourceEnum.SYSTEM);
            
            log.info("远程API响应: {}", response);
            
            // 解析响应
            if (StrUtil.isBlank(response)) {
                log.error("远程API响应为空，采购单号: {}", buyorderNo);
                throw new ServiceException("获取采购单数据失败：API响应为空");
            }
            
            // 解析JSON响应，手动提取data字段
            Map<String, Object> responseMap = JSON.parseObject(response, Map.class);
            
            if (responseMap == null) {
                log.error("解析API响应失败，采购单号: {}", buyorderNo);
                throw new ServiceException("获取采购单数据失败：响应解析失败");
            }
            
            // 检查响应状态 (ApiResponse格式: code=0表示成功)
            Integer code = (Integer) responseMap.get("code");
            String message = (String) responseMap.get("message");
            
            if (code == null || !Integer.valueOf(0).equals(code)) {
                log.error("远程API调用失败，采购单号: {}, 错误码: {}, 错误信息: {}", 
                    buyorderNo, code, message);
                throw new ServiceException("获取采购单数据失败：" + (message != null ? message : "未知错误"));
            }
            
            // 获取data字段并解析为BuyOrderApiDto
            Object dataObj = responseMap.get("data");
            if (dataObj == null) {
                log.error("API响应数据为空，采购单号: {}", buyorderNo);
                throw new ServiceException("获取采购单数据失败：数据为空");
            }
            
            // 将data对象转换为BuyOrderApiDto
            BuyOrderApiDto buyOrderApiDto = JSON.parseObject(JSON.toJSONString(dataObj), BuyOrderApiDto.class);
            if (buyOrderApiDto == null) {
                log.error("转换BuyOrderApiDto失败，采购单号: {}", buyorderNo);
                throw new ServiceException("获取采购单数据失败：数据转换失败");
            }
            
            log.info("成功获取采购单数据，采购单号: {}, 采购单ID: {}", buyorderNo, buyOrderApiDto.getBuyorderId());
            return buyOrderApiDto;
            
        } catch (Exception e) {
            log.error("通过远程API获取采购单数据失败，采购单号: {}, 源ERP: {}", buyorderNo, sourceErp, e);
            if (e instanceof ServiceException) {
                throw e;
            }
            throw new ServiceException("获取采购单数据失败：" + e.getMessage());
        }
    }

    /**
     * 验证ERP推送方向的子公司ERP链接
     * 当推送方向为ERP时，需要检查除末级节点外，所有其他节点都必须是有ERP链接的子公司
     * 
     * @param flowOrderDto 业务流转单数据
     */
    private void validateErpSubsidiariesForErpPush(FlowOrderDto flowOrderDto) {
        // 只有推送方向为ERP时才需要验证
        if (flowOrderDto.getPushDirection() == null || !flowOrderDto.getPushDirection().equals(2)) {
            return;
        }
        
        List<FlowNodeDto> flowNodeList = flowOrderDto.getFlowNodeDto();
        if (CollUtil.isEmpty(flowNodeList)) {
            throw new ServiceException("推送方向为ERP时，流程节点不能为空");
        }
        
        // 找到末级节点（nodeLevel最大的节点）
        FlowNodeDto lastNode = flowNodeList.stream()
                .max(Comparator.comparing(FlowNodeDto::getNodeLevel))
                .orElse(null);
        
        if (lastNode == null) {
            throw new ServiceException("无法确定末级节点");
        }
        
        Integer maxNodeLevel = lastNode.getNodeLevel();
        log.info("确定末级节点级别: {}, 交易者: {}", maxNodeLevel, lastNode.getTraderName());
        
        // 检查除末级节点外的所有节点
        for (FlowNodeDto node : flowNodeList) {
            // 跳过末级节点
            if (Objects.equals(node.getNodeLevel(), maxNodeLevel)) {
                log.info("跳过末级节点: {} - {}", node.getNodeLevel(), node.getTraderName());
                continue;
            }
            
            String traderName = node.getTraderName();
            if (StrUtil.isBlank(traderName)) {
                throw new ServiceException(String.format("节点%d的交易者名称不能为空", node.getNodeLevel()));
            }
            
            // 查询是否为子公司且有ERP链接
            BaseCompanyInfoEntity company = baseCompanyInfoService.selectByCompanyName(traderName);
            if (company == null) {
                throw new ServiceException(String.format("节点%d的交易者'%s'不是系统子公司", node.getNodeLevel(), traderName));
            }
            
            if (StrUtil.isBlank(company.getErpDomain())) {
                throw new ServiceException(String.format("节点%d的子公司'%s'没有配置ERP链接", node.getNodeLevel(), traderName));
            }
            
            log.info("节点{}验证通过: {} - ERP域名: {}", node.getNodeLevel(), traderName, company.getErpDomain());
        }
        
        log.info("ERP推送方向验证通过：除末级节点({})外，所有节点都是有ERP链接的子公司", maxNodeLevel);
    }

}
