package com.vedeng.crm.follow.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.vdurmont.emoji.EmojiParser;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.common.util.CrmEmojiUtils;
import com.vedeng.crm.follow.domain.dto.FollowUpRecordPageResponseDto;
import com.vedeng.crm.follow.service.FollowUpRecordService;
import com.vedeng.crm.task.domain.dto.TaskDto;
import com.vedeng.crm.task.domain.dto.TaskHandleDto;
import com.vedeng.crm.task.domain.dto.TaskItemDto;
import com.vedeng.crm.task.domain.dto.TaskQueryDto;
import com.vedeng.crm.task.domain.vo.MyTaskVo;
import com.vedeng.crm.task.service.TaskService;
import com.vedeng.crm.visitrecord.api.CrmVisitApiService;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.erp.business.common.enums.BusinessChanceStageEnum;
import com.vedeng.erp.business.common.enums.BusinessLeadsFollowStatusEnums;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.domain.entity.BusinessLeadsEntity;
import com.vedeng.erp.business.domain.entity.BussinessChanceEntity;
import com.vedeng.erp.business.feign.CommunicateRecordApiService;
import com.vedeng.erp.business.mapper.BusinessLeadsMapper;
import com.vedeng.erp.business.mapper.BussinessChanceMapper;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.common.enums.CommunicateRecordTypeEnum;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecord;
import com.vedeng.erp.trader.dto.CommunicateTelRecordApiDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecordParams;
import com.vedeng.erp.trader.dto.FollowBindingTelParams;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 跟进记录服务实现
 */
@Service
@Slf4j
public class FollowUpRecordServiceImpl implements FollowUpRecordService {
    
    @Autowired
    private CommunicateRecordApiService communicateRecordApiService;

    @Autowired
    private BusinessChanceService businessChanceService;

    @Autowired
    private BusinessLeadsService businessLeadsService;
    
    @Autowired
    private TaskService taskService;

    @Autowired
    private BussinessChanceMapper bussinessChanceMapper;


    @Autowired
    private BusinessLeadsMapper businessLeadsMapper;

    @Value("${erp_url}")
    private String erpUrl;

    @Autowired
    private CrmVisitApiService crmVisitApiService;
    
    @Autowired
    private UserApiService userApiService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void add(CommunicateRecordDto dto) {
        log.info("CRM跟进记录，添加入参:{}", JSON.toJSONString(dto));
        String contentSuffix = CrmEmojiUtils.removeAllEmojis(dto.getContentSuffix());//表情符替换
        dto.setContentSuffix(contentSuffix);

        CurrentUser currentUser = CurrentUser.getCurrentUser();
        // 1.调用erp沟通记录接口
        dto.setBegintime(dto.getBeginTimeDate()!=null?dto.getBeginTimeDate().getTime():System.currentTimeMillis());
        dto.setIsLfasr(ErpConstant.ZERO);
        dto.setCompanyId(ErpConstant.ONE);
        dto.setCreator(currentUser.getId());
        dto.setAddTime(System.currentTimeMillis());
        ValidatorUtils.validate(dto, AddGroup.class);
        log.info("CRM跟进记录，调用erp接口入参:{}", JSON.toJSONString(dto));
        try {
            communicateRecordApiService.add(dto);
        } catch (Exception e) {
            log.info("CRM跟进记录，添加跟进记录异常", e);
            throw new ServiceException("添加跟进记录异常");
        }
        // 2.添加待办任务
        if (!ErpConstant.ONE.equals(dto.getNoneNextDate()) && Objects.nonNull(dto.getNextContactDate())) {


            log.info("CRM跟进记录，添加待办任务，nextContactDate:{}", dto.getNextContactDate());
            Integer communicateType = dto.getCommunicateType();
            Integer relatedId = dto.getRelatedId();
            TaskDto taskDto = new TaskDto();
            String format = String.format("%s\n 联系人：%s\n 手机：%s\n", dto.getNextContactContent(), dto.getContact(), dto.getContactMob());
            taskDto.setTaskContent(format);
            if (CommunicateRecordTypeEnum.BUSINESS_CHANCE.getCode().equals(communicateType)) {
                taskDto.setBizType(ErpConstant.ONE);
                BussinessChanceEntity entity =  bussinessChanceMapper.selectByPrimaryKey(relatedId);
                if(entity !=null){
                    taskDto.setBizNo(entity.getBussinessChanceNo());
                }

            }
            if (CommunicateRecordTypeEnum.BUSINESS_LEAD.getCode().equals(communicateType)) {
                taskDto.setBizType(ErpConstant.TWO);
                BusinessLeadsEntity entity =  businessLeadsMapper.selectByPrimaryKey(relatedId);
                if(entity !=null){
                    taskDto.setBizNo(entity.getLeadsNo());
                }
            }
            taskDto.setBizId(relatedId);
            taskDto.setMainTaskType(ErpConstant.FIVE);
            taskDto.setCommitTime(new Date());
            taskDto.setDeadline(DateUtil.endOfDay(dto.getNextContactDate()));
            taskDto.setTodoUserList(Collections.singletonList(currentUser.getId()));
            taskService.save(taskDto);
        }  
        // 3.不同类型各自后续的业务逻辑
        processBusinessLogic(dto);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void processBusinessLogic(CommunicateRecordDto communicateRecordDto) {
        Integer communicateType = communicateRecordDto.getCommunicateType();
        // 商机后续业务
        if (CommunicateRecordTypeEnum.BUSINESS_CHANCE.getCode().equals(communicateType)) {
            log.info("CRM跟进记录，商机后续业务，更新商机阶段，businessChanceId:{}", communicateRecordDto.getRelatedId());
            businessChanceService.updateStageAndStageTime(communicateRecordDto.getRelatedId(), BusinessChanceStageEnum.PRELIMINARY_NEGOTIATION);
        }
        // 线索后续业务
        if (CommunicateRecordTypeEnum.BUSINESS_LEAD.getCode().equals(communicateType)) {
            businessLeadsService.updateLeadsFollowStatusAndFirstFollowTime(communicateRecordDto.getRelatedId());
        }
    }

    @Override
    public void update(CommunicateRecordDto communicateRecordDto) {
        log.info("CRM跟进记录，修改入参:{}", JSON.toJSONString(communicateRecordDto));
        communicateRecordDto.setBegintime(communicateRecordDto.getBeginTimeDate().getTime());
        communicateRecordDto.setUpdater(CurrentUser.getCurrentUser().getId());
        communicateRecordDto.setModTime(System.currentTimeMillis());
        communicateRecordDto.setIsLfasr(ErpConstant.ZERO);
        ValidatorUtils.validate(communicateRecordDto, UpdateGroup.class);
        try {
            communicateRecordApiService.update(communicateRecordDto);
        } catch (Exception e) {
            log.info("CRM跟进记录，修改跟进记录异常", e);
            throw new ServiceException("更新跟进记录异常");
        }
    }



    @Override
    public FollowUpRecordPageResponseDto page(CommunicateRecordDto queryDto) {
        log.info("CRM跟进记录，列表入参:{}", JSON.toJSONString(queryDto));
        FollowUpRecordPageResponseDto responseDto = new FollowUpRecordPageResponseDto();
        Integer communicateType = queryDto.getCommunicateType();
        Integer relatedId = queryDto.getRelatedId();
        try {
            if (CommunicateRecordTypeEnum.BUSINESS_CHANCE.getCode().equals(communicateType)) {
                handleChanceFollowUpRecordData(relatedId, responseDto);
                // 查询下是否有关联的线索
                BusinessLeadsDto leadsDto = businessLeadsService.findByBusinessChanceId(relatedId);
                if (Objects.nonNull(leadsDto) && leadsDto.getId() >0) {
                    handleLeadsFollowUpRecordData(leadsDto.getId(), responseDto);
                }

                List<VisitRecordVo> visitRecordVoList = crmVisitApiService.selectVisitRecordByRelateId(relatedId,2);
                handleVisitFollowUpRecordData(visitRecordVoList, responseDto);

            }
            if (CommunicateRecordTypeEnum.BUSINESS_LEAD.getCode().equals(communicateType)) {
                handleLeadsFollowUpRecordData(relatedId, responseDto);
                // 查询下是否有关联的商机
                BusinessLeadsDto leadsDto = businessLeadsService.findById(relatedId);
                if (Objects.nonNull(leadsDto) && leadsDto.getBusinessChanceId() >0) {
                    handleChanceFollowUpRecordData(leadsDto.getBusinessChanceId(), responseDto);
                }
                List<VisitRecordVo> visitRecordVoList = crmVisitApiService.selectVisitRecordByRelateId(relatedId,1);

                handleVisitFollowUpRecordData(visitRecordVoList, responseDto);

            }
        } catch (Exception e) {
            log.info("CRM跟进记录，列表查询异常", e);
            throw new ServiceException("查询跟进记录异常");
        }
        return responseDto;
    }

    private void handleLeadsFollowUpRecordData(Integer relatedId, FollowUpRecordPageResponseDto responseDto) {
        //将整个方法进行trycatch
        try {
            PageParam<CommunicateRecordDto> pageParam = new PageParam<>();
            CommunicateRecordDto param = new CommunicateRecordDto();
            param.setCompanyId(1);
            param.setCommunicateType(CommunicateRecordTypeEnum.BUSINESS_LEAD.getCode());
            param.setRelatedId(relatedId);
            pageParam.setPageNum(1);
            pageParam.setPageSize(500);
            pageParam.setOrderBy("BEGINTIME desc");
            pageParam.setParam(param);
            R<PageInfo<CommunicateRecordDto>> response = communicateRecordApiService.page(pageParam);
            if (ErpConstant.ZERO.equals(response.getCode())) {
                List<CommunicateRecordDto> list = response.getData().getList();
                for (CommunicateRecordDto dto : list) {
                    if (Objects.nonNull(dto.getBegintime())) {
                        dto.setBeginTimeDate(new Date(dto.getBegintime()));
                    }
                    if(Objects.nonNull(dto.getCoidUri())){
                        dto.setAiWindowUrl(erpUrl+"/system/call/getrecordplayForAi.do?communicateRecordId="+dto.getCommunicateRecordId());
                    }
                }
                responseDto.setLeadsFollowUpRecordList(list);
            }
        }catch (Exception e) {
            log.error("获取跟进记录失败");
        }

    }

    private void handleChanceFollowUpRecordData(Integer relatedId, FollowUpRecordPageResponseDto responseDto) {
        PageParam<CommunicateRecordDto> pageParam = new PageParam<>();
        CommunicateRecordDto param = new CommunicateRecordDto();
        param.setCompanyId(1);
        param.setCommunicateType(CommunicateRecordTypeEnum.BUSINESS_CHANCE.getCode());
        param.setRelatedId(relatedId);
        pageParam.setPageNum(1);
        pageParam.setPageSize(500);
        pageParam.setOrderBy("BEGINTIME desc");
        pageParam.setParam(param);
        R<PageInfo<CommunicateRecordDto>> response = communicateRecordApiService.page(pageParam);
        if (ErpConstant.ZERO.equals(response.getCode())) {
            List<CommunicateRecordDto> list = response.getData().getList();
            for (CommunicateRecordDto dto : list) {
                if (Objects.nonNull(dto.getBegintime())) {
                    dto.setBeginTimeDate(new Date(dto.getBegintime()));
                }
                if(Objects.nonNull(dto.getCoidUri())){
                    dto.setAiWindowUrl(erpUrl+"/system/call/getrecordplayForAi.do?communicateRecordId="+dto.getCommunicateRecordId());
                }
            }
            responseDto.setChanceFollowUpRecordList(list);
        }
    }

    private void handleVisitFollowUpRecordData(List<VisitRecordVo> visitRecordVoList, FollowUpRecordPageResponseDto responseDto) {
        if(CollectionUtils.isNotEmpty(visitRecordVoList)){
            List<CommunicateRecordDto> allList = new ArrayList<>();//所有拜访计划的沟通记录
            for(VisitRecordVo visitRecordVo:visitRecordVoList){
                PageParam<CommunicateRecordDto> pageParam = new PageParam<>();
                CommunicateRecordDto param = new CommunicateRecordDto();
                param.setCompanyId(1);
                param.setCommunicateType(CommunicateRecordTypeEnum.VISIT_RECORD.getCode());
                param.setRelatedId(visitRecordVo.getId());
                pageParam.setPageNum(1);
                pageParam.setPageSize(500);
                pageParam.setOrderBy("BEGINTIME desc");
                pageParam.setParam(param);
                R<PageInfo<CommunicateRecordDto>> response = communicateRecordApiService.page(pageParam);
                if (ErpConstant.ZERO.equals(response.getCode())) {
                    List<CommunicateRecordDto> list = response.getData().getList();
                    for (CommunicateRecordDto dto : list) {
                        if (Objects.nonNull(dto.getBegintime())) {
                            dto.setBeginTimeDate(new Date(dto.getBegintime()));
                        }
                        if(Objects.nonNull(dto.getCoidUri())){
                            dto.setAiWindowUrl(erpUrl+"/system/call/getrecordplayForAi.do?communicateRecordId="+dto.getCommunicateRecordId());
                        }
                    }
                    allList.addAll(list);
                }
            }
            //将allList按communicateRecordId 从大到小排序
            Collections.sort(allList, (o1, o2) -> o2.getCommunicateRecordId().compareTo(o1.getCommunicateRecordId()));
            responseDto.setVisitFollowUpRecordList(allList);
        }


    }

    public boolean checkHasFollowUp(Integer relatedId) {
        PageParam<CommunicateRecordDto> pageParam = new PageParam<>();
        CommunicateRecordDto param = new CommunicateRecordDto();
        param.setCompanyId(1);
        param.setCommunicateType(CommunicateRecordTypeEnum.BUSINESS_LEAD.getCode());
        param.setRelatedId(relatedId);
        pageParam.setPageNum(1);
        pageParam.setPageSize(500);
        pageParam.setOrderBy("BEGINTIME desc");
        pageParam.setParam(param);
        R<PageInfo<CommunicateRecordDto>> response = communicateRecordApiService.page(pageParam);
        if (ErpConstant.ZERO.equals(response.getCode())) {
            List<CommunicateRecordDto> list = response.getData().getList();
            if(CollectionUtils.isNotEmpty(list)){
                return true;
            }
        }
        return false;
    }

    @Override
    public CommunicateRecordDto detail(Integer communicateRecordId) {
        log.info("CRM跟进记录，详情入参:{}", JSON.toJSONString(communicateRecordId));
        CommunicateRecordDto communicateRecordDto = new CommunicateRecordDto();
        communicateRecordDto.setCommunicateRecordId(communicateRecordId);
        CommunicateRecordDto data;
        try {
            data = communicateRecordApiService.getOne(communicateRecordDto).getData();
        } catch (Exception e) {
            log.info("CRM跟进记录，详情查询异常", e);
            throw new ServiceException("查询跟进记录详情异常");
        }
        if (Objects.nonNull(data)) {
            if (Objects.nonNull(data.getBegintime())) {
                data.setBeginTimeDate(new Date(data.getBegintime()));
            }
            if(Objects.nonNull(data.getCoidUri())){
                data.setAiWindowUrl(erpUrl+"/system/call/getrecordplayForAi.do?communicateRecordId="+communicateRecordId);
            }
        }
        return data;
    }

	@Override
	public CommunicateTelRecordApiDto getTelList(PageParam<CommunicateTelRecordParams> communicateTelRecordParams, CurrentUser currentUser) {
		log.info("CRM获取通话记录，详情入参:{},用户信息：{}", JSON.toJSONString(communicateTelRecordParams),JSON.toJSONString(currentUser));
		CommunicateTelRecordApiDto communicateTelRecordApiDto = new CommunicateTelRecordApiDto();
		try {
			List<Integer> userIdList = communicateTelRecordParams.getParam().getUserIdList();	
			if(CollectionUtils.isEmpty(userIdList)) {
				//查询的用户为当前登录用户以及当前登录用下的下级用户
				List<Integer> subUserIdList = userApiService.queryUserIdListSubFromUac(currentUser.getId());
				communicateTelRecordParams.getParam().setUserIdList(subUserIdList);
			}
			communicateTelRecordApiDto = communicateRecordApiService.getTelList(communicateTelRecordParams).getData();
			if(Objects.nonNull(communicateTelRecordApiDto) && CollectionUtils.isNotEmpty(communicateTelRecordApiDto.getCommunicateTelRecordList())) {
				List<CommunicateTelRecord> communicateTelRecordList =  communicateTelRecordApiDto.getCommunicateTelRecordList();
				for (CommunicateTelRecord communicateTelRecord : communicateTelRecordList) {
					UserDto userDto = userApiService.searchByUserIdFromUac(communicateTelRecord.getCreator());
					communicateTelRecord.setCreatorAvatarUrl(Objects.nonNull(userDto)?userDto.getAliasHeadPicture():"");
				}
			}
        } catch (Exception e) {
            throw new ServiceException("网络错误，请稍后重试...");
        }
		return communicateTelRecordApiDto;
	}

	@Override
	public List<Integer> followBindingTel(FollowBindingTelParams followBindingTelParams, CurrentUser currentUser) {
		log.info("CRM跟进记录绑定通话，详情入参:{},用户信息：{}", JSON.toJSONString(followBindingTelParams),JSON.toJSONString(currentUser));
		try {
			List<Integer> alreadyBindingTelIdList = communicateRecordApiService.followBindingTel(followBindingTelParams).getData();
			return alreadyBindingTelIdList;
		}catch(Exception e) {
			throw new ServiceException("网络错误，请稍后重试...");
		}
	}

	@Override
	public List<UserDto> getUacSubUserInfoByCurrent(Integer id) {
		return userApiService.queryUserSubFromUac(id);
	}
}
