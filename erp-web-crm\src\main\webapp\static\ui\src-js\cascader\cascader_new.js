//下拉级联，可以多层级（多于三级），目前由于时间原因，暂时只开发多选带搜索的
Vue.component('ui-cascader-new-option', {
    template: `
        <div class="ui-cas-new-option-layout">
            <div class="ui-cas-new-option-wrap" @click.stop>
                <div class="ui-cas-option-item":class="{active: activeIndex == index}" v-for="(item, index) in list" @click.stop="handlerItemClick(item, index)">
                    <div class="ui-cas-option-item-checkbox" @click.stop="handlerItemChange(item)">
                        <i class="vd-ui_icon icon-checkbox2" v-if="item.checked"></i>
                        <i class="vd-ui_icon icon-deduct" v-else-if="item.hasChecked"></i>
                        <i class="vd-ui_icon icon-checkbox1" v-else></i>
                    </div>
                    <div class="ui-cas-option-item-txt text-line-1">{{ item.label }}</div>
                    <i class="vd-ui_icon icon-app-right" v-if="item.children && item.children.length"></i>
                </div>
            </div>
            <template v-if="list[activeIndex] && list[activeIndex].children && list[activeIndex].children.length">
                <ui-cascader-new-option :list="list[activeIndex].children" ref="childLevel" @itemClick="emitItemClick" @itemChange="handlerChildItemChange"></ui-cascader-new-option>
            </template>
        <div>
    `,
    props: {
        list: {
            type: Array,
            default() {
                return []
            }
        }
    },
    data() {
        return {
            activeIndex: -1
        }
    },
    computed: {

    },
    mounted() {

    },
    methods: {
        handlerItemClick(item, index) {
            if (item.children && item.children.length) {
                this.refresh();
                this.activeIndex = index;
                this.emitItemClick();
            } else {
                this.activeIndex = index;

                this.handlerItemChange(item);
            }
            // this.$forceUpdate();
        },
        emitItemClick() {
            this.$emit('itemClick')
        },
        handlerItemChange(item) {
            item.checked = !item.checked;
            item.hasChecked = false;

            if (item.children && item.children.length) {
                this.setItemChecked(item.children, item.checked);
            }

            this.emitItemChange();
        },
        setItemChecked(list, checked) {
            list.forEach(item => {
                item.checked = checked;
                item.hasChecked = checked;

                if (item.children && item.children.length) {
                    this.setItemChecked(item.children, checked);
                }
            })
        },
        emitItemChange() {
            this.$emit('itemChange')
        },
        handlerChildItemChange() {
            this.list.forEach(item => {
                if (item.children && item.children.length) {
                    let hasChecked = false;
                    let allChecked = true;

                    item.children.forEach(child => {
                        if (child.checked || child.hasChecked) {
                            hasChecked = true;
                        } 
                        
                        if (!child.checked){
                            allChecked = false;
                        }
                    })

                    item.checked = allChecked;
                    item.hasChecked = hasChecked;
                }
            });

            this.emitItemChange();
        },
        refresh() {
            this.activeIndex = -1;
            this.$refs.childLevel && this.$refs.childLevel.refresh()
        }
    }
})

Vue.component('ui-cascader-new', {
    template: `
        <div class="ui-cas-new-wrap">
            <div class="ui-cas-new-select" :class="{'on-select': selectedItems && selectedItems.length, open: isDropShow}" ref="container" @click=stop>
                <div class="ui-cas-new-select-placeholder" v-if="!multi"></div>
                <div class="ui-cas-new-multi-label" v-else-if="selectedItems && selectedItems.length" @click="showDrop">
                    <div class="ui-cas-new-multi-label-item">
                        <span class="text-line-1" :title="selectedItems[0].label">{{ selectedItems[0].label }}</span>
                        <i class="vd-ui_icon icon-delete" @click.stop="deleteSelectItem(0)"></i>
                    </div>
                    <div class="ui-cas-new-multi-label-item more" v-if="selectedItems.length > 1">
                        +{{ selectedItems.length - 1 }}
                    </div>
                </div>
                <div class="ui-cas-new-search-input-wrap" v-if="search">
                    <input class="ui-cas-new-search-input" v-model="searchVal" @focus="showDrop" @click.stop :placeholder="selectedItems && selectedItems.length ? '' : placeholder" @input="handlerSearch"/>
                    <i class="vd-ui_icon icon-search"></i>
                </div>
                <div class="ui-cas-new-clear" @click="clear">
                    <i class="vd-ui_icon icon-error2"></i>
                </div>
            </div>
            <ui-poper :show="isDropShow" ref="dropwrap" :autoWidth="true">
                <div v-show="!searchVal" class="ui-cas-new-drop" @click=stop>
                    <ui-cascader-new-option :list="list" @itemClick="handlerItemClick" @itemChange="handlerItemChange" ref="cascaderOption" @click=stop></ui-cascader-new-option>
                </div>
                <div v-show="searchVal" class="ui-cas-new-search-wrap" ref="searchWrap" @click=stop>
                    <div class="ui-cas-new-search-list" v-if="searchList && searchList.length">
                        <div class="ui-cas-new-search-item" v-for="(item, index) in searchList" @click.stop="handlerSearchItemClick(item)">
                            <i class="vd-ui_icon icon-checkbox1" v-if="!item.checked"></i>
                            <i class="vd-ui_icon icon-checkbox2" v-else></i>
                            <div class="ui-cas-new-search-item-txt text-line-1">{{ item.label }}</div>
                        </div>
                    </div>
                    <div class="ui-cas-new-search-empty" v-else>
                        无匹配数据
                    </div>
                </div>
            </ui-poper>
        </div>
    `,
    props: {
        listData: {
            type: Array,
            default() {
                return [];
            }
        },
        placeholder: {
            type: String,
            default: '请选择'
        },
        multi: {
            type: Boolean,
            default: false
        },
        search: {
            type: Boolean,
            default: false
        },
        value: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            selectedItems: [], //选中的所有对象（直到最后一级）
            selectedIds: [], //选中的所有最后一级id
            allSelectedIds: [], //选中的所有层级id，
            searchVal: '',
            searchList: [],
            isDropShow: false,
            list: []
        }
    },
    watch: {
        
    },
    computed: {
       
    },
    created() {
        this.initData();
    },
    mounted() {
        document.body.addEventListener('click', () => {
            this.isDropShow = false;
            this.searchVal = '';
        })
    },
    methods: {
        initData() {
            this.list = JSON.parse(JSON.stringify(this.listData));

            if(this.value) {
                this.selectedIds = this.value;
                this.initListFromIds(this.list);
                this.setListData(this.list);
            }
        },
        //根据已选择的id渲染list,用于初始化和搜索、删除时id变动
        initListFromIds(list) {
            var hasChecked = false;
            var allChecked = true;

            list.forEach(item => {
                if(item.children && item.children.length) {
                    var resData = this.initListFromIds(item.children);
                    item.checked = resData.allChecked;
                    item.hasChecked = resData.hasChecked;
                } else {
                    if(this.selectedIds.indexOf(item.value) !== -1) {
                        item.checked = true;
                    } else {
                        item.checked = false;
                    }
                }

                if(item.checked || item.hasChecked) {
                    hasChecked = true;
                } 

                if(!item.checked){
                    allChecked = false;
                }
            })

            return {
                hasChecked,
                allChecked
            }
        },
        handlerItemClick() {
            console.log('first')
            this.$nextTick(() => {
                this.$refs.dropwrap.calcPosition();
            })
        },
        handlerItemChange() {
            this.selectedItems = [];
            this.selectedIds = [];
            this.allSelectedIds = [];
            this.setListData(this.list);
            this.$emit('change', {
                selectedIds: this.selectedIds,
                allSelectedIds: this.allSelectedIds
            })
            
            this.$emit('input', this.selectedIds)
        },
        setListData(list, parentName) {
            list.forEach(item => {
                if (item.children && item.children.length) {
                    if(item.checked) {
                        this.allSelectedIds.push(item.value);
                    }
                    this.setListData(item.children, parentName ? parentName + '/' + item.label : item.label);
                } else {
                    if (item.checked) {
                        this.selectedItems.push({
                            value: item.value,
                            label: parentName ? parentName + '/' + item.label : item.label
                        });

                        this.selectedIds.push(item.value);
                        this.allSelectedIds.push(item.value);
                    }
                }
            });
        },
        deleteSelectItem(index) {
            this.selectedItems.splice(index, 1);
            this.selectedIds.splice(index, 1);

            this.initListFromIds(this.list);
            this.handlerItemChange();
        },
        getAllChildItems(list, parentName) {
            let resList = [];

            list.forEach(item => {
                if (item.children && item.children.length) {
                    resList = resList.concat(this.getAllChildItems(item.children, parentName ? parentName + '/' + item.label : item.label));
                } else {
                    resList.push({
                        value: item.value,
                        label: parentName ? parentName + '/' + item.label : item.label,
                        checked: item.checked
                    })
                }
            })

            return resList;
        },
        getSearchList(list, parentName) {
            let searchList = [];
            list.forEach(item => {
                if (item.label.indexOf(this.searchVal) !== -1) {
                    if (item.children && item.children.length) {
                        searchList = searchList.concat(this.getAllChildItems(item.children, parentName ? parentName + '/' + item.label : item.label));
                    } else {
                        searchList.push({
                            value: item.value,
                            label: parentName ? parentName + '/' + item.label : item.label,
                            checked: item.checked
                        })
                    }
                } else if (item.children && item.children.length) {
                    searchList = searchList.concat(this.getSearchList(item.children, parentName ? parentName + '/' + item.label : item.label))
                }
            })

            return searchList;
        },
        handlerSearch() {
            this.$refs.searchWrap.style['min-width'] = this.$refs.container.offsetWidth + 'px';

            this.searchVal = this.searchVal.trim();

            if (this.searchVal) {
                this.searchList = this.getSearchList(this.list);
            } else {
                this.searchList = [];
            }
        },
        handlerSearchItemClick(item) {
            item.checked = !item.checked;

            let itemIndex = this.selectedIds.indexOf(item.value)

            if(itemIndex !== -1) {
                this.selectedIds.splice(itemIndex, 1);
            } else {
                this.selectedIds.push(item.value);
            }

            this.initListFromIds(this.list);

            this.handlerItemChange();
        },
        showDrop() {
            this.isDropShow = true;
        },
        clear() {
            this.value = [];
            this.selectedItems = [];
            this.$refs.cascaderOption.refresh()
            this.initData();
            this.$emit('change', {
                selectedIds: [],
                allSelectedIds: []
            })
            
            this.$emit('input', [])
        }
    }
})