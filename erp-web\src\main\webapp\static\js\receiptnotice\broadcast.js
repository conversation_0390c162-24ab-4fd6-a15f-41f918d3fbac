void function () {
    new Vue({
        el: '#page-container',
        data() {
            return {
                filter_picName: '',
                filter_type: '',
                filter_target: '',
                typeList: [{
                    label: '个人',
                    value: 1
                }, {
                    label: '团队',
                    value: 2
                }, {

                    label: '项目',
                    value: 3
                }],
                list: [],
                currentPage: 1,
                pageSize: 30,
                total: 0,
                loadingEl: null,
                isShowAddDialog: false,
                isShowEditDialog: false,
                edit_id: '',
                edit_type: '',
                edit_target: '',
                edit_targetLabel: '',
                userRemoteInfo: {
                    url: '/system/user/searchUserListForSelectLimit.do',
                    paramsMethod: 'get',
                    paramsKey: 'username',
                },
                targetProjectOptions: [{
                    label: '月度AED TOP',
                    value: 1
                }, {
                    label: '月度自有品牌TOP',
                    value: 2
                }],
                departmentList: [],
                editTargetErrorMsg: ''
            }
        },
        async created() {
            await this.getList();
            await this.getAllDepartment();
        },
        methods: {
            getList() {
                this.showloading();
                return axios.post('/broadcastContentConfig/page.do', {
                    pageNum: this.currentPage,
                    pageSize: this.pageSize,
                    param: {
                        picName: this.filter_picName,
                        exclusiveType: this.filter_type,
                        exclusiveTargetLabels: this.filter_target,
                    }
                }).then(({ data }) => {
                    this.hideloading();
                    if (data.code === 0) {
                        this.list = data.data.list || [];

                        this.total = data.data.total;
                    }
                })
            },
            getAllDepartment() {
                return axios.post('/broadcast/common/treeDept.do?parentId=0').then(({ data }) => {
                    if (data.code === 0) {
                        let departmentList = data.data || [];

                        departmentList.forEach(item => {
                            item.children.forEach(lv2 => {
                                delete lv2.children;
                            })
                        });

                        this.departmentList = departmentList
                    }
                })
            },
            search() {
                this.currentPage = 1;
                this.getList();
            },
            reset() {
                window.location.reload();
            },
            showBigImg(url) {
                window.open(url);
            },
            showAddDialog() {
                this.isShowAddDialog = true;
            },
            triggerAddUpload() {
                this.$refs.uploadAddTrigger.click();
            },
            uploadFile() {
                let files = this.$refs.uploadAddTrigger.files;

                this.submitFile(files, 0, () => {
                    this.$message({
                        message: '上传完成，已过滤大于10MB及格式不对的文件',
                        type: 'success'
                    })

                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                })
            },
            submitFile(list, index, callback) {
                let form = new FormData();

                let file = list[index];

                if (file.size > 10 * 1024 * 1024) {
                    if (index < list.length - 1 && index < 9) {
                        this.submitFile(list, index + 1, callback);
                    } else {
                        callback && callback();
                    }
                    return;
                }

                form.append('files', file);
                // form.append('files', reqFiles.files);

                this.showloading();

                axios.post('/broadcastContentConfig/batchUpload.do', form, {
                    headers: { 'Content-Type': 'multipart/form-data' }
                }).then(({ data }) => {
                    this.hideloading();

                    if (index < list.length - 1 && index < 9) {
                        this.submitFile(list, index + 1, callback);
                    } else {
                        callback && callback();
                    }
                })
            },
            editItem(item) {
                this.edit_id = item.id;
                this.edit_type = item.exclusiveType;

                if(this.edit_type != 2 || !item.exclusiveTargetValues) {
                    this.edit_target = item.exclusiveTargetValues || '';
                } else {
                    this.departmentList.forEach(lv1 => {
                        if(lv1.value == item.exclusiveTargetValues) {
                            this.edit_target = [item.exclusiveTargetValues];
                        }

                        lv1.children.forEach(lv2 => {
                            if(lv2.value == item.exclusiveTargetValues) {
                                this.edit_target = [lv1.value, item.exclusiveTargetValues];
                            }
                        })
                    })
                }

                this.edit_targetLabel = item.exclusiveTargetLabels || '';

                this.isShowEditDialog = true;
                this.editTargetErrorMsg = '';
            },
            handlerEditTypeChange() {
                this.edit_target = '';
                this.edit_targetLabel = '';
            },
            handlerEditTargetChange(value, data) {
                if (this.edit_type == 1 || this.edit_type == 3) {
                    this.edit_targetLabel = value.selected.label || '';
                } else {
                    this.edit_targetLabel = this.$refs.departmentSelect.getCheckedNodes()[0].label;
                }

                this.validEditTarget();
            },
            validEditTarget() {
                if ((this.edit_type != 2 && !this.edit_target) || (this.edit_type == 2 && !this.edit_target.length)) {
                    this.editTargetErrorMsg = '请选择专属目标';
                    return false;
                } else {
                    this.editTargetErrorMsg = '';
                    return true;
                }
            },
            submitEdit() {
                if (this.validEditTarget()) {
                    this.showloading();

                    let reqData = {
                        id: this.edit_id,
                        exclusiveType: this.edit_type,
                        exclusiveTargetLabels: this.edit_targetLabel,
                    }

                    if (this.edit_type == 1 || this.edit_type == 3) {
                        reqData.exclusiveTargetValues = this.edit_target;
                    } else {
                        reqData.exclusiveTargetValues = this.edit_target[this.edit_target.length - 1];
                        reqData.exclusiveTargetLabels = this.$refs.departmentSelect.getCheckedNodes()[0].label;
                    }
                    
                    axios.post('/broadcastContentConfig/update.do', reqData).then(({ data }) => {
                        this.hideloading();

                        if (data.code === 0) {
                            this.$message({
                                message: '编辑成功',
                                type: 'success'
                            })

                            this.isShowEditDialog = false;

                            this.getList();
                        }
                    })
                }
            },
            deleteItem(item) {
                this.$confirm('确认删除？', '', {
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                    type: 'warning'
                }).then(() => {
                    this.showloading();

                    axios.get('/broadcastContentConfig/delete.do?id=' + item.id).then(({ data }) => {
                        this.hideloading();
                        if (data.code === 0) {
                            this.$message({
                                message: '删除成功',
                                type: 'success'
                            })

                            this.getList();
                        } else {
                            this.$message({
                                message: data.message || '删除失败',
                                type: 'error'
                            })
                        }
                    })
                });
            },
            handleSizeChange(val) {
                this.currentPage = 1;
                this.pageSize = val;
                this.getList();
            },
            handleCurrentChange(val) {
                this.getList();
            },
            openBroadcastEdit() {
                VD_UI_GLOBAL.openLink('/broadcast/index/broadcastEdit.do', {
                    name: '播报配置',
                    id: 'broadcast_edit'
                })
            },
            openDepartmentEdit() {
                VD_UI_GLOBAL.openLink('/broadcast/index/departmentEdit.do', {
                    name: '部门配置',
                    id: 'broadcast_depart_edit'
                })
            },
            showloading() {
                this.loadingEl = this.$loading({
                    lock: true,
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            hideloading() {
                this.loadingEl && this.loadingEl.close();
            }
        }
    })
}.call(this);