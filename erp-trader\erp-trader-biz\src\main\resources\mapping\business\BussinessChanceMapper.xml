<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.business.mapper.BussinessChanceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.business.domain.entity.BussinessChanceEntity">
    <!--@mbg.generated-->
    <!--@Table T_BUSSINESS_CHANCE-->
    <id column="BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="bussinessChanceId" />
    <result column="WEB_BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="webBussinessChanceId" />
    <result column="BUSSINESS_CHANCE_NO" jdbcType="VARCHAR" property="bussinessChanceNo" />
    <result column="WEB_ACCOUNT_ID" jdbcType="INTEGER" property="webAccountId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="CHECK_TRADER_NAME" jdbcType="VARCHAR" property="checkTraderName" />
    <result column="CHECK_TRADER_AREA" jdbcType="VARCHAR" property="checkTraderArea" />
    <result column="CHECK_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="checkTraderContactName" />
    <result column="CHECK_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="checkTraderContactMobile" />
    <result column="CHECK_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="checkTraderContactTelephone" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="RECEIVE_TIME" jdbcType="BIGINT" property="receiveTime" />
    <result column="INQUIRY" jdbcType="INTEGER" property="inquiry" />
    <result column="SOURCE" jdbcType="INTEGER" property="source" />
    <result column="COMMUNICATION" jdbcType="INTEGER" property="communication" />
    <result column="CONTENT" jdbcType="LONGVARCHAR" property="content" />
    <result column="GOODS_CATEGORY" jdbcType="INTEGER" property="goodsCategory" />
    <result column="GOODS_BRAND" jdbcType="VARCHAR" property="goodsBrand" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="AREA_ID" jdbcType="INTEGER" property="areaId" />
    <result column="AREA_IDS" jdbcType="VARCHAR" property="areaIds" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
    <result column="OTHER_CONTACT" jdbcType="VARCHAR" property="otherContact" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="ASSIGN_TIME" jdbcType="BIGINT" property="assignTime" />
    <result column="FIRST_VIEW_TIME" jdbcType="BIGINT" property="firstViewTime" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="STATUS_COMMENTS" jdbcType="INTEGER" property="statusComments" />
    <result column="CLOSED_COMMENTS" jdbcType="VARCHAR" property="closedComments" />
    <result column="WENXIN_OPEN_ID" jdbcType="VARCHAR" property="wenxinOpenId" />
    <result column="BUSSINESS_LEVEL" jdbcType="INTEGER" property="bussinessLevel" />
    <result column="BUSSINESS_STAGE" jdbcType="INTEGER" property="bussinessStage" />
    <result column="ENQUIRY_TYPE" jdbcType="INTEGER" property="enquiryType" />
    <result column="ORDER_RATE" jdbcType="INTEGER" property="orderRate" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="ORDER_TIME" jdbcType="BIGINT" property="orderTime" />
    <result column="CANCEL_REASON" jdbcType="INTEGER" property="cancelReason" />
    <result column="OTHER_REASON" jdbcType="VARCHAR" property="otherReason" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="ENTRANCES" jdbcType="INTEGER" property="entrances" />
    <result column="FUNCTIONS" jdbcType="INTEGER" property="functions" />
    <result column="IS_NEW" jdbcType="BOOLEAN" property="isNew" />
    <result column="PRODUCT_COMMENTS" jdbcType="VARCHAR" property="productComments" />
    <result column="MERGE_STATUS" jdbcType="TINYINT" property="mergeStatus" />
    <result column="OLD_CHANCE_NO" jdbcType="VARCHAR" property="oldChanceNo" />
    <result column="BUSSINESS_PARENT_ID" jdbcType="INTEGER" property="bussinessParentId" />
    <result column="PRODUCT_COMMENTS_SALE" jdbcType="VARCHAR" property="productCommentsSale" />
    <result column="CLOSE_CHECK_STATUS" jdbcType="BOOLEAN" property="closeCheckStatus" />
    <result column="BNC_LINK" jdbcType="VARCHAR" property="bncLink" />
    <result column="IS_LINK_BD" jdbcType="BOOLEAN" property="isLinkBd" />
    <result column="ORIGIN_SOURCE" jdbcType="INTEGER" property="originSource" />
    <result column="COOKIE_ID" jdbcType="VARCHAR" property="cookieId" />
    <result column="PAGE_FROM" jdbcType="VARCHAR" property="pageFrom" />
    <result column="IS_POLICYMAKER" jdbcType="TINYINT" property="isPolicymaker" />
    <result column="SYSTEM_BUSINESS_LEVEL" jdbcType="INTEGER" property="systemBusinessLevel" />
    <result column="TAG_IDS" jdbcType="VARCHAR" property="tagIds" />
    <result column="SYSTEM_ORDER_RATE" jdbcType="INTEGER" property="systemOrderRate" />
    <result column="COMPLETION" jdbcType="INTEGER" property="completion" />
    <result column="COMPETITOR_INFORMATION" jdbcType="VARCHAR" property="competitorInformation" />
    <result column="PURCHASING_TYPE" jdbcType="INTEGER" property="purchasingType" />
    <result column="TENDER_TIME" jdbcType="DATE" property="tenderTime" />
    <result column="PURCHASING_TIME" jdbcType="INTEGER" property="purchasingTime" />
    <result column="OTHER" jdbcType="VARCHAR" property="other" />
    <result column="TERMINAL_TRADER_NAME" jdbcType="VARCHAR" property="terminalTraderName" />
    <result column="TERMINAL_TRADER_TYPE" jdbcType="INTEGER" property="terminalTraderType" />
    <result column="BUSINESS_CHANCE_GOODS" jdbcType="VARCHAR" property="businessChanceGoods" />
    <result column="SUPERVISOR_GUIDANCE" jdbcType="VARCHAR" property="supervisorGuidance" />
    <result column="BUSINESS_CHANCE_ACCURACY" jdbcType="TINYINT" property="businessChanceAccuracy" />
    <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
    <result column="TERMINAL_TRADER_NATURE" jdbcType="INTEGER" property="terminalTraderNature" />
    <result column="TERMINAL_TRADER_REGION" jdbcType="VARCHAR" property="terminalTraderRegion" />
    <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
    <result column="CUSTOMER_RELATIONSHIP" jdbcType="VARCHAR" property="customerRelationship" />
    <result column="BIDDING_PHASE" jdbcType="INTEGER" property="biddingPhase" />
    <result column="BIDDING_PARAMETER" jdbcType="INTEGER" property="biddingParameter" />
    <result column="STAGE" jdbcType="INTEGER" property="stage" />
    <result column="PRELIMINARY_NEGOTIATION_TIME" jdbcType="TIMESTAMP" property="preliminaryNegotiationTime" />
    <result column="OPPORTUNITY_VERIFICATION_TIME" jdbcType="TIMESTAMP" property="opportunityVerificationTime" />
    <result column="PRELIMINARY_SCHEME_TIME" jdbcType="TIMESTAMP" property="preliminarySchemeTime" />
    <result column="FINAL_SCHEME_TIME" jdbcType="TIMESTAMP" property="finalSchemeTime" />
    <result column="WINNING_ORDER_TIME" jdbcType="TIMESTAMP" property="winningOrderTime" />
    <result column="LOSE_ORDER_TIME" jdbcType="TIMESTAMP" property="loseOrderTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BUSSINESS_CHANCE_ID, WEB_BUSSINESS_CHANCE_ID, BUSSINESS_CHANCE_NO, WEB_ACCOUNT_ID,
    COMPANY_ID, ORG_ID, USER_ID, TRADER_ID, CHECK_TRADER_NAME, CHECK_TRADER_AREA, CHECK_TRADER_CONTACT_NAME,
    CHECK_TRADER_CONTACT_MOBILE, CHECK_TRADER_CONTACT_TELEPHONE, `TYPE`, RECEIVE_TIME,
    INQUIRY, `SOURCE`, COMMUNICATION, CONTENT, GOODS_CATEGORY, GOODS_BRAND, GOODS_NAME,
    TRADER_NAME, AREA_ID, AREA_IDS, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, MOBILE, TELEPHONE,
    OTHER_CONTACT, COMMENTS, ASSIGN_TIME, FIRST_VIEW_TIME, `STATUS`, STATUS_COMMENTS,
    CLOSED_COMMENTS, WENXIN_OPEN_ID, BUSSINESS_LEVEL, BUSSINESS_STAGE, ENQUIRY_TYPE,
    ORDER_RATE, AMOUNT, ORDER_TIME, CANCEL_REASON, OTHER_REASON, ADD_TIME, CREATOR, MOD_TIME,
    UPDATER, ENTRANCES, FUNCTIONS, IS_NEW, PRODUCT_COMMENTS, MERGE_STATUS, OLD_CHANCE_NO,
    BUSSINESS_PARENT_ID, PRODUCT_COMMENTS_SALE, CLOSE_CHECK_STATUS, BNC_LINK, IS_LINK_BD,
    ORIGIN_SOURCE, COOKIE_ID, PAGE_FROM, IS_POLICYMAKER, SYSTEM_BUSINESS_LEVEL, TAG_IDS,
    SYSTEM_ORDER_RATE, `COMPLETION`, COMPETITOR_INFORMATION, PURCHASING_TYPE, TENDER_TIME,
    PURCHASING_TIME, OTHER, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE, BUSINESS_CHANCE_GOODS,
    SUPERVISOR_GUIDANCE, BUSINESS_CHANCE_ACCURACY, VALID_TIME, TERMINAL_TRADER_NATURE,
    TERMINAL_TRADER_REGION, BUSINESS_TYPE, CUSTOMER_RELATIONSHIP, BIDDING_PHASE, BIDDING_PARAMETER,
    STAGE, PRELIMINARY_NEGOTIATION_TIME, OPPORTUNITY_VERIFICATION_TIME, PRELIMINARY_SCHEME_TIME,
    FINAL_SCHEME_TIME, WINNING_ORDER_TIME, LOSE_ORDER_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_BUSSINESS_CHANCE
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </select>

  <select id="findBussinessByBusinessChanceNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_BUSSINESS_CHANCE
    where BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_BUSSINESS_CHANCE
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="BUSSINESS_CHANCE_ID" keyProperty="bussinessChanceId" parameterType="com.vedeng.erp.business.domain.entity.BussinessChanceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSSINESS_CHANCE (WEB_BUSSINESS_CHANCE_ID, BUSSINESS_CHANCE_NO,
      WEB_ACCOUNT_ID, COMPANY_ID, ORG_ID,
      USER_ID, TRADER_ID, CHECK_TRADER_NAME,
      CHECK_TRADER_AREA, CHECK_TRADER_CONTACT_NAME,
      CHECK_TRADER_CONTACT_MOBILE, CHECK_TRADER_CONTACT_TELEPHONE,
      `TYPE`, RECEIVE_TIME, INQUIRY,
      `SOURCE`, COMMUNICATION, CONTENT,
      GOODS_CATEGORY, GOODS_BRAND, GOODS_NAME,
      TRADER_NAME, AREA_ID, AREA_IDS,
      TRADER_CONTACT_ID, TRADER_CONTACT_NAME, MOBILE,
      TELEPHONE, OTHER_CONTACT, COMMENTS,
      ASSIGN_TIME, FIRST_VIEW_TIME, `STATUS`,
      STATUS_COMMENTS, CLOSED_COMMENTS, WENXIN_OPEN_ID,
      BUSSINESS_LEVEL, BUSSINESS_STAGE, ENQUIRY_TYPE,
      ORDER_RATE, AMOUNT, ORDER_TIME,
      CANCEL_REASON, OTHER_REASON, ADD_TIME,
      CREATOR, MOD_TIME, UPDATER,
      ENTRANCES, FUNCTIONS, IS_NEW,
      PRODUCT_COMMENTS, MERGE_STATUS, OLD_CHANCE_NO,
      BUSSINESS_PARENT_ID, PRODUCT_COMMENTS_SALE,
      CLOSE_CHECK_STATUS, BNC_LINK, IS_LINK_BD,
      ORIGIN_SOURCE, COOKIE_ID, PAGE_FROM,
      IS_POLICYMAKER, SYSTEM_BUSINESS_LEVEL, TAG_IDS,
      SYSTEM_ORDER_RATE, `COMPLETION`, COMPETITOR_INFORMATION,
      PURCHASING_TYPE, TENDER_TIME, PURCHASING_TIME,
      OTHER, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE,
      BUSINESS_CHANCE_GOODS, SUPERVISOR_GUIDANCE,
      BUSINESS_CHANCE_ACCURACY, VALID_TIME, TERMINAL_TRADER_NATURE,
      TERMINAL_TRADER_REGION, BUSINESS_TYPE, CUSTOMER_RELATIONSHIP,
      BIDDING_PHASE, BIDDING_PARAMETER, STAGE,
      PRELIMINARY_NEGOTIATION_TIME, OPPORTUNITY_VERIFICATION_TIME,
      PRELIMINARY_SCHEME_TIME, FINAL_SCHEME_TIME,
      WINNING_ORDER_TIME, LOSE_ORDER_TIME)
    values (#{webBussinessChanceId,jdbcType=INTEGER}, #{bussinessChanceNo,jdbcType=VARCHAR},
      #{webAccountId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{orgId,jdbcType=INTEGER},
      #{userId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER}, #{checkTraderName,jdbcType=VARCHAR},
      #{checkTraderArea,jdbcType=VARCHAR}, #{checkTraderContactName,jdbcType=VARCHAR},
      #{checkTraderContactMobile,jdbcType=VARCHAR}, #{checkTraderContactTelephone,jdbcType=VARCHAR},
      #{type,jdbcType=INTEGER}, #{receiveTime,jdbcType=BIGINT}, #{inquiry,jdbcType=INTEGER},
      #{source,jdbcType=INTEGER}, #{communication,jdbcType=INTEGER}, #{content,jdbcType=LONGVARCHAR},
      #{goodsCategory,jdbcType=INTEGER}, #{goodsBrand,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR},
      #{traderName,jdbcType=VARCHAR}, #{areaId,jdbcType=INTEGER}, #{areaIds,jdbcType=VARCHAR},
      #{traderContactId,jdbcType=INTEGER}, #{traderContactName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR},
      #{telephone,jdbcType=VARCHAR}, #{otherContact,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR},
      #{assignTime,jdbcType=BIGINT}, #{firstViewTime,jdbcType=BIGINT}, #{status,jdbcType=TINYINT},
      #{statusComments,jdbcType=INTEGER}, #{closedComments,jdbcType=VARCHAR}, #{wenxinOpenId,jdbcType=VARCHAR},
      #{bussinessLevel,jdbcType=INTEGER}, #{bussinessStage,jdbcType=INTEGER}, #{enquiryType,jdbcType=INTEGER},
      #{orderRate,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{orderTime,jdbcType=BIGINT},
      #{cancelReason,jdbcType=INTEGER}, #{otherReason,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER},
      #{entrances,jdbcType=INTEGER}, #{functions,jdbcType=INTEGER}, #{isNew,jdbcType=BOOLEAN},
      #{productComments,jdbcType=VARCHAR}, #{mergeStatus,jdbcType=TINYINT}, #{oldChanceNo,jdbcType=VARCHAR},
      #{bussinessParentId,jdbcType=INTEGER}, #{productCommentsSale,jdbcType=VARCHAR},
      #{closeCheckStatus,jdbcType=BOOLEAN}, #{bncLink,jdbcType=VARCHAR}, #{isLinkBd,jdbcType=BOOLEAN},
      #{originSource,jdbcType=INTEGER}, #{cookieId,jdbcType=VARCHAR}, #{pageFrom,jdbcType=VARCHAR},
      #{isPolicymaker,jdbcType=TINYINT}, #{systemBusinessLevel,jdbcType=INTEGER}, #{tagIds,jdbcType=VARCHAR},
      #{systemOrderRate,jdbcType=INTEGER}, #{completion,jdbcType=INTEGER}, #{competitorInformation,jdbcType=VARCHAR},
      #{purchasingType,jdbcType=INTEGER}, #{tenderTime,jdbcType=DATE}, #{purchasingTime,jdbcType=INTEGER},
      #{other,jdbcType=VARCHAR}, #{terminalTraderName,jdbcType=VARCHAR}, #{terminalTraderType,jdbcType=INTEGER},
      #{businessChanceGoods,jdbcType=VARCHAR}, #{supervisorGuidance,jdbcType=VARCHAR},
      #{businessChanceAccuracy,jdbcType=TINYINT}, #{validTime,jdbcType=BIGINT}, #{terminalTraderNature,jdbcType=INTEGER},
      #{terminalTraderRegion,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER}, #{customerRelationship,jdbcType=VARCHAR},
      #{biddingPhase,jdbcType=INTEGER}, #{biddingParameter,jdbcType=INTEGER}, #{stage,jdbcType=INTEGER},
      #{preliminaryNegotiationTime,jdbcType=TIMESTAMP}, #{opportunityVerificationTime,jdbcType=TIMESTAMP},
      #{preliminarySchemeTime,jdbcType=TIMESTAMP}, #{finalSchemeTime,jdbcType=TIMESTAMP},
      #{winningOrderTime,jdbcType=TIMESTAMP}, #{loseOrderTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="BUSSINESS_CHANCE_ID" keyProperty="bussinessChanceId" parameterType="com.vedeng.erp.business.domain.entity.BussinessChanceEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSSINESS_CHANCE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="webBussinessChanceId != null">
        WEB_BUSSINESS_CHANCE_ID,
      </if>
      <if test="bussinessChanceNo != null">
        BUSSINESS_CHANCE_NO,
      </if>
      <if test="webAccountId != null">
        WEB_ACCOUNT_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="checkTraderName != null">
        CHECK_TRADER_NAME,
      </if>
      <if test="checkTraderArea != null">
        CHECK_TRADER_AREA,
      </if>
      <if test="checkTraderContactName != null">
        CHECK_TRADER_CONTACT_NAME,
      </if>
      <if test="checkTraderContactMobile != null">
        CHECK_TRADER_CONTACT_MOBILE,
      </if>
      <if test="checkTraderContactTelephone != null">
        CHECK_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="receiveTime != null">
        RECEIVE_TIME,
      </if>
      <if test="inquiry != null">
        INQUIRY,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="communication != null">
        COMMUNICATION,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="goodsCategory != null">
        GOODS_CATEGORY,
      </if>
      <if test="goodsBrand != null">
        GOODS_BRAND,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
      <if test="areaIds != null">
        AREA_IDS,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="telephone != null">
        TELEPHONE,
      </if>
      <if test="otherContact != null">
        OTHER_CONTACT,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="assignTime != null">
        ASSIGN_TIME,
      </if>
      <if test="firstViewTime != null">
        FIRST_VIEW_TIME,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="statusComments != null">
        STATUS_COMMENTS,
      </if>
      <if test="closedComments != null">
        CLOSED_COMMENTS,
      </if>
      <if test="wenxinOpenId != null">
        WENXIN_OPEN_ID,
      </if>
      <if test="bussinessLevel != null">
        BUSSINESS_LEVEL,
      </if>
      <if test="bussinessStage != null">
        BUSSINESS_STAGE,
      </if>
      <if test="enquiryType != null">
        ENQUIRY_TYPE,
      </if>
      <if test="orderRate != null">
        ORDER_RATE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="orderTime != null">
        ORDER_TIME,
      </if>
      <if test="cancelReason != null">
        CANCEL_REASON,
      </if>
      <if test="otherReason != null">
        OTHER_REASON,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="entrances != null">
        ENTRANCES,
      </if>
      <if test="functions != null">
        FUNCTIONS,
      </if>
      <if test="isNew != null">
        IS_NEW,
      </if>
      <if test="productComments != null">
        PRODUCT_COMMENTS,
      </if>
      <if test="mergeStatus != null">
        MERGE_STATUS,
      </if>
      <if test="oldChanceNo != null">
        OLD_CHANCE_NO,
      </if>
      <if test="bussinessParentId != null">
        BUSSINESS_PARENT_ID,
      </if>
      <if test="productCommentsSale != null">
        PRODUCT_COMMENTS_SALE,
      </if>
      <if test="closeCheckStatus != null">
        CLOSE_CHECK_STATUS,
      </if>
      <if test="bncLink != null">
        BNC_LINK,
      </if>
      <if test="isLinkBd != null">
        IS_LINK_BD,
      </if>
      <if test="originSource != null">
        ORIGIN_SOURCE,
      </if>
      <if test="cookieId != null">
        COOKIE_ID,
      </if>
      <if test="pageFrom != null">
        PAGE_FROM,
      </if>
      <if test="isPolicymaker != null">
        IS_POLICYMAKER,
      </if>
      <if test="systemBusinessLevel != null">
        SYSTEM_BUSINESS_LEVEL,
      </if>
      <if test="tagIds != null">
        TAG_IDS,
      </if>
      <if test="systemOrderRate != null">
        SYSTEM_ORDER_RATE,
      </if>
      <if test="completion != null">
        `COMPLETION`,
      </if>
      <if test="competitorInformation != null">
        COMPETITOR_INFORMATION,
      </if>
      <if test="purchasingType != null">
        PURCHASING_TYPE,
      </if>
      <if test="tenderTime != null">
        TENDER_TIME,
      </if>
      <if test="purchasingTime != null">
        PURCHASING_TIME,
      </if>
      <if test="other != null">
        OTHER,
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME,
      </if>
      <if test="terminalTraderType != null">
        TERMINAL_TRADER_TYPE,
      </if>
      <if test="businessChanceGoods != null">
        BUSINESS_CHANCE_GOODS,
      </if>
      <if test="supervisorGuidance != null">
        SUPERVISOR_GUIDANCE,
      </if>
      <if test="businessChanceAccuracy != null">
        BUSINESS_CHANCE_ACCURACY,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="terminalTraderNature != null">
        TERMINAL_TRADER_NATURE,
      </if>
      <if test="terminalTraderRegion != null">
        TERMINAL_TRADER_REGION,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="customerRelationship != null">
        CUSTOMER_RELATIONSHIP,
      </if>
      <if test="biddingPhase != null">
        BIDDING_PHASE,
      </if>
      <if test="biddingParameter != null">
        BIDDING_PARAMETER,
      </if>
      <if test="stage != null">
        STAGE,
      </if>
      <if test="preliminaryNegotiationTime != null">
        PRELIMINARY_NEGOTIATION_TIME,
      </if>
      <if test="opportunityVerificationTime != null">
        OPPORTUNITY_VERIFICATION_TIME,
      </if>
      <if test="preliminarySchemeTime != null">
        PRELIMINARY_SCHEME_TIME,
      </if>
      <if test="finalSchemeTime != null">
        FINAL_SCHEME_TIME,
      </if>
      <if test="winningOrderTime != null">
        WINNING_ORDER_TIME,
      </if>
      <if test="loseOrderTime != null">
        LOSE_ORDER_TIME,
      </if>
      <if test="provinceId != null">
        PROVINCE_ID,
      </if>
      <if test="cityId != null">
        CITY_ID,
      </if>
      <if test="countyId != null">
        COUNTY_ID,
      </if>
      <if test="province != null">
        PROVINCE,
      </if>
      <if test="city != null">
        CITY,
      </if>
      <if test="county != null">
        COUNTY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="webBussinessChanceId != null">
        #{webBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="bussinessChanceNo != null">
        #{bussinessChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="webAccountId != null">
        #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="checkTraderName != null">
        #{checkTraderName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderArea != null">
        #{checkTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactName != null">
        #{checkTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactMobile != null">
        #{checkTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactTelephone != null">
        #{checkTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=BIGINT},
      </if>
      <if test="inquiry != null">
        #{inquiry,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="communication != null">
        #{communication,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="goodsCategory != null">
        #{goodsCategory,jdbcType=INTEGER},
      </if>
      <if test="goodsBrand != null">
        #{goodsBrand,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="otherContact != null">
        #{otherContact,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="assignTime != null">
        #{assignTime,jdbcType=BIGINT},
      </if>
      <if test="firstViewTime != null">
        #{firstViewTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="statusComments != null">
        #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="closedComments != null">
        #{closedComments,jdbcType=VARCHAR},
      </if>
      <if test="wenxinOpenId != null">
        #{wenxinOpenId,jdbcType=VARCHAR},
      </if>
      <if test="bussinessLevel != null">
        #{bussinessLevel,jdbcType=INTEGER},
      </if>
      <if test="bussinessStage != null">
        #{bussinessStage,jdbcType=INTEGER},
      </if>
      <if test="enquiryType != null">
        #{enquiryType,jdbcType=INTEGER},
      </if>
      <if test="orderRate != null">
        #{orderRate,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=INTEGER},
      </if>
      <if test="otherReason != null">
        #{otherReason,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="entrances != null">
        #{entrances,jdbcType=INTEGER},
      </if>
      <if test="functions != null">
        #{functions,jdbcType=INTEGER},
      </if>
      <if test="isNew != null">
        #{isNew,jdbcType=BOOLEAN},
      </if>
      <if test="productComments != null">
        #{productComments,jdbcType=VARCHAR},
      </if>
      <if test="mergeStatus != null">
        #{mergeStatus,jdbcType=TINYINT},
      </if>
      <if test="oldChanceNo != null">
        #{oldChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="bussinessParentId != null">
        #{bussinessParentId,jdbcType=INTEGER},
      </if>
      <if test="productCommentsSale != null">
        #{productCommentsSale,jdbcType=VARCHAR},
      </if>
      <if test="closeCheckStatus != null">
        #{closeCheckStatus,jdbcType=BOOLEAN},
      </if>
      <if test="bncLink != null">
        #{bncLink,jdbcType=VARCHAR},
      </if>
      <if test="isLinkBd != null">
        #{isLinkBd,jdbcType=BOOLEAN},
      </if>
      <if test="originSource != null">
        #{originSource,jdbcType=INTEGER},
      </if>
      <if test="cookieId != null">
        #{cookieId,jdbcType=VARCHAR},
      </if>
      <if test="pageFrom != null">
        #{pageFrom,jdbcType=VARCHAR},
      </if>
      <if test="isPolicymaker != null">
        #{isPolicymaker,jdbcType=TINYINT},
      </if>
      <if test="systemBusinessLevel != null">
        #{systemBusinessLevel,jdbcType=INTEGER},
      </if>
      <if test="tagIds != null">
        #{tagIds,jdbcType=VARCHAR},
      </if>
      <if test="systemOrderRate != null">
        #{systemOrderRate,jdbcType=INTEGER},
      </if>
      <if test="completion != null">
        #{completion,jdbcType=INTEGER},
      </if>
      <if test="competitorInformation != null">
        #{competitorInformation,jdbcType=VARCHAR},
      </if>
      <if test="purchasingType != null">
        #{purchasingType,jdbcType=INTEGER},
      </if>
      <if test="tenderTime != null">
        #{tenderTime,jdbcType=DATE},
      </if>
      <if test="purchasingTime != null">
        #{purchasingTime,jdbcType=INTEGER},
      </if>
      <if test="other != null">
        #{other,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderName != null">
        #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null">
        #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="businessChanceGoods != null">
        #{businessChanceGoods,jdbcType=VARCHAR},
      </if>
      <if test="supervisorGuidance != null">
        #{supervisorGuidance,jdbcType=VARCHAR},
      </if>
      <if test="businessChanceAccuracy != null">
        #{businessChanceAccuracy,jdbcType=TINYINT},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="terminalTraderNature != null">
        #{terminalTraderNature,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderRegion != null">
        #{terminalTraderRegion,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="customerRelationship != null">
        #{customerRelationship,jdbcType=VARCHAR},
      </if>
      <if test="biddingPhase != null">
        #{biddingPhase,jdbcType=INTEGER},
      </if>
      <if test="biddingParameter != null">
        #{biddingParameter,jdbcType=INTEGER},
      </if>
      <if test="stage != null">
        #{stage,jdbcType=INTEGER},
      </if>
      <if test="preliminaryNegotiationTime != null">
        #{preliminaryNegotiationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="opportunityVerificationTime != null">
        #{opportunityVerificationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preliminarySchemeTime != null">
        #{preliminarySchemeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finalSchemeTime != null">
        #{finalSchemeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="winningOrderTime != null">
        #{winningOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loseOrderTime != null">
        #{loseOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=INTEGER},
      </if>
      <if test="countyId != null">
        #{countyId,jdbcType=INTEGER},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        #{county,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.business.domain.entity.BussinessChanceEntity">
    <!--@mbg.generated-->
    update T_BUSSINESS_CHANCE
    <set>
      <if test="frontEndSeq != null">
        FRONT_END_SEQ = #{frontEndSeq,jdbcType=INTEGER},
      </if>
      <if test="webBussinessChanceId != null">
        WEB_BUSSINESS_CHANCE_ID = #{webBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="bussinessChanceNo != null">
        BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="webAccountId != null">
        WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="checkTraderName != null">
        CHECK_TRADER_NAME = #{checkTraderName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderArea != null">
        CHECK_TRADER_AREA = #{checkTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactName != null">
        CHECK_TRADER_CONTACT_NAME = #{checkTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactMobile != null">
        CHECK_TRADER_CONTACT_MOBILE = #{checkTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="checkTraderContactTelephone != null">
        CHECK_TRADER_CONTACT_TELEPHONE = #{checkTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null">
        RECEIVE_TIME = #{receiveTime,jdbcType=BIGINT},
      </if>
      <if test="inquiry != null">
        INQUIRY = #{inquiry,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=INTEGER},
      </if>
      <if test="communication != null">
        COMMUNICATION = #{communication,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="goodsCategory != null">
        GOODS_CATEGORY = #{goodsCategory,jdbcType=INTEGER},
      </if>
      <if test="goodsBrand != null">
        GOODS_BRAND = #{goodsBrand,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="otherContact != null">
        OTHER_CONTACT = #{otherContact,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="assignTime != null">
        ASSIGN_TIME = #{assignTime,jdbcType=BIGINT},
      </if>
      <if test="firstViewTime != null">
        FIRST_VIEW_TIME = #{firstViewTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=TINYINT},
      </if>
      <if test="statusComments != null">
        STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="closedComments != null">
        CLOSED_COMMENTS = #{closedComments,jdbcType=VARCHAR},
      </if>
      <if test="wenxinOpenId != null">
        WENXIN_OPEN_ID = #{wenxinOpenId,jdbcType=VARCHAR},
      </if>
      <if test="bussinessLevel != null">
        BUSSINESS_LEVEL = #{bussinessLevel,jdbcType=INTEGER},
      </if>
      <if test="bussinessStage != null">
        BUSSINESS_STAGE = #{bussinessStage,jdbcType=INTEGER},
      </if>
      <if test="enquiryType != null">
        ENQUIRY_TYPE = #{enquiryType,jdbcType=INTEGER},
      </if>
      <if test="orderRate != null">
        ORDER_RATE = #{orderRate,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderTime != null">
        ORDER_TIME = #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="cancelReason != null">
        CANCEL_REASON = #{cancelReason,jdbcType=INTEGER},
      </if>
      <if test="otherReason != null">
        OTHER_REASON = #{otherReason,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="entrances != null">
        ENTRANCES = #{entrances,jdbcType=INTEGER},
      </if>
      <if test="functions != null">
        FUNCTIONS = #{functions,jdbcType=INTEGER},
      </if>
      <if test="isNew != null">
        IS_NEW = #{isNew,jdbcType=BOOLEAN},
      </if>
      <if test="productComments != null">
        PRODUCT_COMMENTS = #{productComments,jdbcType=VARCHAR},
      </if>
      <if test="mergeStatus != null">
        MERGE_STATUS = #{mergeStatus,jdbcType=TINYINT},
      </if>
      <if test="oldChanceNo != null">
        OLD_CHANCE_NO = #{oldChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="bussinessParentId != null">
        BUSSINESS_PARENT_ID = #{bussinessParentId,jdbcType=INTEGER},
      </if>
      <if test="productCommentsSale != null">
        PRODUCT_COMMENTS_SALE = #{productCommentsSale,jdbcType=VARCHAR},
      </if>
      <if test="closeCheckStatus != null">
        CLOSE_CHECK_STATUS = #{closeCheckStatus,jdbcType=BOOLEAN},
      </if>
      <if test="bncLink != null">
        BNC_LINK = #{bncLink,jdbcType=VARCHAR},
      </if>
      <if test="isLinkBd != null">
        IS_LINK_BD = #{isLinkBd,jdbcType=BOOLEAN},
      </if>
      <if test="originSource != null">
        ORIGIN_SOURCE = #{originSource,jdbcType=INTEGER},
      </if>
      <if test="cookieId != null">
        COOKIE_ID = #{cookieId,jdbcType=VARCHAR},
      </if>
      <if test="pageFrom != null">
        PAGE_FROM = #{pageFrom,jdbcType=VARCHAR},
      </if>
      <if test="isPolicymaker != null">
        IS_POLICYMAKER = #{isPolicymaker,jdbcType=TINYINT},
      </if>
      <if test="systemBusinessLevel != null">
        SYSTEM_BUSINESS_LEVEL = #{systemBusinessLevel,jdbcType=INTEGER},
      </if>
      <if test="tagIds != null">
        TAG_IDS = #{tagIds,jdbcType=VARCHAR},
      </if>
      <if test="systemOrderRate != null">
        SYSTEM_ORDER_RATE = #{systemOrderRate,jdbcType=INTEGER},
      </if>
      <if test="completion != null">
        `COMPLETION` = #{completion,jdbcType=INTEGER},
      </if>
      <if test="competitorInformation != null">
        COMPETITOR_INFORMATION = #{competitorInformation,jdbcType=VARCHAR},
      </if>
      <if test="purchasingType != null">
        PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER},
      </if>
      <if test="tenderTime != null">
        TENDER_TIME = #{tenderTime,jdbcType=DATE},
      </if>
      <if test="purchasingTime != null">
        PURCHASING_TIME = #{purchasingTime,jdbcType=INTEGER},
      </if>
      <if test="other != null">
        OTHER = #{other,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null">
        TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="businessChanceGoods != null">
        BUSINESS_CHANCE_GOODS = #{businessChanceGoods,jdbcType=VARCHAR},
      </if>
      <if test="supervisorGuidance != null">
        SUPERVISOR_GUIDANCE = #{supervisorGuidance,jdbcType=VARCHAR},
      </if>
      <if test="businessChanceAccuracy != null">
        BUSINESS_CHANCE_ACCURACY = #{businessChanceAccuracy,jdbcType=TINYINT},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="terminalTraderNature != null">
        TERMINAL_TRADER_NATURE = #{terminalTraderNature,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderRegion != null">
        TERMINAL_TRADER_REGION = #{terminalTraderRegion,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="customerRelationship != null">
        CUSTOMER_RELATIONSHIP = #{customerRelationship,jdbcType=VARCHAR},
      </if>
      <if test="biddingPhase != null">
        BIDDING_PHASE = #{biddingPhase,jdbcType=INTEGER},
      </if>
      <if test="biddingParameter != null">
        BIDDING_PARAMETER = #{biddingParameter,jdbcType=INTEGER},
      </if>
      <if test="stage != null">
        STAGE = #{stage,jdbcType=INTEGER},
      </if>
      <if test="preliminaryNegotiationTime != null">
        PRELIMINARY_NEGOTIATION_TIME = #{preliminaryNegotiationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="opportunityVerificationTime != null">
        OPPORTUNITY_VERIFICATION_TIME = #{opportunityVerificationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preliminarySchemeTime != null">
        PRELIMINARY_SCHEME_TIME = #{preliminarySchemeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finalSchemeTime != null">
        FINAL_SCHEME_TIME = #{finalSchemeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="winningOrderTime != null">
        WINNING_ORDER_TIME = #{winningOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loseOrderTime != null">
        LOSE_ORDER_TIME = #{loseOrderTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.business.domain.entity.BussinessChanceEntity">
    <!--@mbg.generated-->
    update T_BUSSINESS_CHANCE
    set WEB_BUSSINESS_CHANCE_ID = #{webBussinessChanceId,jdbcType=INTEGER},
      BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR},
      WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      CHECK_TRADER_NAME = #{checkTraderName,jdbcType=VARCHAR},
      CHECK_TRADER_AREA = #{checkTraderArea,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_NAME = #{checkTraderContactName,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_MOBILE = #{checkTraderContactMobile,jdbcType=VARCHAR},
      CHECK_TRADER_CONTACT_TELEPHONE = #{checkTraderContactTelephone,jdbcType=VARCHAR},
      `TYPE` = #{type,jdbcType=INTEGER},
      RECEIVE_TIME = #{receiveTime,jdbcType=BIGINT},
      INQUIRY = #{inquiry,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=INTEGER},
      COMMUNICATION = #{communication,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=LONGVARCHAR},
      GOODS_CATEGORY = #{goodsCategory,jdbcType=INTEGER},
      GOODS_BRAND = #{goodsBrand,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR},
      OTHER_CONTACT = #{otherContact,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      ASSIGN_TIME = #{assignTime,jdbcType=BIGINT},
      FIRST_VIEW_TIME = #{firstViewTime,jdbcType=BIGINT},
      `STATUS` = #{status,jdbcType=TINYINT},
      STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      CLOSED_COMMENTS = #{closedComments,jdbcType=VARCHAR},
      WENXIN_OPEN_ID = #{wenxinOpenId,jdbcType=VARCHAR},
      BUSSINESS_LEVEL = #{bussinessLevel,jdbcType=INTEGER},
      BUSSINESS_STAGE = #{bussinessStage,jdbcType=INTEGER},
      ENQUIRY_TYPE = #{enquiryType,jdbcType=INTEGER},
      ORDER_RATE = #{orderRate,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      ORDER_TIME = #{orderTime,jdbcType=BIGINT},
      CANCEL_REASON = #{cancelReason,jdbcType=INTEGER},
      OTHER_REASON = #{otherReason,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      ENTRANCES = #{entrances,jdbcType=INTEGER},
      FUNCTIONS = #{functions,jdbcType=INTEGER},
      IS_NEW = #{isNew,jdbcType=BOOLEAN},
      PRODUCT_COMMENTS = #{productComments,jdbcType=VARCHAR},
      MERGE_STATUS = #{mergeStatus,jdbcType=TINYINT},
      OLD_CHANCE_NO = #{oldChanceNo,jdbcType=VARCHAR},
      BUSSINESS_PARENT_ID = #{bussinessParentId,jdbcType=INTEGER},
      PRODUCT_COMMENTS_SALE = #{productCommentsSale,jdbcType=VARCHAR},
      CLOSE_CHECK_STATUS = #{closeCheckStatus,jdbcType=BOOLEAN},
      BNC_LINK = #{bncLink,jdbcType=VARCHAR},
      IS_LINK_BD = #{isLinkBd,jdbcType=BOOLEAN},
      ORIGIN_SOURCE = #{originSource,jdbcType=INTEGER},
      COOKIE_ID = #{cookieId,jdbcType=VARCHAR},
      PAGE_FROM = #{pageFrom,jdbcType=VARCHAR},
      IS_POLICYMAKER = #{isPolicymaker,jdbcType=TINYINT},
      SYSTEM_BUSINESS_LEVEL = #{systemBusinessLevel,jdbcType=INTEGER},
      TAG_IDS = #{tagIds,jdbcType=VARCHAR},
      SYSTEM_ORDER_RATE = #{systemOrderRate,jdbcType=INTEGER},
      `COMPLETION` = #{completion,jdbcType=INTEGER},
      COMPETITOR_INFORMATION = #{competitorInformation,jdbcType=VARCHAR},
      PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER},
      TENDER_TIME = #{tenderTime,jdbcType=DATE},
      PURCHASING_TIME = #{purchasingTime,jdbcType=INTEGER},
      OTHER = #{other,jdbcType=VARCHAR},
      TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      BUSINESS_CHANCE_GOODS = #{businessChanceGoods,jdbcType=VARCHAR},
      SUPERVISOR_GUIDANCE = #{supervisorGuidance,jdbcType=VARCHAR},
      BUSINESS_CHANCE_ACCURACY = #{businessChanceAccuracy,jdbcType=TINYINT},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      TERMINAL_TRADER_NATURE = #{terminalTraderNature,jdbcType=INTEGER},
      TERMINAL_TRADER_REGION = #{terminalTraderRegion,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      CUSTOMER_RELATIONSHIP = #{customerRelationship,jdbcType=VARCHAR},
      BIDDING_PHASE = #{biddingPhase,jdbcType=INTEGER},
      BIDDING_PARAMETER = #{biddingParameter,jdbcType=INTEGER},
      STAGE = #{stage,jdbcType=INTEGER},
      PRELIMINARY_NEGOTIATION_TIME = #{preliminaryNegotiationTime,jdbcType=TIMESTAMP},
      OPPORTUNITY_VERIFICATION_TIME = #{opportunityVerificationTime,jdbcType=TIMESTAMP},
      PRELIMINARY_SCHEME_TIME = #{preliminarySchemeTime,jdbcType=TIMESTAMP},
      FINAL_SCHEME_TIME = #{finalSchemeTime,jdbcType=TIMESTAMP},
      WINNING_ORDER_TIME = #{winningOrderTime,jdbcType=TIMESTAMP},
      LOSE_ORDER_TIME = #{loseOrderTime,jdbcType=TIMESTAMP}
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert" keyColumn="BUSSINESS_CHANCE_ID" keyProperty="bussinessChanceId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSSINESS_CHANCE
    (WEB_BUSSINESS_CHANCE_ID, BUSSINESS_CHANCE_NO, WEB_ACCOUNT_ID, COMPANY_ID, ORG_ID,
      USER_ID, TRADER_ID, CHECK_TRADER_NAME, CHECK_TRADER_AREA, CHECK_TRADER_CONTACT_NAME,
      CHECK_TRADER_CONTACT_MOBILE, CHECK_TRADER_CONTACT_TELEPHONE, `TYPE`, RECEIVE_TIME,
      INQUIRY, `SOURCE`, COMMUNICATION, CONTENT, GOODS_CATEGORY, GOODS_BRAND, GOODS_NAME,
      TRADER_NAME, AREA_ID, AREA_IDS, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, MOBILE,
      TELEPHONE, OTHER_CONTACT, COMMENTS, ASSIGN_TIME, FIRST_VIEW_TIME, `STATUS`, STATUS_COMMENTS,
      CLOSED_COMMENTS, WENXIN_OPEN_ID, BUSSINESS_LEVEL, BUSSINESS_STAGE, ENQUIRY_TYPE,
      ORDER_RATE, AMOUNT, ORDER_TIME, CANCEL_REASON, OTHER_REASON, ADD_TIME, CREATOR,
      MOD_TIME, UPDATER, ENTRANCES, FUNCTIONS, IS_NEW, PRODUCT_COMMENTS, MERGE_STATUS,
      OLD_CHANCE_NO, BUSSINESS_PARENT_ID, PRODUCT_COMMENTS_SALE, CLOSE_CHECK_STATUS,
      BNC_LINK, IS_LINK_BD, ORIGIN_SOURCE, COOKIE_ID, PAGE_FROM, IS_POLICYMAKER, SYSTEM_BUSINESS_LEVEL,
      TAG_IDS, SYSTEM_ORDER_RATE, `COMPLETION`, COMPETITOR_INFORMATION, PURCHASING_TYPE,
      TENDER_TIME, PURCHASING_TIME, OTHER, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE,
      BUSINESS_CHANCE_GOODS, SUPERVISOR_GUIDANCE, BUSINESS_CHANCE_ACCURACY, VALID_TIME,
      TERMINAL_TRADER_NATURE, TERMINAL_TRADER_REGION, BUSINESS_TYPE, CUSTOMER_RELATIONSHIP,
      BIDDING_PHASE, BIDDING_PARAMETER, STAGE, PRELIMINARY_NEGOTIATION_TIME, OPPORTUNITY_VERIFICATION_TIME,
      PRELIMINARY_SCHEME_TIME, FINAL_SCHEME_TIME, WINNING_ORDER_TIME, LOSE_ORDER_TIME
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.webBussinessChanceId,jdbcType=INTEGER}, #{item.bussinessChanceNo,jdbcType=VARCHAR},
        #{item.webAccountId,jdbcType=INTEGER}, #{item.companyId,jdbcType=INTEGER}, #{item.orgId,jdbcType=INTEGER},
        #{item.userId,jdbcType=INTEGER}, #{item.traderId,jdbcType=INTEGER}, #{item.checkTraderName,jdbcType=VARCHAR},
        #{item.checkTraderArea,jdbcType=VARCHAR}, #{item.checkTraderContactName,jdbcType=VARCHAR},
        #{item.checkTraderContactMobile,jdbcType=VARCHAR}, #{item.checkTraderContactTelephone,jdbcType=VARCHAR},
        #{item.type,jdbcType=INTEGER}, #{item.receiveTime,jdbcType=BIGINT}, #{item.inquiry,jdbcType=INTEGER},
        #{item.source,jdbcType=INTEGER}, #{item.communication,jdbcType=INTEGER}, #{item.content,jdbcType=LONGVARCHAR},
        #{item.goodsCategory,jdbcType=INTEGER}, #{item.goodsBrand,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
        #{item.traderName,jdbcType=VARCHAR}, #{item.areaId,jdbcType=INTEGER}, #{item.areaIds,jdbcType=VARCHAR},
        #{item.traderContactId,jdbcType=INTEGER}, #{item.traderContactName,jdbcType=VARCHAR},
        #{item.mobile,jdbcType=VARCHAR}, #{item.telephone,jdbcType=VARCHAR}, #{item.otherContact,jdbcType=VARCHAR},
        #{item.comments,jdbcType=VARCHAR}, #{item.assignTime,jdbcType=BIGINT}, #{item.firstViewTime,jdbcType=BIGINT},
        #{item.status,jdbcType=TINYINT}, #{item.statusComments,jdbcType=INTEGER}, #{item.closedComments,jdbcType=VARCHAR},
        #{item.wenxinOpenId,jdbcType=VARCHAR}, #{item.bussinessLevel,jdbcType=INTEGER},
        #{item.bussinessStage,jdbcType=INTEGER}, #{item.enquiryType,jdbcType=INTEGER},
        #{item.orderRate,jdbcType=INTEGER}, #{item.amount,jdbcType=DECIMAL}, #{item.orderTime,jdbcType=BIGINT},
        #{item.cancelReason,jdbcType=INTEGER}, #{item.otherReason,jdbcType=VARCHAR}, #{item.addTime,jdbcType=BIGINT},
        #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, #{item.updater,jdbcType=INTEGER},
        #{item.entrances,jdbcType=INTEGER}, #{item.functions,jdbcType=INTEGER}, #{item.isNew,jdbcType=BOOLEAN},
        #{item.productComments,jdbcType=VARCHAR}, #{item.mergeStatus,jdbcType=TINYINT}, #{item.oldChanceNo,jdbcType=VARCHAR},
        #{item.bussinessParentId,jdbcType=INTEGER}, #{item.productCommentsSale,jdbcType=VARCHAR},
        #{item.closeCheckStatus,jdbcType=BOOLEAN}, #{item.bncLink,jdbcType=VARCHAR}, #{item.isLinkBd,jdbcType=BOOLEAN},
        #{item.originSource,jdbcType=INTEGER}, #{item.cookieId,jdbcType=VARCHAR}, #{item.pageFrom,jdbcType=VARCHAR},
        #{item.isPolicymaker,jdbcType=TINYINT}, #{item.systemBusinessLevel,jdbcType=INTEGER}, #{item.tagIds,jdbcType=VARCHAR},
        #{item.systemOrderRate,jdbcType=INTEGER}, #{item.completion,jdbcType=INTEGER},
        #{item.competitorInformation,jdbcType=VARCHAR}, #{item.purchasingType,jdbcType=INTEGER},
        #{item.tenderTime,jdbcType=DATE}, #{item.purchasingTime,jdbcType=INTEGER}, #{item.other,jdbcType=VARCHAR},
        #{item.terminalTraderName,jdbcType=VARCHAR}, #{item.terminalTraderType,jdbcType=INTEGER},
        #{item.businessChanceGoods,jdbcType=VARCHAR}, #{item.supervisorGuidance,jdbcType=VARCHAR},
        #{item.businessChanceAccuracy,jdbcType=TINYINT}, #{item.validTime,jdbcType=BIGINT},
        #{item.terminalTraderNature,jdbcType=INTEGER}, #{item.terminalTraderRegion,jdbcType=VARCHAR},
        #{item.businessType,jdbcType=INTEGER}, #{item.customerRelationship,jdbcType=VARCHAR},
        #{item.biddingPhase,jdbcType=INTEGER}, #{item.biddingParameter,jdbcType=INTEGER},
        #{item.stage,jdbcType=INTEGER}, #{item.preliminaryNegotiationTime,jdbcType=TIMESTAMP},
        #{item.opportunityVerificationTime,jdbcType=TIMESTAMP}, #{item.preliminarySchemeTime,jdbcType=TIMESTAMP},
        #{item.finalSchemeTime,jdbcType=TIMESTAMP}, #{item.winningOrderTime,jdbcType=TIMESTAMP},
        #{item.loseOrderTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from T_BUSSINESS_CHANCE where BUSSINESS_CHANCE_ID in
    <foreach close=")" collection="list" item="bussinessChanceId" open="(" separator=", ">
      #{bussinessChanceId,jdbcType=INTEGER}
    </foreach>
  </delete>


  <select id="findLatestCommuncationByAllIn" resultType="com.vedeng.erp.business.domain.dto.BusinessChanceDto">
    SELECT
    t.RELATED_ID as BUSSINESS_CHANCE_ID,
    t.COMMUNICATE_RECORD_ID as "latestCommunicateRecordId",
    t.CONTENT_SUFFIX as "latestCommunicateRecordContent"
    FROM
    T_COMMUNICATE_RECORD t
    JOIN
    (SELECT
    RELATED_ID,
    MAX(COMMUNICATE_RECORD_ID) AS MAX_COMMUNICATE_RECORD_ID
    FROM
    T_COMMUNICATE_RECORD
    WHERE
    COMMUNICATE_TYPE = 244
    AND RELATED_ID IN
    <foreach close=")" collection="businessChanceIds" item="bussinessChanceId" open="(" separator=", ">
      #{bussinessChanceId,jdbcType=INTEGER}
    </foreach>
    GROUP BY
    RELATED_ID) max_records
    ON
    t.RELATED_ID = max_records.RELATED_ID
    AND t.COMMUNICATE_RECORD_ID = max_records.MAX_COMMUNICATE_RECORD_ID
  </select>

    <select id="findByAll" resultType="com.vedeng.erp.business.domain.dto.BusinessChanceDto">
        SELECT
      C.BUSSINESS_CHANCE_ID,
      C.BUSSINESS_CHANCE_NO,
      C.BUSINESS_TYPE,
      C.SYSTEM_BUSINESS_LEVEL,
      C.STAGE,
      <if test="latestFollowUpTimeStart != null and latestFollowUpTimeEnd != null">
      TCR.COMMUNICATE_RECORD_ID as latestCommunicateRecordId,
      TCR.CONTENT_SUFFIX as latestCommunicateRecordContent,
      </if>
      C.ADD_TIME,
      C.USER_ID,
      U.USERNAME,
      COR.USERNAME as creatorName,
      CTUD.ALIAS_HEAD_PICTURE as creatorPic,
      TUD.ALIAS_HEAD_PICTURE as belongPic,
      T2.TASK_ID as latestTaskId,
      T2.TASK_CONTENT as latestTaskContent,
      C.AMOUNT,
      C.ORDER_TIME,
      C.TRADER_ID,
      C.TRADER_NAME,
      IF(TTIT.TRADER_INFO_TYC_ID IS NULL, 'N', 'Y') AS tycFlag,
      CUSTOMER.TRADER_CUSTOMER_ID,
      CUSTOMER.CUSTOMER_NATURE,
      C.TRADER_CONTACT_ID,
      C.TRADER_CONTACT_NAME,
      C.MOBILE,
      C.TELEPHONE,
      C.PRODUCT_COMMENTS_SALE,
      C.COMMENTS,
      C.TAG_IDS,
      C.TERMINAL_TRADER_NAME,
      C.TERMINAL_TRADER_NATURE,
      C.TERMINAL_TRADER_REGION,
      C.CUSTOMER_RELATIONSHIP,
      C.PURCHASING_TYPE,
      C.BIDDING_PHASE,
      C.BIDDING_PARAMETER,
      C.TYPE,
      TBL.ID as businessLeadsId,
      TBL.LEADS_NO,
      C.STATUS_COMMENTS,
      C.CLOSED_COMMENTS,
               O.ID          as 'customDataOperDto.id',
               O.ADD_TIME    as 'customDataOperDto.addTime',
               O.CREATOR     as 'customDataOperDto.creator',
               O.BELONGER_ID as 'customDataOperDto.belongerId',
               O.BELONGER    as 'customDataOperDto.belonger'
        FROM T_BUSSINESS_CHANCE C
            left join T_TRADER_INFO_TYC TTIT on C.TRADER_NAME = TTIT.NAME   
            LEFT JOIN T_TRADER_CUSTOMER CUSTOMER ON C.TRADER_ID = CUSTOMER.TRADER_ID
            LEFT JOIN T_CUSTOM_DATA_OPER O ON O.RELATED_ID = C.BUSSINESS_CHANCE_ID and O.BIZ_TYPE = 2 and O.OPER_TYPE = 2
            and O.BELONGER_ID = #{currentUserId,jdbcType=INTEGER}
            LEFT JOIN T_USER U ON U.USER_ID = C.USER_ID
            left join T_USER_DETAIL TUD on TUD.USER_ID = C.USER_ID
            LEFT JOIN T_USER COR ON COR.USER_ID = C.CREATOR
            left join T_USER_DETAIL CTUD on CTUD.USER_ID = COR.USER_ID
      <if test="latestFollowUpTimeStart != null and latestFollowUpTimeEnd != null">
        LEFT JOIN T_COMMUNICATE_RECORD TCR
          ON TCR.COMMUNICATE_RECORD_ID = (
          SELECT MAX(COMMUNICATE_RECORD_ID)
          FROM T_COMMUNICATE_RECORD
          WHERE RELATED_ID = C.BUSSINESS_CHANCE_ID
          AND COMMUNICATE_TYPE = 244
          LIMIT 1
        )
      </if>
      LEFT JOIN T_TASK T2 ON T2.TASK_ID = (
        SELECT MAX(TASK_ID)
        FROM T_TASK
        WHERE BIZ_TYPE = 1
        AND IS_DELETE = 0
        AND BIZ_ID = C.BUSSINESS_CHANCE_ID
      )
      left join T_TASK TT on TT.BIZ_ID = C.BUSSINESS_CHANCE_ID and TT.BIZ_TYPE = 1
      left join T_BUSINESS_LEADS TBL on TBL.BUSINESS_CHANCE_ID = C.BUSSINESS_CHANCE_ID

      <if test="collaboratorIdList != null and collaboratorIdList.size() != 0">
            left join T_R_SALES_J_BUSINESS_ORDER TRSJBO on TRSJBO.BUSINESS_ID = C.BUSSINESS_CHANCE_ID and TRSJBO.BUSINESS_TYPE = 1 and TRSJBO.IS_DELETED = 0
      </if>

      <if test="(skuNo != null and skuNo != '') or (productName != null and productName != '') or (brandName != null and brandName != '') or (modelNumber != null and modelNumber != '')">
            left join T_QUOTEORDER TQ on TQ.BUSSINESS_CHANCE_ID = C.BUSSINESS_CHANCE_ID
            left join T_QUOTEORDER_GOODS TQG on TQG.QUOTEORDER_ID = TQ.QUOTEORDER_ID
      </if>
        WHERE C.COMPANY_ID = 1 AND C.MERGE_STATUS != 1
      <if test="bussinessChanceNo != null and bussinessChanceNo != ''">
        and C.BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR}
      </if>
      <if test="businessTypeList != null and businessTypeList.size() != 0">
        and C.BUSINESS_TYPE in
        <foreach collection="businessTypeList" index="index" item="item" open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="systemBusinessLevelList != null and systemBusinessLevelList.size() != 0">
        and C.SYSTEM_BUSINESS_LEVEL in
        <foreach collection="systemBusinessLevelList" index="index" item="item" open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="stageList != null and stageList.size() != 0">
        and C.STAGE in
        <foreach collection="stageList" index="index" item="item" open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      
      <!-- 添加商品分类ID过滤条件 -->
      <if test="categoryIdList != null and categoryIdList.size() > 0">
        AND EXISTS (
            SELECT 1 FROM T_BUSINESS_ORDER_CATEGORY TBOC
            WHERE TBOC.BUSINESS_ID = C.BUSSINESS_CHANCE_ID 
            AND TBOC.BUSINESS_TYPE = 1  <!-- 1表示商机 -->
            AND TBOC.CATEGORY_ID IN
            <foreach item="categoryId" index="index" collection="categoryIdList" separator="," open="(" close=")">
                #{categoryId,jdbcType=INTEGER}
            </foreach>
        )
      </if>
      
      <!-- 添加省份和城市过滤条件，根据销售关系配置 -->
      <if test="provinceIds != null and provinceIds.size() > 0">
        AND EXISTS (
            SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
            WHERE RUR.PROVINCE_ID IN
            <foreach item="provinceId" index="index" collection="provinceIds" separator="," open="(" close=")">
                #{provinceId,jdbcType=INTEGER}
            </foreach>
            AND C.USER_ID IN (
                SELECT DISTINCT 
                    CASE 
                        WHEN RUR.ONLINE_SALES_ID > 0 THEN RUR.ONLINE_SALES_ID
                        WHEN RUR.OFFLINE_SALES_ID > 0 THEN RUR.OFFLINE_SALES_ID
                        ELSE 0
                    END AS SALES_ID
                FROM ROLE_USER_REGION_CONFIG RUR
                WHERE RUR.PROVINCE_ID IN
                <foreach item="provinceId" index="index" collection="provinceIds" separator="," open="(" close=")">
                    #{provinceId,jdbcType=INTEGER}
                </foreach>
                AND (RUR.ONLINE_SALES_ID > 0 OR RUR.OFFLINE_SALES_ID > 0)
            )
        )
      </if>
      
      <if test="cityIds != null and cityIds.size() > 0">
        AND EXISTS (
            SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
            WHERE RUR.CITY_ID IN
            <foreach item="cityId" index="index" collection="cityIds" separator="," open="(" close=")">
                #{cityId,jdbcType=INTEGER}
            </foreach>
            AND C.USER_ID IN (
                SELECT DISTINCT 
                    CASE 
                        WHEN RUR.ONLINE_SALES_ID > 0 THEN RUR.ONLINE_SALES_ID
                        WHEN RUR.OFFLINE_SALES_ID > 0 THEN RUR.OFFLINE_SALES_ID
                        ELSE 0
                    END AS SALES_ID
                FROM ROLE_USER_REGION_CONFIG RUR
                WHERE RUR.CITY_ID IN
                <foreach item="cityId" index="index" collection="cityIds" separator="," open="(" close=")">
                    #{cityId,jdbcType=INTEGER}
                </foreach>
                AND (RUR.ONLINE_SALES_ID > 0 OR RUR.OFFLINE_SALES_ID > 0)
            )
        )
      </if>
      
      <if test="latestFollowUpTimeStart != null and latestFollowUpTimeEnd != null">
        and TCR.ADD_TIME between #{latestFollowUpTimeStart,jdbcType=BIGINT} and #{latestFollowUpTimeEnd,jdbcType=BIGINT}
      </if>
      <if test="deadlineDateStart != null and deadlineDateEnd != null">
        and TT.DEADLINE between #{deadlineDateStart,jdbcType=TIMESTAMP} and #{deadlineDateEnd,jdbcType=TIMESTAMP}
      </if>
      <if test="doneStatus != null">
        and TT.DONE_STATUS = #{doneStatus,jdbcType=INTEGER}
      </if>
      <if test="addTimeStart != null and addTimeEnd != null">
        and C.ADD_TIME between #{addTimeStart,jdbcType=BIGINT} and #{addTimeEnd,jdbcType=BIGINT}
      </if>
      <if test="belongerIdList != null and belongerIdList.size() != 0">
        and C.USER_ID in
        <foreach close=")" collection="belongerIdList" index="index" item="item" open="(" separator=",">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      
      <if test="belongOrgUserIdList != null and belongOrgUserIdList.size() != 0">
        and C.USER_ID in
        <foreach close=")" collection="belongOrgUserIdList" index="index" item="item" open="(" separator=",">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      
      <if test="creatorIdList != null and creatorIdList.size() != 0">
        and C.CREATOR in
        <foreach close=")" collection="creatorIdList" index="index" item="item" open="(" separator=",">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="collaboratorIdList != null and collaboratorIdList.size() != 0">
        and TRSJBO.SALE_USER_ID in
        <foreach close=")" collection="collaboratorIdList" index="index" item="item" open="(" separator=",">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="attentionState != null and attentionState == 1">
        AND O.OPER_TYPE = 2
      </if>
      <if test="attentionState != null and attentionState == 0">
        AND C.BUSSINESS_CHANCE_ID NOT IN (SELECT RELATED_ID FROM T_CUSTOM_DATA_OPER WHERE BELONGER_ID = #{currentUserId,jdbcType=INTEGER} and BIZ_TYPE = 2 and OPER_TYPE = 2)
      </if>
      <if test="amountMin != null">
        and C.AMOUNT &gt;= #{amountMin,jdbcType=DECIMAL}
      </if>
      <if test="amountMax != null">
        and C.AMOUNT &lt;= #{amountMax,jdbcType=DECIMAL}
      </if>
      <if test="orderTimeStart != null and orderTimeEnd != null">
        and C.ORDER_TIME between #{orderTimeStart,jdbcType=TIMESTAMP} and #{orderTimeEnd,jdbcType=TIMESTAMP}
      </if>
      <if test="traderName != null and traderName != ''">
        AND C.TRADER_NAME like CONCAT('%', #{traderName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        AND C.TRADER_CONTACT_NAME like CONCAT('%', #{traderContactName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="mobile != null and mobile != ''">
        AND C.MOBILE like CONCAT('%', #{mobile,jdbcType=VARCHAR}, '%')
      </if>
      <if test="telephone != null and telephone != ''">
        AND C.TELEPHONE like CONCAT('%', #{telephone,jdbcType=VARCHAR}, '%')
      </if>
      <if test="productCommentsSale != null and productCommentsSale != ''">
        AND C.PRODUCT_COMMENTS_SALE like CONCAT('%', #{productCommentsSale,jdbcType=VARCHAR}, '%')
      </if>
      <if test="comments != null and comments != ''">
        AND C.COMMENTS like CONCAT('%', #{comments,jdbcType=VARCHAR}, '%')
      </if>
      <if test="tagIdList != null and tagIdList.size() &gt; 0">
        <foreach collection="tagIdList" index="index" item="item">
          and find_in_set(#{item}, C.TAG_IDS)
        </foreach>
      </if>
      <if test="terminalTraderName != null and terminalTraderName != ''">
        AND C.TERMINAL_TRADER_NAME like CONCAT('%', #{terminalTraderName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="terminalTraderNatureList != null and terminalTraderNatureList.size() != 0">
        and C.TERMINAL_TRADER_NATURE in
        <foreach collection="terminalTraderNatureList" index="index" item="item" open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="(provinceIdListStr != null and provinceIdListStr.size() > 0) or (cityIdListStr != null and cityIdListStr.size() > 0) or (countyIdListStr != null and countyIdListStr.size() > 0)">
        and (
        <trim prefix="" prefixOverrides="or" suffix="" suffixOverrides="">
          <if test="provinceIdListStr != null and provinceIdListStr.size() &gt; 0">
            <foreach collection="provinceIdListStr" index="index" item="item">
              or C.TERMINAL_TRADER_REGION like concat('%', #{item,jdbcType=INTEGER}, '%')
            </foreach>
          </if>

          <if test="cityIdListStr != null and cityIdListStr.size() &gt; 0">
            <foreach collection="cityIdListStr" index="index" item="item">
              or C.TERMINAL_TRADER_REGION like concat('%', #{item,jdbcType=INTEGER}, '%')
            </foreach>
          </if>

          <if test="countyIdListStr != null and countyIdListStr.size() &gt; 0">
            <foreach collection="countyIdListStr" index="index" item="item">
              or C.TERMINAL_TRADER_REGION like concat('%', #{item,jdbcType=INTEGER}, '%')
            </foreach>
          </if>
        </trim>
        )
      </if>
      <if test="customerRelationshipList != null and customerRelationshipList.size() != 0">
        <foreach collection="customerRelationshipList" index="index" item="item">
          and find_in_set(#{item}, C.CUSTOMER_RELATIONSHIP)
        </foreach>
      </if>
      <if test="purchasingType != null">
        and C.PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER}
      </if>
      <if test="purchasingTypeList != null and purchasingTypeList.size() != 0">
        and C.PURCHASING_TYPE in
        <foreach collection="purchasingTypeList" index="index" item="item" open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="biddingPhaseList != null and biddingPhaseList.size() != 0">
        and C.BIDDING_PHASE in
        <foreach collection="biddingPhaseList" index="index" item="item" open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="biddingParameter != null">
        and C.BIDDING_PARAMETER = #{biddingParameter,jdbcType=INTEGER}
      </if>
      <if test="biddingParameterList != null and biddingParameterList.size() != 0">
        and C.BIDDING_PARAMETER in
        <foreach collection="biddingParameterList" index="index" item="item" open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="type != null">
        AND C.TYPE = #{type,jdbcType=INTEGER}
      </if>
      <if test="leadsNo != null and leadsNo != ''">
        AND TBL.LEADS_NO like CONCAT('%', #{leadsNo,jdbcType=VARCHAR}, '%')
      </if>
      <if test="skuNo != null and skuNo != ''">
        AND TQG.SKU = #{skuNo,jdbcType=VARCHAR}
      </if>
      <if test="productName != null and productName != ''">
        AND TQG.GOODS_NAME like CONCAT('%', #{productName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="brandName != null and brandName != ''">
        AND TQG.BRAND_NAME like CONCAT('%', #{brandName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="modelNumber != null and modelNumber != ''">
        AND TQG.MODEL like CONCAT('%', #{modelNumber,jdbcType=VARCHAR}, '%')
      </if>

      <if test="queryAll != null and queryAll == 0">
          AND
        (
            <!-- 归属人 -->
            EXISTS (
                SELECT 1 
                FROM (
                    <foreach collection="userIdList" index="index" item="userId" separator="UNION ALL">
                        SELECT #{userId,jdbcType=INTEGER} AS USER_ID
                    </foreach>
                ) t 
                WHERE C.USER_ID = t.USER_ID
            )

            <!-- 创建人 -->
            OR EXISTS (
                SELECT 1
                FROM (
                    <foreach collection="userIdList" index="index" item="userId" separator="UNION ALL">
                        SELECT #{userId,jdbcType=INTEGER} AS USER_ID
                    </foreach>
                ) t
                WHERE C.CREATOR = t.USER_ID
            )

            <!-- 修改协作人条件，包含所有三种类型的协作人 -->
            OR EXISTS (
                SELECT 1 FROM T_R_SALES_J_BUSINESS_ORDER
                WHERE BUSINESS_ID = C.BUSSINESS_CHANCE_ID 
                AND BUSINESS_TYPE = 1
                AND IS_DELETED = 0 
                AND SALE_USER_ID = #{currentUserId,jdbcType=INTEGER}
            )
            
            /* 2. 线下销售作为协作人 - 根据归属销售(线上销售)查询对应的线下销售 */
            OR EXISTS (
                SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
                WHERE RUR.ONLINE_SALES_ID = C.USER_ID  
                AND RUR.OFFLINE_SALES_ID = #{currentUserId,jdbcType=INTEGER}
            )
            
            /* 3. 产线负责人作为协作人 */
            OR EXISTS (
                SELECT 1 FROM ROLE_USER_CATEGORY_CONFIG RUC
                WHERE RUC.USER_ID = #{currentUserId,jdbcType=INTEGER}
                AND EXISTS (
                    SELECT 1 FROM T_BUSINESS_ORDER_CATEGORY TBOC 
                    WHERE TBOC.BUSINESS_ID = C.BUSSINESS_CHANCE_ID
                    AND TBOC.BUSINESS_TYPE = 1
                    AND FIND_IN_SET(TBOC.CATEGORY_ID, RUC.CATEGORY_IDS) > 0
                )
                AND (
                    NOT EXISTS (
                        SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR2
                        WHERE RUR2.PRODUCTION_USER_ID = RUC.USER_ID
                    )
                    OR EXISTS (
                        SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR3
                        WHERE RUR3.PRODUCTION_USER_ID = RUC.USER_ID
                        AND RUR3.ONLINE_SALES_ID = C.USER_ID
                    )
                )
            )
        )
      </if>

        GROUP BY C.BUSSINESS_CHANCE_ID
        ORDER BY C.BUSSINESS_CHANCE_ID DESC
    </select>


    <select id="getDetailById" resultType="com.vedeng.erp.business.domain.dto.BusinessChanceDto">
        SELECT
      C.BUSSINESS_CHANCE_ID,
      C.FRONT_END_SEQ,
      C.INQUIRY,
      C.ENTRANCES,
      C.COMMUNICATION,
      C.SOURCE,
      C.CONTENT,
      C.FUNCTIONS,
      C.PROVINCE_ID,
      C.CITY_ID,
      C.COUNTY_ID,
      C.PROVINCE,
      C.CITY,
      C.COUNTY,
      C.BUSSINESS_CHANCE_NO,
      C.OTHER_CONTACT as "otherContactInfo",
      C.TRADER_ID,
      C.TRADER_NAME,
      CASE TYC.NAME when TYC.NAME IS NOT NULL THEN 'Y'
      ELSE 'N' END AS "tycFlag",
      CUSTOMER.TRADER_CUSTOMER_ID,
      CUSTOMER.CUSTOMER_NATURE,
      C.CHECK_TRADER_NAME,
      C.TRADER_CONTACT_ID,
      C.TRADER_CONTACT_NAME,
      C.MOBILE,
      C.TELEPHONE,
      C.TERMINAL_TRADER_NAME,
      C.TERMINAL_TRADER_NATURE,
      S2.TITLE as terminalTraderNatureStr,
      C.TERMINAL_TRADER_REGION,
      C.SYSTEM_BUSINESS_LEVEL,
      S1.TITLE as systemBusinessLevelStr,
      C.BUSINESS_TYPE,
      S0.TITLE as businessTypeStr,
      C.AMOUNT,
      C.ORDER_TIME,
      C.PRODUCT_COMMENTS_SALE,
      C.TAG_IDS,
      C.CUSTOMER_RELATIONSHIP,
      C.PURCHASING_TYPE,
      S3.TITLE as purchasingTypeStr,
      C.BIDDING_PHASE,
      C.BIDDING_PARAMETER,
      S4.TITLE as biddingPhaseStr,
      C.COMMENTS,
      C.TYPE,
      S5.TITLE as typeStr,
      C.USER_ID,
      U.USERNAME,
      TUD.ALIAS_HEAD_PICTURE as belongPic,
      C.STATUS_COMMENTS,
      C.CLOSED_COMMENTS,
      TBL.ID as businessLeadsId,
      TBL.LEADS_NO,
               C.ADD_TIME,
               C.STAGE,
               C.PRELIMINARY_NEGOTIATION_TIME,
               C.OPPORTUNITY_VERIFICATION_TIME,
               C.PRELIMINARY_SCHEME_TIME,
               C.FINAL_SCHEME_TIME,
               C.WINNING_ORDER_TIME,
               C.LOSE_ORDER_TIME,
               O.ID          as 'customDataOperDto.id',
               O.ADD_TIME    as 'customDataOperDto.addTime',
               O.CREATOR     as 'customDataOperDto.creator',
               O.BELONGER_ID as 'customDataOperDto.belongerId',
               O.BELONGER    as 'customDataOperDto.belonger',
               Q.QUOTEORDER_ID       AS 'quoteorderDto.quoteorderId',
               Q.QUOTEORDER_NO       AS 'quoteorderDto.quoteorderNo',
               Q.ADD_TIME            AS 'quoteorderDto.addTime',
               Q.TOTAL_AMOUNT        AS 'quoteorderDto.totalAmount',
               Q.FOLLOW_ORDER_STATUS AS 'quoteorderDto.followOrderStatus',
               Q.VALID_STATUS        AS 'quoteorderDto.validStatus'
<!--               S.SALEORDER_ID        AS 'saleorderInfoDto.saleorderId',-->
<!--               S.SALEORDER_NO        AS 'saleorderInfoDto.saleorderNo',-->
<!--               S.ADD_TIME            AS 'saleorderInfoDto.addTime',-->
<!--               S.TOTAL_AMOUNT        AS 'saleorderInfoDto.totalAmount',-->
<!--               S.`STATUS`            AS 'saleorderInfoDto.status'-->
        FROM T_BUSSINESS_CHANCE C
                 LEFT JOIN T_TRADER_INFO_TYC TYC ON C.TRADER_NAME = TYC.NAME
                 LEFT JOIN T_CUSTOM_DATA_OPER O ON O.RELATED_ID = C.BUSSINESS_CHANCE_ID and O.BIZ_TYPE = 2 and O.OPER_TYPE = 2
                 and O.BELONGER_ID = #{currentUserId,jdbcType=INTEGER}
                 LEFT JOIN T_TRADER_CUSTOMER CUSTOMER ON C.TRADER_ID = CUSTOMER.TRADER_ID
                 left join T_BUSINESS_LEADS TBL on TBL.BUSINESS_CHANCE_ID = C.BUSSINESS_CHANCE_ID
                 LEFT JOIN T_QUOTEORDER Q ON Q.BUSSINESS_CHANCE_ID = C.BUSSINESS_CHANCE_ID
<!--                 LEFT JOIN T_SALEORDER S ON S.QUOTEORDER_ID = Q.QUOTEORDER_ID-->
                 LEFT JOIN T_USER U ON U.USER_ID = C.USER_ID
                 left join T_USER_DETAIL TUD on TUD.USER_ID = C.USER_ID
                  left join T_SYS_OPTION_DEFINITION S0 on C.BUSINESS_TYPE = S0.SYS_OPTION_DEFINITION_ID
                  left join T_SYS_OPTION_DEFINITION S1 on C.SYSTEM_BUSINESS_LEVEL = S1.SYS_OPTION_DEFINITION_ID
                  left join T_SYS_OPTION_DEFINITION S2 on C.TERMINAL_TRADER_NATURE = S2.SYS_OPTION_DEFINITION_ID
                  left join T_SYS_OPTION_DEFINITION S3 on C.PURCHASING_TYPE = S3.SYS_OPTION_DEFINITION_ID
                  left join T_SYS_OPTION_DEFINITION S4 on C.BIDDING_PHASE = S4.SYS_OPTION_DEFINITION_ID
                  left join T_SYS_OPTION_DEFINITION S5 on C.TYPE = S5.SYS_OPTION_DEFINITION_ID
        WHERE C.BUSSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER}
    </select>
    <select id="getChancesByNoAfterMerged" resultType="com.vedeng.erp.business.domain.dto.MergeChanceGoods">
        SELECT B.BUSSINESS_CHANCE_ID,
               B.GOODS_CATEGORY,
               B.GOODS_BRAND,
               B.GOODS_NAME,
               B.CONTENT,
               B.ADD_TIME,
               A.NAME     AS ATTACHMENT_NAME,
               A.URI      AS ATTACHMENT_URI,
               A.DOMAIN   AS ATTACHMENT_DOMAIN,
               sod1.TITLE AS GOODS_CATEGORY_NAME
        FROM T_BUSSINESS_CHANCE B
                 LEFT JOIN T_ATTACHMENT A ON A.RELATED_ID = B.BUSSINESS_CHANCE_ID
            AND A.ATTACHMENT_FUNCTION = 463
                 LEFT JOIN T_SYS_OPTION_DEFINITION sod1 ON sod1.SYS_OPTION_DEFINITION_ID = B.GOODS_CATEGORY
        WHERE B.BUSSINESS_CHANCE_NO = #{businessChanceNo}
          AND B.MERGE_STATUS = 1

        ORDER BY B.BUSSINESS_CHANCE_ID DESC
    </select>
    <select id="getCommunicateChanceInfo" resultType="java.util.Map">
        SELECT C.BUSSINESS_CHANCE_ID AS RELATED_ID,
               C.BUSSINESS_CHANCE_NO AS ORDER_NO
        FROM T_BUSSINESS_CHANCE C
        WHERE C.BUSSINESS_CHANCE_ID IN
        <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="getConcernNum" resultType="java.lang.Integer">
        select count(distinct TBC.BUSSINESS_CHANCE_ID)
        from T_BUSSINESS_CHANCE TBC
                 inner join T_CUSTOM_DATA_OPER TCDO
                            on TBC.BUSSINESS_CHANCE_ID = TCDO.RELATED_ID and TCDO.BIZ_TYPE = 2 and TCDO.OPER_TYPE = 2
        where TCDO.BELONGER_ID = #{userId,jdbcType=INTEGER}
            <if test="statusList != null and statusList.size() != 0">
                and TBC.STATUS in
                <foreach close=")" collection="statusList" item="status" open="(" separator=",">
                    #{status,jdbcType=INTEGER}
                </foreach>
            </if>
    </select>


    <select id="getConcernNumByUserList" resultType="java.lang.Integer">
        select count(distinct TBC.BUSSINESS_CHANCE_ID)
        from T_BUSSINESS_CHANCE TBC
        inner join T_CUSTOM_DATA_OPER TCDO
        on TBC.BUSSINESS_CHANCE_ID = TCDO.RELATED_ID and TCDO.BIZ_TYPE = 2 and TCDO.OPER_TYPE = 2
        where TCDO.BELONGER_ID in
        <foreach close=")" collection="userIdList" item="userId" open="(" separator=",">
            #{userId,jdbcType=INTEGER}
        </foreach>
        <if test="statusList != null and statusList.size() != 0">
            and TBC.STATUS in
            <foreach close=")" collection="statusList" item="status" open="(" separator=",">
                #{status,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>

    <select id="getGoodsPosition" resultType="java.lang.Integer">
        select VCS.GOODS_POSITION_NO
        from T_BUSSINESS_CHANCE TBC
                 left join T_QUOTEORDER TQ on TBC.BUSSINESS_CHANCE_ID = TQ.BUSSINESS_CHANCE_ID
                 left join T_QUOTEORDER_GOODS TQG on TQ.QUOTEORDER_ID = TQG.QUOTEORDER_ID
                 left join V_CORE_SKU VCS on TQG.SKU = VCS.SKU_NO
        where TBC.BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
          and VCS.GOODS_POSITION_NO is not null
          and VCS.GOODS_POSITION_NO != 0
    </select>

<!--auto generated by MybatisCodeHelper on 2024-07-22-->
  <update id="updateStageByBusinessChanceId">
    update T_BUSSINESS_CHANCE
    set STAGE=#{updatedStage,jdbcType=INTEGER}
    where BUSSINESS_CHANCE_ID=#{bussinessChanceId,jdbcType=INTEGER}
    and STAGE &lt; #{updatedStage,jdbcType=INTEGER}
  </update>

  <update id="updateStageTimeByBusinessChanceId" parameterType="com.vedeng.erp.business.domain.entity.BussinessChanceEntity">
    update T_BUSSINESS_CHANCE
    <set>
      <if test="preliminaryNegotiationTime != null">
        PRELIMINARY_NEGOTIATION_TIME = #{preliminaryNegotiationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="opportunityVerificationTime != null">
        OPPORTUNITY_VERIFICATION_TIME = #{opportunityVerificationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preliminarySchemeTime != null">
        PRELIMINARY_SCHEME_TIME = #{preliminarySchemeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finalSchemeTime != null">
        FINAL_SCHEME_TIME = #{finalSchemeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="winningOrderTime != null">
        WINNING_ORDER_TIME = #{winningOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loseOrderTime != null">
        LOSE_ORDER_TIME = #{loseOrderTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
    <if test="preliminaryNegotiationTime != null">
      AND PRELIMINARY_NEGOTIATION_TIME IS NULL
    </if>
    <if test="opportunityVerificationTime != null">
      AND OPPORTUNITY_VERIFICATION_TIME IS NULL
    </if>
    <if test="preliminarySchemeTime != null">
      AND PRELIMINARY_SCHEME_TIME IS NULL
    </if>
    <if test="finalSchemeTime != null">
      AND FINAL_SCHEME_TIME IS NULL
    </if>
    <if test="winningOrderTime != null">
      AND WINNING_ORDER_TIME IS NULL
    </if>
    <if test="loseOrderTime != null">
      AND LOSE_ORDER_TIME IS NULL
    </if>
  </update>

<!--auto generated by MybatisCodeHelper on 2024-07-23-->
  <update id="updateSystemBusinessLevel">
    update T_BUSSINESS_CHANCE
    set SYSTEM_BUSINESS_LEVEL=#{updatedSystemBusinessLevel,jdbcType=INTEGER}
    where BUSSINESS_CHANCE_ID=#{bussinessChanceId,jdbcType=INTEGER}
  </update>

  <select id="findAllShareUser" resultType="com.vedeng.erp.system.dto.UserDto">
    select
    DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
    a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
    c.CC_NUMBER,c.ALIAS_HEAD_PICTURE
    from
    T_USER a
    left join
    T_USER_DETAIL c on a.USER_ID = c.USER_ID
    where a.USER_ID IN (
    select distinct
    TBL.SALE_USER_ID
    from T_R_SALES_J_BUSINESS_ORDER TBL
    WHERE IS_DELETED=0 AND BUSINESS_TYPE=1
    )
    AND a.USER_ID > 1
    <if test="name != null and name != ''">
      AND a.USERNAME LIKE CONCAT('%',#{name},'%')
    </if>
    ORDER BY a.USERNAME  ASC    LIMIT 100
  </select>


  <select id="findAllBelongUserForBussinessStep1" resultType="java.lang.Integer">
  select distinct
  TBC.USER_ID
  from T_BUSSINESS_CHANCE TBC
  where
  TBC.COMPANY_ID = 1
  AND TBC.MERGE_STATUS != 1
  AND USER_ID IN
  <foreach collection="allSubordinateUserIdList" index="index" item="item" open="(" separator="," close=")">
    #{item,jdbcType=INTEGER}
  </foreach>
  </select>

  <select id="findAllBelongUserForBussinessStep2" resultType="java.lang.Integer">
    select distinct
    TBC.USER_ID
    from T_BUSSINESS_CHANCE TBC JOIN T_R_SALES_J_BUSINESS_ORDER RELATE
    ON TBC.BUSSINESS_CHANCE_ID = RELATE.BUSINESS_ID AND RELATE.BUSINESS_TYPE=1
    WHERE
    TBC.COMPANY_ID = 1
    AND TBC.MERGE_STATUS != 1
    AND RELATE.SALE_USER_ID = #{userId,jdbcType=INTEGER}
  </select>

  <select id="findAllBelongUserForBussinessStep3" resultType="com.vedeng.erp.system.dto.UserDto">
    select
      DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
               a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
               c.CC_NUMBER,c.ALIAS_HEAD_PICTURE
    from
      T_USER a
        left join
      T_USER_DETAIL c on a.USER_ID = c.USER_ID
    where a.USER_ID IN
    <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    AND a.USER_ID >1
    <if test="name != null and name != ''">
      AND a.USERNAME LIKE CONCAT('%',#{name},'%')
    </if>
    ORDER BY a.USERNAME  ASC    LIMIT 100
  </select>

  <select id="findAllCreatorForBussinessStep1" resultType="java.lang.Integer">
    select distinct
    TBC.CREATOR
    from T_BUSSINESS_CHANCE TBC
    where
    TBC.COMPANY_ID = 1
    AND TBC.MERGE_STATUS != 1
    AND CREATOR IN
    <foreach collection="allSubordinateUserIdList" index="index" item="item" open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

  <select id="findAllCreatorForBussinessStep2" resultType="java.lang.Integer">
    select distinct
      TBC.CREATOR
    from T_BUSSINESS_CHANCE TBC JOIN T_R_SALES_J_BUSINESS_ORDER RELATE
                                     ON TBC.BUSSINESS_CHANCE_ID = RELATE.BUSINESS_ID AND RELATE.BUSINESS_TYPE=1
    WHERE
      TBC.COMPANY_ID = 1
      AND TBC.MERGE_STATUS != 1
      AND RELATE.SALE_USER_ID = #{userId,jdbcType=INTEGER}
  </select>

  <select id="findAllCreatorForBussinessStep3" resultType="com.vedeng.erp.system.dto.UserDto">
    select
    DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
    a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
    c.CC_NUMBER,c.ALIAS_HEAD_PICTURE
    from
    T_USER a
    left join
    T_USER_DETAIL c on a.USER_ID = c.USER_ID
    where a.USER_ID IN
    <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    AND a.USER_ID >1
    <if test="name != null and name != ''">
      AND a.USERNAME LIKE CONCAT('%',#{name},'%')
    </if>
    ORDER BY a.USERNAME  ASC    LIMIT 100
  </select>


  <select id="findAllBelongUserForBussiness" resultType="com.vedeng.erp.system.dto.UserDto">
    select
    DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
    a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
    c.CC_NUMBER,c.ALIAS_HEAD_PICTURE
    from
    T_USER a
    left join
    T_USER_DETAIL c on a.USER_ID = c.USER_ID
    where a.USER_ID IN (
        select distinct
          TBC.USER_ID
        from T_BUSSINESS_CHANCE TBC
        where
          TBC.COMPANY_ID = 1
          AND TBC.MERGE_STATUS != 1
          AND USER_ID IN
        <foreach collection="allSubordinateUserIdList" index="index" item="item" open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
        UNION ALL
        select distinct
            TBC.USER_ID
        from T_BUSSINESS_CHANCE TBC JOIN T_R_SALES_J_BUSINESS_ORDER RELATE
            ON TBC.BUSSINESS_CHANCE_ID = RELATE.BUSINESS_ID AND RELATE.BUSINESS_TYPE=1
        WHERE
          TBC.COMPANY_ID = 1
          AND TBC.MERGE_STATUS != 1
          AND RELATE.SALE_USER_ID = #{userId,jdbcType=INTEGER}
    )
    AND a.USER_ID >1
    <if test="name != null and name != ''">
      AND a.USERNAME LIKE CONCAT('%',#{name},'%')
    </if>
    ORDER BY a.USERNAME  ASC    LIMIT 100
  </select>

  <select id="findAllBelongUser" resultType="com.vedeng.erp.system.dto.UserDto">
    select
    DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
    a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
    c.CC_NUMBER,c.ALIAS_HEAD_PICTURE
    from
    T_USER a
    left join
    T_USER_DETAIL c on a.USER_ID = c.USER_ID
    where a.USER_ID IN (
    select distinct
    TBC.USER_ID
    from T_BUSSINESS_CHANCE TBC
    where TBC.COMPANY_ID = 1 AND TBC.MERGE_STATUS != 1
    )
    AND a.USER_ID >1
    <if test="name != null and name != ''">
      AND a.USERNAME LIKE CONCAT('%',#{name},'%')
    </if>
    ORDER BY a.USERNAME  ASC    LIMIT 100
  </select>

  <select id="getLastTask" resultType="com.vedeng.erp.business.domain.dto.BusinessChanceDto">
    select
      TASK_ID as latestTaskId,
      TASK_CONTENT as latestTaskContent
    from T_TASK
    where BIZ_TYPE = 1
      and BIZ_ID = #{bussinessChanceId,jdbcType=INTEGER}
      and IS_DELETE = 0
    order by TASK_ID desc
    limit 1
  </select>

  <select id="findByAllCount" resultType="java.lang.Integer">
    SELECT count(0)
    FROM (
    SELECT
    distinct C.BUSSINESS_CHANCE_ID
    FROM T_BUSSINESS_CHANCE C
    <if test="attentionState != null and attentionState == 1">
      LEFT JOIN T_CUSTOM_DATA_OPER O ON O.RELATED_ID = C.BUSSINESS_CHANCE_ID and O.BIZ_TYPE = 2 and O.OPER_TYPE = 2
      and O.BELONGER_ID = #{currentUserId,jdbcType=INTEGER}
    </if>
    <if test="latestFollowUpTimeStart != null and latestFollowUpTimeEnd != null">
      left join (
      select RELATED_ID,
      max(COMMUNICATE_RECORD_ID) as COMMUNICATE_RECORD_ID
      from T_COMMUNICATE_RECORD
      where COMMUNICATE_TYPE = 244
      group by RELATED_ID
      ) R on R.RELATED_ID = C.BUSSINESS_CHANCE_ID
      left join T_COMMUNICATE_RECORD TCR on TCR.COMMUNICATE_RECORD_ID = R.COMMUNICATE_RECORD_ID
    </if>
    <if test="(deadlineDateStart != null and deadlineDateEnd != null) or (doneStatus != null)">
      left join T_TASK TT on TT.BIZ_ID = C.BUSSINESS_CHANCE_ID and TT.BIZ_TYPE = 1
    </if>
    <if test="collaboratorIdList != null and collaboratorIdList.size() != 0">
      left join T_R_SALES_J_BUSINESS_ORDER TRSJBO on TRSJBO.BUSINESS_ID = C.BUSSINESS_CHANCE_ID and TRSJBO.BUSINESS_TYPE = 1 and TRSJBO.IS_DELETED = 0
    </if>
    <if test="leadsNo != null and leadsNo != ''">
      left join T_BUSINESS_LEADS TBL on TBL.BUSINESS_CHANCE_ID = C.BUSSINESS_CHANCE_ID
    </if>
    <if test="(skuNo != null and skuNo != '') or (productName != null and productName != '') or (brandName != null and brandName != '') or (modelNumber != null and modelNumber != '')">
      left join T_QUOTEORDER TQ on TQ.BUSSINESS_CHANCE_ID = C.BUSSINESS_CHANCE_ID
      left join T_QUOTEORDER_GOODS TQG on TQG.QUOTEORDER_ID = TQ.QUOTEORDER_ID
    </if>
    WHERE C.COMPANY_ID = 1
    AND C.MERGE_STATUS != 1
    <if test="bussinessChanceNo != null and bussinessChanceNo != ''">
      and C.BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR}
    </if>
    <if test="businessTypeList != null and businessTypeList.size() != 0">
      and C.BUSINESS_TYPE in
      <foreach collection="businessTypeList" index="index" item="item" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="systemBusinessLevelList != null and systemBusinessLevelList.size() != 0">
      and C.SYSTEM_BUSINESS_LEVEL in
      <foreach collection="systemBusinessLevelList" index="index" item="item" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="stageList != null and stageList.size() != 0">
      and C.STAGE in
      <foreach collection="stageList" index="index" item="item" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>
    
    <!-- 添加商品分类ID过滤条件 -->
    <if test="categoryIdList != null and categoryIdList.size() > 0">
      AND EXISTS (
        SELECT 1 FROM T_BUSINESS_ORDER_CATEGORY TBOC
        WHERE TBOC.BUSINESS_ID = C.BUSSINESS_CHANCE_ID 
        AND TBOC.BUSINESS_TYPE = 1  <!-- 1表示商机 -->
        AND TBOC.CATEGORY_ID IN
        <foreach item="categoryId" index="index" collection="categoryIdList" separator="," open="(" close=")">
          #{categoryId,jdbcType=INTEGER}
        </foreach>
      )
    </if>
    
    <!-- 添加省份和城市过滤条件，根据销售关系配置 -->
    <if test="provinceIds != null and provinceIds.size() > 0">
      AND EXISTS (
        SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
        WHERE RUR.PROVINCE_ID IN
        <foreach item="provinceId" index="index" collection="provinceIds" separator="," open="(" close=")">
          #{provinceId,jdbcType=INTEGER}
        </foreach>
        AND C.USER_ID IN (
          SELECT DISTINCT 
            CASE 
              WHEN RUR.ONLINE_SALES_ID > 0 THEN RUR.ONLINE_SALES_ID
              WHEN RUR.OFFLINE_SALES_ID > 0 THEN RUR.OFFLINE_SALES_ID
              ELSE 0
            END AS SALES_ID
          FROM ROLE_USER_REGION_CONFIG RUR
          WHERE RUR.PROVINCE_ID IN
          <foreach item="provinceId" index="index" collection="provinceIds" separator="," open="(" close=")">
            #{provinceId,jdbcType=INTEGER}
          </foreach>
          AND (RUR.ONLINE_SALES_ID > 0 OR RUR.OFFLINE_SALES_ID > 0)
        )
      )
    </if>
    
    <if test="cityIds != null and cityIds.size() > 0">
      AND EXISTS (
        SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
        WHERE RUR.CITY_ID IN
        <foreach item="cityId" index="index" collection="cityIds" separator="," open="(" close=")">
          #{cityId,jdbcType=INTEGER}
        </foreach>
        AND C.USER_ID IN (
          SELECT DISTINCT 
            CASE 
              WHEN RUR.ONLINE_SALES_ID > 0 THEN RUR.ONLINE_SALES_ID
              WHEN RUR.OFFLINE_SALES_ID > 0 THEN RUR.OFFLINE_SALES_ID
              ELSE 0
            END AS SALES_ID
          FROM ROLE_USER_REGION_CONFIG RUR
          WHERE RUR.CITY_ID IN
          <foreach item="cityId" index="index" collection="cityIds" separator="," open="(" close=")">
            #{cityId,jdbcType=INTEGER}
          </foreach>
          AND (RUR.ONLINE_SALES_ID > 0 OR RUR.OFFLINE_SALES_ID > 0)
        )
      )
    </if>
    
    <if test="latestFollowUpTimeStart != null and latestFollowUpTimeEnd != null">
      and TCR.ADD_TIME between #{latestFollowUpTimeStart,jdbcType=BIGINT} and #{latestFollowUpTimeEnd,jdbcType=BIGINT}
    </if>
    <if test="deadlineDateStart != null and deadlineDateEnd != null">
      and TT.DEADLINE between #{deadlineDateStart,jdbcType=TIMESTAMP} and #{deadlineDateEnd,jdbcType=TIMESTAMP}
    </if>
    <if test="doneStatus != null">
      and TT.DONE_STATUS = #{doneStatus,jdbcType=INTEGER}
    </if>
    <if test="addTimeStart != null and addTimeEnd != null">
      and C.ADD_TIME between #{addTimeStart,jdbcType=BIGINT} and #{addTimeEnd,jdbcType=BIGINT}
    </if>
    <if test="belongerIdList != null and belongerIdList.size() != 0">
      and C.USER_ID in
      <foreach close=")" collection="belongerIdList" index="index" item="item" open="(" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="collaboratorIdList != null and collaboratorIdList.size() != 0">
      and TRSJBO.SALE_USER_ID in
      <foreach close=")" collection="collaboratorIdList" index="index" item="item" open="(" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="attentionState != null and attentionState == 1">
      AND O.OPER_TYPE = 2
    </if>
    <if test="attentionState != null and attentionState == 0">
      AND C.BUSSINESS_CHANCE_ID NOT IN (SELECT RELATED_ID FROM T_CUSTOM_DATA_OPER WHERE BELONGER_ID = #{currentUserId,jdbcType=INTEGER} and BIZ_TYPE = 2 and OPER_TYPE = 2)
    </if>
    <if test="amountMin != null">
      and C.AMOUNT &gt;= #{amountMin,jdbcType=DECIMAL}
    </if>
    <if test="amountMax != null">
      and C.AMOUNT &lt;= #{amountMax,jdbcType=DECIMAL}
    </if>
    <if test="orderTimeStart != null and orderTimeEnd != null">
      and C.ORDER_TIME between #{orderTimeStart,jdbcType=TIMESTAMP} and #{orderTimeEnd,jdbcType=TIMESTAMP}
    </if>
    <if test="traderName != null and traderName != ''">
      AND C.TRADER_NAME like CONCAT('%', #{traderName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="traderContactName != null and traderContactName != ''">
      AND C.TRADER_CONTACT_NAME like CONCAT('%', #{traderContactName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="mobile != null and mobile != ''">
      AND C.MOBILE like CONCAT('%', #{mobile,jdbcType=VARCHAR}, '%')
    </if>
    <if test="telephone != null and telephone != ''">
      AND C.TELEPHONE like CONCAT('%', #{telephone,jdbcType=VARCHAR}, '%')
    </if>
    <if test="productCommentsSale != null and productCommentsSale != ''">
      AND C.PRODUCT_COMMENTS_SALE like CONCAT('%', #{productCommentsSale,jdbcType=VARCHAR}, '%')
    </if>
    <if test="comments != null and comments != ''">
      AND C.COMMENTS like CONCAT('%', #{comments,jdbcType=VARCHAR}, '%')
    </if>
    <if test="tagIdList != null and tagIdList.size() &gt; 0">
      <foreach collection="tagIdList" index="index" item="item">
        and find_in_set(#{item}, C.TAG_IDS)
      </foreach>
    </if>
    <if test="terminalTraderName != null and terminalTraderName != ''">
      AND C.TERMINAL_TRADER_NAME like CONCAT('%', #{terminalTraderName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="terminalTraderNatureList != null and terminalTraderNatureList.size() != 0">
      and C.TERMINAL_TRADER_NATURE in
      <foreach collection="terminalTraderNatureList" index="index" item="item" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="(provinceIdListStr != null and provinceIdListStr.size() > 0) or (cityIdListStr != null and cityIdListStr.size() > 0) or (countyIdListStr != null and countyIdListStr.size() > 0)">
      and (
      <trim prefix="" prefixOverrides="or" suffix="" suffixOverrides="">
        <if test="provinceIdListStr != null and provinceIdListStr.size() &gt; 0">
          <foreach collection="provinceIdListStr" index="index" item="item">
            or C.TERMINAL_TRADER_REGION like concat('%', #{item,jdbcType=INTEGER}, '%')
          </foreach>
        </if>

        <if test="cityIdListStr != null and cityIdListStr.size() &gt; 0">
          <foreach collection="cityIdListStr" index="index" item="item">
            or C.TERMINAL_TRADER_REGION like concat('%', #{item,jdbcType=INTEGER}, '%')
          </foreach>
        </if>

        <if test="countyIdListStr != null and countyIdListStr.size() &gt; 0">
          <foreach collection="countyIdListStr" index="index" item="item">
            or C.TERMINAL_TRADER_REGION like concat('%', #{item,jdbcType=INTEGER}, '%')
          </foreach>
        </if>
      </trim>
      )
    </if>
    <if test="customerRelationshipList != null and customerRelationshipList.size() != 0">
      <foreach collection="customerRelationshipList" index="index" item="item">
        and find_in_set(#{item}, C.CUSTOMER_RELATIONSHIP)
      </foreach>
    </if>
    <if test="purchasingType != null">
      and C.PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER}
    </if>
    <if test="purchasingTypeList != null and purchasingTypeList.size() != 0">
      and C.PURCHASING_TYPE in
      <foreach collection="purchasingTypeList" index="index" item="item" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="biddingPhaseList != null and biddingPhaseList.size() != 0">
      and C.BIDDING_PHASE in
      <foreach collection="biddingPhaseList" index="index" item="item" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="biddingParameter != null">
      and C.BIDDING_PARAMETER = #{biddingParameter,jdbcType=INTEGER}
    </if>
    <if test="biddingParameterList != null and biddingParameterList.size() != 0">
      and C.BIDDING_PARAMETER in
      <foreach collection="biddingParameterList" index="index" item="item" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="type != null">
      AND C.TYPE = #{type,jdbcType=INTEGER}
    </if>
    <if test="leadsNo != null and leadsNo != ''">
      AND TBL.LEADS_NO like CONCAT('%', #{leadsNo,jdbcType=VARCHAR}, '%')
    </if>
    <if test="skuNo != null and skuNo != ''">
      AND TQG.SKU = #{skuNo,jdbcType=VARCHAR}
    </if>
    <if test="productName != null and productName != ''">
      AND TQG.GOODS_NAME like CONCAT('%', #{productName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="brandName != null and brandName != ''">
      AND TQG.BRAND_NAME like CONCAT('%', #{brandName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="modelNumber != null and modelNumber != ''">
      AND TQG.MODEL like CONCAT('%', #{modelNumber,jdbcType=VARCHAR}, '%')
    </if>

    <if test="queryAll != null and queryAll == 0">
      AND
      (
      EXISTS (
          SELECT 1 
          FROM (
              <foreach collection="userIdList" index="index" item="userId" separator="UNION ALL">
                  SELECT #{userId,jdbcType=INTEGER} AS USER_ID
              </foreach>
          ) t 
          WHERE C.USER_ID = t.USER_ID
      )

      <!-- 修改协作人条件，包含所有三种类型的协作人 -->
      OR EXISTS (
          SELECT 1 FROM T_R_SALES_J_BUSINESS_ORDER
          WHERE BUSINESS_ID = C.BUSSINESS_CHANCE_ID 
          AND BUSINESS_TYPE = 1
          AND IS_DELETED = 0 
          AND SALE_USER_ID = #{currentUserId,jdbcType=INTEGER}
      )
      
      /* 2. 线下销售作为协作人 - 根据归属销售(线上销售)查询对应的线下销售 */
      OR EXISTS (
          SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
          WHERE RUR.ONLINE_SALES_ID = C.USER_ID  
          AND RUR.OFFLINE_SALES_ID = #{currentUserId,jdbcType=INTEGER}
      )
      
      /* 3. 产线负责人作为协作人 */
      OR EXISTS (
          SELECT 1 FROM ROLE_USER_CATEGORY_CONFIG RUC
          WHERE RUC.USER_ID = #{currentUserId,jdbcType=INTEGER}
          AND EXISTS (
              SELECT 1 FROM T_BUSINESS_ORDER_CATEGORY TBOC 
              WHERE TBOC.BUSINESS_ID = C.BUSSINESS_CHANCE_ID
              AND TBOC.BUSINESS_TYPE = 1
              AND FIND_IN_SET(TBOC.CATEGORY_ID, RUC.CATEGORY_IDS) > 0
          )
          AND (
              NOT EXISTS (
                  SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR2
                  WHERE RUR2.PRODUCTION_USER_ID = RUC.USER_ID
              )
              OR EXISTS (
                  SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR3
                  WHERE RUR3.PRODUCTION_USER_ID = RUC.USER_ID
                  AND RUR3.ONLINE_SALES_ID = C.USER_ID
              )
          )
      )

      <if test="businessIdList != null and businessIdList.size() != 0">
          OR
          C.BUSSINESS_CHANCE_ID IN
          <foreach close=")" collection="businessIdList" index="index" item="businessId" open="(" separator=",">
              #{businessId,jdbcType=INTEGER}
          </foreach>
      </if>
      )
    </if>
    GROUP BY C.BUSSINESS_CHANCE_ID
    ) AS table_count
  </select>
  
  <select id="findShareTag1" resultType="java.lang.Integer">
    SELECT DISTINCT C.USER_ID
      FROM T_BUSSINESS_CHANCE C
      /* 2. 线下销售作为协作人 - 根据归属销售(线上销售)查询对应的线下销售 */
      where  EXISTS (
        SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
        WHERE RUR.ONLINE_SALES_ID = C.USER_ID
          AND RUR.OFFLINE_SALES_ID = #{currentUserId,jdbcType=INTEGER}
      )
  
        /* 3. 产线负责人作为协作人 */
         OR EXISTS (
        SELECT 1 FROM ROLE_USER_CATEGORY_CONFIG RUC
        WHERE RUC.USER_ID = #{currentUserId,jdbcType=INTEGER}
          AND EXISTS (
          SELECT 1 FROM T_BUSINESS_ORDER_CATEGORY TBOC
          WHERE TBOC.BUSINESS_ID = C.BUSSINESS_CHANCE_ID
            AND TBOC.BUSINESS_TYPE = 1
            AND FIND_IN_SET(TBOC.CATEGORY_ID, RUC.CATEGORY_IDS) > 0
        )
          AND (
          NOT EXISTS (
            SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR2
            WHERE RUR2.PRODUCTION_USER_ID = RUC.USER_ID
          )
            OR EXISTS (
            SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR3
            WHERE RUR3.PRODUCTION_USER_ID = RUC.USER_ID
              AND RUR3.ONLINE_SALES_ID = C.USER_ID
          )
        )
    )
  </select>
  
  <select id="findShareTag2" resultType="java.lang.Integer">
    SELECT DISTINCT C.CREATOR
    FROM T_BUSSINESS_CHANCE C
    /* 2. 线下销售作为协作人 - 根据归属销售(线上销售)查询对应的线下销售 */
    where  EXISTS (
      SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
      WHERE RUR.ONLINE_SALES_ID = C.USER_ID
        AND RUR.OFFLINE_SALES_ID = #{currentUserId,jdbcType=INTEGER}
    )

      /* 3. 产线负责人作为协作人 */
       OR EXISTS (
      SELECT 1 FROM ROLE_USER_CATEGORY_CONFIG RUC
      WHERE RUC.USER_ID = #{currentUserId,jdbcType=INTEGER}
        AND EXISTS (
        SELECT 1 FROM T_BUSINESS_ORDER_CATEGORY TBOC
        WHERE TBOC.BUSINESS_ID = C.BUSSINESS_CHANCE_ID
          AND TBOC.BUSINESS_TYPE = 1
          AND FIND_IN_SET(TBOC.CATEGORY_ID, RUC.CATEGORY_IDS) > 0
      )
        AND (
        NOT EXISTS (
          SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR2
          WHERE RUR2.PRODUCTION_USER_ID = RUC.USER_ID
        )
          OR EXISTS (
          SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR3
          WHERE RUR3.PRODUCTION_USER_ID = RUC.USER_ID
            AND RUR3.ONLINE_SALES_ID = C.USER_ID
        )
        )
    )
  </select>
</mapper>
