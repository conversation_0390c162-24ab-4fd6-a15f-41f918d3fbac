package com.vedeng.aftersales.model.dto;

import java.io.Serializable;
import java.util.List;

public class AfterSaleServiceStandardApplyQueryDto implements Serializable {

    private String keyWord;

    /**
     * 产品是否可安装
     */
    private String productInstall;

    /**
     * 供应商售后政策
     */
    private String supplyMatainment;

    /**
     * 状态
     */
    private String status;

    /**
     * 安装类型
     */
    private String installTrainType;

    /**
     * 审核人
     */
    private String auditPerson;

    /**
     * 归属产品经理
     */
    private String productManagerName;

    /**
     * 分类id
     */
    private Integer categoryId;

    /**
     * 来自工作台的业务类型
     */
    private Integer buzTypeFromTodoList;

    /**
     * 来自工作台的商品等级
     */
    private Integer goodsLevelFromTodoList;

    /**
     * 下属用户集合字符串，逗号分隔
     */
    private String subordinateList;

    /**
     * 下属用户集合
     */
    private List<Integer>  subordinates;

    private Integer hasAfterSaleServiceLabel;

    private Integer afterSalesServiceLevel;

    public String getSubordinateList() {
        return subordinateList;
    }

    public void setSubordinateList(String subordinateList) {
        this.subordinateList = subordinateList;
    }

    public List<Integer> getSubordinates() {
        return subordinates;
    }

    public void setSubordinates(List<Integer> subordinates) {
        this.subordinates = subordinates;
    }

    public Integer getBuzTypeFromTodoList() {
        return buzTypeFromTodoList;
    }

    public void setBuzTypeFromTodoList(Integer buzTypeFromTodoList) {
        this.buzTypeFromTodoList = buzTypeFromTodoList;
    }

    public Integer getGoodsLevelFromTodoList() {
        return goodsLevelFromTodoList;
    }

    public void setGoodsLevelFromTodoList(Integer goodsLevelFromTodoList) {
        this.goodsLevelFromTodoList = goodsLevelFromTodoList;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }


    public String getSupplyMatainment() {
        return supplyMatainment;
    }

    public void setSupplyMatainment(String supplyMatainment) {
        this.supplyMatainment = supplyMatainment;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getProductManagerName() {
        return productManagerName;
    }

    public void setProductManagerName(String productManagerName) {
        this.productManagerName = productManagerName;
    }

    public String getAuditPerson() {
        return auditPerson;
    }

    public void setAuditPerson(String auditPerson) {
        this.auditPerson = auditPerson;
    }

    public String getInstallTrainType() {
        return installTrainType;
    }

    public String getProductInstall() {
        return productInstall;
    }

    public void setProductInstall(String productInstall) {
        this.productInstall = productInstall;
    }

    public void setInstallTrainType(String installTrainType) {
        this.installTrainType = installTrainType;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getHasAfterSaleServiceLabel() {
        return hasAfterSaleServiceLabel;
    }

    public void setHasAfterSaleServiceLabel(Integer hasAfterSaleServiceLabel) {
        this.hasAfterSaleServiceLabel = hasAfterSaleServiceLabel;
    }



    public Integer getAfterSalesServiceLevel() {
        return afterSalesServiceLevel;
    }

    public void setAfterSalesServiceLevel(Integer afterSalesServiceLevel) {
        this.afterSalesServiceLevel = afterSalesServiceLevel;
    }
}
