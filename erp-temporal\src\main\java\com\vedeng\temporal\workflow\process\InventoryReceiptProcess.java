package com.vedeng.temporal.workflow.process;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.enums.ExecutionMode;
import com.vedeng.temporal.exception.ExceptionHandler;
import com.vedeng.temporal.workflow.activity.CompanySequenceActivity;
import com.vedeng.temporal.workflow.activity.InventoryReceiptActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import com.vedeng.temporal.workflow.step.impl.InventoryReceiptStepV3;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * 库存入库流程服务 - 独立流程版本
 * 基于AbstractBusinessProcess架构，专门处理库存入库相关业务
 *
 * 业务职责：
 * - 专注于库存入库单处理
 * - 包含完整的入库流程：查询采购单号 → 等待快递状态 → 创建快递 → 查询库存记录 → 创建同行单
 * - 支持异步快递签收流程
 * 
 * 架构特点：
 * - 继承 AbstractBusinessProcess 统一框架
 * - 使用独立的 InventoryReceiptActivity 接口
 * - 串行执行模式，确保数据一致性
 * - 正序执行（A → B → C → D）
 * 
 * 执行特点：
 * - 每个公司等待前一个公司完成基础步骤
 * - 包含完整的库存入库处理逻辑
 * - 异步处理快递签收，不阻塞主流程
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0 (独立流程版本)
 * @since 2025-01-30
 */
@Slf4j
public class InventoryReceiptProcess extends AbstractBusinessProcess {

    private final InventoryReceiptActivity inventoryReceiptActivity;

    public InventoryReceiptProcess(CompanySequenceActivity companySequenceActivity,
                                   ExceptionHandler exceptionHandler,
                                   InventoryReceiptActivity inventoryReceiptActivity) {

        // 调用父类构造函数
        super(companySequenceActivity, exceptionHandler);

        // 保存Activity依赖
        this.inventoryReceiptActivity = inventoryReceiptActivity;
    }

    @Override
    protected ExecutionMode getExecutionMode() {
        return ExecutionMode.PARALLEL;
    }

    @Override
    protected boolean isReverseOrder() {
        return false; // 正序执行
    }

    @Override
    protected List<BusinessStep> getBusinessSteps() {
        // 库存入库流程只包含一个步骤：库存入库单处理
        return Collections.singletonList(
            new InventoryReceiptStepV3(inventoryReceiptActivity)
        );
    }

    @Override
    protected String getProcessName() {
        return "库存入库流程";
    }

    /**
     * 执行库存入库流程
     * 使用AbstractBusinessProcess统一框架
     *
     * @param request 业务请求
     * @param companySequence 公司执行序列
     * @return 执行结果
     */
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, List<String> companySequence) {
        return super.execute(request, companySequence);
    }
}
