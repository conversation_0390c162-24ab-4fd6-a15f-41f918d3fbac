<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/flowOrder/list.css?rnd=${resourceVersionKey}">

<div id="app" class="list-wrap">
    <div class="search-wrap">
        <div class="search-filter-list">
            <div class="search-item">
                <div class="search-label">流转单号：</div>
                <div class="search-cnt">
                    <el-input v-model="searchParams.flowOrderNo" size="mini"></el-input>
                </div>
            </div>
            <div class="search-item">
                <div class="search-label">业务单号：</div>
                <div class="search-cnt">
                    <el-input v-model="searchParams.baseOrderNo" size="mini"></el-input>
                </div>
            </div>
            <div class="search-item">
                <div class="search-label">末级名称：</div>
                <div class="search-cnt">
                    <el-input v-model="searchParams.lastTraderName" size="mini"></el-input>
                </div>
            </div>
            <div class="search-item">
                <div class="search-label">SKU：</div>
                <div class="search-cnt">
                    <el-input v-model="searchParams.skuNo" size="mini"></el-input>
                </div>
            </div>
            <div class="search-item">
                <div class="search-label">品牌：</div>
                <div class="search-cnt">
                    <el-input v-model="searchParams.brand" size="mini"></el-input>
                </div>
            </div>
            <div class="search-item">
                <div class="search-label">末级合同状态：</div>
                <div class="search-cnt">
                    <el-select v-model="searchParams.contractStatus" size="mini" clearable>
                        <el-option label="无需上传" :value="0"></el-option>
                        <el-option label="未上传" :value="1"></el-option>
                        <el-option label="已上传" :value="2"></el-option>
                    </el-select>
                </div>
            </div>
            <div class="search-item">
                <div class="search-label">创建人：</div>
                <div class="search-cnt">
                    <el-select v-model="searchParams.creatorList" size="mini" multiple collapse-tags filterable clearable>
                        <el-option v-for="item in creatorList" :key="item.userId" :label="item.username" :value="item.userId" ></el-option>
                    </el-select>
                </div>
            </div>
            <div class="search-item">
                <div class="search-label">创建时间：</div>
                <div class="search-cnt">
                    <el-date-picker
                        v-model="searchParams.createDate"
                        type="daterange"
                        range-separator="至"
                        size="mini"
                        value-format="yyyy-MM-dd"
                        @change="handlerDateChange"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </div>
            </div>
            <div class="search-item">
                <div class="search-label">推送方向：</div>
                <div class="search-cnt">
                    <el-select v-model="searchParams.pushDirection" size="mini" clearable>
                        <el-option label="金蝶" :value="1"></el-option>
                        <el-option label="ERP" :value="2"></el-option>
                    </el-select>
                </div>
            </div>
        </div>
        <div class="search-filter-btns">
            <el-button type="primary" size="mini" @click="handlerSearch">搜索</el-button>
            <el-button size="mini" @click="handlerReset">重置</el-button>
            <el-button type="primary" size="mini" plain @click="showAddDialog">新建</el-button>
        </div>
    </div>
    <div class="list-container">
        <div class="list-tab-wrap">
            <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" active-text-color="#409EFF" @select="handleTabSelect">
                <el-menu-item index="1">全部</el-menu-item>
                <el-menu-item index="2">
                    <div class="tab-title-txt">
                        采购未审核
                        <el-badge class="mark" :value="buyNum" :max="99" v-if="buyNum > 0"/>
                    </div>
                </el-menu-item>
                <el-menu-item index="3">采购已审核</el-menu-item>
                <el-menu-item index="4">
                    <div class="tab-title-txt">
                        销售未审核
                        <el-badge class="mark" :value="saleNum" :max="99" v-if="saleNum > 0"/>
                    </div>
                </el-menu-item>
                <el-menu-item index="5">销售已审核</el-menu-item>
            </el-menu>
        </div>
        <div class="list-table-wrap" v-if="!loading">
            <el-table :data="listData" :key="listIndex" ref="listTable" fit header-row-class-name="table-th" size="mini" border highlight-current-row style="width: 100%" :max-height="tableMaxHeight" empty-text>
                <el-table-column prop="flowOrderNo" label="流转单号" width="150">
                    <template slot-scope="scope">
                        <div class="text-link" @click="gotoDetail(scope.row)">{{ scope.row.flowOrderNo }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="pushDirectionName" label="推送方向" width="80"></el-table-column>
                <el-table-column prop="sourceErpName" label="来源ERP" width="80"></el-table-column>
                <el-table-column prop="baseBusinessTypeStr" label="类型" width="50"></el-table-column>
                <el-table-column prop="baseOrderNo" label="业务单号" width="180">
                    <!-- <template slot-scope="scope">
                        <div class="text-link" @click="gotoOrderDetail(scope.row)">{{ scope.row.baseOrderNo }}</div>
                    </template> -->
                </el-table-column>
                <el-table-column prop="firstTraderName" label="1级名称" min-width="220"></el-table-column>
                <el-table-column prop="lastTraderName" label="末级名称" min-width="220"></el-table-column>
                <el-table-column prop="auditStatusStr" label="审核状态" width="80"></el-table-column>
                <el-table-column prop="isPushed" label="推送金蝶状态" width="100"></el-table-column>
                <el-table-column prop="contractStatusStr" label="末级合同状态" width="100"></el-table-column>
                <el-table-column prop="creatorName" label="创建人" width="100"></el-table-column>
                <el-table-column prop="addTime" label="创建时间" width="145"></el-table-column>
                <el-table-column prop="auditUsername" label="审核人" width="100"></el-table-column>
                <el-table-column prop="auditTime" label="审核时间" width="145"></el-table-column>
            </el-table>
            <div class="pagination-wrap">
                <el-pagination
                    background
                    :page-sizes="[20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    @size-change="handlerPagesizeChange"
                    @current-change="handlerPagenumChange"
                >
                </el-pagination>
            </div>
        </div>
    </div>

    <el-dialog
        title="新建"
        :visible.sync="isShowAddDialog"
        :close-on-click-modal="false"
        custom-class="dialog-form-wrap"
        width="480px"
    >
        <el-form label-width="120px">
            <el-form-item label="推送方向：" required>
                <el-radio-group v-model="dialogValue.pushDirection" @change="handlerPushDirectionChange">
                    <template  v-for="item in pushDirectionList">
                        <el-radio :label="item.value" :key="item.value">{{ item.label }}</el-radio>
                    </template>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="来源ERP：" required>
                <el-select 
                    v-model="dialogValue.sourceErp" 
                    :disabled="sourceErpDisabled" 
                    style="width: 260px;"
                >
                    <el-option
                        v-for="item in sourceErpList"
                        :key="item.id"
                        :label="item.companyName"
                        :value="item.companyShortName">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="类型：" required>
                <el-select 
                    v-model="dialogValue.baseBusinessType" 
                    :disabled="baseBusinessTypeDisabled" 
                    size="mini" style="width: 260px;"
                >
                    <el-option
                        v-for="item in baseBusinessTypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="关联单号：" required :class="{'is-error': baseBusinessNoError}">
                <div style="width: 260px">
                    <el-input v-model="dialogValue.baseBusinessNo" size="mini" @blur="validAddBusinessNo"></el-input>
                </div>
                <div class="el-form-item__error" v-if="baseBusinessNoError">{{ baseBusinessNoError }}</div>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="gotoAdd" size="mini">确 定</el-button>
            <el-button @click="isShowAddDialog = false" size="mini">取 消</el-button>
        </div>
    </el-dialog>
</div>

<script src="${pageContext.request.contextPath}/static/js/flowOrder/list.js?rnd=${resourceVersionKey}"></script>