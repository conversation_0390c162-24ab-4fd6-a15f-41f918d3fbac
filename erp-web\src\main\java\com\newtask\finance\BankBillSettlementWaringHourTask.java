package com.newtask.finance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.enums.PatternEnum;
import com.vedeng.common.core.utils.ErpCharUtils;
import com.vedeng.common.core.utils.ErpDateUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.SettlementNoticeRecordDto;
import com.vedeng.erp.finance.service.BankBillApiService;
import com.vedeng.erp.finance.service.SettlementInfoApiService;
import com.vedeng.erp.finance.service.SettlementNoticeRecordService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.dto.TraderBelongDto;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.finance.service.BankBillAutoSettlementService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.infrastructure.wxrobot.constants.WxRobotMsgTemple;
import com.vedeng.infrastructure.wxrobot.dto.WxMsgDto;
import com.vedeng.infrastructure.wxrobot.service.WxRobotService;
import com.vedeng.uac.api.dto.WxUserDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@JobHandler("BankBillSettlementWaringHourTask")
@Component
@Slf4j
public class BankBillSettlementWaringHourTask extends AbstractJobHandler {
    @Value("${settlement_waring_at_name:Crystal}")
    private String settlementWaringAtName;

    @Autowired
    private BankBillApiService bankBillApiService;
    @Autowired
    private TraderCustomerApiService traderCustomerApiService;
    @Autowired
    private SettlementInfoApiService settlementInfoApiService;
    @Autowired
    private BankBillAutoSettlementService bankBillAutoSettlementService;
    @Autowired
    private WxRobotService wxRobotService;
    @Value("${settlement_notice_robot:********-e6ad-418d-8e43-277ad2dae87a}")
    private String settlementNoticeRobot;
    @Autowired
    private SettlementNoticeRecordService settlementNoticeRecordService;
    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;
    @Autowired
    private UserApiService userApiService;
    /**
     * 
     * 告警 任务 【整点告警】
     * 1. 建设银行
     * 3. 能够出现在 财务管理 - 订单结款页 的银行流水
     * 4. 在 财务管理 - 订单结款页 有归属销售
     * 5. 有订单但不满足自动结款 或 没有销售订单
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        // 30天内
        String startTime = ErpDateUtils.format(DateUtils.addDays(new Date(), -30), PatternEnum.YYYY_MM_DD_HH_MM_SS);
        // 1小时前
        String endTime = ErpDateUtils.format(DateUtils.addMinutes(new Date(), -61), PatternEnum.YYYY_MM_DD_HH_MM_SS);
        List<BankBillDto> bankBillDtos = bankBillApiService.querySettlementBankBillInfo(startTime,endTime);

        if (CollUtil.isEmpty(bankBillDtos)){
            log.info("银行流水：{}，无符合条件数据", bankBillDtos);
            return ReturnT.SUCCESS;
        }
        List<String> traderNames = bankBillDtos.stream().map(BankBillDto::getAccName1).collect(Collectors.toList());
        List<TraderBelongDto> traderBelongList = traderCustomerApiService.getTraderBelongInfo(traderNames);
        Map<String, List<TraderBelongDto>> belongMap = new HashMap<>();
        if (CollUtil.isNotEmpty(traderBelongList)){
            belongMap = traderBelongList.stream().distinct().collect(Collectors.groupingBy(TraderBelongDto::getTraderName, Collectors.toList()));
            if (MapUtils.isEmpty(belongMap)){
                belongMap = new HashMap<>();
            }
        }
        for (BankBillDto bankBillDto : bankBillDtos) {
            log.info("银行流水：{}", bankBillDto.getTranFlow());
            // 判断有归属销售
            List<TraderBelongDto> traderBelongDtos = belongMap.get(bankBillDto.getAccName1());
            if (CollUtil.isEmpty(traderBelongDtos)){
                // 未匹配到，尝试重新获取
                // 判断TraderName是否有中英文空号，如果有统一先转中文，再转中文，然后再转左中右英，最后转左英右中
                String accName1 = bankBillDto.getAccName1();
                boolean isContains = ErpCharUtils.containsBrackets(accName1);
                if (!isContains){
                    log.info("银行流水：{}，无归属销售", bankBillDto.getTranFlow());
                    continue;
                }
                log.info("含中英文括号：{}", accName1);
                for (int i = 0 ; i < 3; i++){
                    switch (i){
                        case 0:
                            accName1 = ErpCharUtils.toChinese(accName1);
                            break;
                        case 1:
                            accName1 = ErpCharUtils.toEnglish(accName1);
                            break;
                        case 2:
                            accName1 = ErpCharUtils.toLeftChineseRightEnglish(accName1);
                            break;
                        case 3:
                            accName1 = ErpCharUtils.toLeftEnglishRightChinese(accName1);
                            break;
                    }
                    bankBillDto.setAccName1(accName1);
                    List<TraderBelongDto> integers = traderCustomerApiService.getTraderBelongInfo(Arrays.asList(accName1));
                    if (CollUtil.isNotEmpty(integers)){
                        traderBelongDtos = integers;
                        break;
                    }
                }

                if (CollUtil.isEmpty(traderBelongDtos)) {
                    log.info("银行流水：{}，无归属销售", bankBillDto.getTranFlow());
                    continue;
                }
            }
            if (traderBelongDtos.size() != 1){
                log.info("银行流水：{}，归属销售数量多条", bankBillDto.getTranFlow());
                continue;   
            }
            if (!isContinue(bankBillDto.getBankBillId())){
                log.info("银行流水：{}，本次无需通知", bankBillDto.getTranFlow());
                continue;
            }
            TraderBelongDto traderBelongDto = traderBelongDtos.get(0);

            Map<String, String> userNumberMap = settlementNoticeRecordService.getUserNumber();
            if (userNumberMap.containsKey(traderBelongDto.getNumber())){
                traderBelongDto.setNumber(userNumberMap.get(traderBelongDto.getNumber()));
            }
            
            // 判断有无订单
            Boolean hasSaleOrder = settlementInfoApiService.matchBankBill(bankBillDto);
            if (!hasSaleOrder){
                Integer todayCount = getTodayCount(bankBillDto.getBankBillId(),1);
                String message = StrUtil.format(WxRobotMsgTemple.SETTLEMENT_NO_ORDER, 
                        traderBelongDto.getNumber(),
                        traderBelongDto.getTraderName(),
                        bankBillDto.getTranFlow(),
                        ErpDateUtils.format(bankBillDto.getTrandate(), PatternEnum.YYYY_MM_DD) +" " +  ErpDateUtils.format(bankBillDto.getTrantime(), PatternEnum.HH_MM_SS) ,
                        bankBillDto.getAmt(), 
                        bankBillDto.getAmt().subtract(bankBillDto.getMatchedAmount()),
                        todayCount
                );
                if (isDimission(traderBelongDto.getNumber())){
                    // 离职了 
                    // 查找上级
                    UserDto creatorUser = userApiService.getUserBaseInfoByJobNumber(traderBelongDto.getNumber());
                    UserDto parentUser = userApiService.getUserById(creatorUser.getParentId());
                    message = StrUtil.format(WxRobotMsgTemple.SETTLEMENT_NO_ORDER_DISMISSION,
                            parentUser.getNumber(),
                            traderBelongDto.getUserName(),
                            traderBelongDto.getTraderName(),
                            bankBillDto.getTranFlow(),
                            ErpDateUtils.format(bankBillDto.getTrandate(), PatternEnum.YYYY_MM_DD) +" " +  ErpDateUtils.format(bankBillDto.getTrantime(), PatternEnum.HH_MM_SS) ,
                            bankBillDto.getAmt(),
                            bankBillDto.getAmt().subtract(bankBillDto.getMatchedAmount()),
                            todayCount
                    );
                }
                sendNotice(message);
                continue;
            }
            // 如果有订单，判断是否自动结款
            Integer matchSaleOrderId = bankBillAutoSettlementService.checkBankBillSaleOrder(bankBillDto, new Date());
            if (Objects.nonNull(matchSaleOrderId)) {
                continue;
            }
            Integer todayCount = getTodayCount(bankBillDto.getBankBillId(),2);
            String message = StrUtil.format(WxRobotMsgTemple.CAN_NOT_AUTO_SETTLEMENT,
                    traderBelongDto.getNumber(),
                    traderBelongDto.getTraderName(),
                    bankBillDto.getTranFlow(),
                    ErpDateUtils.format(bankBillDto.getRealTrandatetime(), PatternEnum.YYYY_MM_DD_HH_MM_SS),
                    bankBillDto.getAmt(),
                    bankBillDto.getAmt().subtract(bankBillDto.getMatchedAmount()),
                    todayCount,
                    settlementWaringAtName
            );
            if (isDimission(traderBelongDto.getNumber())){
                // 离职了 
                // 查找上级
                UserDto creatorUser = userApiService.getUserBaseInfoByJobNumber(traderBelongDto.getNumber());
                UserDto parentUser = userApiService.getUserById(creatorUser.getParentId());
                message = StrUtil.format(WxRobotMsgTemple.CAN_NOT_AUTO_SETTLEMENT_DISMISSION,
                        parentUser.getNumber(),
                        traderBelongDto.getUserName(),
                        traderBelongDto.getTraderName(),
                        bankBillDto.getTranFlow(),
                        ErpDateUtils.format(bankBillDto.getRealTrandatetime(), PatternEnum.YYYY_MM_DD_HH_MM_SS),
                        bankBillDto.getAmt(),
                        bankBillDto.getAmt().subtract(bankBillDto.getMatchedAmount()),
                        todayCount,
                        settlementWaringAtName
                );
            }
            sendNotice(message);
           
        }
        return ReturnT.SUCCESS;
    }

    private void sendNotice(String msg) {
        WxMsgDto wxMsgDto = new WxMsgDto().initWxMsgDto(msg);
        wxRobotService.send(settlementNoticeRobot, wxMsgDto);
    }
    
    private Integer getTodayCount(Integer bankBillId, Integer noticeType) {
        Date now = new Date();
        Integer todayCount = 1;
        List<SettlementNoticeRecordDto> list = settlementNoticeRecordService.querySettlementNoticeRecord(bankBillId, noticeType);
        if (CollUtil.isEmpty(list)){
            SettlementNoticeRecordDto settlementNoticeRecordDto = new SettlementNoticeRecordDto();
            settlementNoticeRecordDto.setBankBillId(bankBillId);
            settlementNoticeRecordDto.setNoticeType(noticeType);
            settlementNoticeRecordDto.setNoticeCount(1);
            settlementNoticeRecordDto.setLastNoticeTime(new Date());
            settlementNoticeRecordService.createSettlementNoticeRecord(settlementNoticeRecordDto);
            return todayCount;
        }
        SettlementNoticeRecordDto settlementNoticeRecordDto = list.get(0);
        // 判断最近一次时间是否跨天，如果是，则重置次数为1
        if (!DateUtil.isSameDay(settlementNoticeRecordDto.getLastNoticeTime(), now)) {
            SettlementNoticeRecordDto update = new SettlementNoticeRecordDto();
            update.setNoticeCount(1);
            update.setLastNoticeTime(now);
            update.setSettlementNoticeRecordId(settlementNoticeRecordDto.getSettlementNoticeRecordId());
            settlementNoticeRecordService.updateSettlementNoticeRecord(update);
            return todayCount;
        }
        todayCount = settlementNoticeRecordDto.getNoticeCount() + 1;
        SettlementNoticeRecordDto update = new SettlementNoticeRecordDto();
        update.setNoticeCount(todayCount);
        update.setLastNoticeTime(now);
        update.setSettlementNoticeRecordId(settlementNoticeRecordDto.getSettlementNoticeRecordId());
        settlementNoticeRecordService.updateSettlementNoticeRecord(update);
        return todayCount;
    }
    

    /**
     * 最近一次时间距离本次时间是否超过60分钟
     * @param bankBillId
     * @return
     */
    private Boolean isContinue(Integer bankBillId) {
        List<SettlementNoticeRecordDto> list = settlementNoticeRecordService.querySettlementNoticeRecord(bankBillId, null);
        if (CollUtil.isEmpty(list)){
            log.info("银行流水：{}，小时任务第1次发送", bankBillId);
            return true;
        }
        // 最近一次时间距离本次时间是否超过50分钟
        SettlementNoticeRecordDto lastOne = list.get(0);
        log.info("bankBillId:{},最近一次时间距离本次时间是否超过1小时：{}", bankBillId,ErpDateUtils.format(lastOne.getLastNoticeTime(), PatternEnum.YYYY_MM_DD_HH_MM_SS));
        return DateUtils.addMinutes(lastOne.getLastNoticeTime(), 50).before(new Date());
    }

    /**
     * 判断是否离职
     */
    private Boolean isDimission(String number){
        RestfulResult<WxUserDto> wxUserByJobNumber = uacWxUserInfoApiService.getWxUserByJobNumber(number);
        if (!wxUserByJobNumber.isSuccess()){
            return false;
        }
        WxUserDto data = wxUserByJobNumber.getData();
        if (Objects.isNull(data)){
            return false;
        }
        if (Objects.equals(data.getStatus(), 5)){
            // 已离职
            return true;
        }
        return false;
    }
}
