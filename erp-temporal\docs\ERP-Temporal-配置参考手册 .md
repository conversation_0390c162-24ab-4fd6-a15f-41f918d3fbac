# ERP Temporal 配置参考手册

## 概述

本文档详细说明了 `erp-temporal` 模块中所有可配置的参数，包括配置键名、默认值、说明和使用场景。

## 配置文件

**主配置文件**: `erp-temporal/src/main/resources/erp-temporal.properties`

**Apollo 配置**: 在 Apollo 配置中心的 `application` namespace 中配置

## 配置分类

### 1. Temporal 服务器配置

| 配置键 | 默认值 | 类型 | 说明 | 使用类 |
|--------|--------|------|------|--------|
| `temporal.server.host` | `*************` | String | Temporal 服务器地址 | TemporalProperties |
| `temporal.server.port` | `7233` | int | Temporal 服务器端口 | TemporalProperties |

### 2. Temporal 命名空间配置

| 配置键 | 默认值 | 类型 | 说明 | 使用类 |
|--------|--------|------|------|--------|
| `temporal.namespace` | `erp-namespace` | String | 工作流命名空间 | TemporalProperties |
| `temporal.namespace.description` | `ERP业务工作流命名空间` | String | 命名空间描述 | TemporalProperties |
| `temporal.namespace.auto.create` | `true` | boolean | 是否自动创建命名空间 | TemporalProperties |
| `temporal.namespace.auto.update` | `true` | boolean | 是否自动更新命名空间 | TemporalProperties |
| `temporal.namespace.retention.days` | `30` | int | 命名空间数据保留天数 | TemporalProperties |

### 3. Worker 配置

| 配置键 | 默认值 | 类型 | 说明 | 使用类 |
|--------|--------|------|------|--------|
| `temporal.worker.threads` | `10` | int | Worker 线程数 | TemporalProperties |
| `temporal.worker.max-concurrent-activity-executions` | `20` | int | Worker 最大并发 Activity 执行数 | TemporalProperties |
| `temporal.worker.max-concurrent-workflow-task-executions` | `10` | int | Worker 最大并发工作流任务执行数 | TemporalProperties |

### 4. 任务队列配置

| 配置键 | 默认值 | 类型 | 说明 | 使用类 |
|--------|--------|------|------|--------|
| `temporal.taskQueue.multiCompany` | `erp-multi-company-queue` | String | 多公司业务任务队列 | TemporalProperties |

### 5. 工作流配置

| 配置键 | 默认值 | 类型 | 说明 | 使用类 |
|--------|--------|------|------|--------|
| `temporal.workflow.execution-timeout-hours` | `8` | int | 工作流执行超时时间（小时）⚠️ **重要** | TemporalProperties |
| `temporal.workflow.task-timeout-minutes` | `10` | int | 工作流任务超时时间（分钟） | TemporalProperties |
| `temporal.workflow.id-prefix` | `multi-company-` | String | 工作流ID前缀 | TemporalProperties |

> **⚠️ 重要说明**: `temporal.workflow.execution-timeout-hours` 默认值已从 **2小时** 更新为 **8小时**，以解决复杂业务流程超时问题。可根据业务需要调整：
> - **开发环境**: 建议 2-4小时
> - **测试环境**: 建议 4-6小时  
> - **生产环境**: 建议 8-12小时

### 6. Activity 配置

| 配置键 | 默认值 | 类型 | 说明 | 使用类 |
|--------|--------|------|------|--------|
| `temporal.activity.start-to-close-timeout` | `30` | int | Activity 执行超时时间（分钟） | TemporalProperties |
| `temporal.activity.retry.maximum-attempts` | `3` | int | Activity 重试最大次数 | TemporalProperties |
| `temporal.activity.retry.initial-interval` | `10` | int | Activity 重试初始间隔（秒） | TemporalProperties |
| `temporal.activity.retry.backoff-coefficient` | `2.0` | double | Activity 重试退避系数 | TemporalProperties |

### 7. 通知配置

| 配置键 | 默认值 | 类型 | 说明 | 使用类 |
|--------|--------|------|------|--------|
| `temporal.notification.robot.webhook` | `https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fbe14b75-24cd-4690-9347-665c68769680` | String | 微信机器人通知 Webhook 地址 | TemporalProperties |

### 8. 轮询配置

| 配置键 | 默认值 | 类型 | 说明 | 使用类 |
|--------|--------|------|------|--------|
| `temporal.polling.enabled` | `true` | boolean | 轮询功能总开关 | TemporalProperties |
| `temporal.polling.initial-interval-seconds` | `30` | int | 初始轮询间隔（秒） | TemporalProperties |
| `temporal.polling.max-interval-seconds` | `300` | int | 最大轮询间隔（秒） | TemporalProperties |
| `temporal.polling.backoff-coefficient` | `1.5` | double | 退避系数 | TemporalProperties |
| `temporal.polling.max-timeout-days` | `7` | int | 最大超时时间（天） | TemporalProperties |
| `temporal.polling.heartbeat-interval-minutes` | `5` | int | 心跳间隔（分钟） | TemporalProperties |
| `temporal.polling.max-retry-count` | `10` | int | 最大重试次数 | TemporalProperties |
| `temporal.polling.verbose-logging` | `false` | boolean | 是否启用详细日志 | TemporalProperties |

## 配置验证

系统启动时会自动验证以下配置：

1. **数值格式验证**（TemporalProperties）：
   - Activity 超时时间配置为分钟数（正整数）
   - Activity 重试间隔配置为秒数（正整数）
   - Worker 线程数为正整数
   - 轮询相关时间配置为正整数

2. **轮询配置逻辑验证**：
   - `initial-interval-seconds > 0`
   - `max-interval-seconds > 0`
   - `max-interval-seconds >= initial-interval-seconds`
   - `backoff-coefficient > 1.0`
   - `max-timeout-days > 0`
   - `heartbeat-interval-minutes > 0`
   - `max-retry-count > 0`

## 环境配置建议

### 开发环境
```properties
# 工作流配置 - 开发环境使用较短超时时间，便于快速调试
temporal.workflow.execution-timeout-hours=2
temporal.workflow.task-timeout-minutes=5
temporal.workflow.id-prefix=dev-multi-company-

# 轮询和Activity配置 - 便于调试
temporal.polling.verbose-logging=true
temporal.polling.initial-interval-seconds=10
temporal.polling.max-timeout-days=1
temporal.activity.start-to-close-timeout=10
```

### 测试环境
```properties
# 工作流配置 - 测试环境使用中等超时时间
temporal.workflow.execution-timeout-hours=4
temporal.workflow.task-timeout-minutes=8
temporal.workflow.id-prefix=test-multi-company-

# 其他配置
temporal.polling.initial-interval-seconds=15
temporal.polling.max-timeout-days=3
temporal.activity.start-to-close-timeout=20
```

### 生产环境
```properties
# 工作流配置 - 生产环境使用充足的超时时间
temporal.workflow.execution-timeout-hours=12
temporal.workflow.task-timeout-minutes=15
temporal.workflow.id-prefix=prod-multi-company-

# 其他配置 - 优化性能和稳定性
temporal.polling.verbose-logging=false
temporal.polling.initial-interval-seconds=60
temporal.polling.max-interval-seconds=600
temporal.polling.max-timeout-days=14
temporal.namespace.retention.days=90
temporal.activity.start-to-close-timeout=60
```

## 配置优先级

1. **Apollo 配置中心**（最高优先级）
2. **erp-temporal.properties 文件配置**
3. **@Value 注解默认值**（最低优先级）

## 注意事项

1. **配置同步**：配置文件与 TemporalProperties 类完全同步，共24个配置参数（新增工作流相关3个参数）
2. **工作流超时配置**：
   - `execution-timeout-hours` 是工作流的总执行超时时间，建议根据业务复杂度设置
   - `task-timeout-minutes` 是单个工作流任务的超时时间，通常保持默认值即可
   - **重要**：超时时间过短会导致复杂业务流程失败，过长会影响异常检测
3. **工作流ID前缀**：
   - 建议不同环境使用不同前缀，便于区分和管理
   - 前缀应以字母开头，避免特殊字符
4. **配置更新**：Apollo 配置更改后需要重启应用或使用配置热更新机制
5. **时间配置**：
   - 工作流超时使用小时数，Activity 超时时间使用分钟数，重试间隔使用秒数
   - 均为正整数
6. **布尔值**：使用 `true`/`false`，不区分大小写
7. **数值范围**：注意配置的合理范围，避免设置过大或过小的值
8. **兼容性**：配置类使用 Spring 4.1.9 兼容的 @Value 注解方式

## 配置示例

完整的配置文件示例：

```properties
# ERP Temporal 工作流引擎配置文件
# 与 TemporalProperties 类完全同步的配置参数

# ===== Temporal 服务器配置 =====
temporal.server.host=*************
temporal.server.port=7233

# ===== Temporal 命名空间配置 =====
temporal.namespace=erp-namespace
temporal.namespace.description=ERP业务工作流命名空间
temporal.namespace.auto.create=true
temporal.namespace.auto.update=true
temporal.namespace.retention.days=30

# ===== Worker 配置 =====
temporal.worker.threads=10
temporal.worker.max-concurrent-activity-executions=20
temporal.worker.max-concurrent-workflow-task-executions=10

# ===== 任务队列配置 =====
temporal.taskQueue.multiCompany=erp-multi-company-queue

# ===== 工作流配置 =====
temporal.workflow.execution-timeout-hours=8
temporal.workflow.task-timeout-minutes=10
temporal.workflow.id-prefix=multi-company-

# ===== Activity 配置 =====
temporal.activity.start-to-close-timeout=30
temporal.activity.retry.maximum-attempts=3
temporal.activity.retry.initial-interval=10
temporal.activity.retry.backoff-coefficient=2.0

# ===== 通知配置 =====
temporal.notification.robot.webhook=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fbe14b75-24cd-4690-9347-665c68769680

# ===== 轮询配置 =====
temporal.polling.enabled=true
temporal.polling.initial-interval-seconds=30
temporal.polling.max-interval-seconds=300
temporal.polling.backoff-coefficient=1.5
temporal.polling.max-timeout-days=7
temporal.polling.heartbeat-interval-minutes=5
temporal.polling.max-retry-count=10
temporal.polling.verbose-logging=false
```