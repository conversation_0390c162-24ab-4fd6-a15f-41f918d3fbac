package com.vedeng.erp.broadcast.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 部门播报前置配置表
 */
@Getter
@Setter
public class BroadcastDeptConfigEntity extends BaseEntity {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 一级通知部门ID
     */
    private Integer broadcastDeptId;

    /**
     * 日常标志（0/1）
     */
    private Integer dayFlag;

    /**
     * 周标志（0/1）
     */
    private Integer weekFlag;

    /**
     * 月标志（0/1）
     */
    private Integer monthFlag;

    /**
     * AED标志（0/1）
     */
    private Integer aedFlag;

    /**
     * 自有品牌标志（0/1）
     */
    private Integer zyFlag;

    /**
     * 自定义标志（0/1）
     */
    private Integer customFlag;

    /**
     * 到款阶梯 单位万元
     */
    private Integer amountStep;

    /**
     * 通知URL
     */
    private String webhook;

    /**
     * 是否已删除（0=否，1=是）
     */
    private Integer isDeleted;
}
