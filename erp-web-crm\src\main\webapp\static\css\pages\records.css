.records-panel {
  position: relative;
  height: 100%;
  overflow: hidden;
}
.records-panel .panel-title {
  font-size: 16px;
  font-weight: 700;
  line-height: 56px;
  padding: 0 20px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.records-panel .panel-wrap {
  height: calc(100% - 56px);
  overflow-y: auto;
}
.records-panel .panel-wrap::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.records-panel .panel-wrap::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.records-panel .panel-wrap::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.records-panel .panel-wrap::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.records-panel .panel-wrap::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.records-panel .panel-wrap.has-fixed-btn {
  height: calc(100% - 56px - 53px);
}
.records-panel .panel-wrap.dialog-inner {
  max-height: 510px;
  margin-top: 20px;
}
.records-panel .panel-btn-wrap {
  width: 100%;
  max-width: 353px;
  height: 53px;
  padding: 10px 20px;
  background: #fff;
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 2;
  display: flex;
  align-items: center;
}
.records-panel .panel-btn-wrap .btn {
  width: 100%;
  flex: 1;
}
.records-panel .panel-btn-wrap .panel-btn-link {
  color: #09f;
  cursor: pointer;
  margin-left: 20px;
}
.records-panel .panel-btn-wrap .panel-btn-link:hover {
  color: #f60;
}
.records-panel .panel-null-data {
  padding: 80px 0;
  text-align: center;
}
.records-panel .panel-null-data .icon {
  font-size: 32px;
  margin-bottom: 10px;
  color: #09F;
}
.records-panel .panel-null-data .font {
  color: #999;
}
.records-panel .followUpRecord-records {
  background: #fff;
  position: relative;
}
.records-panel .followUpRecord-records .subtitle {
  font-size: 14px;
  color: #999;
  line-height: 33px;
  background: #f5f7fa;
  padding: 0 20px;
}
.records-panel .followUpRecord-records .list {
  padding: 0 20px 0 40px;
}
.records-panel .followUpRecord-records .list .item {
  position: relative;
  padding: 20px 0;
  border-bottom: solid 1px #e1e5e8;
}
.records-panel .followUpRecord-records .list .item::before {
  content: "";
  display: block;
  width: 11px;
  height: 11px;
  border-radius: 50%;
  overflow: hidden;
  background: #09f;
  position: absolute;
  left: -21px;
  top: 24px;
  z-index: 2;
}
.records-panel .followUpRecord-records .list .item::after {
  content: "";
  display: block;
  border-left: dashed 1px #E1E5E8;
  position: absolute;
  left: -16px;
  top: 24px;
  bottom: -24px;
  z-index: 1;
}
.records-panel .followUpRecord-records .list .item:last-child {
  border-bottom: none;
}
.records-panel .followUpRecord-records .list .item:last-child::after {
  display: none;
}
.records-panel .followUpRecord-records .list .item .row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.records-panel .followUpRecord-records .list .item .row .creator {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}
.records-panel .followUpRecord-records .list .item .row .creator .icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  border-radius: 3px;
}
.records-panel .followUpRecord-records .list .item .row .creator .name {
  flex: 1;
  min-width: 0;
}
.records-panel .followUpRecord-records .list .item .row .time {
  width: 140px;
  flex-shrink: 0;
  white-space: nowrap;
  font-size: 12px;
  color: #999;
  text-align: right;
}
.records-panel .followUpRecord-records .list .item .content-suffix {
  margin-bottom: 5px;
}
.records-panel .followUpRecord-records .list .item .detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.records-panel .followUpRecord-records .list .item .detail .record-audio-wrap {
  display: flex;
  align-items: center;
}
.records-panel .followUpRecord-records .list .item .detail .record-audio-wrap .record-audio-label {
  color: #999;
}
.records-panel .followUpRecord-records .list .item .detail .record-audio-wrap .record-audio-play {
  color: #09f;
  cursor: pointer;
  margin-left: 10px;
}
.records-panel .followUpRecord-records .list .item .detail .record-audio-wrap .record-audio-play:hover {
  color: #f60;
}
.records-panel .followUpRecord-records .list .item .detail > a {
  color: #09f;
  cursor: pointer;
  transition: color 0.15s;
}
.records-panel .followUpRecord-records .list .item .detail > a > i {
  font-size: 16px;
  position: relative;
  top: 3px;
  line-height: 1;
}
.records-panel .followUpRecord-records .list .item .detail > a:hover {
  color: #f60;
}
.records-panel .renwu-list .list {
  padding: 0 20px;
}
.records-panel .renwu-list .list .item {
  padding: 20px 0;
  border-bottom: solid 1px #e1e5e8;
}
.records-panel .renwu-list .list .item:last-child {
  border-bottom: none;
}
.records-panel .renwu-list .list .item .row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.records-panel .renwu-list .list .item .row .label {
  color: #999;
  flex-shrink: 0;
}
.records-panel .renwu-list .list .item .row .content {
  word-break: break-all;
  flex: 1;
}
.records-panel .renwu-list .list .item .row .content.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.records-panel .renwu-list .list .item .row .content .user {
  display: flex;
}
.records-panel .renwu-list .list .item .row .content .user .icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  border-radius: 3px;
}
.records-panel .renwu-list .list .item .coment {
  color: #333;
}
.records-panel .renwu-list .vd-ui-table-wrap .vd-ui-table-body.vd-ui-wrap-scroll {
  overflow-y: visible;
}
.records-panel .operation-records .list {
  padding: 0 20px 0 40px;
}
.records-panel .operation-records .list .item {
  position: relative;
  padding: 20px 0;
  border-bottom: solid 1px #e1e5e8;
}
.records-panel .operation-records .list .item::before {
  content: "";
  display: block;
  width: 11px;
  height: 11px;
  border-radius: 50%;
  overflow: hidden;
  background: #09f;
  position: absolute;
  left: -21px;
  top: 24px;
  z-index: 2;
}
.records-panel .operation-records .list .item::after {
  content: "";
  display: block;
  border-left: dashed 1px #E1E5E8;
  position: absolute;
  left: -16px;
  top: 24px;
  bottom: -24px;
  z-index: 1;
}
.records-panel .operation-records .list .item:last-child {
  border-bottom: none;
}
.records-panel .operation-records .list .item:last-child::after {
  display: none;
}
.records-panel .operation-records .list .item .row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.records-panel .operation-records .list .item .row .creator {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}
.records-panel .operation-records .list .item .row .creator .icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  border-radius: 3px;
}
.records-panel .operation-records .list .item .row .creator .name {
  flex: 1;
  min-width: 0;
}
.records-panel .operation-records .list .item .row .time {
  width: 140px;
  flex-shrink: 0;
  white-space: nowrap;
  font-size: 12px;
  color: #999;
  text-align: right;
}
.records-panel .partner-records .thead {
  padding: 5px 20px;
  background: #f5f7fa;
  color: #999;
}
.records-panel .partner-records .list {
  padding: 0 20px;
}
.records-panel .partner-records .list .item {
  padding-bottom: 10px;
  border-bottom: solid 1px #E1E5E8;
  margin-top: 10px;
}
.records-panel .partner-records .list .item:first-child {
  margin-top: 0;
}
.records-panel .partner-records .list .item .partner-detail-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}
.records-panel .partner-records .list .item .partner-detail-top .partner-detail-option {
  color: #09f;
  cursor: pointer;
}
.records-panel .partner-records .list .item .partner-detail-top .partner-detail-option:hover {
  color: #f60;
}
.records-panel .partner-records .list .item .partner-detail-top .partner-detail-option.disabled {
  color: #999;
  cursor: not-allowed;
}
.records-panel .partner-records .list .item .partner-detail-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #999;
}
.td-link {
  color: #0099FF;
  cursor: pointer;
}
.td-link:hover {
  color: #f60;
}
.tyc-icon {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  word-break: break-all;
}
.tyc-icon .company {
  font-size: 14px;
  color: #333;
}
.tyc-icon .company.blue {
  color: #09f;
  cursor: pointer;
}
.tyc-icon .company.blue:hover {
  color: #f60;
}
.tyc-icon .icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url('../../image/tyc.png') no-repeat;
  background-size: 100%;
  margin-left: 5px;
  cursor: pointer;
  position: relative;
  top: 2px;
}
.remove-partner {
  color: #09f;
  cursor: pointer;
}
.remove-partner:hover {
  color: #f60;
}
.remove-partner.disabled {
  color: #999;
  cursor: not-allowed;
}
.add-record {
  display: flex;
  align-items: center;
}
.add-record .btn.disabled {
  cursor: not-allowed;
  color: #999;
  background-color: #f5f7fa;
  border-color: #d7dade;
}
.add-record .audio-link-btn {
  margin-left: 20px;
  color: #09f;
  cursor: pointer;
}
.add-record .audio-link-btn:hover {
  color: #f60;
}
.border-top {
  border-top: solid 1px #E1E5E8;
  padding-top: 20px !important;
  margin-bottom: 20px;
  margin-top: 20px;
}
.nextData-margin {
  margin-top: 5px;
  margin-left: 6px;
}
/* 任务 */
.renwu-dialog-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.change-btn {
  font-size: 12px;
  font-weight: 400;
  color: #09f;
  cursor: pointer;
}
.change-btn:hover {
  color: #f60;
}
.renwu-status {
  color: #999;
  line-height: 22px;
  padding: 0 5px;
  border-radius: 3px;
  display: inline-block;
}
.renwu-status.status0 {
  color: #F60;
  background: #FFEDE0;
}
.renwu-status.status1 {
  color: #13BF13;
  background: #E3F7E3;
}
.renwu-status.status2 {
  color: #1A4D80;
  background: #E3EAF0;
}
.renwu-deal-btn {
  text-align: right;
  margin-top: 10px;
}
.renwu-deal-btn .btn {
  color: #09f;
  cursor: pointer;
}
.renwu-deal-btn .btn:hover {
  color: #f60;
}
.more-people {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.more-people .user-show {
  margin-right: 20px;
}
.font-danger {
  color: #D9001B;
}
.handle-tips {
  background: #E0F3FF;
  padding: 10px 15px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
.handle-tips .icon {
  font-size: 16px;
  color: #09F;
  margin-right: 10px;
}
.handle-bordertop {
  border-top: solid 1px #E1E5E8;
  margin-top: 40px;
  padding-top: 21px;
}
.handle-wrap {
  max-height: 500px;
  overflow: auto;
  margin-right: -20px;
  padding-right: 20px;
}
.handle-wrap::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.handle-wrap::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.handle-wrap::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.handle-wrap::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.handle-wrap::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.link {
  color: #333;
}
.link.blue {
  color: #09f;
  cursor: pointer;
}
.link.blue:hover {
  color: #f60;
}
.ai-wrap {
  width: 100%;
  margin-top: 10px;
}
.ai-wrap .ai-title {
  color: #999;
  border-top: dashed 1px #E1E5E8;
  padding: 10px 0 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.ai-wrap .ai-title .ai-play-btn {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border: 1px solid #BABFC2;
  border-radius: 15px;
  height: 30px;
  padding: 0 15px 0 10px;
  color: #333;
  font-size: 12px;
  cursor: pointer;
}
.ai-wrap .ai-title .ai-play-btn:hover {
  background: #EBEFF2;
}
.ai-wrap .ai-title .ai-play-btn:active {
  background: #E1E5E8;
}
.ai-wrap .ai-title .ai-play-btn .ai-play-btn-icon {
  width: 18px;
  height: 18px;
  background-image: url(../../image/common/icon-play.svg);
  background-size: 100% 100%;
  margin-right: 3px;
}
.ai-wrap .ai-content {
  margin-top: 7px;
}
.ai-dialog-iframe {
  border: none;
  width: 820px;
  margin: 0 auto;
  height: 488px;
  display: block;
}
.ui-dlg-audio-wrap {
  position: relative;
  padding-bottom: 50px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search {
  margin-bottom: 15px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-list {
  display: flex;
  flex-wrap: wrap;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-list .ui-dlg-audio-search-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  width: calc(33.33% - 13.33px);
  margin-bottom: 10px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-list .ui-dlg-audio-search-item:nth-child(3n) {
  margin-right: 0;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-list .vd-ui-date-range-item {
  flex: 1;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-list .vd-ui-date-range-item .vd-ui-input.vd-ui-input--suffix .vd-ui-input__inner {
  padding-right: 24px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-list .ui-dlg-audio-search-label {
  width: 100px;
  color: #999;
  text-align: right;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-list .ui-dlg-audio-search-cnt {
  flex: 1;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-list .ui-dlg-audio-search-cnt > .vd-ui-input {
  width: 100%;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-list .ui-dlg-audio-search-cnt .vd-ui-select {
  width: 100%;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-list .ui-dlg-audio-search-cnt .vd-ui-date .vd-ui-date-editor {
  width: 100%;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-btns {
  margin-top: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-btns .vd-ui-button {
  margin-right: 10px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-search-btns .vd-ui-button:last-child {
  margin-right: 0;
}
.ui-dlg-audio-wrap .ui-dlg-audio-list {
  border-top: 1px solid #E1E5E8;
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .vd-ui-table-wrap {
  margin-top: -1px;
  margin-bottom: -1px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .vd-ui-table-wrap .vd-ui-table .vd-ui-td {
  vertical-align: top;
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .ui-audio-time-type .ui-audio-type {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .ui-audio-time-type .ui-audio-type .ui-audio-type-icon {
  width: 16px;
  height: 16px;
  background-size: 100%;
  margin-right: 5px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .ui-audio-time-type .ui-audio-type .ui-audio-type-icon.in {
  background-image: url(/static/image/common/call-in.svg);
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .ui-audio-time-type .ui-audio-type .ui-audio-type-icon.out {
  background-image: url(/static/image/common/call-out.svg);
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .ui-audio-time-type .ui-audio-type .ui-audio-type-txt {
  margin-right: 11px;
  position: relative;
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .ui-audio-time-type .ui-audio-type .ui-audio-type-txt::before {
  content: '';
  width: 1px;
  height: 12px;
  background: #e1e5e8;
  position: absolute;
  top: 3px;
  right: -6px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .ui-audio-time-type .ui-audio-type .ui-audio-type-txt:last-child::before {
  display: none;
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .ui-audio-nickname {
  color: #999;
  padding-left: 23px;
  margin-top: 3px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .ui-audio-customer-name {
  padding-left: 23px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-list .ui-audio-belonger-name {
  color: #999;
  margin-top: 3px;
}
.ui-dlg-audio-wrap .ui-dlg-audio-footer {
  padding-top: 20px;
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.ui-dlg-audio-wrap .ui-dlg-audio-footer .ui-dlg-audio-footer-btns {
  display: flex;
  align-items: center;
}
.ui-dlg-audio-wrap .ui-dlg-audio-footer .ui-dlg-audio-footer-btns .vd-ui-button {
  margin-left: 10px;
}
.vd-ui-select-audio-dialog .vd-ui-dialog.vd-ui-dialog--in {
  max-height: calc(100% - 40px);
  margin-bottom: 0;
}
.vd-ui-select-audio-dialog .ui-dlg-audio-list {
  height: calc(100vh - 306px);
}
.vd-ui-select-audio-dialog .vd-ui-table-header {
  background: #f5f7fa;
  position: relative;
  margin-bottom: -1px;
  z-index: 1;
}
