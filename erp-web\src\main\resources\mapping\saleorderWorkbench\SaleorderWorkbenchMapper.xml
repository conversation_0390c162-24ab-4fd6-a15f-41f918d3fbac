<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.saleorder.dao.SaleorderWorkbenchMapper" >


    <select id="getNoVerifyOrderInfo" resultType="java.lang.Integer" parameterType="com.vedeng.erp.saleorder.model.query.WorkbenchDto">
        SELECT
            count(*)
        FROM
            T_SALEORDER s
        LEFT JOIN T_R_TRADER_J_USER tu ON tu.TRADER_ID = s.TRADER_ID AND tu.TRADER_TYPE = 1
        LEFT JOIN T_VERIFIES_INFO v ON s.SALEORDER_ID = v.RELATE_TABLE_KEY AND v.RELATE_TABLE = 'T_SALEORDER'  AND v.VERIFIES_TYPE = 623
        WHERE
        s.COMPANY_ID = 1 AND s.STATUS != 3  AND s.ORDER_TYPE not in (2,3,4,6)
        AND tu.USER_ID IN
        <foreach collection="userIds" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
        <if test="onLineOrderTypeFlg != null and onLineOrderTypeFlg == 1">
             AND  s.ORDER_TYPE in (1,5,7)
        </if>
        <if test="onLineOrderTypeFlg != null and onLineOrderTypeFlg == 0">
            AND  s.ORDER_TYPE in (0,8,9)
        </if>
        <if test="forwardThirtyDate != null">
            AND  s.ADD_TIME > #{forwardThirtyDate,jdbcType=BIGINT}
        </if>
             AND v.`STATUS` is NULL
    </select>

    <select id="getNoPaymentOrderInfo" resultType="java.lang.Integer"  parameterType="com.vedeng.erp.saleorder.model.query.WorkbenchDto">
        SELECT
            count(*)
        FROM
            T_SALEORDER s
        LEFT JOIN T_R_TRADER_J_USER tu ON tu.TRADER_ID = s.TRADER_ID AND tu.TRADER_TYPE = 1
        WHERE
        s.COMPANY_ID = 1 AND s.STATUS != 3 AND s.VALID_STATUS = 1 AND s.PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER}  AND s.ORDER_TYPE not in (2,3,4,6)
        <if test="forwardThirtyDate != null">
            AND  s.ADD_TIME > #{forwardThirtyDate,jdbcType=BIGINT}
        </if>
        AND tu.USER_ID IN
        <foreach collection="userIds" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
    </select>

    <select id="getNoPaymentMoneyInfo" resultType="java.math.BigDecimal"  parameterType="com.vedeng.erp.saleorder.model.query.WorkbenchDto">
        SELECT
        IFNULL(SUM(s.REAL_TOTAL_AMOUNT - s.REAL_PAY_AMOUNT),0) NO_PAY_AMOUNT
        FROM
            T_SALEORDER s
            LEFT JOIN T_R_TRADER_J_USER tu ON tu.TRADER_ID = s.TRADER_ID AND tu.TRADER_TYPE = 1
        WHERE
        s.COMPANY_ID = 1 AND s.STATUS != 3 AND s.VALID_STATUS = 1 AND s.PAYMENT_STATUS IN (0,1)  AND s.ORDER_TYPE not in (2,3,4,6)
        <if test="forwardThirtyDate != null">
            AND  s.ADD_TIME > #{forwardThirtyDate,jdbcType=BIGINT}
        </if>
        AND tu.USER_ID IN
            <foreach collection="userIds" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
    </select>

    <select id="getNoInvoiceOrderInfo" resultType="java.util.Map"  parameterType="com.vedeng.erp.saleorder.model.query.WorkbenchDto">
        SELECT
            count(*) noInvoiceTotal,IFNULL(SUM(s.REAL_TOTAL_AMOUNT),0) noInvoiceMoney
        FROM
            T_SALEORDER s
            LEFT JOIN T_R_TRADER_J_USER tu ON tu.TRADER_ID = s.TRADER_ID AND tu.TRADER_TYPE = 1
        WHERE
        s.COMPANY_ID = 1 AND  s.STATUS not in ( 3,2) and s.ARRIVAL_STATUS =  2 and s.INVOICE_STATUS = 0  AND s.ORDER_TYPE not in (2,3,4,6)
        <if test="forwardThirtyDate != null">
            AND  s.ADD_TIME > #{forwardThirtyDate,jdbcType=BIGINT}
        </if>
        AND tu.USER_ID IN
        <foreach collection="userIds" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
    </select>


    <select id="getVerifyingOrderInfo" resultType="java.lang.Integer"  parameterType="com.vedeng.erp.saleorder.model.query.WorkbenchDto">
        SELECT
        count(*)
        FROM
        T_SALEORDER s
        LEFT JOIN T_R_TRADER_J_USER tu ON tu.TRADER_ID = s.TRADER_ID AND tu.TRADER_TYPE = 1
        LEFT JOIN T_VERIFIES_INFO v ON s.SALEORDER_ID = v.RELATE_TABLE_KEY AND v.RELATE_TABLE = 'T_SALEORDER'  AND v.VERIFIES_TYPE = 623
        WHERE
        s.COMPANY_ID = 1 AND s.STATUS != 3  AND s.ORDER_TYPE not in (2,3,4,6)
        AND tu.USER_ID IN
        <foreach collection="userIds" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
        <if test="onLineOrderTypeFlg != null and onLineOrderTypeFlg == 1">
            AND  s.ORDER_TYPE in (1,5,7)
        </if>
        <if test="onLineOrderTypeFlg != null and onLineOrderTypeFlg == 0">
            AND  s.ORDER_TYPE in (0,8,9)
        </if>
        <if test="forwardThirtyDate != null">
            AND  s.ADD_TIME > #{forwardThirtyDate,jdbcType=BIGINT}
        </if>
        AND v.`STATUS` = 0
    </select>

    <select id="getNoPurchaseOrderInfo" resultType="java.lang.Integer"  parameterType="com.vedeng.erp.saleorder.model.query.WorkbenchDto">
        SELECT
        COUNT(*)
        FROM
        T_SALEORDER s
        LEFT JOIN T_R_TRADER_J_USER tu ON tu.TRADER_ID = s.TRADER_ID AND tu.TRADER_TYPE = 1
        WHERE
        s.COMPANY_ID = 1 AND s.VALID_STATUS = 1 AND s.PAYMENT_STATUS = 2 AND s.STATUS NOT IN (2,3) AND PURCHASE_STATUS = #{purchaseStatus,jdbcType=INTEGER} AND s.ORDER_TYPE not in (2,3,4,6)
        <if test="forwardThirtyDate != null">
            AND  s.ADD_TIME > #{forwardThirtyDate,jdbcType=BIGINT}
        </if>
        AND tu.USER_ID IN
        <foreach collection="userIds" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
    </select>

    <select id="getNoSendGoodsOrderInfo" resultType="java.lang.Integer"  parameterType="com.vedeng.erp.saleorder.model.query.WorkbenchDto">
        SELECT
        COUNT(*)
        FROM
            T_SALEORDER s
            LEFT JOIN T_R_TRADER_J_USER tu ON tu.TRADER_ID = s.TRADER_ID AND tu.TRADER_TYPE = 1
        WHERE
        s.COMPANY_ID = 1 AND  s.VALID_STATUS = 1 AND s.PAYMENT_STATUS = 2 AND s.STATUS NOT IN (2,3) AND DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER} AND s.ORDER_TYPE not in (2,3,4,6)
        <if test="forwardThirtyDate != null">
            AND  s.ADD_TIME > #{forwardThirtyDate,jdbcType=BIGINT}
        </if>
        AND tu.USER_ID IN
        <foreach collection="userIds" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
    </select>

    <select id="getNoCompletedAfterSaleInfo" resultType="java.lang.Integer" parameterType="com.vedeng.erp.saleorder.model.query.WorkbenchDto">
        SELECT
        count(*)
        FROM
            T_AFTER_SALES a
        LEFT JOIN T_SALEORDER tsa ON a.`ORDER_ID` = tsa.`SALEORDER_ID`
        LEFT JOIN T_R_TRADER_J_USER trj ON tsa.TRADER_ID = trj.TRADER_ID  AND trj.TRADER_TYPE = 1
        WHERE
            a.TYPE NOT IN ( 546, 547, 548, 549 )
            AND a.COMPANY_ID = 1 and a.ATFER_SALES_STATUS != 3 AND tsa.ORDER_TYPE not in (2,3,4,6)
            AND trj.USER_ID IN
        <foreach collection="userIds" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
        <if test="forwardThirtyDate != null">
            AND  tsa.ADD_TIME > #{forwardThirtyDate,jdbcType=BIGINT}
        </if>
        <if test="noCompletedAfterSaleFalg != null and noCompletedAfterSaleFalg == 0">
            AND  a.STATUS = 0
        </if>
        <if test="noCompletedAfterSaleFalg != null and noCompletedAfterSaleFalg == 1">
            AND  a.STATUS = 1
        </if>
        <if test="noCompletedAfterSaleFalg != null and noCompletedAfterSaleFalg == 2">
            AND  a.ATFER_SALES_STATUS = 1
        </if>
        <if test="noCompletedAfterSaleFalg != null and noCompletedAfterSaleFalg == 3">
            AND  a.STATUS = 3
        </if>
    </select>

    <select id="getUnReturnedInfo" resultType="com.vedeng.erp.saleorder.model.dto.UnCollectedOrderInfoDto">
        SELECT
        COUNT( res.SALEORDER_NO ) totalNum,
        SUM( res.UNRETURNED_AMOUNT ) amount,
        SUM( res.UNRETURNED_AMOUNT - res.OVERDUE_UNRETURNED_AMOUNT ) notOverdue,
        SUM( IF ( res.OVERDUE_DAYS > 0 AND 60 >= res.OVERDUE_DAYS, OVERDUE_UNRETURNED_AMOUNT, 0 ) ) betweenZeroAndSixtyAmount,
        SUM( IF ( res.OVERDUE_DAYS > 60, OVERDUE_UNRETURNED_AMOUNT, 0 ) ) moreThanSixtyAmount
        FROM
        (
        SELECT
        TEMP.SALEORDER_NO,
        SUM( TEMP.UNRETURNED_AMOUNT ) UNRETURNED_AMOUNT,
        SUM( TEMP.OVERDUE_UNRETURNED_AMOUNT ) OVERDUE_UNRETURNED_AMOUNT,
        SUM( TEMP.OVERDUE_DAYS ) OVERDUE_DAYS
        FROM
        (
        SELECT
        S.SALEORDER_NO,
        UD.UNRETURNED_AMOUNT,
        SUM( IFNULL( IF ( OMD.OVERDUE_DAYS > 0, OMD.UNRETURNED_AMOUNT, 0 ), 0 ) ) AS OVERDUE_UNRETURNED_AMOUNT,
        SUM( IFNULL( OMD.OVERDUE_DAYS, 0 ) ) OVERDUE_DAYS,
        SUM( OMD.OVERDUE_AMOUNT ) OVERDUE_AMOUNT,
        IF
        ( UD.OCCUPANCY = 0 AND UD.UNRETURNED_AMOUNT = 0, 1, 0 ) IS_CLOSED_ORDER
        FROM
        T_CUSTOMER_BILL_PERIOD_USE_DETAIL UD
        INNER JOIN T_SALEORDER S ON S.SALEORDER_ID = UD.RELATED_ID
        AND S.COMPANY_ID = 1
        LEFT JOIN T_CUSTOMER_BILL_PERIOD BP ON UD.BILL_PERIOD_ID = BP.BILL_PERIOD_ID
        AND BP.COMPANY_ID = 1
        LEFT JOIN T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL OMD ON UD.BILL_PERIOD_USE_DETAIL_ID = OMD.BILL_PERIOD_USE_DETAIL_ID
        AND OMD.COMPANY_ID = 1
        LEFT JOIN T_SALEORDER_DATA SD ON SD.SALEORDER_ID = S.SALEORDER_ID
        WHERE
        UD.USE_TYPE = 1
        AND UD.COMPANY_ID = 1
        AND SD.CURRENT_USER_ID IN
        <foreach collection = "userIdList" item = "item" open = "(" close = ")" separator = "," >
            #{item}
        </foreach>
        GROUP BY
        BP.BILL_PERIOD_TYPE,
        UD.RELATED_ID
        HAVING
        IS_CLOSED_ORDER = 0
        ) TEMP
        GROUP BY
        TEMP.SALEORDER_NO
        HAVING
        UNRETURNED_AMOUNT > 0
        ) res
    </select>
    <select id="getNoNoSendOrderInfo" resultType="java.lang.Integer" parameterType="com.vedeng.erp.saleorder.model.query.WorkbenchDto">
        SELECT COUNT(1)
        FROM (SELECT
           s.SALEORDER_ID
        FROM
            T_SALEORDER s
            INNER JOIN T_SALEORDER_GOODS sg ON s.SALEORDER_ID = sg.SALEORDER_ID
            AND sg.IS_DELETE = 0
            INNER JOIN (
                SELECT
                max( CAST( DELIVERY_CYCLE AS SIGNED ) ) * 24 * 60 * 60 * 1000 AS maxDeliveryTime,
                X.SALEORDER_ID
                FROM
                T_SALEORDER_GOODS X LEFT JOIN T_SALEORDER Y ON X.SALEORDER_ID=Y.SALEORDER_ID

                WHERE
                DELIVERY_CYCLE REGEXP '^[0-9]+' = 1  AND   Y.VALID_STATUS = 1
                AND Y.PAYMENT_STATUS = 2
                AND Y.STATUS NOT IN ( 2, 3 )
                AND Y.ARRIVAL_STATUS != 2
                GROUP BY
                X.SALEORDER_ID
            ) sg1 ON sg1.SALEORDER_ID = sg.SALEORDER_ID
            LEFT JOIN T_R_TRADER_J_USER tu ON tu.TRADER_ID = s.TRADER_ID
            AND tu.TRADER_TYPE = 1
            LEFT JOIN T_TRADER t ON t.TRADER_ID = s.TRADER_ID
            LEFT JOIN T_USER u ON u.USER_ID = tu.USER_ID
            LEFT JOIN T_SALEORDER_DATA sd ON s.SALEORDER_ID = sd.SALEORDER_ID
            LEFT JOIN V_CORE_SKU sku ON sku.SKU_NO = sg.SKU
            LEFT JOIN T_ORGANIZATION org ON org.ORG_ID = s.ORG_ID
            LEFT JOIN T_EXPRESS_DETAIL ed ON ed.RELATED_ID = sg.SALEORDER_GOODS_ID
            AND ed.BUSINESS_TYPE = 496
            LEFT JOIN T_EXPRESS ee ON ee.EXPRESS_ID = ed.EXPRESS_ID
            AND ee.ARRIVAL_STATUS IN (  2 )
        WHERE
            s.COMPANY_ID = 1
            AND VALID_STATUS = 1
            AND PAYMENT_STATUS = 2
            AND s.STATUS NOT IN ( 2, 3 )
            AND s.ARRIVAL_STATUS != 2
            AND unix_timestamp( now( ) ) * 1000 > s.PAYMENT_TIME + maxDeliveryTime
            AND s.ORDER_TYPE IN ( 0, 1, 5, 7, 8, 9 )
            AND u.USER_ID IN
            <foreach collection="userIds" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
            <if test="forwardThirtyDate != null">
                AND  s.ADD_TIME > #{forwardThirtyDate,jdbcType=BIGINT}
            </if>
        GROUP BY
            s.SALEORDER_ID) D
    </select>
    <select id="getUnHandleNum" resultType="java.lang.Integer">
        SELECT
        count(0)
        FROM
        (SELECT
        1
        FROM
        T_BUSSINESS_CHANCE C
        LEFT JOIN
        T_CUSTOM_DATA_OPER O
        ON O.RELATED_ID = C.BUSSINESS_CHANCE_ID
        AND O.BIZ_TYPE = 2
        AND O.BELONGER_ID = #{currentUserId,jdbcType=INTEGER}
        LEFT JOIN
        T_USER U
        ON U.USER_ID = C.USER_ID
        LEFT JOIN
        T_USER U2
        ON U2.USER_ID = C.CREATOR
        LEFT JOIN
        T_COMMUNICATE_RECORD TCR
        ON TCR.RELATED_ID = C.BUSSINESS_CHANCE_ID
        AND TCR.COMMUNICATE_TYPE = 244
        WHERE
        C.COMPANY_ID = 1
        AND C.MERGE_STATUS != 1
        AND C.USER_ID IN
        <foreach item="userId" index="index" collection="userIdList" separator="," open="(" close=")">
            #{userId,jdbcType=INTEGER}
        </foreach>
        AND C.STATUS != 5
        AND TCR.COMMUNICATE_RECORD_ID IS NULL
        AND C.ADD_TIME BETWEEN #{startTime,jdbcType=BIGINT}
        AND #{endTime,jdbcType=BIGINT}
        GROUP BY
        C.BUSSINESS_CHANCE_ID) table_count
    </select>
    <select id="getWaitCommunicateNum" resultType="java.lang.Integer">
        SELECT
        count(0)
        FROM
        (SELECT
        1
        FROM
        T_BUSSINESS_CHANCE C
        LEFT JOIN
        T_CUSTOM_DATA_OPER O
        ON O.RELATED_ID = C.BUSSINESS_CHANCE_ID
        AND O.BIZ_TYPE = 2
        AND O.BELONGER_ID = #{currentUserId,jdbcType=INTEGER}
        LEFT JOIN
        T_USER U
        ON U.USER_ID = C.USER_ID
        LEFT JOIN
        T_USER U2
        ON U2.USER_ID = C.CREATOR
        LEFT JOIN
        T_COMMUNICATE_RECORD TCR
        ON TCR.RELATED_ID = C.BUSSINESS_CHANCE_ID
        AND TCR.COMMUNICATE_TYPE = 244
        WHERE
        C.COMPANY_ID = 1
        AND C.MERGE_STATUS != 1
        AND C.USER_ID IN
        <foreach item="userId" index="index" collection="userIdList" separator="," open="(" close=")">
            #{userId,jdbcType=INTEGER}
        </foreach>
        AND C.STATUS != 5
        AND
            (SELECT NEXT_CONTACT_DATE FROM T_COMMUNICATE_RECORD WHERE RELATED_ID = C.BUSSINESS_CHANCE_ID ORDER BY
            COMMUNICATE_RECORD_ID DESC LIMIT 1)
            IS NOT NULL
        AND C.ADD_TIME BETWEEN #{startTime,jdbcType=BIGINT}
        AND #{endTime,jdbcType=BIGINT}
        GROUP BY
        C.BUSSINESS_CHANCE_ID) table_count
    </select>
    <select id="getNoInvoiceOrderNum" resultType="java.lang.Integer">
        SELECT
            COUNT(0)
        from
            T_SALEORDER ts
        LEFT JOIN T_SALEORDER_DATA tsd ON ts.SALEORDER_ID = tsd.SALEORDER_ID
        WHERE
            ts.ADD_TIME > 1675180800000
            AND ts.STATUS !=3
            AND ts.PAYMENT_STATUS = 2
            AND ts.INVOICE_STATUS != 2
            AND tsd.CURRENT_USER_ID IN
        <foreach item="userId" index="index" collection="subUserIdList" separator="," open="(" close=")">
            #{userId,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="getContractNotReviewedOrderNum" resultType="java.lang.Integer">
        SELECT
            COUNT(0)
        from
            T_SALEORDER ts
        LEFT JOIN T_SALEORDER_DATA tsd ON
            ts.SALEORDER_ID = tsd.SALEORDER_ID
        WHERE
            ts.ADD_TIME > 1675180800000
            AND ts.STATUS != 3
            AND ts.PAYMENT_STATUS = 2
            AND tsd.CONTRACT_VERIFY_STATUS != 1
            and ((ts.ADD_TIME &lt; 1685548800000 and ts.TOTAL_AMOUNT >= 5000)
                or (ts.ADD_TIME >= 1685548800000 and ts.ORDER_TYPE in (1,5,7) and ts.TOTAL_AMOUNT >= 50000)
                or (ts.ADD_TIME >= 1685548800000 and ts.ORDER_TYPE not in (1,5,7) and ts.TOTAL_AMOUNT >= 5000))
            AND tsd.CURRENT_USER_ID IN
            <foreach item="userId" index="index" collection="subUserIdList" separator="," open="(" close=")">
                #{userId,jdbcType=INTEGER}
            </foreach>
    </select>

    <select id="getConfirmationNotApprovedOrderNum" resultType="java.lang.Integer">
        SELECT
            COUNT(0)
        from
            T_SALEORDER ts
        LEFT JOIN T_SALEORDER_DATA tsd ON ts.SALEORDER_ID = tsd.SALEORDER_ID
        WHERE
            ts.ADD_TIME > 1675180800000
            AND ts.STATUS != 3
            AND ts.PAYMENT_STATUS = 2
            AND ts.TOTAL_AMOUNT >= 5000
            AND ts.CONFIRMATION_FORM_AUDIT != 2
            AND tsd.CURRENT_USER_ID IN
            <foreach item="userId" index="index" collection="subUserIdList" separator="," open="(" close=")">
                #{userId,jdbcType=INTEGER}
            </foreach>
    </select>

    <!-- 获取到款金额-退款金额 -->
    <select id="getRealReceiveAmount" resultType="java.math.BigDecimal">
        select sum(amount) realReceiveAmount from (
        SELECT sum(TCBD.AMOUNT) amount
        FROM T_CAPITAL_BILL TCB
        LEFT JOIN T_CAPITAL_BILL_DETAIL TCBD ON TCB.CAPITAL_BILL_ID = TCBD.CAPITAL_BILL_ID
        AND TCBD.ORDER_TYPE = 1
        AND TCBD.BUSSINESS_TYPE IN (526,533)
        AND TCBD.TRADER_TYPE IN (1,4)
        WHERE 1=1
        AND TCB.TRADER_MODE IN (520, 521, 522, 523, 528)
        AND TCB.PAYER NOT IN ('支付宝（中国）网络技术有限公司','财付通支付科技有限公司')
        and TCB.TRADER_TIME >= #{startTime}
        and TCB.TRADER_TIME &lt;= #{endTime}
        and TCBD.USER_ID IN
        <foreach collection="userIdList" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
        UNION all
        SELECT sum(TCBD.AMOUNT) amount
        FROM T_CAPITAL_BILL TCB
        LEFT JOIN T_CAPITAL_BILL_DETAIL TCBD ON TCB.CAPITAL_BILL_ID = TCBD.CAPITAL_BILL_ID
        LEFT JOIN T_AFTER_SALES TAS ON TCBD.ORDER_NO = TAS.AFTER_SALES_NO AND TAS.TYPE IN (539,543) AND TCBD.ORDER_TYPE =3
        WHERE 1=1
        AND TCBD.BUSSINESS_TYPE = 531
        AND TCB.TRADER_MODE =530
        AND TCB.TRADER_TYPE IN (2,5)
        and TCB.TRADER_TIME >= #{startTime}
        and TCB.TRADER_TIME &lt;= #{endTime}
        and TCBD.USER_ID IN
        <foreach collection="userIdList" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
        ) a
    </select>

    <!-- 获取到款目标 -->
    <select id="getPaymentTarget" resultType="java.math.BigDecimal">
        select SUM(TARGET_AMOUNT) TARGET_AMOUNT from T_BROADCAST_TARGET
        WHERE TARGET_TYPE=1
          AND TARGET_YEAR=#{year}
        <if test="month != null and month != 0">
          AND TARGET_MONTH=#{month}
        </if>
          AND IS_DELETED=0
        AND TARGET_BUZ_ID IN
        <foreach collection="userIdList" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
    </select>

    <!-- 获取自有品牌销售占比 -->
    <select id="getOwnBrandSalesRatio" resultType="java.util.Map">
        select T.SALEORDER_NO,D.IS_OWN_BRAND
        , SUM((IFNULL(C.NUM, 0)-IFNULL(C.AFTER_RETURN_NUM, 0))*IFNULL(C.PRICE, 0)) REAL_AMOUNT
        from T_SALEORDER T LEFT JOIN T_R_TRADER_J_USER B ON T.TRADER_ID=B.TRADER_ID AND B.TRADER_TYPE=1
        LEFT JOIN T_SALEORDER_GOODS C ON C.SALEORDER_ID=T.SALEORDER_ID AND C.IS_DELETE=0
        left  join V_CORE_SKU SKU ON SKU.SKU_NO=C.SKU
        LEFT JOIN V_CORE_SPU SPU ON SPU.SPU_ID=SKU.SPU_ID
        LEFT JOIN T_BRAND D ON SPU.BRAND_ID=D.BRAND_ID
        WHERE T.SATISFY_DELIVERY_TIME >= #{startTime}
        AND T.SATISFY_DELIVERY_TIME &lt;= #{endTime}
        AND B.USER_ID IN
        <foreach collection="userIdList" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
        AND C.IS_DELETE=0
        group by ifnull(D.IS_OWN_BRAND,0)
    </select>

    <!-- 获取预计成单商机金额 -->
    <select id="getExpectedOrderOpportunityAmount" resultType="java.math.BigDecimal">
        SELECT SUM(AMOUNT) TOTAL_AMOUNT
        FROM T_BUSSINESS_CHANCE C
        WHERE C.COMPANY_ID = 1 AND C.MERGE_STATUS != 1
        and C.STAGE in (1,2,3,4)
        and C.ORDER_TIME >= #{startTime}
        and C.ORDER_TIME &lt;= #{endTime}
        and C.USER_ID in
        <foreach collection="userIdList" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
    </select>

    <!-- 获取商机满足率相关数据 -->
    <select id="getOpportunitySatisfactionData" resultType="java.math.BigDecimal">
        select sum(amount) realReceiveAmount from (
        SELECT sum(TCBD.AMOUNT) amount
        FROM T_CAPITAL_BILL TCB
        LEFT JOIN T_CAPITAL_BILL_DETAIL TCBD ON TCB.CAPITAL_BILL_ID = TCBD.CAPITAL_BILL_ID
        AND TCBD.ORDER_TYPE = 1  AND TCBD.BUSSINESS_TYPE IN (526,533)
        AND TCBD.TRADER_TYPE IN (1,4)
        left join T_SALEORDER SO ON SO.SALEORDER_ID=TCBD.RELATED_ID
        left join T_QUOTEORDER QO ON QO.QUOTEORDER_ID=SO.QUOTEORDER_ID
        WHERE 1=1
        AND TCB.TRADER_MODE IN (520, 521, 522, 523, 528)
        AND TCB.PAYER NOT IN ('支付宝（中国）网络技术有限公司','财付通支付科技有限公司')
        and TCB.TRADER_TIME >= #{startTime}
        and TCB.TRADER_TIME &lt;= #{endTime}
        AND QO.BUSSINESS_CHANCE_ID IS NOT NULL
        and TCBD.USER_ID IN
        <foreach collection="userIdList" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
        UNION all
        SELECT sum(TCBD.AMOUNT) amount
        FROM T_CAPITAL_BILL TCB
        LEFT JOIN T_CAPITAL_BILL_DETAIL TCBD ON TCB.CAPITAL_BILL_ID = TCBD.CAPITAL_BILL_ID

        LEFT JOIN T_AFTER_SALES TAS ON TCBD.ORDER_NO = TAS.AFTER_SALES_NO AND TAS.TYPE IN (539,543) AND TCBD.ORDER_TYPE =3
        left join T_SALEORDER SO ON SO.SALEORDER_ID=TCBD.RELATED_ID
        left join T_QUOTEORDER QO ON QO.QUOTEORDER_ID=SO.QUOTEORDER_ID
        WHERE 1=1
        AND TCBD.BUSSINESS_TYPE = 531
        AND TCB.TRADER_MODE =530
        AND TCB.TRADER_TYPE IN (2,5)
        and TCB.TRADER_TIME >= #{startTime}
        and TCB.TRADER_TIME &lt;= #{endTime}
        and TCBD.USER_ID IN
        <foreach collection="userIdList" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
        AND QO.BUSSINESS_CHANCE_ID IS NOT NULL

        ) a WHERE 1=1
    </select>

    <!-- 获取线索数 -->
    <select id="getClueNum" resultType="java.lang.Integer">
        select count(1)
        from T_BUSINESS_LEADS TBL
        where 1=1 and   TBL.MERGE_STATUS != 1   AND TBL.ADD_TIME >= #{startDate}
        AND TBL.ADD_TIME &lt;= #{endDate}
        <if test="userIdList!=null and userIdList.size()>0">
            and TBL.BELONGER_ID in
            <foreach collection="userIdList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 获取无关联线索商机数 -->
    <select id="getUnrelatedClueOpportunityNum" resultType="java.lang.Integer">
        SELECT count(1) FROM T_BUSSINESS_CHANCE a left join T_BUSINESS_LEADS b on a.BUSSINESS_CHANCE_ID=b.BUSINESS_CHANCE_ID
        where b.BUSINESS_CHANCE_ID is null
        and a.ADD_TIME >= #{startTime}
        and a.ADD_TIME &lt;= #{endTime}
        and a.COMPANY_ID = 1 AND a.MERGE_STATUS != 1
        <if test="userIdList!=null and userIdList.size()>0">
            and a.USER_ID in
            <foreach collection="userIdList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 获取商机数 -->
    <select id="getOpportunityNum" resultType="java.lang.Integer">
        SELECT count(1) FROM T_BUSSINESS_CHANCE a
        where a.ADD_TIME >= #{startTime}
        and a.ADD_TIME &lt;= #{endTime}
        and a.COMPANY_ID = 1 AND a.MERGE_STATUS != 1
        <if test="userIdList!=null and userIdList.size()>0">
            and a.USER_ID in
            <foreach collection="userIdList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 获取各阶段商机数量 -->
    <select id="getOpportunityStageCount" resultType="com.vedeng.erp.saleorder.model.dto.ext.BusinessChanceStageCountDto">
        SELECT count(1) as countNum, IFNULL(STAGE,0) AS stage FROM T_BUSSINESS_CHANCE a
        where a.ADD_TIME >= #{startTime}
        and a.ADD_TIME &lt;= #{endTime}
        and a.COMPANY_ID = 1 AND a.MERGE_STATUS != 1
        <if test="userIdList!=null and userIdList.size()>0">
            and a.USER_ID in
            <foreach collection="userIdList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
        and STAGE in(1,2,3,4,5)
        GROUP BY STAGE
    </select>

</mapper>