package com.vedeng.erp.trader.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecordDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecordParams;
import com.vedeng.erp.trader.dto.FollowBindingTelParams;

import java.util.List;

/**
 * 沟通记录(CommunicateRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-12 15:44:48
 */
public interface CommunicateRecordService {


    /**
     * 分页查询
     *
     * @param recordDtoPageParam 分页参数
     * @return 分页对象
     */
    PageInfo<CommunicateRecordDto> page(PageParam<CommunicateRecordDto> recordDtoPageParam);


    /**
     * 新增数据
     * 需要将沟通记录的数据分装好 传入 内部只做 记录存表 和新增用户自定义标签 以及标签关联信息操作
     * @param communicateRecordDto 实例对象
     */
    void add(CommunicateRecordDto communicateRecordDto);

    /**
     * 根据沟通记录id 查询一个记录
     * @param communicateRecordDto 对象
     * @return 记录
     */
    CommunicateRecordDto getOne(CommunicateRecordDto communicateRecordDto);

    /**
     * 更新沟通记录
     * @param communicateRecordDto 实例对象
     */
    void update(CommunicateRecordDto communicateRecordDto);

    /**
     * 查询 是否存在至少一条沟通记录
     * @param communicateRecordDto  type relatedId
     * @return id
     */
    Integer selectByRelatedIdAndCommunicateType(CommunicateRecordDto communicateRecordDto);


    List<CommunicateRecordDto> findByBegintimeBetween(Long minBegintime, Long maxBegintime);


    /**
     * 灵犀调用通话记录
     * @param communicateTelRecordParams
     * @return List<CommunicateTelRecordApiDto>
     */
    CommunicateTelRecordDto getTelList(PageParam<CommunicateTelRecordParams> communicateTelRecordParams);


	/**
	 * 跟进记录绑定通话
	 * @param followBindingTelParams
	 * @return
	 */
	List<Integer> followBindingTel(FollowBindingTelParams followBindingTelParams);
}
