<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.broadcast.mapdao.BroadcastDeptConfigMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptConfigEntity">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="BROADCAST_DEPT_ID" jdbcType="INTEGER" property="broadcastDeptId"/>
        <result column="DAY_FLAG" jdbcType="TINYINT" property="dayFlag"/>
        <result column="WEEK_FLAG" jdbcType="TINYINT" property="weekFlag"/>
        <result column="MONTH_FLAG" jdbcType="TINYINT" property="monthFlag"/>
        <result column="AED_FLAG" jdbcType="TINYINT" property="aedFlag"/>
        <result column="ZY_FLAG" jdbcType="TINYINT" property="zyFlag"/>
        <result column="CUSTOM_FLAG" jdbcType="TINYINT" property="customFlag"/>
        <result column="AMOUNT_STEP" jdbcType="INTEGER" property="amountStep"/>
        <result column="WEBHOOK" jdbcType="VARCHAR" property="webhook"/>
        <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, BROADCAST_DEPT_ID, DAY_FLAG, WEEK_FLAG, MONTH_FLAG, AED_FLAG, ZY_FLAG, CUSTOM_FLAG,
        AMOUNT_STEP, WEBHOOK, IS_DELETED, ADD_TIME, MOD_TIME, CREATOR, UPDATER
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_DEPT_CONFIG
        where ID = #{id,jdbcType=INTEGER}
        and IS_DELETED = 0
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update T_BROADCAST_DEPT_CONFIG
        set IS_DELETED = 1, MOD_TIME = now()
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptConfigEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_DEPT_CONFIG (BROADCAST_DEPT_ID, DAY_FLAG, WEEK_FLAG,
        MONTH_FLAG, AED_FLAG, ZY_FLAG,
        CUSTOM_FLAG, AMOUNT_STEP, WEBHOOK,
        IS_DELETED, ADD_TIME, MOD_TIME,
        CREATOR, UPDATER)
        values (#{broadcastDeptId,jdbcType=INTEGER}, #{dayFlag,jdbcType=TINYINT}, #{weekFlag,jdbcType=TINYINT},
        #{monthFlag,jdbcType=TINYINT}, #{aedFlag,jdbcType=TINYINT}, #{zyFlag,jdbcType=TINYINT},
        #{customFlag,jdbcType=TINYINT}, #{amountStep,jdbcType=INTEGER}, #{webhook,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptConfigEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_DEPT_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="broadcastDeptId != null">
                BROADCAST_DEPT_ID,
            </if>
            <if test="dayFlag != null">
                DAY_FLAG,
            </if>
            <if test="weekFlag != null">
                WEEK_FLAG,
            </if>
            <if test="monthFlag != null">
                MONTH_FLAG,
            </if>
            <if test="aedFlag != null">
                AED_FLAG,
            </if>
            <if test="zyFlag != null">
                ZY_FLAG,
            </if>
            <if test="customFlag != null">
                CUSTOM_FLAG,
            </if>
            <if test="amountStep != null">
                AMOUNT_STEP,
            </if>
            <if test="webhook != null">
                WEBHOOK,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="broadcastDeptId != null">
                #{broadcastDeptId,jdbcType=INTEGER},
            </if>
            <if test="dayFlag != null">
                #{dayFlag,jdbcType=TINYINT},
            </if>
            <if test="weekFlag != null">
                #{weekFlag,jdbcType=TINYINT},
            </if>
            <if test="monthFlag != null">
                #{monthFlag,jdbcType=TINYINT},
            </if>
            <if test="aedFlag != null">
                #{aedFlag,jdbcType=TINYINT},
            </if>
            <if test="zyFlag != null">
                #{zyFlag,jdbcType=TINYINT},
            </if>
            <if test="customFlag != null">
                #{customFlag,jdbcType=TINYINT},
            </if>
            <if test="amountStep != null">
                #{amountStep,jdbcType=INTEGER},
            </if>
            <if test="webhook != null">
                #{webhook,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptConfigEntity">
        update T_BROADCAST_DEPT_CONFIG
        <set>
            <if test="broadcastDeptId != null">
                BROADCAST_DEPT_ID = #{broadcastDeptId,jdbcType=INTEGER},
            </if>
            <if test="dayFlag != null">
                DAY_FLAG = #{dayFlag,jdbcType=TINYINT},
            </if>
            <if test="weekFlag != null">
                WEEK_FLAG = #{weekFlag,jdbcType=TINYINT},
            </if>
            <if test="monthFlag != null">
                MONTH_FLAG = #{monthFlag,jdbcType=TINYINT},
            </if>
            <if test="aedFlag != null">
                AED_FLAG = #{aedFlag,jdbcType=TINYINT},
            </if>
            <if test="zyFlag != null">
                ZY_FLAG = #{zyFlag,jdbcType=TINYINT},
            </if>
            <if test="customFlag != null">
                CUSTOM_FLAG = #{customFlag,jdbcType=TINYINT},
            </if>
            <if test="amountStep != null">
                AMOUNT_STEP = #{amountStep,jdbcType=INTEGER},
            </if>
            <if test="webhook != null">
                WEBHOOK = #{webhook,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptConfigEntity">
        update T_BROADCAST_DEPT_CONFIG
        set BROADCAST_DEPT_ID = #{broadcastDeptId,jdbcType=INTEGER},
        DAY_FLAG = #{dayFlag,jdbcType=TINYINT},
        WEEK_FLAG = #{weekFlag,jdbcType=TINYINT},
        MONTH_FLAG = #{monthFlag,jdbcType=TINYINT},
        AED_FLAG = #{aedFlag,jdbcType=TINYINT},
        ZY_FLAG = #{zyFlag,jdbcType=TINYINT},
        CUSTOM_FLAG = #{customFlag,jdbcType=TINYINT},
        AMOUNT_STEP = #{amountStep,jdbcType=INTEGER},
        WEBHOOK = #{webhook,jdbcType=VARCHAR},
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER}
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <resultMap id="DtoResultMap" type="com.vedeng.erp.broadcast.domain.dto.BroadcastDeptConfigDto">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="BROADCAST_DEPT_ID" jdbcType="INTEGER" property="broadcastDeptId"/>
        <result column="DEPT_NAME" jdbcType="INTEGER" property="broadcastDeptName"/>
        <result column="DAY_FLAG" jdbcType="TINYINT" property="dayFlag"/>
        <result column="WEEK_FLAG" jdbcType="TINYINT" property="weekFlag"/>
        <result column="MONTH_FLAG" jdbcType="TINYINT" property="monthFlag"/>
        <result column="AED_FLAG" jdbcType="TINYINT" property="aedFlag"/>
        <result column="ZY_FLAG" jdbcType="TINYINT" property="zyFlag"/>
        <result column="CUSTOM_FLAG" jdbcType="TINYINT" property="customFlag"/>
        <result column="AMOUNT_STEP" jdbcType="INTEGER" property="amountStep"/>
        <result column="WEBHOOK" jdbcType="VARCHAR" property="webhook"/>
    </resultMap>
    <select id="selectAll" resultMap="DtoResultMap">
        select
            a.*,case when a.BROADCAST_DEPT_ID=1 then '到款大群' else b.DEPT_NAME end DEPT_NAME
        from T_BROADCAST_DEPT_CONFIG a left join T_BROADCAST_DEPT b
                                                 on a.BROADCAST_DEPT_ID=b.id and b.IS_DELETED=0
        where   a.IS_DELETED = 0
    </select>

    <!-- 逻辑删除所有部门配置记录 -->
    <update id="deleteAllBroadcastDeptConfig" parameterType="java.lang.Integer">
        update T_BROADCAST_DEPT_CONFIG
        set IS_DELETED = 1, 
            MOD_TIME = now(),
            UPDATER = #{userId,jdbcType=INTEGER}
        where IS_DELETED = 0
    </update>

    <!-- 批量插入部门配置记录 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into T_BROADCAST_DEPT_CONFIG 
        (BROADCAST_DEPT_ID, DAY_FLAG, WEEK_FLAG, MONTH_FLAG, AED_FLAG, ZY_FLAG, 
         CUSTOM_FLAG, AMOUNT_STEP, WEBHOOK, IS_DELETED, ADD_TIME, CREATOR)
        values
        <foreach collection="list" item="item" separator=",">
        (#{item.broadcastDeptId,jdbcType=INTEGER}, 
         #{item.dayFlag,jdbcType=TINYINT},
         #{item.weekFlag,jdbcType=TINYINT},
         #{item.monthFlag,jdbcType=TINYINT},
         #{item.aedFlag,jdbcType=TINYINT},
         #{item.zyFlag,jdbcType=TINYINT},
         #{item.customFlag,jdbcType=TINYINT},
         #{item.amountStep,jdbcType=INTEGER},
         #{item.webhook,jdbcType=VARCHAR},
         0,
         #{item.addTime,jdbcType=TIMESTAMP},
         #{item.creator,jdbcType=INTEGER})
        </foreach>
    </insert>

</mapper>
