package com.vedeng.api.standard.core.exception;

import com.vedeng.api.standard.core.BaseResponseCode;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public class BusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private Integer code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 详细错误信息
     */
    private String detailMessage;
    
    /**
     * 构造函数 - 使用默认业务错误码
     */
    public BusinessException(String message) {
        super(message);
        this.code = BaseResponseCode.BUSINESS_ERROR.getCode();
        this.message = message;
    }
    
    /**
     * 构造函数 - 自定义错误码和消息
     */
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数 - 使用响应码枚举
     */
    public BusinessException(BaseResponseCode responseCode) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
    }
    
    /**
     * 构造函数 - 使用响应码枚举和自定义消息
     */
    public BusinessException(BaseResponseCode responseCode, String customMessage) {
        super(customMessage);
        this.code = responseCode.getCode();
        this.message = customMessage;
    }
    
    /**
     * 构造函数 - 包含原因异常
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = BaseResponseCode.BUSINESS_ERROR.getCode();
        this.message = message;
    }
    
    /**
     * 构造函数 - 包含原因异常和错误码
     */
    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 静态工厂方法 - 支持字符串错误码（用于业务系统返回的错误码）
     */
    public static BusinessException fromBusinessCode(String businessCode, String message) {
        // 尝试将字符串错误码转换为整型，失败则使用默认业务错误码
        Integer code;
        try {
            code = Integer.parseInt(businessCode);
        } catch (NumberFormatException e) {
            code = BaseResponseCode.BUSINESS_ERROR.getCode();
        }
        return new BusinessException(code, message);
    }
    
    /**
     * 构造函数 - 包含详细错误信息
     */
    public BusinessException(String message, String detailMessage) {
        super(message);
        this.code = BaseResponseCode.BUSINESS_ERROR.getCode();
        this.message = message;
        this.detailMessage = detailMessage;
    }
    
    /**
     * 静态工厂方法 - 参数错误
     */
    public static BusinessException parameterError(String message) {
        return new BusinessException(BaseResponseCode.PARAMETER_ERROR.getCode(), message);
    }
    
    /**
     * 静态工厂方法 - 数据不存在
     */
    public static BusinessException dataNotExist(String message) {
        return new BusinessException(BaseResponseCode.DATA_NOT_EXIST.getCode(), message);
    }
    
    /**
     * 静态工厂方法 - 数据已存在
     */
    public static BusinessException dataAlreadyExist(String message) {
        return new BusinessException(BaseResponseCode.DATA_ALREADY_EXIST.getCode(), message);
    }
    
    /**
     * 静态工厂方法 - 数据状态异常
     */
    public static BusinessException dataStatusError(String message) {
        return new BusinessException(BaseResponseCode.DATA_STATUS_ERROR.getCode(), message);
    }
    
    /**
     * 静态工厂方法 - 权限不足
     */
    public static BusinessException permissionDenied(String message) {
        return new BusinessException(BaseResponseCode.PERMISSION_DENIED.getCode(), message);
    }
    
    // Getter methods
    public Integer getCode() {
        return code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
    
    public String getDetailMessage() {
        return detailMessage;
    }
    
    public void setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
    }
    
    @Override
    public String toString() {
        return "BusinessException{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", detailMessage='" + detailMessage + '\'' +
                '}';
    }
}
