package com.vedeng.temporal.config;

import com.vedeng.temporal.domain.entity.BaseCompanyInfoEntity;
import com.vedeng.temporal.mapper.TemporalBaseCompanyInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 公司API配置服务
 * 负责实时获取公司域名配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Service
@Slf4j
public class CompanyApiConfigService {

    @Autowired
    private TemporalBaseCompanyInfoMapper temporalBaseCompanyInfoMapper;

    /**
     * 获取公司ERP域名
     * 实时查询数据库，不使用缓存
     *
     * @param companyCode 公司代码
     * @return ERP域名，如果不存在返回null
     */
    public String getCompanyDomain(String companyCode) {
        if (companyCode == null) {
            return null;
        }

        log.debug("实时查询公司域名：{}", companyCode);
        
        try {
            // 根据公司简称查询公司信息
            BaseCompanyInfoEntity companyInfo = temporalBaseCompanyInfoMapper.selectByShortName(companyCode);

            if (companyInfo == null) {
                log.debug("未找到公司代码为 {} 的公司信息", companyCode);
                return null;
            }

            String erpDomain = companyInfo.getErpDomain();
            if (!StringUtils.hasText(erpDomain)) {
                log.debug("公司 {} 的ERP域名为空", companyCode);
                return null;
            }

            log.debug("查询到公司 {} 的域名：{}", companyCode, erpDomain);
            return erpDomain;

        } catch (Exception e) {
            log.error("查询公司 {} 的域名失败", companyCode, e);
            return null;
        }
    }

}