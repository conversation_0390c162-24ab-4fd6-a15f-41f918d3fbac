package com.vedeng.crm.broadcast.web.controller;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.ServletUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 排行榜
 */
@ExceptionController
@Controller
@RequestMapping("/crm/broadcast/")
@Slf4j
public class BroadcastController extends BaseController {
    @Value("${wx.CorpId:ww877e627a6426776c}")
    private  String corpid="ww877e627a6426776c";
    @Value("${wx.AgentId:1000067}")
    private  String agentId="1000067";
    @Value("${wx.CorpSecret:hvsxyGxI8VX30VQ8eeMjnmEKgP0enW-DM1yDpEt41kY}")
    private  String appSecret="hvsxyGxI8VX30VQ8eeMjnmEKgP0enW-DM1yDpEt41kY";
    @Value("${wx.CallbackHost:https://qa.lxcrm.vedeng.com}")
    private  String wxcallbackhost;
    /**
     * 报价单详情
     * @return 页面
     */
    @RequestMapping(value = "leaderboard")
    @NoNeedAccessAuthorization
    public String index(HttpServletRequest request) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (currentUser != null) {//CRM 登录的时候
            //判断是否有免登的cookie
            log.info("qwcallback crm已经登录，直接展示");
            return "/vue/view/crm/profile/broadcast/index";
        }
        Object jobNum= ServletUtils.getRequest().getSession().getAttribute("wxsession_job_num");
        if(jobNum==null){
            log.info("qwcallback jobNum 为空，跳转到企微");
            //crm没有登录且没有免登，则跳转到免登
            return "redirect:"+toBaseAuthUrl(URLEncoder.encode("/crm/broadcast/leaderboard"),agentId,wxcallbackhost) ;
        }
        log.info("qwcallback jobNum 不为空，正常跳转");
        return "/vue/view/crm/profile/broadcast/index";
    }

    /**
     * 用户授权后，跳转到此页面
     * @param code
     * @param state
     * @return
     */
    @RequestMapping(value = "/callback")
    @NoNeedAccessAuthorization
    public String callback( String code, String state) {
        //判断session里面有没有对应的数据
        AccessToken accessToken=null;
        AccessToken getuserinfoResult=null;
        AccessToken getuserDetailResult=null;
        String jobNum="";
        try {
            accessToken =  getTicketCache(agentId,appSecret);
            log.info("qwcallback  state:{} 获取到ticket{}",state,accessToken);
            String getuserinfo = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=" + accessToken.getAccess_token()
                    + "&code=" + code;
            String result = httpGet(getuserinfo);
            log.info("qwcallback  getuserinfo{}",result);
            getuserinfoResult = JSON.parseObject(result, AccessToken.class);
            //企微ID
            if(!"0".equals(getuserinfoResult.getErrcode())){
                log.info("qwcallback 授权失败 jobnum{}",JSON.toJSONString(getuserinfoResult));
                return "rediret:/403.html?code=errorauth";
            }

            jobNum=getuserinfoResult.getUserid();
            log.info("qwcallback 授权成功 jobnum{}",jobNum);
            //TODO 判断jobNum是否存在

            //往session里面塞值
            ServletUtils.getRequest().getSession().setAttribute("wxsession_job_num",jobNum);
            return "redirect:/crm/broadcast/leaderboard";
        }catch (IllegalArgumentException e2) {
            log.error("qwcallback  授权异常 token:{} info:{} detail:{}",accessToken,getuserinfoResult,getuserDetailResult,e2);
            //return "redirect:/wxauth/error?code="+e2.getCode();
            return "redirect:/403.html?code=errorargument";
        }
        catch (Exception e){
            log.error("qwcallback  授权异常 token:{} info:{} detail:{}",accessToken,getuserinfoResult,getuserDetailResult,e);
            // return "redirect:/wxauth/error?code="+code;
        }
        return "rediret:/403.html?code=errorjobnum";
    }
    private String toBaseAuthUrl(String state,String agentId,String wxcallbackhostop){
        String redirectUrl= URLEncoder.encode(wxcallbackhostop);
        String url="https://open.weixin.qq.com/connect/oauth2/authorize?" +
                "appid=" +corpid+ "&" +
                "redirect_uri=" +redirectUrl+
                "&response_type=code&scope=snsapi_base&state="+state+"&agentid="
                +agentId+
                "#wechat_redirect";
        return url;
    }
    //TODO 多台机器了，要使用redis
    private static Cache<String, AccessToken> sixTyMinCache = Caffeine.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(50000)
            .build();
    private AccessToken getTicketCache(String agentId,String appSecret)  {
        // https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=ID&corpsecret=SECRET
        AccessToken token  = sixTyMinCache.get(agentId+appSecret, k->getTicket(agentId,appSecret));
        if(token==null||Integer.parseInt(token.getExpires_in())<=10){
            log.info("缓存为空或者过期");
            sixTyMinCache.invalidateAll();
        }
        return  getTicket(agentId,appSecret);
    }
    private AccessToken getTicket(String agentId, String appSecret1)  {
        String result=""    ;
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + corpid + "&corpsecret=" + appSecret1;
            result =  httpGet(url);
            if (StringUtils.isBlank(result)) {
                return null;
            }
            AccessToken token = JSON.parseObject(result, AccessToken.class);
            if ("0".equals(token.getErrcode())) {
                return token;
            }else{
                log.info("getTicket{}",result);
            }
        }catch (Exception e){
            log.error("获取token异常{}",result,e);
        }
        return null;
    }
    private static final CloseableHttpClient client = HttpClients.createDefault();
    private static final RequestConfig requestConfig = RequestConfig.custom()
            .setSocketTimeout(3000)
            .setConnectTimeout(3000)
            .setConnectionRequestTimeout(3000)
            .build();
    public  String httpGet(String uri) throws IOException {
        HttpGet httpGet = new HttpGet(uri);
        httpGet.setConfig(requestConfig);
        CloseableHttpResponse response = client.execute(httpGet);
        String s= EntityUtils.toString(response.getEntity(), Consts.UTF_8);
        try {
            response.close();
        }catch (Exception e){
            //
            log.error("获取token异常",e);
        }
        return s;
    }

    @Data
    public static class WxBaseUserInfo{
        private List<Integer> department;
        private String position;
        private String userid;
        private String gender;
        private String avatar;
        private String alias;
        private String name;
        private String qr_code;
        private String mobile;
        private String email;
        private String biz_mail;
        private String address;
        private String external_position;
        private String telephone;
        private String errcode;
        private String errmsg;
    }
    @Data
    public   class AccessToken {
        private String access_token;
        private String expires_in;
        private String userid;
        private String user_ticket;
        private String gender;
        private String avatar;
        private String qr_code;
        private String mobile;
        private String email;
        private String biz_mail;
        private String address;
        private String telephone;
        private String errcode;
        private String errmsg;
    }
}
