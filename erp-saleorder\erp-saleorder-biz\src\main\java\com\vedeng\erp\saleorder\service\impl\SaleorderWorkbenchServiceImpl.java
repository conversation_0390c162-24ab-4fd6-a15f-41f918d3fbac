package com.vedeng.erp.saleorder.service.impl;

import cn.hutool.core.date.DateTime;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.vedeng.activiti.ProcessInstanceContext;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.util.DateUtil;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodApplyMapper;
import com.vedeng.erp.common.enums.WorkBenchBusinessChanceStageEnum;
import com.vedeng.erp.saleorder.constant.SaleorderWorkbenchConstant;
import com.vedeng.erp.saleorder.dao.SaleorderWorkbenchMapper;
import com.vedeng.erp.saleorder.feign.OneDataSaleUserFeignApi;
import com.vedeng.erp.saleorder.model.dto.BaseDataInfoDto;
import com.vedeng.erp.saleorder.model.dto.BusinessChanceREQ;
import com.vedeng.erp.saleorder.model.dto.UnCollectedOrderInfoDto;
import com.vedeng.erp.saleorder.model.dto.WorkbenchDataDto;
import com.vedeng.erp.saleorder.model.dto.ext.ApprovalDataInfo;
import com.vedeng.erp.saleorder.model.dto.ext.BusinessChanceDateInfo;
import com.vedeng.erp.saleorder.model.dto.ext.BusinessChanceStageCountDto;
import com.vedeng.erp.saleorder.model.dto.ext.BusinessProfileDataInfo;
import com.vedeng.erp.saleorder.model.query.WorkbenchDto;
import com.vedeng.erp.saleorder.service.SaleorderWorkbenchService;
import com.vedeng.erp.saleorder.service.WorkbenchQueryStep;
import com.vedeng.erp.saleorder.vo.WorkbenchBusinessAnalysisValidationInfo;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.flash.constant.FlashConstant;
import com.vedeng.flash.dto.ProcessResultDto;
import com.vedeng.flash.dto.ReviewQueryDto;
import com.vedeng.flash.dto.vo.ReviewAndMessageVO;
import com.vedeng.order.dao.*;
import com.vedeng.order.model.*;
import com.vedeng.order.model.dto.SaleorderUserInfoDto;
import com.vedeng.order.model.vo.BussinessChanceVo;
import com.vedeng.order.model.vo.TaskGroupVo;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.*;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricProcessInstanceQuery;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.runtime.ProcessInstanceQuery;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Service(value = "saleorderWorkbenchService")
public class SaleorderWorkbenchServiceImpl implements SaleorderWorkbenchService {


    @Autowired
    UserService userService;

    @Autowired
    BusinessChanceQueryStep businessChanceQueryStep;

    @Autowired
    BusinessProfileQueryStep businessProfileQueryStep;

    @Autowired
    ApprovalQueryStep approvalQueryStep;

    @Resource
    SaleorderWorkbenchMapper saleorderWorkbenchMapper;

    @Autowired
    @Qualifier("saleorderService")
    protected SaleorderService saleorderService;

    @Autowired
    AfterSalesMapper afterSalesMapper;

    @Autowired
    SaleorderMapper saleorderMapper;

    @Autowired
    TraderCustomerMapper traderCustomerMapper;

    @Autowired
    QuoteorderMapper quoteorderMapper;

    @Autowired
    CustomerBillPeriodApplyMapper customerBillPeriodApplyMapper;

    @Autowired
    AuthorizationMapper authorizationMapper;

    @Autowired
    BussinessChanceMapper bussinessChanceMapper;

    @Autowired
    SaleorderModifyApplyMapper saleorderModifyApplyMapper;

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private UserApiService userApiService;
    @Autowired
    private OneDataSaleUserFeignApi oneDataSaleUserFeignApi;

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(4, 10, 1, TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(1000), new ThreadFactoryBuilder().setNameFormat("workbenchServicePoll-%d").build(), new ThreadPoolExecutor.AbortPolicy());

    @Override
    public WorkbenchDataDto getOverviewInfoDate(SaleorderUserInfoDto userInfoDto) throws InterruptedException, ExecutionException {
        WorkbenchDataDto workbenchDataDto = new WorkbenchDataDto();

        List<WorkbenchQueryStep> workbenchQueryStepList = new ArrayList<>();
        if (userInfoDto.getIsB2B() != null && userInfoDto.getIsB2B() == 1) {
            workbenchQueryStepList.add(businessChanceQueryStep);
        }
        workbenchQueryStepList.add(businessProfileQueryStep);
      //  workbenchQueryStepList.add(approvalQueryStep);

        CountDownLatch countDownLatch = new CountDownLatch(workbenchQueryStepList.size());

        List<Future> futureList = new ArrayList<>();
        for (WorkbenchQueryStep threadStep : workbenchQueryStepList) {
            Future<BaseDataInfoDto> result = EXECUTOR.submit(() -> {
                try {
                    return threadStep.doQuery(userInfoDto);
                } finally {
                    countDownLatch.countDown();
                }
            });
            futureList.add(result);
        }
        countDownLatch.await();
        handleResult(futureList, workbenchDataDto);
        return workbenchDataDto;
    }

    @Override
    public List<ReviewAndMessageVO> getApprovalDataInfo(ReviewQueryDto reviewQueryDto, User currentUser) {
        List<ReviewAndMessageVO> reviewAndMessageVOList = new ArrayList<>();

        // 创建历史实例查询
        HistoricProcessInstanceQuery historyProcessQueryForFilter = processEngine.getHistoryService().createHistoricProcessInstanceQuery();
        TaskService taskService = processEngine.getTaskService();
        RuntimeService runtimeService = processEngine.getRuntimeService();
        HistoryService historyService = processEngine.getHistoryService();
        ProcessInstanceQuery processQuery = processEngine.getRuntimeService().createProcessInstanceQuery();

        // 任务相关service
        // 创建历史实例查询
        TaskQuery taskInstanceQuery = taskService.createTaskQuery();
        List<String> soProcessKeyList = SaleorderWorkbenchConstant.SO_PROCESS_KEY_LIST;
        HashSet<String> keySets = new HashSet<>(soProcessKeyList);
        //待审核
        if (reviewQueryDto.getTabStatus() == null || FlashConstant.REMINED_REVIEW.equals(reviewQueryDto.getTabStatus())) {
            taskInstanceQuery.taskCandidateOrAssigned(currentUser.getUsername());
            ProcessResultDto activityProcess = getActivityProcessForRemind(processQuery, taskInstanceQuery, keySets, reviewQueryDto, historyProcessQueryForFilter);
            if (CollectionUtils.isNotEmpty(activityProcess.getHistoricProcessInstanceList())) {
                for (HistoricProcessInstance historicProcessInstance : activityProcess.getHistoricProcessInstanceList()) {
                    ReviewAndMessageVO reviewAndMessageVO = new ReviewAndMessageVO();
                    try {
                        resolveActivityTypeForStatus(historicProcessInstance, reviewAndMessageVO, FlashConstant.REMINED_REVIEW, activityProcess, taskService,runtimeService);
                    } catch (Exception e) {
                        log.error("解析销售工作台审批消息报错", e);
                    }
                    reviewAndMessageVOList.add(reviewAndMessageVO);
                }
            }
        }

        //发起的在途
        if (FlashConstant.SUBMMIT_UNFINISH.equals(reviewQueryDto.getTabStatus())) {
            //发起者过滤
            ProcessResultDto activityProcess = getActivityProcessForSubmitUnfinish(processQuery, taskInstanceQuery, keySets, reviewQueryDto, historyProcessQueryForFilter, currentUser);
            //处理
            if (CollectionUtils.isNotEmpty(activityProcess.getHistoricProcessInstanceList())) {
                for (HistoricProcessInstance historicProcessInstance : activityProcess.getHistoricProcessInstanceList()) {
                    ReviewAndMessageVO reviewAndMessageVO = new ReviewAndMessageVO();
                    try {
                        resolveActivityTypeForStatus(historicProcessInstance, reviewAndMessageVO, FlashConstant.SUBMMIT_UNFINISH, activityProcess, taskService, runtimeService);
                        reviewAndMessageVOList.add(reviewAndMessageVO);
                    } catch (Exception e) {
                        String message = e.getMessage();
                        log.error("解析销售工作台审批消息报错", e);
                    }
                }
            }

        }

        return reviewAndMessageVOList;
    }


    private void handleResult(List<Future> futureList, WorkbenchDataDto workbenchDataDto) throws ExecutionException, InterruptedException {

        if(CollectionUtils.isNotEmpty(futureList)){
            for (int i = 0; i < futureList.size(); i++) {
                Future future = futureList.get(i);
                if(future != null){
                    BaseDataInfoDto dataInfoDto = (BaseDataInfoDto)future.get();
                    if(dataInfoDto instanceof BusinessChanceDateInfo){
                        BusinessChanceDateInfo businessChanceDateInfo = (BusinessChanceDateInfo) dataInfoDto;
                        workbenchDataDto.setBusinessChanceDateInfo(businessChanceDateInfo);
                    }
                    if(dataInfoDto instanceof BusinessProfileDataInfo){
                        BusinessProfileDataInfo businessProfileDataInfo = (BusinessProfileDataInfo) dataInfoDto;
                        workbenchDataDto.setBusinessProfileDataInfo(businessProfileDataInfo);
                    }
                    if(dataInfoDto instanceof ApprovalDataInfo){
                        ApprovalDataInfo approvalDataInfo = (ApprovalDataInfo) dataInfoDto;
                        workbenchDataDto.setApprovalDataInfo(approvalDataInfo);
                    }
                }
            }
        }

    }
    //获得待审核
    private ProcessResultDto getActivityProcessForRemind(ProcessInstanceQuery processQuery, TaskQuery taskInstanceQuery, Set<String> processDefinitionKeySet, ReviewQueryDto reviewQueryDto,
                                                         HistoricProcessInstanceQuery historyProcessQueryForFilter) {
        ProcessResultDto processResultDto = new ProcessResultDto();
        // 用户任务
        List<Task> taskInstanceList = taskInstanceQuery.list();
        Set<String> processInstanceIds = new HashSet<>();
        for (Task taskInstance : taskInstanceList) {
            processInstanceIds.add(taskInstance.getProcessInstanceId());
        }

        if (CollectionUtils.isNotEmpty(processDefinitionKeySet) && CollectionUtils.isNotEmpty(processInstanceIds)) {
            processQuery.processDefinitionKeys(processDefinitionKeySet);
            processQuery.processInstanceIds(processInstanceIds);
            List<ProcessInstance> processInstanceList = processQuery.list();
            processResultDto.setProcessInstanceList(processInstanceList);
        } else {
            processResultDto.setProcessInstanceList(new ArrayList<>());
        }
        if (CollectionUtils.isNotEmpty(processResultDto.getProcessInstanceList())) {
            Set<String> instanceIds = new HashSet<>();
            for (ProcessInstance processInstance : processResultDto.getProcessInstanceList()) {
                instanceIds.add(processInstance.getProcessInstanceId());
            }
            historyProcessQueryForFilter.processInstanceIds(instanceIds);
            List<HistoricProcessInstance> list = historyProcessQueryForFilter.orderByProcessInstanceStartTime().desc().list();
            if (CollectionUtils.isNotEmpty(list)) {
                list = list.stream().filter(o -> filterTime(o, reviewQueryDto)).collect(Collectors.toList());
            }
            processResultDto.setHistoricProcessInstanceList(list);
        }
        processResultDto.setTaskInstanceList(taskInstanceList);


        return processResultDto;
    }

    //获得提交未审核
    private ProcessResultDto getActivityProcessForSubmitUnfinish(ProcessInstanceQuery processQuery, TaskQuery taskInstanceQuery, Set<String> processDefinitionKeySet, ReviewQueryDto reviewQueryDto,
                                                                 HistoricProcessInstanceQuery historyProcessQueryForFilter, User currentUser) {
        ProcessResultDto processResultDto = new ProcessResultDto();
        // 用户任务
        List<Task> taskInstanceList = taskInstanceQuery.list();
        Set<String> processInstanceIds = new HashSet<>();
        for (Task taskInstance : taskInstanceList) {
            processInstanceIds.add(taskInstance.getProcessInstanceId());
        }

        if(StringUtils.isNotBlank(reviewQueryDto.getSubmitEndTime())){
            historyProcessQueryForFilter.startedBefore(DateUtil.subDateByDays(DateUtil.StringToDate(reviewQueryDto.getSubmitEndTime()),1));
        }
        if(StringUtils.isNotBlank(reviewQueryDto.getSubmitStartTime())) {
            historyProcessQueryForFilter.startedAfter(DateUtil.StringToDate(reviewQueryDto.getSubmitStartTime()));
        }
        historyProcessQueryForFilter.processDefinitionKeyIn(SaleorderWorkbenchConstant.SO_PROCESS_KEY_LIST);
        historyProcessQueryForFilter.unfinished();
        historyProcessQueryForFilter.startedBy(currentUser.getUsername());
        List<HistoricProcessInstance> list1 = historyProcessQueryForFilter.orderByProcessInstanceStartTime().desc().list();
        long count = historyProcessQueryForFilter.count();

        if (CollectionUtils.isNotEmpty(list1)) {
            processResultDto.setHistoricProcessInstanceList(list1);
        }
        processResultDto.setTaskInstanceList(taskInstanceList);


        return processResultDto;
    }

    private boolean filterTime(HistoricProcessInstance o, ReviewQueryDto reviewQueryDto) {
        //提交时间过滤
        Boolean startFlag = true;
        Boolean endFlag = true;
        if (reviewQueryDto.getSubmitStartTime() != null && reviewQueryDto.getSubmitStartTime() != "") {
            startFlag = o.getStartTime().compareTo(DateUtil.StringToDate(reviewQueryDto.getSubmitStartTime())) > 0 ? true : false;

        }
        if (reviewQueryDto.getSubmitEndTime() != null && reviewQueryDto.getSubmitEndTime() != "") {
            endFlag = o.getStartTime().compareTo(DateUtil.subDateByDays(DateUtil.StringToDate(reviewQueryDto.getSubmitEndTime()),1)) < 0 ? true : false;
        }
        return startFlag && endFlag;
    }
    //解析process
    //"saleorderVerify","afterSalesVerify","traderCustomerVerify","quoteVerify","closeQuoteorderVerify","hc_order_auto_verify",
    //"customerAptitudeVerify","applyCustomerAccountPeriod","","overAfterSalesVerify","testshouquan","quoteVerify","closeQuoteorderVerify",
    //"closeBussinesschanceVerify","editSaleorderVerify","saleorderModifyAudit","contractReturnVerify"
    private void resolveActivityTypeForStatus(HistoricProcessInstance historicProcessInstance, ReviewAndMessageVO reviewAndMessageVO, Integer type, ProcessResultDto processResultDto, TaskService taskService, RuntimeService runtimeService) {


        if ("afterSalesVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            AfterSales afterSalesById = afterSalesMapper.getAfterSalesById(Integer.valueOf(id));
            reviewAndMessageVO.setCheckUrl("./order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=" + id + "&traderType=1");
            if (afterSalesById != null) {
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("您有一个待审核的售后【" + afterSalesById.getAfterSalesNo() + "】待审核，请查看");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("售后申请【" + afterSalesById.getAfterSalesNo() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }

        if ("overAfterSalesVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            AfterSales afterSalesById = afterSalesMapper.getAfterSalesById(Integer.valueOf(id));
            AfterSalesVo afterSalesVo = (AfterSalesVo) runtimeService.getVariables(historicProcessInstance.getId()).get("afterSalesVo");
            reviewAndMessageVO.setCheckUrl("/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=" + afterSalesById.getAfterSalesId() + "&traderType=1");
            if (afterSalesById != null) {
                if (afterSalesVo != null && ErpConst.ZERO.equals(afterSalesVo.getVerifiesType())) {
                    if (FlashConstant.REMINED_REVIEW.equals(type)) {
                        reviewAndMessageVO.setRemindMessage("您有一个售后订单关闭申请【" + afterSalesById.getAfterSalesNo() + "】待审核，请查看");
                    } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                        initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                        reviewAndMessageVO.setRemindMessage("售后订单关闭申请【" + afterSalesById.getAfterSalesNo() + "】已提交审核");
                    }
                } else {
                    if (FlashConstant.REMINED_REVIEW.equals(type)) {
                        reviewAndMessageVO.setRemindMessage("您有一个售后订单完成申请【" + afterSalesById.getAfterSalesNo() + "】待审核，请查看");
                    } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                        initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                        reviewAndMessageVO.setRemindMessage("售后订单完成申请【" + afterSalesById.getAfterSalesNo() + "】已提交审核");
                    }
                }

            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }

        if ("saleorderVerify".equals(historicProcessInstance.getProcessDefinitionKey())
                || "hc_order_auto_verify".equals(historicProcessInstance.getProcessDefinitionKey())
                || "bd_order_auto_verify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            String url = "";
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            Saleorder saleOrderById = saleorderMapper.getSaleOrderById(Integer.valueOf(id));
            url = "./orderstream/saleorder/detail.do?saleOrderId=" + id;
            reviewAndMessageVO.setCheckUrl(url);
            // 2019-02-25 耗材订单跳转不同duke
            if (saleOrderById != null) {
            if (saleOrderById.getOrderType() != null && "5".equals(saleOrderById.getOrderType().toString())) {
                url = "./order/hc/hcOrderDetailsPage.do?saleorderId=" + id;
            }
                reviewAndMessageVO.setCheckUrl(url);
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("您有一个待审核的订单【" + saleOrderById.getSaleorderNo() + "】，请查看");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("销售订单【" + saleOrderById.getSaleorderNo() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }

        if ("traderCustomerVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckUrl("./trader/customer/baseinfo.do?traderId=" + id);
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            TraderCustomerVo customerInfo = traderCustomerMapper.getCustomerInfo(Integer.valueOf(id));
            if (customerInfo != null) {
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("您有一个待审核的客户【" + customerInfo.getTraderName() + "】，请查看");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("客户审核【" + customerInfo.getTraderName() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }

        if ("quoteVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckUrl("./order/quote/getQuoteDetail.do?quoteorderId=" + id + "&viewType=2");
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            Quoteorder quoteorderById = quoteorderMapper.getQuoteorderById(Integer.valueOf(id));
            if (quoteorderById != null) {
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("您有一个待审核的报价【" + quoteorderById.getQuoteorderNo() + "】，请查看");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("报价审核【" + quoteorderById.getQuoteorderNo() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }

        if ("closeQuoteorderVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckUrl("./order/quote/getQuoteDetail.do?quoteorderId=" + id + "&viewType=2");
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            Quoteorder quoteorderById = quoteorderMapper.getQuoteorderById(Integer.valueOf(id));
            if (quoteorderById != null) {
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("您有一个报价【" + quoteorderById.getQuoteorderNo() + "】关闭审核，请查看");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("报价关闭审核【" + quoteorderById.getQuoteorderNo() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }

        if ("customerAptitudeVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);

            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            TraderCustomerVo customerInfo = traderCustomerMapper.getCustomerInfo(Integer.valueOf(id));

            if (customerInfo != null) {
                reviewAndMessageVO.setCheckUrl("./trader/customer/getFinanceAndAptitude.do?traderId=" + customerInfo.getTraderId() + "&traderCustomerId=" + customerInfo.getTraderCustomerId());
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("您有一个待审核的客户资质【" + customerInfo.getTraderName() + "】，请查看");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("客户资质【" + customerInfo.getTraderName() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }

        if ("applyCustomerAccountPeriod".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);

            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());

            Map<String, Object> variables = runtimeService.getVariables(historicProcessInstance.getId());
            String url = new StringBuilder("./finance/accountperiod/getAccountPeriodApply.do")
                    .append("?billPeriodApplyId=" + MapUtils.getString(variables, ProcessInstanceContext.ATTR_NAME_RELATED_TABLE_KEY))
                    .append("&traderAccountPeriodApplyId=" + MapUtils.getString(variables,ProcessInstanceContext.ATTR_NAME_RELATED_TABLE_KEY))
                    .append("&billPeriodType=" + MapUtils.getString(variables,ProcessInstanceContext.ATTR_NAME_ACCOUNT_PERIOD_APPLY_TYPE))
                    .append("&traderCustomerId=" + MapUtils.getString(variables,ProcessInstanceContext.ATTR_NAME_CUSTOMER_ID))
                    .append("&traderType=1")
                    .toString();
            reviewAndMessageVO.setCheckUrl(url);
            if (FlashConstant.REMINED_REVIEW.equals(type)) {
                reviewAndMessageVO.setRemindMessage("您有一个账期审核，请查看");
            } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                reviewAndMessageVO.setRemindMessage("账期审核已提交审核");
            }

            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }

        if ("testshouquan".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);

            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            AuthorizationApply authorizationApplyByKeyId = authorizationMapper.getAuthorizationApplyByKeyId(Integer.valueOf(id));
            if (authorizationApplyByKeyId != null) {
                String url = "./order/quote/authorizationExamine.do?authorizationApplyId=" + id;
                reviewAndMessageVO.setCheckUrl(url);
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("您有一个待审核的授权书审批流【" + authorizationApplyByKeyId.getAuthorizationApplyNum() + "】,请查看");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("授权书审批流【" + authorizationApplyByKeyId.getAuthorizationApplyNum() + "】已提交");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }

        if ("closeBussinesschanceVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);

            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            BussinessChanceVo businessChanceInfo = bussinessChanceMapper.getBusinessChanceInfo(Integer.valueOf(id));
            if (businessChanceInfo != null) {
//                String url = "./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=" + id +"&traderId="+businessChanceInfo.getTraderId();
                String  url = "/businessChance/details.do?id=" + id;
                reviewAndMessageVO.setCheckUrl(url);
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("您有一个商机【" + businessChanceInfo.getBussinessChanceNo() + "】关闭审核，请查看");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("商机关闭【" + businessChanceInfo.getBussinessChanceNo() + "】已提交");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }

        if ("editSaleorderVerify".equals(historicProcessInstance.getProcessDefinitionKey()) || "saleorderModifyAudit".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);

            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            SaleorderModifyApply saleorderModifyApplyInfo = saleorderModifyApplyMapper.getSaleorderModifyApplyInfo(Integer.valueOf(id));
            if (saleorderModifyApplyInfo != null) {
                String url = "./order/saleorder/viewModifyApply.do?saleorderModifyApplyId=" + id +"&saleorderId=" +saleorderModifyApplyInfo.getSaleorderId();
                reviewAndMessageVO.setCheckUrl(url);
                Saleorder saleOrderById = saleorderMapper.getSaleOrderById(saleorderModifyApplyInfo.getSaleorderId());
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("您有一个待审核的订单修改【" + saleOrderById.getSaleorderNo() + "】，请查看");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("订单修改【" + saleOrderById.getSaleorderNo() + "】已提交");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }

        if ("contractReturnVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {

            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);

            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            Saleorder saleOrderById = saleorderMapper.getSaleOrderById(Integer.valueOf(id));
            if (saleOrderById != null) {
                String url = "./order/saleorder/view.do?saleorderId=" + id ;
                reviewAndMessageVO.setCheckUrl(url);

                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("您有一个待审核的【" + saleOrderById.getSaleorderNo() + "】回传合同，请查看");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("合同回传审核【" + saleOrderById.getSaleorderNo() + "】已提交");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }
    }

    private void initReviewUser(HistoricProcessInstance historicProcessInstance, ReviewAndMessageVO reviewAndMessageVO, ProcessResultDto processResultDto, TaskService taskService) {
        try {
            String businessKey = historicProcessInstance.getBusinessKey();
            List<Task> list = taskService.createTaskQuery().processInstanceBusinessKey(businessKey)
                    .list();
            if(CollectionUtils.isNotEmpty(list)){
                Task task = list.get(0);
                List<IdentityLink> identityLinksForTask = taskService.getIdentityLinksForTask(task.getId());
                Set<String> userNameList = identityLinksForTask.stream().map(e -> e.getUserId()).collect(Collectors.toSet());
                String verifyUsers = StringUtils.join(userNameList, ",");
                reviewAndMessageVO.setReviewUser(verifyUsers);
            }
        } catch (Exception e) {
            log.info("1" + e.getMessage());
        }
    }


    @Override
    public Map getNoVerifyOrderInfo(List<Integer> userIds) {
        Map<String,Integer> map = new HashMap<>();

        WorkbenchDto workbenchDto = new WorkbenchDto();
        queryDatetimeFill(workbenchDto);
        workbenchDto.setUserIds(userIds);
        //总数
        map.put("noVerifyTotal",saleorderWorkbenchMapper.getNoVerifyOrderInfo(workbenchDto));
        //线上
        workbenchDto.setOnLineOrderTypeFlg(1);
        map.put("noVerifyOnLine",saleorderWorkbenchMapper.getNoVerifyOrderInfo(workbenchDto));
        //线下
        workbenchDto.setOnLineOrderTypeFlg(0);
        map.put("noVerifyOffLine",saleorderWorkbenchMapper.getNoVerifyOrderInfo(workbenchDto));
        return map;
    }

    @Override
    public Map getUnHandleBusinessChance(List<Integer> userIds) {
        Map<String,Integer> map = new HashMap<>();

        WorkbenchDto workbenchDto = new WorkbenchDto();
        queryDatetimeFill(workbenchDto);
        workbenchDto.setUserIds(userIds);

        // 获取所有下级用户id
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        UserDto userDto = UserDto.builder().isAdmin(currentUser.getIsAdmin())
                .companyId(currentUser.getCompanyId())
                .positType(currentUser.getPositType())
                .userId(currentUser.getId())
                .build();
        List<Integer> positionType = Collections.singletonList(310);
        List<UserDto> allSubUserList = userApiService.getAllSubUserList(userDto, positionType, false);
        List<Integer> userIdList = allSubUserList.stream().map(UserDto::getUserId).collect(Collectors.toList());
        BusinessChanceREQ businessChanceREQ = new BusinessChanceREQ();
        businessChanceREQ.setCurrentUserId(currentUser.getId());
        businessChanceREQ.setUserIdList(userIdList);
        //近3个月时间
        Calendar calendarStart = Calendar.getInstance();
        calendarStart.add(Calendar.MONTH, -3);
        calendarStart.set(Calendar.HOUR_OF_DAY, 0);
        calendarStart.set(Calendar.MINUTE, 0);
        calendarStart.set(Calendar.SECOND, 0);
        calendarStart.set(Calendar.MILLISECOND, 0);
        businessChanceREQ.setStartTime(calendarStart.getTimeInMillis());
        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.set(Calendar.HOUR_OF_DAY, 23);
        calendarEnd.set(Calendar.MINUTE, 59);
        calendarEnd.set(Calendar.SECOND, 59);
        calendarEnd.set(Calendar.MILLISECOND, 0);
        businessChanceREQ.setEndTime(calendarEnd.getTimeInMillis());

        /**
         * 计算未处理商机数
         */
        map.put("unHandleNum",saleorderWorkbenchMapper.getUnHandleNum(businessChanceREQ));

        /**
         * 计算待再次沟通商机数
         */
        map.put("waitCommunicateNum",saleorderWorkbenchMapper.getWaitCommunicateNum(businessChanceREQ));
        return map;
    }

    @Override
    public Map getNoPaymentOrderInfo(List<Integer> userIds) {
        Map<String,Object> map = new HashMap<>();
        WorkbenchDto workbenchDto = new WorkbenchDto();
        queryDatetimeFill(workbenchDto);
        workbenchDto.setUserIds(userIds);
        //未收款
        workbenchDto.setPaymentStatus(0);
        map.put("noPaymentAll",saleorderWorkbenchMapper.getNoPaymentOrderInfo(workbenchDto));
        //部分收款
        workbenchDto.setPaymentStatus(1);
        map.put("noPaymentSome",saleorderWorkbenchMapper.getNoPaymentOrderInfo(workbenchDto));
        //金额
        map.put("noPaymentMoeny",saleorderWorkbenchMapper.getNoPaymentMoneyInfo(workbenchDto));
        return map;
    }

    @Override
    public Map getNoInvoiceOrderInfo(List<Integer> userIds) {
        WorkbenchDto workbenchDto = new WorkbenchDto();
        queryDatetimeFill(workbenchDto);
        workbenchDto.setUserIds(userIds);
        return saleorderWorkbenchMapper.getNoInvoiceOrderInfo(workbenchDto);
    }

    @Override
    public Map getNoContractOrderInfo(User user) {

        SaleorderContract order = new SaleorderContract();
        order.setCompanyId(1);
        order.setUserId(user.getUserId());
        Map<String,Object> map = new HashMap<>();
        //总数
        map.put("noContractTotal", saleorderService.getContractReturnOrderCount(order, "1"));
        //金额
        map.put("noContractMoney",saleorderService.getContractReturnOrderCount(order));
        return map;
    }

    @Override
    public Map getVerifyingOrderInfo(List<Integer> userIds) {
        Map<String,Integer> map = new HashMap<>();
        WorkbenchDto workbenchDto = new WorkbenchDto();
        queryDatetimeFill(workbenchDto);
        workbenchDto.setUserIds(userIds);
        //总数
        map.put("verifyingTotal",saleorderWorkbenchMapper.getVerifyingOrderInfo(workbenchDto));
        //线上
        workbenchDto.setOnLineOrderTypeFlg(1);
        map.put("verifyingOnLine",saleorderWorkbenchMapper.getVerifyingOrderInfo(workbenchDto));
        //线下
        workbenchDto.setOnLineOrderTypeFlg(0);
        map.put("verifyingOffLine",saleorderWorkbenchMapper.getVerifyingOrderInfo(workbenchDto));
        return map;
    }

    @Override
    public Map getNoPurchaseOrderInfo(List<Integer> userIds) {
        Map<String,Integer> map = new HashMap<>();
        WorkbenchDto workbenchDto = new WorkbenchDto();
        queryDatetimeFill(workbenchDto);
        workbenchDto.setUserIds(userIds);
        //未采购
        workbenchDto.setPurchaseStatus(0);
        map.put("noPurchaseAll",saleorderWorkbenchMapper.getNoPurchaseOrderInfo(workbenchDto));
        //部分采购
        workbenchDto.setPurchaseStatus(1);
        map.put("noPurchaseSome",saleorderWorkbenchMapper.getNoPurchaseOrderInfo(workbenchDto));
        return map;
    }

    @Override
    public Map getNoSendGoodsOrderInfo(List<Integer> userIds) {
        Map<String,Integer> map = new HashMap<>();
        WorkbenchDto workbenchDto = new WorkbenchDto();
        queryDatetimeFill(workbenchDto);
        workbenchDto.setUserIds(userIds);
        //未发货
        workbenchDto.setDeliveryStatus(0);
        map.put("noSendGoodsAll",saleorderWorkbenchMapper.getNoSendGoodsOrderInfo(workbenchDto));
        //部分发货
        workbenchDto.setDeliveryStatus(1);
        map.put("noSendGoodsSome",saleorderWorkbenchMapper.getNoSendGoodsOrderInfo(workbenchDto));
        //逾期交付
        map.put("noNoSend",saleorderWorkbenchMapper.getNoNoSendOrderInfo(workbenchDto));
        return map;
    }

    @Override
    public Map getNoCompletedAfterSaleInfo(List<Integer> userIds) {
        Map<String,Integer> map = new HashMap<>();
        WorkbenchDto workbenchDto = new WorkbenchDto();
        queryDatetimeFill(workbenchDto);
        workbenchDto.setUserIds(userIds);
        //待审核
        workbenchDto.setNoCompletedAfterSaleFalg(0);
        map.put("noCompletedAfterSaleReadyVerify",saleorderWorkbenchMapper.getNoCompletedAfterSaleInfo(workbenchDto));
        //审核中
        workbenchDto.setNoCompletedAfterSaleFalg(1);
        map.put("noCompletedAfterSaleVerifing",saleorderWorkbenchMapper.getNoCompletedAfterSaleInfo(workbenchDto));
        //进行中
        workbenchDto.setNoCompletedAfterSaleFalg(2);
        map.put("noCompletedAfterSaleIng",saleorderWorkbenchMapper.getNoCompletedAfterSaleInfo(workbenchDto));
        //已驳回
        workbenchDto.setNoCompletedAfterSaleFalg(3);
        map.put("noCompletedAfterSaleRejected",saleorderWorkbenchMapper.getNoCompletedAfterSaleInfo(workbenchDto));
        return map;
    }


    @Override
    public UnCollectedOrderInfoDto getUnCollectedOrderInfo(List<Integer> userIdList) {
       return saleorderWorkbenchMapper.getUnReturnedInfo(userIdList);
    }


    private SaleorderUserInfoDto getUserInfo(){
        SaleorderUserInfoDto userInfoDto = null;
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            userInfoDto = userService.getPositionAndSubUser(user);
        } catch (Exception e) {
            log.error("获取用户信息异常：e:{}", e);
        }
        return userInfoDto;
    }


    private void queryDatetimeFill(WorkbenchDto workbenchDto){
        SaleorderUserInfoDto userInfo = getUserInfo();
        if (Objects.nonNull(userInfo) && !ErpConst.ONE.equals(userInfo.getLevelType())){
            workbenchDto.setForwardThirtyDate(cn.hutool.core.date.DateUtil.offsetDay(new Date(),-30).getTime());
        }
    }

    @Override
    public Map getPerformanceEvaluationInfo(List<Integer> userIds) {
        Map<String, Integer> result = new HashMap<>(3);
        result.put("noInvoiceOrderNum", saleorderWorkbenchMapper.getNoInvoiceOrderNum(userIds));
        result.put("contractNotReviewedOrderNum", saleorderWorkbenchMapper.getContractNotReviewedOrderNum(userIds));
        result.put("confirmationNotApprovedOrderNum", saleorderWorkbenchMapper.getConfirmationNotApprovedOrderNum(userIds));
        return result;
    }

    @Override
    public Integer getCrmTaskCount(Integer userId) {
        return bussinessChanceMapper.getCrmTaskCount(userId);
    }

    @Override
    public Integer getCrmTodoTaskCount(Integer userId) {
        return bussinessChanceMapper.getCrmTodoTaskCount(userId);
    }

    @Override
    public List<TaskGroupVo> getCrmTaskGroupCount(Integer userId, String startDate, String endDate) {
        return bussinessChanceMapper.getCrmTaskGroupCount(userId, startDate, endDate);
    }
    @Override
    public List<com.vedeng.order.model.vo.TaskDetailVo> getCrmTaskForOneDay(Integer userId, String dateStr) {
        return bussinessChanceMapper.getCrmTaskForOneDay(userId, dateStr);
    }

    @Override
    public com.vedeng.erp.saleorder.vo.WorkbenchBusinessAnalysisVO getWorkbenchBusinessAnalysis(com.vedeng.erp.saleorder.model.query.WorkbenchDto workbenchDto) {
        com.vedeng.erp.saleorder.vo.WorkbenchBusinessAnalysisVO vo = new com.vedeng.erp.saleorder.vo.WorkbenchBusinessAnalysisVO();
        //获取查询人员
        List<Integer> userIdList = workbenchDto.getUserIds();
        if (CollectionUtils.isEmpty(userIdList)) {
            return vo;
        }
        
        // 设置查询时间参数
        Long startTime = System.currentTimeMillis();
        Long endTime = System.currentTimeMillis();
        Date startDate= cn.hutool.core.date.DateUtil.beginOfMonth(new Date());
        Date endDate=cn.hutool.core.date.DateUtil.endOfMonth(new Date());
        Integer year = workbenchDto.getYear();
        Integer month = workbenchDto.getMonth();
        
        // 如果传入了开始时间和结束时间，直接使用
        if (year != null) {
            if (month != null) {
                // 查询指定年月的数据
                DateTime current= cn.hutool.core.date.DateUtil.parse(year + "-" + String.format("%02d", month) + "-01");
                startTime = cn.hutool.core.date.DateUtil.beginOfMonth(current).getTime();
                endTime = cn.hutool.core.date.DateUtil.endOfMonth(current).getTime();

                startDate = cn.hutool.core.date.DateUtil.beginOfMonth(current);
                endDate = cn.hutool.core.date.DateUtil.endOfMonth(current);
            } else {
                // 查询全年数据
                startTime = cn.hutool.core.date.DateUtil.beginOfYear(cn.hutool.core.date.DateUtil.parse(year + "-01-01")).getTime();
                endTime = cn.hutool.core.date.DateUtil.endOfYear(cn.hutool.core.date.DateUtil.parse(year + "-01-01")).getTime();
                startDate = cn.hutool.core.date.DateUtil.beginOfYear(cn.hutool.core.date.DateUtil.parse(year + "-01-01"));
                endDate = cn.hutool.core.date.DateUtil.endOfYear(cn.hutool.core.date.DateUtil.parse(year + "-01-01"));
            }
        } else {
            // 默认查询当月数据
            startTime = cn.hutool.core.date.DateUtil.beginOfMonth(new Date()).getTime();
            endTime = cn.hutool.core.date.DateUtil.endOfMonth(new Date()).getTime();
            year = cn.hutool.core.date.DateUtil.year(new Date());
            month = cn.hutool.core.date.DateUtil.month(new Date()) + 1;
        }

        
        try {
            // 1. 获取到款金额
            BigDecimal realReceiveAmount = Optional.ofNullable( saleorderWorkbenchMapper.getRealReceiveAmount(userIdList, startTime, endTime)).orElse(BigDecimal.ZERO);
            vo.setActualPayment(toRMBdefaultZero(realReceiveAmount));

            Long startTimeToday = cn.hutool.core.date.DateUtil.beginOfDay(new Date()).getTime();
            Long  endTimeToday = cn.hutool.core.date.DateUtil.endOfDay(new Date()).getTime();
            // 1. 获取到款金额
            BigDecimal todayPayAmount = Optional.ofNullable( saleorderWorkbenchMapper.getRealReceiveAmount(userIdList, startTimeToday, endTimeToday)).orElse(BigDecimal.ZERO);
            vo.setTodayPayment(toRMBdefaultZero(todayPayAmount));
            
            // 2. 获取到款目标
            BigDecimal paymentTarget = Optional.ofNullable(saleorderWorkbenchMapper.getPaymentTarget(userIdList, year, month)).orElse(BigDecimal.ZERO);
            vo.setPaymentTarget(toRMBdefaultZero(paymentTarget));
            
            // 3. 计算到款完成率
            if (paymentTarget != null && paymentTarget.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal completionRate = realReceiveAmount.divide(paymentTarget, 4, BigDecimal.ROUND_HALF_UP);
                vo.setPaymentCompletionRateNumber(completionRate);
                vo.setPaymentCompletionRate(completionRate.multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
            } else {
                vo.setPaymentCompletionRateNumber(BigDecimal.ZERO);
                vo.setPaymentCompletionRate("0%");
            }

//            // 4. 毛利率 - 使用现有大数据接口，这里先设置为空
//            vo.setGrossProfitRate("0%");
//
            // 5. 自有品牌销售占比
            List<Map<String, Object>> ownBrandData =Optional.ofNullable( saleorderWorkbenchMapper.getOwnBrandSalesRatio(userIdList, startTime, endTime)).orElse(Collections.emptyList());
            if (CollectionUtils.isNotEmpty(ownBrandData)) {
                BigDecimal totalAmount = BigDecimal.ZERO;
                BigDecimal ownBrandAmount = BigDecimal.ZERO;
                
                for (Map<String, Object> data : ownBrandData) {
                    BigDecimal amount = (BigDecimal) data.get("REAL_AMOUNT");
                    Integer isOwnBrand = (Integer) data.get("IS_OWN_BRAND");
                    if (amount != null) {
                        totalAmount = totalAmount.add(amount);
                        if (isOwnBrand != null && isOwnBrand == 1) {
                            ownBrandAmount = ownBrandAmount.add(amount);
                        }
                    }
                }
                
                if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal ratio = ownBrandAmount.divide(totalAmount, 4, BigDecimal.ROUND_HALF_UP);
                    vo.setOwnBrandSalesRatio(ratio.multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
                } else {
                    vo.setOwnBrandSalesRatio("0%");
                }
            } else {
                vo.setOwnBrandSalesRatio("0%");
            }
            
            // 6. 预计成单商机金额
            BigDecimal expectedAmount = Optional.ofNullable(saleorderWorkbenchMapper.getExpectedOrderOpportunityAmount(userIdList, startTime, endTime)).orElse(BigDecimal.ZERO);
            vo.setExpectedOrderOpportunityAmount(toRMBdefaultZero(expectedAmount));

            // 7. 商机满足率
            BigDecimal satisfactionData = Optional.ofNullable(saleorderWorkbenchMapper.getOpportunitySatisfactionData(userIdList, startTime, endTime)).orElse(BigDecimal.ZERO);
            if (paymentTarget != null && paymentTarget.compareTo(BigDecimal.ZERO) > 0) {
                //加上 预计成单商机金额
                satisfactionData=(satisfactionData==null?BigDecimal.ZERO:satisfactionData).add(expectedAmount==null?BigDecimal.ZERO:expectedAmount);
                BigDecimal satisfactionRate = satisfactionData.divide(paymentTarget, 4, BigDecimal.ROUND_HALF_UP);
                vo.setOpportunitySatisfactionRate(satisfactionRate.multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
            } else {
                vo.setOpportunitySatisfactionRate("0%");
            }
            
            // 8. 线索数
            Integer clueNum = Optional.ofNullable(saleorderWorkbenchMapper.getClueNum(userIdList, startDate, endDate)).orElse(0);
            Integer clueNumCompany = Optional.ofNullable(saleorderWorkbenchMapper.getClueNum(null, startDate, endDate)).orElse(0);
            // 无关联线索商机数
            Integer noRelateNum = Optional.ofNullable(saleorderWorkbenchMapper.getUnrelatedClueOpportunityNum(userIdList, startTime, endTime)).orElse(0);
            Integer noRelateNumCompany =Optional.ofNullable( saleorderWorkbenchMapper.getUnrelatedClueOpportunityNum(null, startTime, endTime)).orElse(0);

            //线索数
            Integer totalClueNum = (clueNum != null ? clueNum : 0)+(noRelateNum != null ? noRelateNum : 0);
            Integer totalClueNumCompany = (clueNumCompany != null ? clueNumCompany : 0)+(noRelateNumCompany != null ? noRelateNumCompany : 0);
            //商机数
            Integer totalOpNum = Optional.ofNullable(saleorderWorkbenchMapper.getOpportunityNum(userIdList, startTime, endTime)).orElse(0);
            Integer totalOpNumCompany =Optional.ofNullable( saleorderWorkbenchMapper.getOpportunityNum(null, startTime, endTime)).orElse(0);
            vo.setClueNum(totalClueNum);

            Map<String, com.vedeng.erp.saleorder.vo.WorkbenchBusinessAnalysisValidationInfo> validationInfoMap = new HashMap<>();
            //商机map手动处理
            WorkbenchBusinessAnalysisValidationInfo info=new WorkbenchBusinessAnalysisValidationInfo();
            if(totalClueNum>0){
                //商机转化率人员
                BigDecimal userConver = new BigDecimal(""+totalOpNum).divide(new BigDecimal(""+totalClueNum), 4, BigDecimal.ROUND_HALF_UP);
                info.setConversionRateNumber(userConver.multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP));
                //商机转化率均值
                BigDecimal companyConver = new BigDecimal(""+totalOpNumCompany).divide(new BigDecimal(""+totalClueNumCompany), 4, BigDecimal.ROUND_HALF_UP);
                info.setConversionRateAvgNumber(companyConver.multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP) );
            }else{
                info.setConversionRateNumber(BigDecimal.ZERO);
                info.setConversionRateAvgNumber(BigDecimal.ZERO);
            }
            info.setConversionRate(info.getConversionRateNumber()+"%");
            info.setConversionRateAvg(info.getConversionRateAvgNumber() + "%");
            info.setNum(totalOpNum);
            validationInfoMap.put("opportunity",info);
            //其他阶段性数据
            List<BusinessChanceStageCountDto> stageCountList = saleorderWorkbenchMapper.getOpportunityStageCount(userIdList, startTime, endTime);
            List<BusinessChanceStageCountDto> stageCountListCompany = saleorderWorkbenchMapper.getOpportunityStageCount(null, startTime, endTime);
            Arrays.stream(WorkBenchBusinessChanceStageEnum.values()).forEach(bcs->{
                WorkbenchBusinessAnalysisValidationInfo stageInfo=new WorkbenchBusinessAnalysisValidationInfo();
                if(totalClueNum>0&&CollectionUtils.isNotEmpty(stageCountList)){
                    Integer preliminaryNum = stageCountList.stream().filter(e -> e.getStage().equals(bcs.getCode())).mapToInt(BusinessChanceStageCountDto::getCountNum).sum();
                    Integer preliminaryNumCompany = stageCountListCompany.stream().filter(e -> e.getStage().equals(bcs.getCode())).mapToInt(BusinessChanceStageCountDto::getCountNum).sum();
                    BigDecimal userConver = new BigDecimal(""+preliminaryNum).divide(new BigDecimal(""+totalClueNum), 4, BigDecimal.ROUND_HALF_UP);
                    stageInfo.setConversionRateNumber(userConver.multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP));
                    BigDecimal companyConver = new BigDecimal(""+preliminaryNumCompany).divide(new BigDecimal(""+totalClueNumCompany), 4, BigDecimal.ROUND_HALF_UP);
                    stageInfo.setConversionRateAvgNumber(companyConver.multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP) );
                    stageInfo.setNum(preliminaryNum);
                }else{
                    stageInfo.setConversionRateNumber(BigDecimal.ZERO);
                    stageInfo.setConversionRateAvgNumber(BigDecimal.ZERO);
                    stageInfo.setNum(0);
                }
                stageInfo.setConversionRate(stageInfo.getConversionRateNumber()+"%");
                stageInfo.setConversionRateAvg(stageInfo.getConversionRateAvgNumber() + "%");
                validationInfoMap.put(bcs.getResKey(),stageInfo);
            });
            vo.setValidationInfoMap(validationInfoMap);
        } catch (Exception e) {
            log.error("获取工作台业务分析数据异常：e: ", e);
        }
        return vo;
    }
    private String toRMBdefaultZero(BigDecimal d){
        return d != null ?""+ d.toString() : "0";
    }
}
