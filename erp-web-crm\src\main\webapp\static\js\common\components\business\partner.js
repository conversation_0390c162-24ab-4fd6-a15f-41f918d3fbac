// 协作人
Vue.component('partner-list', {
    template: `
        <div class="records-panel has-fixed-btn">
            <div class="panel-title">协作人</div>
            <div class="panel-wrap has-fixed-btn" v-show="!pageLoading">
                <div class="partner-records" v-if="partnerList.length">
                    <div class="list tbody">
                        <div 
                            class="item tr" 
                            v-for="(item, index) in partnerList"
                            :key="index"
                        >
                            <div class="partner-detail-top">
                                <ui-user :name="item.saleUserName" :avatar="item.saleAliasHeadPicture"></ui-user>
                                <template v-if="item.shareTags[0] == 3">
                                    <div class="partner-detail-option" v-if="canAdd" @click="remove(item)">移除</div>
                                    <div class="partner-detail-option disabled" v-else title="请联系归属销售进行操作">移除</div>
                                </template>
                            </div>
                            <div class="partner-detail-bottom">
                                <div class="partner-detail-tag">{{ getTag(item.shareTags) }}</div>
                                <div class="partner-detail-time">{{ item.addTime | partnerTime }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="panel-null-data" v-else>
                    <i class="vd-ui_icon icon-info1 icon"></i>
                    <p class="font">未添加协作人</p>
                </div>
            </div>

            <div class="panel-btn-wrap" v-if="canAdd">
                <ui-button class="btn" type="primary" @click="addPartner">添加协同人员</ui-button>
            </div>
            <div class="panel-btn-wrap" v-else>
                <ui-button class="btn" disabled type="primary" title="请联系归属销售进行操作">添加协同人员</ui-button>
            </div>

            <partner-create-dialog ref="partner_Create_Dialog" :success-fun="initData"></partner-create-dialog>
        </div>
    `,
    props: {
        relatedId: {
            type: [Number, String]
        },
        // 业务类型 1.商机 2.报价 3.订单 4.售后 5.线索
        businessType: {
            type: [Number, String],
        },
        // 归属销售-ID
        belongerId: {
            type: [Number, String],
        },
        businessNo: {
            type: [Number, String],
        },
    },
    data () {
        return {
            pageLoading: true,
            partnerList: [],
            userId: '', // 操作用id
            canAdd: false
        }
    },
    filters: {
        partnerTime (val) {
            if (val) return val.substring(5);
            else return val;
        }
    },
    mounted () {
        this.userId = USERINFO.userId;
        this.initData();
    },
    methods: {
        initData () {
            this.pageLoading = true;
            this.$axios.post(`/crm/businessLeads/profile/queryShardList?businessId=${this.relatedId}&businessType=${this.businessType}`)
                .then(({data}) => {
                    if (data.success) {
                        this.partnerList = data.data.list || [];
                        this.canAdd = data.data.shareBtn;
                    } else {
                        this.$message.error(data.message);
                    }
                    this.pageLoading = false;
                })
        },
        remove (item) {
            let _this = this;
            this.$popup.warn({
                message: '移除后该用户不能查看和操作，确定移除吗?',
                buttons: [{
                    txt: '移除',
                    btnClass: 'confirm',
                    callback() {
                        GLOBAL.showGlobalLoading();

                        _this.$axios.post('/crm/businessLeads/profile/cancelShare?id=' + item.id).then(({ data }) => {
                            GLOBAL.hideGlobalLoading();
                            if (data.success) {
                                _this.$message.success("移除成功");
                                _this.initData();
                            } else {
                                _this.$message.warn(data.message || '网络异常');
                            }
                        })
                    }
                }, {
                    txt: '取消',
                }]
            })
        },
        addPartner () {
            this.$refs.partner_Create_Dialog.open({
                relatedId: this.relatedId,
                businessNo: this.businessNo,
                businessType: this.businessType,
            });
        },
        getTag(shareTags) {
            let tags = {1: '线下销售', 2: '产线负责人', 3: '手动添加'};

            if(!(shareTags && shareTags.length)) {
                return '';
            } else {
                let tagsArr = [];
                let flag = false;
                shareTags.forEach(item => {
                    if(item == 1 || item == 2) {
                        flag = true;
                    } else if(flag){
                        return false;
                    }
                    tagsArr.push(tags[item]);
                })

                return tagsArr.join('/');
            }
        },
    }
})

// 协作人列表--弹层
Vue.component('partner-list-dialog', {
    template: `
        <div>
            <ui-dialog
                :visible.sync="isShow"
                title="协作人"
                width="720px"
                align="center"
            >
                <div v-if="canAdd">
                    <ui-button type="primary" @click="addPartner">添加协作人</ui-button>
                </div>
                <div v-else>
                    <ui-button type="primary" disabled title="请联系归属销售进行操作">添加协作人</ui-button>
                </div>

                <div class="records-panel">
                    <div class="panel-wrap dialog-inner" v-if="list.length">
                        <ui-table container-height="470px" :width-border="true" :auto-scroll="false" :headers="shareListHeaders" :list="list">
                            <template v-slot:tag="{ row }">
                                {{ getTag(row.shareTags) }}
                            </template>
                            <template v-slot:option="{ row }">
                                <div class="option-wrap" v-if="row.shareTags[0] == 3">
                                    <a v-if="canAdd" class="remove-partner" @click="remove(row)">移除</a>
                                    <a v-else class="remove-partner disabled" title="请联系归属销售进行操作">移除</a>
                                </div>
                            </template>
                        </ui-table>
                    </div>
                    <div class="panel-null-data" v-else>
                        <i class="vd-ui_icon icon-info1 icon"></i>
                        <p class="font">未添加协作人</p>
                    </div>
                </div>
            </ui-dialog>

            <partner-create-dialog ref="partnerCreateDialog" :success-fun="refreshList"></partner-create-dialog>
        </div>
    `,

    props: {
        refreshPanel: Function
    },
    data () {
        return {
            userId: '',   // 操作用户id
            belongerId: '',   // 归属销售
            relatedId: '',    // 业务id
            businessType: '', // 业务类型 1.商机 2.报价 3.订单 4.售后 5.线索

            isShow: false,
            shareListHeaders: [
                {
                    key: "saleUserName",
                    label: "协作人",
                    width: "180px",
                    avatar: 'saleAliasHeadPicture'
                },
                {
                    key: "tag",
                    label: "标签",
                    width: "220px"
                },
                {
                    key: "addTime",
                    label: "添加时间",
                    width: "209px"
                },
                {
                    key: "option",
                    label: "操作",
                    width: "66px"
                },
            ],
            list: [],
            canAdd: false,
        }
    },
    mounted () {
        this.userId = USERINFO.userId;
    },
    methods: {
        close () {
            this.isShow = false;
        },
        open (query) {
            console.log('dialog prop：', query);
            this.relatedId = query.relatedId || '';
            this.belongerId = query.belongerId || '';
            this.businessType = query.businessType || '';
            this.creatQuery = query;

            this.initData();
        },
        refreshList () {
            this.initData();
            if (this.refreshPanel) { // 更新外层列表
                this.refreshPanel();
            }
        },
        // 初始化列表
        initData () {
            GLOBAL.showGlobalLoading();
            this.$axios.post(`/crm/businessLeads/profile/queryShardList?businessId=${this.relatedId}&businessType=${this.businessType}`).then(({data}) => {
                if (data.success) {
                    this.list = data.data.list || []; // 操作记录列表
                    this.canAdd = data.data.shareBtn;
                    this.isShow = true;
                } else {
                    this.$message.error(data.message);
                }
                GLOBAL.hideGlobalLoading();
            });
        },

        remove (item) {
            let _this = this;
            this.$popup.warn({
                message: '移除后该用户不能查看和操作，确定移除吗',
                buttons: [{
                    txt: '移除',
                    btnClass: 'confirm',
                    callback() {
                        GLOBAL.showGlobalLoading();
                        _this.$axios.post('/crm/businessLeads/profile/cancelShare?id=' + item.id).then(({ data }) => {
                            GLOBAL.hideGlobalLoading();
                            if (data.success) {
                                _this.$message.success("移除成功");
                                _this.refreshList();
                                if (_this.refreshPanel) { // 更新外层列表
                                    _this.refreshPanel();
                                }
                            } else {
                                _this.$message.warn(data.message || '网络异常')
                            }
                        })
                    }
                }, {
                    txt: '取消',
                }]
            })
        },
        addPartner () {
            this.$refs.partnerCreateDialog.open(this.creatQuery);
        },
        getTag(shareTags) {
            let tags = {1: '线下销售', 2: '产线负责人', 3: '手动添加'};

            if(!(shareTags && shareTags.length)) {
                return '';
            } else {
                let tagsArr = [];
                let flag = false;
                shareTags.forEach(item => {
                    if(item == 1 || item == 2) {
                        flag = true;
                    } else if(flag){
                        return false;
                    }
                    tagsArr.push(tags[item]);
                })

                return tagsArr.join('/');
            }
        },
    }
})

// 添加协作人--弹层
Vue.component('partner-create-dialog', {
    template: `
        <div class="partner-created-dialog">
            <ui-dialog
                :visible.sync="isShow"
                title="添加协作人"
                width="720px"
                align="center"
            >
                <div class="form-wrap label-width-3" v-if="isShow">
                    <ui-form-item label="协作人" :must="true">
                        <div class="ui-col-4">
                            <ui-select 
                                :remote="true" 
                                :avatar="true" 
                                placeholder="请输入并选择协作人" 
                                v-model="shareUserId" 
                                clearable 
                                :remote-info="allUserRemoteInfo" 
                                valid="createPartnerForm_shareList" 
                                @change="handlerShareSelect"
                                multipleType="fixed"
                            ></ui-select>
                        </div>
                    </ui-form-item>
                </div>

                <template slot="footer">
                    <div class="dlg-form-footer">
                        <ui-button @click="submit" type="primary">确定</ui-button>
                        <ui-button @click="close" class="close">取消</ui-button>
                    </div>
                </template>
            </ui-dialog>
        </div>
    `,
    props: {
        successFun: Function
    },
    data () {
        return {
            isShow: false,
            canAjax: true,
            query: {
                // relatedId: '',    // 业务单据的ID 线索id / 商机id / 
                // businessType: '',  // 业务类型 1.商机 2.报价 3.订单 4.售后 5.线索
                // businessNo: '',    // 业务单据的编号
            },

            // form
            shareUserId: '',
            shareUserName: '',
            allUserRemoteInfo: {
                url: '/crm/businessLeads/profile/queryShardUserForBusiness',
                paramsType: 'url',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            isMulti: true
        }
    },
    mounted () {
    },
    methods: {
        open(query) {
            console.log('create query:', query);
            this.query = query;
            this.allUserRemoteInfo.url = `/crm/businessLeads/profile/queryShardUserForBusiness?businessId=${query.relatedId || ''}&businessType=${query.businessType || ''}`;
            this.isShow = true;

            this.isMulti = !!query.multi;

            this.shareUserId = [];
            // this.shareUserName = '';

            this.shareList = [];

            this.$form.rules({
                shareList: {
                    required: '请选择协作人'
                },
            }, 'createPartnerForm', this)
        },
        close() {
            this.isShow = false;
        },
        handlerShareSelect(data) {
            // this.shareUserName = data.selected.label;
            this.shareList = [];
            data.list.forEach(item => {
                this.shareList.push({
                    saleUserId: item.value,
                    saleUserName: item.label
                })
            })
        },

        submit () {
            if (this.$form.validForm('createPartnerForm')) {
                this.canAjax = false;

                GLOBAL.showGlobalLoading();

                // let url = '/crm/businessLeads/profile/shareBusiness';
                // let reqData = {
                //     businessId: this.query.relatedId,
                //     businessNo: this.query.businessNo,
                //     businessType: this.query.businessType,
                //     saleUserId: this.shareUserId,
                //     saleUserName: this.shareUserName,
                // };

                let businessOrderItemDtoList = [];

                if(this.isMulti) {
                    // url = '/crm/businessLeads/profile/batchShareBusiness';
                    businessOrderItemDtoList = this.query.list;
                } else {
                    businessOrderItemDtoList = [{
                        businessNo: this.query.businessNo,
                        businessType: this.query.businessType,
                        businessId: this.query.relatedId,
                    }];
                }
                
                let reqData = {
                    businessOrderItemDtoList: businessOrderItemDtoList,
                    saleUserList: this.shareList
                }

                this.$axios.post('/crm/businessLeads/profile/batchShareBusiness', reqData).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        this.$message.success(data.message || '添加成功');
                        this.isShow = false;
                        if (this.successFun) {
                            this.successFun();
                        }
                    } else {
                        this.canAjax = true;
                        this.$message.warn(data.message || '网络异常')
                    }
                })
            }
        },
    },
})