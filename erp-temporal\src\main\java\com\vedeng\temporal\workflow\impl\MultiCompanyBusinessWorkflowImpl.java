package com.vedeng.temporal.workflow.impl;

import com.vedeng.temporal.config.ActivityConfigManager;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.notification.NotificationContext;
import com.vedeng.temporal.workflow.MultiCompanyBusinessWorkflow;
import com.vedeng.temporal.workflow.activity.*;
import com.vedeng.temporal.workflow.process.InvoiceEntryProcess;
import com.vedeng.temporal.workflow.process.InventoryReceiptProcess;
import com.vedeng.temporal.workflow.process.PeerListProcess;
import com.vedeng.temporal.workflow.process.PaymentTransferProcess;
import com.vedeng.temporal.workflow.process.PurchaseOrderProcess;
import com.vedeng.temporal.workflow.process.SalesOrderProcess;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 多公司业务流程工作流实现类
 * 基于 Temporal 最佳实践重构：Workflow 只负责编排，具体业务委托给服务层
 * <p>
 * 架构特点：
 * - 符合 Temporal 最佳实践：Workflow 编排，Service 执行
 * - 移除了违规的"流程型 Activity"
 * - 使用服务层处理具体业务逻辑
 * - 大幅简化代码，提高可维护性
 * <p>
 * 执行模式：并行执行六个业务流程
 * 1. 销售订单流程：委托给 SalesOrderProcess
 * 2. 采购订单流程：委托给 PurchaseOrderProcess
 * 3. 库存入库流程：委托给 InventoryReceiptProcess
 * 4. 同行单流程：委托给 PassageReceiptProcess
 * 5. 发票录入流程：委托给 InvoiceEntryProcess
 * 6. 付款传递流程：委托给 PaymentTransferProcess
 *
 * <AUTHOR> 4.0 sonnet
 * @version 8.0 (销售采购订单流程独立版本)
 * @since 2025-01-30
 */
@Slf4j
public class MultiCompanyBusinessWorkflowImpl implements MultiCompanyBusinessWorkflow {

    // ==================== 原子 Activity Stubs ====================

    private final CompanySequenceActivity companySequenceActivity;
    private final PurchaseOrderActivity purchaseOrderActivity;
    private final SalesOrderActivity salesOrderActivity;
    private final InventoryReceiptActivity inventoryReceiptActivity;
    private final PaymentActivity paymentActivity;
    private final InvoiceEntryActivity invoiceEntryActivity;

    // ==================== 业务流程服务 ====================

    private final SalesOrderProcess salesOrder;
    private final PurchaseOrderProcess purchaseOrder;
    private final InventoryReceiptProcess inventoryReceipt;
    private final PeerListProcess passageReceipt;
    private final InvoiceEntryProcess invoiceEntry;
    private final PaymentTransferProcess paymentTransfer;

    /**
     * 构造函数 - 初始化所有原子 Activity Stubs 和业务流程服务
     * 符合 Temporal 最佳实践：Workflow 编排，Service 执行
     */
    public MultiCompanyBusinessWorkflowImpl() {
        // 创建配置管理器
        ActivityConfigManager configManager = ActivityConfigManager.createDefault();

        // 创建原子 Activity Stubs
        this.companySequenceActivity = Workflow.newActivityStub(
                CompanySequenceActivity.class, configManager.getQuickActivityOptions());

        this.purchaseOrderActivity = Workflow.newActivityStub(
                PurchaseOrderActivity.class, configManager.getActivityOptions());

        this.salesOrderActivity = Workflow.newActivityStub(
                SalesOrderActivity.class, configManager.getActivityOptions());

        this.inventoryReceiptActivity = Workflow.newActivityStub(
                InventoryReceiptActivity.class, configManager.getActivityOptions());

        this.paymentActivity = Workflow.newActivityStub(
                PaymentActivity.class, configManager.getActivityOptions());

        this.invoiceEntryActivity = Workflow.newActivityStub(
                InvoiceEntryActivity.class, configManager.getActivityOptions());


        // 创建业务流程服务
        this.salesOrder = new SalesOrderProcess(
                companySequenceActivity, salesOrderActivity);

        this.purchaseOrder = new PurchaseOrderProcess(
                companySequenceActivity, purchaseOrderActivity);

        this.inventoryReceipt = new InventoryReceiptProcess(
                companySequenceActivity, inventoryReceiptActivity);

        this.passageReceipt = new PeerListProcess(
                companySequenceActivity, inventoryReceiptActivity);

        this.invoiceEntry = new InvoiceEntryProcess(
                companySequenceActivity, invoiceEntryActivity);

        this.paymentTransfer = new PaymentTransferProcess(
                companySequenceActivity, paymentActivity);

        log.info("MultiCompanyBusinessWorkflow 初始化完成，所有原子 Activity Stubs 和业务流程服务已创建（包括新的销售订单、采购订单和同行单独立流程）");
    }

    // ==================== 主工作流方法 ====================

    @Override
    public CompanyBusinessResponse executeMultiCompanyBusiness(CompanyBusinessRequest request) {
        try {
            logBusinessProgress("开始", request.getBusinessId(), "多公司业务流程启动");

            // 获取公司执行顺序
            List<String> companySequence = getCompanyExecutionSequence(request);
            if (companySequence == null || companySequence.isEmpty()) {
                return CompanyBusinessResponse.failure("无法获取公司执行顺序配置", "COMPANY_SEQUENCE_ERROR");
            }

            logBusinessProgress("配置", request.getBusinessId(),
                    String.format("获取到公司执行顺序: %s", companySequence));

            // 并行执行六个业务流程
            CompanyBusinessResponse result = executeBusinessProcessesInParallel(request, companySequence);

            if (Boolean.TRUE.equals(result.getSuccess())) {
                logBusinessProgress("完成", request.getBusinessId(), "多公司业务流程执行成功");
            } else {
                logBusinessProgress("失败", request.getBusinessId(),
                        String.format("多公司业务流程执行失败: %s", result.getMessage()));
            }

            return result;

        } catch (Exception e) {
            log.error("多公司业务流程执行异常，businessId: {}", request.getBusinessId(), e);

            // 发送工作流失败通知（更严重的异常）
            try {
                sendWorkflowFailureNotification(request.getBusinessId(), "多公司业务流程", e.getMessage());
            } catch (Exception notificationError) {
                log.error("发送工作流失败通知失败", notificationError);
            }

            return CompanyBusinessResponse.failure("多公司业务流程执行异常", "WORKFLOW_EXECUTION_ERROR", e);
        }
    }

    // ==================== 公司序列获取 ====================

    /**
     * 基于流转单配置获取公司序列
     */
    private List<String> getCompanyExecutionSequence(CompanyBusinessRequest request) {
        Long flowOrderId = Long.parseLong(request.getBusinessId());
        List<String> companySequence = companySequenceActivity.getCompanySequenceByFlowOrder(flowOrderId);
        if (companySequence == null || companySequence.isEmpty()) {
            log.error("未找到公司执行顺序配置，flowOrderId: {}", flowOrderId);
            return null;
        }

        return companySequence;
    }


    // ==================== 并行业务流程执行 ====================

    /**
     * 并行执行六个业务流程
     * 符合 Temporal 最佳实践：在 Workflow 中编排并行执行，委托给服务层
     */
    private CompanyBusinessResponse executeBusinessProcessesInParallel(CompanyBusinessRequest request,
                                                                       List<String> companySequence) {
        logBusinessProgress("并行执行", request.getBusinessId(), "同时启动六个业务流程");

        try {
            // 并行启动六个业务流程 - 委托给服务层
            Promise<CompanyBusinessResponse> salesProcessPromise = Async.function(
                    salesOrder::execute, request, companySequence);

            Promise<CompanyBusinessResponse> purchaseProcessPromise = Async.function(
                    purchaseOrder::execute, request, companySequence);

            Promise<CompanyBusinessResponse> inventoryProcessPromise = Async.function(
                    inventoryReceipt::execute, request, companySequence);

            Promise<CompanyBusinessResponse> passageProcessPromise = Async.function(
                    passageReceipt::execute, request, companySequence);

            Promise<CompanyBusinessResponse> invoiceProcessPromise = Async.function(
                    invoiceEntry::execute, request, companySequence);

            Promise<CompanyBusinessResponse> paymentProcessPromise = Async.function(
                    paymentTransfer::execute, request, companySequence);

            logBusinessProgress("并行执行", request.getBusinessId(), "六个流程已同时启动，等待执行结果");

            // 等待所有流程完成并收集结果
            CompanyBusinessResponse purchaseResult = purchaseProcessPromise.get();
            CompanyBusinessResponse salesResult = salesProcessPromise.get();
            CompanyBusinessResponse inventoryResult = inventoryProcessPromise.get();
            CompanyBusinessResponse passageResult = passageProcessPromise.get();
            CompanyBusinessResponse invoiceResult = invoiceProcessPromise.get();
            CompanyBusinessResponse paymentResult = paymentProcessPromise.get();

            // 检查销售订单流程结果
            if (!Boolean.TRUE.equals(salesResult.getSuccess())) {
                log.error("销售订单流程执行失败: {}", salesResult.getMessage());
                sendWorkflowFailureNotification(request.getBusinessId(), "销售订单流程", salesResult.getMessage());
                return salesResult;
            }

            // 检查采购订单流程结果
            if (!Boolean.TRUE.equals(purchaseResult.getSuccess())) {
                log.error("采购订单流程执行失败: {}", purchaseResult.getMessage());
                sendWorkflowFailureNotification(request.getBusinessId(), "采购订单流程", purchaseResult.getMessage());
                return purchaseResult;
            }

            // 检查库存入库流程结果
            if (!Boolean.TRUE.equals(inventoryResult.getSuccess())) {
                log.error("库存入库流程执行失败: {}", inventoryResult.getMessage());
                sendWorkflowFailureNotification(request.getBusinessId(), "库存入库流程", inventoryResult.getMessage());
                return inventoryResult;
            }

            // 检查同行单流程结果
            if (!Boolean.TRUE.equals(passageResult.getSuccess())) {
                log.error("同行单流程执行失败: {}", passageResult.getMessage());
                sendWorkflowFailureNotification(request.getBusinessId(), "同行单流程", passageResult.getMessage());
                return passageResult;
            }

            // 检查发票录入流程结果
            if (!Boolean.TRUE.equals(invoiceResult.getSuccess())) {
                log.error("发票录入流程执行失败: {}", invoiceResult.getMessage());
                sendWorkflowFailureNotification(request.getBusinessId(), "发票录入流程", invoiceResult.getMessage());
                return invoiceResult;
            }

            // 检查付款传递流程结果
            if (!Boolean.TRUE.equals(paymentResult.getSuccess())) {
                log.error("付款传递流程执行失败: {}", paymentResult.getMessage());
                sendWorkflowFailureNotification(request.getBusinessId(), "付款传递流程", paymentResult.getMessage());
                return paymentResult;
            }

            // 验证所有流程的执行结果
            logBusinessProgress("完成", request.getBusinessId(), "六个业务流程并行执行全部成功");
            return CompanyBusinessResponse.success(
                    "多公司业务流程并行执行完成（销售订单 + 采购订单 + 库存入库 + 同行单 + 发票录入 + 付款传递）",
                    request.getBusinessId());

        } catch (Exception e) {
            log.error("并行执行业务流程时发生异常，businessId: {}", request.getBusinessId(), e);

            // 发送工作流失败通知
            sendWorkflowFailureNotification(request.getBusinessId(), "并行执行业务流程", e.getMessage());

            return CompanyBusinessResponse.failure("并行执行业务流程失败", "PARALLEL_EXECUTION_ERROR", e);
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 记录业务进度日志
     */
    private void logBusinessProgress(String stage, String businessId, String message) {
        log.info("【{}】业务ID: {} - {}", stage, businessId, message);
    }


    /**
     * 发送工作流失败通知
     */
    private void sendWorkflowFailureNotification(String businessId, String operationName, String errorMessage) {
        try {
            NotificationContext context = NotificationContext.builder()
                    .businessId(businessId)
                    .operationName(operationName)
                    .location("MultiCompanyBusinessWorkflow")
                    .errorMessage(errorMessage)
                    .build();

            log.debug("发送工作流失败通知成功，业务ID: {}, 操作: {}", businessId, operationName);
        } catch (Exception e) {
            log.error("发送工作流失败通知失败，业务ID: {}, 操作: {}", businessId, operationName, e);
            // 不抛出异常，避免影响主流程
        }
    }

}
