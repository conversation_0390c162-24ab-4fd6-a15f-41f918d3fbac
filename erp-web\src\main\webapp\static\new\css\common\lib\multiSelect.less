.multi-select-wrap {
    color: #333;
    position: relative;

    .multi-select-label {
        display: flex;
        align-items: center;
        border: 1px solid #BABFC2;
        border-radius: 3px;
        padding-left: 10px;
        height: 31px;
        cursor: pointer;

        .icon-down {
            padding: 0 5px;
            transition: transform .22s ease;
            color: #666;
        }

        .icon-error2 {
            padding: 0 5px;
            display: none;
            color: #666;
            cursor: pointer;

            &:hover {
                color: #333;
            }
        }

        &.on-select {
            &:hover {
                .icon-down { 
                    display: none;
                }

                .icon-error2 { 
                    display: block;
                }
            }
        }

        .multi-select-label-txt {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            height: 22px;
            overflow: hidden;
            line-height: 22px;

            &.placeholder {
                color: #999;
            }
        
            .select-label-item {
                display: flex;
                height: 22px;
                align-items: center;
                border: 1px solid #E1E5E8;
                background: #F5F7FA;
                padding-left: 5px;
                border-radius: 3px;
                margin-right: 5px;
                line-height: 1.5;

                &.item-num {
                    padding-right: 5px;
                }

                .icon-delete {
                    width: 22px;
                    height: 22px;
                    color: #666;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &:hover {
                        color: #09f;
                    }
                }
            }
        }

        &:hover {
            border-color: #969B9E;
        }

        &.small {
            height: 26px;

            .multi-select-label-txt {
                height: 20px;

                .select-label-item {
                    height: 20px;
                }
            }
        }

        &.open {
            border-color: #09f;

            .icon-down {
                transform: rotate(180deg);
            }

            &.on-select {
                .icon-down {
                    display: none;
                }

                .icon-error2 {
                    display: block;
                }
            }
        }
    }

    .multi-select-drop {
        position: absolute;
        background: #fff;
        box-shadow: 0px 5px 10px rgba(0, 0, 0, .1);
        padding: 5px 0;
        min-width: 100%;
        border: 1px solid #BABFC2;
        border-radius: 3px;
        display: none;

        .multi-select-list {
            max-height: 330px;
            overflow: auto;

            .multi-select-option {
                height: 33px;
                padding: 0 10px;
                display: flex;
                align-items: center;
                cursor: pointer;

                &:hover {
                    background: #e6ecf2;
                }

                .vd-icon {
                    font-size: 16px;
                    margin-right: 5px;

                    &.icon-checkbox1 {
                        color: #999;
                    }

                    &.icon-checkbox2 {
                        color: #09f;
                        display: none;
                    }
                }

                .multi-select-option-txt {
                    flex: 1;
                }

                &.checked {
                    .vd-icon {
                        &.icon-checkbox1 {
                            display: none;
                        }

                        &.icon-checkbox2 {
                            display: block;
                        }
                    }

                    .multi-select-option-txt {
                        color: #09f;
                    }
                }
            }
        }
    }
}