<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.broadcast.mapdao.BroadcastLogMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.broadcast.domain.entity.BroadcastLogEntity">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="BROADCAST_PIC_CONFIG_ID" jdbcType="INTEGER" property="broadcastPicConfigId"/>
        <result column="BROADCAST_TIME" jdbcType="TIMESTAMP" property="broadcastTime"/>
        <result column="BROADCAST_TYPE" jdbcType="TINYINT" property="broadcastType"/>
        <result column="BROADCAST_TARGET" jdbcType="TINYINT" property="broadcastTarget"/>
        <result column="USER_ID" jdbcType="INTEGER" property="userId"/>
        <result column="DEPT_ID" jdbcType="INTEGER" property="deptId"/>
        <result column="SECOND_DEPT_ID" jdbcType="INTEGER" property="secondDeptId"/>
        <result column="AMOUNT_STEP" jdbcType="DECIMAL" property="amountStep"/>
        <result column="AMOUNT_STEP_NUM" jdbcType="INTEGER" property="amountStepNum"/>
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="COMPLET_RATE" jdbcType="INTEGER" property="completRate"/>
        <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, BROADCAST_PIC_CONFIG_ID, BROADCAST_TIME, BROADCAST_TYPE, BROADCAST_TARGET, USER_ID,
        DEPT_ID, SECOND_DEPT_ID, AMOUNT_STEP, AMOUNT_STEP_NUM,TOTAL_AMOUNT, COMPLET_RATE, IS_DELETED,
        ADD_TIME, MOD_TIME, CREATOR, UPDATER
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_LOG
        where ID = #{id,jdbcType=INTEGER}
        and IS_DELETED = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update T_BROADCAST_LOG
        set IS_DELETED = 1, MOD_TIME = now()
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastLogEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_LOG (BROADCAST_PIC_CONFIG_ID, BROADCAST_TIME, BROADCAST_TYPE,
        BROADCAST_TARGET, USER_ID, DEPT_ID,
        SECOND_DEPT_ID, AMOUNT_STEP,AMOUNT_STEP_NUM, TOTAL_AMOUNT,
        COMPLET_RATE, IS_DELETED, ADD_TIME,
        MOD_TIME, CREATOR, UPDATER)
        values (#{broadcastPicConfigId,jdbcType=INTEGER}, #{broadcastTime,jdbcType=TIMESTAMP}, #{broadcastType,jdbcType=TINYINT},
        #{broadcastTarget,jdbcType=TINYINT}, #{userId,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER},
        #{secondDeptId,jdbcType=INTEGER}, #{amountStep,jdbcType=DECIMAL}, #{amountStepNum,jdbcType=INTEGER}, #{totalAmount,jdbcType=DECIMAL},
        #{completRate,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP},
        #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastLogEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="broadcastPicConfigId != null">
                BROADCAST_PIC_CONFIG_ID,
            </if>
            <if test="broadcastTime != null">
                BROADCAST_TIME,
            </if>
            <if test="broadcastType != null">
                BROADCAST_TYPE,
            </if>
            <if test="broadcastTarget != null">
                BROADCAST_TARGET,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="deptId != null">
                DEPT_ID,
            </if>
            <if test="secondDeptId != null">
                SECOND_DEPT_ID,
            </if>
            <if test="amountStep != null">
                AMOUNT_STEP,
            </if>
            <if test="amountStepNum != null">
                AMOUNT_STEP_NUM,
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT,
            </if>
            <if test="completRate != null">
                COMPLET_RATE,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="broadcastPicConfigId != null">
                #{broadcastPicConfigId,jdbcType=INTEGER},
            </if>
            <if test="broadcastTime != null">
                #{broadcastTime,jdbcType=TIMESTAMP},
            </if>
            <if test="broadcastType != null">
                #{broadcastType,jdbcType=TINYINT},
            </if>
            <if test="broadcastTarget != null">
                #{broadcastTarget,jdbcType=TINYINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="secondDeptId != null">
                #{secondDeptId,jdbcType=INTEGER},
            </if>
            <if test="amountStep != null">
                #{amountStep,jdbcType=DECIMAL},
            </if>
            <if test="amountStepNum != null">
                #{amountStepNum,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="completRate != null">
                #{completRate,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastLogEntity">
        update T_BROADCAST_LOG
        <set>
            <if test="broadcastPicConfigId != null">
                BROADCAST_PIC_CONFIG_ID = #{broadcastPicConfigId,jdbcType=INTEGER},
            </if>
            <if test="broadcastTime != null">
                BROADCAST_TIME = #{broadcastTime,jdbcType=TIMESTAMP},
            </if>
            <if test="broadcastType != null">
                BROADCAST_TYPE = #{broadcastType,jdbcType=TINYINT},
            </if>
            <if test="broadcastTarget != null">
                BROADCAST_TARGET = #{broadcastTarget,jdbcType=TINYINT},
            </if>
            <if test="userId != null">
                USER_ID = #{userId,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                DEPT_ID = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="secondDeptId != null">
                SECOND_DEPT_ID = #{secondDeptId,jdbcType=INTEGER},
            </if>
            <if test="amountStep != null">
                AMOUNT_STEP = #{amountStep,jdbcType=DECIMAL},
            </if>
            <if test="amountStepNum != null">
                AMOUNT_STEP_NUM = #{amountStepNum,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="completRate != null">
                COMPLET_RATE = #{completRate,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastLogEntity">
        update T_BROADCAST_LOG
        set BROADCAST_PIC_CONFIG_ID = #{broadcastPicConfigId,jdbcType=INTEGER},
        BROADCAST_TIME = #{broadcastTime,jdbcType=TIMESTAMP},
        BROADCAST_TYPE = #{broadcastType,jdbcType=TINYINT},
        BROADCAST_TARGET = #{broadcastTarget,jdbcType=TINYINT},
        USER_ID = #{userId,jdbcType=INTEGER},
        DEPT_ID = #{deptId,jdbcType=INTEGER},
        SECOND_DEPT_ID = #{secondDeptId,jdbcType=INTEGER},
        AMOUNT_STEP = #{amountStep,jdbcType=DECIMAL},
        AMOUNT_STEP_NUM = #{amountStepNum,jdbcType=INTEGER},
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
        COMPLET_RATE = #{completRate,jdbcType=INTEGER},
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    
    <select id="selectByParams" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastLogEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_LOG
        <where>
	        IS_DELETED = 0
	        <if test="broadcastPicConfigId != null">
	            AND BROADCAST_PIC_CONFIG_ID = #{broadcastPicConfigId}
	        </if>
	        <if test="broadcastType != null">
	            AND BROADCAST_TYPE = #{broadcastType}
	        </if>
	        <if test="broadcastTarget != null">
	            AND BROADCAST_TARGET = #{broadcastTarget}
	        </if>
	        <if test="amountStepNum != null">
	            AND AMOUNT_STEP_NUM = #{amountStepNum}
	        </if>
	        <if test="userId != null">
	            AND USER_ID = #{userId}
	        </if>
	        <if test="deptId != null">
	            AND DEPT_ID = #{deptId}
	        </if>
	        <if test="secondDeptId != null">
	            AND SECOND_DEPT_ID = #{secondDeptId}
	        </if>
	        <if test="broadcastTime != null">
			    AND DATE(BROADCAST_TIME) = DATE(#{broadcastTime})
			</if>
	        
	    </where>
        
    </select>
    

</mapper>
