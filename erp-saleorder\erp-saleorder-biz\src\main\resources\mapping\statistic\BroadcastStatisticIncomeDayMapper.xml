<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.statistic.mapper.BroadcastStatisticIncomeDayMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="STATISTICS_TYPE" jdbcType="INTEGER" property="statisticsType" />
    <result column="STATISTICS_TIME" jdbcType="VARCHAR" property="statisticsTime" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="TEAM_ID" jdbcType="INTEGER" property="teamId" />
    <result column="TEAM_NAME" jdbcType="VARCHAR" property="teamName" />
    <result column="DEPT_ID" jdbcType="INTEGER" property="deptId" />
    <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="IS_DEL" jdbcType="INTEGER" property="isDel" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="CONDITION_TEXT" jdbcType="LONGVARCHAR" property="conditionText" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    ID, STATISTICS_TYPE, STATISTICS_TIME, USER_ID, USER_NAME, TEAM_ID, TEAM_NAME, DEPT_ID, 
    DEPT_NAME, AMOUNT, IS_DEL, CREATE_TIME, UPDATE_TIME
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    CONDITION_TEXT
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_BROADCAST_STATISTIC_INCOME_DAY
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_BROADCAST_STATISTIC_INCOME_DAY
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayKey" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_BROADCAST_STATISTIC_INCOME_DAY
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayKey">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from T_BROADCAST_STATISTIC_INCOME_DAY
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from T_BROADCAST_STATISTIC_INCOME_DAY
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_BROADCAST_STATISTIC_INCOME_DAY (STATISTICS_TYPE, STATISTICS_TIME, USER_ID, 
      USER_NAME, TEAM_ID, TEAM_NAME, 
      DEPT_ID, DEPT_NAME, AMOUNT, 
      IS_DEL, CREATE_TIME, UPDATE_TIME, 
      CONDITION_TEXT)
    values (#{statisticsType,jdbcType=INTEGER}, #{statisticsTime,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, 
      #{userName,jdbcType=VARCHAR}, #{teamId,jdbcType=INTEGER}, #{teamName,jdbcType=VARCHAR}, 
      #{deptId,jdbcType=INTEGER}, #{deptName,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, 
      #{isDel,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{conditionText,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_BROADCAST_STATISTIC_INCOME_DAY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statisticsType != null">
        STATISTICS_TYPE,
      </if>
      <if test="statisticsTime != null">
        STATISTICS_TIME,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="userName != null">
        USER_NAME,
      </if>
      <if test="teamId != null">
        TEAM_ID,
      </if>
      <if test="teamName != null">
        TEAM_NAME,
      </if>
      <if test="deptId != null">
        DEPT_ID,
      </if>
      <if test="deptName != null">
        DEPT_NAME,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="conditionText != null">
        CONDITION_TEXT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statisticsType != null">
        #{statisticsType,jdbcType=INTEGER},
      </if>
      <if test="statisticsTime != null">
        #{statisticsTime,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=INTEGER},
      </if>
      <if test="teamName != null">
        #{teamName,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="deptName != null">
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="conditionText != null">
        #{conditionText,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from T_BROADCAST_STATISTIC_INCOME_DAY
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update T_BROADCAST_STATISTIC_INCOME_DAY
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.statisticsType != null">
        STATISTICS_TYPE = #{record.statisticsType,jdbcType=INTEGER},
      </if>
      <if test="record.statisticsTime != null">
        STATISTICS_TIME = #{record.statisticsTime,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        USER_ID = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.userName != null">
        USER_NAME = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.teamId != null">
        TEAM_ID = #{record.teamId,jdbcType=INTEGER},
      </if>
      <if test="record.teamName != null">
        TEAM_NAME = #{record.teamName,jdbcType=VARCHAR},
      </if>
      <if test="record.deptId != null">
        DEPT_ID = #{record.deptId,jdbcType=INTEGER},
      </if>
      <if test="record.deptName != null">
        DEPT_NAME = #{record.deptName,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        AMOUNT = #{record.amount,jdbcType=DECIMAL},
      </if>
      <if test="record.isDel != null">
        IS_DEL = #{record.isDel,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.conditionText != null">
        CONDITION_TEXT = #{record.conditionText,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update T_BROADCAST_STATISTIC_INCOME_DAY
    set ID = #{record.id,jdbcType=INTEGER},
      STATISTICS_TYPE = #{record.statisticsType,jdbcType=INTEGER},
      STATISTICS_TIME = #{record.statisticsTime,jdbcType=VARCHAR},
      USER_ID = #{record.userId,jdbcType=INTEGER},
      USER_NAME = #{record.userName,jdbcType=VARCHAR},
      TEAM_ID = #{record.teamId,jdbcType=INTEGER},
      TEAM_NAME = #{record.teamName,jdbcType=VARCHAR},
      DEPT_ID = #{record.deptId,jdbcType=INTEGER},
      DEPT_NAME = #{record.deptName,jdbcType=VARCHAR},
      AMOUNT = #{record.amount,jdbcType=DECIMAL},
      IS_DEL = #{record.isDel,jdbcType=INTEGER},
      CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      CONDITION_TEXT = #{record.conditionText,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update T_BROADCAST_STATISTIC_INCOME_DAY
    set ID = #{record.id,jdbcType=INTEGER},
      STATISTICS_TYPE = #{record.statisticsType,jdbcType=INTEGER},
      STATISTICS_TIME = #{record.statisticsTime,jdbcType=VARCHAR},
      USER_ID = #{record.userId,jdbcType=INTEGER},
      USER_NAME = #{record.userName,jdbcType=VARCHAR},
      TEAM_ID = #{record.teamId,jdbcType=INTEGER},
      TEAM_NAME = #{record.teamName,jdbcType=VARCHAR},
      DEPT_ID = #{record.deptId,jdbcType=INTEGER},
      DEPT_NAME = #{record.deptName,jdbcType=VARCHAR},
      AMOUNT = #{record.amount,jdbcType=DECIMAL},
      IS_DEL = #{record.isDel,jdbcType=INTEGER},
      CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update T_BROADCAST_STATISTIC_INCOME_DAY
    <set>
      <if test="statisticsType != null">
        STATISTICS_TYPE = #{statisticsType,jdbcType=INTEGER},
      </if>
      <if test="statisticsTime != null">
        STATISTICS_TIME = #{statisticsTime,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="teamId != null">
        TEAM_ID = #{teamId,jdbcType=INTEGER},
      </if>
      <if test="teamName != null">
        TEAM_NAME = #{teamName,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        DEPT_ID = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="deptName != null">
        DEPT_NAME = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="conditionText != null">
        CONDITION_TEXT = #{conditionText,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update T_BROADCAST_STATISTIC_INCOME_DAY
    set STATISTICS_TYPE = #{statisticsType,jdbcType=INTEGER},
      STATISTICS_TIME = #{statisticsTime,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=INTEGER},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      TEAM_ID = #{teamId,jdbcType=INTEGER},
      TEAM_NAME = #{teamName,jdbcType=VARCHAR},
      DEPT_ID = #{deptId,jdbcType=INTEGER},
      DEPT_NAME = #{deptName,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      IS_DEL = #{isDel,jdbcType=INTEGER},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CONDITION_TEXT = #{conditionText,jdbcType=LONGVARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update T_BROADCAST_STATISTIC_INCOME_DAY
    set STATISTICS_TYPE = #{statisticsType,jdbcType=INTEGER},
      STATISTICS_TIME = #{statisticsTime,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=INTEGER},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      TEAM_ID = #{teamId,jdbcType=INTEGER},
      TEAM_NAME = #{teamName,jdbcType=VARCHAR},
      DEPT_ID = #{deptId,jdbcType=INTEGER},
      DEPT_NAME = #{deptName,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      IS_DEL = #{isDel,jdbcType=INTEGER},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  
  
  
  <insert id="batchInsert" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayWithBLOBs">
  <foreach collection ="broadcastStatisticIncomeDayList" item="item" index= "index" separator =";">
    insert into T_BROADCAST_STATISTIC_INCOME_DAY
	    <trim prefix="(" suffix=")" suffixOverrides=",">
	      <if test="item.statisticsType != null">
	        STATISTICS_TYPE,
	      </if>
	      <if test="item.statisticsTime != null">
	        STATISTICS_TIME,
	      </if>
	      <if test="item.userId != null">
	        USER_ID,
	      </if>
	      <if test="item.userName != null">
	        USER_NAME,
	      </if>
	      <if test="item.teamId != null">
	        TEAM_ID,
	      </if>
	      <if test="item.teamName != null">
	        TEAM_NAME,
	      </if>
	      <if test="item.deptId != null">
	        DEPT_ID,
	      </if>
	      <if test="item.deptName != null">
	        DEPT_NAME,
	      </if>
	      <if test="item.amount != null">
	        AMOUNT,
	      </if>
	      <if test="item.isDel != null">
	        IS_DEL,
	      </if>
	      <if test="item.createTime != null">
	        CREATE_TIME,
	      </if>
	      <if test="item.updateTime != null">
	        UPDATE_TIME,
	      </if>
	      <if test="item.conditionText != null">
	        CONDITION_TEXT,
	      </if>
	    </trim>
	    <trim prefix="values (" suffix=")" suffixOverrides=",">
	      <if test="item.statisticsType != null">
	        #{item.statisticsType,jdbcType=INTEGER},
	      </if>
	      <if test="item.statisticsTime != null">
	        #{item.statisticsTime,jdbcType=VARCHAR},
	      </if>
	      <if test="item.userId != null">
	        #{item.userId,jdbcType=INTEGER},
	      </if>
	      <if test="item.userName != null">
	        #{item.userName,jdbcType=VARCHAR},
	      </if>
	      <if test="item.teamId != null">
	        #{item.teamId,jdbcType=INTEGER},
	      </if>
	      <if test="item.teamName != null">
	        #{item.teamName,jdbcType=VARCHAR},
	      </if>
	      <if test="item.deptId != null">
	        #{item.deptId,jdbcType=INTEGER},
	      </if>
	      <if test="item.deptName != null">
	        #{item.deptName,jdbcType=VARCHAR},
	      </if>
	      <if test="item.amount != null">
	        #{item.amount,jdbcType=DECIMAL},
	      </if>
	      <if test="item.isDel != null">
	        #{item.isDel,jdbcType=INTEGER},
	      </if>
	      <if test="item.createTime != null">
	        #{item.createTime,jdbcType=TIMESTAMP},
	      </if>
	      <if test="item.updateTime != null">
	        #{item.updateTime,jdbcType=TIMESTAMP},
	      </if>
	      <if test="item.conditionText != null">
	        #{item.conditionText,jdbcType=LONGVARCHAR},
	      </if>
	    </trim>
    </foreach>
  </insert>
  
</mapper>