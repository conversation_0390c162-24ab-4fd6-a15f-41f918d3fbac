<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>线索列表</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/records.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/businessLeads.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <!--隐藏的登录用户信息 -->
    <jsp:include page="../common/head_import.jsp"></jsp:include>

    <div class="page-wrap" id="page-container">
        <page-header :side="true"></page-header>
        <div class="page-container">
            <div class="page-main">
                <template v-if="isFromChance">
                    <div class="list-title-btns">
                        <div class="list-title-btn-item active">线索</div>
                        <a class="list-title-btn-item" @click="triggerTabChange(2)">商机</a>
                    </div>
                    <div class="list-top-option">
                        <ui-button type="primary" icon="icon-add" @click="gotoChanceAdd">新建线索/商机</ui-button>
                    </div>
                </template>
                <template v-else>
                    <div class="list-title">线索</div>
                    <div class="list-top-option">
                        <ui-button type="primary" icon="icon-add" @click="gotoLeadsAdd">新建</ui-button>
                    </div>
                </template>
                <ui-search-container ref="listContainer" list-name="01" :default-tab="defaultTab" :headers="tableHeaders" url="/crm/businessLeads/profile/page" :search-params="searchParams" @call="handlerCallNumber" @afterreset="initAddTime">
                    <template v-slot:filter-list>
                        <ui-search-item label="线索编号" :lock="true">
                            <ui-input v-model="searchParams.leadsNo"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="线索状态">
                            <ui-select :data="leadsStatusList"  multiple-type="fixed" placeholder="全部" v-model="searchParams.followStatusList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="来源">
                            <ui-select placeholder="全部" :data="sourceList" multiple-type="fixed" v-model="searchParams.clueTypeList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="归属销售">
                            <ui-select :remote="true" :avatar="true" multiple-type="fixed" :default-multi="searchParams.belongerMultiItems" placeholder="全部" v-model="searchParams.belongerIdList" clearable :remote-info="belongerListRemoteInfo" @change="remoteSelectChange('belongerMultiItems', $event)"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="销售部门">
                            <ui-cascader-new :multi="true" v-model="searchParams.organizationIdValues" :search="true" :list-data="departmentData" v-if="departmentData && departmentData.length" @change="handlerDepartmentChange"></ui-cascader-new>
                        </ui-search-item>
                        <!-- <ui-search-item label="销售区域">
                            <ui-cascader 
                                class="margin" 
                                :data="addressLv2Data" 
                                v-model="searchParams.salerAreaSelectedIds" 
                                clearable 
                                multiple
                                filterable
                                @change="handleFilterSalerAddressChange"
                                width="100%"
                                v-if="addressLv2Data && addressLv2Data.length"
                            ></ui-cascader>
                        </ui-search-item> -->
                        <ui-search-item label="协作人">
                            <ui-select :remote="true" :avatar="true" multiple-type="fixed" :default-multi="searchParams.shareUserMultiItems" @change="remoteSelectChange('shareUserMultiItems', $event)" placeholder="全部" v-model="searchParams.shareUserIdList" clearable :remote-info="shareUserListRemoteInfo"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="联系人">
                            <ui-input v-model="searchParams.contact"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="联系方式">
                            <ui-input v-model="searchParams.contactMerge" placeholder="固话、手机、其他联系方式"></ui-input>
                        </ui-search-item>
                        <!-- <ui-search-item label="固话">
                            <ui-input v-model="searchParams.telephone"></ui-input>
                        </ui-search-item> -->
                        <ui-search-item label="客户名称">
                            <ui-input v-model="searchParams.traderName"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="创建时间" :lock="true">
                            <ui-date-picker
                                v-model="searchParams.filterAddTime"
                                type="daterange"
                                start-placeholder="起始时间"
                                end-placeholder="截止时间"
                                @input="handlerFilterDateChange('AddTime', $event)"
                            ></ui-date-picker>
                        </ui-search-item>
                        <ui-search-item label="分配时间">
                            <ui-date-picker
                                v-model="searchParams.filterAssignTime"
                                type="daterange"
                                start-placeholder="起始时间"
                                end-placeholder="截止时间"
                                @input="handlerFilterDateChange('AssignTime', $event)"
                            ></ui-date-picker>
                        </ui-search-item>
                        <ui-search-item label="下次跟进日期">
                            <ui-date-picker
                                v-model="searchParams.filterNextCommunicationDate"
                                type="daterange"
                                start-placeholder="起始时间"
                                end-placeholder="截止时间"
                                @input="handlerFilterDateChange('nextCommunicationDate', $event)"
                            ></ui-date-picker>
                        </ui-search-item>
                        <ui-search-item label="最近跟进日期">
                            <ui-date-picker
                                v-model="searchParams.filterLastCommunicationTime"
                                type="daterange"
                                start-placeholder="起始时间"
                                end-placeholder="截止时间"
                                @change="handlerFilterDateChange('LastCommunicationTime', $event)"
                            ></ui-date-picker>
                            <!-- endLastCommunicationTime -->
                        </ui-search-item>
                        <ui-search-item label="线索渠道">
                            <ui-cascader 
                                class="margin" 
                                :data="sourceData" 
                                v-model="searchParams.sourceSelectedIds" 
                                clearable 
                                multiple
                                filterable
                                @change="handleFilterSourceChange"
                                width="100%"
                                v-if="sourceData && sourceData.length"
                            ></ui-cascader>
                        </ui-search-item>
                        <ui-search-item label="商品分类">
                            <ui-cascader 
                                class="margin" 
                                :data="allCategoryList" 
                                v-model="searchParams.selectedCategoryIdList" 
                                clearable 
                                multiple
                                filterable
                                @change="handleFilterCategoryChange"
                                width="100%"
                                v-if="allCategoryList && allCategoryList.length"
                            ></ui-cascader>
                        </ui-search-item>
                        <ui-search-item label="产品信息">
                            <ui-input v-model="searchParams.goodsInfo"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="地区">
                            <ui-cascader 
                                class="margin" 
                                :data="addressData" 
                                v-model="searchParams.areaSelectedIds" 
                                clearable 
                                multiple
                                filterable
                                @change="handleFilterAddressChange"
                                width="100%"
                                v-if="addressData && addressData.length"
                            ></ui-cascader>
                        </ui-search-item>
                        <ui-search-item label="询价行为">
                            <ui-select dictionary="391" multiple-type="fixed" placeholder="全部" v-model="searchParams.inquiryList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="咨询入口">
                            <ui-select dictionary="1404" multiple-type="fixed" placeholder="全部" v-model="searchParams.entrancesList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="功能">
                            <ui-select dictionary="1405" multiple-type="fixed" placeholder="全部" v-model="searchParams.functionsList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="创建人">
                            <ui-select :remote="true" :avatar="true" multiple-type="fixed" :default-multi="searchParams.creatorUserMultiItems" @change="remoteSelectChange('creatorUserMultiItems', $event)" placeholder="全部" v-model="searchParams.creatorUserIdList" clearable :remote-info="creatorUserIdListRemoteInfo"></ui-select>
                        </ui-search-item>
                    </template>
                    <template v-slot:list-button>
                        <ui-button @click="assignLeads" v-if="GLOBAL.auth('C0107')">分配线索</ui-button>
                        <ui-button @click="multiAddPartner">添加协作人</ui-button>
                    </template>
                    <template v-slot:leadsNo="{ row }">
                        <div class="td-link leadsno" @click="GLOBAL.link({name:'查看线索', url: '/crm/businessLeads/profile/detail?id=' + row.id})"><i class="tag-icon-hebing" title="线索合并" v-if="row.mergeStatus == 2"></i>{{row.leadsNo}}</div>
                    </template>
                    <template v-slot:followstatus="{ row }">{{GLOBAL.BUSINESSLEADS_STATUS[row.followStatus] || '-'}}</template>
                    <template v-slot:traderName="{ row }">
                        <template v-if="row.traderName">
                            <div class="td-tyc-wrap">
                                <div class="text-line-1" :title="row.traderName">
                                    <template v-if="row.traderNameLink">
                                        <div class="td-link" @click="GLOBAL.link({name:'客户详情', url: row.traderNameInnerLink, link: row.traderNameLink, nohost: true})">{{ row.traderName }}</div>
                                    </template>
                                    <template v-else>{{ row.traderName }}</template>
                                </div>
                                <span v-if="row.tycFlag == 'Y'" @click="openTyc(row.traderName)" class="tyc-icon"></span>
                            </div>
                        </template>
                        <template v-else>-</template>
                    </template>
                    <template v-slot:record="{ row }">
                        <div class="record-wrap" v-if="row.communicateRecordDto" :title="row.communicateRecordDto.contentSuffix">
                            <i class="vd-ui_icon icon-sms" @click="showRecordDialog(row)"></i>
                            <div class="record-txt text-line-1" >{{ row.communicateRecordDto.contentSuffix || '-' }}</div>
                        </div>
                        <template v-else>-</template>
                    </template>
                    <template v-slot:content="{ row }">
                        <ui-category :list="row.categoryList || []"></ui-category>
                    </template>
                    <template v-slot:address="{ row }">
                        <div class="text-line-1">{{ row.province }}{{ row.city ? '-' +  row.city : '' }}{{ row.county ? '-' + row.county : '' }}</div>
                    </template>
                    <template v-slot:option="{ row }">
                        <div class="option-wrap">
                            <a @click="assignLeadsItem(row)" class="table-edit" v-if="GLOBAL.auth('C0107') && row.followStatus != 3 && row.followStatus != 4">分配线索</a>
                            <!-- <a class="table-edit" @click="showShareList(row)" v-if="USERINFO.userId == row.belongerId">设置协作人</a>
                            <a class="table-edit disabled" title="仅限归属销售可以操作" v-else>设置协作人</a> -->
                        </div>
                    </template>
                </ui-search-container>
            </div>
        </div>
        <ui-dialog
            :visible.sync="isShowBelongerDialog"
            title="分配线索"
            width="720px"
        >
            <div class="form-wrap label-width-2" v-if="isShowBelongerDialog">
                <ui-form-item label="新归属销售" :must="true">
                    <div class="ui-col-4">
                        <ui-select :remote="true" :avatar="true" placeholder="请输入英文名搜索选择归属销售" v-model="belonger" clearable :remote-info="allUserRemoteInfo" valid="belongerForm_belonger"></ui-select>
                    </div>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer">
                    <ui-button @click="setBelonger" type="primary">保存</ui-button>
                    <ui-button @click="isShowBelongerDialog=false" class="close">取消</ui-button>
                </div>
            </template>
        </ui-dialog>
        <ui-dialog
            :visible.sync="isShowShareAddDialog"
            title="添加协作人"
            width="720px"
        >
            <div class="form-wrap label-width-2" v-if="isShowShareAddDialog">
                <ui-form-item label="协作人" :must="true">
                    <div class="ui-col-4">
                        <ui-select :remote="true" :avatar="true" placeholder="请输入并选择协作人" v-model="shareUserId" clearable :remote-info="allShardUserRemoteInfo" valid="shareDlgForm_shareUserId" @change="handlerShareSelect"></ui-select>
                    </div>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer">
                    <ui-button @click="addShareUser" type="primary">保存</ui-button>
                    <ui-button @click="isShowShareAddDialog=false" class="close">取消</ui-button>
                </div>
            </template>
        </ui-dialog>
        <ui-dialog
            :visible.sync="isShowShareDialog"
            title="协作人"
            width="720px"
        >
            <div class="dlg-share-wrap">
                <div class="dlg-share-btn">
                    <ui-button type="primary" @click="showShareAddDialog">添加协作人</ui-button>
                </div>
                <div class="dlg-share-list">
                    <ui-table v-if="!isShareLoading" container-height="470px" :width-border="true" :auto-scroll="false" :headers="shareListHeaders" :list="sharelist">
                        <template v-slot:option="{ row }">
                            <div class="option-wrap">
                                <a class="table-edit" @click="removeShareUser(row)" class="table-edit">移除</a>
                            </div>
                        </template>
                    </ui-table>
                </div>
            </div>
        </ui-dialog>
        <follow-record-list-dialog ref="followRecordListDialog" :communicate-type="4109" @addrecord="handlerRecordAdd"></follow-record-list-dialog>
        <!-- 天眼查详情 -->
        <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
        <!-- 列表操作提示 -->
        <ui-list-option-tip></ui-list-option-tip>
        <!-- 添加协作人弹层 -->
        <partner-create-dialog ref="partnerCreateDialog" :success-fun="handlerAddPartner"></partner-create-dialog>
    </div>
    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/vuedraggable.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/components/business/records.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 协作人 -->
    <script src="/static/js/common/components/business/partner.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/pages/businessleadsList.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
