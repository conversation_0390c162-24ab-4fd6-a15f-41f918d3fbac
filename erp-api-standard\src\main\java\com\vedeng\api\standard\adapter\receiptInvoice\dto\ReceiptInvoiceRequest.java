package com.vedeng.api.standard.adapter.receiptInvoice.dto;

import com.vedeng.erp.finance.dto.InvoiceGoodsDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 付款申请请求对象
 */
@Data
public class ReceiptInvoiceRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;

    private String invoiceNo;
    
    private String invoiceHref;
    
    private String buyOrderNo;
    
    private List<InvoiceGoodsDto> invoiceGoods;
} 
