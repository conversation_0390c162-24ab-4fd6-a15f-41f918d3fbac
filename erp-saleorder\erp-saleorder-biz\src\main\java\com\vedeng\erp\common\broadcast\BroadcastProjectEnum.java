package com.vedeng.erp.common.broadcast;

/**
 * 播报项目枚举
 * @ClassName:  BroadcastProjectEnum   
 * @author: <PERSON><PERSON>yang
 * @date:   2025年6月11日 上午9:16:03    
 * @Copyright:
 */
public enum BroadcastProjectEnum {

	STATISTICS_DAY(1,"日到款"),
	STATISTICS_WEEK(2,"周到款"),
	STATISTICS_MONTH(3,"月到款"),
	STATISTICS_MONTH_AED(4,"月度AED出库量"),
	STATISTICS_MONTH_VD(5,"月度自由品牌出库金额"),
	STATISTICS_USER_DEFINE(6,"自定义播报");

	private BroadcastProjectEnum(Integer project, String projectName) {
		this.project = project;
		this.projectName = projectName;
	}
	
	private Integer project;

    private String projectName;

	public Integer getProject() {
		return project;
	}

	public String getProjectName() {
		return projectName;
	}

	
}
