// 排除数字0 获取值
const getVal = (val) => {
    if (val == 0) return val;
    if (val) {
        return val;
    } else {
        return '';
    }
};

void function () {
    new Vue({
        el: '#page-container',
        data: {
            isEdit: false, // true:编辑状态  false:新建
            businessLeadsId: '', // 线索id - 仅编辑
            canAjax: true,
            fixedTop: layout_hidden_value ? 0 : 50, //表头悬浮高度，根据有没有头部判断
            pageLoading: true, // 页面加载状态

            /* Card 1 */
            clueType: 391, // 线索类型 391:总机线索 394:自有商城线索
            clueTypeName: '总机线索',
            // 企微提醒
            radioList1: [
                { label: "提醒", value: 'Y' },
                { label: "不提醒", value: 'N' },
            ],
            sendVx: 'Y', // 企微提醒 默认提醒
            // 询价行为
            SysOptions: [], 
            inquiry: '',
            // 线索渠道
            Sources: [],
            source: '', // 渠道类型
            SourceNames: [],
            communication: '', // 渠道名称

            /* Card 2 */
            CategoryContent: '', // 三级分类
            goodsInfo: '', // 产品信息
            // 客户名称
            traderName: '',
            traderId: '', // 客户Id
            tycFlag: '', // 天眼查标识
            // 联系人
            contact: '',
            traderContactId: '',
            // 号码
            phone: '',
            phoneMsg: '', 
            telephone: '',
            telephoneMsg: '',
            otherContactInfo: '', // 其他联系方式
            // 省市区
            addressData: [],
            area: [],
            areaLabel: [],
            // 归属销售
            belongerDisabled: false, // 归属销售禁用
            belonger: '',
            belongerId: '', // 归属销售id
            allUserRemoteInfo: {
                url: '/crm/user/profile/getAllUserInfo',
                paramsUrl: 'data.list',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            remark: '', // 备注
            aiValue: {
                keywords: [],
                categoryIds: []
            },
        },
        async created () {
            GLOBAL.showGlobalLoading();
            this.businessLeadsId = document.getElementById('businessLeadsId').value || '';

            // 线索渠道
            const {data: result} = await this.$axios.post('/crm/businessLeads/profile/getCascaderChannelOptions');
            if (result.success) {
                this.Sources = result.data || [];
            }

            // 询价行为
            const {data: result1} = await this.$axios.post(`/crm/sysOption/public/getByParentId?parentId=391`);
            if (result1.success) {
                let arr = result1.data || [];
                this.SysOptions = arr.map(m1 =>{
                    return {
                        label: m1.title,
                        value: m1.sysOptionDefinitionId
                    }
                })
                console.log('询价行为字典:', this.SysOptions)
            }

            // 线索渠道
            const {data: result2} = await this.$axios.post('/crm/businessLeads/profile/getCascaderChannelOptions');
            if (result2.success) {
                let res = result2.data || [];
                res.forEach(item => {
                    if (!(item.children && item.children.length)) {
                        item.disabled = true;
                    }
                })
                this.Sources = res;
            }

            // 地址数据
            const {data: result3} = await this.$axios.get('/crm/common/profile/getRegionAll');
            console.log('result3:', result3);
            if(result3.success) {
                this.addressData = result3.data || [];
            }

            // 编辑
            if (this.businessLeadsId) {
                this.isEdit = true;
                const {data: resData} = await this.$axios.post(`/crm/businessLeads/profile/getOne?id=${this.businessLeadsId}`);
                if (resData.success) {
                    this.initForm(resData.data);
                }
            } else {
                this.pageLoading = false;

                // im带参
                this.imDefaultQuery();
                GLOBAL.hideGlobalLoading();
            }
        },
        mounted() {
            // 归属销售valid初始化
            this.$form && this.$form.rules({
                inquiry: {
                    required: '请选择询价行为'
                },
                source: {
                    required: '请选择渠道类型'
                },
                communication: {
                    required: '请选择渠道名称'
                },
                // CategoryContent: {
                //     required: '请选择三级分类'
                // },
                goodsInfo: {
                    required: '请输入产品信息'
                },
                // traderName: {
                //     required: '请输入客户名称'
                // },
                area: {
                    required: '请选择省市区'
                },
                belongerId: {
                    required: '请选择归属销售'
                },
            }, 'editBusinessleads', this);
        },
        methods: {
            // 值是否为空 排除0的情况
            
            // 回显表单 - 仅编辑
            initForm (res) {
                console.log('回显 res:', res);
                // card1 ---
                this.clueType = res.clueType || 391;
                this.clueTypeName = res.clueTypeName || '-';
                this.sendVx = getVal(res.sendVx);
                this.inquiry = getVal(res.inquiry);
                this.source = getVal(res.source);
                // 渠道名称列表数据
                if (this.source) {
                    this.Sources.forEach(item => {
                        if (item.value == this.source) {
                            this.SourceNames = item.children || [];
                        }
                    });
                }
                this.communication = getVal(res.communication);

                // card2 ---
                // this.CategoryContent = res.content || '';
                this.goodsInfo = res.goodsInfo || '';
                this.traderName = res.traderName || '';
                this.traderId = res.traderId || '';
                this.tycFlag = res.tycFlag || 'N';
                this.contact = res.contact || '';
                this.phone = res.phone || '';
                this.telephone = res.telephone || '';
                this.otherContactInfo = res.otherContactInfo || '';
                if (res.provinceId) {
                    this.area.push(res.provinceId);
                    this.areaLabel.push(res.province);
                }
                if (res.cityId) {
                    this.area.push(res.cityId)
                    this.areaLabel.push(res.city)
                }
                if (res.countyId) {
                    this.area.push(res.countyId)
                    this.areaLabel.push(res.county)
                }
                this.belongerId = res.belongerId || '';
                this.belonger = res.belonger || '';
                // 归属销售有值不可修改
                if (res.traderId && this.belongerId) {
                    this.belongerDisabled = true;
                }
                this.remark = res.remark || '';

                if (!layout_hidden_value) {
                    document.title = (this.traderName || '') + '线索编辑';
                }
                this.pageLoading = false;
                GLOBAL.hideGlobalLoading();
            },
            // im入参处理 - im仅在新增时带参
            imDefaultQuery () {
                this.traderId = document.getElementById('traderId').value || '';
                this.traderName = document.getElementById('traderName').value || '';
                this.phone = document.getElementById('mobile').value || '';
                this.inquiry = document.getElementById('inquiryId').value || '';
                this.source = document.getElementById('sourceId').value || '';
                if (this.source) {
                    // 渠道名称列表数据
                    this.Sources.forEach(item => {
                        if (item.value == this.source) {
                            this.SourceNames = item.children || [];
                        }
                    });
                }
                this.communication = document.getElementById('communicationId').value || ''; // 渠道名称
                this.belongerId = document.getElementById('belongerId').value || '';
                this.belonger = document.getElementById('belonger').value || '';
                // this.belongerPic = document.getElementById('belongerPic').value || '';
            },
            
            handlerAiCategoryChange(data) {
                console.log(data)

                this.aiValue = data;
            },
            triggerAiInfo() {
                this.$refs.aicategory && this.$refs.aicategory.getAiInfo(this.goodsInfo);
            },
            // 渠道类型
            changeSource (val) {
                console.log('changeSource:', val);
                this.SourceNames = val.children || [];
                this.communication = '';
            },
            // // 三级分类
            // handlerCategory (val) {
            //     console.log('handlerCategory:', val);
            //     console.log('CategoryContent:', this.CategoryContent);
            // },
            // 客户名称
            handlerTrader (data) {
                console.log('handler Trader:', this.traderName, data);
                this.traderId = data.traderId || '';
                this.tycFlag = data.tycFlag || 'N';

                if (data.saleId) { // 有归属销售带入归属销售
                    this.belonger = data.saleName;
                    this.belongerId = data.saleId;
                    this.belongerDisabled = true;
                } else { // 没有归属销售
                    // 归属销售已禁用 - 则上次是选择带入的，可清空
                    if (this.belongerDisabled) {
                        this.belonger = '';
                        this.belongerId = '';
                        this.belongerDisabled = false;
                    }
                }
            },
            // 手机
            handlerPhone (val) {
                console.log('handlerPhone:', this.phone, val);
                console.log('traderId:', this.traderId);

                if (val.choosed) {
                    this.contact = val.traderContactName || '';
                    this.traderContactId = val.traderContactId || '';
                } else {
                    this.traderContactId ='';
                }

                this.phone_Blur();
            },
            // 其他联系方式
            handlerMoreContact () {
                if (this.phone || this.telephone || this.otherContactInfo) {
                    if (this.phoneMsg == '手机、固话和其他联系方式至少填写一项') {
                        this.phoneMsg = '';
                    }
                }
            },
            // 联系人
            handlerContact (val) {
                if (val.choosed) {
                    this.traderContactId = val.traderContactId || '';
                    this.phone = val.mobile || '';

                    if (this.phone) {
                        this.phone_Blur();
                    }
                } else {
                    this.traderContactId = '';
                }
            },
            // 地区
            handleArea (val) {
                console.log('handleArea:', val);
                let area = [];
                areaLabel = [];
                val.forEach(item => {
                    if (item.value) {
                        area.push(item.value);
                        areaLabel.push(item.label);
                    }
                })
                console.log(area)
                this.area = area;
                this.areaLabel = areaLabel;
            },
            // 归属销售
            handlerBelonger (val) {
                this.belonger = val.selected.label;
            },

            // 号码重复校验
            async telRepeat (key) {
                /* return值： '报错文案'
                 *     true:  不可继续新建
                 *     false: 可新建
                */ 
                let reqQuery = {}
                key == 'phone' && (reqQuery['phone'] = this.phone);
                key == 'telephone' && (reqQuery['telephone'] = this.telephone);
                if (this.isEdit) {
                    reqQuery['id'] = this.businessLeadsId;
                }
                let {data} = await this.$axios.post('/crm/businessLeads/profile/getLeadsListByDto', reqQuery);
                if (data.code == 0) {
                    return '';
                } else {
                    return data.message || '该号码今日已存在线索';
                }
            },

            async phone_Blur () {
                if (this.phone) {
                    if (this.phone.length != 11) {
                        this.phoneMsg = '请输入11位手机号码';
                        return false;
                    } else {
                        let valid_phone = await this.telRepeat('phone');
                        if (valid_phone) {
                            this.phoneMsg = valid_phone;
                        } else {
                            this.phoneMsg = '';
                        }
                    }
                } else {
                    if (!(this.phone || this.telephone || this.otherContactInfo)) {
                        this.phoneMsg = '手机、固话和其他联系方式至少填写一项'
                    } else {
                        this.phoneMsg = '';
                    }
                }
            },
            async telephone_Blur () {
                if (this.phone || this.telephone || this.otherContactInfo) {
                    if (this.phoneMsg == '手机、固话和其他联系方式至少填写一项') {
                        this.phoneMsg = '';
                    }
                }

                if (this.telephone) {
                    let valid_phone = await this.telRepeat('telephone');
                    if (valid_phone) {
                        this.telephoneMsg = valid_phone;
                    } else {
                        this.telephoneMsg = '';
                    }
                }
            },
            // 验证表单
            async checkForm() {
                let error = 0;
                if (!this.$form.validForm('editBusinessleads')) {
                    error = 1;
                }
                if (!(this.phone || this.telephone || this.otherContactInfo)) {
                    this.phoneMsg = '手机、固话和其他联系方式至少填写一项';
                    error = 2;
                }

                if (this.phone) {
                    if (this.phone.length != 11) {
                        this.phoneMsg = '请输入11位手机号码';
                        error = 31;
                        return false;
                    } else {
                        let validPhone = await this.telRepeat('phone');
                        if (validPhone) {
                            this.phoneMsg = validPhone;
                            error = 32;
                        }
                    }
                }

                if (this.telephone) {
                    let validTelephone = await this.telRepeat('telephone');
                    if (validTelephone) {
                        this.telephoneMsg = validTelephone;
                        error = 4;
                    }
                }

                console.log('error:', error);
                if (error) {
                    return false;
                }

                this.phoneMsg = '';
                return true;
            },

            // 提交
            async submit() {
                let valided = await this.checkForm();
                if (!valided) return;

                let reqData = {
                    clueType: this.clueType,
                    sendVx: this.sendVx,
                    inquiry: this.inquiry,
                    source: this.source,
                    communication: this.communication,
                    // content: this.CategoryContent,
                    goodsInfo: this.goodsInfo,
                    traderName: this.traderName,
                    traderId: this.traderId,
                    tycFlag: this.tycFlag  || 'N',
                    contact: this.contact,
                    traderContactId: this.traderContactId,
                    phone: this.phone,
                    telephone: this.telephone,
                    otherContactInfo: this.otherContactInfo,
                    belongerId: this.belongerId, // 归属销售Id
                    belonger: this.belonger, // 归属销售
                    remark: this.remark,

                    provinceId: this.area[0] && this.area[0] || '', // 省ID
                    cityId: this.area[1] && this.area[1] || '', // 市ID
                    countyId: this.area[2] && this.area[2] || '', // 区ID
                    province: this.areaLabel[0] && this.areaLabel[0] || '', // 省
                    city: this.areaLabel[1] && this.areaLabel[1] || '', // 市
                    county: this.areaLabel[2] && this.areaLabel[2] || '', // 区
                    keywords: this.aiValue.keywords.join(','),
                    categoryIds: this.aiValue.categoryIds.join(','),
                }

                if (this.canAjax) {
                    this.canAjax = false;

                     //将分类记录历史
                     let histroyList = JSON.parse(localStorage.getItem('crm_category_history') || '[]')
                     this.CategoryContent.split('&&').forEach(item => {
                         if(histroyList.indexOf(item) === -1) {
                             histroyList = [item].concat(histroyList);
                         }
                     })
 
                     histroyList = histroyList.splice(0, 10);
                     localStorage.setItem('crm_category_history', JSON.stringify(histroyList));
 

                    if (this.isEdit) {
                        this.axiosUpdate(reqData);
                    } else {
                        this.axiosCreated(reqData);
                    }
                }
            },

            // 编辑
            async axiosUpdate (reqData) {
                reqData['id'] = this.businessLeadsId;
                const { data } = await this.$axios.post(`/crm/businessLeads/profile/update`, reqData);
                if (data.success) {
                    this.$message.success("保存成功");

                    setTimeout(()=> {
                        window.location.href = `/crm/businessLeads/profile/detail?id=${this.businessLeadsId}`;
                    }, 2000)
                } else {
                    this.$message.warn(data.message || '网络异常');
                    this.canAjax = true;
                }
            },
            // 新建
            async axiosCreated (reqData) {
                const { data } = await this.$axios.post(`/crm/businessLeads/profile/add`, reqData);
                if (data.success) {
                    this.$message.success("创建成功");
                    setTimeout(()=> {
                        window.location.href = `/crm/businessLeads/profile/detail?id=${data.data}`;
                    }, 2000)
                } else {
                    this.$message.warn(data.message || '网络异常');
                    this.canAjax = true;
                }
            }
        }
    })
}.call(this);