package com.vedeng.api.standard.core;

import com.vedeng.api.standard.core.exception.ApiStandardException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 抽象服务适配器基类
 * 提供通用的操作调度机制
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-30
 */
public abstract class AbstractServiceAdapter implements ServiceAdapter {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * Operation handler mapping
     * key: operation name (lowercase)
     * value: operation handler function
     */
    private final Map<String, Function<ApiRequest, Object>> operationHandlers = new HashMap<>();

    /**
     * Initialize operation handlers
     */
    @PostConstruct
    private void initOperationHandlers() {
        try {
            registerOperationHandlers();
            logger.info("{} operation handlers initialized, supported operations: {}",
                    this.getClass().getSimpleName(), operationHandlers.keySet());
        } catch (Exception e) {
            logger.error("Failed to initialize operation handlers: {}", this.getClass().getSimpleName(), e);
            throw ApiStandardException.configurationError("Service adapter initialization failed", e);
        }
    }

    /**
     * 子类实现此方法注册操作处理器
     * <p>
     * 示例：
     * registerHandler("create", this::executeCreateOperation);
     * registerHandler("update", this::executeUpdateOperation);
     */
    protected abstract void registerOperationHandlers();

    /**
     * 注册操作处理器
     *
     * @param action  操作名称
     * @param handler 处理函数
     */
    protected final void registerHandler(String action, Function<ApiRequest, Object> handler) {
        if (action == null || action.trim().isEmpty()) {
            throw new IllegalArgumentException("操作名称不能为空");
        }

        if (handler == null) {
            throw new IllegalArgumentException("操作处理器不能为空");
        }

        String normalizedAction = action.trim().toLowerCase();

        if (operationHandlers.containsKey(normalizedAction)) {
            logger.warn("操作{}的处理器已存在，将被覆盖", normalizedAction);
        }

        operationHandlers.put(normalizedAction, handler);
        logger.debug("注册操作处理器: {}", normalizedAction);
    }

    /**
     * 注册操作处理器（支持异常处理的版本）
     *
     * @param action  操作名称
     * @param handler 处理函数（可能抛出异常）
     */
    protected final void registerThrowingHandler(String action, ThrowingFunction<ApiRequest, Object> handler) {
        registerHandler(action, request -> {
            try {
                return handler.apply(request);
            } catch (Exception e) {
                if (e instanceof RuntimeException) {
                    throw (RuntimeException) e;
                }
                throw ApiStandardException.serviceExecutionError("操作执行失败: " + action, e);
            }
        });
    }

    @Override
    public final Object execute(String action, ApiRequest request) throws Exception {
        if (action == null || action.trim().isEmpty()) {
            throw new IllegalArgumentException("操作名称不能为空");
        }

        if (request == null) {
            throw new IllegalArgumentException("请求对象不能为空");
        }

        String normalizedAction = action.trim().toLowerCase();
        Function<ApiRequest, Object> handler = operationHandlers.get(normalizedAction);

        if (handler == null) {
            throw new UnsupportedOperationException("不支持的操作: " + action);
        }

        logger.debug("执行操作: action={}, requestId={}", action, request.getRequestId());

        try {
            // 预处理
            preProcess(action, request);

            // 执行操作
            Object result = handler.apply(request);

            // 后处理
            return postProcess(action, request, result);

        } catch (Exception e) {
            logger.error("执行操作失败: action={}, requestId={}, error={}",
                    action, request.getRequestId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public final String[] getSupportedActions() {
        return operationHandlers.keySet().toArray(new String[0]);
    }

    @Override
    public final boolean supportsAction(String action) {
        if (action == null || action.trim().isEmpty()) {
            return false;
        }
        return operationHandlers.containsKey(action.trim().toLowerCase());
    }

    /**
     * 获取操作处理器数量
     *
     * @return 处理器数量
     */
    protected final int getHandlerCount() {
        return operationHandlers.size();
    }

    /**
     * 检查是否已注册指定操作
     *
     * @param action 操作名称
     * @return 是否已注册
     */
    protected final boolean isHandlerRegistered(String action) {
        if (action == null || action.trim().isEmpty()) {
            return false;
        }
        return operationHandlers.containsKey(action.trim().toLowerCase());
    }

    /**
     * 函数式接口，支持抛出异常的函数
     */
    @FunctionalInterface
    protected interface ThrowingFunction<T, R> {
        R apply(T t) throws Exception;
    }
}
