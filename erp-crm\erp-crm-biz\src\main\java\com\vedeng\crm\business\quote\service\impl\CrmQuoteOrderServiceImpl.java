package com.vedeng.crm.business.quote.service.impl;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.crm.business.business.domain.entity.CrmBusinessChanceEntity;
import com.vedeng.crm.business.quote.domain.dto.CrmQuoteOrderDto;
import com.vedeng.crm.business.quote.domain.dto.CrmQuoteShareDto;
import com.vedeng.crm.business.quote.domain.dto.QuoteOrderGoodsExportVo;
import com.vedeng.crm.business.quote.domain.dto.QuoteValidRequestDto;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteOrderEntity;
import com.vedeng.crm.business.quote.mapper.CrmQuoteorderMapper;
import com.vedeng.crm.business.quote.mapstruct.CrmQuoteConvertor;
import com.vedeng.crm.business.quote.service.CrmQuoteOrderService;
import com.vedeng.erp.system.domain.entity.VerifiesInfoEntity;
import com.vedeng.erp.system.mapper.VerifiesInfoEntityMapper;
import com.vedeng.erp.trader.domain.entity.QuoteorderEntity;
import com.vedeng.goods.domain.entity.GoodsAttachmentEntity;
import com.vedeng.goods.mapper.CoreSkuMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class CrmQuoteOrderServiceImpl implements CrmQuoteOrderService {
    @Autowired
    private CrmQuoteorderMapper crmQuoteOrderMapper;

    @Autowired
    private CrmQuoteConvertor crmQuoteConvertor;

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Override
    public CrmQuoteOrderEntity selectByPrimaryKey(Integer quoteorderId){
        return crmQuoteOrderMapper.selectByPrimaryKey(quoteorderId);
    }


    @Override
    public CrmQuoteOrderDto selectOne(Integer quoteorderId) {
        if (Objects.isNull(quoteorderId)){
            throw new ServiceException("quoteorderId不能为空");
        }
        CrmQuoteOrderEntity crmQuoteOrderEntity = crmQuoteOrderMapper.selectByPrimaryKey(quoteorderId);
        if (Objects.isNull(crmQuoteOrderEntity)){
            throw new ServiceException("报价单不存在" + quoteorderId);
        }
        CrmQuoteOrderDto dto = crmQuoteConvertor.toDto(crmQuoteOrderEntity);
        return dto;
    }

    @Override
    public CrmQuoteOrderDto selectByBusinessChanceId(Integer businessChanceId) {
        if (Objects.isNull(businessChanceId)){
            throw new ServiceException("businessChanceId不能为空");
        }
        CrmQuoteOrderEntity crmQuoteOrderEntity = crmQuoteOrderMapper.selectByBusinessChanceId(businessChanceId);
        if (Objects.isNull(crmQuoteOrderEntity)){
            return null;
        }
        CrmQuoteOrderDto dto = crmQuoteConvertor.toDto(crmQuoteOrderEntity);
        return dto;
    }

    @Override
    public List<QuoteOrderGoodsExportVo> selectQuoteOrderGoodsExportVo(Integer quoteorderId,Boolean isNeeds) {
        List<QuoteOrderGoodsExportVo> quoteOrderGoodsExportVos = new ArrayList<>();
        if (isNeeds){
            quoteOrderGoodsExportVos = crmQuoteOrderMapper.selectQuoteOrderGoodsExportVo(quoteorderId,isNeeds);
        }else {
            quoteOrderGoodsExportVos = crmQuoteOrderMapper.selectQuoteOrderGoodsExportVo(quoteorderId,isNeeds);
        }

        for (QuoteOrderGoodsExportVo quoteOrderGoodsExportVo : quoteOrderGoodsExportVos) {
            GoodsAttachmentEntity goodsAttachment = coreSkuMapper.getGoodsAttachment(quoteOrderGoodsExportVo.getSkuId());
            if (Objects.nonNull(goodsAttachment)){
                quoteOrderGoodsExportVo.setPicture(goodsAttachment.getUrl());
            }
        }
        return quoteOrderGoodsExportVos;
    }

    @Autowired
    private VerifiesInfoEntityMapper verifiesInfoEntityMapper;

    @Override
    public int updateQuoteOrderValid(QuoteValidRequestDto quoteValidRequestDto, CurrentUser currentUser) {
        CrmQuoteOrderEntity crmQuoteOrderEntity = new CrmQuoteOrderEntity();
        crmQuoteOrderEntity.setQuoteorderId(quoteValidRequestDto.getQuoteorderId());
        crmQuoteOrderEntity.setUpdater(currentUser.getId());
        crmQuoteOrderEntity.setModTime(System.currentTimeMillis());
        crmQuoteOrderEntity.setValidStatus(quoteValidRequestDto.isValid() ? 1 : 0);
        crmQuoteOrderEntity.setValidTime(System.currentTimeMillis());

        int i = crmQuoteOrderMapper.updateByPrimaryKeySelective(crmQuoteOrderEntity);


        //原有的流程是审核通过即生效，CRM中，生效即审核通过，补充T_VERIFIES_INFO表的数据。
        //VERIFIES_INFO_ID,RELATE_TABLE,RELATE_TABLE_KEY,VERIFIES_TYPE,LAST_VERIFY_USERNAME,VERIFY_USERNAME,STATUS,ADD_TIME,MOD_TIME,VERIFYER_ID,APPLYER_ID
        //1910365,T_QUOTEORDER,485847,612,(null),Hilary.li,1,1727317818361,1727317818361,0,0
        //先按报价ID查询是否已经存在了
        VerifiesInfoEntity verifiesInfoEntity = new VerifiesInfoEntity();
        verifiesInfoEntity.setRelateTable("T_QUOTEORDER");
        verifiesInfoEntity.setRelateTableKey(quoteValidRequestDto.getQuoteorderId());
        VerifiesInfoEntity oldVerifiesInfoEntity = verifiesInfoEntityMapper.findOneByAll(verifiesInfoEntity);
        if(oldVerifiesInfoEntity!=null){
            oldVerifiesInfoEntity.setStatus(quoteValidRequestDto.isValid() ? 1 : 2);
            oldVerifiesInfoEntity.setVerifyUsername(currentUser.getUsername());
            oldVerifiesInfoEntity.setVerifyerId(currentUser.getId());
            oldVerifiesInfoEntity.setModTime(System.currentTimeMillis());
            verifiesInfoEntityMapper.updateByPrimaryKey(oldVerifiesInfoEntity);
        }else{
            verifiesInfoEntity.setVerifiesType(612);
            verifiesInfoEntity.setLastVerifyUsername(currentUser.getUsername());
            verifiesInfoEntity.setVerifyUsername(currentUser.getUsername());
            verifiesInfoEntity.setStatus(quoteValidRequestDto.isValid() ? 1 : 2);
            verifiesInfoEntity.setAddTime(System.currentTimeMillis());
            verifiesInfoEntity.setModTime(System.currentTimeMillis());
            verifiesInfoEntity.setVerifyerId(currentUser.getId());
            verifiesInfoEntity.setApplyerId(0);
            verifiesInfoEntityMapper.insert(verifiesInfoEntity);
        }
        log.info("更新报价单生效状态:{}",i);
        return i;
    }

    @Override
    public int updateQuoteOrder(CrmQuoteOrderDto crmQuoteOrderDto, CurrentUser currentUser) {
        CrmQuoteOrderEntity crmQuoteOrderEntity = crmQuoteConvertor.toEntity(crmQuoteOrderDto);
        crmQuoteOrderEntity.setUpdater(currentUser.getId());
        crmQuoteOrderEntity.setModTime(System.currentTimeMillis());
        int i = crmQuoteOrderMapper.updateByPrimaryKeySelective(crmQuoteOrderEntity);
        log.info("更新报价单:{}",i);
        return i;
    }

    @Override
    public int getAuthorizationSum(Integer quoteorderId, Integer applyStatus) {
        return crmQuoteOrderMapper.getAuthorizationSum(quoteorderId,applyStatus);
    }

    @Override
    public String selectBusinessChanceByQuoteorderId(Integer quoteorderId) {
        CrmBusinessChanceEntity crm = crmQuoteOrderMapper.selectBusinessChanceByQuoteorderId(quoteorderId);
        String businessChanceNo = Optional.ofNullable(crm).map(CrmBusinessChanceEntity::getBussinessChanceNo).orElse("");
        return businessChanceNo;
    }

    @Override
    public CrmBusinessChanceEntity selectBusinessChanceEntityByQuoteorderId(Integer quoteorderId) {
        CrmBusinessChanceEntity crmBusinessChanceEntity = crmQuoteOrderMapper.selectBusinessChanceByQuoteorderId(quoteorderId);
        return crmBusinessChanceEntity;
    }

    @Override
    public List<Map<String, Object>> checkSkuCheckStatusForQuoteShard(Integer quoteorderId) {
        return crmQuoteOrderMapper.checkQuoteOrderSkuIfCheckStatus3(quoteorderId);
    }

    @Override
    public CrmQuoteShareDto getQuoteShardInfoById(Integer quoteorderId) {

        CrmQuoteShareDto crmQuoteShareDto =  crmQuoteOrderMapper.getQuoteShardInfoById(quoteorderId);
        if(crmQuoteShareDto!= null && crmQuoteShareDto.getOnlineShareTime() == null){
            CrmQuoteOrderEntity crmQuoteOrderEntity =  new CrmQuoteOrderEntity();
            crmQuoteOrderEntity.setQuoteorderId(quoteorderId);
            crmQuoteOrderEntity.setOnlineShareTime(new Date());
            crmQuoteOrderMapper.updateByPrimaryKeySelective(crmQuoteOrderEntity);
        }
        return crmQuoteShareDto;
    }

    @Override
    public Integer getQuoteWithNoSkuInfoById(Integer quoteorderId) {
       return crmQuoteOrderMapper.getQuoteWithNoSkuInfoById(quoteorderId);
    }


	@Override
	public void updateQuoteOrderForBuildUserIds(CrmQuoteOrderDto updateOrderDto, CurrentUser currentUser) {
        CrmQuoteOrderEntity crmQuoteOrderEntity = crmQuoteConvertor.toEntity(updateOrderDto);
        crmQuoteOrderEntity.setUpdater(currentUser.getId());
        crmQuoteOrderEntity.setModTime(System.currentTimeMillis());
        int i = crmQuoteOrderMapper.updateQuoteOrderForBuildUserIds(crmQuoteOrderEntity);
        log.info("更新报价单:{}",i);
		
	}


}
