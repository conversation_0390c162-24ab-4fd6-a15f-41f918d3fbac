package com.vedeng.erp.broadcast.statistics.project;

import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticAedNumExample;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticAedNumWithBLOBs;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptRErpDeptMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastRAedUserMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastStatisticsMapper;
import com.vedeng.erp.common.broadcast.MessageSubjectEnum;
import com.vedeng.erp.common.broadcast.StatDateRangeEnum;
import com.vedeng.erp.common.broadcast.config.BroadcastDeptConfigStatistics;
import com.vedeng.erp.common.broadcast.config.BroadcastGlobalConfigStatistics;
import com.vedeng.erp.common.broadcast.config.GlobalConfig;
import com.vedeng.erp.common.broadcast.constants.BroadcastRedisKey;
import com.vedeng.erp.common.broadcast.constants.MessageTemplate;
import com.vedeng.erp.common.broadcast.param.QwMessageParam;
import com.vedeng.erp.common.broadcast.param.TargetOrgAndUser;
import com.vedeng.erp.common.broadcast.param.TimePeriod;
import com.vedeng.erp.common.broadcast.param.UserDefineUser;
import com.vedeng.erp.common.broadcast.param.DeptInfo;
import com.vedeng.erp.common.broadcast.param.UserOrgInfo;
import com.vedeng.erp.common.broadcast.param.QwMessageParam.Article;
import com.vedeng.erp.common.broadcast.param.QwMessageParam.News;
import com.vedeng.erp.common.broadcast.statistics.StatisticsDto;
import com.vedeng.erp.statistic.mapper.BroadcastStatisticAedNumMapper;
import com.vedeng.erp.system.dto.OrganizationDto;
import com.vedeng.erp.system.service.OrganizationApiService;

/**
 * 月度AED_TOP播报项目
 * @ClassName:  BroadcastMonthAed   
 * @author: Neil.yang
 * @date:   2025年6月9日 下午3:56:38    
 * @Copyright:
 */
@Component
public class BroadcastMonthAed extends AbstractBroadcast {

	@Autowired
	private BroadcastStatisticsMapper broadcastStatisticsMapper;
	
	@Autowired
    OrganizationApiService organizationApiService;
	
	@Autowired
	private BroadcastDeptRErpDeptMapper broadcastDeptRErpDeptMapper;
	
	@Autowired
	private BroadcastRAedUserMapper broadcastRAedUserMapper;
	
	@Autowired
	private BroadcastStatisticAedNumMapper broadcastStatisticAedNumMapper;
	
	@Autowired
	private BroadcastHelper broadcastHelper;
	
	@Autowired
    private RedisUtils redisUtils;
	
	@Value("${redis_dbtype}")
	private String dbType;
	
	@Value("${userTopN:50}")
	private Integer userTopN;
	
	@Value("${b2b_business_division_id}")
    private Integer b2bBusinessDivisionId;

	@Override
	public List<MessageSubjectEnum> getMessageSubjectList(GlobalConfig globalConfig) {
		List<MessageSubjectEnum> messageSubjectList = Arrays.asList(MessageSubjectEnum.SALES_SINGLE,MessageSubjectEnum.SALES_TEAM,MessageSubjectEnum.SALES_DEPT);
		return messageSubjectList;
	}

	@Override
	public int getLineNum() {
		return 6;
	}

	@Override
	public boolean isSaveDb() {
		return true;
	}

	@Override
	public List<BroadcastDeptConfigStatistics> getBroadcastDeptConfigByProject(List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList) {
		return broadcastDeptConfigStatisticsList.stream().filter(item -> item.getAedFlag() == 1).collect(Collectors.toList());
	}
	
	/**
     * 获取统计时间月期
     * @return 统计时间周期
     */
    public TimePeriod getStatisticsTime() {
    	Calendar calendarMonth = Calendar.getInstance();
    	//calendarMonth.add(Calendar.YEAR, -2);
    	calendarMonth.set(Calendar.DAY_OF_MONTH, 1);
    	calendarMonth.set(Calendar.HOUR_OF_DAY, 0);
    	calendarMonth.set(Calendar.MINUTE, 0);
    	calendarMonth.set(Calendar.SECOND, 0);
    	calendarMonth.set(Calendar.MILLISECOND, 0);
    	//本月的第一天作为月开始时间
    	Date monthStartDate = calendarMonth.getTime();
    	//开始时间和结束时间
    	return new TimePeriod(null,null,monthStartDate,new Date());
    }
    

	@Override
	public List<QwMessageParam> execute(GlobalConfig globalConfig,Map<MessageSubjectEnum, List<TargetOrgAndUser>> targetOrgAndUserMap,Integer deptId,Integer amountStep,Integer isDefineUser,TimePeriod timePeriod,StatDateRangeEnum statDateRange,boolean isSendQwMessageFlag) {
		//月AED执行时间参数
		if(Objects.isNull(timePeriod)) {
			timePeriod = getStatisticsTime();
		}
		//播报的消息列表
		List<QwMessageParam> qwMessageParamList = new ArrayList<>();
		//获取全局配置
		BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics = globalConfig.getBroadcastGlobalConfigStatistics();
		//获取个人所有出库数据
		List<StatisticsDto> statisticsDtoList = broadcastStatisticsMapper.selectStatisticsAedWarehouse(timePeriod.getMonthStartTime(),timePeriod.getMonthEndTime(),broadcastGlobalConfigStatistics.getAedSkuIds(),broadcastGlobalConfigStatistics.getExcludeSaleIds(),broadcastGlobalConfigStatistics.getStatSkuIds(),broadcastGlobalConfigStatistics.getStatBrandIds(),broadcastGlobalConfigStatistics.getStatCategoryIds(),isDefineUser);
		if(CollectionUtils.isEmpty(statisticsDtoList)) {
			return null;
		}
		if(showLog) {
			LOGGER.info("月度出库，个人数据统计(可能存在不符合条件的数据)：{}",JSON.toJSONString(statisticsDtoList));
		}
		//遍历这个出库数据，对出库数据中的userId进行判断，是否在营销中心下的末级组织下，如果不在，此用户数据剔除， 如果在，此数据在统计范围内，如果存在多个，取其中一个
		//获取所有营销中心的末级组织
		List<OrganizationDto> orgList= organizationApiService.getOrgAllLeafOrgByParentId(b2bBusinessDivisionId);
		if(CollectionUtils.isEmpty(orgList)) {
			return null;
		}
		LOGGER.info("获取所有营销中心的末级组织:{}",JSON.toJSONString(orgList));
		List<Integer> allOrgIdList = orgList.stream().map(temp->temp.getOrgId()).collect(Collectors.toList());
		//统计数据，对不符合的用户进行剔除，并更新一个唯一的组织ID
		List<StatisticsDto> statisticsDtoListChecked = new ArrayList<>();
		for (StatisticsDto statisticsDto : statisticsDtoList) {
			if(Objects.isNull(statisticsDto.getUserId())) {
				continue;
			}
			//获取用户的所属组织ID列表
			UserOrgInfo userOrgInfo = broadcastStatisticAedNumMapper.getUserOrgIdInfo(statisticsDto.getUserId());
			if(Objects.isNull(userOrgInfo)) {
				continue;
			}
			if(StringUtils.isEmpty(userOrgInfo.getOrgIds())) {
				continue;
			}
			//用户归属的组织ID列表
			List<Integer> belongOrgIdList = Arrays.asList(userOrgInfo.getOrgIds().split(",")).stream().map(temp->Integer.parseInt(temp)).collect(Collectors.toList());
			LOGGER.info("用户归属的组织ID列表:{}",JSON.toJSONString(belongOrgIdList));
			//获取用户所属组织信息
			List<UserDefineUser> userDefineUserList =  broadcastHelper.getExcludeUserId();
			Optional<UserDefineUser> user = userDefineUserList.stream()
	                .filter(userDefineUser -> userDefineUser.getUserId().equals(statisticsDto.getUserId()))
	                .findFirst();
			UserDefineUser userDefineUser = null;
			if(user.isPresent()) {
				userDefineUser = user.get();
			}
			//判断用户的ID是否在营销中心的末级部门
			for (Integer bealongOrgId : belongOrgIdList) {
				if(allOrgIdList.contains(bealongOrgId)) {
					//获取bealongOrgId属于那个部门那个小组
					DeptInfo  deptInfo  = broadcastDeptRErpDeptMapper.selectByErpOrgId(bealongOrgId);
					if(Objects.isNull(deptInfo)) {
						continue;
					}
					statisticsDto.setDeptId(deptInfo.getDeptId());
					statisticsDto.setDeptName(deptInfo.getDeptName());
					
					Integer teamId = deptInfo.getTeamId();
					String teamName = deptInfo.getTeamName();
					if(Objects.nonNull(userDefineUser)) {
						//应产品要求，自定义用户找不到原归属小组信息，该数据不计入
						if(CollectionUtils.isEmpty(userDefineUser.getTeamIdOldList()) || CollectionUtils.isEmpty(userDefineUser.getTeamNameOldList())) {
							continue;
						}else {
							 teamId = userDefineUser.getTeamIdOldList().get(0);
							 teamName = userDefineUser.getTeamNameOldList().get(0);
						}
					}
					statisticsDto.setTeamId(teamId);
					statisticsDto.setTeamName(teamName);
					statisticsDto.setOrgId(bealongOrgId);
					statisticsDto.setOrgIdList(belongOrgIdList);
					LOGGER.info("设置用户的组织信息:{}",JSON.toJSONString(statisticsDto));
					statisticsDtoListChecked.add(statisticsDto);
					//给用户找到一个符合的部门，跳出循环
					break;
				}
			}
		}
		//按出库量排序
		statisticsDtoListChecked = statisticsDtoListChecked.stream().sorted(Comparator.comparing(StatisticsDto::getTotalNum).reversed()).collect(Collectors.toList());
		if(showLog) {
			LOGGER.info("月度出库，符合条件的个人数据统计：{}",JSON.toJSONString(statisticsDtoListChecked));
		}
		//月AED出库量-个人
		List<TargetOrgAndUser> targetOrgAndUserPerson = targetOrgAndUserMap.get(MessageSubjectEnum.SALES_SINGLE);
		if(Objects.nonNull(targetOrgAndUserPerson) && !CollectionUtils.isEmpty(statisticsDtoListChecked)) {
			List<StatisticsDto> statisticsDtoListMonthSingleAedAll = new ArrayList<>();
			//遍历所有个人，统计所有符合的数据
			for (TargetOrgAndUser targetOrgAndUser : targetOrgAndUserPerson) {
				List<Integer> statisticOrgIdList = targetOrgAndUser.getOrgIdList();
				List<Integer> inUserIdList = targetOrgAndUser.getInUserIdList();
				List<Integer> outUserIdList = targetOrgAndUser.getOutUserIdList();
				List<StatisticsDto> tempList = null;
				if(deptId == 1) {
					tempList = statisticsDtoListChecked.stream()
							.filter(dto -> (statisticOrgIdList.contains(dto.getOrgId()) || inUserIdList.contains(dto.getUserId())) && !outUserIdList.contains(dto.getUserId()))
							.collect(Collectors.toList());
				}else {
					tempList = statisticsDtoListChecked.stream()
							.filter(dto -> (statisticOrgIdList.contains(dto.getOrgId())))
							.collect(Collectors.toList());
				}
				if(!CollectionUtils.isEmpty(tempList)) {
					statisticsDtoListMonthSingleAedAll.addAll(tempList); 
				}
			}
			
			//按照totalNum降序排列
			statisticsDtoListMonthSingleAedAll = statisticsDtoListMonthSingleAedAll.stream().sorted(Comparator.comparing(StatisticsDto::getTotalNum).reversed()).collect(Collectors.toList());
			if(!CollectionUtils.isEmpty(statisticsDtoListMonthSingleAedAll)) {
				//是否保存表信息(只排序后的结果，没有剔除播报数量),是否保存，且非自定义存表
				if(isSaveDb() && isDefineUser==0  && deptId == 1) {
					//存表
					try {
						if(statisticsDtoListMonthSingleAedAll.size()>userTopN) {
							statisticsDtoListMonthSingleAedAll = statisticsDtoListMonthSingleAedAll.subList(0, userTopN);
						}
						batchInert(statisticsDtoListMonthSingleAedAll,MessageSubjectEnum.SALES_SINGLE,timePeriod,globalConfig);
					}catch(Exception e) {
						LOGGER.error("月AED出库量播报个人信息排行存表失败...参数：全局参数：{},数据：{}",JSON.toJSONString(globalConfig),JSON.toJSONString(statisticsDtoListMonthSingleAedAll),e);
					}
				}
				if(isSendQwMessageFlag) {
					setTop(statisticsDtoListMonthSingleAedAll);
					//统计信息获取，组装企微信息
					List<QwMessageParam> qwMessageParams = combineQwMessage(statisticsDtoListMonthSingleAedAll,globalConfig,deptId,MessageSubjectEnum.SALES_SINGLE,isDefineUser,statDateRange);
					qwMessageParamList.addAll(qwMessageParams);
				}
			}
		}
		//月AED出库量-小组
		List<TargetOrgAndUser> targetOrgAndUserTeam = targetOrgAndUserMap.get(MessageSubjectEnum.SALES_TEAM);
		if(Objects.nonNull(targetOrgAndUserTeam)) {
			List<StatisticsDto> statisticsDtoListMonthTeamAedAll = new ArrayList<>();
			if(!CollectionUtils.isEmpty(targetOrgAndUserTeam) && !CollectionUtils.isEmpty(statisticsDtoListChecked)) {
				//遍历所有小组，将属于该小组的多个erp组织ID的用户数据进行合并统计,同时判断排除和额外添加
				for (TargetOrgAndUser targetOrgAndUser : targetOrgAndUserTeam) {
					List<Integer> statisticOrgIdList = targetOrgAndUser.getOrgIdList();
					List<Integer> inUserIdList = targetOrgAndUser.getInUserIdList();
					List<Integer> outUserIdList = targetOrgAndUser.getOutUserIdList();
					
					Integer calculateTotalNum = statisticsDtoListChecked.stream()
					        .filter(Objects::nonNull)
					        .filter(dto -> 
					            (dto.getOrgId() != null && statisticOrgIdList.contains(dto.getOrgId())) || 
					            (dto.getUserId() != null && inUserIdList.contains(dto.getUserId())))
					        .filter(dto -> dto.getUserId() == null || !outUserIdList.contains(dto.getUserId()))
					        .map(StatisticsDto::getTotalNum)
					        .filter(Objects::nonNull)
					        .mapToInt(Integer::intValue)
					        .sum();
					
					StatisticsDto statisticsDto = new StatisticsDto();
					statisticsDto.setTeamId(targetOrgAndUser.getTeamId());
					statisticsDto.setTeamName(targetOrgAndUser.getTeamName());
					statisticsDto.setTotalNum(calculateTotalNum);
					statisticsDtoListMonthTeamAedAll.add(statisticsDto);
				}
				//按照totalNum降序排列
				statisticsDtoListMonthTeamAedAll = statisticsDtoListMonthTeamAedAll.stream().sorted(Comparator.comparing(StatisticsDto::getTotalNum).reversed()).collect(Collectors.toList());
				//是否保存表信息(只排序后的结果)
				if(isSaveDb() && isDefineUser==0  && deptId == 1) {
					//存表
					try {
						batchInert(statisticsDtoListMonthTeamAedAll,MessageSubjectEnum.SALES_TEAM,timePeriod,globalConfig);
					}catch(Exception e) {
						LOGGER.error("月AED出库量播报小组信息排行存表失败...参数：全局参数：{},数据：{}",JSON.toJSONString(globalConfig),JSON.toJSONString(statisticsDtoListMonthTeamAedAll),e);
					}
				}
				if(isSendQwMessageFlag) {
					setTop(statisticsDtoListMonthTeamAedAll);
					//统计信息获取，组装企微信息
					List<QwMessageParam> qwMessageParams = combineQwMessage(statisticsDtoListMonthTeamAedAll,globalConfig,deptId,MessageSubjectEnum.SALES_TEAM,isDefineUser,statDateRange);
					qwMessageParamList.addAll(qwMessageParams);
				}
			}
		}
		//月AED出库量-部门
		List<TargetOrgAndUser> targetOrgAndUserDept = targetOrgAndUserMap.get(MessageSubjectEnum.SALES_DEPT);
		if(Objects.nonNull(targetOrgAndUserDept) && !CollectionUtils.isEmpty(statisticsDtoListChecked)) {
			List<StatisticsDto> statisticsDtoListMonthDeptAedAll = new ArrayList<>();
			//遍历所有部门，将属于该部门的多个erp组织ID的用户数据进行合并统计,同时判断排除和额外添加
			for (TargetOrgAndUser targetOrgAndUser : targetOrgAndUserDept) {
				List<Integer> statisticOrgIdList = targetOrgAndUser.getOrgIdList();
				List<Integer> inUserIdList = targetOrgAndUser.getInUserIdList();
				List<Integer> outUserIdList = targetOrgAndUser.getOutUserIdList();
				
				Integer calculateTotalNum = statisticsDtoListChecked.stream()
			            .filter(dto -> (statisticOrgIdList.contains(dto.getOrgId()) || inUserIdList.contains(dto.getUserId())) && !outUserIdList.contains(dto.getUserId()))
			            .mapToInt(StatisticsDto::getTotalNum)
			            .sum();
				
				StatisticsDto statisticsDto = new StatisticsDto();
				statisticsDto.setDeptId(targetOrgAndUser.getDeptId());
				statisticsDto.setDeptName(targetOrgAndUser.getDeptName());
				statisticsDto.setTotalNum(calculateTotalNum);
				statisticsDtoListMonthDeptAedAll.add(statisticsDto);
			}
			
			//月AED出库量
			if(!CollectionUtils.isEmpty(statisticsDtoListMonthDeptAedAll)) {
				//按照totalNum降序排列
				statisticsDtoListMonthDeptAedAll = statisticsDtoListMonthDeptAedAll.stream().sorted(Comparator.comparing(StatisticsDto::getTotalNum,Comparator.nullsFirst(Comparator.naturalOrder())).reversed()).collect(Collectors.toList());;
				//是否保存表信息(只排序后的结果，没有剔除梯度和播报数量)
				if(isSaveDb() && isDefineUser==0  && deptId == 1) {
					//存表
					try {
						batchInert(statisticsDtoListMonthDeptAedAll,MessageSubjectEnum.SALES_DEPT,timePeriod,globalConfig);
					}catch(Exception e) {
						LOGGER.error("月AED出库量播报部门信息排行存表失败...参数：全局参数：{},数据：{}",JSON.toJSONString(globalConfig),JSON.toJSONString(statisticsDtoListMonthDeptAedAll),e);
					}
				}
				if(isSendQwMessageFlag) {
					//设置top
					setTop(statisticsDtoListMonthDeptAedAll);
					//统计信息获取，组装企微信息，记录需要按挡位播报
					List<QwMessageParam> qwMessageParams = combineQwMessage(statisticsDtoListMonthDeptAedAll,globalConfig,deptId,MessageSubjectEnum.SALES_DEPT,isDefineUser,statDateRange);
					qwMessageParamList.addAll(qwMessageParams);
				}
			}
		}
		return qwMessageParamList;
	}

	private void batchInert(List<StatisticsDto> statisticsDtoListMonthSingleAedAll, MessageSubjectEnum messageSubjectEnum,TimePeriod timePeriod, GlobalConfig globalConfig) {
		//删除统计类型【1：个人；2：小组；3：部门】 和 统计日期【yyyy-MM】 
		BroadcastStatisticAedNumExample example = new BroadcastStatisticAedNumExample();
		String statisticsTime = DateUtil.DateToString(timePeriod.getMonthStartTime(), DateUtil.DATE_FORMAT_MONTH);
		example.createCriteria().andIsDelEqualTo(0).andStatisticsTypeEqualTo(messageSubjectEnum.getSubject()).andStatisticsTimeEqualTo(statisticsTime);
		//历史数据先删除，再新增
		broadcastStatisticAedNumMapper.deleteByExample(example);
		
		List<BroadcastStatisticAedNumWithBLOBs> broadcastStatisticAedNumWithBLOBsList = new ArrayList<>();
		Date now = new Date();
		for (StatisticsDto statisticsDto : statisticsDtoListMonthSingleAedAll) {
			BroadcastStatisticAedNumWithBLOBs broadcastStatisticAedNumWithBLOBs = new BroadcastStatisticAedNumWithBLOBs();
			broadcastStatisticAedNumWithBLOBs.setConditionText(JSON.toJSONString(globalConfig.getBroadcastGlobalConfigStatistics())+"=="+JSON.toJSONString(globalConfig.getBroadcastDeptConfigStatisticsList()));
			broadcastStatisticAedNumWithBLOBs.setCreateTime(now);
			broadcastStatisticAedNumWithBLOBs.setDeptId(statisticsDto.getDeptId());
			broadcastStatisticAedNumWithBLOBs.setDeptName(statisticsDto.getDeptName());
			broadcastStatisticAedNumWithBLOBs.setIsDel(0);
			broadcastStatisticAedNumWithBLOBs.setStatisticsTime(statisticsTime);
			broadcastStatisticAedNumWithBLOBs.setStatisticsType(messageSubjectEnum.getSubject());
			broadcastStatisticAedNumWithBLOBs.setTeamId(statisticsDto.getTeamId());
			broadcastStatisticAedNumWithBLOBs.setTeamName(statisticsDto.getTeamName());
			broadcastStatisticAedNumWithBLOBs.setUpdateTime(now);
			broadcastStatisticAedNumWithBLOBs.setUserId(statisticsDto.getUserId());
			broadcastStatisticAedNumWithBLOBs.setUserName(statisticsDto.getUserName());
			broadcastStatisticAedNumWithBLOBs.setWarehouseNum(statisticsDto.getTotalNum());
			broadcastStatisticAedNumWithBLOBsList.add(broadcastStatisticAedNumWithBLOBs);
		}
		if(!CollectionUtils.isEmpty(broadcastStatisticAedNumWithBLOBsList)) {
			if(showLog) {
				LOGGER.info("月AED出库量新增，入参：{}",JSON.toJSONString(broadcastStatisticAedNumWithBLOBsList));
			}
			broadcastStatisticAedNumMapper.batchInsert(broadcastStatisticAedNumWithBLOBsList);
		}
		
	}

	private void setTop(List<StatisticsDto> statisticsDtoListAll) {
		AtomicInteger rank = new AtomicInteger(1);
		for (int i = 0; i < statisticsDtoListAll.size(); i++) {
		    // 处理第一个元素或与前一个元素不同时递增排名
		    if (i == 0 || 
		    		statisticsDtoListAll.get(i).getTotalNum().compareTo(statisticsDtoListAll.get(i-1).getTotalNum()) != 0) {
		        rank.set(i + 1);
		    }
		    statisticsDtoListAll.get(i).setTopNum(rank.get());
		}
	}
	

	/**
	 * 组装企微消息参数
	 * @param statisticsDtoList 需要发送的数据
	 * @param globalConfig  全局配置
	 * @param amountStep  日到款梯度
	 * @param deptId  大群或者部门ID
	 * @param messageSubjectEnum 消息主体的枚举
	 * @param isDefineUser 
	 * @param statDateRange 
	 * @return
	 */
	private List<QwMessageParam> combineQwMessage(List<StatisticsDto> statisticsDtoList,GlobalConfig globalConfig, Integer deptId, MessageSubjectEnum messageSubjectEnum, Integer isDefineUser, StatDateRangeEnum statDateRange) {
		List<QwMessageParam> qwMessageParamList = new ArrayList<>();
		BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics = globalConfig.getBroadcastGlobalConfigStatistics();
		//组装要发送的企微参数
		List<String> descriptionList = getDescription(statisticsDtoList,globalConfig,deptId,messageSubjectEnum,isDefineUser,statDateRange);
		for (String description : descriptionList) {
			QwMessageParam qwMessageParam = new QwMessageParam();
			qwMessageParam.setMsgtype("news");
			News news = new News();
			List<Article> articles = new ArrayList<>();
			Article article = new Article();
			if(Objects.nonNull(isDefineUser) && isDefineUser==1) {
				if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
					article.setTitle(broadcastGlobalConfigStatistics.getBroadcastTitleCustom()+"（个人）");
				}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM)) {
					article.setTitle(broadcastGlobalConfigStatistics.getBroadcastTitleCustom()+"（小组）");
				}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
					article.setTitle(broadcastGlobalConfigStatistics.getBroadcastTitleCustom()+"（部门）");
				}
			}else {
				if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
					article.setTitle(broadcastGlobalConfigStatistics.getBroadcastTitleAed()+"（个人）");
				}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM)) {
					article.setTitle(broadcastGlobalConfigStatistics.getBroadcastTitleAed()+"（小组）");
				}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
					article.setTitle(broadcastGlobalConfigStatistics.getBroadcastTitleAed()+"（部门）");
				}
			}
			// 如果最后有多余的 \n\n，去除它
		    if (description.length() > 2 && description.substring(description.length() - 2).equals("\n\n")) {
		    	description = description.subSequence(0,description.length() - 2).toString();
		    }
			article.setDescription(description);
			//跳转链接，本期固定
			article.setUrl(qwUrl);
			//获取图片逻辑（排序第一的个人或者团队）
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
				article.setPicurl(getPicUrl(statisticsDtoList.get(0).getUserId(),globalConfig,MessageSubjectEnum.SALES_SINGLE,isDefineUser,deptId));
			}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM)) {
				article.setPicurl(getPicUrl(statisticsDtoList.get(0).getTeamId(),globalConfig,MessageSubjectEnum.SALES_TEAM,isDefineUser,deptId));
			}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
				article.setPicurl(getPicUrl(statisticsDtoList.get(0).getDeptId(),globalConfig,MessageSubjectEnum.SALES_DEPT,isDefineUser,deptId));
			}
			articles.add(article);
			news.setArticles(articles);
			qwMessageParam.setNews(news);
			qwMessageParamList.add(qwMessageParam);
		}
		return qwMessageParamList;
	}
	
	
	/**
	 * 拼接企微消息
	 * @param statisticsDtoList
	 * @param amountStep 
	 * @param deptId 
	 * @param messageSubjectEnum 
	 * @param isDefineUser 
	 * @param statDateRange 
	 * @return
	 */
	private List<String> getDescription(List<StatisticsDto> statisticsDtoList,GlobalConfig globalConfig, Integer deptId, MessageSubjectEnum messageSubjectEnum, Integer isDefineUser, StatDateRangeEnum statDateRange) {
		List<String> descriptionList = new ArrayList<>();
		//先获取获取topN
		BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics = globalConfig.getBroadcastGlobalConfigStatistics();
		Integer topnUser = broadcastGlobalConfigStatistics.getTopnUser();
		Integer topnDept = broadcastGlobalConfigStatistics.getTopnDept();
		Integer totalCount = statisticsDtoList.size();
		if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE) && totalCount>topnUser) {
			statisticsDtoList = statisticsDtoList.subList(0, topnUser);
		}
		if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM) || messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
			if(totalCount>topnDept) {
				statisticsDtoList = statisticsDtoList.subList(0, topnDept);
			}
		}
		
		//由于企微限制，每次不能超过512个字符，此处需要判断
		List<String> messageInfoList = new ArrayList<>();
		for (StatisticsDto statisticsDto : statisticsDtoList) {
			messageInfoList.add(processBatchMessage(Arrays.asList(statisticsDto),messageSubjectEnum,isDefineUser,statDateRange));
		}
	    Integer	batchSize = getLineNum();
	    while (batchSize > 0) {
	    	descriptionList.clear();
            boolean validBatch = true;

            for (int i = 0; i < messageInfoList.size(); i += batchSize) {
                List<String> batch = messageInfoList.subList(i, Math.min(i + batchSize, messageInfoList.size()));
                String combined = String.join("", batch);
                
                // 检查字节长度（UTF-8 编码）
                byte[] bytes = combined.getBytes(StandardCharsets.UTF_8);
                if (bytes.length > 512) {
                    validBatch = false;
                    break;
                }
                descriptionList.add(combined);
            }

            if (validBatch) {
                return descriptionList; // 找到合适的批次大小
            }
            batchSize--; // 减小批次大小重试
        }
	    
		return descriptionList;
	}
	

	private String processBatchMessage(List<StatisticsDto> statisticsDtoList, MessageSubjectEnum messageSubjectEnum, Integer isDefineUser, StatDateRangeEnum statDateRange) {
		StringBuilder stb = new StringBuilder();
		for (StatisticsDto statisticsDto : statisticsDtoList) {
			//处理个人
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
				String userName = "未知用户";
				String teamName = statisticsDto.getTeamName();
				if(StringUtils.isNotEmpty(statisticsDto.getUserName())) {
					userName = statisticsDto.getUserName().split("\\.")[0];
				}
				String message = "";
				if(isDefineUser==0) {
					//AED销售名称处理
					String aedUserName = selectAedIdAndType(statisticsDto.getUserId(),MessageSubjectEnum.SALES_SINGLE);
					if(StringUtils.isEmpty(aedUserName)) {
						message = MessageFormat.format(MessageTemplate.MONTH_AED_PERSON_MESSAGE_DEFINE_USER,statisticsDto.getTopNum(),teamName,userName,statisticsDto.getTotalNum().toString());
					}else {
						message = MessageFormat.format(MessageTemplate.MONTH_AED_PERSON_MESSAGE,statisticsDto.getTopNum(),teamName,userName,statisticsDto.getTotalNum().toString(),aedUserName);
					}
				}else if(statDateRange.equals(StatDateRangeEnum.STATISTICS_DAY)){
					message = MessageFormat.format(MessageTemplate.MONTH_AED_PERSON_MESSAGE_DEFINE_USER_DAY,statisticsDto.getTopNum(),teamName,userName,statisticsDto.getTotalNum().toString());
				}else if(statDateRange.equals(StatDateRangeEnum.STATISTICS_WEEK)){
					message = MessageFormat.format(MessageTemplate.MONTH_AED_PERSON_MESSAGE_DEFINE_USER_WEEK,statisticsDto.getTopNum(),teamName,userName,statisticsDto.getTotalNum().toString());
				}else if(statDateRange.equals(StatDateRangeEnum.STATISTICS_MONTH)){
					message = MessageFormat.format(MessageTemplate.MONTH_AED_PERSON_MESSAGE_DEFINE_USER_MONTH,statisticsDto.getTopNum(),teamName,userName,statisticsDto.getTotalNum().toString());
				}
				stb.append(message);
				stb.append("\n\n");
			}
			//处理小组
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM)) {
				String message = "";
				if(isDefineUser==0) {
					//AED销售名称处理
					String aedUserName = selectAedIdAndType(statisticsDto.getTeamId(),MessageSubjectEnum.SALES_TEAM);
					if(StringUtils.isEmpty(aedUserName)) {
						message = MessageFormat.format(MessageTemplate.MONTH_AED_TEAM_MESSAGE_DEFINE_USER_MONTH,statisticsDto.getTopNum(),statisticsDto.getTeamName(),statisticsDto.getTotalNum().toString());
					}else {
						message = MessageFormat.format(MessageTemplate.MONTH_AED_TEAM_MESSAGE,statisticsDto.getTopNum(),statisticsDto.getTeamName(),statisticsDto.getTotalNum().toString(),aedUserName);
					}
				}else if(statDateRange.equals(StatDateRangeEnum.STATISTICS_DAY)){
					message = MessageFormat.format(MessageTemplate.MONTH_AED_TEAM_MESSAGE_DEFINE_USER_DAY,statisticsDto.getTopNum(),statisticsDto.getTeamName(),statisticsDto.getTotalNum().toString());
				}else if(statDateRange.equals(StatDateRangeEnum.STATISTICS_WEEK)){
					message = MessageFormat.format(MessageTemplate.MONTH_AED_TEAM_MESSAGE_DEFINE_USER_WEEK,statisticsDto.getTopNum(),statisticsDto.getTeamName(),statisticsDto.getTotalNum().toString());
				}else if(statDateRange.equals(StatDateRangeEnum.STATISTICS_MONTH)){
					message = MessageFormat.format(MessageTemplate.MONTH_AED_TEAM_MESSAGE_DEFINE_USER_MONTH,statisticsDto.getTopNum(),statisticsDto.getTeamName(),statisticsDto.getTotalNum().toString());
				}
				stb.append(message);
				stb.append("\n\n");
			}
			//处理部门
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
				String message = "";
				if(isDefineUser==0) {
					//AED销售名称处理
					String aedUserName = selectAedIdAndType(statisticsDto.getDeptId(),MessageSubjectEnum.SALES_DEPT);
					if(StringUtils.isEmpty(aedUserName)) {
						message = MessageFormat.format(MessageTemplate.MONTH_AED_DEPT_MESSAGE_DEFINE_USER_MONTH,statisticsDto.getTopNum(),statisticsDto.getDeptName(),statisticsDto.getTotalNum().toString());
					}else {
						message = MessageFormat.format(MessageTemplate.MONTH_AED_DEPT_MESSAGE,statisticsDto.getTopNum(),statisticsDto.getDeptName(),statisticsDto.getTotalNum().toString(),aedUserName);
					}
				}else if(statDateRange.equals(StatDateRangeEnum.STATISTICS_DAY)){
					message = MessageFormat.format(MessageTemplate.MONTH_AED_DEPT_MESSAGE_DEFINE_USER_DAY,statisticsDto.getTopNum(),statisticsDto.getDeptName(),statisticsDto.getTotalNum().toString());
				}else if(statDateRange.equals(StatDateRangeEnum.STATISTICS_WEEK)){
					message = MessageFormat.format(MessageTemplate.MONTH_AED_DEPT_MESSAGE_DEFINE_USER_WEEK,statisticsDto.getTopNum(),statisticsDto.getDeptName(),statisticsDto.getTotalNum().toString());
				}else if(statDateRange.equals(StatDateRangeEnum.STATISTICS_MONTH)){
					message = MessageFormat.format(MessageTemplate.MONTH_AED_DEPT_MESSAGE_DEFINE_USER_MONTH,statisticsDto.getTopNum(),statisticsDto.getDeptName(),statisticsDto.getTotalNum().toString());
				}
				stb.append(message);
				stb.append("\n\n");
			}
		}
		return stb.toString();
	}
	


	private String selectAedIdAndType(Integer id, MessageSubjectEnum messageSubject) {
		String aedUserName = "";
		String strs = "";
		if(messageSubject.equals(MessageSubjectEnum.SALES_SINGLE)) {
			strs = broadcastRAedUserMapper.selectAedSingleByErpUserId(id);
		}else if(messageSubject.equals(MessageSubjectEnum.SALES_TEAM)) {
			strs = broadcastRAedUserMapper.selectAedTeamByErpUserId(id);
		}else if(messageSubject.equals(MessageSubjectEnum.SALES_DEPT)) {
			strs = broadcastRAedUserMapper.selectAedDeptByErpUserId(id);
		}
		if(StringUtils.isBlank(strs)) {
			return aedUserName;
		}
		List<String> userNameList = Arrays.asList(strs.split(","));
		List<String> returnList = new ArrayList<>();
		for (String userName : userNameList) {
			if(userName.contains(".")) {
				userName = userName.split("\\.")[0];
				returnList.add(userName);
			}
		}
		aedUserName = String.join(",", returnList);
		return aedUserName;
	}

	/**
	 * 获取展示的图片
	 * 1. 优先从专属目标为"月度AED TOP"的图片中随机选择，每张图片每日仅使用一次，如当日全部使用，则重置使用次数
	 * 2. 如排序第一的个人或团队，有专属图片，则随机选择专属图片（播报管理 - 专属目标），每张图片每日仅使用一次，如当日全部使用，则重置使用次数
	 * 3. 否则随机获取非专属目标的图片，每张图片每日仅使用一次，如当日全部使用，则重置使用次数
	 * @param userId
	 * @param globalConfig
	 * @param messageSubjectEnum 
	 * @param isDefineUser 
	 * @param deptId 
	 * @return
	 */
	private String getPicUrl(Integer targetId, GlobalConfig globalConfig, MessageSubjectEnum messageSubjectEnum, Integer isDefineUser, Integer deptId) {
		String picUrl = "http://#";
		Integer picId = null;
		List<BroadcastContentConfigEntity> broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList();
		//为空，返回空字符串
		if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
			return picUrl;
		}
		//判断是否有月度AED TOP的图片
		boolean	aedFlag = false;
		//自定义没有月度AED TOP图片专属
		if(isDefineUser==0) {
			aedFlag = broadcastContentConfigStatisticsList.stream().anyMatch(temp->(Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==3 && temp.getExclusiveTargetValues().equals("1")));;
		}
		if(aedFlag) {
			//筛选出AED专属图片列表
			broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream()
					.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==3 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals("1"))
					.collect(Collectors.toList());
			if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
				return picUrl;
			}
			//REDIS获取已使用的图片列表
			String picConfigRecordKey = MessageFormat.format(BroadcastRedisKey.PIC_CONFIG_RECORD,dbType,3,1);
			List<Integer> picConfigRecordValue = redisUtils.lgetAllInt(picConfigRecordKey);
			LOGGER.info("专属图片ID使用情况:{}",JSON.toJSONString(picConfigRecordValue));
			//REDIS为空，说明当日首次使用
			int expireTime = Integer.parseInt(String.valueOf(DateUtil.getSecondsToNextDay()));
			if(CollectionUtils.isEmpty(picConfigRecordValue)) {
				Collections.shuffle(broadcastContentConfigStatisticsList);
				picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
				picId = broadcastContentConfigStatisticsList.get(0).getId();
				redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
			}else {
				//筛选出专属图片列表，并且在已使用记录中不存在的
				broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream().filter(temp-> !picConfigRecordValue.contains(temp.getId())).collect(Collectors.toList());
				//无筛选值，删除REDIS，随机取一个，重建REDIS
				if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
					//无筛选值，重新获取
					broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList().stream()
							.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==3 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals("1"))
							.collect(Collectors.toList());
					redisUtils.del(picConfigRecordKey);
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}else {
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}
			}
		}else {
			boolean flag = false;
			//是否存在个人专属图片
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
				flag = broadcastContentConfigStatisticsList.stream().anyMatch(temp->(Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==1 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId))));
			}
			//是否存在团队专属图片
			else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM) || messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
				flag = broadcastContentConfigStatisticsList.stream().anyMatch(temp->(Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==2 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId))));
			}
			LOGGER.info("{},是否有专属图片：{}",messageSubjectEnum.getSubjectName(),flag);
			if(flag) {
				if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
					//筛选出个人专属图片列表
					broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream()
							.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==1 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId)))
							.collect(Collectors.toList());
				}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM) || messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
					//筛选出团队专属图片列表
					broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream()
							.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==2 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId)))
							.collect(Collectors.toList());
				}
				
				if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
					return picUrl;
				}
				//REDIS获取已使用的图片列表
				String picConfigRecordKey = "";
				if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
					picConfigRecordKey = MessageFormat.format(BroadcastRedisKey.PIC_CONFIG_RECORD,dbType,1,targetId);
				}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM) || messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
					picConfigRecordKey = MessageFormat.format(BroadcastRedisKey.PIC_CONFIG_RECORD,dbType,2,targetId);
				}
				List<Integer> picConfigRecordValue = redisUtils.lgetAllInt(picConfigRecordKey);
				LOGGER.info("专属图片ID使用情况:{}",JSON.toJSONString(picConfigRecordValue));
				//REDIS为空，说明当日首次使用
				int expireTime = Integer.parseInt(String.valueOf(DateUtil.getSecondsToNextDay()));
				if(CollectionUtils.isEmpty(picConfigRecordValue)) {
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}else {
					//筛选出专属图片列表，并且在已使用记录中不存在的
					broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream().filter(temp-> !picConfigRecordValue.contains(temp.getId())).collect(Collectors.toList());
					//无筛选值，删除REDIS，随机取一个，重建REDIS
					if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
						//无筛选值，重新获取
						
						if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
							broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList().stream()
									.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==1 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId)))
									.collect(Collectors.toList());;
						}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM) || messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
							broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList().stream()
									.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==2 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId)))
									.collect(Collectors.toList());;
						}
						redisUtils.del(picConfigRecordKey);
						Collections.shuffle(broadcastContentConfigStatisticsList);
						picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
						picId = broadcastContentConfigStatisticsList.get(0).getId();
						redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
					}else {
						Collections.shuffle(broadcastContentConfigStatisticsList);
						picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
						picId = broadcastContentConfigStatisticsList.get(0).getId();
						redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
					}
				}
			}else {
				broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream().filter(temp-> temp.getExclusiveType()== 0).collect(Collectors.toList());
				if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
					return picUrl;
				}
				//REDIS获取已使用的图片列表
				String picConfigRecordKey = MessageFormat.format(BroadcastRedisKey.PIC_CONFIG_RECORD,dbType,0,0);
				List<Integer> picConfigRecordValue = redisUtils.lgetAllInt(picConfigRecordKey);
				LOGGER.info("非专属图片ID使用情况:{}",JSON.toJSONString(picConfigRecordValue));
				//REDIS为空，说明当日首次使用
				int expireTime = Integer.parseInt(String.valueOf(DateUtil.getSecondsToNextDay()));
				if(CollectionUtils.isEmpty(picConfigRecordValue)) {
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}else {
					//筛选出图片列表,非专属，并且在已使用记录中不存在的
					broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream().filter(temp->!picConfigRecordValue.contains(temp.getId()) ).collect(Collectors.toList());
					//无筛选值，删除REDIS，随机取一个，重建REDIS
					if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
						broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList().stream().filter(temp-> temp.getExclusiveType()== 0).collect(Collectors.toList());
						redisUtils.del(picConfigRecordKey);
						Collections.shuffle(broadcastContentConfigStatisticsList);
						picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
						picId = broadcastContentConfigStatisticsList.get(0).getId();
						redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
					}else {
						Collections.shuffle(broadcastContentConfigStatisticsList);
						picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
						picId = broadcastContentConfigStatisticsList.get(0).getId();
						redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
					}
				}
			}
		}
		if(deptId == 1) {
			String picConfigRecordKey = MessageFormat.format(BroadcastRedisKey.ALREADY_DISPLAY_PIC_URL,dbType,3,messageSubjectEnum.getSubject());
			//不用设置过期时间，直接进行替换
			redisUtils.set(picConfigRecordKey, picUrl);
		}
		return picUrl;
	}

}
