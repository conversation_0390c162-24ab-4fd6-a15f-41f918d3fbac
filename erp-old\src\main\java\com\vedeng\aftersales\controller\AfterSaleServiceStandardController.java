package com.vedeng.aftersales.controller;

import com.pricecenter.dto.SkuPriceChangeApplyDto;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.component.dto.AfterSaleServiceLabelDto;
import com.vedeng.aftersales.constant.SkuSubTypeEnum;
import com.vedeng.aftersales.model.vo.ApprovedSkuAfterSalesInfoVO;
import com.vedeng.aftersales.model.*;
import com.vedeng.aftersales.model.dto.*;
import com.vedeng.aftersales.service.AfterSaleServiceStandardService;
import com.vedeng.authorization.model.Position;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.service.AuthService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.model.*;
import com.vedeng.goods.service.BrandService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.vedeng.trader.model.vo.TraderVo;
import com.vedeng.trader.service.TraderSupplierService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 售后服务标准申请入口
 */
@Controller
@RequestMapping("/aftersale/serviceStandard")
public class AfterSaleServiceStandardController extends BaseController {

    @Autowired
    private AfterSaleServiceStandardService afterSaleServiceStandardService;

    //定义日志
    private static final Logger LOGGER = LoggerFactory.getLogger(AfterSaleServiceStandardController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private VgoodsService vGoodsService;

    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    @Qualifier("traderSupplierService")
    private TraderSupplierService traderSupplierService;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private CoreSpuMapper coreSpuMapper;

    @Resource
    private BrandService brandService;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Autowired
    private AuthService authService;

    /**
     * 售后服务标准列表页
     */
    @ResponseBody
    @RequestMapping(value = "/index",produces = "application/json;charset=UTF-8")
    public ModelAndView index(HttpServletRequest request, AfterSaleServiceStandardApplyQueryDto queryDto,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize) throws Exception {

        Page page = super.getPageTag(request,pageNo,pageSize);
        if (StringUtils.isNotBlank(queryDto.getSubordinateList())){
            queryDto.setSubordinates(Arrays.stream(queryDto.getSubordinateList().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
        }
        List<AfterSaleServiceStandardApplyDto> serviceStandardApplyDtoList  = this.afterSaleServiceStandardService.querylistPage(queryDto,page);

        ModelAndView mv=new ModelAndView("/aftersales/serviceStandard/index");
        mv.addObject("serviceStandardApplyDtoList",serviceStandardApplyDtoList);
        mv.addObject("queryDto",queryDto);
        mv.addObject("page",page);
        //产品归属
        mv.addObject("managerUserList",userService.selectAllAssignUser());
        //待审核人
        mv.addObject("afterSaleDirectorList",userService.selectAllAfterSaleDirector());

        return mv;
    }

    /**
     * 售后详情标准申请页
     */
    @RequestMapping(value = "/detail")
    public ModelAndView detail(HttpServletRequest request,@Param("skuNo") String skuNo) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        AfterSaleServiceStandardApply afterSaleServiceStandardApply = afterSaleServiceStandardService.selectAfterSaleServiceStandardBySkuNo(skuNo);

        List<AfterSaleSupplyPolicyDto> supplyAfterSalePolicyList = this.afterSaleServiceStandardService.getSupplyAfterSalePolicyListBySkuNo(skuNo);

        if(afterSaleServiceStandardApply == null){

            AfterSaleServiceStandardApply insertStandardApply = new AfterSaleServiceStandardApply();
            insertStandardApply.setSkuNo(skuNo);
            insertStandardApply.setCreator(user.getUserId());
            insertStandardApply.setUpdator(user.getUserId());

            String nowDate = DateUtil.getNowDate(DateUtil.TIME_FORMAT);
            insertStandardApply.setAddTime(nowDate);
            insertStandardApply.setModTime(nowDate);

            insertStandardApply.setSupplyAfterSaleIsMaintain(CollectionUtils.isEmpty(supplyAfterSalePolicyList) ? 0 : 1);
            afterSaleServiceStandardService.insertServiceStandardApply(insertStandardApply);

            //因为数据库设置了一些默认值 所以再查一遍
            afterSaleServiceStandardApply =  afterSaleServiceStandardService.selectAfterSaleServiceStandardBySkuNo(skuNo);
        }

        if(CollectionUtils.isNotEmpty(supplyAfterSalePolicyList)){
            //更新贝登的售后政策=已维护
            AfterSaleServiceStandardApply updateApply  = new AfterSaleServiceStandardApply();
            updateApply.setSkuNo(skuNo);
            updateApply.setSupplyAfterSaleIsMaintain(1);
            this.afterSaleServiceStandardService.updateBySkuNo(updateApply);

            afterSaleServiceStandardApply.setSupplyAfterSaleIsMaintain(1);
        }

        ModelAndView mv = new ModelAndView();


        List<AfterSaleServiceStandardApplyAttachment> applyAttashmentList =  afterSaleServiceStandardService.getAfterSaleServiceStandardAttashmentById(afterSaleServiceStandardApply.getServiceStandardApplyId());
        mv.addObject("applyAttashmentList",applyAttashmentList);

        AfterSaleServiceStandardApplyInstallArea applyInstallArea = afterSaleServiceStandardService.selectServiceStandardApplyInstallArea(afterSaleServiceStandardApply.getServiceStandardApplyId());

        /**
         * 获取审核记录
         */
        receieveVerifyInfo(mv,afterSaleServiceStandardApply.getServiceStandardApplyId());

        CoreSkuGenerate skuGenerate = coreSkuGenerateMapper.selectBySkuNo(skuNo);
        mv.addObject("skuGenerate", skuGenerate);

        CoreSpuGenerate spuInfo = this.vGoodsService.findSpuInfoBySkuNo(skuNo);
        mv.addObject("spuType", spuInfo.getSpuType());

        AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto = afterSaleServiceStandardService.getEffectAfterSalePolicy(skuNo);

        //如果供应商的售后政策维护了
        if(afterSaleServiceStandardApply.getSupplyAfterSaleIsMaintain() == 1){
            List<AfterSaleSupplyPolicyDto> afterSaleSupplyPolicyList =  afterSaleServiceStandardService.getSupplyAfterSalePolicyListBySkuNo(skuNo);
            //默认值
            for (int i = 0; i < afterSaleSupplyPolicyList.size(); i++) {
                defaultSupplierValue(skuGenerate,afterSaleSupplyPolicyList.get(i));
            }
            mv.addObject("afterSaleSupplyPolicyList",afterSaleSupplyPolicyList);
        }
        //计算售后服务等级使用哪个对象 start

        AfterSaleServiceStandardInfoDto afterSaleServiceLevelDto=afterSaleServiceStandardService.getAfterSalesServiceLevelDtoBySku(skuNo);
        mv.addObject("afterSaleServiceLevelDto",afterSaleServiceLevelDto);

        //计算售后服务等级使用哪个对象 end

        mv.addObject("afterSaleServiceStandardInfoDto",defaultVedengValue(skuGenerate,afterSaleServiceStandardInfoDto,afterSaleServiceStandardApply));
        //默认值


        mv.addObject("first",afterSaleServiceStandardInfoDto == null ? 1 : 0);

        //售后服务标签
        List<AfterSaleServiceLabelDto> labels = afterSaleServiceStandardService.getAfterSaleServiceLabels(skuNo);

        mv.addObject("afterSaleServiceStandardApply",afterSaleServiceStandardApply);
        //获取安调、技术支持，维修 等资料上传情况
        docFlags(  mv,  skuNo);

        mv.addObject("applyInstallArea",applyInstallArea);
        mv.addObject("skuNo",skuNo);
        mv.addObject("labels",labels);
        mv.setViewName("aftersales/serviceStandard/detail");
        return mv;
    }
    private AfterSaleServiceStandardInfoDto  defaultVedengValue(CoreSkuGenerate skuGenerate, AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto,AfterSaleServiceStandardApply afterSaleServiceStandardApply) {
        if(afterSaleServiceStandardInfoDto==null){
            return afterSaleServiceStandardInfoDto;
        }
        boolean noInstallFlag= skuGenerate.getIsInstallable()==null||skuGenerate.getIsInstallable()==0||afterSaleServiceStandardInfoDto.getInstallPolicyInstallType()==null||
                afterSaleServiceStandardInfoDto.getInstallPolicyInstallType()==2 ;

        boolean noTechSupportFlag= (afterSaleServiceStandardInfoDto.getTechnicalDirectSupplyMaintain()!=null&&afterSaleServiceStandardInfoDto.getTechnicalDirectSupplyMaintain()== 0);
        boolean noGuaranteeFlag= (afterSaleServiceStandardInfoDto.getGuaranteePolicyIsGuarantee()!=null&&afterSaleServiceStandardInfoDto.getGuaranteePolicyIsGuarantee()== 0);
        //默认值处理
        if(noInstallFlag  ){
            afterSaleServiceStandardInfoDto.setInstallPolicyResponseTime("");
            afterSaleServiceStandardInfoDto.setInstallPolicyInstallFee(null);
            afterSaleServiceStandardInfoDto.setInstallArea(null);
            afterSaleServiceStandardInfoDto.setInstallPolicyInstallAreaComment("");
            afterSaleServiceStandardInfoDto.setInstallPolicyHaveInstallationQualification(null);
            afterSaleServiceStandardInfoDto.setInstallPolicyFreeRemoteInstall(null);
            afterSaleServiceStandardInfoDto.setInstallPolicyVisitTime(null);
            afterSaleServiceStandardInfoDto.setInstallPolicyInstallTime(null);
            //审核通过的时候不展示tips
            if(afterSaleServiceStandardApply.getAfterSaleStandardStatus()!=null&&afterSaleServiceStandardApply.getAfterSaleStandardStatus()==2){
                afterSaleServiceStandardApply.setInstallPolicyResponseTime("");
                afterSaleServiceStandardApply.setInstallPolicyInstallFee(null);
                // afterSaleServiceStandardApply.setInstallArea(null);
                afterSaleServiceStandardApply.setInstallPolicyInstallAreaComment("");
                afterSaleServiceStandardApply.setInstallPolicyHaveInstallationQualification(null);
                afterSaleServiceStandardApply.setInstallPolicyFreeRemoteInstall(null);
                afterSaleServiceStandardApply.setInstallPolicyVisitTime(null);
                afterSaleServiceStandardApply.setInstallPolicyInstallTime(null);
            }
        }
        if(noTechSupportFlag){
            afterSaleServiceStandardInfoDto.setTechnicalDirectEffectTime("");
            afterSaleServiceStandardInfoDto.setTechnicalDirectResponseTime("");
            if(afterSaleServiceStandardApply.getAfterSaleStandardStatus()!=null&&afterSaleServiceStandardApply.getAfterSaleStandardStatus()==2) {

                afterSaleServiceStandardApply.setTechnicalDirectEffectTime("");
                afterSaleServiceStandardApply.setTechnicalDirectResponseTime("");
            }
        }
        if(noGuaranteeFlag){
            afterSaleServiceStandardInfoDto.setGuaranteePolicyRepaireTime("");
            afterSaleServiceStandardInfoDto.setGuaranteePolicyGuaranteeType("");
            afterSaleServiceStandardInfoDto.setGuaranteePolicyHostGuaranteePeriod("");
            afterSaleServiceStandardInfoDto.setGuaranteePolicyPartsGuaranteePeriod("");
            afterSaleServiceStandardInfoDto.setGuaranteePolicyCycleCaltype(null);
            afterSaleServiceStandardInfoDto.setGuaranteePolicyArea(null);
            afterSaleServiceStandardInfoDto.setGuaranteePolicyAreaComment("");
            afterSaleServiceStandardInfoDto.setGuaranteePolicyResponseTime("");
            afterSaleServiceStandardInfoDto.setGuaranteePolicyVisitTime("");
            afterSaleServiceStandardInfoDto.setGuaranteePolicyRepaireComment("");
            if(afterSaleServiceStandardApply.getAfterSaleStandardStatus()!=null&&afterSaleServiceStandardApply.getAfterSaleStandardStatus()==2) {
                afterSaleServiceStandardApply.setGuaranteePolicyRepaireTime("");
                afterSaleServiceStandardApply.setGuaranteePolicyGuaranteeType("");
                afterSaleServiceStandardApply.setGuaranteePolicyHostGuaranteePeriod("");
                afterSaleServiceStandardApply.setGuaranteePolicyPartsGuaranteePeriod("");
                afterSaleServiceStandardApply.setGuaranteePolicyCycleCaltype(null);
                afterSaleServiceStandardApply.setGuaranteePolicyArea(null);
                afterSaleServiceStandardApply.setGuaranteePolicyAreaComment("");
                afterSaleServiceStandardApply.setGuaranteePolicyResponseTime("");
                afterSaleServiceStandardApply.setGuaranteePolicyVisitTime("");
                afterSaleServiceStandardApply.setGuaranteePolicyRepaireComment("");
            }
        }
        return afterSaleServiceStandardInfoDto;
    }

    private AfterSaleSupplyPolicyDto  defaultSupplierValue(CoreSkuGenerate skuGenerate, AfterSaleSupplyPolicyDto afterSaleSupplyPolicyDto)  {
        if(afterSaleSupplyPolicyDto==null){
            return afterSaleSupplyPolicyDto;
        }
        boolean noInstallFlag= skuGenerate.getIsInstallable()==null||skuGenerate.getIsInstallable()==0||afterSaleSupplyPolicyDto.getInstallPolicyInstallType()==null||
                afterSaleSupplyPolicyDto.getInstallPolicyInstallType()==2 ;
        boolean noTechSupportFlag= (afterSaleSupplyPolicyDto.getTechnicalDirectSupplyMaintain()!=null&&afterSaleSupplyPolicyDto.getTechnicalDirectSupplyMaintain()== 0);
        boolean noGuaranteeFlag= (afterSaleSupplyPolicyDto.getGuaranteePolicyIsGuarantee()!=null&&afterSaleSupplyPolicyDto.getGuaranteePolicyIsGuarantee()== 0);
        //默认值处理
        if(noInstallFlag  ){
            afterSaleSupplyPolicyDto.setInstallPolicyResponseTime("");
            afterSaleSupplyPolicyDto.setInstallPolicyInstallFee(null);
            afterSaleSupplyPolicyDto.setInstallPolicyInstallArea(null);
            afterSaleSupplyPolicyDto.setInstallPolicyHaveInstallationQualification(null);
            afterSaleSupplyPolicyDto.setInstallPolicyFreeRemoteInstall(null);
            afterSaleSupplyPolicyDto.setInstallPolicyVisitTime(null);
            afterSaleSupplyPolicyDto.setInstallPolicyInstallTime(null);
        }
        if(noTechSupportFlag){
            afterSaleSupplyPolicyDto.setTechnicalDirectEffectTime("");
            afterSaleSupplyPolicyDto.setTechnicalDirectResponseTime("");
        }
        if(noGuaranteeFlag){
            afterSaleSupplyPolicyDto.setGuaranteePolicyRepaireTime("");
            afterSaleSupplyPolicyDto.setGuaranteePolicyGuaranteeType("");
            afterSaleSupplyPolicyDto.setGuaranteePolicyHostGuaranteePeriod("");
            afterSaleSupplyPolicyDto.setGuaranteePolicyPartsGuaranteePeriod("");
            afterSaleSupplyPolicyDto.setGuaranteePolicyCycleCaltype(null);
            afterSaleSupplyPolicyDto.setGuaranteePolicyArea(null);
            afterSaleSupplyPolicyDto.setGuaranteePolicyAreaComment("");
            afterSaleSupplyPolicyDto.setGuaranteePolicyResponseTime("");
            afterSaleSupplyPolicyDto.setGuaranteePolicyVisitTime("");
            afterSaleSupplyPolicyDto.setGuaranteePolicyRepaireComment("");
        }
        return afterSaleSupplyPolicyDto;
    }


    private void docFlags(ModelAndView mv,String skuNo){

        List<ApprovedSkuAfterSalesInfoVO> installDebugInfo = afterSaleServiceStandardService.getAllApprovedSkuAfterSalesInfo(skuNo);
        if(CollectionUtils.isNotEmpty(installDebugInfo)){
            installDebugInfo.forEach(item->{
                SkuSubTypeEnum subtype=SkuSubTypeEnum.fromCode(item.getSubtype());
                switch (subtype) {
                    case INSTRUCTION_MANUAL:
                        mv.addObject("docInstructionManual", true);
                        break;
                    case OPERATION_FLOW_CARD:
                        mv.addObject("docOperationFlowCard", true);
                        break;
                    case GUIDE:
                        mv.addObject("docGuide", true);
                        break;
                    case PACKING_LIST:
                        mv.addObject("docPackingList", true);
                        break;
                    case SECURITY_ADJUSTMENT_VIDEO:
                        mv.addObject("docSecurityAdjustmentVideo", true);
                        break;
                    case PRODUCT_PPT:
                        mv.addObject("docProductPPT", true);
                        break;
                    case TECHNICAL_CONTACT:
                        mv.addObject("docTechnicalContact", true);
                        break;
                    case AFTER_SALES_MANAGER:
                        mv.addObject("docAfterSalesManager", true);
                        break;
                    case MANUAL:
                        mv.addObject("docManual", true);
                        break;
                    case CASE_STUDY:
                        mv.addObject("docCaseStudy", true);
                        break;
                    case REPAIR_VIDEO:
                        mv.addObject("docRepairVideo", true);
                        break;
                    case MAINTENANCE:
                        mv.addObject("docMaintenance", true);
                        break;
                    case TRAINING:
                        mv.addObject("docTraining", true);
                        break;
                    case SURVEY:
                        mv.addObject("docSurvey", true);
                        break;
                    case OTHER:
                        mv.addObject("docOther", true);
                        break;
                    default:
                        // 未知类型可选处理
                        break;
                }
            });

        }
    }


    private void receieveVerifyInfo(ModelAndView mv,long serviceStandardApplyId) {

        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "afterSaleStandardVerify_" + serviceStandardApplyId);
        mv.addObject("historicInfo", historicInfo);
        mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        mv.addObject("commentMap", historicInfo.get("commentMap"));

        if (historicInfo.get("taskInfo") != null) {

            Task taskInfo = (Task) historicInfo.get("taskInfo");
            mv.addObject("taskId",taskInfo.getId());
            mv.addObject("taskInfo", taskInfo);

            //获取审核人候选组
            Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

            if(CollectionUtils.isNotEmpty(candidateUserList)){

                List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

                mv.addObject("verifyUsers", StringUtils.join(userNameList,","));
            }
        }
    }

    /**
     * 新增供应商售后政策
     */
    @RequestMapping(value = "/toAddSupplyAfterSalePolicy")
    public ModelAndView toAddSupplyAfterSalePolicy(HttpServletRequest request,
                               @Param("skuNo") String skuNo,
                               @RequestParam(required = false)  @Param("traderId") String traderId,
                               @RequestParam(required = false)@Param("traderName") String traderName) {

        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        CoreSkuGenerate skuGenerate = coreSkuGenerateMapper.selectBySkuNo(skuNo);
        ModelAndView mv = new ModelAndView();

        CoreSpuGenerate spuInfo = this.vGoodsService.findSpuInfoBySkuNo(skuNo);
        mv.addObject("spuType", spuInfo.getSpuType());
        mv.addObject("skuNo",skuNo);
        mv.addObject("traderId",traderId);
        mv.addObject("traderName",traderName);
        mv.addObject("skuGenerate",skuGenerate);

        mv.setViewName("aftersales/serviceStandard/addSupplyAfterSalePolicy");
        return mv;
    }

    /**
     * 新增供应商售后政策
     * @param addSupplyAfterSalePolicyDto
     */
    @RequestMapping(value = "/addSupplyAfterSalePolicy")
    public ModelAndView addSupplyAfterSalePolicy(HttpServletRequest request, AddSupplyAfterSalePolicyDto addSupplyAfterSalePolicyDto){

        ModelAndView mv = new ModelAndView();

        try{
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

            this.afterSaleServiceStandardService.addSupplyAfterSalePolicy(request,addSupplyAfterSalePolicyDto,user);

            mv.addObject("url", "./detail.do?skuNo=" + addSupplyAfterSalePolicyDto.getSkuNo());
            return success(mv);
        }catch(Exception e){
            LOGGER.error("addSupplyAfterSalePolicy error",e);
            return fail(mv);
        }
    }

    /**
     * 供应商售后政策编辑页面
     */
    @RequestMapping(value = "/toModifySupplyPolicyPage")
    public ModelAndView toModifySupplyPolicyPage(HttpServletRequest request,@Param(value="supplyPolicyId") Long supplyPolicyId){

        AfterSaleSupplyPolicyDto afterSaleSupplyPolicyDto= this.afterSaleServiceStandardService.findSupplyAfterSalePolicy(supplyPolicyId);

        List<AfterSaleSupplyPolicyAttachment> attashmentList = afterSaleServiceStandardService.findSupplyAfterSalePolicyAttashMent(supplyPolicyId);

        CoreSkuGenerate skuGenerate = coreSkuGenerateMapper.selectBySkuNo(afterSaleSupplyPolicyDto.getSkuNo());

        ModelAndView mv = new ModelAndView();

        CoreSpuGenerate spuInfo = this.vGoodsService.findSpuInfoBySkuNo(afterSaleSupplyPolicyDto.getSkuNo());
        mv.addObject("spuType", spuInfo.getSpuType());
        mv.addObject("afterSaleSupplyPolicyDto",afterSaleSupplyPolicyDto);
        mv.addObject("skuGenerate",skuGenerate);
        mv.addObject("attashmentList",attashmentList);
        mv.addObject("skuNo",afterSaleSupplyPolicyDto.getSkuNo());


        mv.setViewName("aftersales/serviceStandard/editSupplyAfterSalePolicy");
        return mv;
    }

    /**
     * 编辑供应商售后政策
     * @param editSupplyAfterSalePolicyDto
     */
    @RequestMapping(value = "/modifySupplyPolicy")
    public ModelAndView modifySupplyPolicy(HttpServletRequest request, EditSupplyAfterSalePolicyDto editSupplyAfterSalePolicyDto){

        ModelAndView mv = new ModelAndView();

        try{
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

            this.afterSaleServiceStandardService.modifySupplyAfterSalePolicy(request,editSupplyAfterSalePolicyDto,user);

            mv.addObject("url", "./detail.do?skuNo=" + editSupplyAfterSalePolicyDto.getSkuNo());
            return success(mv);
        }catch(Exception e){
            LOGGER.error("addSupplyAfterSalePolicy error",e);
            return fail(mv);
        }
    }


    @ResponseBody
    @RequestMapping(value="/deleteSupplyPolicy")
    public ResultInfo<Position> deleteSupplyPolicy(HttpServletRequest request, @Param(value="supplyPolicyId") Long supplyPolicyId){
        ResultInfo<Position> resultInfo = new ResultInfo<Position>();
        try{
            afterSaleServiceStandardService.deleteSupplyPolicyById(supplyPolicyId);
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
        }catch (Exception e){
            LOGGER.error("deleteSupplyPolicy",e);
        }
        return resultInfo;
    }

    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value="/haveFactoryServicesPolicy")
    public ResultInfo<AfterSaleSupplyPolicyDto> haveFactoryServicesPolicy(HttpServletRequest request, @Param(value="skuNo") String skuNo,
                                                                          @RequestParam(required = false) Long excludeSupplyPolicyId){
        ResultInfo<AfterSaleSupplyPolicyDto> resultInfo = new ResultInfo<AfterSaleSupplyPolicyDto>();
        try{

            List<AfterSaleSupplyPolicyDto> supplyPolicyList  = afterSaleServiceStandardService.findSupplyPolicyBySkuNoAndServiceType(skuNo,1);

            boolean isExsit = false;

            supplyPolicyList = supplyPolicyList.stream().filter(policy -> {

                if(excludeSupplyPolicyId == null){
                    return true;
                }

                return !excludeSupplyPolicyId.equals(policy.getSupplyPolicyId());

            }).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(supplyPolicyList)){
                resultInfo.setCode(0);
                resultInfo.setMessage("操作成功");
            }

        }catch (Exception e){
            LOGGER.error("haveFactoryServicesPolicy",e);
        }
        return resultInfo;
    }


    /**
     * 维护贝登标准页
     */
    @RequestMapping(value = "/toBdAfterSalePolicyApply")
    public ModelAndView toBdAfterSalePolicyApply(HttpServletRequest request,
                                                 @Param("skuNo") String skuNo) {

        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        AfterSaleServiceStandardApply afterSaleServiceStandardApply = null;

        AfterSaleServiceStandardApply afterSaleServiceStandardApplyInDb = afterSaleServiceStandardService.selectAfterSaleServiceStandardBySkuNo(skuNo);

        if(afterSaleServiceStandardApplyInDb == null){

            afterSaleServiceStandardApply = new AfterSaleServiceStandardApply();
            afterSaleServiceStandardApply.setSkuNo(skuNo);
            afterSaleServiceStandardApply.setCreator(user.getUserId());
            afterSaleServiceStandardApply.setUpdator(user.getUserId());

            String nowDate = DateUtil.getNowDate(DateUtil.TIME_FORMAT);
            afterSaleServiceStandardApply.setAddTime(nowDate);
            afterSaleServiceStandardApply.setModTime(nowDate);

            List<AfterSaleSupplyPolicyDto> afterSaleSupplyPolicyDtoList = this.afterSaleServiceStandardService.getSupplyAfterSalePolicyListBySkuNo(skuNo);

            afterSaleServiceStandardApply.setSupplyAfterSaleIsMaintain(CollectionUtils.isEmpty(afterSaleSupplyPolicyDtoList) ? 0 : 1);

            afterSaleServiceStandardService.insertServiceStandardApply(afterSaleServiceStandardApply);

        }else{
            afterSaleServiceStandardApply = afterSaleServiceStandardApplyInDb;
        }

        ModelAndView mv = new ModelAndView();

        CoreSpuGenerate spuInfo = this.vGoodsService.findSpuInfoBySkuNo(skuNo);
        mv.addObject("spuType", spuInfo.getSpuType());

        List<AfterSaleServiceLabelDto> labelDtos = afterSaleServiceStandardService.getAllAfterSaleServiceLabels(skuNo);


        mv.addObject("skuNo",skuNo);
        mv.addObject("labels",labelDtos);
        mv.addObject("afterSaleServiceStandardApply",afterSaleServiceStandardApply);
        mv.addObject("skuGenerate",coreSkuGenerateMapper.selectBySkuNo(skuNo));
        mv.addObject("attashmentList",afterSaleServiceStandardService.selectAfterSaleServiceStandardAttashmentList(afterSaleServiceStandardApply.getServiceStandardApplyId()));
        mv.addObject("applyInstallArea",afterSaleServiceStandardService.selectServiceStandardApplyInstallArea(afterSaleServiceStandardApply.getServiceStandardApplyId()));

        mv.setViewName("aftersales/serviceStandard/bdAfterSalePolicyApplyEdit");
        return mv;
    }

    /**
     * 编辑贝登页面
     */
    @RequestMapping(value = "/editBdAfterSalePolicyApply")
    public ModelAndView editBdAfterSalePolicyApply(HttpServletRequest request,EditBdAfterSalePolicyDto editBdAfterSalePolicyDto) {

        ModelAndView mv = new ModelAndView();

        try{
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

            this.afterSaleServiceStandardService.editBdAfterSalePolicyApply(request,editBdAfterSalePolicyDto,user);

            startProcessInstance(request,editBdAfterSalePolicyDto);

            mv.addObject("url", "./detail.do?skuNo=" + editBdAfterSalePolicyDto.getSkuNo());
            return success(mv);
        }catch(Exception e){
            LOGGER.error("addSupplyAfterSalePolicy error",e);
            return fail(mv);
        }
    }

    //开始工作流
    private void startProcessInstance(HttpServletRequest request, EditBdAfterSalePolicyDto editBdAfterSalePolicyDto) throws Exception{

        //初始化工作流变量
        Map<String, Object> variableMap = new HashMap<String, Object>();

        AfterSaleServiceStandardApply afterSaleServiceStandardApply = afterSaleServiceStandardService.selectAfterSaleServiceStandardBySkuNo(editBdAfterSalePolicyDto.getSkuNo());

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        variableMap.put("currentAssinee", user.getUsername());
        variableMap.put("processDefinitionKey", "afterSaleStandardVerify");
        variableMap.put("skuNo", editBdAfterSalePolicyDto.getSkuNo());
        variableMap.put("serviceStandardApplyId", afterSaleServiceStandardApply.getServiceStandardApplyId());
        variableMap.put("businessKey", "afterSaleStandardVerify_" + afterSaleServiceStandardApply.getServiceStandardApplyId());

        actionProcdefService.createProcessInstance(request, variableMap.get("processDefinitionKey").toString(), variableMap.get("businessKey").toString(), variableMap);

        // 获取当前活动节点
        Task taskInfo = processEngine.getTaskService()
                .createTaskQuery()
                .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                .singleResult();

        //完成当前任务
        actionProcdefService.complementTask(request, taskInfo.getId(), "", user.getUsername(), variableMap);
    }

    /**
     * 参考同类商品
     */
    @ResponseBody
    @RequestMapping(value = "/referenceSimilarProductList",produces = "application/json;charset=UTF-8")
    public ModelAndView referenceSimilarProductList(HttpServletRequest request, ReferenceSimilarProductQueryDto queryDto,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize) throws Exception {

        Page page = super.getPageTag(request,pageNo,pageSize);

        CoreSpu coreSpu = coreSpuMapper.getSpuBySku(queryDto.getSkuNo());
        queryDto.setCategoryId(coreSpu.getCategoryId());

        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        //查询品牌列表
        Brand brand = new Brand();
        brand.setCompanyId(user.getCompanyId());
        List<BrandGenerate> brandList = brandService.getBrandInfoByParam();

        //查询最大和最小的安装费
        Map<String,String> installFeeMap = afterSaleServiceStandardService.queryMaxAndMinInstallFee(queryDto);

        //分页查询参考商品列表
        List<AfterSaleServiceStandardApplyDto> similarProductList = this.afterSaleServiceStandardService.querySimilarProductListPage(queryDto,page);

        ModelAndView mv=new ModelAndView("/aftersales/serviceStandard/similarProductIndex");

        mv.addObject("similarProductList",similarProductList);
        mv.addObject("queryDto",queryDto);
        mv.addObject("installFeeMap",installFeeMap);
        mv.addObject("brandList",brandList);
        mv.addObject("page",page);

        return mv;
    }


    /**
     * 供应商售后政策
     */
    @ResponseBody
    @RequestMapping(value = "/toCopySupplyAfterSalePolicy",produces = "application/json;charset=UTF-8")
    public ModelAndView toCopySupplyAfterSalePolicy(HttpServletRequest request, CopySupplyAfterSalePolicyQueryDto queryDto,
                                                    @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                                    @RequestParam(required = false) Integer pageSize) throws Exception {

        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        Page page = super.getPageTag(request,pageNo,pageSize);

        if(StringUtils.isEmpty(queryDto.getPolicyMatained())){
            queryDto.setPolicyMatained("0");
        }

        AfterSaleSupplyPolicyDto afterSaleSupplyPolicyDto = this.afterSaleServiceStandardService.findSupplyAfterSalePolicy(queryDto.getSupplyPolicyId());
        queryDto.setTraderId(afterSaleSupplyPolicyDto.getTraderId());

        //分页查询参考商品列表
        List<CopySupplyAfterSalePolicyDto> supplyAfterSaleList = this.afterSaleServiceStandardService.querySupplyAfterSalePolicyListPage(queryDto,page);
        if(StringUtils.isEmpty(queryDto.getPolicyMatained())){
            List<String> skuNosList = afterSaleServiceStandardService.findSupplyAfterSalePolicyByTraderId(afterSaleSupplyPolicyDto.getTraderId()).stream().map(e->e.getSkuNo()).collect(Collectors.toList());
            supplyAfterSaleList.stream().forEach(e->{
                e.setMaintained(skuNosList.contains(e.getSkuNo())? "1":"0");
            });
        }

        ModelAndView mv=new ModelAndView("/aftersales/serviceStandard/copySupplyAfterSalePolicy");

        mv.addObject("page",page);
        mv.addObject("queryDto",queryDto);
        mv.addObject("afterSaleSupplyPolicyDto",afterSaleSupplyPolicyDto);
        mv.addObject("supplyAfterSaleList",supplyAfterSaleList);
        return mv;
    }


    /**
     * 维护安装区域
     * @param request
     * @param queryDto
     * @param pageNo
     * @param pageSize
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/maintainInstallArea",produces = "application/json;charset=UTF-8")
    public ModelAndView maintainInstallArea(HttpServletRequest request, MaintainInstallAreaDto queryDto,
                                                    @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                                    @RequestParam(required = false) Integer pageSize) throws Exception {

        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        Page page = super.getPageTag(request,pageNo,pageSize);

        CoreSpu coreSpu = coreSpuMapper.getSpuBySku(queryDto.getSkuNo());

        queryDto.setBrandId(coreSpu.getBrandId());

        //维护安装区域列表
        List<AfterSaleServiceStandardApplyDto> maintainInstallAreaList = this.afterSaleServiceStandardService.maintainInstallAreaListPage(queryDto,page);

        CoreSkuGenerate skuGenerate = coreSkuGenerateMapper.selectBySkuNo(queryDto.getSkuNo());

        ModelAndView mv=new ModelAndView("/aftersales/serviceStandard/maintainInstallAreaListIndex");

        mv.addObject("page",page);
        mv.addObject("queryDto",queryDto);
        mv.addObject("skuGenerate",skuGenerate);
        mv.addObject("maintainInstallAreaList",maintainInstallAreaList);

        return mv;
    }

    /**
     * 维护安装区域-查看安装区域
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/queryInstallAreaList",produces = "application/json;charset=UTF-8")
    public ModelAndView queryInstallAreaList(HttpServletRequest request,@Param("skuNo") String skuNo) throws Exception {

        ModelAndView mv = new ModelAndView("/aftersales/serviceStandard/installAreaListIndex");
        AfterSaleServiceStandardApplyInstallArea installArea = this.afterSaleServiceStandardService.queryInstallArea(skuNo);
        mv.addObject("installArea",installArea);
        return mv;
    }


    /**
     * 参考供应商的售后政策
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/referenceSupplyAfterSalePolicy",produces = "application/json;charset=UTF-8")
    public ModelAndView referenceSupplyAfterSalePolicy(HttpServletRequest request,@Param("skuNo") String skuNo,
                                                       @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                                       @RequestParam(required = false) Integer pageSize,
                                                       @RequestParam(required = false) String searchInfo,
                                                       @RequestParam(required = false) Integer policyType,
                                                       @RequestParam(required = false) Integer formerSupplyPolicyId) throws Exception {

        Page page = super.getPageTag(request,pageNo,pageSize);
        List<AfterSaleSupplyPolicyDto> supplyPolicyList = this.afterSaleServiceStandardService.referenceSupplyAfterSalePolicy(searchInfo,page);

        supplyPolicyList.stream().forEach(supplyPolicy -> {
            Map<String,BigDecimal> returnMap = afterSaleServiceStandardService.findLastestYearNumAndAmout(skuNo,supplyPolicy.getTraderId());
            supplyPolicy.setLatestOneYearNum(returnMap == null ? "0" :  returnMap.get("latestOneYearNum").toString());
            supplyPolicy.setLatestOneYearAmout(returnMap == null ? "0" : returnMap.get("latestOneYearAmout").toString());
        });

        ModelAndView mv = new ModelAndView("/aftersales/serviceStandard/referenceSupplyPolicyIndex");
        mv.addObject("supplyPolicyList",supplyPolicyList);
        mv.addObject("skuNo",skuNo);
        mv.addObject("searchInfo",searchInfo);
        mv.addObject("page",page);
        mv.addObject("formerSupplyPolicyId",formerSupplyPolicyId==null?0:formerSupplyPolicyId);
        mv.addObject("policyType",policyType==null?0:policyType);
        return mv;
    }

    /**
     * 查询服务联系人
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/queryServiceContactList",produces = "application/json;charset=UTF-8")
    public ModelAndView queryServiceContactList(HttpServletRequest request,@Param("traderId") Integer traderId) throws Exception {
        TraderSupplier traderSupplier = new TraderSupplier();
        traderSupplier.setTraderId(traderId);
        TraderSupplierVo traderSupplierVo = traderSupplierService.getTraderSupplierBaseInfo(traderSupplier);
        ModelAndView mv = new ModelAndView("/aftersales/serviceStandard/serviceContactListIndex");
        mv.addObject("traderSupplierVo",traderSupplierVo);
        return mv;
    }


    /**
     * 复制供应商原始厂商的售后政策
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/copyOrginalFactorySupplyPolicy",produces = "application/json;charset=UTF-8")
    public ResultInfo copyOrginalFactorySupplyPolicy(HttpServletRequest request,
                                                @Param("skuNo") String skuNo,
                                                @Param("traderId") Long traderId) throws Exception {
        try{
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

            Long supplyPolicyId = this.afterSaleServiceStandardService.copyOrginalFactorySupplyPolicy(skuNo,traderId,user);

            return new ResultInfo(0,"成功",supplyPolicyId);

        } catch (Exception e) {
            logger.error("copyOrginalFactorySupplyPolicy error:", e);
            return new ResultInfo<>(-1, "复制供应商原始厂商的售后政策异常");
        }
    }


    /**
     * 查询售后服务申请的状态
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/queryAfterSaleApplyStatus",produces = "application/json;charset=UTF-8")
    public ResultInfo queryAfterSaleApplyStatus(HttpServletRequest request,
                                                     @Param("traderId") Long serviceStandardApplyId) throws Exception {
        try{

            AfterSaleServiceStandardApply afterSaleServiceStandardApply = this.afterSaleServiceStandardService.findServiceStandardApplyById(serviceStandardApplyId);

            if(afterSaleServiceStandardApply == null || afterSaleServiceStandardApply.getAfterSaleStandardStatus() != 1){
                return new ResultInfo<>(-1, "");
            }

            return new ResultInfo(0,"成功");

        } catch (Exception e) {
            logger.error("queryAfterSaleApplyStatus error:", e);
            return new ResultInfo<>(-1, "查询售后服务申请的状态");
        }
    }



    /**
     * 复制原始厂商的售后政策到当前供应上售后政策
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/copyOrginalFactoryToCurrentSuppplyPolicy",produces = "application/json;charset=UTF-8")
    public ResultInfo copyOrginalFactoryToCurrentSuppplyPolicy(HttpServletRequest request,
                                                     @Param("supplyPolicyId") Long supplyPolicyId) throws Exception {
        try{
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

            this.afterSaleServiceStandardService.copyOrginalFactoryToCurrentSuppplyPolicy(supplyPolicyId,user);

            return new ResultInfo(0,"成功");

        } catch (Exception e) {
            logger.error("copyOrginalFactorySupplyPolicy error:", e);
            return new ResultInfo<>(-1, "复制供应商原始厂商的售后政策异常");
        }
    }



    /**
     * 判断skuNo某个供应商的售后政策是存在
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/supplyPolicyIsExsit",produces = "application/json;charset=UTF-8")
    public ResultInfo supplyPolicyIsExsit(HttpServletRequest request,
                                                     @Param("skuNo") String skuNo,
                                                     @Param("traderId") Long traderId,
                                                     @RequestParam(required = false) Long excludeSupplyPolicyId) throws Exception {
        try{

            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

            List<AfterSaleSupplyPolicyDto> supplyList = this.afterSaleServiceStandardService.getSupplyAfterSalePolicyListBySkuNo(skuNo);

            if(CollectionUtils.isEmpty(supplyList)){
                return new ResultInfo<>(-1, "无供应商的售后政策");
            }

            boolean isExsit = false;

            for(AfterSaleSupplyPolicyDto  supply : supplyList){

                if(excludeSupplyPolicyId != null && excludeSupplyPolicyId.equals(supply.getSupplyPolicyId())){
                    continue;
                }

                if(supply.getTraderId().equals(traderId)){
                    isExsit = true;
                    break;
                }

            }


            return new ResultInfo(isExsit? 1: -1,"成功");

        } catch (Exception e) {
            logger.error("supplyPolicyIsExsit error:", e);
            return new ResultInfo<>(-1, "供应商的售后政策是否存在异常");
        }
    }

    /**
     * 复制参考售后政策
     * @param request
     * @param supplyPolicyId 参考供应商为供应商售后表id，参考贝登为贝登售后id
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/copySupplyAfterSalePolicy",produces = "application/json;charset=UTF-8")
    public ResultInfo copySupplyAfterSalePolicy(HttpServletRequest request,
                                                  @Param("skuNo") String skuNo,
                                                  @Param("supplyPolicyId") Long supplyPolicyId,
                                                @RequestParam(required = false) Integer copyType,
                                                @RequestParam(required = false) Integer policyType,
                                                @RequestParam(required = false) Integer formerSupplyPolicyId) throws Exception {
        try{
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            ResultInfo resultInfo = new ResultInfo(0,"成功");
            if(policyType != null && policyType == 2){
                resultInfo = this.afterSaleServiceStandardService.copySupplyAfterSalePolicyList(skuNo,supplyPolicyId,user,copyType,formerSupplyPolicyId);
            }else {
                this.afterSaleServiceStandardService.copySupplyAfterSalePolicy(skuNo,supplyPolicyId,copyType);
            }
            return resultInfo;
        } catch (Exception e) {
            logger.error("batchSaveBhSaleorderGoods 2:", e);
            return new ResultInfo<>(-1, "供应商的售后政策异常");
        }
    }

    /**
     * 复制参考供应商的售后政策
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/copySupplyAfterSalePolicyList",produces = "application/json;charset=UTF-8")
    public ResultInfo copySupplyAfterSalePolicyList(HttpServletRequest request,
                                                @Param("skuNo") String skuNo,
                                                @RequestParam(required = false) Integer copyType,
                                                    @RequestParam(required = false) Integer formerSupplyPolicyId,
                                                @Param("supplyPolicyId") Long supplyPolicyId) throws Exception {
        try{

            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

            this.afterSaleServiceStandardService.copySupplyAfterSalePolicyList(skuNo,supplyPolicyId,user,copyType,formerSupplyPolicyId);

            return new ResultInfo(0,"成功");
        } catch (Exception e) {
            logger.error("copySupplyAfterSalePolicyList:", e);
            return new ResultInfo<>(-1, "供应商的售后政策异常");
        }
    }






    /**
     * 审核结果页面
     * @param taskId
     * @param pass
     * @param serviceStandardApplyId
     * @return
     */
    @RequestMapping(value = "/auditResult")
    public ModelAndView complement(String taskId, Boolean pass,Long serviceStandardApplyId) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("taskId", taskId);
        mv.addObject("pass", pass);
        mv.addObject("serviceStandardApplyId", serviceStandardApplyId);

        mv.setViewName("/aftersales/serviceStandard/auditResult");
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "/complementTask")
    public ResultInfo<?> complementTask(HttpServletRequest request,String taskId, String comment, Boolean pass,
                                        HttpSession session) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        // add by Randy.Xu 2021/3/26 9:12 .Desc: . begin
        //VDERP-5839 数据越权修复
        //当前用户信息
        Integer curUserId = user.getUserId();
        User curUser = authService.getUserById(curUserId);
        //获取当前任务

        TaskService taskService = processEngine.getTaskService();
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        //获取审核流节点审核人员信息
        List<IdentityLink> identityLinksForTask = taskService.getIdentityLinksForTask(taskId);
        if(CollectionUtils.isNotEmpty(identityLinksForTask)){
            List<String> userNameList = new ArrayList<>();
            Boolean checKflag = false;
            for (IdentityLink identityLink : identityLinksForTask) {
                String userId = identityLink.getUserId();
                if(curUser.getUsername().equals(userId)){
                    checKflag = true;
                    break;
                }
            }
            if(!checKflag){
                logger.info("审核越权操作:接口[aftersale/serviceStandard/complementTask],行为[非审核人审核售后],操作人{}",user.getUsername());
            }
        }else{
            logger.info("销售越权操作:接口[aftersale/serviceStandard/complementTask],行为[非审核人审核售后],操作人{}",user.getUsername());
        }
        // add by Randy.Xu 2021/3/26 9:12 .Desc: . end

        // 审批操作
        try {

            Map<String, Object> taskVaribles = new HashMap<String, Object>();
            taskVaribles.put("pass", pass);

            actionProcdefService.complementTask(request, taskId, comment,user.getUsername(), taskVaribles);

            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("complementTask:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    @ResponseBody
    @RequestMapping(value = "/validateBuyOrderAfterSaleStaus")
    public ResultInfo<?> validateBuyOrderAfterSaleStaus(HttpServletRequest request,@RequestParam("buyorderId") Integer buyorderId) {

        try {

            Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderId);

            List<BuyorderGoodsVo> buyorderGoodsVoList = this.buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorderId);
            if(CollectionUtils.isEmpty(buyorderGoodsVoList)){
                return new ResultInfo(0, "操作成功");
            }

            StringBuffer sb = new StringBuffer();
            boolean flag = true;
            for(int i = 0;i <=  buyorderGoodsVoList.size() -1 ; i++ ){

                AfterSaleSupplyPolicy afterSaleSupplyPolicy = this.afterSaleServiceStandardService.
                        getSupplyAfterSalePolicyListBySkuNoAndTraderId(buyorderGoodsVoList.get(i).getSku(),buyorder.getTraderId().longValue());

                if(afterSaleSupplyPolicy != null){
                    continue;
                }

                sb.append(StringUtils.isBlank(buyorderGoodsVoList.get(i).getSku())?"":buyorderGoodsVoList.get(i).getSku());

                flag = false;

                if(i != buyorderGoodsVoList.size() -1){
                    sb.append(",");
                }

            };

            if(!flag){
                return new ResultInfo(-1, sb.toString() + "尚未维护该供应商的售后政策，");
            }

            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("validateBuyOrderAfterSaleStaus error:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }


    @ResponseBody
    @RequestMapping(value = "/getSupplierByName")
    public ModelAndView getSupplierByName(HttpServletRequest request, TraderSupplierVo traderSupplierVo,
                                          String supplierName, @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                          @RequestParam(value="callbackFuntion",required=false) String callbackFuntion) throws UnsupportedEncodingException {
        ModelAndView mav = new ModelAndView("aftersales/serviceStandard/search_supplier");
        supplierName = URLDecoder.decode(URLDecoder.decode(supplierName, "UTF-8"), "UTF-8");
        mav.addObject("supplierName", supplierName);

        mav.addObject("callbackFuntion", callbackFuntion);
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Page page = getPageTag(request, pageNo, 10);
        traderSupplierVo.setCompanyId(user.getCompanyId());
        traderSupplierVo.setTraderSupplierName(supplierName);
        traderSupplierVo.setIsEnable(ErpConst.ONE);
        traderSupplierVo.setRequestType("cg");// 采购搜索供应商用，其他地方可以不用
        // 查询所有职位类型为311的员工
        List<Integer> positionType = new ArrayList<>();
        positionType.add(SysOptionConstant.ID_311);// 采购
        List<User> userList = userService.getMyUserList(user, positionType, false);
        Map<String, Object> map = this.traderSupplierService.getSupplierByName(traderSupplierVo, page, userList);
        List<TraderSupplierVo> list = null;
        if (map != null) {
            list = (List<TraderSupplierVo>) map.get("list");
            page = (Page) map.get("page");
        }
        mav.addObject("list", list);
        mav.addObject("page", page);
        return mav;
    }

}
