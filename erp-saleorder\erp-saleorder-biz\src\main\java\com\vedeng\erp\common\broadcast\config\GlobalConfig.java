package com.vedeng.erp.common.broadcast.config;

import java.util.List;

import com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity;
import com.vedeng.erp.common.broadcast.MessageSubjectEnum;

import lombok.Data;

/**
 * 全局配置
 * @ClassName:  GlobalConfig   
 * @author: <PERSON><PERSON>yang
 * @date:   2025年6月6日 下午3:59:55    
 * @Copyright:
 */
@Data
public class GlobalConfig {
	
	/**全局公用配置表*/
	private BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics;
	
	/**播报部门配置表*/
	private List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList;
	
	/**图片配置*/
	private List<BroadcastContentConfigEntity> broadcastContentConfigStatisticsList;
	
	/**需要播报的主体枚举列表*/
	private List<MessageSubjectEnum> messageSubjectList;

}
