package com.vedeng.infrastructure.tyc.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.ResultInfo;
import com.vedeng.common.core.utils.HttpClientUtil;
import com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc;
import com.vedeng.infrastructure.tyc.domain.dto.TycResultDto;
import com.vedeng.infrastructure.tyc.mapper.TycMapper;
import com.vedeng.infrastructure.tyc.util.TycHttpSendUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

/**
 * 天眼查查询接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/11 20:11
 */
@Component
@Slf4j
public class TycSearchService {

    @Value("${tyc_api_url_search}")
    private String tycApiUrlSearch;

    @Value("${tyc_api_token}")
    private String tycApiToken;

    @Value("${http_url}")
    protected String httpUrl;

    @Value("${tyc_api_url_baseinfo}")
    private String getTycApiUrlBaseinfo;

    @Autowired
    private TycMapper tycMapper;


    /**
     * 根据公司名分页查询（目前由于Apollo配置，仅返回第一页20条）
     *
     * @param terminalName 公司名
     * @return PageInfo<TycResultDto>
     */
    public PageInfo<TycResultDto> searchByTerminalName(String terminalName) {
        PageInfo<TycResultDto> pageResult = new PageInfo<>();
        pageResult.setTotal(0);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", tycApiToken);
        Map<String, String> params = new HashMap<>();
        try {
            log.info("调用天眼查search接口，word：{}", terminalName);
            String encodeName = URLEncoder.encode(terminalName, "UTF-8");
            ResultInfo resultInfo = HttpClientUtil.doGet(tycApiUrlSearch + encodeName, headers, params);
            String result = resultInfo.getData().toString();
            JSONObject jsonObject = JSON.parseObject(result).getJSONObject("result");
            if (Objects.nonNull(jsonObject) && Objects.nonNull(jsonObject.get("items"))) {
                JSONArray items = JSON.parseArray(jsonObject.get("items").toString());
                List<TycResultDto> tycResultDtoList = items.toJavaList(TycResultDto.class);
                int total = Integer.parseInt(jsonObject.get("total").toString());
                // 天眼查接口限制，只能返回20条数据
                pageResult.setTotal(total > 20 ? 20 : total);
                pageResult.setList(tycResultDtoList);
            }
        } catch (Exception e) {
            log.error("调用天眼查接口异常", e);
        }
        return pageResult;
    }

    public List<TraderInfoTyc> searchLocalByNames(String names) {
        List<String> tcyNames = Arrays.asList(names.split(","));
        List<TraderInfoTyc> result = tycMapper.getTcyLocalByTraderName(tcyNames);
        return result;
    }


    /**
     * 根据客户名称获取天眼查客户详情
     * @param traderName
     * @return
     */
    public TraderInfoTyc getTycInfo(String traderName) {
         if(StringUtils.isEmpty(traderName)) {
             return null;
         }
        TraderInfoTyc tycInfoQuery = new TraderInfoTyc();
        if(traderName.indexOf("\"")==0) {
            traderName = traderName.substring(1,traderName.length());   //去掉第一个 "
        }
        if(traderName.lastIndexOf("\"")==(traderName.length()-1)) {
            traderName = traderName.substring(0,traderName.length()-1);
        }
        tycInfoQuery.setName(traderName);
        TraderInfoTyc res = tycMapper.getTraderInfoTycByTraderName(tycInfoQuery);
        int dayCnt = 0;

        if (res != null) {
            dayCnt = (int) ((res.getSyncTime() - System.currentTimeMillis()) / (1000 * 60 * 60 * 24));
        }
        try {
            if (res == null || Math.abs(dayCnt) >= 15) {
                // 记录调用天眼查API的原因
                if (res == null) {
                    log.info("天眼查API调用：企业信息不存在于缓存中，首次查询企业：{}", traderName);
                } else {
                    log.info("天眼查API调用：缓存已过期，企业：{}，缓存时间：{}天，重新查询", traderName, Math.abs(dayCnt));
                }
                
                String result = TycHttpSendUtil.queryDetails(getTycApiUrlBaseinfo,tycApiToken, traderName);
                JSONObject jsonObject = JSONObject.parseObject(result);
                String code = jsonObject.getString("error_code");
                if ("300000".equals(code)) {
                    res = new TraderInfoTyc();
                    res.setCodeType(2);//查无数据
                    return res;
                } else if ("300006".equals(code)) {
                    res = new TraderInfoTyc();
                    res.setCodeType(3);//余额不足
                    return res;
                } else {
                    JSONObject json = JSONObject.parseObject(jsonObject.getString("result"));

                    TraderInfoTyc tycInfo = new TraderInfoTyc();
                    if (json.containsKey("updatetime") && StringUtils.isNumeric(json.getString("updatetime"))) {
                        tycInfo.setUpdateTime(Long.parseLong(json.getString("updatetime")));
                    }
                    if (json.containsKey("fromTime") && StringUtils.isNumeric(json.getString("fromTime"))) {
                        tycInfo.setFromTime(Long.parseLong(json.getString("fromTime")));
                    }
                    if (json.containsKey("categoryScore") && StringUtils.isNumeric(json.getString("categoryScore"))) {
                        tycInfo.setCategoryScore(Integer.parseInt(json.getString("categoryScore")));
                    }
                    if (json.containsKey("type") && StringUtils.isNumeric(json.getString("type"))) {
                        tycInfo.setType(Integer.parseInt(json.getString("type")));
                    }
                    if (json.containsKey("id") && StringUtils.isNumeric(json.getString("id"))) {
                        tycInfo.setId(Long.parseLong(json.getString("id")));
                    }
                    if (json.containsKey("percentileScore") && StringUtils.isNumeric(json.getString("percentileScore"))) {
                        tycInfo.setPercentileScore(Integer.parseInt(json.getString("percentileScore")));
                    }
                    if (json.containsKey("regNumber")) {
                        tycInfo.setRegNumber(json.getString("regNumber"));
                    }
                    if (json.containsKey("phoneNumber")) {
                        tycInfo.setPhoneNumber(json.getString("phoneNumber"));
                    }
                    if (json.containsKey("regCapital")) {
                        tycInfo.setRegCapital(json.getString("regCapital"));
                    }
                    if (json.containsKey("name")) {
                        tycInfo.setName(json.getString("name"));
                    }
                    if (json.containsKey("regInstitute")) {
                        tycInfo.setRegInstitute(json.getString("regInstitute"));
                    }
                    if (json.containsKey("regLocation")) {
                        tycInfo.setRegLocation(json.getString("regLocation"));
                    }
                    if (json.containsKey("industry")) {
                        tycInfo.setIndustry(json.getString("industry"));
                    }
                    if (json.containsKey("approvedTime") && StringUtils.isNumeric(json.getString("approvedTime"))) {
                        tycInfo.setApprovedTime(Long.parseLong(json.getString("approvedTime")));
                    }
                    if (json.containsKey("orgApprovedInstitute")) {
                        tycInfo.setOrgApprovedInstitute(json.getString("orgApprovedInstitute"));
                    }
                    if (json.containsKey("logo")) {
                        tycInfo.setLogo(json.getString("logo"));
                    }
                    if (json.containsKey("taxNumber")) {
                        tycInfo.setTaxNumber(json.getString("taxNumber"));
                    }
                    if (json.containsKey("businessScope")) {
                        tycInfo.setBusinessScope(json.getString("businessScope"));
                    }
                    if (json.containsKey("orgNumber")) {
                        tycInfo.setOrgNumber(json.getString("orgNumber"));
                    }
                    if (json.containsKey("estiblishTime") && StringUtils.isNumeric(json.getString("estiblishTime"))) {
                        tycInfo.setEstiblishTime(Long.parseLong(json.getString("estiblishTime")));
                    }
                    if (json.containsKey("regStatus")) {
                        tycInfo.setRegStatus(json.getString("regStatus"));
                    }
                    if (json.containsKey("legalPersonName")) {
                        tycInfo.setLegalPersonName(json.getString("legalPersonName"));
                    }
                    if (json.containsKey("websiteList")) {
                        tycInfo.setWebsiteList(json.getString("websiteList"));
                    }
                    if (json.containsKey("toTime") && StringUtils.isNumeric(json.getString("toTime"))) {
                        tycInfo.setToTime(Long.parseLong(json.getString("toTime")));
                    }
                    if (json.containsKey("legalPersonId") && StringUtils.isNumeric(json.getString("legalPersonId"))) {
                        tycInfo.setLegalPersonId(Long.parseLong(json.getString("legalPersonId")));
                    }
                    if (json.containsKey("sourceFlag")) {
                        tycInfo.setSourceFlag(json.getString("sourceFlag"));
                    }
                    if (json.containsKey("actualCapital")) {
                        tycInfo.setActualCapital(json.getString("actualCapital"));
                    }
                    if (json.containsKey("flag") && StringUtils.isNumeric(json.getString("flag"))) {
                        tycInfo.setFlag(Integer.parseInt(json.getString("flag")));
                    }
                    if (json.containsKey("correctCompanyId")) {
                        tycInfo.setCorrectCompanyId(json.getString("correctCompanyId"));
                    }
                    if (json.containsKey("companyOrgType")) {
                        tycInfo.setCompanyOrgType(json.getString("companyOrgType"));
                    }
                    if (json.containsKey("updateTimes") && StringUtils.isNumeric(json.getString("updateTimes"))) {
                        //新的tyc api没有updateTime属性
                        tycInfo.setUpdateTime(Long.parseLong(json.getString("updateTimes")));
                        tycInfo.setUpdateTimes(Long.parseLong(json.getString("updateTimes")));
                    }
                    if (json.containsKey("base")) {
                        tycInfo.setBase(json.getString("base"));
                    }
                    if (json.containsKey("companyType") && StringUtils.isNumeric(json.getString("companyType"))) {
                        tycInfo.setCompanyType(Integer.parseInt(json.getString("companyType")));
                    }
                    if (json.containsKey("creditCode")) {
                        tycInfo.setCreditCode(json.getString("creditCode"));
                    }
                    if (json.containsKey("companyId") && StringUtils.isNumeric(json.getString("companyId"))) {
                        tycInfo.setCompanyId(Long.parseLong(json.getString("companyId")));
                    }
                    if (json.containsKey("historyNames")) {
                        tycInfo.setHistoryNames(json.getString("historyNames"));
                    }
                    if (json.containsKey("socialStaffNum") && StringUtils.isNumeric(json.getString("socialStaffNum"))) {
                        tycInfo.setSocialStaffNum(Integer.parseInt(json.getString("socialStaffNum")));
                    }
                    tycInfo.setJsonData(json.toString());
                    tycInfo.setSyncTime(System.currentTimeMillis());
                    tycMapper.deleteByName(tycInfo);
                    tycMapper.insert(tycInfo);
                    return tycInfo;
                }

            } else {
                log.info("天眼查缓存命中，公司名称：{}，已缓存天数：{}天", traderName, dayCnt);
                return res;
            }
        }catch (Exception e) {
            return null;
        }


    }
}