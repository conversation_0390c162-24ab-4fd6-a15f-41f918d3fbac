package com.vedeng.erp.broadcast.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.broadcast.domain.dto.BroadCastDeptFormDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastDeptListDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastDeptQueryDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BroadcastDeptServiceImpl implements com.vedeng.erp.broadcast.service.BroadcastDeptService {


    @Autowired
    BroadcastDeptMapper broadcastDeptMapper;
    /**
     * 根据父ID获取的广播部门
     *
     * @param parentId
     * @return
     */
    @Override
    public List<BroadcastDeptEntity> getBroadcastDeptListByParentId(Integer parentId,String deptName) {
        return broadcastDeptMapper.listByParentId(parentId,deptName);
    }
    @Override
    public List<BroadcastDeptEntity> getBroadcastDeptListAll() {
        return broadcastDeptMapper.listByParentId(null,null);
    }

    /**
     * 分页查询播报部门列表
     * 
     * @param pageParam 分页查询参数，包含分页信息和查询条件
     * @return 分页播报部门列表，包含总数、页码等分页信息
     */
    @Override
    public PageInfo<BroadCastDeptListDto> getBroadcastDeptListPage(PageParam<BroadCastDeptQueryDto> pageParam) {
        log.info("分页查询播报部门列表，查询参数：{}", pageParam);
        
        // 获取查询条件
        BroadCastDeptQueryDto queryDto = pageParam.getParam();
        if (queryDto == null) {
            queryDto = new BroadCastDeptQueryDto();
        }
        
        // 设置分页参数
        Integer pageNum = pageParam.getPageNum() != null ? pageParam.getPageNum() : 1;
        Integer pageSize = pageParam.getPageSize() != null ? pageParam.getPageSize() : 20;
        PageHelper.startPage(pageNum, pageSize);
        
        // 构建查询参数
        String deptName = StringUtils.hasText(queryDto.getDeptName()) ? queryDto.getDeptName().trim() : null;
        String groupName = StringUtils.hasText(queryDto.getGroupName()) ? queryDto.getGroupName().trim() : null;
        Integer aedUserId = queryDto.getAedUserId();
        
        log.info("查询条件 - 一级部门名称: {}, 二级小组名称: {}, AED用户ID: {}", 
                deptName, groupName, aedUserId);
        
        // 调用Mapper查询
        List<BroadCastDeptListDto> result = broadcastDeptMapper.selectBroadcastDeptListPage(deptName, groupName, aedUserId);
        
        // 转换为PageInfo对象
        PageInfo<BroadCastDeptListDto> pageInfo = new PageInfo<>(result);
        
        log.info("查询到播报部门列表数量：{}，总记录数：{}", result != null ? result.size() : 0, pageInfo.getTotal());
        return pageInfo;
    }
    
    /**
     * 保存播报部门
     * 新增播报部门或小组
     * 
     * @param formDto 播报部门表单数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBroadcastDept(BroadCastDeptFormDto formDto) {
        log.info("新增播报部门，参数：{}", formDto);
        
        if (formDto == null) {
            throw new ServiceException("表单数据不能为空");
        }
        
        if (!StringUtils.hasText(formDto.getGroupName())) {
            throw new ServiceException("小组名称不能为空");
        }
        
        // 构建实体对象
        BroadcastDeptEntity entity = new BroadcastDeptEntity();
        entity.setDeptName(formDto.getGroupName().trim());
        entity.setParentId(formDto.getDeptId()); // 父级部门ID
        entity.setAedUserId(formDto.getAedUserIds());
        entity.setIsDeleted(0);
        entity.setAddTime(new Date());
        entity.setModTime(new Date());
        
        // 设置创建人和更新人
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (currentUser != null) {
            entity.setCreator(currentUser.getId());
            entity.setUpdater(currentUser.getId());
        }
        
        // 调用Mapper插入
        int result = broadcastDeptMapper.insertSelective(entity);
        
        if (result <= 0) {
            throw new ServiceException("新增播报部门失败");
        }
        
        log.info("新增播报部门成功，生成ID：{}", entity.getId());
    }
    
    /**
     * 更新播报部门
     * 编辑已存在的播报部门或小组
     * 
     * @param formDto 播报部门表单数据，必须包含ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBroadcastDept(BroadCastDeptFormDto formDto) {
        log.info("更新播报部门，参数：{}", formDto);
        
        if (formDto == null) {
            throw new ServiceException("表单数据不能为空");
        }
        
        if (formDto.getId() == null) {
            throw new ServiceException("ID不能为空");
        }
        
        if (!StringUtils.hasText(formDto.getGroupName())) {
            throw new ServiceException("小组名称不能为空");
        }
        
        // 检查记录是否存在
        BroadcastDeptEntity existEntity = broadcastDeptMapper.selectByPrimaryKey(formDto.getId());
        if (existEntity == null || existEntity.getIsDeleted() == 1) {
            throw new ServiceException("播报部门不存在");
        }
        
        // 构建更新实体对象（只更新允许修改的字段）
        BroadcastDeptEntity entity = new BroadcastDeptEntity();
        entity.setId(formDto.getId());
        entity.setDeptName(formDto.getGroupName().trim()); // 小组名称可以修改
        entity.setAedUserId(formDto.getAedUserIds()); // AED用户ID列表可以修改
        entity.setModTime(new Date());
        // 注意：不更新parentId，保持部门归属关系不变
        
        // 设置更新人
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (currentUser != null) {
            entity.setUpdater(currentUser.getId());
        }
        
        // 调用Mapper更新
        int result = broadcastDeptMapper.updateByPrimaryKeySelective(entity);
        
        if (result <= 0) {
            throw new ServiceException("更新播报部门失败");
        }
        
        log.info("更新播报部门成功，ID：{}，小组名称：{}，AED用户：{}", formDto.getId(), formDto.getGroupName(), formDto.getAedUserIds());
    }
    
    /**
     * 删除播报部门
     * 逻辑删除播报部门或小组
     * 
     * @param id 播报部门ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBroadcastDept(Integer id) {
        log.info("删除播报部门，ID：{}", id);
        
        if (id == null) {
            throw new ServiceException("ID不能为空");
        }
        
        // 检查记录是否存在
        BroadcastDeptEntity existEntity = broadcastDeptMapper.selectByPrimaryKey(id);
        if (existEntity == null || existEntity.getIsDeleted() == 1) {
            throw new ServiceException("播报部门不存在");
        }
        
        // 检查是否为一级部门（没有上级部门的不允许删除）
        if (existEntity.getParentId() == null || existEntity.getParentId() == 0) {
            throw new ServiceException("一级部门不允许删除，只能删除二级小组");
        }
        
        // 调用Mapper进行逻辑删除
        int result = broadcastDeptMapper.deleteByPrimaryKey(id);
        
        if (result <= 0) {
            throw new ServiceException("删除播报部门失败");
        }
        
        log.info("删除播报部门成功，ID：{}", id);
    }
}
