.dlg-depart-wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}
.dlg-depart-wrap .vd-icon {
  line-height: 1;
}
.dlg-depart-wrap .dlg-depart-container {
  width: 720px;
  position: relative;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-title {
  border-radius: 5px 5px 0 0;
  background: #F5F7FA;
  padding: 0 20px;
  line-height: 44px;
  border-bottom: solid 1px #E1E5E8;
  font-size: 16px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-close {
  font-size: 24px;
  color: #ccc;
  cursor: pointer;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 0;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-close:hover {
  color: #666;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt {
  display: flex;
  background: #fff;
  border-radius: 0 0 5px 5px;
  font-size: 14px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block {
  flex: 1;
  border-right: solid 1px #E1E5E8;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block:last-child {
  border-right: 0;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-cnt-top {
  height: 54px;
  border-bottom: solid 1px #E1E5E8;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-cnt-top.top-l {
  padding: 0 20px;
  display: flex;
  align-items: center;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-cnt-top.top-r {
  padding: 0 20px;
  line-height: 54px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-wrap {
  position: relative;
  width: 100%;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-wrap .dlg-depart-search-input {
  width: 100%;
  border: 1px solid #BABFC2;
  border-radius: 3px;
  line-height: 33px;
  height: 33px;
  padding: 0 36px 0 10px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-wrap .dlg-depart-search-input:hover {
  border-color: #969B9E;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-wrap .dlg-depart-search-input:focus {
  border-color: #09f;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-wrap .dlg-depart-search-input::placeholder {
  color: #999;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-wrap .vd-icon {
  font-size: 16px;
  color: #666;
  width: 36px;
  height: 33px;
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-wrap .vd-icon.icon-error2 {
  display: none;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-wrap.can-clear .icon-search {
  display: none;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-wrap.can-clear .icon-error2 {
  display: flex;
  cursor: pointer;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-wrap.can-clear .icon-error2:hover {
  color: #f60;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree {
  margin: 5px 3px 5px 5px;
  height: 555px;
  overflow: auto;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item {
  display: flex;
  align-items: center;
  line-height: 33px;
  cursor: pointer;
  border-radius: 3px;
  margin-right: 2px;
  padding: 0 15px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item:hover {
  background: #efefef;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item.lv-8 {
  padding-left: 141px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item.lv-7 {
  padding-left: 123px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item.lv-6 {
  padding-left: 105px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item.lv-5 {
  padding-left: 87px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item.lv-4 {
  padding-left: 69px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item.lv-3 {
  padding-left: 51px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item.lv-2 {
  padding-left: 33px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-checkbox {
  margin-right: 10px;
  display: flex;
  align-items: center;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-checkbox .icon-checkbox1 {
  font-size: 16px;
  color: #969B9E;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-checkbox .icon-checkbox2,
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-checkbox .icon-deduct {
  display: none;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-checkbox.on-select .icon-deduct {
  width: 16px;
  height: 16px;
  background: #09f;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  border-radius: 3px;
  padding-left: 1px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-checkbox.on-select .icon-checkbox1,
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-checkbox.on-select .icon-checkbox2 {
  display: none;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-checkbox.all-select .icon-checkbox2 {
  display: block;
  color: #09f;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-checkbox.all-select .icon-checkbox1,
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-checkbox.all-select .icon-deduct {
  display: none;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .icon-right {
  font-size: 16px;
  margin-right: 2px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-icon-file {
  width: 20px;
  height: 20px;
  background-image: url(../../../img/file.svg);
  background-size: 100% 100%;
  margin-right: 2px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-tree .dlg-tree-node-item .tree-node-avatar {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  overflow: hidden;
  object-fit: cover;
  margin-right: 10px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list {
  margin: 5px 3px 5px 5px;
  height: 555px;
  overflow: auto;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-item {
  display: flex;
  align-items: center;
  border-radius: 3px;
  height: 33px;
  margin-right: 2px;
  padding-left: 15px;
  cursor: pointer;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-item:hover {
  background: #efefef;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-item .dlg-depart-search-checkbox {
  font-size: 16px;
  margin-right: 10px;
  line-height: 1;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-item .dlg-depart-search-checkbox .icon-checkbox1 {
  color: #969B9E;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-item .dlg-depart-search-checkbox .icon-checkbox2 {
  color: #09f;
  display: none;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-item .dlg-depart-search-avatar {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  overflow: hidden;
  object-fit: cover;
  margin-right: 10px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-item .dlg-depart-search-name .strong {
  color: #f60;
  font-weight: normal;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-item.checked .dlg-depart-search-checkbox .icon-checkbox1 {
  display: none;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-item.checked .dlg-depart-search-checkbox .icon-checkbox2 {
  display: block;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-empty {
  padding-top: 226px;
  text-align: center;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-empty .icon-info1 {
  color: #09f;
  margin-bottom: 10px;
  font-size: 32px;
  display: inline-block;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-search-list .dlg-depart-search-empty .dlg-depart-search-empty-txt {
  color: #999;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list {
  margin: 5px 3px 5px 10px;
  height: 500px;
  overflow: auto;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list .dlg-depart-selected-item {
  display: flex;
  align-items: center;
  height: 33px;
  padding-left: 10px;
  border-radius: 3px;
  margin-right: 7px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list .dlg-depart-selected-item:hover {
  background: #efefef;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list .dlg-depart-selected-item:hover .icon-delete {
  display: flex;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list .dlg-depart-selected-item .dlg-depart-selected-avatar {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  overflow: hidden;
  object-fit: cover;
  margin-right: 10px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list .dlg-depart-selected-item .dlg-depart-selected-name {
  flex: 1;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list .dlg-depart-selected-item .icon-delete {
  font-size: 16px;
  width: 33px;
  height: 33px;
  display: none;
  align-items: center;
  justify-content: center;
  color: #999;
  cursor: pointer;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-selected-list .dlg-depart-selected-item .icon-delete:hover {
  color: #f60;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-cnt-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 10px 20px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-cnt-footer .dlg-depart-btn {
  height: 35px;
  line-height: 33px;
  background: #F5F7FA;
  border: 1px solid #BABFC2;
  padding: 0 15px;
  margin-left: 10px;
  cursor: pointer;
  border-radius: 3px;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-cnt-footer .dlg-depart-btn:hover {
  background: #EBEFF2;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-cnt-footer .dlg-depart-btn.btn-primary {
  background: #09f;
  color: #fff;
  border-color: #09f;
}
.dlg-depart-wrap .dlg-depart-container .dlg-depart-cnt .dlg-depart-cnt-block .dlg-depart-cnt-footer .dlg-depart-btn.btn-primary:hover {
  background: #0087e0;
  border-color: #0087e0;
}
.dus-select-trigger-wrap {
  display: flex;
  align-items: center;
  height: 30px;
  border: 1px solid #BABFC2;
  border-radius: 3px;
  font-size: 12px;
  padding: 0 10px;
  width: 100%;
  text-align: left;
  cursor: pointer;
  max-width: 350px;
}
.dus-select-trigger-wrap .dus-select-trigger-label {
  flex: 1;
  display: none;
  flex-wrap: wrap;
  align-items: flex-start;
  height: 30px;
  overflow: hidden;
  padding-top: 4px;
  position: relative;
}
.dus-select-trigger-wrap .dus-select-trigger-label .dus-select-trigger-label-item {
  display: flex;
  align-items: center;
  border: solid 1px #E1E5E8;
  border-radius: 3px;
  background: #f5f7fa;
  height: 22px;
  margin-bottom: 5px;
  margin-right: 5px;
  padding: 0 6px;
}
.dus-select-trigger-wrap .dus-select-trigger-label .dus-select-trigger-label-item .icon-delete {
  font-size: 14px;
  color: #999;
  margin-left: 5px;
  line-height: 22px;
}
.dus-select-trigger-wrap .dus-select-trigger-label .dus-select-trigger-label-item .icon-delete:hover {
  color: #f60;
}
.dus-select-trigger-wrap .dus-select-trigger-placeholder {
  flex: 1;
  color: #999;
}
.dus-select-trigger-wrap .icon-down {
  font-size: 13px;
  margin-left: 5px;
}
