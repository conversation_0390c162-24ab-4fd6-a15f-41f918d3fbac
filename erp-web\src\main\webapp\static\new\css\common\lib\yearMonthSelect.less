.scrollbar() {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background: #D7DADE;
        width: 6px;
        height: 6px;
        border-radius: 3px;

        &:hover {
            background: #BABFC2;
        }

        &:active {
            background: #969B9E;
        }
    }
}

.ym-select-wrap {
    position: relative;

    .ym-select-trigger {
        border: 1px solid #BABFC2;
        height: 30px;
        border-radius: 3px;
        padding: 0 10px;
        display: flex;
        align-items: center;
        font-size: 12px;
        cursor: pointer;

        .icon-down {
            font-size: 13px;
            margin-left: 5px;
            transition: transform .22s ease;
        }

        .placeholder {
            color: #999;
        }

        &.open {
            border-color: #09f;

            .icon-down {
                transform: rotate(180deg);
            }
        }
    }

    .ym-select-drop-wrap {
        position: absolute;
        z-index: 11;
        font-size: 12px;
        border: solid 1px #BABFC2;
        background: #fff;
        width: 132px;
        left: 0;
        border-radius: 3px;
        display: none;

        .ym-select-drop-year {
            display: flex;
            align-items: center;
            border-bottom: solid 1px #E1E5E8;

            .icon-slide-up {
                width: 38px;
                height: 38px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                color: #666;
                
                &.left {
                    transform: rotate(-90deg);
                }
                
                &.right {
                    transform: rotate(90deg);
                }

                &:hover {
                    color: #09f;
                }

                &.hide {
                    opacity: 0;
                    pointer-events: none;
                    z-index: -1;
                }
            }

            .year-txt {
                flex: 1;
                text-align: center;
            }
        }
    }

    .ym-select-drop-month-wrap {
        padding: 5px 0;

        .ym-select-drop-month-list {
            max-height: 264px;
            overflow: auto;
            .scrollbar();
            overscroll-behavior: contain;

            .ym-month-item {
                line-height: 33px;
                padding: 0 10px;
                cursor: pointer;

                &:hover {
                    background: #f5f7fa;
                }

                &.disabled {
                    cursor: not-allowed;
                    color: #999;

                    &:hover {
                        background: #fff;
                    }
                }

                &.active {
                    color: #09f;
                }
            }
        }
    }
}