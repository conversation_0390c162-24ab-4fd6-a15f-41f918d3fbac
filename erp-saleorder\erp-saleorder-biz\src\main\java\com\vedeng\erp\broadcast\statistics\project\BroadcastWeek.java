package com.vedeng.erp.broadcast.statistics.project;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity;
import com.vedeng.erp.broadcast.domain.entity.BroadcastTargetEntity;
import com.vedeng.erp.broadcast.mapdao.BroadcastStatisticsMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastTargetMapper;
import com.vedeng.erp.common.broadcast.MessageSubjectEnum;
import com.vedeng.erp.common.broadcast.StatDateRangeEnum;
import com.vedeng.erp.common.broadcast.config.BroadcastDeptConfigStatistics;
import com.vedeng.erp.common.broadcast.config.BroadcastGlobalConfigStatistics;
import com.vedeng.erp.common.broadcast.config.GlobalConfig;
import com.vedeng.erp.common.broadcast.constants.BroadcastRedisKey;
import com.vedeng.erp.common.broadcast.constants.MessageTemplate;
import com.vedeng.erp.common.broadcast.param.QwMessageParam;
import com.vedeng.erp.common.broadcast.param.TargetOrgAndUser;
import com.vedeng.erp.common.broadcast.param.TimePeriod;
import com.vedeng.erp.common.broadcast.param.QwMessageParam.Article;
import com.vedeng.erp.common.broadcast.param.QwMessageParam.News;
import com.vedeng.erp.common.broadcast.statistics.StatisticsDto;

/**
 * 周到款播报项目
 * @ClassName:  BroadcastWeek   
 * @author: Neil.yang
 * @date:   2025年6月9日 下午3:55:06    
 * @Copyright:
 */
@Component
public class BroadcastWeek extends AbstractBroadcast {

	@Autowired
	private BroadcastStatisticsMapper broadcastStatisticsMapper;
	
	@Autowired
	private BroadcastTargetMapper broadcastTargetMapper;
	
	@Autowired
    private RedisUtils redisUtils;
	
	@Value("${redis_dbtype}")
	private String dbType;
	
	@Override
	public List<MessageSubjectEnum> getMessageSubjectList(GlobalConfig globalConfig) {
		List<MessageSubjectEnum> messageSubjectList = Arrays.asList(MessageSubjectEnum.SALES_SINGLE,MessageSubjectEnum.SALES_TEAM,MessageSubjectEnum.SALES_DEPT);
		return messageSubjectList;
	}

	@Override
	public int getLineNum() {
		return 6;
	}

	@Override
	public boolean isSaveDb() {
		return true;
	}

	@Override
	public List<BroadcastDeptConfigStatistics> getBroadcastDeptConfigByProject(
			List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList) {
		return broadcastDeptConfigStatisticsList.stream().filter(item -> item.getWeekFlag() == 1).collect(Collectors.toList());
	}
	
	/**
     * 获取统计时间周期
     * @return 统计时间周期
     */
    public TimePeriod getStatisticsTime() {
    	Calendar calendar = Calendar.getInstance();
    	//calendar.add(Calendar.YEAR, -2);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        //本周的第一天作为周开始时间
    	Date startDate = calendar.getTime();
    	
    	Calendar calendarMonth = Calendar.getInstance();
    	//calendarMonth.add(Calendar.YEAR, -2);
    	calendarMonth.set(Calendar.DAY_OF_MONTH, 1);
    	calendarMonth.set(Calendar.HOUR_OF_DAY, 0);
    	calendarMonth.set(Calendar.MINUTE, 0);
    	calendarMonth.set(Calendar.SECOND, 0);
    	calendarMonth.set(Calendar.MILLISECOND, 0);
    	//本月的第一天作为月开始时间
    	Date monthStartDate = calendarMonth.getTime();
    	
    	Calendar calendarNow = Calendar.getInstance();
    	//获取年份
        int year = calendarNow.get(Calendar.YEAR);
        //获取月份
        int month = calendarNow.get(Calendar.MONTH) + 1;
    	
    	return new TimePeriod(startDate,new Date(),monthStartDate,new Date(),year,month);
    }

	@Override
	public List<QwMessageParam> execute(GlobalConfig globalConfig,Map<MessageSubjectEnum, List<TargetOrgAndUser>> targetOrgAndUserMap,Integer deptId,Integer amountStep,Integer isUserDefine,TimePeriod timePeriod,StatDateRangeEnum statDateRange,boolean isSendQwMessageFlag) {
		//日到款执行时间参数
		if(Objects.isNull(timePeriod)) {
			timePeriod = getStatisticsTime();
		}
		//播报的消息列表
		List<QwMessageParam> qwMessageParamList = new ArrayList<>();
		BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics = globalConfig.getBroadcastGlobalConfigStatistics();
		//周到款-个人
		List<TargetOrgAndUser> targetOrgAndUserPerson = targetOrgAndUserMap.get(MessageSubjectEnum.SALES_SINGLE);
		if(Objects.nonNull(targetOrgAndUserPerson)) {
			List<StatisticsDto> statisticsDtoListAll = new ArrayList<>();
			for (TargetOrgAndUser targetOrgAndUser : targetOrgAndUserPerson) {
				if(deptId == 1) {
					List<StatisticsDto> statisticsDtoList = broadcastStatisticsMapper.selectStatisticsAmountByPersonParams(timePeriod.getStartTime().getTime(),timePeriod.getEndTime().getTime(),targetOrgAndUser.getOrgIdList(),broadcastGlobalConfigStatistics.getExcludeSaleIds(),broadcastGlobalConfigStatistics.getExcludeTraderIds(),targetOrgAndUser.getInUserIdList(),targetOrgAndUser.getOutUserIdList(),targetOrgAndUser.getInUserIdBelongOrgIdList());
					statisticsDtoListAll.addAll(statisticsDtoList);
				}else {
					List<StatisticsDto> statisticsDtoList = broadcastStatisticsMapper.selectStatisticsAmountByPersonParams(timePeriod.getStartTime().getTime(),timePeriod.getEndTime().getTime(),targetOrgAndUser.getOrgIdList(),broadcastGlobalConfigStatistics.getExcludeSaleIds(),broadcastGlobalConfigStatistics.getExcludeTraderIds(),null,null,null);
					statisticsDtoListAll.addAll(statisticsDtoList);
				}
			}
			if(showLog) {
				LOGGER.info("获取周到款播报个人信息排行信息：{}",JSON.toJSONString(statisticsDtoListAll));
			}
			if(!CollectionUtils.isEmpty(statisticsDtoListAll)) {
				//按照totalAmount降序排列
				statisticsDtoListAll = statisticsDtoListAll.stream().sorted(Comparator.comparing(StatisticsDto::getTotalAmount).reversed()).collect(Collectors.toList());
				if(isSendQwMessageFlag) {
					setTop(statisticsDtoListAll);
					//统计信息获取，组装企微信息
					List<QwMessageParam> qwMessageParams = combineQwMessage(statisticsDtoListAll,globalConfig,amountStep,deptId,MessageSubjectEnum.SALES_SINGLE);
					qwMessageParamList.addAll(qwMessageParams);
				}
			}
		}
		//周到款-小组
		List<TargetOrgAndUser> targetOrgAndUserTeam = targetOrgAndUserMap.get(MessageSubjectEnum.SALES_TEAM);
		if(Objects.nonNull(targetOrgAndUserTeam)) {
			List<StatisticsDto> statisticsDtoListWeekAll = new ArrayList<>();
			//处理周收款
			for (TargetOrgAndUser targetOrgAndUser : targetOrgAndUserTeam) {
				StatisticsDto statisticsDto = broadcastStatisticsMapper.selectStatisticsAmountByTeamParams(timePeriod.getStartTime().getTime(),timePeriod.getEndTime().getTime(),targetOrgAndUser.getOrgIdList(),broadcastGlobalConfigStatistics.getExcludeSaleIds(),broadcastGlobalConfigStatistics.getExcludeTraderIds(),targetOrgAndUser.getInUserIdList(),targetOrgAndUser.getOutUserIdList(),targetOrgAndUser.getInUserIdBelongOrgIdList());
				if(showLog) {
					LOGGER.info("获取周到款播报小组:{}信息排行信息：{}",targetOrgAndUser.getTeamName(),JSON.toJSONString(statisticsDto));
				}
				if(Objects.nonNull(statisticsDto)) {
					statisticsDto.setTeamId(targetOrgAndUser.getTeamId());
					statisticsDto.setTeamName(targetOrgAndUser.getTeamName());
					statisticsDtoListWeekAll.add(statisticsDto);
				}
			}
			//处理月收款，用于计算达成率
			List<StatisticsDto> statisticsDtoListMonthAll = new ArrayList<>();
			//处理月收款
			for (TargetOrgAndUser targetOrgAndUser : targetOrgAndUserTeam) {
				StatisticsDto statisticsDto = broadcastStatisticsMapper.selectStatisticsAmountByTeamParams(timePeriod.getMonthStartTime().getTime(),timePeriod.getMonthEndTime().getTime(),targetOrgAndUser.getOrgIdList(),broadcastGlobalConfigStatistics.getExcludeSaleIds(),broadcastGlobalConfigStatistics.getExcludeTraderIds(),targetOrgAndUser.getInUserIdList(),targetOrgAndUser.getOutUserIdList(),targetOrgAndUser.getInUserIdBelongOrgIdList());
				if(showLog) {
					LOGGER.info("获取小组月到款情况,播报小组:{}信息排行信息：{}",targetOrgAndUser.getTeamName(),JSON.toJSONString(statisticsDto));
				}
				if(Objects.nonNull(statisticsDto)) {
					statisticsDto.setTeamId(targetOrgAndUser.getTeamId());
					statisticsDto.setTeamName(targetOrgAndUser.getTeamName());
					statisticsDtoListMonthAll.add(statisticsDto);
				}
			}
			//按照小组进行分类，获取月到款
			Map<Integer,StatisticsDto> statisticsDtoListMonthMap = statisticsDtoListMonthAll.stream().collect(Collectors.toMap(StatisticsDto::getTeamId, Function.identity(),(existing, replacement) -> existing));;
			
			//获取达成目标列表
			List<Integer> teamIdList = targetOrgAndUserTeam.stream().map(temp->temp.getTeamId()).collect(Collectors.toList());
			List<BroadcastTargetEntity> broadcastTargetEntityList = broadcastTargetMapper.selectBroadcastTargeByParams(timePeriod.getYear(),timePeriod.getMonth(),MessageSubjectEnum.SALES_TEAM.getSubject(),teamIdList);
			//按照小组进行分组，获取小组达成率
			Map<Integer,BroadcastTargetEntity> broadcastTargetMap = broadcastTargetEntityList.stream().collect(Collectors.toMap(BroadcastTargetEntity::getTargetBuzId, Function.identity(),(existing, replacement) -> existing));;
			
			//对statisticsDtoListWeekAll 进行计算达成率
			for (StatisticsDto statisticsDto : statisticsDtoListWeekAll) {
			    Integer teamId = statisticsDto.getTeamId();
				BigDecimal monthTotalAmount = Objects.nonNull(statisticsDtoListMonthMap.get(teamId))?statisticsDtoListMonthMap.get(teamId).getTotalAmount():null;
				BigDecimal achieved = Objects.nonNull(broadcastTargetMap.get(teamId))?broadcastTargetMap.get(teamId).getTargetAmount():null;
				//计算达成率
				if(achieved!=null && monthTotalAmount!=null) {
					Integer achievedNum = monthTotalAmount.multiply(new BigDecimal(100)).divide(achieved, 0, RoundingMode.HALF_UP).intValue();
					statisticsDto.setAchievedNum(achievedNum);
				}
			}
			
			//周播小组按照完成率排序
			if(!CollectionUtils.isEmpty(statisticsDtoListWeekAll)) {
				//按照totalAmount降序排列
				statisticsDtoListWeekAll = statisticsDtoListWeekAll.stream()
					    .sorted(Comparator.comparing(
					        StatisticsDto::getAchievedNum, 
					        Comparator.nullsLast(Comparator.reverseOrder())
					    	).thenComparing(
					            StatisticsDto::getTotalAmount,
					            Comparator.nullsLast(Comparator.reverseOrder())
					        ))
					    .collect(Collectors.toList());
				if(isSendQwMessageFlag) {
					setTop(statisticsDtoListWeekAll);
					//统计信息获取，组装企微信息，记录需要按挡位播报
					List<QwMessageParam> qwMessageParams = combineQwMessage(statisticsDtoListWeekAll,globalConfig,amountStep,deptId,MessageSubjectEnum.SALES_TEAM);
					qwMessageParamList.addAll(qwMessageParams);
				}
			}
		}
		//周到款-部门
		List<TargetOrgAndUser> targetOrgAndUserDept = targetOrgAndUserMap.get(MessageSubjectEnum.SALES_DEPT);
		if(Objects.nonNull(targetOrgAndUserDept)) {
			List<StatisticsDto> statisticsDtoListWeekAll = new ArrayList<>();
			//处理周到款
			for (TargetOrgAndUser targetOrgAndUser : targetOrgAndUserDept) {
				StatisticsDto statisticsDto = broadcastStatisticsMapper.selectStatisticsAmountByTeamParams(timePeriod.getStartTime().getTime(),timePeriod.getEndTime().getTime(),targetOrgAndUser.getOrgIdList(),broadcastGlobalConfigStatistics.getExcludeSaleIds(),broadcastGlobalConfigStatistics.getExcludeTraderIds(),targetOrgAndUser.getInUserIdList(),targetOrgAndUser.getOutUserIdList(),targetOrgAndUser.getInUserIdBelongOrgIdList());
				if(showLog) {
					LOGGER.info("获取月到款播报小组:{}信息排行信息：{}",targetOrgAndUser.getTeamName(),JSON.toJSONString(statisticsDto));
				}
				if(Objects.nonNull(statisticsDto)) {
					statisticsDto.setDeptId(targetOrgAndUser.getDeptId());
					statisticsDto.setDeptName(targetOrgAndUser.getDeptName());
					statisticsDtoListWeekAll.add(statisticsDto);
				}
				
			}
			
			//处理月收款，用于计算达成率
			List<StatisticsDto> statisticsDtoListMonthAll = new ArrayList<>();
			//处理周收款
			for (TargetOrgAndUser targetOrgAndUser : targetOrgAndUserDept) {
				StatisticsDto statisticsDto = broadcastStatisticsMapper.selectStatisticsAmountByTeamParams(timePeriod.getMonthStartTime().getTime(),timePeriod.getMonthEndTime().getTime(),targetOrgAndUser.getOrgIdList(),broadcastGlobalConfigStatistics.getExcludeSaleIds(),broadcastGlobalConfigStatistics.getExcludeTraderIds(),targetOrgAndUser.getInUserIdList(),targetOrgAndUser.getOutUserIdList(),targetOrgAndUser.getInUserIdBelongOrgIdList());
				if(showLog) {
					LOGGER.info("获取小组月到款情况,播报小组:{}信息排行信息：{}",targetOrgAndUser.getTeamName(),JSON.toJSONString(statisticsDto));
				}
				if(Objects.nonNull(statisticsDto)) {
					statisticsDto.setDeptId(targetOrgAndUser.getDeptId());
					statisticsDto.setDeptName(targetOrgAndUser.getDeptName());
					statisticsDtoListMonthAll.add(statisticsDto);
				}
			}
			
			//按照小组进行分类，获取月到款
			Map<Integer,StatisticsDto> statisticsDtoListMonthMap = statisticsDtoListMonthAll.stream().collect(Collectors.toMap(StatisticsDto::getDeptId, Function.identity(),(existing, replacement) -> existing));;
			
			//获取达成目标列表
			List<Integer> deptIdList = targetOrgAndUserDept.stream().map(temp->temp.getDeptId()).collect(Collectors.toList());
			List<BroadcastTargetEntity> broadcastTargetEntityList = broadcastTargetMapper.selectBroadcastTargeByParams(timePeriod.getYear(),timePeriod.getMonth(),MessageSubjectEnum.SALES_DEPT.getSubject(),deptIdList);
			//按照小组进行分组，获取小组达成率
			Map<Integer,BroadcastTargetEntity> broadcastTargetMap = broadcastTargetEntityList.stream().collect(Collectors.toMap(BroadcastTargetEntity::getTargetBuzId, Function.identity(),(existing, replacement) -> existing));;
			
			//对statisticsDtoListWeekAll 进行计算达成率
			for (StatisticsDto statisticsDto : statisticsDtoListWeekAll) {
			    Integer deptIdSingle = statisticsDto.getDeptId();
				BigDecimal monthTotalAmount = Objects.nonNull(statisticsDtoListMonthMap.get(deptIdSingle))?statisticsDtoListMonthMap.get(deptIdSingle).getTotalAmount():null;
				BigDecimal achieved = Objects.nonNull(broadcastTargetMap.get(deptIdSingle))?broadcastTargetMap.get(deptIdSingle).getTargetAmount():null;
				//计算达成率
				if(achieved!=null && monthTotalAmount!=null) {
					Integer achievedNum = monthTotalAmount.multiply(new BigDecimal(100)).divide(achieved, 0, RoundingMode.HALF_UP).intValue();
					statisticsDto.setAchievedNum(achievedNum);
				}
			}
			
			//周播报部门按照完成率排序
			if(!CollectionUtils.isEmpty(statisticsDtoListWeekAll)) {
				//按照totalAmount降序排列
				statisticsDtoListWeekAll = statisticsDtoListWeekAll.stream(
						).sorted(Comparator.comparing(
							StatisticsDto::getAchievedNum,
							Comparator.nullsFirst(Comparator.reverseOrder()))
						.thenComparing(
				            StatisticsDto::getTotalAmount,
				            Comparator.nullsLast(Comparator.reverseOrder())
				        ))
						.collect(Collectors.toList());;
				if(isSendQwMessageFlag) {
					//设置top
					setTop(statisticsDtoListWeekAll);
					//统计信息获取，组装企微信息，记录需要按挡位播报
					List<QwMessageParam> qwMessageParams = combineQwMessage(statisticsDtoListWeekAll,globalConfig,amountStep,deptId,MessageSubjectEnum.SALES_DEPT);
					qwMessageParamList.addAll(qwMessageParams);
				}
			}
		}
		return qwMessageParamList;
		
	}

	private void setTop(List<StatisticsDto> statisticsDtoListAll) {
		AtomicInteger rank = new AtomicInteger(1);
		for (int i = 0; i < statisticsDtoListAll.size(); i++) {
		    // 处理第一个元素或与前一个元素不同时递增排名
		    if (i == 0 || 
		    		statisticsDtoListAll.get(i).getTotalAmount().compareTo(statisticsDtoListAll.get(i-1).getTotalAmount()) != 0) {
		        rank.set(i + 1);
		    }
		    statisticsDtoListAll.get(i).setTopNum(rank.get());
		}
	}
	

	/**
	 * 组装企微消息参数
	 * @param statisticsDtoList 需要发送的数据
	 * @param globalConfig  全局配置
	 * @param amountStep  日到款梯度
	 * @param deptId  大群或者部门ID
	 * @param messageSubjectEnum 消息主体的枚举
	 * @return
	 */
	private List<QwMessageParam> combineQwMessage(List<StatisticsDto> statisticsDtoList,GlobalConfig globalConfig, Integer amountStep, Integer deptId, MessageSubjectEnum messageSubjectEnum) {
		List<QwMessageParam> qwMessageParamList = new ArrayList<>();
		BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics = globalConfig.getBroadcastGlobalConfigStatistics();
		//组装要发送的企微参数
		List<String> descriptionList = getDescription(statisticsDtoList,globalConfig,amountStep,deptId,messageSubjectEnum);
		for (String description : descriptionList) {
			QwMessageParam qwMessageParam = new QwMessageParam();
			qwMessageParam.setMsgtype("news");
			News news = new News();
			List<Article> articles = new ArrayList<>();
			Article article = new Article();
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
				article.setTitle(broadcastGlobalConfigStatistics.getBroadcastTitleWeek()+"（个人）");
			}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM)) {
				article.setTitle(broadcastGlobalConfigStatistics.getBroadcastTitleWeek()+"（小组）");
			}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
				article.setTitle(broadcastGlobalConfigStatistics.getBroadcastTitleWeek()+"（部门）");
			}
			// 如果最后有多余的 \n\n，去除它
		    if (description.length() > 2 && description.substring(description.length() - 2).equals("\n\n")) {
		    	description = description.subSequence(0,description.length() - 2).toString();
		    }
			article.setDescription(description);
			//跳转链接，本期固定
			article.setUrl(qwUrl);
			//获取图片逻辑（排序第一的个人或者团队）
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
				article.setPicurl(getPicUrl(statisticsDtoList.get(0).getUserId(),globalConfig,MessageSubjectEnum.SALES_SINGLE));
			}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM)) {
				article.setPicurl(getPicUrl(statisticsDtoList.get(0).getTeamId(),globalConfig,MessageSubjectEnum.SALES_TEAM));
			}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
				article.setPicurl(getPicUrl(statisticsDtoList.get(0).getDeptId(),globalConfig,MessageSubjectEnum.SALES_DEPT));
			}
			articles.add(article);
			news.setArticles(articles);
			qwMessageParam.setNews(news);
			qwMessageParamList.add(qwMessageParam);
		}
		return qwMessageParamList;
	}
	
	
	/**
	 * 拼接企微消息
	 * @param statisticsDtoList
	 * @param amountStep 
	 * @param deptId 
	 * @param messageSubjectEnum 
	 * @return
	 */
	private List<String> getDescription(List<StatisticsDto> statisticsDtoList,GlobalConfig globalConfig, Integer amountStep, Integer deptId, MessageSubjectEnum messageSubjectEnum) {
		List<String> descriptionList = new ArrayList<>();
		//先获取获取topN
		BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics = globalConfig.getBroadcastGlobalConfigStatistics();
		Integer topnUser = broadcastGlobalConfigStatistics.getTopnUser();
		Integer topnDept = broadcastGlobalConfigStatistics.getTopnDept();
		Integer totalCount = statisticsDtoList.size();
		if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE) && totalCount>topnUser) {
			statisticsDtoList = statisticsDtoList.subList(0, topnUser);
		}
		if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM) || messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
			if(totalCount>topnDept) {
				statisticsDtoList = statisticsDtoList.subList(0, topnDept);
			}
		}
		
		//由于企微限制，每次不能超过512个字符，此处需要判断
		List<String> messageInfoList = new ArrayList<>();
		for (StatisticsDto statisticsDto : statisticsDtoList) {
			messageInfoList.add(processBatchMessage(Arrays.asList(statisticsDto),messageSubjectEnum));
		}
	    Integer	batchSize = getLineNum();
	    while (batchSize > 0) {
	    	descriptionList.clear();
            boolean validBatch = true;

            for (int i = 0; i < messageInfoList.size(); i += batchSize) {
                List<String> batch = messageInfoList.subList(i, Math.min(i + batchSize, messageInfoList.size()));
                String combined = String.join("", batch);
                
                // 检查字节长度（UTF-8 编码）
                byte[] bytes = combined.getBytes(StandardCharsets.UTF_8);
                if (bytes.length >= 512) {
                    validBatch = false;
                    break;
                }
                descriptionList.add(combined);
            }

            if (validBatch) {
                return descriptionList; // 找到合适的批次大小
            }
            batchSize--; // 减小批次大小重试
        }
		return descriptionList;
	}

	private String processBatchMessage(List<StatisticsDto> statisticsDtoList, MessageSubjectEnum messageSubjectEnum) {
		StringBuilder stb = new StringBuilder();
		for (StatisticsDto statisticsDto : statisticsDtoList) {
			//处理个人
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
				String userName = "未知用户";
				String teamName = statisticsDto.getTeamName();
				if(StringUtils.isNotEmpty(statisticsDto.getUserName())) {
					userName = statisticsDto.getUserName().split("\\.")[0];
				}
				String message = MessageFormat.format(MessageTemplate.WEEK_PERSON_MESSAGE,statisticsDto.getTopNum(),teamName,userName,statisticsDto.getTotalAmount().toString());
				stb.append(message);
				stb.append("\n\n");
			}
			//处理小组
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM)) {
				String message = MessageFormat.format(MessageTemplate.WEEK_TEAM_MESSAGE,statisticsDto.getTopNum(),statisticsDto.getTeamName(),statisticsDto.getTotalAmount().toString());
				stb.append(message);
				stb.append("\n\n");
			}
			//处理部门
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
				String message = MessageFormat.format(MessageTemplate.WEEK_DEPT_MESSAGE,statisticsDto.getTopNum(),statisticsDto.getDeptName(),statisticsDto.getTotalAmount().toString());
				stb.append(message);
				stb.append("\n\n");
			}
		}
		return stb.toString();
	}
	


	/**
	 * 获取展示的图片
	 * 1. 如排序第一的个人或团队，有专属图片，则随机选择专属图片（播报管理 - 专属目标），每张图片每日仅使用一次，如当日全部使用，则重置使用次数
	 * 2. 否则随机获取非专属目标的图片，每张图片每日仅使用一次，如当日全部使用，则重置使用次数
	 * @param userId
	 * @param globalConfig
	 * @param messageSubjectEnum 
	 * @return
	 */
	private String getPicUrl(Integer targetId, GlobalConfig globalConfig, MessageSubjectEnum messageSubjectEnum) {
		String picUrl = "http://#";
		Integer picId = null;
		List<BroadcastContentConfigEntity> broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList();
		//为空，返回空字符串
		if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
			return picUrl;
		}
		boolean flag = false;
		//是否存在个人专属图片
		if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
			flag = broadcastContentConfigStatisticsList.stream().anyMatch(temp->(Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==1 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId))));
		}
		//是否存在团队专属图片
		else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM) || messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
			flag = broadcastContentConfigStatisticsList.stream().anyMatch(temp->(Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==2 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId))));
		}
		LOGGER.info("{},是否有专属图片：{}",messageSubjectEnum.getSubjectName(),flag);
		if(flag) {
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
				//筛选出个人专属图片列表
				broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream()
						.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==1 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId)))
						.collect(Collectors.toList());
			}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM) || messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
				//筛选出团队专属图片列表
				broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream()
						.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==2 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId)))
						.collect(Collectors.toList());
			}
			
			if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
				return picUrl;
			}
			//REDIS获取已使用的图片列表
			String picConfigRecordKey = "";
			if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
				picConfigRecordKey = MessageFormat.format(BroadcastRedisKey.PIC_CONFIG_RECORD,dbType,1,targetId);
			}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM) || messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
				picConfigRecordKey = MessageFormat.format(BroadcastRedisKey.PIC_CONFIG_RECORD,dbType,2,targetId);
			}
			List<Integer> picConfigRecordValue = redisUtils.lgetAllInt(picConfigRecordKey);
			LOGGER.info("专属图片ID使用情况:{}",JSON.toJSONString(picConfigRecordValue));
			//REDIS为空，说明当日首次使用
			int expireTime = Integer.parseInt(String.valueOf(DateUtil.getSecondsToNextDay()));
			if(CollectionUtils.isEmpty(picConfigRecordValue)) {
				Collections.shuffle(broadcastContentConfigStatisticsList);
				picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
				picId = broadcastContentConfigStatisticsList.get(0).getId();
				redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
			}else {
				//筛选出专属图片列表，并且在已使用记录中不存在的
				broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream().filter(temp-> !picConfigRecordValue.contains(temp.getId())).collect(Collectors.toList());
				//无筛选值，删除REDIS，随机取一个，重建REDIS
				if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
					//无筛选值，重新获取
					
					if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_SINGLE)) {
						broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList().stream()
								.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==1 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId)))
								.collect(Collectors.toList());;
					}else if(messageSubjectEnum.equals(MessageSubjectEnum.SALES_TEAM) || messageSubjectEnum.equals(MessageSubjectEnum.SALES_DEPT)) {
						broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList().stream()
								.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==2 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(targetId)))
								.collect(Collectors.toList());;
					}
					redisUtils.del(picConfigRecordKey);
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}else {
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}
			}
		}else {
			broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream().filter(temp-> temp.getExclusiveType()== 0).collect(Collectors.toList());
			if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
				return picUrl;
			}
			//REDIS获取已使用的图片列表
			String picConfigRecordKey = MessageFormat.format(BroadcastRedisKey.PIC_CONFIG_RECORD,dbType,0,0);
			List<Integer> picConfigRecordValue = redisUtils.lgetAllInt(picConfigRecordKey);
			LOGGER.info("非专属图片ID使用情况:{}",JSON.toJSONString(picConfigRecordValue));
			//REDIS为空，说明当日首次使用
			int expireTime = Integer.parseInt(String.valueOf(DateUtil.getSecondsToNextDay()));
			if(CollectionUtils.isEmpty(picConfigRecordValue)) {
				Collections.shuffle(broadcastContentConfigStatisticsList);
				picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
				picId = broadcastContentConfigStatisticsList.get(0).getId();
				redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
			}else {
				//筛选出图片列表,非专属，并且在已使用记录中不存在的
				broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream().filter(temp->!picConfigRecordValue.contains(temp.getId()) ).collect(Collectors.toList());
				//无筛选值，删除REDIS，随机取一个，重建REDIS
				if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
					broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList().stream().filter(temp-> temp.getExclusiveType()== 0).collect(Collectors.toList());
					redisUtils.del(picConfigRecordKey);
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}else {
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}
			}
		}
		return picUrl;
	}
	

}
