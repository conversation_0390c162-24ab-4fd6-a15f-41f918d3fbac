package com.vedeng.erp.broadcast.mapdao;

import com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 播报内容配置Mapper
 */
public interface BroadcastContentConfigMapper {

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 删除记录数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insert(BroadcastContentConfigEntity record);

    /**
     * 选择性插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insertSelective(BroadcastContentConfigEntity record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    BroadcastContentConfigEntity selectByPrimaryKey(Integer id);

    /**
     * 选择性更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKeySelective(BroadcastContentConfigEntity record);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKey(BroadcastContentConfigEntity record);

    /**
     * 分页查询播报内容配置
     *
     * @param picName 图片名称
     * @param exclusiveType 专属类型
     * @param exclusiveTargetValues 专属目标值
     * @param exclusiveTargetLabels 专属目标标签
     * @param creatorList 创建人ID列表
     * @param startAddTime 创建时间起
     * @param endAddTime 创建时间止
     * @return 播报内容配置列表
     */
    List<BroadcastContentConfigEntity> selectByCondition(
            @Param("picName") String picName,
            @Param("exclusiveType") Integer exclusiveType,
            @Param("exclusiveTargetValues") String exclusiveTargetValues,
            @Param("exclusiveTargetLabels") String exclusiveTargetLabels,
            @Param("creatorList") List<String> creatorList,
            @Param("startAddTime") String startAddTime,
            @Param("endAddTime") String endAddTime
    );

    /**
     * 批量删除（逻辑删除）
     *
     * @param ids ID列表
     * @param updater 更新人
     * @return 更新记录数
     */
    int batchDelete(@Param("ids") List<Integer> ids, @Param("updater") Integer updater);
}
