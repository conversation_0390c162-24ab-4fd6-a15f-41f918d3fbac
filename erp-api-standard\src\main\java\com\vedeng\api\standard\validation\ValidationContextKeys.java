package com.vedeng.api.standard.validation;

/**
 * 验证上下文键名常量类
 * 定义标准化的context key命名规范，确保数据传递的一致性和可维护性
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
public final class ValidationContextKeys {
    
    /**
     * 私有构造函数，防止实例化
     */
    private ValidationContextKeys() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
    
    // ==================== 销售单相关 ====================
    
    /**
     * 销售单信息
     * 类型：SaleOrderInfo
     * 用途：验证销售单存在性时查询到的销售单详细信息
     */
    public static final String SALE_ORDER_INFO = "saleOrderInfo";
    
    /**
     * 销售单状态
     * 类型：String
     * 用途：销售单的当前状态（如：草稿、待审核、已审核等）
     */
    public static final String SALE_ORDER_STATUS = "saleOrderStatus";
    
    /**
     * 交付时间
     * 类型：Date 或 Long
     * 用途：记录订单的预计或实际交付时间
     */
    public static final String DELIVERY_TIMES = "deliveryTimes";
    
    // ==================== 采购单相关 ====================
    
    /**
     * 采购单信息
     * 类型：BuyOrderInfo
     * 用途：验证采购单存在性时查询到的采购单详细信息
     */
    public static final String BUY_ORDER_INFO = "buyOrderInfo";
    
    /**
     * 采购单状态
     * 类型：String
     * 用途：采购单的当前状态
     */
    public static final String BUY_ORDER_STATUS = "buyOrderStatus";
    
    /**
     * 采购单视图对象
     * 类型：BuyorderVo
     * 用途：存储采购单的视图对象信息
     */
    public static final String BUYORDER_VO = "buyorderVo";
    
    // ==================== 客户相关 ====================
    
    /**
     * 客户信息
     * 类型：CustomerInfo
     * 用途：验证客户存在性时查询到的客户详细信息
     */
    public static final String CUSTOMER_INFO = "customerInfo";
    
    /**
     * 客户状态
     * 类型：String
     * 用途：客户的当前状态（如：正常、冻结、黑名单等）
     */
    public static final String CUSTOMER_STATUS = "customerStatus";
    
    // ==================== 供应商相关 ====================
    
    /**
     * 供应商信息
     * 类型：SupplierInfo
     * 用途：验证供应商存在性时查询到的供应商详细信息
     */
    public static final String SUPPLIER_INFO = "supplierInfo";
    
    /**
     * 供应商状态
     * 类型：String
     * 用途：供应商的当前状态
     */
    public static final String SUPPLIER_STATUS = "supplierStatus";
    
    // ==================== 商品相关 ====================
    
    /**
     * 商品信息
     * 类型：GoodsInfo
     * 用途：验证商品存在性时查询到的商品详细信息
     */
    public static final String GOODS_INFO = "goodsInfo";
    
    /**
     * 商品库存信息
     * 类型：StockInfo
     * 用途：验证商品库存时查询到的库存信息
     */
    public static final String STOCK_INFO = "stockInfo";
    
    // ==================== 用户权限相关 ====================
    
    /**
     * 当前用户信息
     * 类型：User
     * 用途：验证用户权限时使用的用户信息
     */
    public static final String CURRENT_USER = "currentUser";
    
    /**
     * 用户权限列表
     * 类型：List<Permission>
     * 用途：验证用户权限时查询到的权限列表
     */
    public static final String USER_PERMISSIONS = "userPermissions";
    
    /**
     * 操作类型
     * 类型：String
     * 用途：当前执行的操作类型（如：CREATE、UPDATE、DELETE等）
     */
    public static final String OPERATION_TYPE = "operationType";
    
    // ==================== 业务流程相关 ====================
    
    /**
     * 工作流实例ID
     * 类型：String
     * 用途：Temporal工作流实例的唯一标识
     */
    public static final String WORKFLOW_INSTANCE_ID = "workflowInstanceId";
    
    /**
     * 业务流程状态
     * 类型：String
     * 用途：当前业务流程的执行状态
     */
    public static final String BUSINESS_PROCESS_STATUS = "businessProcessStatus";
    
    // ==================== 公司相关 ======================================
    
    /**
     * 公司信息
     * 类型：CompanyInfo
     * 用途：验证公司存在性时查询到的公司详细信息
     */
    public static final String COMPANY_INFO = "companyInfo";
    
    /**
     * 公司执行顺序
     * 类型：List<String>
     * 用途：多公司业务流程的执行顺序
     */
    public static final String COMPANY_SEQUENCE = "companySequence";
    
    // ==================== 发票相关 ====================
    
    /**
     * 发票信息
     * 类型：String
     * 用途：发票基本信息
     */
    public static final String INVOICE = "invoice";
    
    /**
     * 发票保存类型
     * 类型：String
     * 用途：指定发票的保存类型
     */
    public static final String SAVE_INVOICE_TYPE = "saveInvoiceType";
    
    /**
     * 相关ID数组
     * 类型：String
     * 用途：与发票相关的ID列表
     */
    public static final String RELATED_ID_ARR = "relatedIdArr";
    
    /**
     * 商品详情ID数组
     * 类型：String
     * 用途：发票中商品详情的ID列表
     */
    public static final String DETAIL_GOODS_ID_ARR = "detailGoodsIdArr";

    /**
     * 用途：直发同行单信息
     */
    public static final String PURCHASE_DELIVERY_DIRECT_BATCH_INFO_VO = "purchaseDeliveryDirectBatchInfoVo";

    /**
     * 发票号码数组
     * 类型：String
     * 用途：发票号码列表
     */
    public static final String INVOICE_NUM_ARR = "invoiceNumArr";
    
    /**
     * 发票价格数组
     * 类型：String
     * 用途：发票价格列表
     */
    public static final String INVOICE_PRICE_ARR = "invoicePriceArr";
    
    /**
     * 商品类型数组
     * 类型：String
     * 用途：发票中商品的类型列表
     */
    public static final String GOODS_TYPE_ARR = "goodsTypeArr";
    
    /**
     * 发票总金额数组
     * 类型：String
     * 用途：发票总金额列表
     */
    public static final String INVOICE_TOTAL_AMOUNT_ARR = "invoiceTotalAmountArr";
    
    /**
     * 订单商品ID、数量、金额、SKU信息
     * 类型：String
     * 用途：存储格式为"buyOrderGoodsId|数量|金额|SKU_buyOrderGoodsId|数量|金额|SKU2"的订单商品信息
     */
    public static final String ID_NUM_PRICE = "id_num_price";
    
    /**
     * 订单商品发货数量信息
     * 类型：String
     * 用途：存储格式为"buyOrderGoodsId|发货数量|已发货数量|合计发货数量_buyOrderGoodsId|发货数量|已发货数量|合计发货数量"的订单发货信息
     */
    public static final String ID_SEND_N_SENDED_N_SUM_N = "id_sendN_sendedN_sumN";
    
    // ==================== 快递相关 ====================
    
    /**
     * 快递信息
     * 类型：ExpressInfo
     * 用途：存储快递相关的信息
     */
    public static final String EXPRESS = "express";
    
    /**
     * 金额
     * 类型：String
     * 用途：存储金额信息
     */
    public static final String AMOUNT = "amount";
    public static final String EXPRESS_IDS = "expressIds";
    public static final String BEFORE_PARAMS = "beforeParams";
    
    
    
    // ==================== 辅助方法 ====================
    
    /**
     * 获取所有定义的键名
     * 用于调试和文档生成
     * 
     * @return 所有键名的数组
     */
    public static String[] getAllKeys() {
        return new String[] {
            SALE_ORDER_INFO, SALE_ORDER_STATUS, DELIVERY_TIMES,
            BUY_ORDER_INFO, BUY_ORDER_STATUS, BUYORDER_VO,
            CUSTOMER_INFO, CUSTOMER_STATUS,
            SUPPLIER_INFO, SUPPLIER_STATUS,
            GOODS_INFO, STOCK_INFO,
            CURRENT_USER, USER_PERMISSIONS, OPERATION_TYPE,
            WORKFLOW_INSTANCE_ID, BUSINESS_PROCESS_STATUS,
            COMPANY_INFO, COMPANY_SEQUENCE,
            INVOICE, SAVE_INVOICE_TYPE, RELATED_ID_ARR, 
            DETAIL_GOODS_ID_ARR, INVOICE_NUM_ARR, INVOICE_PRICE_ARR,
            GOODS_TYPE_ARR, INVOICE_TOTAL_AMOUNT_ARR, ID_NUM_PRICE, ID_SEND_N_SENDED_N_SUM_N, EXPRESS, AMOUNT,PURCHASE_DELIVERY_DIRECT_BATCH_INFO_VO
        };
    }
    
    /**
     * 检查键名是否为预定义的标准键名
     * 
     * @param key 要检查的键名
     * @return 如果是预定义的标准键名返回true，否则返回false
     */
    public static boolean isStandardKey(String key) {
        if (key == null) {
            return false;
        }
        
        for (String standardKey : getAllKeys()) {
            if (standardKey.equals(key)) {
                return true;
            }
        }
        return false;
    }
}
