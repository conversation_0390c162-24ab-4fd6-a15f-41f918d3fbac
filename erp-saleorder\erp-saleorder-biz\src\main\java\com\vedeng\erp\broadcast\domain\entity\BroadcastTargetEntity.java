package com.vedeng.erp.broadcast.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 业绩目标表
 */
@Getter
@Setter
public class BroadcastTargetEntity extends BaseEntity {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 目标类型：1个人，2小组，3部门
     */
    private Integer targetType;

    /**
     * 目标业务ID：1=用户ID，2=二级部门ID，3=部门ID
     */
    private Integer targetBuzId;

    /**
     * 目标金额
     */
    private BigDecimal targetAmount;

    /**
     * 目标月份（1-12）
     */
    private Integer targetMonth;

    /**
     * 目标年份
     */
    private Integer targetYear;

    /**
     * 是否已删除（0=否，1=是）
     */
    private Integer isDeleted;

    /**
     * 目标对象名称（查询时使用，不存储到数据库）
     */
    private String targetName;
}
