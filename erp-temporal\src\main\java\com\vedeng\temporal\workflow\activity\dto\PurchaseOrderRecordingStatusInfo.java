package com.vedeng.temporal.workflow.activity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PurchaseOrderRecordingStatusInfo {

    private boolean completed;
    
    private List<String> invoiceNoList;

    public static PurchaseOrderRecordingStatusInfo notCompleted() {
        PurchaseOrderRecordingStatusInfo info = new PurchaseOrderRecordingStatusInfo();
        info.setCompleted(false);
        return info;
    }
}
