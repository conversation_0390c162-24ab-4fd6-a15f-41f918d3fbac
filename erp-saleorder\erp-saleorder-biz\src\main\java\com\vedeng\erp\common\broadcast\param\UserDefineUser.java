package com.vedeng.erp.common.broadcast.param;

import java.util.List;

import lombok.Data;

/**
 * 自定义用户信息
 * @ClassName:  UserDefineUser   
 * @author: <PERSON>.yang
 * @date:   2025年6月6日 下午5:22:43    
 * @Copyright:
 */
@Data
public class UserDefineUser {
	
	/**用户信息*/
	private Integer userId;
	
	/**现部门名称*/
	private String deptNameNow;
	
	/**现部门ID*/
	private Integer deptIdNow;
	
	/**现小组名称*/
	private String teamNameNow;
	
	/**现小组名称*/
	private Integer teamIdNow;
	
	/**原部门名称*/
	private List<String> deptNameOldList;
	
	/**原部门ID*/
	private List<Integer> deptIdOldList;
	
	/**原小组名称*/
	private List<String> teamNameOldList;
	
	/**原小组ID*/
	private List<Integer> teamIdOldList;

}
