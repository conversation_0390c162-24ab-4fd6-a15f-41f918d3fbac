; void function () {
    var defaults = {
        el: '',
        open: false
    };

    var DepartmentUserSelect = function (config) {
        this.config = $.extend({}, defaults, config);
        this.__init();
        return this;
    };

    var getDlgHtml = function (config) {
        config = config || {};
        return `<div class="dlg-depart-wrap J-dlg-depart-wrap">
            <div class="dlg-depart-container">
                <div class="dlg-depart-title">${config.title || '选择销售'}</div>
                <span class="dlg-depart-close vd-icon icon-delete J-dlg-depart-close"></span>
                <div class="dlg-depart-cnt">
                    <div class="dlg-depart-cnt-block">
                        <div class="dlg-depart-cnt-top top-l">
                            <div class="dlg-depart-search-wrap J-dlg-depart-search-wrap">
                                <input class="dlg-depart-search-input J-dlg-depart-search-input" placeholder="搜索">
                                <span class="vd-icon icon-search"></span>
                                <span class="vd-icon icon-error2 J-dlg-depart-search-clear"></span>
                            </div>
                        </div>
                        <div class="dlg-depart-tree J-dlg-depart-tree"></div>
                        <div class="dlg-depart-search-list J-dlg-depart-search-list" style="display:none;"></div>
                    </div>
                    <div class="dlg-depart-cnt-block">
                        <div class="dlg-depart-cnt-top top-r">
                            <div class="dlg-depart-cnt-selected-txt J-dlg-depart-selected"></div>
                        </div>
                        <div class="dlg-depart-selected-list J-dlg-depart-selected-list"></div>
                        <div class="dlg-depart-cnt-footer">
                            <div class="dlg-depart-btn btn-primary J-dlg-depart-select-confirm">确定</div>
                            <div class="dlg-depart-btn J-dlg-depart-select-cancel">取消</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>`
    };

    DepartmentUserSelect.prototype = {
        constructor: 'DepartmentUserSelect',
        __init: function () {
            console.log('new dus')
            this.$el = $(this.config.el);
            this.selectedData = this.config.selected || [];
            this.selectedItems = [];
            this.listData = this.config.listData || [];
            this.allUserList = [];
            this.allUserIds = [];
            this.isInputFocus = false;
            this.__bindEvent();
        },
        __bindEvent: function () {
            var _this = this;

            this.$el.click(function () {

                if (_this.listData && _this.listData.length) {
                    _this.__initPageData();
                } else if (_this.config.url) {
                    var loading = '';
                    if (window.layer) {
                        loading = layer.load();
                    }

                    $.ajax({
                        url: _this.config.url,
                        dataType: 'json',
                        success: function (res) {
                            if (res.code === 0) {
                                if (window.layer) {
                                    layer.close(loading);
                                }

                                var list = [];

                                try {
                                    list = res.data.childOrganization[0].childOrganization
                                } catch (error) {

                                }

                                _this.listData = list;
                                _this.__initPageData();
                            }
                        }
                    })
                }
            })

            $(document).on('click', '.J-dlg-depart-close', function () {
                _this.close();
            })

            $(document).on('click', '.J-dlg-tree-depart', function () {
                var id = $(this).data('id');

                if ($(this).hasClass('open')) {
                    $(this).removeClass('open');
                    $('.J-dlg-tree-block[data-id=' + id + ']').hide();
                } else {
                    $(this).addClass('open')
                    $('.J-dlg-tree-block[data-id=' + id + ']').show();
                }

            })

            $(document).on('click', '.J-dlg-tree-user', function () {
                var id = $(this).data('id');
                var userIndex = _this.selectedData.indexOf(id);

                if (userIndex !== -1) {
                    _this.selectedData.splice(userIndex, 1);
                } else {
                    _this.selectedData.push(id);
                }

                _this.__refreshList();
            })

            $(document).on('click', '.J-dlg-tree-depart-checkbox', function (e) {
                e.stopPropagation();
                var $users = $(this).parents('.J-dlg-tree-depart:first').next('.J-dlg-tree-block').find('.J-dlg-tree-user');
                if ($(this).hasClass('all-select')) {
                    $users.each(function () {
                        var id = $(this).data('id');
                        var userIndex = _this.selectedData.indexOf(id);

                        if (userIndex !== -1) {
                            _this.selectedData.splice(userIndex, 1);
                        }
                    })
                } else {
                    $users.each(function () {
                        var id = $(this).data('id');
                        var userIndex = _this.selectedData.indexOf(id);

                        if (userIndex === -1) {
                            _this.selectedData.push(id);
                        }
                    })
                }

                _this.__refreshList();
            })

            $(document).on('click', '.J-dlg-depart-delete', function () {
                var id = $(this).data('id');
                var userIndex = _this.selectedData.indexOf(id);

                if (userIndex !== -1) {
                    _this.selectedData.splice(userIndex, 1);
                }

                _this.__refreshList();
            })

            $(document).on('click', '.J-dlg-depart-search-item', function () {
                var id = $(this).data('id');
                var userIndex = _this.selectedData.indexOf(id);

                if (userIndex !== -1) {
                    $(this).removeClass('checked');
                    _this.selectedData.splice(userIndex, 1);
                } else {
                    $(this).addClass('checked');
                    _this.selectedData.push(id);
                }

                _this.__refreshList();
            })

            $(document).on('input', '.J-dlg-depart-search-input', function () {
                _this.__search();
                _this.__checkInputValue();
            })

            $(document).on('blur', '.J-dlg-depart-search-input', function () {
                setTimeout(() => {
                    $('.J-dlg-depart-search-input').parent().removeClass('can-clear');
                    _this.isInputFocus = false;
                }, 200)
            })

            $(document).on('focus', '.J-dlg-depart-search-input', function () {
                _this.isInputFocus = true;
                _this.__checkInputValue();

                console.log('trigger focus')
            })

            $(document).on('mouseover', '.J-dlg-depart-search-wrap', function () {
                _this.__checkInputValue();
            })

            $(document).on('mouseleave', '.J-dlg-depart-search-wrap', function () {
                if (!_this.isInputFocus) {
                    setTimeout(() => {
                        $('.J-dlg-depart-search-input').parent().removeClass('can-clear');
                    }, 200)
                }
            })

            $(document).on('click', '.J-dlg-depart-search-clear', function () {
                $('.J-dlg-depart-search-input').val('');
                _this.__search();
                _this.__checkInputValue();

                setTimeout(() => {
                    $('.J-dlg-depart-search-input').focus();
                }, 300)
            })

            $(document).on('click', '.J-dlg-depart-select-confirm', function () {
                if(_this.config.must && !_this.selectedData.length) {
                    layer.msg(_this.config.must);

                    return;
                }
                _this.config.confirm && _this.config.confirm(_this.selectedData, _this.selectedItems);
                _this.close();
            })

            $(document).on('click', '.J-dlg-depart-select-cancel', function () {
                _this.close();
            })

        },
        __initPageData() {
            $('body').append(getDlgHtml(this.config));
            this.__getAllUsers(this.listData);

            this.__appendTreeItems(this.listData);

            if (this.config.open) {
                $('.J-dlg-tree-depart').click();
            }

            $('.J-dlg-depart-search-input').focus();

            if (this.selectedData && this.selectedData.length) {
                this.__refreshList();
            }
        },
        __checkInputValue() {
            var searchVal = $('.J-dlg-depart-search-input').val().trim();

            if (searchVal) {
                $('.J-dlg-depart-search-input').parent().addClass('can-clear');
            } else {
                $('.J-dlg-depart-search-input').parent().removeClass('can-clear');
            }
        },
        __appendTreeItems: function (list, params) {
            var _this = this;

            params = params || {
                lv: 1,
            }

            list.forEach(item => {
                if ((item.users && item.users.length) || (item.childOrganization && item.childOrganization.length)) {
                    var $wrap = $('.J-dlg-depart-tree');

                    if (params.id) {
                        $wrap = $('.J-dlg-tree-block[data-id=' + params.id + ']')
                    }

                    $wrap.append(`<div class="dlg-tree-node-item lv-${params.lv} J-dlg-tree-depart" data-parent="${params.id || ''}" data-id="${item.organizationId}">
                        <div class="tree-node-checkbox J-dlg-tree-depart-checkbox">
                            <span class="vd-icon icon-checkbox1"></span>
                            <span class="vd-icon icon-checkbox2"></span>
                            <span class="vd-icon icon-deduct"></span>
                        </div>
                        <span class="vd-icon icon-right"></span>
                        <span class="tree-node-icon-file"></span>
                        <div class="tree-node-name">${item.organizationName}</div>
                    </div><div class="J-dlg-tree-block" data-id="${item.organizationId}" style="display:none;"></div>`);

                    $.each(item.users, function (i, user) {
                        $('.J-dlg-tree-block[data-id=' + item.organizationId + ']').append(`<div class="dlg-tree-node-item lv-${params.lv + 1} J-dlg-tree-user" data-parent="${item.organizationId}" data-id="${user.userId}" data-name="${user.userName}" data-avatar="${user.aliasHeadPicture}">
                            <div class="tree-node-checkbox">
                                <span class="vd-icon icon-checkbox1"></span>
                                <span class="vd-icon icon-checkbox2"></span>
                            </div>
                            <img class="tree-node-avatar" src="${user.aliasHeadPicture || '/static/new/img/user-avatar.svg'}">
                            <div class="tree-node-name">${user.userName}</div>
                        </div>`);
                    })

                    if (item.childOrganization && item.childOrganization.length) {
                        _this.__appendTreeItems(item.childOrganization, {
                            lv: params.lv + 1,
                            id: item.organizationId
                        })
                    }
                }
            });
        },
        __refreshList() {
            var _this = this;

            $('.J-dlg-depart-wrap .tree-node-checkbox').removeClass('all-select on-select');
            $.each(this.selectedData, function (i, item) {
                $('.J-dlg-tree-user[data-id=' + item + ']').find('.tree-node-checkbox').addClass('all-select');
            })

            $('.J-dlg-tree-depart').each(function () {
                var num = 0;
                $(this).next('.J-dlg-tree-block').find('.J-dlg-tree-user').each(function () {
                    if ($(this).find('.tree-node-checkbox').hasClass('all-select')) {
                        num++;
                    }
                })

                if (num) {
                    if (num === $(this).next('.J-dlg-tree-block').find('.J-dlg-tree-user').length) {
                        $(this).find('.tree-node-checkbox').addClass('all-select')
                    } else {
                        $(this).find('.tree-node-checkbox').addClass('on-select')
                    }
                }
            })

            if (this.selectedData.length) {
                $('.J-dlg-depart-selected').html('已选（' + this.selectedData.length + '）');
            } else {
                $('.J-dlg-depart-selected').html('');
            }

            var selectedItems = [];
            $('.J-dlg-depart-selected-list').empty();

            $.each(this.selectedData, function (i, item) {
                var $user = $('.J-dlg-tree-user[data-id=' + item + ']').eq(0);
                var name = $user.data('name');
                var avatar = $user.data('avatar');

                selectedItems.push({
                    userId: item,
                    userName: name,
                    avatar: avatar
                });

                $('.J-dlg-depart-selected-list').append(`<div class="dlg-depart-selected-item">
                    <img class="dlg-depart-selected-avatar" src="${avatar || '/static/new/img/user-avatar.svg'}"/>
                    <div class="dlg-depart-selected-name">${name}</div>
                    <span class="vd-icon icon-delete J-dlg-depart-delete" data-id="${item}"></span>
                </div>`);
            })

            this.selectedItems = selectedItems;

            if ($('.J-dlg-depart-search-item').length) {
                $('.J-dlg-depart-search-item').each(function () {
                    var id = $(this).data('id');

                    if (_this.selectedData.indexOf(id) !== -1) {
                        $(this).addClass('checked');
                    } else {
                        $(this).removeClass('checked');
                    }
                })
            }
        },
        __getAllUsers: function (list) {
            var _this = this;
            $.each(list, function (i, item) {
                if (item.users && item.users.length) {
                    $.each(item.users, function (ii, user) {
                        if (_this.allUserIds.indexOf(user.userId) === -1) {
                            _this.allUserIds.push(user.userId);
                            _this.allUserList.push(user);
                        }
                    })
                }

                if (item.childOrganization) {
                    _this.__getAllUsers(item.childOrganization);
                }
            })
        },
        __search: function () {
            var searchVal = $('.J-dlg-depart-search-input').val().trim();

            var searchList = [];

            if (searchVal) {
                var reg = new RegExp('(' + searchVal + ')', 'ig');
                $.each(this.allUserList, function (i, user) {
                    if (user.userName.toUpperCase().indexOf(searchVal.toUpperCase()) !== -1) {
                        user.userNameLabel = user.userName.replace(reg, '<span class="strong">$1</span>');
                        searchList.push(user);
                    }
                })

                $('.J-dlg-depart-search-list').empty();

                var _this = this;

                if (searchList && searchList.length) {
                    $.each(searchList, function (i, item) {

                        $('.J-dlg-depart-search-list').append(`<div class="dlg-depart-search-item J-dlg-depart-search-item ${_this.selectedData.indexOf(item.userId) !== -1 ? 'checked' : ''}" data-id="${item.userId}">
                            <div class="dlg-depart-search-checkbox">
                                <span class="vd-icon icon-checkbox1"></span>
                                <span class="vd-icon icon-checkbox2"></span>
                            </div>
                            <img class="dlg-depart-search-avatar" src="${item.aliasHeadPicture || '/static/new/img/user-avatar.svg'}"/>
                            <div class="dlg-depart-search-name">${item.userNameLabel}</div>
                        </div>`)
                    })
                } else {
                    $('.J-dlg-depart-search-list').append(`<div class="dlg-depart-search-empty">
                        <span class="vd-icon icon-info1"></span>
                        <div class="dlg-depart-search-empty-txt">无匹配数据</div>
                    </div>`)
                }


                $('.J-dlg-depart-tree').hide();
                $('.J-dlg-depart-search-list').show();
            } else {
                $('.J-dlg-depart-tree').show();
                $('.J-dlg-depart-search-list').empty().hide();
            }
        },
        close: function () {
            $('.J-dlg-depart-wrap').remove();
        }
    }

    window.DepartmentUserSelect = DepartmentUserSelect;

    DepartmentUserSelect.initSelect = function (config) {
        var $wrap = $(config.placeholderEl);

        $wrap.append(`<div class="dus-select-trigger-wrap J-dus-select-trigger">
            <div class="dus-select-trigger-label J-dus-select-trigger-label">
                
            </div>
            <div class="dus-select-trigger-placeholder J-dus-select-trigger-placeholder">${config.placeholder || '全部'}</div>
            <span class="vd-icon icon-down"></span>
        </div>`);

        var selectedIds = config.selected;
        var selectedItems = config.selectedItems;

        var initSelectedLabel = function(list) {
            if(list && list.length) {
                $('.J-dus-select-trigger-label', $wrap).empty().css('display', 'flex');
                $('.J-dus-select-trigger-placeholder', $wrap).hide();
                $.each(list, function(i, item) {
                    $('.J-dus-select-trigger-label', $wrap).append(`
                        <div class="dus-select-trigger-label-item J-dus-select-trigger-label-item" data-id="${item.userId}">
                            <div class=""dus-select-trigger-label-txt">${item.userName ? item.userName.split('.')[0] : ''}</div>
                            <span class="vd-icon icon-delete J-dus-select-trigger-label-del" data-id="${item.userId}"></span>
                        </div>
                    `)
                });

                var top = 0;
                var num = 0;

                $('.J-dus-select-trigger-label-item').each(function(i) {
                    if(i === 0) {
                        top = $(this).offset().top;
                    }
                    
                    if($(this).offset().top !== top) {
                        num++;
                        $(this).addClass('need-hide');
                    }
                })

                $('.J-dus-select-trigger-label-item.need-hide').hide();

                if(num) {
                    $('.J-dus-select-trigger-label-item').eq($('.J-dus-select-trigger-label-item').length - num - 1).hide();
                    num++;
                    $('.J-dus-select-trigger-label', $wrap).append(`
                        <div class="dus-select-trigger-label-item J-dus-select-trigger-num">
                            <div class="dus-select-trigger-label-txt">+${num}</div>
                        </div>
                    `)

                    var checkNumTop = function() {
                        if($('.J-dus-select-trigger-num', $wrap).offset().top !== top) {
                            $('.J-dus-select-trigger-label-item').eq($('.J-dus-select-trigger-label-item').length - num - 1).hide();
                            num++;
                            $('.J-dus-select-trigger-num .dus-select-trigger-label-txt', $wrap).html('+' + num);

                            checkNumTop();
                        }
                    }

                    checkNumTop();
                }
            } else {
                $('.J-dus-select-trigger-label', $wrap).hide();
                $('.J-dus-select-trigger-placeholder', $wrap).show();
            }

            $('.J-dus-select-trigger-label-del').click(function(e) {
                e.stopPropagation();
    
                var id = $(this).data('id');
    
                if(selectedIds.indexOf(id) !== -1) {
                    selectedIds.splice(selectedIds.indexOf(id), 1);
                }
    
                $.each(selectedItems, function(i, user) {
                    if(user && user.userId == id) {
                        selectedItems.splice(i, 1);
                    }
                })
    
                initSelectedLabel(selectedItems);
    
                config.change && config.change(selectedIds, selectedItems);
            })
        };

        if(config.selectedItems && config.selectedItems.length) {
            initSelectedLabel(config.selectedItems);
        }

        new DepartmentUserSelect({
            el: $wrap.find('.J-dus-select-trigger'),
            ...config,
            confirm: function(value, items) {
                selectedIds = value;
                selectedItems = items;
                initSelectedLabel(selectedItems);
                config.change && config.change(selectedIds, selectedItems);
            }
        })
    }

}.call(this);