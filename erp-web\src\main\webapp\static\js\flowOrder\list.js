void function () {
    new Vue({
        el: '#app',
        data: {
            searchParams: {
                flowOrderNo: '',
                baseOrderNo: '',
                lastTraderName: '',
                skuNo: '',
                brand: '',
                contractStatus: null,
                creatorList: [],
                createDate: '',
                startAddTime: '',
                endAddTime: '',
                pushDirection: '',
            },
            creatorList: [],
            creatorLoading: false,
            activeIndex: '1',
            listData: [],
            total: 0,
            pageSize: 20,
            pageNo: 1,
            tableMaxHeight: 360,
            listIndex: 0,
            loading: true,
            status: '',
            baseBusinessType: '',
            buyNum: 0,
            saleNum: 0,
            //新增弹层字段
            isShowAddDialog: false,
            baseBusinessTypeList: [
                { label: '采购', value: 1 },
                { label: '销售', value: 2 }
            ],
            dialogValue: {
                pushDirection: '2',
                sourceErp: '',
                baseBusinessType: 1,
                baseBusinessNo: ''
            },
            baseBusinessNoError: '',
            pushDirectionList: [
                { label: 'ERP', value: 2 },
                { label: '金蝶', value: 1 },
            ],
            sourceErpList: [],
            sourceErpDisabled: false,
            baseBusinessTypeDisabled: false,
        },
        mounted() {
            document.title = '流转单列表';
            this.initTableMaxHeight();
            this.search();
            this.getCreator();
            this.getCompany();
        },
        methods: {
            initTableMaxHeight() {
                this.tableMaxHeight = window.innerHeight - 306;
            },
            handleTabSelect(index) {
                switch (index) {
                    case '1': {
                        this.status = '';
                        this.baseBusinessType = '';
                        break;
                    }
                    case '2': {
                        this.status = 0;
                        this.baseBusinessType = 1;
                        break;
                    }
                    case '3': {
                        this.status = 1;
                        this.baseBusinessType = 1;
                        break;
                    }
                    case '4': {
                        this.status = 0;
                        this.baseBusinessType = 2;
                        break;
                    }
                    case '5': {
                        this.status = 1;
                        this.baseBusinessType = 2;
                        break;
                    }
                }
                this.pageNo = 1;
                this.search();
            },
            search() {
                console.log(this.searchParams)

                let loading = this.$loading({
                    lock: true,
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.6)',
                    customClass: 'global-loading'
                });

                let reqData = {
                    pageSize: this.pageSize,
                    pageNum: this.pageNo,
                    param: {
                        ...this.searchParams,
                        auditStatus: this.status,
                        baseBusinessType: this.baseBusinessType,
                    }
                };

                axios.post('/flowOrder/page.do', reqData).then(({ data }) => {
                    loading.close();
                    this.loading = false;
                    if (data.code === 0) {
                        this.total = data.data.total;
                        // 处理合同状态显示
                        this.listData = data.data.list.map(item => {
                            return {
                                ...item,
                                contractStatusStr: this.getContractStatusStr(item.contractStatus),
                            };
                        });
                    } else {
                        this.$message({
                            message: data.message,
                            type: 'warning'
                        });
                    }
                })

                this.getUnauthNum(reqData)
            },
            getUnauthNum(reqData) {
                axios.post('/flowOrder/cornerNum.do', reqData).then(({ data }) => {
                    if (data.code === 0) {
                        this.buyNum = data.data.buyOrderNoAuditNum || 0;
                        this.saleNum = data.data.saleOrderNoAuditNum || 0;
                    }
                })
            },
            getCreator() {
                axios.get('/user/getAllNotDisabledUserList.do').then(({ data }) => {
                    if (data.code === 0) {
                        this.creatorList = data.data;
                    }
                })
            },
            handlerDateChange(date) {
                if (date && date.length) {
                    this.searchParams.startAddTime = date[0] + ' 00:00:00';
                    this.searchParams.endAddTime = date[1] + ' 23:59:59';
                } else {
                    this.searchParams.startAddTime = '';
                    this.searchParams.endAddTime = '';
                }
            },
            handlerSearch() {
                this.pageNo = 1;
                this.search();
            },
            handlerReset() {
                this.searchParams = {
                    flowOrderNo: '',
                    baseOrderNo: '',
                    lastTraderName: '',
                    skuNo: '',
                    brand: '',
                    contractStatus: null,
                    creatorList: [],
                    createDate: '',
                    startAddTime: '',
                    endAddTime: '',
                    pushDirection: '',
                };
                this.pageNo = 1;
                this.search();
            },
            handlerPagesizeChange(pageSize) {
                console.log(pageSize)
                this.pageNo = 1;
                this.pageSize = pageSize;
                this.search();
            },
            handlerPagenumChange(pageNum) {
                this.pageNo = pageNum;
                this.search();
            },

            gotoDetail(data) {
                let link = ''

                if (data.baseBusinessType == 1) {
                    link = `/flowOrder/buyOrderDetail.do?flowOrderId=${data.flowOrderId}`;
                } else {
                    link = `/flowOrder/saleOrderDetail.do?flowOrderId=${data.flowOrderId}`;
                }

                this.openLink(link, {
                    name: '业务流转-详情'
                })
            },
            gotoOrderDetail(data) {
                let link = ''

                if (data.baseBusinessType == 1) {
                    link = `/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=${data.baseOrderId}`;
                    this.openLink(link, {
                        name: '采购详情',
                        id: 'buy_order_detail_' + data.baseOrderId
                    })
                } else {
                    link = `/order/saleorder/view.do?saleorderId=${data.baseOrderId}`;
                    this.openLink(link, {
                        name: '销售详情',
                        id: 'sale_order_detail_' + data.baseOrderId
                    })
                }
            },


            // 新建弹层
            showAddDialog() {
                this.isShowAddDialog = true;

                this.dialogValue = {
                    pushDirection: 2,
                    sourceErp: '',
                    baseBusinessType: 1,
                    baseBusinessNo: ''
                };
                this.baseBusinessNoError = '';
                this.handlerPushDirectionChange(2)
            },
            handlerPushDirectionChange(val) {
                if (val == 1) {
                    // 选中金蝶：来源ERP禁用，来源固定"南京贝登医疗股份有限公司"
                    this.sourceErpDisabled = true;
                    let sourceFilter = this.sourceErpList.filter(item => item.companyShortName == 'VEDENG');
                    this.dialogValue.sourceErp = sourceFilter[0] && sourceFilter[0].companyShortName || '';

                    this.baseBusinessTypeDisabled = false; // 恢复类型禁用状态
                } else if (val == 2) {
                    // 选中ERP：类型禁用，类型固定"采购"
                    this.baseBusinessTypeDisabled = true;
                    this.dialogValue.baseBusinessType = 1;

                    this.sourceErpDisabled = false; // 恢复来源erp禁用状态
                    this.defaultSource = '';
                }
            },
            validAddBusinessNo() {
                let bizNo = this.dialogValue.baseBusinessNo.trim();
                if (!bizNo) {
                    this.baseBusinessNoError = '请填写关联单号';
                    return false;
                } else {
                    this.baseBusinessNoError = '';
                    return true;
                }
            },
            getCompany() {
                axios.get('/flowOrder/queryAllCompanyInfo.do').then(({ data }) => {
                    this.sourceErpList = data || [];
                })
            },
            gotoAdd() {
                if (!this.validAddBusinessNo()) {
                    return;
                }

                let loading = this.$loading({
                    lock: true,
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.6)',
                    customClass: 'global-loading'
                });

                axios.post('/flowOrder/init.do?baseBusinessNo=' + this.dialogValue.baseBusinessNo.trim() + 
                            '&baseBusinessType=' + this.dialogValue.baseBusinessType + 
                            '&pushDirection=' + this.dialogValue.pushDirection +
                            '&sourceErp=' + this.dialogValue.sourceErp
                ).then(({ data }) => {
                    loading.close();
                    this.isShowAddDialog = false;
                    if(data.code === 0) {
                        let link = ''

                        if (this.dialogValue.baseBusinessType == 1) {
                            link = `/flowOrder/editBuyOrder.do?baseBusinessType=1&baseBusinessNo=${this.dialogValue.baseBusinessNo.trim()}&pushDirection=${this.dialogValue.pushDirection}&sourceErp=${this.dialogValue.sourceErp}`;
                        } else {
                            link = `/flowOrder/editSaleOrder.do?baseBusinessType=2&baseBusinessNo=${this.dialogValue.baseBusinessNo.trim()}&pushDirection=${this.dialogValue.pushDirection}&sourceErp=${this.dialogValue.sourceErp}`;
                        }

                        this.openLink(link, {
                            name: '业务流转-新建'
                        })
                    } else {
                        this.$alert(data.message, '', {
                            confirmButtonText: '我知道了'
                        });
                    }
                })

            },
            getContractStatusStr(contractStatus) {
                switch (contractStatus) {
                    case 0:
                        return '无需上传';
                    case 1:
                        return '未上传';
                    case 2:
                        return '已上传';
                    default:
                        return '';
                }
            },
            openLink(link, params) {
                if (window.parent != window) {
                    if (window.parent.closableTab) {
                        var item = {
                            'id': params.id ? params.id : new Date().getTime(),
                            'name': params.name,
                            'url': link,
                            'closable': params.noclose ? false : true
                        };

                        window.parent.closableTab.addTab(item);
                        window.parent.closableTab.resizeMove();
                    }
                } else {
                    window.open(link);
                }
            },
        }
    });
}.call(this)
