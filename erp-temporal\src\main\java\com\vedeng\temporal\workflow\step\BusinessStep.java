package com.vedeng.temporal.workflow.step;

import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.enums.BusinessStepType;

/**
 * 业务步骤接口
 * 定义单个业务步骤的执行规范
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-03
 */
public interface BusinessStep {
    
    /**
     * 执行业务步骤
     *
     * @param request 业务请求对象
     * @param context 公司执行上下文
     * @return 业务处理结果
     */
    CompanyBusinessResponse execute(CompanyBusinessRequest request, CompanyExecutionContext context);
    
    /**
     * 获取业务步骤类型
     * 
     * @return 步骤类型
     */
    BusinessStepType getStepType();
    
    /**
     * 获取步骤名称
     * 
     * @return 步骤名称
     */
    String getStepName();
    
    /**
     * 获取步骤描述
     * 
     * @return 步骤描述
     */
    String getStepDescription();
    



}
