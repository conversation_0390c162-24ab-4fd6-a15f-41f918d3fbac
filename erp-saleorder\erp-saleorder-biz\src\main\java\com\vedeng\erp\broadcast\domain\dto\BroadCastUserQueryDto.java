package com.vedeng.erp.broadcast.domain.dto;

import lombok.*;

/**
 * 播报用户查询条件DTO
 * 用于人员管理页面的分页查询和筛选条件
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BroadCastUserQueryDto {
    
    /**
     * 用户名
     * 支持模糊查询，用于搜索ERP用户名
     */
    private String username;
    
    /**
     * AED销售ID
     * 用于筛选指定AED销售关联的用户
     */
    private Integer aedUserId;
} 