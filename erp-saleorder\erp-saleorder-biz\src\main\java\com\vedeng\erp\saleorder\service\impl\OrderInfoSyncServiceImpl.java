package com.vedeng.erp.saleorder.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.orderstrategy.OrderAmountStrategy;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.erp.buyorder.dto.Buyorder2SaleorderGoodsDto;
import com.vedeng.erp.buyorder.dto.ModifyOrderMessageDto;
import com.vedeng.erp.saleorder.api.OrderInfoSyncService;
import com.vedeng.erp.saleorder.model.dto.PaymentDto;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.finance.dao.CapitalBillMapper;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.goods.service.GoodsApiService;
import com.vedeng.goods.vo.CoreSkuVo;
import com.vedeng.logistics.dao.ExpressDetailMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.vo.ExpressArrivalDetailVo;
import com.vedeng.order.dao.*;
import com.vedeng.order.model.*;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.service.BussinessChanceService;
import com.vedeng.order.service.QuoteService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.RTraderJUserMapper;
import com.vedeng.trader.model.RTraderJUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 订单信息同步
 */
@Service("orderInfoSyncService")
public class OrderInfoSyncServiceImpl extends BaseSaleOrderService implements OrderInfoSyncService {

    Logger logger= LoggerFactory.getLogger(OrderInfoSyncServiceImpl.class);

    @Resource
    private UserMapper userMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private SaleorderService saleorderService;

    @Resource
    private AfterSalesService afterSalesOrderService;


    @Autowired
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    private CapitalBillMapper capitalBillMapper;

    @Autowired
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;

    @Autowired
    private ExpressDetailMapper expressDetailMapper;

    @Autowired
    private RBuyorderSaleorderMapper rBuyorderSaleorderMapper;

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Autowired
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private SaleorderModifyApplyMapper saleorderModifyApplyMapper;

    @Resource
    private SaleorderModifyApplyGoodsMapper saleorderModifyApplyGoodsMapper;

    @Autowired
    private VerifiesRecordService verifiesRecordService;

    @Resource
    private RTraderJUserMapper rTraderJUserMapper;

    @Autowired
    private OrderAmountStrategy orderAmountStrategy;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Autowired
    private UserService userService;

    @Resource
    private OrderNoDict orderNoDict;

    @Autowired
    private GoodsApiService goodsApiService;
    
    @Autowired
    @Qualifier("quoteService")
    private QuoteService quoteService;

    @Autowired
    private BussinessChanceService bussinessChanceService;

    @Override
    public void unlockSaleOrderGoods(String sku, Integer orderId, Integer userId) {
        AfterSalesVo afterSalesInfo = new AfterSalesVo();
        afterSalesInfo.setOrderId(orderId);
        afterSalesInfo.setSku(sku);
        afterSalesOrderService.getNoLockSaleorderGoodsVo(afterSalesInfo);

        Saleorder saleorder = new Saleorder();
        saleorder.setCompanyId(userId);
        saleorder.setSaleorderId(orderId);
        User user = userMapper.selectByPrimaryKey(userId);
        saleorderService.synchronousOrderStatus(user,saleorder);
    }



    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncPaymentStatusOfSaleOrder(Integer saleOrderId) {
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderId);
        if (saleorder == null) {
            return;
        }

        PaymentDto paymentDto = calculateOrderPaymentStatus(saleOrderId);
        Integer paymentStatus = paymentDto.getPaymentStatus();
        //更新订单支付状态，当销售商品全部退货时，支付状态不能回退
        if (paymentStatus > saleorder.getPaymentStatus()){
            Saleorder updatePaymentStatus = new Saleorder();
            updatePaymentStatus.setSaleorderId(saleOrderId);
            updatePaymentStatus.setPaymentStatus(paymentStatus);
            saleorderMapper.updateByPrimaryKeySelective(updatePaymentStatus);
            logger.info("同步订单：{}的支付状态，由支付状态：{}改为：{}",saleorder.getSaleorderNo(),saleorder.getPaymentStatus(),paymentStatus);
        }
        orderAmountStrategy.execute(saleorder);
    }

    /**
     * 计算订单有无付款
     * @param saleOrderId 销售订单ID
     * @return 付款状态 0:无付款 1：有付款
     */
    
    public PaymentDto calculatePaymentStatus(Integer saleOrderId) {
        logger.info("计算订单：{}的付款状态",saleOrderId);
        //实际支付金额（现金支付 + 账期支付）
        BigDecimal paymentAmount = capitalBillMapper.getPayedAmountOfSaleorder(saleOrderId);
        paymentAmount = paymentAmount == null ? BigDecimal.ZERO : paymentAmount;

        PaymentDto dto = new PaymentDto();
        dto.setPaymentStatus(0);
        if (paymentAmount.compareTo(BigDecimal.ZERO) > 0){
            dto.setPaymentStatus(1);
        }
        logger.info("计算订单：{}的付款状态为：{}",saleOrderId,dto.getPaymentStatus());
        return dto;
    }


    /**
     * 计算订单付款状态
     * @param saleOrderId 销售订单ID
     * @return 付款状态 1:部分付款 2:已付款
     */
    public PaymentDto calculateOrderPaymentStatus(Integer saleOrderId) {
        logger.info("计算订单：{}的付款状态",saleOrderId);
        //除去完结的售后退货商品
        List<SaleorderGoods> realSaleOrderGoodsList = getRealSaleOrderGoodsListExceptAfterSales(saleOrderId);

        //订单实际金额，除去售后的实际订单商品信息
        BigDecimal realAmount = realSaleOrderGoodsList
                .stream()
                .map(goods -> goods.getPrice().multiply(new BigDecimal(goods.getNum())))
                .reduce(BigDecimal.ZERO,BigDecimal::add);

        //实际支付金额（现金支付 + 账期支付）
        BigDecimal paymentAmount = capitalBillMapper.getPayedAmountOfSaleorder(saleOrderId);
        paymentAmount = paymentAmount == null ? BigDecimal.ZERO : paymentAmount;

        int i = realAmount.compareTo(paymentAmount) > 0 ? 1 : 2;
        PaymentDto dto = new PaymentDto();
        dto.setPaymentStatus(i);
        logger.info("计算订单：{}的付款状态为：{}",saleOrderId,dto.getPaymentStatus());
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncDeliveryAndArrivalDetailOfSaleOrder(Integer saleOrderId) {
        logger.info("syncDeliveryAndArrivalDetailOfSaleOrder执行了，saleOrderId：{}",saleOrderId);
        //虚拟商品集合
        List<Integer> vitrulSkuIdList = goodsApiService.getAllVirtualGoodsInfo(1).stream().map(CoreSkuVo::getSkuId).collect(Collectors.toList());

        //特殊商品集合
//        List<Integer> specialSkuIdList = sysOptionDefinitionMapper.getDictionaryByParentId(SysOptionConstant.SPECIAL_SKU)
//                .stream()
//                .map(SysOptionDefinition::getComments)
//                .map(Integer::valueOf)
//                .collect(Collectors.toList());

        //当前订单商品实际数量 = （除去虚拟商品）订单商品数量-售后退货商品数量
        List<SaleorderGoods> currentSaleOrderGoodsList = getRealSaleOrderGoodsListExceptAfterSales(saleOrderId)
                .stream()
                .filter(saleOrderGoods -> !vitrulSkuIdList.contains(saleOrderGoods.getGoodsId()))
                .collect(Collectors.toList());

        long deliveryTimeOfOrder = 0, arrivalTimeOfOrder = 0;

        //根据所有订单商品的发货状态、收货状态更新订单的发货状态和收货状态
        int deliveryStatusOfOrder = 0, arrivalStatusOfOrder = 0, sumDeliveryNumOfOrder = 0, sumArrivalNumOfOrder = 0,
                sumDeliveryStatusOfOrder = 0, sumArrivalStatusOfOrder = 0;

        for (SaleorderGoods saleOrderGoods : currentSaleOrderGoodsList){
            //获取订单商品的物流信息
            List<ExpressArrivalDetailVo> expressArrivalDetailVoList = new ArrayList<>();
            if (saleOrderGoods.getDeliveryDirect() == 1){
                //获取销售订单直发商品对应的采购单商品ID
                List<Integer> buyOrderGoodsIdList = rBuyorderSaleorderMapper.getBuyOrderGoodsIdBySaleOrderGoodsId(saleOrderGoods.getSaleorderGoodsId());
                if (!CollectionUtils.isEmpty(buyOrderGoodsIdList)){
                    expressArrivalDetailVoList = expressDetailMapper.getExpressArrivalDetailByRelatedIdListAndBusinessType(buyOrderGoodsIdList,515);
                }
            } else {
                //普发商品，根据出库记录来计算发货数量和时间
                expressArrivalDetailVoList = warehouseGoodsOperateLogMapper.getDeliveryDetailOfSaleorderGoods(saleOrderGoods.getSaleorderGoodsId(),
                        saleOrderGoods.getGoodsId());
            }
            //根据物流信息更新发货、收货状态、数量、时间等
            int deliveryStatus = 0, deliveryNum = 0, arrivalNum = 0, arrivalStatus = 0;
            long deliveryTime = 0, arrivalTime = 0;
            for (ExpressArrivalDetailVo item : expressArrivalDetailVoList){
                deliveryNum += item.getDeliveryNum();
                arrivalNum += item.getArrivalNum();
                deliveryTime = Math.max(item.getDeliveryTime(),deliveryTime);
                arrivalTime = Math.max(item.getArrivalTime(),arrivalTime);
            }

            if (deliveryNum > 0){
                deliveryStatus = deliveryNum < saleOrderGoods.getNum() ? 1 : 2;
            }

            if (arrivalNum > 0) {
                arrivalStatus = arrivalNum < saleOrderGoods.getNum() ? 1 : 2;
            }

            deliveryTimeOfOrder = Math.max(deliveryTime, deliveryTimeOfOrder);
            arrivalTimeOfOrder = Math.max(arrivalTime,arrivalTimeOfOrder);
            sumDeliveryNumOfOrder += deliveryNum;
            sumArrivalNumOfOrder += arrivalNum;
//            sumDeliveryStatusOfOrder += saleOrderGoods.getNum() == 0 ? 2 : deliveryStatus;
//            sumArrivalStatusOfOrder += saleOrderGoods.getNum() == 0 ? 2 : arrivalStatus;

            //更新订单商品的发货、收货状态和时间
            SaleorderGoods updateDeliveryAndArrivalOfGoods = new SaleorderGoods();
            updateDeliveryAndArrivalOfGoods.setSaleorderGoodsId(saleOrderGoods.getSaleorderGoodsId());
            updateDeliveryAndArrivalOfGoods.setDeliveryStatus(deliveryStatus);
            updateDeliveryAndArrivalOfGoods.setModTime(System.currentTimeMillis());
            updateDeliveryAndArrivalOfGoods.setDeliveryNum(deliveryNum);
            updateDeliveryAndArrivalOfGoods.setDeliveryTime(deliveryTime);
            updateDeliveryAndArrivalOfGoods.setArrivalStatus(arrivalStatus);
            updateDeliveryAndArrivalOfGoods.setArrivalTime(arrivalTime);
            updateDeliveryAndArrivalOfGoods.setAfterReturnNum(saleOrderGoods.getAfterReturnNum());
            saleorderGoodsMapper.updateByPrimaryKeySelective(updateDeliveryAndArrivalOfGoods);
            logger.info("同步更新订单商品：{}的发货、收货状态，更新值：{}",updateDeliveryAndArrivalOfGoods.getSaleorderGoodsId(), JSONObject.toJSONString(updateDeliveryAndArrivalOfGoods));
        }



        List<Integer> specialSkuIds = goodsApiService.findAllSpecialSkuIds();
        //实物+可见虚拟
        List<SaleorderGoods> saleorderGoods = getRealSaleOrderGoodsListExceptAfterSales(saleOrderId).stream()
                .filter(saleOrderGoods -> !specialSkuIds.contains(saleOrderGoods.getGoodsId()))
                .collect(Collectors.toList());

        // 未发货的全退
        long deliveryReturnNum = saleorderGoods.stream().filter(e -> e.getNum() == 0 && e.getDeliveryStatus() == 0).collect(Collectors.toList()).stream().count();
        long arrivalReturnNum = saleorderGoods.stream().filter(e -> e.getNum() == 0 && e.getArrivalStatus() == 0).collect(Collectors.toList()).stream().count();

        for (SaleorderGoods saleorderGood : saleorderGoods) {
            sumDeliveryStatusOfOrder += saleorderGood.getNum() == 0 ? (saleorderGood.getDeliveryStatus() == 0 ? 0 : 2) : saleorderGood.getDeliveryStatus();
            sumArrivalStatusOfOrder += saleorderGood.getNum() == 0 ? (saleorderGood.getArrivalStatus() == 0 ? 0 : 2) : saleorderGood.getArrivalStatus();
        }


        if (sumDeliveryStatusOfOrder > 0) {
            deliveryStatusOfOrder = sumDeliveryStatusOfOrder < 2 * (saleorderGoods.size() - deliveryReturnNum) ? 1 : 2;
        }

        if (sumArrivalStatusOfOrder > 0) {
            arrivalStatusOfOrder = sumArrivalStatusOfOrder < 2 * (saleorderGoods.size() - arrivalReturnNum) ? 1 : 2;
        }
        
        Saleorder updateDeliveryAndArrivalOfOrder = new Saleorder();
        updateDeliveryAndArrivalOfOrder.setSaleorderId(saleOrderId);
        updateDeliveryAndArrivalOfOrder.setDeliveryTime(deliveryTimeOfOrder);
        updateDeliveryAndArrivalOfOrder.setModTime(System.currentTimeMillis());
        updateDeliveryAndArrivalOfOrder.setDeliveryStatus(deliveryStatusOfOrder);
        updateDeliveryAndArrivalOfOrder.setArrivalTime(arrivalTimeOfOrder);
        updateDeliveryAndArrivalOfOrder.setArrivalStatus(arrivalStatusOfOrder);
        saleorderMapper.updateByPrimaryKeySelective(updateDeliveryAndArrivalOfOrder);
        logger.info("销售单：{}的sumArrivalStatusOfOrder为：{}，currentSaleOrderGoodsList为：{}", saleOrderId, sumArrivalStatusOfOrder, JSONObject.toJSONString(currentSaleOrderGoodsList));
        logger.info("同步更新订单：{}的发货、收货状态，更新值：{}",saleOrderId,JSONObject.toJSONString(updateDeliveryAndArrivalOfOrder));
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncInvoiceDetailOfSaleOrder(Integer saleOrderId) {
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderId);
        if (saleorder == null) {
            return;
        }

        //除去完结的售后退货商品
        List<SaleorderGoods> realSaleOrderGoodsList = getRealSaleOrderGoodsListExceptAfterSales(saleOrderId);
        //订单实际金额，除去售后的实际订单商品信息
        BigDecimal realAmount = realSaleOrderGoodsList
                .stream()
                .map(goods -> goods.getPrice().multiply(new BigDecimal(goods.getNum())))
                .reduce(BigDecimal.ZERO,BigDecimal::add);

        //订单已开票金额
        BigDecimal openInvoiceAmount = invoiceMapper.getSaleOpenInvoiceAmount(saleOrderId);
        int invoiceStatus = 0;
        if (openInvoiceAmount.compareTo(BigDecimal.ZERO) > 0){
            if (openInvoiceAmount.compareTo(realAmount) >= 0) {
                invoiceStatus = 2;
            } else {
                invoiceStatus = 1;
            }
        }
        if (saleorder.getInvoiceStatus() != invoiceStatus){
            //更新订单的开票状态
            Saleorder updateInvoiceStatus = new Saleorder();
            updateInvoiceStatus.setSaleorderId(saleOrderId);
            updateInvoiceStatus.setInvoiceStatus(invoiceStatus);
            saleorderMapper.updateByPrimaryKeySelective(updateInvoiceStatus);
            logger.info("同步更新订单：{}的开票状态，由原值：{}改为{}",saleorder.getSaleorderNo(),saleorder.getInvoiceStatus(),invoiceStatus);
        }
    }

    /**
     * 新增销售售后单（退货、换货、退款）时，审核结束后，通过activiti监听器来触发业务操作。除了activiti监听器，其他场景不可以调用该方法，不然有可能导致事务锁。
     * 1、更新销售单的售后状态 SERVICE_STATUS 为售后中
     * 2、更新销售单锁定状态，更新订单的预警状态
     * 3、如果售后存在商品，则同时锁定销售单商品
     * @param afterSalesId 售后单id
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void lockSaleOrderWhenAddAfterSales(Integer afterSalesId){
        long start=System.currentTimeMillis();
        AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
        if (afterSales == null) {
            return;
        }
        if (afterSales.getSubjectType() != 535 || !(afterSales.getType() == 539 || afterSales.getType() == 540 || afterSales.getType() == 543)){
            return;
        }
        Saleorder lockSaleOrder = new Saleorder();
        lockSaleOrder.setSaleorderId(afterSales.getOrderId());
        lockSaleOrder.setLockedStatus(1);
        lockSaleOrder.setLockedReason("售后锁定");
        lockSaleOrder.setServiceStatus(1);
        //锁定订单、更新订单售后状态
        saleorderMapper.updateByPrimaryKeySelective(lockSaleOrder);
        logger.info("新建售后单：{}，更新销售单：{}的锁定状态",afterSalesId,afterSales.getOrderNo());

        //售后对应的销售商品id
        List<Integer> saleOrderGoodsIdOfAfterSales = afterSalesGoodsMapper.getAfterSalesGoodsByAfterSalesId(afterSalesId)
                .stream()
                .filter(item -> item.getOrderDetailId() != null && item.getOrderDetailId() > 0)
                .map(AfterSalesGoods::getOrderDetailId)
                .collect(Collectors.toList());
        saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(afterSales.getOrderId()).forEach(
                item -> {
                    SaleorderGoods lockSaleOrderGoods = new SaleorderGoods();
                    lockSaleOrderGoods.setSaleorderGoodsId(item.getSaleorderGoodsId());
                    //如果售后单存在销售单商品，则锁定对应的销售订单商品
                    if (saleOrderGoodsIdOfAfterSales.contains(item.getSaleorderGoodsId())){
                        lockSaleOrderGoods.setLockedStatus(1);
                        saleorderGoodsMapper.updateByPrimaryKeySelective(lockSaleOrderGoods);
                        logger.info("新建售后单：{}，更新销售单商品：{}的锁定状态",afterSalesId,item.getSaleorderGoodsId());
                    }
                }
        );
        long end=System.currentTimeMillis();
        if((end-start)>10000){
            logger.error("事务过长");
        }

    }


    /**
     * 完结/关闭售后单时（退货、换货、退款），审核结束后，通过activiti监听器来触发业务操作。除了activiti监听器，其他场景不可以调用该方法，不然有可能导致事务锁。
     * 1、更新销售单的售后状态 SERVICE_STATUS 为售后关闭
     * 2、（退货、换货、退款）售后单->当前销售订单下面是否还有其他退换货的售后单->没有则解锁销售单商品锁定状态和销售单锁定状态
     * 3、更新销售单商品的预警状态
     * 4、售后单完结时，刷新销售单的票货款状态
     * @param afterSalesId 售后单id
     * @param operateType 售后单操作类型，1：关闭，2：完结
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void unlockSaleOrderWhenAfterSalesClosedOrFinished(Integer afterSalesId, Integer operateType){
        long start=System.currentTimeMillis();
        AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
        if (afterSales == null) {
            return;
        }
        if (afterSales.getSubjectType() != 535 || !(afterSales.getType() == 539 || afterSales.getType() == 540 || afterSales.getType() == 543)){
            return;
        }

        //判断是否还有待确认、进行中的退换货、退款的售后单（除去当前售后单）
        List<AfterSales> afterSalesListOfRunning = afterSalesMapper.getAfterSalesBySaleOrderId(afterSales.getOrderId())
                .stream()
                .filter(item -> !item.getAfterSalesId().equals(afterSalesId))
                .filter(item -> (item.getType() == 539 || item.getType() == 540 || item.getType() == 543))
                .filter(item -> item.getAtferSalesStatus() < 2)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(afterSalesListOfRunning)){
            Saleorder unlockSaleOrder = new Saleorder();
            unlockSaleOrder.setSaleorderId(afterSales.getOrderId());
            unlockSaleOrder.setServiceStatus(operateType == 1 ? 3 : 2);
            unlockSaleOrder.setLockedStatus(0);
            unlockSaleOrder.setRetainageAmount(null);
            saleorderMapper.updateByPrimaryKeySelective(unlockSaleOrder);
            logger.info("完结/关闭售后单：{}，更新销售单：{}的锁定状态",afterSalesId,afterSales.getOrderId());

            //订单解锁后，更新订单商品的预警状态
            long nowTime = DateUtil.gainNowDate();
            saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(afterSales.getOrderId()).forEach(
                    item -> {
                        if (item.getAgingTime() != null && item.getAgingTime() > 0) {
                            SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
                            saleorderGoodsVo.setSaleorderGoodsId(item.getSaleorderGoodsId());
                            saleorderGoodsVo.setWarnLevel(null);
                            saleorderGoodsVo.setAging(0);
                            saleorderGoodsVo.setAgingTime(nowTime);
                            saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
                        }
                    }
            );
        }

        List<Integer> saleOrderGoodsIdOfAfterSales = new ArrayList<>();
        if (!CollectionUtils.isEmpty(afterSalesListOfRunning)){
            List<Integer> afterSalesIdListOfRunning = afterSalesListOfRunning.stream().map(AfterSales::getAfterSalesId).collect(Collectors.toList());
            saleOrderGoodsIdOfAfterSales = afterSalesGoodsMapper.getAfterSalesGoodsByAfterSalesIdList(afterSalesIdListOfRunning)
                    .stream()
                    .map(AfterSalesGoods::getOrderDetailId)
                    .distinct()
                    .collect(Collectors.toList());
        }
        List<SaleorderGoods> saleOrderGoodsList = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(afterSales.getOrderId());
        if (!CollectionUtils.isEmpty(saleOrderGoodsList)){
            for (SaleorderGoods item : saleOrderGoodsList){
                if (!saleOrderGoodsIdOfAfterSales.contains(item.getSaleorderGoodsId())){
                    SaleorderGoods unlockSaleOrderGoods = new SaleorderGoods();
                    unlockSaleOrderGoods.setSaleorderGoodsId(item.getSaleorderGoodsId());
                    unlockSaleOrderGoods.setLockedStatus(0);
                    saleorderGoodsMapper.updateByPrimaryKeySelective(unlockSaleOrderGoods);
                    logger.info("完结/关闭售后单：{}时，更新订单商品：{}的锁定状态",afterSalesId,item.getSaleorderGoodsId());
                }
            }
        }

        if (operateType == 2){
            logger.info("售后单：{}完结，同步销售单：{}的票货款信息",afterSalesId,afterSales.getOrderId());
            this.syncPaymentStatusOfSaleOrder(afterSales.getOrderId());
            this.syncDeliveryAndArrivalDetailOfSaleOrder(afterSales.getOrderId());
            this.syncInvoiceDetailOfSaleOrder(afterSales.getOrderId());

            //销售订单商品全部退货，并无待确认和进行中的售后订单时，自动关闭销售订单
            autoCloseSaleOrderWhenAfterSalesFinished(afterSales.getOrderId(),afterSalesId);
        }
        long end=System.currentTimeMillis();
        if((end-start)>10000){
            logger.error("事务过长");
        }
    }


    /**
     * 当售后退货单完结时，判断销售订单商品是否全部退货，并无待确认和进行中的售后订单时，自动关闭销售订单
     * @param saleOrderId 销售订单id
     * @param afterSalesId 售后单id
     */
    private void autoCloseSaleOrderWhenAfterSalesFinished(Integer saleOrderId, Integer afterSalesId){
        Integer allCountOfSaleOrder = getRealSaleOrderGoodsListExceptAfterSales(saleOrderId)
                .stream()
                .map(SaleorderGoods::getNum)
                .reduce(0, Integer::sum);

        //判断是否还有待确认、进行中的售后单（除去当前售后单）
        List<AfterSales> afterSalesListOfRunning = afterSalesMapper.getAfterSalesBySaleOrderId(saleOrderId)
                .stream()
                .filter(item -> !item.getAfterSalesId().equals(afterSalesId))
                .filter(item -> item.getAtferSalesStatus() < 2)
                .collect(Collectors.toList());

        if (allCountOfSaleOrder <= 0 && CollectionUtils.isEmpty(afterSalesListOfRunning)){
            //自动关闭对应的销售单
            Saleorder closeSaleOrder = new Saleorder();
            closeSaleOrder.setSaleorderId(saleOrderId);
            closeSaleOrder.setStatus(3);
            closeSaleOrder.setCloseComments("售后单完结且全部退货，自动关闭订单");
            saleorderMapper.updateByPrimaryKeySelective(closeSaleOrder);
            logger.info("售后单：{}完结后，订单全部退货并无待确认和进行中的售后单，订单：{}自动更新为关闭状态",afterSalesId,saleOrderId);
            try{
                Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderId);
                if (Objects.nonNull(saleorder.getQuoteorderId())) {
                    Quoteorder quoteorder = quoteService.relateCloseQuote(saleorder.getQuoteorderId(), "SYS_AUTO_CLOSE_TYPE_2");
                    logger.info("联动关闭报价单：{}", JSON.toJSONString(quoteorder));
                    if (Objects.nonNull(quoteorder) && Objects.nonNull(quoteorder.getBussinessChanceId())) {
                        BussinessChance bussinessChance = bussinessChanceService.relateCloseBussChance(quoteorder.getBussinessChanceId(), "SYS_AUTO_CLOSE_TYPE_2", 3);
                        logger.info("联动关闭商机：{}", JSON.toJSONString(bussinessChance));
                    }
                }
            }catch (Exception e){
                logger.info("订单关闭后联动关闭报价、商机失败：{}", e.getMessage());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editBuyOrderInfoSync(ModifyOrderMessageDto modifyOrderMessageDto) {
        switch (modifyOrderMessageDto.getValidStatus()){
            case 0:
                //提交审核，创建销售修改单
                createModifySaleOrder(modifyOrderMessageDto);
                break;
            case 1:
            case 2:
                updateModifySaleOrderStatus(modifyOrderMessageDto);
                break;
            default:
                break;
        }

    }

    public void createModifySaleOrder(ModifyOrderMessageDto modifyOrderMessageDto){
        SaleorderModifyApply saleorderModifyApply = new SaleorderModifyApply();
        User user = userMapper.selectByPrimaryKey(modifyOrderMessageDto.getCreatorId());
        if (!Objects.isNull(user)) {
            saleorderModifyApply.setCompanyId(user.getCompanyId());
            saleorderModifyApply.setCreator(user.getUserId());
        }
        if (modifyOrderMessageDto.getIsWmsCancel() != null && modifyOrderMessageDto.getIsWmsCancel()){
            saleorderModifyApply.setIsWmsCancel(ErpConst.ONE);
        }else {
            saleorderModifyApply.setIsWmsCancel(ErpConst.ZERO);
        }
        saleorderModifyApply.setAddTime(System.currentTimeMillis());
        saleorderModifyApply.setRelatedId(modifyOrderMessageDto.getBuyorderModifyApplyId());
        // 获取原订单主信息
        Saleorder saleorderInfo = saleorderMapper.selectByPrimaryKey(modifyOrderMessageDto.getSaleorderId());
        //设置基本信息
        setBaseInfo(saleorderModifyApply,saleorderInfo);
        Integer i = saleorderModifyApplyMapper.insertSelective(saleorderModifyApply);
        Integer saleorderModifyApplyId = saleorderModifyApply.getSaleorderModifyApplyId();
        // 申请修改单号
        SaleorderModifyApply saleorderExtra = new SaleorderModifyApply();
        saleorderExtra.setSaleorderModifyApplyId(saleorderModifyApplyId);
        saleorderModifyApply.setSaleorderModifyApplyNo(orderNoDict.getOrderNum(saleorderModifyApplyId, 12));
        saleorderExtra.setSaleorderModifyApplyNo(saleorderModifyApply.getSaleorderModifyApplyNo());
        saleorderModifyApplyMapper.updateByPrimaryKeySelective(saleorderExtra);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(modifyOrderMessageDto.getBuyorder2SaleorderGoodsDtoList())){
            for (Buyorder2SaleorderGoodsDto buyorder2SaleorderGoodsDto : modifyOrderMessageDto.getBuyorder2SaleorderGoodsDtoList()) {
                SaleorderGoods saleOrderGoods = saleorderGoodsMapper.selectByPrimaryKey(buyorder2SaleorderGoodsDto.getSaleorderGoodsId());
                SaleorderModifyApplyGoods saleorderModifyApplyGoods = new SaleorderModifyApplyGoods();
                saleorderModifyApplyGoods.setSaleorderModifyApplyId(saleorderModifyApply.getSaleorderModifyApplyId());
                saleorderModifyApplyGoods.setSaleorderGoodsId(buyorder2SaleorderGoodsDto.getSaleorderGoodsId());
                saleorderModifyApplyGoods.setDeliveryDirect(buyorder2SaleorderGoodsDto.getDirectNowValue());
                saleorderModifyApplyGoods.setDeliveryDirectComments(buyorder2SaleorderGoodsDto.getDeliveryDirectChangeReason());
                saleorderModifyApplyGoods.setGoodsComments(saleOrderGoods.getGoodsComments());
                saleorderModifyApplyGoods
                        .setOldDeliveryDirect(buyorder2SaleorderGoodsDto.getDirectOrginalValue());
                saleorderModifyApplyGoods
                        .setOldDeliveryDirectComments(saleOrderGoods.getDeliveryDirectComments());
                saleorderModifyApplyGoods.setOldGoodsComments(saleOrderGoods.getGoodsComments());
                saleorderModifyApplyGoodsMapper.insertSelective(saleorderModifyApplyGoods);
            }
        }
        saleorderModifyApply.setSaleorderModifyApplyId(saleorderModifyApplyId);

        // 对应销售订单需要锁定
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleorderModifyApply.getSaleorderId());
        saleorder.setLockedStatus(1);
        saleorder.setLockedReason("订单修改审核");
        int x = saleorderMapper.updateByPrimaryKeySelective(saleorder);

        //锁定时更新预警状态
        updateLockSaleorderWarning(saleorderModifyApply.getSaleorderId());

        //保存审核状态
        VerifiesInfo verifiesInfo = new VerifiesInfo();
        verifiesInfo.setRelateTable("T_SALEORDER_MODIFY_APPLY");
        verifiesInfo.setRelateTableKey(saleorderModifyApplyId);
        verifiesInfo.setStatus(ErpConst.ZERO);
        //对应字典表editSaleorderVerify
        verifiesInfo.setVerifiesType(634);
        verifiesInfo.setAddTime(System.currentTimeMillis());
        verifiesRecordService.saveVerifiesInformation(verifiesInfo);


        Map<String, String> map = new HashMap<>();
        map.put("saleorderNo", saleorderInfo.getSaleorderNo());
        String url = "./orderstream/saleorder/viewModifyApply.do?saleorderModifyApplyId="
                + saleorderModifyApply.getSaleorderModifyApplyId() + "&saleorderId="
                + saleorderInfo.getSaleorderId();
        //订单归属销售
        RTraderJUser queryParam = new RTraderJUser();
        if (saleorderInfo.getTraderId() > 0){
            queryParam.setTraderId(saleorderInfo.getTraderId());
            queryParam.setTraderType(ErpConst.ONE);
            List<RTraderJUser> rTraderJUsers = rTraderJUserMapper.getUserTrader(queryParam);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(rTraderJUsers)) {
                List<Integer> userIds = rTraderJUsers.stream().map(item -> item.getUserId()).collect(Collectors.toList());
                //站内信通知归属销售
                MessageUtil.sendMessage(207, userIds, map, url);
            }
        }

    }

    public void setBaseInfo(SaleorderModifyApply saleorderModifyApply,Saleorder saleorderInfo){
        saleorderModifyApply.setSaleorderId(saleorderInfo.getSaleorderId());
        saleorderModifyApply.setTakeTraderName(saleorderInfo.getTakeTraderName());
        saleorderModifyApply.setInvoiceTraderName(saleorderInfo.getInvoiceTraderName());
        saleorderModifyApply.setTakeTraderId(saleorderInfo.getTakeTraderId());
        saleorderModifyApply.setInvoiceTraderId(saleorderInfo.getInvoiceTraderId());
        saleorderModifyApply.setTakeTraderContactId(saleorderInfo.getTakeTraderContactId());
        saleorderModifyApply.setTakeTraderContactName(saleorderInfo.getTakeTraderContactName());
        saleorderModifyApply.setTakeTraderContactMobile(saleorderInfo.getTakeTraderContactMobile());
        saleorderModifyApply.setTakeTraderContactTelephone(saleorderInfo.getTakeTraderContactTelephone());
        saleorderModifyApply.setTakeTraderAddressId(saleorderInfo.getTakeTraderAddressId());
        saleorderModifyApply.setTakeTraderArea(saleorderInfo.getTakeTraderArea());
        saleorderModifyApply.setTakeTraderAddress(saleorderInfo.getTakeTraderAddress());
        saleorderModifyApply.setLogisticsComments(saleorderInfo.getLogisticsComments());
        saleorderModifyApply.setInvoiceTraderContactId(saleorderInfo.getInvoiceTraderContactId());
        saleorderModifyApply.setInvoiceTraderContactName(saleorderInfo.getInvoiceTraderContactName());
        saleorderModifyApply.setInvoiceTraderContactMobile(saleorderInfo.getInvoiceTraderContactMobile());
        saleorderModifyApply.setInvoiceTraderContactTelephone(saleorderInfo.getInvoiceTraderContactTelephone());
        saleorderModifyApply.setInvoiceTraderAddressId(saleorderInfo.getInvoiceTraderAddressId());
        saleorderModifyApply.setInvoiceTraderArea(saleorderInfo.getInvoiceTraderArea());
        saleorderModifyApply.setInvoiceTraderAddress(saleorderInfo.getInvoiceTraderAddress());
        saleorderModifyApply.setInvoiceComments(saleorderInfo.getInvoiceComments());
        saleorderModifyApply.setInvoiceType(saleorderInfo.getInvoiceType());
        saleorderModifyApply.setIsSendInvoice(saleorderInfo.getIsSendInvoice());
        saleorderModifyApply.setIsDelayInvoice(saleorderInfo.getIsDelayInvoice());
        saleorderModifyApply.setInvoiceMethod(saleorderInfo.getInvoiceMethod());
        saleorderModifyApply.setDeliveryMethod(saleorderInfo.getDeliveryMethod());
        saleorderModifyApply.setDeliveryType(saleorderInfo.getDeliveryType());
        saleorderModifyApply.setDeliveryClaim(saleorderInfo.getDeliveryClaim());
        saleorderModifyApply.setDeliveryDelayTime(saleorderInfo.getDeliveryDelayTime());
        saleorderModifyApply.setIsPrintout(saleorderInfo.getIsPrintout());
        saleorderModifyApply.setLogisticsId(saleorderInfo.getLogisticsId());
        saleorderModifyApply.setFreightDescription(saleorderInfo.getFreightDescription());
        saleorderModifyApply.setOldTakeTraderContactId(saleorderInfo.getTakeTraderContactId());
        saleorderModifyApply.setOldTakeTraderContactName(saleorderInfo.getTakeTraderContactName());
        saleorderModifyApply.setOldTakeTraderContactMobile(saleorderInfo.getTakeTraderContactMobile());
        saleorderModifyApply.setOldTakeTraderContactTelephone(saleorderInfo.getTakeTraderContactTelephone());
        saleorderModifyApply.setOldTakeTraderAddressId(saleorderInfo.getTakeTraderAddressId());
        saleorderModifyApply.setOldTakeTraderArea(saleorderInfo.getTakeTraderArea());
        saleorderModifyApply.setOldTakeTraderAddress(saleorderInfo.getTakeTraderAddress());
        saleorderModifyApply.setOldLogisticsComments(saleorderInfo.getLogisticsComments());
        saleorderModifyApply.setOldInvoiceTraderContactId(saleorderInfo.getInvoiceTraderContactId());
        saleorderModifyApply.setOldInvoiceTraderContactName(saleorderInfo.getInvoiceTraderContactName());
        saleorderModifyApply.setOldInvoiceTraderContactMobile(saleorderInfo.getInvoiceTraderContactMobile());
        saleorderModifyApply.setOldInvoiceTraderContactTelephone(saleorderInfo.getInvoiceTraderContactTelephone());
        saleorderModifyApply.setOldInvoiceTraderAddressId(saleorderInfo.getInvoiceTraderAddressId());
        saleorderModifyApply.setOldInvoiceTraderArea(saleorderInfo.getInvoiceTraderArea());
        saleorderModifyApply.setOldInvoiceTraderAddress(saleorderInfo.getInvoiceTraderAddress());
        saleorderModifyApply.setOldInvoiceComments(saleorderInfo.getInvoiceComments());
        saleorderModifyApply.setOldInvoiceType(saleorderInfo.getInvoiceType());
        saleorderModifyApply.setOldIsSendInvoice(saleorderInfo.getIsSendInvoice());
        saleorderModifyApply.setOldIsDelayInvoice(saleorderInfo.getIsDelayInvoice());
        saleorderModifyApply.setOldInvoiceMethod(saleorderInfo.getInvoiceMethod());
        saleorderModifyApply.setOldDeliveryMethod(saleorderInfo.getDeliveryMethod());
        saleorderModifyApply.setOldDeliveryType(saleorderInfo.getDeliveryType());
        saleorderModifyApply.setOldDeliveryClaim(saleorderInfo.getDeliveryClaim());
        saleorderModifyApply.setOldDeliveryDelayTime(saleorderInfo.getDeliveryDelayTime());
        saleorderModifyApply.setOldIsPrintout(saleorderInfo.getIsPrintout());
        saleorderModifyApply.setOldLogisticsId(saleorderInfo.getLogisticsId());
        saleorderModifyApply.setOldFreightDescription(saleorderInfo.getFreightDescription());
        saleorderModifyApply.setModifyType(ErpConst.ONE);
    }

    public void updateModifySaleOrderStatus(ModifyOrderMessageDto modifyOrderMessageDto){
        // 获取修改订单信息
        SaleorderModifyApply saleorderModifyApply = saleorderModifyApplyMapper.getSaleorderModifyApply(modifyOrderMessageDto.getSaleorderId(),modifyOrderMessageDto.getBuyorderModifyApplyId());
        if (Objects.isNull(saleorderModifyApply)){
            return;
        }
        if (ErpConst.ONE.equals(modifyOrderMessageDto.getValidStatus())){
            //保存审核状态,审核通过
            updateStatus(1,saleorderModifyApply);
            //更新主表信息
            saveSaleorderModifyApplyToSaleorder(saleorderModifyApply);
            //站内信通知归属销售
            sendMessage(209,modifyOrderMessageDto,saleorderModifyApply);


        }else if (ErpConst.TWO.equals(modifyOrderMessageDto.getValidStatus())){
            //保存审核状态,审核不通过
            updateStatus(2,saleorderModifyApply);

            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderId(modifyOrderMessageDto.getSaleorderId());
            // 释放订单锁定状态
            saleorder.setLockedStatus(0);
            saleorderMapper.updateByPrimaryKeySelective(saleorder);

            updateUnlockSaleOrderWarning(saleorder.getSaleorderId());
            //站内信通知归属销售
            sendMessage(208,modifyOrderMessageDto,saleorderModifyApply);
        }
    }

    public void updateStatus(Integer status,SaleorderModifyApply saleorderModifyApply){
        //保存审核状态
        VerifiesInfo verifiesInfo = new VerifiesInfo();
        verifiesInfo.setRelateTable("T_SALEORDER_MODIFY_APPLY");
        verifiesInfo.setRelateTableKey(saleorderModifyApply.getSaleorderModifyApplyId());
        verifiesInfo.setStatus(status);
        //对应字典表editSaleorderVerify
        verifiesInfo.setVerifiesType(634);
        verifiesInfo.setModTime(System.currentTimeMillis());
        verifiesRecordService.saveVerifiesInformation(verifiesInfo);
    }

    public void sendMessage(Integer messageTemplateId,ModifyOrderMessageDto modifyOrderMessageDto,SaleorderModifyApply saleorderModifyApply){
        Saleorder saleorderInfo = saleorderMapper.selectByPrimaryKey(modifyOrderMessageDto.getSaleorderId());
        //订单归属销售
        RTraderJUser queryParam = new RTraderJUser();
        queryParam.setTraderId(saleorderInfo.getTraderId());
        queryParam.setTraderType(ErpConst.ONE);
        List<RTraderJUser> rTraderJUsers = rTraderJUserMapper.getUserTrader(queryParam);
        Map<String, String> map = new HashMap<>();
        map.put("saleorderNo", saleorderInfo.getSaleorderNo());
        String url = "./orderstream/saleorder/viewModifyApply.do?saleorderModifyApplyId="
                + saleorderModifyApply.getSaleorderModifyApplyId() + "&saleorderId="
                + saleorderModifyApply.getSaleorderId();
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(rTraderJUsers)) {
            List<Integer> userIds = rTraderJUsers.stream().map(item -> item.getUserId()).collect(Collectors.toList());
            MessageUtil.sendMessage(messageTemplateId, userIds, map, url);
        }
    }




    /**
     * 订单修改信息同步到订单
     * @param saleorderModifyApply
     */
    public void saveSaleorderModifyApplyToSaleorder(SaleorderModifyApply saleorderModifyApply){
        // 获取订单修改主信息
        SaleorderModifyApply saleorderModifyApplyInfo = saleorderModifyApplyMapper
                .getSaleorderModifyApplyInfo(saleorderModifyApply.getSaleorderModifyApplyId());
        // 修改对应的订单
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleorderModifyApplyInfo.getSaleorderId());
        saleorder.setLockedStatus(0);// 释放订单锁定状态


        // 获取订单修改产品列表信息
        List<SaleorderModifyApplyGoods> goodsList = saleorderModifyApplyGoodsMapper
                .getSaleorderModifyApplyGoodsById(saleorderModifyApply);
        // 普发数量
        int notDeliveryDirectNum = 0;
        for (SaleorderModifyApplyGoods saleorderModifyApplyGoods : goodsList) {
            SaleorderGoods saleorderGoods = new SaleorderGoods();
            saleorderGoods.setSaleorderGoodsId(saleorderModifyApplyGoods.getSaleorderGoodsId());
            saleorderGoods.setDeliveryDirect(saleorderModifyApplyGoods.getDeliveryDirect());
            saleorderGoods.setDeliveryDirectComments(saleorderModifyApplyGoods.getDeliveryDirectComments());
            saleorderGoods.setGoodsComments(saleorderModifyApplyGoods.getGoodsComments());
            saleorderGoodsMapper.updateByPrimaryKeySelective(saleorderGoods);

            if (saleorderModifyApplyGoods.getDeliveryDirect() == 1) {
                saleorder.setDeliveryDirect(1);
            } else {
                notDeliveryDirectNum++;
            }
        }

        if (notDeliveryDirectNum == goodsList.size()) {
            saleorder.setDeliveryDirect(0);
        }

        saleorderMapper.updateByPrimaryKeySelective(saleorder);

        updateUnlockSaleOrderWarning(saleorder.getSaleorderId());
    }


    @Override
    public void updateSaleorderForbuyorderReturnOver(Integer afterSalesId, Integer buyorderId) {
        if(afterSalesId > 0 && buyorderId > 0){
            Buyorder so = buyorderMapper.selectByPrimaryKey(buyorderId);

            //采购售后完成后需刷新销售单的采购状态
            AfterSalesGoods afterSalesGoods = new AfterSalesGoods();

            afterSalesGoods.setAfterSalesId(afterSalesId);

            List<AfterSalesGoodsVo> list = afterSalesGoodsMapper.getBuyorderAfterSalesGoodsVosList(afterSalesGoods);

            if(!CollectionUtils.isEmpty(list)){

                for (AfterSalesGoodsVo asgv : list) {

                    //查询采购数量
                    Integer buyNum = rBuyorderSaleorderMapper.getBuyorderGoodsNumByParam(asgv.getOrderDetailId());

                    if(buyNum.intValue() >= asgv.getNum().intValue()){//采购数量大于采购售后数量，销售单采购状态肯定为部分采购
                        //最简单情况--同类产品售后全退
                        List<SaleorderGoods> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoByBuyorderGoodsId(asgv.getOrderDetailId());

                        if(sgvList != null && sgvList.size() == 1){

                            //如果当前销售订单产品有多个，且已全部采购，则本次修改为部分采购，&& sgvList.get(0).getNum().intValue() == buyNum.intValue()
                            Saleorder saleorder = new Saleorder();

                            saleorder.setSaleorderId(sgvList.get(0).getSaleorderId());

                            List<SaleorderGoodsVo> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);

                            if(!CollectionUtils.isEmpty(saleorderGoodsList) && saleorder.getSaleorderId() > 0){

                                saleorder.setPurchaseStatus(ErpConst.ZERO);

                                for (SaleorderGoodsVo sgv : saleorderGoodsList) {

                                    Integer everybuyNum = rBuyorderSaleorderMapper.getBuyorderGoodsNum(sgv.getSaleorderGoodsId());//每个销售商品的采购数量
                                    Integer everybuyafterNum = afterSalesGoodsMapper.getBuyorderAftersaleReturnGoodsBySaleorderGoodsId(sgv.getSaleorderGoodsId());//每个销售商品对应的采购商品的采购退货数量

                                    if(everybuyNum - everybuyafterNum > 0 ){
                                        saleorder.setPurchaseStatus(1);
                                        break;
                                    }

                                }
                                saleorderMapper.updateByPrimaryKeySelective(saleorder);
                            }

                        }

                    }

                }

            }
        }
    }

    @Override
    public Integer setSaleorderIsNewByOrg(String saleorderNo,Integer traderId) {

        if(traderId == null || ErpConst.ZERO.equals(traderId)){
            logger.info("销售单{},的客户id信息为空，无法查询对应归属部门信息",saleorderNo);
            return ErpConst.ZERO;
        }
        User sale = userMapper.getUserByTraderId(traderId, ErpConst.ONE);//获取归属销售信息
        if(sale == null){
            logger.info("销售单{},的客户id{},归属人信息为空",saleorderNo,traderId);
            return ErpConst.ZERO;
        }
        User saleInfo = userMapper.getUserByName(sale.getUsername());//获取归属销售的部门信息
        if(saleInfo.getOrgId() == null){
            logger.info("销售单{},的客户id{},归属人三级部门部门信息为空",saleorderNo,traderId);
            return ErpConst.ZERO;
        }
        if(userService.handleOrgIds(saleInfo)){
            //三级部门信息与apollo配置对比
            //符合条件即为使用新订单流的部门
            return ErpConst.ONE;
        }else {
            return ErpConst.ZERO;
        }
    }
}
