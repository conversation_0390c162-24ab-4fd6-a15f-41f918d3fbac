package com.vedeng.crm.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 企微
 * @date 2024/7/12 14:54
 */
@Controller
@RequestMapping("/crm/wx")
@Slf4j
public class WxController extends BaseController {

    @Value("${wx.agent-id:1000062}")
    private String agentId ;


    @RequestMapping(value = "/index")
    @NoNeedAccessAuthorization
    public String index(HttpServletRequest request, @RequestParam(value = "target", required = false) String target) {
        request.setAttribute("agentId",agentId);
        request.setAttribute("target", target);
        return "/vue/view/crm/profile/wx/index";
    }


    @RequestMapping(value = "/transfer")
    @NoNeedAccessAuthorization
    public String transfer() {
        return "/vue/view/crm/profile/wx/transfer";
    }

    @RequestMapping(value = "/jump")
    @NoNeedAccessAuthorization
    public String jump(HttpServletRequest request,@RequestParam(value = "m")String murl, @RequestParam(value = "p")String pcurl) {
        request.setAttribute("murl",murl);
        request.setAttribute("pcurl",pcurl);

        return "/vue/view/crm/profile/wx/jump";
    }
}
