void function () {
    new Vue({
        el: '#page-container',
        data: {
            customList: [],
            baseSettingList: [],
            departmentList: [],
            allUserList: [],
            isloading: true,
            userRemoteInfo: {
                url: '/system/user/searchUserListForSelectLimit.do',
                paramsMethod: 'get',
                paramsKey: 'username',
            },
            isShowCustomError: false,
            customErrorMsg: '',
            cansubmit: true,
        },
        async created() {
            const loading = this.$loading({
                lock: true,
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });

            this.isloading = true;

            await this.getAllDepartmentList();

            await this.getInitData();

            this.isloading = false;

            loading.close();
        },
        methods: {
            getAllDepartmentList() {
                return axios.post('/broadcast/common/treeDept.do?parentId=0').then(({ data }) => {
                    if (data.code === 0) {
                        this.departmentList = data.data || [];
                    }
                })
            },
            getInitData() {
                return axios.post('/broadcast/relate/init.do').then(({ data }) => {
                    if (data.code === 0) {
                        let depList = data.data.deptRelationList || [];
                        depList.forEach(item => {
                            if (item.broadcastDeptSelect && item.broadcastDeptSelect.value) {
                                item.department = item.broadcastDeptSelect.value;

                                this.departmentList.forEach(dep => {
                                    if (dep.value == item.department) {
                                        item.groupList = dep.children || [];
                                    }
                                })

                                if (item.broadcastGroupSelect && item.broadcastGroupSelect.value) {
                                    item.group = item.broadcastGroupSelect.value;
                                }
                            } else {
                                item.department = '';
                                item.group = '';
                                item.groupList = [];
                            }

                            item.isGroupLoading = false;
                        });

                        this.baseSettingList = depList;

                        let userList = data.data.userRelationList || [];

                        userList.forEach(item => {
                            if (item.erpUserSelect && item.erpUserSelect.value) {
                                item.userId = item.erpUserSelect.value;
                                item.userName = item.erpUserSelect.label;
                            }

                            if (item.broadcastDeptSelect && item.broadcastDeptSelect.value) {
                                item.department = item.broadcastDeptSelect.value;

                                this.departmentList.forEach(dep => {
                                    if (dep.value == item.department) {
                                        item.groupList = dep.children || [];
                                    }
                                })

                                if (item.broadcastGroupSelect && item.broadcastGroupSelect.value) {
                                    item.group = item.broadcastGroupSelect.value;
                                }
                            } else {
                                item.department = '';
                                item.group = '';
                                item.groupList = [];
                            }

                            item.isGroupLoading = false;
                        });

                        this.customList = userList;
                    }
                })
            },
            handlerDepartmentChange(index, data) {
                if (data.value && this.baseSettingList[index].prevDepartment !== data.value) {
                    this.baseSettingList[index].isGroupLoading = true;
                    this.baseSettingList[index].groupList = data.selected.children || [];
                    this.baseSettingList[index].prevDepartment = data.value;
                    this.baseSettingList[index].group = '';

                    setTimeout(() => {
                        this.baseSettingList[index].isGroupLoading = false;
                    })
                } else if (!data.value) {
                    this.baseSettingList[index].isGroupLoading = true;
                    this.baseSettingList[index].groupList = [];
                    this.baseSettingList[index].prevDepartment = '';
                    this.baseSettingList[index].group = '';

                    setTimeout(() => {
                        this.baseSettingList[index].isGroupLoading = false;
                    })
                }
            },
            handlerCustomDepartmentChange(index, data) {
                if (data.value && this.customList[index].prevDepartment !== data.value) {
                    this.customList[index].isGroupLoading = true;
                    this.customList[index].groupList = data.selected.children || [];
                    this.customList[index].prevDepartment = data.value;
                    this.customList[index].group = '';

                    setTimeout(() => {
                        this.customList[index].isGroupLoading = false;
                    })
                }
            },
            handlerCustomChange() {
                if (this.checkCustomValue()) {
                    this.isShowCustomError = false;
                }
            },
            addCustom() {
                this.customList.push({
                    userId: '',
                    department: '',
                    group: '',
                    groupList: [],
                    isGroupLoading: false
                });
            },
            deleteCustomItem(index) {
                this.customList.splice(index, 1);
                this.handlerCustomChange();
            },
            checkCustomValue() {
                let flag = true;
                let sameFlag = false;
                let users = {};

                this.customList.forEach(item => {
                    if (!item.userId || !item.department || !item.group) {
                        flag = false;
                        this.customErrorMsg = '请完整填写销售、归属二级部及归属三级组。';
                    }

                    if(item.userId) {
                        if(!users[item.userId]) {
                            users[item.userId] = 1;
                        } else {
                            sameFlag = true;
                        }
                    }
                })

                if(!flag) {
                    return false;
                }

                if(flag && sameFlag) {
                    this.customErrorMsg = '请勿重复配置同一销售';
                    return false;
                }

                return true;
            },
            save() {
                if (!this.checkCustomValue()) {
                    this.isShowCustomError = true;
                    window.scrollTo(0, document.body.scrollHeight);
                    return;
                }

                let deptSaveList = [];

                this.baseSettingList.forEach(item => {
                    if (item.group) {
                        deptSaveList.push({
                            broadcastGroupId: item.group,
                            erpDeptId: item.erpDeptId
                        })
                    }
                })

                let userSaveList = [];

                this.customList.forEach(item => {
                    userSaveList.push({
                        broadcastGroupId: item.group,
                        erpUserId: item.userId
                    })
                })

                this.cansubmit = false;

                const loading = this.$loading({
                    lock: true,
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
        
                axios.post('/broadcast/relate/saveOrUpdate.do', {
                    deptSaveList,
                    userSaveList
                }).then(({ data }) => {
                    loading.close();
                    if(data.code === 0) {
                        this.$message({
                            message: '保存成功',
                            type: 'success'
                        })
                    } else {
                        this.$message({
                            message: data.message || '保存失败',
                            type: 'error'
                        })
                    }

                    this.cansubmit = true;
                })
            }
        }
    })
}.call(this);