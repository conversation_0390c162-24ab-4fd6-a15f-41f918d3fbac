	<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
      	<groupId>com.vedeng.erp</groupId>
		<artifactId>erp</artifactId>
		<version>1.0.0-SNAPSHOT</version>
    </parent>
	<artifactId>erp-old</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<packaging>jar</packaging>
	<name>erp-old</name>
	<url>http://maven.apache.org</url>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<mysql.driver.version>5.1.47</mysql.driver.version>
		<spring.version>4.1.9.RELEASE</spring.version>
		<activiti.version>5.22.0</activiti.version>
		<mybatis.version>3.3.1</mybatis.version>
		<mybatis-spring.version>1.2.3</mybatis-spring.version>
		<shiro.version>1.3.2</shiro.version>
		<junit.version>4.12</junit.version>
		<druid.version>1.1.16</druid.version>
		<log4j.version>1.2.17</log4j.version>
		<fastjson.version>1.2.72</fastjson.version>
		<commons.fileupload.version>1.3.2</commons.fileupload.version>
		<commons.codec.version>1.10</commons.codec.version>
		<jedis.version>2.6.2</jedis.version>
		<axis2.version>1.7.8</axis2.version>
		<ftpclient-pool.version>1.0-SNAPSHOT</ftpclient-pool.version>
		<skipTests>true</skipTests>
	</properties>

	<dependencies>



		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itextpdf</artifactId>
			<version>5.5.13</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.lowagie/itext -->
		<dependency>
			<groupId>com.lowagie</groupId>
			<artifactId>itext</artifactId>
			<version>4.2.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>fontbox</artifactId>
			<version>2.0.31</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.31</version>
		</dependency>
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itext-asian</artifactId>
			<version>5.2.0</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.base</groupId>
			<artifactId>base-api</artifactId>
			<version>1.2.4-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>com.vedeng.framework</groupId>
					<artifactId>vedeng-bean</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.vedeng</groupId>
			<artifactId>wechat-api</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>


		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-saleorder-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-buyorder-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-goods-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-kingdee-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-wms-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-common-core</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-common-activiti</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-common-trace</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-infrastructure</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-websocket</artifactId>
			<version>8.5.39</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-jasper</artifactId>
			<version>8.5.39</version>
			<scope>provided</scope>
		</dependency>

		<!--避免启动出错-->
		<dependency>
			<groupId>tomcat</groupId>
			<artifactId>jasper-runtime</artifactId>
			<version>5.5.23</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.9</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.invi</groupId>
			<artifactId>invi-api</artifactId>
			<version>1.0.1-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<artifactId>bcpkix-jdk15on</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
			</exclusions>

		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.solr/solr-solrj -->
		<!--rabbitMq依赖-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-amqp</artifactId>
			<version>1.5.2.RELEASE</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.solr/solr-solrj -->
		<dependency>
			<groupId>org.apache.solr</groupId>
			<artifactId>solr-solrj</artifactId>
			<version>7.4.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.hamcrest/hamcrest-all -->
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-all</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
		</dependency>

		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis</artifactId>
			<version>${mybatis.version}</version>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-spring</artifactId>
			<version>${mybatis-spring.version}</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>${junit.version}</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql.driver.version}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<version>${druid.version}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>

		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>${commons.codec.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>${commons.fileupload.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-core</artifactId>
			<version>${shiro.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-spring</artifactId>
			<version>${shiro.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-ehcache</artifactId>
			<version>${shiro.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-redis</artifactId>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>${jedis.version}</version>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
			<version>1.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.5</version>
		</dependency>
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-mapper-asl</artifactId>
			<version>1.9.13</version>
		</dependency>

		<dependency>
			<groupId>commons-jxpath</groupId>
			<artifactId>commons-jxpath</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<classifier>jdk15</classifier>
			<version>2.2.3</version>
		</dependency>
		<dependency>
			<groupId>javax.inject</groupId>
			<artifactId>javax.inject</artifactId>
			<version>1</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.8.8</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.8.8</version>
		</dependency>


		<dependency>
			<groupId>org.apache.xmlbeans</groupId>
			<artifactId>xmlbeans</artifactId>
			<version>3.1.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.2</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.4</version>
		</dependency>



		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20160810</version>
		</dependency>

		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.5</version>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>5.4.1.Final</version>
		</dependency>

		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
			<version>1.1.0.Final</version>
		</dependency>

		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-adb</artifactId>
			<version>${axis2.version}</version>
			<exclusions>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-spring</artifactId>
			<version>${axis2.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-transport-http</artifactId>
			<version>${axis2.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-transport-local</artifactId>
			<version>${axis2.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-xmlbeans</artifactId>
			<version>${axis2.version}</version>
		</dependency>
		<!--axis2 end -->
		<dependency>
			<groupId>org.jdom</groupId>
			<artifactId>jdom</artifactId>
			<version>2.0.0</version>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-engine</artifactId>
			<version>${activiti.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-spring</artifactId>
			<version>${activiti.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-bpmn-model</artifactId>
			<version>${activiti.version}</version>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-modeler</artifactId>
			<version>${activiti.version}</version>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-bpmn-converter</artifactId>
			<version>${activiti.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-bpmn-layout</artifactId>
			<version>${activiti.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-common-rest</artifactId>
			<version>${activiti.version}</version>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-image-generator</artifactId>
			<version>${activiti.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-json-converter</artifactId>
			<version>${activiti.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-rest</artifactId>
			<version>${activiti.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-process-validation</artifactId>
			<version>${activiti.version}</version>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-explorer</artifactId>
			<version>${activiti.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.4</version>
		</dependency>

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>2.9.9</version>
		</dependency>

		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.2.1</version>
		</dependency>

		<dependency>

			<groupId>org.springframework</groupId>
			<artifactId>spring-websocket</artifactId>
			<version>${spring.version}</version>
		</dependency>

		<dependency>

			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.2.1</version>
		</dependency>

		<dependency>
			<groupId>net.sf.barcode4j</groupId>
			<artifactId>barcode4j-light</artifactId>
			<version>2.0</version>
		</dependency>

		<!-- AOP begin -->
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>1.7.4</version>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.7.4</version>
		</dependency>
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib</artifactId>
			<version>3.1</version>
		</dependency>
		<!-- AOP end -->

		<!-- https://mvnrepository.com/artifact/javax.mail/mail -->
		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>mail</artifactId>
			<version>1.5.0-b01</version>
		</dependency>


        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.0</version>
        </dependency>

        <!-- iReport JasperReports -->
		<dependency>
			<groupId>net.sf.jasperreports</groupId>
			<artifactId>jasperreports</artifactId>
			<version>5.6.0</version>
			<exclusions>
				<exclusion>
					<groupId>eclipse</groupId>
					<artifactId>jdtcore</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-all</artifactId>
			<version>2.2.2</version>
		</dependency>

		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-pdf-itext5</artifactId>
			<version>9.0.3</version>
		</dependency>

		<dependency>
			<groupId>org.jboss.logging</groupId>
			<artifactId>jboss-logging</artifactId>
			<version>3.1.4.GA</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml</groupId>
			<artifactId>classmate</artifactId>
			<version>1.4.0</version>
		</dependency>
		<dependency>
			<groupId>org.thymeleaf</groupId>
			<artifactId>thymeleaf</artifactId>
			<version>3.0.11.RELEASE</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>


		<dependency>
			<groupId>sf-csim</groupId>
			<artifactId>printer-api</artifactId>
			<version>1.0.3</version>
		</dependency>
		<dependency>
			<groupId>sf-csim</groupId>
			<artifactId>express-sdk</artifactId>
			<version>1.3</version>
		</dependency>


		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.1.0</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.ezadmin</groupId>
			<artifactId>ezadmin-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>io.github.ezadmin126</groupId>
			<artifactId>ezadmin-core</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.tomcat</groupId>
			<artifactId>tomcat-jdbc</artifactId>
			<version>8.5.6</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.baidu.unbiz</groupId>
			<artifactId>fluent-validator</artifactId>
			<version>1.0.9</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-logging</artifactId>
			<version>2.1.3.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.xuxueli</groupId>
			<artifactId>xxl-job-core</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.codehaus.janino</groupId>
			<artifactId>janino</artifactId>
			<version>2.6.1</version>
		</dependency>
		<dependency>
			<groupId>org.tuckey</groupId>
			<artifactId>urlrewritefilter</artifactId>
			<version>4.0.4</version>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper</artifactId>
			<version>5.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.im4java</groupId>
			<artifactId>im4java</artifactId>
			<version>1.4.0</version>
		</dependency>

	  <dependency>
	      <groupId>com.vedeng.passport</groupId>
	      <artifactId>passport-api</artifactId>
	      <version>1.0.2-SNAPSHOT</version>
		  <exclusions>
			  <exclusion>
				  <groupId>org.springframework.cloud</groupId>
				  <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
			  </exclusion>
			  <exclusion>
				  <groupId>org.springframework.cloud</groupId>
				  <artifactId>spring-cloud-starter-openfeign</artifactId>
			  </exclusion>
		  </exclusions>
	  </dependency>

		<dependency>
			<groupId>com.vedeng.stock</groupId>
			<artifactId>stock-api</artifactId>
			<version>1.0.4.3-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.stock.admin</groupId>
			<artifactId>stock.api.admin</artifactId>
			<version>1.0.0.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>easyexcel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.vedeng.price</groupId>
			<artifactId>price-api</artifactId>
			<version>1.0.8-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.crm</groupId>
			<artifactId>crm-api</artifactId>
			<version>1.1.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
		</dependency>

		<!-- vedeng oss依赖包-->
		<dependency>
			<groupId>com.vedeng.file</groupId>
			<artifactId>file-api</artifactId>
			<version>1.1.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-web</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>

			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.vedeng.jc</groupId>
			<artifactId>jc-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<!--goods 服务-->
		<dependency>
			<groupId>com.vedeng.goods</groupId>
			<artifactId>goods-provider-api</artifactId>
			<version>1.1.6-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.market</groupId>
			<artifactId>market-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.vedeng.message</groupId>
			<artifactId>message-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-web</artifactId>
				</exclusion>

			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.vedeng.ftpclient</groupId>
			<artifactId>ftpclient-pool</artifactId>
			<version>${ftpclient-pool.version}</version>
			<exclusions>
				<exclusion>
					<groupId>slf4j-api</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alibaba.otter</groupId>
			<artifactId>canal.client</artifactId>
			<version>1.1.0</version>
			<exclusions>
				<exclusion>
					<groupId>io.netty</groupId>
					<artifactId>netty-all</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

  		<dependency>
			<groupId>com.vedeng.track</groupId>
			<artifactId>track-api</artifactId>
			<version>1.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.op</groupId>
			<artifactId>op-api</artifactId>
			<version>1.2.4-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson</artifactId>
			<version>3.11.6</version>
		</dependency>

		<!-- 马良ApiJAR -->
		<dependency>
			<groupId>com.vedeng.ml</groupId>
			<artifactId>ml-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.ty</groupId>
			<artifactId>ty-api</artifactId>
			<version>1.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>io.buji</groupId>
			<artifactId>buji-pac4j</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.pac4j</groupId>
			<artifactId>pac4j-core</artifactId>
			<version>2.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.pac4j</groupId>
			<artifactId>pac4j-cas</artifactId>
			<version>2.1.0</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.order</groupId>
			<artifactId>order-api</artifactId>
			<version>1.2.2-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>org.hibernate.validator</groupId>
					<artifactId>hibernate-validator</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.vedeng.uac</groupId>
			<artifactId>vedeng-uac-api</artifactId>
			<version>1.1.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.baidu</groupId>
			<artifactId>ueditor</artifactId>
			<version>1.1.3</version>
		</dependency>
		<dependency>
			<groupId>cow-java</groupId>
			<artifactId>java-sdk</artifactId>
			<version>0.0.1</version>
		</dependency>


		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-base</artifactId>
			<version>4.4.0</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml-schemas</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-web</artifactId>
			<version>4.4.0</version>
		</dependency>
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-annotation</artifactId>
			<version>4.4.0</version>
		</dependency>

		<dependency>
			<groupId>eu.bitwalker</groupId>
			<artifactId>UserAgentUtils</artifactId>
			<version>1.21</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-trader-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.doc</groupId>
			<artifactId>doc-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-system-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-finance-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

		<dependency>
		    <groupId>com.itextpdf.tool</groupId>
		    <artifactId>xmlworker</artifactId>
		    <version>5.5.13</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>0.9.1</version>
		</dependency>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-oa-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


	<build>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.8.1</version>
					<configuration>
						<encoding>UTF-8</encoding>
						<source>1.8</source>
						<target>1.8</target>
					</configuration>
				</plugin>

				<plugin>
					<groupId>org.mybatis.generator</groupId>
					<artifactId>mybatis-generator-maven-plugin</artifactId>
					<version>1.3.2</version>
					<configuration>

						<verbose>true</verbose>
						<overwrite>true</overwrite>
					</configuration>
					<dependencies>
						<!--mysql驱动包-->
						<dependency>
							<groupId>mysql</groupId>
							<artifactId>mysql-connector-java</artifactId>
							<version>${mysql.driver.version}</version>
						</dependency>
					</dependencies>

				</plugin>
			</plugins>
	</build>


	<distributionManagement>
		<repository>
			<id>vedeng-release</id>
			<name>maven-releases</name>
			<url>http://nexus.ivedeng.com/repository/maven-releases/</url>
		</repository>

		<snapshotRepository>
			<id>vedeng-snapshot</id>
			<name>maven-snapshots</name>
			<url>http://nexus.ivedeng.com/repository/maven-snapshots/</url>
		</snapshotRepository>
	</distributionManagement>

	<repositories>
		<repository>
			<id>maven</id>
			<name>maven-public</name>
				<url>http://nexus.ivedeng.com/repository/maven-public/</url>
			<layout>default</layout>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>third</id>
			<name>third</name>
			<url>http://nexus.ivedeng.com/repository/third/</url>
			<layout>default</layout>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>

</project>
