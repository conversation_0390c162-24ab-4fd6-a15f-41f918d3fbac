.ym-select-wrap {
  position: relative;
}
.ym-select-wrap .ym-select-trigger {
  border: 1px solid #BABFC2;
  height: 30px;
  border-radius: 3px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  font-size: 12px;
  cursor: pointer;
}
.ym-select-wrap .ym-select-trigger .icon-down {
  font-size: 13px;
  margin-left: 5px;
  transition: transform 0.22s ease;
}
.ym-select-wrap .ym-select-trigger .placeholder {
  color: #999;
}
.ym-select-wrap .ym-select-trigger.open {
  border-color: #09f;
}
.ym-select-wrap .ym-select-trigger.open .icon-down {
  transform: rotate(180deg);
}
.ym-select-wrap .ym-select-drop-wrap {
  position: absolute;
  z-index: 11;
  font-size: 12px;
  border: solid 1px #BABFC2;
  background: #fff;
  width: 132px;
  left: 0;
  border-radius: 3px;
  display: none;
}
.ym-select-wrap .ym-select-drop-wrap .ym-select-drop-year {
  display: flex;
  align-items: center;
  border-bottom: solid 1px #E1E5E8;
}
.ym-select-wrap .ym-select-drop-wrap .ym-select-drop-year .icon-slide-up {
  width: 38px;
  height: 38px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
}
.ym-select-wrap .ym-select-drop-wrap .ym-select-drop-year .icon-slide-up.left {
  transform: rotate(-90deg);
}
.ym-select-wrap .ym-select-drop-wrap .ym-select-drop-year .icon-slide-up.right {
  transform: rotate(90deg);
}
.ym-select-wrap .ym-select-drop-wrap .ym-select-drop-year .icon-slide-up:hover {
  color: #09f;
}
.ym-select-wrap .ym-select-drop-wrap .ym-select-drop-year .icon-slide-up.hide {
  opacity: 0;
  pointer-events: none;
  z-index: -1;
}
.ym-select-wrap .ym-select-drop-wrap .ym-select-drop-year .year-txt {
  flex: 1;
  text-align: center;
}
.ym-select-wrap .ym-select-drop-month-wrap {
  padding: 5px 0;
}
.ym-select-wrap .ym-select-drop-month-wrap .ym-select-drop-month-list {
  max-height: 264px;
  overflow: auto;
  overscroll-behavior: contain;
}
.ym-select-wrap .ym-select-drop-month-wrap .ym-select-drop-month-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.ym-select-wrap .ym-select-drop-month-wrap .ym-select-drop-month-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.ym-select-wrap .ym-select-drop-month-wrap .ym-select-drop-month-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.ym-select-wrap .ym-select-drop-month-wrap .ym-select-drop-month-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.ym-select-wrap .ym-select-drop-month-wrap .ym-select-drop-month-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.ym-select-wrap .ym-select-drop-month-wrap .ym-select-drop-month-list .ym-month-item {
  line-height: 33px;
  padding: 0 10px;
  cursor: pointer;
}
.ym-select-wrap .ym-select-drop-month-wrap .ym-select-drop-month-list .ym-month-item:hover {
  background: #f5f7fa;
}
.ym-select-wrap .ym-select-drop-month-wrap .ym-select-drop-month-list .ym-month-item.disabled {
  cursor: not-allowed;
  color: #999;
}
.ym-select-wrap .ym-select-drop-month-wrap .ym-select-drop-month-list .ym-month-item.disabled:hover {
  background: #fff;
}
.ym-select-wrap .ym-select-drop-month-wrap .ym-select-drop-month-list .ym-month-item.active {
  color: #09f;
}
