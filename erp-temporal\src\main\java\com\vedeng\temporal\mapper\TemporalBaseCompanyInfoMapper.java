package com.vedeng.temporal.mapper;

import com.vedeng.temporal.domain.entity.BaseCompanyInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Temporal模块专用的公司基本信息Mapper接口
 * 用于访问T_BASE_COMPANY_INFO表
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-26
 */
public interface TemporalBaseCompanyInfoMapper {

    /**
     * 根据公司简称查询公司信息
     *
     * @param shortName 公司简称
     * @return 公司信息实体
     */
    BaseCompanyInfoEntity selectByShortName(@Param("shortName") String shortName);

    /**
     * 查询所有有效的公司信息
     * 排除已删除的记录
     *
     * @return 有效的公司信息列表
     */
    List<BaseCompanyInfoEntity> findAllActive();

    /**
     * 根据主键查询公司信息
     *
     * @param id 主键ID
     * @return 公司信息实体
     */
    BaseCompanyInfoEntity selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 根据公司名称查询公司信息
     *
     * @param companyName 公司名称
     * @return 公司信息实体
     */
    BaseCompanyInfoEntity selectByCompanyName(@Param("companyName") String companyName);

    /**
     * 根据公司简称查询对应的真实TRADER_ID
     * 通过关联T_TRADER_SUPPLIER表获取正确的traderId
     *
     * @param shortName 公司简称
     * @return 真实的TRADER_ID，如果未找到则返回null
     */
    Long selectTraderIdByShortName(@Param("shortName") String shortName);
}
