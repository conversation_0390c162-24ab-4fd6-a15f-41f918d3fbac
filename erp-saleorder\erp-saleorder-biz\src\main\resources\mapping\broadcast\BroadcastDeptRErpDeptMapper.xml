<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.broadcast.mapdao.BroadcastDeptRErpDeptMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptRErpDeptEntity">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="ERP_DEPT_ID" jdbcType="INTEGER" property="erpDeptId"/>
        <result column="BROADCAST_DEPT_ID" jdbcType="INTEGER" property="broadcastDeptId"/>
        <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, ERP_DEPT_ID, BROADCAST_DEPT_ID, IS_DELETED, ADD_TIME, MOD_TIME, CREATOR, UPDATER
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_DEPT_R_ERP_DEPT
        where ID = #{id,jdbcType=INTEGER}
        and IS_DELETED = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update T_BROADCAST_DEPT_R_ERP_DEPT
        set IS_DELETED = 1, MOD_TIME = now()
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptRErpDeptEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_DEPT_R_ERP_DEPT (ERP_DEPT_ID, BROADCAST_DEPT_ID,
        IS_DELETED, ADD_TIME, MOD_TIME,
        CREATOR, UPDATER)
        values (#{erpDeptId,jdbcType=INTEGER}, #{broadcastDeptId,jdbcType=INTEGER},
        #{isDeleted,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptRErpDeptEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_DEPT_R_ERP_DEPT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="erpDeptId != null">
                ERP_DEPT_ID,
            </if>
            <if test="broadcastDeptId != null">
                BROADCAST_DEPT_ID,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="erpDeptId != null">
                #{erpDeptId,jdbcType=INTEGER},
            </if>
            <if test="broadcastDeptId != null">
                #{broadcastDeptId,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptRErpDeptEntity">
        update T_BROADCAST_DEPT_R_ERP_DEPT
        <set>
            <if test="erpDeptId != null">
                ERP_DEPT_ID = #{erpDeptId,jdbcType=INTEGER},
            </if>
            <if test="broadcastDeptId != null">
                BROADCAST_DEPT_ID = #{broadcastDeptId,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptRErpDeptEntity">
        update T_BROADCAST_DEPT_R_ERP_DEPT
        set ERP_DEPT_ID = #{erpDeptId,jdbcType=INTEGER},
        BROADCAST_DEPT_ID = #{broadcastDeptId,jdbcType=INTEGER},
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER}
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <select id="getAllBroadcastDeptRelate"
            resultType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptRErpDeptEntity">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_DEPT_R_ERP_DEPT
        where IS_DELETED = 0
    </select>

    <!-- 逻辑删除所有部门关系记录 -->
    <update id="deleteAllBroadcastDeptRelate" parameterType="java.lang.Integer">
        update T_BROADCAST_DEPT_R_ERP_DEPT
        set IS_DELETED = 1, 
            MOD_TIME = now(),
            UPDATER = #{userId,jdbcType=INTEGER}
        where IS_DELETED = 0
    </update>

    <!-- 批量插入部门关系记录 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into T_BROADCAST_DEPT_R_ERP_DEPT 
        (ERP_DEPT_ID, BROADCAST_DEPT_ID, IS_DELETED, ADD_TIME,  CREATOR )
        values
        <foreach collection="list" item="item" separator=",">
        (#{item.erpDeptId,jdbcType=INTEGER}, 
         #{item.broadcastDeptId,jdbcType=INTEGER},
        0,
         #{item.addTime,jdbcType=TIMESTAMP},

         #{item.creator,jdbcType=INTEGER} )
        </foreach>
    </insert>
    

    <!-- 根据部门ID获取获取该部门下所有的小组信息 -->
    <select id="selectAllByDeptId" resultType="com.vedeng.erp.common.broadcast.bo.BroadcastDeptRErpDeptBo">
        select 
			GROUP_CONCAT(DISTINCT orgId ORDER BY orgId SEPARATOR ',') AS orgIds,
		    	teamId,
    			teamName,
    			deptId,
                deptName
		 from ( SELECT 
			    r.ERP_DEPT_ID orgId,
			    team.ID AS  teamId,
			    team.DEPT_NAME AS teamName,
			    department.ID AS deptId,
			    department.DEPT_NAME AS deptName
			FROM 
			    T_BROADCAST_DEPT_R_ERP_DEPT r
			JOIN 
			    T_BROADCAST_DEPT team ON r.BROADCAST_DEPT_ID = team.ID AND team.PARENT_ID != 0
			JOIN 
			    T_BROADCAST_DEPT department ON team.PARENT_ID = department.ID
			WHERE 
			    r.IS_DELETED = 0 
			    <if test="deptId != null">
			    	AND department.ID=#{deptId,jdbcType=INTEGER}
			    </if>
			    AND team.IS_DELETED = 0
			    AND department.IS_DELETED = 0
			    ) tab
		    	group by teamId
		    
    </select>
    
    <!-- 根据部门ID获取获取该部门下所有的小组信息 -->
    <select id="selectDeptByDeptId" resultType="com.vedeng.erp.common.broadcast.bo.BroadcastDeptRErpDeptBo">
        select 
			GROUP_CONCAT(DISTINCT orgId ORDER BY orgId SEPARATOR ',') AS orgIds,
		    	deptId,
		        deptName
		 from ( SELECT 
			    r.ERP_DEPT_ID orgId,
			    team.ID AS  teamId,
			    team.DEPT_NAME AS teamName,
			    department.ID AS deptId,
			    department.DEPT_NAME AS deptName
			FROM 
			    T_BROADCAST_DEPT_R_ERP_DEPT r
			JOIN 
			    T_BROADCAST_DEPT team ON r.BROADCAST_DEPT_ID = team.ID AND team.PARENT_ID != 0
			JOIN 
			    T_BROADCAST_DEPT department ON team.PARENT_ID = department.ID
			WHERE 
			    r.IS_DELETED = 0 
			    <if test="deptId != null">
			    	AND department.ID=#{deptId,jdbcType=INTEGER}
			    </if>
			    AND team.IS_DELETED = 0
			    AND department.IS_DELETED = 0
			    ) tab
		    	group by deptId
    </select>
    
    <!-- 根据erp组织ID，获取在部门配置中所属小组和部门 -->
    <select id="selectByErpOrgId" resultType="com.vedeng.erp.common.broadcast.param.DeptInfo">
	    SELECT 
			r.ERP_DEPT_ID orgId,
			team.ID AS  teamId,
			team.DEPT_NAME AS teamName,
			department.ID AS deptId,
			department.DEPT_NAME AS deptName
		FROM 
			T_BROADCAST_DEPT_R_ERP_DEPT r
		JOIN 
			T_BROADCAST_DEPT team ON r.BROADCAST_DEPT_ID = team.ID AND team.PARENT_ID != 0
		JOIN 
			T_BROADCAST_DEPT department ON team.PARENT_ID = department.ID
		WHERE 
			r.IS_DELETED = 0 
			AND team.IS_DELETED = 0
			AND department.IS_DELETED = 0
			AND r.ERP_DEPT_ID = #{orgId,jdbcType=INTEGER}
    </select>
    
    <!-- 根据部门ID获取获取该部门下所有的小组信息 -->
    <select id="selectByErpDeptIdList" resultType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptRErpDeptEntity">
        SELECT 
		    *
		FROM
		    T_BROADCAST_DEPT_R_ERP_DEPT
		WHERE
			IS_DELETED = 0 
		    and ERP_DEPT_ID IN 
			<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
				#{orgId,jdbcType=INTEGER}
			</foreach>
		    
    </select>
    
    

</mapper>
