package com.vedeng.logistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.newtask.LogisticsInfoTask;
import com.rabbitmq.MsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.service.WebAccountService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.base.api.dto.kuaidi.KuaiDiReqDTO;
import com.vedeng.base.api.dto.kuaidi.LogisticsDTO;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.*;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.logisticmd.zto.ZTOExpressAdapter;
import com.vedeng.common.model.ReqVo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.*;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum;
import com.vedeng.erp.saleorder.api.OrderInfoSyncService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import com.vedeng.infrastructure.logistics.api.KuaiDiApi;
import com.vedeng.logistics.dao.*;
import com.vedeng.logistics.model.dto.LogisticsInfoDto;
import com.vedeng.logistics.model.vo.BatchExpressVo;
import com.vedeng.logistics.model.vo.SaleOrderConfirmBatchExpressVo;
import com.vedeng.logistics.service.*;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.RBuyorderSaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.RBuyorderSaleorder;
import com.vedeng.order.model.vo.ExpressOnlineReceiptVo;
import com.vedeng.order.service.OrderAccountPeriodService;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.logistics.eums.ExpressSupportedEnum;
import com.vedeng.logistics.model.*;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.passport.api.wechat.dto.req.ReqTemplateVariable;
import com.vedeng.passport.api.wechat.dto.template.TemplateVar;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.dao.PeriodUseNodeRecordMapper;
import com.vedeng.trader.model.WebAccount;
import com.vedeng.trader.model.po.PeriodUseNodeRecordPo;
import com.vedeng.wechat.JcWeChatEnum;
import com.vedeng.wechat.model.JcWeChatModel;
import com.vedeng.wechat.service.impl.JcWeChatArrServiceImpl;
import com.wms.constant.OutBoundBatchConstant;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 修改的时候注意一下定时任务：LogisticsInfoTask
 *
 * <AUTHOR>
 */
@Service("expressService")
public class ExpressServiceImpl extends BaseServiceimpl implements ExpressService {


    private static final Integer BUSINESS_TYPE_SALE_ORDER = 496;

    private static final Integer BUSINESS_TYPE_INVOICE = 497;

    private static final Integer BUSINESS_TYPE_BUY_ORDER = 515;

    @Resource
    LogisticsInfoTask logisticsInfoTask;

    @Autowired
    VgoodsService vGoodsService;

    @Value("${OrderClosingNotice}")
    protected String OrderClosingNotice; //订单关闭通知

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    @Qualifier("confirmationBatchesRelationMapper")
    private ConfirmationBatchesRelationMapper confirmationBatchesRelationMapper;

    @Autowired
    @Qualifier("userService")
    private UserService userService;//


    @Autowired
    @Qualifier("orgService")
    private OrgService orgService;

    @Autowired
    @Qualifier("confirmationFormRecodeMapper")
    private ConfirmationFormRecodeMapper confirmationFormRecodeMapper;


    @Value("${vx_service}")
    protected String vxService;

    @Value("${mjx_page}")
    protected String mjxPage;

    @Autowired
    @Qualifier("userMapper")
    private UserMapper userMapper;

    @Autowired
    @Qualifier("vedengSoapService")
    private VedengSoapService vedengSoapService;

    @Lazy
    @Autowired
    @Qualifier("saleorderService")
    private SaleorderService saleorderService;

    @Autowired
    protected WebAccountService webAccountService;

    @Autowired
    @Qualifier("logisticsService")
    protected LogisticsService logisticsService;

    @Autowired
    @Qualifier("saleorderMapper")
    private SaleorderMapper saleorderMapper;

    @Resource
    private ExpressMapper expressMapper;


    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    private OrderInfoSyncService orderInfoSyncService;

    @Resource
    private LogisticsMapper logisticsMapper;

    @Autowired
    @Qualifier("outboundBatchesRecodeMapper")
    private OutboundBatchesRecodeMapper outboundBatchesRecodeMapper;

    /**
     * ERP 发送医械购微信模板消息开关 1 默认开启 0 不开启
     */
    @Value("${erp_send_yxg_wx_temp_msg_flag}")
    protected Integer sendYxgWxTempMsgFlag;

    @Autowired
    private HcSaleorderService hcSaleorderService;


    @Autowired
    @Qualifier("msgProducer")
    MsgProducer msgProducer;

    @Autowired
    private WarehouseOutService warehouseOutService;

    @Autowired
    private JcWeChatArrServiceImpl jcWeChatArrService;


    @Resource
    private PeriodUseNodeRecordMapper periodUseNodeRecordMapper;
    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private ExpressDetailMapper expressDetailMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private ExpressOprateLogMapper expressOprateLogMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private RBuyorderSaleorderMapper rBuyorderSaleorderMapper;

    @Autowired
    private KuaiDiApi kuaiDiApi;

    @Value("${baseserver.kuaidi.erp.authorization}")
    String kuaidiAuthorization;

    @Autowired
    private WarehouseGoodsOutInItemMapper warehouseGoodsOutInItemMapper;

    /**
     * 记录日志
     */

    public static Logger LOG = LoggerFactory.getLogger(ExpressServiceImpl.class);

    @Override
    public List<Express> getExpressInfo(Saleorder saleorder) {
        List<Express> list = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<Express>>> TypeRef = new TypeReference<ResultInfo<List<Express>>>() {
        };
        String url = httpUrl + "logistics/express/getexpresslist.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
            list = (List<Express>) result.getData();
        } catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
        }
        return list;
    }

    /**
     * 获取快递信息
     */
    @Override
    public List<Express> getExpressList(Express express) throws Exception {
        // TODO Auto-generated method stub
        // 调用接口补充快递单信息
        String url = httpUrl + "logistics/express/getexpressinfo.htm";
        logger.info("printShOutOrder->express:{}", express);
        // 定义反序列化 数据格式
        List<Express> expressList = null;
        final TypeReference<ResultInfo<List<Express>>> TypeRef = new TypeReference<ResultInfo<List<Express>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, express, clientId, clientKey, TypeRef);
            if (result.getCode() == 0) {
                expressList = (List<Express>) result.getData();
                // 快递操作人人员查询
                List<Integer> userIds = new ArrayList<>();
                if (null != expressList) {
                    for (Express e : expressList) {
                        userIds.add(e.getCreator());
                    }
                }
                if (userIds.size() > 0) {
                    List<User> userList = userMapper.getUserByUserIds(userIds);
                    // 信息补充
                    if (null != expressList) {
                        for (Express e : expressList) {
                            for (User u : userList) {
                                if (e.getCreator().equals(u.getUserId())) {
                                    e.setUpdaterUsername(u.getUsername());
                                }
                            }
                        }
                    }
                }
            }
            return expressList;
        } catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    /*新商品流*/
    @Override
    public List<Express> getExpressListNew(Express express) {
        // TODO Auto-generated method stub
        // 调用接口补充快递单信息
        String url = httpUrl + "logistics/express/getexpressinfo.htm";
        logger.info("printShOutOrder->express:{}",express);
        // 定义反序列化 数据格式
        List<Express> expressList = null;
        final TypeReference<ResultInfo<List<Express>>> TypeRef = new TypeReference<ResultInfo<List<Express>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, express, clientId, clientKey, TypeRef);
            if (result.getCode() == 0) {
                expressList = (List<Express>) result.getData();
                // 快递操作人人员查询
                List<Integer> userIds = new ArrayList<>();
                if (null != expressList) {
                    for (Express e : expressList) {
                        userIds.add(e.getCreator());
                    }
                }
                if (userIds.size() > 0) {
                    List<User> userList = userMapper.getUserByUserIds(userIds);
                    // 信息补充
                    if (null != expressList) {
                        for (Express e : expressList) {
                            for (User u : userList) {
                                if (e.getCreator().equals(u.getUserId())) {
                                    e.setUpdaterUsername(u.getUsername());
                                }
                            }
                        }
                    }
                }
            }
            return expressList;
        } catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    public List<Express> getExpressListNewConfirm(Express express) {
        try {
            List<Express> expressList = getexpressinfoNewConfirm(express);
            // 快递操作人人员查询
            List<Integer> userIds = new ArrayList<>();
            if (null != expressList) {
                for (Express e : expressList) {
                    userIds.add(e.getCreator());
                }
            }
            if (userIds.size() > 0) {
                List<User> userList = userMapper.getUserByUserIds(userIds);
                // 信息补充
                if (null != expressList) {
                    for (Express e : expressList) {
                        for (User u : userList) {
                            if (e.getCreator().equals(u.getUserId())) {
                                e.setUpdaterUsername(u.getUsername());
                            }
                        }
                    }
                }
            }
//            }
            return expressList;
        } catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    /**
     * 保存快递信息
     */
    @Override
    public ResultInfo<?> saveExpress(Express express) {
        //生成直发批次号
        if (StringUtils.isEmpty(express.getBatchNo()) && SysOptionConstant.ID_515.equals(express.getBusinessType())) {
            generateDirectBatchNO(express);
        }
        long saveExpressStartTime = System.currentTimeMillis();
        //----------复制db保存快递信息
        logger.info("db包裹保存快递信息start logisticsNo:{},saveExpressStartTime:{}", express.getLogisticsNo(), saveExpressStartTime);
        // 如果插入成功返回成功
        ResultInfo result = new ResultInfo();
        // 先插入Express
        int e = 0;// 新增express返回值
        int ed = 0;// 新增expressDetail返回值
        if (express != null && express.getExpressId() != null && !SysOptionConstant.ID_515.equals(express.getBusinessType())) {
            logger.warn("删除快递saveExpressInfo" + JSON.toJSONString(express));
            expressMapper.deleteByPrimaryKey(express.getExpressId());
            ExpressDetail expressDetail = new ExpressDetail();
            expressDetail.setExpressId(express.getExpressId());
            expressDetailMapper.deleteSelective(expressDetail);
        }
        Boolean updateFlag = Boolean.FALSE;
        if (express != null && express.getExpressId() != null && !ErpConst.ZERO.equals(express.getExpressId()) && SysOptionConstant.ID_515.equals(express.getBusinessType())) {
            e = expressMapper.updateByPrimaryKeySelective(express);
            updateFlag = Boolean.TRUE;
        } else {
            e = expressMapper.insertSelective(express);
        }
        result.setData(express);
        if (e == 1) {
            // 批量保存快递详情
            List<ExpressDetail> eList = express.getExpressDetail();
            for (ExpressDetail expressDetail : eList) {
                expressDetail.setExpressId(express.getExpressId());
            }
            if (SysOptionConstant.ID_515.equals(express.getBusinessType()) && CollectionUtils.isNotEmpty(express.getExpressDetail()) && updateFlag) {
                for (ExpressDetail expressDetail : eList) {
                    expressDetailMapper.updateByPrimaryKeySelectiveByExpressId(expressDetail);
                }
                result.setCode(0);
                result.setMessage("保存成功");
                result.setParam(express);
                logger.info("保存包裹结果日志信息 result:{}", JSON.toJSONString(result));
                return result;
            } else {
                ed = expressDetailMapper.insertSelectiveBatch(eList);
            }
            for (ExpressDetail epd : express.getExpressDetail()) {
            /*if(null == express.getExpressDetail() || express.getExpressDetail().size()==0) {
                continue;
            }*/
                /************************** 快递单绑定出库记录开始 **********************************/
                if (express.getBusinessType() == 496) {
                    // 查询快递详情未绑定的出库记录
                    List<WarehouseGoodsOperateLog> bdList = warehouseGoodsOperateLogMapper.getbdListBySaleGoodsId(epd);
                    if (bdList != null && bdList.size() > 0) {
                        List<ExpressOprateLog> elList = new ArrayList<>();
                        for (int i = 0; i < express.getExpressDetail().size(); i++) {
                            if (i >= bdList.size()) {
                                continue;
                            }
                            bdList.get(i).setIsExpress(1);
                            ExpressOprateLog el = new ExpressOprateLog();
                            el.setExpressDetailId(epd.getExpressDetailId());
                            el.setWarehouseGoodsOperateLogId(bdList.get(i).getWarehouseGoodsOperateLogId());
                            elList.add(el);
                        }
                        if (elList.size() > 0) {
                            // 批量保存快递单与出入库关系
                            int i = expressOprateLogMapper.insertSelectiveBatch(elList);
                            if (i > 0) {
                                // 批量更新出库记录的绑定
                                int j = warehouseGoodsOperateLogMapper.batchUpdateWgolIsExpress(bdList);
                            }
                        }
                    }
                }
                /************************** 快递单绑定出库记录结束 **********************************/
            }
            // 销售发票 -- 发票寄送
            if (497 == express.getBusinessType()) {
                result.setCode(0);
                result.setMessage("插入成功");
                result.setData(express);
                logger.info("保存包裹发票寄送返回信息 result:{}", JSON.toJSONString(result));
                return result;
            }
        }
        long saveExpressEndTime = System.currentTimeMillis();
        logger.info("db包裹保存包裹用时信息start logisticsNo:{},saveExpressInfoUseTime:{}", express.getLogisticsNo(), saveExpressEndTime - saveExpressStartTime);
        List<String> virtureSku = Arrays.asList("V127063", "V251526", "V256675", "V253620", "V251462");
        if (ed > 0) {
            if (express.getBusinessType() == 496 && !"虚拟快递单".equals(express.getLogisticsComments())) {
                int tsGoogFlag = -1;
                Saleorder s = new Saleorder();
                s.setSaleorderId(express.getSaleorderId());
                s.setIsExpreeInfo("1");
                List<SaleorderGoods> list = saleorderGoodsMapper.getSaleorderGoodsById(s);
                List<Integer> relatedIdList = express.getExpressDetail().stream().map(ExpressDetail::getRelatedId).collect(Collectors.toList());
                for (SaleorderGoods sd : list) {
                    //第n处快递签收过滤虚拟商品(前面在DB和采购新增快递）
                    if (!Constants.ONE.equals(sd.getDeliveryDirect()) && (sd.getIsVirtureSku() == null || sd.getIsVirtureSku() == 0) && !virtureSku.contains(sd.getSku())) {
                        if (!relatedIdList.contains(sd.getSaleorderGoodsId())) {
                            logger.info("saveExpressInfo快递单,该快递不包含此销售商品,不进行更新,relatedIdList:{},saleorderGoodsId:{}", relatedIdList, sd.getSaleorderGoodsId());
                            continue;
                        }
                        express.setOrderGoodsId(sd.getSaleorderGoodsId());
                        Express ex = expressMapper.getSEGoodsNum(express);
                        SaleorderGoods sds = new SaleorderGoods();
                        sds.setSaleorderGoodsId(sd.getSaleorderGoodsId());
                        if (ex.getFnum() == 0) {
                            // 未签收
                            sds.setArrivalStatus(0);
                        } else if (ex.getAllnum() > ex.getFnum()) {
                            // 部分签收
                            tsGoogFlag = 1;
                            sds.setArrivalStatus(1);
                            sds.setArrivalTime(DateUtil.sysTimeMillis());
                        } else {
                            // 全部签收
                            tsGoogFlag = 1;
                            sds.setArrivalStatus(2);
                            sds.setArrivalTime(DateUtil.sysTimeMillis());
                        }
                        logger.info("采购快递签收：订单商品：订单ID={}  订单商品ID={} 到货状态={}", s.getSaleorderId(), sd.getSaleorderGoodsId(), sds.getArrivalStatus());
                        int i = saleorderGoodsMapper.updateByPrimaryKeySelective(sds);
                    }
                }
                /********* 修改订单状态 ********/
                // 有一个普通商品收货，所有的特殊商品都收货
                if (tsGoogFlag == 1) {
                    int j = saleorderGoodsMapper.updateAllTsGoodsInfo(s.getSaleorderId());
                }
                List<SaleorderGoods> sdList = saleorderGoodsMapper.getSaleorderGoodsById(s);
                if (sdList != null) {
                    Saleorder saleorder = new Saleorder();
                    saleorder.setSaleorderId(express.getSaleorderId());
                    Integer arrivalStatus2 = 0;
                    Integer arrivalStatus0 = 0;
                    Integer arrivalStatus = 0;
                    for (SaleorderGoods saleorderGoods : sdList) {
                        // 实际销售商品数量大于零 并且 商品状态非已收货
                        if (saleorderGoods.getNum() - saleorderGoods.getAfterReturnNum() > 0 && saleorderGoods.getArrivalStatus() != 2) {
                            arrivalStatus2 = -1;
                            break;
                        }
                    }
                    logger.info("采购快递签收：订单商品：订单ID={} 到货状态={} ", s.getSaleorderId(), arrivalStatus2);
                    if (Constants.ZERO.equals(arrivalStatus2)) {
                        arrivalStatus = 2;
                    } else {
                        for (SaleorderGoods saleorderGoods : sdList) {
                            // 实际销售商品数量大于零  并且  商品状态非待收货
                            if (saleorderGoods.getNum() - saleorderGoods.getAfterReturnNum() > 0 && saleorderGoods.getArrivalStatus() != 0) {
                                arrivalStatus0 = -1;
                                break;
                            }
                        }
                        if (arrivalStatus0 == 0) {
                            arrivalStatus = 0;
                        } else {
                            arrivalStatus = 1;
                        }
                    }
                    logger.info("采购快递签收：{} {} ", s.getSaleorderId(), arrivalStatus2);
                    saleorder.setArrivalStatus(arrivalStatus);
                    saleorder.setArrivalTime(DateUtil.sysTimeMillis());
                    saleorder.setUpdateDataTime(new Date());
                    logger.info("更新销售订单收货状态入参：{}",JSON.toJSONString(saleorder));
                    int k = saleorderMapper.updateByPrimaryKeySelective(saleorder);
                }
            } else if (express.getBusinessType() == 515) {
                Buyorder b = new Buyorder();
                b.setBuyorderId(express.getBuyorderId());

                int k = 1;
                if (k > 0) {
                    Buyorder buy = buyorderMapper.selectByPrimaryKey(b.getBuyorderId());
                    if (buy != null && buy.getDeliveryDirect() == 1) {// 直发采购需修改销售订单的发货状态
                        List<RBuyorderSaleorder> listR = rBuyorderSaleorderMapper.getRBuyorderSaleorderListByParam(buy.getBuyorderId());
                        Buyorder bu = new Buyorder();
                        bu.setBuyorderId(buy.getBuyorderId());
                        bu.setDeliveryStatus(2);
                        bu.setDeliveryTime(DateUtil.sysTimeMillis());
                        boolean flag = false;
                        for (RBuyorderSaleorder rBuyorderSaleorder : listR) {
                            for (ExpressDetail epd : express.getExpressDetail()) {
                                if (rBuyorderSaleorder.getBuyorderGoodsId().equals(epd.getRelatedId())
                                        && rBuyorderSaleorder.getNum() > epd.getNum()) {
                                    flag = true;
                                    break;
                                }
                            }
                            if (flag) {
                                bu.setDeliveryStatus(1);
                                break;
                            }
                        }
                        buyorderMapper.updateByPrimaryKeySelective(bu);

                        // 修改对应的销售商品的发货状态
                        List<RBuyorderSaleorder> listRInfo = rBuyorderSaleorderMapper
                                .getRBuyorderSaleorderInfoList(buy.getBuyorderId());
                        List<SaleorderGoods> sdLists = saleorderGoodsMapper.getSaleorderGoodsList(listRInfo);
                        for (SaleorderGoods saleorderGoods : sdLists) {
                            //订单流升级更新销售单收发货信息
                            orderInfoSyncService.syncDeliveryAndArrivalDetailOfSaleOrder(saleorderGoods.getSaleorderId());
                        }
                    }
                }
            }
            Saleorder saleOrder = null;
            Express expressInfo = new Express();
            expressInfo.setExpressId(express.getExpressId());
            expressInfo = expressMapper.getExpressById(expressInfo);
            if (expressInfo.getBusinessType() == 496) {// 销售的订单,商品只会是普发
                saleOrder = saleorderMapper.getBaseSaleorderInfo(expressInfo.getSaleorderId());
            } else if (expressInfo.getBusinessType() == 515) {// 采购的订单，直发+普法
                SaleorderGoods SaleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(expressInfo.getRelatedId());
                if (SaleorderGoods.getDeliveryDirect() == 1) {// 该快递单下的商品是直发，则该快递单也是直发的
                    // 查询该快递单的销售单
                    saleOrder = saleorderMapper.getBaseSaleorderInfo(SaleorderGoods.getSaleorderId());
                }
            }
            long saveOrderEndTime = System.currentTimeMillis();
            logger.info("db包裹保存订单信息end logisticsNo:{},saveOrderUseTime:{}", express.getLogisticsNo(), saveOrderEndTime - saveExpressEndTime);
            // 如果是部分收货或者全部收货
            if (saleOrder != null && (saleOrder.getArrivalStatus() == 1 || saleOrder.getArrivalStatus() == 2)) {
                // 如果是耗材商城的订单，推送至耗材商城
                if (saleOrder.getOrderType() == 5) {
                    Saleorder saleorder = saleOrder;
                    Express expressIn = expressInfo;

                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("type", 1);// 收货推送标志
                    map.put("saleOrder", saleorder);
                    map.put("method", "save");
                    // 查询该快递单下的商品列表
                    List<SaleorderGoods> goodsList = saleorderGoodsMapper
                            .getSaleorderGoodsListByExId(expressIn.getExpressId());
                    map.put("goodsList", goodsList);
                    hcSaleorderService.putExpressToHC(map);
                }
            }
            logger.info("db包裹保存所有信息end logisticsNo:{},pushHcUseTime:{}",
                    express.getLogisticsNo(), System.currentTimeMillis() - saveOrderEndTime);
            result.setCode(0);
            result.setMessage("保存成功");
            result.setData(express);
            result.setParam(express);
        } else {
            result.setCode(-1);
            result.setMessage("保存失败");
        }
        logger.info("保存包裹结果日志信息 result:{}", JSON.toJSONString(result));
        return result;
        //----------复制db保存快递信息
    }

    @Override
    public Map<String, Object> getExpressListPage(Express express, Page page) {
        List<Express> list = null;
        Map<String, Object> map = new HashMap<>();
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<Express>>> TypeRef = new TypeReference<ResultInfo<List<Express>>>() {
        };
        String url = httpUrl + "logistics/express/getexpresslistpage.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, express, clientId, clientKey, TypeRef,
                    page);
            list = (List<Express>) result.getData();
            if (null != list && list.size() > 0) {
                List<Integer> userIds = new ArrayList<>();
                for (Express ex : list) {
                    if (ex.getCreator() > 0) {
                        userIds.add(ex.getCreator());
                    }
                    if (StringUtils.endsWith(ex.getXsNo(), "、")) {
                        ex.setXsNo(StringUtils.substring(ex.getXsNo(), 0, ex.getXsNo().length() - 1));
                    }

                    // 是否超时
                    String addTime = DateUtil.convertString(ex.getAddTime(), "yyyy-MM-dd HH:mm:ss");
                    String now = DateUtil.convertString(DateUtil.sysTimeMillis(), "yyyy-MM-dd HH:mm:ss");
                    long days = DateUtil.getDistanceTimeDays(addTime, now);

                    if ((int) days > 5) {
                        ex.setIsovertime(1);
                    }
                }

                if (userIds.size() > 0) {
                    List<User> userList = userMapper.getUserByUserIds(userIds);
                    for (Express ex : list) {
                        for (User u : userList) {
                            if (ex.getCreator().equals(u.getUserId())) {
                                ex.setCreatName(u.getUsername());
                            }
                        }
                    }
                }
            }

            map.put("list", list);
            map.put("page", result.getPage());
        } catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
        }
        return map;
    }

    @Override
    public ResultInfo<?> editBatchExpress(List<Express> epList) {
        String url = httpUrl + "logistics/express/editbatchexpress.htm";
        final TypeReference<ResultInfo<List<SaleorderVo>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderVo>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, epList, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                // 签收消息推送
                List<SaleorderVo> saleorderList = (List<SaleorderVo>) result.getData();
                // 批量签收推送
                if (CollectionUtils.isNotEmpty(saleorderList) && null != saleorderList.get(0)) {
                    vedengSoapService.messageBtachSignSyncWeb(saleorderList);

                    //更新订单updateData时间
                    for (SaleorderVo saleorderVo : saleorderList) {
                        orderCommonService.updateSaleOrderDataUpdateTime(saleorderVo.getSaleorderId(), null, OrderDataUpdateConstant.SALE_ORDER_EXPRESS_END);
                    }
                }
                return result;
            } else {
                return new ResultInfo();
            }
        } catch (IOException e) {
            LOG.error("call db editBatchExpress error:", e);
            return new ResultInfo();
        }
    }

    @Override
    public ResultInfo<?> delExpress(Express express) {
        String url = httpUrl + "logistics/express/delExpress.htm";
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, express, clientId, clientKey, TypeRef);
            // 接口返回条码生成的记录
            if (result.getCode() == 0) {
                return new ResultInfo(0, "操作成功");
            } else {
                return new ResultInfo();
            }
        } catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return new ResultInfo();
        }
    }



    @Override
    public Express getCntExpress(Express express) {
        // 接口调用
        String url = httpUrl + "logistics/express/getcntexpress.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Express>> TypeRef2 = new TypeReference<ResultInfo<Express>>() {
        };
        try {
            ResultInfo<Express> result2 = (ResultInfo<Express>) HttpClientUtils.post(url, express, clientId, clientKey,
                    TypeRef2);
            if (null == result2) {
                return null;
            }
            Express res = (Express) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public Express getExpressInfoByNo(Express ex) {
        // 接口调用
        String url = httpUrl + "logistics/express/getexpressinfobyno.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Express>> TypeRef2 = new TypeReference<ResultInfo<Express>>() {
        };
        try {
            ResultInfo<Express> result2 = (ResultInfo<Express>) HttpClientUtils.post(url, ex, clientId, clientKey,
                    TypeRef2);
            if (null == result2) {
                return null;
            }
            Express res = (Express) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

	/**
	 *
	 * 仅限销售订单，采购订单不再处理
	 * @param exList1
	 * @return
	 */
	@Override
	public ResultInfo<?> editExpres(List<Express> exList1) {
		String url = httpUrl + "warehouseout/editexpress.htm";
		final TypeReference<ResultInfo<List<SaleorderVo>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderVo>>>() {
		};
		exList1.forEach(item->{
		 	try {
				List<Express> exListTemp = Lists.newArrayList();
				exListTemp.add(item);
				Express expressByLogisticsNo = expressMapper.getExpressByLogisticsNo(item.getLogisticsNo());
				if (Objects.isNull(expressByLogisticsNo)){
					 logger.info("editpress 采购快递 {}", item.getLogisticsNo() );
					return;
				}
				ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, exListTemp, clientId, clientKey, TypeRef);
				logger.info("editpress {} {} ", item.getLogisticsNo() , result);
				// 接口返回条码生成的记录
				if (result!=null&&result.getCode() == 0) {
					sendBdSaleorderStatusByExpress(exListTemp);
					logger.info("editpress success:" + item.getLogisticsNo());
				} else {
					logger.error("editpress error:" +item.getLogisticsNo());
				}
			}catch (Exception e){
				logger.error("editpress error "+item.getLogisticsNo(),e);
			}
		});
		return new ResultInfo(0, "修改成功", null);
	}

    private void sendBdSaleorderStatusByExpress(List<Express> exList) {
        try {
            for (Express express : exList) {
                logger.info("开始处理：{}", com.alibaba.fastjson.JSONObject.toJSONString(express));
                List<ExpressDetail> dList = expressMapper.getExpressDetailsList(express);
                if (CollectionUtils.isEmpty(dList)) {
                    logger.info("dList 为空,入参：{}", com.alibaba.fastjson.JSONObject.toJSONString(express));
                    continue;
                }
                Map<Integer, ExpressDetail> expressDetailMap = dList.stream().collect(Collectors.toMap(ExpressDetail::getRelatedId, Function.identity(), (key1, key2) -> key1));
                if (MapUtils.isEmpty(expressDetailMap)){
                    logger.info("expressDetailMap 为空,dList:{}", com.alibaba.fastjson.JSONObject.toJSONString(expressDetailMap));
                    continue;
                }
                logger.info("expressDetailMap个数为：{}",expressDetailMap.size());
                Iterator<Map.Entry<Integer, ExpressDetail>> iterator = expressDetailMap.entrySet().iterator();
                while (iterator.hasNext()){
                    Map.Entry<Integer, ExpressDetail> next = iterator.next();
                    ExpressDetail expressDetail = next.getValue();
                    if (null != expressDetail && expressDetail.getBusinessType() == 496) {
                        SaleorderGoods SaleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(expressDetail.getRelatedId());
                        Saleorder saleorder = new Saleorder();
                        saleorder.setSaleorderId(SaleorderGoods.getSaleorderId());
                        logger.info("syncSaleorderStatus2Mjx {} updateBDLogisticsStatus", SaleorderGoods.getSaleorderId());
                        saleorderService.updateBDLogisticsStatus(saleorder);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("sendBdSaleorderStatusByExpress error", e);
        }

    }

    @Override
    public ResultInfo<?> editLogisticsDetail(List<LogisticsDetail> ldList) {
        String url = httpUrl + "warehouseout/editlogisticsdetail.htm";
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, ldList, clientId, clientKey, TypeRef);
            // 接口返回条码生成的记录
            if (result.getCode() == 0) {
                return new ResultInfo(0, "修改成功");
            } else {
                return new ResultInfo();
            }
        } catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return new ResultInfo();
        }
    }

    @Override
    public SaleorderGoods getSaleorderGoodsInfoById(Integer saleorderGoodsId) {
        SaleorderGoods saleorderGoods = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<SaleorderGoods>> TypeRef = new TypeReference<ResultInfo<SaleorderGoods>>() {
        };
        String url = httpUrl + "order/saleorder/getsaleordergoodsinfobyid.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderGoodsId, clientId, clientKey,
                    TypeRef);
            saleorderGoods = (SaleorderGoods) result.getData();
        } catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
        }
        return saleorderGoods;
    }


    @Override
    public ResultInfo<?> batchUpdateExpress(List<Express> epList) {
        String url = httpUrl + "logistics/express/editbatchexpress.htm";
        final TypeReference<ResultInfo<List<SaleorderVo>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderVo>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, epList, clientId, clientKey, TypeRef);
            // 接口响应信息
            if (null == result || result.getCode() != 0) {
                return new ResultInfo();
            } else {
                return new ResultInfo(0, "修改成功", result.getData());
            }
        } catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return new ResultInfo();
        }
    }

    @Override
    public Express updateExpressInfoById(Express express) {
        // 接口调用
        String url = httpUrl + "logistics/express/updateexpressInfoById.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Express>> TypeRef2 = new TypeReference<ResultInfo<Express>>() {
        };
        try {
            ResultInfo<Express> result2 = (ResultInfo<Express>) HttpClientUtils.post(url, express, clientId, clientKey,
                    TypeRef2);
            if (null == result2) {
                return null;
            }
            Express res = (Express) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public Express getExpressInfoByInvoiceNo(Integer invoiceId) {
        // 接口调用
        String url = httpUrl + "logistics/express/getexpressinfobyinvoiceno.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Express>> TypeRef2 = new TypeReference<ResultInfo<Express>>() {
        };
        try {
            ResultInfo<Express> result2 = (ResultInfo<Express>) HttpClientUtils.post(url, invoiceId, clientId,
                    clientKey, TypeRef2);
            if (null == result2) {
                return null;
            }
            Express res = (Express) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public LogisticsDetail getLogisticsDetailInfo(Express express) {
        if (StringUtils.isBlank(express.getLogisticsNo())) {
            logger.info("查询物流信息失败，查询条件不足");
            return null;
        }
        //查询快递信息
        logger.info("查询物流单号:{}的快递信息", express.getLogisticsNo());
        // 物流单号
        String LNO = express.getLogisticsNo();
        Express expressResult = expressMapper.getExpressByLogisticsNoNoLimit(LNO);
        if (expressResult == null) {
            logger.info("未查询到物流单号:{}对应的快递信息", LNO);
            return null;
        }
        String phone = this.getPhoneByBusinessType(expressResult.getExpressId(), expressResult.getLogisticsNo());
        Logistics logisticsById = logisticsService.getLogisticsById(expressResult.getLogisticsId());
        ResultInfo resultInfo = logisticsInfoTask.queryInfo(logisticsService.getLogisticsCode(logisticsById.getName()), LNO, phone);
        LogisticsDetail logisticsDetail = new LogisticsDetail();
        if (ObjectUtil.isNotNull(resultInfo)) {
            logisticsDetail.setContent(resultInfo.getData().toString());
        }

        // 补充一些信息
        LogisticsInfoDto logisticsInfoDto = new LogisticsInfoDto();
        logisticsInfoDto.setLogisticsNo(LNO);
        logisticsInfoDto.setLogisticsName(expressResult.getLogisticsCompanyName());
        String deliveryTime = cn.hutool.core.date.DateUtil.formatDate(cn.hutool.core.date.DateUtil.date(expressResult.getDeliveryTime()));
        String arrivalTime = cn.hutool.core.date.DateUtil.formatDate(cn.hutool.core.date.DateUtil.date(expressResult.getArrivalTime()));
        logisticsInfoDto.setSendTime(deliveryTime);
        logisticsInfoDto.setSignTime(arrivalTime);
        logisticsInfoDto.setTraderCustomerName(express.getTraderCustomerName());
        logisticsInfoDto.setOrderNo(express.getXsNo());
        logisticsDetail.setLogisticsInfoDto(logisticsInfoDto);

        return logisticsDetail;
    }

    @Value("${kuaidi.cutomer}")
    String customer;
    @Value("${kuaidi.key}")
    String key;

    public String queryInfo(String com, String logisticsNo) {
        String param = "{\'com\':\'" + com + "\',\'num\':\'" + logisticsNo + "\'}";

        String sign = MD5.encode(param + key + customer);
        HashMap<String, String> params = new HashMap<String, String>();
        params.put("param", param);
        params.put("sign", sign);
        params.put("customer", customer);
        String resp = "";
        try {
            KuaiDiReqDTO kuaiDiReqDTO = new KuaiDiReqDTO();
            kuaiDiReqDTO.setAuthorization(kuaidiAuthorization);
            KuaiDiReqDTO.KuaiDiParam kuaiDiParam = new KuaiDiReqDTO.KuaiDiParam();
            kuaiDiParam.setCom(com);
            kuaiDiParam.setNum(logisticsNo);
            kuaiDiReqDTO.setParam(kuaiDiParam);
            RestfulResult<LogisticsDTO> waybillState = kuaiDiApi.getWaybillState(kuaiDiReqDTO);
            if (waybillState != null && waybillState.getData() != null) {
                resp = JSON.toJSONString(waybillState.getData());
            }
        } catch (Exception e) {
            logger.error("调用Base快递服务失败", e);
            try {
                resp = HttpRequestExpress.postData("http://poll.kuaidi100.com/poll/query.do", params, "utf-8");
            } catch (Exception fe) {
                logger.error("", fe);
            }
        }
        return resp;
    }

    @Override
    public List<Express> getExpressListBySaleorderNo(String saleorderNo) throws Exception {
        // 调用接口补充快递单信息
        String url = httpUrl + "logistics/express/getexpresslistbysaleorderno.htm";
        // 定义反序列化 数据格式
        List<Express> expressList = null;
        final TypeReference<ResultInfo<List<Express>>> TypeRef = new TypeReference<ResultInfo<List<Express>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderNo, clientId, clientKey, TypeRef);
            if (result.getCode() == 0) {
                expressList = (List<Express>) result.getData();
            }
            return expressList;
        } catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
    }


    @SuppressWarnings("unchecked")
    @Override
    public List<SaleorderGoods> getSaleorderGoodsListByexpressId(Integer expressId) {
        // TODO Auto-generated method stub
        // 调用接口补充快递单信息
        String url = restDbUrl + "rest/order/hc/v1/search/getSaleOrderGoods";
        final TypeReference<ResultInfo<List<SaleorderGoods>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoods>>>() {
        };
        Express ex = new Express();
        ex.setExpressId(expressId);
        ReqVo<Express> reqVo = new ReqVo<Express>();
        reqVo.setReq(ex);
        List<SaleorderGoods> goodsList = null;
        ResultInfo<?> result = (ResultInfo<?>) HttpRestClientUtil.post(url, TypeRef, reqVo);
        if (result.getCode() == 0) {
            goodsList = (List<SaleorderGoods>) result.getData();
        }
        return goodsList;
    }


    @Override
    @Deprecated
    public ResultInfo sendWxMessageForArrival(Integer saleorderId) {
        ResultInfo resultInfo = new ResultInfo();
        Saleorder saleOrderInfo = new Saleorder();

        try {
            //查询订单信息
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderId(saleorderId);
            saleOrderInfo = saleorderService.getBaseSaleorderInfo(saleorder);
            if (saleOrderInfo != null) {
                /*********************准备调用接口数据START************************/
                //获取销售订单联系人
                WebAccount webaccount = new WebAccount();
                //存在没有联系人的情况
                //if(saleOrderInfo.getTraderContactId() !=null &&saleOrderInfo.getTraderContactId()>0){
                webaccount.setTraderContactId(saleOrderInfo.getTraderContactId());
                //获取销售单联系人关联的注册用户信息
                //List<WebAccount> webAccountList= webAccountService.getWebAccountByTraderContactId(webaccount);
                //订单实际金额
                // 获取交易信息（订单实际金额，客户已付款金额）
                Map<String, BigDecimal> saleorderDataInfo = saleorderService.getSaleorderDataInfo(saleorderId);
                BigDecimal realAmount = saleorderDataInfo.get("realAmount");
                // 获取订单产品信息(与订单详情中获取相同)
                Saleorder sale = new Saleorder();
                sale.setSaleorderId(saleOrderInfo.getSaleorderId());
                sale.setTraderId(saleOrderInfo.getTraderId());
                sale.setCompanyId(saleOrderInfo.getCompanyId());
                List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleorderGoodsById(sale);
                //商品总数量（除去售后数量）
                Integer saleorderAllNum = 0;
                String saleorderFirstGoodsName = "";
                if (saleorderGoodsList != null && saleorderGoodsList.size() > 0) {
                    for (SaleorderGoods sg : saleorderGoodsList) {
                        // 运费
                        if (null == sg || "V127063".equals(sg.getSku()) || "V251526".equals(sg.getSku()) || "V252843".equals(sg.getSku()) || "V256675".equals(sg.getSku())) {
                            continue;
                        }
                        if (EmptyUtils.isBlank(saleorderFirstGoodsName)) {
                            saleorderFirstGoodsName = sg.getGoodsName();
                        }
                        //商品数-售后数
                        saleorderAllNum = saleorderAllNum + (sg.getNum() - sg.getAfterReturnNum());
                    }
                }

                Map data = new HashMap<>();//消息数据
                //订单编号
                data.put("totalAmount", realAmount);
                //快递单号对应的第一个商品名称
                data.put("saleorderFirstGoodsName", saleorderFirstGoodsName);
                //商品总数量（除去售后数量）
                data.put("saleorderAllNum", saleorderAllNum);
                //快递单商品总数量
                data.put("traderContactName", saleOrderInfo.getTakeTraderContactName());
                data.put("traderContactMobile", saleOrderInfo.getTakeTraderContactMobile());
                data.put("traderAddress", saleOrderInfo.getTakeTraderAddress());
                data.put("saleorderNo", saleOrderInfo.getSaleorderNo());
                data.put("takeTraderAddress", saleOrderInfo.getTakeTraderAddress());
                data.put("takeTraderArea", saleOrderInfo.getTakeTraderArea());
                data.put("paymentStatus", saleOrderInfo.getPaymentStatus());
                //订单生效时间
                data.put("validTime", DateUtil.convertString(saleOrderInfo.getValidTime(), DateUtil.TIME_FORMAT));
                data.put("saleOrderInfo", saleOrderInfo);
                data.put("mjx", mjxUrl);

                resultInfo.setData(data);

                /*********************准备调用接口数据END************************/

            }
            return resultInfo;
        } catch (Exception e) {
            LOG.error("订单签收发送微信模版消息失败 订单ID:" + saleorderId, e);
            return resultInfo;
        }
    }

    @Override
    public Map<String, String> sendForExpress(Saleorder saleOrderInfo, Express express) {

        LOG.info("sendForExpress | saleOrderInfo :{}, express : {}", saleOrderInfo, express);
        if (null == saleOrderInfo || null == express) {
            return null;
        }

        // 获取订单产品信息(与订单详情中获取相同)
        Saleorder sale = new Saleorder();
        sale.setSaleorderId(saleOrderInfo.getSaleorderId());
        sale.setTraderId(saleOrderInfo.getTraderId());
        sale.setCompanyId(saleOrderInfo.getCompanyId());
        sale.setReqType(1);
        List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleorderGoodsById(sale);

        // 物流信息
        Express expressInfo = new Express();
        expressInfo.setBusinessType(SysOptionConstant.ID_496);
        expressInfo.setCompanyId(saleOrderInfo.getCompanyId());
        expressInfo.setExpressId(express.getExpressId());
        List<Integer> relatedIds = new ArrayList<Integer>();
        //商品总数量（除去售后数量）
        Integer saleorderAllNum = 0;
        for (SaleorderGoods sg : saleorderGoodsList) {
            // 运费
            if (null == sg || "V127063".equals(sg.getSku()) || "V251526".equals(sg.getSku()) || "V252843".equals(sg.getSku()) || "V256675".equals(sg.getSku())) {
                continue;
            }
            relatedIds.add(sg.getSaleorderGoodsId());
            //商品数-售后数
            saleorderAllNum = saleorderAllNum + (sg.getNum() - sg.getAfterReturnNum());
        }
        //快递单号对应的第一个商品名称
        String expressFirstGoodsName = "";
        //快递单商品总数量
        Integer expressAllNum = 0;
        if (relatedIds != null && relatedIds.size() > 0) {
            //根据ID获取快递单信息
            Express expressList = this.getExpressInfoById(expressInfo);
            if (expressList != null && expressList.getExpressDetail() != null) {
                expressFirstGoodsName = expressList.getExpressDetail().get(0).getGoodName();
                for (ExpressDetail ed : expressList.getExpressDetail()) {
                    expressAllNum = expressAllNum + ed.getNum();
                }
            }

        }
        //快递信息（快递公司名称+快递单号）
        Logistics logistics = logisticsService.getLogisticsById(express.getLogisticsId());

        Map<String, String> returnData = new HashMap<>();
        //订单编号
        returnData.put("saleorderNo", saleOrderInfo.getSaleorderNo());
        //快递单号对应的第一个商品名称
        returnData.put("expressFirstGoodsName", expressFirstGoodsName);
        //商品总数量（除去售后数量）
        returnData.put("saleorderAllNum", saleorderAllNum + "");
        //快递单商品总数量
        returnData.put("expressAllNum", expressAllNum + "");
        //订单生效时间
        returnData.put("validTime", DateUtil.convertString(saleOrderInfo.getValidTime(), DateUtil.TIME_FORMAT));
        //客户信息（联系人名称+联系人手机号）
        returnData.put("customerInfo", saleOrderInfo.getTraderContactName() + " " + saleOrderInfo.getTraderContactMobile());
        //快递信息（快递公司名称+快递单号）
        returnData.put("logisticsName", logistics.getName());
        returnData.put("logisticsNo", express.getLogisticsNo());

        // 返回结果
        return returnData;
    }

    @Override
    public List<Express> getLendOutExpressList(Express express) {
        // 快递操作人人员查询
        List<Integer> userIds = new ArrayList<>();
        List<Express> expressList = expressMapper.getLendOutExpressInfo(express);
        if (null != expressList) {
            for (Express ep : expressList) {
                userIds.add(ep.getCreator());
                ep.setBusiness_Type(express.getBusinessType());
                if (ep.getContent() != null && !"".equals(ep.getContent())) {
                    String str = "";
                    JSONObject rd = JSONObject.fromObject(ep.getContent());
                    if (rd != null) {
                        String message = rd.getString("message");
                        if ("ok".equals(message)) {
                            JSONArray ja = rd.getJSONArray("data");
                            JSONObject jl = ja.getJSONObject(0);
                            str = jl.getString("time") + jl.getString("context");
                        } else {
                            str = message;
                        }
                        ep.setContentNew(str);
                    } else {
                        ep.setContentNew("暂无信息");
                    }

                } else {
                    ep.setContentNew("暂无信息");
                }
            }
        }
        if (userIds.size() > 0) {
            List<User> userList = userMapper.getUserByUserIds(userIds);
            // 信息补充
            if (null != expressList) {
                for (Express e : expressList) {
                    for (User u : userList) {
                        if (e.getCreator().equals(u.getUserId())) {
                            e.setUpdaterUsername(u.getUsername());
                        }
                    }
                }
            }
        }
        return expressList;
    }


    @Override
    public void sendOrderConfirmedClose(Saleorder saleorders, Map sTempMap) {
        ReqTemplateVariable reqTemp = new ReqTemplateVariable();
        if (null != saleorders.getCreateMobile()) {
            // 订单客户联系人
            reqTemp.setMobile(saleorders.getCreateMobile());
            //reqTemp.setMobile("17554243894");
        }

        // reqTemp.setMobile(phone);
        //reqTemp.setTemplateId(OrderClosingNotice);
        reqTemp.setTemplateId(OrderClosingNotice);
        reqTemp.setJumpUrl(mjxPage + "?orderNo=" + saleorders.getSaleorderNo());
        TemplateVar first = new TemplateVar();
        String firstStr = getConfigStringByDefault("尊敬的客户，您的订单已自动关闭", SysOptionConstant.THE_ORDER_CLOSED);
        LOG.info("获取数据配置 | firstStr：{} ", firstStr);
        first.setValue(firstStr + "\r\n");

        TemplateVar keyword1 = new TemplateVar();
        TemplateVar keyword2 = new TemplateVar();
        TemplateVar keyword3 = new TemplateVar();

        TemplateVar remark = new TemplateVar();
        String remarkStr = getConfigStringByDefault("感谢您对贝登的支持与信任，如有疑问请联系：4006-999-569", SysOptionConstant.WECHAT_TEMPLATE_BEDENG_REMARK);
        remark.setValue(remarkStr);

        if (null != sTempMap) {

            String saleorderAllNum = String.valueOf(sTempMap.get("saleorderAllNum"));
            // 商品名称
            keyword1.setValue((String) sTempMap.get("saleorderFirstGoodsName") + "等 " + saleorderAllNum + "个商品");
            // 订单编号
            keyword2.setValue(saleorders.getSaleorderNo());
            // 订单编号
            keyword3.setValue("超时未确认订单" + "\r\n");

            reqTemp.setFirst(first);
            reqTemp.setKeyword1(keyword1);
            reqTemp.setKeyword2(keyword2);
            reqTemp.setKeyword3(keyword3);
            reqTemp.setRemark(remark);
            // 发送 待用户确认消息推送
            sendTemplateMsg(vxService + "/wx/wxchat/send", reqTemp);
            //sendTemplateMsg("http://************:8280/wx/wxchat/send",reqTemp);
            LOG.info("订单关闭消息推送结束");
        }
    }

    /**
     * @description: VDERP-1325 分批开票 查询已收货数量..
     * @notes: 从dbcenter迁移过来.
     * @author: Tomcat.Hui.
     * @date: 2019/11/11 11:28.
     * @return: com.vedeng.logistics.model.Express.
     * @throws: .
     */
    @Override
    public List<ExpressDetail> getSEGoodsNum(List<Integer> saleorderIds) {
        String url = httpUrl + "logistics/express/getSEGoodsNum.htm";
        // 定义反序列化 数据格式
        List<ExpressDetail> expressList = null;
        final TypeReference<ResultInfo<List<ExpressDetail>>> TypeRef = new TypeReference<ResultInfo<List<ExpressDetail>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderIds, clientId, clientKey, TypeRef);
            if (result.getCode() == 0) {
                expressList = (List<ExpressDetail>) result.getData();
            }
            return expressList;
        } catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
    }


    @Override
    public Express getExpressInfoById(Express ex) {
        // 接口调用
        String url = httpUrl + "logistics/express/getexpressinfobyid.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Map<String, Object>>> TypeRef = new TypeReference<ResultInfo<Map<String, Object>>>() {
        };
        try {
            ResultInfo<Express> result2 = (ResultInfo<Express>) HttpClientUtils.post(url, ex, clientId, clientKey,
                    TypeRef);
            if (null == result2) {
                return null;
            }
            Map<String, Object> res = (Map<String, Object>) result2.getData();
            JSONObject jsonObject = JSONObject.fromObject(res.get("express"));
            Express expressInfo = (Express) JSONObject.toBean(jsonObject, Express.class);
            JSONArray jsonArray = JSONArray.fromObject(res.get("expressDetails"));
            List<ExpressDetail> expressDetails = (List<ExpressDetail>) JSONArray.toCollection(jsonArray,
                    ExpressDetail.class);
            expressInfo.setExpressDetail(expressDetails);
            return expressInfo;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public List<ExpressDetail> getExpressDetailInfoBySaleorderId(Saleorder saleorder) {
        List<ExpressDetail> expressList = expressMapper.getExpressDetailList(saleorder);
        return expressList;
    }

    @Override
    public Map<Integer, Integer> getExpressDetailNumInfo(Saleorder saleorder) {
        List<ExpressDetail> expressDetaillist = getExpressDetailInfoBySaleorderId(saleorder);
        Map<Integer, Integer> map = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(expressDetaillist)) {
            for (ExpressDetail expressDetail : expressDetaillist) {
                Integer num = map.get(expressDetail.getRelatedId());
                if (num == null) {
                    num = 0;
                }
                num += expressDetail.getNum();
                map.put(expressDetail.getRelatedId(), num);
            }
        }
        return map;
    }

    @Override
    public List<Integer> getExpressIds() {
        return expressMapper.getExpressIds();
    }

    /**
     * 获取当前快递下某商品数量
     *
     * @Author:strange
     * @Date:15:24 2020-01-06
     */
    @Override
    public ExpressDetail getExpressDetailNumByExpressId(ExpressDetail expressDetail) {
        return expressMapper.getExpressDetailNumByExpressId(expressDetail);
    }

    /**
     * 获取当前快递单商品详情
     *
     * @Author:strange
     * @Date:08:55 2020-01-07
     */
    @Override
    public List<ExpressDetail> getExpressDetailByExpressId(Integer expressId) {
        return expressMapper.getExpressDetailByExpressId(expressId);
    }

    /**
     * 判断是否需要重新生成开票申请
     *
     * @Author:strange
     * @Date:15:20 2020-01-07
     */
    @Override
    public boolean isUpdateExpressAndInvoice(Express express) {
        //如果快递商品信息变更则重新生成新的开票申请
        List<ExpressDetail> nweExpressDetailList = express.getExpressDetail();
        List<ExpressDetail> oldExpressDetailList = getExpressDetailByExpressId(express.getExpressId());
        //
        if (CollectionUtils.isEmpty(oldExpressDetailList)) {
            return true;
        } else if (CollectionUtils.isEmpty(nweExpressDetailList)) {
            return true;
        } else {
            Map<Integer, ExpressDetail> map = oldExpressDetailList.stream().collect(Collectors.toMap(ExpressDetail::getRelatedId, goods -> goods));
            if (nweExpressDetailList.size() != oldExpressDetailList.size()) {
                return true;
            }
            for (ExpressDetail nweExpressDetail : nweExpressDetailList) {
                ExpressDetail oldexpressDetail = map.get(nweExpressDetail.getRelatedId());
                if (oldexpressDetail == null) {
                    return true;
                }
                if (!nweExpressDetail.getNum().equals(oldexpressDetail.getNum())) {
                    return true;
                }
            }
        }
        //如果该快递下没有非关闭的开票申请则生成
        InvoiceApply invoiceApply = new InvoiceApply();
        invoiceApply.setExpressId(express.getExpressId());
        List<InvoiceApply> invoiceApplyInfoList = invoiceService.getInvoiceApplyInfoByExpressId(invoiceApply);
        if (CollectionUtils.isEmpty(invoiceApplyInfoList)) {
            return true;
        }
        return false;
    }

    //是否为第一次物流
    @Override
    public List<Express> getFirst(Integer traId) {
        return expressMapper.getFirst(traId);
    }

    //改变是否开据发票状态
    @Override
    public int changeIsinvoicing(Integer invoiceApplyId) {
        return expressMapper.changeIsinvoicing(invoiceApplyId);
    }

    //改变是否开据发票状态
    @Override
    public int updateIsinvoicing(Integer expressId) {
        return expressMapper.updateIsinvoicing(expressId);
    }

    //改变是否开据发票状态
    @Override
    public int updateIsinvoicingNo(Integer expressId) {
        return expressMapper.updateIsinvoicingNo(expressId);
    }

    /**
     * 保存快递单相关信息
     *
     * @param express
     * @param deliveryTimes
     * @param amount
     * @param id_num_price
     * @param saleOrder
     * @param flag
     * @param orderId
     * @param session
     * @param Identifier
     * @param delLogisticsNo
     * @return
     */
    @Override
    public ResultInfo saveExpressInfo(Express express, String deliveryTimes, BigDecimal amount, String id_num_price,
                                      Saleorder saleOrder, String flag, Integer orderId, HttpSession session, String Identifier, String delLogisticsNo) {
        ResultInfo<?> result = new ResultInfo<>();

        // 获取session中user信息
        User session_user = session != null ? (User) session.getAttribute(ErpConst.CURR_USER) : null;

        // 准备express中的expressDetailList
        List<ExpressDetail> expressDetailList = new ArrayList<ExpressDetail>();
        LogisticsOrderData logisticsOrderData = new LogisticsOrderData();
        List<LogisticsOrderGoodsData> LogisticsOrderGoodsDataList = new ArrayList<>();
        int countNum = 0;
        // add by frnalin.wu for[统计快递单只呢个商量数量] at 2019-06-19 begin
        // 统计当前快递单中 商品数量
        int exPressSkuNum = 0;
        // add by frnalin.wu for[统计快递单只呢个商量数量] at 2019-06-19 end
        //-----------票货同行------------
        boolean goodsPeerFlag = false;
        //快递新增入口票货同行相关逻辑取消
		/*if(saleOrder.getSaleorderId() != null && "1".equals(flag)) {
			goodsPeerFlag = invoiceService.getOrderIsGoodsPeer(saleOrder.getSaleorderId());
		}*/
        //List<InvoiceApplyDetail> invoiceApplyDetails = new ArrayList<>();


        //判断是否第一次物流
        Integer tradeFirst = null;
        //String hcTwo=null;
        Saleorder saleorderfirst = null;
        try {
            if (saleOrder.getSaleorderId() != null && "1".equals(flag)) {
                saleorderfirst = saleorderService.getsaleorderbySaleorderId(saleOrder.getSaleorderId());
                List<Express> first = getFirst(saleorderfirst.getTraderId());
                tradeFirst = first.size();
                //hcTwo = saleorderfirst.getSaleorderNo().substring(0, 2);
            }
        } catch (Exception e) {
            logger.error("判断第一次物流发生异常", e);
        }
        Integer relateId = null;
        //-----------票货同行------------
        if (null != id_num_price) {
            // 切割RelatedId和num拼接成的字符串
            String[] params = id_num_price.split("_");
            // 单价
            Integer price = 0;
            // 所有产品总价
            Integer allPrice = 0;
            // 已经分配过的金额
            Double allAmount = 0.00;
            // 每种产品平摊的运费
            Double expressDetailAmount = 0.00;
            if (null != params) {
                for (String s : params) {
                    String[] bid_num = s.split("\\|");
                    if (null != bid_num[1] && null != bid_num[2]) {
                        // 数量*金额
                        allPrice += Integer.parseInt(bid_num[1]) * Double.valueOf(bid_num[2]).intValue();
                    }
                }
                for (int j = 0; j < params.length; j++) {
                    String[] bid_num = params[j].split("\\|");
                    ExpressDetail expressDetail = new ExpressDetail();
                    LogisticsOrderGoodsData logisticsOrderGoodsData = new LogisticsOrderGoodsData();
                    //InvoiceApplyDetail invoiceApplyDetail = new InvoiceApplyDetail();
                    if (null != bid_num[0]) {
                        // 关联字段
                        expressDetail.setRelatedId(Integer.parseInt(bid_num[0]));
                        relateId = Integer.parseInt(bid_num[0]);
                        //invoiceApplyDetail.setDetailgoodsId(Integer.parseInt(bid_num[0]));
                    }
                    if (null != bid_num[1]) {
                        Integer skuN = Integer.parseInt(bid_num[1]);
                        if (skuN == 0) {
                            countNum += 1;
                            continue;
                        }
                        // 数量
                        expressDetail.setNum(skuN);
                        //invoiceApplyDetail.setNum(new BigDecimal(skuN));
                        //放到rabbitmq数据
                        logisticsOrderGoodsData.setNum(skuN);
                        // 累计
                        exPressSkuNum += skuN;
                    }
                    if (null != bid_num[2]) {
                        // 单价
                        price = Double.valueOf(bid_num[2]).intValue();
                    }
                    if ("1".equals(flag)) {
                        if (null != bid_num[3]) {
                            //放到rabbitmq数据
                            logisticsOrderGoodsData.setSkuNo(bid_num[3]);
                        }
                    }
                    //invoiceApplyDetails.add(invoiceApplyDetail);
                    // 最后一个产品时
                    if (null != amount) {
                        if (j == (params.length - 1)) {
                            expressDetailAmount = amount.doubleValue() - allAmount;
                        } else {
                            // 运费价格*（产品单价*产品数量）/所有产品总价 保留两位小数
                            allAmount += (double) Math.round(amount.doubleValue()
                                    * (Integer.parseInt(bid_num[1]) * Double.valueOf(bid_num[2]).intValue()) / allPrice
                                    * 100) / 100;
                            expressDetailAmount = (double) Math.round(amount.doubleValue()
                                    * (Integer.parseInt(bid_num[1]) * Double.valueOf(bid_num[2]).intValue()) / allPrice
                                    * 100) / 100;
                        }
                        BigDecimal eda = new BigDecimal(expressDetailAmount);
                        eda = eda.setScale(2, BigDecimal.ROUND_HALF_UP);

                        // 金额
                        expressDetail.setAmount(eda);
                    }
                    // 业务类型
                    if ("1".equals(flag)) {
                        // 销售
                        expressDetail.setBusinessType(SysOptionConstant.ID_496);
                    } else if ("2".equals(flag)) {
                        // 售后
                        expressDetail.setBusinessType(SysOptionConstant.ID_582);
                    } else if ("3".equals(flag)) {
                        //外借
                        expressDetail.setBusinessType(SysOptionConstant.ID_660);
                    } else {
                        // 采购
                        expressDetail.setBusinessType(SysOptionConstant.ID_515);
                    }
                    expressDetailList.add(expressDetail);
                    //rabbitmq需要发送的商品数据
                    LogisticsOrderGoodsDataList.add(logisticsOrderGoodsData);
                }
                logisticsOrderData.setOrderGoodsLogisticsDataList(LogisticsOrderGoodsDataList);
            }
        }
        // 业务类型
        if ("1".equals(flag)) {
            // 销售
            express.setBusinessType(SysOptionConstant.ID_496);
        } else if ("2".equals(flag)) {
            // 售后
            express.setBusinessType(SysOptionConstant.ID_582);
        } else if ("3".equals(flag)) {
            //外借
            express.setBusinessType(SysOptionConstant.ID_660);
        } else {
            // 采购
            express.setBusinessType(SysOptionConstant.ID_515);
        }
        if (CollectionUtils.isEmpty(expressDetailList)) {
            result.setMessage("所选商品的数量为0，不允许提交");
            return result;
        }
        if (express.getExpressId() == null || express.getExpressId() == 0) {
            express.setAddTime(DateUtil.sysTimeMillis());
        } else {
            Express expressInfoById = expressMapper.getExpressInfoById(express.getExpressId());
            express.setAddTime(expressInfoById.getAddTime());
            express.setWmsOrderNo(expressInfoById.getWmsOrderNo());
            express.setBatchNo(expressInfoById.getBatchNo());
        }
        express.setCreator(session_user != null ? session_user.getUserId() : 1);
        express.setUpdater(session_user != null ? session_user.getUserId() : 1);
        express.setModTime(DateUtil.sysTimeMillis());
        express.setDeliveryTime(DateUtil.convertLong(deliveryTimes, "yyyy-MM-dd"));
        express.setExpressDetail(expressDetailList);
        express.setIsEnable(1);
        express.setCompanyId(session_user != null ? session_user.getCompanyId() : 1);
        express.setSaleorderId(orderId);
        try {
            //--------------票货同行---------
            boolean isupdatefalg = true;
			/*if(goodsPeerFlag && "编辑".equals(Identifier)) {
				isupdatefalg = isUpdateExpressAndInvoice(express);
				if(isupdatefalg){
					//判断是否已开票
					Boolean expressFlag = invoiceService.getInvoiceApplyByExpressId(express.getExpressId());
					if(expressFlag){
						result.setMessage("已开票,不允许编辑");
						return result;
					}
				}
			}
			if(goodsPeerFlag) {
				express.setTravelingByTicket(1);
			}*/
            if ("1".equals(flag) && saleOrder.getSaleorderId() != null && !ErpConst.VIRTUAL_EXPRESS_NO.equals(express.getLogisticsComments())) {
                //数据校验避免超量提交
                List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleordergoodsList(saleOrder);
                //获取商品信息
                Map<Integer, SaleorderGoods> saleorderGoodsMap = saleorderGoodsList.stream().collect(Collectors.toMap(SaleorderGoods::getSaleorderGoodsId, goods -> goods));
                Map<Integer, Integer> expressDetailMap = getExpressDetailNumInfo(saleOrder);
                for (ExpressDetail expressDetail : expressDetailList) {

                    SaleorderGoods saleorderGoods = saleorderGoodsMap.get(expressDetail.getRelatedId());
                    Integer oldNum = expressDetailMap.get(expressDetail.getRelatedId());
                    if (oldNum == null) {
                        oldNum = 0;
                    }
                    if (!"编辑".equals(Identifier)) {
                        if (saleorderGoods.getDeliveryNum() < oldNum + expressDetail.getNum() && saleorderGoods.getNum() < oldNum + expressDetail.getNum()) {
                            logger.info("新增包裹回传超出可快递数量orderNo{}DeliveryNum{}oldNum{}expressDetailNum{}saleorderGoodsNum{}", saleOrder.getSaleorderNo(),
                                    saleorderGoods.getDeliveryNum(),
                                    oldNum, expressDetail.getNum(), saleorderGoods.getNum());
                            result.setMessage("超出可快递数量!");
                            result.setCode(-1);
                            logger.info("saveExpress 超出可快递数量! orderId:{}", orderId);
                            return result;
                        }
                    } else {
                        expressDetail.setExpressId(express.getExpressId());
                        ExpressDetail expressDetail1 = getExpressDetailNumByExpressId(expressDetail);
                        Integer num = 0;
                        if (expressDetail1 != null) {
                            num = expressDetail1.getNum();
                        }
                        //编辑时将之前当前快递单数量减去
                        if (saleorderGoods.getDeliveryNum() < oldNum + expressDetail.getNum() - num &&
                                saleorderGoods.getNum() < oldNum + expressDetail.getNum() - num) {
                            logger.info("编辑包裹回传超出可快递数量orderNo{}DeliveryNum{}oldNum{}expressDetailNum{}saleorderGoodsNum{}num{}", saleOrder.getSaleorderNo(),
                                    saleorderGoods.getDeliveryNum(),
                                    oldNum, expressDetail.getNum(), saleorderGoods.getNum(), num);
                            result.setMessage("超出可快递数量!");
                            result.setCode(-1);
                            logger.info("saveExpress 超出可快递数量! orderId:{}", orderId);
                            return result;
                        }
                    }
                }
            }
            //添加普发批次号
            generateNormalBatchNO(express, relateId);
            //添加wms出库单号
            if ("1".equals(flag) && saleOrder.getSaleorderId() != null) {
                Saleorder saleorder = saleorderMapper.selectByPrimaryKey(saleOrder.getSaleorderId());
                List<WarehouseGoodsOutInItem> items = warehouseGoodsOutInItemMapper.findByRelatedId(relateId);
                String orderNo = Objects.isNull(saleorder)?"":StringUtil.isEmpty(saleorder.getSaleorderNo())?"":saleorder.getSaleorderNo();
                for (WarehouseGoodsOutInItem item : items){
                    String wmsNo = orderNo + "/" + item.getWmsNo();
                    List<Express> byRelatedIdAndWmsOrderNo = expressMapper.findByWmsOrderNo(wmsNo);
                    if(CollUtil.isNotEmpty(byRelatedIdAndWmsOrderNo)){
                        continue;
                    }
                    express.setWmsOrderNo(wmsNo);
                    break;
                }
            }



            //保存快递单信息
            result = saveExpress(express);
            logger.info("添加包裹快递单返回信息 result:{}", JSON.toJSONString(result));
            // 销售
            Express reExpress = null;
            if ("1".equals(flag) && result != null && result.getCode().equals(0)) {
                reExpress = (Express) result.getData();
                reExpress.setLogisticsNo(express.getLogisticsNo());
                reExpress.setWmsOrderNo(express.getWmsOrderNo());

                dealPeriodUseNodeRecordByExpressInfo(reExpress, orderId);

                //保存快递详情和出入库记录关联关系
                warehouseOutService.addExpressDeatilsWarehouse(reExpress);
            }

            try {
                if ("3".equals(flag) && result != null && result.getCode().equals(0)) {
                    reExpress = (Express) result.getData();
                    reExpress.setLogisticsNo(express.getLogisticsNo());
                    reExpress.setWmsOrderNo(express.getWmsOrderNo());
                    //保存快递详情和出入库记录关联关系
                    logger.info("样品包裹绑定出库单信息 reExpress:{}", JSON.toJSONString(reExpress));
                    warehouseOutService.addExpressDeatilsWarehouse(reExpress);
                }
            } catch (Exception e) {
                logger.error("样品包裹绑定出库单信息错误告警", e);
            }

            //--------------票货同行---------
            if ("1".equals(flag) && ErpConst.VIRTUAL_EXPRESS_NO.equals(express.getLogisticsComments())) {
                logger.info("包裹票货同行返回信息 result:{}", JSON.toJSONString(result));
                return result;
            }

            if (null == result || !CommonConstants.SUCCESS_CODE.equals(result.getCode())) {
                logger.info("saveExpress 查询结果为空 orderId:{}", orderId);
                return new ResultInfo(-1, "saveExpress 查询结果为空 orderId:" + orderId);
            }

            if (null != reExpress) {
                logger.info("推送包裹信息 reExpress:{},orderId:{},countNum:{},logisticsOrderData:{}",
                        JSON.toJSONString(reExpress), orderId, countNum, JSON.toJSONString(logisticsOrderData));
                pushExpressToWeb(reExpress, flag, orderId, countNum, logisticsOrderData, Identifier, false);
            }

        } catch (Exception e) {
            logger.error("warehourse saveExpress:", e);
            result.setCode(-1);
            result.setMessage("操作失败");
            return result;
        }
        if (CommonConstants.SUCCESS_CODE.equals(tradeFirst)
//				&& "HC".equals(hcTwo)
                && saleorderfirst != null
                && ErpConst.FIVE.equals(saleorderfirst.getOrderType())
                && CommonConstants.SUCCESS_CODE.equals(result.getCode())) {
            result.setMessage(result.getMessage() + ",该订单首次发货时，请随货邮寄资质清单");
            //*first.get(0).setLogisticsComments(first.get(0).getLogisticsComments()+"首次合作客户，请随货邮寄资质清单");*//*
            //如果为空，null不显示在前端
            if (saleorderfirst.getLogisticsComments() == null) {
                saleorderService.updateLogisticsComments(saleorderfirst.getSaleorderId(), "首次合作客户，请随货邮寄资质清单");
            } else {
                saleorderService.updateLogisticsComments(saleorderfirst.getSaleorderId(), saleorderfirst.getLogisticsComments() + "  首次合作客户，请随货邮寄资质清单");
            }
        }
        logger.info("添加包裹快递单业务返回信息 result:{}", JSON.toJSONString(result));

        /**
         * base订阅快递信息
         */
        Express expressData = (Express) result.getData();
        String phone = "";
        if (ObjectUtil.isNotNull(expressData) && StringUtils.isNotBlank(expressData.getLogisticsNo()) && null != expressData.getLogisticsId()) {
            phone = this.getPhoneByBusinessType(expressData.getExpressId(), express.getLogisticsNo());
        }
        logger.info("开始推送快递信息至base服务,单号:{},快照信息:{}", expressData.getLogisticsNo(), JSON.toJSONString(expressData));
        logisticsService.pushLogisticsToBase(express.getLogisticsNo()
                , logisticsService.getLogisticsCodeByLogisticsId(express.getLogisticsId())
                , phone);

        return result;
    }

    /**
     * 销售订单保存快递信息时处理账期逾期编码
     *
     * @param reExpress
     * @param orderId
     */
    private void dealPeriodUseNodeRecordByExpressInfo(Express reExpress, Integer orderId) {
        logger.info("dealPeriodUseNodeRecordByExpressInfo orderId:{}, reExpress:{}", orderId, JSON.toJSONString(reExpress));
        if (reExpress == null || orderId == null) {
            return;
        }
        if (ErpConst.VIRTUAL_EXPRESS_NO.equals(reExpress.getLogisticsComments())) {
            logger.info("添加的包裹快递单为wms回传虚拟销售单 expressId:{}", reExpress.getExpressId());
            return;
        }
        PeriodUseNodeRecordPo periodUseNodeRecordPo = new PeriodUseNodeRecordPo();
        periodUseNodeRecordPo.setOrderType(CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_EXPRESS.getCode());
        periodUseNodeRecordPo.setOrderId(orderId.longValue());
        periodUseNodeRecordPo.setRelatedId(reExpress.getExpressId().longValue());
        periodUseNodeRecordPo.setAddTime(System.currentTimeMillis());
        periodUseNodeRecordPo.setCreator(ErpConst.ONE);
        logger.info("生成账期逾期编码为发货类型 expressId:{}", JSON.toJSONString(reExpress));
        BigDecimal goodsAmount = expressMapper.getGoodsAmountByExpressId(reExpress.getExpressId());
        if (goodsAmount == null || goodsAmount.compareTo(BigDecimal.ZERO) < 1) {
            logger.info("该订单为虚拟快递单或金额不存在 expressId:{}", reExpress.getExpressId());
            return;
        }
        periodUseNodeRecordPo.setAmount(goodsAmount);
        logger.info("销售保存包裹信息添加处理节点记录 periodUseNodeRecordPo:{}", JSON.toJSONString(periodUseNodeRecordPo));
        periodUseNodeRecordMapper.insertRecord(periodUseNodeRecordPo);
    }

    /**
     * @param reExpress          快递信息
     * @param flag               1:销售订单
     * @param orderId            销售订单id
     * @param countZeroNum       快递数为0的商品计数
     * @param logisticsOrderData 物流信息 setLogisticsOrderGoodsDataList 具体商品物流信息
     * @param Identifier         action 新增/修改
     * @param isDeliveryDirect   是否是直发
     */
    @Override
    public void pushExpressToWeb(Express reExpress,
                                 String flag,
                                 Integer orderId,
                                 Integer countZeroNum,
                                 LogisticsOrderData logisticsOrderData,
                                 String Identifier,
                                 Boolean isDeliveryDirect) {
        logger.info("saveExpress rabbitmq发送快递信息 start-----orderId:{} countZeroNum{} flag{} Identifier{} isDeliveryDirect{},reExpress:{},logisticsOrderData:{}", orderId, countZeroNum, flag
                , Identifier, isDeliveryDirect,
                JsonUtils.convertObjectToJsonStr(reExpress), JsonUtils.convertObjectToJsonStr(logisticsOrderData));
        if (null == reExpress) {
            return;
        }
        if (countZeroNum == 0 && "1".equals(flag)) {
            //添加完成后向rabbitmq发送快递信息
            Saleorder saleorder = saleorderMapper.getWebAccountId(orderId);
            if (null != saleorder && saleorder.getOrderType() <= 1 || saleorder.virtualBdOrder()) {
                logger.info("开始封装包裹推送信息 saleorder:{}", JSON.toJSONString(saleorder));
                if (EmptyUtils.isNotBlank(reExpress.getLogisticsName())) {
                    String logisticsCode = logisticsService.getLogisticsCode(reExpress.getLogisticsName());
                    logisticsOrderData.setLogisticsCode(logisticsCode);
                } else {
                    logisticsOrderData.setLogisticsCode(logisticsService.getLogisticsCodeByLogisticsId(reExpress.getLogisticsId()));
                }
                logisticsOrderData.setAddLogisticsNo(reExpress.getLogisticsNo());
                logisticsOrderData.setAccountId(saleorder.getWebAccountId());
                logisticsOrderData.setSsoAccountId(saleorder.getSsoAccountId());
                logisticsOrderData.setOrderNo(saleorder.getSaleorderNo());
                if (isDeliveryDirect) {
                    logisticsOrderData.setLogisticsType(SysOptionConstant.LOGISTICS_TYPE_1 + 1);
                } else {
                    logisticsOrderData.setLogisticsType(SysOptionConstant.LOGISTICS_TYPE_1);
                }

                logger.info("生产者开始发送消息=======" + JSON.toJSONString(logisticsOrderData));
                if ("保存".equals(Identifier)) {
                    logisticsOrderData.setDelLogisticsNo(reExpress.getLogisticsNo());
                    logisticsOrderData.setType(SysOptionConstant.LOGISTICS_ADD_1);
                    msgProducer.sendMsg(RabbitConfig.MJX_ADDLOGISTICS_EXCHANGE, RabbitConfig.MJX_ADDLOGISTICS_ROUTINGKEY, JSON.toJSONString(logisticsOrderData));

                }
                if ("编辑".equals(Identifier)) {
                    logisticsOrderData.setDelLogisticsNo(reExpress.getLogisticsNo());
                    logisticsOrderData.setType(SysOptionConstant.LOGISTICS_UPDATE_2);
                    msgProducer.sendMsg(RabbitConfig.MJX_ADDLOGISTICS_EXCHANGE, RabbitConfig.MJX_ADDLOGISTICS_ROUTINGKEY, JSON.toJSONString(logisticsOrderData));
                }
                logger.info("生产者开始发送消息完毕=======" + JSON.toJSONString(logisticsOrderData));
            }
        }
        // 耗材商城订单物流信息推送
        Saleorder saleOrderFr = null;
        if ("1".equals(flag)) {
            saleOrderFr = new Saleorder();
            saleOrderFr.setSaleorderId(orderId);
            saleOrderFr = saleorderService.getBaseSaleorderInfo(saleOrderFr);
        }
        final Saleorder saleOrderWx = saleOrderFr;

        if (SysOptionConstant.ID_496.equals(reExpress.getBusinessType()) || isDeliveryDirect) {
            if (null != saleOrderWx) {
                // 如果是耗材商城的订单
                if (OrderConstant.ORDER_TYPE_HC.equals(saleOrderWx.getOrderType())) {
                    // 根据快递单查询该快递单下的商品列表
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("saleOrder", saleOrderWx);
                    try {
                        // 查询该快递单下的商品信息列表
                        List<SaleorderGoods> goodsList = null;
                        // 直发商品信息
                        if (isDeliveryDirect) {
                            goodsList = saleorderGoodsMapper.getDeliveryDirectSaleOrderGoodsListByExpressId(reExpress.getExpressId());
                        } else {
                            goodsList = getSaleorderGoodsListByexpressId(reExpress.getExpressId());
                        }
                        map.put("goodsList", goodsList);
                        map.put("method", "save");
                        // 调用ERP推送到耗材的接口
                        hcSaleorderService.putExpressToHC(map);

                    } catch (Exception e) {
                        logger.error("调用ERP推送到耗材的接口 | 发生异常", e);
                    }

                }

                /*****************************微信发货提醒START**************************************/

                new Thread() {
                    @Override
                    public void run() {
                        try {
                            String uuid = UUID.randomUUID().toString().replace("-", "");
                            logger.info("开始执行微信发货提醒,uuid:{}",uuid);
                            // 根据订单的Id查询订单信息，如果是耗材的销售订单，需要退物流信息以及订单的状态

                            //VDERP-8294 【采购单】直发的采购单添加虚拟物流信息不发短信
                            if ("XN23333333".equals(reExpress.getLogisticsNo())) {
                                return;
                            }
                            // 微信查询数据 查询结果
                            Map<String, String> wxSendMap = sendForExpress(saleOrderWx, reExpress);

                            // 医械购 发送模板消息开关
                            if (CommonConstants.ONE.equals(sendYxgWxTempMsgFlag)) {
                                // 医械购
                                sendTemplateMsgHcForShip(saleOrderWx, wxSendMap);
                            }
                            JcWeChatModel jcWeChatModel = new JcWeChatModel(orderId);
                            jcWeChatModel.setExpressId(reExpress.getExpressId());
                            jcWeChatArrService.sendTemplateMsgJcorder(jcWeChatModel, JcWeChatEnum.SEND_DELIVERY, isDeliveryDirect);

                            if (!reExpress.getLogisticsId().equals(4) && !reExpress.getLogisticsId().equals(7) && !reExpress.getLogisticsId().equals(8)) {
                                //微信公共号
                                sendTemplateVxService(saleOrderWx, wxSendMap);
                            }
                            logger.info("结束执行微信发货提醒，uuid:{}",uuid);
                        } catch (Exception e) {
                            logger.info("微信发货提醒 | 发生异常,告警待处理", e);
                        }

                    }

                }.start();

                /*****************************微信发货提醒END**************************************/

            }
        }
    }


    private final ZTOExpressAdapter ztoExpressAdapter = new ZTOExpressAdapter();

    private final static Pattern SPECIAL_PROVINCE_PATTERN = Pattern.compile("上海|江苏|浙江|安徽");


    @Override
    public String getTrackingOrderNo(ExpressSupportedEnum expressSupportedType, Saleorder saleorder, AfterSalesDetail av, Express ep, Map<String, String> map, Integer companyId, Integer btype, Integer cwtype) {
        Preconditions.checkArgument(Objects.equals(expressSupportedType, ExpressSupportedEnum.ZTO_EXPRESS), "目前只支持中通快递");

        final String traderArea = determineTraderArea(btype, saleorder, av);

        // 判断是普通还是保价系统 ---- 江浙沪皖地区使用中通普通系统 更具售票信息判断使用中通那个系统
        boolean xinLianEnable = allowXinLianOrderType(traderArea);
        if (xinLianEnable) {
            RequestUtils.bindToRequestScope(ExpressUtil.XINLIAN_ATTR_NAME, xinLianEnable);
        }
        String trackingOderNo;
        try {
            trackingOderNo = ztoExpressAdapter.getExpressNo(saleorder, av, ep, map, companyId, cwtype, btype);
        } catch (Exception e) {
            logger.error("调用中通快递获取快递单号时发生错误 - saleOrderNo:{} ,type:{}, msg:{}", saleorder.getSaleorderNo(), btype, e.getMessage());
            throw e;
        } finally {
            if (xinLianEnable) {
                RequestUtils.unbindToRequestScope(ExpressUtil.XINLIAN_ATTR_NAME);
            }
        }

        return trackingOderNo;
    }


    @Override
    public void printTrackingOrder(ExpressSupportedEnum expressSupportedType, Saleorder saleorder, AfterSalesDetail av, Express express, Map<String, String> map, Integer companyId, Integer btype, Integer cwtype) {
        Preconditions.checkArgument(Objects.equals(expressSupportedType, ExpressSupportedEnum.ZTO_EXPRESS), "目前只支持中通快递");
        Preconditions.checkArgument(companyId != null, "操作人所属公司为空");

        final String traderArea = determineTraderArea(btype, saleorder, av);

        // 判断是普通还是保价系统 ---- 江浙沪皖地区使用中通普通系统 更具售票信息判断使用中通那个系统
        boolean xinLianEnable = allowXinLianOrderType(traderArea);
        if (xinLianEnable) {
            RequestUtils.bindToRequestScope(ExpressUtil.XINLIAN_ATTR_NAME, xinLianEnable);
        }
        try {
            ztoExpressAdapter.printTrackingOrder(saleorder, av, express, map, companyId, cwtype, btype);
        } catch (Exception e) {
            logger.error("调用中通快递打印在线快递单时发生错误 - saleOrderNo:{}, type:{}, msg:{}", saleorder.getSaleorderNo(), btype, e.getMessage());
            throw e;
        } finally {
            if (xinLianEnable) {
                RequestUtils.unbindToRequestScope(ExpressUtil.XINLIAN_ATTR_NAME);
            }
        }
    }

    @Override
    public Express getExpressInfoByPrimaryKey(Integer expressId) {

        return expressMapper.selectExpressByPrimaryKey(expressId);
    }

    @Override
    public Boolean checkRelatedWarehouseInfo(Integer expressId) {

        Buyorder buyorder = expressMapper.getBuyorderInfoByExpressId(expressId);

        //查询是否有同行单关联数据
        Integer num = expressMapper.getPurchaseDeliveryDirectBatchDetail(expressId);

        if (ErpConst.LOCK_STATUS.equals(buyorder.getLockedStatus()) ||
                (!ErpConst.ONE.equals(buyorder.getStatus()) && !ErpConst.THREE.equals(buyorder.getStatus()))
                || !ErpConst.ZERO.equals(num)
        ) {
            return Boolean.TRUE;

        }
        return Boolean.FALSE;
    }

    @Override
    public boolean updateExpressArrivalStatusFromBase(LogisticsDTO logisticsDTO) {

        //快递单当前状态，包括0在途，1揽收，2疑难，3签收，4退签，5派件，6退回，7转投等8个状态
        if (!Integer.valueOf(3).equals(logisticsDTO.getState())) {
            LOG.info("base推送更新快递收货状态失败，快递单当前状态非《签收》,快递单号:{},快递单状态:{}", logisticsDTO.getNu(), logisticsDTO.getState());
            return false;
        }
        if (StringUtils.isNotBlank(logisticsDTO.getCom()) && StringUtils.isNotBlank(logisticsDTO.getNu())) {
            List<Integer> expressIdList = logisticsMapper.getExpressIdByCodeAndNo(logisticsDTO.getCom(), logisticsDTO.getNu());
            if (expressIdList.size() == 0) {
                LOG.info("base推送更新快递收货状态失败,未查询到快递记录,快递单号:{}", logisticsDTO.getNu());
                return false;
            }
            expressMapper.updateExpressArrivalStatusByLogisticsNo(expressIdList, logisticsDTO.getNu());
            LOG.info("base推送更新快递收货状态成功,快递单号:{}", logisticsDTO.getNu());
            return true;
        }
        return false;
    }


    private String determineTraderArea(Integer bizType, Saleorder saleorder, AfterSalesDetail av) {
        String traderArea;
        if (bizType == 1) {
            //销售开票
            traderArea = saleorder.getInvoiceTraderArea();
        } else if (bizType == 3) {
            // 售后开票
            traderArea = av.getInvoiceTraderArea();
        } else {
            throw new ShowErrorMsgException("暂不支持此业务类型: " + bizType);
        }

        if (StringUtils.isEmpty(traderArea)) {
            throw new ShowErrorMsgException("缺少客户收票地区信息");
        }

        return traderArea;
    }

    private boolean allowXinLianOrderType(String traderArea) {
        Matcher matcher = SPECIAL_PROVINCE_PATTERN.matcher(traderArea);
        return !matcher.find();
    }

    @Override
    public String getPhoneByBusinessType(Integer expressId, String logisticsNo) {
        //获取手机号
        String phone = "";
        List<ExpressDetail> expressDetails = expressMapper.getExpressDetailByExpressId(expressId);
        if (expressDetails.size() < 1) {
            return phone;
        }
        Integer businessType = expressDetails.get(0).getBusinessType();
        // 快递业务类型为销售订单
        if (Objects.equals(BUSINESS_TYPE_SALE_ORDER, businessType)) {
            List<String> phoneList = expressMapper.getPhoneByBusinessTypeSaleOrder(expressId);
            if (CollectionUtils.isNotEmpty(phoneList) && phoneList.size() == 1) {
                logger.info("增加传参收件手机号--业务类型为销售订单--expressId={}--logisticsNo={}--phone={}",
                        expressId, logisticsNo, phoneList.get(0));
                phone = phoneList.get(0);
            } else {
                logger.info("增加传参收件手机号--业务类型为销售订单--expressId={}--logisticsNo={},未查询到phone或phone不唯一",
                        expressId, logisticsNo);
            }
        }
        // 快递业务类型为采购订单
        if (Objects.equals(BUSINESS_TYPE_BUY_ORDER, businessType)) {
            List<String> phoneList = expressMapper.getPhoneByBusinessTypeBuyOrder(expressId);
            if (CollectionUtils.isNotEmpty(phoneList) && phoneList.size() == 1) {
                logger.info("增加传参收件手机号--业务类型为销售订单--expressId={}--logisticsNo={}--phone={}",
                        expressId, logisticsNo, phoneList.get(0));
                phone = phoneList.get(0);
            } else {
                logger.info("增加传参收件手机号--业务类型为销售订单--expressId={}--logisticsNo={},未查询到phone或phone不唯一",
                        expressId, logisticsNo);
            }
        }
        // 快递业务类型为发票寄送
        if (Objects.equals(BUSINESS_TYPE_INVOICE, businessType)) {
            List<String> phoneList = expressMapper.getPhoneByBusinessTypeInvoice(expressId);
            if (CollectionUtils.isNotEmpty(phoneList) && phoneList.size() == 1) {
                logger.info("增加传参收件手机号--业务类型为发票寄送--expressId={}--logisticsNo={}--phone={}",
                        expressId, logisticsNo, phoneList.get(0));
                phone = phoneList.get(0);
            } else {
                logger.info("增加传参收件手机号--业务类型为发票寄送--expressId={}--logisticsNo={},未查询到phone或phone不唯一",
                        expressId, logisticsNo);
            }
        }
        return phone;
    }

    @Override
    public List<Express> getexpressinfoNewConfirm(Express express) {
        Integer saleorderId = express.getSaleorderId();
        List<Express> list = new ArrayList<>();
        List<Express> list2 = new ArrayList<>();
        List<Express> list3 = new ArrayList<>();
        List<Integer> relatedIds = new ArrayList<Integer>();
        list = expressMapper.getExpressInfoNew(express);
        Map<Integer, ConfirmationBatchesRelation> ewmap = null;
        Map<String, List<Integer>> wcmap = null;
        try {
            List<ConfirmationBatchesRelation> confirmationBatchesRelations = confirmationBatchesRelationMapper.selectAllBySaleorderId(saleorderId);
            //快递-批次map
            ewmap = confirmationBatchesRelations.stream().collect(Collectors.toMap(ConfirmationBatchesRelation::getExpressId, p -> p,(v1, v2)->v1));
            //批次-确认单map
            wcmap = confirmationBatchesRelations.stream().collect(Collectors.toMap(ConfirmationBatchesRelation::getBatchNo, p -> {
                        List<Integer> longs = new ArrayList<>();
                        longs.add(p.getConfirmationId());
                        return longs;
                    }, (List<Integer> v1, List<Integer> v2) -> {
                        v1.addAll(v2);
                        return v1;
                    }
            ));
            if(list!=null&&list.size()>0){
                for (Express ep2 : list) {
                    if(null!=ep2.getLogisticsName()){
                        String logisticCode= logisticsMapper.getLogisticsCode(ep2.getLogisticsName());
                        ep2.setLogisticsCode(logisticCode);
                    }
                    ep2.setBusiness_Type(express.getBusinessType());
                }
            }
        } catch (Exception e) {
            logger.error("查询物流信息时发生错误 - saleorderId:{}, msg:{}", saleorderId, e.getMessage());
            throw e;
        }

        if (express.getBusinessType() != null && express.getBusinessType() == 496) {
            // 查询此销售单，对应的直发采购单下的，快递单list
            relatedIds = express.getRelatedIds();
            if (relatedIds != null && relatedIds.size() > 0) {
                list2 = expressMapper.getBuyExpressList(express);
                list.addAll(list2);
            }
        }
        for (Express ep : list) {
            if (ep.getContent() != null && !"".equals(ep.getContent())) {
                String str = "";
                JSONObject rd = JSONObject.fromObject(ep.getContent());
                if (rd != null) {
                    String message = rd.getString("message");
                    if ("ok".equals(message)) {
                        JSONArray ja = rd.getJSONArray("data");
                        JSONObject jl = ja.getJSONObject(0);
                        str = jl.getString("time") + jl.getString("context");
                    } else {
                        str = message;
                    }
                    ep.setContentNew(str);
                } else {
                    ep.setContentNew("暂无信息");
                }

            } else {
                ep.setContentNew("暂无信息");
            }
            boolean weatherConformation = false;
            if (ep.getBatchNo()!=null&&StringUtil.isNotEmpty(ep.getBatchNo())){
                //在线确认逻辑操作
                String batchNo = ep.getBatchNo();
                OutboundBatchesRecode byBatchNo = outboundBatchesRecodeMapper.getByBatchNo(batchNo);
                Integer onlineConfirm = byBatchNo.getOnlineConfirm();
                Integer auditStatus = byBatchNo.getAuditStatus();
                if (onlineConfirm!=null&&onlineConfirm.equals(CommonConstants.STATUS_1)){
                    Express express2 = new Express();
                    BeanUtils.copyProperties(ep,express2);
                    //确认单类型
                    express2.setConfirmationType(CommonConstants.CATEGORY_LEVEL_3);
                    if (auditStatus == null){
                        //待审核
                        express2.setIsConfirmationPass(0);
                    }
                    if (auditStatus !=null && auditStatus == 0){
                        //审核通过
                        express2.setIsConfirmationPass(2);
                    }
                    if (auditStatus !=null && auditStatus == 1){
                        //审核未通过
                        express2.setIsConfirmationPass(1);
                    }
                    if (auditStatus !=null && auditStatus == 2){
                        //审核中
                        express2.setIsConfirmationPass(3);
                    }
                    if (auditStatus !=null && auditStatus == 3){
                        //待审核
                        express2.setIsConfirmationPass(0);
                    }
                    //确认单操作时间
                    ExpressOnlineReceiptVo expressOnlineReceiptById = expressMapper.getExpressOnlineReceiptById(ep.getOnlineReceiptId());
                    express2.setConfirmationModTime(expressOnlineReceiptById.getSignTime());
                    express2.setFirstRoll(1);
                    express2.setRollSize(1);
                    list3.add(express2);
                    weatherConformation = true;
                }
            }
            //确认单业务操作，增加确认单类型，确认单名称，确认单操作人，确认单操作时间，确认单审核状态
            ConfirmationBatchesRelation confirmationBatchesRelation = ewmap.get(ep.getExpressId());
            if (confirmationBatchesRelation != null) {
                String batchNo = confirmationBatchesRelation.getBatchNo();
                Integer auditStatus = confirmationBatchesRelation.getAuditStatus();
                List<Integer> confirmationids = wcmap.get(batchNo);

                if (confirmationids != null && confirmationids.size()>0) {

                    //主键索引
                    List<ConfirmationFormRecode> confirmationFormRecodes = confirmationFormRecodeMapper.selectAllByIds(confirmationids);

                    //前端行合并逻辑
                    if (weatherConformation){
                        Express express1 = list3.get(list3.size() - 1);
                        express1.setRollSize(1+confirmationFormRecodes.size());
                        express1.setFirstRoll(1);
                    }

                    for (int i = 0; i < confirmationFormRecodes.size(); i++) {
                        ConfirmationFormRecode confirmationFormRecode = confirmationFormRecodes.get(i);
                        Express express1 = getExpress1(ep, auditStatus, confirmationFormRecode);
                        OutboundBatchesRecode byBatchNo = outboundBatchesRecodeMapper.getByBatchNo(batchNo);
                        Integer onlineConfirm = byBatchNo.getOnlineConfirm();
                        if (CommonConstants.STATUS_1.equals(onlineConfirm)){
                            //确认单操作时间
                            if (ep.getOnlineReceiptId() != null) {
                                ExpressOnlineReceiptVo expressOnlineReceiptById = expressMapper.getExpressOnlineReceiptById(ep.getOnlineReceiptId());
                                if (expressOnlineReceiptById != null) {
                                    express1.setConfirmationModTime(expressOnlineReceiptById.getSignTime());
                                }
                            }
                        }
                        express1.setConfirmationRefuseReason(byBatchNo.getComments());
                        if (confirmationFormRecodes.size()>1){

                            if (!weatherConformation){
                                if (i==0){
                                    express1.setFirstRoll(1);
                                    express1.setRollSize(confirmationFormRecodes.size());
                                }else {
                                    express1.setFirstRoll(0);
                                }
                            }else {
                                express1.setFirstRoll(0);
                            }
                        }else {
                            if (!weatherConformation){
                                express1.setFirstRoll(1);
                                express1.setRollSize(1);
                            }else {
                                express1.setFirstRoll(0);
                            }

                        }
                        list3.add(express1);
                    }

                }else {
                    if (!weatherConformation){
                        ep.setFirstRoll(1);
                        ep.setRollSize(1);
                        list3.add(ep);
                    }
                }
            }else {
                if (!weatherConformation){
                    ep.setFirstRoll(1);
                    ep.setRollSize(1);
                    list3.add(ep);
                }
            }


        }
        return list3;
    }

    private  Express getExpress1(Express ep, Integer auditStatus, ConfirmationFormRecode confirmationFormRecode) {
        Express express1 = new Express();
        BeanUtils.copyProperties(ep,express1);
        //确认单id
        express1.setConfirmationId(confirmationFormRecode.getId());
        //确认单类型
        express1.setConfirmationType(confirmationFormRecode.getConfirmationType());
        //确认单名称
        express1.setConfirmationName(confirmationFormRecode.getConfirmationName());
        if (auditStatus == null){
            //待审核
            express1.setIsConfirmationPass(0);
        }
        if (auditStatus !=null && auditStatus == 0){
            //审核通过
            express1.setIsConfirmationPass(2);
        }
        if (auditStatus !=null && auditStatus == 1){
            //审核未通过
            express1.setIsConfirmationPass(1);
        }
        if (auditStatus !=null && auditStatus == 2){
            //审核中
            express1.setIsConfirmationPass(3);
        }
        if (auditStatus !=null && auditStatus == 3){
            //待审核
            express1.setIsConfirmationPass(0);
        }
        //确认单创建时间
        express1.setConfirmationAddTime(confirmationFormRecode.getAddTime());
        //确认单操作人
        User userById = userService.getUserById(confirmationFormRecode.getCreator());
        express1.setConfirmationCreator(userById.getUsername());
        //确认单操作时间
        express1.setConfirmationModTime(confirmationFormRecode.getModTime());
        return express1;
    }

    @Override
    public SaleOrderConfirmBatchExpressVo getSaleOrderConfiremInformation(HttpSession session,Integer saleorderId) {
        SaleOrderConfirmBatchExpressVo backVo = new SaleOrderConfirmBatchExpressVo();
        ConfirmationBatchesRelation confirmationBatchesRelation = new ConfirmationBatchesRelation(saleorderId);
        //获取销售订单下的所有确认单 批次 关系
        List<ConfirmationBatchesRelation> confirmationBatchesRelations = confirmationBatchesRelationMapper.selectAllByRelation(confirmationBatchesRelation);
        //获取所有确认单
        List<Integer> confirmationIds = confirmationBatchesRelations.stream().map(ConfirmationBatchesRelation::getConfirmationId).collect(Collectors.toList());
        List<ConfirmationFormRecode> confirmationFormRecodes = confirmationFormRecodeMapper.selectAllByIds(confirmationIds);
        //获取确认单对多个批次  map关系
        Map<Integer, List<String>> cwmap = confirmationBatchesRelations.stream().collect(Collectors.toMap(ConfirmationBatchesRelation::getConfirmationId, p -> {
                    List<String> strings = new ArrayList<>();
                    strings.add(p.getBatchNo());
                    return strings;
                }, (List<String> v1, List<String> v2) -> {
                    v1.addAll(v2);
                    return v1;
                }
        ));
        //获取每个批次下的物流信息
        List<String> batchNoList = confirmationBatchesRelations.stream().map(ConfirmationBatchesRelation::getBatchNo).collect(Collectors.toList());
        HashMap<String, List<Express>> batchkvExpress = new HashMap<>();
        batchNoList.stream().forEach(x->{
            List<Express> expressListByBatchNo = expressMapper.getExpressListByBatchNo(x);
            batchkvExpress.put(x,expressListByBatchNo);
        });
        //批次报错信息
        if (batchNoList.size()==0){
            SaleOrderConfirmBatchExpressVo saleOrderConfirmBatchExpressVo = new SaleOrderConfirmBatchExpressVo();
            saleOrderConfirmBatchExpressVo.setErroMessage("销售订单中确认单下批次为空，审核查询失败");
            return saleOrderConfirmBatchExpressVo;
        }
        List<OutboundBatchesRecode> allByBatchNoList = outboundBatchesRecodeMapper.findAllByBatchNoList(batchNoList);
        ArrayList<ConfirmationFormRecode> recodes = new ArrayList<>();
        //组装确认单,批次,物流信息
        for (int i = 0; i < confirmationFormRecodes.size(); i++) {
            //url
            List<String> urls = new ArrayList<>();
            List<String> pdfurls = new ArrayList<>();
            ConfirmationFormRecode cf = confirmationFormRecodes.get(i);
            List<String> fileIdstrs = Arrays.asList(cf.getFileId().split(","));
            List<Long> fileIds = fileIdstrs.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            List<Attachment> attachments = attachmentMapper.selectAllByIds(fileIds);
            attachments.stream().forEach(x->{
                String uri =x.getUri();

                if(x.getSuffix().equals("pdf")){
                    pdfurls.add(x.getUri());
                }else {
                    urls.add(uri);
                }
            });
            cf.setUrlList(urls);
            cf.setPdfList(pdfurls);
            //批次集合
            List<String> batchNos = cwmap.get(cf.getId());
            List<OutboundBatchesRecode> outboundBatchesRecodes = allByBatchNoList.stream().filter(x -> {
                if (batchNos.contains(x.getBatchNo())) {
                    //批次审核状态为通过不展示
                    if (x.getAuditStatus()!=null&&x.getAuditStatus()==1){
                            return false;
                    }
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            for (int i1 = 0; i1 < outboundBatchesRecodes.size(); i1++) {
                OutboundBatchesRecode outboundBatchesRecode = outboundBatchesRecodes.get(i1);
                String batchNo = outboundBatchesRecode.getBatchNo();
                //获取批次下不同商品
                CurrentUser curr_user = CurrentUser.getCurrentUser();
                Saleorder saleorder = new Saleorder();
                saleorder.setSaleorderId(saleorderId);
                // 获取订单详情
                saleorder.setCompanyId(curr_user.getCompanyId());

                saleorder.setBussinessType(2);

                List<BatchExpressVo> batchExpressByIds = new ArrayList<>();

                //判断直发普发
               if(outboundBatchesRecode.getBatchType().equals(1)){
                   List<WarehouseGoodsOperateLog> wgOlistByComments = warehouseOutService.getWGOlistByComments(batchNo,saleorderId);
                   if (wgOlistByComments.size()==0) {
                       continue;
                   }
                   //物流单号
                   List<Express> expressList = batchkvExpress.get(batchNo).stream().collect(Collectors.toList());
                   List<String> expressLogiscsNo = expressList.stream().map(Express::getLogisticsNo).collect(Collectors.toList());
                   String logiscsNos = String.join(",", expressLogiscsNo);

                   saleorder.setBatchNoComments(batchNo);
                   // 普发出库记录清单
                   List<WarehouseGoodsOperateLog> warehouseOutList = warehouseOutService.getOutDetil(saleorder);

                   List<String> checkSku = new ArrayList<>();
                   //遍历处理普发出库记录
                   if (null != warehouseOutList) {
                       Map<String, Integer> skuNum = warehouseOutList.stream().collect(Collectors.toMap(WarehouseGoodsOperateLog::getSku, WarehouseGoodsOperateLog::getRealGoodsNum, (v1, v2) -> v1 + v2));

                       for (WarehouseGoodsOperateLog wl : warehouseOutList) {
                           outboundBatchesRecode.setBatchTime(DateUtil.convertString(wl.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
                           BatchExpressVo batchExpressVo = new BatchExpressVo();
                           if (checkSku.contains(wl.getSku())){
                               continue;
                           }
                           batchExpressVo.setNum(skuNum.get(wl.getSku()));
                           batchExpressVo.setSku(wl.getSku());
                           batchExpressVo.setModel(wl.getModel());
                           batchExpressVo.setBrand(wl.getBrandName());
                           batchExpressVo.setGoodsName(wl.getGoodsName());
                           batchExpressVo.setLogisticsOrderNo(logiscsNos);
                           batchExpressByIds.add(batchExpressVo);
                           checkSku.add(wl.getSku());

                       }
                   }
               }else {
                   directBatchNos(saleorderId, outboundBatchesRecode, batchExpressByIds);
               }
                outboundBatchesRecode.setBatchExpressVos(batchExpressByIds);
            }
            cf.setBatchesRecodes(outboundBatchesRecodes);
            if (outboundBatchesRecodes.size()>0){
                recodes.add(cf);
            }
        }

        backVo.setConfirmationFormRecodes(recodes);
        return backVo;
    }

    private void directBatchNos(Integer saleorderId, OutboundBatchesRecode outboundBatchesRecode, List<BatchExpressVo> batchExpressByIds) {
        String batchNo = outboundBatchesRecode.getBatchNo();
        Express expressQueryVo = new Express();
        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(saleorderId);
        List<Express> buyExpressList = new ArrayList<>();
        if (saleorderGoods.size()>0){
            List<Integer> listSale = saleorderGoods.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
            expressQueryVo.setRelatedIds(listSale);
            expressQueryVo.setBatchNo(batchNo);
            buyExpressList = expressMapper.getBuyExpressList(expressQueryVo);
        }


        Map<String, BatchExpressVo> skuNum = new HashMap<>();
        for (int j = 0; j < buyExpressList.size(); j++) {
            Express express = buyExpressList.get(j);
            String logisticsNo = express.getLogisticsNo();
            List<ExpressDetail> expressDetail = express.getExpressDetail();
            List<String> checkSku = new ArrayList<>();
            outboundBatchesRecode.setBatchTime(DateUtil.convertString(express.getDeliveryTime(), "yyyy-MM-dd"));

            for (ExpressDetail detail : expressDetail) {
                String sku = detail.getSku();
                if (skuNum.containsKey(sku)) {
                    BatchExpressVo batchExpressVo = skuNum.get(sku);
                    Integer num = batchExpressVo.getNum();
                    num = num+detail.getNum();
                    batchExpressVo.setNum(num);
                    List<String> logisticsOrderNoUse = batchExpressVo.getLogisticsOrderNoUse();
                    if (!logisticsOrderNoUse.contains(logisticsNo)) {
                        logisticsOrderNoUse.add(logisticsNo);
                        batchExpressVo.setLogisticsOrderNo(String.join(",",logisticsOrderNoUse));
                    }
                }else{
                    BatchExpressVo batchExpressVo = new BatchExpressVo();
                    batchExpressVo.setNum(detail.getNum());
                    List<String> logisticsOrderNoUse = new ArrayList<>();
                    logisticsOrderNoUse.add(logisticsNo);
                    batchExpressVo.setLogisticsOrderNoUse(logisticsOrderNoUse);
                    batchExpressVo.setLogisticsOrderNo(logisticsNo);
                    batchExpressVo.setSku(detail.getSku());
                    batchExpressVo.setModel(detail.getModel());
                    batchExpressVo.setBrand(detail.getBrandName());
                    batchExpressVo.setGoodsName(detail.getGoodName());
                    skuNum.put(detail.getSku(),batchExpressVo);
                }

            }
        }

        Set<String> keySet = skuNum.keySet();
        for (String s : keySet) {
            BatchExpressVo batchExpressVo = skuNum.get(s);
            batchExpressByIds.add(batchExpressVo);
        }
    }

    @Override
    public List<OutboundBatchesRecode> getSaleOrderOutboundBatchesRecode(HttpSession session, Integer saleOrderId) {
        List<OutboundBatchesRecode> outboundBatchesRecodes = new ArrayList<>();
        //获取销售订单下所有出库批次
        Express exQueryVo = new Express();
        exQueryVo.setSaleorderId(saleOrderId);
        List<Express> expressInfoConfirmation = expressMapper.getExpressInfoConfirmation(exQueryVo);

        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(saleOrderId);
        List<Express> buyExpressList = new ArrayList<>();
        if (saleorderGoods.size()>0){
            List<Integer> listSale = saleorderGoods.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
            exQueryVo.setRelatedIds(listSale);
            buyExpressList = expressMapper.getBuyExpressList(exQueryVo);
        }

        if (expressInfoConfirmation.size()==0&&buyExpressList.size()==0){
            return outboundBatchesRecodes;
        }
        expressInfoConfirmation.addAll(buyExpressList);
        Map<String, List<Express>> batchkvExpress = expressInfoConfirmation.stream().collect(Collectors.toMap(Express::getBatchNo, p -> {
                    List<Express> expressList = new ArrayList<>();
                    expressList.add(p);
                    return expressList;
                }, (List<Express> v1, List<Express> v2) -> {
                    v1.addAll(v2);
                    return v1;
                }
        ));
        List<String> batchNos = expressInfoConfirmation.stream().map(Express::getBatchNo).distinct().collect(Collectors.toList());
        List<OutboundBatchesRecode> allByBatchNoList = outboundBatchesRecodeMapper.findAllByBatchNoList(batchNos);
        allByBatchNoList = allByBatchNoList.stream().filter(x -> {
            if (x.getAuditStatus()!=null && x.getAuditStatus().equals(CommonConstants.STATUS_1)) {
                return false;
            } else {
                return true;
            }
        }).collect(Collectors.toList());
        for (int i1 = 0; i1 < allByBatchNoList.size(); i1++) {
            OutboundBatchesRecode outboundBatchesRecode = allByBatchNoList.get(i1);
            String batchNo = outboundBatchesRecode.getBatchNo();

            //获取批次下不同商品
            CurrentUser curr_user = CurrentUser.getCurrentUser();
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderId(saleOrderId);
            // 获取订单详情
            saleorder.setCompanyId(curr_user.getCompanyId());
            saleorder.setBussinessType(2);

            List<BatchExpressVo> batchExpressByIds = new ArrayList<>();

            //判断直发普发
            if(outboundBatchesRecode.getBatchType().equals(1)){
                List<WarehouseGoodsOperateLog> wgOlistByComments = warehouseOutService.getWGOlistByComments(batchNo,saleOrderId);
                if (wgOlistByComments.size()==0) {
                    continue;
                }
                //物流单号
                List<Express> expressList = batchkvExpress.get(batchNo).stream().collect(Collectors.toList());
                List<String> expressLogiscsNo = expressList.stream().map(Express::getLogisticsNo).collect(Collectors.toList());
                String logiscsNos = String.join(",", expressLogiscsNo);

                saleorder.setBatchNoComments(batchNo);
                // 普发出库记录清单
                List<WarehouseGoodsOperateLog> warehouseOutList = warehouseOutService.getOutDetil(saleorder);
                outboundBatchesRecode.setExpressList(expressList);

                List<String> timeArrayWl = new ArrayList<>();
                List<String> checkSku = new ArrayList<>();
                //遍历处理普发出库记录
                if (null != warehouseOutList) {
                    Map<String, Integer> skuNum = warehouseOutList.stream().collect(Collectors.toMap(WarehouseGoodsOperateLog::getSku, WarehouseGoodsOperateLog::getRealGoodsNum, (v1, v2) -> v1 + v2));

                    for (WarehouseGoodsOperateLog wl : warehouseOutList) {
                        timeArrayWl.add(DateUtil.convertString(wl.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
                        outboundBatchesRecode.setBatchTime(DateUtil.convertString(wl.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
                        BatchExpressVo batchExpressVo = new BatchExpressVo();
                        if (checkSku.contains(wl.getSku())){
                            continue;
                        }
                        batchExpressVo.setNum(skuNum.get(wl.getSku()));
                        batchExpressVo.setSku(wl.getSku());
                        batchExpressVo.setModel(wl.getModel());
                        batchExpressVo.setBrand(wl.getBrandName());
                        batchExpressVo.setGoodsName(wl.getGoodsName());
                        batchExpressVo.setLogisticsOrderNo(logiscsNos);
                        batchExpressByIds.add(batchExpressVo);
                        checkSku.add(wl.getSku());

                    }
                }
            }else {
                directBatchNos(saleOrderId, outboundBatchesRecode, batchExpressByIds);

            }

            outboundBatchesRecode.setBatchExpressVos(batchExpressByIds);
        }
        return allByBatchNoList;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editExpressIntercepted(List<Express> epList) {
        //epList更新已拦截,拦截时间
        epList.forEach(x->{
            expressMapper.updateByPrimaryKeySelective(x);
            //log.info日志
            String msg = "更新快递为"+x.getExpressId()+"的物流单已拦截,拦截时间为"+DateUtil.convertString(x.getInterceptTime(),"yyyy-MM-dd HH:mm:ss");
            logger.info(msg);
        });
        return true;
    }

    @Override
    public Integer getSendedNum(Integer buyOrderGoodsId) {
        Integer sendedNum = expressMapper.getSendedNum(buyOrderGoodsId);
        return sendedNum;
    }

    @Override
    public SyncExpressDetailDto getDetail(Integer buyorderId, String logisticsNo, String sku) {
        SyncExpressDetailDto detailDto = expressMapper.getDetail(buyorderId, logisticsNo, sku);
        return detailDto;
    }

    @Override
    public SyncExpressDetailDto getDetailByNo(String buyOrderNo, String logisticsNo) {
        SyncExpressDetailDto detailDto = expressMapper.getExpressByNo(buyOrderNo, logisticsNo);
        return detailDto;
    }

    /**
     * 生成普发批次号
     *
     * @param express
     */
    private void generateNormalBatchNO(Express express, Integer relateId) {
        //判断BATCH_NO和WMS_ORDER_NO是否为空
        String batchNo = express.getBatchNo();
        try {
            String wmsOrderNo = express.getWmsOrderNo();
            String normalNo = StringUtils.isNotEmpty(wmsOrderNo) && wmsOrderNo.indexOf(OutBoundBatchConstant.NORMAL_PREFIX) > -1 ? OutBoundBatchConstant.NORMAL_PREFIX + wmsOrderNo.split(OutBoundBatchConstant.NORMAL_PREFIX)[1] : "";
            if (StringUtils.isEmpty(batchNo)) {
                if (StringUtils.isEmpty(normalNo) && relateId != null) {
                    WarehouseGoodsOperateLog warehouseGoodsOperateLog = warehouseGoodsOperateLogMapper.getAddTimeByRelatedId(relateId);
                    if (warehouseGoodsOperateLog != null) {
                        wmsOrderNo = warehouseGoodsOperateLog.getComments();

                        if (StringUtils.isNotEmpty(wmsOrderNo)) {
                            express.setWmsOrderNo(wmsOrderNo);
                        }
                        normalNo = StringUtils.isNotEmpty(wmsOrderNo) && wmsOrderNo.indexOf(OutBoundBatchConstant.NORMAL_PREFIX) > -1 ? OutBoundBatchConstant.NORMAL_PREFIX + wmsOrderNo.split(OutBoundBatchConstant.NORMAL_PREFIX)[1] : "";
                        normalNo = StringUtils.isEmpty(normalNo) ? OutBoundBatchConstant.NORMAL_PREFIX + warehouseGoodsOperateLog.getAddTime() : normalNo;
                    }
                }
                express.setBatchNo(normalNo.trim());
            }
            //批次表新增或删除批次
            OutboundBatchesRecode outboundBatchesRecode = new OutboundBatchesRecode();
            outboundBatchesRecode.setBatchType(Constants.ONE);
            insertOrUpdateOutboundBatches(express, outboundBatchesRecode);

        } catch (Exception e) {
            logger.error("新建批次无物流关系失败", e);
        }

    }

    /**
     * 生成直发批次号
     *
     * @param express
     */
    private void generateDirectBatchNO(Express express) {
        Long deliveryTime = express.getDeliveryTime();
        Integer buyorderId = express.getBuyorderId();
        if (buyorderId == null||deliveryTime==null) {
            return;
        }
        Buyorder buy = buyorderMapper.selectByPrimaryKey(buyorderId);
        //判断是否为直发，否直接跳出
        if (buy != null && !Constants.ONE.equals(buy.getDeliveryDirect())) {
            return;
        }
        //直发出库批次号
        String delivery = DateUtil.convertString(deliveryTime, DateUtil.DATE_FORMAT_NO);
        String directNo = OutBoundBatchConstant.DIRECT_PREFIX + buyorderId + delivery;
        if (express.getExpressId() == null || express.getExpressId() == 0) {
            express.setBatchNo(directNo);
        } else {
            Express expressInfoById = expressMapper.getExpressInfoById(express.getExpressId());
            String batchNo = expressInfoById.getBatchNo();
            //如果已有批次号，则保持原有批次号
            express.setBatchNo(StringUtils.isEmpty(batchNo)?directNo:batchNo);
        }
        //批次表新增或删除批次
        OutboundBatchesRecode outboundBatchesRecode = new OutboundBatchesRecode();
        outboundBatchesRecode.setBatchType(Constants.TWO);
        insertOrUpdateOutboundBatches(express,outboundBatchesRecode);
    }

    /**
     * 出库批次表新建批次
     *
     * @param express
     */
    private void insertOrUpdateOutboundBatches(Express express,OutboundBatchesRecode outboundBatchesRecode) {
        try {
            if (StringUtils.isNotEmpty(express.getBatchNo())) {
                outboundBatchesRecode.setBatchNo(express.getBatchNo());
                outboundBatchesRecode.setDeliveryTime(express.getDeliveryTime());
                outboundBatchesRecode.setCreator(express.getCreator());
                outboundBatchesRecode.setUpdater(express.getUpdater());
                outboundBatchesRecode.setAddTime(DateUtil.sysTimeMillis());
                outboundBatchesRecode.setModTime(DateUtil.sysTimeMillis());
                //新增批次表
                outboundBatchesRecodeMapper.insertOrUpdate(outboundBatchesRecode);
            }

        } catch (Exception e) {
            logger.error("新建批次无物流关系失败", e);
        }
    }

    @Override
    public Express getExpressByLogisticsNoAndBuyOrderNo(String logisticsNo, String buyOrderNo) {
        logger.info("根据快递单号和采购单号查询快递信息，快递单号: {}, 采购单号: {}", logisticsNo, buyOrderNo);

        try {
            Express express = expressMapper.getExpressByLogisticsNoAndBuyOrderNo(logisticsNo, buyOrderNo);
            if (express != null) {
                logger.info("查询到快递信息，快递ID: {}, 快递单号: {}, 采购单号: {}",
                           express.getExpressId(), logisticsNo, buyOrderNo);
            } else {
                logger.info("未查询到快递信息，快递单号: {}, 采购单号: {}", logisticsNo, buyOrderNo);
            }
            return express;
        } catch (Exception e) {
            logger.info("查询快递信息异常，快递单号: {}, 采购单号: {}", logisticsNo, buyOrderNo, e);
            throw new RuntimeException("查询快递信息失败: " + e.getMessage(), e);
        }
    }
}
