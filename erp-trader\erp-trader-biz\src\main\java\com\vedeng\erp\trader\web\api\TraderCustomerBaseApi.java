package com.vedeng.erp.trader.web.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.dto.SelectDto;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.dto.D3ResultDto;
import com.vedeng.erp.trader.domain.dto.*;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.dto.TraderCustomerDto;
import com.vedeng.erp.trader.dto.*;
import com.vedeng.erp.trader.service.RSalesJTraderApiService;
import com.vedeng.erp.trader.service.RSalesJTraderService;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 客户信息
 * @date 2022/7/15 10:51
 **/
@ExceptionController
@RestController
@RequestMapping("/traderCustomerBase")
@Slf4j
public class TraderCustomerBaseApi {

    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    @Autowired
    private RSalesJTraderService salesJTraderService;

    @Autowired
    private RSalesJTraderApiService rSalesJTraderApiService;

    /**
     * 更具名字模糊查询
     * @param limit 限制条数
     * @param name 客户名
     * @return 信息
     */
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> queryTraderCustomerInfoByName(@RequestParam(value = "limit", required = false,defaultValue = "15") Integer limit, String name) {
        if(StringUtils.isBlank(name)){
            return R.success();
        }
        name=name.trim().replaceAll(" ","%");
        List<TraderCustomerInfoVo> list= traderCustomerBaseService.getTraderCustomerByTraderName(name,0,20);
        List<SelectDto> result = list.stream().map(sku -> {
            SelectDto map = new SelectDto(sku.getTraderId(),sku.getTraderName());
            return map;
        }).collect(Collectors.toList());
        return R.success(result);
    }
    @RequestMapping(value = "/queryWithDefault", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> queryTraderCustomerInfoByNameDefault(@RequestParam(value = "limit", required = false,defaultValue = "15") Integer limit, String name) {
        if(StringUtils.isNotBlank(name)){
            name=name.trim().replaceAll(" ","%");
        }
        List<TraderCustomerInfoVo> list= traderCustomerBaseService.getTraderCustomerByTraderNameDefault(name,0,limit);
        List<SelectDto> result = list.stream().map(sku -> {
            SelectDto map = new SelectDto(sku.getTraderId(),sku.getTraderName());
            return map;
        }).collect(Collectors.toList());
        return R.success(result);
    }

    /**
     * 获取客户详情信息
     * @param traderId 客户id
     * @return 信息
     */
    @RequestMapping(value = "/getTraderCustomerInfo",method = RequestMethod.POST)
    public R<?> getTraderCustomerInfoVoById(Integer traderId) {
        return R.success(traderCustomerBaseService.getTraderCustomerInfoVo(traderId));
    }

    /**
     * 获取客户详情信息
     * @param traderId 客户id
     * @return 信息
     */
    @RequestMapping(value = "/getCustomerBaseInfo",method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> getCustomerBaseInfo(Integer traderId) {
        return R.success(traderCustomerBaseService.getTraderCustomerInfo(traderId));
    }

    /**
     * 获取客户360信息
     *
     * @param traderId 客户id
     * @return TraderCustomerPortraitDto
     */
    @RequestMapping(value = "/get/portrait")
    @NoNeedAccessAuthorization
    public R<TraderCustomerPortraitDto> getTraderCustomerPortrait(@RequestParam Integer traderId) {
        return R.success(traderCustomerBaseService.getTraderCustomerPortrait(traderId));
    }

    /**
     * 查询当前客户的关联客户信息
     *
     * @param traderId traderId
     * @return node节点信息
     */
    @RequestMapping(value = "/get/customer/relation")
    @NoNeedAccessAuthorization
    public R<?> getTraderRelationInfo(@RequestParam Integer traderId) {
        return R.success(traderCustomerBaseService.handleRelatedCustomerInfo(traderId));
    }

    /**
     * 查询大数据终端信息
     * @param name 查询条件
     * @param pageSize 页大小
     * @param traderId -1 默认 1 已选择打标
     * @param pageNum 页码
     * @return R<List<TraderCustomerTerminal>>
     */
    @RequestMapping(value = "/queryTerminalData")
    @NoNeedAccessAuthorization
    public R<PageInfo<TraderCustomerTerminal>> getTerminalDetail(String name,@RequestParam(defaultValue = "-1") Integer traderId, @RequestParam(defaultValue = "10") Integer pageSize, @RequestParam(defaultValue = "1") Integer pageNum) {
        return R.success(traderCustomerBaseService.getTerminalDetail(name,traderId, pageSize, pageNum));
    }

    @RequestMapping(value = "/searchTyc")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<PageInfo<TraderCustomerTerminal>> searchTycTerminal(String name,@RequestParam(defaultValue = "-1") Integer traderId, @RequestParam(defaultValue = "10") Integer pageSize, @RequestParam(defaultValue = "1") Integer pageNum) {
        return R.success(traderCustomerBaseService.getTycTerminalInfo(name,traderId, pageSize, pageNum));
    }

    /**
     * 用户行为轨迹
     *
     * @param traderId traderId
     * @return R
     */
    @RequestMapping(value = "/behavior/trace")
    @NoNeedAccessAuthorization
    public R<?> getCustomerBehaviorTrace(@RequestParam Integer traderId) {
        return R.success(traderCustomerBaseService.getCustomerBehaviorTrace(traderId));
    }

    /**
     * 用户档案（事件）
     * @param traderId
     * @return
     */
    @RequestMapping(value = "/archive/trace")
    @NoNeedAccessAuthorization
    public R<?> getCustomerArchiveTrace(@RequestParam Integer traderId,@RequestParam String archiveCursor,@RequestParam(required = false) List<Long> archiveIds) {
        return R.success(traderCustomerBaseService.getCustomerArchiveTrace(traderId,archiveCursor,archiveIds));
    }

    /**
     * 查询经销链路列表信息
     *
     * @param pageParam 分页查询参数
     * @return List<DistributionLinkDto>
     */
    @RequestMapping(value = "/distributionLink/page")
    @NoNeedAccessAuthorization
    public R<PageInfo<DistributionLinkDto>> searchDistributionLinkPage(@RequestBody PageParam<DistributionLinkDto> pageParam) {
        return R.success(traderCustomerBaseService.searchDistributionLinkPage(pageParam));
    }

    /**
     * 查询并组装关系图信息（d3）
     *
     * @param traderId             当前traderId
     * @param linkSourceType       链路来源     0:全部 1:招投标 2:贝登交易数据 3:终端建链
     * @param cooperationTimeFrame 合作时间范围 1近一年 2近两年 3近三年
     * @return D3ResultDto
     */
    @RequestMapping(value = "/distributionLink/d3")
    @NoNeedAccessAuthorization
    public R<D3ResultDto> searchDistributionLinkD3(@RequestParam Integer traderId, @RequestParam Integer linkSourceType, @RequestParam Integer cooperationTimeFrame) {
        PageParam<DistributionLinkDto> pageParam = new PageParam<>();
        pageParam.setPageSize(20);
        pageParam.setPageNum(1);
        DistributionLinkDto param = new DistributionLinkDto();
        param.setTraderId(traderId);
        param.setLinkSourceType(linkSourceType);
        param.setCooperationTimeFrame(cooperationTimeFrame);
        pageParam.setParam(param);
        return R.success(traderCustomerBaseService.searchDistributionLinkD3(pageParam));
    }

    /**
     * 查询大数据CPM计算标签
     *
     * @param traderId traderId
     * @return TraderPerfectTagDto
     */
    @RequestMapping(value = "/cpm")
    @NoNeedAccessAuthorization
    public R<TraderPerfectTagDto> getTraderCpm(@RequestParam Integer traderId) {
        return R.success(traderCustomerBaseService.getTraderCpmTag(traderId));
    }

    /**
     * 分页查询客户信息（同新增订单页面逻辑）
     *
     * @param pageParam 分页查询参数
     * @return TraderCustomerDto
     */
    @RequestMapping(value = "/page")
    @NoNeedAccessAuthorization
    public R<PageInfo<TraderCustomerDto>> getTraderCustomerPage(@RequestBody PageParam<TraderCustomerDto> pageParam) {
        return R.success(traderCustomerBaseService.getTraderCustomerPage(pageParam));
    }

    /**
     * 查询客户列表返回给贝壳助理
     */
    @RequestMapping(value = "/traderListForSmartQuote")
    @NoNeedAccessAuthorization
    public R<?> getTraderListForSmartQuote(String traderName,
                                           Integer userId,
                                           @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                           @RequestParam(required = false, defaultValue = "5")Integer pageSize){
        PageParam<TraderForSmartQuoteDto> pageParam = new PageParam<>();
        pageParam.setPageNum(pageNo);
        pageParam.setPageSize(pageSize);
        return traderCustomerBaseService.getTraderListForSmartQuote(traderName,userId,pageParam);
    }

    /**
     * 保存客户分享信息
     *
     * @param salesJoinTraderDto RSalesJTraderDto
     * @return R<?>
     */
    @RequestMapping("/saveShareTrader")
    @NoRepeatSubmit
    @NoNeedAccessAuthorization
    public R<?> saveShareTrader(@RequestBody RSalesJTraderDto salesJoinTraderDto) {
        salesJTraderService.saveTraderShare(salesJoinTraderDto);
        return R.success();
    }

    /**
     * 查询当前客户的分享记录
     *
     * @param traderId traderId
     * @return List<RSalesJTraderDto>
     */
    @RequestMapping("/getShareTraderList")
    @NoNeedAccessAuthorization
    public R<List<RSalesJTraderDto>> getShareTraderList(@RequestParam Integer traderId) {
        return R.success(rSalesJTraderApiService.getShareTraderList(traderId));
    }

    /**
     * 取消分享
     *
     * @param id id
     * @return R<?>
     */
    @RequestMapping("/saveCancelShare")
    @NoRepeatSubmit
    @NoNeedAccessAuthorization
    public R<?> saveCancelShare(@RequestParam Integer id) {
        int result = salesJTraderService.saveCancelShare(id);
        if(result >0){
            return R.success();
        }
        return R.error("该分享已取消，请刷新页面后查看");

    }
    /**
     * 取消分享
     *
     * @param ids ids
     * @return R<?>
     */
    @RequestMapping("/saveCancelShareByIds")
    @NoRepeatSubmit
    @NoNeedAccessAuthorization
    public R<?> saveCancelShareByIds(@RequestParam String ids) {
        List<Integer> idList =  Arrays.stream(ids.split(","))
                .map(Integer::parseInt) // 将字符串转换为整数
                .collect(Collectors.toList()); // 收集为列表
        int result = 0;
        for(Integer id:idList){
            int r = salesJTraderService.saveCancelShare(id);
            if(r>0){
                result ++;
            }
        }
        if(result >0){
            return R.success("共取消分享了"+result+"条记录");
        }
        return R.error("该分享已取消，请刷新页面后查看");

    }

    /**
     * 批量分享
     *
     * @param
     * @return R<?>
     */
    @RequestMapping("/batchSaveShare")
    @NoRepeatSubmit
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<?> batchSaveShare(@RequestParam Integer USERID,@RequestParam String USERNAME,@RequestParam Integer belongSalesId) {
        if(USERID == belongSalesId){
            return R.error("不能分享给自己");
        }
         TraderBatchShareDto traderBatchShareDto = new TraderBatchShareDto();
         traderBatchShareDto.setBelongSalesId(belongSalesId);
         traderBatchShareDto.setUSERID(USERID);
         traderBatchShareDto.setUSERNAME(USERNAME);
        int result = salesJTraderService.saveBatchShare(traderBatchShareDto);
        if(result>0){
            log.info("客户批量分享操作,{},分享结果：{}", JSONObject.toJSONString(traderBatchShareDto),result);
            R r = R.success();
            r.setData("reload");
            r.setMessage("共批量分享了"+result+"个客户");
            return r;
        }else{
            return R.error("所有客户都已分享，无需重复操作");
        }

    }

    /**
     * 查询分享人的所有列表
     *
     * @param
     * @return R<?>
     */
    @RequestMapping("/queryShardUserList")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<?> queryShardUserList(@RequestParam Integer userId) {
        List<Map<String,Object>> resultList = salesJTraderService.queryShardUserList(userId);
        return R.success(resultList);

    }

    /**
     * 查询终端
     *
     * @param pageParam 分页查询参数
     * @return List<DistributionLinkDto>
     */
    @RequestMapping(value = "/terminal/page")
    @NoNeedAccessAuthorization
    public R<PageInfo<TerminalResponseDto>> searchTerminalPage(@RequestBody PageParam<TerminalRequestDto> pageParam) {
        log.info("查询终端searchTerminalPage:{}", JSON.toJSONString(pageParam));
        Integer userId = CurrentUser.getCurrentUser().getId();
        return R.success(traderCustomerBaseService.searchTerminalPage(pageParam,userId));
    }

    /**
     * 查询终端合作经销商
     *
     * @param pageParam 分页查询参数
     * @return List<DistributionLinkDto>
     */
    @RequestMapping(value = "/terminal/cooperationDistributionLink/page")
    @NoNeedAccessAuthorization
    public R<PageInfo<TerminalDistributionLinkResponseDto>> searchTerminaCooperationDistributionLinkPage(@RequestBody PageParam<TerminalDistributionLinkRequestDto> pageParam) {
        log.info("查询终端合作经销商searchTerminaCooperationDistributionLinkPage:{}", JSONObject.toJSONString(pageParam));
        Integer userId = CurrentUser.getCurrentUser().getId();
        PageInfo<TerminalDistributionLinkResponseDto> result = traderCustomerBaseService.searchTerminaCooperationDistributionLinkPage(pageParam, userId);
        return R.success(result);
    }

    /**
     * 查询终端区域
     *
     * @return Object[]
     */
    @RequestMapping(value = "/terminal/areaSearchList")
    @NoNeedAccessAuthorization
    public R<Object[]> terminalAreaSearchList() {
        Integer userId = CurrentUser.getCurrentUser().getId();
        return R.success(traderCustomerBaseService.terminalAreaSearchList(userId));
    }

    /**
     * 获取终端360信息
     *
     * @param searchName 终端名称
     * @return TraderCustomerPortraitDto
     */
    @RequestMapping(value = "/get/terminalPortrait")
    @NoNeedAccessAuthorization
    public R<TraderCustomerTerminalPortraitDto> getTraderCustomerTerminalPortrait(@RequestParam String searchName) {
        return R.success(traderCustomerBaseService.getTraderCustomerTerminalPortrait(searchName));
    }

    /**
     * 终端360详情查询并组装经销链路关系图信息（d3）
     *
     * @param searchName           终端名称
     * @param linkSourceType       链路来源 0全部 1中标数据 2贝登交易 3商机 4报价
     * @param cooperationTimeFrame 合作时间范围 1近一年 2近两年 3近三年
     * @return D3ResultDto
     */
    @RequestMapping(value = "/terminalDistributionLink/d3")
    @NoNeedAccessAuthorization
    public R<D3ResultDto> searchTerminalDistributionLinkD3(@RequestParam String searchName, @RequestParam Integer linkSourceType, @RequestParam Integer cooperationTimeFrame) {
        PageParam<DistributionLinkDto> pageParam = new PageParam<>();
        pageParam.setPageSize(20);
        pageParam.setPageNum(1);
        DistributionLinkDto param = new DistributionLinkDto();
        param.setSearchName(searchName);
        param.setLinkSourceType(linkSourceType);
        param.setCooperationTimeFrame(cooperationTimeFrame);
        pageParam.setParam(param);
        return R.success(traderCustomerBaseService.searchTerminalDistributionLinkD3(pageParam));
    }

    /**
     * 查询终端360经销链路列表信息
     *
     * @param pageParam 分页查询参数
     * @return List<DistributionLinkDto>
     */
    @RequestMapping(value = "/terminalDistributionLink/page")
    @NoNeedAccessAuthorization
    public R<PageInfo<DistributionLinkDto>> searchTerminalDistributionLinkPage(@RequestBody PageParam<DistributionLinkDto> pageParam) {
        return R.success(traderCustomerBaseService.searchTerminalDistributionLinkPage(pageParam));
    }

    /**
     * 终端批量派发拜访计划
     *
     * @param terminalResponseDtoList terminalResponseDtoList
     * @return ModelAndView
     */
    @RequestMapping("/distribute/visitPlan")
    @NoNeedAccessAuthorization
    public R<DistributionVisitPlanDto> getDistributeVisitPlan(@RequestBody List<TerminalResponseDto> terminalResponseDtoList) {
        log.info("终端批量派发拜访计划getDistributeVisitPlan:{}", JSONObject.toJSONString(terminalResponseDtoList));
        Integer userId = CurrentUser.getCurrentUser().getId();
        return R.success(traderCustomerBaseService.getDistributeVisitPlanList(terminalResponseDtoList,userId));
    }

    /**
     * 终端批量派发拜访计划提交接口
     *
     * @param submitVisitPlanDto submitVisitPlanDto
     * @return ModelAndView
     */
    @RequestMapping("/submit/visitPlan")
    @NoNeedAccessAuthorization
    public R<DistributionVisitPlanDto> submitVisitPlan(@RequestBody SubmitVisitPlanDto submitVisitPlanDto) {
        Integer userId = CurrentUser.getCurrentUser().getId();
        traderCustomerBaseService.submitVisitPlan(submitVisitPlanDto,userId);
        return R.success();
    }
}
