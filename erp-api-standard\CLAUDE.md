# CLAUDE.md - ERP API标准化模块

本文件为 `erp-api-standard` 模块提供 Claude Code 工作指南，帮助 AI 助手理解项目结构和开发规范。

## 项目概述

`erp-api-standard` 是 ERP 系统接口标准化改造的统一模块，通过适配器模式实现新旧系统的无缝对接，提供标准化的 API 接口框架。

### 核心设计理念
- **统一入口**：所有标准化 API 通过 `/api/v1/{module}/{action}` 统一访问
- **适配器模式**：通过 ServiceAdapter 适配现有业务 Controller
- **类型安全**：使用泛型和强类型 DTO 确保编译时类型安全
- **框架化开发**：提供 BusinessTemplate 消除重复代码

## 构建和测试命令

### 基础构建命令
```bash
# 构建模块
mvn clean compile

# 打包
mvn clean package

# 安装到本地仓库
mvn clean install

# 运行测试
mvn test

# 跳过测试构建
mvn clean install -DskipTests
```

### 与其他模块一起构建
```bash
# 在项目根目录构建整个项目
mvn clean install

# 构建当前模块及其依赖
mvn clean install -pl erp-api-standard -am
```

## 技术架构

### 核心组件层次结构

```
UnifiedApiController (统一入口)
    ↓
ServiceAdapterFactory (适配器工厂)
    ↓
ServiceAdapter (业务适配器)
    ↓
BusinessTemplate (业务模板框架)
    ↓
InternalHttpCallService (内部调用)
    ↓
现有Controller/Service (业务实现)
```

### 关键类结构

#### 核心框架类
- `UnifiedApiController`: 统一 API 入口控制器
- `AbstractServiceAdapter`: 服务适配器抽象基类
- `ServiceAdapterFactory`: 适配器工厂，负责路由请求
- `BusinessTemplate`: 业务执行模板，提供统一的业务流程

#### 请求响应类
- `ApiRequest`: 统一请求格式
- `ApiResponse<T>`: 统一响应格式
- `InternalCallRequest/Result`: 内部调用请求响应

#### 业务框架类
- `ApprovalExecutor`: 通用审核执行器
- `StandardValidator`: 标准验证框架
- `ResponseProcessor`: 响应处理器

### 包结构说明

```
com.vedeng.api.standard/
├── core/                    # 核心框架类
├── controller/              # 统一API控制器
├── adapter/                 # 业务适配器实现
│   ├── buyorder/           # 采购单适配器
│   ├── saleorder/          # 销售单适配器
│   └── ...                 # 其他业务模块适配器
├── template/               # 业务模板框架
├── approval/               # 通用审核框架
├── validation/             # 验证框架
├── converter/              # 响应转换框架
├── internal/               # 内部调用框架
├── factory/                # 工厂类
└── interceptor/            # 拦截器
```

## 开发指南

### 创建新的服务适配器

1. **继承AbstractServiceAdapter**：
```java
@Component("yourModuleServiceAdapter")
public class YourModuleServiceAdapter extends AbstractServiceAdapter {
    
    @Override
    protected void registerOperationHandlers() {
        registerThrowingHandler("create", this::executeCreateOperation);
        registerThrowingHandler("query", this::executeQueryOperation);
        // 注册其他操作...
    }
    
    @Override
    public String getModuleName() {
        return "yourmodule";
    }
}
```

2. **使用BusinessTemplate框架**：
```java
private Object executeCreateOperation(ApiRequest request) throws Exception {
    return businessTemplate.<YourRequest, YourResponse>executeCreate(request)
        .requestType(YourRequest.class)
        .responseType(YourResponse.class)
        .validationRules(YourValidationRule.class)
        .controller("yourController", "createMethod")
        .withHttpParameters(
            ParameterConfig.of(YourVo.class, yourVo)
        )
        .responseConfig(ResponseConfig.create("创建成功", "id"))
        .execute();
}
```

### DTO类设计规范

```java
// 请求DTO
public class YourCreateRequest {
    private String name;
    private Integer type;
    
    // JSR-303验证注解
    @NotBlank(message = "名称不能为空")
    public String getName() { return name; }
    
    // getters and setters...
}

// 响应DTO
public class YourCreateResponse {
    private Boolean success;
    private String message;
    private Integer id;
    
    // 工厂方法
    public static YourCreateResponse success(Integer id) {
        YourCreateResponse response = new YourCreateResponse();
        response.setSuccess(true);
        response.setMessage("创建成功");
        response.setId(id);
        return response;
    }
    
    // getters and setters...
}
```

### API接口规范

#### 路径规范
- **创建**: `POST /api/v1/{module}/create`
- **查询**: `GET /api/v1/{module}/query` 或 `POST /api/v1/{module}/query`
- **详情**: `GET /api/v1/{module}/detail`
- **更新**: `POST /api/v1/{module}/update`
- **删除**: `POST /api/v1/{module}/delete`
- **提交审核**: `POST /api/v1/{module}/submit`
- **审核**: `POST /api/v1/{module}/approve`

#### 请求格式
```json
{
    "字段名": "字段值",
    "数值字段": 123,
    "布尔字段": true
}
```

#### 响应格式
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "id": 123,
        "result": "具体数据"
    },
    "requestId": "req-uuid",
    "timestamp": 1640995200000
}
```

## 验证和审核框架

### 验证规则开发
```java
@Component
public class YourValidationRule implements ValidationRule<YourRequest> {
    
    @Override
    public ValidationResult validate(YourRequest request, Map<String, Object> context) {
        // 执行验证逻辑
        if (/* 验证失败条件 */) {
            return ValidationResult.failure(getRuleName(), "验证失败消息");
        }
        
        // 将查询到的数据存储到上下文中供后续使用
        context.put(ValidationContextKeys.YOUR_DATA, yourData);
        
        return ValidationResult.success(getRuleName());
    }
    
    @Override
    public String getRuleName() {
        return "YourValidationRule";
    }
}
```

### 审核框架使用
```java
// 让请求类实现ApprovalRequest接口
public class YourApprovalRequest implements ApprovalRequest {
    private String taskId;
    private String comment;
    // 其他业务字段...
    
    // ApprovalRequest接口方法
    @Override
    public String getTaskId() { return taskId; }
    @Override
    public void setTaskId(String taskId) { this.taskId = taskId; }
    @Override
    public String getComment() { return comment; }
    @Override
    public void setComment(String comment) { this.comment = comment; }
}

// 在ServiceAdapter中使用审核框架
@Autowired
private ApprovalExecutor approvalExecutor;

private ApprovalResult executeApproveOperation(ApiRequest request) throws Exception {
    YourApprovalRequest approvalRequest = dataConverter.convert(request.getData(), YourApprovalRequest.class);
    
    ApprovalConfig<YourApprovalRequest, YourApprovalResponse> config = ApprovalConfig
        .<YourApprovalRequest, YourApprovalResponse>builder()
        .requestType(YourApprovalRequest.class)
        .responseType(YourApprovalResponse.class)
        .controller("yourController", "approveMethod")
        .validationRules(YourExistsRule.class, YourStatusRule.class)
        .responseConfig(ResponseConfig.approval("审核成功"))
        .build();
    
    // 推荐使用循环审核，自动完成整个审核流程
    return approvalExecutor.executeMultiStepApproval(request, approvalRequest, config);
}
```

## 测试指南

### 单元测试结构
```java
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:META-INF/spring/api-standard-context.xml"})
public class YourModuleServiceAdapterTest {
    
    @Autowired
    private YourModuleServiceAdapter adapter;
    
    @Test
    public void testCreateOperation() {
        // 准备测试数据
        ApiRequest request = createTestRequest();
        
        // 执行测试
        Object result = adapter.execute("create", request);
        
        // 验证结果
        assertNotNull(result);
        // 更多断言...
    }
}
```

### 集成测试
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=YourModuleServiceAdapterTest

# 运行测试并生成报告
mvn test jacoco:report
```

## 依赖说明

### 核心依赖
- **Spring Framework 4.1.9**: 与现有ERP系统保持一致
- **Jackson 2.6.x**: JSON序列化/反序列化
- **Servlet API 3.1**: Web容器支持

### ERP模块依赖
- `erp-system-api`: 系统基础API
- `erp-buyorder-api/biz`: 采购单模块（示例适配器）
- `erp-saleorder-api/biz`: 销售单模块（示例适配器）
- `erp-old`: 兼容旧系统

## 最佳实践

### 代码规范
1. **类命名**：
   - ServiceAdapter: `{Module}ServiceAdapter`
   - Request DTO: `{Module}{Action}Request`
   - Response DTO: `{Module}{Action}Response`

2. **包结构**：
   - 适配器类放在 `adapter.{module}` 包下
   - DTO类放在 `adapter.{module}.dto` 包下
   - 转换器类放在 `adapter.{module}.converter` 包下

3. **异常处理**：
   - 使用标准异常类：`BusinessException`, `ValidationException`
   - 提供清晰的错误消息
   - 记录适当的日志级别

### 性能优化
1. **参数传递优化**：使用验证上下文避免重复查询
2. **响应缓存**：对查询操作使用合适的缓存策略
3. **异步处理**：对耗时操作考虑异步执行

### 安全考虑
1. **参数验证**：使用JSR-303注解和自定义验证规则
2. **权限检查**：在验证规则中实现权限控制
3. **数据脱敏**：在响应处理中对敏感数据脱敏

## 故障排除

### 常见问题
1. **Bean未找到**：检查Spring配置文件导入和组件扫描路径
2. **内部调用失败**：验证Controller Bean名称和方法签名
3. **参数转换错误**：检查DTO类字段类型和JSON格式
4. **审核流程异常**：确认工作流配置和审核人设置

### 调试技巧
```java
// 启用详细日志
logger.debug("执行操作: {}, 请求数据: {}", action, request.getData());

// 检查内部调用结果
if (!callResult.isSuccess()) {
    logger.error("内部调用失败: {}", callResult.getErrorMessage());
}
```

## 扩展指南

### 添加新业务模块
1. 在 `adapter` 包下创建新的业务包
2. 实现对应的 ServiceAdapter
3. 创建相应的 DTO 类和转换器
4. 编写单元测试
5. 更新文档

### 自定义验证规则
1. 实现 `ValidationRule<T>` 接口
2. 使用 `@Component` 注解注册为Spring Bean
3. 在 BusinessTemplate 中引用验证规则

### 扩展响应格式
1. 继承或扩展 `ResponseProcessor`
2. 自定义 `ResponseMappingConfig`
3. 在 Spring 配置中注册自定义处理器

---

**注意**：本模块专注于提供标准化的API框架，业务逻辑实现仍在各自的业务模块中。开发时应遵循单一职责原则，保持框架的通用性和可扩展性。