<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:apollo="http://www.ctrip.com/schema/apollo" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/mvc
		http://www.springframework.org/schema/mvc/spring-mvc.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://www.ctrip.com/schema/apollo http://www.ctrip.com/schema/apollo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <apollo:config namespaces="application,vedeng-sso-client,ezadmin"/>

    <mvc:resources mapping="/static/**" location="/static/,classpath:/static/ezadmin/" cache-period="864000"/>
    <mvc:resources mapping="/webjars/**" location="classpath:/META-INF/resources/webjars/" cache-period="864000"/>
    <mvc:resources mapping="/public/**" location="classpath:/webjars/ezadmin/" cache-period="864000"/>

    <!-- 自动扫描 -->
    <context:component-scan base-package="com.vedeng">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>

    <context:component-scan base-package="com.**.service,com.**.cache,com.rabbitmq"/>
    <context:component-scan base-package="com.**.rabbitmq"/>
    <context:component-scan base-package="com.common"/>

    <!-- 注解shiro扫描器 -->
    <context:component-scan base-package="com.vedeng.common.shiro"/>
    <context:component-scan base-package="com.vedeng.kpi"/>

    <!-- 开启切面支持 -->
    <aop:aspectj-autoproxy proxy-target-class="true" expose-proxy="true"/>

    <import resource="classpath:spring-mybatis.xml"/>
    <!-- 引入 activiti 流程模块配置文件 -->
    <import resource="classpath:activiti.cfg.xml"/>

    <import resource="classpath:shiro/shiro-redis.xml"/>
    <import resource="classpath:shiro/shiro-cas.xml"/>
    <import resource="classpath:shiro/spring-shiro.xml"/>

    <!-- 报表数据库  -->
    <import resource="classpath:spring-db-report.xml"/>
    <import resource="classpath:spring-db-canal.xml"/>
    <import resource="classpath:spring-db-dwh.xml"/>

    <!-- DDI数据库 -->
    <import resource="classpath:spring-ddi-collect.xml"/>
    
    <!-- 群到款播报 -->
    <import resource="classpath:spring-db-brodecast-mycat.xml"/>

    <!-- 接口soap  -->
    <import resource="classpath:spring-soap.xml"/>

    <import resource="classpath:ftpClientPool.xml"/>
    <import resource="classpath:wmsFtpClientPool.xml"/>


    <bean id="redisUtils" class="com.vedeng.common.redis.RedisUtils"/>

    <!-- 站内信 -->
    <bean id="messageUtil" class="com.vedeng.common.util.MessageUtil">
        <property name="websocketUrl" value="${websocket_url}"/>
    </bean>
    <bean class="com.vedeng.common.util.LogbackAutoConfig">
        <!-- 日志控制台打印append名称，默认：consoleLog -->
        <property name="consoleAppenderName" value="stdout"></property>
    </bean>

    <bean id="javaMailSender"
          class="org.springframework.mail.javamail.JavaMailSenderImpl">
        <property name="protocol" value="${email_protocol}"/>
        <property name="host" value="${email_host}"/>
        <property name="port" value="25"/>
        <property name="username" value="${email_username}"/>
        <property name="password" value="${email_password}"/>
        <property name="defaultEncoding" value="UTF-8"></property>
        <property name="javaMailProperties">
            <props>
                <prop key="mail.auth">true</prop>
                <prop key="mail.smtp.timeout">3000</prop>
            </props>
        </property>
    </bean>

    <bean id="simpleMailMessage" class="org.springframework.mail.SimpleMailMessage">
        <!-- 发件人email -->
        <property name="from" value="${email_username}"/>
        <!--收件人email-->
        <property name="to" value="<EMAIL>"/>
        <!--email主题(标题)-->
        <property name="subject" value="test"/>
        <!--email主题内容-->
        <property name="text">
            <value>test</value>
        </property>
    </bean>
    <bean id="sqlMonitorPluginConfig" class="com.vedeng.core.sqlmonitor.config.SqlMonitorPluginConfig" />

    <import resource="classpath:spring-newtask.xml"/>
    <import resource="spring-rabbit-context.xml"/>
    <import resource="spring-rabbit-im.xml"/>
    <import resource="spring-rabbit-hcorderconsumer.xml"/>
    <import resource="spring-rabbit-erp.xml"/>
    <import resource="spring-rabbit-op.xml"/>
    <import resource="spring-rabbit-base.xml"/>
    <import resource="spring-rabbit-ml.xml"/>
    <import resource="spring-rabbit-kingdee.xml"/>
    <import resource="kingdee-spring-mybatis.xml"/>

    <!-- API标准化模块配置 -->
    <import resource="classpath:META-INF/spring/api-standard-context.xml"/>

</beans>
