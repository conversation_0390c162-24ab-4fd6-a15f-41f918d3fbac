package com.vedeng.erp.broadcast.domain.entity;

/**
 * T_BROADCAST_STATISTIC_AED_NUM
 */
public class BroadcastStatisticAedNumWithBLOBs extends BroadcastStatisticAedNum {
    /**
     * <pre>
     * 计算条件快照
     * 表字段 : T_BROADCAST_STATISTIC_AED_NUM.CONDITION_TEXT
     * </pre>
     * 
     */
    private String conditionText;

    /**
     * 计算条件快照
     * @return CONDITION_TEXT 计算条件快照
     */
    public String getConditionText() {
        return conditionText;
    }

    /**
     * 计算条件快照
     * @param conditionText 计算条件快照
     */
    public void setConditionText(String conditionText) {
        this.conditionText = conditionText == null ? null : conditionText.trim();
    }
}