package com.vedeng.aftersales.service.impl;

import com.google.common.base.Splitter;
import com.vedeng.aftersales.component.dto.AfterSaleServiceLabelDto;
import com.vedeng.aftersales.constant.AfterSaleServiceLevelEnum;
import com.vedeng.aftersales.constant.SkuSubTypeEnum;
import com.vedeng.aftersales.dao.*;
import com.vedeng.aftersales.model.*;
import com.vedeng.aftersales.model.dto.*;
import com.vedeng.aftersales.model.vo.ApprovedSkuAfterSalesInfoVO;
import com.vedeng.aftersales.service.AfterSaleServiceStandardService;
import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.dao.CoreSpuGenerateExtendMapper;
import com.vedeng.goods.manager.constants.GoodsConstants;
import com.vedeng.goods.manager.extension.handler.InternalAfterSalePolicyTodoHandler;
import com.vedeng.goods.manager.extension.handler.SupplierAfterSalePolicyTodoHandler;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.dto.CoreSkuBaseDTO;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.vedeng.common.util.DateUtil.TIME_FORMAT;
import static com.vedeng.common.util.DateUtil.logger;

@Service
public class AfterSaleServiceStandardServiceImpl implements AfterSaleServiceStandardService, InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(AfterSaleServiceStandardServiceImpl.class);

    public static final String SPLIT_CHARACTOR = ",";

    private static final Integer HAS_DONE_SUPPLIER_POLICY = 1;

    @Value("${oss_url}")
    protected String domain;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Resource
    private AfterSaleSupplyPolicyMapper afterSaleSupplyPolicyMapper;

    @Resource
    private AfterSaleSupplyPolicyAttachmentMapper afterSaleSupplyPolicyAttachmentMapper;

    @Resource
    private AfterSaleServiceStandardInfoMapper afterSaleServiceStandardInfoMapper;

    @Resource
    private AfterSaleServiceStandardInfoAttachmentMapper afterSaleServiceStandardInfoAttachmentMapper;

    @Resource
    private AfterSaleServiceStandardInfoInstallAreaMapper afterSaleServiceStandardInfoInstallAreaMapper;

    @Resource
    private AfterSaleServiceStandardApplyMapper afterSaleServiceStandardApplyMapper;

    @Resource
    private AfterSaleServiceStandardApplyAttachmentMapper afterSaleServiceStandardApplyAttachmentMapper;

    @Resource
    private AfterSaleServiceStandardApplyInstallAreaMapper afterSaleServiceStandardApplyInstallAreaMapper;

    @Resource
    private TraderMapper traderMapper;

    @Resource
    private SupplierAfterSalePolicyTodoHandler supplierAfterSalePolicyTodoHandler;
    @Resource
    private InternalAfterSalePolicyTodoHandler internalAfterSalePolicyTodoHandler;

    private BeanInfo afterSaleSupplyPolicyDtoBeanInfo;

    private BeanInfo afterSaleSupplyPolicyBeanInfo;

    @Resource
    private AfterSaleServiceLabelMapper afterSaleServiceLabelMapper;



    @Override
    public List<AfterSaleServiceStandardApplyDto> querylistPage(AfterSaleServiceStandardApplyQueryDto serviceStandardDto, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("serviceStandardDto", serviceStandardDto);
        map.put("page", page);
        return afterSaleServiceStandardApplyMapper.querylistPage(map);
    }

    @Override
    public List<AfterSaleServiceStandardApplyDto> querySimilarProductListPage(ReferenceSimilarProductQueryDto queryDto, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("queryDto", queryDto);
        map.put("page", page);
        return afterSaleServiceStandardApplyMapper.querySimilarProductListPage(map);
    }

    @Override
    public Map<String,String> queryMaxAndMinInstallFee(ReferenceSimilarProductQueryDto queryDto) {
        return afterSaleServiceStandardApplyMapper.queryMaxAndMinInstallFee(queryDto);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertServiceStandardApply(AfterSaleServiceStandardApply afterSaleServiceStandardApply) {
        //解除供应商售后政策
        if (HAS_DONE_SUPPLIER_POLICY.equals(afterSaleServiceStandardApply.getSupplyAfterSaleIsMaintain())) {
            unbindSupplierPolicyTodoItem(afterSaleServiceStandardApply.getSkuNo());
        }
        return afterSaleServiceStandardApplyMapper.insertSelective(afterSaleServiceStandardApply);
    }

    @Override
    public AfterSaleServiceStandardApply findServiceStandardApplyById(Long serviceStandardApplyId) {
        return afterSaleServiceStandardApplyMapper.selectByPrimaryKey(serviceStandardApplyId);
    }

    @Override
    public AfterSaleServiceStandardApply selectAfterSaleServiceStandardBySkuNo(String skuNo) {
        return afterSaleServiceStandardApplyMapper.selectAfterSaleServiceStandardBySkuNo(skuNo);
    }

    /**
     * 添加供应商售后政策
     * @param request
     * @param addSupplyAfterSalePolicyDto
     */
    @Override
    @Transactional
    public void addSupplyAfterSalePolicy(HttpServletRequest request, AddSupplyAfterSalePolicyDto addSupplyAfterSalePolicyDto,User user) throws Exception{

        AfterSaleSupplyPolicy afterSaleSupplyPolicyDb = afterSaleSupplyPolicyMapper.getBySkuNoAndTraderId(addSupplyAfterSalePolicyDto.getTraderId(),addSupplyAfterSalePolicyDto.getSkuNo());
        //如果该供应商对应的该SKU已经存在 直接删除重新添加
        if(afterSaleSupplyPolicyDb != null){
            afterSaleSupplyPolicyMapper.deleteByPrimaryKey(afterSaleSupplyPolicyDb.getSupplyPolicyId());
            afterSaleSupplyPolicyAttachmentMapper.deleteBySupplyPolicyId(afterSaleSupplyPolicyDb.getSupplyPolicyId());
        }

        AfterSaleSupplyPolicy afterSaleSupplyPolicy = new AfterSaleSupplyPolicy();
        BeanUtils.copyProperties(addSupplyAfterSalePolicyDto,afterSaleSupplyPolicy);

        List<String> timePropertyList = Arrays.asList(
                                                        //安装政策-响应时效 安装政策-上门时效 安装政策-安装时效
                                                       "installPolicyResponseTime","installPolicyVisitTime","installPolicyInstallTime",
                                                        //技术指导-响应时效 技术指导-技术指导时效
                                                       "technicalDirectResponseTime","technicalDirectEffectTime",
                                                       //保修政策-主机保修期 保修政策-配件保修期 保修政策-响应时效 保修政策-上门时效 保修政策-维修时效
                                                       "guaranteePolicyHostGuaranteePeriod","guaranteePolicyPartsGuaranteePeriod","guaranteePolicyResponseTime","guaranteePolicyVisitTime","guaranteePolicyRepaireTime",
                                                       //退货政策-退货期限
                                                       "returnPolicyReturnPeriod",
                                                       //换货政策-换货期限
                                                       "exchangePolicyExchangePeriod"
                                                    );

        List<String> timePropertyDefalutValueList = Arrays.asList(
                //安装政策-响应时效 安装政策-上门时效 安装政策-安装时效
                ",小时",",小时",",小时",
                //技术指导-响应时效 技术指导-技术指导时效
                ",小时",",小时",
                //保修政策-主机保修期 保修政策-配件保修期 保修政策-响应时效 保修政策-上门时效 保修政策-维修时效
                ",月",",月",",小时",",小时",",小时",
                //退货政策-退货期限
                ",天",
                //换货政策-换货期限
                ",天"
        );

        copyTimePropertyValue(addSupplyAfterSalePolicyDto,afterSaleSupplyPolicy,timePropertyList,timePropertyDefalutValueList);

        afterSaleSupplyPolicy.setAddTime(DateUtil.getNowDate(TIME_FORMAT));
        afterSaleSupplyPolicy.setModTime(DateUtil.getNowDate(TIME_FORMAT));
        afterSaleSupplyPolicy.setCreator(user.getUserId());
        afterSaleSupplyPolicy.setUpdator(user.getUserId());

        //新增售后政策
        this.afterSaleSupplyPolicyMapper.insertSelective(afterSaleSupplyPolicy);

        this.saveAfterSaleSupplyPolicyAttachment(afterSaleSupplyPolicy.getSupplyPolicyId(),request,user);

        //更新贝登的售后政策=已维护
        AfterSaleServiceStandardApply afterSaleServiceStandardApply  = new AfterSaleServiceStandardApply();
        afterSaleServiceStandardApply.setSkuNo(addSupplyAfterSalePolicyDto.getSkuNo());
        afterSaleServiceStandardApply.setSupplyAfterSaleIsMaintain(1);
        this.afterSaleServiceStandardApplyMapper.updateBySkuNo(afterSaleServiceStandardApply);

        //解除供应商售后政策
        unbindSupplierPolicyTodoItem(addSupplyAfterSalePolicyDto.getSkuNo());

        //如果是原厂服务 需要将本次新增的数据同步到其他原厂服务中去
        if(afterSaleSupplyPolicy.getServiceProviderType() == 1) {
            copyCurrentPolicyToOtherOriginalPolicy(afterSaleSupplyPolicy,afterSaleSupplyPolicy.getSupplyPolicyId());
        }

        updateServiceLevel(afterSaleServiceStandardApply.getSkuNo());

    }

    private PropertyDescriptor getPropertyDescriptor(PropertyDescriptor[] propertyDescriptorArray,String propertyName){
        return Arrays.stream(propertyDescriptorArray).filter(property->property.getName().equals(propertyName)).findFirst().orElse(null);
    }


    private void unbindSupplierPolicyTodoItem(String skuNo) {
        //解除供应商售后政策
        supplierAfterSalePolicyTodoHandler.finish(skuNo);
    }

    /**
     * 复制事件相关的属性值 如果没有 就给与默认值
     * @param addSupplyAfterSalePolicyDto
     * @param afterSaleSupplyPolicy
     * @param timePropertyList
     * @param timePropertyDefalutValueList
     */
    private void copyTimePropertyValue(AddSupplyAfterSalePolicyDto addSupplyAfterSalePolicyDto,
                                       AfterSaleSupplyPolicy afterSaleSupplyPolicy,
                                       List<String> timePropertyList,
                                       List<String> timePropertyDefalutValueList) throws Exception{
        String timeProperty = "";
        Object timeNumValue = null;
        Object timeUnitValue = null;
        Method writeMethod = null;

        for(int i = 0;i < timePropertyList.size();i++){

            timeProperty = timePropertyList.get(i);

            timeNumValue= getPropertyDescriptor(afterSaleSupplyPolicyDtoBeanInfo.getPropertyDescriptors(),timeProperty + "Num").getReadMethod().invoke(addSupplyAfterSalePolicyDto);;

            timeUnitValue= getPropertyDescriptor(afterSaleSupplyPolicyDtoBeanInfo.getPropertyDescriptors(), timeProperty + "Unit").getReadMethod().invoke(addSupplyAfterSalePolicyDto);

            writeMethod= getPropertyDescriptor(afterSaleSupplyPolicyBeanInfo.getPropertyDescriptors(), timeProperty).getWriteMethod();

            if(timeNumValue != null && StringUtils.isNotEmpty(timeNumValue.toString())){
                writeMethod.invoke(afterSaleSupplyPolicy, timeNumValue + SPLIT_CHARACTOR + timeUnitValue);
            }else{
                writeMethod.invoke(afterSaleSupplyPolicy,timePropertyDefalutValueList.get(i));
            }
        };

    }

    /**
     * 复制原厂的供应商售后政策到其他原厂的售后政策上
     */
    private void copyCurrentPolicyToOtherOriginalPolicy(AfterSaleSupplyPolicy afterSaleSupplyPolicy,Long excludeSupplyPolicyId) {

        List<AfterSaleSupplyPolicyDto> supplyPolicyList = this.afterSaleSupplyPolicyMapper.findSupplyPolicyBySkuNoAndServiceType(afterSaleSupplyPolicy.getSkuNo(),1);

        if(CollectionUtils.isEmpty(supplyPolicyList)){
            return;
        }

        for(AfterSaleSupplyPolicyDto supplyPolicy : supplyPolicyList){

            if(excludeSupplyPolicyId != null && supplyPolicy.getSupplyPolicyId().equals(excludeSupplyPolicyId)){
                continue;
            }

            AfterSaleSupplyPolicy updateSupplyPolicy = new AfterSaleSupplyPolicy();
            BeanUtils.copyProperties(afterSaleSupplyPolicy,updateSupplyPolicy);
            updateSupplyPolicy.setSupplyPolicyId(supplyPolicy.getSupplyPolicyId());
            updateSupplyPolicy.setTraderId(supplyPolicy.getTraderId());
            updateSupplyPolicy.setTraderName(supplyPolicy.getTraderName());
            updateSupplyPolicy.setSkuNo(supplyPolicy.getSkuNo());
            updateSupplyPolicy.setAddTime(DateUtil.getNowDate(TIME_FORMAT));
            updateSupplyPolicy.setModTime(DateUtil.getNowDate(TIME_FORMAT));
            updateSupplyPolicy.setCreator(supplyPolicy.getCreator());
            updateSupplyPolicy.setUpdator(supplyPolicy.getUpdator());

            afterSaleSupplyPolicyMapper.updateByPrimaryKey(updateSupplyPolicy);

            afterSaleSupplyPolicyAttachmentMapper.deleteBySupplyPolicyId(supplyPolicy.getSupplyPolicyId());

            List<AfterSaleSupplyPolicyAttachment> attachmentList = afterSaleSupplyPolicyAttachmentMapper.getSupplyAfterSalePolicyAttashMent(afterSaleSupplyPolicy.getSupplyPolicyId());
            if(CollectionUtils.isEmpty(attachmentList)){
                continue;
            }

            List<AfterSaleSupplyPolicyAttachment> insertAttashmentList = new ArrayList<>();
            String nowTime = DateUtil.getNowDate(TIME_FORMAT);

            attachmentList.stream().forEach(supplyAttachment -> {
                AfterSaleSupplyPolicyAttachment attashment = new AfterSaleSupplyPolicyAttachment();
                BeanUtils.copyProperties(supplyAttachment,attashment);
                attashment.setAddTime(nowTime);
                attashment.setModTime(nowTime);
                attashment.setSupplyPolicyId(updateSupplyPolicy.getSupplyPolicyId());
                insertAttashmentList.add(attashment);
            });

            //批量新增附件
            this.afterSaleSupplyPolicyAttachmentMapper.batchInsertAttashment(insertAttashmentList);
        }

    }

    @Override
    public List<AfterSaleSupplyPolicyDto> getSupplyAfterSalePolicyListBySkuNo(String skuNo) {

        List<AfterSaleSupplyPolicyDto> supplyAfterSalePolicyList = afterSaleSupplyPolicyMapper.getSupplyAfterSalePolicyListBySkuNo(skuNo);

        if(CollectionUtils.isEmpty(supplyAfterSalePolicyList)){
            return Arrays.asList();
        }

        supplyAfterSalePolicyList.stream().forEach(supplyAfterSalePolicy -> {
            List<AfterSaleSupplyPolicyAttachment> attachments = afterSaleSupplyPolicyAttachmentMapper.getSupplyAfterSalePolicyAttashMent(supplyAfterSalePolicy.getSupplyPolicyId());
            supplyAfterSalePolicy.setAfterSaleSupplyPolicyAttachmentList(attachments);
        });

        return supplyAfterSalePolicyList;
    }


    /**
     * 新增供应商售后政策的附件
     * @param supplyPolicyId
     * @param request
     */
    private void saveAfterSaleSupplyPolicyAttachment(Long supplyPolicyId, HttpServletRequest request,User user) {


        String attashmentName = request.getParameter("attashmentName");
        String attashmentUri = request.getParameter("attashmentUri");

        if(StringUtils.isEmpty(attashmentName)){
            return;
        }

        List<AfterSaleSupplyPolicyAttachment> attashmentList = new ArrayList<>();

        AfterSaleSupplyPolicyAttachment attashment = new AfterSaleSupplyPolicyAttachment();
        attashment.setFileName(attashmentName);
        attashment.setUri(attashmentUri);
        attashment.setSupplyPolicyId(supplyPolicyId);
        attashment.setDomain(this.domain);
        attashment.setAddTime(DateUtil.getNowDate(TIME_FORMAT));
        attashment.setModTime(DateUtil.getNowDate(TIME_FORMAT));
        attashment.setCreator(user.getUserId());
        attashment.setUpdator(user.getUserId());
        attashmentList.add(attashment);

        String[] otherPicNames = request.getParameterValues("name_1");
        String[] otherPicUris = request.getParameterValues("uri_1");

        if(otherPicNames != null && otherPicNames.length > 0){
            for(int i = 0;i < otherPicNames.length;i++){

                if(StringUtils.isBlank(otherPicNames[i])){
                    continue;
                }

                AfterSaleSupplyPolicyAttachment otherAttashment = new AfterSaleSupplyPolicyAttachment();
                BeanUtils.copyProperties(attashment,otherAttashment);
                otherAttashment.setDomain(this.domain);
                otherAttashment.setFileName(otherPicNames[i]);
                otherAttashment.setUri(otherPicUris[i]);
                attashmentList.add(otherAttashment);
            }
        }

        if(CollectionUtils.isNotEmpty(attashmentList)){
            //批量新增附件
            this.afterSaleSupplyPolicyAttachmentMapper.batchInsertAttashment(attashmentList);
        }
    }

    /**
     * 编辑贝登售后标准
     * @param request
     * @param editBdAfterSalePolicyDto
     * @param user
     */
    @Override
    @Transactional
    public void editBdAfterSalePolicyApply(HttpServletRequest request, EditBdAfterSalePolicyDto editBdAfterSalePolicyDto, User user) {

        Long serviceStandardApplyId = this.afterSaleServiceStandardApplyMapper.selectAfterSaleServiceStandardBySkuNo(editBdAfterSalePolicyDto.getSkuNo()).getServiceStandardApplyId();

        AfterSaleServiceStandardApply afterSaleServiceStandardApply = new AfterSaleServiceStandardApply();
        afterSaleServiceStandardApply.setServiceStandardApplyId(serviceStandardApplyId);
        BeanUtils.copyProperties(editBdAfterSalePolicyDto,afterSaleServiceStandardApply);

        //安装政策-响应时效
        afterSaleServiceStandardApply.setInstallPolicyResponseTime(editBdAfterSalePolicyDto.getInstallPolicyResponseTimeNum()==null?"":editBdAfterSalePolicyDto.getInstallPolicyResponseTimeNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getInstallPolicyResponseTimeUnit());
        //安装政策-上门时效
        afterSaleServiceStandardApply.setInstallPolicyVisitTime(editBdAfterSalePolicyDto.getInstallPolicyVisitTimeNum() == null?"":editBdAfterSalePolicyDto.getInstallPolicyVisitTimeNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getInstallPolicyVisitTimeUnit());
        //安装政策-安装时效
        afterSaleServiceStandardApply.setInstallPolicyInstallTime(editBdAfterSalePolicyDto.getInstallPolicyInstallTimeNum() == null?"":editBdAfterSalePolicyDto.getInstallPolicyInstallTimeNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getInstallPolicyInstallTimeUnit());

        //技术指导-响应时效
        afterSaleServiceStandardApply.setTechnicalDirectResponseTime(editBdAfterSalePolicyDto.getTechnicalDirectResponseTimeNum() == null?"":editBdAfterSalePolicyDto.getTechnicalDirectResponseTimeNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getTechnicalDirectResponseTimeUnit());
        //技术指导-技术指导时效
        afterSaleServiceStandardApply.setTechnicalDirectEffectTime(editBdAfterSalePolicyDto.getTechnicalDirectEffectTimeNum() == null?"":editBdAfterSalePolicyDto.getTechnicalDirectEffectTimeNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getTechnicalDirectEffectTimeUnit());

        //保修政策-主机保修期
        afterSaleServiceStandardApply.setGuaranteePolicyHostGuaranteePeriod(editBdAfterSalePolicyDto.getGuaranteePolicyHostGuaranteePeriodNum() == null?"":editBdAfterSalePolicyDto.getGuaranteePolicyHostGuaranteePeriodNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getGuaranteePolicyHostGuaranteePeriodUnit());
        //保修政策-配件保修期
        afterSaleServiceStandardApply.setGuaranteePolicyPartsGuaranteePeriod(editBdAfterSalePolicyDto.getGuaranteePolicyPartsGuaranteePeriodNum() == null?"":editBdAfterSalePolicyDto.getGuaranteePolicyPartsGuaranteePeriodNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getGuaranteePolicyPartsGuaranteePeriodUnit());
        //保修政策-响应时效
        afterSaleServiceStandardApply.setGuaranteePolicyResponseTime(editBdAfterSalePolicyDto.getGuaranteePolicyResponseTimeNum() == null?"":editBdAfterSalePolicyDto.getGuaranteePolicyResponseTimeNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getGuaranteePolicyResponseTimeUnit());
        //保修政策-上门时效
        afterSaleServiceStandardApply.setGuaranteePolicyVisitTime(editBdAfterSalePolicyDto.getGuaranteePolicyVisitTimeNum() == null?"":editBdAfterSalePolicyDto.getGuaranteePolicyVisitTimeNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getGuaranteePolicyVisitTimeUnit());
        //保修政策-维修时效
        afterSaleServiceStandardApply.setGuaranteePolicyRepaireTime(editBdAfterSalePolicyDto.getGuaranteePolicyRepaireTimeNum() == null?"":editBdAfterSalePolicyDto.getGuaranteePolicyRepaireTimeNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getGuaranteePolicyRepaireTimeUnit());

        //退货政策-退货期限
        afterSaleServiceStandardApply.setReturnPolicyReturnPeriod(editBdAfterSalePolicyDto.getReturnPolicyReturnPeriodNum() == null?"":editBdAfterSalePolicyDto.getReturnPolicyReturnPeriodNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getReturnPolicyReturnPeriodUnit());

        //换货政策-换货期限
        afterSaleServiceStandardApply.setExchangePolicyExchangePeriod(editBdAfterSalePolicyDto.getExchangePolicyExchangePeriodNum() == null?"":editBdAfterSalePolicyDto.getExchangePolicyExchangePeriodNum()
                + SPLIT_CHARACTOR + editBdAfterSalePolicyDto.getExchangePolicyExchangePeriodUnit());

        afterSaleServiceStandardApply.setAfterSaleStandardStatus(1);

        afterSaleServiceStandardApply.setAuditor(String.join(",", roleMapper.getUserNameByRoleName("售后总监",1)));
        afterSaleServiceStandardApply.setModTime(DateUtil.getNowDate(TIME_FORMAT));
        afterSaleServiceStandardApply.setUpdator(user.getUserId());

        if(CollectionUtils.isNotEmpty(editBdAfterSalePolicyDto.getServiceLabels())) {
            String labels = editBdAfterSalePolicyDto.getServiceLabels().stream().collect(Collectors.joining(";"));
            StringBuffer sb = new StringBuffer();
            for (String serviceLabel : editBdAfterSalePolicyDto.getServiceLabels()) {
                AfterSaleServiceLabelDto afterSaleServiceLabelDto = afterSaleServiceLabelMapper.selectByPrimaryKey(Integer.valueOf(serviceLabel));
                sb.append(afterSaleServiceLabelDto.getLabelCode());
                sb.append(";");
            }
            if(sb.length()>0){
                String labelCodes = sb.substring(0,sb.length()-1);
                afterSaleServiceStandardApply.setAfterSaleServiceLabels(labelCodes);
            }

        }else{
            afterSaleServiceStandardApply.setAfterSaleServiceLabels("");
        }
        //    /** 维修配附件：0-请选择，1-有配附件，2-无配附件 **/
        //    private Integer repairPartsAccessory;
        //
        //    /** 安调培训：0-请选择，1-已培训，2-未培训 **/
        //    private Integer installTraining;
        //
        //    /** 维修培训：0-请选择，1-已培训，2-未培训 **/
        //    private Integer repairTraining;
        //
        //    /** 服务人员等级：0-请选择，1-S级，2-A级，3-B级，4-C级，5-D级 **/
        //    private Integer servicePersonnelLevel;
        // 设置售后服务标准的四个字段
                // Assigning the four fields from editBdAfterSalePolicyDto to afterSaleServiceStandardApply
        afterSaleServiceStandardApply.setRepairPartsAccessory(editBdAfterSalePolicyDto.getRepairPartsAccessory());
        afterSaleServiceStandardApply.setInstallTraining(editBdAfterSalePolicyDto.getInstallTraining());
        afterSaleServiceStandardApply.setRepairTraining(editBdAfterSalePolicyDto.getRepairTraining());
        afterSaleServiceStandardApply.setServicePersonnelLevel(editBdAfterSalePolicyDto.getServicePersonnelLevel());


        //修改售后服务标准
        this.afterSaleServiceStandardApplyMapper.updateByPrimaryKeySelective(afterSaleServiceStandardApply);

        //编辑BD售后标准的附件
        editBdAfterSalePolicyApplyAttachment(serviceStandardApplyId,request,user);

        AfterSaleServiceStandardApplyInstallArea applyInstallArea = afterSaleServiceStandardApplyInstallAreaMapper.queryInstallAreaByApplyId(afterSaleServiceStandardApply.getServiceStandardApplyId());

        if (applyInstallArea == null) {
            applyInstallArea = new AfterSaleServiceStandardApplyInstallArea();
            applyInstallArea.setProvinceCityJsonvalue(editBdAfterSalePolicyDto.getProvinceCityJsonvalue());
            applyInstallArea.setServiceStandardApplyId(afterSaleServiceStandardApply.getServiceStandardApplyId());
            applyInstallArea.setCreator(afterSaleServiceStandardApply.getCreator());
            applyInstallArea.setUpdator(afterSaleServiceStandardApply.getUpdator());
            applyInstallArea.setAddTime(DateUtil.getNowDate(TIME_FORMAT));
            applyInstallArea.setModTime(DateUtil.getNowDate(TIME_FORMAT));
            afterSaleServiceStandardApplyInstallAreaMapper.insertSelective(applyInstallArea);
        }else{
            AfterSaleServiceStandardApplyInstallArea applyInstallAreaUpdate = new AfterSaleServiceStandardApplyInstallArea();
            applyInstallAreaUpdate.setInstallAreaId(applyInstallArea.getInstallAreaId());
            applyInstallAreaUpdate.setProvinceCityJsonvalue(StringUtils.isEmpty(editBdAfterSalePolicyDto.getProvinceCityJsonvalue())?"": editBdAfterSalePolicyDto.getProvinceCityJsonvalue());
            afterSaleServiceStandardApplyInstallAreaMapper.updateByPrimaryKeySelective(applyInstallAreaUpdate);
        }
        // 重新计算售后服务等级
        updateServiceLevel(afterSaleServiceStandardApply.getSkuNo());
    }

    @Override
    public List<AfterSaleServiceStandardApplyAttachment> selectAfterSaleServiceStandardAttashmentList(Long serviceStandardApplyId) {
        return afterSaleServiceStandardApplyAttachmentMapper.selectByApplyId(serviceStandardApplyId);
    }

    //编辑BD售后标准的附件
    private void editBdAfterSalePolicyApplyAttachment(Long serviceStandardApplyId, HttpServletRequest request, User user) {

        //先删除附件
        afterSaleServiceStandardApplyAttachmentMapper.deleteByServiceStandardApplyId(serviceStandardApplyId);

        String[] attashmentName = request.getParameterValues("attashmentName");
        String[] attashmentUri = request.getParameterValues("attashmentUri");

        if(attashmentName == null || attashmentName.length == 0){
            return;
        }

        List<AfterSaleServiceStandardApplyAttachment> attashmentList = new ArrayList<>();
        String nowTime = DateUtil.getNowDate(TIME_FORMAT);

        for(int i = 0;i < attashmentName.length;i++){

            if(StringUtils.isBlank(attashmentName[i]) ){
                continue;
            }

            AfterSaleServiceStandardApplyAttachment attashment = new AfterSaleServiceStandardApplyAttachment();
            attashment.setFileName(attashmentName[i]);
            attashment.setUri(attashmentUri[i]);
            attashment.setServiceStandardApplyId(serviceStandardApplyId);
            attashment.setDomain(this.domain);
            attashment.setAddTime(nowTime);
            attashment.setModTime(nowTime);
            attashment.setCreator(user.getUserId());
            attashment.setUpdator(user.getUserId());
            attashmentList.add(attashment);
        }

        /*String otherPicNames = request.getParameter("name_1");
        String otherPicUris = request.getParameter("uri_1");
        if(StringUtils.isNotEmpty(otherPicNames) && StringUtils.isNotEmpty(otherPicUris)){
            for(int i = 0;i < otherPicNames.split(",").length;i++){
                AfterSaleServiceStandardApplyAttachment otherAttashment = new AfterSaleServiceStandardApplyAttachment();
                BeanUtils.copyProperties(attashment,otherAttashment);
                otherAttashment.setDomain(this.domain);
                otherAttashment.setFileName(otherPicNames.split(",")[i]);
                otherAttashment.setUri(otherPicUris.split(",")[i]);
                attashmentList.add(otherAttashment);
            }
        }*/

        if(CollectionUtils.isNotEmpty(attashmentList)){
            //批量新增附件
            this.afterSaleServiceStandardApplyAttachmentMapper.batchInsertAttashment(attashmentList);
        }

    }

    @Override
    public List<AfterSaleServiceStandardApplyDto> maintainInstallAreaListPage(MaintainInstallAreaDto queryDto, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("queryDto", queryDto);
        map.put("page", page);
        return afterSaleServiceStandardApplyMapper.maintainInstallAreaListPage(map);
    }

    /*@Override
    public List<AfterSaleServiceStandardApplyInstallArea> queryInstallAreaList(String skuNo) {

        AfterSaleServiceStandardApply afterSaleServiceStandardApply = this.afterSaleServiceStandardApplyMapper.selectAfterSaleServiceStandardBySkuNo(skuNo);

        if(afterSaleServiceStandardApply == null){
            return Arrays.asList();
        }

        return afterSaleServiceStandardApplyInstallAreaMapper.queryInstallAreaListByApplyId(afterSaleServiceStandardApply.getServiceStandardApplyId());
    }*/

    @Override
    public List<AfterSaleSupplyPolicyDto> referenceSupplyAfterSalePolicy(String skuNo,Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("skuNo",skuNo);
        map.put("page",page);
        return afterSaleSupplyPolicyMapper.referenceSupplyAfterSalePolicyListPage(map);
    }

    /**
     * 复制供应商额售后政策
     * @param skuNo
     * @param supplyPolicyId
     */
    @Override
    public void copySupplyAfterSalePolicy(String skuNo, Long supplyPolicyId,Integer copyType) throws Exception{
        AfterSaleSupplyPolicy afterSaleSupplyPolicy = new AfterSaleSupplyPolicy();
        //贝登售后申请
        AfterSaleServiceStandardApply afterSaleServiceStandardApply = this.afterSaleServiceStandardApplyMapper.selectAfterSaleServiceStandardBySkuNo(skuNo);
        if(copyType == 0){
            //参考贝登售后政策
            AfterSaleServiceStandardApply afterSaleServiceStandardApply1 = this.afterSaleServiceStandardApplyMapper.selectByPrimaryKey(supplyPolicyId);
            BeanUtils.copyProperties(afterSaleServiceStandardApply1,afterSaleSupplyPolicy);
        }else {
            //供应商的售后政策
            afterSaleSupplyPolicy = this.afterSaleSupplyPolicyMapper.selectByPrimaryKey(supplyPolicyId);
        }

        //复制主表信息
        copyAfterSalePolicy(afterSaleServiceStandardApply,afterSaleSupplyPolicy);

        //复制附件信息
        copyAfterSaleAttashmentInfo(afterSaleServiceStandardApply,afterSaleSupplyPolicy);
    }


    @Override
    @Transactional
    public ResultInfo copySupplyAfterSalePolicyList(String skuNosStr, Long supplyPolicyId, User user, Integer copyType, Integer formerSupplyPolicyId) {
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");
        //供应商的售后政策
        AfterSaleSupplyPolicy afterSaleSupplyPolicy = new AfterSaleSupplyPolicy();
        if(copyType == 0){
            //参考贝登售后政策
            AfterSaleServiceStandardApply afterSaleServiceStandardApply1 = this.afterSaleServiceStandardApplyMapper.selectByPrimaryKey(supplyPolicyId);
            BeanUtils.copyProperties(afterSaleServiceStandardApply1,afterSaleSupplyPolicy);
        }else {
            //供应商的售后政策
            afterSaleSupplyPolicy = this.afterSaleSupplyPolicyMapper.selectByPrimaryKey(supplyPolicyId);
        }

        String[] skuNos = skuNosStr.split(",");
        for(int i = 0;i <= skuNos.length - 1;i++){

            if(StringUtils.isBlank(skuNos[i])){
                continue;
            }
            AfterSaleSupplyPolicy sameSupplyInfo = this.afterSaleSupplyPolicyMapper.getBySkuNoAndTraderId(afterSaleSupplyPolicy.getTraderId(),skuNos[i]);
            if(sameSupplyInfo != null && sameSupplyInfo.getSupplyPolicyId() != formerSupplyPolicyId.longValue()){
                return new ResultInfo(-1,"复制的供应商对应的sku已存在售后服务标准");
            }
            AfterSaleSupplyPolicy formerAfterSaleSupplyPolicy = this.afterSaleSupplyPolicyMapper.selectByPrimaryKey(formerSupplyPolicyId.longValue());
//            AfterSaleSupplyPolicy dbPolicy = afterSaleSupplyPolicyMapper.getBySkuNoAndTraderId(formerAfterSaleSupplyPolicy.getTraderId(),skuNos[i]);
            if(formerAfterSaleSupplyPolicy != null){
                afterSaleSupplyPolicyMapper.deleteByPrimaryKey(formerAfterSaleSupplyPolicy.getSupplyPolicyId());
                afterSaleSupplyPolicyAttachmentMapper.deleteBySupplyPolicyId(formerAfterSaleSupplyPolicy.getSupplyPolicyId());
            }

            AfterSaleSupplyPolicy copySupplyPolicy = new AfterSaleSupplyPolicy();
            BeanUtils.copyProperties(afterSaleSupplyPolicy,copySupplyPolicy);
            copySupplyPolicy.setSkuNo(skuNos[i]);
            copySupplyPolicy.setTraderId(formerAfterSaleSupplyPolicy.getTraderId());
            copySupplyPolicy.setTraderName(formerAfterSaleSupplyPolicy.getTraderName());
            this.afterSaleSupplyPolicyMapper.insertSelective(copySupplyPolicy);

            AfterSaleServiceStandardApply afterSaleServiceStandardApplyInDb = afterSaleServiceStandardApplyMapper.selectAfterSaleServiceStandardBySkuNo(skuNos[i]);

            if(afterSaleServiceStandardApplyInDb == null){

                AfterSaleServiceStandardApply afterSaleServiceStandardApply = new AfterSaleServiceStandardApply();
                afterSaleServiceStandardApply.setSkuNo(skuNos[i]);
                afterSaleServiceStandardApply.setCreator(user.getUserId());
                afterSaleServiceStandardApply.setUpdator(user.getUserId());

                String nowDate = DateUtil.getNowDate(DateUtil.TIME_FORMAT);
                afterSaleServiceStandardApply.setAddTime(nowDate);
                afterSaleServiceStandardApply.setModTime(nowDate);
                afterSaleServiceStandardApply.setSupplyAfterSaleIsMaintain(1);

                afterSaleServiceStandardApplyMapper.insertSelective(afterSaleServiceStandardApply);

            }else{
                //更新贝登的售后政策=已维护
                AfterSaleServiceStandardApply updateApply  = new AfterSaleServiceStandardApply();
                updateApply.setSkuNo(skuNos[i]);
                updateApply.setSupplyAfterSaleIsMaintain(1);
                this.afterSaleServiceStandardApplyMapper.updateBySkuNo(updateApply);
            }

            List<AfterSaleSupplyPolicyAttachment>  attachmentList = this.afterSaleSupplyPolicyAttachmentMapper.getSupplyAfterSalePolicyAttashMent(supplyPolicyId);

            if (CollectionUtils.isEmpty(attachmentList)) {
                continue;
            }

            List<AfterSaleSupplyPolicyAttachment> insertAttashmentList = new ArrayList<>();
            String nowTime = DateUtil.getNowDate(TIME_FORMAT);

            attachmentList.stream().forEach(supplyAttachment -> {
                AfterSaleSupplyPolicyAttachment attashment = new AfterSaleSupplyPolicyAttachment();
                BeanUtils.copyProperties(supplyAttachment,attashment);
                attashment.setAddTime(nowTime);
                attashment.setModTime(nowTime);
                attashment.setSupplyPolicyId(copySupplyPolicy.getSupplyPolicyId());
                insertAttashmentList.add(attashment);
            });

            //批量新增附件
            this.afterSaleSupplyPolicyAttachmentMapper.batchInsertAttashment(insertAttashmentList);
        }
        return resultInfo;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBySkuNo(AfterSaleServiceStandardApply updateApply) {
        //解除供应商售后政策
        if (HAS_DONE_SUPPLIER_POLICY.equals(updateApply.getSupplyAfterSaleIsMaintain())) {
            unbindSupplierPolicyTodoItem(updateApply.getSkuNo());
        }

        this.afterSaleServiceStandardApplyMapper.updateBySkuNo(updateApply);
    }

    /**
     * 删除供应商售后政策
     * @param supplyPolicyId
     */
    @Override
    public void deleteSupplyPolicyById(Long supplyPolicyId) {

        AfterSaleSupplyPolicy afterSaleSupplyPolicy = afterSaleSupplyPolicyMapper.selectByPrimaryKey(supplyPolicyId);

        afterSaleSupplyPolicyMapper.deleteByPrimaryKey(supplyPolicyId);

        afterSaleSupplyPolicyAttachmentMapper.deleteBySupplyPolicyId(supplyPolicyId);

        List<AfterSaleSupplyPolicyDto> afterSaleSupplyPolicyList = this.afterSaleSupplyPolicyMapper.getSupplyAfterSalePolicyListBySkuNo(afterSaleSupplyPolicy.getSkuNo());

        //更新贝登的售后政策 已维护或者未维护
        AfterSaleServiceStandardApply updateApply  = new AfterSaleServiceStandardApply();
        updateApply.setSkuNo(afterSaleSupplyPolicy.getSkuNo());
        updateApply.setSupplyAfterSaleIsMaintain(CollectionUtils.isEmpty(afterSaleSupplyPolicyList) ? 0 : 1);
        this.afterSaleServiceStandardApplyMapper.updateBySkuNo(updateApply);

        updateServiceLevel(afterSaleSupplyPolicy.getSkuNo());

    }

    /**
     * 修改供应商售后政策
     * @param request
     * @param editSupplyAfterSalePolicyDto
     * @param user
     */
    @Override
    public void modifySupplyAfterSalePolicy(HttpServletRequest request, EditSupplyAfterSalePolicyDto editSupplyAfterSalePolicyDto, User user) {

        AfterSaleSupplyPolicy afterSaleSupplyPolicy = new AfterSaleSupplyPolicy();
        BeanUtils.copyProperties(editSupplyAfterSalePolicyDto,afterSaleSupplyPolicy);

        //安装政策-响应时效
        afterSaleSupplyPolicy.setInstallPolicyResponseTime(editSupplyAfterSalePolicyDto.getInstallPolicyResponseTimeNum() == null?"":editSupplyAfterSalePolicyDto.getInstallPolicyResponseTimeNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getInstallPolicyResponseTimeUnit());
        //安装政策-上门时效
        afterSaleSupplyPolicy.setInstallPolicyVisitTime(editSupplyAfterSalePolicyDto.getInstallPolicyVisitTimeNum() == null?"":editSupplyAfterSalePolicyDto.getInstallPolicyVisitTimeNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getInstallPolicyVisitTimeUnit());
        //安装政策-安装时效
        afterSaleSupplyPolicy.setInstallPolicyInstallTime(editSupplyAfterSalePolicyDto.getInstallPolicyInstallTimeNum() == null?"":editSupplyAfterSalePolicyDto.getInstallPolicyInstallTimeNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getInstallPolicyInstallTimeUnit());

        //技术指导-响应时效
        afterSaleSupplyPolicy.setTechnicalDirectResponseTime(editSupplyAfterSalePolicyDto.getTechnicalDirectResponseTimeNum() == null?"":editSupplyAfterSalePolicyDto.getTechnicalDirectResponseTimeNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getTechnicalDirectResponseTimeUnit());
        //技术指导-技术指导时效
        afterSaleSupplyPolicy.setTechnicalDirectEffectTime(editSupplyAfterSalePolicyDto.getTechnicalDirectEffectTimeNum() == null?"":editSupplyAfterSalePolicyDto.getTechnicalDirectEffectTimeNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getTechnicalDirectEffectTimeUnit());

        //保修政策-主机保修期
        afterSaleSupplyPolicy.setGuaranteePolicyHostGuaranteePeriod(editSupplyAfterSalePolicyDto.getGuaranteePolicyHostGuaranteePeriodNum() == null?"":editSupplyAfterSalePolicyDto.getGuaranteePolicyHostGuaranteePeriodNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getGuaranteePolicyHostGuaranteePeriodUnit());
        //保修政策-配件保修期
        afterSaleSupplyPolicy.setGuaranteePolicyPartsGuaranteePeriod(editSupplyAfterSalePolicyDto.getGuaranteePolicyPartsGuaranteePeriodNum() == null?"":editSupplyAfterSalePolicyDto.getGuaranteePolicyPartsGuaranteePeriodNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getGuaranteePolicyPartsGuaranteePeriodUnit());
        //保修政策-响应时效
        afterSaleSupplyPolicy.setGuaranteePolicyResponseTime(editSupplyAfterSalePolicyDto.getGuaranteePolicyResponseTimeNum() == null?"":editSupplyAfterSalePolicyDto.getGuaranteePolicyResponseTimeNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getGuaranteePolicyResponseTimeUnit());
        //保修政策-上门时效
        afterSaleSupplyPolicy.setGuaranteePolicyVisitTime(editSupplyAfterSalePolicyDto.getGuaranteePolicyVisitTimeNum() == null?"":editSupplyAfterSalePolicyDto.getGuaranteePolicyVisitTimeNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getGuaranteePolicyVisitTimeUnit());
        //保修政策-维修时效
        afterSaleSupplyPolicy.setGuaranteePolicyRepaireTime(editSupplyAfterSalePolicyDto.getGuaranteePolicyRepaireTimeNum() == null?"":editSupplyAfterSalePolicyDto.getGuaranteePolicyRepaireTimeNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getGuaranteePolicyRepaireTimeUnit());

        //退货政策-退货期限
        afterSaleSupplyPolicy.setReturnPolicyReturnPeriod(editSupplyAfterSalePolicyDto.getReturnPolicyReturnPeriodNum() == null?"":editSupplyAfterSalePolicyDto.getReturnPolicyReturnPeriodNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getReturnPolicyReturnPeriodUnit());

        //换货政策-换货期限
        afterSaleSupplyPolicy.setExchangePolicyExchangePeriod(editSupplyAfterSalePolicyDto.getExchangePolicyExchangePeriodNum() == null?"":editSupplyAfterSalePolicyDto.getExchangePolicyExchangePeriodNum()
                + SPLIT_CHARACTOR + editSupplyAfterSalePolicyDto.getExchangePolicyExchangePeriodUnit());

        this.afterSaleSupplyPolicyMapper.updateByPrimaryKey(afterSaleSupplyPolicy);


        //先删除附件
        afterSaleSupplyPolicyAttachmentMapper.deleteBySupplyPolicyId(afterSaleSupplyPolicy.getSupplyPolicyId());

        String[] attashmentName = request.getParameterValues("attashmentName");
        String[] attashmentUri = request.getParameterValues("attashmentUri");

        if(attashmentName == null || attashmentName.length == 0){
            return;
        }

        List<AfterSaleSupplyPolicyAttachment> attashmentList = new ArrayList<>();
        String nowTime = DateUtil.getNowDate(TIME_FORMAT);

        for(int i = 0;i < attashmentName.length;i++){

            if(StringUtils.isBlank(attashmentName[i])){
                continue;
            }
            AfterSaleSupplyPolicyAttachment attashment = new AfterSaleSupplyPolicyAttachment();
            attashment.setFileName(attashmentName[i]);
            attashment.setUri(attashmentUri[i]);
            attashment.setSupplyPolicyId(editSupplyAfterSalePolicyDto.getSupplyPolicyId());
            attashment.setDomain(this.domain);
            attashment.setAddTime(nowTime);
            attashment.setModTime(nowTime);
            attashment.setCreator(user.getUserId());
            attashment.setUpdator(user.getUserId());
            attashmentList.add(attashment);
        }

        if(CollectionUtils.isNotEmpty(attashmentList)){
            afterSaleSupplyPolicyAttachmentMapper.batchInsertAttashment(attashmentList);
        }

        if(afterSaleSupplyPolicy.getServiceProviderType() == 1){
            copyCurrentPolicyToOtherOriginalPolicy(afterSaleSupplyPolicy,afterSaleSupplyPolicy.getSupplyPolicyId());
        }

        updateServiceLevel(afterSaleSupplyPolicy.getSkuNo());
    }

    @Override
    public AfterSaleSupplyPolicy findSupplyAfterSalePolicyBySkuNoAndTraderId(Long traderId, String skuNo) {
        return this.afterSaleSupplyPolicyMapper.getBySkuNoAndTraderId(traderId,skuNo);
    }

    @Override
    public AfterSaleServiceStandardInfoDto getEffectAfterSalePolicy(String skuNo) {
        AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto = this.afterSaleServiceStandardInfoMapper.selectBySkuNo(skuNo);
        if(afterSaleServiceStandardInfoDto != null){
            afterSaleServiceStandardInfoDto.setInstallArea(afterSaleServiceStandardInfoInstallAreaMapper.selectByServiceStandardInfoId(afterSaleServiceStandardInfoDto.getServiceStandardInfoId()));
        }
        return afterSaleServiceStandardInfoDto;
    }

    @Override
    public Map<String, BigDecimal> findLastestYearNumAndAmout(String skuNo, Long traderId) {
        return afterSaleSupplyPolicyMapper.findLastestYearNumAndAmout(skuNo,traderId);
    }

    @Override
    public List<AfterSaleSupplyPolicyAttachment> findSupplyAfterSalePolicyAttashMent(Long supplyPolicyId) {
        return afterSaleSupplyPolicyAttachmentMapper.getSupplyAfterSalePolicyAttashMent(supplyPolicyId);
    }

    /**
     * 查询原始厂商的供应商售后政策
     * @param skuNo
     * @param serviceType
     * @return
     */
    @Override
    public List<AfterSaleSupplyPolicyDto> findSupplyPolicyBySkuNoAndServiceType(String skuNo, Integer serviceType) {
        return afterSaleSupplyPolicyMapper.findSupplyPolicyBySkuNoAndServiceType(skuNo,serviceType);
    }

    /**
     * 复制原始厂商的供应商政策
     * @param skuNo
     * @param traderId
     */
    @Override
    public Long copyOrginalFactorySupplyPolicy(String skuNo, Long traderId,User user) {

        List<AfterSaleSupplyPolicyDto> originalFactoryPolicyList = findSupplyPolicyBySkuNoAndServiceType(skuNo,1);
        if(CollectionUtils.isEmpty(originalFactoryPolicyList)){
            return 0L;
        }

        AfterSaleSupplyPolicyDto sourceSupplyPolicy = originalFactoryPolicyList.get(0);
        AfterSaleSupplyPolicy afterSaleSupplyPolicy = new AfterSaleSupplyPolicyDto();

        Trader trader = this.traderMapper.getTraderByTraderId(traderId.intValue());

        BeanUtils.copyProperties(sourceSupplyPolicy,afterSaleSupplyPolicy);
        afterSaleSupplyPolicy.setTraderId(traderId);
        afterSaleSupplyPolicy.setSkuNo(skuNo);
        afterSaleSupplyPolicy.setTraderName(trader.getTraderName());
        afterSaleSupplyPolicy.setAddTime(DateUtil.getNowDate(TIME_FORMAT));
        afterSaleSupplyPolicy.setModTime(DateUtil.getNowDate(TIME_FORMAT));
        afterSaleSupplyPolicy.setCreator(user.getUserId());
        afterSaleSupplyPolicy.setUpdator(user.getUserId());

        afterSaleSupplyPolicyMapper.insertSelective(afterSaleSupplyPolicy);

        List<AfterSaleSupplyPolicyAttachment> attachmentList = this.afterSaleSupplyPolicyAttachmentMapper.getSupplyAfterSalePolicyAttashMent(sourceSupplyPolicy.getSupplyPolicyId());

        if(CollectionUtils.isEmpty(attachmentList)){
            return afterSaleSupplyPolicy.getSupplyPolicyId();
        }

        List<AfterSaleSupplyPolicyAttachment> insertAttachmnetList = new ArrayList<>();

        //附件列表
        attachmentList.stream().forEach(attachment -> {
            AfterSaleSupplyPolicyAttachment insertAttashment = new AfterSaleSupplyPolicyAttachment();
            BeanUtils.copyProperties(attachment,insertAttashment);
            insertAttashment.setSupplyPolicyId(afterSaleSupplyPolicy.getSupplyPolicyId());
            insertAttachmnetList.add(insertAttashment);
        });

        //批量新增
        afterSaleSupplyPolicyAttachmentMapper.batchInsertAttashment(insertAttachmnetList);

        return afterSaleSupplyPolicy.getSupplyPolicyId();
    }

    @Override
    public void copyOrginalFactoryToCurrentSuppplyPolicy(Long supplyPolicyId, User user) {

        AfterSaleSupplyPolicy afterSaleSupplyPolicyDb = this.afterSaleSupplyPolicyMapper.selectByPrimaryKey(supplyPolicyId);

        List<AfterSaleSupplyPolicyDto> originalFactoryPolicyList = findSupplyPolicyBySkuNoAndServiceType(afterSaleSupplyPolicyDb.getSkuNo(),1);
        if(CollectionUtils.isEmpty(originalFactoryPolicyList)){
            return;
        }

        AfterSaleSupplyPolicyDto sourceSupplyPolicy = originalFactoryPolicyList.get(0);

        AfterSaleSupplyPolicy updateSupplyPolicy = new AfterSaleSupplyPolicy();
        BeanUtils.copyProperties(sourceSupplyPolicy,updateSupplyPolicy);

        updateSupplyPolicy.setSupplyPolicyId(supplyPolicyId);
        updateSupplyPolicy.setTraderId(afterSaleSupplyPolicyDb.getTraderId());
        updateSupplyPolicy.setSkuNo(afterSaleSupplyPolicyDb.getSkuNo());
        updateSupplyPolicy.setTraderName(afterSaleSupplyPolicyDb.getTraderName());
        updateSupplyPolicy.setAddTime(DateUtil.getNowDate(TIME_FORMAT));
        updateSupplyPolicy.setModTime(DateUtil.getNowDate(TIME_FORMAT));
        updateSupplyPolicy.setCreator(user.getUserId());
        updateSupplyPolicy.setUpdator(user.getUserId());

        afterSaleSupplyPolicyMapper.updateByPrimaryKey(updateSupplyPolicy);
        afterSaleSupplyPolicyAttachmentMapper.deleteBySupplyPolicyId(supplyPolicyId);

        List<AfterSaleSupplyPolicyAttachment> attachmentList = this.afterSaleSupplyPolicyAttachmentMapper.getSupplyAfterSalePolicyAttashMent(sourceSupplyPolicy.getSupplyPolicyId());
        if(CollectionUtils.isEmpty(attachmentList)){
            return;
        }

        List<AfterSaleSupplyPolicyAttachment> insertAttachmnetList = new ArrayList<>();
        //附件列表
        attachmentList.stream().forEach(attachment -> {
            AfterSaleSupplyPolicyAttachment insertAttashment = new AfterSaleSupplyPolicyAttachment();
            BeanUtils.copyProperties(attachment,insertAttashment);
            insertAttashment.setSupplyPolicyId(supplyPolicyId);
            insertAttachmnetList.add(insertAttashment);
        });
        //批量新增
        afterSaleSupplyPolicyAttachmentMapper.batchInsertAttashment(insertAttachmnetList);
    }

    @Override
    public AfterSaleSupplyPolicy getSupplyAfterSalePolicyListBySkuNoAndTraderId(String skuNo, Long traderId) {
        return afterSaleSupplyPolicyMapper.getBySkuNoAndTraderId(traderId,skuNo);
    }

    @Override
    public AfterSaleServiceStandardApplyInstallArea queryInstallArea(String skuNo) {
        return null;
    }

    @Override
    public AfterSaleServiceStandardApplyInstallArea selectServiceStandardApplyInstallArea(Long serviceStandardApplyId) {
        return this.afterSaleServiceStandardApplyInstallAreaMapper.queryInstallAreaByApplyId(serviceStandardApplyId);
    }

    @Override
    public List<AfterSaleServiceStandardInfoAttachment> getAttashmentListByInfoId(Long serviceStandardInfoId) {
        return afterSaleServiceStandardInfoAttachmentMapper.selectByStandardInfoId(serviceStandardInfoId);
    }



    @Override
    public List<AfterSaleServiceStandardApplyAttachment> getAfterSaleServiceStandardAttashmentById(Long serviceStandardApplyId) {
        return afterSaleServiceStandardApplyAttachmentMapper.selectByApplyId(serviceStandardApplyId);
    }



    /**
     * 审核完成
     * @param serviceStandardApplyId
     * @param pass
     */
    @Override
    public void verifyFinish(Long serviceStandardApplyId, boolean pass) {

        AfterSaleServiceStandardApply updateStandardApply = new AfterSaleServiceStandardApply();
        String nowTime = DateUtil.getNowDate(TIME_FORMAT);
        updateStandardApply.setAuditor("");
        updateStandardApply.setServiceStandardApplyId(serviceStandardApplyId);
        updateStandardApply.setModTime(nowTime);
        //审核不通过
        if(!pass){
            updateStandardApply.setAfterSaleStandardStatus(3);
            this.afterSaleServiceStandardApplyMapper.updateByPrimaryKeySelective(updateStandardApply);
            return;
        }

        updateStandardApply.setAfterSaleStandardStatus(2);
        updateStandardApply.setLatestVerifyPassTime(nowTime);
        this.afterSaleServiceStandardApplyMapper.updateByPrimaryKeySelective(updateStandardApply);

        //贝登售后政策审核通过时，解除待办
        internalAfterSalePolicyTodoHandler.finish(serviceStandardApplyId);

        AfterSaleServiceStandardApply afterSaleServiceStandardApply = this.afterSaleServiceStandardApplyMapper.selectByPrimaryKey(serviceStandardApplyId);

        Long serviceStandardInfoId = copyServiceStandardApplyInfo(afterSaleServiceStandardApply);
        copyServiceStandardAttashmentInfo(afterSaleServiceStandardApply,serviceStandardInfoId);
        copyServiceStandardInstallAreaInfo(afterSaleServiceStandardApply,serviceStandardInfoId);
        CoreSkuGenerate coreSkuGenerate = new CoreSkuGenerate();
        coreSkuGenerate.setSkuNo(afterSaleServiceStandardApply.getSkuNo());
        //更新AfterSalesServiceLevel与时间
        if(StringUtils.isNotBlank(afterSaleServiceStandardApply.getAfterSaleServiceLabels())){
            coreSkuGenerate.setHasAfterSaleServiceLabel(ErpConst.ONE);
            coreSkuGenerateMapper.updateLabelStausByNo(coreSkuGenerate);
        } else {
            coreSkuGenerate.setHasAfterSaleServiceLabel(ErpConst.ZERO);
            coreSkuGenerateMapper.updateLabelStausByNo(coreSkuGenerate);
        }

        updateServiceLevel(afterSaleServiceStandardApply.getSkuNo());

    }

    @Override
    public AfterSaleSupplyPolicyDto findSupplyAfterSalePolicy(Long supplyPolicyId) {
        return this.afterSaleSupplyPolicyMapper.findSupplyAfterSalePolicy(supplyPolicyId);
    }

    @Override
    public List<CopySupplyAfterSalePolicyDto> querySupplyAfterSalePolicyListPage(CopySupplyAfterSalePolicyQueryDto queryDto, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("queryDto", queryDto);
        map.put("page", page);
        return afterSaleSupplyPolicyMapper.querySupplyAfterSalePolicyListPage(map);
    }

    @Override
    public List<AfterSaleSupplyPolicyDto> findSupplyAfterSalePolicyByTraderId(Long traderId) {
        return afterSaleSupplyPolicyMapper.findSupplyAfterSalePolicyByTraderId(traderId);
    }

    /**
     * copy主表信息
     * @param afterSaleServiceStandardApply
     */
    private Long copyServiceStandardApplyInfo(AfterSaleServiceStandardApply afterSaleServiceStandardApply) {

        AfterSaleServiceStandardInfo afterSaleServiceStandardInfo = new AfterSaleServiceStandardInfo();
        BeanUtils.copyProperties(afterSaleServiceStandardApply,afterSaleServiceStandardInfo);

        AfterSaleServiceStandardInfo afterSaleServiceStandardInfoDb = this.afterSaleServiceStandardInfoMapper.selectBySkuNo(afterSaleServiceStandardApply.getSkuNo());
        if(afterSaleServiceStandardInfoDb == null){
            this.afterSaleServiceStandardInfoMapper.insertSelective(afterSaleServiceStandardInfo);
        }else{
            afterSaleServiceStandardInfo.setServiceStandardInfoId(afterSaleServiceStandardInfoDb.getServiceStandardInfoId());
            afterSaleServiceStandardInfoMapper.updateByPrimaryKey(afterSaleServiceStandardInfo);
        }
        return afterSaleServiceStandardInfo.getServiceStandardInfoId();
    }

    /**
     * copy服务标准安装区域
     * @param afterSaleServiceStandardApply
     * @param serviceStandardInfoId
     */
    private void copyServiceStandardInstallAreaInfo(AfterSaleServiceStandardApply afterSaleServiceStandardApply, Long serviceStandardInfoId) {

        afterSaleServiceStandardInfoInstallAreaMapper.deleteByServiceStandardInfoId(serviceStandardInfoId);

        AfterSaleServiceStandardApplyInstallArea applyInstallArea = this.afterSaleServiceStandardApplyInstallAreaMapper.queryInstallAreaByApplyId(afterSaleServiceStandardApply.getServiceStandardApplyId());
        if(applyInstallArea == null){
            return;
        }

        AfterSaleServiceStandardInfoInstallArea afterSaleServiceStandardInfoInstallArea = new AfterSaleServiceStandardInfoInstallArea();
        afterSaleServiceStandardInfoInstallArea = new AfterSaleServiceStandardInfoInstallArea();
        afterSaleServiceStandardInfoInstallArea.setProvinceCityJsonvalue(applyInstallArea.getProvinceCityJsonvalue());
        afterSaleServiceStandardInfoInstallArea.setServiceStandardInfoId(serviceStandardInfoId);
        afterSaleServiceStandardInfoInstallArea.setCreator(afterSaleServiceStandardApply.getCreator());
        afterSaleServiceStandardInfoInstallArea.setUpdator(afterSaleServiceStandardApply.getUpdator());
        afterSaleServiceStandardInfoInstallArea.setAddTime(DateUtil.getNowDate(TIME_FORMAT));
        afterSaleServiceStandardInfoInstallArea.setModTime(DateUtil.getNowDate(TIME_FORMAT));
        afterSaleServiceStandardInfoInstallAreaMapper.insertSelective(afterSaleServiceStandardInfoInstallArea);
    }

    /**
     * copy服务标准附件信息
     * @param afterSaleServiceStandardApply
     * @param serviceStandardInfoId
     */
    private void copyServiceStandardAttashmentInfo(AfterSaleServiceStandardApply afterSaleServiceStandardApply, Long serviceStandardInfoId) {

        //先删除附件
        afterSaleServiceStandardInfoAttachmentMapper.deleteByServiceStandardInfoId(serviceStandardInfoId);

        List<AfterSaleServiceStandardApplyAttachment> applyAttachmentList = this.afterSaleServiceStandardApplyAttachmentMapper.selectByApplyId(afterSaleServiceStandardApply.getServiceStandardApplyId());

        if (CollectionUtils.isEmpty(applyAttachmentList)) {
            return;
        }

        List<AfterSaleServiceStandardInfoAttachment> attashmentList = new ArrayList<>();
        String nowTime = DateUtil.getNowDate(TIME_FORMAT);

        applyAttachmentList.stream().forEach(applyAttachment -> {
            AfterSaleServiceStandardInfoAttachment attashment = new AfterSaleServiceStandardInfoAttachment();
            BeanUtils.copyProperties(applyAttachment,attashment);
            attashment.setServiceStandardInfoId(serviceStandardInfoId);
            attashment.setAddTime(nowTime);
            attashment.setModTime(nowTime);
            attashmentList.add(attashment);
        });

        if(CollectionUtils.isNotEmpty(attashmentList)){
            //批量新增附件
            this.afterSaleServiceStandardInfoAttachmentMapper.batchInsertAttashment(attashmentList);
        }

    }

    /**
     * 复制主表信息
     * @param afterSaleServiceStandardApply
     * @param afterSaleSupplyPolicy
     */
    private void copyAfterSalePolicy(AfterSaleServiceStandardApply afterSaleServiceStandardApply, AfterSaleSupplyPolicy afterSaleSupplyPolicy) {


        Integer guaranteePolicyCycleCaltype = null;
        Integer returnPolicyCycleCaltyp = null;
        Integer exchangePolicyCycleCaltyp = null;
        BigDecimal installFee = afterSaleServiceStandardApply.getInstallPolicyInstallFee();

        // 5 = 周期计算方式-贝登入库时间
        if(afterSaleSupplyPolicy.getGuaranteePolicyCycleCaltype() != null && afterSaleSupplyPolicy.getGuaranteePolicyCycleCaltype() == 5){
            guaranteePolicyCycleCaltype = afterSaleServiceStandardApply.getGuaranteePolicyCycleCaltype();
        }

        if(afterSaleSupplyPolicy.getReturnPolicyCycleCaltyp() != null && afterSaleSupplyPolicy.getReturnPolicyCycleCaltyp() == 5){
            returnPolicyCycleCaltyp = afterSaleServiceStandardApply.getReturnPolicyCycleCaltyp();
        }

        if(afterSaleSupplyPolicy.getExchangePolicyCycleCaltyp() != null && afterSaleSupplyPolicy.getExchangePolicyCycleCaltyp() == 5){
            exchangePolicyCycleCaltyp = afterSaleServiceStandardApply.getExchangePolicyCycleCaltyp();
        }
        //参考政策调整，可以参考其他所有产品的政策
        //此处保留sku，防止后续覆盖出错
        String skuNo = afterSaleServiceStandardApply.getSkuNo();
        BeanUtils.copyProperties(afterSaleSupplyPolicy,afterSaleServiceStandardApply);
        //重新设置sku
        afterSaleServiceStandardApply.setSkuNo(skuNo);

        if(afterSaleSupplyPolicy.getGuaranteePolicyCycleCaltype() != null && afterSaleSupplyPolicy.getGuaranteePolicyCycleCaltype() == 5){
            afterSaleServiceStandardApply.setGuaranteePolicyCycleCaltype(guaranteePolicyCycleCaltype);
        }

        if(afterSaleSupplyPolicy.getReturnPolicyCycleCaltyp() != null && afterSaleSupplyPolicy.getReturnPolicyCycleCaltyp() == 5){
            afterSaleServiceStandardApply.setReturnPolicyCycleCaltyp(returnPolicyCycleCaltyp);
        }

        if(afterSaleSupplyPolicy.getExchangePolicyCycleCaltyp() != null && afterSaleSupplyPolicy.getExchangePolicyCycleCaltyp() == 5){
            afterSaleServiceStandardApply.setExchangePolicyCycleCaltyp(exchangePolicyCycleCaltyp);
        }
        afterSaleServiceStandardApply.setInstallPolicyInstallFee(installFee);
        //重设审批状态
        afterSaleServiceStandardApply.setAfterSaleStandardStatus(0);
        //修改表数据
//        this.afterSaleServiceStandardApplyMapper.updateByPrimaryKeySelective(afterSaleServiceStandardApply);
        this.afterSaleServiceStandardApplyMapper.updateByPrimaryKey(afterSaleServiceStandardApply);

    }
    /**
     * 复制附件信息
     * @param afterSaleServiceStandardApply
     * @param afterSaleSupplyPolicy
     */
    private void copyAfterSaleAttashmentInfo(AfterSaleServiceStandardApply afterSaleServiceStandardApply, AfterSaleSupplyPolicy afterSaleSupplyPolicy) {

        //先删除附件
        afterSaleServiceStandardApplyAttachmentMapper.deleteByServiceStandardApplyId(afterSaleServiceStandardApply.getServiceStandardApplyId());

        List<AfterSaleSupplyPolicyAttachment> supplyAttachmentList = this.afterSaleSupplyPolicyAttachmentMapper.getSupplyAfterSalePolicyAttashMent(afterSaleSupplyPolicy.getSupplyPolicyId());

        if (CollectionUtils.isEmpty(supplyAttachmentList)) {
            return;
        }

        List<AfterSaleServiceStandardApplyAttachment> attashmentList = new ArrayList<>();
        String nowTime = DateUtil.getNowDate(TIME_FORMAT);

        supplyAttachmentList.stream().forEach(supplyAttachment -> {

            if(StringUtils.isBlank(supplyAttachment.getUri())){
                return;
            }
            AfterSaleServiceStandardApplyAttachment attashment = new AfterSaleServiceStandardApplyAttachment();
            attashment.setServiceStandardApplyId(afterSaleServiceStandardApply.getServiceStandardApplyId());
            attashment.setFileName(supplyAttachment.getFileName());
            attashment.setUri(supplyAttachment.getUri());
            attashment.setDomain(supplyAttachment.getDomain());
            attashment.setAddTime(nowTime);
            attashment.setModTime(nowTime);
            attashment.setCreator(supplyAttachment.getCreator());
            attashment.setUpdator(supplyAttachment.getUpdator());
            attashmentList.add(attashment);
        });

        if(CollectionUtils.isNotEmpty(attashmentList) ){
            //批量新增附件
            this.afterSaleServiceStandardApplyAttachmentMapper.batchInsertAttashment(attashmentList);
        }
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            afterSaleSupplyPolicyDtoBeanInfo = Introspector.getBeanInfo(AddSupplyAfterSalePolicyDto.class);
            afterSaleSupplyPolicyBeanInfo = Introspector.getBeanInfo(AfterSaleSupplyPolicy.class);
        } catch (IntrospectionException e) {
            throw new IllegalArgumentException(e);
        }
    }

    @Override
    public List<AfterSaleServiceLabelDto> getAfterSaleServiceLabels(String skuNo) {
        List<AfterSaleServiceLabelDto> labelDtos = afterSaleServiceLabelMapper.getAllLabels();
        AfterSaleServiceStandardApply afterSaleServiceStandardApply = afterSaleServiceStandardApplyMapper.selectAfterSaleServiceStandardBySkuNo(skuNo);
        AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto = afterSaleServiceStandardInfoMapper.selectBySkuNo(skuNo);
        if(CollectionUtils.isNotEmpty(labelDtos)){
            String modifyLabelStr = afterSaleServiceStandardApply != null? afterSaleServiceStandardApply.getAfterSaleServiceLabels():null;
            String originLabelStr = afterSaleServiceStandardInfoDto !=null? afterSaleServiceStandardInfoDto.getAfterSaleServiceLabels():null;
            boolean flag = StringUtils.equals(modifyLabelStr,originLabelStr);
            List<String> modifyLabels = StringUtils.isNotBlank(modifyLabelStr)? Splitter.on(";").splitToList(modifyLabelStr) : new ArrayList<>();
            List<String> originLabels =  StringUtils.isNotBlank(originLabelStr)? Splitter.on(";").splitToList(originLabelStr) : new ArrayList<>();
            for (AfterSaleServiceLabelDto labelDto : labelDtos) {
                if(originLabels.contains(String.valueOf(labelDto.getLabelCode()))){
                    labelDto.setChecked(ErpConst.ONE);
                }
                if(ErpConst.ONE.equals(afterSaleServiceStandardApply.getAfterSaleStandardStatus())){
                    if(modifyLabels.contains(String.valueOf(labelDto.getLabelCode()))){
                        labelDto.setBackUp(ErpConst.ONE);
                    }else{
                        labelDto.setBackUp(ErpConst.ZERO);
                    }
                }
            }
        }

        return labelDtos;
    }

    @Override
    public List<AfterSaleServiceLabelDto> getAllAfterSaleServiceLabels(String skuNo) {
        List<AfterSaleServiceLabelDto> labelDtos = afterSaleServiceLabelMapper.getAllLabels();
        AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto = afterSaleServiceStandardInfoMapper.selectBySkuNo(skuNo);
        String originLabelStr = afterSaleServiceStandardInfoDto !=null? afterSaleServiceStandardInfoDto.getAfterSaleServiceLabels():null;
        List<String> originLabels =  StringUtils.isNotBlank(originLabelStr)? Splitter.on(";").splitToList(originLabelStr) : new ArrayList<>();
        if(CollectionUtils.isNotEmpty(labelDtos) &&afterSaleServiceStandardInfoDto !=null){
            for (AfterSaleServiceLabelDto labelDto : labelDtos) {
                if("S1".equals(labelDto.getLabelCode())){
                    if(ErpConst.ONE.equals(afterSaleServiceStandardInfoDto.getInstallPolicyInstallType())){
                        labelDto.setChecked(ErpConst.ONE);
                    }
                }
                if(originLabels.contains(String.valueOf(labelDto.getLabelCode()))){
                    labelDto.setChecked(ErpConst.ONE);
                }
            }
        }
        return labelDtos;
    }

    /**
     * 根据审核通过后的信息计算售后服务等级
     * @param skuNo
     * @return 5五星级、4四星级、3三星级、2二星级、1一星级、0待评级
     */
    @Autowired
    CoreSpuGenerateExtendMapper coreSpuGenerateExtendMapper;
    @Override
    public Integer calculateAfterSalesServiceLevelBySku(String skuNo,StringBuilder sbLog) {
        CoreSkuBaseDTO skuBaseDTO= coreSpuGenerateExtendMapper.selectSpuTypeBySkuNo(skuNo);
        //sku商品类型为设备时需要评级，否则耗材、试剂、配件等默认无需评级；
        if(skuBaseDTO==null||skuBaseDTO.getSpuType()==null||skuBaseDTO.getSpuType()!= GoodsConstants.SpuType.EQUIPMENT){
            if(sbLog!=null){
                sbLog.append("AfterSaleServiceLevel sku:"+skuNo+" sku禁用或不为设备 无需评级 \n");
            }
            return AfterSaleServiceLevelEnum.LEVEL_6.getLevel();
        }
        //贝登服务
        AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto = afterSaleServiceStandardInfoMapper.selectBySkuNo(skuNo);
        //贝登服务审核
        AfterSaleServiceStandardApply afterSaleServiceStandardApply =  selectAfterSaleServiceStandardBySkuNo(skuNo);
        //供应商服务
        List<AfterSaleSupplyPolicyDto> supplyAfterSalePolicyList = afterSaleSupplyPolicyMapper.getSupplyAfterSalePolicyListBySkuNo(skuNo);

        AfterSaleSupplyPolicyDto latestSupplyPolicy = null;
        if(CollectionUtils.isNotEmpty(supplyAfterSalePolicyList)){
            latestSupplyPolicy = supplyAfterSalePolicyList.stream()
                    .max(Comparator.comparing(AfterSaleSupplyPolicyDto::getModTime,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .orElse(null);
        }
        //非审核通过 且 供应商售后政策未开启 判断为未评级
        if((afterSaleServiceStandardInfoDto == null||afterSaleServiceStandardApply==null
                || (afterSaleServiceStandardApply.getAfterSaleStandardStatus()!=null
                &&afterSaleServiceStandardApply.getAfterSaleStandardStatus()!=2))
                &&(latestSupplyPolicy == null)
        ){
            if(sbLog!=null){
                sbLog.append("AfterSaleServiceLevel sku:"+skuNo+" 贝登售后政策非审核通过 且 供应商售后政策未开启 判断为未评级 \n");
            }
            return AfterSaleServiceLevelEnum.LEVEL_0.getLevel();
        }
            // 参数初始化
        //响应
        String installPolicyResponseTime="";
        //技术指导时效
        String technicalDirectEffectTime="";
        //维修
        String guaranteePolicyRepaireTime="";
        //维修配附件
        Integer repairPartsAccessory=0;
        //安调培训
        Integer installTraining=0;
        //维修培训
        Integer repairTraining=0;
        //服务人员等级
        Integer servicePersonnelLevel=2;
        //资料库相关数据
        List<ApprovedSkuAfterSalesInfoVO> installDebugInfo =  getAllApprovedSkuAfterSalesInfo(skuNo);
        java.util.Set<Integer> docSet = new java.util.HashSet<>();
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(installDebugInfo)){
            for(ApprovedSkuAfterSalesInfoVO item : installDebugInfo){
                //7 转成 8 或者关系
                // 5转成3  或者关系
                if(item.getSubtype()==7){ //厂家/供应商技术对接人及联系方式or厂家/供应商售后负责人及联系方式
                    docSet.add(8);
                }else if(item.getSubtype()==5){ //安调视频或者指南
                    docSet.add(3);
                }
                else{
                    docSet.add(item.getSubtype());
                }
            }
        }

        //贝登是审核通过，优先取贝登的
        if(afterSaleServiceStandardApply.getAfterSaleStandardStatus()!=null&&afterSaleServiceStandardApply.getAfterSaleStandardStatus()==2){
            //响应
            installPolicyResponseTime = afterSaleServiceStandardInfoDto.getInstallPolicyResponseTime();
            //远程指导
            technicalDirectEffectTime = afterSaleServiceStandardInfoDto.getTechnicalDirectEffectTime();
            //维修
            guaranteePolicyRepaireTime = afterSaleServiceStandardInfoDto.getGuaranteePolicyRepaireTime();
            //维修配附件
            repairPartsAccessory = afterSaleServiceStandardInfoDto.getRepairPartsAccessory();
            //安调培训
            installTraining = afterSaleServiceStandardInfoDto.getInstallTraining();
            //维修培训
            repairTraining = afterSaleServiceStandardInfoDto.getRepairTraining();
            //服务人员等级
            servicePersonnelLevel = afterSaleServiceStandardInfoDto.getServicePersonnelLevel();

            //一些默认值处理，主要是兼容历史数据，表单填写不完整，不规范。 首先是无需评级
            //安装政策：“产品是否可安装”为“不可安装”或者“是否提供上门安装服务”为“不提供安装”；
            //技术指导：“是否提供技术维修指导”为“不提供”；
            //保修政策：“是否保修”为“否”；

            boolean noInstallFlag= skuBaseDTO.getIsInstallable()==null||skuBaseDTO.getIsInstallable()==0||afterSaleServiceStandardInfoDto.getInstallPolicyInstallType()==null||
                    afterSaleServiceStandardInfoDto.getInstallPolicyInstallType()==2 ;


            boolean noTechSupportFlag= (afterSaleServiceStandardInfoDto.getTechnicalDirectSupplyMaintain()!=null&&afterSaleServiceStandardInfoDto.getTechnicalDirectSupplyMaintain()== 0);
            boolean noGuaranteeFlag= (afterSaleServiceStandardInfoDto.getGuaranteePolicyIsGuarantee()!=null&&afterSaleServiceStandardInfoDto.getGuaranteePolicyIsGuarantee()== 0);
            if(noInstallFlag&&noTechSupportFlag&&noGuaranteeFlag){
                //无需评级
                if(sbLog!=null){
                    sbLog.append("AfterSaleServiceLevel sku:"+skuNo+" 贝登审核通过 但三大政策都不提供，判断为无需评级 \n");
                }
                return AfterSaleServiceLevelEnum.LEVEL_6.getLevel();
            }
            //默认值处理
            //当安装政策-响应时效为空值默认“2h”；
            if(noInstallFlag||  parseHour(installPolicyResponseTime)==null){
                installPolicyResponseTime = "2,小时";
            }
            //当技术指导-是否提供技术维修指导-不提供，则默认“技术指导时效”为“0.5天”；技术指导-是否提供技术维修指导-提供，空值则默认“1天”；
            if(noTechSupportFlag){
                technicalDirectEffectTime = "0.5,天";
            } else if(parseHour(technicalDirectEffectTime)==null){
                technicalDirectEffectTime = "1,天";
            }
            //当保修政策-是否保修-是，维修时效为空值，则默认“7天”；保修政策-是否保修-否，则默认维修时效为“3天”。
            if(noGuaranteeFlag){
                guaranteePolicyRepaireTime = "3,天";
            } else if(parseHour(guaranteePolicyRepaireTime)==null){
                guaranteePolicyRepaireTime = "7,天";
            }
        }else if(latestSupplyPolicy != null ) {
            //供应商售后政策
            //响应
            installPolicyResponseTime = latestSupplyPolicy.getInstallPolicyResponseTime();
            //远程指导
            technicalDirectEffectTime = latestSupplyPolicy.getTechnicalDirectEffectTime();
            //维修
            guaranteePolicyRepaireTime = latestSupplyPolicy.getGuaranteePolicyRepaireTime();
            //维修配附件
            repairPartsAccessory = latestSupplyPolicy.getRepairPartsAccessory();
            //安调培训
            installTraining = latestSupplyPolicy.getInstallTraining();
            //维修培训
            repairTraining = latestSupplyPolicy.getRepairTraining();
            //服务人员等级
            servicePersonnelLevel = latestSupplyPolicy.getServicePersonnelLevel();

            //一些默认值处理，主要是兼容历史数据，表单填写不完整，不规范。 首先是无需评级
            //安装政策：“产品是否可安装”为“不可安装”或者“是否提供上门安装服务”为“不提供安装”；
            //技术指导：“是否提供技术维修指导”为“不提供”；
            //保修政策：“是否保修”为“否”；

            boolean noInstallFlag= skuBaseDTO.getIsInstallable()==null||skuBaseDTO.getIsInstallable()==0||latestSupplyPolicy.getInstallPolicyInstallType()==null||
                    latestSupplyPolicy.getInstallPolicyInstallType()==2 ;


            boolean noTechSupportFlag= (latestSupplyPolicy.getTechnicalDirectSupplyMaintain()!=null&&latestSupplyPolicy.getTechnicalDirectSupplyMaintain()== 0);
            boolean noGuaranteeFlag= (latestSupplyPolicy.getGuaranteePolicyIsGuarantee()!=null&&latestSupplyPolicy.getGuaranteePolicyIsGuarantee()== 0);
            if(noInstallFlag&&noTechSupportFlag&&noGuaranteeFlag){
                //无需评级
                if(sbLog!=null){
                    sbLog.append("AfterSaleServiceLevel sku:"+skuNo+" 贝登非审核通过 但供应商三大政策都不提供，判断为无需评级 \n");
                }
                return AfterSaleServiceLevelEnum.LEVEL_6.getLevel();
            }
            //默认值处理
            //当安装政策-响应时效为空值默认“2h”；
            if(noInstallFlag||  parseHour(installPolicyResponseTime)==null ){
                installPolicyResponseTime = "2,小时";
            }

            //当技术指导-是否提供技术维修指导-不提供，则默认“技术指导时效”为“0.5天”；技术指导-是否提供技术维修指导-提供，空值则默认“1天”；
            if(noTechSupportFlag){
                technicalDirectEffectTime = "0.5,天";
            } else if( parseHour(technicalDirectEffectTime)==null){
                technicalDirectEffectTime = "1,天";
            }
            //当保修政策-是否保修-是，维修时效为空值，则默认“7天”；保修政策-是否保修-否，则默认维修时效为“3天”。
            if(noGuaranteeFlag){
                guaranteePolicyRepaireTime = "3,天";
            } else if( parseHour(guaranteePolicyRepaireTime)==null){
                guaranteePolicyRepaireTime = "7,天";
            }
        }
        return calLevelStatic(skuNo,  installPolicyResponseTime,  technicalDirectEffectTime,  guaranteePolicyRepaireTime,
            docSet,   repairPartsAccessory,  installTraining,  repairTraining,  servicePersonnelLevel,sbLog);
    }


    public AfterSaleServiceStandardInfoDto getAfterSalesServiceLevelDtoBySku(String skuNo) {
        CoreSkuBaseDTO skuBaseDTO= coreSpuGenerateExtendMapper.selectSpuTypeBySkuNo(skuNo);
        //sku商品类型为设备时需要评级，否则耗材、试剂、配件等默认无需评级；
        if(skuBaseDTO==null||skuBaseDTO.getSpuType()==null||skuBaseDTO.getSpuType()!= GoodsConstants.SpuType.EQUIPMENT){
            return null;
        }
        //贝登服务
        AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto = afterSaleServiceStandardInfoMapper.selectBySkuNo(skuNo);
        //贝登服务审核
        AfterSaleServiceStandardApply afterSaleServiceStandardApply =  selectAfterSaleServiceStandardBySkuNo(skuNo);
        //供应商服务
        List<AfterSaleSupplyPolicyDto> supplyAfterSalePolicyList = afterSaleSupplyPolicyMapper.getSupplyAfterSalePolicyListBySkuNo(skuNo);

        AfterSaleSupplyPolicyDto latestSupplyPolicy = null;
        if(CollectionUtils.isNotEmpty(supplyAfterSalePolicyList)){
            //如果getModTime 为空 排在最后
            latestSupplyPolicy = supplyAfterSalePolicyList.stream()
                    .max(Comparator.comparing(AfterSaleSupplyPolicyDto::getModTime,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .orElse(null);
        }
        //非审核通过 且 供应商售后政策未开启 判断为未评级
        if((afterSaleServiceStandardInfoDto == null||afterSaleServiceStandardApply==null
                || (afterSaleServiceStandardApply.getAfterSaleStandardStatus()!=null
                &&afterSaleServiceStandardApply.getAfterSaleStandardStatus()!=2))
                &&(latestSupplyPolicy == null)
        ){
            return null;
        }
        //贝登是审核通过，优先取贝登的
        if(afterSaleServiceStandardInfoDto!=null&&afterSaleServiceStandardApply.getAfterSaleStandardStatus()!=null&&afterSaleServiceStandardApply.getAfterSaleStandardStatus()==2){

            boolean noInstallFlag= skuBaseDTO.getIsInstallable()==null||skuBaseDTO.getIsInstallable()==0||afterSaleServiceStandardInfoDto.getInstallPolicyInstallType()==null||
                    afterSaleServiceStandardInfoDto.getInstallPolicyInstallType()==2 ;


            boolean noTechSupportFlag= (afterSaleServiceStandardInfoDto.getTechnicalDirectSupplyMaintain()!=null&&afterSaleServiceStandardInfoDto.getTechnicalDirectSupplyMaintain()== 0);
            boolean noGuaranteeFlag= (afterSaleServiceStandardInfoDto.getGuaranteePolicyIsGuarantee()!=null&&afterSaleServiceStandardInfoDto.getGuaranteePolicyIsGuarantee()== 0);
            if(noInstallFlag&&noTechSupportFlag&&noGuaranteeFlag){
                //无需评级
                return null;
            }
            //默认值处理
            if(noInstallFlag  ){
                afterSaleServiceStandardInfoDto.setInstallPolicyResponseTime("");
            }
            if(noTechSupportFlag){
                afterSaleServiceStandardInfoDto.setTechnicalDirectEffectTime("");
            }
            if(noGuaranteeFlag){
                afterSaleServiceStandardInfoDto.setGuaranteePolicyRepaireTime("");
            }
            return afterSaleServiceStandardInfoDto;

        }else if(latestSupplyPolicy != null ) {
            //安装政策：“产品是否可安装”为“不可安装”或者“是否提供上门安装服务”为“不提供安装”；

            boolean noInstallFlag= skuBaseDTO.getIsInstallable()==null||skuBaseDTO.getIsInstallable()==0||latestSupplyPolicy.getInstallPolicyInstallType()==null||
                    latestSupplyPolicy.getInstallPolicyInstallType()==2 ;


            boolean noTechSupportFlag= (latestSupplyPolicy.getTechnicalDirectSupplyMaintain()!=null&&latestSupplyPolicy.getTechnicalDirectSupplyMaintain()== 0);
            boolean noGuaranteeFlag= (latestSupplyPolicy.getGuaranteePolicyIsGuarantee()!=null&&latestSupplyPolicy.getGuaranteePolicyIsGuarantee()== 0);
            if(noInstallFlag&&noTechSupportFlag&&noGuaranteeFlag){
                //无需评级
                return null;
            }
            AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDtoNew = new AfterSaleServiceStandardInfoDto();
            afterSaleServiceStandardInfoDtoNew.setInstallPolicyResponseTime(latestSupplyPolicy.getInstallPolicyResponseTime());
            afterSaleServiceStandardInfoDtoNew.setTechnicalDirectEffectTime( latestSupplyPolicy.getTechnicalDirectEffectTime());
            afterSaleServiceStandardInfoDtoNew.setGuaranteePolicyRepaireTime(latestSupplyPolicy.getGuaranteePolicyRepaireTime());
            afterSaleServiceStandardInfoDtoNew.setRepairPartsAccessory(latestSupplyPolicy.getRepairPartsAccessory());
            afterSaleServiceStandardInfoDtoNew.setInstallTraining(latestSupplyPolicy.getInstallTraining());
            afterSaleServiceStandardInfoDtoNew.setRepairTraining(latestSupplyPolicy.getRepairTraining());
            afterSaleServiceStandardInfoDtoNew.setServicePersonnelLevel(latestSupplyPolicy.getServicePersonnelLevel());
            //默认值处理
            if(noInstallFlag  ){
                afterSaleServiceStandardInfoDtoNew.setInstallPolicyResponseTime("");
            }
            if(noTechSupportFlag){
                afterSaleServiceStandardInfoDtoNew.setTechnicalDirectEffectTime("");
            }
            if(noGuaranteeFlag){
                afterSaleServiceStandardInfoDtoNew.setGuaranteePolicyRepaireTime("");
            }
            return afterSaleServiceStandardInfoDtoNew;
        }
        return null;
    }

    private int calLevelStatic(String skuNo,
            //响应时间
            String installPolicyResponseTime,
            //远程指导
            String technicalDirectEffectTime,
            //维修
            String guaranteePolicyRepaireTime,
            //资料集合
            Set<Integer> docSet,
            //维修配附件
            Integer repairPartsAccessory,
            //安调培训
            Integer installTraining,
            //维修培训
            Integer repairTraining,
            //服务人员等级
            Integer servicePersonnelLevel,StringBuilder sbLog
    ){
// 1. 时效判定
        int timeLevel = 5;
        Double installTime = parseHour(installPolicyResponseTime);
        Double techTime = parseHour(technicalDirectEffectTime);
        Double repairTime = parseHour(guaranteePolicyRepaireTime);
        if(installTime == null || techTime == null || repairTime == null) {
            timeLevel = 0;
        } else if(installTime <= 0.5 && techTime <= 12 && repairTime <= 72) {
            timeLevel = 5;
        } else if(installTime <= 2 && techTime <= 24 && repairTime <= 168) {
            timeLevel = 3;
        } else if( repairTime <=  168) {
            timeLevel = 2;
        }else if(  repairTime > 168) {
            timeLevel = 1;
        }
        // 2. 资料判定
        int docLevel = 5;
        // 资料星级规则 参见： SkuSubTypeEnum
        int[] fiveStar = {1,2,3,4,6, 8,9,10,11};
        int[] fourStar = {1,2,3,4, 8,9,10};
        int[] threeStar = {1,3,4, 8,10};
        int[] twoStar = {1, 8};
        int[] oneStar = {1};
        if(!containsAll(docSet, fiveStar)) {
            docLevel = 4;
            if(!containsAll(docSet, fourStar)) {
                docLevel = 3;
                if(!containsAll(docSet, threeStar)) {
                    docLevel = 2;
                    if(!containsAll(docSet, twoStar)) {
                        docLevel = 1;
//                        if(!containsAll(docSet, oneStar)) {
//                            docLevel = 0;
//                        }
                    }
                }
            }
        }
        // 3. 支持判定
        int supportLevel = 5;
        if(repairPartsAccessory == null || installTraining == null || repairTraining == null || servicePersonnelLevel == null) {
            supportLevel = 0;
        } else if(repairPartsAccessory == 1 && installTraining == 1 && repairTraining == 1 && (servicePersonnelLevel == 1 || servicePersonnelLevel == 2)) {
            supportLevel = 5;
        } else if(repairPartsAccessory == 1 && installTraining == 1 && repairTraining == 1 && (servicePersonnelLevel == 1 || servicePersonnelLevel == 2 || servicePersonnelLevel == 3)) {
            supportLevel = 3;
        } else if(servicePersonnelLevel >= 1 && servicePersonnelLevel <= 4) {
            supportLevel = 2;
        } else if(servicePersonnelLevel >= 1 && servicePersonnelLevel <= 5) {
            supportLevel = 1;
        } else {
            supportLevel = 0;
        }
        // 4. 综合取最低
        int finalLevel = Math.min(timeLevel, Math.min(docLevel, supportLevel));
        if(sbLog!=null){
            sbLog.append(String.format("\nAfterSaleServiceLevel sku:%s 安调响应%f, 远程指导%f,repairTime%f ",skuNo,installTime,techTime,repairTime));
            sbLog.append(String.format("\nAfterSaleServiceLevel sku:%s 资料星级 %s ",skuNo,docSet));
            sbLog.append(String.format("\nAfterSaleServiceLevel sku:%s 维修配附件%s 安调培训%s 维修培训%s 服务人员等级%s ",skuNo,repairPartsAccessory,installTraining,repairTraining,servicePersonnelLevel));
            sbLog.append(String.format("\nAfterSaleServiceLevel sku:%s 时效判定%s, 资料判定%s,支持判定%s,最终等级%s",skuNo,timeLevel,docLevel,supportLevel,finalLevel));
        }
            return finalLevel;
    }





    // 工具方法：解析xx,小时/天/等，返回小时数，无法解析返回null
    private Double parseHour(String timeStr) {
        if(org.apache.commons.lang3.StringUtils.isBlank(timeStr)) return null;
        String[] arr = timeStr.split(",");
        if(arr.length != 2) return null;
        try {
            if(StringUtils.isBlank(arr[0]) || StringUtils.isBlank(arr[1])||  StringUtils.equals("null",arr[0])
                    ||  StringUtils.equals("null",arr[1])) {
                return null;
            }
            double num = Double.parseDouble(arr[0]);
            String unit = arr[1];
            if("小时".equals(unit)) return num;
            if("天".equals(unit)) return num * 24;
            if("月".equals(unit)) return num * 30 * 24;
            if("年".equals(unit)) return num * 365 * 24;
        } catch(Exception e) {
            logger.error("parseHour error for timeStr: {}", timeStr, e);
            return null;
        }
        return null;
    }
    // 工具方法：判断set是否包含所有目标
    private boolean containsAll(java.util.Set<Integer> set, int[] arr) {
        for(int i : arr) {
                if(!set.contains(i)) return false;
        }
        return true;
    }

    /**
     * 查询 售后资料 1，2，3
     */
    public List<ApprovedSkuAfterSalesInfoVO> getAllApprovedSkuAfterSalesInfo(String skuNo) {
        return afterSaleServiceStandardInfoMapper.selectAllApprovedSkuAfterSalesInfo(skuNo);
    }

    /**
     * 更新售后服务等级
     * @param skuNo
     */
    public void updateServiceLevel(String skuNo){
        try {
            CoreSkuGenerate coreSku = new CoreSkuGenerate();
            coreSku.setSkuId(Integer.parseInt(skuNo.substring(1)));
            StringBuilder logs = new StringBuilder();
            Integer level = calculateAfterSalesServiceLevelBySku(skuNo, logs);
            coreSku.setAfterSalesServiceLevel(level);
            logger.info("updateServiceLevel:" + logs.toString());
            //刷新索引
            coreSku.setModTime(new Date());
            coreSku.setUpdater(1);
            coreSkuGenerateMapper.updateByPrimaryKeySelective(coreSku);
        }catch (Exception e){
            logger.error("更新售后服务等级失败，skuNo:{}", skuNo, e);
        }
    }

}
