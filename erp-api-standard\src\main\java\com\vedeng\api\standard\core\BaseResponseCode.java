package com.vedeng.api.standard.core;

/**
 * 基础响应码枚举
 * 定义系统中所有标准的响应码和消息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public enum BaseResponseCode {
    
    /**
     * 成功
     */
    SUCCESS(0, "操作成功"),
    
    /**
     * 系统异常
     */
    SYSTEM_BUSY(500, "系统繁忙，请稍后重试"),
    
    /**
     * 操作失败
     */
    OPERATION_ERROR(400, "操作失败"),
    
    /**
     * 参数错误
     */
    PARAMETER_ERROR(401, "参数错误"),
    
    /**
     * 参数为空
     */
    PARAMETER_NULL(402, "参数不能为空"),
    
    /**
     * 参数格式错误
     */
    PARAMETER_FORMAT_ERROR(403, "参数格式错误"),
    
    /**
     * 用户未登录
     */
    TOKEN_ERROR(601, "用户未登录"),
    
    /**
     * 权限不足
     */
    PERMISSION_DENIED(602, "权限不足"),
    
    /**
     * 用户不存在
     */
    USER_NOT_EXIST(603, "用户不存在"),
    
    /**
     * 用户已被禁用
     */
    USER_DISABLED(604, "用户已被禁用"),
    
    /**
     * 业务异常
     */
    BUSINESS_ERROR(700, "业务处理异常"),
    
    /**
     * 数据不存在
     */
    DATA_NOT_EXIST(701, "数据不存在"),
    
    /**
     * 数据已存在
     */
    DATA_ALREADY_EXIST(702, "数据已存在"),
    
    /**
     * 数据状态异常
     */
    DATA_STATUS_ERROR(703, "数据状态异常"),
    
    /**
     * 操作频繁
     */
    OPERATION_TOO_FREQUENT(800, "操作过于频繁，请稍后重试"),
    
    /**
     * 接口不存在
     */
    API_NOT_FOUND(801, "接口不存在"),
    
    /**
     * 请求方法不支持
     */
    METHOD_NOT_SUPPORTED(802, "请求方法不支持"),
    
    /**
     * 媒体类型不支持
     */
    MEDIA_TYPE_NOT_SUPPORTED(803, "媒体类型不支持"),
    
    /**
     * 服务不可用
     */
    SERVICE_UNAVAILABLE(900, "服务暂时不可用"),
    
    /**
     * 网络超时
     */
    NETWORK_TIMEOUT(901, "网络请求超时"),
    
    /**
     * 外部服务异常
     */
    EXTERNAL_SERVICE_ERROR(902, "外部服务异常"),
    
    /**
     * 数据验证失败
     */
    VALIDATION_ERROR(410, "数据验证失败"),
    
    /**
     * 重复请求
     */
    DUPLICATE_REQUEST(411, "重复请求"),
    
    /**
     * 操作不支持
     */
    OPERATION_NOT_SUPPORTED(412, "操作不支持"),
    
    /**
     * 幂等性处理异常
     */
    IDEMPOTENCY_ERROR(413, "幂等性处理异常");
    
    /**
     * 响应码
     */
    private final Integer code;
    
    /**
     * 响应消息
     */
    private final String message;
    
    /**
     * 构造函数
     */
    BaseResponseCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    /**
     * 获取响应码
     */
    public Integer getCode() {
        return code;
    }
    
    /**
     * 获取响应消息
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * 根据响应码获取枚举
     */
    public static BaseResponseCode getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (BaseResponseCode responseCode : BaseResponseCode.values()) {
            if (responseCode.getCode().equals(code)) {
                return responseCode;
            }
        }
        
        return null;
    }
    
    /**
     * 判断是否为成功响应码
     */
    public static boolean isSuccess(Integer code) {
        return SUCCESS.getCode().equals(code);
    }
    
    @Override
    public String toString() {
        return "BaseResponseCode{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}
