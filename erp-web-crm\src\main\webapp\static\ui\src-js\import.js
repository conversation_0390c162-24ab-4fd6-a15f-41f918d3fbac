Vue.component('ui-import', {
    template: `<div class="vd-ui-upload-wrap" @click.stop>
        <div class="vd-ui-upload-file-wrap">
            <ui-button @click="triggerInputClick">{{ file ? '重新上传' : '本地上传' }}</ui-button>
            <div class="vd-ui-file-list" v-if="file">
                <div class="vd-ui-file-item">
                    <div class="vd-ui-file-info">
                        <div class="vd-ui-file-icon">
                            <img :src="'/static/image/upload-icon/' + (typeParse[file.type] || 'other') + '.svg'"></img>
                        </div>
                        <div class="vd-ui-file-name">{{file.name}}</div>
                        <div class="vd-ui-file-size">{{file.size}}</div>
                        <div class="vd-ui-file-option" @click="clear">
                            <i class="vd-ui_icon icon-recycle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="vd-ui-input-error" v-if="errorMsg">
            <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
            <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
        </div>
        <!-- 提示 -->
        <div class="vd-ui-upload-tips"> 
            <slot name="tips"></slot>
        </div>
        <input 
            type="file" 
            ref="fileInput" 
            :accept="accept" 
            @change="handlerFileInputChange" 
            style="display:none"/>
    </div>`,
    props: {
        label: {
            type: String,
            default: '本地上传'
        },
        errorMsg: String,
        limitType: String,
        limitTypeError: {
            type: String,
            default: '文件格式错误，请使用模板整理数据后重新导入'
        },
        limitSize: {
            type: Number,
            default: 5 //单位MB
        },
        limitSizeError: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            accept: '',
            acceptParse: {
                gif: 'image/gif',
                jpg: 'image/jpeg',
                jpeg: 'image/jpeg',
                png: 'image/png',
                pdf: 'application/pdf',
                doc: 'application/msword',
                xls: 'application/vnd.ms-excel',
                xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            },
            typeParse: {
                xls: 'excel',
                xlsx: 'excel',
                pdf: 'pdf',
                png: 'pic',
                jpg: 'pic',
                jpeg: 'pic',
                bmp: 'pic',
                gif: 'pic',
                ppt: 'ppt',
                pptx: 'ppt',
                doc: 'word',
                docx: 'word',
            },
            file: null
        }
    },
    watch: {},
    mounted() {
        this.init();
    },
    methods: {
        init() {
            if (this.limitType) {
                let acceptList = [];
                let isAll = false;
                this.limitType.split(',').forEach(item => {
                    if(!this.acceptParse[item]) {
                        isAll = true;
                    } else {
                        acceptList.push(this.acceptParse[item]);
                    }
                })

                if(!isAll) {
                    this.accept = acceptList.join(',')
                } else if (this.type == 'img') {
                    this.accept = 'image/*';
                }
            }
        },
        triggerInputClick() {
            this.$refs.fileInput.click();
        },
        getSize(size) {
            if(size > 1024 * 1024) {
                return parseFloat((size/1024/1024).toFixed(1)) + 'MB';
            } else {
                return parseInt(size/1024) + 'KB';
            }
        },
        handlerFileInputChange() {
            let files = this.$refs.fileInput.files;

            if(files.length) {
                this.errorMsg = '';
                let file = files[0];
                let fileName = file.name;
                let fileNameTxt = fileName.substring(0, fileName.lastIndexOf('.'));
                let fileType = fileName.substring(fileName.lastIndexOf('.') + 1, fileName.length) || '';

                if(!fileType || this.limitType.toUpperCase().indexOf(fileType.toUpperCase()) === -1) {
                    this.$refs.fileInput.value = "";
                    this.errorMsg = this.limitTypeError;
                    return false;
                }

                if(file.size > this.limitSize * 1024 * 1024) {
                    this.$refs.fileInput.value = "";
                    this.errorMsg = this.limitSizeError || '文件大小不超过' + this.limitSize + 'M';
                    return false;
                }

                this.file = {
                    name: fileNameTxt,
                    type: fileType,
                    fullName: fileName,
                    size: this.getSize(file.size)
                }

                this.$refs.fileInput.value = "";
                this.$emit('change', file);
            }
        },
        clear() {
            this.$refs.fileInput.value = "";
            this.file = null;
            this.$emit('change', null);
        }
    }
})