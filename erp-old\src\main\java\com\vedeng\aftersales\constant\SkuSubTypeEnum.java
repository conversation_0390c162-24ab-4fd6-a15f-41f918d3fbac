package com.vedeng.aftersales.constant;

/**
 * 来源于DOC
 */
public enum SkuSubTypeEnum {
    INSTRUCTION_MANUAL(1, "说明书"),
    OPERATION_FLOW_CARD(2, "操作流程卡"),
    GUIDE(3, "指南"),
    PACKING_LIST(4, "装箱清单"),
    SECURITY_ADJUSTMENT_VIDEO(5, "安调视频"),
    PRODUCT_PPT(6, "产品PPT"),
    TECHNICAL_CONTACT(7, "技术对接人"),
    AFTER_SALES_MANAGER(8, "售后负责人"),
    MANUAL(9, "手册"),
    CASE_STUDY(10, "案例"),
    REPAIR_VIDEO(11, "维修视频"),
    MAINTENANCE(12, "维护"),
    TRAINING(13, "培训"),
    SURVEY(14, "勘察"),
    OTHER(15, "其它");

    private final int code;
    private final String description;

    SkuSubTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static SkuSubTypeEnum fromCode(int code) {
        for (SkuSubTypeEnum skuSubTypeEnum : SkuSubTypeEnum.values()) {
            if (skuSubTypeEnum.getCode() == code) {
                return skuSubTypeEnum;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }
}