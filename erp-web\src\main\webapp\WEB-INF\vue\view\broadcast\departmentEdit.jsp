<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="/static/vue/vd-ui/common.css">
<link rel="stylesheet" href="/static/vue/vd-ui/ui.css">
<link rel="stylesheet" href="/static/vue/receiptnotice/edit.css">

<div class="setting-form-wrap form-wrap" id="page-container">
    <template v-if="!isloading">
        <div class="setting-form-block">
            <div class="setting-form-block-title">基础设置</div>
            <div class="setting-form-tip">仅处理归属于营销中心（ORG_ID:38）的末级部。若归属小组为空，该行数据不会保存</div>
            <div class="form-table depart-base-table">
                <div class="table-tr">
                    <div class="table-th">末级部门ID</div>
                    <div class="table-th">末级部门名称</div>
                    <div class="table-th">归属部门</div>
                    <div class="table-th">归属小组</div>
                </div>
                <div class="table-tr" v-for="(item, index) in baseSettingList">
                    <div class="table-td">{{ item.erpDeptId }}</div>
                    <div class="table-td">{{ item.erpDeptFullName }}</div>
                    <div class="table-td select-wrap">
                        <ui-select :data="departmentList" clearable v-model="item.department" @change="handlerDepartmentChange(index, $event)"></ui-select>
                    </div>
                    <div class="table-td select-wrap">
                        <ui-select :data="item.groupList" clearable v-model="item.group" v-if="!item.isGroupLoading"></ui-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="setting-form-block">
            <div class="setting-form-block-title">自定义归属</div>
            <div class="add-wrap">
                <div class="add-btn" @click="addCustom">
                    <span class="vd-ui_icon icon-add"></span>增行
                </div>
                <div class="add-btn-tip">自定义配置员工归属二级部与三级组</div>
            </div>
            <div class="form-table depart-custom-table">
                <div class="table-tr">
                    <div class="table-th"><span class="must">*</span>销售</div>
                    <div class="table-th"><span class="must">*</span>归属二级部</div>
                    <div class="table-th"><span class="must">*</span>归属三级组</div>
                    <div class="table-th">操作</div>
                </div>
                <template v-if="customList && customList.length">
                    <div class="table-tr" v-for="(item, index) in customList" :key="index">
                        <div class="table-td select-wrap">
                            <ui-select :remote="true" :remote-info="userRemoteInfo" :default-label="item.userName" v-model="item.userId" @change="handlerCustomChange"></ui-select>
                        </div>
                        <div class="table-td select-wrap">
                            <ui-select :data="departmentList" v-model="item.department" @change="handlerCustomDepartmentChange(index, $event)"></ui-select>
                        </div>
                        <div class="table-td select-wrap">
                            <ui-select :data="item.groupList" v-model="item.group" v-if="!item.isGroupLoading" @change="handlerCustomChange"></ui-select>
                        </div>
                        <div class="table-td">
                            <div class="link-btn warn" @click="deleteCustomItem(index)">删除</div>
                        </div>
                    </div>
                </template>
                <template v-else>
                    <div class="table-empty">
                        暂无自定义归属
                    </div>
                </template>
            </div>
            <div class="form-error-wrap" v-if="isShowCustomError">
                {{ customErrorMsg }}
            </div>
        </div>
        <div class="form-footer-wrap">
            <ui-button type="primary" @click="save">保存</ui-button>
        </div>
    </template>
</div>

<script src="${pageContext.request.contextPath}/static/vue/vd-ui/ui.js"></script>
<script src="${pageContext.request.contextPath}/static/js/receiptnotice/departmentEdit.js"></script>