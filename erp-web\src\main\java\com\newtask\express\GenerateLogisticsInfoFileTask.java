package com.newtask.express;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.wms.domain.entity.LogisticsInfoFileEntity;
import com.vedeng.erp.wms.mapper.LogisticsInfoFileMapper;
import com.vedeng.infrastructure.wxrobot.constants.WxRobotMsgTemple;
import com.vedeng.infrastructure.wxrobot.dto.WxMsgDto;
import com.vedeng.infrastructure.wxrobot.service.WxRobotService;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.List;
import java.util.ArrayList;

/**
 * 生成物流信息附件任务
 *
 * <AUTHOR>
 */
@JobHandler(value = "GenerateLogisticsInfoFileTask")
@Component
@Slf4j
public class GenerateLogisticsInfoFileTask extends AbstractJobHandler {

    @Value("${erp.domain}")
    private String erpDomain;

    @Value("${html2Pdf.domain}")
    private String html2PdfDomain;

    @Value("${generateLogisticsInfoFile.wxRootNum}")
    public String wxRootNum;

    
    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private ExpressMapper expressMapper;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Autowired
    private WxRobotService wxRobotService;

    @Autowired
    private LogisticsInfoFileMapper logisticsInfoFileMapper;

    private static final String PRINT_CONTRACT_URL = "/warehouse/warehousesout/expressInfoFile.do?logisticsNo={}&logisticsId={}&traderCustomerName={}&xsNo={}";

    private static final String RENDER_URL = "/api/render";

    /**
     * 支持的参数格式：
     * 1. 时间段查询：{"beginTime":"2024-06-17 09:00:00", "endTime":"2024-06-18 23:00:00"}
     * 2. 快递单号列表查询：{"logisticsNos":["SF1234567890","YT9876543210"]}
     *
     * @param param JSON格式的参数
     * @return 执行结果
     * @throws Exception 异常
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==============生成物流信息附件开始========================");

        Long beginTime = null;
        Long endTime = null;
        String[] logisticsNos = null;
        String queryMode = "默认查询";
        
        if (StrUtil.isNotBlank(param)) {
            try {
                JSONObject jsonObject = JSON.parseObject(param);
                String beginTimeStr = jsonObject.getString("beginTime");
                String endTimeStr = jsonObject.getString("endTime");
                
                // 支持快递单号数组查询
                if (jsonObject.containsKey("logisticsNos")) {
                    Object logisticsNosObj = jsonObject.get("logisticsNos");
                    if (logisticsNosObj instanceof com.alibaba.fastjson.JSONArray) {
                        com.alibaba.fastjson.JSONArray jsonArray = (com.alibaba.fastjson.JSONArray) logisticsNosObj;
                        logisticsNos = jsonArray.toArray(new String[0]);
                    }
                }
                
                // 添加时间字符串的null检查和异常处理
                if (StrUtil.isNotBlank(beginTimeStr)) {
                    beginTime = DateUtil.parseDateTime(beginTimeStr).getTime();
                }
                if (StrUtil.isNotBlank(endTimeStr)) {
                    endTime = DateUtil.parseDateTime(endTimeStr).getTime();
                }
                
                // 确定查询模式
                if (logisticsNos != null && logisticsNos.length > 0) {
                    queryMode = "快递单号列表查询";
                } else if (beginTime != null || endTime != null) {
                    queryMode = "时间段查询";
                }
                
            } catch (Exception ex) {
                log.error("解析任务参数失败，param: {}", param, ex);
                XxlJobLogger.log("解析任务参数失败，将使用默认查询条件");
            }
        }
        
        // 参数验证：确保至少有一个查询条件
        if (beginTime == null && endTime == null && (logisticsNos == null || logisticsNos.length == 0)) {
            log.warn("缺少查询条件，将使用默认的2023年以后数据查询");
            queryMode = "默认查询";
        }
        
        log.info("查询模式：{}，快递单号数组：{}，时间范围：{} - {}", 
            queryMode, 
            logisticsNos != null ? String.join(",", logisticsNos) : "无",
            beginTime != null ? DateUtil.formatDateTime(new java.util.Date(beginTime)) : "无",
            endTime != null ? DateUtil.formatDateTime(new java.util.Date(endTime)) : "无");
        XxlJobLogger.log("查询模式：" + queryMode);

        // 使用配置的批次大小，优化性能
        int pageSize = 500;
        log.info("使用批次大小：{}", pageSize);

        try {
            List<Integer> allExpressIds = new ArrayList<>();
            
            // 根据不同查询模式获取快递单ID列表
            if ("快递单号列表查询".equals(queryMode)) {
                // 快递单号列表查询模式
                allExpressIds = expressMapper.getExpressIdsByLogisticsNos(logisticsNos);
                log.info("根据快递单号列表查询到需要处理的快递单总数:{}", allExpressIds.size());
            } else {
                // 时间段查询或默认查询模式
                Express express = new Express();
                
                // 只处理2023年1月1日之后的数据（历史数据可能存在问题）
                // 2023-01-01 00:00:00 对应的时间戳
                final long DELIVERY_TIME_FILTER = 1672502400000L;
                express.setDeliveryTime(DELIVERY_TIME_FILTER);
                
                allExpressIds = expressMapper.getExpressIdsForGeneration(express, beginTime, endTime);
                log.info("根据{}查询到需要处理的快递单总数:{}", queryMode, allExpressIds.size());
            }
            XxlJobLogger.log("查询到需要处理的快递单总数:" + allExpressIds.size());
            
            if (allExpressIds.isEmpty()) {
                String message = "";
                switch (queryMode) {
                    case "快递单号列表查询":
                        message = "指定的快递单号列表中没有需要处理的快递单: " + String.join(",", logisticsNos);
                        break;
                    case "时间段查询":
                        message = "指定的时间段内没有需要处理的快递单";
                        break;
                    default:
                        message = "默认查询条件下没有需要处理的快递单";
                        break;
                }
                log.info(message);
                XxlJobLogger.log(message);
                XxlJobLogger.log("==============生成物流信息附件结束========================");
                return ReturnT.SUCCESS;
            }
            
            // 第二步：按ID列表分页处理，避免动态数据变化导致的遗漏
            int totalProcessed = 0;
            int totalFailed = 0;
            long startTime = System.currentTimeMillis();
            
            for (int i = 0; i < allExpressIds.size(); i += pageSize) {
                int endIndex = Math.min(i + pageSize, allExpressIds.size());
                List<Integer> pageIds = allExpressIds.subList(i, endIndex);
                
                // 避免空列表查询
                if (pageIds.isEmpty()) {
                    continue;
                }
                
                long batchStartTime = System.currentTimeMillis();
                int batchNumber = i / pageSize + 1;
                
                log.info("处理第{}批数据，ID范围：{}-{}，数量：{}", 
                    batchNumber, pageIds.get(0), pageIds.get(pageIds.size() - 1), pageIds.size());
                
                // 根据ID列表批量查询详细信息
                List<Express> expresslist = expressMapper.getExpressByIds(pageIds);
                log.info("第{}批：批量查询到快递单详情数量:{}", batchNumber, expresslist.size());
                
                if (expresslist.isEmpty()) {
                    log.warn("第{}批：根据ID列表查询到的数据为空，跳过此批次", batchNumber);
                    continue;
                }
                
                // 验证数据完整性
                if (expresslist.size() != pageIds.size()) {
                    log.warn("第{}批：查询结果数量({})与ID列表数量({})不匹配，可能存在数据问题", 
                        batchNumber, expresslist.size(), pageIds.size());
                }
                
                // 处理每个快递单
                int batchProcessed = 0;
                int batchFailed = 0;
                for (Express e : expresslist) {
                    try {
                        this.handleExpress(e);
                        batchProcessed++;
                        totalProcessed++;
                    } catch (Exception handleEx) {
                        batchFailed++;
                        totalFailed++;
                        log.error("第{}批：生成物流信息附件失败,物流单号:{},业务类型:{},业务id:{}", 
                            batchNumber, e.getLogisticsNo(), e.getBusinessType(), e.getRelatedId(), handleEx);
                        String format = StrUtil.format(WxRobotMsgTemple.GENERATE_LOGISTICSINFO_FILE_ERROR, e.getLogisticsNo());
                        WxMsgDto wxMsg = new WxMsgDto().initWxMsgDto(format);
                        wxRobotService.send(wxRootNum, wxMsg);
                    }
                }
                
                long batchEndTime = System.currentTimeMillis();
                long batchDuration = batchEndTime - batchStartTime;
                log.info("第{}批处理完成，耗时：{}ms，成功：{}，失败：{}，总进度：{}/{}", 
                    batchNumber, batchDuration, batchProcessed, batchFailed, totalProcessed, allExpressIds.size());
                
                // 记录处理进度到XXL-JOB日志
                if (batchNumber % 10 == 0) {
                    XxlJobLogger.log(String.format("处理进度：%d/%d (%.2f%%)", 
                        totalProcessed, allExpressIds.size(), 
                        (double) totalProcessed / allExpressIds.size() * 100));
                }
            }
            
            long totalDuration = System.currentTimeMillis() - startTime;
            // 记录最终处理结果
            log.info("物流信息附件生成任务完成，总耗时：{}ms，总计划处理：{}，实际成功：{}，失败：{}，成功率：{:.2f}%", 
                totalDuration, allExpressIds.size(), totalProcessed, totalFailed,
                allExpressIds.size() > 0 ? (double) totalProcessed / allExpressIds.size() * 100 : 0);
            XxlJobLogger.log(String.format("任务完成 - 耗时：%dms，计划：%d，成功：%d，失败：%d，成功率：%.2f%%", 
                totalDuration, allExpressIds.size(), totalProcessed, totalFailed,
                allExpressIds.size() > 0 ? (double) totalProcessed / allExpressIds.size() * 100 : 0));
                
        } catch (Exception e) {
            log.error("生成物流信息附件任务执行异常", e);
            return ReturnT.FAIL;
        }

        XxlJobLogger.log("==============生成物流信息附件结束========================");
        return ReturnT.SUCCESS;
    }

    private void handleExpress(Express e) throws Exception {
        switch (e.getBusinessType()) {
            case 496:
                // 销售订单
                Saleorder saleorder = saleorderMapper.getSaleorderBySaleorderGoodsId(e.getRelatedId());
                if (saleorder != null) {
                    // 添加null检查确保数据安全
                    if (StringUtils.isNotBlank(saleorder.getSaleorderNo())) {
                        e.setXsNo(saleorder.getSaleorderNo());
                    }
                    if (saleorder.getSaleorderId() != null) {
                        e.setYwId(saleorder.getSaleorderId());
                    }
                    if (StringUtils.isNotBlank(saleorder.getTraderName())) {
                        e.setTraderCustomerName(saleorder.getTraderName());
                    }
                } else {
                    log.warn("未找到对应的销售订单，relatedId: {}", e.getRelatedId());
                }
                break;
            case 515:
                // 采购订单
                BuyorderVo buyorderVo = buyorderMapper.selectBuyorderGoodsId(e.getRelatedId());
                if (buyorderVo != null) {
                    // 添加null检查确保数据安全
                    if (StringUtils.isNotBlank(buyorderVo.getBuyorderNo())) {
                        e.setXsNo(buyorderVo.getBuyorderNo());
                    }
                    if (buyorderVo.getBuyorderId() != null) {
                        e.setYwId(buyorderVo.getBuyorderId());
                    }
                    if (StringUtils.isNotBlank(buyorderVo.getTraderName())) {
                        e.setTraderCustomerName(buyorderVo.getTraderName());
                    }
                } else {
                    log.warn("未找到对应的采购订单，relatedId: {}", e.getRelatedId());
                }
                break;
            default:
                log.debug("未处理的业务类型: {}", e.getBusinessType());
                break;
        }

        this.generateLogisticsInfoFile(e);
    }

    private void generateLogisticsInfoFile(Express e) throws Exception {
        // 双重检查：再次确认是否已存在附件（防止并发情况下的重复处理）
        List<LogisticsInfoFileEntity> existingFiles = logisticsInfoFileMapper.findAllByExpressId(e.getExpressId());
        if (CollUtil.isNotEmpty(existingFiles)) {
            log.info("物流信息附件已存在(双重检查),物流单号:{},物流id:{}", e.getLogisticsNo(), e.getExpressId());
            return;
        }
        
        // 添加null检查防止空指针异常
        String traderCustomerName = e.getTraderCustomerName();
        if (StringUtils.isBlank(traderCustomerName)) {
            log.warn("客户名称为空，物流单号:{}, 跳过生成附件", e.getLogisticsNo());
            return;
        }
        
        // 验证必要字段
        if (e.getLogisticsId() == null || StringUtils.isBlank(e.getLogisticsNo()) || StringUtils.isBlank(e.getXsNo())) {
            log.warn("必要字段缺失，物流单号:{}, 物流ID:{}, 单号:{}, 跳过生成附件", 
                e.getLogisticsNo(), e.getLogisticsId(), e.getXsNo());
            return;
        }
        
        String encodedTraderCustomerName = URLEncoder.encode(traderCustomerName, "UTF-8");
        String format = StrUtil.format(PRINT_CONTRACT_URL,
                e.getLogisticsNo(),
                e.getLogisticsId(),
                encodedTraderCustomerName,
                e.getXsNo());
        String contractTemplateUrl = erpDomain + format;
        String html2PdfUrl = html2PdfDomain + RENDER_URL;
        UrlToPdfParam urlToPdfParam = UrlToPdfParam.defaultUrlToPdfParam(contractTemplateUrl);
        String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf", "物流信息" + e.getLogisticsNo(), urlToPdfParam);
        log.info("生成物流信息附件,物流单号[{}]，ossUrl[{}]", e.getLogisticsNo(), ossUrl);
        if (StringUtils.isBlank(ossUrl)) {
            log.error("生成物流信息附件失败，物流单号[{}]", e.getLogisticsNo());
            return;
        }
        log.info("生成物流信息附件:{},ossUrl:{}", JSON.toJSONString(e), ossUrl);
        
        // 存入物流附件
        LogisticsInfoFileEntity entity = new LogisticsInfoFileEntity();
        BeanUtil.copyProperties(e, entity);
        entity.setUrl(ossUrl);
        
        // 修复字段映射bug：手动设置字段名不匹配的属性
        entity.setOrderNo(e.getXsNo());  // 业务单号：Express.xsNo -> LogisticsInfoFileEntity.orderNo
        entity.setTraderName(e.getTraderCustomerName());  // 客户名称：确保正确映射
        
        logisticsInfoFileMapper.insertSelective(entity);
        log.debug("成功插入物流信息附件记录，快递ID:{}, 物流单号:{}, 业务单号:{}", 
            e.getExpressId(), e.getLogisticsNo(), e.getXsNo());
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class GenerateLogisticsInfoParam {

        private String orderNo;

        private String logisticsNo;

        private Integer logisticsId;

        private String traderName;

        private Integer expressId;

    }
}
