@import (less) '../common/mixin.css';

// panel操作面板
.records-panel {
    position: relative;
    height: 100%;
    overflow: hidden;

    .panel-title {
        font-size: 16px;
        font-weight: 700;
        line-height: 56px;
        padding: 0 20px;
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .panel-wrap {
        height: calc(100% - 56px);
        overflow-y: auto;
        .scrollbar;

        &.has-fixed-btn {
            height: calc(100% - 56px - 53px);
        }

        &.dialog-inner {
            max-height: 510px;
            margin-top: 20px;
        }
    }

    .panel-btn-wrap {
        width: 100%;
        max-width: 353px;
        height: 53px;
        padding: 10px 20px;
        background: #fff;
        position: fixed;
        bottom: 0;
        right: 0;
        z-index: 2;
        display: flex;
        align-items: center;

        .btn {
            width: 100%;
            flex: 1;
        }

        .panel-btn-link {
            color: #09f;
            cursor: pointer;
            margin-left: 20px;

            &:hover {
                color: #f60;
            }
        }
    }

    .panel-null-data {
        padding: 80px 0;
        text-align: center;

        .icon {
            font-size: 32px;
            margin-bottom: 10px;
            color: #09F;
        }
        .font {
            color: #999;
        }
    }



    // 跟进记录
    .followUpRecord-records {
        background: #fff;
        position: relative;

        .subtitle {
            font-size: 14px;
            color: #999;
            line-height: 33px;
            background: #f5f7fa;
            padding: 0 20px;
        }

        .list {
            padding: 0 20px 0 40px;

            .item {
                position: relative;
                padding: 20px 0;
                border-bottom: solid 1px #e1e5e8;

                &::before {
                    content: "";
                    display: block;
                    width: 11px;
                    height: 11px;
                    border-radius: 50%;
                    overflow: hidden;
                    background: #09f;
                    position: absolute;
                    left: -21px;
                    top: 24px;
                    z-index: 2;
                }
                &::after {
                    content: "";
                    display: block;
                    border-left: dashed 1px #E1E5E8;
                    position: absolute;
                    left: -16px;
                    top: 24px;
                    bottom: -24px;
                    z-index: 1;
                }

                &:last-child {
                    border-bottom: none;

                    &::after {
                        display: none;
                    }
                }

                .row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;

                    .creator {
                        flex: 1;
                        min-width: 0;
                        display: flex;
                        align-items: center;

                        .icon {
                            width: 20px;
                            height: 20px;
                            margin-right: 5px;
                            border-radius: 3px;
                        }
                        .name {
                            flex: 1;
                            min-width: 0;
                        }
                    }
                    .time {
                        width: 140px;
                        flex-shrink: 0;
                        white-space: nowrap;
                        font-size: 12px;
                        color: #999;
                        text-align: right;
                    }
                }
                .content-suffix {
                    margin-bottom: 5px;
                }
                .detail {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .record-audio-wrap {
                        display: flex;
                        align-items: center;

                        .record-audio-label {
                            color: #999;
                        }

                        .record-audio-play {
                            color: #09f;
                            cursor: pointer;
                            margin-left: 10px;

                            &:hover {
                                color: #f60;
                            }
                        }
                    }

                    > a {
                        color: #09f;
                        cursor: pointer;
                        transition: color 0.15s;

                        >i {
                            font-size: 16px;
                            position: relative;
                            top: 3px;
                            line-height: 1;
                        }
                        &:hover {
                            color: #f60;
                        }
                    }
                }
            }
        }
    }

    // 任务记录
    .renwu-list {
        .list {
            padding: 0 20px;

            .item {
                padding: 20px 0;
                border-bottom: solid 1px #e1e5e8;

                &:last-child {
                    border-bottom: none;
                }

                .row {
                    display: flex;
                    align-items: center;
                    margin-bottom: 5px;

                    .label {
                        color: #999;
                        flex-shrink: 0;
                    }

                    .content {
                        word-break: break-all;
                        flex: 1;

                        &.flex {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }

                        .user {
                            display: flex;
                            .icon {
                                width: 20px;
                                height: 20px;
                                margin-right: 8px;
                                border-radius: 3px;
                            }
                            .name {}
                        }
                    }
                }
                .coment {
                    color: #333;
                }
            }
        }

        .vd-ui-table-wrap .vd-ui-table-body.vd-ui-wrap-scroll {
            overflow-y: visible;
        }
    }

    // 操作记录
    .operation-records {

        .list {
            padding: 0 20px 0 40px;

            .item {
                position: relative;
                padding: 20px 0;
                border-bottom: solid 1px #e1e5e8;

                &::before {
                    content: "";
                    display: block;
                    width: 11px;
                    height: 11px;
                    border-radius: 50%;
                    overflow: hidden;
                    background: #09f;
                    position: absolute;
                    left: -21px;
                    top: 24px;
                    z-index: 2;
                }
                &::after {
                    content: "";
                    display: block;
                    border-left: dashed 1px #E1E5E8;
                    position: absolute;
                    left: -16px;
                    top: 24px;
                    bottom: -24px;
                    z-index: 1;
                }

                &:last-child {
                    border-bottom: none;

                    &::after {
                        display: none;
                    }
                }

                .row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;

                    .creator {
                        flex: 1;
                        min-width: 0;
                        display: flex;
                        align-items: center;

                        .icon {
                            width: 20px;
                            height: 20px;
                            margin-right: 5px;
                            border-radius: 3px;
                        }
                        .name {
                            flex: 1;
                            min-width: 0;
                        }
                    }
                    .time {
                        width: 140px;
                        flex-shrink: 0;
                        white-space: nowrap;
                        font-size: 12px;
                        color: #999;
                        text-align: right;
                    }
                }
            }
        }
    }

    // 协同人
    .partner-records {

        .thead {
            padding: 5px 20px;
            background: #f5f7fa;
            color: #999;
        }

        .list {
            padding: 0 20px;

            .item {
                padding-bottom: 10px;
                border-bottom: solid 1px #E1E5E8;
                margin-top: 10px;

                &:first-child {
                    margin-top: 0;
                }

                .partner-detail-top {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 5px;

                    .partner-detail-option {
                        color: #09f;
                        cursor: pointer;

                        &:hover {
                            color: #f60;
                        }

                        &.disabled {
                            color: #999;
                            cursor: not-allowed;
                        }
                    }
                }

                .partner-detail-bottom {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    color: #999;
                }
            }
        }
    }
}


// dialog
.td-link {
    color: #0099FF;
    cursor: pointer;

    &:hover {
        color: #f60;
    }
}

.tyc-icon {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    word-break: break-all;

    .company {
        font-size: 14px;
        color: #333;

        &.blue {
            color: #09f;
            cursor: pointer;

            &:hover {
                color: #f60;
            }
        }
    }

    .icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('../../image/tyc.png') no-repeat;
        background-size: 100%;
        margin-left: 5px;
        cursor: pointer;
        position: relative;
        top: 2px;
    }
}

// 移除协作人
.remove-partner {
    color: #09f;
    cursor: pointer;
    &:hover {
        color: #f60;
    }

    &.disabled {
        color: #999;
        cursor: not-allowed;
    }
}
.add-record {
    display: flex;
    align-items: center;

    .btn {
        &.disabled {
            cursor: not-allowed;
            color: #999;
            background-color: #f5f7fa;
            border-color: #d7dade;
            // pointer-events: none;
        }
    }

    .audio-link-btn {
        margin-left: 20px;
        color: #09f;
        cursor: pointer;

        &:hover {
            color: #f60;
        }
    }
}
.border-top {
    border-top: solid 1px #E1E5E8;
    padding-top: 20px !important;
    margin-bottom: 20px;
    margin-top: 20px;
}
.nextData-margin {
    margin-top: 5px;
    margin-left: 6px;
}

/* 任务 */
.renwu-dialog-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.change-btn {
    font-size: 12px;
    font-weight: 400;
    color: #09f;
    cursor: pointer;

    &:hover {
        color: #f60;
    }
}
.renwu-status {
    color: #999;
    line-height: 22px;
    padding: 0 5px;
    border-radius: 3px;
    display: inline-block;

    &.status0 {
        color: #F60;
        background: #FFEDE0;
    }

    &.status1 {
        color: #13BF13;
        background: #E3F7E3;
    }

    &.status2 {
        color: #1A4D80;
        background: #E3EAF0;
    }
}
// 任务操作按钮
.renwu-deal-btn {
    text-align: right;
    margin-top: 10px;

    .btn {
        color: #09f;
        cursor: pointer;

        &:hover {
            color: #f60;
        }
    }
}
// 多人
.more-people {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .user-show {
        margin-right: 20px;
    }
}
.font-danger {
    color: #D9001B;
}

.handle-tips {
    background: #E0F3FF;
    padding: 10px 15px;
    // margin: -20px -20px 20px;

    position: absolute;
    top: 0;
    left: 0;
    right: 0;

    .icon {
        font-size: 16px;
        color: #09F;
        margin-right: 10px;
    }
    span {}
}
.handle-bordertop {
    border-top: solid 1px #E1E5E8;
    margin-top: 40px;
    padding-top: 21px;
}
.handle-wrap {
    max-height: 500px;
    overflow: auto;
    margin-right: -20px;
    padding-right: 20px;
    .scrollbar;
}
.link {
    color: #333;

    &.blue {
        color: #09f;
        cursor: pointer;

        &:hover {
            color: #f60;
        }
    }
}


.ai-wrap {
    width: 100%;
    // max-width: 590px;
    margin-top: 10px;

    .ai-title {
        color: #999;
        border-top: dashed 1px #E1E5E8;
        padding: 10px 0 3px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .ai-play-btn {
            display: flex;
            align-items: center;
            background: #F5F7FA;
            border: 1px solid #BABFC2;
            border-radius: 15px;
            height: 30px;
            padding: 0 15px 0 10px;
            color: #333;
            font-size: 12px;
            cursor: pointer;

            &:hover {
                background: #EBEFF2;
            }

            &:active {
                background: #E1E5E8;
            }

            .ai-play-btn-icon {
                width: 18px;
                height: 18px;
                background-image: url(../../image/common/icon-play.svg);
                background-size: 100% 100%;
                margin-right: 3px;
            }
        }
    }

    .ai-content {
        margin-top: 7px;
    }
}

.ai-dialog-iframe {
    border: none;
    width: 820px;
    margin: 0 auto;
    height: 488px;
    display: block;
}



//选择录音弹层样式
.ui-dlg-audio-wrap {
    position: relative;
    padding-bottom: 50px;

    .ui-dlg-audio-search {
        margin-bottom: 15px;
    }

    .ui-dlg-audio-search-list {
        display: flex;
        flex-wrap: wrap;

        .ui-dlg-audio-search-item {
            display: flex;
            align-items: center;
            margin-right: 20px;
            width: calc(33.33% - 13.33px);
            margin-bottom: 10px;

            &:nth-child(3n) {
                margin-right: 0;
            }
        }

        .vd-ui-date-range-item {
            flex: 1;

            .vd-ui-input.vd-ui-input--suffix .vd-ui-input__inner {
                padding-right: 24px;
            }
        }

        .ui-dlg-audio-search-label {
            width: 100px;
            color: #999;
            text-align: right;
        }

        .ui-dlg-audio-search-cnt {
            flex: 1;

            >.vd-ui-input {
                width: 100%;
            }

            .vd-ui-select {
                width: 100%;
            }

            .vd-ui-date .vd-ui-date-editor {
                width: 100%;
            }
        }
    }

    .ui-dlg-audio-search-btns {
        margin-top: 5px;
        display: flex;
        align-items: center;
        justify-content: center;

        .vd-ui-button {
            margin-right: 10px;

            &:last-child {
                margin-right: 0;
            }
        }
    }

    .ui-dlg-audio-list {
        border-top: 1px solid #E1E5E8;

        .vd-ui-table-wrap {
            margin-top: -1px;
            margin-bottom: -1px;
        }

        .vd-ui-table-wrap .vd-ui-table .vd-ui-td {
            vertical-align: top;
        }

        .ui-audio-time-type {
            .ui-audio-type {
                display: flex;
                align-items: center;
                margin-bottom: 3px;

                .ui-audio-type-icon {
                    width: 16px;
                    height: 16px;
                    background-size: 100%;
                    margin-right: 5px;

                    &.in {
                        background-image: url(/static/image/common/call-in.svg);
                    }

                    &.out {
                        background-image: url(/static/image/common/call-out.svg);
                    }
                }

                .ui-audio-type-txt {
                    margin-right: 11px;
                    position: relative;

                    &::before {
                        content: '';
                        width: 1px;
                        height: 12px;
                        background: #e1e5e8;
                        position: absolute;
                        top: 3px;
                        right: -6px;
                    }

                    &:last-child {
                        &::before {
                            display: none;
                        }
                    }
                }
            }
        }

        .ui-audio-nickname {
            color: #999;
            padding-left: 23px;
            margin-top: 3px;
        }

        .ui-audio-customer-name {
            padding-left: 23px;
        }

        .ui-audio-belonger-name {
            color: #999;
            margin-top: 3px;
        }
    }

    .ui-dlg-audio-footer {
        padding-top: 20px;
        position: absolute;
        width: 100%;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .ui-dlg-audio-footer-btns {
            display: flex;
            align-items: center;

            .vd-ui-button {
                margin-left: 10px;
            }
        }
    }
}

.vd-ui-select-audio-dialog {
    .vd-ui-dialog.vd-ui-dialog--in {
        max-height: calc(100% - 40px);
        margin-bottom: 0;
    }

    .ui-dlg-audio-list {
        height: calc(100vh - 306px);
    }

    .vd-ui-table-header {
        background: #f5f7fa;
        position: relative;
        margin-bottom: -1px;
        z-index: 1;
    }
}