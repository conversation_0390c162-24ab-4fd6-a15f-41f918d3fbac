<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.AfterSaleSupplyPolicyMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSaleSupplyPolicy" >
    <id column="SUPPLY_POLICY_ID" property="supplyPolicyId" jdbcType="BIGINT" />
    <result column="SKU_NO" property="skuNo" jdbcType="VARCHAR" />
    <result column="TRADER_ID" property="traderId" jdbcType="BIGINT" />
    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
    <result column="SERVICE_PROVIDER_TYPE" property="serviceProviderType" jdbcType="INTEGER" />
    <result column="INSTALL_POLICY_INSTALL_TYPE" property="installPolicyInstallType" jdbcType="INTEGER" />
    <result column="INSTALL_POLICY_INSTALL_AREA" property="installPolicyInstallArea" jdbcType="VARCHAR" />
    <result column="INSTALL_POLICY_INSTALL_FEE" property="installPolicyInstallFee" jdbcType="DECIMAL" />
    <result column="INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION" property="installPolicyHaveInstallationQualification" jdbcType="INTEGER" />
    <result column="INSTALL_POLICY_FREE_REMOTE_INSTALL" property="installPolicyFreeRemoteInstall" jdbcType="INTEGER" />
    <result column="INSTALL_POLICY_RESPONSE_TIME" property="installPolicyResponseTime" jdbcType="VARCHAR" />
    <result column="INSTALL_POLICY_VISIT_TIME" property="installPolicyVisitTime" jdbcType="VARCHAR" />
    <result column="INSTALL_POLICY_INSTALL_TIME" property="installPolicyInstallTime" jdbcType="VARCHAR" />
    <result column="TECHNICAL_DIRECT_SUPPLY_MAINTAIN" property="technicalDirectSupplyMaintain" jdbcType="INTEGER" />
    <result column="TECHNICAL_DIRECT_RESPONSE_TIME" property="technicalDirectResponseTime" jdbcType="VARCHAR" />
    <result column="TECHNICAL_DIRECT_EFFECT_TIME" property="technicalDirectEffectTime" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_IS_GUARANTEE" property="guaranteePolicyIsGuarantee" jdbcType="INTEGER" />
    <result column="GUARANTEE_POLICY_GUARANTEE_TYPE" property="guaranteePolicyGuaranteeType" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD" property="guaranteePolicyHostGuaranteePeriod" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD" property="guaranteePolicyPartsGuaranteePeriod" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_CYCLE_CALTYPE" property="guaranteePolicyCycleCaltype" jdbcType="INTEGER" />
    <result column="GUARANTEE_POLICY_AREA" property="guaranteePolicyArea" jdbcType="INTEGER" />
    <result column="GUARANTEE_POLICY_AREA_COMMENT" property="guaranteePolicyAreaComment" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_RESPONSE_TIME" property="guaranteePolicyResponseTime" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_VISIT_TIME" property="guaranteePolicyVisitTime" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_REPAIRE_TIME" property="guaranteePolicyRepaireTime" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_REPAIRE_COMMENT" property="guaranteePolicyRepaireComment" jdbcType="VARCHAR" />
    <result column="RETURN_POLICY_SUPPORT_RETURN" property="returnPolicySupportReturn" jdbcType="INTEGER" />
    <result column="RETURN_POLICY_CONDITION" property="returnPolicyCondition" jdbcType="VARCHAR" />
    <result column="RETURN_POLICY_NEED_IDENTIFY" property="returnPolicyNeedIdentify" jdbcType="INTEGER" />
    <result column="RETURN_POLICY_IDENTIFY_TYPE" property="returnPolicyIdentifyType" jdbcType="VARCHAR" />
    <result column="RETURN_POLICY_RETURN_PERIOD" property="returnPolicyReturnPeriod" jdbcType="VARCHAR" />
    <result column="RETURN_POLICY_CYCLE_CALTYP" property="returnPolicyCycleCaltyp" jdbcType="INTEGER" />
    <result column="RETURN_POLICY_PACKAGING_REQUIREMENTS" property="returnPolicyPackagingRequirements" jdbcType="VARCHAR" />
    <result column="RETURN_POLICY_RETURN_COMMENTS" property="returnPolicyReturnComments" jdbcType="VARCHAR" />
    <result column="EXCHANGE_POLICY_SUPPORT_CHANGE" property="exchangePolicySupportChange" jdbcType="INTEGER" />
    <result column="EXCHANGE_POLICY_EXCHANGE_CONTITION" property="exchangePolicyExchangeContition" jdbcType="VARCHAR" />
    <result column="EXCHANGE_POLICY_NEED_IDENTIFY" property="exchangePolicyNeedIdentify" jdbcType="INTEGER" />
    <result column="EXCHANGE_POLICY_IDENTIFY_TYPE" property="exchangePolicyIdentifyType" jdbcType="VARCHAR" />
    <result column="EXCHANGE_POLICY_EXCHANGE_PERIOD" property="exchangePolicyExchangePeriod" jdbcType="VARCHAR" />
    <result column="EXCHANGE_POLICY_CYCLE_CALTYP" property="exchangePolicyCycleCaltyp" jdbcType="INTEGER" />
    <result column="EXCHANGE_POLICY_PACKAGING_REQUIREMENTS" property="exchangePolicyPackagingRequirements" jdbcType="VARCHAR" />
    <result column="EXCHANGE_POLICY_EXCHANGE_COMMENTS" property="exchangePolicyExchangeComments" jdbcType="VARCHAR" />
    <result column="PAROLE_POLICY_SUPPORT_REPAIR" property="parolePolicySupportRepair" jdbcType="INTEGER" />
    <result column="PAROLE_POLICY_SUPPORT_RENOVATION" property="parolePolicySupportRenovation" jdbcType="INTEGER" />
    <result column="PAROLE_POLICY_SUPPLY_BOX" property="parolePolicySupplyBox" jdbcType="INTEGER" />
    <result column="PAROLE_POLICY_SUPPLY_ATTACHMENT" property="parolePolicySupplyAttachment" jdbcType="INTEGER" />
    <result column="OVERDUE_POLICY_SUPPLY_BACKUP" property="overduePolicySupplyBackup" jdbcType="VARCHAR" />
    <result column="OVERDUE_POLICY_DETAIL" property="overduePolicyDetail" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATOR" property="updator" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="VARCHAR" />
    <result column="MOD_TIME" property="modTime" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SUPPLY_POLICY_ID, SKU_NO, TRADER_ID, TRADER_NAME, SERVICE_PROVIDER_TYPE, INSTALL_POLICY_INSTALL_TYPE, INSTALL_POLICY_INSTALL_AREA,
    INSTALL_POLICY_INSTALL_FEE, INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION, INSTALL_POLICY_FREE_REMOTE_INSTALL, 
    INSTALL_POLICY_RESPONSE_TIME, INSTALL_POLICY_VISIT_TIME, INSTALL_POLICY_INSTALL_TIME, 
    TECHNICAL_DIRECT_SUPPLY_MAINTAIN, TECHNICAL_DIRECT_RESPONSE_TIME, TECHNICAL_DIRECT_EFFECT_TIME, 
    GUARANTEE_POLICY_IS_GUARANTEE, GUARANTEE_POLICY_GUARANTEE_TYPE, GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD, 
    GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD, GUARANTEE_POLICY_CYCLE_CALTYPE, GUARANTEE_POLICY_AREA, 
    GUARANTEE_POLICY_AREA_COMMENT, GUARANTEE_POLICY_RESPONSE_TIME, GUARANTEE_POLICY_VISIT_TIME, 
    GUARANTEE_POLICY_REPAIRE_TIME, GUARANTEE_POLICY_REPAIRE_COMMENT, RETURN_POLICY_SUPPORT_RETURN, 
    RETURN_POLICY_CONDITION, RETURN_POLICY_NEED_IDENTIFY, RETURN_POLICY_IDENTIFY_TYPE, 
    RETURN_POLICY_RETURN_PERIOD, RETURN_POLICY_CYCLE_CALTYP, RETURN_POLICY_PACKAGING_REQUIREMENTS, 
    RETURN_POLICY_RETURN_COMMENTS, EXCHANGE_POLICY_SUPPORT_CHANGE, EXCHANGE_POLICY_EXCHANGE_CONTITION, 
    EXCHANGE_POLICY_NEED_IDENTIFY, EXCHANGE_POLICY_IDENTIFY_TYPE, EXCHANGE_POLICY_EXCHANGE_PERIOD, 
    EXCHANGE_POLICY_CYCLE_CALTYP, EXCHANGE_POLICY_PACKAGING_REQUIREMENTS, EXCHANGE_POLICY_EXCHANGE_COMMENTS, 
    PAROLE_POLICY_SUPPORT_REPAIR, PAROLE_POLICY_SUPPORT_RENOVATION, PAROLE_POLICY_SUPPLY_BOX, 
    PAROLE_POLICY_SUPPLY_ATTACHMENT, OVERDUE_POLICY_SUPPLY_BACKUP, OVERDUE_POLICY_DETAIL, 
    CREATOR, UPDATOR, ADD_TIME, MOD_TIME,REPAIR_PARTS_ACCESSORY,INSTALL_TRAINING,REPAIR_TRAINING,SERVICE_PERSONNEL_LEVEL
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SUPPLY_POLICY
    where SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT}
  </select>

  <select id="findLastestYearNumAndAmout" resultType="map">
      SELECT
        SUM(B.num-IFNULL(T.SHNUM,0)) as latestOneYearNum,
        SUM( (B.num-IFNULL(T.SHNUM,0)) * B.PRICE) latestOneYearAmout
      FROM T_BUYORDER A
      LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_ID = B.BUYORDER_ID AND B.IS_DELETE = 0
      LEFT JOIN (
          SELECT SUM(b.NUM) SHNUM,b.ORDER_DETAIL_ID
          FROM
              T_AFTER_SALES_GOODS b
          LEFT JOIN T_AFTER_SALES c ON b.AFTER_SALES_ID = c.AFTER_SALES_ID
          WHERE b.GOODS_TYPE = 0
          AND c.TYPE = 546
          AND c.SUBJECT_TYPE = 536
          AND c.ATFER_SALES_STATUS !=3
          AND b.GOODS_ID NOT IN (
              SELECT
                  COMMENTS
              FROM
                  T_SYS_OPTION_DEFINITION
              WHERE
                  PARENT_ID = 693
          )
          GROUP BY b.ORDER_DETAIL_ID
      ) T ON B.BUYORDER_GOODS_ID = T.ORDER_DETAIL_ID
      WHERE
        FROM_UNIXTIME( A.VALID_TIME / 1000, '%Y-%m-%d %H:%i:%S' )> DATE_SUB(CURDATE(),INTERVAL 365 DAY)
      AND A.PAYMENT_STATUS IN (1,2)
      AND A.STATUS IN (1,2) AND B.SKU = #{skuNo,jdbcType=VARCHAR} AND A.TRADER_ID = #{traderId,jdbcType=BIGINT}
  </select>

  <select id="findSupplyAfterSalePolicyByTraderId" resultType="com.vedeng.aftersales.model.dto.AfterSaleSupplyPolicyDto" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SUPPLY_POLICY
    where TRADER_ID = #{traderId,jdbcType=BIGINT}
  </select>


  <select id="findSupplyAfterSalePolicy" resultType="com.vedeng.aftersales.model.dto.AfterSaleSupplyPolicyDto" parameterType="java.lang.Long" >
    select
      SUPPLY_POLICY_ID, policy.SKU_NO, TRADER_ID, TRADER_NAME, SERVICE_PROVIDER_TYPE, INSTALL_POLICY_INSTALL_TYPE, INSTALL_POLICY_INSTALL_AREA,
      INSTALL_POLICY_INSTALL_FEE, INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION, INSTALL_POLICY_FREE_REMOTE_INSTALL,
      INSTALL_POLICY_RESPONSE_TIME, INSTALL_POLICY_VISIT_TIME, INSTALL_POLICY_INSTALL_TIME,
      TECHNICAL_DIRECT_SUPPLY_MAINTAIN, TECHNICAL_DIRECT_RESPONSE_TIME, TECHNICAL_DIRECT_EFFECT_TIME,
      GUARANTEE_POLICY_IS_GUARANTEE, GUARANTEE_POLICY_GUARANTEE_TYPE, GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD,
      GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD, GUARANTEE_POLICY_CYCLE_CALTYPE, GUARANTEE_POLICY_AREA,
      GUARANTEE_POLICY_AREA_COMMENT, GUARANTEE_POLICY_RESPONSE_TIME, GUARANTEE_POLICY_VISIT_TIME,
      GUARANTEE_POLICY_REPAIRE_TIME, GUARANTEE_POLICY_REPAIRE_COMMENT, RETURN_POLICY_SUPPORT_RETURN,
      RETURN_POLICY_CONDITION, RETURN_POLICY_NEED_IDENTIFY, RETURN_POLICY_IDENTIFY_TYPE,
      RETURN_POLICY_RETURN_PERIOD, RETURN_POLICY_CYCLE_CALTYP, RETURN_POLICY_PACKAGING_REQUIREMENTS,
      RETURN_POLICY_RETURN_COMMENTS, EXCHANGE_POLICY_SUPPORT_CHANGE, EXCHANGE_POLICY_EXCHANGE_CONTITION,
      EXCHANGE_POLICY_NEED_IDENTIFY, EXCHANGE_POLICY_IDENTIFY_TYPE, EXCHANGE_POLICY_EXCHANGE_PERIOD,
      EXCHANGE_POLICY_CYCLE_CALTYP, EXCHANGE_POLICY_PACKAGING_REQUIREMENTS, EXCHANGE_POLICY_EXCHANGE_COMMENTS,
      PAROLE_POLICY_SUPPORT_REPAIR, PAROLE_POLICY_SUPPORT_RENOVATION, PAROLE_POLICY_SUPPLY_BOX,
      PAROLE_POLICY_SUPPLY_ATTACHMENT, OVERDUE_POLICY_SUPPLY_BACKUP, OVERDUE_POLICY_DETAIL,
      sku.SHOW_NAME,REPAIR_PARTS_ACCESSORY,INSTALL_TRAINING,REPAIR_TRAINING,SERVICE_PERSONNEL_LEVEL
    from T_AFTER_SALE_SUPPLY_POLICY policy
    left join V_CORE_SKU sku on policy.SKU_NO = sku.SKU_NO
    where SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT}
  </select>


  <select id="querySupplyAfterSalePolicyListPage" parameterType="Map"
          resultType="com.vedeng.aftersales.model.dto.CopySupplyAfterSalePolicyDto">
    SELECT
      sku.SKU_NO,
      sku.SHOW_NAME,
      brand.BRAND_NAME,
      sku.SPEC,
      register.REGISTRATION_NUMBER
    FROM V_CORE_SKU sku
    LEFT JOIN V_CORE_SPU spu ON sku.SPU_ID = spu.SPU_ID
    LEFT JOIN T_FIRST_ENGAGE engage ON spu.FIRST_ENGAGE_ID = engage.FIRST_ENGAGE_ID
    LEFT JOIN T_REGISTRATION_NUMBER register ON engage.REGISTRATION_NUMBER_ID = register.REGISTRATION_NUMBER_ID
    LEFT JOIN T_PRODUCT_COMPANY company ON company.PRODUCT_COMPANY_ID = register.PRODUCT_COMPANY_ID
    LEFT JOIN T_BRAND brand ON spu.BRAND_ID = brand.BRAND_ID
    <where>
      <if test="queryDto.keyWord1!=null and queryDto.keyWord1!=''">
        (  sku.sku_no   LIKE CONCAT('%',#{queryDto.keyWord1,jdbcType=VARCHAR},'%')
        or sku.sku_name LIKE CONCAT('%',#{queryDto.keyWord1,jdbcType=VARCHAR},'%')
        )
      </if>
      <if test="queryDto.keyWord2 != null and queryDto.keyWord2!=''">
        AND (
          register.REGISTRATION_NUMBER LIKE CONCAT('%',#{queryDto.keyWord2,jdbcType=VARCHAR},'%')
        )
      </if>
      <if test="queryDto.brandName != null">
        AND brand.BRAND_NAME LIKE CONCAT('%',#{queryDto.brandName,jdbcType=VARCHAR},'%')
      </if>

      <if test="queryDto.policyMatained != '-1'">
        <if test="queryDto.policyMatained == 0">
          and (select count(*) from T_AFTER_SALE_SUPPLY_POLICY where SKU_NO = sku.SKU_NO and TRADER_ID = #{queryDto.traderId,jdbcType=BIGINT})  = 0
        </if>
        <if test="queryDto.policyMatained == 1">
          and (select count(*) from T_AFTER_SALE_SUPPLY_POLICY where SKU_NO = sku.SKU_NO and TRADER_ID = #{queryDto.traderId,jdbcType=BIGINT})  > 0
        </if>
      </if>
    </where>
    order by sku.MOD_TIME desc
  </select>



  <select id="getSupplyAfterSalePolicyListBySkuNo" resultType="com.vedeng.aftersales.model.dto.AfterSaleSupplyPolicyDto" parameterType="java.lang.String" >
    select
      <include refid="Base_Column_List" />
    from T_AFTER_SALE_SUPPLY_POLICY
    where SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </select>

  <select id="findSupplyPolicyBySkuNoAndServiceType" resultType="com.vedeng.aftersales.model.dto.AfterSaleSupplyPolicyDto">
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SUPPLY_POLICY
    where SKU_NO = #{skuNo,jdbcType=VARCHAR} and SERVICE_PROVIDER_TYPE = #{serviceType,jdbcType=INTEGER}
  </select>


  <select id="getBySkuNoAndTraderId" resultType="com.vedeng.aftersales.model.dto.AfterSaleSupplyPolicyDto">
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SUPPLY_POLICY
    where SKU_NO = #{skuNo,jdbcType=VARCHAR} and TRADER_ID = #{traderId,jdbcType=BIGINT}
  </select>


  <select id="referenceSupplyAfterSalePolicyListPage" resultType="com.vedeng.aftersales.model.dto.AfterSaleSupplyPolicyDto" parameterType="Map" >
    SELECT
      D.SUPPLY_POLICY_ID,
      A.SKU_NO,
      A.SKU_NAME,
      C.BRAND_NAME,
      A.MODEL,
      '-' AS bdAfterSaleServiceStandard,
      D.TRADER_ID,
      D.TRADER_NAME,
      (CASE WHEN D.SERVICE_PROVIDER_TYPE = 1 THEN '原厂服务'
      WHEN D.SERVICE_PROVIDER_TYPE = 2 THEN '该供应商提供服务' END)AS supplyAfterSaleServiceStandard,
      D.SERVICE_PROVIDER_TYPE
    from V_CORE_SKU A
    LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
    LEFT JOIN T_BRAND C ON B.BRAND_ID = C.BRAND_ID
    ,T_AFTER_SALE_SUPPLY_POLICY D
    WHERE A.SKU_NO = D.SKU_NO
    <if test="skuNo != null">
      AND (A.SKU_NO LIKE CONCAT('%',#{skuNo,jdbcType=VARCHAR},'%')
      OR A.SKU_NAME LIKE CONCAT('%',#{skuNo,jdbcType=VARCHAR},'%')
      OR C.BRAND_NAME LIKE CONCAT('%',#{skuNo,jdbcType=VARCHAR},'%')
      OR D.TRADER_NAME LIKE CONCAT('%',#{skuNo,jdbcType=VARCHAR},'%')
      )
    </if>
    UNION ALL
    SELECT
    D.SERVICE_STANDARD_APPLY_ID AS SUPPLY_POLICY_ID,
    A.SKU_NO,
    A.SKU_NAME,
    C.BRAND_NAME,
    A.MODEL,
    '贝登售后标准' AS bdAfterSaleServiceStandard,
    0 AS TRADER_ID,
    '_' AS TRADER_NAME,
    '-' AS supplyAfterSaleServiceStandard,
    0 AS SERVICE_PROVIDER_TYPE
    from V_CORE_SKU A
    LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
    LEFT JOIN T_BRAND C ON B.BRAND_ID = C.BRAND_ID
    ,T_AFTER_SALE_SERVICE_STANDARD_APPLY D
    WHERE A.SKU_NO = D.SKU_NO AND D.AFTER_SALE_STANDARD_STATUS = 2
    <if test="skuNo != null">
      AND (A.SKU_NO LIKE CONCAT('%',#{skuNo,jdbcType=VARCHAR},'%')
      OR A.SKU_NAME LIKE CONCAT('%',#{skuNo,jdbcType=VARCHAR},'%')
      OR C.BRAND_NAME LIKE CONCAT('%',#{skuNo,jdbcType=VARCHAR},'%')
      )
    </if>
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from T_AFTER_SALE_SUPPLY_POLICY
    where SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.vedeng.aftersales.model.AfterSaleSupplyPolicy" useGeneratedKeys="true" keyProperty="supplyPolicyId">
    insert into T_AFTER_SALE_SUPPLY_POLICY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="skuNo != null" >
        SKU_NO,
      </if>
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="traderName != null" >
        TRADER_NAME,
      </if>
      <if test="serviceProviderType != null" >
        SERVICE_PROVIDER_TYPE,
      </if>
      <if test="installPolicyInstallType != null" >
        INSTALL_POLICY_INSTALL_TYPE,
      </if>
      <if test="installPolicyInstallArea != null" >
        INSTALL_POLICY_INSTALL_AREA,
      </if>
      <if test="installPolicyInstallFee != null" >
        INSTALL_POLICY_INSTALL_FEE,
      </if>
      <if test="installPolicyHaveInstallationQualification != null" >
        INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION,
      </if>
      <if test="installPolicyFreeRemoteInstall != null" >
        INSTALL_POLICY_FREE_REMOTE_INSTALL,
      </if>
      <if test="installPolicyResponseTime != null" >
        INSTALL_POLICY_RESPONSE_TIME,
      </if>
      <if test="installPolicyVisitTime != null" >
        INSTALL_POLICY_VISIT_TIME,
      </if>
      <if test="installPolicyInstallTime != null" >
        INSTALL_POLICY_INSTALL_TIME,
      </if>
      <if test="technicalDirectSupplyMaintain != null" >
        TECHNICAL_DIRECT_SUPPLY_MAINTAIN,
      </if>
      <if test="technicalDirectResponseTime != null" >
        TECHNICAL_DIRECT_RESPONSE_TIME,
      </if>
      <if test="technicalDirectEffectTime != null" >
        TECHNICAL_DIRECT_EFFECT_TIME,
      </if>
      <if test="guaranteePolicyIsGuarantee != null" >
        GUARANTEE_POLICY_IS_GUARANTEE,
      </if>
      <if test="guaranteePolicyGuaranteeType != null" >
        GUARANTEE_POLICY_GUARANTEE_TYPE,
      </if>
      <if test="guaranteePolicyHostGuaranteePeriod != null" >
        GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD,
      </if>
      <if test="guaranteePolicyPartsGuaranteePeriod != null" >
        GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD,
      </if>
      <if test="guaranteePolicyCycleCaltype != null" >
        GUARANTEE_POLICY_CYCLE_CALTYPE,
      </if>
      <if test="guaranteePolicyArea != null" >
        GUARANTEE_POLICY_AREA,
      </if>
      <if test="guaranteePolicyAreaComment != null" >
        GUARANTEE_POLICY_AREA_COMMENT,
      </if>
      <if test="guaranteePolicyResponseTime != null" >
        GUARANTEE_POLICY_RESPONSE_TIME,
      </if>
      <if test="guaranteePolicyVisitTime != null" >
        GUARANTEE_POLICY_VISIT_TIME,
      </if>
      <if test="guaranteePolicyRepaireTime != null" >
        GUARANTEE_POLICY_REPAIRE_TIME,
      </if>
      <if test="guaranteePolicyRepaireComment != null" >
        GUARANTEE_POLICY_REPAIRE_COMMENT,
      </if>
      <if test="returnPolicySupportReturn != null" >
        RETURN_POLICY_SUPPORT_RETURN,
      </if>
      <if test="returnPolicyCondition != null" >
        RETURN_POLICY_CONDITION,
      </if>
      <if test="returnPolicyNeedIdentify != null" >
        RETURN_POLICY_NEED_IDENTIFY,
      </if>
      <if test="returnPolicyIdentifyType != null" >
        RETURN_POLICY_IDENTIFY_TYPE,
      </if>
      <if test="returnPolicyReturnPeriod != null" >
        RETURN_POLICY_RETURN_PERIOD,
      </if>
      <if test="returnPolicyCycleCaltyp != null" >
        RETURN_POLICY_CYCLE_CALTYP,
      </if>
      <if test="returnPolicyPackagingRequirements != null" >
        RETURN_POLICY_PACKAGING_REQUIREMENTS,
      </if>
      <if test="returnPolicyReturnComments != null" >
        RETURN_POLICY_RETURN_COMMENTS,
      </if>
      <if test="exchangePolicySupportChange != null" >
        EXCHANGE_POLICY_SUPPORT_CHANGE,
      </if>
      <if test="exchangePolicyExchangeContition != null" >
        EXCHANGE_POLICY_EXCHANGE_CONTITION,
      </if>
      <if test="exchangePolicyNeedIdentify != null" >
        EXCHANGE_POLICY_NEED_IDENTIFY,
      </if>
      <if test="exchangePolicyIdentifyType != null" >
        EXCHANGE_POLICY_IDENTIFY_TYPE,
      </if>
      <if test="exchangePolicyExchangePeriod != null" >
        EXCHANGE_POLICY_EXCHANGE_PERIOD,
      </if>
      <if test="exchangePolicyCycleCaltyp != null" >
        EXCHANGE_POLICY_CYCLE_CALTYP,
      </if>
      <if test="exchangePolicyPackagingRequirements != null" >
        EXCHANGE_POLICY_PACKAGING_REQUIREMENTS,
      </if>
      <if test="exchangePolicyExchangeComments != null" >
        EXCHANGE_POLICY_EXCHANGE_COMMENTS,
      </if>
      <if test="parolePolicySupportRepair != null" >
        PAROLE_POLICY_SUPPORT_REPAIR,
      </if>
      <if test="parolePolicySupportRenovation != null" >
        PAROLE_POLICY_SUPPORT_RENOVATION,
      </if>
      <if test="parolePolicySupplyBox != null" >
        PAROLE_POLICY_SUPPLY_BOX,
      </if>
      <if test="parolePolicySupplyAttachment != null" >
        PAROLE_POLICY_SUPPLY_ATTACHMENT,
      </if>
      <if test="overduePolicySupplyBackup != null" >
        OVERDUE_POLICY_SUPPLY_BACKUP,
      </if>
      <if test="overduePolicyDetail != null" >
        OVERDUE_POLICY_DETAIL,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>

      <if test="repairPartsAccessory != null  ">
        REPAIR_PARTS_ACCESSORY  ,
      </if>
      <if test="installTraining != null  ">
        INSTALL_TRAINING  ,
      </if>
      <if test="repairTraining != null  ">
        REPAIR_TRAINING  ,
      </if>
      <if test="servicePersonnelLevel != null  ">
        SERVICE_PERSONNEL_LEVEL ,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="skuNo != null" >
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null" >
        #{traderId,jdbcType=BIGINT},
      </if>
      <if test="traderName != null" >
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="serviceProviderType != null" >
        #{serviceProviderType,jdbcType=INTEGER},
      </if>
      <if test="installPolicyInstallType != null" >
        #{installPolicyInstallType,jdbcType=INTEGER},
      </if>
      <if test="installPolicyInstallArea != null" >
        #{installPolicyInstallArea,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallFee != null" >
        #{installPolicyInstallFee,jdbcType=DECIMAL},
      </if>
      <if test="installPolicyHaveInstallationQualification != null" >
        #{installPolicyHaveInstallationQualification,jdbcType=INTEGER},
      </if>
      <if test="installPolicyFreeRemoteInstall != null" >
        #{installPolicyFreeRemoteInstall,jdbcType=INTEGER},
      </if>
      <if test="installPolicyResponseTime != null" >
        #{installPolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyVisitTime != null" >
        #{installPolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallTime != null" >
        #{installPolicyInstallTime,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectSupplyMaintain != null" >
        #{technicalDirectSupplyMaintain,jdbcType=INTEGER},
      </if>
      <if test="technicalDirectResponseTime != null" >
        #{technicalDirectResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectEffectTime != null" >
        #{technicalDirectEffectTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyIsGuarantee != null" >
        #{guaranteePolicyIsGuarantee,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyGuaranteeType != null" >
        #{guaranteePolicyGuaranteeType,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyHostGuaranteePeriod != null" >
        #{guaranteePolicyHostGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyPartsGuaranteePeriod != null" >
        #{guaranteePolicyPartsGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyCycleCaltype != null" >
        #{guaranteePolicyCycleCaltype,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyArea != null" >
        #{guaranteePolicyArea,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyAreaComment != null" >
        #{guaranteePolicyAreaComment,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyResponseTime != null" >
        #{guaranteePolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyVisitTime != null" >
        #{guaranteePolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireTime != null" >
        #{guaranteePolicyRepaireTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireComment != null" >
        #{guaranteePolicyRepaireComment,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicySupportReturn != null" >
        #{returnPolicySupportReturn,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyCondition != null" >
        #{returnPolicyCondition,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyNeedIdentify != null" >
        #{returnPolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyIdentifyType != null" >
        #{returnPolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnPeriod != null" >
        #{returnPolicyReturnPeriod,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyCycleCaltyp != null" >
        #{returnPolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyPackagingRequirements != null" >
        #{returnPolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnComments != null" >
        #{returnPolicyReturnComments,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicySupportChange != null" >
        #{exchangePolicySupportChange,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyExchangeContition != null" >
        #{exchangePolicyExchangeContition,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyNeedIdentify != null" >
        #{exchangePolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyIdentifyType != null" >
        #{exchangePolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangePeriod != null" >
        #{exchangePolicyExchangePeriod,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyCycleCaltyp != null" >
        #{exchangePolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyPackagingRequirements != null" >
        #{exchangePolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangeComments != null" >
        #{exchangePolicyExchangeComments,jdbcType=VARCHAR},
      </if>
      <if test="parolePolicySupportRepair != null" >
        #{parolePolicySupportRepair,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupportRenovation != null" >
        #{parolePolicySupportRenovation,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyBox != null" >
        #{parolePolicySupplyBox,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyAttachment != null" >
        #{parolePolicySupplyAttachment,jdbcType=INTEGER},
      </if>
      <if test="overduePolicySupplyBackup != null" >
        #{overduePolicySupplyBackup,jdbcType=VARCHAR},
      </if>
      <if test="overduePolicyDetail != null" >
        #{overduePolicyDetail,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=VARCHAR},
      </if>
      <if test="repairPartsAccessory != null  ">
        #{repairPartsAccessory,jdbcType=INTEGER},
      </if>
      <if test="installTraining != null  ">
        #{installTraining,jdbcType=INTEGER},
      </if>
      <if test="repairTraining != null  ">
        #{repairTraining,jdbcType=INTEGER},
      </if>
      <if test="servicePersonnelLevel != null  ">
        #{servicePersonnelLevel,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.aftersales.model.AfterSaleSupplyPolicy" >
    update T_AFTER_SALE_SUPPLY_POLICY
    <set >
      <if test="skuNo != null and skuNo != ''" >
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=BIGINT},
      </if>
      <if test="traderName != null" >
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="serviceProviderType != null" >
        SERVICE_PROVIDER_TYPE = #{serviceProviderType,jdbcType=INTEGER},
      </if>
      <if test="installPolicyInstallArea != null" >
        INSTALL_POLICY_INSTALL_AREA = #{installPolicyInstallArea,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallType != null" >
        INSTALL_POLICY_INSTALL_TYPE = #{installPolicyInstallType,jdbcType=INTEGER},
      </if>
      <if test="installPolicyInstallFee != null" >
        INSTALL_POLICY_INSTALL_FEE = #{installPolicyInstallFee,jdbcType=DECIMAL},
      </if>
      <if test="installPolicyHaveInstallationQualification != null" >
        INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION = #{installPolicyHaveInstallationQualification,jdbcType=INTEGER},
      </if>
      <if test="installPolicyFreeRemoteInstall != null" >
        INSTALL_POLICY_FREE_REMOTE_INSTALL = #{installPolicyFreeRemoteInstall,jdbcType=INTEGER},
      </if>
      <if test="installPolicyResponseTime != null" >
        INSTALL_POLICY_RESPONSE_TIME = #{installPolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyVisitTime != null" >
        INSTALL_POLICY_VISIT_TIME = #{installPolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallTime != null" >
        INSTALL_POLICY_INSTALL_TIME = #{installPolicyInstallTime,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectSupplyMaintain != null" >
        TECHNICAL_DIRECT_SUPPLY_MAINTAIN = #{technicalDirectSupplyMaintain,jdbcType=INTEGER},
      </if>
      <if test="technicalDirectResponseTime != null" >
        TECHNICAL_DIRECT_RESPONSE_TIME = #{technicalDirectResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectEffectTime != null" >
        TECHNICAL_DIRECT_EFFECT_TIME = #{technicalDirectEffectTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyIsGuarantee != null" >
        GUARANTEE_POLICY_IS_GUARANTEE = #{guaranteePolicyIsGuarantee,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyGuaranteeType != null" >
        GUARANTEE_POLICY_GUARANTEE_TYPE = #{guaranteePolicyGuaranteeType,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyHostGuaranteePeriod != null" >
        GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD = #{guaranteePolicyHostGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyPartsGuaranteePeriod != null" >
        GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD = #{guaranteePolicyPartsGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyCycleCaltype != null" >
        GUARANTEE_POLICY_CYCLE_CALTYPE = #{guaranteePolicyCycleCaltype,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyArea != null" >
        GUARANTEE_POLICY_AREA = #{guaranteePolicyArea,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyAreaComment != null" >
        GUARANTEE_POLICY_AREA_COMMENT = #{guaranteePolicyAreaComment,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyResponseTime != null" >
        GUARANTEE_POLICY_RESPONSE_TIME = #{guaranteePolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyVisitTime != null" >
        GUARANTEE_POLICY_VISIT_TIME = #{guaranteePolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireTime != null" >
        GUARANTEE_POLICY_REPAIRE_TIME = #{guaranteePolicyRepaireTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireComment != null" >
        GUARANTEE_POLICY_REPAIRE_COMMENT = #{guaranteePolicyRepaireComment,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicySupportReturn != null" >
        RETURN_POLICY_SUPPORT_RETURN = #{returnPolicySupportReturn,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyCondition != null" >
        RETURN_POLICY_CONDITION = #{returnPolicyCondition,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyNeedIdentify != null" >
        RETURN_POLICY_NEED_IDENTIFY = #{returnPolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyIdentifyType != null" >
        RETURN_POLICY_IDENTIFY_TYPE = #{returnPolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnPeriod != null" >
        RETURN_POLICY_RETURN_PERIOD = #{returnPolicyReturnPeriod,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyCycleCaltyp != null" >
        RETURN_POLICY_CYCLE_CALTYP = #{returnPolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyPackagingRequirements != null" >
        RETURN_POLICY_PACKAGING_REQUIREMENTS = #{returnPolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnComments != null" >
        RETURN_POLICY_RETURN_COMMENTS = #{returnPolicyReturnComments,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicySupportChange != null" >
        EXCHANGE_POLICY_SUPPORT_CHANGE = #{exchangePolicySupportChange,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyExchangeContition != null" >
        EXCHANGE_POLICY_EXCHANGE_CONTITION = #{exchangePolicyExchangeContition,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyNeedIdentify != null" >
        EXCHANGE_POLICY_NEED_IDENTIFY = #{exchangePolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyIdentifyType != null" >
        EXCHANGE_POLICY_IDENTIFY_TYPE = #{exchangePolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangePeriod != null" >
        EXCHANGE_POLICY_EXCHANGE_PERIOD = #{exchangePolicyExchangePeriod,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyCycleCaltyp != null" >
        EXCHANGE_POLICY_CYCLE_CALTYP = #{exchangePolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyPackagingRequirements != null" >
        EXCHANGE_POLICY_PACKAGING_REQUIREMENTS = #{exchangePolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangeComments != null" >
        EXCHANGE_POLICY_EXCHANGE_COMMENTS = #{exchangePolicyExchangeComments,jdbcType=VARCHAR},
      </if>
      <if test="parolePolicySupportRepair != null" >
        PAROLE_POLICY_SUPPORT_REPAIR = #{parolePolicySupportRepair,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupportRenovation != null" >
        PAROLE_POLICY_SUPPORT_RENOVATION = #{parolePolicySupportRenovation,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyBox != null" >
        PAROLE_POLICY_SUPPLY_BOX = #{parolePolicySupplyBox,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyAttachment != null" >
        PAROLE_POLICY_SUPPLY_ATTACHMENT = #{parolePolicySupplyAttachment,jdbcType=INTEGER},
      </if>
      <if test="overduePolicySupplyBackup != null" >
        OVERDUE_POLICY_SUPPLY_BACKUP = #{overduePolicySupplyBackup,jdbcType=VARCHAR},
      </if>
      <if test="overduePolicyDetail != null" >
        OVERDUE_POLICY_DETAIL = #{overduePolicyDetail,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=VARCHAR},
      </if>
    </set>
    where SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.aftersales.model.AfterSaleSupplyPolicy" >
    update T_AFTER_SALE_SUPPLY_POLICY
    set SKU_NO = #{skuNo,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=BIGINT},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      SERVICE_PROVIDER_TYPE = #{serviceProviderType,jdbcType=INTEGER},
      INSTALL_POLICY_INSTALL_TYPE = #{installPolicyInstallType,jdbcType=INTEGER},
      INSTALL_POLICY_INSTALL_AREA = #{installPolicyInstallArea,jdbcType=VARCHAR},
      INSTALL_POLICY_INSTALL_FEE = #{installPolicyInstallFee,jdbcType=DECIMAL},
      INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION = #{installPolicyHaveInstallationQualification,jdbcType=INTEGER},
      INSTALL_POLICY_FREE_REMOTE_INSTALL = #{installPolicyFreeRemoteInstall,jdbcType=INTEGER},
      INSTALL_POLICY_RESPONSE_TIME = #{installPolicyResponseTime,jdbcType=VARCHAR},
      INSTALL_POLICY_VISIT_TIME = #{installPolicyVisitTime,jdbcType=VARCHAR},
      INSTALL_POLICY_INSTALL_TIME = #{installPolicyInstallTime,jdbcType=VARCHAR},
      TECHNICAL_DIRECT_SUPPLY_MAINTAIN = #{technicalDirectSupplyMaintain,jdbcType=INTEGER},
      TECHNICAL_DIRECT_RESPONSE_TIME = #{technicalDirectResponseTime,jdbcType=VARCHAR},
      TECHNICAL_DIRECT_EFFECT_TIME = #{technicalDirectEffectTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_IS_GUARANTEE = #{guaranteePolicyIsGuarantee,jdbcType=INTEGER},
      GUARANTEE_POLICY_GUARANTEE_TYPE = #{guaranteePolicyGuaranteeType,jdbcType=INTEGER},
      GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD = #{guaranteePolicyHostGuaranteePeriod,jdbcType=VARCHAR},
      GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD = #{guaranteePolicyPartsGuaranteePeriod,jdbcType=VARCHAR},
      GUARANTEE_POLICY_CYCLE_CALTYPE = #{guaranteePolicyCycleCaltype,jdbcType=INTEGER},
      GUARANTEE_POLICY_AREA = #{guaranteePolicyArea,jdbcType=INTEGER},
      GUARANTEE_POLICY_AREA_COMMENT = #{guaranteePolicyAreaComment,jdbcType=VARCHAR},
      GUARANTEE_POLICY_RESPONSE_TIME = #{guaranteePolicyResponseTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_VISIT_TIME = #{guaranteePolicyVisitTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_REPAIRE_TIME = #{guaranteePolicyRepaireTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_REPAIRE_COMMENT = #{guaranteePolicyRepaireComment,jdbcType=VARCHAR},
      RETURN_POLICY_SUPPORT_RETURN = #{returnPolicySupportReturn,jdbcType=INTEGER},
      RETURN_POLICY_CONDITION = #{returnPolicyCondition,jdbcType=VARCHAR},
      RETURN_POLICY_NEED_IDENTIFY = #{returnPolicyNeedIdentify,jdbcType=INTEGER},
      RETURN_POLICY_IDENTIFY_TYPE = #{returnPolicyIdentifyType,jdbcType=VARCHAR},
      RETURN_POLICY_RETURN_PERIOD = #{returnPolicyReturnPeriod,jdbcType=INTEGER},
      RETURN_POLICY_CYCLE_CALTYP = #{returnPolicyCycleCaltyp,jdbcType=INTEGER},
      RETURN_POLICY_PACKAGING_REQUIREMENTS = #{returnPolicyPackagingRequirements,jdbcType=VARCHAR},
      RETURN_POLICY_RETURN_COMMENTS = #{returnPolicyReturnComments,jdbcType=VARCHAR},
      EXCHANGE_POLICY_SUPPORT_CHANGE = #{exchangePolicySupportChange,jdbcType=INTEGER},
      EXCHANGE_POLICY_EXCHANGE_CONTITION = #{exchangePolicyExchangeContition,jdbcType=VARCHAR},
      EXCHANGE_POLICY_NEED_IDENTIFY = #{exchangePolicyNeedIdentify,jdbcType=INTEGER},
      EXCHANGE_POLICY_IDENTIFY_TYPE = #{exchangePolicyIdentifyType,jdbcType=INTEGER},
      EXCHANGE_POLICY_EXCHANGE_PERIOD = #{exchangePolicyExchangePeriod,jdbcType=VARCHAR},
      EXCHANGE_POLICY_CYCLE_CALTYP = #{exchangePolicyCycleCaltyp,jdbcType=INTEGER},
      EXCHANGE_POLICY_PACKAGING_REQUIREMENTS = #{exchangePolicyPackagingRequirements,jdbcType=VARCHAR},
      EXCHANGE_POLICY_EXCHANGE_COMMENTS = #{exchangePolicyExchangeComments,jdbcType=VARCHAR},
      PAROLE_POLICY_SUPPORT_REPAIR = #{parolePolicySupportRepair,jdbcType=INTEGER},
      PAROLE_POLICY_SUPPORT_RENOVATION = #{parolePolicySupportRenovation,jdbcType=INTEGER},
      PAROLE_POLICY_SUPPLY_BOX = #{parolePolicySupplyBox,jdbcType=INTEGER},
      PAROLE_POLICY_SUPPLY_ATTACHMENT = #{parolePolicySupplyAttachment,jdbcType=INTEGER},
      OVERDUE_POLICY_SUPPLY_BACKUP = #{overduePolicySupplyBackup,jdbcType=VARCHAR},
      OVERDUE_POLICY_DETAIL = #{overduePolicyDetail,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      UPDATOR = #{updator,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=VARCHAR}
            ,
        REPAIR_PARTS_ACCESSORY = #{repairPartsAccessory,jdbcType=INTEGER},

        INSTALL_TRAINING = #{installTraining,jdbcType=INTEGER},

        REPAIR_TRAINING = #{repairTraining,jdbcType=INTEGER},

        SERVICE_PERSONNEL_LEVEL = #{servicePersonnelLevel,jdbcType=INTEGER}



    where SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT}
  </update>
</mapper>