<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.temporal.mapper.TemporalFlowOrderInfoMapper">

  <!-- 结果映射 -->
  <resultMap id="BaseResultMap" type="com.vedeng.temporal.domain.entity.FlowOrderInfoEntity">
    <id column="FLOW_ORDER_INFO_ID" property="flowOrderInfoId" />
    <result column="FLOW_ORDER_INFO_TYPE" property="flowOrderInfoType" />
    <result column="FLOW_ORDER_INFO_NO" property="flowOrderInfoNo" />
    <result column="FLOW_NODE_ID" property="flowNodeId" />
    <result column="ORDER_STATUS" property="orderStatus" />
    <result column="PAYMENT_STATUS" property="paymentStatus" />
    <result column="STORAGE_STATUS" property="storageStatus" />
    <result column="INVOICE_STATUS" property="invoiceStatus" />
    <result column="INVOICE_INFO" property="invoiceInfo" />
    <result column="IS_DELETE" property="isDelete" />
    <result column="CREATOR" property="creator" />
    <result column="UPDATER" property="updater" />
    <result column="CREATOR_NAME" property="creatorName" />
    <result column="UPDATER_NAME" property="updaterName" />
    <result column="ADD_TIME" property="addTime" />
    <result column="MOD_TIME" property="modTime" />
    <result column="CONTRACT_FILE_URL" property="contractFileUrl" />
    <result column="CONTRACT_FILE_NAME" property="contractFileName" />
    <result column="CONTRACT_UPLOAD_TIME" property="contractUploadTime" />
  </resultMap>

  <!-- 基础字段列表 -->
  <sql id="Base_Column_List">
    FLOW_ORDER_INFO_ID, FLOW_ORDER_INFO_TYPE, FLOW_ORDER_INFO_NO, FLOW_NODE_ID,
    ORDER_STATUS, PAYMENT_STATUS, STORAGE_STATUS, INVOICE_STATUS, INVOICE_INFO,
    IS_DELETE, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, ADD_TIME, MOD_TIME,
    CONTRACT_FILE_URL, CONTRACT_FILE_NAME, CONTRACT_UPLOAD_TIME
  </sql>

  <!-- 根据主键查询 -->
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER_INFO
    WHERE FLOW_ORDER_INFO_ID = #{flowOrderInfoId}
  </select>

  <!-- 根据节点ID查询 -->
  <select id="selectByFlowNodeId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER_INFO
    WHERE FLOW_NODE_ID = #{flowNodeId}
    AND IS_DELETE = 0
  </select>

  <!-- 根据节点ID查询所有记录 -->
  <select id="selectListByFlowNodeId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER_INFO
    WHERE FLOW_NODE_ID = #{flowNodeId}
    AND IS_DELETE = 0
    ORDER BY FLOW_ORDER_INFO_TYPE, ADD_TIME DESC
  </select>

  <!-- 根据节点ID和业务类型查询记录列表 -->
  <select id="selectListByFlowNodeIdAndType" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER_INFO
    WHERE FLOW_NODE_ID = #{flowNodeId}
    AND FLOW_ORDER_INFO_TYPE = #{businessType}
    AND IS_DELETE = 0
    ORDER BY ADD_TIME DESC
  </select>

  <!-- 根据业务编号查询 -->
  <select id="selectByBusinessNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER_INFO
    WHERE FLOW_ORDER_INFO_NO = #{businessNo}
    AND IS_DELETE = 0
  </select>

  <!-- 根据流转单编号查询所有相关的FlowOrderInfo记录 -->
  <select id="selectByFlowOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
    foi.FLOW_ORDER_INFO_ID, foi.FLOW_ORDER_INFO_TYPE, foi.FLOW_ORDER_INFO_NO, foi.FLOW_NODE_ID,
    foi.ORDER_STATUS, foi.PAYMENT_STATUS, foi.STORAGE_STATUS, foi.INVOICE_STATUS, foi.INVOICE_INFO,
    foi.IS_DELETE, foi.CREATOR, foi.UPDATER, foi.CREATOR_NAME, foi.UPDATER_NAME, foi.ADD_TIME, foi.MOD_TIME,
    foi.CONTRACT_FILE_URL, foi.CONTRACT_FILE_NAME, foi.CONTRACT_UPLOAD_TIME
    FROM T_FLOW_ORDER_INFO foi
    INNER JOIN T_FLOW_NODE fn ON foi.FLOW_NODE_ID = fn.FLOW_NODE_ID
    INNER JOIN T_FLOW_ORDER fo ON fn.FLOW_ORDER_ID = fo.FLOW_ORDER_ID
    WHERE fo.FLOW_ORDER_NO = #{flowOrderNo}
      AND foi.IS_DELETE = 0
      AND fn.IS_DELETE = 0
      AND fo.IS_DELETE = 0
    ORDER BY foi.FLOW_ORDER_INFO_TYPE, foi.ADD_TIME DESC
  </select>

  <!-- 根据业务类型查询列表 -->
  <select id="selectByBusinessType" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER_INFO
    WHERE FLOW_ORDER_INFO_TYPE = #{businessType}
    AND IS_DELETE = 0
    ORDER BY ADD_TIME DESC
  </select>

  <select id="selectByBusinessTypeAndFlowNodeId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER_INFO
    WHERE FLOW_ORDER_INFO_TYPE = #{businessType}
    and FLOW_NODE_ID = #{flowNodeId}
    AND IS_DELETE = 0
    LIMIT 1
  </select>



  <!-- 插入新记录（选择性插入） -->
  <insert id="insertSelective" keyColumn="FLOW_ORDER_INFO_ID" keyProperty="flowOrderInfoId" 
          parameterType="com.vedeng.temporal.domain.entity.FlowOrderInfoEntity" useGeneratedKeys="true">
    INSERT INTO T_FLOW_ORDER_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowOrderInfoType != null">FLOW_ORDER_INFO_TYPE,</if>
      <if test="flowOrderInfoNo != null">FLOW_ORDER_INFO_NO,</if>
      <if test="flowNodeId != null">FLOW_NODE_ID,</if>
      <if test="orderStatus != null">ORDER_STATUS,</if>
      <if test="paymentStatus != null">PAYMENT_STATUS,</if>
      <if test="storageStatus != null">STORAGE_STATUS,</if>
      <if test="invoiceStatus != null">INVOICE_STATUS,</if>
      <if test="invoiceInfo != null">INVOICE_INFO,</if>
      <if test="contractFileUrl != null">CONTRACT_FILE_URL,</if>
      <if test="contractFileName != null">CONTRACT_FILE_NAME,</if>
      <if test="contractUploadTime != null">CONTRACT_UPLOAD_TIME,</if>
      <if test="isDelete != null">IS_DELETE,</if>
      <if test="creator != null">CREATOR,</if>
      <if test="updater != null">UPDATER,</if>
      <if test="creatorName != null">CREATOR_NAME,</if>
      <if test="updaterName != null">UPDATER_NAME,</if>
      <if test="addTime != null">ADD_TIME,</if>
      <if test="modTime != null">MOD_TIME</if>
    </trim>
    <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
      <if test="flowOrderInfoType != null">#{flowOrderInfoType},</if>
      <if test="flowOrderInfoNo != null">#{flowOrderInfoNo},</if>
      <if test="flowNodeId != null">#{flowNodeId},</if>
      <if test="orderStatus != null">#{orderStatus},</if>
      <if test="paymentStatus != null">#{paymentStatus},</if>
      <if test="storageStatus != null">#{storageStatus},</if>
      <if test="invoiceStatus != null">#{invoiceStatus},</if>
      <if test="invoiceInfo != null">#{invoiceInfo},</if>
      <if test="contractFileUrl != null">#{contractFileUrl},</if>
      <if test="contractFileName != null">#{contractFileName},</if>
      <if test="contractUploadTime != null">#{contractUploadTime},</if>
      <if test="isDelete != null">#{isDelete},</if>
      <if test="creator != null">#{creator},</if>
      <if test="updater != null">#{updater},</if>
      <if test="creatorName != null">#{creatorName},</if>
      <if test="updaterName != null">#{updaterName},</if>
      <if test="addTime != null">#{addTime},</if>
      <if test="modTime != null">#{modTime}</if>
    </trim>
  </insert>

  <!-- 插入新记录 -->
  <insert id="insert" keyColumn="FLOW_ORDER_INFO_ID" keyProperty="flowOrderInfoId" 
          parameterType="com.vedeng.temporal.domain.entity.FlowOrderInfoEntity" useGeneratedKeys="true">
    INSERT INTO T_FLOW_ORDER_INFO (
      FLOW_ORDER_INFO_TYPE, FLOW_ORDER_INFO_NO, FLOW_NODE_ID,
      ORDER_STATUS, PAYMENT_STATUS, STORAGE_STATUS, INVOICE_STATUS, INVOICE_INFO,
      CONTRACT_FILE_URL, CONTRACT_FILE_NAME, CONTRACT_UPLOAD_TIME,
      IS_DELETE, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, ADD_TIME, MOD_TIME
    ) VALUES (
      #{flowOrderInfoType}, #{flowOrderInfoNo}, #{flowNodeId},
      #{orderStatus}, #{paymentStatus}, #{storageStatus}, #{invoiceStatus}, #{invoiceInfo},
      #{contractFileUrl}, #{contractFileName}, #{contractUploadTime},
      #{isDelete}, #{creator}, #{updater}, #{creatorName}, #{updaterName}, #{addTime}, #{modTime}
    )
  </insert>

  <!-- 根据主键更新 -->
  <update id="updateByPrimaryKey" parameterType="com.vedeng.temporal.domain.entity.FlowOrderInfoEntity">
    UPDATE T_FLOW_ORDER_INFO
    SET 
      FLOW_ORDER_INFO_TYPE = #{flowOrderInfoType},
      FLOW_ORDER_INFO_NO = #{flowOrderInfoNo},
      FLOW_NODE_ID = #{flowNodeId},
      ORDER_STATUS = #{orderStatus},
      PAYMENT_STATUS = #{paymentStatus},
      STORAGE_STATUS = #{storageStatus},
      INVOICE_STATUS = #{invoiceStatus},
      INVOICE_INFO = #{invoiceInfo},
      CONTRACT_FILE_URL = #{contractFileUrl},
      CONTRACT_FILE_NAME = #{contractFileName},
      CONTRACT_UPLOAD_TIME = #{contractUploadTime},
      UPDATER = #{updater},
      UPDATER_NAME = #{updaterName},
      MOD_TIME = #{modTime}
    WHERE FLOW_ORDER_INFO_ID = #{flowOrderInfoId}
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.temporal.domain.entity.FlowOrderInfoEntity">
    UPDATE T_FLOW_ORDER_INFO
    SET
    <if test="flowOrderInfoType != null">FLOW_ORDER_INFO_TYPE = #{flowOrderInfoType},</if>
    <if test="flowOrderInfoNo != null">FLOW_ORDER_INFO_NO = #{flowOrderInfoNo},</if>
    <if test="orderStatus != null">ORDER_STATUS = #{orderStatus},</if>
    <if test="paymentStatus != null">PAYMENT_STATUS = #{paymentStatus},</if>
    <if test="storageStatus != null">STORAGE_STATUS = #{storageStatus},</if>
    <if test="invoiceStatus != null">INVOICE_STATUS = #{invoiceStatus},</if>
    <if test="invoiceInfo != null">INVOICE_INFO = #{invoiceInfo},</if>
    <if test="contractFileUrl != null">CONTRACT_FILE_URL = #{contractFileUrl},</if>
    <if test="contractFileName != null">CONTRACT_FILE_NAME = #{contractFileName},</if>
    <if test="contractUploadTime != null">CONTRACT_UPLOAD_TIME = #{contractUploadTime},</if>
    <if test="updater != null">UPDATER = #{updater},</if>
    <if test="updaterName != null">UPDATER_NAME = #{updaterName},</if>
    MOD_TIME = NOW()
    WHERE FLOW_ORDER_INFO_ID = #{flowOrderInfoId}
  </update>

  <!-- 根据节点ID动态更新 -->
  <update id="updateByFlowNodeIdSelective" parameterType="com.vedeng.temporal.domain.entity.FlowOrderInfoEntity">
    UPDATE T_FLOW_ORDER_INFO
    <set>
      <if test="flowOrderInfoType != null">FLOW_ORDER_INFO_TYPE = #{flowOrderInfoType},</if>
      <if test="flowOrderInfoNo != null">FLOW_ORDER_INFO_NO = #{flowOrderInfoNo},</if>
      <if test="orderStatus != null">ORDER_STATUS = #{orderStatus},</if>
      <if test="paymentStatus != null">PAYMENT_STATUS = #{paymentStatus},</if>
      <if test="storageStatus != null">STORAGE_STATUS = #{storageStatus},</if>
      <if test="invoiceStatus != null">INVOICE_STATUS = #{invoiceStatus},</if>
      <if test="invoiceInfo != null">INVOICE_INFO = #{invoiceInfo},</if>
      <if test="contractFileUrl != null">CONTRACT_FILE_URL = #{contractFileUrl},</if>
      <if test="contractFileName != null">CONTRACT_FILE_NAME = #{contractFileName},</if>
      <if test="contractUploadTime != null">CONTRACT_UPLOAD_TIME = #{contractUploadTime},</if>
      <if test="updater != null">UPDATER = #{updater},</if>
      <if test="updaterName != null">UPDATER_NAME = #{updaterName},</if>
      MOD_TIME = NOW()
    </set>
    WHERE FLOW_NODE_ID = #{flowNodeId}
    AND IS_DELETE = 0
  </update>

  <!-- 根据节点ID和业务类型动态更新 -->
  <update id="updateByFlowNodeIdAndTypeSelective" parameterType="com.vedeng.temporal.domain.entity.FlowOrderInfoEntity">
    UPDATE T_FLOW_ORDER_INFO
    <set>
      <if test="flowOrderInfoNo != null">FLOW_ORDER_INFO_NO = #{flowOrderInfoNo},</if>
      <if test="orderStatus != null">ORDER_STATUS = #{orderStatus},</if>
      <if test="paymentStatus != null">PAYMENT_STATUS = #{paymentStatus},</if>
      <if test="storageStatus != null">STORAGE_STATUS = #{storageStatus},</if>
      <if test="invoiceStatus != null">INVOICE_STATUS = #{invoiceStatus},</if>
      <if test="invoiceInfo != null">INVOICE_INFO = #{invoiceInfo},</if>
      <if test="contractFileUrl != null">CONTRACT_FILE_URL = #{contractFileUrl},</if>
      <if test="contractFileName != null">CONTRACT_FILE_NAME = #{contractFileName},</if>
      <if test="contractUploadTime != null">CONTRACT_UPLOAD_TIME = #{contractUploadTime},</if>
      <if test="updater != null">UPDATER = #{updater},</if>
      <if test="updaterName != null">UPDATER_NAME = #{updaterName},</if>
      MOD_TIME = NOW()
    </set>
    WHERE FLOW_NODE_ID = #{flowNodeId}
    AND FLOW_ORDER_INFO_TYPE = #{flowOrderInfoType}
    AND IS_DELETE = 0
  </update>

  <!-- 根据主键删除（物理删除） -->
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    DELETE FROM T_FLOW_ORDER_INFO
    WHERE FLOW_ORDER_INFO_ID = #{flowOrderInfoId}
  </delete>

  <!-- 根据节点ID逻辑删除 -->
  <update id="logicalDeleteByFlowNodeId" parameterType="java.lang.Long">
    UPDATE T_FLOW_ORDER_INFO
    SET IS_DELETE = 1, MOD_TIME = NOW()
    WHERE FLOW_NODE_ID = #{flowNodeId}
    AND IS_DELETE = 0
  </update>

  <!-- 批量插入 -->
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO T_FLOW_ORDER_INFO (
      FLOW_ORDER_INFO_TYPE, FLOW_ORDER_INFO_NO, FLOW_NODE_ID,
      ORDER_STATUS, PAYMENT_STATUS, STORAGE_STATUS, INVOICE_STATUS, INVOICE_INFO,
      CONTRACT_FILE_URL, CONTRACT_FILE_NAME, CONTRACT_UPLOAD_TIME,
      IS_DELETE, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, ADD_TIME, MOD_TIME
    ) VALUES
    <foreach collection="entities" item="entity" separator=",">
      (
        #{entity.flowOrderInfoType}, #{entity.flowOrderInfoNo}, #{entity.flowNodeId},
        #{entity.orderStatus}, #{entity.paymentStatus}, #{entity.storageStatus}, 
        #{entity.invoiceStatus}, #{entity.invoiceInfo},
        #{entity.contractFileUrl}, #{entity.contractFileName}, #{entity.contractUploadTime},
        #{entity.isDelete}, #{entity.creator}, #{entity.updater}, 
        #{entity.creatorName}, #{entity.updaterName}, #{entity.addTime}, #{entity.modTime}
      )
    </foreach>
  </insert>

  <!-- 根据条件查询列表 -->
  <select id="selectByConditions" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER_INFO
    <where>
      <if test="flowNodeId != null">AND FLOW_NODE_ID = #{flowNodeId}</if>
      <if test="businessType != null">AND FLOW_ORDER_INFO_TYPE = #{businessType}</if>
      <if test="businessNo != null and businessNo != ''">AND FLOW_ORDER_INFO_NO = #{businessNo}</if>
      <if test="includeDeleted == null or includeDeleted == false">AND IS_DELETE = 0</if>
    </where>
    ORDER BY ADD_TIME DESC
  </select>

  <!-- 统计记录数 -->
  <select id="countByConditions" resultType="java.lang.Integer">
    SELECT COUNT(1)
    FROM T_FLOW_ORDER_INFO
    <where>
      <if test="businessType != null">AND FLOW_ORDER_INFO_TYPE = #{businessType}</if>
      <if test="includeDeleted == null or includeDeleted == false">AND IS_DELETE = 0</if>
    </where>
  </select>

  <!-- 根据节点ID列表查询 -->
  <select id="selectByFlowNodeIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER_INFO
    WHERE FLOW_NODE_ID IN
    <foreach collection="flowNodeIds" item="flowNodeId" open="(" separator="," close=")">
      #{flowNodeId}
    </foreach>
    AND IS_DELETE = 0
    ORDER BY ADD_TIME DESC
  </select>

  <!-- 更新订单状态 -->
  <update id="updateOrderStatus">
    UPDATE T_FLOW_ORDER_INFO
    SET ORDER_STATUS = #{orderStatus},
        UPDATER = #{updater},
        UPDATER_NAME = #{updaterName},
        MOD_TIME = NOW()
    WHERE FLOW_NODE_ID = #{flowNodeId}
    AND IS_DELETE = 0
  </update>

  <!-- 更新款项状态 -->
  <update id="updatePaymentStatus">
    UPDATE T_FLOW_ORDER_INFO
    SET PAYMENT_STATUS = #{paymentStatus},
        UPDATER = #{updater},
        UPDATER_NAME = #{updaterName},
        MOD_TIME = NOW()
    WHERE FLOW_NODE_ID = #{flowNodeId}
    AND IS_DELETE = 0
  </update>

  <!-- 更新入库状态 -->
  <update id="updateStorageStatus">
    UPDATE T_FLOW_ORDER_INFO
    SET STORAGE_STATUS = #{storageStatus},
        UPDATER = #{updater},
        UPDATER_NAME = #{updaterName},
        MOD_TIME = NOW()
    WHERE FLOW_NODE_ID = #{flowNodeId}
    AND IS_DELETE = 0
  </update>

  <!-- 更新票据状态 -->
  <update id="updateInvoiceStatus">
    UPDATE T_FLOW_ORDER_INFO
    SET INVOICE_STATUS = #{invoiceStatus},
        UPDATER = #{updater},
        UPDATER_NAME = #{updaterName},
        MOD_TIME = NOW()
    WHERE FLOW_NODE_ID = #{flowNodeId}
    AND IS_DELETE = 0
  </update>

  <!-- 批量更新 -->
  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="entities" item="entity" separator=";">
      UPDATE T_FLOW_ORDER_INFO
      <set>
        <if test="entity.flowOrderInfoType != null">FLOW_ORDER_INFO_TYPE = #{entity.flowOrderInfoType},</if>
        <if test="entity.flowOrderInfoNo != null">FLOW_ORDER_INFO_NO = #{entity.flowOrderInfoNo},</if>
        <if test="entity.orderStatus != null">ORDER_STATUS = #{entity.orderStatus},</if>
        <if test="entity.paymentStatus != null">PAYMENT_STATUS = #{entity.paymentStatus},</if>
        <if test="entity.storageStatus != null">STORAGE_STATUS = #{entity.storageStatus},</if>
        <if test="entity.invoiceStatus != null">INVOICE_STATUS = #{entity.invoiceStatus},</if>
        <if test="entity.invoiceInfo != null">INVOICE_INFO = #{entity.invoiceInfo},</if>
        <if test="entity.contractFileUrl != null">CONTRACT_FILE_URL = #{entity.contractFileUrl},</if>
        <if test="entity.contractFileName != null">CONTRACT_FILE_NAME = #{entity.contractFileName},</if>
        <if test="entity.contractUploadTime != null">CONTRACT_UPLOAD_TIME = #{entity.contractUploadTime},</if>
        <if test="entity.updater != null">UPDATER = #{entity.updater},</if>
        <if test="entity.updaterName != null">UPDATER_NAME = #{entity.updaterName},</if>
        MOD_TIME = NOW()
      </set>
      WHERE FLOW_ORDER_INFO_ID = #{entity.flowOrderInfoId}
    </foreach>
  </update>

  <!-- 查询指定状态的记录 -->
  <select id="selectByStatus" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER_INFO
    <where>
      <if test="orderStatus != null">AND ORDER_STATUS = #{orderStatus}</if>
      <if test="paymentStatus != null">AND PAYMENT_STATUS = #{paymentStatus}</if>
      <if test="storageStatus != null">AND STORAGE_STATUS = #{storageStatus}</if>
      <if test="invoiceStatus != null">AND INVOICE_STATUS = #{invoiceStatus}</if>
      AND IS_DELETE = 0
    </where>
    ORDER BY ADD_TIME DESC
  </select>

  <!-- 查询需要数据一致性检查的记录 -->  
  <select id="selectInconsistentRecords" resultMap="BaseResultMap">
    SELECT
    foi.FLOW_ORDER_INFO_ID, foi.FLOW_ORDER_INFO_TYPE, foi.FLOW_ORDER_INFO_NO, foi.FLOW_NODE_ID,
    foi.ORDER_STATUS, foi.PAYMENT_STATUS, foi.STORAGE_STATUS, foi.INVOICE_STATUS, foi.INVOICE_INFO,
    foi.IS_DELETE, foi.CREATOR, foi.UPDATER, foi.CREATOR_NAME, foi.UPDATER_NAME, foi.ADD_TIME, foi.MOD_TIME,
    foi.CONTRACT_FILE_URL, foi.CONTRACT_FILE_NAME, foi.CONTRACT_UPLOAD_TIME
    FROM T_FLOW_ORDER_INFO foi
    INNER JOIN T_FLOW_NODE fn ON foi.FLOW_NODE_ID = fn.FLOW_NODE_ID
    INNER JOIN T_FLOW_ORDER fo ON fn.FLOW_ORDER_ID = fo.FLOW_ORDER_ID
    WHERE foi.IS_DELETE = 0
      AND fn.IS_DELETE = 0
      AND fo.IS_DELETE = 0
      AND fo.PUSH_DIRECTION = 2  -- 只处理ERP方向的记录
      AND foi.ORDER_STATUS != 1  -- 过滤掉已完结的记录
    ORDER BY foi.MOD_TIME DESC
  </select>

  <!-- 查询所有处理中且可能需要标记为已完结的记录 -->
  <select id="selectPendingCompletionRecords" resultMap="BaseResultMap">
    SELECT
    foi.FLOW_ORDER_INFO_ID, foi.FLOW_ORDER_INFO_TYPE, foi.FLOW_ORDER_INFO_NO, foi.FLOW_NODE_ID,
    foi.ORDER_STATUS, foi.PAYMENT_STATUS, foi.STORAGE_STATUS, foi.INVOICE_STATUS, foi.INVOICE_INFO,
    foi.IS_DELETE, foi.CREATOR, foi.UPDATER, foi.CREATOR_NAME, foi.UPDATER_NAME, foi.ADD_TIME, foi.MOD_TIME,
    foi.CONTRACT_FILE_URL, foi.CONTRACT_FILE_NAME, foi.CONTRACT_UPLOAD_TIME
    FROM T_FLOW_ORDER_INFO foi
    INNER JOIN T_FLOW_NODE fn ON foi.FLOW_NODE_ID = fn.FLOW_NODE_ID
    INNER JOIN T_FLOW_ORDER fo ON fn.FLOW_ORDER_ID = fo.FLOW_ORDER_ID
    WHERE foi.IS_DELETE = 0
      AND fn.IS_DELETE = 0
      AND fo.IS_DELETE = 0
      AND fo.PUSH_DIRECTION = 2  -- 只处理ERP方向的记录
      AND foi.ORDER_STATUS = 0  -- 处理中的记录
      AND foi.PAYMENT_STATUS = 2  -- 款项已完成
      AND foi.STORAGE_STATUS = 2  -- 入库已完成
      AND foi.INVOICE_STATUS = 2  -- 票据已完成
      AND foi.INVOICE_INFO IS NOT NULL  -- 发票信息不为空
      AND foi.INVOICE_INFO != ''  -- 发票信息不为空字符串
      AND foi.INVOICE_INFO != '[]'  -- 发票信息不为空数组
      AND foi.CONTRACT_FILE_URL IS NOT NULL  -- 合同文件链接不为空
      AND foi.CONTRACT_FILE_URL != ''  -- 合同文件链接不为空字符串
    ORDER BY foi.MOD_TIME DESC
  </select>

</mapper>
