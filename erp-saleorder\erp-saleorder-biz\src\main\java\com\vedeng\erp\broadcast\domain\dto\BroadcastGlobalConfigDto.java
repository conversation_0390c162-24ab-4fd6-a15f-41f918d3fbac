package com.vedeng.erp.broadcast.domain.dto;

import com.common.dto.SelectDto;
import lombok.*;

import java.util.List;
import java.util.Map;

/**
 * 播报配置DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BroadcastGlobalConfigDto {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 过滤销售，逗号分隔
     */
    private String excludeSaleIds;
    private List<SelectDto> excludeSaleIdList;
    /**
     *AED商品ID列表，逗号分隔
     */
    private String aedSkuIds;
    private List<SelectDto> aedSkuIdList;
    /**
     * 过滤客户ID，逗号分隔
     */
    private String excludeTraderIds;
    private List<SelectDto> excludeTraderIdList;
    /**
     * 个人榜单TopN
     */
    private Integer topnUser;
    /**
     * 团队、部门榜单TopN
     */
    private Integer topnDept;
    /**
     *日常到款播报标题
     */
    private String broadcastTitleDay;
    /**
     * 周榜播报标题
     */
    private String broadcastTitleWeek;
    /**
     * 月榜播报标题
     */
    private String broadcastTitleMonth;
    /**
     * AED榜播报标题
     */
    private String broadcastTitleAed;
    /**
     * 自有到款播报标题
     */
    private String broadcastTitleZy;
    /**
     * 自定义播报标题
     */
    private String broadcastTitleCustom;
    /**
     * 统计维度1.结款金额，2.出库数量 3.出库金额
     */
    private Integer statType;
    /**
     * 统计时间范围1.本日 2.本周 3.本月
     */
    private Integer statDateRange;
    /**
     *  播报对象 逗号分隔   1.个人 2.团队 3.部门
     */
    private String statTarget;
    /**
     * 统计商品ID列表，逗号分隔
     */
    private String statSkuIds;
    private List<SelectDto> statSkuIdList;
    /**
     * 统计品牌ID列表，逗号分隔
     */
    private String statBrandIds;
    private List<SelectDto> statBrandIdList;
    /**
     * 统计分类ID列表，逗号分隔
     */
    private String statCategoryIds;
    private List<SelectDto> statCategoryIdList;
    //播报时间配置
    private Map<String,String> jobTimeStrMap;
    //各个部门播报配置
    private List<BroadcastDeptConfigDto> deptConfigList;

}
