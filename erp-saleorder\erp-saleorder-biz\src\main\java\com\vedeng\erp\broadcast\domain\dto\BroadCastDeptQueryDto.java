package com.vedeng.erp.broadcast.domain.dto;

import lombok.*;

/**
 * 播报部门查询条件DTO
 * 用于部门管理页面的分页查询和筛选条件
 * 支持两级部门结构：一级部门(大区/业务部) + 二级小组
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BroadCastDeptQueryDto {
    
    /**
     * 一级部门名称（大区/业务部）
     * 支持模糊查询，用于搜索一级部门名称
     * 如：东北大区、华东大区、AED业务部等
     */
    private String deptName;
    
    /**
     * 二级小组名称
     * 支持模糊查询，用于搜索二级小组名称
     * 如：东北-线上组、华东-线上一组、华东-线下二组等
     */
    private String groupName;
    
    /**
     * AED用户ID
     * 用于筛选包含指定AED用户的部门或小组
     */
    private Integer aedUserId;
} 