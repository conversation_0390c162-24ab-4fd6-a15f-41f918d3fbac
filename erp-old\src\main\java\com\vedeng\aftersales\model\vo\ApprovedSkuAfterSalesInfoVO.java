package com.vedeng.aftersales.model.vo;

import java.io.Serializable;

public class ApprovedSkuAfterSalesInfoVO implements Serializable {
    private Integer id;
    private Integer skuAfterSalesInfoId;
    private Integer skuId;
    private String skuNo;
    private Integer fileType;
    private Integer subtype;
    private String fileName;
    private String suffix;
    private String url;

    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public Integer getSkuAfterSalesInfoId() { return skuAfterSalesInfoId; }
    public void setSkuAfterSalesInfoId(Integer skuAfterSalesInfoId) { this.skuAfterSalesInfoId = skuAfterSalesInfoId; }
    public Integer getSkuId() { return skuId; }
    public void setSkuId(Integer skuId) { this.skuId = skuId; }
    public String getSkuNo() { return skuNo; }
    public void setSkuNo(String skuNo) { this.skuNo = skuNo; }
    public Integer getFileType() { return fileType; }
    public void setFileType(Integer fileType) { this.fileType = fileType; }
    public Integer getSubtype() { return subtype; }
    public void setSubtype(Integer subtype) { this.subtype = subtype; }
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    public String getSuffix() { return suffix; }
    public void setSuffix(String suffix) { this.suffix = suffix; }
    public String getUrl() { return url; }
    public void setUrl(String url) { this.url = url; }
} 