package com.vedeng.temporal.workflow.process;


import com.vedeng.temporal.context.CompanyContextCalculator;
import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.enums.ExecutionMode;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.exception.ExceptionHandler;
import com.vedeng.temporal.workflow.activity.CompanySequenceActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 业务流程抽象基类
 * 提供统一的执行框架
 *
 * <AUTHOR>
 * @version 2.0 (简化版)
 * @since 2025-01-03
 */
@Slf4j
public abstract class AbstractBusinessProcess {

    // Activity 依赖（通过构造函数注入）
    protected final CompanySequenceActivity companySequenceActivity;

    // 异常处理器（通过构造函数注入）
    protected final ExceptionHandler exceptionHandler;

    // 上下文计算器（复用实例）
    private final CompanyContextCalculator contextCalculator;


    /**
     * 构造函数
     *
     * @param companySequenceActivity 公司序列Activity（必需）
     * @param exceptionHandler 异常处理器（必需）
     */
    protected AbstractBusinessProcess(
            CompanySequenceActivity companySequenceActivity,
            ExceptionHandler exceptionHandler) {

        // 必需依赖检查
        this.companySequenceActivity = Objects.requireNonNull(companySequenceActivity, "companySequenceActivity不能为空");
        this.exceptionHandler = Objects.requireNonNull(exceptionHandler, "exceptionHandler不能为空");

        // 初始化复用对象
        this.contextCalculator = new CompanyContextCalculator();
    }


    // ==================== 抽象方法 ====================

    /**
     * 获取执行模式
     */
    protected abstract ExecutionMode getExecutionMode();

    /**
     * 是否逆序执行
     */
    protected abstract boolean isReverseOrder();

    /**
     * 获取业务步骤列表
     */
    protected abstract List<BusinessStep> getBusinessSteps();

    /**
     * 获取流程名称
     */
    protected abstract String getProcessName();

    // ==================== 日志记录辅助方法 ====================

    /**
     * 记录执行完成日志
     */
    private void logExecutionComplete(CompanyBusinessRequest request, long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        log.info("{}执行完成，业务ID: {}, 耗时: {}ms",
                getProcessName(), request.getBusinessId(), duration);
    }

    /**
     * 记录执行失败日志
     */
    private void logExecutionFailure(CompanyBusinessRequest request, long startTime, BusinessProcessException e) {
        long duration = System.currentTimeMillis() - startTime;
        log.error("{}执行失败，业务ID: {}, 耗时: {}ms",
                getProcessName(), request.getBusinessId(), duration, e);
    }

    /**
     * 记录执行异常日志
     */
    private void logExecutionError(CompanyBusinessRequest request, long startTime, Exception e) {
        long duration = System.currentTimeMillis() - startTime;
        log.error("{}执行异常，业务ID: {}, 耗时: {}ms",
                getProcessName(), request.getBusinessId(), duration, e);
    }

    /**
     * 执行业务流程的模板方法
     */
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, List<String> companySequence) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始执行{}，业务ID: {}, 公司序列: {}",
                    getProcessName(), request.getBusinessId(), companySequence);

            // 根据执行模式直接调用对应的执行方法
            CompanyBusinessResponse response = getExecutionMode() == ExecutionMode.PARALLEL
                    ? executeInParallel(request, companySequence)
                    : executeSequentially(request, companySequence);

            logExecutionComplete(request, startTime);
            return response;

        } catch (BusinessProcessException e) {
            // 业务异常：转换为失败响应，保持 Temporal 工作流状态为 Completed
            // 这样避免触发 Temporal 自动重试，由上层工作流进行精细的错误处理
            logExecutionFailure(request, startTime, e);
            return CompanyBusinessResponse.failure(e.getMessage(), e.getErrorCode());

        } catch (Exception e) {
            // 未知异常：使用 ExceptionHandler 进行统一处理（异常分类 + 通知）
            // 统一异常处理策略：所有异常最终都转换为 CompanyBusinessResponse.failure()
            // 避免 Temporal 工作流进入 Failed 状态，保持业务逻辑的可控性
            logExecutionError(request, startTime, e);
            return exceptionHandler.handleBusinessExceptionWithNotificationOnly(
                    e, getProcessName(), request.getBusinessId(), "ALL_COMPANIES");
        }
    }



    // ==================== 核心执行方法 ====================

    /**
     * 串行执行模式
     * 公司按顺序依次执行，后一个公司等待前一个公司完成
     */
    private CompanyBusinessResponse executeSequentially(CompanyBusinessRequest request, List<String> companySequence) {
        // 获取执行配置
        boolean reverseOrder = isReverseOrder();
        List<BusinessStep> steps = getBusinessSteps();
        List<String> executionSequence = contextCalculator.prepareExecutionSequence(companySequence, reverseOrder);

        log.info("使用串行执行模式，公司序列: {}", executionSequence);

        for (int i = 0; i < executionSequence.size(); i++) {
            String currentCompany = executionSequence.get(i);

            // 计算公司执行上下文
            CompanyExecutionContext context = contextCalculator.calculateSingleContext(
                    currentCompany, companySequence, executionSequence, i, reverseOrder, ExecutionMode.SEQUENTIAL);

            log.info("处理公司: {}, 上下文: {}", currentCompany, context.getContextDescription());

            // 执行业务步骤（依赖等待由 Step 内部处理）
            // 异常会向上传播到最外层的 execute() 方法进行统一处理
            executeBusinessSteps(request, context, steps, currentCompany);

            log.info("公司{}步骤执行完成", currentCompany);
        }

        return CompanyBusinessResponse.success(getProcessName() + "串行执行完成", request.getBusinessId());
    }

    /**
     * 并行执行模式
     * 所有公司同时启动，独立执行，无相互依赖
     */
    private CompanyBusinessResponse executeInParallel(CompanyBusinessRequest request, List<String> companySequence) {
        // 获取执行配置
        boolean reverseOrder = isReverseOrder();
        List<BusinessStep> steps = getBusinessSteps();
        List<String> executionSequence = contextCalculator.prepareExecutionSequence(companySequence, reverseOrder);

        log.info("使用并行执行模式，公司序列: {}", executionSequence);

        // 1. 创建每个公司的执行任务
        List<Promise<CompanyBusinessResponse>> promises = new ArrayList<>();

        for (int i = 0; i < executionSequence.size(); i++) {
            final String currentCompany = executionSequence.get(i);
            final int currentIndex = i;

            // 使用 Temporal 的 Async.function 创建异步任务
            Promise<CompanyBusinessResponse> promise = Async.function(
                    () -> executeCompanyInParallel(
                            request, companySequence, executionSequence, reverseOrder, steps,
                            currentCompany, currentIndex
                    )
            );

            promises.add(promise);
        }

        // 2. 等待所有公司执行完成
        Promise<Void> allPromises = Promise.allOf(promises);
        allPromises.get(); // 等待所有任务完成

        // 3. 获取所有任务的结果
        List<CompanyBusinessResponse> results = new ArrayList<>();
        for (Promise<CompanyBusinessResponse> promise : promises) {
            results.add(promise.get());
        }

        // 4. 检查结果
        for (CompanyBusinessResponse result : results) {
            if (!Boolean.TRUE.equals(result.getSuccess())) {
                log.error("并行执行失败，错误: {}", result.getMessage());
                return result;
            }
        }

        log.info("所有公司并行执行完成");
        return CompanyBusinessResponse.success(getProcessName() + "并行执行完成", request.getBusinessId());
    }

    /**
     * 并行模式下执行单个公司的业务步骤
     */
    private CompanyBusinessResponse executeCompanyInParallel(
            CompanyBusinessRequest request,
            List<String> originalSequence,
            List<String> executionSequence,
            boolean reverseOrder,
            List<BusinessStep> steps,
            String currentCompany,
            int currentIndex) {

            log.info("开始并行处理公司: {}", currentCompany);

            // 计算公司执行上下文
            CompanyExecutionContext context = contextCalculator.calculateSingleContext(
                    currentCompany, originalSequence, executionSequence, currentIndex, reverseOrder, ExecutionMode.PARALLEL);

            log.info("公司: {}, 上下文: {}", currentCompany, context.getContextDescription());

            // 执行业务步骤（依赖等待由 Step 内部处理）
            executeBusinessSteps(request, context, steps, currentCompany);

            log.info("公司{}并行处理完成", currentCompany);
            return CompanyBusinessResponse.success("公司" + currentCompany + "并行处理完成", request.getBusinessId());

    }


    /**
     * 执行业务步骤
     */
    private void executeBusinessSteps(CompanyBusinessRequest request, CompanyExecutionContext context,
                                      List<BusinessStep> steps, String currentCompany) {

        // 使用统一模板执行业务步骤
        for (BusinessStep step : steps) {
            // 统一的Step执行：异常处理、日志记录、监控指标都由本类处理
            CompanyBusinessResponse stepResult = executeStepWithEnhancedExceptionHandling(step, request, context);

            // 检查步骤执行结果
            if (!Boolean.TRUE.equals(stepResult.getSuccess())) {
                // 使用 ExceptionHandler 进行智能异常分类并发送通知
                // 根据错误码和错误信息判断是否可重试，保持与场景1的处理逻辑一致
                BusinessProcessException exception = exceptionHandler.classifyStepResultFailureWithNotification(
                        stepResult, step.getStepName(), currentCompany, getProcessName(), request.getBusinessId());

                // 抛出异常（保持原有逻辑）
                throw exception;
            }

            log.info("公司 {} 完成步骤: {}", currentCompany, step.getStepName());

        }
    }



    /**
     * 统一的Step执行入口（简化版）
     */
    private CompanyBusinessResponse executeStepWithEnhancedExceptionHandling(BusinessStep step, CompanyBusinessRequest request,
                                                                             CompanyExecutionContext context) {
        long startTime = System.currentTimeMillis();
        String stepName = step.getStepName();
        String companyCode = context.getCurrentCompany();
        String businessId = request.getBusinessId();

        try {
            log.info("开始执行步骤: {}, 业务ID: {}, 公司: {}", stepName, businessId, companyCode);

            // 执行Step核心逻辑
            CompanyBusinessResponse result = step.execute(request, context);

            long duration = System.currentTimeMillis() - startTime;
            log.info("步骤执行成功: {}, 业务ID: {}, 公司: {}, 耗时: {}ms",
                    stepName, businessId, companyCode, duration);

            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("步骤执行异常: {}, 业务ID: {}, 公司: {}, 耗时: {}ms",
                    stepName, businessId, companyCode, duration, e);

            // 使用 ExceptionHandler 处理异常（仅通知版本）
            // 这样可以获得统一的异常分类、通知机制，同时保持返回失败响应的设计
            return exceptionHandler.handleBusinessExceptionWithNotificationOnly(
                    e, stepName, businessId, companyCode);
        }
    }

}
