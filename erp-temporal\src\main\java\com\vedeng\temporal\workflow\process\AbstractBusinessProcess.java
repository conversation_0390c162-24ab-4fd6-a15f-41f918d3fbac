package com.vedeng.temporal.workflow.process;


import com.vedeng.temporal.context.CompanyContextCalculator;
import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.enums.ExecutionMode;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.workflow.activity.CompanySequenceActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 业务流程抽象基类
 * 提供统一的执行框架
 *
 * <AUTHOR>
 * @version 2.0 (简化版)
 * @since 2025-01-03
 */
@Slf4j
public abstract class AbstractBusinessProcess {

    // Activity 依赖（通过构造函数注入）
    protected final CompanySequenceActivity companySequenceActivity;


    // 上下文计算器（复用实例）
    private final CompanyContextCalculator contextCalculator;


    /**
     * 构造函数
     *
     * @param companySequenceActivity 公司序列Activity（必需）
     */
    protected AbstractBusinessProcess(
            CompanySequenceActivity companySequenceActivity) {

        // 必需依赖检查
        this.companySequenceActivity = Objects.requireNonNull(companySequenceActivity, "companySequenceActivity不能为空");

        // 初始化复用对象
        this.contextCalculator = new CompanyContextCalculator();
    }


    /**
     * 执行业务流程的模板方法
     */
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, List<String> companySequence) {
        long startTime = System.currentTimeMillis();

        try {

            log.info("开始执行{}，业务ID: {}, 公司序列: {}",
                    getProcessName(), request.getBusinessId(), companySequence);


            // 1. 获取执行配置和业务步骤
            ExecutionMode executionMode = getExecutionMode();
            boolean reverseOrder = isReverseOrder();
            List<BusinessStep> steps = getBusinessSteps();

            // 2. 执行业务流程
            List<String> executionSequence = prepareExecutionSequence(companySequence, reverseOrder);
            CompanyBusinessResponse response = executeBusinessProcess(request, companySequence,
                    executionSequence, executionMode, reverseOrder, steps);

            long duration = System.currentTimeMillis() - startTime;
            log.info("{}执行完成，业务ID: {}, 耗时: {}ms",
                    getProcessName(), request.getBusinessId(), duration);


            return response;

        } catch (BusinessProcessException e) {
            // 业务异常：转换为失败响应，保持 Temporal 工作流状态为 Completed
            // 这样避免触发 Temporal 自动重试，由上层工作流进行精细的错误处理
            long duration = System.currentTimeMillis() - startTime;
            log.error("{}执行失败，业务ID: {}, 耗时: {}ms", getProcessName(), request.getBusinessId(), duration, e);

            return CompanyBusinessResponse.failure(e.getMessage(), e.getErrorCode());

        } catch (Exception e) {
            // 未知异常：包装为业务异常后转换为失败响应
            // 统一异常处理策略：所有异常最终都转换为 CompanyBusinessResponse.failure()
            // 避免 Temporal 工作流进入 Failed 状态，保持业务逻辑的可控性
            long duration = System.currentTimeMillis() - startTime;
            BusinessProcessException businessException = BusinessProcessException.retryable(
                    String.format("%s执行失败: %s", getProcessName(), e.getMessage()),
                    "EXECUTION_FAILED",
                    "Process=" + getProcessName()
            );

            log.error("{}执行异常，业务ID: {}, 耗时: {}ms", getProcessName(), request.getBusinessId(), duration, businessException);

            return CompanyBusinessResponse.failure(businessException.getMessage(), businessException.getErrorCode());
        }
    }


    // ==================== 抽象方法 ====================

    /**
     * 获取执行模式
     */
    protected abstract ExecutionMode getExecutionMode();

    /**
     * 是否逆序执行
     */
    protected abstract boolean isReverseOrder();

    /**
     * 获取业务步骤列表
     */
    protected abstract List<BusinessStep> getBusinessSteps();

    /**
     * 获取流程名称
     */
    protected abstract String getProcessName();


    // ==================== 核心执行方法 ====================

    /**
     * 准备执行序列
     */
    private List<String> prepareExecutionSequence(List<String> originalSequence, boolean reverseOrder) {
        return contextCalculator.prepareExecutionSequence(originalSequence, reverseOrder);
    }

    /**
     * 执行具体的业务流程
     */
    private CompanyBusinessResponse executeBusinessProcess(CompanyBusinessRequest request,
                                                           List<String> originalSequence,
                                                           List<String> executionSequence,
                                                           ExecutionMode executionMode,
                                                           boolean reverseOrder,
                                                           List<BusinessStep> steps) {

        log.info("开始执行{}，公司序列: {}, 执行模式: {}",
                getProcessName(), executionSequence, executionMode);

        // 根据执行模式选择不同的处理方式
        switch (executionMode) {
            case SEQUENTIAL:
                return executeSequentially(request, originalSequence, executionSequence, reverseOrder, steps);
            case PARALLEL:
                return executeInParallel(request, originalSequence, executionSequence, reverseOrder, steps);
            default:
                return executeSequentially(request, originalSequence, executionSequence, reverseOrder, steps);
        }
    }

    /**
     * 串行执行模式
     * 公司按顺序依次执行，后一个公司等待前一个公司完成
     */
    private CompanyBusinessResponse executeSequentially(CompanyBusinessRequest request,
                                                        List<String> originalSequence,
                                                        List<String> executionSequence,
                                                        boolean reverseOrder,
                                                        List<BusinessStep> steps) {

        log.info("使用串行执行模式，公司序列: {}", executionSequence);

        for (int i = 0; i < executionSequence.size(); i++) {
            String currentCompany = executionSequence.get(i);

            // 计算公司执行上下文
            CompanyExecutionContext context = contextCalculator.calculateSingleContext(
                    currentCompany, originalSequence, executionSequence, i, reverseOrder, ExecutionMode.SEQUENTIAL);

            log.info("处理公司: {}, 上下文: {}", currentCompany, context.getContextDescription());

            // 执行业务步骤（依赖等待由 Step 内部处理）
            executeBusinessSteps(request, context, steps, currentCompany);

            log.info("公司{}步骤执行完成", currentCompany);
        }

        return CompanyBusinessResponse.success(getProcessName() + "串行执行完成", request.getBusinessId());
    }

    /**
     * 并行执行模式
     * 所有公司同时启动，独立执行，无相互依赖
     */
    private CompanyBusinessResponse executeInParallel(CompanyBusinessRequest request,
                                                      List<String> originalSequence,
                                                      List<String> executionSequence,
                                                      boolean reverseOrder,
                                                      List<BusinessStep> steps) {

        log.info("使用并行执行模式，公司序列: {}", executionSequence);

        // 1. 创建每个公司的执行任务
        List<Promise<CompanyBusinessResponse>> promises = new ArrayList<>();

        for (int i = 0; i < executionSequence.size(); i++) {
            final String currentCompany = executionSequence.get(i);
            final int currentIndex = i;

            // 使用 Temporal 的 Async.function 创建异步任务
            Promise<CompanyBusinessResponse> promise = Async.function(
                    () -> executeCompanyInParallel(
                            request, originalSequence, executionSequence, reverseOrder, steps,
                            currentCompany, currentIndex
                    )
            );

            promises.add(promise);
        }

        // 2. 等待所有公司执行完成
        Promise<Void> allPromises = Promise.allOf(promises);
        allPromises.get(); // 等待所有任务完成

        // 3. 获取所有任务的结果
        List<CompanyBusinessResponse> results = new ArrayList<>();
        for (Promise<CompanyBusinessResponse> promise : promises) {
            results.add(promise.get());
        }

        // 4. 检查结果
        for (CompanyBusinessResponse result : results) {
            if (!Boolean.TRUE.equals(result.getSuccess())) {
                log.error("并行执行失败，错误: {}", result.getMessage());
                return result;
            }
        }

        log.info("所有公司并行执行完成");
        return CompanyBusinessResponse.success(getProcessName() + "并行执行完成", request.getBusinessId());
    }

    /**
     * 并行模式下执行单个公司的业务步骤
     */
    private CompanyBusinessResponse executeCompanyInParallel(
            CompanyBusinessRequest request,
            List<String> originalSequence,
            List<String> executionSequence,
            boolean reverseOrder,
            List<BusinessStep> steps,
            String currentCompany,
            int currentIndex) {

        try {
            log.info("开始并行处理公司: {}", currentCompany);

            // 计算公司执行上下文
            CompanyExecutionContext context = contextCalculator.calculateSingleContext(
                    currentCompany, originalSequence, executionSequence, currentIndex, reverseOrder, ExecutionMode.PARALLEL);

            log.info("公司: {}, 上下文: {}", currentCompany, context.getContextDescription());

            // 执行业务步骤（依赖等待由 Step 内部处理）
            executeBusinessSteps(request, context, steps, currentCompany);

            log.info("公司{}并行处理完成", currentCompany);
            return CompanyBusinessResponse.success("公司" + currentCompany + "并行处理完成", request.getBusinessId());

        } catch (BusinessProcessException e) {
            log.error("公司{}并行处理业务异常", currentCompany, e);
            return CompanyBusinessResponse.failure(e.getMessage(), e.getErrorCode());
        } catch (Exception e) {
            log.error("公司{}并行处理异常", currentCompany, e);
            BusinessProcessException businessException = BusinessProcessException.retryable(
                    "公司" + currentCompany + "并行处理异常: " + e.getMessage(),
                    "PARALLEL_EXECUTION_ERROR",
                    "Process=" + getProcessName() + ", Company=" + currentCompany);

            return CompanyBusinessResponse.failure(businessException.getMessage(), businessException.getErrorCode());
        }
    }


    /**
     * 执行业务步骤（简化版，使用统一模板）
     */
    private void executeBusinessSteps(CompanyBusinessRequest request, CompanyExecutionContext context,
                                      List<BusinessStep> steps, String currentCompany) {

        // 使用统一模板执行业务步骤
        for (BusinessStep step : steps) {
            // 统一的Step执行：异常处理、日志记录、监控指标都由本类处理
            CompanyBusinessResponse stepResult = executeStepWithEnhancedExceptionHandling(step, request, context);

            // 检查步骤执行结果
            if (!Boolean.TRUE.equals(stepResult.getSuccess())) {
                // Step模板已经处理了异常，这里转换为Process层异常
                throw BusinessProcessException.nonRetryable(
                        String.format("公司 %s 执行步骤 %s 失败: %s", currentCompany, step.getStepName(), stepResult.getMessage()),
                        stepResult.getErrorCode() != null ? stepResult.getErrorCode() : "STEP_EXECUTION_FAILED",
                        String.format("Process=%s, Company=%s, Step=%s", getProcessName(), currentCompany, step.getStepName())
                );
            }

            log.info("公司 {} 完成步骤: {}", currentCompany, step.getStepName());

        }
    }



    /**
     * 统一的Step执行入口（简化版）
     */
    private CompanyBusinessResponse executeStepWithEnhancedExceptionHandling(BusinessStep step, CompanyBusinessRequest request,
                                                                             CompanyExecutionContext context) {
        long startTime = System.currentTimeMillis();
        String stepName = step.getStepName();
        String companyCode = context.getCurrentCompany();
        String businessId = request.getBusinessId();

        try {
            log.info("开始执行步骤: {}, 业务ID: {}, 公司: {}", stepName, businessId, companyCode);

            // 执行Step核心逻辑
            Object result = step.execute(request, context);

            // 处理执行结果
            CompanyBusinessResponse response = processStepResult(result, step, request);

            long duration = System.currentTimeMillis() - startTime;
            log.info("步骤执行成功: {}, 业务ID: {}, 公司: {}, 耗时: {}ms",
                    stepName, businessId, companyCode, duration);

            return response;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("步骤执行异常: {}, 业务ID: {}, 公司: {}, 耗时: {}ms",
                    stepName, businessId, companyCode, duration, e);

            // 简化异常处理：直接转换为失败响应
            return CompanyBusinessResponse.failure(
                    stepName + "执行失败: " + e.getMessage(),
                    "STEP_EXECUTION_ERROR"
            );
        }
    }

    /**
     * 处理Step执行结果（简化版）
     */
    private CompanyBusinessResponse processStepResult(Object result, BusinessStep step,
                                                      CompanyBusinessRequest request) {
        if (result instanceof CompanyBusinessResponse) {
            CompanyBusinessResponse response = (CompanyBusinessResponse) result;
            if (!Boolean.TRUE.equals(response.getSuccess())) {
                return response;
            }
            return response;
        } else {
            return CompanyBusinessResponse.success(
                    step.getStepName() + "执行成功",
                    result != null ? result.toString() : null
            );
        }
    }
}
