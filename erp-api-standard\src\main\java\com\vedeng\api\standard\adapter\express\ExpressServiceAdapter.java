package com.vedeng.api.standard.adapter.express;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.vedeng.api.standard.adapter.express.dto.*;
import com.vedeng.api.standard.core.AbstractServiceAdapter;
import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.core.exception.ApiStandardException;
import com.vedeng.api.standard.template.BusinessTemplate;
import com.vedeng.api.standard.template.ParameterConfig;
import com.vedeng.api.standard.validation.ValidationContextKeys;
import com.vedeng.api.standard.validation.rules.AddExpressEnableRule;
import com.vedeng.api.standard.validation.rules.SignExpressEnableRule;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.SyncExpressDetailDto;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.erp.buyorder.mapper.BuyorderMapper;
import com.vedeng.erp.buyorder.mapper.BuyorderGoodsMapper;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.dto.ExpressGoodsDto;
import com.vedeng.erp.buyorder.domain.entity.BuyorderGoods;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component("expressServiceAdapter")
public class ExpressServiceAdapter extends AbstractServiceAdapter {

    @Autowired
    private BusinessTemplate businessTemplate;
    
    @Override
    protected void registerOperationHandlers() {
        registerThrowingHandler("create", this::executeCreateOperation);
        registerThrowingHandler("query", this::executeQueryOperation);
        registerThrowingHandler("sign", this::executeSignOperation);
        registerThrowingHandler("signCheck", this::executeSignCheckOperation);
        registerThrowingHandler("checkDownstreamCompletion", this::executeCheckDownstreamCompletionOperation);
        registerThrowingHandler("getDownstreamAll", this::executeGetDownstreamAllOperation);
    }

    /**
     * 获取不需要身份认证的操作列表
     *
     * 采购单模块中，查询操作通常不需要身份认证，
     * 允许外部系统或匿名用户查询采购单信息
     *
     * @return 不需要认证的操作名称数组
     */
    @Override
    public String[] getNoAuthActions() {
        return new String[]{"query","signCheck","checkDownstreamCompletion","getDownstreamAll"};
    }

    private Object executeQueryOperation(ApiRequest apiRequest) {
        ExpressQueryRequest expressRequest = new ExpressQueryRequest();
        BeanUtil.fillBeanWithMap(apiRequest.getData(), expressRequest, true);
        try {
            return businessTemplate.<ExpressQueryRequest, ExpressResponse>executeCreate(apiRequest)
                    .requestType(ExpressQueryRequest.class)
                    .controller("buyorderApiServiceImpl", "getExpressGoodsList")
                    .withoutHttpParameters(
                            ParameterConfig.of(String.class, expressRequest.getBuyOrderNo()),
                            ParameterConfig.of(List.class, expressRequest.getNotInExpressNoList())

                    )
                    .execute();
        } catch (Exception e) {
            throw new RuntimeException("新增快递:失败: " + e.getMessage(), e);
        }
    }

    private Object executeCreateOperation(ApiRequest apiRequest) throws Exception {
        ExpressCreateRequest expressRequest = new ExpressCreateRequest();
        logger.info("apiRequest", JSONUtil.toJsonStr(apiRequest.getData()));
        BeanUtil.fillBeanWithMap(apiRequest.getData(), expressRequest, true);

        return businessTemplate.<ExpressCreateRequest, ExpressResponse>executeCreate(apiRequest)
                .requestType(ExpressCreateRequest.class)
                .responseType(ExpressResponse.class)
                .validationRules(AddExpressEnableRule.class)
                .controller("buyorderController", "saveAddExpress")
                .withIdempotencyHandling("EXPRESS_CREATE")
                .withoutHttpParameters(
                        ParameterConfig.fromValidationContext(ValidationContextKeys.AMOUNT, BigDecimal.class),
                        ParameterConfig.fromValidationContext(ValidationContextKeys.EXPRESS, Express.class),
                        ParameterConfig.fromValidationContext(ValidationContextKeys.DELIVERY_TIMES, String.class),
                        ParameterConfig.fromValidationContext(ValidationContextKeys.ID_NUM_PRICE, String.class),
                        ParameterConfig.fromValidationContext(ValidationContextKeys.ID_SEND_N_SENDED_N_SUM_N, String.class),
                        ParameterConfig.fromValidationContext(ValidationContextKeys.BUYORDER_VO, BuyorderVo.class)

                )
                .execute();
    }

    /**
     * 执行快递签收操作
     * 根据快递单号和采购单号查询expressId，拼接&515，调用WarehousesOutController.editExpressStatus
     */
    private Object executeSignOperation(ApiRequest apiRequest) throws Exception {
        ExpressSignRequest signRequest = new ExpressSignRequest();
        BeanUtil.fillBeanWithMap(apiRequest.getData(), signRequest, true);

        String requestId = apiRequest.getRequestId();
        String logisticsNo = signRequest.getLogisticsNo();
        String buyOrderNo = signRequest.getBuyOrderNo();

        logger.info("开始执行快递签收操作，requestId: {}, 快递单号: {}, 采购单号: {}",
                   requestId, logisticsNo, buyOrderNo);

        Object result = businessTemplate.<ExpressSignRequest, Object>executeCreate(apiRequest)
                .requestType(ExpressSignRequest.class)
                .controller("warehousesOutController", "editExpressStatus")
                .validationRules(SignExpressEnableRule.class)
                .withoutHttpParameters(
                        ParameterConfig.fromValidationContext(ValidationContextKeys.EXPRESS_IDS, String.class),
                        ParameterConfig.fromValidationContext(ValidationContextKeys.BEFORE_PARAMS, String.class)
                )
                .execute();

        logger.info("快递签收操作完成，requestId: {}, 快递单号: {}, 采购单号: {}, 结果: {}",
                   requestId, logisticsNo, buyOrderNo, result != null ? "成功" : "失败");

        return result;
    }

    @Autowired
    private ExpressService expressService;

    @Autowired
    @Qualifier("newBuyorderMapper")
    private BuyorderMapper buyorderMapper;

    @Autowired
    @Qualifier("newBuyorderGoodsMapper")
    private BuyorderGoodsMapper buyorderGoodsMapper;

    /**
     * 执行快递签收状态检查操作
     * 检查快递是否已经签收
     */
    private Object executeSignCheckOperation(ApiRequest apiRequest) throws Exception {
        ExpressSignRequest signRequest = new ExpressSignRequest();
        BeanUtil.fillBeanWithMap(apiRequest.getData(), signRequest, true);

        String requestId = apiRequest.getRequestId();
        String logisticsNo = signRequest.getLogisticsNo();
        String buyOrderNo = signRequest.getBuyOrderNo();

        logger.info("开始检查快递签收状态，requestId: {}, 快递单号: {}, 采购单号: {}",
                   requestId, logisticsNo, buyOrderNo);

        SyncExpressDetailDto detailDto = expressService.getDetailByNo(buyOrderNo, logisticsNo);

        if (detailDto != null) {
            logger.info("快递签收状态检查完成，requestId: {}, 快递单号: {}, 采购单号: {}, expressId: {}, 签收状态: {}",
                       requestId, logisticsNo, buyOrderNo, detailDto.getExpressId(), detailDto.getArrivalStatus());
        } else {
            logger.warn("未找到快递信息，requestId: {}, 快递单号: {}, 采购单号: {}",
                       requestId, logisticsNo, buyOrderNo);
        }

        return detailDto;
    }

    /**
     * 执行检查下游完成情况操作
     * 根据采购单号查询采购单商品数量和快递商品数量，比较两者大小
     */
    private Object executeCheckDownstreamCompletionOperation(ApiRequest apiRequest) {
        ExpressDownstreamCompletionRequest request = new ExpressDownstreamCompletionRequest();
        BeanUtil.fillBeanWithMap(apiRequest.getData(), request, true);

        String requestId = apiRequest.getRequestId();
        String buyOrderNo = request.getBuyOrderNo();

        logger.info("开始检查下游完成情况，requestId: {}, 采购单号: {}", requestId, buyOrderNo);

        try {
            // 1. 查询采购单信息
            BuyOrderApiDto buyOrderInfo = buyorderMapper.findByBuyorderNo(buyOrderNo);
            if (buyOrderInfo == null) {
                logger.warn("未找到采购单信息，requestId: {}, 采购单号: {}", requestId, buyOrderNo);
                throw ApiStandardException.serviceExecutionError("未找到采购单信息: " + buyOrderNo);
            }

            // 2. 查询采购单商品列表并计算总数量
            List<BuyorderGoods> buyorderGoodsList = buyorderGoodsMapper.findByBuyorderId(buyOrderInfo.getBuyorderId());
            int buyOrderGoodsCount = 0;
            if (!CollectionUtils.isEmpty(buyorderGoodsList)) {
                for (BuyorderGoods goods : buyorderGoodsList) {
                    // 排除费用商品
                    if (goods.getNum() != null) {
                        buyOrderGoodsCount += goods.getNum();
                    }
                }
            }

            // 3. 查询快递商品列表并计算总数量
            List<ExpressGoodsDto> expressGoodsList = buyorderMapper.getExpressGoodsList(buyOrderNo);
            int expressGoodsCount = 0;
            if (!CollectionUtils.isEmpty(expressGoodsList)) {
                for (ExpressGoodsDto expressGoods : expressGoodsList) {
                    if (expressGoods.getSendNum() != null) {
                        expressGoodsCount += expressGoods.getSendNum().intValue();
                    }
                }
            }

            logger.info("采购单商品数量: {}, 快递商品数量: {}, requestId: {}, 采购单号: {}",
                       buyOrderGoodsCount, expressGoodsCount, requestId, buyOrderNo);

            // 4. 比较数量并返回结果

            // 5. 构建响应对象
            ExpressDownstreamCompletionResponse response = new ExpressDownstreamCompletionResponse();
            response.setBuyOrderNo(buyOrderNo);
            response.setBuyOrderGoodsCount(buyOrderGoodsCount);
            response.setExpressGoodsCount(expressGoodsCount);
            response.setIsCompleted(expressGoodsCount >= buyOrderGoodsCount);

            return response;

        } catch (Exception e) {
            logger.error("检查下游完成情况异常，requestId: {}, 采购单号: {}", requestId, buyOrderNo, e);
            throw ApiStandardException.serviceExecutionError("检查下游完成情况失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行查询所有下游快递单号操作
     * 根据采购单号查询所有快递单号
     */
    private Object executeGetDownstreamAllOperation(ApiRequest apiRequest) {
        ExpressDownstreamAllRequest request = new ExpressDownstreamAllRequest();
        BeanUtil.fillBeanWithMap(apiRequest.getData(), request, true);

        String requestId = apiRequest.getRequestId();
        String buyOrderNo = request.getBuyOrderNo();

        logger.info("开始查询所有下游快递单号，requestId: {}, 采购单号: {}", requestId, buyOrderNo);

        try {
            // 1. 参数校验
            if (buyOrderNo == null || buyOrderNo.trim().isEmpty()) {
                logger.warn("采购单号为空，requestId: {}", requestId);
                throw ApiStandardException.serviceExecutionError("采购单号不能为空");
            }

            // 2. 查询所有快递单号
            List<Map<String, Object>> expressMapList = buyorderMapper.getAllExpressList(buyOrderNo);
            List<String> expressList = new ArrayList<>();

            if (!CollectionUtils.isEmpty(expressMapList)) {
                for (Map<String, Object> expressMap : expressMapList) {
                    expressList.add((String) expressMap.get("logisticsNo"));
                }
            }

            logger.info("查询所有下游快递单号完成，requestId: {}, 采购单号: {}, 快递单数量: {}",
                       requestId, buyOrderNo, expressList.size());

            // 3. 构建响应对象
            ExpressDownstreamAllResponse response = new ExpressDownstreamAllResponse();
            response.setBuyOrderNo(buyOrderNo);
            response.setExpressList(expressList);
            response.setTotalCount(expressList.size());

            return response;

        } catch (Exception e) {
            logger.error("查询所有下游快递单号异常，requestId: {}, 采购单号: {}", requestId, buyOrderNo, e);
            throw ApiStandardException.serviceExecutionError("查询所有下游快递单号失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getModuleName() {
        return "express";
    }
}
