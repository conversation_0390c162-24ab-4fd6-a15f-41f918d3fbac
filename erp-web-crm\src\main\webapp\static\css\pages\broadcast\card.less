.vd-ui-rank-card {
    border-radius: 5px;
    background: #fff;
    padding: 20px;
    height: 703px;

    .rank-card-top {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .card-title {
            font-size: 16px;
            font-weight: 700;
            color: #333;
        }

        .card-filter {
            display: flex;
            align-items: center;

            .filter-item {
                height: 24px;
                font-size: 14px;
                color: #333;
                line-height: 24px;
                border-radius: 12px;
                padding: 0 10px;
                margin-right: 10px;
                cursor: pointer;

                &:last-child {
                    margin-right: 0;
                }

                // &:hover {
                //     background: #FFEDE0;
                //     color: #F60;
                // }

                &:hover {
                    background: #FFEDE0;
                    color: #F60;
                }

                &.active {
                    background: #f60 !important;
                    color: #fff !important;
                    cursor: auto;
                }
            }
        }
    }

    .rank-card-signature {
        height: 160px;
        margin-top: 20px;

        > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 5px;
        }
    }

    // .rank-card-signature-wall {
    //     height: 134px;
    //     border-radius: 10px;
    //     background: url('../image/sign-bg.svg') no-repeat;
    //     background-size: cover;
    //     font-size: 14px;
    //     padding: 20px;
    //     margin-top: 20px;
        
    //     .signature-content {}

    //     .signature-user-name {
    //         margin-top: 10px;
    //         text-align: right;
    //     }
    // }

    .rank-card-list {
        margin-top: 20px;

        .rank_front-three {
            display: flex;
            margin: 0 -5px;

            &.center {
                justify-content: center;
            }

            .front-three-item {
                width: calc(33.33% - 10px);
                flex-shrink: 0;
                margin: 0 5px;
                position: relative;
                padding-top: 35px;

                .avatar-wrap {
                    position: absolute;
                    top: 10px;
                    left: 50%;
                    margin-left: -25px;

                    > .img {
                        width: 50px;
                        height: 50px;
                        border-radius: 50%;
                        overflow: hidden;
                    }

                    .level {
                        width: 24px;
                        height: 24px;
                        position: absolute;
                        right: -9px;
                        top: -9px;
                    }
                }

                .front-three-cup {
                    height: 111px;
                    text-align: center;
                    font-size: 16px;
                    color: #333;
                    border-radius: 5px;
                    padding-top: 30px;

                    .group {
                        font-size: 12px;
                        color: #999;
                    }
                    .price {
                        font-weight: 700;
                    }
                }

                &.level2 {
                    .front-three-cup {
                        background: linear-gradient(180deg, #f5f5f5 0%, #ffffff 100%);
                    }
                }
                &.level1 {
                    position: relative;
                    top: -10px;

                    .front-three-cup {
                        background: linear-gradient(180deg, #fff8ed 0%, #ffffff 100%);
                    }
                }
                &.level3 {
                    .front-three-cup {
                        background: linear-gradient(180deg, #fff1ea 0%, #ffffff 100%);
                    }
                }
            }
        }

        .user-rank-list {
            padding: 0 5px;
            margin-top: 10px;

            .rank-item {
                display: flex;
                align-items: center;
                color: #333;
                margin-top: 20px;

                &:first-child {
                    margin-top: 5px;
                }

                .num {
                    width: 22px;
                    height: 22px;
                    flex-shrink: 0;
                    border-radius: 50%;
                    background: #ebeff2;
                    margin-right: 10px;
                    font-size: 12px;
                    text-align: center;
                    line-height: 22px;
                }

                .name {
                    flex: 1;
                    min-width: 0;
                    margin-right: 10px;
                    font-size: 16px;
                }

                .group {
                    width: 36%;
                    min-width: 131px;
                    flex-shrink: 0;
                    margin-right: 10px;
                    font-size: 12px;
                    color: #999;
                }

                .price {
                    width: 23%;
                    min-width: 82px;
                    flex-shrink: 0;
                    font-size: 14px;
                    font-weight: 700;
                    text-align: right;
                }
            }
        }
    }

    .group-rank-list {
        padding-top: 5px;

        .rank-item {
            display: flex;
            align-items: center;
            padding-right: 30px;
            margin-top: 20px;

            &.before-three {
                height: 34px;
                padding: 5px 30px 5px 0;
                margin-top: 15px;
            }

            &.bg0 {
                background: url('../../../image/broadcast/bg1.png') no-repeat;
                background-size: 100% 100%;
            }
            &.bg1 {
                background: url('../../../image/broadcast/bg2.png') no-repeat;
                background-size: 100% 100%;
            }
            &.bg2 {
                background: url('../../../image/broadcast/bg3.png') no-repeat;
                background-size: 100% 100%;
            }

            .rank {
                width: 24px;
                height: 24px;
                margin-right: 10px;
            }
            .num {
                width: 22px;
                height: 22px;
                border-radius: 50%;
                background: #ebeff2;
                margin-right: 11px;
                margin-left: 1px;
                font-size: 12px;
                text-align: center;
                line-height: 22px;
            }

            .group {
                flex: 1;
                min-width: 0;
                margin-right: 5px;
                font-size: 14px;
                color: #333;
            }

            .process-wrap {
                width: 105px;
                display: flex;
                align-items: center;
                margin-right: 5px;

                .process-line {
                    width: 65px;
                    height: 5px;
                    border-radius: 3px;
                    background: #EBEFF2;

                    .process {
                        width: 60px;
                        height: 100%;
                        border-radius: 3px;
                        background: #59D25A;
                    }
                }

                .process-num {
                    width: 45px;
                    margin-left: 5px;
                    font-size: 16px;
                    font-weight: 700;
                    text-align: right;
                }
            }

            .price {
                width: 74px;
                font-size: 14px;
                font-weight: 700;
                text-align: right;
            }

        }

        &.rank-percent {
            .rank-item {
                padding-right: 15px;
            }

            .rank-item.before-three {
                padding-right: 15px;
            }

            .price {
                font-size: 12px;
                color: #999;
                font-weight: normal;
                width: 74px;
            }
        }
    }

    .card-empty {
        padding-top: 200px;
        text-align: center;
        font-size: 14px;

        .icon-info1 {
            font-size: 40px;
            margin-bottom: 10px;
            color: #666;
        }
    }
}



@media screen and (max-width: 768px) {
    .vd-ui-rank-card {
        padding: 15px;
        height: auto;

        .card-empty {
            padding: 90px 0;
        }

        .rank-card-top {
            .card-filter {
                .filter-item {
                    margin-right: 5px;

                    &:hover {
                        background: none;
                        color: #333;
                    }
                }
            }
        }

        .rank-card-signature {
            height: 0;
            margin-top: 15px;
            padding-top: 42.77%;
            position: relative;
    
            > img {
                width: 100%;
                height: auto;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
        }

        .rank-card-list {    
            .rank_front-three {
                .front-three-item {
                    .front-three-cup {
                        height: 108px;
                        font-size: 14px;
                        color: #000;

                        .price {
                            font-size: 14px;
                            font-weight: 700;
                        }
                    }
                }
            }

            .user-rank-list {
                margin-top: 0;

                .rank-item {
                    margin-top: 15px;
                    color: #000;

                    .name {
                        font-size: 14px;
                        margin-right: 5px;
                    }

                    .group {
                        width: 105px;
                        min-width: auto;
                        margin-right: 5px;
                    }

                    .price {
                        font-size: 14px;
                        min-width: auto;
                        width: 82px;
                    }
                }
            }
        }

        .group-rank-list {
            padding-top: 5px;

            .rank-item {
                margin-top: 15px;
                padding-right: 15px;

                &.before-three {
                    padding: 8px 15px 8px 0;
                    margin-top: 10px;
                }

                .group {
                    margin-right: 5px;
                    color: #000;
                }

                .process-wrap {
                    width: 42px;
                    min-width: auto;

                    .process-line {
                        display: none;
                    }

                    .process-num {
                        width: 42px;
                        font-size: 15px;
                        text-align: right;
                        margin-left: 0;
                    }
                }
            }
        }
    }
}


@media screen and (min-width: 320px) and (max-width: 359px) {
    .vd-ui-rank-card {
        .rank-card-list {
            .rank_front-three {
                .front-three-item {
                    .front-three-cup {
                        height: 95px;
                        font-size: 12px;

                        .price {
                            font-size: 12px;
                        }
                    }
                }
            }

            .user-rank-list {
                .rank-item {
                    .name {
                        font-size: 12px;
                    }

                    .group {
                        width: 105px;
                    }

                    .price {
                        font-size: 12px;
                        width: 62px;
                    }
                }
            }
        }

        .group-rank-list {
            .rank-item {
                .group {
                    font-size: 12px;
                }

                .process-wrap {
                    width: 30px;

                    .process-num {
                        width: 30px;
                        font-size: 13px;
                    }
                }
            }
        }
    }
}