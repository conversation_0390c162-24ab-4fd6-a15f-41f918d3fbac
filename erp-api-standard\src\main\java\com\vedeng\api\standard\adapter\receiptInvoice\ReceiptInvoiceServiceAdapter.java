package com.vedeng.api.standard.adapter.receiptInvoice;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.api.standard.adapter.receiptInvoice.dto.ReceiptInvoiceQueryDetailRequest;
import com.vedeng.api.standard.adapter.receiptInvoice.dto.ReceiptInvoiceQueryRequest;
import com.vedeng.api.standard.adapter.receiptInvoice.dto.ReceiptInvoiceRequest;
import com.vedeng.api.standard.adapter.receiptInvoice.dto.ReceiptInvoiceResponse;
import com.vedeng.api.standard.converter.ResponseConfig;
import com.vedeng.api.standard.converter.ResponseMappingConfig;
import com.vedeng.api.standard.core.AbstractServiceAdapter;
import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.template.BusinessTemplate;
import com.vedeng.api.standard.template.ParameterConfig;
import com.vedeng.api.standard.validation.ValidationContextKeys;
import com.vedeng.api.standard.validation.rules.ReceiptInvoiceApproveRule;
import com.vedeng.api.standard.validation.rules.ReceiptInvoiceEnableRule;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.dto.BuyorderGoodsApiDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.dto.InvoiceGoodsDto;
import com.vedeng.erp.finance.dto.InvoiceGoodsResultDto;
import com.vedeng.erp.finance.dto.InvoiceResultDto;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.service.InvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 收票申请适配器
 */
@Component("receiptInvoiceServiceAdapter")
public class ReceiptInvoiceServiceAdapter extends AbstractServiceAdapter {
    
    @Autowired
    private BusinessTemplate businessTemplate;
    
    @Override
    protected void registerOperationHandlers() {
        registerThrowingHandler("create", this::executeCreateOperation);
        registerThrowingHandler("query", this::executeQueryOperation);
        registerThrowingHandler("queryInvoiceDetail", this::executeQueryInvoiceDetailOperation);
        registerThrowingHandler("approve", this::executeApproveOperation);
        registerThrowingHandler("queryHref", this::executeQueryHrefOperation);
        registerThrowingHandler("updateHref", this::executeUpdateHrefOperation);
        registerThrowingHandler("checkReceiptInvoiceStatus", this::executeCheckReceiptInvoiceStatusOperation);
    }

    /**
     * 获取不需要身份认证的操作列表
     *
     * 采购单模块中，查询操作通常不需要身份认证，
     * 允许外部系统或匿名用户查询采购单信息
     *
     * @return 不需要认证的操作名称数组
     */
    @Override
    public String[] getNoAuthActions() {
        return new String[]{"query","queryInvoiceDetail","queryHref","checkReceiptInvoiceStatus"};
    }


    @Override
    public String getModuleName() {
        return "receiptInvoice";
    }
    
    @Autowired
    private InvoiceApiService invoiceApiService;
    
    @Autowired
    private BuyorderApiService buyorderApiService;
    
    @Autowired
    private InvoiceService invoiceService;

    @Override
    public void preProcess(String action, ApiRequest apiRequest) {
        super.preProcess(action, apiRequest);
    }

    @Override
    public Object postProcess(String action, ApiRequest request, Object result) {
        return super.postProcess(action, request, result);
    }
    
    /**
     * 执行采购录票操作
     */
    private Object executeCreateOperation(ApiRequest request) throws Exception {
        ResponseMappingConfig responseConfig = ResponseConfig.create("采购录票成功");

        ReceiptInvoiceRequest receiptInvoiceRequest = new ReceiptInvoiceRequest();
        BeanUtil.fillBeanWithMap(request.getData(), receiptInvoiceRequest, true);

            logger.info("开始执行采购录票: requestId={}，入参：{}", request.getRequestId(), JSONObject.toJSONString(request.getData()));
            
            return businessTemplate.<ReceiptInvoiceRequest, ReceiptInvoiceResponse>executeCreate(request)
                    .requestType(ReceiptInvoiceRequest.class)
                    .responseType(ReceiptInvoiceResponse.class)
                    .validationRules(ReceiptInvoiceEnableRule.class)
                    .controller("invoiceController", "saveBuyOrderInvoiceNew")
                    .withIdempotencyHandling("RECEIPT_INVOICE")
                    .withBusinessFields("invoiceNo","buyOrderNo")
                    .withHttpParameters(
                            ParameterConfig.fromValidationContext(ValidationContextKeys.INVOICE, Invoice.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.SAVE_INVOICE_TYPE, Integer.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.RELATED_ID_ARR, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.DETAIL_GOODS_ID_ARR, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.INVOICE_NUM_ARR, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.INVOICE_PRICE_ARR, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.GOODS_TYPE_ARR, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.INVOICE_TOTAL_AMOUNT_ARR, String.class)
                    )
                    .responseConfig(responseConfig)
                    .execute();
    }
    

    private Object executeQueryOperation(ApiRequest request) throws Exception {
        ReceiptInvoiceQueryRequest invoiceRequest = new ReceiptInvoiceQueryRequest();
        BeanUtil.fillBeanWithMap(request.getData(), invoiceRequest, true);
        return businessTemplate.<ReceiptInvoiceQueryRequest, InvoiceDto>executeQuery(request)
                .requestType(ReceiptInvoiceQueryRequest.class)
                .responseType(InvoiceDto.class)
                .controller("invoiceServiceImpl", "queryReceiptInvoiceRecord")
                .withoutHttpParameters(
                        ParameterConfig.integer(invoiceRequest.getBuyOrderId()),
                        ParameterConfig.string(invoiceRequest.getInvoiceNo())
                ).execute();
    }

    private Object executeQueryInvoiceDetailOperation(ApiRequest request) throws Exception{
        ReceiptInvoiceQueryDetailRequest invoiceRequest = new ReceiptInvoiceQueryDetailRequest();
        BeanUtil.fillBeanWithMap(request.getData(), invoiceRequest, true);
        return businessTemplate.<ReceiptInvoiceQueryDetailRequest, InvoiceResultDto>executeQuery(request)
                .requestType(ReceiptInvoiceQueryDetailRequest.class)
                .responseType(InvoiceResultDto.class)
                .controller("invoiceServiceImpl", "queryInvoiceGoodsBySaleorderNo")
                .withoutHttpParameters(
                        ParameterConfig.string(invoiceRequest.getSaleOrderNo()),
                        ParameterConfig.of(List.class,invoiceRequest.getInvoiceNoList())
                ).execute();
    }

    private Object executeApproveOperation(ApiRequest request) throws Exception {
        ReceiptInvoiceRequest receiptInvoiceRequest = new ReceiptInvoiceRequest();
        BeanUtil.fillBeanWithMap(request.getData(), receiptInvoiceRequest, true);

        logger.info("开始执行收票审核: requestId={}", request.getRequestId());
        return businessTemplate.<ReceiptInvoiceRequest, ReceiptInvoiceResponse>executeCreate(request)
                .requestType(ReceiptInvoiceRequest.class)
                .responseType(ReceiptInvoiceResponse.class)
                .validationRules(ReceiptInvoiceApproveRule.class)
                .controller("invoiceService", "saveInvoiceAudit")
                .withoutHttpParameters(
                        ParameterConfig.fromValidationContext(ValidationContextKeys.INVOICE, Invoice.class)
                )
                .execute();
    }

    /**
     * 执行查询链接操作
     */
    private Object executeQueryHrefOperation(ApiRequest request) throws Exception {
        ReceiptInvoiceQueryRequest invoiceRequest = new ReceiptInvoiceQueryRequest();
        BeanUtil.fillBeanWithMap(request.getData(), invoiceRequest, true);
        
        logger.info("开始执行查询链接: requestId={}", request.getRequestId());
        return businessTemplate.<ReceiptInvoiceQueryRequest, Object>executeQuery(request)
                .requestType(ReceiptInvoiceQueryRequest.class)
                .responseType(Object.class)
                .controller("invoiceServiceImpl", "queryHref")
                .withoutHttpParameters(
                        ParameterConfig.string(invoiceRequest.getInvoiceNo())
                ).execute();
    }

    /**
     * 执行更新链接操作
     */
    private Object executeUpdateHrefOperation(ApiRequest request) throws Exception {
        ReceiptInvoiceRequest receiptInvoiceRequest = new ReceiptInvoiceRequest();
        BeanUtil.fillBeanWithMap(request.getData(), receiptInvoiceRequest, true);

        logger.info("开始执行更新链接: requestId={}", request.getRequestId());
        ResponseMappingConfig responseConfig = ResponseConfig.create("更新链接成功");
        
        return businessTemplate.<ReceiptInvoiceRequest, ReceiptInvoiceResponse>executeCreate(request)
                .requestType(ReceiptInvoiceRequest.class)
                .responseType(ReceiptInvoiceResponse.class)
                .controller("invoiceServiceImpl", "updateHref")
                .withoutHttpParameters(
                        ParameterConfig.string(receiptInvoiceRequest.getInvoiceNo()),
                        ParameterConfig.string(receiptInvoiceRequest.getInvoiceHref())
                )
                .responseConfig(responseConfig)
                .execute();
    }

    /**
     * 检查采购单录票状态
     * 
     * @param request API请求
     * @return 录票状态结果
     */
    private Object executeCheckReceiptInvoiceStatusOperation(ApiRequest request) {
        String buyOrderNo = (String) request.getData().get("buyOrderNo");
        
        if (buyOrderNo == null || buyOrderNo.trim().isEmpty()) {
            throw new RuntimeException("采购单号不能为空");
        }
        
        try {
            logger.info("开始检查采购单录票状态: buyOrderNo={}, requestId={}", buyOrderNo, request.getRequestId());
            
            // 根据采购单号获取采购单信息
            BuyOrderApiDto buyOrder = buyorderApiService.getBuyorderByBuyorderNo(buyOrderNo);
            if (buyOrder == null) {
                throw new RuntimeException("采购单不存在: " + buyOrderNo);
            }
            
            List<String> invoiceNoList = new ArrayList<>();
            boolean isCompleted = checkReceiptInvoiceStatus(buyOrderNo,invoiceNoList);
            
            Map<String, Object> result = new HashMap<>();
            result.put("completed", isCompleted);
            result.put("invoiceNoList", invoiceNoList);
            
            logger.info("采购单录票状态检查完成: buyOrderNo={}, isCompleted={}", buyOrderNo, isCompleted);
            return result;
            
        } catch (Exception e) {
            logger.error("检查采购单录票状态失败: buyOrderNo={}, requestId={}", buyOrderNo, request.getRequestId(), e);
            throw new RuntimeException("检查采购单录票状态失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查采购单录票状态
     * 根据采购单查询采购商品数量和采购单录票数量对比，如果前者大于后者，则返回false，否则true
     * 
     * @param buyOrderNo 采购单号
     * @return isCompleted 是否完成录票
     */
    public boolean checkReceiptInvoiceStatus(String buyOrderNo,List<String> invoiceNoList) {
        // 根据采购单号获取采购单信息
        BuyOrderApiDto buyOrder = buyorderApiService.getBuyorderByBuyorderNo(buyOrderNo);
        if (buyOrder == null) {
            throw new RuntimeException("采购单不存在: " + buyOrderNo);
        }
        
        // 获取采购单商品列表
        List<BuyorderGoodsApiDto> goodsList = buyOrder.getBuyorderGoodsApiDtos();
        if (goodsList == null || goodsList.isEmpty()) {
            return false;
        }

        List<InvoiceGoodsDto> invoiceNo = invoiceApiService.queryInvoiceByBuyOrderNo(buyOrderNo);
        if (invoiceNo == null){
            return false;
        }
        invoiceNoList.addAll(invoiceNo.stream().map(InvoiceGoodsDto::getInvoiceNo).distinct().collect(Collectors.toList()));

        
        // 计算总采购数量和总录票数量
        BigDecimal totalPurchaseNum = BigDecimal.valueOf(goodsList.stream().mapToInt(BuyorderGoodsApiDto::getNum).sum());
        BigDecimal totalInvoiceNum = invoiceNo.stream().map(InvoiceGoodsDto::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        
        
        // 如果采购数量大于录票数量，则返回false，否则返回true
        return totalPurchaseNum.compareTo(totalInvoiceNum) <= 0;
    }

}
