package com.vedeng.temporal.workflow.activity.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.workflow.activity.InvoiceEntryActivity;
import com.vedeng.temporal.workflow.activity.core.UniversalActivityTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 发票录入 Activity 实现类
 *
 * 架构迁移说明：
 * - 从 InvoiceEntryFlow 迁移核心业务逻辑到 Activity 层
 * - 使用 UniversalBusinessTemplate 处理所有业务逻辑：固定参数、API调用、异常处理、通知发送
 * - 每个方法都是独立的 Activity，由 Temporal 管理重试
 * - 直接使用 CompanyBusinessRequest 传递数据，自动解析 JSON
 * - 保持与原 InvoiceEntryFlow 完全一致的业务逻辑和API调用
 *
 * 业务流程：
 * 1. createInvoiceEntry - 执行完整发票录入流程，包含查询详情、录入、审核
 * 2. queryInvoiceDetail - 查询发票详情，用于流程控制
 * 3. approveInvoice - 审核发票，用于流程控制
 * 4. createInvoiceWithDetails - 基于预查询数据创建发票录入
 *
 * 迁移内容：
 * - execute 方法迁移为 createInvoiceEntry 方法
 * - executeInvoiceEntry、executeSubmitApproval、executeApproval 逻辑集成
 * - extractInvoiceId、extractApproveResult 方法保持不变
 * - 保持原有的API路径和参数结构
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0 (架构迁移版本，从 InvoiceEntryFlow 迁移)
 * @since 2025-01-21
 */
@Component
@Slf4j
public class InvoiceEntryActivityImpl implements InvoiceEntryActivity {

    @Autowired
    private UniversalActivityTemplate universalActivityTemplate;



    @Override
    public CompanyBusinessResponse createInvoiceWithDetails(CompanyBusinessRequest request,
                                                            Map<String, Object> invoiceDetailData) {
        request.setUserName("admin");
        // 配置业务操作 - 创建发票
        UniversalActivityTemplate.BusinessOperationConfig config =
                UniversalActivityTemplate.BusinessOperationConfig.create()
                        .operationName("创建发票")
                        .apiPath("/api/v1/receiptInvoice/create.do")
                        .dataPreparer(req -> prepareInvoiceCreateData(req, invoiceDetailData));

        // 直接调用，异常处理由模板统一处理
        CompanyBusinessResponse result = universalActivityTemplate.execute(request, config);
        return result;
    }


    @Override
    public CompanyBusinessResponse approveInvoice(CompanyBusinessRequest request,Map<String, Object> invoiceDetailData) {
        // 配置业务操作 - 审核发票
        request.setUserName("admin");
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("审核发票")
                .apiPath("/api/v1/receiptInvoice/approve.do")
                .dataPreparer(req -> prepareInvoiceApproveData(req, invoiceDetailData));

        // 直接调用，异常处理由模板统一处理
        return universalActivityTemplate.execute(request, config);
    }

    @Override
    public CompanyBusinessResponse updateInvoiceHref(CompanyBusinessRequest request) {
        request.setUserName("admin");
        // 配置业务操作 - 更新发票InvoiceHref字段
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("更新发票InvoiceHref字段")
                .apiPath("/api/v1/receiptInvoice/updateInvoiceHref.do")
                .dataPreparer(req -> prepareInvoiceHrefUpdateData(req));

        // 直接调用，异常处理由模板统一处理
        return universalActivityTemplate.execute(request, config);
    }

    /**
     * 准备发票创建数据
     */
    private Map<String, Object> prepareInvoiceCreateData(CompanyBusinessRequest request,
                                                         Map<String, Object> data) {
        Map<String, Object> createData = new HashMap<>();

        // 处理与InvoiceEntryFlow完全一致的嵌套数据结构 (第107-111行逻辑)
        if (data != null) {
            // 组装录票字段 (第119-121行逻辑)
            createData.put("invoiceNo", data.get("invoiceNo"));
            createData.put("invoiceGoods", data.get("invoiceGoods"));
        }

        // 从扩展属性中获取采购单号
        String buyOrderNo = getBuyOrderNoFromExtendedProperties(request);
        if (buyOrderNo != null) {
            createData.put("buyOrderNo", buyOrderNo);
        }

        log.info("准备发票创建数据完成，业务ID: {},{}公司执行发票创建入参：{}", request.getBusinessId(), request.getTargetCompanyCode(),JSON.toJSON(createData));
        return createData;
    }

    /**
     * 准备发票审核数据
     */
    private Map<String, Object> prepareInvoiceApproveData(CompanyBusinessRequest request, Map<String, Object> data) {
        Map<String, Object> approveData = new HashMap<>();
        if (data != null) {
            // 组装录票字段 (第119-121行逻辑)
            approveData.put("invoiceNo", data.get("invoiceNo"));
        }
        // 从扩展属性中获取采购单号
        String buyOrderNo = getBuyOrderNoFromExtendedProperties(request);
        if (buyOrderNo != null) {
            approveData.put("buyOrderNo", buyOrderNo);
        }
        log.info("准备发票审核数据完成，业务ID: {},入参：{}", request.getBusinessId(), JSON.toJSON(approveData));
        return approveData;
    }


    /**
     * 从扩展属性中获取采购单号（修正为String类型，与InvoiceEntryFlow保持一致）
     */
    private String getBuyOrderNoFromExtendedProperties(CompanyBusinessRequest request) {
        if (request.getExtendedProperties() != null) {
            return (String) request.getExtendedProperties().get("buyOrderNo");
        }
        return null;
    }

    /**
     * 准备发票InvoiceHref更新数据
     */
    private Map<String, Object> prepareInvoiceHrefUpdateData(CompanyBusinessRequest request) {
        Map<String, Object> updateData = new HashMap<>();
        
        // 从业务数据中获取发票号
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        String invoiceNo = (String) businessData.get("invoiceNo");
        
        if (invoiceNo != null) {
            updateData.put("invoiceNo", invoiceNo);
        }
        
        // 从扩展属性中获取采购单号
        String saleOrderNo = (String) request.getExtendedProperties().get("saleOrderNo");

        // 构建API参数
        if (saleOrderNo != null) {
            updateData.put("saleOrderNo", saleOrderNo);
        }
        
        log.info("准备发票InvoiceHref更新数据完成，业务ID: {}, 入参：{}", request.getBusinessId(), JSON.toJSON(updateData));
        return updateData;
    }
    
    /**
     * 解析业务数据 JSON 字符串
     */
    private Map<String, Object> parseBusinessData(String businessDataJson) {
        if (!StringUtils.hasText(businessDataJson)) {
            return new HashMap<>();
        }

        try {
            return JSON.parseObject(businessDataJson, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.warn("解析业务数据JSON失败: {}", businessDataJson, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 检查采购单录票完成状态
     */
    @Override
    public CompanyBusinessResponse checkPurchaseOrderRecordingStatus(CompanyBusinessRequest request) {
        try {
            log.info("开始检查采购单录票完成状态，业务ID: {}", request.getBusinessId());
            // 配置业务操作 - 查询快递信息
            UniversalActivityTemplate.BusinessOperationConfig config =
                    UniversalActivityTemplate.BusinessOperationConfig.create()
                            .operationName("查询录票状态")
                            .apiPath("/api/v1/receiptInvoice/checkReceiptInvoiceStatus.do")
                            .dataPreparer(this::prepareInvoiceQueryData);

            // 直接调用，异常处理由模板统一处理
            return universalActivityTemplate.execute(request, config);
            
        } catch (Exception e) {
            log.error("检查采购单录票状态失败", e);
            return CompanyBusinessResponse.failure("录票状态检查失败: " + e.getMessage(), "CHECK_STATUS_ERROR");
        }
    }

    private Object prepareInvoiceQueryData(CompanyBusinessRequest request) {
        Map<String, Object> map = new HashMap<>();
        map.put("buyOrderNo", getBuyOrderNoFromExtendedProperties(request));
        return map;
    }

    /**
     * 查询发票Href链接
     * 
     * 功能说明：
     * - 查询指定发票号的InvoiceHref字段值
     * - 用于检查发票链接是否已准备就绪
     * - 支持轮询等待InvoiceHref字段有值且不为空
     * 
     * @param request 业务请求，包含发票号
     * @return 查询结果，包含InvoiceHref字段信息
     */
    @Override
    public CompanyBusinessResponse queryInvoiceHref(CompanyBusinessRequest request) {
        try {
            log.info("开始查询发票Href链接，业务ID: {}", request.getBusinessId());
            
            // 配置业务操作 - 查询发票Href
            UniversalActivityTemplate.BusinessOperationConfig config =
                    UniversalActivityTemplate.BusinessOperationConfig.create()
                            .operationName("查询发票Href链接")
                            .apiPath("/api/v1/receiptInvoice/queryHref.do")
                            .dataPreparer(this::prepareInvoiceHrefQueryData);

            // 直接调用，异常处理由模板统一处理
            return universalActivityTemplate.execute(request, config);
            
        } catch (Exception e) {
            log.error("查询发票Href链接失败", e);
            return CompanyBusinessResponse.failure("发票Href链接查询失败: " + e.getMessage(), "QUERY_HREF_ERROR");
        }
    }
    
    /**
     * 准备发票Href查询数据
     */
    private Object prepareInvoiceHrefQueryData(CompanyBusinessRequest request) {
        Map<String, Object> queryData = new HashMap<>();
        
        // 从业务数据中获取发票号
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        String invoiceNo = (String) businessData.get("invoiceNo");
        
        if (invoiceNo != null) {
            queryData.put("invoiceNo", invoiceNo);
        }
        
        log.info("准备发票Href查询数据完成，业务ID: {}, 入参：{}", request.getBusinessId(), JSON.toJSON(queryData));
        return queryData;
    }

    /**
     * 查询发票详情
     * 
     * 功能说明：
     * - 查询销售单的发票详情信息
     * - 用于获取发票录入所需的完整数据
     * - 支持基于销售单号和已录发票列表的查询
     * 
     * @param request 业务请求，包含销售单号和已录发票列表
     * @return 发票详情查询结果，包含发票商品信息等
     */
    @Override
    public CompanyBusinessResponse queryInvoiceDetail(CompanyBusinessRequest request) {
        try {
            log.info("开始查询发票详情，业务ID: {}", request.getBusinessId());
            
            // 配置业务操作 - 查询发票详情
            UniversalActivityTemplate.BusinessOperationConfig config =
                    UniversalActivityTemplate.BusinessOperationConfig.create()
                            .operationName("查询发票详情")
                            .apiPath("/api/v1/receiptInvoice/queryInvoiceDetail.do")
                            .dataPreparer(this::prepareInvoiceDetailQueryData);

            // 直接调用，异常处理由模板统一处理
            return universalActivityTemplate.execute(request, config);
            
        } catch (Exception e) {
            log.error("查询发票详情失败", e);
            return CompanyBusinessResponse.failure("发票详情查询失败: " + e.getMessage(), "QUERY_DETAIL_ERROR");
        }
    }
    
    /**
     * 准备发票详情查询数据
     */
    private Object prepareInvoiceDetailQueryData(CompanyBusinessRequest request) {
        Map<String, Object> queryData = new HashMap<>();
        
        // 从扩展属性中获取销售单号和已录发票列表
        if (request.getExtendedProperties() != null) {
            queryData.put("saleOrderNo", request.getExtendedProperties().get("saleOrderNo"));
            queryData.put("invoiceNoList", request.getExtendedProperties().get("invoiceNoList"));
        }
        
        log.info("准备发票详情查询数据完成，业务ID: {}, 入参：{}", request.getBusinessId(), JSON.toJSON(queryData));
        return queryData;
    }

    /**
     * 查询发票录入记录
     */
    @Override
    public CompanyBusinessResponse queryInvoiceRecord(CompanyBusinessRequest request) {
        try {
            log.info("开始查询发票录入记录，业务ID: {}", request.getBusinessId());
            
            // 解析业务数据
            Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
            String buyOrderNo = (String) businessData.get("buyOrderNo");
            String invoiceNo = (String) businessData.get("invoiceNo");
            
            log.debug("查询参数 - 采购单号: {}, 发票号: {}", buyOrderNo, invoiceNo);
            
            // 调用查询API - 使用现有的query接口
            // 创建操作配置
            UniversalActivityTemplate.BusinessOperationConfig config = new UniversalActivityTemplate.BusinessOperationConfig();
            config.setOperationName("查询发票录入记录");
            config.setApiPath("/api/v1/receiptInvoice/query");
            config.setDataPreparer(req -> businessData);
            config.setResultExtractor(response -> response != null ? response.toString() : null);
            
            return universalActivityTemplate.execute(request, config);
            
        } catch (Exception e) {
            log.error("查询发票录入记录失败", e);
            return CompanyBusinessResponse.failure("查询发票录入记录失败: " + e.getMessage(), "QUERY_RECORD_ERROR");
        }
    }
}
