package com.vedeng.erp.trader.dto;

import lombok.Data;

/**
 * 通话记录
 * @ClassName:  CommunicateTelRecordApiDto   
 * @author: <PERSON>.yang
 * @date:   2025年7月3日 下午4:47:30    
 * @Copyright:
 */
@Data
public class CommunicateTelRecord{
    
	/**沟通记录ID*/
	private Integer communicateRecordId;
	
	/**添加时间（沟通时间）*/
	private String addTime;
	
	/**沟通方向【 1座机-呼入；2座机-呼出；3：企微-呼出；4：企微-呼入】*/
    private Integer coidType;
    
    /**沟通电话*/
    private String phone;
    
    /**沟通客户ID*/
    private Integer traderId;
    
    /**公司名称*/
    private String traderName;
    
    /**公司的归属销售*/
    private String saleName;
    
    /**沟通客户所属类型 1::经销商（包含终端）2:供应商*/
    private Integer traderType;
    
    /**添加人*/
    private Integer creator;
    
    /**录音时长*/
    private Integer coidLength;
    
    /**录音地址*/
    private String coidUri;
    
    /**AI 沟通记录的弹框*/
    private String aiWindowUrl;
    
    /**呼叫中心COID*/
    private String coid;
    
    /**创建人（话务人员）*/
    private String creatorName;
    
    /**创建人（话务人员）头像*/
    private String creatorAvatarUrl;
    
    /**号码归属地*/
    private String phoneArea;
    
    /**主叫号码*/
    private String ttNumber;
    
    /**头像图片URL*/
    private String avatarUrl;
    
    /**联系人备注*/
    private String contact;
    
    /**联系人名称*/
    private String contactName;
    
    /**联系人昵称*/
    private String contactNickName;
}
