<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="商品查询" scope="application" />
<%@ include file="../common/common.jsp" %>
<%@ include file="../component/remarkComponent.jsp" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/pager.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/lvSelect.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/multiSelect.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/goodsSearch/goodsSearch.css?rnd=${resourceVersionKey}">
<div class="wrap">
    <div class="search-wrap">
        <div class="search-inner">
            <input type="text" value = "${keywords}" placeholder="输入商品/品类/品牌" autocomplete="off" class="input-text search-input J-search-input" maxlength="200">
            <div class="search-history-list J-history-wrap" style="display: none;">
                <div class="search-history-clear">
                    <span class="clear-txt J-history-clear">删除历史</span>
                </div>
            </div>
            <div class="btn J-search">搜索</div>
            <div class="J-scene-search-wrap" style="display: none;"></div>
            <input type="hidden" name="" class="J-scene-search-value">
            <a class="reset J-reset">
                <i class="vd-icon icon-rotate"></i>重置
            </a>
        </div>
        <a class="price-list J-pop-new-data" layerparams='{"width":"90%","height":"90%","title":"调价列表","link":"/price/skuPriceModifyRecord/index.do?fromMenuFlag=0"}'>调价列表</a>
    </div>
    <div class="filter-wrap J-filter-wrap">
        <div class="filter-inner J-filter-block"></div>
        <div class="filter-more">
            <div class="filter-more-btn J-filter-more-btn"><span class="filter-more-btn-txt">展开</span><span class="filter-less-btn-txt">收起</span>更多选项<i class="vd-icon icon-down"></i></div>
        </div>
    </div>

    <div class="list-filter-info J-list-filter-info">
        <div class="list-filter-info-inner">
            <div class="list-filter-info-l J-filter-select-wrap link-last">
                <div class="filter-item-link J-filter-all">全部商品<i class="vd-icon icon-right"></i></div>
            </div>
            <div class="list-filter-info-r">
                <div class="filter-list J-filter-info-list" style="display: none;">
                    <div class="filter-item J-list-filter-check" data-key="saleNotContainsFee">
                        <i class="vd-icon icon-checkbox1"></i>
                        <i class="vd-icon icon-checkbox2"></i>
                        经销价不含运费
                    </div>
                    <div class="filter-item J-list-filter-check" data-key="hasVerifiedPrice">
                        <i class="vd-icon icon-checkbox1"></i>
                        <i class="vd-icon icon-checkbox2"></i>
                        已核价
                    </div>
                    <div class="filter-item J-list-filter-check" data-key="hasAuthorized">
                        <i class="vd-icon icon-checkbox1"></i>
                        <i class="vd-icon icon-checkbox2"></i>
                        已授权
                    </div>
                    <div class="filter-item J-list-filter-check" data-key="hasStock">
                        <i class="vd-icon icon-checkbox1"></i>
                        <i class="vd-icon icon-checkbox2"></i>
                        有可用库存
                    </div>
                    <div class="filter-item J-list-filter-check" data-key="isSupportInstallation">
                        <i class="vd-icon icon-checkbox1"></i>
                        <i class="vd-icon icon-checkbox2"></i>
                        支持安装
                    </div>
                    <div class="select-filter-item J-list-filter-select" data-key="afterSalesServiceLevels">
                        <div class="J-multi-star-wrap"></div>
                    </div>
                </div>
                <div class="page-simple-wrap J-simple-page" style="display: none;">
                    <div class="option-btn btn-l disabled J-page-prev"><i class="vd-icon icon-left"></i></div>
                    <div class="option-btn btn-r J-page-next"><i class="vd-icon icon-right"></i></div>
                </div>
                <div class="list-setting">
                    <div class="option-btn J-table-setting">
                        <i class="vd-icon icon-setting"></i>
                    </div>
                    <div class="sort-tip J-sort-tip">
                        列表字段新增排序功能 <i class="vd-icon icon-delete J-sort-tip-hide"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="list-wrap">
        <div class="list-cnt J-table-list-wrap">
            <table class="table J-table J-table-list">
                <thead class="J-table-head"><tr></tr></thead>
                <tbody class="J-table-body"></tbody>
            </table>
        </div>
        <div class="J-pager"></div>
    </div>
</div>
<div class="dlg-setting-wrap J-dlg-setting-wrap">
    <div class="dlg-cnt">
        <div class="dlg-title">自定义元素</div>
        <i class="dlg-close vd-icon icon-delete J-setting-close"></i>
        <div class="dlg-main">
            <div class="check-list-wrap J-unsetable">
                <div class="check-list-title">列表固定字段，不可取消和排序</div>
                <div class="check-list"></div>
            </div>
            <div class="check-list-wrap J-setable">
                <div class="check-list-title">可选择在列表中展示的字段，拖拽可进行排序</div>
                <div class="check-list"></div>
            </div>
            <div class="dlg-footer">
                <div class="dlg-btns">
                    <div class="btn J-setting-confirm">保存</div>
                    <div class="btn btn-gray J-setting-close">取消</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="dlg-setting-wrap dlg-tip J-dlg-tip-wrap">
    <div class="dlg-cnt">
        <i class="dlg-close vd-icon icon-delete J-tip-close"></i>
        <div class="dlg-main">
            <div class="tip-cnt">
                <div class="tip-img"></div>
                <div class="tip-txt">通过 Shift 加鼠标滚轮上下滚动，实现列表左右快速查看</div>
            </div>
            <div class="dlg-footer">
                <div class="dlg-btns">
                    <div class="btn J-tip-close">我知道了</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="table-fixed J-table-fixed">
    <div class="table-inner">
        <table class="table J-table">
            <thead class="J-table-head"><tr></tr></thead>
        </table>
    </div>
</div>

<div class="table-cnt-fixed J-table-cnt-fixed">
    <div class="table-head-inner">
        <table class="table">
            <thead class="J-table-cnt-head"><tr></tr></thead>
        </table>
    </div>
    <div class="table-body-inner">
        <table class="table">
            <tbody class="J-table-cnt-body"></tbody>
        </table>
    </div>
</div>
<div class="page-loading J-loading">
    <div class="img"></div>
</div>

<div class="page-tip J-page-tip">
    <div class="txt"><i class="vd-icon icon-caution2"></i><span class="tip-cnt">这边显示报错提示</span></div>
</div>

<div class="page-tip tip-success J-page-success-tip">
    <div class="txt"><i class="vd-icon icon-yes2"></i><span class="tip-cnt"></span></div>
</div>
<div class="tip-cnt-question J-tip-position-cnt" style="display: none"></div>
<script type="text/tmpl" class="J-filter-tmpl">
    <div class="filter-block J-filter-block-item" data-item='{{=JSON.stringify(filterData)}}'>
        <div class="filter-label" title="{{=filterData.filterLabel}}">{{=filterData.filterLabel}}：</div>
        <div class="filter-cnt">
            <div class="filter-list">
                {{ $.each(data, function(i, filterItem){ }}
                    <div class="filter-item J-filter-item" data-value="{{=filterItem[filterData.filterId]}}" data-name="{{=filterItem[filterData.filterName]}}">
                        {{ if(filterData.isMulti){ }}
                            <i class="item-checkbox vd-icon icon-checkbox1"></i>
                            <i class="item-checkbox vd-icon icon-checkbox2"></i>
                        {{ } }}
                        <div class="item-label">{{=filterItem[filterData.filterName]}}</div>
                    </div>
                {{ }) }}
            </div>
            <div class="filter-option">
                <div class="option-item J-filter-item-more" style="display: none;"><span class="more-txt">更多</span><span class="less-txt">收起</span><i class="vd-icon icon-down"></i></div>
                {{ if(filterData.isMulti){ }}
                    <div class="option-item J-filter-item-multi">多选<i class="vd-icon icon-add"></i></div>
                {{ } }}
            </div>
            {{ if(filterData.isMulti){ }}
                <div class="filter-option option-multi">
                    <a class="btn btn-s disabled J-filter-multi-confirm">确定</a>
                    <a class="btn btn-gray btn-s J-filter-multi-cancel">取消</a>
                </div>
            {{ } }}
        </div>
    </div>
</script>
<script type="text/tmpl" class="J-setting-item-tmpl">
    <div class="checkbox-item-wrap {{ if(data.disabled){ }}disabled{{ } }}" data-key="{{=data.key}}">
        <div class="checkbox-item {{ if(data.checked){ }}checked{{ } }} {{ if(data.disabled){ }}disabled{{ } }}" data-key="{{=data.key}}" title="{{=data.name}}">
            <i class="vd-icon icon-checkbox1"></i>
            <i class="vd-icon icon-checkbox2"></i>
            <span class="item-txt">{{=data.name}}</span>
        </div>
        {{ if(!data.disabled){ }}<i class="drag-icon"></i>{{ } }}
    </div>
</script>
<script type="text/tmpl" class="J-table-head-tmpl">
    <th {{ if(item.sort){ }} class="sort J-th-sort {{ if(sortKey===item.key){ }} checked  {{=['up', 'down'][sortType]}} {{ } }} "  {{ } }} {{ if(item.align){ }} style="text-align:{{=item.align}};" {{ } }} data-key="{{=item.key}}">
        <div class="th-wrap">
            <div class="th-txt">{{=item.name}}</div>
            {{ if(item.sort){ }}
                <div class="th-sort"></div>
            {{ } }}
            {{ if(item.icon){ }}
                <div class="tip-wrap J-tip-position" data-txt="{{=item.iconTxt}}">
                    <i class="tip-icon vd-icon {{=item.icon}}"></i>
                </div>
            {{ } }}
        </div>
    </th>
</script>
<script src="${pageContext.request.contextPath}/static/new/js/common/pager.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/lv-select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/multiSelect.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/sortable.min.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/goodsSearch/goodsSearch.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../common/footer.jsp" %>