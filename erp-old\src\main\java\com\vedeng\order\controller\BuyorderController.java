package com.vedeng.order.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.constants.Contant;
import com.ezadmin.common.utils.Utils;
import com.google.gson.Gson;
import com.newtask.data.dao.BuyorderDataMapper;
import com.newtask.data.dto.BuyorderDataDto;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.constant.InstallPolicyInstallTypeEnum;
import com.vedeng.aftersales.model.AfterSaleSupplyPolicy;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSaleServiceStandardService;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.dao.UserDetailMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Company;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.model.UserDetail;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.enums.SupplierAssetEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.service.BaseService;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.*;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.buyorder.dto.BuyOrderSaleOrderGoodsDetailDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.dto.OrderPaymentDetailsDto;
import com.vedeng.erp.buyorder.dto.PurchaseDeliveryBatchInfoDto;
import com.vedeng.erp.buyorder.service.*;
import com.vedeng.erp.finance.cmd.SettlementBillQueryCmd;
import com.vedeng.erp.finance.dto.PayApplyCheckDto;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.finance.dto.SettlementBillApiDto;
import com.vedeng.erp.finance.dto.SettlementBillItemApiDto;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.finance.service.PayApplyAutoPayApi;
import com.vedeng.erp.finance.service.SettlementBillApiService;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.system.service.FlowOrderApiService;
import com.vedeng.erp.trader.dto.SupplierAssetApiDto;
import com.vedeng.erp.trader.service.SupplierAssetApiService;
import com.vedeng.finance.dao.PayApplyMapper;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.model.PayApplyDetail;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.finance.service.PayApplyService;
import com.vedeng.firstengage.service.FirstEngageService;
import com.vedeng.flash.constant.FlashConstant;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.model.GoodsSafeStock;
import com.vedeng.goods.model.dto.CoreSkuDto;
import com.vedeng.goods.model.dto.CoreSpuBaseDTO;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.goods.model.vo.ProductManagerAndAssistantIdVo;
import com.vedeng.goods.service.BaseGoodsService;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.Logistics;
import com.vedeng.logistics.model.*;
import com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo;
import com.vedeng.logistics.service.*;
import com.vedeng.order.chain.model.BuyorderRiskModelVo;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.OrderAssistantRelationMapper;
import com.vedeng.order.model.*;
import com.vedeng.order.model.dto.OrderAssistantRelationDto;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import com.vedeng.order.model.dto.PurchaseContractDto;
import com.vedeng.order.model.ge.GeTraderSku;
import com.vedeng.order.model.vo.*;
import com.vedeng.order.service.*;
import com.vedeng.order.service.impl.BuyorderServiceImpl;
import com.vedeng.order.service.validator.dto.ValidaterResult;
import com.vedeng.order.service.validator.impl.QualifyAutoAudtioValidatorChain;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.dto.SkuPriceInfoPurchaseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.ParamsConfigValue;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.Tag;
import com.vedeng.system.model.vo.AddressVo;
import com.vedeng.system.model.vo.ParamsConfigVo;
import com.vedeng.system.service.*;
import com.vedeng.todolist.service.RiskCheckLogService;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.vo.*;
import com.vedeng.trader.service.TraderCustomerService;
import com.vedeng.trader.service.TraderSupplierService;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.model.dto.ValidatorResult;
import com.wms.service.CancelTypeService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.validator.*;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import org.thymeleaf.util.MapUtils;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <b>Description:</b><br>
 * 采购订单控制器
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.order.model <br>
 * <b>ClassName:</b> BuyorderController <br>
 * <b>Date:</b> 2017年7月11日 上午9:07:20
 */
@Controller
@RequestMapping("/order/buyorder")
public class BuyorderController extends BaseController {
    public static Logger logger = LoggerFactory.getLogger(BuyorderController.class);


    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
    @Resource
    private BuyorderService buyorderService;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private PayApplyMapper payApplyMapper;

    @Autowired
    @Qualifier("saleorderService")
    private SaleorderService saleorderService;
    @Resource
    private OrgService orgService;
    @Resource
    private UserService userService;
    @Resource
    private TraderSupplierService traderSupplierService;
    @Resource
    private TraderCustomerService traderCustomerService;
    @Resource
    private TagService tagService;
    @Autowired
    @Qualifier("expressService")
    private ExpressService expressService;
    @Autowired
    @Qualifier("warehouseInService")
    private WarehouseInService warehouseInService;
    @Autowired
    @Qualifier("logisticsService")
    private LogisticsService logisticsService;
    @Autowired
    @Qualifier("payApplyService")
    private PayApplyService payApplyService;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private AfterSalesService afterSalesOrderService;
    @Resource
    private FtpUtilService ftpUtilService;

    @Autowired
    @Qualifier("goodsService")
    private GoodsService goodsService;

    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    @Qualifier("userDetailMapper")
    private UserDetailMapper userDetailMapper;

    @Autowired
    @Qualifier("companyService")
    private CompanyService companyService;

    @Autowired
    @Qualifier("addressService")
    private AddressService addressService;

    @Autowired
    @Qualifier("vedengSoapService")
    private VedengSoapService vedengSoapService;

    @Autowired
    @Qualifier("hcSaleorderService")
    protected HcSaleorderService hcSaleorderService;


    @Value("${gongyinglian_org_id}")
    protected Integer gongyinglian_org_id;
    @Value("${vedeng_address_phone}")
    protected String vedeng_address_phone;

    @Autowired
    private VgoodsService vGoodsService;

    @Autowired
    private BasePriceService basePriceService;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private PurchaseExgCloseValidator purchaseExgCloseValidator;

    @Resource
    private PurchaseAfterSaleCloseValidator purchaseAfterSaleCloseValidator;

    @Resource
    private PurchaseReturnCloseValidator purchaseReturnCloseValidator;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private FirstEngageService firstEngageService;

    @Autowired
    private BaseGoodsService baseGoodsService;

    @Autowired
    private RiskCheckService riskCheckService;

    @Autowired
    private RiskCheckLogService riskCheckLogService;

    @Value("${GE_TRADER_SKU}")
    protected String GE_TRADER_SKU;

    @Autowired
    private AfterSaleServiceStandardService afterSaleServiceStandardService;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private SysOptionDefinitionService sysOptionDefinitionService;

    @Autowired
    @Qualifier("orderAssistantRelationService")
    private OrderAssistantRelationService orderAssistantRelationService;

    @Resource
    private OrderAssistantRelationMapper orderAssistantRelationMapper;

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private QualifyAutoAudtioValidatorChain qualifyAutoAudtioValidatorChain;

    @Autowired
    private OrderReviewProcessService orderReviewProcessService;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private BuyorderInfoSyncService buyorderInfoSyncService;

    @Resource
    private BuyorderDataMapper buyorderDataMapper;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Autowired
    private BaseService baseService;
    @Autowired
    private PurchaseDeliveryBatchInfoService purchaseDeliveryBatchInfoService;

    @Autowired
    private AttachmentService attachmentService;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private CapitalBillService capitalBillService;


    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    private RBuyorderExpenseJSaleorderService rBuyorderExpenseJSaleorderService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private SupplierAssetApiService supplierAssetApiService;

    @Autowired
    private BuyOrderRebateApiService buyOrderRebateApiService;

    @Autowired
    private SettlementBillApiService settlementBillApiService;
    @Autowired
    private CapitalBillApiService capitalBillApiService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private PayApplyAutoPayApi payApplyAutoPayApi;

    @Autowired
    private BuyorderApiService buyorderApiService;

    @Autowired
    private PayApplyApiService payApplyApiService;


    @RequestMapping(value = "indexPendingPurchaseExport")
    public void indexPendingPurchaseExport(HttpServletRequest request, HttpServletResponse response) throws IOException {

        GoodsVo goodsVo = new GoodsVo();
        //goodsVo.setComponentId(2);
        goodsVo.setOrderType(1);
        goodsVo.setCompanyId(1);
        Page page = Page.newBuilder(1, Integer.MAX_VALUE, null);

        Map<String, Object> map = buyorderService.getUnPurchasingOrderList(goodsVo, page);
        if (map == null) {
            return;
        }

        List<SaleorderVo> saleorderList = (List<SaleorderVo>) map.get("list");

        //订单号 saleorderNo
        //申请人 applicantName
        //可采购时间 satisfyDeliveryTime
        //订货号 sku
        //产品名称 xxxx
        //归属产品经理 assignmentManagerId
        //归属产品助理 assignmentAssistantId
        //商品总数 num
        //采购类型 satisfyDeliveryTime
        //原因 buyProcessModReson

        List<String> skuNos = new ArrayList<>();
        saleorderList.stream().forEach(saleOrder -> {
            saleOrder.getSgvList().stream().forEach(saleOrderGoods -> {
                skuNos.add(saleOrderGoods.getSku());
            });
        });

        List<CoreSkuDto> skuList = coreSkuMapper.batchFindBySkuNos(skuNos);

        List<ProductManageAndAsistDto> asistList = coreSkuMapper.batchQueryProductManageAndAsist(skuNos);

        List<User> allUserList = userMapper.getAllUserListInfo();

        List<SaleorderGoodsExportVo> saleorderGoodsExportList = new ArrayList<>();

        for (SaleorderVo saleOrder : saleorderList) {

            if (CollectionUtils.isEmpty(saleOrder.getSgvList())) {
                continue;
            }

            String userName = allUserList.stream().filter(user -> user.getUserId().equals(saleOrder.getValidUserId().intValue()))
                    .findFirst()
                    .map(user -> user.getUsername())
                    .orElse("");

            String orgName = allUserList.stream().filter(user -> user.getUserId().equals(saleOrder.getValidUserId().intValue()))
                    .findFirst()
                    .map(user -> user.getOrgName())
                    .orElse("");


            if (StringUtil.isEmpty(userName)) {
                userName = Optional.ofNullable(userMapper.getUserByTraderId(saleOrder.getTraderId(), 1)).map(user -> user.getUsername()).orElse("");
            }

            for (SaleorderGoodsVo saleOrderGood : saleOrder.getSgvList()) {

                SaleorderGoodsExportVo saleorderGoodsExportVo = new SaleorderGoodsExportVo();

                saleorderGoodsExportVo.setSaleorderNo(saleOrder.getSaleorderNo());
                saleorderGoodsExportVo.setApplicantName(userName);
                saleorderGoodsExportVo.setSalesDeptName(orgName);
                saleorderGoodsExportVo.setSatisfyDeliveryTimeStr(DateUtil.convertString(saleOrder.getSatisfyDeliveryTime(), "yyyy-MM-dd HH:mm:ss"));
                saleorderGoodsExportVo.setSku(saleOrderGood.getSku());
                saleorderGoodsExportVo.setSkuName(skuList.stream()
                        .filter(sku -> sku.getSkuNo().equals(saleOrderGood.getSku()))
                        .findFirst()
                        .map(sku -> sku.getSkuName())
                        .orElse("")
                );

                saleorderGoodsExportVo.setAssignmentAssistantId(asistList.stream()
                        .filter(asist -> asist.getSkuNo().equals(saleOrderGood.getSku()))
                        .findFirst()
                        .map(asist -> asist.getProductAssitName())
                        .orElse("")
                );

                saleorderGoodsExportVo.setAssignmentManagerId(asistList.stream()
                        .filter(asist -> asist.getSkuNo().equals(saleOrderGood.getSku()))
                        .findFirst()
                        .map(asist -> asist.getProductManageName())
                        .orElse("")
                );


                saleorderGoodsExportVo.setStockValue(saleOrderGood.getCanUseGoodsStock() + "/" + saleOrderGood.getGoodsStock());

                saleorderGoodsExportVo.setOnWayNum(saleOrderGood.getOnWayNum());
                saleorderGoodsExportVo.setDeliveryDirect(saleOrderGood.getDeliveryDirect() == 0 ? "普发" : "直发");
                saleorderGoodsExportVo.setNum(saleOrderGood.getNum());
                saleorderGoodsExportVo.setInsideComments(saleOrderGood.getInsideComments());
                saleorderGoodsExportVo.setLogisticsComments(saleOrder.getLogisticsComments());

                if (saleOrderGood.getComponentId() == null || saleOrderGood.getComponentId() == 2) {
                    saleorderGoodsExportVo.setPurchaseType("立即采购");
                } else if (saleOrderGood.getComponentId() == 3) {
                    saleorderGoodsExportVo.setPurchaseType("暂缓采购");
                } else if (saleOrderGood.getComponentId() == 14) {
                    saleorderGoodsExportVo.setPurchaseType("无需采购");
                }

                saleorderGoodsExportVo.setBuyProcessModReson(saleOrderGood.getBuyProcessModReson());
                saleorderGoodsExportVo.setBuyProcessModTime(saleOrderGood.getBuyProcessModTime() != null ?
                        DateUtil.convertString(saleOrderGood.getBuyProcessModTime(), "yyyy-MM-dd HH:mm:ss") : "");

                saleorderGoodsExportList.add(saleorderGoodsExportVo);
            }

        }
        ;


//		IreportExport.exportWrite(request, response, "/WEB-INF/ireport/jrxml/待采购订单明细.jrxml", saleorderGoodsExportList, "待采购订单列表.xls");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = URLEncoder.encode("待采购订单明细", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), SaleorderGoodsExportVo.class).sheet("待采购订单明细").doWrite(saleorderGoodsExportList);

    }

    /**
     * <b>Description:</b><br>
     * 查询待采购订单列表不分页
     *
     * @param goodsVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月20日 下午4:48:42
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "indexPendingPurchase")
    public ModelAndView indexPendingPurchase(GoodsVo goodsVo, HttpServletRequest request,
                                             @RequestParam(defaultValue = "0") Integer tabFlag,
                                             @RequestParam(required = false) String urlSaleorderNo,
                                             @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                             @RequestParam(required = false) Integer pageSize) {
        Page page = getPageTag(request, pageNo, pageSize);
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView("order/buyorder/index_pendingPurchase");
        List<Organization> productOrgList = orgService.getOrgListByPositTypes(user.getCompanyId());
        mav.addObject("productOrgList", productOrgList);
        mav.addObject("tabFlag", tabFlag);
        mav.addObject("urlSaleorderNo", urlSaleorderNo);
        if (StringUtil.isNotEmpty(urlSaleorderNo)) {
            goodsVo.setSaleorderNo(urlSaleorderNo);
            goodsVo.setUrlSaleorderNo(urlSaleorderNo);
        }
        // 产品负责人
        List<User> productUserList = userService.getUserListByPositType(SysOptionConstant.ID_311, user.getCompanyId());
        //所有的分配人
        List<User> assUser = userService.selectAllAssignUser();
        //查询订单助理
        List<User> orderAssUserList = buyorderService.getOrderAssitantInfoByIdSelective(null);
        Integer orderAssId = goodsVo.getOrderAssId();
        if (goodsVo.getOrderAssId() == null && CollectionUtils.isNotEmpty(orderAssUserList)) {
            User user1 = orderAssUserList.stream().filter(e -> e.getUserId().equals(user.getUserId())).findAny().orElse(null);
            if (user1 != null) {
                goodsVo.setOrderAssId(user1.getUserId());
            }
        }
        mav.addObject("orderAssUserList", orderAssUserList);

        if (ObjectUtils.notEmpty(goodsVo.getProOrgtId())) {
            assUser = userService.getUserListByOrgIddcg(goodsVo.getProOrgtId());
        }
        mav.addObject("productUserList", assUser);

        // 销售类型
        List<User> salesUserList = userService.getUserListByPositType(SysOptionConstant.ID_310, user.getCompanyId());
        // 申请人（包括产品和销售）
        List<User> applicantList = new ArrayList<>();
        applicantList.addAll(productUserList);
        applicantList.addAll(salesUserList);
        mav.addObject("applicantList", applicantList);

        goodsVo.setCompanyId(user.getCompanyId());
        goodsVo.setSearchUserId(user.getUserId());
        //2019-12-20
        //判断归属和部门
        if (goodsVo != null && ObjectUtils.notEmpty(goodsVo.getProUserId())
                && ObjectUtils.isEmpty(goodsVo.getProOrgtId())) {
            List<Integer> saleOrderGoodsIdListByUserId = saleorderService.getSaleOrderGoodsIdListByUserId(goodsVo.getProUserId());
            if (saleOrderGoodsIdListByUserId == null || saleOrderGoodsIdListByUserId.size() == 0) {
                saleOrderGoodsIdListByUserId = new ArrayList<>();
                saleOrderGoodsIdListByUserId.add(-1);
            }
            goodsVo.setSaleOrderGoodsIdList(saleOrderGoodsIdListByUserId);

        } else if (goodsVo != null && ObjectUtils.notEmpty(goodsVo.getProOrgtId())
                && ObjectUtils.isEmpty(goodsVo.getProUserId())) {
            List<User> userList = userService.getUserListByOrgId(goodsVo.getProOrgtId());
            List<Integer> saleOrderGoodsIdListByUserIds = saleorderService.getSaleOrderGoodsIdListByUserIds(userList);
            if (saleOrderGoodsIdListByUserIds == null || saleOrderGoodsIdListByUserIds.size() == 0) {
                saleOrderGoodsIdListByUserIds = new ArrayList<>();
                saleOrderGoodsIdListByUserIds.add(-1);
            }
            goodsVo.setSaleOrderGoodsIdList(saleOrderGoodsIdListByUserIds);

        } else if (goodsVo != null && ObjectUtils.notEmpty(goodsVo.getProUserId())
                && ObjectUtils.notEmpty(goodsVo.getProOrgtId())) {
            List<Integer> saleOrderGoodsIdListByUserId = saleorderService.getSaleOrderGoodsIdListByUserId(goodsVo.getProUserId());
            //	List<User> userList = userService.getUserListByOrgId(goodsVo.getProOrgtId());
            //判断部门有没有这个归属人,没有的话查不到

            if (saleOrderGoodsIdListByUserId == null || saleOrderGoodsIdListByUserId.size() == 0) {
                saleOrderGoodsIdListByUserId = new ArrayList<>();
                saleOrderGoodsIdListByUserId.add(-1);
            }

            goodsVo.setSaleOrderGoodsIdList(saleOrderGoodsIdListByUserId);

        }

        //可采购时间（日期组件）、时效状态（包含全部、正常、临期、逾期），预警等级（全部、三级、二级、一级）
        //可采购起始时间
        if (StringUtils.isNotBlank(goodsVo.getBuyStartTimeStr())) {
            long startTime = DateUtil.convertLong(goodsVo.getBuyStartTimeStr(), DateUtil.DATE_FORMAT);
            goodsVo.setBuyStartTime(startTime);
        }

        if (StringUtils.isNotBlank(goodsVo.getBuyEndTimeStr())) {
            long endTime = DateUtil.convertLong(goodsVo.getBuyEndTimeStr(), DateUtil.DATE_FORMAT);
            goodsVo.setBuyEndTime(endTime);
        }

        if (ObjectUtils.isEmpty(goodsVo.getOrderType())) {
            goodsVo.setOrderType(1);
        }
        //立即采购，暂缓采购，无需采购分别对应组件表中的ID记录为2，3，14
        //界面中三个立即采购，暂缓采购，无需采购的sheet的tabFlag分别为0，1，2
        if (Integer.valueOf(0).equals(tabFlag)) {
            goodsVo.setComponentId(2);
        } else if (Integer.valueOf(1).equals(tabFlag)) {
            goodsVo.setComponentId(3);
        } else {
            goodsVo.setComponentId(14);
        }
        if (goodsVo.getOrderAssId() != null && goodsVo.getOrderAssId() != -1) {
            List<OrderAssistantRelationDo> bingdedInfoByOrderAssIdList = new ArrayList<>();
            bingdedInfoByOrderAssIdList = orderAssistantRelationMapper.getBingdedInfoByOrderAssId(goodsVo.getOrderAssId());
            if (CollectionUtils.isEmpty(bingdedInfoByOrderAssIdList)) {
                mav.addObject("goodsVo", goodsVo);
                return mav;
            }
            List<ProductManagerAndAssistantIdVo> productManagerAndAssistantIdVoList = new ArrayList<>();
            for (OrderAssistantRelationDo orderAssistantRelationDo : bingdedInfoByOrderAssIdList) {
                ProductManagerAndAssistantIdVo productManagerAndAssistantIdVo = new ProductManagerAndAssistantIdVo();
                productManagerAndAssistantIdVo.setProductManagerUserId(orderAssistantRelationDo.getProductManagerUserId());
                productManagerAndAssistantIdVo.setProductAssistantUserId(orderAssistantRelationDo.getProductAssitantUserId());
                productManagerAndAssistantIdVoList.add(productManagerAndAssistantIdVo);
            }
            goodsVo.setProductManagerAndAssistantIdVoList(productManagerAndAssistantIdVoList);
        }


        Map<String, Object> map = buyorderService.getSaleorderGoodsVoListPage(goodsVo, page);

        if (map != null) {
            List<SaleorderVo> list = (List<SaleorderVo>) map.get("list");
            List<Integer> saleorderIds = new ArrayList<>();
            //根据订单号查询归属人
            for (SaleorderVo saleorderVo : list) {
                List<SaleorderGoodsVo> sgvList = saleorderVo.getSgvList();
                if (sgvList != null) {
                    Spu spu = null;
                    for (SaleorderGoodsVo sgv : sgvList) {
                        spu = saleorderService.getSpu(sgv.getSku());
                        if (spu == null) {
                            sgv.setAssignmentManagerId(null);
                            sgv.setAssignmentAssistantId(null);
                        } else {
                            sgv.setAssignmentManagerId(spu.getAssignmentManagerId());
                            sgv.setAssignmentAssistantId(spu.getAssignmentAssistantId());
                        }
                        saleorderIds.add(sgv.getSaleorderGoodsId());
                    }
                }
            }
            //计算虚拟商品需采购数量并排除需采为0,并重新计算待采数量
            Integer buySum = (Integer) map.get("buySum");
            List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtoList = buyorderExpenseApiService.waitBuyExpenseNeed(saleorderIds);
            for (int i = list.size() - 1; i >= 0; i--) {
                List<SaleorderGoodsVo> sgvList = list.get(i).getSgvList();
                Integer proBuySum = list.get(i).getProBuySum();
                if (sgvList.size()>0){
                    for (int j = sgvList.size() - 1; j >= 0; j--) {
                        SaleorderGoodsVo sgv = sgvList.get(j);
                        if(sgv.getIsVirture()==2){
                            for (BuyOrderSaleOrderGoodsDetailDto goodsDetailDto : buyOrderSaleOrderGoodsDetailDtoList) {
                                if (sgv.getSaleorderGoodsId().equals(goodsDetailDto.getSaleorderGoodsId())){
                                    buySum -= sgv.getNum();
                                    proBuySum -= sgv.getNum();
                                    if (goodsDetailDto.getNum() > 0){
                                        buySum += goodsDetailDto.getNum();
                                        sgv.setNeedBuyNum(goodsDetailDto.getNum());
                                        proBuySum += goodsDetailDto.getNum();
                                    }else {
                                        sgvList.remove(j);
                                    }
                                    break;
                                }
                            }
                        }
                    }
                    if (sgvList.size() == 0){
                        list.remove(i);
                    }else {
                        list.get(i).setProBuySum(proBuySum);
                    }
                }
            }

            //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
            if (!CollectionUtils.isEmpty(list)) {

                List<Integer> skuIds = new ArrayList<>();

                list.forEach(saleOrder -> {
                    boolean flag = saleOrderApiService.periodOrderCanDeliver(saleOrder.getSaleorderId());
                    if (flag) {
                        saleOrder.setSatisfyDeliveryTime(null);
                    }
                    if (!CollectionUtils.isEmpty(saleOrder.getSgvList())) {
                        saleOrder.getSgvList().forEach(saleOrderGoods -> {
                            skuIds.add(saleOrderGoods.getGoodsId());
                            // 账期订单锁定
                            if(flag){
                                saleOrderGoods.setLockedStatus(2);
                                saleOrderGoods.setAging(null);
                            }
                        });
                    }
                });

                List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);

                Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream()
                        .collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));

                mav.addObject("newSkuInfosMap", newSkuInfosMap);
            }
            //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end


            mav.addObject("list", list);
            mav.addObject("buySum", buySum);
            mav.addObject("page", (Page) map.get("page"));
        } else {
            mav.addObject("buySum", 0);
        }
        mav.addObject("goodsVo", goodsVo);
        return mav;
    }

    /**
     * <b>Description:</b><br>
     *
     * @param request
     * @param orgId
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年12月9日 下午1:09:39
     */
    @ResponseBody
    @RequestMapping(value = "getProductorUserList")
    public ResultInfo<?> getProductorUserList(HttpServletRequest request, Integer orgId) {
        if (ObjectUtils.isEmpty(orgId)) {
            return new ResultInfo<>(-1, "查询失败！");
        }
        List<User> userList = userService.getUserListByOrgIdcg(orgId);
        return new ResultInfo<>(0, "查询成功！", userList);
    }


    @ResponseBody
    @RequestMapping(value = "getProductorUserListCount")
    public ResultInfo<?> getProductorUserListCount(HttpServletRequest request) {
		/*if (ObjectUtils.isEmpty(orgId)) {
			return new ResultInfo<>(-1, "查询失败！");
		}*/
        List<User> userList = userService.selectAllAssignUser();
        return new ResultInfo<>(0, "查询成功！", userList);
    }

    /**
     * <b>Description:</b><br>
     * 忽略待采购订单
     *
     * @param saleorderGoodsIDs
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月21日 下午5:09:26
     */
    @SuppressWarnings("rawtypes")
    @ResponseBody
    @RequestMapping(value = "saveIgnore")
    @SystemControllerLog(type = 1, operationType = "eidt", desc = "忽略待采购订单")
    public ResultInfo saveIgnore(String saleorderGoodsIDs, HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ResultInfo res = buyorderService.saveIgnore(saleorderGoodsIDs, user);
        return res;
    }

    /**
     * <b>Description:</b><br>
     * 保存新增采购订单
     *
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月25日 下午5:24:24
     */
    @ResponseBody
    @RequestMapping(value = "/saveAddBuyorder")
    @SystemControllerLog(operationType = "add", desc = "保存新增采购订单")
    public ModelAndView saveAddBuyorder(HttpServletRequest request, BuyorderVo buyorderVo) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ResultInfo<?> res = buyorderService.saveOrUpdateBuyorderVo(buyorderVo, user);
        if (res != null && res.getCode() == 0 && Integer.valueOf(res.getData().toString()) > 0) {
            ModelAndView mav = new ModelAndView("redirect:/order/buyorder/addBuyorderPage.do");
            mav.addObject("buyorderId", Integer.valueOf(res.getData().toString()));
            // RECORD.V01 VDERP-5951 采销节点信息互通】采购单支持填写预计发货时间和到货时间并通知销售/order/buyorder/saveAddBuyorder.d
            // CAUSE.V01 --|由于VP采购单第一次生成是通过初始化采购单(采购单单号,修改时间等) 然后修改采购单对应供应商
            // 			   |所以这边给定参数 【firstSendReceiveFlag】 1-首次 0-非首次
            //			   |后续判断参数为1说明首次修改(对应本文件下RECORD.V02)保存
            //			   |则保存采购单对应FIRST_SEND_GOODS_TIME && FIRST_RECEIVE_GOODS_TIME
            mav.addObject("firstSendReceiveFlag", Integer.valueOf(1));
            return mav;
        } else {
            ModelAndView mav = new ModelAndView("common/fail");
            if (res != null && (res.getCode() == -2 || res.getCode() == -3)) {
                mav.addObject("message", res.getMessage());
            }
            return mav;
        }
    }

    /**
     * <b>Description:</b><br>
     * 新增采购订单 --普发或直发页面
     *
     * @param request
     * @param firstSendReceiveFlag 是否是首次修改编辑采购单信息
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月25日 下午5:24:24
     */
    @SuppressWarnings("all")
    @ResponseBody
    @RequestMapping(value = "/addBuyorderPage")
    public ModelAndView addBuyorderPage(HttpServletRequest request, Buyorder buyorder, String uri, Integer firstSendReceiveFlag) throws IOException {
        ModelAndView mav = new ModelAndView();
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        BuyorderVo bv = buyorderService.getAddBuyorderVoDetail(buyorder, user);
        mav.addObject("buyorderVo", bv);
        if (bv != null && ObjectUtils.notEmpty(bv.getTraderId())) {
            TraderContactVo traderContactVo = new TraderContactVo();
            traderContactVo.setTraderId(bv.getTraderId());
            traderContactVo.setTraderType(ErpConst.TWO);
            Map<String, Object> map = traderCustomerService.getTraderContactVoList(traderContactVo);
            String tastr = (String) map.get("contact");
            net.sf.json.JSONArray json = net.sf.json.JSONArray.fromObject(tastr);
            List<TraderContactVo> list = (List<TraderContactVo>) json.toCollection(json, TraderContactVo.class);
            List<TraderAddressVo> taList = (List<TraderAddressVo>) map.get("address");
            mav.addObject("contactList", list);
            mav.addObject("tarderAddressList", taList);
        }
        // 普发收货地址
        if (bv != null && bv.getDeliveryDirect() == 0) {
            ParamsConfigValue paramsConfigValue = new ParamsConfigValue();
            paramsConfigValue.setCompanyId(user.getCompanyId());
            paramsConfigValue.setParamsConfigId(ErpConst.TWO);

            // 2018-2-4 查询全部收货地址
            mav.addObject("addressList", buyorderService.getAddressVoList(paramsConfigValue));
        }
        // 付款方式
        List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
        mav.addObject("paymentTermList", paymentTermList);

        // 收票种类
        List<SysOptionDefinition> receiptTypes = getSysOptionDefinitionList(SysOptionConstant.ID_428);
        mav.addObject("receiptTypes", receiptTypes);

        // 运费说明
        List<SysOptionDefinition> freightDescriptions = getSysOptionDefinitionList(SysOptionConstant.ID_469);
        mav.addObject("freightDescriptions", freightDescriptions);

        // 物流公司
        List<Logistics> logisticsList = getLogisticsList(user.getCompanyId());
        mav.addObject("logisticsList", logisticsList);
        if (uri == null || "".equals(uri)) {
            uri = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + request.getContextPath() + "/order/buyorder/addBuyorderPage.do";
        }
        mav.addObject("uri", uri);


        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())) {
            List<Integer> skuIds = new ArrayList<>();
            bv.getBuyorderGoodsVoList().stream().forEach(buyOrder -> {
                skuIds.add(buyOrder.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mav.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        mav.setViewName("order/buyorder/add_buyorder_pf");
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(bv)));

        // RECORD.V01 VDERP-5951 采销节点信息互通】采购单支持填写预计发货时间和到货时间并通知销售
        // CAUSE.V01 参数firstSendReceiveFlag来源于待采购订单页面跳转附加，用于判断是否是首次编辑采购单
        if (firstSendReceiveFlag != null && Integer.valueOf(1).equals(firstSendReceiveFlag)) {
            mav.addObject("firstSendReceiveFlag", Integer.valueOf(1));
        }

        // needBuyNum为空则跳转错误页面
        if (isBuyorderVoHaveNeedBuyNumEqualsZero(bv)) {
            mav.setViewName("common/fail");
            mav.addObject("message", "已选列表存在采购单，请刷新待采购列表重试");
        }
        return mav;
    }

    /**
     * @return boolean
     * @Description 判断BuyorderVo中有没有需采购数量为0（即为不需要采购）的待采购项
     * <AUTHOR>
     * @Date 16:21 2021/7/30
     * @Param [bv]
     **/
    private boolean isBuyorderVoHaveNeedBuyNumEqualsZero(BuyorderVo bv) {
        if (CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())) {
            return true;
        }
        for (BuyorderGoodsVo buyorderGoodsVo : bv.getBuyorderGoodsVoList()) {
            if (CollectionUtils.isEmpty(buyorderGoodsVo.getSaleorderGoodsVoList())) {
                return true;
            } else {
                return buyorderGoodsVo.getSaleorderGoodsVoList().stream().anyMatch(
                        saleorderGoodsVo -> isSaleorderGoodsVoHaveNeedBuyNumEqualsZero(saleorderGoodsVo, bv)
                );
            }
        }
        return true;
    }

    /**
     * @return boolean
     * @Description 判断SaleorderGoods中有没有需采购数量为0（即为不需要采购）的待采购项
     * <AUTHOR>
     * @Date 16:21 2021/7/30
     * @Param [saleorderGoodsVo, bv]
     **/
    private boolean isSaleorderGoodsVoHaveNeedBuyNumEqualsZero(SaleorderGoodsVo saleorderGoodsVo, BuyorderVo bv) {
        if (ErpConst.ZERO.equals(bv.getDeliveryDirect())) {//普发销售单NeedBuyNum = NeedBuyNum
            return saleorderGoodsVo.getNeedBuyNum() == null || ErpConst.ZERO.equals(saleorderGoodsVo.getNeedBuyNum());
        } else if (ErpConst.ONE.equals(bv.getDeliveryDirect())) {//直发销售单NeedBuyNum = Num-BuyNum
            return saleorderGoodsVo.geteNum() == null || saleorderGoodsVo.getBuyNum() == null ||
                    ErpConst.ZERO.equals(new Integer(saleorderGoodsVo.getNum().intValue() - saleorderGoodsVo.getBuyNum().intValue()));
        }
        return true;
    }

    /**
     * <b>Description:</b><br>
     * 编辑采购订单 --普发或直发页面
     *
     * @param request
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月25日 下午5:24:24
     */
    @SuppressWarnings("all")
    @ResponseBody
    @RequestMapping(value = "/editBuyorderPage")
    public ModelAndView editBuyorderPage(HttpServletRequest request, Buyorder buyorder, String uri) throws IOException {
        ModelAndView mav = new ModelAndView();
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        BuyorderVo bv = buyorderService.getBuyorderVoDetail(buyorder, user);
        mav.addObject("buyorderVo", bv);
        if (bv.getTraderId() != null && bv.getTraderId() != 0) {
            TraderContactVo traderContactVo = new TraderContactVo();
            traderContactVo.setTraderId(bv.getTraderId());
            traderContactVo.setTraderType(ErpConst.TWO);
            Map<String, Object> map = traderCustomerService.getTraderContactVoList(traderContactVo);
            String tastr = (String) map.get("contact");
            net.sf.json.JSONArray json = net.sf.json.JSONArray.fromObject(tastr);
            List<TraderContactVo> list = (List<TraderContactVo>) json.toCollection(json, TraderContactVo.class);
            List<TraderAddressVo> taList = (List<TraderAddressVo>) map.get("address");
            mav.addObject("contactList", list);
            mav.addObject("tarderAddressList", taList);
        }
        // 普发收货地址
        if (bv.getDeliveryDirect() == 0) {
            ParamsConfigValue paramsConfigValue = new ParamsConfigValue();
            paramsConfigValue.setCompanyId(user.getCompanyId());
            paramsConfigValue.setParamsConfigId(ErpConst.TWO);

            // 2018-2-4 查询全部收货地址
            mav.addObject("addressList", buyorderService.getAddressVoList(paramsConfigValue));
        }
        // 付款方式
        List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
        mav.addObject("paymentTermList", paymentTermList);

        // 收票种类
        List<SysOptionDefinition> receiptTypes = getSysOptionDefinitionList(SysOptionConstant.ID_428);
        mav.addObject("receiptTypes", receiptTypes);

        // 运费说明
        List<SysOptionDefinition> freightDescriptions = getSysOptionDefinitionList(SysOptionConstant.ID_469);
        mav.addObject("freightDescriptions", freightDescriptions);

        // 物流公司
        List<Logistics> logisticsList = getLogisticsList(user.getCompanyId());
        mav.addObject("logisticsList", logisticsList);
        if (uri == null || "".equals(uri)) {
            uri = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + request.getContextPath() + "/order/buyorder/addBuyorderPage.do";
        }
        mav.addObject("uri", uri);
        mav.setViewName("order/buyorder/add_buyorder_pf");
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(bv)));
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 详情页修改采购订单
     *
     * @param request
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月25日 下午5:24:24
     */
    @SuppressWarnings("all")
    @ResponseBody
    @RequestMapping(value = "/editAddBuyorderPage")
    public ModelAndView editAddBuyorderPage(HttpServletRequest request, Buyorder buyorder, String uri)
            throws IOException {
        ModelAndView mav = new ModelAndView();
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        BuyorderVo bv = buyorderService.getBuyorderVoDetail(buyorder, user);
        riskCheckService.setBuyorderGoodsIsRiskInfo(bv, bv.getBuyorderGoodsVoList());
        mav.addObject("buyorderVo", bv);

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())) {
            List<Integer> skuIds = new ArrayList<>();
            bv.getBuyorderGoodsVoList().stream().forEach(buyOrderGood -> {
                skuIds.add(buyOrderGood.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mav.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        //判断商品是否已经核价
        if (!CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())) {

            for (BuyorderGoodsVo buyorderGoodsVo : bv.getBuyorderGoodsVoList()) {

                SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = basePriceService
                        .findSkuPriceInfoBySkuNo(buyorderGoodsVo.getSku());

                if (skuPriceInfoDetailResponseDto == null || CollectionUtils.isEmpty(skuPriceInfoDetailResponseDto.getPurchaseList())) {
                    buyorderGoodsVo.setAlreadyPrice(0);
                    continue;
                }

                int alreadyPrice = 0;
                for (SkuPriceInfoPurchaseDto purchaseInfo : skuPriceInfoDetailResponseDto.getPurchaseList()) {
                    if (purchaseInfo.getTraderId().intValue() == bv.getTraderId()) {
                        alreadyPrice = 1;
                        buyorderGoodsVo.setCostPirce(purchaseInfo.getPurchasePrice());
                        break;
                    }
                }
                buyorderGoodsVo.setAlreadyPrice(alreadyPrice);

            }
        }

        if (bv.getTraderId() != null && bv.getTraderId() != 0) {
            TraderContactVo traderContactVo = new TraderContactVo();
            traderContactVo.setTraderId(bv.getTraderId());
            traderContactVo.setTraderType(ErpConst.TWO);
            Map<String, Object> map = traderCustomerService.getTraderContactVoList(traderContactVo);
            String tastr = (String) map.get("contact");
            net.sf.json.JSONArray json = net.sf.json.JSONArray.fromObject(tastr);
            List<TraderContactVo> list = (List<TraderContactVo>) json.toCollection(json, TraderContactVo.class);
            List<TraderAddressVo> taList = (List<TraderAddressVo>) map.get("address");
            mav.addObject("contactList", list);
            mav.addObject("tarderAddressList", taList);
        }
        // 普发收货地址
        if (bv.getDeliveryDirect() == 0) {
            ParamsConfigValue paramsConfigValue = new ParamsConfigValue();
            paramsConfigValue.setCompanyId(user.getCompanyId());
            paramsConfigValue.setParamsConfigId(ErpConst.TWO);

            // 2018-2-4 查询全部收货地址
            mav.addObject("addressList", buyorderService.getAddressVoList(paramsConfigValue));
        }
        // 付款方式
        List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
        mav.addObject("paymentTermList", paymentTermList);

        // 收票种类
        List<SysOptionDefinition> receiptTypes = getSysOptionDefinitionList(SysOptionConstant.ID_428);
        mav.addObject("receiptTypes", receiptTypes);

        // 运费说明
        List<SysOptionDefinition> freightDescriptions = getSysOptionDefinitionList(SysOptionConstant.ID_469);
        mav.addObject("freightDescriptions", freightDescriptions);

        // 物流公司
        List<Logistics> logisticsList = getLogisticsList(user.getCompanyId());
        mav.addObject("logisticsList", logisticsList);
        if (uri == null || "".equals(uri)) {
            uri = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + request.getContextPath() + "/order/buyorder/addBuyorderPage.do";
        }
        mav.addObject("uri", uri);
        mav.setViewName("order/buyorder/edit_buyorder_pf");
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(bv)));
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 新增采购页面提交
     *
     * @param request
     * @param buyorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月3日 上午10:26:31
     */
    @ResponseBody
    @RequestMapping(value = "saveEditBuyorder")
    @SystemControllerLog(operationType = "edit", desc = "新增采购页面提交")
    public ModelAndView saveEditBuyorder(HttpServletRequest request, Buyorder buyorder, String beforeParams, HttpSession session) {
        // RECORD.V02 保存采购单修改信息 sendGoodsTimeStr&receiveGoodsTimeStr
        log.info("/order/buyorder/saveEditBuyorder --> " + buyorder.toString());
        User currentUser = (User) session.getAttribute(ErpConst.CURR_USER);
        buyorder.setUpdater(currentUser.getUserId());
        String[] receiveGoodsTimeStrings = request.getParameterValues("receiveGoodsTimeStr");
        //打印receiveGoodsTimeStrings
        logger.info("receiveGoodsTimeStrings:{}",receiveGoodsTimeStrings);
        ResultInfo<?> res = buyorderService.saveEditBuyorderAndBuyorderGoods(buyorder, request);
        if (res != null && res.getCode() == 0) {
            ModelAndView mav = new ModelAndView("redirect:/order/buyorder/viewBuyorder.do");
            mav.addObject("buyorderId", buyorder.getBuyorderId());
            return mav;
        } else {
            ModelAndView mav = new ModelAndView("common/fail");
            mav.addObject("message", res == null ? "" : res.getMessage());
            return mav;
        }
    }

    /**
     * <b>Description:</b><br>
     * 搜索供应商信息页面
     *
     * @param request
     * @param traderSupplierVo
     * @return
     * @throws UnsupportedEncodingException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月25日 下午5:10:20
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "/getSupplierByName")
    public ModelAndView getSupplierByName(HttpServletRequest request, TraderSupplierVo traderSupplierVo,
                                          String supplierName, @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false, defaultValue = "10") Integer pageSize, Integer lendOut, String searchTraderName,
                                          @RequestParam(value = "callbackFuntion", required = false) String callbackFuntion,@RequestParam(value = "isGift",required = false)String isGift) throws UnsupportedEncodingException {
        ModelAndView mav = null;
        if (lendOut == null) {
            mav = new ModelAndView("order/buyorder/search_supplier");
            supplierName = URLDecoder.decode(URLDecoder.decode(supplierName, "UTF-8"), "UTF-8");
            mav.addObject("supplierName", supplierName);
        } else {
            //外借出库搜索
            supplierName = URLDecoder.decode(URLDecoder.decode(searchTraderName, "UTF-8"), "UTF-8");
            mav = new ModelAndView("trader/customer/search_customer_list");
        }
        mav.addObject("callbackFuntion", callbackFuntion);
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Page page = getPageTag(request, pageNo, 10);
        traderSupplierVo.setCompanyId(user.getCompanyId());
        traderSupplierVo.setTraderSupplierName(supplierName);
        traderSupplierVo.setIsEnable(ErpConst.ONE);
        traderSupplierVo.setRequestType("cg");// 采购搜索供应商用，其他地方可以不用
        // 查询所有职位类型为311的员工
        List<Integer> positionType = new ArrayList<>();
        positionType.add(SysOptionConstant.ID_311);// 采购
        List<User> userList = userService.getMyUserList(user, positionType, false);
        Map<String, Object> map = this.traderSupplierService.getSupplierByName(traderSupplierVo, page, userList);
        List<TraderSupplierVo> list = null;
        if (map != null) {
            list = (List<TraderSupplierVo>) map.get("list");
            page = (Page) map.get("page");
            if (lendOut != null && lendOut == 1) {
                mav.addObject("lendOut", 1);
                List<TraderVo> traderList = new ArrayList<TraderVo>();
                for (TraderSupplierVo t : list) {
                    TraderVo trader = new TraderVo();
                    trader.setTraderId(t.getTraderId());
                    trader.setTraderName(t.getTraderSupplierName());
                    trader.setOwner(t.getPersonal());
                    trader.setAddress(t.getTraderSupplierAddress());
                    trader.setAddTime(t.getAddTime());
                    trader.setTraderType(2);
                    traderList.add(trader);
                }
                mav.addObject("traderList", traderList);
                TraderCustomerVo traderCustomerVo = new TraderCustomerVo();
                traderCustomerVo.setSearchTraderName(supplierName);
                mav.addObject("traderCustomerVo", traderCustomerVo);
                mav.addObject("block", 2);//外借单选项
            }
            list.forEach(l -> l.setCertificateOverdue(traderSupplierService.traderCertificateOverdue(l.getTraderId())));
        }
        list.forEach( t -> {
            SupplierAssetApiDto supplierAsset = supplierAssetApiService.getSupplierAsset(t.getTraderSupplierId(), SupplierAssetEnum.rebate.getCode());
            t.setValidRebateCharge(null == supplierAsset ? BigDecimal.ZERO : supplierAsset.getApplyAsset());
        });
        mav.addObject("list", list);
        mav.addObject("page", page);
        mav.addObject("isGift",isGift);
        return mav;
    }

    /**
     * 搜索实际供应商信息页面
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getSupplierByNameAct")
    public ModelAndView getSupplierByNameAct(HttpServletRequest request,
                                             TraderSupplierVo traderSupplierVo,
                                             String supplierName,
                                             @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                             @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                             @RequestParam(value = "callbackFuntion", required = false) String callbackFuntion,
                                             @RequestParam(value = "isGift", required = false) String isGift) throws UnsupportedEncodingException {
        ModelAndView mav = new ModelAndView("order/buyorder/search_supplier_act");
        supplierName = URLDecoder.decode(URLDecoder.decode(supplierName, "UTF-8"), "UTF-8");
        mav.addObject("supplierName", supplierName);
        mav.addObject("callbackFuntion", callbackFuntion);
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Page page = getPageTag(request, pageNo, 10);
        traderSupplierVo.setCompanyId(user.getCompanyId());
        traderSupplierVo.setTraderSupplierName(supplierName);
        traderSupplierVo.setIsEnable(ErpConst.ONE);
        traderSupplierVo.setRequestType("cg");// 采购搜索供应商用，其他地方可以不用
        // 查询所有职位类型为311的员工
        List<Integer> positionType = new ArrayList<>();
        positionType.add(SysOptionConstant.ID_311);// 采购
        List<User> userList = userService.getMyUserList(user, positionType, false);
        Map<String, Object> map = this.traderSupplierService.getSupplierByName(traderSupplierVo, page, userList);
        List<TraderSupplierVo> list = null;
        if (map != null) {
            list = (List<TraderSupplierVo>) map.get("list");
            page = (Page) map.get("page");
            list.forEach(l -> l.setCertificateOverdue(traderSupplierService.traderCertificateOverdue(l.getTraderId())));
        }
        list.forEach(t -> {
            SupplierAssetApiDto supplierAsset = supplierAssetApiService.getSupplierAsset(t.getTraderSupplierId(), SupplierAssetEnum.rebate.getCode());
            t.setValidRebateCharge(null == supplierAsset ? BigDecimal.ZERO : supplierAsset.getApplyAsset());
        });
        mav.addObject("list", list);
        mav.addObject("page", page);
        mav.addObject("isGift", isGift);
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 获取当前供应商的联系人和地址
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 上午9:08:41
     */
    @SuppressWarnings({"unchecked", "rawtypes", "static-access"})
    @ResponseBody
    @RequestMapping(value = "/getContactsAddress")
    public ResultInfo getContactsAddress(HttpServletRequest request, Integer traderId, Integer traderType) {
        TraderContactVo traderContactVo = new TraderContactVo();
        traderContactVo.setTraderId(traderId);
        traderContactVo.setTraderType(traderType);
        Map<String, Object> map = traderCustomerService.getTraderContactVoList(traderContactVo);
        String tastr = (String) map.get("contact");
        net.sf.json.JSONArray json = net.sf.json.JSONArray.fromObject(tastr);
        List<TraderContactVo> list = (List<TraderContactVo>) json.toCollection(json, TraderContactVo.class);
        List<TraderAddressVo> taList = (List<TraderAddressVo>) map.get("address");
        ResultInfo res = new ResultInfo<>(0, "查询成功");
        res.setData(list);
        res.setListData(taList);
        return res;
    }

    /**
     * <b>Description:</b><br>
     * 获取sku对应的价格信息
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 上午9:08:41
     */
    @ResponseBody
    @RequestMapping(value = "/getSkuPriceInfoBySkuNo")
    public ResultInfo getSkuPriceInfoBySkuNo(HttpServletRequest request, @Param(value = "skuNo") String skuNo) {


        SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = basePriceService.findSkuPriceInfoBySkuNo(skuNo);
        if (skuPriceInfoDetailResponseDto == null) {
            return new ResultInfo(-1, "未核价", skuNo);
        }

        return new ResultInfo<>(0, "查询成功", skuPriceInfoDetailResponseDto);
    }

    @ResponseBody
    @RequestMapping(value = "/clearPriceInfo")
    public ResultInfo clearPriceInfo(HttpServletRequest request, @Param(value = "buyorderGoodId") Integer buyorderGoodId) {

        buyorderService.clearPriceInfo(buyorderGoodId);

        return new ResultInfo<>(0, "查询成功", null);
    }

    /**
     * <b>Description:</b><br>
     * 加入采购订单页面
     *
     * @param goodsVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月8日 上午10:39:12
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "addSaleorderToBuyorderPage")
    public ModelAndView addSaleorderToBuyorderPage(BuyorderVo buyorderVo, HttpServletRequest request) {
        ModelAndView mav = new ModelAndView("order/buyorder/add_saleorderToBuyorder");
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        buyorderVo.setCompanyId(user.getCompanyId());
        Map<String, Object> map = buyorderService.getGoodsVoList(buyorderVo);
        if (map.containsKey("-2")) {
            mav = new ModelAndView("common/fail");
            mav.addObject("message", "提交订单中含有已锁定销售商品，请确认");
        }
        if (map.containsKey("gvList")) {
            List<GoodsVo> gvList = (List<GoodsVo>) map.get("gvList");
            mav.addObject("gvList", gvList);


            //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
//			if(!CollectionUtils.isEmpty(gvList)){
//				List<Integer> skuIds = new ArrayList<>();
//				gvList.stream().forEach(saleGood -> {
//					skuIds.add(saleGood.getGoodsId());
//				});
//				List<Map<String,Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
//				Map<String,Map<String,Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key->key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
//				mav.addObject("newSkuInfosMap", newSkuInfosMap);
//			}
            //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        }
        if (map.containsKey("sum")) {
            Integer sum = (Integer) map.get("sum");
            mav.addObject("sum", sum);
        }
        if (map.containsKey("isHaveVirtureSku")) {
            Integer isHaveVirtureSku = (Integer) map.get("isHaveVirtureSku");
            mav.addObject("isHaveVirtureSku", isHaveVirtureSku);
        }
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 查询采购订单分页信息
     *
     * @param request
     * @param buyorderVo
     * @param pageNo
     * @param pageSize
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月11日 上午9:46:49
     */
    @ResponseBody
    @RequestMapping(value = "getBuyorderList")
    public ModelAndView indexBuy(HttpServletRequest request, BuyorderVo buyorderVo, TraderSupplier traderSupplier,
                                 @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                 @RequestParam(required = false) Integer pageSize) {
        ModelAndView mav = new ModelAndView("order/buyorder/index");
        Page page = getPageTag(request, pageNo, pageSize);
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        mav.addObject("curr_user", user);

        buyorderVo.setCompanyId(user.getCompanyId());

        // 产品部门--选择条件
        List<Organization> productOrgList = orgService.getOrgListByPositType(SysOptionConstant.ID_311,
                user.getCompanyId());
        mav.addObject("productOrgList", productOrgList);

        //所有的分配人
        List<User> assUser = userService.selectAllAssignUser();
//		List<User> userList = userService.getUserByPositType(SysOptionConstant.ID_311, user.getCompanyId());
        mav.addObject("productUserList", assUser);

        List<Integer> userIds;
        userIds = getUserIdList(buyorderVo.getProOrgtId(), request);

        if (buyorderVo.getProUserId() != null && buyorderVo.getProUserId() != 0) {
            userIds = null;
        }

        if (buyorderVo.getProOrgtId() != null && buyorderVo.getProOrgtId() != 0) {
            buyorderVo.setOrgId(buyorderVo.getProOrgtId());
        }
        if (userIds != null) {
            if (userIds.size() > 0) {
                buyorderVo.setUserIds(userIds);
            }
        }

        String start = request.getParameter("searchBegintimeStr");
        String end = request.getParameter("searchEndtimeStr");
        if (start != null && !"".equals(start)) {
            buyorderVo.setSearchBegintime(DateUtil.convertLong(start, DateUtil.DATE_FORMAT));
        }
        if (end != null && !"".equals(end)) {
            buyorderVo.setSearchEndtime(DateUtil.convertLong(end + " 23:59:59", DateUtil.TIME_FORMAT));
        }
        //6 代表供应链管理部
        List<Integer> orgList = new ArrayList<>(userService.getChildrenSetOfOrgByCache(gongyinglian_org_id, 1));

        List<User> userlist = userService.getUserListByOrgIds(orgList, 1);

        mav.addObject("addUserlist", userlist);

        int addUserId = NumberUtils.toInt(request.getParameter("addUserId"));
        int currentUserId = 0;
        if (CollectionUtils.isNotEmpty(userlist)) {
            for (User cuser : userlist) {
                if (cuser.getUserId().equals(user.getUserId())) {
                    currentUserId = cuser.getUserId();
                    break;
                }
            }
        }
        if (buyorderVo.getIsAdmin() != null && buyorderVo.getIsAdmin() == 1) {
            List<Organization> organizationList = orgService.getChildrenOrgByParentId(user.getOrgId(), 1);
            if (CollectionUtils.isNotEmpty(organizationList)) {
                List<Integer> orgIdList = organizationList.stream().map(e -> e.getOrgId()).collect(Collectors.toList());
                List<User> userListBtOrgId = organizationMapper.getUserListBtOrgId(orgIdList, FlashConstant.BUY_TYPE, FlashConstant.COMPAMY_ID);
                if (CollectionUtils.isNotEmpty(userListBtOrgId)) {
                    List<Integer> creatorIdList = userListBtOrgId.stream().map(User::getUserId).collect(Collectors.toList());
                    buyorderVo.setCreatorIdList(creatorIdList);
                }
            }
        }

        //如果登录用户属于供应链管理部，则默认展示自己名下的
        if (buyorderVo.getCreator() == null && currentUserId > 0 && buyorderVo.getCreatorIdList() == null) {
            buyorderVo.setCreator(currentUserId);
        }


        // 客户信息里面的交易记录
        if (null != buyorderVo.getTraderId() && buyorderVo.getTraderId() > 0) {
            mav.addObject("method", "buyorder");
            mav.addObject("traderId", buyorderVo.getTraderId());
        }

        if (StringUtils.isNotBlank(buyorderVo.getCurrentOperateUser())) {
            List<Integer> buyOrderIds = getBuyOrderIdsByCurrentOperateUser(page, buyorderVo.getCurrentOperateUser());
            if (buyOrderIds.size() == 0) {
                mav.addObject("list", null);
                mav.addObject("page", page);
                mav.addObject("buyorderVo", buyorderVo);
                return mav;
            } else {
                buyorderVo.setBuyorderIdList(buyOrderIds);
            }
        }

        //跟进记录筛选项
        if (buyorderVo.getExpeditingFollowStatus() != null) {

        }

        Map<String, Object> map = buyorderService.getBuyorderVoPage(buyorderVo, page);
        if (!MapUtils.isEmpty(map)) {
            mav.addObject("list", map.get("list"));
            mav.addObject("page", map.get("page"));
        }
        mav.addObject("buyorderVo", buyorderVo);
        return mav;
    }


    private List<Integer> getBuyOrderIdsByCurrentOperateUser(Page page, String currentOperateUser) {
        return buyorderService.getBuyOrderIdsByCurrentOperateUser(page, currentOperateUser);
    }

    /**
     * <b>Description:</b><br>
     * 根据公司的id以及部门id查询所属员工
     *
     * @param parentId
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月13日 上午11:47:48
     */
    private List<Integer> getUserIdList(Integer parentId, HttpServletRequest request) {
        List<Integer> userIds = new ArrayList<>();
        if (parentId != null && parentId != 0) {
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            List<Integer> orgIds = new ArrayList<>();
            List<Organization> list = orgService.getOrgList(parentId, user.getCompanyId(), false);
            if (list != null && list.size() > 0) {
                for (Organization org : list) {
                    orgIds.add(org.getOrgId());
                }
            }
            if (orgIds.size() > 0) {
                List<User> uList = userService.getUserListByOrgIds(orgIds, user.getCompanyId());
                if (uList != null && uList.size() > 0) {
                    for (User us : uList) {
                        userIds.add(us.getUserId());
                    }
                }
            }
        }
        return userIds;
    }

    /**
     * <b>Description:</b><br>
     * 查看采购订单详情
     *
     * @param buyorder
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月19日 上午10:03:21
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "viewBuyorder")
    public ModelAndView viewBuyorder(HttpServletRequest request, Buyorder buyorder, String uri, @RequestParam(required = false, value = "skuNos") String skuNos) throws IOException {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        BuyorderVo bv = buyorderService.getBuyorderVoDetailNew(buyorder, user);
        ModelAndView mav = new ModelAndView();


        if (uri == null || "".equals(uri)) {
            uri = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + request.getContextPath() + "/order/buyorder/viewBuyorder.do";
        }
        mav.addObject("uri", uri);

        // 订单流，新老订单分别跳转到对应的详情页
        if (bv != null && ErpConst.ONE.equals(bv.getIsNew())) {
            mav.setViewName("redirect:/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=" + buyorder.getBuyorderId());
            return mav;
        }


        mav.setViewName("order/buyorder/view_buyorder_pf");
        //----------------------风控-----------------
        riskCheckService.setBuyorderGoodsIsRiskInfo(bv, bv.getBuyorderGoodsVoList());
        Integer riskFlag = riskCheckService.getRiskFlag(user, bv.getIsRisk());
        Boolean prBoolean = riskCheckService.permoissionsFlag(user, ErpConst.QUALITY_ORG);
        mav.addObject("riskFlag", riskFlag);
        mav.addObject("prBoolean", prBoolean);
        //----------------------风控-----------------
        mav.addObject("buyorderVo", bv);

        mav.addObject("skuNos", skuNos);
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())) {
            List<Integer> skuIds = new ArrayList<>();
            bv.getBuyorderGoodsVoList().stream().forEach(buyOrderGood -> {
                skuIds.add(buyOrderGood.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mav.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        //处理贝登售后标准
        dealWithBdAfterSaleStandard(bv);
        //处理贝登售后标准

        CommunicateRecord communicateRecord = new CommunicateRecord();
        communicateRecord.setBuyorderId(bv.getBuyorderId());
        communicateRecord.setTraderType(2);
        List<CommunicateRecord> crList = traderCustomerService.getCommunicateRecordList(communicateRecord);
        mav.addObject("communicateList", crList);
        // 付款方式
        List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
        mav.addObject("paymentTermList", paymentTermList);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(bv)));

        // 交易方式
        List<SysOptionDefinition> traderModeList = getSysOptionDefinitionList(519);
        SysOptionDefinition sysOptionDefinition = new SysOptionDefinition();
        sysOptionDefinition.setSysOptionDefinitionId(ErpConstant.BANK_ACCEPTANCE);
        sysOptionDefinition.setTitle("银行承兑汇票");
        traderModeList.add(sysOptionDefinition);
        mav.addObject("traderModeList", traderModeList);
        // 获取付款申请列表
        PayApply payApply = new PayApply();
        payApply.setCompanyId(user.getCompanyId());
        payApply.setPayType(517);// 采购付款申请
        payApply.setRelatedId(bv.getBuyorderId());
        List<PayApply> payApplyList = payApplyService.getPayApplyList(payApply);
        mav.addObject("payApplyList", payApplyList);

        // 判断是否有正在审核中的付款申请
        Integer isPayApplySh = 0;
        for (int i = 0; i < payApplyList.size(); i++) {
            if (payApplyList.get(i).getValidStatus() == 0) {
                isPayApplySh = 1;
                break;
            }
        }
        mav.addObject("isPayApplySh", isPayApplySh);


        String processKey = getProcessKey(bv);

        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                processKey + "_" + buyorder.getBuyorderId());
        mav.addObject("taskInfo", historicInfo.get("taskInfo"));
        mav.addObject("startUser", historicInfo.get("startUser") != null ?
                getRealNameByUserName(historicInfo.get("startUser").toString()) : null);
        mav.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态
        mav.addObject("endStatus", historicInfo.get("endStatus"));

        List<HistoricActivityInstance> historic = setAssignRealNames(mav, historicInfo);

        boolean permoissionsFlag = false;
        if (historicInfo != null && historicInfo.get("startUser") != null) {
            User startUser = userService.getByUsername(historicInfo.get("startUser").toString(), 1);
            permoissionsFlag = startUser != null ? riskCheckService.isOrgFlag(startUser, ErpConst.QUALITY_ORG) : false;
        }
        mav.addObject("permoissionsFlag", permoissionsFlag);
        mav.addObject("historicActivityInstance", historic);
        //mav.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        mav.addObject("commentMap", historicInfo.get("commentMap"));
        Task taskInfoPay = (Task) historicInfo.get("taskInfo");
        String verifyUsersPay = null;
        if (null != taskInfoPay) {

			/*Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfoPay);
			verifyUsersPay = (String) taskInfoVariables.get("verifyUsers");*/

            Map candidateUserMap = (Map) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfoPay.getId());

            if (CollectionUtils.isNotEmpty(candidateUserList)) {

                List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

                verifyUsersPay = StringUtils.join(userNameList, ",");

            }

        }
        mav.addObject("verifyUsers", getVerifyUserRealNames(verifyUsersPay));
        Attachment att = new Attachment();
        att.setRelatedId(bv.getBuyorderId());
        att.setAttachmentFunction(SysOptionConstant.BUYGOODS_DOC);
        List<Attachment> goodsDocAttachmentList = warehouseInService.getAttachmentList(att);
        mav.addObject("goodsDocAttachmentList", setOSSDownloadUri(goodsDocAttachmentList));
        return mav;
    }

    /**
     * 处理贝登售后标准
     *
     * @param buyorderVo
     */
    private void dealWithBdAfterSaleStandard(BuyorderVo buyorderVo) {

        if (buyorderVo.getTraderId() == null) {
            buyorderVo.getBuyorderGoodsVoList().stream().forEach(buyOrderGood -> {
                buyOrderGood.setSupplyPolicyMaintained(0);
                buyOrderGood.setQualityPeriod("/");
                buyOrderGood.setInstallPolicy("/");
            });
            return;
        }

        buyorderVo.getBuyorderGoodsVoList().stream().forEach(buyOrderGood -> {

            AfterSaleSupplyPolicy afterSaleSupplyPolicy = this.afterSaleServiceStandardService.findSupplyAfterSalePolicyBySkuNoAndTraderId(buyorderVo.getTraderId().longValue(), buyOrderGood.getSku());
            if (afterSaleSupplyPolicy == null) {
                buyOrderGood.setSupplyPolicyMaintained(0);
                buyOrderGood.setQualityPeriod("/");
                buyOrderGood.setInstallPolicy("/");
                return;
            }
            //处理安装政策
            dealWithInstallPolicy(afterSaleSupplyPolicy, buyOrderGood);

            //处理保修期
            buyOrderGood.setQualityPeriod("/");
            if (StringUtils.isNotEmpty(afterSaleSupplyPolicy.getGuaranteePolicyHostGuaranteePeriod())) {
                buyOrderGood.setQualityPeriod(afterSaleSupplyPolicy.getGuaranteePolicyHostGuaranteePeriod());
            }

            buyOrderGood.setSupplyPolicyMaintained(1);
        });
    }

    private void dealWithInstallPolicy(AfterSaleSupplyPolicy afterSaleSupplyPolicy, BuyorderGoodsVo buyOrderGood) {

        buyOrderGood.setInstallPolicy("/");

        if (afterSaleSupplyPolicy.getInstallPolicyInstallType() == InstallPolicyInstallTypeEnum.CHARGE.getType() ||
                afterSaleSupplyPolicy.getInstallPolicyInstallType() == InstallPolicyInstallTypeEnum.FREE.getType()) {
            buyOrderGood.setInstallPolicy(InstallPolicyInstallTypeEnum.getNameByValue(afterSaleSupplyPolicy.getInstallPolicyInstallType()));
            return;
        }

        if (afterSaleSupplyPolicy.getTechnicalDirectSupplyMaintain() != null && afterSaleSupplyPolicy.getTechnicalDirectSupplyMaintain() == 1) {
            buyOrderGood.setInstallPolicy("提供远程指导");
            return;
        }

    }

    private String getProcessKey(BuyorderVo buyorderInfo) {

        User creatorInfo = userService.getUserById(buyorderInfo.getCreator());
        boolean isSupplyHCGroup = false;
        //是否是供应链耗材组
        if (!StringUtils.isEmpty(creatorInfo.getOrgName())
                && creatorInfo.getOrgName().indexOf("供应链管理部耗材组") > -1) {
            isSupplyHCGroup = true;
        }

        if (!isSupplyHCGroup) {
            return "buyorderVerify";
        }

        Long lastPublishTime = DateUtil.convertLong("2021-03-24 18:52:00", DateUtil.TIME_FORMAT);
        if (buyorderInfo.getAddTime() >= lastPublishTime) {
            return "buyorderVerify";
        }


        // 获取订单审核信息
        TaskService taskService = processEngine.getTaskService();
        // 获取当前活动节点
        Task taskInfo = taskService.createTaskQuery().processInstanceBusinessKey("buyorderVerify_HC_" + buyorderInfo.getBuyorderId()).singleResult();

        HistoryService historyService = processEngine.getHistoryService();
        List<HistoricProcessInstance> historicProcessInstanceList = historyService.createHistoricProcessInstanceQuery()
                .processInstanceBusinessKey("buyorderVerify_HC_" + buyorderInfo.getBuyorderId())
                .list();

        if (taskInfo != null || CollectionUtils.isNotEmpty(historicProcessInstanceList)) {
            return "buyorderVerify_HC";
        }

        return "buyorderVerify";
    }

    @RequestMapping(value = "toEditPurchasePricePage")
    public ModelAndView toEditPurchasePricePage(@Param("buyorderGoodsId") Integer buyorderGoodsId,
                                                @Param("price") BigDecimal price) {
        ModelAndView mv = new ModelAndView("order/buyorder/edit_purchase_price");

        BuyorderGoods buyorderGoods = this.buyorderGoodsMapper.selectByPrimaryKey(buyorderGoodsId);

        mv.addObject("buyorderGoodsId", buyorderGoodsId);
        mv.addObject("skuNo", buyorderGoods.getSku());
        mv.addObject("price", price);
        return mv;
    }


    @ResponseBody
    @RequestMapping(value = "/editPurchasePrice")
    public ResultInfo<?> editPurchasePrice(@Param("buyorderGoodsId") Integer buyorderGoodsId,
                                           @Param("couponReason") String couponReason,
                                           @Param("price") BigDecimal price,
                                           @Param("price") BigDecimal originalPurchasePrice,
                                           @RequestParam(value = "callbackFuntion", required = false) String callbackFuntion) {
        ResultInfo<?> resultInfo = new ResultInfo<>();
        try {

            buyorderService.updateBuyorderGoodPrice(buyorderGoodsId, price, originalPurchasePrice, couponReason);

            resultInfo.setCode(0);
            resultInfo.setMessage("编辑采购价成功");

        } catch (Exception e) {
            logger.error("editPurchasePrice error", e);
            resultInfo.setMessage("编辑采购价失败");
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 订单新增沟通记录
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月30日 上午10:17:31
     */
    @FormToken(save = true)
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "addCommunicatePagePf")
    public ModelAndView addCommunicatePage(Buyorder buyorder, TraderSupplier traderSupplier, String flag,
                                           HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView("order/buyorder/add_communicate_pf");
        TraderContact traderContact = new TraderContact();
        // 联系人
        traderContact.setTraderId(traderSupplier.getTraderId());
        traderContact.setIsEnable(ErpConst.ONE);
        traderContact.setTraderType(ErpConst.TWO);
        List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);
        // 客户标签
        Tag tag = new Tag();
        tag.setTagType(SysOptionConstant.ID_32);
        tag.setIsRecommend(ErpConst.ONE);
        tag.setCompanyId(user.getCompanyId());

        Integer pageNo = 1;
        Integer pageSize = 10;

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

        mav.addObject("traderSupplier", traderSupplier);
        mav.addObject("buyorder", buyorder);
        mav.addObject("contactList", contactList);

        CommunicateRecord communicate = new CommunicateRecord();
        communicate.setBegintime(DateUtil.sysTimeMillis());
        communicate.setEndtime(DateUtil.sysTimeMillis() + 2 * 60 * 1000);
        mav.addObject("communicateRecord", communicate);

        // 沟通方式
        List<SysOptionDefinition> communicateList = getSysOptionDefinitionList(SysOptionConstant.ID_23);
        mav.addObject("communicateList", communicateList);

        mav.addObject("tagList", (List<Tag>) tagMap.get("list"));
        mav.addObject("page", (Page) tagMap.get("page"));
        mav.addObject("flag", flag);
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 申请付款页面
     *
     * @param request
     * @param buyorder
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2017年8月23日 下午4:17:12
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "applyPayment")
    public ModelAndView applyPayment(HttpServletRequest request, Buyorder buyorder, @RequestParam(required = false, defaultValue = "0") Integer type) {
        ModelAndView mv = new ModelAndView();
        User user = getSessionUser(request);
        buyorder.setFlag("fk");
        mv.addObject("type", type);
        int bankAcceptance = 0;
        try {
            // 获取对应供应商主信息
            TraderSupplier traderSupplier = new TraderSupplier();
            if (ErpConst.ZERO.equals(type)) {
                //普通采购单发起付款申请，包含直属采购费用单
                BuyorderVo bv = buyorderService.getBuyorderVoDetailNew(buyorder, user);
                traderSupplier.setTraderId(bv.getTraderId());
                BigDecimal tradersupplierOccupyAmount = payApplyService.queryTraderSupplierOccupyAmount(bv.getTraderId());
                mv.addObject("occupyAmount", tradersupplierOccupyAmount);
                //VDERP-5510：判断该订单是否有业务类型为订单付款的交易记录
                CapitalBill capitalBillExist = null;
                if (bv.getCapitalBillList().size() > 0) {
                    capitalBillExist = bv.getCapitalBillList()
                            .stream()
                            .filter(capitalBill -> null != capitalBill.getCapitalBillDetail() && SysOptionConstant.ID_525.equals(capitalBill.getCapitalBillDetail().getBussinessType()))
                            .findFirst()
                            .orElse(null);
                }
                mv.addObject("maxPrePaymentAmount", getMaxPrePaymentAmount(capitalBillExist, bv));
                mv.addObject("isHavePayed", capitalBillExist != null);
                for (int i = 0; i < bv.getBuyorderGoodsVoList().size(); i++) {
                    // 获取付款申请该产品（已申请数量、已申请总额）
                    BuyorderGoodsVo buyorderGoodsVo = bv.getBuyorderGoodsVoList().get(i);
                    Map<String, BigDecimal> passedMap = payApplyService
                            .getPassedByBuyorderGoodsId(buyorderGoodsVo.getBuyorderGoodsId());
                    buyorderGoodsVo.setApplyPaymentNum(passedMap.get("passedNum"));
                    buyorderGoodsVo.setApplyPaymentAmount(passedMap.get("passedAmount"));
                    //如果明细使用了返利，单价修改为扣减返利后的单价
                    if (BigDecimal.ZERO.compareTo(null == buyorderGoodsVo.getRebatePrice() ? BigDecimal.ZERO : buyorderGoodsVo.getRebatePrice()) != 0){
                        buyorderGoodsVo.setPrice(buyorderGoodsVo.getPrice().subtract(buyorderGoodsVo.getRebatePrice()));
                    }
                }
                //获取直属采购单的已申请信息
                if (bv.getBuyorderExpenseDto() != null && CollectionUtils.isNotEmpty(bv.getBuyorderExpenseDto().getBuyorderExpenseItemDtos())) {
                    //判断直属采购单是否有业务类型为订单付款的交易记录
                    CapitalBill virtureCapitalBillExist = null;
                    if (bv.getBuyorderExpenseCapitalBills().size() > 0) {
                        virtureCapitalBillExist = bv.getBuyorderExpenseCapitalBills()
                                .stream()
                                .filter(capitalBill -> null != capitalBill.getCapitalBillDetail() && SysOptionConstant.ID_525.equals(capitalBill.getCapitalBillDetail().getBussinessType()))
                                .findFirst()
                                .orElse(null);
                    }
                    mv.addObject("virtureMaxPrePaymentAmount", getVirtureMaxPrePaymentAmount(virtureCapitalBillExist,
                            bv.getBuyorderExpenseDto().getBuyorderExpenseDetailDto().getPaymentType(), bv.getBuyorderExpenseDto().getBuyorderExpenseDetailDto().getPrepaidAmount(), bv.getBuyorderExpenseDto().getBuyorderExpenseDetailDto().getTotalAmount()));
                    mv.addObject("virtureIsHavePayed", virtureCapitalBillExist != null);
                    for (int i = 0; i < bv.getBuyorderExpenseDto().getBuyorderExpenseItemDtos().size(); i++) {
                        Map<String, BigDecimal> applyedMap = payApplyService.
                                queryPassedByBuyorderExpenseGoodsId(bv.getBuyorderExpenseDto().getBuyorderExpenseItemDtos().get(i).getBuyorderExpenseItemId());
                        bv.getBuyorderExpenseDto().getBuyorderExpenseItemDtos().get(i).setPassedPayApplyNum(applyedMap.get("passedPayApplyNum"));
                        bv.getBuyorderExpenseDto().getBuyorderExpenseItemDtos().get(i).setPassedPayApplyAmount(applyedMap.get("passedPayApplyAmount"));
						Map<Integer,Integer> returnMap = buyorderExpenseApiService.calcReturnNum(bv.getBuyorderExpenseDto().getBuyorderExpenseId());
						bv.getBuyorderExpenseDto().getBuyorderExpenseItemDtos().get(i).setAfterReturnNum(returnMap.get(bv.getBuyorderExpenseDto().getBuyorderExpenseItemDtos().get(i).getBuyorderExpenseItemId()));
                    }
                }
                mv.addObject("buyorderVo", bv);
                // 获取银行帐号列表
                TraderFinance tf = new TraderFinance();
                tf.setTraderId(bv.getTraderId());
                tf.setTraderType(ErpConst.TWO);
                List<TraderFinance> traderFinance = traderCustomerService.getTraderCustomerFinanceList(tf);
                mv.addObject("traderFinance", traderFinance);
                // 是否银行承兑汇票
                OrderPaymentDetailsDto orderPaymentDetailsDto = buyorderApiService.queryOrderPaymentDetails(bv.getBuyorderId(), ErpConstant.ZERO, ErpConstant.BANK_ACCEPTANCE);
                bankAcceptance = Objects.nonNull(orderPaymentDetailsDto) ? ErpConstant.ONE : ErpConstant.ZERO;
            } else if (ErpConst.ONE.equals(type)) {
                //采购费用单发起付款申请
                BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.getOrderMainData(buyorder.getBuyorderExpenseId());
                traderSupplier.setTraderId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId());
                BigDecimal tradersupplierOccupyAmount = payApplyService.queryTraderSupplierOccupyAmount(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId());
                mv.addObject("occupyAmount", tradersupplierOccupyAmount);
                //判断采购单费用单是否有业务类型为订单付款的交易记录
                CapitalBill virtureCapitalBill = new CapitalBill();
                CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
                capitalBillDetail.setOrderType(4);// 采购费用订单类型
                capitalBillDetail.setOrderNo(buyorderExpenseDto.getBuyorderExpenseNo());
                capitalBillDetail.setRelatedId(buyorderExpenseDto.getBuyorderExpenseId());
                virtureCapitalBill.setCapitalBillDetail(capitalBillDetail);
                List<CapitalBill> virtureCapitalBills = capitalBillService.getCapitalBillList(virtureCapitalBill);
                CapitalBill virtureCapitalBillExist = null;
                if (virtureCapitalBills.size() > 0) {
                    virtureCapitalBillExist = virtureCapitalBills
                            .stream()
                            .filter(capitalBill -> null != capitalBill.getCapitalBillDetail() && SysOptionConstant.ID_525.equals(capitalBill.getCapitalBillDetail().getBussinessType()))
                            .findFirst()
                            .orElse(null);
                }
                mv.addObject("virtureMaxPrePaymentAmount", getVirtureMaxPrePaymentAmount(virtureCapitalBillExist,
                        buyorderExpenseDto.getBuyorderExpenseDetailDto().getPaymentType(), buyorderExpenseDto.getBuyorderExpenseDetailDto().getPrepaidAmount(), buyorderExpenseDto.getBuyorderExpenseDetailDto().getTotalAmount()));
                mv.addObject("virtureIsHavePayed", virtureCapitalBillExist != null);
                mv.addObject("buyorderExpenseDto", buyorderExpenseDto);
                for (int i = 0; i < buyorderExpenseDto.getBuyorderExpenseItemDtos().size(); i++) {
                    Map<String, BigDecimal> applyedMap = payApplyService.
                            queryPassedByBuyorderExpenseGoodsId(buyorderExpenseDto.getBuyorderExpenseItemDtos().get(i).getBuyorderExpenseItemId());
                    buyorderExpenseDto.getBuyorderExpenseItemDtos().get(i).setPassedPayApplyNum(applyedMap.get("passedPayApplyNum"));
                    buyorderExpenseDto.getBuyorderExpenseItemDtos().get(i).setPassedPayApplyAmount(applyedMap.get("passedPayApplyAmount"));
                    Map<Integer,Integer> returnMap = buyorderExpenseApiService.calcReturnNum(buyorderExpenseDto.getBuyorderExpenseId());
                    buyorderExpenseDto.getBuyorderExpenseItemDtos().get(i).setAfterReturnNum(returnMap.get(buyorderExpenseDto.getBuyorderExpenseItemDtos().get(i).getBuyorderExpenseItemId()));
                }
                // 获取银行帐号列表
                TraderFinance tf = new TraderFinance();
                tf.setTraderId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId());
                tf.setTraderType(ErpConst.TWO);
                List<TraderFinance> traderFinance = traderCustomerService.getTraderCustomerFinanceList(tf);
                mv.addObject("traderFinance", traderFinance);
            }
            TraderSupplierVo supplierInfo = traderSupplierService.getSupplierInfoByTraderSupplier(traderSupplier);
            mv.addObject("supplierInfo", supplierInfo);
        } catch (Exception e) {
            logger.error("applyPayment:", e);
        }
        mv.addObject("bankAcceptance", bankAcceptance);
        mv.setViewName("order/buyorder/apply_payment");
        return mv;
    }

    private String getMaxPrePaymentAmount(CapitalBill capitalBill, BuyorderVo buyorderVo) {

        if (capitalBill != null) {
            return "0";
        }

        SysOptionDefinition sysOptionDefinition = new SysOptionDefinition();
        sysOptionDefinition.setSysOptionDefinitionId(buyorderVo.getPaymentType());
        //获取选择的付款方式，并从字符串中提取出所用的百分数
        String maxPrePaymentAmountPrecent = Optional.ofNullable(sysOptionDefinitionService.getOptionById(sysOptionDefinition))
                .map(sysOption -> sysOption.getTitle())
                .map(title -> StringUtil.extractNumber(title))
                .orElse("");

        // VDERP-8140 当付款方式为424自定义时 会进入这个if 导致所有的最大金额为0。此处为一个历史遗留bug
        // 此处修改为:当为自定义时 取T_BUYORDER的PREPAID_AMOUNT预付金额字段作为最大可预付金额
        // 后续在保证字段正确的情况下 可以选用T_BUYORDER的PREPAID_AMOUNT替代计算
        if (StringUtil.isBlank(maxPrePaymentAmountPrecent)) {
            return buyorderVo.getPrepaidAmount().toString();
        }
        //最大可预付款金额=订单中额*所选付款方式的预付款百分数/100
        BigDecimal maxPrePaymentAmount = buyorderVo.getTotalAmount()
                .multiply(new BigDecimal(maxPrePaymentAmountPrecent))
                .divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);

        return maxPrePaymentAmount.toPlainString();
    }

    /**
     * @param virtureCapitalBill
     * @param
     * @return
     * @desc 获取费用单的预付款
     */
    private String getVirtureMaxPrePaymentAmount(CapitalBill virtureCapitalBill, Integer paymentType, BigDecimal prePaidAmount, BigDecimal totalAmount) {
        if (virtureCapitalBill != null) {
            return "0";
        }
        SysOptionDefinition sysOptionDefinition = new SysOptionDefinition();
        sysOptionDefinition.setSysOptionDefinitionId(paymentType);
        //获取选择的付款方式，并从字符串中提取出所用的百分数
        String virtureMaxPrePaymentAmountPrecent = Optional.ofNullable(sysOptionDefinitionService.getOptionById(sysOptionDefinition))
                .map(sysOption -> sysOption.getTitle())
                .map(title -> StringUtil.extractNumber(title))
                .orElse("");

        // VDERP-8140 当付款方式为424自定义时 会进入这个if 导致所有的最大金额为0。此处为一个历史遗留bug
        // 此处修改为:当为自定义时 取T_BUYORDER的PREPAID_AMOUNT预付金额字段作为最大可预付金额
        // 后续在保证字段正确的情况下 可以选用T_BUYORDER的PREPAID_AMOUNT替代计算
        if (StringUtil.isBlank(virtureMaxPrePaymentAmountPrecent)) {
            return prePaidAmount.toString();
        }
        //最大可预付款金额=订单中额*所选付款方式的预付款百分数/100
        BigDecimal virtureMaxPrePaymentAmount = totalAmount
                .multiply(new BigDecimal(virtureMaxPrePaymentAmountPrecent))
                .divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);

        return virtureMaxPrePaymentAmount.toPlainString();
    }

    /**
     * <b>Description:</b><br>
     * 保存申请付款
     *
     * @param request
     * @param payApply
     * @param priceArr
     * @param numArr
     * @param totalAmountArr
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2017年8月30日 下午6:12:54
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "saveApplyPayment")
    @SystemControllerLog(operationType = "add", desc = "保存申请付款")
    @Transactional
    public ResultInfo<?> saveApplyPayment(HttpServletRequest request, PayApply payApply,
                                          @RequestParam(required = false, value = "priceArr") String priceArr,
                                          @RequestParam(required = false, value = "numArr") String numArr,
                                          @RequestParam(required = false, value = "totalAmountArr") String totalAmountArr,
                                          @RequestParam(required = false, value = "virturePriceArr") String virturePriceArr,
                                          @RequestParam(required = false, value = "virtureNumArr") String virtureNumArr,
                                          @RequestParam(required = false, value = "virtureTotalAmountArr") String virtureTotalAmountArr,
                                          Integer payApplyType) {
        logger.info("保存申请付款,payApply:{}",JSON.toJSONString(payApply));
        logger.info("保存申请付款,priceArr:{},numArr:{},totalAmountArr:{}",priceArr,numArr,totalAmountArr);
        User user = getSessionUser(request);
        if (user != null) {
            payApply.setCreator(user.getUserId());
            payApply.setAddTime(DateUtil.sysTimeMillis());
            payApply.setUpdater(user.getUserId());
            payApply.setModTime(DateUtil.sysTimeMillis());
            payApply.setCompanyId(user.getCompanyId());
        }
        //校验付款申请的信息
        ResultInfo resultInfo = payApplyService.verifyPayApplyInfo(payApply, user, payApplyType);
        if (resultInfo.getCode() == -1) {
            return resultInfo;
        }
        ResultInfo<?> result = new ResultInfo<>(0,"操作成功");
        //根据支付类型，发起审核流0采购单+采购费用1采购费用
        if (ErpConst.ZERO.equals(payApplyType)) {
            PayApply buyorderApply = new PayApply();
            BeanUtil.copyProperties(payApply, buyorderApply);
            //采购单的申请金额需要与总额分开
            buyorderApply.setAmount(payApply.getBuyorder_amount());
            //-----付款申请拆分---//
            //---采购付款申请----//
            buyorderApply.setCurrencyUnitId(ErpConst.ONE);
            buyorderApply.setPayType(SysOptionConstant.ID_517);// 采购
            //保存采购明细id
            List<Double> priceList = JSON.parseArray(priceArr, Double.class);
            List<Double> numList = JSON.parseArray(numArr, Double.class);
            List<Double> totalAmountList = JSON.parseArray(totalAmountArr, Double.class);
            List<Integer> buyorderGoodsIdList = JSON.parseArray(request.getParameter("buyorderGoodsIdArr").toString(), Integer.class);
            List<PayApplyDetail> detailList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(buyorderGoodsIdList)) {
                for (int i = 0; i < buyorderGoodsIdList.size(); i++) {
                    if (buyorderGoodsIdList.get(i) == null) {
                        continue;
                    }
                    PayApplyDetail payApplyDetail = new PayApplyDetail();
                    payApplyDetail.setDetailgoodsId(buyorderGoodsIdList.get(i));
                    payApplyDetail.setPrice(new BigDecimal(priceList.get(i)));
                    payApplyDetail.setNum(new BigDecimal(numList.get(i)));
                    payApplyDetail.setTotalAmount(new BigDecimal(totalAmountList.get(i)));
                    detailList.add(payApplyDetail);
                }
                buyorderApply.setDetailList(detailList);
                // 调用提交付款申请公共校验接口，校验不通过返回报错信息
                try {
                    PayApplyCheckDto checkDto = new PayApplyCheckDto();
                    checkDto.setPayType(ErpConst.BUY_ORDER_TYPE);
                    checkDto.setAmount(buyorderApply.getAmount());
                    checkDto.setTraderId(buyorderApply.getTraderId());
                    List<Integer> ContractReviewStatus = buyorderApiService.getContractReviewStatus(buyorderApply.getRelatedId());
                    logger.info("采购单提交付款申请公共校验,ContractReviewStatus:{}", JSON.toJSONString(ContractReviewStatus));
                    checkDto.setIsAudit(CollUtil.isNotEmpty(ContractReviewStatus) && ContractReviewStatus.contains(ErpConst.ONE) && !ContractReviewStatus.contains(ErpConst.ZERO));
                    logger.info("采购单提交付款申请公共校验接口,入参:{}", JSON.toJSONString(checkDto));
                    payApplyAutoPayApi.payApplyRuleCheck(checkDto);
                } catch (Exception e) {
                    logger.info("采购单提交付款申请公共校验接口异常", e);
                    return new ResultInfo<>(-1, e.getMessage());
                }
                result = buyorderService.saveApplyPayment(buyorderApply);
                if (result == null || result.getCode() == -1) {
                    return result;
                }
                Buyorder buyorder = new Buyorder();
                buyorder.setBuyorderId(buyorderApply.getRelatedId());
                BuyorderVo buyorderVo = buyorderService.getBuyorderVoDetail(buyorder);
                // 付款申请ID
                Integer payApplyId = (Integer) result.getData();
                // 调用自动制单公共校验接口，根据结果更新AUTO_BILL字段
                try {
                    List<PayApplyCreateBillDto> payApplyCreateBillDtoList = payApplyAutoPayApi.findPayApply(payApplyId);
                    if (CollUtil.isNotEmpty(payApplyCreateBillDtoList)) {
                        payApplyAutoPayApi.createBillRuleCheck(payApplyCreateBillDtoList.get(0));
                        payApplyApiService.updateAutoBill(payApplyId, ErpConst.ONE);
                    }
                } catch (ServiceException e) {
                    log.info("采购单调用自动制单公共校验接口不满足自动制单:{}", e.getMessage());
                } catch (Exception e) {
                    log.info("采购单调用自动制单公共校验接口异常", e);
                }
                PayApply payApplyInfo = payApplyService.getPayApplyInfo(payApplyId);
                payApplyInfo.setOrderNo(buyorderVo.getBuyorderNo());

                result = startPayApplyProcess(payApplyInfo, user, request, ErpConst.ZERO, buyorderVo.getBuyorderId());
            }
        }
        if (ErpConst.ONE.equals(payApplyType) || (payApply.getBuyorderExpenseRelatedId() != null
                && !ErpConst.ZERO.equals(payApply.getBuyorderExpenseRelatedId()))) {
            //费用单的付款申请
            PayApply buyorderExpenseApply = new PayApply();
            BeanUtil.copyProperties(payApply, buyorderExpenseApply);
            buyorderExpenseApply.setRelatedId(payApply.getBuyorderExpenseRelatedId());
            //采购单的申请金额需要与总额分开
            buyorderExpenseApply.setAmount(payApply.getVirture_amount());

            //-----付款申请拆分---//
            //---采购付款申请----//
            buyorderExpenseApply.setCurrencyUnitId(ErpConst.ONE);
            buyorderExpenseApply.setPayType(SysOptionConstant.ID_4125);// 采购费用
            //保存采购明细id
            List<Double> priceList = JSON.parseArray(virturePriceArr, Double.class);
            List<Double> numList = JSON.parseArray(virtureNumArr, Double.class);
            List<Double> totalAmountList = JSON.parseArray(virtureTotalAmountArr, Double.class);
            List<Integer> buyorderExpenseGoodsIdList = JSON.parseArray(request.getParameter("buyorderExpenseGoodsIdArr").toString(), Integer.class);
            List<PayApplyDetail> detailList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(buyorderExpenseGoodsIdList)) {
                for (int i = 0; i < buyorderExpenseGoodsIdList.size(); i++) {
                    if (buyorderExpenseGoodsIdList.get(i) == null) {
                        continue;
                    }
                    PayApplyDetail payApplyDetail = new PayApplyDetail();
                    payApplyDetail.setDetailgoodsId(buyorderExpenseGoodsIdList.get(i));
                    payApplyDetail.setPrice(new BigDecimal(priceList.get(i)));
                    payApplyDetail.setNum(new BigDecimal(numList.get(i)));
                    payApplyDetail.setTotalAmount(new BigDecimal(totalAmountList.get(i)));
                    detailList.add(payApplyDetail);
                }
                buyorderExpenseApply.setDetailList(detailList);
                result = buyorderService.saveApplyPayment(buyorderExpenseApply);
                if (result == null || result.getCode() == -1) {
                    return result;
                }

                BuyorderExpenseDto buyorderExpense = new BuyorderExpenseDto();
                buyorderExpense.setBuyorderExpenseId(buyorderExpenseApply.getRelatedId());
                buyorderExpense = buyorderExpenseApiService.getOrderMainData(buyorderExpense.getBuyorderExpenseId());
                // 付款申请ID
                Integer payApplyId = (Integer) result.getData();
                // 调用自动制单公共校验接口，根据结果更新AUTO_BILL字段
                try {
                    List<PayApplyCreateBillDto> payApplyCreateBillDtoList = payApplyAutoPayApi.findPayApply(payApplyId);
                    if (CollUtil.isNotEmpty(payApplyCreateBillDtoList)) {
                        payApplyAutoPayApi.createBillRuleCheck(payApplyCreateBillDtoList.get(0));
                        payApplyApiService.updateAutoBill(payApplyId, ErpConst.ONE);
                    }
                } catch (ServiceException e) {
                    log.info("采购费用单调用自动制单公共校验接口不满足自动制单:{}", e.getMessage());
                } catch (Exception e) {
                    log.info("采购费用单调用自动制单公共校验接口异常", e);
                }
                PayApply payApplyInfo = payApplyService.getPayApplyInfo(payApplyId);
                payApplyInfo.setOrderNo(buyorderExpense.getBuyorderExpenseNo());
                result = startPayApplyProcess(payApplyInfo, user, request, ErpConst.ONE, buyorderExpense.getBuyorderExpenseId());
            }
        }
        return result;
    }

    /**
     * @param payApplyInfo
     * @param user
     * @param request
     * @param payType
     * @param relatedId
     * @return
     * <AUTHOR>
     * @desc 付款申请发起审核
     */
    private ResultInfo startPayApplyProcess(PayApply payApplyInfo, User user, HttpServletRequest request, Integer payType, Integer relatedId) {
        ResultInfo result = new ResultInfo(0, "保存成功");
        try {
            Map<String, Object> variableMap = new HashMap<String, Object>();
            // 开始生成流程(如果没有taskId表示新流程需要生成)
            variableMap.put("payApply", payApplyInfo);
            //申请付款的总金额，用于判断是否流转到供应链老大
            if (new BigDecimal(100000).compareTo(payApplyInfo.getAmount()) <= 0) {
                variableMap.put("needManagerVerify", ErpConst.ONE);
            } else {
                variableMap.put("needManagerVerify", ErpConst.ZERO);
            }
            variableMap.put("currentAssinee", user.getUsername());
            variableMap.put("processDefinitionKey", "paymentVerify");
            variableMap.put("businessKey", "paymentVerify_" + payApplyInfo.getPayApplyId());
            variableMap.put("relateTableKey", payApplyInfo.getPayApplyId());
            variableMap.put("relateTable", "T_PAY_APPLY");
            variableMap.put("orgId", user.getOrgId());
            //采购付款申请
            variableMap.put("applyPayType", ErpConst.ZERO);
            // 流程条件标识
            variableMap.put("activitiType", "buyorderPaymentVerify");
            if (StringUtil.isNotBlank(paymentVerifyVersionId)) {
                //指定付款申请的审核流程版本--灰度测试使用
                logger.info("灰度测试审核流版本{},", paymentVerifyVersionId);
                actionProcdefService.createProcessInstanceByVersionId(request, paymentVerifyVersionId,
                        "paymentVerify_" + payApplyInfo.getPayApplyId(), variableMap);
            } else {
                actionProcdefService.createProcessInstance(request, "paymentVerify",
                        "paymentVerify_" + payApplyInfo.getPayApplyId(), variableMap);
            }
            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    "paymentVerify_" + payApplyInfo.getPayApplyId());
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                String taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<String, Object>();
                // 设置审核完成监听器回写参数
                variables.put("tableName", "T_PAY_APPLY");
                variables.put("id", "PAY_APPLY_ID");
                variables.put("idValue", payApplyInfo.getPayApplyId());
                variables.put("key", "VALID_STATUS");
                variables.put("value", 1);
                variables.put("payType", payType);
                // 回写数据的表在db中
                variables.put("db", 2);
                if (ErpConst.ZERO.equals(payType)) {
                    //采购单的付款申请
                    // 审核完成时解锁
                    variables.put("buyorderId", relatedId);
                    // 默认审批通过
                    ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                            user.getUsername(), variables);
                    // 如果未结束添加审核对应主表的审核状态
                    if (!complementStatus.getData().equals("endEvent")) {
                        verifiesRecordService.saveVerifiesInfo(taskId, 0);
                    }
                    // 发起审核时锁定采购单
                    actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", relatedId, "LOCKED_STATUS",
                            1, 2);

                    // FAQ3336 采购单申请付款后更新T_BUYORDER_DATA的IS_FINANCE_ALREADY_STATUS字段
                    long nowTime = System.currentTimeMillis();
                    BuyorderDataDto buyorderDataDto = new BuyorderDataDto();
                    buyorderDataDto.setBuyorderId(relatedId);
                    buyorderDataDto.setIsFinanceAlreadyStatus(1);
                    buyorderDataMapper.updateOrderIsFinanceAlreadyByTime(Collections.singletonList(buyorderDataDto), nowTime);
                } else if (ErpConst.ONE.equals(payType)) {
                    //采购费用单的付款申请
                    // 审核完成时解锁
                    variables.put("buyorderExpenseId", relatedId);
                    // 默认审批通过
                    ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                            user.getUsername(), variables);
                    // 如果未结束添加审核对应主表的审核状态
                    if (!complementStatus.getData().equals("endEvent")) {
                        verifiesRecordService.saveVerifiesInfo(taskId, 0);
                    }
                    // 发起审核时锁定采购单
                    actionProcdefService.updateInfo("T_BUYORDER_EXPENSE", "BUYORDER_EXPENSE_ID", relatedId, "LOCKED_STATUS",
                            1, 2);
                }
            }
            return result;
        } catch (Exception e) {
            logger.error("saveApplyPayment:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/saveApplyPayment1")
    public ModelAndView saveApplyPayment(HttpServletRequest request, HttpSession session, PayApply payApply,
                                         PayApplyDetail payApplyDetail) {
        ModelAndView mv = new ModelAndView();
        try {
            payApply.setModTime(1l);
            payApplyDetail.setPayApplyId(1);

            return success(mv);
            /*
             * buyorder = buyorderService.saveApplyPayment(payApply); if(null !=
             * buyorder){
             * mv.addObject("url","./viewbaseinfo.do?goodsId="+buyorder.
             * getGoodsId()); return success(mv); }else{ return fail(mv); }
             */
        } catch (Exception e) {
            logger.error("saveApplyPayment1:", e);
            return fail(mv);
        }
    }

    /**
     * <b>Description:</b><br>
     * 编辑沟通记录
     *
     * @param communicateRecord
     * @param request
     * @param session
     * @return
     * @throws IOException
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月24日 下午1:31:13
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "/editcommunicate")
    public ModelAndView editCommunicate(CommunicateRecord communicateRecord, TraderSupplier traderSupplier, String flag,
                                        Buyorder buyorder, HttpServletRequest request, HttpSession session) throws IOException {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView("order/buyorder/edit_communicate");
        CommunicateRecord communicate = traderCustomerService.getCommunicate(communicateRecord);
        communicate.setTraderSupplierId(communicateRecord.getTraderSupplierId());
        communicate.setTraderId(communicateRecord.getTraderId());
        communicate.setBuyorderId(communicateRecord.getBuyorderId());
        TraderContact traderContact = new TraderContact();
        // 联系人
        traderContact.setTraderId(communicateRecord.getTraderId());
        traderContact.setIsEnable(ErpConst.ONE);
        traderContact.setTraderType(ErpConst.TWO);
        List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);

        // 沟通方式
        List<SysOptionDefinition> communicateList = getSysOptionDefinitionList(SysOptionConstant.ID_23);
        mv.addObject("communicateList", communicateList);

        // 客户标签
        Tag tag = new Tag();
        tag.setTagType(SysOptionConstant.ID_32);
        tag.setIsRecommend(ErpConst.ONE);
        tag.setCompanyId(user.getCompanyId());

        Integer pageNo = 1;
        Integer pageSize = 10;

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

        mv.addObject("communicateRecord", communicate);

        mv.addObject("contactList", contactList);

        mv.addObject("tagList", (List<Tag>) tagMap.get("list"));
        mv.addObject("page", (Page) tagMap.get("page"));
        mv.addObject("method", "communicaterecord");
        mv.addObject("traderSupplier", traderSupplier);
        mv.addObject("buyorder", buyorder);
        mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(communicate)));
        mv.addObject("flag", flag);
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 保存新增沟通
     *
     * @param communicateRecord
     * @param request
     * @param session
     * @return
     * @throws Exception
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月24日 下午2:36:53
     */
    @FormToken(remove = true)
    @SuppressWarnings("all")
    @ResponseBody
    @RequestMapping(value = "/saveaddcommunicate")
    @SystemControllerLog(operationType = "add", desc = "保存新增采购沟通记录")
    public ResultInfo saveAddCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request, String flag,
                                         HttpSession session) throws Exception {
        Boolean record = false;
        communicateRecord.setCommunicateType(SysOptionConstant.ID_247);// 采购订单
        communicateRecord.setRelatedId(communicateRecord.getBuyorderId());
        communicateRecord.setTraderType(2);
        record = traderSupplierService.saveAddCommunicate(communicateRecord, request, session);
        if (record) {
            return new ResultInfo(0, "操作成功！", communicateRecord.getBuyorderId() + "," + flag);
        } else {
            return new ResultInfo(1, "操作失败！");
        }

    }

    /**
     * <b>Description:</b><br>
     * 保存编辑沟通记录
     *
     * @param communicateRecord
     * @param request
     * @param session
     * @return
     * @throws Exception
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月24日 下午2:36:53
     */
    @SuppressWarnings("all")
    @ResponseBody
    @RequestMapping(value = "/saveeditcommunicate")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑沟通记录")
    public ResultInfo saveEditCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request,
                                          String beforeParams, String flag, HttpSession session) throws Exception {
        Boolean record = false;
        communicateRecord.setCommunicateType(SysOptionConstant.ID_247);// 采购订单
        communicateRecord.setRelatedId(communicateRecord.getBuyorderId());
        record = traderSupplierService.saveEditCommunicate(communicateRecord, request, session);
        if (record) {
            return new ResultInfo(0, "操作成功！", communicateRecord.getBuyorderId() + "," + flag);
        } else {
            return new ResultInfo(1, "操作失败！");
        }

    }

    /**
     * <b>Description:</b><br>
     * 获取已忽略列表
     *
     * @param request
     * @param pageNo
     * @param pageSize
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月1日 上午9:44:49
     */
    @ResponseBody
    @RequestMapping(value = "getIgnoreSaleorderPage")
    public ModelAndView getIgnoreSaleorderPage(HttpServletRequest request, SaleorderGoodsVo saleorderGoodsVo,
                                               @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                               @RequestParam(required = false) Integer pageSize) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView("order/buyorder/list_ignore");
        Page page = getPageTag(request, pageNo, pageSize);
        // 产品负责人
        List<User> productUserList = userService.getUserListByPositType(SysOptionConstant.ID_311, user.getCompanyId());
        mav.addObject("productUserList", productUserList);
        // 销售类型
        List<User> salesUserList = userService.getUserListByPositType(SysOptionConstant.ID_310, user.getCompanyId());
        // 申请人（包括产品和销售）
        List<User> applicantList = new ArrayList<>();
        applicantList.addAll(productUserList);
        applicantList.addAll(salesUserList);
        mav.addObject("applicantList", applicantList);
        saleorderGoodsVo.setCompanyId(user.getCompanyId());
        if (ObjectUtils.notEmpty(saleorderGoodsVo.getApplicantId())) {
            List<Integer> userIds = new ArrayList<>();
            userIds.add(saleorderGoodsVo.getApplicantId());
            saleorderGoodsVo.setUserIds(userIds);
        }
        Map<String, Object> map = buyorderService.getIgnoreSaleorderPage(saleorderGoodsVo, page);

        List<SaleorderGoodsVo> saleorderGoodsLists = (List<SaleorderGoodsVo>) map.get("list");

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(saleorderGoodsLists)) {
            List<Integer> skuIds = new ArrayList<>();
            saleorderGoodsLists.stream().forEach(saleGood -> {
                skuIds.add(saleGood.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mav.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end


        mav.addObject("list", saleorderGoodsLists);
        page = (Page) map.get("page");
        mav.addObject("page", page);
        mav.addObject("saleorderGoodsVo", saleorderGoodsVo);
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 保存申请审核
     *
     * @param buyorder
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月7日 下午1:23:46
     */
    @ResponseBody
    @RequestMapping(value = "saveApplyReview")
    @SystemControllerLog(operationType = "eidt", desc = "采购订单申请审核")
    public ModelAndView saveApplyReview(Buyorder buyorder, HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        buyorder.setCompanyId(user.getCompanyId());
        buyorder.setUpdater(user.getUserId());
        buyorder.setModTime(DateUtil.sysTimeMillis());
        // 审核暂时为直接生效
        buyorder.setValidStatus(ErpConst.ONE);
        buyorder.setCompanyName(user.getCompanyName());
        buyorder.setValidTime(DateUtil.sysTimeMillis());
        buyorder.setStatus(ErpConst.ONE);
        ResultInfo<?> res = buyorderService.saveApplyReview(buyorder);
        if (res != null && res.getCode() == 0) {
            ModelAndView mav = new ModelAndView("redirect:/order/buyorder/viewBuyordersh.do");
            mav.addObject("buyorderId", buyorder.getBuyorderId());
            return mav;
        } else {
            ModelAndView mav = new ModelAndView("common/fail");
            return mav;
        }
    }

    /**
     * <b>Description:</b><br>
     * 详情页大数据量时异步加载补全产品信息
     *
     * @param request
     * @param buyorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年5月31日 下午6:32:51
     */
    @SuppressWarnings("all")
    @ResponseBody
    @RequestMapping(value = "getBuyGoodsListByAjax")
    public ResultInfo<?> getBuyGoodsListByAjax(HttpServletRequest request, BuyorderVo buyorder) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        List<BuyorderGoodsVo> list = buyorderService.getBuyorderGoodsVoListByAjax(buyorder, user);
        if (list != null) {
            return new ResultInfo(0, "查询成功！", list);
        }
        return new ResultInfo(1, "查询失败！");

    }

    /**
     * <b>Description:</b><br>
     * 获取出入库记录
     *
     * @param request
     * @param buyorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年5月31日 下午6:32:51
     */
    @SuppressWarnings("all")
    @ResponseBody
    @RequestMapping(value = "getWarehouseGoodsOperateLogVoListPage")
    public ModelAndView getWarehouseGoodsOperateLogVoListPage(HttpServletRequest request, BuyorderVo buyorder) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        buyorder.setCompanyId(user.getCompanyId());
        ModelAndView mav = new ModelAndView("order/buyorder/warehouseGoodsOperateLog_page");
        List<WarehouseGoodsOperateLogVo> list = buyorderService.getWarehouseGoodsOperateLogVoListPage(buyorder);
        mav.addObject("warehouseGoodsOperateLogVoList", list);
        return mav;

    }

    /**
     * <b>Description:</b><br>
     * 获取采购详情的产品信息
     *
     * @param request
     * @param buyorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年5月31日 下午6:32:51
     */
    @SuppressWarnings("all")
    @ResponseBody
    @RequestMapping(value = "getBuyorderGoodsVoListPage")
    public ModelAndView getBuyorderGoodsVoListPage(HttpServletRequest request, BuyorderVo buyorder) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        buyorder.setCompanyId(user.getCompanyId());
        ModelAndView mav = new ModelAndView("order/buyorder/buyorderGoods_page");
        BuyorderVo bv = buyorderService.getBuyorderGoodsVoListPage(buyorder);
        mav.addObject("buyorderVo", bv);
        return mav;

    }

    /**
     * <b>Description:</b><br>
     * 通过ajax后补数据
     *
     * @param buyorder
     * @return
     * @throws IOException
     * @Note <b>Author:</b> Cooper <br>
     * <b>Date:</b> 2017年7月19日 上午10:03:21
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "getSaleBuyNumByAjax")
    public ResultInfo<?> getSaleBuyNumByAjax(HttpServletRequest request, Buyorder buyorder) throws IOException {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        // ModelAndView mav = new ModelAndView();
        // Page page = getPageTag(request, pageNo,pageSize);
        // 获取采购订单详情
        BuyorderVo bv = buyorderService.getSaleBuyNumByAjax(buyorder, user);
        /*
         * Map<String,Object> map=(Map<String, Object>) res.getData();
         * JSONObject json = JSONObject.fromObject(map.get("buyorderVo"));
         * BuyorderVo bv = JsonUtils.readValue(json.toString(),
         * BuyorderVo.class);
         */
        return new ResultInfo<Object>(0, "查询成功", bv);
    }

    /**
     * <b>Description:</b><br>
     * 查看采购订单申请审核后页面
     *
     * @param buyorder
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月19日 上午10:03:21
     */
    @ResponseBody
    @RequestMapping(value = "viewBuyordersh")
    public ModelAndView viewBuyorderSH(HttpServletRequest request, Buyorder buyorder, String uri) throws IOException {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        BuyorderVo bv = buyorderService.getBuyorderVoDetailNew(buyorder, user);
        ModelAndView mav = new ModelAndView();


        if (uri == null || "".equals(uri)) {
            uri = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + request.getContextPath() + "/order/buyorder/viewBuyordersh.do";
        }
        mav.addObject("uri", uri);

        // 订单流，新老订单分别跳转到对应的详情页
        if (bv != null && ErpConst.ONE.equals(bv.getIsNew())) {
            mav.setViewName("redirect:/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=" + buyorder.getBuyorderId());
            return mav;
        }


        mav.setViewName("order/buyorder/view_buyorder_sx");
        mav.addObject("ezBuyorderInstockUrl", ezBuyorderInstockUrl);

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())) {
            List<Integer> skuIds = new ArrayList<>();
            bv.getBuyorderGoodsVoList().stream().forEach(buyOrderGood -> {
                skuIds.add(buyOrderGood.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            try {
                Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
                mav.addObject("newSkuInfosMap", newSkuInfosMap);
            } catch (Exception e) {
                logger.error("skuIds:" + skuIds, e);
            }

        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        //处理贝登售后标准
        dealWithBdAfterSaleStandard(bv);

        mav.addObject("curr_user", user);
        // 发票类型
        List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(SysOptionConstant.ID_428);
        mav.addObject("invoiceTypes", invoiceTypes);
        riskCheckService.setBuyorderGoodsIsRiskInfo(bv, bv.getBuyorderGoodsVoList());
        //设置合同下载地址
        String contractUrl = "";
        if (bv.getContractUrl() != null && !"".equals(bv.getContractUrl())) {
            //有合同章
            contractUrl = bv.getContractUrl().replace("display", "download");
            mav.addObject("contractUrl", contractUrl);
        }
        mav.addObject("buyorderVo", bv);

        CommunicateRecord communicateRecord = new CommunicateRecord();
        communicateRecord.setBuyorderId(bv.getBuyorderId());
        List<CommunicateRecord> crList = traderCustomerService.getCommunicateRecordList(communicateRecord);
        mav.addObject("communicateList", crList);
        // 付款方式
        List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
        mav.addObject("paymentTermList", paymentTermList);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(bv)));

        // 交易方式
        List<SysOptionDefinition> traderModeList = getSysOptionDefinitionList(519);
        SysOptionDefinition sysOptionDefinition = new SysOptionDefinition();
        sysOptionDefinition.setSysOptionDefinitionId(ErpConstant.BANK_ACCEPTANCE);
        sysOptionDefinition.setTitle("银行承兑汇票");
        traderModeList.add(sysOptionDefinition);
        mav.addObject("traderModeList", traderModeList);

        // 售后订单列表
        AfterSalesVo as = new AfterSalesVo();
        as.setOrderId(buyorder.getBuyorderId());
        as.setSubjectType(536);
        List<AfterSalesVo> asList = afterSalesOrderService.getAfterSalesVoListByOrderId(as);
        if (asList != null && asList.size() > 0) {
            if (asList.get(0).getAtferSalesStatus() == 2 || asList.get(0).getAtferSalesStatus() == 3) {
                mav.addObject("addAfterSales", 1);
            } else {
                mav.addObject("addAfterSales", 0);
                mav.addObject("lockedReason", "售后锁定");
            }
        } else {
            mav.addObject("addAfterSales", 1);
        }
        mav.addObject("asList", asList);

        // 获取付款申请列表
        PayApply payApply = new PayApply();
        payApply.setCompanyId(user.getCompanyId());
        payApply.setPayType(517);// 采购付款申请
        payApply.setRelatedId(bv.getBuyorderId());
        List<PayApply> payApplyList = payApplyService.getPayApplyList(payApply);
        mav.addObject("payApplyList", payApplyList);

        // 判断是否有正在审核中的付款申请
        Integer isPayApplySh = 0;
        Integer payApplyId = 0;
        for (int i = 0; i < payApplyList.size(); i++) {
            if (payApplyList.get(i).getValidStatus() == 0 || payApplyList.get(i).getValidStatus() == 2) {
                if (payApplyList.get(i).getValidStatus() == 0) {
                    isPayApplySh = 1;
                    mav.addObject("lockedReason", "付款申请锁定");
                }
                // payApplyId = payApplyList.get(i).getPayApplyId();
                break;
            }
        }
        if (!payApplyList.isEmpty() && payApplyId == 0) {
            payApplyId = payApplyList.get(0).getPayApplyId();
        }
        mav.addObject("isPayApplySh", isPayApplySh);
        mav.addObject("payApplyId", payApplyId);

        String businessKey = getProcessKey(bv);

        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                businessKey + "_" + buyorder.getBuyorderId());
        mav.addObject("taskInfo", historicInfo.get("taskInfo"));
        mav.addObject("startUser", historicInfo.get("startUser") != null ?
                getRealNameByUserName(historicInfo.get("startUser").toString()) : null);
        mav.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态
        mav.addObject("endStatus", historicInfo.get("endStatus"));

        List<HistoricActivityInstance> historic = setAssignRealNames(mav, historicInfo);
        boolean permoissionsFlag = false;
        if (historicInfo != null && historicInfo.get("startUser") != null) {
            User startUser = userService.getByUsername(historicInfo.get("startUser").toString(), 1);
            permoissionsFlag = startUser != null ? riskCheckService.isOrgFlag(startUser, ErpConst.QUALITY_ORG) : false;
        }
        mav.addObject("permoissionsFlag", permoissionsFlag);
        mav.addObject("historicActivityInstance", historic);

        mav.addObject("commentMap", historicInfo.get("commentMap"));
        Task taskInfo = (Task) historicInfo.get("taskInfo");
        // 当前审核人
        String verifyUsers = null;
        if (null != taskInfo) {

            Map candidateUserMap = (Map) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

            if (CollectionUtils.isNotEmpty(candidateUserList)) {

                List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

                verifyUsers = StringUtils.join(userNameList, ",");

            }

        }
        mav.addObject("verifyUsers", getVerifyUserRealNames(verifyUsers));


        Map<String, Object> historicInfoPay = actionProcdefService.getHistoric(processEngine,
                "paymentVerify_" + payApplyId);
        Task taskInfoPay = (Task) historicInfoPay.get("taskInfo");
        mav.addObject("taskInfoPay", taskInfoPay);
        mav.addObject("startUserPay", historicInfoPay.get("startUser"));
        // 最后审核状态
        mav.addObject("endStatusPay", historicInfoPay.get("endStatus"));
        mav.addObject("historicActivityInstancePay", historicInfoPay.get("historicActivityInstance"));
        mav.addObject("commentMapPay", historicInfoPay.get("commentMap"));
        mav.addObject("candidateUserMapPay", historicInfoPay.get("candidateUserMap"));
        // 当前审核人
        String verifyUsersPay = null;
        if (null != taskInfoPay) {
            Map<String, Object> taskInfoVariablesPay = actionProcdefService.getVariablesMap(taskInfoPay);
            verifyUsersPay = (String) taskInfoVariablesPay.get("verifyUsers");
        }
        // 当前付款审核人
        mav.addObject("verifyUsersPay", verifyUsersPay);
        // 采购订单修改申请列表（不分页）
        BuyorderModifyApply buyorderModifyApply = new BuyorderModifyApply();
        buyorderModifyApply.setBuyorderId(buyorder.getBuyorderId());
        List<BuyorderModifyApply> buyorderModifyApplyList = buyorderService
                .getBuyorderModifyApplyList(buyorderModifyApply);
        mav.addObject("buyorderModifyApplyList", buyorderModifyApplyList);
        // 判断是否有正在审核中的付款申请
        for (int i = 0; i < buyorderModifyApplyList.size(); i++) {
            if (buyorderModifyApplyList.get(i).getVerifyStatus() == 0) {
                mav.addObject("lockedReason", "采购单修改锁定");
            }
            break;
        }
        // 入库附件
        Attachment att = new Attachment();
        att.setRelatedId(bv.getBuyorderId());
        att.setAttachmentFunction(SysOptionConstant.ID_837);
        List<Attachment> AttachmentList = warehouseInService.getAttachmentList(att);
        att.setAttachmentFunction(SysOptionConstant.BUYGOODS_DOC);
        List<Attachment> goodsDocAttachmentList = warehouseInService.getAttachmentList(att);

        mav.addObject("goodsDocAttachmentList", setOSSDownloadUri(goodsDocAttachmentList));
        mav.addObject("AttachmentList", AttachmentList);
        //直发采购单校验是否展示新增同行单按钮
        if (ErpConst.ONE.equals(bv.getDeliveryDirect())) {
            List<Integer> buyorderGoodsIds = bv.getBuyorderGoodsVoList().stream().map(BuyorderGoodsVo::getBuyorderGoodsId).collect(Collectors.toList());
            mav.addObject("isShowDtBtn", purchaseDeliveryBatchInfoService.verifyIsShowDeliveryDirectBtn(buyorderGoodsIds));
        }
        //查询同行单信息
        List<PurchaseDeliveryBatchInfoDto> purchaseDeliveryDirectBatchInfoVoList = purchaseDeliveryBatchInfoService.queryPurchaseDeliveryInfoByBuyorderId(bv.getBuyorderId());
        for (PurchaseDeliveryBatchInfoDto purchaseDeliveryBatchInfoDto : purchaseDeliveryDirectBatchInfoVoList) {
            //遍历同行单展示
            Attachment attachment = new Attachment();
            attachment.setAttachmentType(SysOptionConstant.ID_460);
            attachment.setAttachmentFunction(SysOptionConstant.ID_4080);
            attachment.setRelatedId(purchaseDeliveryBatchInfoDto.getPurchaseDeliveryDirectBatchInfoId());
            List<Attachment> attachmentList = attachmentService.queryAttachmentList(attachment);
            if (CollectionUtils.isEmpty(attachmentList)) {
                purchaseDeliveryBatchInfoDto.setIsUploadFile(ErpConst.ZERO);
            } else {
                purchaseDeliveryBatchInfoDto.setIsUploadFile(ErpConst.ONE);
            }
        }
        mav.addObject("purchaseDeliveryDirectBatchInfoVoList", purchaseDeliveryDirectBatchInfoVoList);
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 跳转到修改采购订单页面
     *
     * @param buyorder
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月7日 下午1:23:46
     */
    @ResponseBody
    @RequestMapping(value = "saveEditBuyorderPage")
    public ModelAndView saveEditBuyorder(Buyorder buyorder, HttpServletRequest request) {
        ModelAndView mav = new ModelAndView("redirect:/order/buyorder/editAddBuyorderPage.do");
        mav.addObject("buyorderId", buyorder.getBuyorderId());
        return mav;

    }

    @Autowired
    private FlowOrderApiService flowOrderApiService;
    /**
     * <b>Description:</b><br>
     * 保存关闭采购订单
     *
     * @param buyorder
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月7日 下午1:23:46
     */
    @SuppressWarnings("rawtypes")
    @ResponseBody
    @RequestMapping(value = "saveColseBuyorder")
    @SystemControllerLog(operationType = "eidt", desc = "关闭采购订单")
    public ResultInfo saveColseBuyorder(Buyorder buyorder, HttpServletRequest request) {

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        buyorder.setUpdater(user.getUserId());
        buyorder.setModTime(DateUtil.sysTimeMillis());

//		Buyorder buyorderDB = this.buyorderService.getBuyOrderByOrderId(buyorder.getBuyorderId());

        BuyorderVo buyorderDB = buyorderMapper.getBuyorderVoById(buyorder.getBuyorderId());
        
        
        buyorder.setIsNew(buyorderDB.getIsNew());

        // FAQ3323 增加校验，解决多人操作一个订单时 未刷新页面导致的状态错误问题
        boolean closeBuyOrder = checkCloseBuyOrder(buyorderDB);
        if (!closeBuyOrder) {
            return new ResultInfo<>(-1, "采购订单状态已改变，请刷新页面后重试!");
        }
        Integer auditStatus = flowOrderApiService.queryAuditStatus(buyorderDB.getBuyorderNo());
        if (Objects.equals(auditStatus,1)){
            return new ResultInfo<>(-1, "业务流转单已审核，采购单不可关闭");
        }

        //处理采购订单催货预警相关信息
        buyorderService.dealExpeditingByCloseOrder(buyorder.getBuyorderId());

        /**
         * VDERP-10214 【供应链工作台】采购单关闭，系统自动关闭单据内sku的催票任务
         */
        buyorderService.dealUrgeTicketBuyCloseOrder(buyorderDB.getBuyorderNo());

        //是否普发
        boolean isPufa = buyorderDB.getDeliveryDirect() == 0;

        //是否有入库记录
        boolean hasWarehouseInLog = buyorderHasWarehouseInLog(buyorder.getBuyorderId());

        //采购订单是否审核通过
        boolean isValid = buyorderDB.getValidStatus() == 1;


        //如果普发，审核通过且无入库记录
        if (isPufa && isValid && !hasWarehouseInLog) {

            logger.info("采购订单:" + buyorderDB.getBuyorderNo() + " 关闭,请求WMS start==========================");

            try {
                if (!cancelTypeService.cancelInputPurchaseMethod(buyorderDB.getBuyorderNo(), "采购订单关闭需要撤销")) {
                    return new ResultInfo(-1, "物流正在进行货物操作，请等待作业完成后操作，如紧急请直接联系物流部。");
                }
                return buyorderService.saveCloseBuyorder(buyorder);
            } catch (Exception e) {
                logger.error("BuyorderController->saveColseBuyorder:采购订单关闭失败:", e);
                return new ResultInfo<>(-1, "采购订单关闭失败");
            }
        }

        orderReviewProcessService.dealBuyOrderReviewProcess(buyorder.getBuyorderId(), user != null && user.getUserId() != null ? user.getUserId() : 1);

        return buyorderService.saveCloseBuyorder(buyorder);
    }


    // FAQ3323 校验采购单是否可关闭
    // 关闭采购订单的前置条件：
    // 待确认&待审核 || 待确认&审核不通过 || 已生效&未付款&未发货&未收货&未收票&未锁定【规避付款审核中】且非已关闭
    private boolean checkCloseBuyOrder(BuyorderVo buyorderDB) {
        if ((buyorderDB.getStatus() == 0 && Objects.isNull(buyorderDB.getVerifyStatus())) || (buyorderDB.getStatus() == 0 && buyorderDB.getVerifyStatus() == 2)
                || (buyorderDB.getValidStatus() == 1 && buyorderDB.getPaymentStatus() == 0 && buyorderDB.getDeliveryStatus() == 0 && buyorderDB.getArrivalStatus() == 0
                && buyorderDB.getInvoiceStatus() == 0 && buyorderDB.getLockedStatus() == 0 && buyorderDB.getStatus() != 3)) {
            return true;
        }
        return false;
    }


    /**
     * 采购单是否有对应的入库记录
     *
     * @param buyorderId
     * @return
     */
    private boolean buyorderHasWarehouseInLog(Integer buyorderId) {

        List<BuyorderGoodsVo> buyorderGoodsVoList = this.buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorderId);

        List<Integer> buyOrderGoodIdList = buyorderGoodsVoList.stream().map(good -> good.getBuyorderGoodsId()).collect(Collectors.toList());

        List<WarehouseGoodsOperateLog> inputWarehouseLogList = warehouseGoodsOperateLogMapper.getInputWarehouseLog(buyOrderGoodIdList);

        return CollectionUtils.isEmpty(inputWarehouseLogList) ? false : true;
    }

    /**
     * <b>Description:</b><br>
     * 通过采购单号查询采购单详情
     *
     * @param request
     * @param buyorder
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月8日 下午5:08:38
     */
    @SuppressWarnings("all")
    @ResponseBody
    @RequestMapping(value = "getBuyorderByBuyorderNo")
    public ResultInfo getBuyorderByBuyorderNo(HttpServletRequest request, Buyorder buyorder) throws IOException {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        BuyorderVo bv = buyorderService.getBuyorderVoDetail(buyorder, user);
        if (bv != null) {
            return new ResultInfo(0, saveBeforeParamToRedis(JsonUtils.translateToJson(bv)), bv);
        } else {
            return new ResultInfo(1, "订单不存在");
        }

    }

    /**
     * <b>Description:</b><br>
     * 保存加入已有的采购订单
     *
     * @param buyorder
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月9日 上午10:27:32
     */
    @ResponseBody
    @RequestMapping(value = "saveAddHavedBuyorder")
    @SystemControllerLog(operationType = "edit", desc = "加入已有的采购订单")
    public ModelAndView saveAddHavedBuyorder(Buyorder buyorder, HttpServletRequest request,
                                             @RequestParam(required = false, value = "buySum") String[] buysums,
                                             @RequestParam(required = false, value = "referPriceStrs") String[] referPriceStrs,
                                             @RequestParam(required = false, value = "sendGoodsTimeStr") String[] sendGoodsTimeStrs,
                                             @RequestParam(required = false, value = "receiveGoodsTimeStr") String[] receiveGoodsTimeStrs,
                                             @RequestParam(required = false, value = "dbBuyNum") String[] dbbuysums,
                                             @RequestParam(required = false, value = "saleorderNos") String[] saleorderNos, String beforeParams,
                                             @RequestParam(required = false, value = "skuNos") String skuNos) {

        // VDERP-9808 加入已有的采购单时 专项发货对应采购单校验
        BuyorderVo buyorderVo = new BuyorderVo();
        BeanUtils.copyProperties(buyorder, buyorderVo);
        ResultInfo resultInfo = buyorderService.checkBuyorderSpecial(buyorderVo, dbbuysums);
        if (resultInfo.getCode() != 0) {
            ModelAndView mav = new ModelAndView("common/fail");
            mav.addObject("message", "专项发货不可混单采购,无法创建采购单");
            return mav;
        }

        Map<String, Object> map = new HashMap<>();
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Buyorder bo = new Buyorder();
        bo.setBuyorderId(buyorder.getBuyorderId());
        bo.setUpdater(user.getUserId());
        bo.setModTime(DateUtil.sysTimeMillis());
        bo.setIsNew(buyorder.getIsNew());
        map.put("buyorder", bo);
        if (buysums != null && buysums.length > 0) {
            String buysum = "";
            for (int i = 0; i < buysums.length; i++) {
                buysum += buysums[i] + ",";

            }
            map.put("buysums", buysum);
        }
        // 参考数量
        if (referPriceStrs != null && referPriceStrs.length > 0) {
            String referPriceStr = "";
            for (int i = 0; i < referPriceStrs.length; i++) {
                referPriceStr += referPriceStrs[i] + ",";
            }
            map.put("referPriceStrs", referPriceStr);
        }
        if (sendGoodsTimeStrs != null && sendGoodsTimeStrs.length > 0) {
            String sendGoodsTimeStr = "";
            for (int i = 0; i < sendGoodsTimeStrs.length; i++) {
                sendGoodsTimeStr += sendGoodsTimeStrs[i] + ",";
            }
            map.put("sendGoodsTimeStrs", sendGoodsTimeStr);
        }
        if (receiveGoodsTimeStrs != null && receiveGoodsTimeStrs.length > 0) {
            String receiveGoodsTimeStr = "";
            for (int i = 0; i < receiveGoodsTimeStrs.length; i++) {
                receiveGoodsTimeStr += receiveGoodsTimeStrs[i] + ",";

            }
            map.put("receiveGoodsTimeStrs", receiveGoodsTimeStr);
        }
        if (saleorderNos != null && saleorderNos.length > 0) {
            String saleorderNoStr = "";
            for (int i = 0; i < saleorderNos.length; i++) {
                saleorderNoStr += saleorderNos[i] + ",";
            }
            map.put("saleorderNoStr", saleorderNoStr);
        }

        if (buysums != null && buysums.length > 0) {
            String dbbuysum = "";
            for (int i = 0; i < dbbuysums.length; i++) {
                dbbuysum += dbbuysums[i] + ",";
            }
            map.put("dbbuysums", dbbuysum);
        }
        logger.info("referPriceStrs :" + map.get("referPriceStrs"));
        logger.info("sendGoodsTimeStrs :" + map.get("sendGoodsTimeStrs"));
        logger.info("receiveGoodsTimeStrs :" + map.get("receiveGoodsTimeStrs"));
        ResultInfo<?> res = buyorderService.saveAddHavedBuyorder(map);

        if (res != null && res.getCode() == 0) {
            // 发送站内信 buyOrderGoodsIdList
            Map data = (Map) res.getData();
            // 成功后会返回对应修改的采购商品编码
            List<Integer> buyOrderGoodsIdList = (List<Integer>) data.get("buyOrderGoodsIdList");
            HashSet<Integer> buyOrderGoodsIdSet = new HashSet<>(buyOrderGoodsIdList);
            for (Integer buyOrderGoodsIdKey : buyOrderGoodsIdSet) {
                logger.info("buyOrderGoodsIdKey" + buyOrderGoodsIdKey);
                // 获取当前采购商品对应采购单信息
                Buyorder buyOrderDetail = buyorderGoodsMapper.selectBuyorderByBuyOrderGoodsId(Integer.valueOf(buyOrderGoodsIdKey));
                logger.info("buyOrderDetail :" + buyOrderDetail);
                if (buyOrderDetail == null) {
                    continue;
                }
                // 过滤 非VP、 对应销售单商品已经全部发货
                List<Saleorder> validSaleOrderNos = buyorderGoodsMapper.selectValidSaleOrderByBuyOrderGoodsId(Integer.valueOf(buyOrderGoodsIdKey));
                if (validSaleOrderNos == null || validSaleOrderNos.size() == 0) {
                    continue;
                }
                // 加入 <==> 新增
                Map<String, String> mapParams = new HashMap<>();
                for (Saleorder saleOrderItem : validSaleOrderNos) {
                    // 根据销售单编码查找对应销售
                    List<Integer> saleOrderTraderIds = buyorderGoodsMapper.selectSaleOrderTraderIdsBySaleOrderId(saleOrderItem.getSaleorderId());
                    saleOrderTraderIds = saleOrderTraderIds.stream().filter(ObjectUtils::notEmpty).collect(Collectors.toList());
                    if (!ObjectUtils.allNotNull(saleOrderItem)) {
                        continue;
                    }
                    mapParams.put("saleOrderNo", saleOrderItem.getSaleorderNo());
                    MessageUtil.sendMessage2(171, saleOrderTraderIds, mapParams, "./order/saleorder/view.do?saleorderId=" + saleOrderItem.getSaleorderId().intValue(), user.getUsername());
                }

            }

            ModelAndView mav = new ModelAndView("redirect:/order/buyorder/viewBuyorder.do");

            //修改采购单中商品的价格
            mav.addObject("skuNos", skuNos);

            updateBuyOrderGoodsPrice(skuNos, buyorder.getBuyorderId());

            mav.addObject("buyorderId", buyorder.getBuyorderId());
            return mav;
        } else {
            ModelAndView mav = new ModelAndView("common/fail");
            mav.addObject("message", res.getMessage());
            return mav;
        }
    }

    /**
     * 更新采购单商品价格
     *
     * @param skuNos
     * @param buyorderId
     */
    private void updateBuyOrderGoodsPrice(String skuNos, Integer buyorderId) {

        Buyorder buyorder = this.buyorderService.getBuyOrderByOrderId(buyorderId);

        if (StringUtil.isEmpty(skuNos)) {
            return;
        }

        String[] skuNoArray = skuNos.split(",");

        for (int i = 0; i < skuNoArray.length; i++) {

            SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = this.basePriceService.findSkuPriceInfoBySkuNo(skuNoArray[i]);
            //无sku对应的采购信息 跳过
            if (skuPriceInfoDetailResponseDto == null) {
                continue;
            }

            SkuPriceInfoPurchaseDto priceInfoPurchaseDto = skuPriceInfoDetailResponseDto.getPurchaseList().stream().filter(skuPriceInfoPurchaseDto -> {
                return buyorder.getTraderId().equals(skuPriceInfoPurchaseDto.getTraderId().intValue());
            }).findFirst().orElse(null);

            //没有对应供应商的采购价
            if (priceInfoPurchaseDto == null) {
                continue;
            }

            //更新对应的采购价
            this.buyorderGoodsMapper.updatePriceByBuyorderIdAndSkuNo(buyorderId, skuNoArray[i], priceInfoPurchaseDto.getPurchasePrice());
        }
    }

    /**
     * <b>Description:</b><br>
     * 新增快递
     *
     * @param session
     * @param buyorderVo
     * @return
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年8月29日 下午3:54:40
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addExpress")
    public ModelAndView addExpress(HttpSession session, BuyorderVo buyorderVo) {
        ModelAndView mv = new ModelAndView("order/buyorder/add_express");
        User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
        BuyorderVo buyorderInfo = buyorderService.getBuyOrderInfoForAddExpress(buyorderVo);
        // 物流信息
        Express express = new Express();
        express.setBusinessType(SysOptionConstant.ID_515);
        Map<Integer, Object> map = new HashMap<>();
        List<Integer> relatedIds = new ArrayList<>();
        for (BuyorderGoodsVo buyorderGoodsVo : buyorderInfo.getBuyorderGoodsVoList()) {
            // 拼接关联ID的组
            relatedIds.add(buyorderGoodsVo.getBuyorderGoodsId());
            // 准备计算所有快递单中产品数量
            map.put(buyorderGoodsVo.getBuyorderGoodsId(), 0);
        }
        String nowTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
        mv.addObject("nowTime", nowTime);
        express.setRelatedIds(relatedIds);
        // 获取物流公司列表
        List<Logistics> logisticsList = logisticsService.getLogisticsList(session_user.getCompanyId());
        try {
            List<Express> expressList = expressService.getExpressList(express);
            if (null != expressList) {
                for (Express e : expressList) {
                    if (null != e.getExpressDetail()) {
                        // 循环计算每件产品发货数量
                        for (ExpressDetail ed : e.getExpressDetail()) {
                            Integer num = 0;
                            num = (Integer) map.get(ed.getRelatedId());
                            num = num + ed.getNum();
                            map.put(ed.getRelatedId(), num);
                        }
                    }
                }
            }

            String json = new Gson().toJson(logisticsList.stream().map(model -> {
                Map<String, Object> jsonmap = new HashMap<>();
                jsonmap.put("value", model.getLogisticsId());
                jsonmap.put("name", model.getName());
                return jsonmap;
            }).collect(Collectors.toList()));
            mv.addObject("json", json);
            mv.addObject("expressNumMap", map);
            mv.addObject("expressList", expressList);
        } catch (Exception e) {
            logger.error("buy order addExpress:", e);
        }
        mv.addObject("logisticsList", logisticsList);
        mv.addObject("buyorderInfo", buyorderInfo);
        // 判断直发普发 普发不展示
        Buyorder delivery_direct_item = buyorderMapper.selectByPrimaryKey(buyorderVo.getBuyorderId());
        mv.addObject("delivery_direct_item", delivery_direct_item);
        return mv;
    }

    /**
     * <b>Description:</b><br>
     *
     * @param session
     * @param buyorderVo
     * @param express
     * @param beforeParams
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年9月12日 下午5:37:12
     */
    @ResponseBody
    @RequestMapping(value = "/editExpress")
    public ModelAndView editExpress(HttpSession session, BuyorderVo buyorderVo, Express express) {
        ModelAndView mv = new ModelAndView();
        // 获取session中user信息
        User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
        BuyorderVo buyorderInfo = buyorderService.getBuyorderVoDetail(buyorderVo, session_user);
        List<Integer> relatedIds = new ArrayList<Integer>();
        // 物流信息
        Map<Integer, Object> map = new HashMap<Integer, Object>();
        Map<Integer, Object> oldmap = new HashMap<Integer, Object>();
        Express oldExpress = new Express();
        oldExpress.setBusinessType(SysOptionConstant.ID_515);
        for (BuyorderGoodsVo buyorderGoodsVo : buyorderInfo.getBuyorderGoodsVoList()) {
            // 拼接关联ID的组
            relatedIds.add(buyorderGoodsVo.getBuyorderGoodsId());
            // 准备计算所有快递单中产品数量
            map.put(buyorderGoodsVo.getBuyorderGoodsId(), 0);
            oldmap.put(buyorderGoodsVo.getBuyorderGoodsId(), 0);
        }
        oldExpress.setRelatedIds(relatedIds);
        // 获取物流公司列表
        List<Logistics> logisticsList = logisticsService.getLogisticsList(session_user.getCompanyId());
        try {
            List<Express> expressList = expressService.getExpressList(express);
            List<Express> oldExpressList = expressService.getExpressList(oldExpress);
            if (null != oldExpressList) {
                for (Express e : oldExpressList) {
                    if (null != e.getExpressDetail()) {
                        // 循环计算每件产品发货数量
                        for (ExpressDetail ed : e.getExpressDetail()) {
                            Integer num = 0;
                            num = (Integer) oldmap.get(ed.getRelatedId());
                            num = num + ed.getNum();
                            oldmap.put(ed.getRelatedId(), num);
                        }
                    }
                }
            }
            if (null != expressList.get(0)) {
                if (null != expressList.get(0).getExpressDetail()) {
                    // 循环计算每件产品发货数量
                    for (ExpressDetail ed : expressList.get(0).getExpressDetail()) {
                        Integer num = 0;
                        num = (Integer) map.get(ed.getRelatedId());
                        num = num + ed.getNum();
                        map.put(ed.getRelatedId(), num);
                    }
                }
            }
            mv.addObject("allExpressNumMap", oldmap);
            mv.addObject("expressNumMap", map);
            mv.addObject("expressList", expressList.get(0));
            mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(expressList.get(0))));

            String json = new Gson().toJson(logisticsList.stream().map(model -> {
                Map<String, Object> jsonmap = new HashMap<>();
                jsonmap.put("value", model.getLogisticsId());
                jsonmap.put("name", model.getName());
                if(expressList.get(0).getLogisticsId()!=null && expressList.get(0).getLogisticsId() == model.getLogisticsId()){
                    jsonmap.put("selected", true);
                }

                return jsonmap;
            }).collect(Collectors.toList()));
            mv.addObject("json", json);
        } catch (Exception e) {
            logger.error("buy order editExpress:", e);
        }
        mv.addObject("logisticsList", logisticsList);
        mv.addObject("buyorderInfo", buyorderInfo);
        mv.setViewName("order/buyorder/edit_express");
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 保存新增快递信息
     *
     * @param express
     * @param deliveryTimes
     * @param amount
     * @param id_num_price
     * @return
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年8月30日 下午1:23:53
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveAddExpress")
//    @SystemControllerLog(operationType = "add", desc = "保存新增快递信息")
    public ResultInfo saveAddExpress(BigDecimal amount,Express express, String deliveryTimes,  String id_num_price,
                                      String id_sendN_sendedN_sumN,BuyorderVo buyorderVo) {
        logger.info("保存新增快递信息");
        ResultInfo<?> result = new ResultInfo<>();
        // 获取session中user信息
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        // 准备express中的expressDetailList
        List<ExpressDetail> expressDetailList = new ArrayList<>();
        // 准备提示销售订单下所有采购单对应商品集合
        List<Integer> buyOrderGoodsIdList = new ArrayList<>();
        //科研购公众号推送Map
        Map<String, Object> sMap = new HashMap<>();
        sMap.put("logisticNo", express.getLogisticsNo());
        //获取快递公司
        Logistics logistics = logisticsService.getLogisticsById(express.getLogisticsId());
        sMap.put("logisticName", logistics.getName());
        sMap.put("logisticTime", deliveryTimes);
        if (null != id_num_price) {
            // 切割RelatedId和num拼接成的字符串
            String[] params = id_num_price.split("_");
            logger.info(Arrays.toString(params));
            // 单价
            int price = 0;
            // 所有产品总价
            int allPrice = 0;
            // 已经分配过的金额
            double allAmount = 0.00;
            // 每种产品平摊的运费
            double expressDetailAmount;

            for (String s : params) {
                String[] bid_num = s.split("\\|");
                if (null != bid_num[1] && null != bid_num[2]) {
                    // 数量*金额
                    allPrice += Integer.parseInt(bid_num[1]) * Double.valueOf(bid_num[2]).intValue();
                }
                if (bid_num[0] != null) {
                    buyOrderGoodsIdList.add(Integer.valueOf(bid_num[0]));
                }
            }
            Integer goodTypeSum = 0;
            String goodName = "";
            int goodNum = 0;
            for (int j = 0; j < params.length; j++) {
                goodTypeSum++;
                String[] bid_num = params[j].split("\\|");
                ExpressDetail expressDetail = new ExpressDetail();
                if (null != bid_num[0]) {
                    // 关联字段
                    expressDetail.setRelatedId(Integer.parseInt(bid_num[0]));
                    if ("".equals(goodName)) {
                        BuyorderGoods buyorderGoods = buyorderGoodsMapper.selectByPrimaryKey(Integer.parseInt(bid_num[0]));
                        goodName = buyorderGoods.getGoodsName();
                    }
                }
                if (null != bid_num[1]) {
                    // 数量
                    goodNum += Integer.parseInt(bid_num[1]);
                    expressDetail.setNum(Integer.parseInt(bid_num[1]));
                }
                if (null != bid_num[2]) {
                    // 单价
                    price = Double.valueOf(bid_num[2]).intValue();
                }
                // 最后一个产品时
                if (null != amount) {
                    if (j == (params.length - 1)) {
                        expressDetailAmount = amount.doubleValue() - allAmount;
                    } else {
                        // 运费价格*（产品单价*产品数量）/所有产品总价 保留两位小数
                        allAmount += (double) Math.round(amount.doubleValue()
                                * (Integer.parseInt(bid_num[1]) * Double.valueOf(bid_num[2]).intValue()) / allPrice
                                * 100) / 100;
                        expressDetailAmount = (double) Math.round(amount.doubleValue()
                                * (Integer.parseInt(bid_num[1]) * Double.valueOf(bid_num[2]).intValue()) / allPrice
                                * 100) / 100;
                    }
                    BigDecimal eda = new java.math.BigDecimal(expressDetailAmount);
                    eda = eda.setScale(2, BigDecimal.ROUND_HALF_UP);

                    // 金额
                    expressDetail.setAmount(eda);
                }
                // 业务类型
                expressDetail.setBusinessType(SysOptionConstant.ID_515);
                expressDetailList.add(expressDetail);
            }
            sMap.put("goodTypeSum", goodTypeSum);
            sMap.put("goodName", goodName);
            sMap.put("goodNum", goodNum);

        }
        express.setAddTime(DateUtil.sysTimeMillis());
        express.setCreator(currentUser.getId());
        express.setUpdater(currentUser.getId());
        express.setModTime(DateUtil.sysTimeMillis());
        express.setDeliveryTime(DateUtil.convertLong(deliveryTimes, "yyyy-MM-dd"));
        express.setExpressDetail(expressDetailList);
        express.setIsEnable(1);
        express.setCompanyId(1);
        express.setBusinessType(SysOptionConstant.ID_515);
        //如果是直发采购单
        if(1 == buyorderMapper.selectDeliveryDirect(buyorderVo.getBuyorderId())){
            express.setEnableReceive(0);
        }
        try {

            // 站内消息提示
            if (buyOrderGoodsIdList.size() > 0) {
                for (Integer buyOrderGoodsIdKey : buyOrderGoodsIdList) {
                    logger.info("buyOrderGoodsIdKey" + buyOrderGoodsIdKey);
                    // 获取当前采购商品对应采购单信息
                    Buyorder buyOrderDetail = buyorderGoodsMapper.selectBuyorderByBuyOrderGoodsId(buyOrderGoodsIdKey);
                    logger.info("buyOrderDetail :" + buyOrderDetail);
                    if (buyOrderDetail == null) {
                        continue;
                    }
                    // 过滤 非VP、 对应销售单商品已经全部发货
                    List<Saleorder> validSaleOrderNos = buyorderGoodsMapper.selectValidSaleOrderByBuyOrderGoodsId(buyOrderGoodsIdKey);
                    if (validSaleOrderNos == null || validSaleOrderNos.size() == 0) {
                        continue;
                    }

                    // 获取当前发货数/之前已发货数/总数量
                    int sendNum = 0;
                    int sendedNum = 0;
                    int sumNum = 0;
                    if (id_sendN_sendedN_sumN != null) {
                        String[] idSendSumNums = id_sendN_sendedN_sumN.split("_");
                        for (String idSendSumNum : idSendSumNums) {
                            String[] bid_num = idSendSumNum.split("\\|");
                            if (bid_num.length < 4) {
                                continue;
                            }
                            if (bid_num[0] != null && buyOrderGoodsIdKey.equals(Integer.valueOf(bid_num[0]))) {
                                if (bid_num[1] != null && bid_num[2] != null && bid_num[3] != null) {
                                    sendNum = Integer.valueOf(bid_num[1]);
                                    sendedNum = Integer.valueOf(bid_num[2]);
                                    sumNum = Integer.valueOf(bid_num[3]);
                                }
                                break;
                            }
                        }
                    }

                    Map<String, String> mapParams = new HashMap<>();
                    if (Integer.valueOf(0).equals(buyOrderDetail.getDeliveryDirect())) {
                        for (Saleorder saleOrderItem : validSaleOrderNos) {
                            // 根据销售单编码查找对应销售
                            List<Integer> saleOrderTraderIds = buyorderGoodsMapper.selectSaleOrderTraderIdsBySaleOrderId(saleOrderItem.getSaleorderId());
                            saleOrderTraderIds = saleOrderTraderIds.stream().filter(ObjectUtils::notEmpty).collect(Collectors.toList());
                            if (!ObjectUtils.allNotNull(saleOrderItem)) {
                                continue;
                            }
                            mapParams.put("saleOrderNo", saleOrderItem.getSaleorderNo());
                            mapParams.put("buyOrderNo", buyOrderDetail.getBuyorderNo());
                            MessageUtil.sendMessage2(169, saleOrderTraderIds, mapParams, "./order/saleorder/view.do?saleorderId=" + saleOrderItem.getSaleorderId(), currentUser.getUsername());
                        }
                        // 更新采购单商品进程描述
                        buyorderGoodsMapper.updateProcessDescByBuyOrderGoodsId(buyOrderGoodsIdKey, "供应商已发货至仓库(" + (sendNum + sendedNum) + "/" + (sumNum - sendNum - sendedNum) + ")");
                    } else if (Integer.valueOf(1).equals(buyOrderDetail.getDeliveryDirect())) {
                        List<Integer> salesId = new ArrayList<>();
                        for (Saleorder saleOrderItem : validSaleOrderNos) {
                            // 根据销售单编码查找对应销售
                            List<Integer> saleOrderTraderIds = buyorderGoodsMapper.selectSaleOrderTraderIdsBySaleOrderId(saleOrderItem.getSaleorderId());
                            saleOrderTraderIds = saleOrderTraderIds.stream().filter(ObjectUtils::notEmpty).collect(Collectors.toList());
                            if (!ObjectUtils.allNotNull(saleOrderItem)) {
                                continue;
                            }
                            mapParams.put("saleOrderNo", saleOrderItem.getSaleorderNo());
                            mapParams.put("buyOrderNo", buyOrderDetail.getBuyorderNo());
                            MessageUtil.sendMessage2(168, saleOrderTraderIds, mapParams, "./order/saleorder/view.do?saleorderId=" + saleOrderItem.getSaleorderId(), currentUser.getUsername());
                            try {
                                //VDERP-8189【科研购公众号】科研购订单发货推送公众号消息提醒客户
                                if (!salesId.contains(saleOrderItem.getSaleorderId())) {
                                    //baseService.sendKygDeliveryOrder(saleOrderItem, sMap);
                                    salesId.add(saleOrderItem.getSaleorderId());
                                }
                            } catch (Exception e) {
                                logger.error("科研购直发推送信息失败，异常信息,", e);
                            }

                            //埋点
                            addTrackDelivery(saleOrderItem.getSaleorderNo(),express.getLogisticsNo(),saleOrderItem.getTraderId());
                        }
                        // 更新采购单商品进程描述
                        buyorderGoodsMapper.updateProcessDescByBuyOrderGoodsId(buyOrderGoodsIdKey,
                                "供应商已发货(" + (sendNum + sendedNum) + "/" + (sumNum - sendNum - sendedNum) + ")");

                    }
                }
            }

            result = expressService.saveExpress(express);
            if (result == null || result.getCode() == -1) {
                return new ResultInfo<>();
            }

            //更新采购单发货状态 VDERP-2431
            buyorderService.updateBuyorderDeliveryStatus(buyorderVo.getBuyorderId());

            //VDERP-14182 更新采购订单ExpressEnableReceive
            if(1 == buyorderMapper.selectDeliveryDirect(buyorderVo.getBuyorderId())){
                buyorderService.checkExpressEnableReceive(buyorderVo.getBuyorderId());
            }

            // VDERP-8759 订单流发货状态同步
            buyorderInfoSyncService.syncDeliveryStatus(buyorderVo.getBuyorderId(), 2);

            // 更新采购单关联的直属费用单的发货状态
            BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.queryAttributiveExpenseInfoByBuyorderId(buyorderVo.getBuyorderId());
            if (Objects.nonNull(buyorderExpenseDto)) {
                buyorderExpenseApiService.doDeliveryStatus(buyorderExpenseDto.getBuyorderExpenseId());
                log.info("直发采购单新增物流信息同步更新直属费用单发货状态，采购费用单：{}", JSON.toJSONString(buyorderExpenseDto));
            }
            //更新赠品单状态
            buyorderService.updateGiftOrderStatus(buyorderVo.getBuyorderId());

            pushExpressToWeb((Express) result.getData(), buyorderVo.getBuyorderId(), id_num_price, "保存");

            //采购添加快递处理直发订单账期编码监管
            buyorderService.dealDirectOrderPeriodManagement((Express) result.getData(), buyorderVo.getBuyorderId(), ErpConst.OperateType.INSERT_OPERATE);
        } catch (Exception e) {
            logger.error("saveAddExpress:", e);
        }

        /**
         * 推送快递信息至base服务
         */
        Express expressData = (Express) result.getData();
        String phone = "";
        if (expressData != null) {
            phone = expressService.getPhoneByBusinessType(expressData.getExpressId(), express.getLogisticsNo());
        }
        logger.info("开始推送快递信息至base服务,单号:{}", express.getLogisticsNo());
        logisticsService.pushLogisticsToBase(express.getLogisticsNo()
                , logisticsService.getLogisticsCodeByLogisticsId(express.getLogisticsId())
                , phone);
        return result;
    }

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    private void addTrackDelivery( String orderNo,String logisticsNo, Integer traderId) {
        EventTrackingEnum eventTrackingEnum = EventTrackingEnum.SALE_ORDER_DELIVER;
        try {
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            trackParams.put("traderId",traderId);
            trackParams.put("orderNo",orderNo);
            trackParams.put("logisticsNo",logisticsNo);
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(eventTrackingEnum);
            trackParamsData.setEventTrackingEnum(eventTrackingEnum);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackStrategy.track(trackParamsData);
        }catch(Exception e) {
            logger.error("埋点：{}，失败，不影响正常业务",eventTrackingEnum.getArchivedName(),e);
        }
    }


//	@Async // 异步

    /**
     * TODO 两边同步修改
     *
     * @param reExpress
     * @param buyOrderId
     * @param id_num_price_sku
     * @param action
     * @see BuyorderServiceImpl#pushExpressToWeb(com.vedeng.logistics.model.Express, java.lang.Integer, java.lang.String, java.lang.String)
     */
    public void pushExpressToWeb(Express reExpress, Integer buyOrderId, String id_num_price_sku, String action) {
        if (reExpress == null) {
            logger.info("保存物流信息失败");
            return;
        }
        BuyorderVo buyorderVo = buyorderService.getBuyOrderVoForDeliveryDirect(buyOrderId);
        if (null == buyorderVo) {
            // 非直发销售单
            return;
        }
        Integer saleOrderId = buyorderVo.getSaleorderId();
        if (null != saleOrderId && 0 != saleOrderId) {
            LogisticsOrderData logisticsOrderData = new LogisticsOrderData();
            List<LogisticsOrderGoodsData> logisticsOrderGoodsDataList = new ArrayList<>();
            int countZeroSum = 0;
            String[] params = id_num_price_sku.split("_");
            if (params.length > 0) {
                for (String line : params) {
                    String[] rows = line.split("\\|");
                    LogisticsOrderGoodsData logisticsOrderGoodsData = new LogisticsOrderGoodsData();
                    if (null != rows[1]) {
                        int skuN = Integer.parseInt(rows[1]);
                        if (skuN == 0) {
                            countZeroSum += 1;
                            continue;
                        }
                        //数量放到rabbitmq数据
                        logisticsOrderGoodsData.setNum(skuN);
                    }
                    if (null != rows[3]) {
                        //放到rabbitmq数据
                        logisticsOrderGoodsData.setSkuNo(rows[3]);
                    }
                    //rabbitmq需要发送的商品数据
                    logisticsOrderGoodsDataList.add(logisticsOrderGoodsData);
                }
                logisticsOrderData.setOrderGoodsLogisticsDataList(logisticsOrderGoodsDataList);
                // 推送前端
                logger.info("采购单包裹信息推送 reExpress:{},saleOrderId:{},logisticsOrderData:{}",
                        JSON.toJSONString(reExpress), saleOrderId, JSON.toJSONString(logisticsOrderData));
                expressService.pushExpressToWeb(reExpress, "1", saleOrderId, countZeroSum, logisticsOrderData, action, true);
            }
        }


    }


    /**
     * <b>Description:</b><br>
     * 保存编辑快递信息
     *
     * @param express
     * @param deliveryTimes
     * @param amount
     * @param id_num_price
     * @param session
     * @return
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年8月30日 下午1:23:53
     */
    @ResponseBody
    @RequestMapping(value = "/saveEditExpress")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑快递信息")
    public ResultInfo saveEditExpress(HttpServletRequest request, Express express, String deliveryTimes,
                                      BigDecimal amount, String id_num_price, String beforeParams, BuyorderVo buyorderVo, HttpSession session, String id_sendN_sendedN_sumN) {
        ResultInfo<?> result = new ResultInfo<>();

        // 校验签收状态
        if (Objects.isNull(express) || Objects.isNull(express.getExpressId())) {
            return null;
        }
        Express currentExpress = expressService.getExpressInfoByPrimaryKey(express.getExpressId());
        if (!Objects.isNull(currentExpress) && ErpConst.TWO.equals(currentExpress.getArrivalStatus())) {
            return ResultInfo.error("当前快递" + currentExpress.getLogisticsNo() + "已签收，禁止编辑");
        }
        //科研购公众号推送Map
        Map<String, Object> sMap = new HashMap<>();
        sMap.put("logisticNo", express.getLogisticsNo());
        //获取快递公司
        Logistics logistics = logisticsService.getLogisticsById(express.getLogisticsId());
        sMap.put("logisticName", logistics.getName());
        sMap.put("logisticTime", deliveryTimes);
        // 获取session中user信息
        User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
        // 准备express中的expressDetailList
        List<ExpressDetail> expressDetailList = new ArrayList<ExpressDetail>();
        // 准备提示销售订单下所有采购单对应商品集合
        List<Integer> buyOrderGoodsIdList = new ArrayList<>();
        if (null != id_num_price) {
            // 切割RelatedId和num拼接成的字符串
            String[] params = id_num_price.split("_");
            // 单价
            Integer price = 0;
            // 所有产品总价
            Integer allPrice = 0;
            // 已经分配过的金额
            Double allAmount = 0.00;
            // 每种产品平摊的运费
            Double expressDetailAmount = 0.00;
            if (null != params) {
                for (String s : params) {
                    String[] bid_num = s.split("\\|");
                    if (null != bid_num[1] && null != bid_num[2]) {
                        // 数量*金额
                        allPrice += Integer.parseInt(bid_num[1]) * Double.valueOf(bid_num[2]).intValue();
                    }
                    if (bid_num[0] != null) {
                        buyOrderGoodsIdList.add(Integer.valueOf(bid_num[0]));
                    }
                }
                Integer goodTypeSum = 0;
                String goodName = "";
                Integer goodNum = 0;
                for (int j = 0; j < params.length; j++) {
                    goodTypeSum++;
                    String[] bid_num = params[j].split("\\|");
                    ExpressDetail expressDetail = new ExpressDetail();
                    if (null != bid_num[0]) {
                        // 关联字段
                        expressDetail.setRelatedId(Integer.parseInt(bid_num[0]));
                        if (goodName.equals("")) {
                            BuyorderGoods buyorderGoods = buyorderGoodsMapper.selectByPrimaryKey(Integer.parseInt(bid_num[0]));
                            goodName = buyorderGoods.getGoodsName();
                        }
                    }
                    if (null != bid_num[1]) {
                        // 数量
                        goodNum += Integer.parseInt(bid_num[1]);
                        expressDetail.setNum(Integer.parseInt(bid_num[1]));
                    }
                    if (null != bid_num[2]) {
                        // 单价
                        price = Double.valueOf(bid_num[2]).intValue();
                    }
                    // 最后一个产品时
                    if (null != amount) {
                        if (j == (params.length - 1)) {
                            expressDetailAmount = amount.doubleValue() - allAmount;
                        } else {
                            // 运费价格*（产品单价*产品数量）/所有产品总价 保留两位小数
                            allAmount += (double) Math.round(amount.doubleValue()
                                    * (Integer.parseInt(bid_num[1]) * Double.valueOf(bid_num[2]).intValue()) / allPrice
                                    * 100) / 100;
                            expressDetailAmount = (double) Math.round(amount.doubleValue()
                                    * (Integer.parseInt(bid_num[1]) * Double.valueOf(bid_num[2]).intValue()) / allPrice
                                    * 100) / 100;
                        }
                        BigDecimal eda = new java.math.BigDecimal(expressDetailAmount);
                        eda = eda.setScale(2, BigDecimal.ROUND_HALF_UP);

                        // 金额
                        expressDetail.setAmount(eda);
                    }
                    // 业务类型
                    expressDetail.setBusinessType(SysOptionConstant.ID_515);
                    expressDetailList.add(expressDetail);
                }
                sMap.put("goodTypeSum", goodTypeSum);
                sMap.put("goodName", goodName);
                sMap.put("goodNum", goodNum);
            }
        }
        express.setAddTime(DateUtil.sysTimeMillis());
        express.setCreator(session_user.getUserId());
        express.setUpdater(session_user.getUserId());
        express.setModTime(DateUtil.sysTimeMillis());
        express.setDeliveryTime(DateUtil.convertLong(deliveryTimes, "yyyy-MM-dd"));
        express.setExpressDetail(expressDetailList);
        express.setIsEnable(1);
        express.setBusinessType(SysOptionConstant.ID_515);
        if(1 == buyorderMapper.selectDeliveryDirect(buyorderVo.getBuyorderId())){
            express.setEnableReceive(0);
        }
        try {
            // 站内消息提示
            if (buyOrderGoodsIdList.size() > 0) {
                for (Integer buyOrderGoodsIdKey : buyOrderGoodsIdList) {
                    logger.info("buyOrderGoodsIdKey" + buyOrderGoodsIdKey);
                    // 获取当前采购商品对应采购单信息
                    Buyorder buyOrderDetail = buyorderGoodsMapper.selectBuyorderByBuyOrderGoodsId(buyOrderGoodsIdKey);
                    logger.info("buyOrderDetail :" + buyOrderDetail);
                    if (buyOrderDetail == null) {
                        continue;
                    }
                    // 过滤 非VP、 对应销售单商品已经全部发货
                    List<Saleorder> validSaleOrderNos = buyorderGoodsMapper.selectValidSaleOrderByBuyOrderGoodsId(Integer.valueOf(buyOrderGoodsIdKey));
                    if (validSaleOrderNos == null || validSaleOrderNos.size() == 0) {
                        continue;
                    }
                    // 获取当前发货数/之前已发货数/总数量
                    int sendNum = 0;
                    int sendedNum = 0;
                    int sumNum = 0;
                    if (id_sendN_sendedN_sumN != null) {
                        String[] idSendSumNums = id_sendN_sendedN_sumN.split("_");
                        for (String idSendSumNum : idSendSumNums) {
                            String[] bid_num = idSendSumNum.split("\\|");
                            if (bid_num.length < 4) {
                                continue;
                            }
                            if (bid_num[0] != null && buyOrderGoodsIdKey.equals(Integer.valueOf(bid_num[0]))) {
                                if (bid_num[1] != null && bid_num[2] != null && bid_num[3] != null) {
                                    sendNum = Integer.valueOf(bid_num[1]);
                                    sendedNum = Integer.valueOf(bid_num[2]);
                                    sumNum = Integer.valueOf(bid_num[3]);
                                }
                                break;
                            }
                        }
                    }
                    Map<String, String> mapParams = new HashMap<>();
                    // 普发
                    if (Integer.valueOf(0).equals(buyOrderDetail.getDeliveryDirect())) {
                        for (Saleorder saleOrderItem : validSaleOrderNos) {
                            // 根据销售单编码查找对应销售
                            List<Integer> saleOrderTraderIds = buyorderGoodsMapper.selectSaleOrderTraderIdsBySaleOrderId(saleOrderItem.getSaleorderId());
                            saleOrderTraderIds = saleOrderTraderIds.stream().filter(ObjectUtils::notEmpty).collect(Collectors.toList());
                            if (!ObjectUtils.allNotNull(saleOrderItem)) {
                                continue;
                            }
                            mapParams.put("saleOrderNo", saleOrderItem.getSaleorderNo());
                            mapParams.put("buyOrderNo", buyOrderDetail.getBuyorderNo());
                            MessageUtil.sendMessage2(169, saleOrderTraderIds, mapParams, "./order/saleorder/view.do?saleorderId=" + saleOrderItem.getSaleorderId().intValue(), session_user.getUsername());
                        }
                        // 更新采购单商品进程描述
                        buyorderGoodsMapper.updateProcessDescByBuyOrderGoodsId(buyOrderGoodsIdKey, "供应商已发货至仓库(" + sendNum + "/" + (sumNum - sendedNum) + ")");
                        // 直发
                    } else if (Integer.valueOf(1).equals(buyOrderDetail.getDeliveryDirect())) {
                        List<Integer> salesId = new ArrayList<>();
                        for (Saleorder saleOrderItem : validSaleOrderNos) {
                            // 根据销售单编码查找对应销售
                            List<Integer> saleOrderTraderIds = buyorderGoodsMapper.selectSaleOrderTraderIdsBySaleOrderId(saleOrderItem.getSaleorderId());
                            saleOrderTraderIds = saleOrderTraderIds.stream().filter(ObjectUtils::notEmpty).collect(Collectors.toList());
                            if (!ObjectUtils.allNotNull(saleOrderItem)) {
                                continue;
                            }
                            mapParams.put("saleOrderNo", saleOrderItem.getSaleorderNo());
                            mapParams.put("buyOrderNo", buyOrderDetail.getBuyorderNo());
                            MessageUtil.sendMessage2(168, saleOrderTraderIds, mapParams, "./order/saleorder/view.do?saleorderId=" + saleOrderItem.getSaleorderId().intValue(), session_user.getUsername());

                            try {
                                //VDERP-8189【科研购公众号】科研购订单发货推送公众号消息提醒客户
                                if (!salesId.contains(saleOrderItem.getSaleorderId())) {
                                    //baseService.sendKygDeliveryOrder(saleOrderItem, sMap);
                                    salesId.add(saleOrderItem.getSaleorderId());
                                }
                            } catch (Exception e) {
                                logger.error("科研购直发推送信息失败，异常信息,", e);
                            }
                        }
                        // 更新采购单商品进程描述
                        buyorderGoodsMapper.updateProcessDescByBuyOrderGoodsId(buyOrderGoodsIdKey, "供应商已发货(" + (sendNum + sendedNum) + "/" + (sumNum - sendedNum) +
                                ")");
                    }
                }
            }

            //采购修改快递处理直发订单账期编码监管
            buyorderService.dealDirectOrderPeriodManagement(express, buyorderVo.getBuyorderId(), ErpConst.OperateType.UPDATE_OPERATE_FRONT);

            result = expressService.saveExpress(express);
            if (result == null || result.getCode() == -1) {
                return new ResultInfo<>();
            }
            if(1 == buyorderMapper.selectDeliveryDirect(buyorderVo.getBuyorderId())){
                buyorderService.checkExpressEnableReceive(buyorderVo.getBuyorderId());
            }

            //更新采购单发货状态 VDERP-2431
            buyorderService.updateBuyorderDeliveryStatus(buyorderVo.getBuyorderId());

            // VDERP-8759 订单流发货状态同步
            buyorderInfoSyncService.syncDeliveryStatus(buyorderVo.getBuyorderId(), 2);

            // 直发采购单 + 订单类型0销售订单采购  推送物流信息到前端
            pushExpressToWeb((Express) result.getData(), buyorderVo.getBuyorderId(), id_num_price, "编辑");

            //采购修改快递处理直发订单账期编码监管
            buyorderService.dealDirectOrderPeriodManagement((Express) result.getData(), buyorderVo.getBuyorderId(), ErpConst.OperateType.UPDATE_OPERATE_END);

        } catch (Exception e) {
            logger.error("saveEditExpress:", e);
        }
        /**
         * base订阅快递信息
         */
        String phone = expressService.getPhoneByBusinessType(express.getExpressId(), express.getLogisticsNo());
        logger.info("开始推送快递信息至base服务,单号:{}，快照信息:{}", express.getLogisticsNo(), JSON.toJSONString(express));
        logisticsService.pushLogisticsToBase(express.getLogisticsNo()
                , logisticsService.getLogisticsCodeByLogisticsId(express.getLogisticsId())
                , phone);

        return result;
    }

    /**
     * <b>Description:</b><br>
     * 跳转到新增售后页面
     *
     * @param request
     * @param saleorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年10月10日 上午10:48:10
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addAfterSalesPage")
    public ModelAndView addAfterSalesPage(HttpServletRequest request, Buyorder buyorder) {
        ModelAndView mav = new ModelAndView();
        if ("qt".equals(buyorder.getFlag())) {
            mav.addObject("buyorder", buyorder);
            mav.setViewName("order/buyorder/add_afterSales_qt");
            return mav;
        }
        BuyorderVo sv = buyorderService.getBuyorderGoodsVoList(buyorder);
        mav.addObject("buyorder", sv);
        // 获取退货原因
        List<SysOptionDefinition> sysList = getSysOptionDefinitionList(536);
        mav.addObject("sysList", sysList);
        mav.addObject("domain", domain);
        if ("th".equals(buyorder.getFlag())) {// 退货
            mav.setViewName("order/buyorder/add_afterSales_th");
        } else if ("hh".equals(buyorder.getFlag())) {
            mav.setViewName("order/buyorder/add_afterSales_hh");
        } else if ("tp".equals(buyorder.getFlag())) {
            mav.setViewName("order/buyorder/add_afterSales_tp");
        } else if ("tk".equals(buyorder.getFlag())) {
            mav.setViewName("order/buyorder/add_afterSales_tk");
        }
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 保存新增售后
     *
     * @param request
     * @param afterSalesVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年10月11日 下午1:27:56
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveAddAfterSales")
    @SystemControllerLog(operationType = "add", desc = "保存新增售后")
    public ModelAndView saveAddAfterSales(HttpServletRequest request, AfterSalesVo afterSalesVo,
                                          @RequestParam(required = false, value = "afterSaleNums") String[] afterSaleNums,
                                          @RequestParam(required = false, value = "fileName") String[] fileName,
                                          @RequestParam(required = false, value = "fileUri") String[] fileUri,
                                          @RequestParam(required = false, value = "invoiceIds") String[] invoiceIds) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView();

        Buyorder buyorder = buyorderService.getBuyOrderByOrderId(afterSalesVo.getOrderId());

        // VDERP-10681 直发采购售后校验
        if (buyorder.getStatus() == 0 || buyorder.getStatus() == 3 || buyorder.getLockedStatus() == 1) {
            mav.addObject("message", "采购单已锁定或已关闭,无法提交售后,请刷新页面!");
            return fail(mav);
        }

        //采购单退货且普法
        if (afterSalesVo.getType() == 546 && buyorder.getDeliveryDirect() == 0) {

            logger.info("新增退货单,请求WMS关闭原始的采购单" + buyorder.getBuyorderNo());

            try {
				/*//取消订单参数对象
				CancelPoDto cancelPoDto = new CancelPoDto();
				cancelPoDto.setDocNo(afterSalesVo.getOrderNo());
				cancelPoDto.setPoType(WmsInterfaceOrderType.INPUT_PURCHASE);
				cancelPoDto.setErpCancelReason("采购新增售后关闭采购单");

				//采购单申请 TODO Holiis ok
				WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.CANCEL_PO);*/
                // hollis 代码抽取
                //失败且 不是订单不存在这种情况才可以
                if (!cancelTypeService.cancelInputPurchaseMethod(afterSalesVo.getOrderNo(), WmsInterfaceOrderType.INPUT_PURCHASE)) {
                    mav.addObject("message", "采购单正在物流作业,无法提交售后,请稍后重试!");
                    return fail(mav);
                }
            } catch (Exception e) {
                logger.error("BuyorderController -> saveAddAfterSales error: ", e);
                return fail(mav);
            }
        }

        afterSalesVo.setAfterSalesNum(afterSaleNums);
        afterSalesVo.setAttachName(fileName);
        afterSalesVo.setAttachUri(fileUri);
        afterSalesVo.setSubjectType(536);// 采购
        afterSalesVo.setAtferSalesStatus(0);
        afterSalesVo.setServiceUserId(user.getUserId());
        afterSalesVo.setStatus(0);
        afterSalesVo.setValidStatus(0);
        afterSalesVo.setDomain(domain);
        afterSalesVo.setInvoiceIds(invoiceIds);
        afterSalesVo.setCompanyId(user.getCompanyId());
        afterSalesVo.setPayee(user.getCompanyName());
        afterSalesVo.setTraderType(2);

        ResultInfo<?> res = afterSalesOrderService.saveAddAfterSales(afterSalesVo, user);
        // mav.addObject("refresh",
        // "true_false_true");//是否关闭当前页，是否刷新当前页，是否刷新上层页面----三个参数为必传项
        // mav.addObject("url","./viewBuyordersh.do?buyorderId="+afterSalesVo.getOrderId());
        if (res.getCode() == 0) {
            mav.addObject("url", "./viewAfterSalesDetail.do?afterSalesId=" + res.getData());
            return success(mav);
        } else {
            return fail(mav);
        }
    }

    /**
     * <b>Description:</b><br>
     * 查看采购订单的售后详情
     *
     * @param request
     * @param afterSales
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年10月17日 下午1:54:47
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/viewAfterSalesDetail")
    public ModelAndView viewAfterSalesDetail(HttpServletRequest request, AfterSalesVo afterSales) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        afterSales.setCompanyId(user.getCompanyId());
        afterSales.setTraderType(2);
        ModelAndView mav = new ModelAndView();
        AfterSalesVo afterSalesVo = afterSalesOrderService.getAfterSalesVoDetail(afterSales);
        mav.addObject("afterSalesVo", afterSalesVo);
        CommunicateRecord communicateRecord = new CommunicateRecord();
        communicateRecord.setAfterSalesId(afterSalesVo.getAfterSalesId());
        List<CommunicateRecord> crList = traderCustomerService.getCommunicateRecordList(communicateRecord);
        mav.addObject("communicateList", crList);

        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                "afterSalesVerify_" + afterSalesVo.getAfterSalesId());
        Task taskInfo = (Task) historicInfo.get("taskInfo");
        mav.addObject("taskInfo", historicInfo.get("taskInfo"));
        mav.addObject("startUser", historicInfo.get("startUser") != null ?
                getRealNameByUserName(historicInfo.get("startUser").toString()) : null);
        mav.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态
        mav.addObject("endStatus", historicInfo.get("endStatus"));

        List<HistoricActivityInstance> historic = setAssignRealNames(mav, historicInfo);
        mav.addObject("historicActivityInstance", historic);

        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>) historicInfo
                .get("historicActivityInstance");
        mav.addObject("commentMap", historicInfo.get("commentMap"));
        // 当前审核人
        String verifyUsers = null;
        if (null != taskInfo) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfo);
            verifyUsers = (String) taskInfoVariables.get("verifyUsers");
        }
        mav.addObject("verifyUsers", getVerifyUserRealNames(verifyUsers));


        List<Integer> skuIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(afterSalesVo.getAfterReturnOutstockList())) {
            afterSalesVo.getAfterReturnOutstockList().stream().forEach(saleGood -> {
                skuIds.add(saleGood.getGoodsId());
            });
        }


        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(afterSalesVo.getAfterSalesGoodsList())) {
            afterSalesVo.getAfterSalesGoodsList().stream().forEach(saleGood -> {
                skuIds.add(saleGood.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mav.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        if (afterSalesVo.getType() == 546) {
            mav.setViewName("order/buyorder/view_afterSales_th");
        } else if (afterSalesVo.getType() == 547) {
            mav.setViewName("order/buyorder/view_afterSales_hh");
        } else if (afterSalesVo.getType() == 548) {
            mav.setViewName("order/buyorder/view_afterSales_tp");
        } else if (afterSalesVo.getType() == 549) {
            mav.setViewName("order/buyorder/view_afterSales_tk");
        }
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 采购订单申请审核
     *
     * @param request
     * @param afterSales
     * @param taskId
     * @return
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年12月22日 上午10:05:08
     */
//	@FormToken(remove = true)
    @MethodLock(field = "buyorderId", className = Buyorder.class)
    @ResponseBody
    @RequestMapping(value = "/editApplyValidBuyorder")
    @SystemControllerLog(operationType = "edit", desc = "采购订单申请审核")
    public ResultInfo<?> editApplyValidBuyorder(HttpServletRequest request, Buyorder buyorder, String taskId) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        if (user == null) {
            if (riskKey.equals(buyorder.getRiskKey())) {
                user = userService.getUserInfoByName("njadmin");
            } else {
                return new ResultInfo<>(-1, "风控权限失败!");
            }
        }

        try {

            Map<String, Object> variableMap = new HashMap<>();
            // 查询当前订单的一些状态
            BuyorderVo buyorderInfo = buyorderService.getBuyorderVoDetail(buyorder, user);
            log.info("采购订单申请审核:buyorderInfo{}", JSON.toJSONString(buyorderInfo));

            String processDefinitionKey = getProcessKey(buyorderInfo);

            Map<String, Object> historicInfo3 = actionProcdefService.getHistoric(processEngine,
                    processDefinitionKey + "_" + buyorder.getBuyorderId());
            if (historicInfo3.get("endStatus") != null) {
                if (!"驳回".equals(historicInfo3.get("endStatus"))) {
                    return new ResultInfo(-1, ErpConst.APPLY_VALID_ERROR_MESSAGE);
                }
            }

            buyorder.setCompanyId(user.getCompanyId());
            buyorder.setUpdater(user.getUserId());
            buyorder.setModTime(DateUtil.sysTimeMillis());
            buyorder.setCompanyName(user.getCompanyName());
            buyorder.setStatus(null);
            // 锁单
            buyorder.setLockedStatus(0);

            ResultInfo<?> res = buyorderService.saveBuyorder(buyorder);
            if (res.getCode() == -1) {
                return res;
            }

            if (StringUtil.isBlank(buyorder.getRiskKey())) {
                //风控人工审核
                riskCheckLogService.manualFinishTriggerOfRiskCheck(buyorderInfo.getBuyorderNo(), user.getUserId(), "");
            }
            BigDecimal beihuo = null;
            // 查询供应商主要产品
            TraderSupplier traderSupplier = new TraderSupplier();
            traderSupplier.setTraderId(buyorderInfo.getTraderId());
            TraderSupplierVo traderSupplierInfo = traderSupplierService.getTraderSupplierInfo(traderSupplier);
            List<Integer> goodsIdListBySupplier = goodsService
                    .getSupplierGoodsIds(traderSupplierInfo.getTraderSupplierId());
            // 是否主要供应商(预留)
            int isMainSupply = 0;
            // 产品ID和结算价的Map
            Map<Integer, Object> goodsIdsMap = new HashMap<>();
            if (null != buyorderInfo.getBuyorderGoodsVoList()) {
                List<Integer> goodsIds = new ArrayList<>();
                for (BuyorderGoodsVo bg : buyorderInfo.getBuyorderGoodsVoList()) {
                    goodsIds.add(bg.getGoodsId());
                }
                if (goodsIds != null && !goodsIds.isEmpty()) {
                    goodsIdsMap = saleorderService.getSaleorderGoodsSettlementPriceByGoodsIds(goodsIds,
                            user.getCompanyId());
                }
                for (BuyorderGoodsVo b : buyorderInfo.getBuyorderGoodsVoList()) {
                    if (goodsIdsMap != null) {
                        BigDecimal settlementPrice = (BigDecimal) goodsIdsMap.get(b.getGoodsId());
                        if (null != settlementPrice) {
                            b.setSettlementPrice(settlementPrice);
                            beihuo = new java.math.BigDecimal(1.02);
                            beihuo = beihuo.setScale(2, BigDecimal.ROUND_HALF_UP);
                            // 备货价>(结算价/1.02)
                            if (b.getPrice()
                                    .compareTo(settlementPrice.divide(beihuo, 2, BigDecimal.ROUND_HALF_UP)) == 1) {
                                buyorderInfo.setIsOverSettlementPrice(1);
                            }
                        }
                        if (null != goodsIdListBySupplier && !goodsIdListBySupplier.contains(b.getGoodsId())) {
                            isMainSupply = 1;
                        }
                    }
                }

            }


            //User creatorInfo = userService.getUserById(buyorderInfo.getCreator());

            String orderBelongUser = userService.getUserNameByUserId(buyorderInfo.getUserId());


            // 开始生成流程(如果没有taskId表示新流程需要生成)
            if (taskId.equals("0")) {
                variableMap.put("isMainSupply", isMainSupply);
                variableMap.put("buyorderInfo", buyorderInfo);
                variableMap.put("currentAssinee", orderBelongUser);
                variableMap.put("relateTableKey", buyorderInfo.getBuyorderId());
                variableMap.put("relateTable", "T_BUYORDER");
                variableMap.put("orgId", user.getOrgId());
                variableMap.put("processDefinitionKey", processDefinitionKey);
                variableMap.put("businessKey", processDefinitionKey + "_" + buyorderInfo.getBuyorderId());
                actionProcdefService.createProcessInstance(request, processDefinitionKey,
                        processDefinitionKey + "_" + buyorderInfo.getBuyorderId(), variableMap);
            }

            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    processDefinitionKey + "_" + buyorderInfo.getBuyorderId());
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<String, Object>();
                // 设置审核完成监听器回写参数
                variables.put("tableName", "T_BUYORDER");
                variables.put("id", "BUYORDER_ID");
                variables.put("idValue", buyorderInfo.getBuyorderId());
                variables.put("key", "VALID_STATUS");
                variables.put("value", 1);
                variableMap.put("key1", "LOCKED_STATUS");
                variableMap.put("value1", 0);
                // 回写数据的表在db中
                variables.put("db", 2);
                // 默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                        orderBelongUser, variables);
                // 如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                    //维护verifyStatus主表字段
                    buyorderMapper.saveVerifyStatus(buyorderInfo.getBuyorderId(), 0);
                }

                // 获取当前活动节点
                taskInfo = processEngine.getTaskService()
                        .createTaskQuery()
                        .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                        .singleResult();

                if (taskInfo != null) {

                    setProductBelongInfo(buyorderInfo.getBuyorderGoodsVoList());

                    List<String> skuNos = buyorderInfo.getBuyorderGoodsVoList().stream().map(good -> good.getSku()).collect(Collectors.toList());

                    Set<String> manageAndAsistNameSet = getProductManageAndAsistNameList(skuNos);

                    for (String manageAndAsistName : manageAndAsistNameSet) {
                        processEngine.getTaskService().addCandidateUser(taskInfo.getId() + "", manageAndAsistName);
                    }
                    ;

                    //发送消息提醒
                    sendBuyOrderInfo(getProductManageAndAsistIdList(skuNos), processEngine.getTaskService().getVariables(taskInfo.getId()));
                }

                // 如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                }

            }
            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("editApplyValidBuyorder:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    private void setProductBelongInfo(List<BuyorderGoodsVo> buyorderGoodsVoList) {

        if (CollectionUtils.isEmpty(buyorderGoodsVoList)) {
            return;
        }

        buyorderGoodsVoList.stream().forEach(buyorderGood -> {

            ProductManageAndAsistDto productManageAndAsistDto = this.coreSkuMapper.queryProductManageAndAsist(buyorderGood.getSku());

            BuyorderGoods updateBuyOrderGood = new BuyorderGoods();
            updateBuyOrderGood.setBuyorderGoodsId(buyorderGood.getBuyorderGoodsId());
            updateBuyOrderGood.setProductBelongIdInfo(productManageAndAsistDto.getProductAssitUserId() + "," + productManageAndAsistDto.getProductManageUserId());
            updateBuyOrderGood.setProductBelongNameInfo(productManageAndAsistDto.getProductAssitName() + "," + productManageAndAsistDto.getProductManageName());

            this.buyorderGoodsMapper.updateByPrimaryKeySelective(updateBuyOrderGood);

        });
    }

    private void sendBuyOrderInfo(List<Integer> userIdList, Map<String, Object> variables) {

        // 消息模板编号N035
        Map<String, String> variableMap = new HashedMap();
        BuyorderVo buyorderInfo = (BuyorderVo) variables.get("buyorderInfo");
        variableMap.put("buyorderNo", buyorderInfo.getBuyorderNo());

        String url = "./order/buyorder/viewBuyorder.do?buyorderId=" + buyorderInfo.getBuyorderId();
        String preAssignee = variables.get("currentAssinee").toString();

        MessageUtil.sendMessage(14, userIdList, variableMap, url, preAssignee);

    }

    Set<String> getProductManageAndAsistNameList(List<String> skuNos) {

        List<ProductManageAndAsistDto> manageAndAsistDtoList = coreSkuMapper.batchQueryProductManageAndAsist(skuNos);

        Set<String> manageAndAsistNameSet = new HashSet<>();

        manageAndAsistDtoList.stream().forEach(manageAndAsist -> {

            if (StringUtils.isNotEmpty(manageAndAsist.getProductAssitName())) {
                manageAndAsistNameSet.add(manageAndAsist.getProductAssitName());
            }

            if (StringUtils.isNotEmpty(manageAndAsist.getProductManageName())) {
                manageAndAsistNameSet.add(manageAndAsist.getProductManageName());
            }
        });

        return manageAndAsistNameSet;
    }

    List<Integer> getProductManageAndAsistIdList(List<String> skuNos) {

        List<ProductManageAndAsistDto> manageAndAsistDtoList = coreSkuMapper.batchQueryProductManageAndAsist(skuNos);

        Set<Integer> manageAndAsistNameIdSet = new HashSet<>();

        manageAndAsistDtoList.stream().forEach(manageAndAsist -> {

            if (StringUtils.isNotEmpty(manageAndAsist.getProductAssitName())) {
                manageAndAsistNameIdSet.add(manageAndAsist.getProductAssitUserId());
            }

            if (StringUtils.isNotEmpty(manageAndAsist.getProductManageName())) {
                manageAndAsistNameIdSet.add(manageAndAsist.getProductManageUserId());
            }

        });

        return new ArrayList<>(manageAndAsistNameIdSet);
    }

    /**
     * <b>Description:</b><br>
     * 采购售后申请审核
     *
     * @param request
     * @param afterSales
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年10月19日 下午4:14:13
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/editApplyAudit")
    @SystemControllerLog(operationType = "edit", desc = "采购售后申请审核")
    public ResultInfo<?> editApplyAudit(HttpServletRequest request, AfterSalesVo afterSales, String taskId) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        afterSales.setStatus(1);// 审核中
        afterSales.setCompanyId(user.getCompanyId());
        afterSales.setModTime(DateUtil.sysTimeMillis());
        afterSales.setUpdater(user.getUserId());
        afterSales.setTraderType(2);

        // 查询当前订单的一些状态
        AfterSalesVo afterSalesInfo = afterSalesOrderService.getAfterSalesVoDetail(afterSales);

		/*try{
			//如果是采购退货单申请，需要请求WMS取消订单
			if(afterSalesInfo.getSubjectType() == 536 && afterSalesInfo.getType() == 546){

				//取消订单参数对象
				CancelPoDto cancelPoDto = new CancelPoDto();
				cancelPoDto.setDocNo(afterSalesInfo.getOrderNo());
				cancelPoDto.setPoType(WmsInterfaceOrderType.INPUT_PURCHASE);
				cancelPoDto.setErpCancelReason(afterSalesInfo.getReasonName());

				//撤销入库单
				WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.CANCEL_PO);
				WmsResponse wmsResponse = wmsInterface.request(cancelPoDto);

				//如果不能取消,返回不能取消的原因
				if("0".equals(wmsResponse.getReturnFlag()) && !"993".equals(wmsResponse.getReturnCode())){
					return new ResultInfo(-1, wmsResponse.getReturnDesc());
				}
			}

		}catch (Exception e){
			logger.error("BuyorderController -> editApplyAudit失败:",e);
			return new ResultInfo(-1, "WMS请求接口失败");
		}*/

        ResultInfo<?> res = afterSalesOrderService.editApplyAudit(afterSales);
        if (res.getCode() == -1) {
            return res;
        }

        try {
            Map<String, Object> variableMap = new HashMap<String, Object>();

            // 订单中产品类型（0未维护,1 只有设备,2 只有试剂,3 又有试剂又有设备）
            afterSalesInfo.setGoodsType(0);
            List<Integer> goodsTypeList = new ArrayList<>();
            if (afterSalesInfo.getAfterSalesGoodsList() != null && !afterSalesInfo.getAfterSalesGoodsList().isEmpty()) {
                for (AfterSalesGoodsVo asgv : afterSalesInfo.getAfterSalesGoodsList()) {
                    if (asgv.getGoodsType() != null && (asgv.getGoodsType() == 316 || asgv.getGoodsType() == 319)) {
                        goodsTypeList.add(1);
                    } else if (asgv.getGoodsType() != null
                            && (asgv.getGoodsType() == 317 || asgv.getGoodsType() == 318)) {
                        goodsTypeList.add(2);
                    }
                }

                if (!goodsTypeList.isEmpty()) {
                    List<Integer> newList = new ArrayList(new HashSet(goodsTypeList));
                    if (newList.size() == 2) {
                        afterSalesInfo.setGoodsType(3);
                    }

                    if (newList.size() == 1) {

                        if (newList.get(0) == 1) {
                            afterSalesInfo.setGoodsType(1);
                        } else if (newList.get(0) == 2) {
                            afterSalesInfo.setGoodsType(2);
                        }

                    }
                }
            }

            // 开始生成流程(如果没有taskId表示新流程需要生成)
            if (taskId.equals("0")) {
                variableMap.put("afterSalesInfo", afterSalesInfo);
                variableMap.put("currentAssinee", user.getUsername());
                variableMap.put("processDefinitionKey", "afterSalesVerify");
                variableMap.put("businessKey", "afterSalesVerify_" + afterSalesInfo.getAfterSalesId());
                variableMap.put("relateTableKey", afterSalesInfo.getAfterSalesId());
                variableMap.put("relateTable", "T_AFTER_SALES");
                variableMap.put("orgId", user.getOrgId());
                actionProcdefService.createProcessInstance(request, "afterSalesVerify",
                        "afterSalesVerify_" + afterSalesInfo.getAfterSalesId(), variableMap);
            }
            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    "afterSalesVerify_" + afterSalesInfo.getAfterSalesId());
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<String, Object>();
                // 设置审核完成监听器回写参数
                variables.put("tableName", "T_AFTER_SALES");
                variables.put("id", "AFTER_SALES_ID");
                variables.put("idValue", afterSalesInfo.getAfterSalesId());
                variables.put("key", "STATUS");
                variables.put("value", 2);
                // 回写数据的表在db中
                variables.put("db", 2);
                // 默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                        user.getUsername(), variables);
                // 如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                }
            }

            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("editApplyAudit:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    /**
     * <b>Description:</b><br>
     * 跳转到售后编辑页
     *
     * @param request
     * @param afterSales
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年10月20日 上午9:00:02
     */
    @ResponseBody
    @RequestMapping(value = "/editAfterSalesPage")
    public ModelAndView editAfterSalesPage(HttpServletRequest request, AfterSalesVo afterSales) throws IOException {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView();
        if (afterSales == null) {
            return pageNotFound(request);
        }
        afterSales.setTraderType(2);
        afterSales.setCompanyId(user.getCompanyId());
        afterSales = afterSalesOrderService.getAfterSalesVoDetail(afterSales);
        mav.addObject("afterSales", afterSales);

        // 获取退货原因
        List<SysOptionDefinition> sysList = getSysOptionDefinitionList(536);
        mav.addObject("sysList", sysList);
        mav.addObject("domain", domain);

        Buyorder buyorder = new Buyorder();
        buyorder.setBuyorderId(afterSales.getOrderId());
        if (afterSales.getType() == 546) {
            buyorder.setFlag("th");
        } else if (afterSales.getType() == 547) {
            buyorder.setFlag("hh");
        } else if (afterSales.getType() == 548) {
            buyorder.setFlag("tp");
        } else if (afterSales.getType() == 549) {
            buyorder.setFlag("tk");
        }
        BuyorderVo sv = buyorderService.getBuyorderGoodsVoList(buyorder);
        mav.addObject("buyorder", sv);
        if (afterSales.getType() == 546) {// 退货
            mav.setViewName("order/buyorder/edit_afterSales_th");
        } else if (afterSales.getType() == 547) {
            mav.setViewName("order/buyorder/edit_afterSales_hh");
        } else if (afterSales.getType() == 548) {
            mav.setViewName("order/buyorder/edit_afterSales_tp");
        } else if (afterSales.getType() == 548) {
            mav.setViewName("order/buyorder/edit_afterSales_tk");
        }
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(sv)));
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 保存编辑售后
     *
     * @param request
     * @param afterSalesVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年10月11日 下午1:27:56
     */
    @ResponseBody
    @RequestMapping(value = "/saveEditAfterSales")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑采购售后")
    public ModelAndView saveEditAfterSales(HttpServletRequest request, AfterSalesVo afterSalesVo,
                                           @RequestParam(required = false, value = "afterSaleNums") String[] afterSaleNums,
                                           @RequestParam(required = false, value = "fileName") String[] fileName,
                                           @RequestParam(required = false, value = "fileUri") String[] fileUri,
                                           @RequestParam(required = false, value = "invoiceIds") String[] invoiceIds) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        afterSalesVo.setAfterSalesNum(afterSaleNums);
        afterSalesVo.setAttachName(fileName);
        afterSalesVo.setAttachUri(fileUri);
        afterSalesVo.setDomain(domain);
        afterSalesVo.setInvoiceIds(invoiceIds);
        afterSalesVo.setTraderType(1);
        afterSalesVo.setPayee(user.getCompanyName());
        ModelAndView mav = new ModelAndView();
        ResultInfo<?> res = afterSalesOrderService.saveEditAfterSales(afterSalesVo, user);
        // mav.addObject("refresh",
        // "true_false_true");//是否关闭当前页，是否刷新当前页，是否刷新上层页面----三个参数为必传项
        // mav.addObject("url","./viewBuyordersh.do?buyorderId="+afterSalesVo.getOrderId());
        if (res.getCode() == 0) {
            mav.addObject("url", "./viewAfterSalesDetail.do?afterSalesId=" + res.getData());
            return success(mav);
        } else {
            return fail(mav);
        }
    }

    /**
     * <b>Description:</b><br>
     * 关闭售后订单
     *
     * @param request
     * @param afterSalesVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年10月20日 下午5:44:00
     */
    @ResponseBody
    @RequestMapping(value = "/saveCloseAfterSales")
    @SystemControllerLog(operationType = "edit", desc = "关闭售后订单")
    public ResultInfo<?> saveCloseAfterSales(HttpServletRequest request, AfterSalesVo afterSalesVo) {

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        AfterSalesVo queryVo = new AfterSalesVo();
        queryVo.setAfterSalesId(afterSalesVo.getAfterSalesId());
        queryVo.setCompanyId(user.getCompanyId());
        queryVo.setTraderType(2);

        AfterSalesVo afterSales = afterSalesOrderService.getAfterSalesVoDetail(queryVo);

        //采购退货单和采购售后单是否能关闭
        try {

            WMSValidatorChain validatorChain = WmsValidatorChainBuild
                    .newBuild()
                    .setPurchaseExgCloseValidator(this.purchaseExgCloseValidator)
                    .setPurchaseReturnCloseValidator(this.purchaseReturnCloseValidator)
                    .setPurchaseAfterSaleCloseValidator(this.purchaseAfterSaleCloseValidator)
                    .create();

            ValidatorResult validatorResult = validatorChain.validator(afterSales);
            if (validatorResult.getResult() == false) {
                return new ResultInfo<>(-1, validatorResult.getMessage());
            }

        } catch (Exception e) {
            logger.error("校验失败,", e);
            return new ResultInfo<>(-1, "WMS请求失败");
        }

        ResultInfo<?> res = afterSalesOrderService.saveCloseAfterSales(afterSalesVo, user);
        if (res == null) {
            return new ResultInfo<>();
        }
        return res;
    }

    /**
     * <b>Description:</b><br>
     * 订单合同回传初始化
     *
     * @param request
     * @param session
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2017年7月24日 下午2:19:47
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/contractReturnInit")
    public ModelAndView contractReturnInit(HttpServletRequest request, HttpSession session, Integer buyorderId, String altType) {
        // User user =
        // (User)request.getSession().getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();

        mv.addObject("buyorderId", buyorderId);
        mv.addObject("altType", altType);
        mv.setViewName("vue/view/contract/uploadPurchaseContract");
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 订单合同回传文件上传
     *
     * @param request
     * @param response
     * @param lwfile
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2017年7月24日 下午2:47:39
     */
    @ResponseBody
    @RequestMapping(value = "/contractReturnUpload")
    public FileInfo contractReturnUpload(HttpServletRequest request, HttpServletResponse response,
                                         @RequestParam("lwfile") MultipartFile lwfile) {
        String path = "/upload/buyorder";
        long size = lwfile.getSize();
        if (size > 2 * 1024 * 1024) {
            return new FileInfo(-1, "图片大小应为2MB以内");
        }
        return ossUtilsService.upload2Oss(request, lwfile);

    }

    /**
     * <b>Description:</b><br>
     * 订单合同回传保存
     *
     * @param request
     * @param attachment
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2017年7月24日 下午2:55:15
     */
    @NoRepeatSubmit
    @ResponseBody
    @RequestMapping(value = "/contractReturnSave")
    @SystemControllerLog(operationType = "add", desc = "采购订单合同回传保存")
    public ResultInfo<?> contractReturnSave(HttpServletRequest request, @RequestBody PurchaseContractDto purchaseContractDto) {
        return buyorderService.saveBuyorderAttachment(request,purchaseContractDto);
    }

    /**
     * <b>Description:</b><br>
     * 订单合同回传删除
     *
     * @param request
     * @param attachment
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2017年7月24日 下午2:58:22
     */
    @ResponseBody
    @RequestMapping(value = "/contractReturnDel")
    @SystemControllerLog(operationType = "edit", desc = "采购订单合同回传删除")
    public ResultInfo<?> contractReturnDel(HttpServletRequest request, Attachment attachment) {
        return saleorderService.delSaleorderAttachment(attachment);
    }

    /**
     * <b>Description:</b><br>
     * 备货计划管理
     *
     * @param request
     * @param pageNo
     * @param pageSize
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年11月22日 下午4:29:57
     */
    @ResponseBody
    @RequestMapping(value = "/bhmanage")
    public ModelAndView bhManage(HttpServletRequest request, GoodsVo goodsVo,
                                 @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                 @RequestParam(required = false) Integer pageSize, HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        Page page = getPageTag(request, pageNo, pageSize);

        List<SysOptionDefinition> levelList = getSysOptionDefinitionList(334);

        goodsVo.setCompanyId(user.getCompanyId());

        List<GoodsVo> goodsList = null;
        Map<String, Object> map = buyorderService.getBHManageList(goodsVo, page);

        goodsList = (List<GoodsVo>) map.get("list");
        mv.addObject("goodsList", goodsList);

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(goodsList)) {
            List<Integer> skuIds = new ArrayList<>();
            goodsList.stream().forEach(goods -> {
                skuIds.add(goods.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        mv.addObject("levelList", levelList);
        mv.addObject("page", (Page) map.get("page"));
        // mv.addObject("maybeSaleNum", map.get("maybeSaleNum"));
        // mv.addObject("maybeOccupyAmount", map.get("maybeOccupyAmount"));
        mv.setViewName("order/buyorder/bh_manage");
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 备货计划管理分析
     *
     * @param goodsVo
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2018年2月10日 下午2:00:23
     */
    @ResponseBody
    @RequestMapping(value = "/bhmanagestat")
    public ModelAndView bhManageStat(GoodsVo goodsVo, HttpSession session, HttpServletRequest request,
                                     HttpServletResponse response) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();

        goodsVo.setCompanyId(user.getCompanyId());

        String maybeSaleNum = "maybeSaleNum";
        String maybeOccupyAmount = "maybeOccupyAmount";

        Cookie[] cookies = request.getCookies();// 这样便可以获取一个cookie数组
        if (null == cookies) {
            Map<String, Object> map = buyorderService.getBHManageStat(goodsVo);

            Cookie cookie = new Cookie(maybeSaleNum, map.get("maybeSaleNum").toString());
            cookie.setMaxAge(30 * 60);// 设置为30min
            response.addCookie(cookie);
            Cookie cookie2 = new Cookie(maybeOccupyAmount, map.get("maybeOccupyAmount").toString());
            cookie2.setMaxAge(30 * 60);// 设置为30min
            response.addCookie(cookie2);

            mv.addObject("maybeSaleNum", map.get("maybeSaleNum"));
            mv.addObject("maybeOccupyAmount", map.get("maybeOccupyAmount"));
        } else {
            Boolean ismaybeSaleNum = false;
            Boolean ismaybeOccupyAmount = false;
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(maybeSaleNum)) {
                    ismaybeSaleNum = true;
                    mv.addObject("maybeSaleNum", cookie.getValue());
                }
                if (cookie.getName().equals(maybeOccupyAmount)) {
                    ismaybeOccupyAmount = true;
                    mv.addObject("maybeOccupyAmount", cookie.getValue());
                }
            }

            if (!ismaybeSaleNum || !ismaybeOccupyAmount) {
                Map<String, Object> map = buyorderService.getBHManageStat(goodsVo);

                Cookie cookie = new Cookie(maybeSaleNum, map.get("maybeSaleNum").toString());
                cookie.setMaxAge(30 * 60);// 设置为30min
                response.addCookie(cookie);
                Cookie cookie2 = new Cookie(maybeOccupyAmount, map.get("maybeOccupyAmount").toString());
                cookie2.setMaxAge(30 * 60);// 设置为30min
                response.addCookie(cookie2);

                mv.addObject("maybeSaleNum", map.get("maybeSaleNum"));
                mv.addObject("maybeOccupyAmount", map.get("maybeOccupyAmount"));
            }
        }

        mv.setViewName("order/buyorder/bh_manage_stat");
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 备货计划生成备货订单
     *
     * @param request
     * @param session
     * @param goodsIds
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年11月23日 下午2:34:49
     */
    @ResponseBody
    @RequestMapping(value = "/addbhorder")
    @SystemControllerLog(operationType = "add", desc = "备货计划生成备货订单")
    public ModelAndView addBHOrder(HttpServletRequest request, HttpSession session,
                                   @RequestParam("goodsIds") String goodsIds) {
        ModelAndView mv = new ModelAndView();
        if (null == goodsIds || goodsIds.equals("")) {
            return pageNotFound(request);
        }
        Saleorder saleorder = new Saleorder();
        Saleorder bhSaleorder = saleorderService.saveAddBhSaleorder(saleorder, request, session);

        if (null != bhSaleorder) {
            String[] idList = goodsIds.split(",");
            List<Integer> list = new ArrayList<>();
            for (String id : idList) {
                list.add(Integer.parseInt(id));
            }
            ResultInfo resultInfo = buyorderService.batchAddBhSaleorderGoods(list, bhSaleorder, session);
            if (resultInfo.getCode().equals(0)) {
                mv.addObject("url",
                        request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                                + request.getContextPath() + "/order/saleorder/editBhSaleorder.do?saleorderId="
                                + bhSaleorder.getSaleorderId());
                return success(mv);
            } else {
                return fail(mv);
            }
        } else {
            return fail(mv);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/addBhOrderNew")
    @SystemControllerLog(operationType = "add", desc = "备货计划生成备货订单")
    public ModelAndView addBhOrderNew(HttpServletRequest request, HttpSession session,
                                      @RequestParam("goodsId") String goodsIds) {
        ModelAndView mv = new ModelAndView();
        if (null == goodsIds || goodsIds.equals("")) {
            return pageNotFound(request);
        }
        Saleorder saleorder = new Saleorder();
        Saleorder bhSaleorder = saleorderService.saveAddBhSaleorder(saleorder, request, session);

        if (null != bhSaleorder) {
            String[] idList = goodsIds.split(",");
            List<Integer> list = new ArrayList<>();
            for (String id : idList) {
                list.add(Integer.parseInt(id));
            }
            ResultInfo resultInfo = buyorderService.batchAddBhSaleorderGoodsNew(list, bhSaleorder, session);
            if (resultInfo.getCode().equals(0)) {
                mv.addObject("url",
                        request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                                + request.getContextPath() + "/order/saleorder/editBhSaleorder.do?saleorderId="
                                + bhSaleorder.getSaleorderId());
                return success(mv);
            } else {
                return fail(mv);
            }
        } else {
            return fail(mv);
        }
    }

    /**
     * <b>Description:</b><br>
     * 设置安全库存
     *
     * @param request
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年11月27日 下午1:39:35
     */
    @ResponseBody
    @RequestMapping(value = "/uplodegoodssafesotck")
    public ModelAndView uplodeGoodsSafeSotck(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("order/buyorder/uplode_goods_safe_stock");
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 保存安全库存
     *
     * @param request
     * @param lwfile
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年11月27日 下午3:11:19
     */
    @ResponseBody
    @RequestMapping("saveuplodegoodssafesotck")
    @SystemControllerLog(operationType = "import", desc = "保存安全库存")
    public ResultInfo<?> saveUplodeGoodsSafeSotck(HttpServletRequest request, HttpSession session,
                                                  @RequestParam("lwfile") MultipartFile lwfile) {
        ResultInfo<?> resultInfo = new ResultInfo<>();

        FileInputStream fileInputStream = null;
        Workbook workbook = null;
        try {
            User user = (User) session.getAttribute(Consts.SESSION_USER);
            // 临时文件存放地址
            String path = request.getSession().getServletContext().getRealPath("/upload/goods");
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);
            if (fileInfo.getCode() == 0) {
                List<GoodsSafeStock> list = new ArrayList<>();
                // 获取excel路径
                fileInputStream = new FileInputStream(new File(fileInfo.getFilePath()));
                workbook = WorkbookFactory.create(fileInputStream);
                // 获取第一面sheet
                Sheet sheet = workbook.getSheetAt(0);
                // 起始行
                int startRowNum = sheet.getFirstRowNum() + 1;
                int endRowNum = sheet.getLastRowNum();// 结束行

                for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {// 循环行数
                    Row row = sheet.getRow(rowNum);
                    int startCellNum = row.getFirstCellNum();// 起始列
                    int endCellNum = row.getLastCellNum() - 1;// 结束列
                    // 获取excel的值
                    GoodsSafeStock goodsSafeStock = new GoodsSafeStock();
                    if (user != null) {
                        goodsSafeStock.setCompanyId(user.getCompanyId());
                        goodsSafeStock.setModTime(DateUtil.gainNowDate());
                        goodsSafeStock.setUpdater(user.getUserId());
                    }
                    for (int cellNum = startCellNum; cellNum <= endCellNum; cellNum++) {// 循环列数（下表从0开始）
                        Cell cell = row.getCell(cellNum);

                        if (cellNum == 0) {// 第一列数据cellNum==startCellNum
                            // 若excel中无内容，而且没有空格，cell为空--默认3，空白
                            if (cell == null || cell.getCellType() != CellType.STRING) {
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列订货号错误！");
                                throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列订货号错误！");
                            } else {
                                goodsSafeStock.setSku(cell.getStringCellValue().toString());
                            }
                        }

                        if (cellNum == 3) {// 第二列数据cellNum==(startCellNum+1)
                            // 若excel中无内容，而且没有空格，cell为空--默认3，空白
                            if (cell == null) {
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列安全库存不允许为空！");
                                throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列安全库存不允许为空！");
                            } else if (cell.getCellType() != CellType.NUMERIC) {
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行第" + (cellNum + 1) + "列安全库存只能为数字！");
                                throw new Exception("表格项错误，第" + (rowNum + 1) + "行第" + (cellNum + 1) + "列安全库存只能为数字！");
                            } else if (cell.getNumericCellValue() > 999999999) {
                                resultInfo.setMessage(
                                        "表格项错误，第" + (rowNum + 1) + "行第" + (cellNum + 1) + "列安全库存数量不得超过999999999！");
                                throw new Exception(
                                        "表格项错误，第" + (rowNum + 1) + "行第" + (cellNum + 1) + "列安全库存数量不得超过999999999！");
                            } else {
                                goodsSafeStock.setNum((int) cell.getNumericCellValue());
                            }
                        }
                    }
                    list.add(goodsSafeStock);
                }

                // 保存更新
                resultInfo = goodsService.saveUplodeGoodsSafeSotck(list);
            }

        } catch (Exception e) {
            logger.error("saveuplodegoodssafesotck:", e);
            return resultInfo;
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (Exception e) {
                    log.error("【saveUplodeGoodsSafeSotck】处理异常",e);
                }
            }

            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    log.error("【saveUplodeGoodsSafeSotck】处理异常",e);
                }
            }
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 打印采购单
     *
     * @param request
     * @param buyorder
     * @param autoGenerate
     * @return
     * @Note <b>Author:</b> scott <br>
     * <b>Date:</b> 2017年12月14日 上午9:34:16
     */
    @ResponseBody
    @RequestMapping(value = "/printOrder")
    @NoNeedAccessAuthorization
    public ModelAndView printOrder(HttpServletRequest request, Buyorder buyorder, @RequestParam(required = false) Boolean autoGenerate) {

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        buyorder.setCompanyId(user.getCompanyId());
        BuyorderVo buyorderVo = buyorderService.getBuyOrderPrintInfo(buyorder);
        // BuyorderVo bv = buyorderService.getBuyorderVoDetail(buyorder, user);
        BuyorderVo buyorderInfo = buyorderService.getBuyorderInDetail(buyorderVo, user);


        //采购单中管是否包含医疗器械类型的SKU
        mv.addObject("haveMedicalApparatus", 0);

        if (CollectionUtils.isNotEmpty(buyorderVo.getBuyorderGoodsVoList())) {
            for (BuyorderGoodsVo buyOrderGood : buyorderVo.getBuyorderGoodsVoList()) {

                if (StringUtil.isEmpty(buyOrderGood.getSku())) {
                    continue;
                }

                CoreSpuGenerate spuInfo = this.vGoodsService.findSpuInfoBySkuNo(buyOrderGood.getSku());
                if (spuInfo == null) {
                    continue;
                }

                CoreSpuBaseDTO coreSpuDto = baseGoodsService.selectSpuBaseById(spuInfo.getSpuId());
                //医疗器械 注册证号不为空就是医疗机械
                if (StringUtils.isNotBlank(coreSpuDto.getRegistrationNumber())) {
                    mv.addObject("haveMedicalApparatus", 1);
                    break;
                }
            }
        }

        dealWithBdAfterSaleStandard(buyorderVo);

        // 获取采购人员信息
        UserDetail detail = userDetailMapper.getUserDetail(buyorderVo.getUserId());
        User userInfo = userService.getUserById(buyorderVo.getUserId());
        mv.addObject("orgId", userInfo.getOrgId());
        String username = userService.getUserById(buyorderVo.getUserId()).getUsername();

        Long currTime = DateUtil.sysTimeMillis();
        mv.addObject("currTime", DateUtil.convertString(currTime, "YYYY-MM-dd "));

        // 发票类型
        List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(428);
        mv.addObject("invoiceTypes", invoiceTypes);

        // 获取公司信息
        Company company = companyService.getCompanyByCompangId(user.getCompanyId());
        mv.addObject("company", company);

        ParamsConfigVo paramsConfigVo = new ParamsConfigVo();
        paramsConfigVo.setCompanyId(company.getCompanyId());
        paramsConfigVo.setParamsKey(100);
        AddressVo delivery = addressService.getDeliveryAddress(paramsConfigVo);

        mv.addObject("delivery", delivery);
        mv.addObject("detail", detail);
        mv.addObject("username", username);
        mv.addObject("buyorderVo", buyorderVo);
        // mv.addObject("bv", bv);
        // 运费类型
        List<SysOptionDefinition> yfTypes = getSysOptionDefinitionList(469);
        mv.addObject("yfTypes", yfTypes);
        mv.addObject("buyorderGoodsList", buyorderVo.getBuyorderGoodsVoList());
        BigDecimal pageTotalPrice = new BigDecimal(0.00);
        BigDecimal zioe = pageTotalPrice;
        Integer flag = -1;
        for (BuyorderGoodsVo buyorderGoods : buyorderVo.getBuyorderGoodsVoList()) {

            BigDecimal priceAll = buyorderGoods.getPrice();
            BigDecimal rebatePrice = Objects.isNull(buyorderGoods.getRebatePrice())?BigDecimal.ZERO:buyorderGoods.getRebatePrice();
            BigDecimal realPrice = priceAll.subtract(rebatePrice);
            buyorderGoods.setPrice(realPrice);
            String price = getCommaFormat(buyorderGoods.getPrice());
            if (!price.contains(".")) {
                price += ".00";
            }
            buyorderGoods.setPrices(price);
            String allprice = getCommaFormat(buyorderGoods.getPrice().multiply(new BigDecimal(buyorderGoods.getNum())));
            if (!allprice.contains(".")) {
                allprice += ".00";
            }
            buyorderGoods.setAllPrice(allprice);
            pageTotalPrice = pageTotalPrice
                    .add(buyorderGoods.getPrice().multiply(new BigDecimal(buyorderGoods.getNum())));
        }
        String totalAmount = getCommaFormat(pageTotalPrice);
        if (!totalAmount.contains(".")) {
            totalAmount += ".00";
        }
        mv.addObject("totalAmount", totalAmount);
        try {
            mv.addObject("chineseNumberTotalPrice", pageTotalPrice.compareTo(zioe) > 0
                    ? DigitToChineseUppercaseNumberUtils.numberToChineseNumber(pageTotalPrice) : null);
        } catch (ShowErrorMsgException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        mv.addObject("vedeng_address_phone", vedeng_address_phone.trim());
        mv.addObject("autoGenerate", autoGenerate);
        //处理合同日期
        Date contractDate = 0 == buyorderVo.getValidTime() ? new Date() : new Date(buyorderVo.getValidTime());
        List<String> contractDateList = Arrays.asList(Integer.toString(cn.hutool.core.date.DateUtil.year(contractDate))
                ,String.format("%02d",cn.hutool.core.date.DateUtil.month(contractDate)+1)
                ,String.format("%02d",cn.hutool.core.date.DateUtil.dayOfMonth(contractDate)));
        mv.addObject("contractDateList",contractDateList);
        if (user.getCompanyId() == 10) {
            if (buyorder.getDeliveryDirect() == 0) {
                mv.setViewName("order/buyorder/buyorder_pf_print");
            } else if (buyorder.getDeliveryDirect() == 1) {
                mv.setViewName("order/buyorder/buyoredr_zf_print");
            }
        } else {
            if (buyorder.getDeliveryDirect() == 0) {
                mv.setViewName("order/buyorder/order_pf_print");
            } else if (buyorder.getDeliveryDirect() == 1) {
                mv.setViewName("order/buyorder/order_zf_print");
            }
        }
        return mv;
    }

    // 每3位中间添加逗号的格式化显示
    public static String getCommaFormat(BigDecimal value) {
        return getFormat(",###.##", value);
    }

    // 自定义数字格式方法
    public static String getFormat(String style, BigDecimal value) {
        DecimalFormat df = new DecimalFormat();
        df.applyPattern(style);// 将格式应用于格式化器
        return df.format(value.doubleValue());
    }

    /**
     * <b>Description:</b><br>
     * 确认审核
     *
     * @param session
     * @param taskId
     * @param pass
     * @param type
     * @param buyorderId
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年1月3日 下午1:54:42
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/complement")
    public ModelAndView complement(HttpSession session, String taskId, Boolean pass, Integer type,
                                   @RequestParam(required = false) String pageType, Integer buyorderId,
                                   Integer payType, Integer relatedId, String expenseTaskId,
                                   @RequestParam(required = false, value = "refreshParent") Integer refreshParent) {
        ModelAndView mv = new ModelAndView();
        // VDERP-14398 费用大于10万，采购小于,taskId此时为null， expenseTaskId此时为费用单的
        if (taskId != null) {
            //防止多个用户点击审核操作，导致空指针的异常，如果查询不到对应的task，则弹出提示框，刷新父页面
            Task task = processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                mv.addObject("error_tips", "该采购单审核状态已发生变化，将刷新采购单详情页");
                mv.setViewName("order/buyorder/parent_reload");
                return mv;
            }
            String taskName = task.getName();
            if (("供应主管审核".equals(taskName) || "产品主管审核".equals(taskName)) && pass) {
                StringBuffer tips = new StringBuffer();
                this.buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorderId)
                        .forEach(buyorderGoodsVo -> {
                            if (buyorderGoodsVo.getOriginalPurchasePrice() != null) {
                                tips.append("sku ").append(buyorderGoodsVo.getSku())
                                        .append("的采购价与价格中心的已核定成本不一致(价格中心成本为")
                                        .append(buyorderGoodsVo.getOriginalPurchasePrice())
                                        .append("，采购价为").append(buyorderGoodsVo.getPrice()).append("),");
                                mv.addObject("purchasePriceChange", "1");
                            }
                        });
                tips.append("确认审核通过么?");
                mv.addObject("tips", tips.toString());
            }

            if ("T_PAY_APPLY".equals(pageType)) {
                Map<String, Object> historicInfoPay = actionProcdefService.getVariablesMap(task);
                Integer payApplyId = Integer.parseInt(historicInfoPay.get("relateTableKey").toString());
                PayApply payApply = payApplyService.getPayApplyInfo(payApplyId);
                String dongGe = "上海圆迈贸易有限公司（京东商城）";
                if (!dongGe.equals(payApply.getTraderName()) && SysOptionConstant.ID_521.equals(payApply.getTraderMode())) {
                    mv.addObject("isShow", ErpConst.ONE);
                }
                mv.addObject("paymentBankList", payApplyService.getPayVedengBankList());
            }
            if (null != buyorderId) {
                mv.addObject("buyorderId", buyorderId);
            } else {
                mv.addObject("buyorderId", 0);
            }
        }
        mv.addObject("pass", pass);
        mv.addObject("type", type);
        mv.addObject("pageType", pageType);
        mv.addObject("taskId", taskId);
        mv.addObject("expenseTaskId", expenseTaskId);
        logger.info("refreshParent弹窗8");
        mv.addObject("refreshParent",refreshParent);

        // VDERP-13386
        boolean expenseCheckStatus = true;
        if (payType != null && payType == 4125) {
            // 查询直属采购费用订单所属采购订单是否有审核不通过的付款申请
            BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.getOrderMainData(relatedId);
            if (buyorderExpenseDto != null) {
                List<PayApply> payApplyList = payApplyService.getPayApplyListByOrderIdAndPayType(517, buyorderExpenseDto.getBuyorderId(), 2);
                expenseCheckStatus = payApplyList.size() == 0;
                // 当为true时，即代表无审核不通过的，校验通过；否则代表校验不通过
            }
        }
        mv.addObject("expenseCheckStatus", expenseCheckStatus);
        mv.setViewName("order/buyorder/complement");
        return mv;
    }


    @RequestMapping(value = "/batchConfirmView")
    public ModelAndView batchConfirmView(@RequestParam(required = false) Integer[] ids) {
        int weatherShow = 1;
        boolean expenseCheckStatus = true;
        if (ids != null) {
            List<Integer> idList = Arrays.asList(ids);
            if (idList.size() > 0) {
                List<PayApply> payApplyListById = payApplyMapper.getPayApplyListById(idList);
                String dongGe = "上海圆迈贸易有限公司（京东商城）";
                for (PayApply payApply : payApplyListById) {
                    if (!(dongGe.equals(payApply.getTraderName()) || payApply.getTraderMode().equals(528))) {
                        weatherShow = 0;
                    }
                }

                for (PayApply payApply : payApplyListById) {
                    // VDERP-13386
                    if (payApply.getPayType() == 4125) {
                        // 查询直属采购费用订单所属采购订单是否有审核不通过的付款申请
                        BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.getOrderMainData(payApply.getRelatedId());
                        if (buyorderExpenseDto != null) {
                            List<PayApply> payApplyList = payApplyService.getPayApplyListByOrderIdAndPayType(517, buyorderExpenseDto.getBuyorderId(), 2);
                            if (payApplyList.size() > 0) {
                                expenseCheckStatus = false;
                                break;
                            }
                            // 当为true时，即代表无审核不通过的，校验通过；否则代表校验不通过
                        }
                    }
                }
            }
        }
        ModelAndView mv = new ModelAndView();
        mv.addObject("weatherShow", weatherShow);
        mv.addObject("paymentBankList", payApplyService.getPayVedengBankList());
        mv.addObject("expenseCheckStatus", expenseCheckStatus);
        mv.setViewName("order/buyorder/batchConfirmView");
        return mv;
    }


    /**
     * @param request
     * @param ids
     * @description: payApplyPassBatch.
     * @notes: VDERP-1215 付款申请增加批量操作功能.
     * @author: Tomcat.Hui.
     * @date: 2019/9/12 17:11.
     * @return: com.vedeng.common.model.ResultInfo.
     * @throws: .
     */
    @ResponseBody
    @RequestMapping(value = "/batchComplementTask")
    @SystemControllerLog(operationType = "edit", desc = "批量审核通过")
    @NoRepeatSubmit
    public ResultInfo batchComplementTask(HttpServletRequest request, String idListStr, HttpSession session, String validComments,
                                          Integer payVedengBankId) {

        String[] ids = idListStr.split(",");
        if (null != ids && ids.length > 0) {
            for (String idStr : ids) {

                //获取付款申请信息
                PayApply payApply = payApplyService.getPayApplyInfo(Integer.parseInt(idStr));
                Map<String, Object> historicInfoPay = actionProcdefService.getHistoric(processEngine, "paymentVerify_" + idStr);
                Task taskInfoPay = (Task) historicInfoPay.get("taskInfo");
                Map<Object, Object> candidateUserMapPay = (Map<Object, Object>) historicInfoPay.get("candidateUserMap");
                User user = (User) session.getAttribute(ErpConst.CURR_USER);

                boolean b1 = null != payApply && null != payApply.getPayType();
                boolean b2 = null != taskInfoPay && null != taskInfoPay.getId();
                boolean b3 = (null != taskInfoPay.getProcessInstanceId() && null != taskInfoPay.getAssignee()) || !((List) candidateUserMapPay.get(taskInfoPay.getId())).isEmpty();
                boolean b4 = user.getUsername().equals(taskInfoPay.getAssignee());
                boolean b5 = null != candidateUserMapPay.get("belong") && (boolean) candidateUserMapPay.get("belong") == true;


                if (b1 && b2 && b3 && (b4 || b5)) {

                    /** 开启子线程共享 ServletRequestAttributes .
                     * 在activiti中有使用到 RequestContextHolder.getRequestAttributes()
                     * 默认情况下该属性不会被传递到子线程，所以会出现 nullPointer
                     * */
                    ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                    RequestContextHolder.setRequestAttributes(sra, true);

                    if (payApply.getPayType().equals(517)) {
                        //采购
                        new Thread(() -> {
                            String uuid = UUID.randomUUID().toString().replace("-", "");
                            log.info("开始执行complementTask,uuid:{}",uuid);
                            log.info("批量付款申请-采购线程 {} 启动", Thread.currentThread().getId());
                            this.complementTask(request, taskInfoPay.getId(), validComments, true, payVedengBankId, 0, session, null);
                            log.info("结束执行complementTask,uuid:{}",uuid);
                        }).start();
                    } else if (payApply.getPayType().equals(518)) {
                        //售后
                        new Thread(() -> {
                            String uuid = UUID.randomUUID().toString().replace("-", "");
                            log.info("开始执行complementTaskSH方法，uuid：{}",uuid);
                            log.info("批量付款申请-售后线程 {} 启动", Thread.currentThread().getId());
                            this.complementTaskSH(request, taskInfoPay.getId(), validComments, true, payVedengBankId, 0, session);
                            log.info("结束执行complementTaskSH方法，uuid：{}",uuid);
                        }).start();
                    } else if (payApply.getPayType().equals(4125)) {
                        //采购费用
                        log.info("批量付款申请-采购费用线程 {} 启动", Thread.currentThread().getId());
                        new Thread(() -> {
                            String uuid = UUID.randomUUID().toString().replace("-", "");
                            log.info("开始执行complementTask方法，uuid：{}",uuid);
                            log.info("批量付款申请-采购线程 {} 启动", Thread.currentThread().getId());
                            this.complementTask(request, taskInfoPay.getId(), validComments, true, payVedengBankId, 0, session, null);
                            log.info("结束执行complementTask方法，uuid：{}",uuid);
                        }).start();
                    }
                }
            }
        }
        return new ResultInfo(0, "已提交处理，请注意跟进订单状态");
    }

    /**
     * <b>Description:</b><br>
     * 采购单审核操作
     *
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年11月10日 下午1:39:42
     */
    @ResponseBody
    @RequestMapping(value = "/complementTask")
    @SystemControllerLog(operationType = "edit", desc = "采购单审核操作")
    @NoRepeatSubmit
    public ResultInfo<?> complementTask(HttpServletRequest request, String taskId, String comment, Boolean pass,
                                        @RequestParam(required = false, defaultValue = "0") Integer payVedengBankId,
                                        Integer buyorderId, HttpSession session, String expenseTaskId) {
        //同意申请后，计入金蝶推送表
        // 获取session中user信息
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", pass);
        // 审批操作 (这边是原有逻辑，为了和VDERP-14398区分开)
        String tableName = "";
        try {
            if (taskId != null && !"".equals(taskId)) {
                // VDERP-14398 此处是原有逻辑，为了区分
                // 如果审核没结束添加审核对应主表的审核状态
                int status;
                TaskService taskService = processEngine.getTaskService();
                String id = (String) taskService.getVariable(taskId, "id");
                Integer idValue = (Integer) taskService.getVariable(taskId, "idValue");
                String key = (String) taskService.getVariable(taskId, "key");
                tableName = (String) taskService.getVariable(taskId, "tableName");
                // 使用任务id,获取任务对象，获取流程实例id
                Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
                String taskName = task.getName();
                if (pass) {
                    // 如果修改的主表是付款申请表，审核节点为财务制单，则修改制单状态
                    status = 0;
                    if ("T_PAY_APPLY".equals(tableName) && "财务制单".equals(taskName)) {
                        // 加锁
                        boolean lock = redisUtils.tryGetDistributedLock(Contant.REDIS_KEY_LOAD + idValue, UUID.randomUUID().toString(), Contant.LOCK_TIME);
                        if (!lock){
                            logger.info("五分钟内请勿重复提交，请检查支付状态并五分钟后重试,payApplyId:{}", idValue);

                            // 加锁失败
                            return new ResultInfo<>(-1,"五分钟内请勿重复提交，请检查支付状态并五分钟后重试");
                        }
                        // 制单
                        actionProcdefService.updateInfo(tableName, id, idValue, "IS_BILL", 1, 2);

                        PayApply updatePayApply = new PayApply();
                        updatePayApply.setPayApplyId(idValue);
                        updatePayApply.setBillTime(new Date());
                        updatePayApply.setBillMethod(0);
                        log.info("更新制单信息:{}", JSONObject.toJSONString(updatePayApply));
                        payApplyService.updateBillMethod(updatePayApply);
                        if (payVedengBankId != 0) {
                            //更新制单信息
                            payApplyService.updatePayApplyIsBillInfo(idValue, comment, payVedengBankId);
                        }
                    }
                } else {
                    // 如果审核不通过
                    status = 2;
                    // 回写数据的表在db中
                    variables.put("db", 2);
                    if ("T_BUYORDER".equals(tableName)) {
                        // 采购单申请不通过解锁
                        actionProcdefService.updateInfo(tableName, "BUYORDER_ID", buyorderId, "STATUS", 0, 2);
                        actionProcdefService.updateInfo(tableName, "BUYORDER_ID", buyorderId, "LOCKED_STATUS", 0, 2);
                    } else if ("T_PAY_APPLY".equals(tableName)) {
                        if (taskService.getVariable(taskId, "buyorderId") != null) {
                            buyorderId = (Integer) taskService.getVariable(taskId, "buyorderId");
                            // 采购单付款申请不通过解锁
                            actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", buyorderId, "LOCKED_STATUS", 0, 2);
                        } else {
                            //采购费用单付款申请不通过
                            Integer relatedId = (Integer) taskService.getVariable(taskId, "buyorderExpenseId");
                            actionProcdefService.updateInfo("T_BUYORDER_EXPENSE", "BUYORDER_EXPENSE_ID", relatedId, "LOCKED_STATUS",
                                    0, 2);
                        }
                        if (id != null && idValue != null && key != null) {
                            actionProcdefService.updateInfo(tableName, id, idValue, key, 2, 2);
                        }
                    } else if ("T_BUYORDER_MODIFY_APPLY".equals(tableName)) {
                        actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", buyorderId, "LOCKED_STATUS", 0, 2);
                    } else {
                        if (id != null && idValue != null && key != null) {
                            actionProcdefService.updateInfo(tableName, id, idValue, key, 2, 2);
                        }
                    }
                    verifiesRecordService.saveVerifiesInfo(taskId, status);
                    if (buyorderId != null && !ErpConst.ZERO.equals(buyorderId)) {
                        riskCheckService.resetBuyorderRiks(buyorderId);
                    }
                }


                if (StrUtil.isNotBlank(comment)){
                    payApplyService.updatePayApplyComment(idValue, comment);
                }
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment,
                        user.getUsername(), variables);
                // 如果未结束添加审核对应主表的审核状态
                if (!"endEvent".equals(complementStatus.getData())) {
                    verifiesRecordService.saveVerifiesInfo(taskId, status);
                }
            }

            // VDERP-14398 这个分支仅当 直属费用单大于10万 供应链产品总监审核时，才会进入
            if (expenseTaskId != null && !"".equals(expenseTaskId)) {
                int status;
                TaskService taskService = processEngine.getTaskService();
                String id = (String) taskService.getVariable(expenseTaskId, "id");
                Integer idValue = (Integer) taskService.getVariable(expenseTaskId, "idValue");
                String key = (String) taskService.getVariable(expenseTaskId, "key");
                tableName = (String) taskService.getVariable(expenseTaskId, "tableName");
                // 使用任务id,获取任务对象，获取流程实例id
                if (pass) {
                    status = 0;
                } else {
                    // 如果审核不通过
                    status = 2;
                    // 回写数据的表在db中
                    variables.put("db", 2);
                    //采购费用单付款申请不通过
                    Integer relatedId = (Integer) taskService.getVariable(expenseTaskId, "buyorderExpenseId");
                    actionProcdefService.updateInfo("T_BUYORDER_EXPENSE", "BUYORDER_EXPENSE_ID", relatedId, "LOCKED_STATUS", 0, 2);
                    if (id != null && idValue != null && key != null) {
                        actionProcdefService.updateInfo(tableName, id, idValue, key, 2, 2);
                    }
                    verifiesRecordService.saveVerifiesInfo(expenseTaskId, status);
                }
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, expenseTaskId, comment, user.getUsername(), variables);
                // 如果未结束添加审核对应主表的审核状态
                if (!"endEvent".equals(complementStatus.getData())) {
                    verifiesRecordService.saveVerifiesInfo(expenseTaskId, status);
                }
            }

            Buyorder buyorder = new Buyorder();
            if ("T_PAY_APPLY".equals(tableName)) {
                return new ResultInfo(0, "操作成功");
            } else {
                if (buyorderId != null && buyorderId != 0) {
                    buyorder.setBuyorderId(buyorderId);
                    BuyorderVo buyorderInfo = buyorderService.getBuyorderVoDetail(buyorder, user);
                    int statusInfo;
                    Map<String, Object> data = new HashMap<>();
                    if (buyorderInfo.getValidStatus() == 1) {
                        statusInfo = 1;
                        data.put("buyorderId", buyorderInfo.getBuyorderId());
                    } else {
                        statusInfo = 0;
                    }
                    return new ResultInfo(0, "操作成功", statusInfo, data);
                } else {
                    return new ResultInfo(0, "操作成功");
                }
            }
        } catch (Exception e) {
            logger.error("buy order complementTask:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }


    /**
     * 分离出来采购单审核的操作
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/complementTaskForBuyOrder")
    @SystemControllerLog(operationType = "edit", desc = "采购单审核操作")
    public ResultInfo<?> complementTaskForBuyOrder(HttpServletRequest request, String taskId,
                                                   String comment, Boolean pass,
                                                   Integer buyorderId) {
        //获取session中user信息
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", pass);

        // 审批操作
        try {

            TaskService taskService = processEngine.getTaskService();

            // 使用任务id,获取任务对象，获取流程实例id
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();

            if (task == null) {
                return new ResultInfo<>(-1, "流程此节点已操作完成，请确认");
            }

            List<BuyorderGoodsVo> buyOrderGoodList = this.buyorderGoodsMapper.queryBuyorderGoodListIncludeSpecialGoods(buyorderId);

            String businessKey = (String) taskService.getVariable(taskId, "businessKey");

            if ("产品经理或助理审核".equals(task.getName())) {


                List<BuyorderGoodsVo> unAuditSkuList = buyOrderGoodList.stream()
                        .filter(buyOrderGood -> {
                            return buyOrderGood.getProductAudit() == 0;
                        }).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(unAuditSkuList)) {

                    Set<String> uAuditorSet = new HashSet<>();
                    for (BuyorderGoodsVo buyorderGoodsVo : unAuditSkuList) {
                        uAuditorSet.addAll(Arrays.asList(buyorderGoodsVo.getProductBelongNameInfo().split(",")));
                    }

                    //Set<String> uAuditorSet = getProductManageAndAsistNameList(unAuditSkuList.stream().map(sku->sku.getSku()).collect(Collectors.toList()));

                    if (!uAuditorSet.contains(user.getUsername())) {
                        //判断当前人 对应的产品是否已经审核了 如果已经审核了 就直接返回
                        return new ResultInfo(-1, "当前产品已经有对应的产品经理和产品助理审核，请刷新页面重试");
                    }
                }
            }

            if (!pass) {

                // 采购单申请不通过解锁
                actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", buyorderId, "STATUS", 0, 2);
                actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", buyorderId, "LOCKED_STATUS", 0, 2);

                riskCheckService.resetBuyorderRiks(buyorderId);

                //如果未结束添加审核对应主表的审核状态
                verifiesRecordService.saveVerifiesInfo(taskId, 2);

                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);

                //更新主表审核状态
                buyorderMapper.saveVerifyStatus(buyorderId, 2);

                return new ResultInfo(0, "操作成功", buyorderId);
            }
            Map<String, Object> para = new HashMap<>();
            //当前审核人是谁
            if ("产品经理或助理审核".equals(task.getName())) {

//				//开启采购单的资质自动审核 自动审核如果报错，则不通过审核 检查一下 是否能够自动审核
                para = startQualifyAutoAudit(task, businessKey, buyorderId);

                for (BuyorderGoodsVo buyOrderGood : buyOrderGoodList) {

                    ProductManageAndAsistDto productManageAndAsistDto = convertToProductManageAndAsistDto(buyOrderGood);

                    if (currentUserIsProduct(user, productManageAndAsistDto)) {
                        BuyorderGoods updateSaleOrderGoods = new BuyorderGoods();
                        updateSaleOrderGoods.setBuyorderGoodsId(buyOrderGood.getBuyorderGoodsId());
                        updateSaleOrderGoods.setProductAudit(1);
                        buyorderGoodsMapper.updateByPrimaryKeySelective(updateSaleOrderGoods);
                    }

                }
                ;

                buyOrderGoodList = this.buyorderGoodsMapper.queryBuyorderGoodListIncludeSpecialGoods(buyorderId);

                List<BuyorderGoodsVo> unAuditSkuList = buyOrderGoodList.stream()
                        .filter(buyOrderGood -> {
                            return buyOrderGood.getProductAudit() == 0;
                        }).collect(Collectors.toList());

                //还有未审核的产品
                if (CollectionUtils.isNotEmpty(unAuditSkuList)) {

                    taskService.setVariable(taskId, "allPass", false);

                    //完成当前任务
                    actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);

                    Set<String> uAuditorSet = new HashSet<>();
                    for (BuyorderGoodsVo buyorderGoodsVo : unAuditSkuList) {
                        uAuditorSet.addAll(Arrays.asList(buyorderGoodsVo.getProductBelongNameInfo().split(",")));
                    }

                    //设置下个任务的候选人
                    setTaskCandidateUser(taskService, businessKey, uAuditorSet);

                    verifiesRecordService.saveVerifiesInfo(taskId, 0);

                    return new ResultInfo(0, "操作成功", buyorderId);
                }

                taskService.setVariable(taskId, "allPass", true);

            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);

            // 如果未结束添加审核对应主表的审核状态
            if (!complementStatus.getData().equals("endEvent")) {
                verifiesRecordService.saveVerifiesInfo(taskId, 0);
            }

            //开启采购单的资质自动审核 自动审核如果报错，则不通过审核
            //Map<String, Object> para=startQualifyAutoAudit(task,businessKey,buyorderId);
            //操作人审核通过之后，再根据自动审核的结果做自动审核操作
            boolean qualifyAutoVerifyEndFlag = autoCheck(para, businessKey);

            // 如果是人工审核结束，或者资质自动审核结束，执行返利结算相关操作
            if (ErpConstant.END_EVENT.equals(complementStatus.getData()) || qualifyAutoVerifyEndFlag) {
                // 采购订单审核通过，执行返利结算相关操作
                buyOrderRebateApiService.passAuditBuyOrderRebateProcess(buyorderId);
            }
            return new ResultInfo(0, "操作成功", buyorderId);
        } catch (IllegalArgumentException ar) {
            logger.info("complementTaskForBuyOrder :", ar);
            return new ResultInfo(-1, "任务完成操作失败：" + ar.getMessage());
        } catch (Exception e) {
            logger.error("complementTaskForBuyOrder :", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        } finally {
            ThreadLocalContext.removeAll();
        }
    }

    //采购单的资质自动审核
    private Map<String, Object> startQualifyAutoAudit(Task task, String businessKey, Integer buyorderId) {

        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderId);

        //如果当前节点是产品经理或助理审核审核 如果审核通过 那么要开启资质自动审核流程 否则就无需开启
        if (!("产品经理或助理审核".equals(task.getName()) && buyorder.getNewFlow() == 1)) {
            return new HashMap<>();
        }


        List<BuyorderGoodsVo> buyorderGoodsVoList = this.buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorderId);

        //审核通过消息需要去重
        Set<String> auditPassMessageSet = new HashSet<>();

        //审核不通过原因
        String auditNotPassReason = null;

        boolean qualifyAutoVerify = true;

        for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsVoList) {
            logger.info("采购单自动审核 开始   {} {} ", buyorderId, buyorderGoodsVo.getBuyorderGoodsId());
            ValidaterResult validatorResult = this.qualifyAutoAudtioValidatorChain.validator(buyorderGoodsVo);

            if (!validatorResult.isValidatePass()) {
                auditNotPassReason = validatorResult.getAuditFailMessage();
                logger.info("采购单自动审核 错误   {} {} {}", buyorderId, buyorderGoodsVo.getBuyorderGoodsId(), auditNotPassReason);
                qualifyAutoVerify = false;
                break;
            }
            logger.info("采购单自动审核 结束  {} {} ", buyorderId, buyorderGoodsVo.getBuyorderGoodsId());
            auditPassMessageSet.add(validatorResult.getAuditPassMessage());
        }
        ;

        Map<String, Object> variableMap = new HashMap<String, Object>();
        variableMap.put("qualifyAutoVerify", qualifyAutoVerify);

        String comment = qualifyAutoVerify ? StringUtils.join(auditPassMessageSet, ",") : auditNotPassReason;

        variableMap.put("comment", comment);
        return variableMap;
    }

    private boolean autoCheck(Map<String, Object> para, String businessKey) {
        if (StringUtils.isBlank(Utils.trimNull(para.get("qualifyAutoVerify")))) {
            return false;
        }
        Task taskInfo = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
        String taskId = taskInfo.getId();
        Map<String, Object> variableMap = new HashMap<String, Object>();
        variableMap.put("qualifyAutoVerify", para.get("qualifyAutoVerify"));
        ResultInfo<?> complementStatus2 = actionProcdefService.complementTask(null, taskInfo.getId(),
                Utils.trimNull(para.get("comment")),
                "njadmin", variableMap);
        // 如果未结束添加审核对应主表的审核状态
        if (!complementStatus2.getData().equals("endEvent")) {
            verifiesRecordService.saveVerifiesInfo(taskId, 0);
        }
        return ErpConstant.END_EVENT.equals(complementStatus2.getData());
    }

    private ProductManageAndAsistDto convertToProductManageAndAsistDto(BuyorderGoods buyorderGoods) {
        ProductManageAndAsistDto productManageAndAsistDto = new ProductManageAndAsistDto();
        productManageAndAsistDto.setProductAssitName(buyorderGoods.getProductBelongNameInfo().split(",")[0]);
        productManageAndAsistDto.setProductManageName(buyorderGoods.getProductBelongNameInfo().split(",")[1]);
        String[] productBelongId = buyorderGoods.getProductBelongIdInfo().split(",");
        if (StringUtils.isNumeric(productBelongId[0])) {
            productManageAndAsistDto.setProductAssitUserId(Integer.valueOf(productBelongId[0]));
        }
        if (StringUtils.isNumeric(productBelongId[1])) {
            productManageAndAsistDto.setProductManageUserId(Integer.valueOf(productBelongId[1]));
        }
        return productManageAndAsistDto;
    }

    /**
     * 当前用户是否是产品助理后者产品经理
     *
     * @param user
     * @param productManageAndAsistDto
     * @return
     */
    private boolean currentUserIsProduct(User user, ProductManageAndAsistDto productManageAndAsistDto) {

        if (user.getUserId().equals(productManageAndAsistDto.getProductManageUserId()) ||
                user.getUserId().equals(productManageAndAsistDto.getProductAssitUserId())) {
            return true;
        }

        return false;
    }

    /**
     * 设置任务候选人
     *
     * @param taskService
     * @param businessKey
     * @param manageAndAsistNameSet
     */
    private void setTaskCandidateUser(TaskService taskService, String businessKey, Set<String> manageAndAsistNameSet) {

        Task nextTask = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();

        for (String manageAndAsistName : manageAndAsistNameSet) {
            processEngine.getTaskService().addCandidateUser(nextTask.getId() + "", manageAndAsistName);
        }
        ;

    }

    /**
     * <b>Description:</b><br>
     * 采购单售后审核操作
     *
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年11月10日 下午1:39:42
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/complementAfterSaleTask")
    @SystemControllerLog(operationType = "edit", desc = "采购单售后审核操作")
    public ResultInfo<?> complementAfterSaleTask(HttpServletRequest request, String taskId, String comment,
                                                 Boolean pass, HttpSession session) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("pass", pass);
        // 审批操作
        try {
            if (!pass) {
                // 如果不通过审核
                TaskService taskService = processEngine.getTaskService();// 获取任务的Service，设置和获取流程变量
                taskService.setVariable(taskId, "value", 3);
                String tableName = (String) taskService.getVariable(taskId, "tableName");
                String id = (String) taskService.getVariable(taskId, "id");
                Integer idValue = (Integer) taskService.getVariable(taskId, "idValue");
                String key = (String) taskService.getVariable(taskId, "key");
                if (tableName != null && id != null && idValue != null && key != null) {
                    actionProcdefService.updateInfo(tableName, id, idValue, key, 3, 2);
                }
            }
            // 如果审核没结束添加审核对应主表的审核状态
            Integer status = 0;
            if (pass) {
                // 如果审核通过
                status = 0;
            } else {
                // 如果审核不通过
                status = 2;
                verifiesRecordService.saveVerifiesInfo(taskId, status);

            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment,
                    user.getUsername(), variables);
            // 如果未结束添加审核对应主表的审核状态
            if (!complementStatus.getData().equals("endEvent")) {
                verifiesRecordService.saveVerifiesInfo(taskId, status);
            }
            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("complementAfterSaleTask:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    /**
     * <b>Description:</b><br>
     * 采购订单确认收货初始化页面
     *
     * @param request
     * @param buyorder
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2018年2月11日 下午4:12:51
     */
    @ResponseBody
    @RequestMapping(value = "confirmArrivalInit")
    public ModelAndView confirmArrivalInit(HttpServletRequest request, Buyorder buyorder) {
        ModelAndView mv = new ModelAndView("order/buyorder/confirm_arrival_init");
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        BuyorderVo bv = buyorderService.getBuyorderVoDetail(buyorder, user);

        // 根据采购skuId查询物流，根据物流分组
        buyorderService.logisticsGroupingByBuyOrderGoodsId(bv);

        // 订单流 区分页面跳转
        if (ErpConst.ONE.equals(buyorder.getIsNew())) {
            mv.addObject("isNew", 1);
        }
        mv.addObject("buyorderVo", bv);
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 采购订单确认收货
     *
     * @param request
     * @param buyorderId
     * @param id_arrivalNum 快递ID_采购SKU(ID)_到货数量
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2018年2月11日 下午4:13:14
     */
    @ResponseBody
    @RequestMapping(value = "/confirmArrival")
    public ResultInfo confirmArrival(HttpServletRequest request,
                                     Integer buyorderId,
                                     String id_arrivalNum,
                                     Integer isNew,
                                     String id_nonAllArrivalReason) {
        ResultInfo resultInfo = new ResultInfo();
        try {
            Buyorder buyorder = new Buyorder();
            buyorder.setBuyorderId(buyorderId);

            //VDERP-8287 【账期】客户账期监管添加直接收货的场景
            boolean checkFlag = buyorderService.checkNumByExpress(buyorderId, id_arrivalNum);
            if (!checkFlag) {
                resultInfo.setCode(-1);
                resultInfo.setMessage("产品的发货数少于收货数，请先至物流记录中新增快递，无法获知单号可勾选获取失败！");
                return resultInfo;
            }

            buyorder = buyorderService.confirmArrival(buyorder, request, id_arrivalNum, id_nonAllArrivalReason);

            this.confirmArrivalSaveBuyorderInvoiceStatus(buyorder.getBuyorderId());
            // VDERP-8759 订单流发货状态同步
            buyorderInfoSyncService.syncDeliveryStatus(buyorder.getBuyorderId(), 1);

            //更新不可见虚拟商品收货
            List<BuyorderGoods> goodsList = buyorder.getGoodsList();
            List<Integer> buyorderGoodIds = goodsList.stream().filter(o -> o.getArrivalNum() != null && o.getArrivalNum() > 0)
                    .map(BuyorderGoods::getBuyorderGoodsId).collect(Collectors.toList());
//            saleOrderGoodsApiService.doNoSeeGoodsArrivalByBuyOrderId(buyorderGoodIds);

            // 更新直发采购单关联的直属费用单的收货状态
            BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.queryAttributiveExpenseInfoByBuyorderId(buyorder.getBuyorderId());
            if (Objects.nonNull(buyorderExpenseDto)) {
                buyorderExpenseApiService.doArrivalStatus(buyorderExpenseDto.getBuyorderExpenseId());
                saleOrderGoodsApiService.dosaleDeliveryStatus(buyorderExpenseDto.getBuyorderExpenseId());
                saleOrderGoodsApiService.doConfirmArrival(buyorderExpenseDto.getBuyorderExpenseId());
                log.info("直发采购单确认收货同步更新直属费用单的收货状态，采购费用单：{}", JSON.toJSONString(buyorderExpenseDto));
                List<Integer> saleOrderIds = rBuyorderExpenseJSaleorderService.findSaleOrderIds(buyorderExpenseDto.getBuyorderExpenseId());
                if (saleOrderIds.size()>0){
                    for (Integer saleOrderId : saleOrderIds) {
                        log.info("开始校验销售单收发货状态,销售单Id：{}",saleOrderId);
                        saleOrderApiService.checkSaleorderDeliveryAndArrivalStatus(saleOrderId);
                    }
                }
            }

            if (null != buyorder) {
                resultInfo.setCode(0);
                resultInfo.setMessage("成功！");

                // 订单流 区分页面跳转
                if (!Objects.isNull(isNew) && isNew == 1) {
                    resultInfo.setData("/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=" + buyorder.getBuyorderId());
                } else {
                    resultInfo.setData("/viewBuyorder.do?buyorderId=" + buyorder.getBuyorderId());
                }

            } else {
                resultInfo.setCode(1);
                resultInfo.setMessage("失败！");
            }
            return resultInfo;
        } catch (Exception e) {
            logger.error("confirmArrival:", e);
            resultInfo.setCode(1);
            resultInfo.setMessage("失败！");
            return resultInfo;
        }
    }

    @ResponseBody
    @RequestMapping(value = "/cancelOrder")
    public ResultInfo cancelOrder(@RequestParam("buyorderNo") String buyorderNo) {
        try {


            logger.info("采购订单" + buyorderNo + "修改,请求WMS是否能取消================start");

			/*//取消订单参数对象
			CancelPoDto cancelPoDto = new CancelPoDto();
			cancelPoDto.setDocNo(buyorderNo);
			cancelPoDto.setPoType(WmsInterfaceOrderType.INPUT_PURCHASE);
			cancelPoDto.setErpCancelReason("采购订单修改撤销");

			//撤销采购单申请 TODO Holiis ok
			WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.CANCEL_PO);
			WmsResponse wmsResponse = wmsInterface.request(cancelPoDto);*/

            //失败且 不是订单不存在这种情况才可以
            if (!cancelTypeService.cancelInputPurchaseMethod(buyorderNo, "采购订单修改撤销")) {
                return new ResultInfo(-1, "采购订单修改撤销失败");
            }
            return new ResultInfo(0, "成功");

        } catch (Exception e) {
            logger.error("BuyorderController -> cancelOrder error: ", e);
            return new ResultInfo(-1, "失败");
        }
    }


    /**
     * <b>Description:</b><br>
     * 采购订单申请修改
     *
     * @param request
     * @param buyorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年5月23日 上午9:22:26
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "modifyApplyInit")
    public ModelAndView modifyApplyInit(HttpServletRequest request, Buyorder buyorder) {
        ModelAndView mv = new ModelAndView("order/buyorder/modify_apply_init");
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        buyorder.setCompanyId(user.getCompanyId());
        BuyorderVo bv = buyorderService.getBuyorderVoApplyUpdateDetail(buyorder);
        mv.addObject("bv", bv);

        // 普发收货地址
        ParamsConfigValue paramsConfigValue = new ParamsConfigValue();
        paramsConfigValue.setCompanyId(user.getCompanyId());
        paramsConfigValue.setParamsConfigId(ErpConst.TWO);
        // 2018-2-4 查询全部收货地址
        mv.addObject("addressList", buyorderService.getAddressVoList(paramsConfigValue));

        // 获取公司信息
        Company company = companyService.getCompanyByCompangId(user.getCompanyId());
        mv.addObject("companyName", company.getCompanyName());

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(bv.getBgvList())) {

            List<Integer> skuIds = new ArrayList<>();

            bv.getBgvList().stream().forEach(buyOrderGoods -> {
                skuIds.add(buyOrderGoods.getGoodsId());
            });

            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        // 收票种类
        List<SysOptionDefinition> receiptTypes = getSysOptionDefinitionList(SysOptionConstant.ID_428);
        mv.addObject("receiptTypes", receiptTypes);
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 保存采购申请修改
     *
     * @param request
     * @param buyorderGoodsVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年3月6日 上午10:59:38
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveBuyorderApplyUpdate")
    @SystemControllerLog(operationType = "edit", desc = "保存采购申请修改")
    public ModelAndView saveBuyorderApplyUpdate(HttpServletRequest request, BuyorderModifyApply buyorderModifyApply,
                                                BuyorderModifyApplyGoodsVo buyorderModifyApplyGoodsVo) {
        logger.info("getOldInsideCommentsArray : " + Arrays.toString(buyorderModifyApplyGoodsVo.getOldInsideCommentsArray()));
        logger.info("getSendGoodsTimeStrArray : " + Arrays.toString(buyorderModifyApplyGoodsVo.getSendGoodsTimeStr()));
        logger.info("getOldSendGoodsTimeStrArray : " + Arrays.toString(buyorderModifyApplyGoodsVo.getOldSendGoodsTimeStr()));
        logger.info("getReceiveGoodsTimeStrArray : " + Arrays.toString(buyorderModifyApplyGoodsVo.getReceiveGoodsTimeStr()));
        logger.info("getOldReceiveGoodsTimeStrArray : " + Arrays.toString(buyorderModifyApplyGoodsVo.getOldReceiveGoodsTimeStr()));
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView();
        buyorderModifyApply.setCompanyId(user.getCompanyId());
        buyorderModifyApply.setAddTime(DateUtil.sysTimeMillis());
        buyorderModifyApply.setCreator(user.getUserId());
        ResultInfo res = buyorderService.saveBuyorderApplyUpdate(buyorderModifyApply, buyorderModifyApplyGoodsVo);
        if (null != res && res.getCode() == 0) {
            // 原先只能获取id ,但是db不能发送站内信，所以将采购商品id集合回传 key: [buyOrderGoodsIdList]
//			Map data = (Map)res.getData();
            // 生成流程
            try {
                // 获取订单修改信息
                buyorderModifyApply.setBuyorderModifyApplyId(Integer.parseInt(String.valueOf(res.getData())));
                BuyorderModifyApplyVo bmav = buyorderService.getBuyorderModifyApplyVoDetail(buyorderModifyApply);
                Map<String, Object> variableMap = new HashMap<String, Object>();
                // 开始生成流程(如果没有taskId表示新流程需要生成)
                variableMap.put("buyorderModifyApplyInfo", bmav);
                variableMap.put("orderId", buyorderModifyApply.getBuyorderId());
                variableMap.put("currentAssinee", user.getUsername());
                variableMap.put("processDefinitionKey", "editBuyorderVerify");
                variableMap.put("businessKey", "editBuyorderVerify_" + buyorderModifyApply.getBuyorderModifyApplyId());
                variableMap.put("relateTableKey", buyorderModifyApply.getBuyorderModifyApplyId());
                variableMap.put("relateTable", "T_BUYORDER_MODIFY_APPLY");
                // 设置审核完成监听器回写参数
                variableMap.put("tableName", "T_BUYORDER_MODIFY_APPLY");
                variableMap.put("id", "BUYORDER_MODIFY_APPLY_ID");
                variableMap.put("idValue", buyorderModifyApply.getBuyorderModifyApplyId());
                variableMap.put("idValue1", buyorderModifyApply.getBuyorderId());
                variableMap.put("key", "VALID_STATUS");
                variableMap.put("value", 1);
                // 回写数据的表在db中
                variableMap.put("db", 2);
                actionProcdefService.createProcessInstance(request, "editBuyorderVerify",
                        "editBuyorderVerify_" + buyorderModifyApply.getBuyorderModifyApplyId(), variableMap);
                // 默认申请人通过
                // 根据BusinessKey获取生成的审核实例
                Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                        "editBuyorderVerify_" + buyorderModifyApply.getBuyorderModifyApplyId());
                if (historicInfo.get("endStatus") != "审核完成") {
                    Saleorder saleorderLocked = new Saleorder();
                    Task taskInfo = (Task) historicInfo.get("taskInfo");
                    String taskId = taskInfo.getId();
                    Authentication.setAuthenticatedUserId(user.getUsername());
                    Map<String, Object> variables = new HashMap<>();
                    // 产品总监默认审批通过
                    String startUser = (String) historicInfo.get("startUser");
                    boolean rolefalg = false;
                    if (startUser != null) {
                        rolefalg = userService.isRoledirector(startUser);
                    }

                    // VDERP-8297 订单流 修改了流程图 所以在此处增加了标志位永远为true,兼容后保证老的页面操作不会进入新的分支
                    variables.put("cancelFlag", true);

                    variables.put("pass", rolefalg);
                    ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                            user.getUsername(), variables);
                    // 如果未结束添加审核对应主表的审核状态
                    if (!complementStatus.getData().equals("endEvent")) {
                        verifiesRecordService.saveVerifiesInfo(taskId, 0);
                    }
                }
            } catch (Exception e) {
                logger.error("saveBuyorderApplyUpdate:", e);
                mav.addObject("message", "任务完成操作失败：" + e.getMessage());
                return fail(mav);
            }


            mav.addObject("url", "./viewModifyApply.do?buyorderModifyApplyId=" + res.getData());
            return success(mav);
        } else {
            return fail(mav);
        }
    }

    /**
     * <b>Description:</b><br>
     * 添加运费
     *
     * @param request
     * @param session
     * @param buyorder
     * @return
     * @throws IOException
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2018年2月11日 下午4:13:14
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addFreightPage")
    public ModelAndView addFreightPage(HttpServletRequest request, BuyorderGoodsVo buyorderGoodsVo) throws IOException {
        ModelAndView mav = new ModelAndView("order/buyorder/add_freight");
        buyorderGoodsVo = buyorderService.getFreightByBuyorderId(buyorderGoodsVo);
        mav.addObject("bgv", buyorderGoodsVo);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(buyorderGoodsVo)));
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 保存采购订单的运费
     *
     * @param request
     * @param buyorderGoodsVo
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年3月6日 上午10:59:38
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveBuyorderFreight")
    @SystemControllerLog(operationType = "edit", desc = "保存采购订单的运费")
    public ResultInfo<?> saveBuyorderFreight(HttpServletRequest request, BuyorderGoodsVo buyorderGoodsVo) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ResultInfo res = buyorderService.saveBuyorderFreight(buyorderGoodsVo, user);
        return res;
    }

    /**
     * <b>Description:</b><br>
     * 保存采购修改订单生效状态
     *
     * @param request
     * @param buyorderGoods
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年7月13日 上午9:31:43
     */
    @ResponseBody
    @RequestMapping(value = "/saveApplyBuyorderModfiy")
    @SystemControllerLog(operationType = "add", desc = "保存采购修改订单生效状态")
    public ResultInfo<?> saveApplyBuyorderModfiy(HttpServletRequest request, BuyorderModifyApply buyorderModifyApply) {
        // User user = (User)
        // request.getSession().getAttribute(ErpConst.CURR_USER);
        buyorderModifyApply.setValidStatus(1);
        buyorderModifyApply.setValidTime(DateUtil.sysTimeMillis());
        ResultInfo<?> res = buyorderService.saveApplyBuyorderModfiyValidStatus(buyorderModifyApply);
        return res;
    }

    /**
     * <b>Description:</b><br>
     * 采购订单修改列表
     *
     * @param request
     * @param buyorderModifyApply
     * @param pageNo
     * @param pageSize
     * @return
     * @Note <b>Author:</b> <br>
     * <b>Date:</b>
     */
    @ResponseBody
    @RequestMapping(value = "getBuyorderModifyApplyListPage")
    public ModelAndView getBuyorderModifyApplyListPage(HttpServletRequest request,
                                                       BuyorderModifyApplyVo buyorderModifyApplyVo,
                                                       @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                                       @RequestParam(required = false) Integer pageSize) {
        ModelAndView mv = new ModelAndView("order/buyorder/modify_apply_index");
        Page page = getPageTag(request, pageNo, pageSize);
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        try {
            buyorderModifyApplyVo.setCompanyId(user.getCompanyId());
            Map<String, Object> map = buyorderService.getBuyorderModifyApplyListPage(buyorderModifyApplyVo, page);
            if (map != null && map.containsKey("list")) {
                mv.addObject("list", (List<BuyorderModifyApplyVo>) map.get("list"));
            }
            if (map != null && map.containsKey("page")) {
                mv.addObject("page", (Page) map.get("page"));
            }
            mv.addObject("bmav", buyorderModifyApplyVo);
            return mv;
        } catch (Exception e) {
            logger.error("getBuyorderModifyApplyListPage:", e);
            return pageNotFound(request);
        }
    }

    /**
     * <b>Description:</b><br>
     * 查看订单修改详情
     *
     * @param request
     * @param saleorderModifyApply
     * @return
     * @Note <b>Author:</b> <br>
     * <b>Date:</b>
     */
    @ResponseBody
    @RequestMapping(value = "viewModifyApply")
    public ModelAndView viewModifyApply(HttpServletRequest request, BuyorderModifyApply buyorderModifyApply) throws Exception {

        User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        mv.addObject("curr_user", curr_user);
        buyorderModifyApply.setCompanyId(curr_user.getCompanyId());

        // 校验订单流新旧跳转
        Integer newBuyOrderModifyApply = isNewBuyOrderModifyApply(buyorderModifyApply.getBuyorderModifyApplyId());
        if (Integer.valueOf(1).equals(newBuyOrderModifyApply)) {
            mv.setViewName("redirect:/order/newBuyorder/viewModifyApply.do?buyorderModifyApplyId=" + buyorderModifyApply.getBuyorderModifyApplyId());
            return mv;
        }

        // 获取订单修改信息
        BuyorderModifyApplyVo bmav = buyorderService.getBuyorderModifyApplyVoDetail(buyorderModifyApply);
        mv.addObject("bmav", bmav);

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(bmav.getBgvList())) {
            List<Integer> skuIds = new ArrayList<>();
            bmav.getBgvList().stream().forEach((BuyorderGoodsVo buyorderGoods) -> {
                skuIds.add(buyorderGoods.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end


        // 发票类型
        List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(428);
        mv.addObject("invoiceTypes", invoiceTypes);

        // 订单修改审核信息
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                "editBuyorderVerify_" + buyorderModifyApply.getBuyorderModifyApplyId());
        mv.addObject("taskInfo", historicInfo.get("taskInfo"));
        mv.addObject("startUser", historicInfo.get("startUser"));
        // 最后审核状态
        mv.addObject("endStatus", historicInfo.get("endStatus"));
        mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        mv.addObject("commentMap", historicInfo.get("commentMap"));
        mv.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        Task taskInfo = (Task) historicInfo.get("taskInfo");
        // 当前审核人
        String verifyUsers = null;
        List<String> verifyUserList = new ArrayList<>();
        if (null != taskInfo) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfo);
            verifyUsers = (String) taskInfoVariables.get("verifyUsers");
            String verifyUser = (String) taskInfoVariables.get("verifyUserList");
            if (null != verifyUser) {
                verifyUserList = Arrays.asList(verifyUser.split(","));
            }
        }
        List<String> verifyUsersList = new ArrayList<>();
        if (verifyUsers != null) {
            verifyUsersList = Arrays.asList(verifyUsers.split(","));
        }
        mv.addObject("verifyUsers", verifyUsers);
        mv.addObject("verifyUserList", verifyUserList);
        mv.addObject("verifyUsersList", verifyUsersList);

        mv.setViewName("order/buyorder/view_modify_apply");
        return mv;
    }

    /**
     * @param request
     * @param taskId
     * @param comment
     * @param pass
     * @param buyorderId
     * @param session
     * @description: complementTaskSH.
     * @notes: add by Tomcat.Hui 2019/9/11 13:05 .Desc: VDERP-1215 付款申请增加批量操作功能
     * (从com.vedeng.finance.controller.InvoiceAfterController#complementTask()中拷贝而来).
     * @author: Tomcat.Hui.
     * @date: 2019/9/18 13:57.
     * @return: com.vedeng.common.model.ResultInfo<?>.
     * @throws: .
     */
    public ResultInfo<?> complementTaskSH(HttpServletRequest request, String taskId, String comment, Boolean pass,
                                          @RequestParam(required = false, defaultValue = "0") Integer payVedengBankId,
                                          Integer buyorderId, HttpSession session) {
        // 获取session中user信息
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("pass", pass);
        // 审批操作
        try {
            // 如果审核没结束添加审核对应主表的审核状态
            Integer status = 0;
            TaskService taskService = processEngine.getTaskService();// 获取任务的Service，设置和获取流程变量
            String id = (String) taskService.getVariable(taskId, "id");
            Integer idValue = (Integer) taskService.getVariable(taskId, "idValue");
            String key = (String) taskService.getVariable(taskId, "key");
            String tableName = (String) taskService.getVariable(taskId, "tableName");
            // 使用任务id,获取任务对象，获取流程实例id
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            String taskName = task.getName();

            PayApply payApply = payApplyService.getPayApplyInfo(idValue);
            if (payApply == null) {
                return new ResultInfo(-1, "退款支付申请不存在");
            }

            if (pass) {
                // 如果审核通过
                status = 0;
                //如果修改的主表是付款申请表，审核节点为财务制单，则修改制单状态
                if (tableName.equals("T_PAY_APPLY") && taskName.equals("财务制单")) {
                    // 加锁
                    boolean lock = redisUtils.tryGetDistributedLock(Contant.REDIS_KEY_LOAD + idValue, UUID.randomUUID().toString(), Contant.LOCK_TIME);
                    if (!lock){
                        logger.info("五分钟内请勿重复提交，请检查支付状态并五分钟后重试,payApplyId:{}", idValue);

                        // 加锁失败
                        return new ResultInfo<>(-1,"五分钟内请勿重复提交，请检查支付状态并五分钟后重试");
                    }
                    //根据付款申请查询关联表，如果是售后，并且是售后类型是539,543；
                    //VDERP-2193 如果是售后退货、退款生成的支付申请，那么在制单之前增加限制：退款金额不能大于账户余额
                    TraderCustomer traderCustomer = null;
                    if (payApply.getPayType().equals(SysOptionConstant.ID_518)) {

                        AfterSales afterSales = afterSalesOrderService.getAfterSalesById(payApply.getRelatedId());
                        if (SysOptionConstant.ID_539.equals(afterSales.getType()) || SysOptionConstant.ID_543.equals(afterSales.getType())) {
                            //获取售后支付申请客户的余额信息
                            traderCustomer = traderCustomerService.getTraderByPayApply(idValue);
                            if (payApply.getAmount().compareTo(traderCustomer.getAmount()) > 0) {
                                return new ResultInfo<>(-1, "退款金额大于账户余额，无法退款");
                            }
                        }

                    }


                    //制单
                    actionProcdefService.updateInfo(tableName, id, idValue, "IS_BILL", 1, 2);

                    //更新制单信息
                    payApplyService.updatePayApplyIsBillInfo(idValue, comment, payVedengBankId);

                    PayApply updatePayApply = new PayApply();
                    updatePayApply.setPayApplyId(idValue);
                    updatePayApply.setBillTime(new Date());
                    updatePayApply.setBillMethod(0);
                    log.info("更新制单信息:{}", JSONObject.toJSONString(updatePayApply));
                    payApplyService.updateBillMethod(updatePayApply);

                    ////VDERP-2193 如果是售后退货、退款生成的支付申请，那么制单成功则扣减余额
                    if (traderCustomer != null) {
                        traderCustomerService.updateTraderAmount(traderCustomer.getTraderId(), payApply.getAmount().multiply(new BigDecimal(-1)));
                        logger.info("售后订单：{}在财务制单环节，余额扣减金额：{}", payApply.getRelatedId(), payApply.getAmount());
                    }

                }
            } else {
                // 如果审核不通过
                status = 2;
                // 回写数据的表在db中
                variables.put("db", 2);
                if (tableName != null && id != null && idValue != null && key != null) {
                    actionProcdefService.updateInfo(tableName, id, idValue, key, 2, 2);
                }
                verifiesRecordService.saveVerifiesInfo(taskId, status);

                // 流程 paymentVerify:3:1792504 的财务审核节点，点击不通过，即售后退款财务审核不通过；
                // VDERP-2193 将售后退款流程中制单环节提前扣减余额的款项补加回来

                if ("T_PAY_APPLY".equals(tableName) && "财务审核".equals(taskName)) {

                    if (payApply.getPayType().equals(SysOptionConstant.ID_518)) {

                        AfterSales afterSales = afterSalesOrderService.getAfterSalesById(payApply.getRelatedId());
                        if (SysOptionConstant.ID_539.equals(afterSales.getType()) || SysOptionConstant.ID_543.equals(afterSales.getType())) {

                            Optional.ofNullable(traderCustomerService.getTraderByPayApply(idValue))
                                    .ifPresent(traderCustomer -> {
                                        traderCustomerService.updateTraderAmount(traderCustomer.getTraderId(), payApply.getAmount());
                                        logger.info("售后订单：{}在财务审核环节，审核不通过，余额增加金额：{}", payApply.getRelatedId(), payApply.getAmount());
                                    });
                        }

                    }
                }

            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment,
                    user.getUsername(), variables);
            // 如果未结束添加审核对应主表的审核状态
            if (!complementStatus.getData().equals("endEvent")) {
                verifiesRecordService.saveVerifiesInfo(taskId, status);
            }
            return new ResultInfo(0, "操作成功");

        } catch (Exception e) {
            logger.error("invoice after complementTask:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }

    /**
     * @description: 采购随同同行单上传页面
     * @return: ModelAndView
     * @author: Strange
     * @date: 2020/11/26
     **/
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/buygoodsDocInit")
    public ModelAndView buygoodsDocInit(HttpServletRequest request, HttpSession session, Integer buyorderId) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();

        mv.addObject("buyorderId", buyorderId);
        mv.setViewName("order/buyorder/buyGoods_doc_init");
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 附件保存
     *
     * @param request
     * @param attachment
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2017年7月24日 下午2:55:15
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/savefileUpInOrder")
    @SystemControllerLog(operationType = "add", desc = "附件保存")
    public ResultInfo<?> savefileUpInOrder(HttpServletRequest request, Attachment attachment) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        if (attachment == null) {
            return ResultInfo.error();
        }
        String attachmentName = attachment.getName();
        if (attachmentName.contains("jpg") || attachmentName.contains("png")) {
            attachment.setAttachmentType(SysOptionConstant.ID_460);
        } else if (attachmentName.contains("doc") || attachmentName.contains("docx") || attachmentName.contains("xlsx")
                || attachmentName.contains("xls") || attachmentName.contains("csv")) {
            attachment.setAttachmentType(SysOptionConstant.ID_461);
        }
        if (user != null) {
            attachment.setCreator(user.getUserId());
            attachment.setAddTime(DateUtil.sysTimeMillis());
        }
        return saleorderService.saveSaleorderAttachment(attachment);
    }

    /**
     * @description: 校验采购单
     * @return: ResultInfo
     * @author: Strange
     * @date: 2020/12/17
     **/
    @MethodLock(className = Buyorder.class, field = "buyorderId")
    @ResponseBody
    @RequestMapping(value = "/checkBuyorder")
    public ResultInfo<?> checkBuyorder(HttpServletRequest request, Buyorder buyorder) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        ResultInfo res = new ResultInfo();
        res.setCode(0);
        // 查询当前订单的一些状态
        BuyorderVo buyorderInfo = buyorderService.getBuyorderInfoById(buyorder.getBuyorderId());

        if (buyorderInfo == null) {
            res.setCode(-1);
            res.setMessage("订单异常!");
            return res;
        }
        if (buyorderInfo.getTraderId() == null || buyorderInfo.getTraderId().equals(0)) {
            res.setCode(-1);
            res.setMessage("未选择供应商,不得申请审核!");
            return res;
        }
        Integer auditStatus = flowOrderApiService.queryAuditStatus(buyorderInfo.getBuyorderNo());
        if (Objects.equals(auditStatus,0)){
            res.setCode(-1);
            res.setMessage("业务流转单未审核，请先处理业务流转单");
            return res;
        }



        GeTraderSku geTraderSku = JSON.parseObject(GE_TRADER_SKU, GeTraderSku.class);
        boolean isGeTrader = buyorderInfo.getTraderId().equals(geTraderSku.getTraderId());

        if (isGeTrader && CollectionUtils.isNotEmpty(buyorderInfo.getBuyorderGoodsVoList())) {
            buyorderInfo.getBuyorderGoodsVoList().forEach(item -> {
                boolean containsGeSku = CollectionUtils.isNotEmpty(geTraderSku.getSkuList()) && geTraderSku.getSkuList().contains(item.getSku());
                if (isGeTrader && containsGeSku) {
                    if (StringUtils.isEmpty(item.getGeContractNo()) || StringUtils.isEmpty(item.getGeSaleContractNo())) {
                        res.setCode(-1);
                        res.setMessage("GE合同编号和GE销售订单编号必填项！");
                        return;
                    }
                }
            });
            if (res.getCode() == -1) {
                return res;
            }
        }
        //风控 -------------必须在最后一步校验-----------------
        //是否质管部
        Boolean prBoolean = riskCheckService.permoissionsFlag(user, ErpConst.QUALITY_ORG);
        if (!prBoolean && !riskCheckService.getisRiskcheck()) {
            BuyorderRiskModelVo buyorderRiskModelVo = riskCheckService.getTitleAndcheckBuyorder(buyorder);
            if (!buyorderRiskModelVo.getIsRisk()) {
                return new ResultInfo(-1, buyorderRiskModelVo.getTitle(), buyorderRiskModelVo);
            }
        }
        //风控 -------------必须在最后一步校验-----------------
        return new ResultInfo(0, "校验通过");
    }

    /**
     * @return org.springframework.web.servlet.ModelAndView
     * @Description 打开关联在途VB单界面
     * <AUTHOR>
     * @Date 18:43 2021/4/12
     * @Param [request, traderSupplierVo, supplierName, goodsId, pageNo, pageSize]
     **/
    @ResponseBody
    @RequestMapping(value = "/searchVB")
    public ModelAndView searchVB(HttpServletRequest request, SearchVBByGoodVo searchVBByGoodVo,
                                 @RequestParam(required = false, defaultValue = "1") Integer pageNo, @RequestParam(required = false, defaultValue = "5") Integer pageSize) throws UnsupportedEncodingException {
        ModelAndView mav = new ModelAndView("order/buyorder/addVB");
        Goods good = new Goods();
        good.setGoodsId(searchVBByGoodVo.getGoodId());
        good = goodsService.getGoodsById(good);
        mav.addObject("saleorderGoodsIds", searchVBByGoodVo.getSaleorderGoodsIds());
        mav.addObject("buyOrderId", searchVBByGoodVo.getBuyOrderId());
        mav.addObject("goodName", good.getGoodsName());
        mav.addObject("goodId", searchVBByGoodVo.getGoodId());
        mav.addObject("goodNum", searchVBByGoodVo.getGoodNum());
        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> map = buyorderService.queryVBBuyorderList(searchVBByGoodVo, page);
        mav.addObject("list", (List<BuyorderVBVo>) map.get("list"));
        mav.addObject("page", (Page) map.get("page"));
        return mav;
    }

    /**
     * @return com.vedeng.common.model.ResultInfo<?>
     * @Description 保存绑定VB单关系
     * <AUTHOR>
     * @Date 13:51 2021/4/14
     * @Param [request, rGoodsJTraderSupplier]
     **/
    @ResponseBody
    @RequestMapping(value = "/saveBindVB")
    @SystemControllerLog(operationType = "add", desc = "保存绑定VB单关系")
    public ResultInfo<?> saveBindVB(HttpServletRequest request, BuyOrderBindVBVO buyOrderBindVBVO) {
        return buyorderService.buyOrderBindVB(buyOrderBindVBVO);
    }

    /**
     * @return
     * @Description 修改采购单采购要求
     * <AUTHOR>
     * @Date 15:29 2021/4/15
     * @Param
     **/
    @ResponseBody
    @RequestMapping(value = "/changeComponent")
    public ResultInfo<?> changeComponent(HttpServletRequest request,
                                         @RequestParam String saleorderGoodsIds,
                                         @RequestParam Integer componentId,
                                         @RequestParam(required = false) String reson) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        return buyorderService.changeComponent(saleorderGoodsIds, componentId, user.getUserId(), reson);
    }

    /**
     * @return
     * @Description 展示更改采购要求原因界面
     * <AUTHOR>
     * @Date 15:38 2021/4/15
     * @Param
     **/
    @ResponseBody
    @RequestMapping(value = "/showResonView")
    public ModelAndView showResonView(HttpServletRequest request,
                                      @RequestParam String saleorderGoodsIds,
                                      @RequestParam Integer componentId) {
        ModelAndView mav = new ModelAndView("order/buyorder/resonView");
        mav.addObject("saleorderGoodsIds", saleorderGoodsIds);
        mav.addObject("componentId", componentId);
        return mav;
    }

    /**
     * 访问绑定订单助理和产品经理或者产品助理页面
     *
     * @param
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/4/12 14:30.
     * @author: Randy.Xu.
     * @return: .
     * @throws: .
     */
    @RequestMapping("bindOrderAssiIndex")
    public ModelAndView bindOrderAssiIndex(@RequestParam(required = false) Integer orderAssitantUserId,
                                           HttpServletRequest request, HttpSession session,
                                           @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                           @RequestParam(required = false, defaultValue = "10") Integer pageSize) {

        ModelAndView mav = new ModelAndView("order/buyorder/bind_orderasssistan_productuser");
        //查询所有订单助理
        List<User> orderAssitantUserList = buyorderService.getOrderAssitantInfoByIdSelective(null);
        mav.addObject("orderAssitantUserList", orderAssitantUserList);
        mav.addObject("orderAssitantUserId", orderAssitantUserId);

        Page page = getPageTag(request, pageNo, pageSize);

        //带参查询
        Map<String, Object> resultMap = orderAssistantRelationService.getAllBindedInfoByOrderAssIdSelectiveListPage(orderAssitantUserId, page);
        mav.addObject("orderAssInfoList", resultMap.get("list"));
        mav.addObject("page", resultMap.get("page"));
        return mav;
    }

    /**
     * @param orderAssitantUserId
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/20 14:30.
     * @author: Randy.Xu.
     * @return: org.springframework.web.servlet.ModelAndView.
     * @throws: .
     */
    @RequestMapping("bindOrderAssistantPage")
    @NoNeedAccessAuthorization
    public ModelAndView bindOrderAssiToProductUser(@RequestParam(required = true) Integer orderAssitantUserId,
                                                   HttpServletRequest request, HttpSession session) {

        ModelAndView mav = new ModelAndView("order/buyorder/bind_orderassistant_sub");
        //产品经理
        List<User> productManagerList = userService.selectAllAssignUser();
        //产品助理
        List<User> productAssistantList = userService.selectAllAssignUser();
        mav.addObject("productManagerList", productManagerList);
        mav.addObject("productAssistantList", productAssistantList);
        mav.addObject("orderAssistantId", orderAssitantUserId);
        return mav;
    }


    @RequestMapping(value = "getBindedOrderAssistantInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResultInfo<ProductPositionUserVo> getBinderOrderAssistantInfo(ProductPositionUserVo productPositionUserVo) {
        String managerUser = "";
        String assUser = "";
//		List<User> managerUser = userService.selectAllAssignUser();
//		List<User> assUser = userService.selectAllAssignUser();
        if (productPositionUserVo.getOrderAssitantUserId() != null) {
            //产品经理
            List<User> productManagerList = buyorderService.getProductUserByOrderAssistantId(productPositionUserVo.getOrderAssitantUserId(), OrderAssistantJProductPositionDo.PRODUCT_MANAGER_TYPE);
            //产品助理
            List<User> productAssistantList = buyorderService.getProductUserByOrderAssistantId(productPositionUserVo.getOrderAssitantUserId(), OrderAssistantJProductPositionDo.PRODUCT_ASSITANT_TYPE);
            if (CollectionUtils.isNotEmpty(productManagerList)) {
                for (User user : productManagerList) {
                    managerUser += user.getUserId() + "@";
                }
                managerUser = managerUser.substring(0, managerUser.length() - 1);
            }
            if (CollectionUtils.isNotEmpty(productAssistantList)) {
                for (User user : productAssistantList) {
                    assUser += user.getUserId() + "@";
                }
                assUser = assUser.substring(0, assUser.length() - 1);
            }

        }

        productPositionUserVo.setManagerUser(managerUser);
        productPositionUserVo.setAssUser(assUser);
        return new ResultInfo<ProductPositionUserVo>(0, "取值成功", productPositionUserVo);
    }

    private void initIsSelected(List<User> managerUser, List<User> productManagerList) {
        if (CollectionUtils.isNotEmpty(managerUser) && CollectionUtils.isNotEmpty(productManagerList)) {
            for (User user : managerUser) {
                for (User user1 : productManagerList) {
                    if (null != user.getUserId() && user.getUserId().equals(user1.getUserId())) {
                        user.setSelected(true);
                    }
                }
            }
        }
    }

    @RequestMapping(value = "bindOrderAssistantToProductUser", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo<?> bindOrderAssistantToProductUser(@RequestBody OrderAssistantRelationDto orderAssistantRelationDto, HttpSession session) {
        ResultInfo resultInfo = new ResultInfo<>();
        try {
            resultInfo = buyorderService.bindOrderAssitantToProductUser(orderAssistantRelationDto, session);
        } catch (Exception e) {
            logger.error("bindOrderAssistantToProductUser绑定失败，绑定信息：{}，e:{}", JSON.toJSONString(orderAssistantRelationDto), e);
            return new ResultInfo<>(-1, "操作失败");
        }

        return resultInfo;
    }

    @RequestMapping(value = "unbindOrderAssRelation", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo<?> unbindOrderAssRelation(@RequestBody OrderAssistantRelationDto orderAssistantRelationDto, HttpSession session) {
        ResultInfo resultInfo = buyorderService.unbindOrderAssRelation(orderAssistantRelationDto);
        return new ResultInfo<>(0, "已解除绑定");
    }

    private void confirmArrivalSaveBuyorderInvoiceStatus(Integer buyorderId) {
        try {
            logger.info("开始保存采购单收票状态信息 buyorderId:{}", buyorderId);
            if (buyorderId == null){
                return;
            }
            BuyorderVo buyorderInfo = buyorderMapper.getBuyorderVoById(buyorderId);
            buyorderInfo.setRealAmount(buyorderMapper.getRealAmount(buyorderId));
            logger.info("采购单的真实总价为 buyorderId :{},  realAmount :{}", buyorderId, buyorderInfo.getRealAmount());

            //获取采购单的商品信息
            List<BuyorderGoodsVo> buyorderGoodsVos = new ArrayList<>();
            for (BuyorderGoodsVo item : buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderId(buyorderId)){
                if (item.getIsDelete() == 0){
                    buyorderGoodsVos.add(item);
                }
            }
            buyorderInfo.setBuyorderGoodsVoList(buyorderGoodsVos);
            logger.info("采购单的基本信息 buyorderInfo:{}", JSON.toJSONString(buyorderInfo));
            int invoiceStatus;
            /**
             * 1.若采购单总额=0
             * （1）当采购单的到货状态变为“全部到货”时，将收票状态更新为：全部收票；
             * （2）否则为：未收票
             */
            if (BigDecimal.ZERO.compareTo(buyorderInfo.getTotalAmount()) == 0) {
                logger.info("采购订单的采购总额为0 buyorderId:{}", JSON.toJSONString(buyorderId));
                if (buyorderInfo.getArrivalStatus().equals(2)) {
                    logger.info("采购订单总额为0并且全部到货 buyorderId:{}", JSON.toJSONString(buyorderId));
                    invoiceStatus = 2;
                } else {
                    logger.info("采购订单总额为0未全部到货 buyorderId:{}", JSON.toJSONString(buyorderId));
                    invoiceStatus = 0;
                }
            } else {
                /**
                 * 2（1）若采购单各sku的录票数量合计=0，则更新收票状态为：未收票
                 * （2）若采购单中每个商品均满足以下条件，则采购单的收票状态为“全部收票”：
                 * ① -1≤（商品的实际金额-商品的收票总额）≤1
                 * ② 商品的收票数量=商品的实际数量
                 */
                logger.info("采购订单的采购总额不为0 buyorderId:{}", JSON.toJSONString(buyorderId));
                BigDecimal invoiceTotalNum = BigDecimal.ZERO;
                boolean isComplete = true;
                boolean isRebate = true;
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(buyorderInfo.getBuyorderGoodsVoList())) {
                    //去掉采购单价为0的采购商品
                    List<BuyorderGoodsVo> buyorderGoodsVoList = new ArrayList<>();
                    for (BuyorderGoodsVo item : buyorderInfo.getBuyorderGoodsVoList()){
                        if (item.getPrice().compareTo(BigDecimal.ZERO) != 0){
                            buyorderGoodsVoList.add(item);
                        }
                    }
                    for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsVoList) {
                        BigDecimal invoiceTotalAmount = buyorderMapper.getHaveInvoiceTotalAmount(buyorderGoodsVo.getBuyorderGoodsId());
                        logger.info("采购订单商品的收票总额 buyorderGoodsId:{}，invoiceTotalAmount:{}", buyorderGoodsVo.getBuyorderGoodsId(), invoiceTotalAmount);

                        //采购商品的已收票数量
                        BigDecimal haveInvoiceNums = buyorderMapper.getHaveInvoiceNums(buyorderGoodsVo.getBuyorderGoodsId());
                        logger.info("采购订单商品的已收票数量 buyorderGoodsId:{}, haveInvoiceNums:{}", buyorderGoodsVo.getBuyorderGoodsId(), haveInvoiceNums);

                        if (haveInvoiceNums != null) {
                            invoiceTotalNum = invoiceTotalNum.add(haveInvoiceNums);
                        }

                        //采购商品的实际数量
                        BigDecimal realTotalNum = buyorderMapper.getGoodsTotalNum(buyorderGoodsVo.getBuyorderGoodsId());
                        logger.info("采购订单商品的实际数量 buyorderGoodsId:{}, realTotalNum:{}", buyorderGoodsVo.getBuyorderGoodsId(), realTotalNum);
//                        VDERP-6880修复采购单sku采购又全部售后的情况导致的收票状态不正确，原本全部收票的状态误判为部分收票
                        if(realTotalNum.compareTo(new BigDecimal(0)) == 0 && haveInvoiceNums == null){
                            continue;
                        }

                        //采购订单的实际总额
                        // VDERP-16204 计算采购单实际金额时，需要减去每个sku的返利金额
                        BigDecimal realPrice = buyorderGoodsVo.getPrice().subtract(buyorderGoodsVo.getRebatePrice());

                        BigDecimal realTotalAmount = realPrice.multiply(realTotalNum);

                        if (realPrice.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }

                        if(realPrice.compareTo(BigDecimal.ZERO) > 0){
                            //商品的收票数量!=商品的实际数量
                            if (haveInvoiceNums == null || haveInvoiceNums.compareTo(realTotalNum) != 0) {
                                isComplete = false;
                                logger.info("商品的收票数量不等于商品的实际数量， 采购商品：{}", JSON.toJSONString(buyorderGoodsVo));
                            }
                            isRebate = false;
                        }
                        logger.info("采购订单商品的实际总额 buyorderGoodsId:{}, realTotalAmount:{}", buyorderGoodsVo.getBuyorderGoodsId(),realTotalAmount);

                        //-1≤（商品的实际金额-商品的收票总额）≤1 不成立
                        if (realTotalAmount.subtract(invoiceTotalAmount == null ? BigDecimal.ZERO : invoiceTotalAmount).abs().compareTo(BigDecimal.ONE) >= 1 ) {
                            isComplete = false;
                            logger.info("商品的实际金额与商品的收票总额校验不通过， 采购商品：{}", JSON.toJSONString(buyorderGoodsVo));
                        }
                    }
                }

                if (invoiceTotalNum.compareTo(BigDecimal.ZERO) == 0&&!isRebate) {
                    //采购订单为未收票
                    logger.info("采购订单的收票数量为0 未收票 buyorderId:{}", JSON.toJSONString(buyorderId));
                    invoiceStatus = 0;
                } else if (isComplete) {
                    logger.info("采购订单符合条件为全部收票 buyorderId:{}", JSON.toJSONString(buyorderId));
                    //采购订单全部收票
                    invoiceStatus = 2;
                } else {
                    logger.info("采购订单不符合条件为部分收票 buyorderId:{}", JSON.toJSONString(buyorderId));
                    //采购订单未全部收票
                    invoiceStatus = 1;
                }
                //使用了返利更新收票状态
                SettlementBillQueryCmd settlementBillQueryCmd = new SettlementBillQueryCmd(buyorderId, BusinessSourceTypeEnum.buyOrder);
                SettlementBillApiDto settlement = settlementBillApiService.getSettlementByBusiness(settlementBillQueryCmd);
                //使用了返利
                if (ObjectUtil.isNotNull(settlement)) {
                    BigDecimal rebateAmount = settlement.getSettlementBillItemDtoList().stream().map(SettlementBillItemApiDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // rebateAmount还需扣除售后的金蝶
                    BigDecimal afterRebateAmount = capitalBillApiService.getAfterSalesFlByBuyOrderId(buyorderId);
                    BigDecimal invoiceAmount = buyorderMapper.getInvoiceAmount(buyorderId);
                    BigDecimal amount = (null == buyorderMapper.getRealAmount(buyorderId)) ? BigDecimal.ZERO : buyorderMapper.getRealAmount(buyorderId);
                    BigDecimal realAmount = amount.subtract(rebateAmount).add(afterRebateAmount);
                    logger.info("更新采购订单收票状态,使用了返利,实际收票金额:{},实际订单金额(未扣除返利):{},返利金额:{},售后返利金额:{},采购单总金额:{}", invoiceAmount, amount, rebateAmount, afterRebateAmount, buyorderInfo.getTotalAmount());
                    //全部收票（己收票金额-售后退票金额）=（原始金额-售后金额-返利金额）｜｜ 订单全部使用返利
                    //此外为部分收票
                    invoiceStatus = (invoiceAmount.compareTo(realAmount) == 0 || (buyorderInfo.getTotalAmount().compareTo(rebateAmount) == 0)) ? 2 : 1;
                }
            }

            logger.info("更新采购单录票状态信息 buyorderId:{},invoiceStatus:{}", buyorderId, invoiceStatus);
            buyorderMapper.saveInvoiceStatus(buyorderId, invoiceStatus);

            // VDERP-8755 订单流 采购单票货款全部完成时，将状态置为 已完结
            if (invoiceStatus == 2) {
                Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderId);
                if (buyorder.getIsNew() == 1 && buyorder.getStatus() == 1 && buyorder.getPaymentStatus() == 2 && buyorder.getDeliveryStatus() == 2 && buyorder.getArrivalStatus() == 2) {
                    buyorder.setStatus(2);
                    buyorderMapper.updateByPrimaryKeySelective(buyorder);
                    logger.info("订单流票货款全部完成更新采购单状态为已完结--全部收票:{}", buyorderId);
                }
            }

        } catch (Exception e) {
            logger.error("采购订单保存收票状态error", e);
        }
    }
    /**
     * 保存从医购优选推送过来的销售单物流信息，同步到采购订单中
     * @param syncExpressDtoList
     * @return
     */
    @ResponseBody
    @RequestMapping(value ="/savebuyorderLogisticsSaveFromOtherErp", produces = "application/json;charset=UTF-8")
    @NoNeedAccessAuthorization
    public ResultInfo<?> savebuyorderLogisticsSaveFromOtherErp(@RequestBody List<SyncExpressDto> syncExpressDtoList) {
        ResultInfo<?> resultInfo = new ResultInfo<>();
        logger.info("SaleorderController->savebuyorderLogisticsSaveFromOtherErp:" + com.alibaba.fastjson.JSONObject.toJSONString(syncExpressDtoList));
        if(CollectionUtils.isEmpty(syncExpressDtoList)) {
            resultInfo.setCode(-1);
            resultInfo.setMessage("无请求内容，请重试");
            return resultInfo;
        }
        try {
            return buyorderService.processLogisticsFromSaleOrder(syncExpressDtoList);
        } catch (Exception e) {
            resultInfo.setCode(-1);
            resultInfo.setMessage("失败");
            logger.error("保存销售订单-关联的采购单物流信息失败",e);
        }
        return resultInfo;
    }






}
