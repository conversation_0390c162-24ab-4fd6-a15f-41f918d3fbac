html,body {
    color: #333;
    font: 14px / 1.5 'Microsoft YaHei', 'arial', 'sans-serif';
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    background-color: #f5f7fa;
    margin: 0;
}

.target-wrap {
    max-width: 1600px;
    padding: 20px;
    margin:  0 auto;
    min-width: 1200px;

    .target-top {
        background: #fff;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;

        .target-filter-item {
            display: flex;
            align-items: center;
        }

        .target-options {
            display: flex;
            align-items: center;

            .el-button {
                margin-left: 10px;
            }
        }
    }

    .target-content {
        background: #fff;
        padding: 20px;

        .gray {
            background: #f5f7fa;
        }
    }

    .target-empty {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 100px 0;
        background: #fff;

        .el-icon-info {
            font-size: 18px;
            margin-right: 5px;
            color: #09f;
        }
    }
}