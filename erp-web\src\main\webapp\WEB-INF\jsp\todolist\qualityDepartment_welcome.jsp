<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="质量部工作台" scope="application" />
<%@ include file="../common/common.jsp"%>
<div class="content mt10 ">
    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">
                今日待办事项
            </div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th class="wid9">事项</th>
                <th class="wid8">客户资质审核</th>
                <th class="wid8">销售订单审核</th>
                <th class="wid8">采购订单审核</th>
                <th class="wid9">销售订单异常风控</th>
                <th class="wid9">采购订单异常风控</th>
                <th class="wid9">合同回传审核</th>
                <th class="wid9">确认单审核</th>
            </tr>
            </thead>
            <tbody>
            <tr class="renderData" id="todoData">
                <td>合计</td>
                <td title="客户资质审核" id="traderCertificateCheckCount" redirect="${customerQualificationApproval}">加载中</td>
                <td title="销售订单审核" id="saleorderCheckCount" redirect="${saleorderApproval}">加载中</td>
                <td title="采购订单审核" id="buyorderCheckCount" redirect="${buyorderApproval}">加载中</td>
                <td title="销售订单异常风控" id="riskSaleorderCheckCount" redirect="${saleorderExceptionRisk}">加载中</td>
                <td title="采购订单异常风控" id="riskBuyorderCheckCount" redirect="${buyorderExceptionRisk}">加载中</td>
                <td title="合同回传审核" id="contractCheckCount" redirect="${contractApproval}">加载中</td>
                <td title="确认单审核" id="confirmationCheckCount" redirect="${confirmationApproval}">加载中</td>
            </tr>
            </tbody>
        </table>


        <div class="title-container title-container-blue">
            <div class="table-title nobor">
                今日预警事项
            </div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th class="">类别</th>
                <th class=""  colspan="4">供应商</th>
                <th class="" colspan="3">客户</th>
                <th class="">产品</th>
            </tr>
            <tr>
                <th class="wid3">事项</th>
                <th class="wid3">营业执照</th>
                <th class="wid3">生产许可证</th>
                <th class="wid3">生产产品登记表</th>
                <th class="wid3">医疗器械经营许可证（三类）</th>
                <th class="wid3">营业执照</th>
                <th class="wid3">执业许可证</th>
                <th class="wid3">医疗器械经营许可证（三类）</th>
                <th class="wid3">注册证</th>
            </tr>
            </thead>
            <tbody>
            <tr class="renderData" id="warnData">
                <td>合计</td>
                <td title="营业执照" id="supplyBusinessLicense" redirect="${supplierBusinessLicense}">0</td>
                <td title="生产许可证" id="supplyManufacturingLicense" redirect="${supplierProductionLicense}">0</td>
                <td title="生产产品登记表" id="supplyProductsRegister" redirect="${supplierProductRegistrationForm}">0</td>
                <td title="医疗器械经营许可证（三类）" id="supplyThirdLevelLicense" redirect="${supplierBusinessLicenseOfMedicalDevices}">0</td>
                <td title="营业执照" id="traderBusinessLicense" redirect="${customerBusinessLicense}">0</td>
                <td title="执业许可证" id="traderPracticiseLicense" redirect="${customerPracticeLicense}">0</td>
                <td title="医疗器械经营许可证（三类）" id="traderThirdLevelLicense" redirect="${customerBusinessLicenseOfMedicalDevices}">加载中</td>
                <td title="注册证" id="productsRegistration" redirect="/firstengage/baseinfo/getFirstEngageInfo.do?isOverDate=3,4">加载中</td>
            </tr>
            </tbody>
        </table>
    </div>
</div>
<script>

    getQualityDepartTodoList();

    $(setInterval(getQualityDepartTodoList,60000));

    function getQualityDepartTodoList(){
        renderMaintainData();
        renderWarnData();
        renderWarnDataRegister();
    }

    function renderMaintainData() {
        $("#todoData").children('td').each(function (){
            let $td = $(this);
            let id = $td.attr('id');
            if (typeof(id) != 'undefined'){
                let countRedirect = $td.attr('redirect').replace('list-','count-');
                $.ajax({
                    url: countRedirect,
                    dataType:'json',
                    // async 默认为 true
                    success: function(data){
                        if (data.code == 0) {
                            let count = data.count;
                            if (typeof(count) != 'undefined'){
                                if (count == 0){
                                    $td.text(0)
                                } else {
                                    let redirect = $td.attr('redirect');
                                    let title = $td.attr("title");
                                    $td.html("<a class='addtitle_dyn' href='javascript:void(0);' tabtitle='{\"num\":\""+ id + "\",\"title\":\"" + title +
                                        "\",\"link\":\"" + redirect + "\"}'>" + count + "</a>");
                                }
                            }
                        }
                    }
                });
            }
        })
    }

    function renderWarnData() {
        $("#warnData").children('td').each(function (){
            let $td = $(this);
            let id = $td.attr('id');
            if (typeof(id) != 'undefined'){
                let countRedirect = $td.attr('redirect').replace('list-','count-');
                $.ajax({
                    url: countRedirect,
                    dataType:'json',
                    // async 默认为 true
                    success: function(data){
                        if (data.code == 0) {
                            let count = data.count;
                            if (typeof(count) != 'undefined'){
                                if (count == 0){
                                    $td.text(0)
                                } else {
                                    let redirect = $td.attr('redirect');
                                    let title = $td.attr("title");
                                    $td.html("<a class='addtitle_dyn' href='javascript:void(0);' tabtitle='{\"num\":\""+ id + "\",\"title\":\"" + title +
                                        "\",\"link\":\"" + redirect + "\"}'>" + count + "</a>");
                                }
                            }
                        }
                    }
                });
            }
        })
    }

   function renderWarnDataRegister(){
       $.getJSON('/todolist/qualityDepartment/warnList.do',function (data){
               let id = 'productsRegistration';
               let count = data.data.productsRegistration;
               if (typeof(count) != 'undefined'){
                   if (count == 0){
                       $(this).text(0)
                   } else {
                       let redirect = $('#productsRegistration').attr('redirect');
                       let title = $('#productsRegistration').attr("title");
                       $('#productsRegistration').html("<a class='addtitle_dyn' href='javascript:void(0);' tabtitle='{\"num\":\""+ id + "\",\"title\":\"" + title +
                           "\",\"link\":\"" + redirect + "\"}'>" + count + "</a>");
                   }
               }
       });
   }

</script>
<%@ include file="../common/footer.jsp"%>