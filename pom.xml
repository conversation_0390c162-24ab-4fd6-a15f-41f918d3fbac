<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.vedeng.erp</groupId>
    <artifactId>erp</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>erp</name>
    <description>erp</description>

    <properties>
        <spring.version>4.1.9.RELEASE</spring.version>
        <spring.data.redis.version>1.6.6.RELEASE</spring.data.redis.version>
        <project.version>1.0.0-SNAPSHOT</project.version>
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
        <lombok.version>1.16.18</lombok.version>
        <pagehelper.version>5.3.0</pagehelper.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <retry.version>1.2.5.RELEASE</retry.version>
        <xxl-job.version>2.1.1</xxl-job.version>
        <cat.version>3.0.0</cat.version>
        <onedataapi.version>1.2.0-SNAPSHOT</onedataapi.version>
        <guava.version>29.0-jre</guava.version>
        <mysql.driver.version>5.1.47</mysql.driver.version>
        <!-- 新增Temporal相关版本 -->
        <temporal.version>1.22.0</temporal.version>
        <!-- gRPC和Protobuf版本 - 兼容Temporal 1.22.0 -->
        <grpc.version>1.53.0</grpc.version>
        <protobuf.version>3.21.12</protobuf.version>
    </properties>


    <modules>
        <module>erp-common</module>
        <module>erp-api-standard</module>
        <module>erp-temporal</module>
        <module>erp-infrastructure</module>
        <module>erp-saleorder</module>
        <module>erp-buyorder</module>
        <module>erp-goods</module>

        <module>erp-doc</module>
        <module>erp-old</module>
        <module>erp-trader</module>
        <module>erp-oa</module>
        <module>erp-web</module>
        <module>erp-web-mobile</module>
        <module>erp-system</module>
        <module>erp-kingdee</module>
        <module>erp-finance</module>
        <module>erp-mobile</module>
        <module>erp-wms</module>
        <module>erp-web-crm</module>
        <module>erp-crm</module>
    </modules>


    <dependencies>
       <!-- <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.connector.version}</version>
            <scope>runtime</scope>
        </dependency>-->
        <dependency>
            <groupId>com.vedeng.uac</groupId>
            <artifactId>vedeng-uac-api</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
            <version>${cat.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>
        <!-- apollo -->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>1.4.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>snakeyaml</artifactId>
                    <groupId>org.yaml</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.17</version>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>4.0.1</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.driver.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-redis</artifactId>
                <version>${spring.data.redis.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ezadmin</groupId>
                <artifactId>ezadmin-spring-boot-starter</artifactId>
                <version>3.6.17</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-autoconfigure</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.github.ezadmin126</groupId>
                <artifactId>ezadmin-core</artifactId>
                <version>2.11.4-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jsoup</groupId>
                        <artifactId>jsoup</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>QLExpress</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>com.vedeng.doc</groupId>
                <artifactId>doc-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <!--mapStruct依赖 -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${retry.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>


            <dependency>
                <groupId>com.vedeng.onedataapi</groupId>
                <artifactId>onedataapi-api</artifactId>
                <version>${onedataapi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- Temporal工作流引擎依赖 -->
            <dependency>
                <groupId>io.temporal</groupId>
                <artifactId>temporal-sdk</artifactId>
                <version>${temporal.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <!-- 排除所有Jackson依赖，使用项目统一版本 -->
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.datatype</groupId>
                        <artifactId>jackson-datatype-jsr310</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Google Protocol Buffers - Temporal依赖 -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>

            <!-- gRPC依赖 - Temporal通信需要 -->
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-core</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-api</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty-shaded</artifactId>
                <version>${grpc.version}</version>
            </dependency>

        </dependencies>


    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
     <repositories>
        <repository>
            <id>vedeng</id>
            <name>maven-public</name>
            <url>http://nexus.ivedeng.com/repository/maven-public/</url>
            <snapshots>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>

            <id>third</id>
            <name>Team Vedeng Release Repository</name>
            <url>http://nexus.ivedeng.com/repository/third/</url>
        </repository>
    </repositories>
</project>
