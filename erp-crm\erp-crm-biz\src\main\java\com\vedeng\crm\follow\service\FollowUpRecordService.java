package com.vedeng.crm.follow.service;

import java.util.List;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.follow.domain.dto.FollowUpRecordPageResponseDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecordApiDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecordParams;
import com.vedeng.erp.trader.dto.FollowBindingTelParams;

/**
 * 跟进记录服务接口
 */
public interface FollowUpRecordService {

    void add(CommunicateRecordDto communicateRecordDto);

    void update(CommunicateRecordDto communicateRecordDto);

    FollowUpRecordPageResponseDto page(CommunicateRecordDto communicateRecordQueryDto);

    CommunicateRecordDto detail(Integer communicateRecordId);

    public boolean checkHasFollowUp(Integer relatedId);

    /**
     * 获取通话记录
     * @param pageParam
     * @param currentUser
     * @return
     */
    CommunicateTelRecordApiDto getTelList(PageParam<CommunicateTelRecordParams> communicateTelRecordParams,CurrentUser currentUser);

    /**
     * 跟进记录绑定通话
     * @param followBindingTelParams
     * @param currentUser
     * @return 
     */
	List<Integer> followBindingTel(FollowBindingTelParams followBindingTelParams, CurrentUser currentUser);

	/**
	 * 返回当前用户自身以及下属所有启用在岗未删除用户信息集合
	 * @param id
	 * @return
	 */
	List<UserDto> getUacSubUserInfoByCurrent(Integer id);
}
