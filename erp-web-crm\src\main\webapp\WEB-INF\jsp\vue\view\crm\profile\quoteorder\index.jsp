<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>报价单</title>
    <meta name="viewport" content="initial-scale=0.2,minimum-scale=0.2, maximum-scale=0.2, user-scalable=no">
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/records.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/common/file.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/common/selectProd.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/quoteOrder.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <!--隐藏的登录用户信息 -->
    <jsp:include page="../common/head_import.jsp"></jsp:include>
    <input type="hidden" value="${requestScope.autoRefreshMin}" id="autoRefreshMin" name="autoRefreshMin"/>
    <div class="page-wrap" id="page-container">
        <page-header :is-show-menu="false"></page-header>
        <div class="page-container">
            <div class="page-main" v-show="!firstLoading">
                <div class="page-main-header-wrap" :style="'top:' + (layout_hidden_value ? '0' : '50px')">
                    <div class="page-main-header">
                        <div class="header-title">报价单</div>
                        <div class="order-status-tag" v-if="quoteValidStatus == 0">未生效</div>
                        <div class="order-status-tag tag-green" v-else>已生效</div>
                        <div class="order-business-info">
                            <div class="business-info-item">
                                <div class="info-label">商机编号：</div>
                                <div class="info-txt" @click="isShowBusinessInfo = true">{{ businessInfo.bussinessChanceNo }}</div>
                            </div>
                        </div>
                        <div class="header-options">
                            <a :href="helpUrl" target="_blank" class="header-link">
                                <i class="vd-ui_icon icon-problem2"></i>
                                <span class="link-txt">操作帮助</span>
                            </a>
                            <div class="options-btns">
                                <ui-button v-if="isAuthToOrder" @click="handlerToOrder" type="primary">转订单</ui-button>
                                <ui-button type="primary" @click="validQuoteStatus" v-if="isAuthValid && quoteValidStatus == 0">生效报价</ui-button>
                                <ui-button @click="invalidQuoteStatus" v-if="isAuthInvalid && quoteValidStatus == 1">撤销生效</ui-button>
                                <ui-button @click="showMultiImportAdd" v-if="isAuthImport">导入客户需求</ui-button>
                                <ui-button @click="exportQuote" v-if="isAuthExport">导出报价</ui-button>
                                <ui-button @click="shareQuote" v-if="isAuthShare">分享线上报价</ui-button>
                                <ui-button v-if="isAuthApply" @click="gotoApply">申请授权书</ui-button>
                                <template v-if="isAuthChat">
                                    <ui-button @click="showChatDialog" v-if="isBuildChat == 0">发起群聊</ui-button>
                                    <ui-button @click="showChatDialogTip" icon="icon-selected2" v-if="isBuildChat == 1">已建群</ui-button>
                                    <ui-button @click="showChatDialog" v-if="isBuildChat == 2">群聊(审核中)</ui-button>
                                </template>
                            </div>
                            <div class="options-icons">
                                <ui-title-tip title="跟进记录" :position="layout_hidden_value ? 'bottom' : 'top'" :y="-23">
                                    <div class="btn-icon btn-message" @click="openRecord"></div>
                                </ui-title-tip>
                                <ui-title-tip title="任务记录" :position="layout_hidden_value ? 'bottom' : 'top'" :y="-23">
                                    <div class="btn-icon btn-record" @click="openTask"></div>
                                    <div class="h-a-item-num" v-if="headerTaskNum">{{ headerTaskNum }}</div>
                                </ui-title-tip>
                                <ui-title-tip title="操作记录" :position="layout_hidden_value ? 'bottom' : 'top'" :y="-23">
                                    <div class="btn-icon btn-time" @click="openLog"></div>
                                </ui-title-tip>
                                <ui-title-tip title="协作人" :position="layout_hidden_value ? 'bottom' : 'top'" :y="-23">
                                    <div class="btn-icon btn-user" @click="openPartner"></div>
                                </ui-title-tip>
                            </div>
                        </div>
                    </div>
                    <div class="page-top-tip" v-if="taskNum || productNum">
                        <template v-if="taskNum && productNum">
                            <i class="vd-ui_icon icon-info2"></i>该报价单中您有{{taskNum}}条待办和{{productNum}}个产品的咨询，具体可通过产品上的<i class="vd-ui_icon icon-m-message"></i>进行查看，处理完成后请点击“<div class="tip-option" @click="finishTaskConfirm">完成咨询</div>”
                        </template>
                        <template v-else-if="taskNum">
                            <i class="vd-ui_icon icon-info2"></i>当报价单中您有{{taskNum}}待办，处理完成后请点击“<div class="tip-option" @click="finishTaskConfirm">完成咨询</div>”
                        </template>
                        <template v-else-if="productNum">
                            <i class="vd-ui_icon icon-info2"></i>当报价单中您有{{productNum}}个产品咨询，处理完成后请点击“<div class="tip-option" @click="finishTaskConfirm">完成咨询</div>”
                        </template>
                    </div>
                </div>
                <div class="quote-wrap" :class="{'no-tip': !taskNum && !productNum}">
                    <div class="quote-content">
                        <div class="options-wrap-placeholder">
                            <div class="options-wrap" :class="{fixed: isContentHeaderFixed}" :style="'top:' + (tableFixedTop - 54) + 'px'">
                                <div class="options-l">
                                    <div class="options-btns">
                                        <ui-select-button  v-if="isAuthAddProduct && !hasNeeds">
                                            <div @click="showSelectProdDialog({})">添加产品</div>
                                            <template v-slot:drop>
                                                <div @click="showMultiAdd">批量添加</div>
                                            </template>
                                        </ui-select-button>
                                        <ui-button v-if="isAuthConsultation" @click="multiConsultation">咨询报备</ui-button>
                                        <ui-button @click="deleteConfirm('multi')" v-if="isAuthDelete">删除</ui-button>
                                    </div>
                                    <div class="options-links">
                                        <div class="link-item" @click="openFile">需求附件（{{ attachmentsNum }}）</div>
                                        <div class="link-item" v-if="isAuthEditReqDescription || resDescription" @click="isShowReqDescription = true"><i class="vd-ui_icon icon-sms"></i>客户需求描述</div>
                                    </div>
                                    <div class="selected-num" v-if="tableSelectedList.length">已选{{ tableSelectedList.length }}项</div>
                                </div>
                                <div class="filter-wrap">
                                    <div class="filter-checkbox-item">
                                        <ui-checkbox label="报价未填写" @change="handlerPriceFilterChange" :checked.sync="isFilterPrice"></ui-checkbox>
                                    </div>
                                    <ui-custom-search-select :list="productMangerFilterList" :multi="true" @open="getFilterProductManagerList" @select="handlerFilterChange('pm', $event)" :need-all="true">
                                        <template v-slot:placeholder="{ selected }">
                                            <div class="filter-item">
                                                <div class="filter-txt">产品负责</div>
                                                <i class="vd-ui_icon icon-filter" :class="{active: Object.keys(selected).length}"></i>
                                            </div>
                                        </template>
                                    </ui-custom-search-select>
                                    <ui-custom-search-select :list="reportStatusFilterList" :multi="true" :no-search="true" @select="handlerFilterChange('status', $event)">
                                        <template v-slot:placeholder="{ selected }">
                                            <div class="filter-item">
                                                <div class="filter-txt">报备状态</div>
                                                <i class="vd-ui_icon icon-filter" :class="{active: Object.keys(selected).length}"></i>
                                            </div>
                                        </template>
                                    </ui-custom-search-select>
                                </div>
                                <div class="price-total"><span class="price-label">报价总金额：</span><span class="price-num">{{ totalPrice }}</span>元</div>
                            </div>
                        </div>
                        <div class="list-wrap">
                            <ui-table 
                                :custom-list="true" 
                                :headers="quoteTableHeaders" 
                                :left-fixed-number="quoteValidStatus == 0 ? 2 : 1" 
                                :left-fixed="true" 
                                :list="showList" 
                                :right-fixed="!quoteValidStatus" 
                                :can-choose="!quoteValidStatus" 
                                @selectchange="handlerTableListSelect" 
                                ref="tableList" 
                                :fixed-top="tableFixedTop"
                                @truescroll="handlerTableScroll"
                                @tablescroll="handlerTableScrollLeft"
                            >
                                <template v-slot:left_tip="{ row }">
                                    <div class="td-left-tip">
                                        <ui-tip icon="m-message" position="r" 
                                            v-if="needLeftTip(row)">
                                            销售咨询：{{ leftTipText(row.goodsInfo) }}
                                        </ui-tip>
                                    </div>
                                </template>
                                <template v-slot:tr="{ row }">
                                    <td class="vd-ui-td sticky-item vertical-center last-fixed num" :style="'left:' + (quoteValidStatus == 0 ? 36 : 0) +'px;'">
                                        <div class="cnt-center">
                                            <div class="td-left-tip" style="left: 7px;" v-if="quoteValidStatus == 1">
                                                <ui-tip icon="m-message" position="r" 
                                                    v-if="needLeftTip(row)">
                                                    销售咨询：{{ leftTipText(row.goodsInfo) }}
                                                </ui-tip>
                                            </div>
                                            {{ row.listUiIndex + 1 }}
                                        </div>
                                    </td>
                                    <template v-if="row.needsInfo || row.needEmpty">
                                        <td class="vd-ui-td td-need-info color1" :rowspan="row.rowspan || 1">
                                            <div class="quote-td-cnt" :style="'height:' + (row.rowspan * 128 - 20) + 'px;'" v-if="row.needsInfo">
                                                <div class="label-item">
                                                    <div class="label">需求产品：</div>
                                                    <div class="txt">{{ row.needsInfo.productNeeds || '-' }}</div>
                                                </div>
                                                <div class="label-item">
                                                    <div class="label">需求数量：</div>
                                                    <div class="txt">{{ row.needsInfo.numNeeds || '-' }}</div>
                                                </div>
                                                <div class="label-item" v-if="row.needsInfo.distributeBudget">
                                                    <div class="label">经销预算(元)：</div>
                                                    <div class="txt">{{ row.needsInfo.distributeBudget || '-' }}</div>
                                                </div>
                                                <div class="label-item" v-if="row.needsInfo.terminalBudget">
                                                    <div class="label">终端预算(元)：</div>
                                                    <div class="txt">{{ row.needsInfo.terminalBudget || '-' }}</div>
                                                </div>
                                                <div class="option-item-delete" v-if="quoteValidStatus == 0" @click="deleteNeedsConfirm(row.needsInfo.quoteorderNeedsId)" title="删除需求">
                                                    <i class="vd-ui_icon icon-delete"></i>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="vd-ui-td color1" :rowspan="row.rowspan">
                                            <div class="quote-td-cnt" :style="'height:' + (row.rowspan * 128 - 20) + 'px;'" v-if="row.needsInfo">
                                                {{ row.needsInfo.extraNeeds || '-' }}
                                            </div>
                                        </td>
                                    </template>
                                    <td class="vd-ui-td prod-cnt">
                                        <template v-if="row.goodsInfo && row.goodsInfo.skuName">
                                            <div class="quote-td-cnt">
                                                <div class="goods-info-wrap">
                                                    <div class="goods-img">
                                                        <img :src="row.goodsInfo.imageUrl || '/static/image/img-placeholder.png'" onerror="this.src='/static/image/img-error.png'" alt="">
                                                        <div class="goods-tags">
                                                            <template v-if="(row.goodsInfo.salePrice && row.goodsInfo.salePrice === '0') || row.goodsInfo.salePrice === 0">
                                                                <div class="tag-item tag-zeng"></div>
                                                            </template>
                                                        </div>
                                                    </div>
                                                    <div class="goods-info">
                                                        <div class="label-item label-sku" v-if="row.goodsInfo.skuNo">
                                                            <div class="label">订货号：</div>
                                                            <div class="link" @click="GLOBAL.link({name:'商品整合查询页', url: row.goodsInfo.skuNameInnerLink, link: row.goodsInfo.skuNameLink, nohost: true})" v-if="row.goodsInfo.skuNameLink">{{ row.goodsInfo.skuNo || '-' }}</div>
                                                            <div class="txt" v-else>{{ row.goodsInfo.skuNo || '-' }}</div>
                                                            <ui-tip :position="showList.length > 4 && row.listUiIndex == showList.length - 1 ? 'rb': 'rt'" icon="info2" @hover="handlerGoodsTipHover(row.goodsInfo.skuNo)">
                                                                <div class="goods-tip-info" v-if="goodsTipInfo[row.goodsInfo.skuNo]">
                                                                    <div class="goods-tip-item">
                                                                        <div class="item-label">物料编码：</div>
                                                                        <div class="item-txt">{{ goodsTipInfo[row.goodsInfo.skuNo].materialCode || '-' }}</div>
                                                                    </div>
                                                                    <div class="goods-tip-item">
                                                                        <div class="item-label">注册证号：</div>
                                                                        <div class="item-txt">{{ goodsTipInfo[row.goodsInfo.skuNo].registrationNumber || '-' }}</div>
                                                                    </div>
                                                                    <div class="goods-tip-item">
                                                                        <div class="item-label">管理类别：</div>
                                                                        <div class="item-txt">{{ goodsTipInfo[row.goodsInfo.skuNo].manageCategoryLevel || '-' }}</div>
                                                                    </div>
                                                                    <div class="goods-tip-item">
                                                                        <div class="item-label">产品负责人：</div>
                                                                        <div class="item-txt">
                                                                            {{ goodsTipInfo[row.goodsInfo.skuNo].managers || '-' }}</div>
                                                                    </div>
                                                                    <div class="goods-tip-item">
                                                                        <div class="item-label">包装清单：</div>
                                                                        <div class="item-txt">{{ goodsTipInfo[row.goodsInfo.skuNo].packingList || '-' }}</div>
                                                                    </div>
                                                                    <div class="goods-tip-item">
                                                                        <div class="item-label">质保年限：</div>
                                                                        <div class="item-txt">{{ goodsTipInfo[row.goodsInfo.skuNo].qaYears || '-' }}</div>
                                                                    </div>
                                                                    <div class="goods-tip-item">
                                                                        <div class="item-label">库存：</div>
                                                                        <div class="item-txt">{{ goodsTipInfo[row.goodsInfo.skuNo].stockNum || goodsTipInfo[row.goodsInfo.skuNo].stockNum === 0 ? goodsTipInfo[row.goodsInfo.skuNo].stockNum : '-' }}</div>
                                                                    </div>
                                                                    <div class="goods-tip-item">
                                                                        <div class="item-label">可用库存：</div>
                                                                        <div class="item-txt">{{ goodsTipInfo[row.goodsInfo.skuNo].availableStockNum || goodsTipInfo[row.goodsInfo.skuNo].availableStockNum === 0 ? goodsTipInfo[row.goodsInfo.skuNo].availableStockNum : '-' }}</div>
                                                                    </div>
                                                                    <div class="goods-tip-item">
                                                                        <div class="item-label">订单占用：</div>
                                                                        <div class="item-txt">{{ goodsTipInfo[row.goodsInfo.skuNo].occupynum || goodsTipInfo[row.goodsInfo.skuNo].occupynum === 0 ? goodsTipInfo[row.goodsInfo.skuNo].occupynum : '-' }}</div>
                                                                    </div>
                                                                    <div class="goods-tip-item">
                                                                        <div class="item-label">审核状态：</div>
                                                                        <div class="item-txt">{{ goodsTipInfo[row.goodsInfo.skuNo].checkStatus || '-' }}</div>
                                                                    </div>
                                                                </div>
                                                                <div class="goods-tip-loading" v-else>
                                                                    <i class="vd-ui_icon icon-loading"></i>
                                                                    <div class="loading-txt">加载中...</div>
                                                                </div>
                                                            </ui-tip>
                                                        </div>
                                                        <div class="label-item" v-else>
                                                            <div class="label label-tip">
                                                                <div class="txt">手动添加</div>
                                                                <ui-tip position="r" width="500px">“转订单”和“分享线上报价”时需把手动添加产品换成ERP已建档产品。</ui-tip>
                                                            </div>
                                                        </div>
                                                        <div class="label-item">
                                                            <div class="label">名称：</div>
                                                            <div class="txt">{{ row.goodsInfo.skuName || '-' }}</div>
                                                        </div>
                                                        <div class="label-item">
                                                            <div class="label">品牌：</div>
                                                            <div class="txt">{{ row.goodsInfo.brandName || '-' }}</div>
                                                        </div>
                                                        <div class="label-item">
                                                            <div class="label">型号：</div>
                                                            <div class="txt">{{ row.goodsInfo.modelOrSpec || '-' }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="prod-status-tag" v-if="row.goodsInfo.checkStatus != 3">
                                                {{parseProdStatus[row.goodsInfo.checkStatus]}}
                                            </div>
                                        </template>
                                        <div class="quote-td-cnt center visible" v-else>
                                            <!-- <ui-button type="primary" v-if="isAuthAddProduct" @click="showSingleAdd(row)">添加产品</ui-button> -->
                                            <!-- <ui-button type="primary" v-if="isAuthAddProduct" @click="showSelectProdDialog">添加产品</ui-button> -->
                                            <ui-select-button  v-if="isAuthAddProduct">
                                                <div @click="showSelectProdDialog(row)">添加产品</div>
                                                <template v-slot:drop>
                                                    <div @click="showCustomSelectProdDialog(row)">手工添加</div>
                                                </template>
                                            </ui-select-button>
                                        </div>
                                    </td>
                                    <td class="vd-ui-td">
                                        <div class="quote-td-cnt" v-if="row.goodsInfo && row.goodsInfo.skuName">
                                            <template v-if="row.goodsInfo.mainParam && row.goodsInfo.mainParam.length">
                                                <div class="text-item" v-for="(item, index) in row.goodsInfo.mainParam" :key="index" v-html="item.replace(/\n/g, '<br/>')"></div>
                                            </template>
                                        </div>
                                    </td>
                                    <td class="vd-ui-td">
                                        <div class="quote-td-cnt visible" v-if="row.goodsInfo && row.goodsInfo.skuName">
                                            <div class="edit-item edit-item-price" @mouseenter="getPriceHistory(row.goodsInfo.quoteorderGoodsId)" @mouseleave="hidePriceHistory" :class="{'can-edit': quoteValidStatus == 0 && businessStage != 6}">
                                                <div class="label" :class="{highlight: needLeftTip(row) && row.goodsInfo.isConsulPrice}">销售单价：</div>
                                                <div class="content">
                                                    <div class="cnt-input-wrap" v-if="quoteValidStatus == 0 && businessStage != 6" :class="{focus: isOnfocus(row.goodsInfo, 'salePrice')}">
                                                        <ui-input 
                                                            class="input-strong"
                                                            @focus="handlerPositionChange(row.goodsInfo, 'salePrice', row.goodsInfo.salePrice)" 
                                                            @input="handlerListInputChange"
                                                            :class="{'value-change': priceChangeObj[row.goodsInfo.quoteorderGoodsId]}" 
                                                            size="small" 
                                                            v-model="row.goodsInfo.salePrice" 
                                                            @blur="handlerSalePriceChange(row)" 
                                                            width="92px">
                                                        </ui-input>
                                                        <div class="focus-tip" v-if="isOnfocus(row.goodsInfo, 'salePrice')">
                                                            {{localListObj[syncLocalPosition].userName}}正在编辑
                                                        </div>
                                                    </div>
                                                    <template v-else>
                                                        <div class="txt">{{ row.goodsInfo.salePrice }}</div>
                                                    </template>
                                                    <div class="unit">元</div>
                                                    <ui-tip position="r" v-if="quoteValidStatus == 0 && checkSalePrice(row)">低于核价</ui-tip>
                                                </div>
                                                <!-- 价格修改记录-start -->
                                                    <price-history :show="row.goodsInfo.quoteorderGoodsId == priceHistoryFocusId || (!priceHistoryFocusId && row.goodsInfo.quoteorderGoodsId == priceHistoryCurrentId)" :loading="priceHistoryLoading" :list="priceHistoryList" :reset="needResetPriceHistory"></price-history>
                                                <!-- 价格修改记录-end -->
                                            </div>
                                            <div class="edit-item" :class="{'can-edit': quoteValidStatus == 0 && businessStage != 6}">
                                                <div class="label">数量/单位：</div>
                                                <div class="content">
                                                    <div class="cnt-input-wrap" v-if="quoteValidStatus == 0 && businessStage != 6" :class="{focus: isOnfocus(row.goodsInfo, 'num')}">
                                                        <ui-number-input 
                                                            v-model="row.goodsInfo.num" 
                                                            @focus="handlerPositionChange(row.goodsInfo, 'num', row.goodsInfo.num)" 
                                                            @blur="handlerNumberBlur" 
                                                            @input="handlerListInputChange" 
                                                            :class="{'value-change': numChangeObj[row.goodsInfo.quoteorderGoodsId]}" @change="handlerNumberChange(row)">
                                                        </ui-number-input>
                                                        <div class="focus-tip" v-if="isOnfocus(row.goodsInfo, 'num')">
                                                            {{localListObj[syncLocalPosition].userName}}正在编辑
                                                        </div>
                                                    </div>
                                                    <template v-else>
                                                        <div class="txt">{{ row.goodsInfo.num }}</div>
                                                    </template>
                                                    <div class="unit text-line-1" :title="row.goodsInfo.unitName">{{ row.goodsInfo.unitName }}</div>
                                                </div>
                                            </div>
                                            <div class="edit-item" :class="{'can-edit': quoteValidStatus == 0 && businessStage != 6}">
                                                <div class="label" :class="{highlight: needLeftTip(row) && row.goodsInfo.isConsulDeliveryCycle}">预计货期：</div>
                                                <div class="content">
                                                    <div class="cnt-input-wrap" v-if="quoteValidStatus == 0 && businessStage != 6" :class="{focus: isOnfocus(row.goodsInfo, 'time')}">
                                                        <ui-input 
                                                            size="small" 
                                                            v-model="row.goodsInfo.expectDeliveryTime"  
                                                            @focus="handlerPositionChange(row.goodsInfo, 'time', row.goodsInfo.expectDeliveryTime)"  
                                                            @blur="handlerExpectDeliveryTimeChange(row)" 
                                                            @input="handlerListInputChange" 
                                                            type="number"
                                                            maxlength="3" 
                                                            width="92px" 
                                                            :class="{'value-change': timeChangeObj[row.goodsInfo.quoteorderGoodsId]}">
                                                        </ui-input>
                                                        <div class="focus-tip" v-if="isOnfocus(row.goodsInfo, 'time')">
                                                            {{localListObj[syncLocalPosition].userName}}正在编辑
                                                        </div>
                                                    </div>
                                                    <template v-else>
                                                        <div class="txt">{{ row.goodsInfo.expectDeliveryTime }}</div>
                                                    </template>
                                                    <div class="unit">天</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="vd-ui-td">
                                        <div class="quote-td-cnt" v-if="row.goodsInfo && row.goodsInfo.skuNo">
                                            <div class="label-item">
                                                <div class="label">保修期：</div>
                                                <div class="txt">{{ row.goodsInfo.warrantyInfo || '-' }}</div>
                                            </div>
                                            <div class="label-item">
                                                <div class="label">使用年限/效期：</div>
                                                <div class="txt">{{ row.goodsInfo.useLife || '-' }}</div>
                                            </div>
                                            <div class="label-item">
                                                <div class="label">安装政策：</div>
                                                <div class="txt">{{ ['不可安装','可安装'][row.goodsInfo.isInstall] || '-' }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="vd-ui-td color1">
                                        <div class="quote-td-cnt" v-if="row.goodsInfo && row.goodsInfo.skuNo">
                                            <div class="label-item">
                                                <div class="label">参考核价：</div>
                                                <div class="txt">{{ row.goodsInfo.dealerPrice || '未核价' }}</div>
                                            </div>
                                            <div class="label-item">
                                                <div class="label">可用库存：</div>
                                                <div class="txt">{{ row.goodsInfo.availableStockNum || row.goodsInfo.availableStockNum === 0 ? row.goodsInfo.availableStockNum : '-' }}</div>
                                            </div>
                                            <div class="label-item">
                                                <div class="label">产品负责：</div>
                                                <div class="user-list" v-if="row.goodsInfo && row.goodsInfo.skuNo">
                                                    <template v-if="row.goodsInfo && row.goodsInfo.productManager && row.goodsInfo.productManager.length">
                                                        <template v-for="(item, index) in row.goodsInfo.productManager">
                                                            <div class="user-item" v-if="!(index == 1 && item.userId == row.goodsInfo.productManager[0].userId)">
                                                                <div class="user-avatar">
                                                                    <img :src="item.aliasHeadPicture || GLOBAL.defaultAvatar" alt="">
                                                                </div>
                                                                <div class="user-name text-line-1" :title="item.username">{{ item.username }}</div>
                                                            </div>
                                                        </template>
                                                    </template>
                                                    <template v-else>-</template>
                                                </div>
                                                <div class="user-list" v-else-if="row.needsInfo">
                                                    <template v-if="row.needsInfo && row.needsInfo.headUserList">
                                                        {{ row.needsInfo.headUserList[0].username || '-' }}
                                                    </template>
                                                    <template v-else>-</template>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="vd-ui-td color1">
                                        <div class="quote-td-cnt">
                                            <div class="label-item">
                                                <div class="label">是否需要报备：</div>
                                                <div class="txt">
                                                    <template v-if="row.goodsInfo && row.goodsInfo.skuNo">
                                                        {{ {0:'否', 1:'是'}[row.goodsInfo.isNeedReport] || '-' }}
                                                    </template>
                                                    <template v-else>-</template>
                                                </div>
                                            </div>
                                            <div class="label-item">
                                                <div class="label" :class="{highlight: needLeftTip(row) && row.goodsInfo && row.goodsInfo.isConsulDeliveryCycle}">
                                                    <template v-if="row.goodsInfo && row.goodsInfo.skuName && GLOBAL.auth('C0220') && quoteValidStatus == 0 && businessStage != 6">
                                                        报备：
                                                    </template>
                                                    <template v-else>
                                                        报备状态：
                                                    </template>
                                                </div>
                                                <template v-if="row.goodsInfo && row.goodsInfo.skuName">
                                                    <div class="txt report-txt" :class="[{1:'orange',2:'green',3:'red',0:'green'}[row.goodsInfo.reportStatus]]">
                                                        <div class="txt-cnt txt-option" v-if="GLOBAL.auth('C0220') && quoteValidStatus == 0 && businessStage != 6">
                                                            <!-- {{ {0:'无需报备',1:'报备中',2:'报备成功',3:'报备失败'}[row.goodsInfo.reportStatus] || '未报备' }}
                                                            <i class="vd-ui_icon icon-edit"></i>
                                                             -->
                                                             <div class="report-radio-wrap">
                                                                <ui-radio-group
                                                                    :list="reportStatusList"
                                                                    :value.sync="row.goodsInfo.reportStatus"
                                                                    @change="handlerReportStatusChange(row)"
                                                                ></ui-radio-group>
                                                            </div>
                                                        </div>
                                                        <template v-else>
                                                            <div class="txt-cnt">
                                                                {{ {0:'无需报备',1:'报备中',2:'报备成功',3:'报备失败'}[row.goodsInfo.reportStatus] || '未报备' }}
                                                                <!-- <i class="vd-ui_icon icon-edit disabled"></i> -->
                                                            </div>
                                                            <div class="txt-tip" v-if="row.goodsInfo.reportStatus == 3 && row.goodsInfo.reportComments">
                                                                <ui-tip position="tl">{{ row.goodsInfo.reportComments }}</ui-tip>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </template>
                                                <template v-else>
                                                    <div class="txt">-</div>
                                                </template>
                                            </div>
                                            <div class="report-reason-wrap" v-if="row.goodsInfo && row.goodsInfo.skuName && GLOBAL.auth('C0220') && quoteValidStatus == 0 && businessStage != 6 && row.goodsInfo.reportStatus == 3">
                                                <ui-input type="textarea" @input="handlerPositionChange(row.goodsInfo, 'reportComments', row.goodsInfo.reportComments)"  resize="none" maxlength="100" placeholder="请填写失败原因" v-model="row.goodsInfo.reportComments" @blur="handlerReportStatusChange(row)" width="185px" height="62px"></ui-input>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="vd-ui-td color1">
                                        <template v-if="row.goodsInfo">
                                            <template v-if="row.goodsInfo.remark">
                                                <div class="td-remark-info">
                                                    <div class="remark-title">
                                                        <div class="title-name text-line-1">{{ row.goodsInfo.remarkUserName }}</div>
                                                        <div class="title-time">{{ row.goodsInfo.remarkAddTime }}</div>
                                                    </div>
                                                    <div class="remark-cnt text-line-3" v-html="row.goodsInfo.remark.replace(/\n/g, '<br/>')"></div>
                                                    <div class="remark-options">
                                                        <div class="remark-option-item" @click="showRemarkDetail(row)">查看全部（{{row.goodsInfo.remarkCount}}）</div>
                                                        <div class="remark-option-item" @click="showAddRemarkDialog(row)">添加备注</div>
                                                    </div>
                                                </div>
                                            </template>
                                            <div class="td-remark-btn" v-else>
                                                <div class="btn-link" @click="showAddRemarkDialog(row)">添加备注</div>
                                            </div>
                                        </template>
                                    </td>
                                    <td class="vd-ui-td color1" v-if="quoteValidStatus == 0">
                                        <div class="quote-td-cnt visible option">
                                            <div class="option-item" v-if="isAuthConsultation" @click="singleConsultation(row)">咨询报备</div>
                                            <template v-if="(row.needsInfo || row.parent) && GLOBAL.auth('C0210') && isAuthAddProduct">
                                                <template v-if="row.goodsInfo && row.goodsInfo.skuName && !row.goodsInfo.skuNo">
                                                    <div class="option-item" @click="showCustomSelectProdDialog(row, 'edit')">编辑产品</div>
                                                </template>                                               
                                                <ui-select-button type="text" placeholder="添加对应产品">
                                                    <div @click="showSelectProdDialog(row)">选择产品</div>
                                                    <div @click="showCustomSelectProdDialog(row)">手工添加</div>
                                                </ui-select-button>
                                            </template>
                                            <div class="option-item" v-if="isAuthDelete && row.goodsInfo && row.goodsInfo.skuName" @click="deleteConfirm('single', row)">删除报价产品</div>
                                        </div>
                                    </td>
                                </template>
                            </ui-table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <ui-dialog
            :visible.sync="isShowMultiAdd"
            title="添加产品"
            width="720px"
        >
            <div class="multi-add-wrap">
                <div class="multi-add-l">
                    <ui-input type="textarea" :errorable="!!multiAddErrorMsg" 
                    :placeholder="`案例：\nV136266\nV166977`" 
                    min-height="480px" resize="none" @blur="hanlderMultiAddInputBlur" v-model="multiValue"></ui-input>
                </div>
                <div class="multi-add-r">
                    <div class="multi-add-tips">
                        可输入多个订货号，并使用回车换行区分；<br>
                        单次操作最多支持选择 200 个订货号；<br>
                        添加商品排序会与输入的订货号顺序保持一致；<br>
                        可直接复制粘贴EXCEL“列”，程序将会自动去除空行及订货号中的空格；
                    </div>
                    <div class="multi-error-wrap" v-if="multiAddErrorMsg">
                        <i class="vd-ui_icon icon-error2"></i>
                        <div class="error-txt">{{ multiAddErrorMsg }}</div>
                    </div>
                </div>
            </div>
            <template slot="footer">
                <ui-button type="primary" @click="multiAddProds">确定</ui-button>
                <ui-button @click="hideMultiAdd">取消</ui-button>
            </template>
        </ui-dialog>
        <!-- <ui-dialog
            :visible.sync="isShowSingleAdd"
            :title="singleAddPrevGoods ? '重新选择产品' : '添加产品'"
            width="720px"
        >
            <div class="form-wrap label-width-2 error-nowrap">
                <ui-form-item label="订货号" :must="true">
                    <ui-input v-model="singleAddValue" width="170px" @blur="checkSingleProd" :errorable="!!singleAddErrorMsg" :error-msg="singleAddErrorMsg" 
                    ></ui-input>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer">
                    <ui-button type="primary" @click="singleAddProd">确定</ui-button>
                    <ui-button @click="hideSingleAdd">取消</ui-button>
                </div>
            </template>
        </ui-dialog> -->
        <ui-dialog
            :visible.sync="isShowConsultationDialog"
            title="报备"
            width="720px"
        >
            <div class="form-wrap label-width-2 error-nowrap" v-if="isShowConsultationDialog">
                <ui-form-item label="咨询内容" :must="true" :text="true">
                    <ui-checkbox-group
                        :list="consultationContent"
                        :values.sync="consultationValue"
                        valid="quoteConsultationDialog_consultationValue"
                    ></ui-checkbox-group>
                </ui-form-item>
                <ui-form-item label="咨询人员">
                    <div class="dlg-consultation-users">
                        <div class="user-list">
                            <div class="user-item" v-for="(item, index) in consultationUserList">
                                <div class="item-avatar">
                                    <img :src="item.headPic || GLOBAL.defaultAvatar" alt="">
                                </div>
                                <div class="item-name">{{ item.userName }}</div>
                            </div>
                        </div>
                    </div>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer">
                    <ui-button type="primary" @click="consultationSubmit">提交</ui-button>
                    <ui-button @click="hideConsultationDialog">取消</ui-button>
                </div>
            </template>
        </ui-dialog>
        <ui-dialog
            :visible.sync="isShowImportNeeds"
            title="导入客户需求"
            width="720px"
            class="import-dialog"
        >
            <div class="form-top-tip">
                <i class="vd-ui_icon icon-info2"></i>
                <div class="tip-cnt">按照模板整理客户需求，供应链部门可更高效的协助处理报价单。</div>
            </div>
            <div class="import-content-wrap form-wrap label-width-2" v-if="quoteorderId">
                <ui-form-item label="关系配置" :must="true" v-if="isShowImportNeeds">
                    <ui-import limit-type="xls,xlsx" @change="handlerImportChange" :error-msg="importErrorMsg">
                        <template v-slot:tips>
                            - 请按照模板要求整理好表格文件；<br>
                            - 单个文件大小不超过5M，数据不超过1000行；<br>
                            - “需求产品”为空的行自动跳过，不做导入；<br>
                            - 再次导入时会覆盖原“客户需求”，请注意产品顺序。
                        </template>
                    </ui-import>
                    <div class="tmpl-download-wrap">
                        <div class="tmpl-icon">
                            <img src="/static/image/upload-icon/excel.svg" alt="">
                        </div>
                        <div class="tmpl-name">客户需求模板</div>
                        <a class="tmpl-link" href="https://file.vedeng.com/file/download?resourceId=cc23e3b2193c4eb9926763de8579fc04" target="_blank">下载</a>
                    </div>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer">
                    <ui-button type="primary" @click="multiImportProds">确定</ui-button>
                    <ui-button @click="hideMultiImportAdd">取消</ui-button>
                </div>
            </template>
        </ui-dialog>
        <ui-dialog
            :visible.sync="isShowChatDialog"
            title="协同建群"
            width="960px"
        >
            <div class="group-user-list">
                <ui-table :can-choose="true" v-if="isShowChatDialog" container-height="470px" :oneline="true" :width-border="true" :auto-scroll="false" :headers="groupChatHeaders" :list="chatUserList" ref="chatTableList"  @selectchange="handlerChatTableListSelect">
                    <template v-slot:username="{ row }">
                        <div class="group-user-item-wrap">
                            <div class="user-avatar">
                                <img :src="row.aliasHeadPicture || GLOBAL.defaultAvatar" alt="">
                            </div>
                            <div class="user-name">{{ row.userName }}</div>
                        </div>
                    </template>
                </ui-table>
            </div>
            <template slot="footer">
                <template v-if="isBuildChat == 0">
                    <div class="create-chat-tip">需归属销售主管审批后方可建群</div>
                    <ui-button @click="approveGroupChat(2)" type="primary">提交审批</ui-button>
                    <ui-button @click="isShowChatDialog = false" class="close">取消</ui-button>
                </template>
                <template v-if="isBuildChat == 2">
                    <div class="create-user-status">
                        <ui-user :avatar="chargeInfo.chargeNameHeadPicture" :name="chargeInfo.chargeName + '审批中'"></ui-user>
                    </div>
                    <template v-if="chargeInfo.chargeId != USERINFO.userId">
                        <ui-button disabled>审批中</ui-button>
                        <ui-button @click="isShowChatDialog = false" class="close">取消</ui-button>
                    </template>
                    <template v-else>
                        <ui-button type="danger" @click="approveGroupChat(1)">审批通过</ui-button>
                        <ui-button class="close" @click="approveGroupChat(0)">驳回</ui-button>
                    </template>
                </template>
            </template>
        </ui-dialog>
        <ui-dialog
            :visible.sync="isShowShareDialog"
            title="分享线上报价单"
            width="720px"
        >
            <div class="share-dlg-wrap" v-if="isShowShareDialog">
                <div class="share-info">
                    <div class="info-item">
                        <div class="info-label">报价单号：</div>
                        <div class="info-txt">{{ shareQuoteInfo.quoteorderNo }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">价格有效期：</div>
                        <div class="info-txt">{{ shareQuoteInfo.day }}天 ( {{ shareQuoteInfo.now }}~{{ shareQuoteInfo.end }} )</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">甲方：</div>
                        <div class="info-txt">{{ shareQuoteInfo.traderName || '-' }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">联系人：</div>
                        <div class="info-txt">{{ shareQuoteInfo.traderContactName || '-' }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">手机号：</div>
                        <div class="info-txt">{{ shareQuoteInfo.mobile || '-' }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">商品总价：</div>
                        <div class="info-txt"><span class="price">{{  shareQuoteInfo.quoteMoney.toFixed(2) }}</span>元 （共{{shareQuoteInfo.goodsCount}}种，{{shareQuoteInfo.totalNum}}件商品）</div>
                    </div>
                    <!-- <div class="info-item">
                        <div class="info-label">产品配送费：</div>
                        <div class="info-txt">0.00元</div>
                    </div> -->
                </div>
                <div class="share-msg">
                    {{shareTxt}}
                    <a :href="shareQuoteInfo.shardUrl" target="_blank" class="msg-link">{{ shareQuoteInfo.shardUrl }}</a>
                </div>
                <div class="share-footer">
                    <div class="footer-tip"><i class="vd-ui_icon icon-info2"></i>复制文案发给客户，并告知用户登录状态下进行查看。</div>
                    <div class="footer-options">
                        <a :href="shareQuoteInfo.shardUrl" target="_blank" class="footer-link">预览报价单</a>
                        <ui-button type="primary" @click="copyShareInfo">复制文案</ui-button>
                    </div>
                </div>
            </div>
        </ui-dialog>
        <!-- 手动添加产品 -->
        <ui-dialog
            :visible.sync="isShowCustomAddDialog"
            :title="customDialogType === 'edit' ? '编辑产品' : '添加产品'"
            width="960px"
        >
            <div class="custom-add-dlg-wrap" v-if="isShowCustomAddDialog">
                <div class="form-top-tip" >
                    <i class="vd-ui_icon icon-info2"></i>
                    <div class="tip-cnt">
                        “转订单”和“分享线上报价”时需把手动添加产品换成ERP已建档产品。
                    </div>
                </div>
                <div class="custom-add-form form-wrap label-width-5">
                    <ui-form-item label="产品名称" :must="true">
                        <div class="ui-col-10">
                            <ui-input maxlength="100" placeholder="不超过100字" valid="customAddProdForm_goodsName" v-model="customAddInfo.goodsName"></ui-input>
                        </div>
                    </ui-form-item>
                    <ui-form-item label="产品品牌" :must="true">
                        <div class="ui-col-6">
                            <ui-select :remote="true" @change="customBrandSelectChange" :default-label="customAddInfo.brandName" valid="customAddProdForm_brandId" placeholder="请选择" v-model="customAddInfo.brandId" clearable :remote-info="customBrandRemoteInfo"></ui-select>
                        </div>
                    </ui-form-item>
                    <ui-form-item label="产品型号" :must="true">
                        <div class="ui-col-6">
                            <ui-input maxlength="50" placeholder="不超过50字" valid="customAddProdForm_model" v-model="customAddInfo.model"></ui-input>
                        </div>
                    </ui-form-item>
                    <ui-form-item label="产品单位" :must="true">
                        <div class="ui-col-2">
                            <ui-input maxlength="4" placeholder="不超过4个字" valid="customAddProdForm_unitName" v-model="customAddInfo.unitName"></ui-input>
                        </div>
                    </ui-form-item>
                    <ui-form-item label="产品图片">
                        <div class="ui-col-10">
                            <ui-upload type="img" :list="customAddInfo.imgUrl ? [{url: customAddInfo.imgUrl}] : []" limit-type="jpg,jpeg,png" :error-msg="{limitSize: '图片请勿超过2M。', limitType: '图片格式仅限jpg、png、jpeg格式。'}" @change="handlerCustomProdUpload">
                                <template v-slot:tips>-支持 2M 以内 jpg、png、jpeg 格式图片</template>
                            </ui-upload>
                        </div>
                    </ui-form-item>
                    <ui-form-item label="产品参数">
                        <div class="ui-col-10">
                            <ui-input 
                                type="textarea"
                                width="590px"
                                height="150px"
                                maxlength="500"
                                placeholder="不超过500字"
                                v-model="customAddInfo.paramContent"
                            >
                            </ui-input>
                        </div>
                    </ui-form-item>
                </div>
                <div class="dlg-form-footer">
                    <ui-button type="primary" @click="submitCustomProdInfo">提交</ui-button>
                    <ui-button @click="isShowCustomAddDialog = false">取消</ui-button>
                </div>
            </div>
        </ui-dialog>
        <!-- 商机详情 -->
        <ui-dialog
            :visible.sync="isShowBusinessInfo"
            title="商机信息"
            width="720px"
        >
            <div class="biz-dlg-info-wrap" v-if="isShowBusinessInfo">
                <div class="dlg-info-block">
                    <div class="dlg-info-title">客户和终端信息</div>
                    <div class="dlg-info-item">
                        <div class="info-label">商机编号：</div>
                        <div class="info-txt">{{ businessInfo.bussinessChanceNo }}</div>
                    </div>
                    <div class="dlg-info-item">
                        <div class="info-label">客户名称：</div>
                        <div class="info-txt">{{ businessInfo.traderName }}</div>
                    </div>
                    <div class="dlg-info-item">
                        <div class="info-label">归属销售：</div>
                        <div class="info-txt">{{ businessInfo.belonger }}</div>
                    </div>
                    <div class="dlg-info-item">
                        <div class="info-label">终端名称：</div>
                        <div class="info-txt">{{ businessInfo.terminalTraderName || '-' }}</div>
                    </div>
                    <div class="dlg-info-item">
                        <div class="info-label">终端性质：</div>
                        <div class="info-txt">{{ businessInfo.terminalTraderNatureStr || '-' }}</div>
                    </div>
                    <div class="dlg-info-item">
                        <div class="info-label">终端区域：</div>
                        <div class="info-txt">{{ businessInfo.terminalTraderRegionStr || '-' }}</div>
                    </div>
                </div>
                <div class="dlg-info-block">
                    <div class="dlg-info-title">商机信息</div>
                    <div class="dlg-info-item">
                        <div class="info-label">商机等级：</div>
                        <div class="info-txt">{{ businessInfo.systemBusinessLevelStr || '-' }}</div>
                    </div>
                    <div class="dlg-info-item">
                        <div class="info-label">商机类型：</div>
                        <div class="info-txt">{{ businessInfo.businessTypeStr || '-' }}</div>
                    </div>
                    <div class="dlg-info-item">
                        <div class="info-label">预计成单金额：</div>
                        <div class="info-txt">{{ businessInfo.amount? businessInfo.amount + '元' : '-' }}</div>
                    </div>
                    <div class="dlg-info-item">
                        <div class="info-label">预计成单时间：</div>
                        <div class="info-txt">{{ businessInfo.orderTimeDate || '-' }}</div>
                    </div>
                    <div class="dlg-info-item">
                        <div class="info-label">产品信息：</div>
                        <div class="info-txt">{{ businessInfo.productCommentsSale || '-' }}</div>
                    </div>
                    <div class="dlg-info-item" v-if="tagsShow">
                        <div class="info-label">商机标签：</div>
                        <div class="info-txt">{{ tagsShow }}</div>
                    </div>
                    <div class="dlg-info-item" v-if="businessInfo.customerRelationshipStr &&  businessInfo.customerRelationshipStr.length">
                        <div class="info-label">客情关系：</div>
                        <div class="info-txt">{{ businessInfo.customerRelationshipStr.join('/') }}</div>
                    </div>
                    <div class="dlg-info-item" v-if="businessInfo.purchasingTypeStr">
                        <div class="info-label">采购方式：</div>
                        <div class="info-txt">{{ businessInfo.purchasingTypeStr }}</div>
                    </div>
                    <div class="dlg-info-item" v-if="businessInfo.biddingPhaseStr">
                        <div class="info-label">招标阶段：</div>
                        <div class="info-txt">{{ businessInfo.biddingPhaseStr }}</div>
                    </div>
                    <div class="dlg-info-item" v-if="businessInfo.biddingParameter">
                        <div class="info-label">参数可调整：</div>
                        <div class="info-txt">{{ {1:'可调整', 2: '不可调整'}[businessInfo.biddingParameter] }}</div>
                    </div>
                </div>
            </div>
        </ui-dialog>
        <!-- 客户需求描述 -->
        <ui-dialog
            :visible.sync="isShowReqDescription"
            :title="isAuthEditReqDescription ? '编辑客户需求描述' : '客户需求描述'"
            width="720px"
        >
            <div class="req-des-wrap" v-if="isShowReqDescription">
                <template v-if="isAuthEditReqDescription">
                    <div class="form-wrap label-width-2">
                        <ui-form-item label="客户需求描述">
                            <div class="ui-col-10">
                                <ui-input 
                                    type="textarea"
                                    width="440px"
                                    height="75px"
                                    show-word-limit
                                    maxlength="500"
                                    placeholder="请添加客户报价一些特殊需求备注，供应链会根据特殊需求进行报价"
                                    v-model="resDescription"
                                ></ui-input>
                            </div>
                        </ui-form-item>
                    </div>
                    <div class="dlg-form-footer">
                        <ui-button type="primary" @click="editResDescription">提交</ui-button>
                        <ui-button @click="isShowReqDescription = false">取消</ui-button>
                    </div>
                </template>
                <div v-else v-html="resDescription.replace(/\n/g, '<br/>')"></div>
            </div>
        </ui-dialog>
        <!-- 查看全部备注 -->
        <ui-dialog
            :visible.sync="isShowRemarkDetail"
            title="备注信息"
            width="720px"
            @close="handlerRemarkDialogClose"
        >
            <div class="remark-dialog-info-wrap" v-if="isShowRemarkDetail">
                <div class="remark-info-options">
                    <ui-button type="primary" @click="listShowAddRemarkDialog">添加备注</ui-button>
                </div>
                <div class="remark-info-list">
                    <div class="remark-info-item" v-for="(item, index) in remarkInfoList" :key="index">
                        <div class="remark-item-t">
                            <div class="remark-user">
                                <div class="user-avatar">
                                    <img :src="item.headPic || GLOBAL.defaultAvatar" alt="">
                                </div>
                                <div class="user-name">{{ item.userName }}</div>
                            </div>
                            <div class="remark-time">{{ item.addTime }}</div>
                        </div>
                        <div class="remark-cnt" v-html="item.remark.replace(/\n/g, '<br/>')"></div>
                    </div>
                </div>
            </div>
        </ui-dialog>
        <!-- 添加备注 -->
        <ui-dialog
            :visible.sync="isShowAddRemark"
            title="添加备注"
            width="720px"
        >
            <div class="req-des-wrap" v-if="isShowAddRemark">
                <div class="form-wrap label-width-2">
                    <ui-form-item label="备注" :must="true">
                        <div class="ui-col-10">
                            <ui-input 
                                type="textarea"
                                width="440px"
                                height="75px"
                                show-word-limit
                                maxlength="100"
                                v-model="remarkEditValue"
                                valid="remarkAddForm_remarkEditValue"
                            ></ui-input>
                        </div>
                    </ui-form-item>
                </div>
                <div class="dlg-form-footer">
                    <ui-button type="primary" @click="submitRemark">提交</ui-button>
                    <ui-button @click="isShowAddRemark = false">取消</ui-button>
                </div>
            </div>
        </ui-dialog>
        <!-- 跟进记录 dialog -->
        <follow-record-list-dialog ref="followRecordListDialog" :communicate-type="244"></follow-record-list-dialog>
        <!-- 任务 dialog -->
        <renwu-dialog ref="renwuDialog" :refresh-panel="getHeaderTaskNum"></renwu-dialog>
        <!-- 操作记录 dialog -->
        <operation-log-dialog ref="operationLogDialog"></operation-log-dialog>
        <!-- 协作人 dialog -->
        <partner-list-dialog ref="partnerListDialog"></partner-list-dialog>
        <!-- 选产品 -->
        <ui-select-prod ref="selectProd" @select="handlerProdSelect"></ui-select-prod>
        <!-- 报价单附件 -->
        <file-dialog ref="fileDialog" @change="fileUploadChange"></file-dialog>
        <!-- 长时间未操作提示 -->
        <div class="option-refresh-tip-wrap" v-show="isShowOptionTip">
            <div class="option-refresh-tip-cnt">
                <div class="option-tip-icon"></div>
                <div class="option-tip-txt">由于您长时间未操作，可能带来页面数据同步不及时<br>点击下方按钮进行页面数据重新同步</div>
                <ui-button type="primary" @click="refreshPage">同步数据</ui-button>
            </div>
        </div>
        <!-- 转订单 -->
        <ui-dialog
            :visible.sync="isShowToOrder"
            :title="GLOBAL.checkCrmType().label + '-转订单'"
            width="720px"
        >
            <div class="form-wrap label-width-2">
                <ui-form-item label="" :text="true">
                    <div class="ui-col-8 order-type">
                        <ui-radio-group
                            :list="ToOrderTypes"
                            :value.sync="toorderOrderType"
                            average-num="1"
                            valid="ToOrderValid_toorderOrderType"
                        ></ui-radio-group>
                    </div>
                </ui-form-item>
                <ui-form-item label="" v-if="toorderOrderType == '2'" style="margin-top: -10px;">
                    <div class="ui-col-7">
                        <ui-input
                            width="260px"
                            height="75px"
                            placeholder="请输入销售单号"
                            show-word-limit
                            maxlength="50"
                            @change="toorderChackOrderNo"
                            v-model="toorderSaleOrderNo"
                            :errorable="!!toorderSaleOrderNoError"
                            :error-msg="toorderSaleOrderNoError"
                        ></ui-input>
                    </div>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer">
                    <ui-button @click="toorderValidSubmit" type="primary">提交</ui-button>
                    <ui-button @click="isShowToOrder=false" class="close">取消</ui-button>
                </div>
            </template>
        </ui-dialog>
    </div>

    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/vuedraggable.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 价格记录 -->
    <script src="/static/js/common/components/business/priceHistory.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 跟进记录相关 -->
    <script src="/static/js/common/components/business/records.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 任务记录 -->
    <script src="/static/js/common/components/business/renwu.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 操作记录 -->
    <script src="/static/js/common/components/business/operationLog.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/components/business/operationLog-dialog.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 协作人 -->
    <script src="/static/js/common/components/business/partner.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 选产品弹层 -->
    <script src="/static/js/common/components/selectProd.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/pages/quoteOrder.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 需求附件 -->
    <script src="/static/js/common/components/business/file.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
