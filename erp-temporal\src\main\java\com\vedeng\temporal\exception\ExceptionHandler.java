package com.vedeng.temporal.exception;

import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.notification.NotificationContext;
import com.vedeng.temporal.notification.TemporalNotificationService;
import com.vedeng.temporal.notification.NotificationLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 异常处理工具类（精简版）
 *
 * 核心功能：
 * 1. 统一的异常处理和转换
 * 2. 简化的处理流程，减少冗余代码
 * 3. 集成通知服务和日志记录
 *
 * <AUTHOR> 4.0 sonnet
 * @version 6.0 (精简版)
 * @since 2025-01-26
 */
@Component
@Slf4j
public class ExceptionHandler {

    @Autowired
    private ErrorClassifier errorClassifier;

    @Autowired
    private TemporalNotificationService notificationService;

    /**
     * 处理业务异常（统一入口）
     * 
     * @param e 原始异常
     * @param operationName 操作名称
     * @param businessId 业务ID
     * @param companyCode 公司代码
     * @return CompanyBusinessResponse（业务异常）或抛出RuntimeException（技术异常）
     */
    public CompanyBusinessResponse handleBusinessException(Exception e, String operationName,
                                                         String businessId, String companyCode) {
        try {
            // 构建业务上下文
            String businessContext = buildBusinessContext(operationName, businessId, companyCode);
            
            // 分类并创建标准业务异常
            BusinessProcessException businessException = classifyException(e, businessContext);
            
            // 根据异常类型处理
            return processException(businessException, operationName, businessId);
            
        } catch (Exception handlingException) {
            // 异常处理过程中出现异常，使用兜底策略
            log.error("异常处理过程中发生错误，操作: {}, 业务ID: {}", operationName, businessId, handlingException);
            return CompanyBusinessResponse.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }

    /**
     * 处理Activity异常并抛出（用于Activity层）
     * 
     * @param e 原始异常
     * @param operationName 操作名称
     * @param businessId 业务ID
     * @param companyCode 公司代码
     * @throws BusinessProcessException 业务异常时抛出
     * @throws RuntimeException 技术异常时抛出（触发Temporal重试）
     */
    public void handleAndThrowActivityException(Exception e, String operationName, 
                                              String businessId, String companyCode) {
        String businessContext = buildBusinessContext(operationName, businessId, companyCode);
        BusinessProcessException businessException = classifyException(e, businessContext);
        
        // 发送通知
        sendNotification(businessException, operationName, businessId);
        
        // 根据异常类型抛出
        if (businessException.isRetryable()) {
            log.warn("技术异常，将由Temporal重试: {}", businessException.getMessage());
            throw new RuntimeException(businessException.getMessage(), businessException);
        } else {
            log.error("业务异常，停止重试: {}", businessException.getMessage());
            throw businessException;
        }
    }

    /**
     * 统一处理执行结果和异常
     * 
     * @param response 执行结果（可为null）
     * @param exception 异常（可为null）
     * @param operationName 操作名称
     * @param businessId 业务ID
     * @param companyCode 公司代码
     * @param duration 执行耗时（毫秒）
     * @return CompanyBusinessResponse 处理后的统一响应
     */
    public CompanyBusinessResponse handleExecutionResult(CompanyBusinessResponse response, 
                                                        Exception exception,
                                                        String operationName, 
                                                        String businessId, 
                                                        String companyCode,
                                                        Long duration) {
        try {
            // 构建通知上下文
            NotificationContext.NotificationContextBuilder contextBuilder = NotificationContext.builder()
                    .operationName(operationName)
                    .businessId(businessId)
                    .targetCompany(companyCode)
                    .duration(duration);
            
            // 情况1：有异常发生
            if (exception != null) {
                log.error("执行异常: 操作={}, 业务ID={}, 公司={}, 耗时={}ms", 
                         operationName, businessId, companyCode, duration, exception);
                
                // 构建异常上下文并发送通知
                NotificationContext context = contextBuilder.build()
                        .withException(exception, duration);
                
                // 根据异常类型发送不同级别的通知
                if (isRetryableException(exception)) {
                    notificationService.sendTechnicalErrorNotification(context);
                } else {
                    notificationService.sendBusinessFailureNotification(context);
                }
                
                // 返回异常响应
                return CompanyBusinessResponse.failure(
                        operationName + "执行异常: " + exception.getMessage(),
                        "EXECUTION_EXCEPTION"
                );
            }
            
            // 情况2：执行成功但业务失败
            if (response != null && !Boolean.TRUE.equals(response.getSuccess())) {
                log.warn("业务失败: 操作={}, 业务ID={}, 公司={}, 耗时={}ms, 原因={}", 
                        operationName, businessId, companyCode, duration, response.getMessage());
                
                // 根据错误码判断是否需要通知
                if (shouldNotifyForBusinessFailure(response.getErrorCode())) {
                    NotificationContext context = contextBuilder.build()
                            .withFailure(response.getMessage(), response.getErrorCode(), duration);
                    notificationService.sendBusinessFailureNotification(context);
                }
                
                return response;
            }
            
            // 情况3：执行成功
            if (response != null) {
                // 检查是否需要性能预警
                if (duration != null && shouldWarnForPerformance(duration)) {
                    log.warn("性能预警: 操作={}, 业务ID={}, 公司={}, 耗时={}ms", 
                            operationName, businessId, companyCode, duration);
                    
                    // 可选：发送性能预警通知
                    // NotificationContext context = contextBuilder.build();
                    // notificationService.sendPerformanceWarningNotification(context);
                }
                
                log.debug("执行成功: 操作={}, 业务ID={}, 公司={}, 耗时={}ms", 
                         operationName, businessId, companyCode, duration);
                return response;
            }
            
            // 情况4：没有响应也没有异常（不应该发生）
            log.error("未知状态: 操作={}, 业务ID={}, 公司={}, 无响应无异常", 
                     operationName, businessId, companyCode);
            return CompanyBusinessResponse.failure(
                    operationName + "执行状态未知",
                    "UNKNOWN_EXECUTION_STATE"
            );
            
        } catch (Exception handlingException) {
            // 异常处理过程中出现异常，使用兜底策略
            log.error("处理执行结果时发生异常，操作: {}, 业务ID: {}", operationName, businessId, handlingException);
            return CompanyBusinessResponse.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }

    // ========== 私有辅助方法 ==========
    
    /**
     * 分类异常并创建标准业务异常
     */
    private BusinessProcessException classifyException(Exception e, String businessContext) {
        if (e instanceof BusinessProcessException) {
            return (BusinessProcessException) e;
        }
        
        return errorClassifier.classifyAndCreateException(e, null, businessContext);
    }
    
    /**
     * 处理分类后的业务异常
     */
    private CompanyBusinessResponse processException(BusinessProcessException e, String operationName, String businessId) {
        // 发送通知
        sendNotification(e, operationName, businessId);
        
        if (e.isRetryable()) {
            // 技术异常：记录日志并抛出RuntimeException让Temporal重试
            log.warn("技术异常，将由Temporal重试: 操作={}, 业务ID={}, 错误码={}, 消息={}", 
                    operationName, businessId, e.getErrorCode(), e.getMessage());
            throw new RuntimeException(e.getMessage(), e);
            
        } else {
            // 业务异常：记录日志并返回失败响应
            log.error("业务异常，停止重试: 操作={}, 业务ID={}, 错误码={}, 消息={}", 
                    operationName, businessId, e.getErrorCode(), e.getMessage());
            return CompanyBusinessResponse.failure(e.getMessage(), e.getErrorCode());
        }
    }
    
    /**
     * 发送通知
     */
    private void sendNotification(BusinessProcessException e, String operationName, String businessId) {
        try {
            // 从业务上下文中提取公司代码
            String companyCode = extractCompanyFromContext(e.getBusinessContext());
            
            NotificationContext context = NotificationContext.builder()
                    .operationName(operationName)
                    .businessId(businessId)
                    .targetCompany(companyCode)
                    .build()
                    .withException(e, 0L);

            if (e.isRetryable()) {
                notificationService.sendTechnicalErrorNotification(context);
            } else {
                notificationService.sendBusinessFailureNotification(context);
            }
        } catch (Exception notificationException) {
            log.warn("发送通知失败: {}", notificationException.getMessage());
        }
    }
    
    /**
     * 构建业务上下文
     */
    private String buildBusinessContext(String operationName, String businessId, String companyCode) {
        StringBuilder sb = new StringBuilder();
        if (operationName != null) sb.append("Operation=").append(operationName);
        if (businessId != null) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("BusinessId=").append(businessId);
        }
        if (companyCode != null) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("Company=").append(companyCode);
        }
        return sb.length() > 0 ? sb.toString() : null;
    }
    
    /**
     * 从业务上下文中提取公司代码
     */
    private String extractCompanyFromContext(String businessContext) {
        if (businessContext == null) return null;
        String pattern = "Company=";
        int start = businessContext.indexOf(pattern);
        if (start == -1) return null;
        start += pattern.length();
        int end = businessContext.indexOf(", ", start);
        return end == -1 ? businessContext.substring(start) : businessContext.substring(start, end);
    }
    
    /**
     * 判断异常是否可重试
     */
    private boolean isRetryableException(Exception exception) {
        if (exception instanceof BusinessProcessException) {
            return ((BusinessProcessException) exception).isRetryable();
        }
        
        // 使用现有的 ErrorClassifier 进行分类判断
        try {
            BusinessProcessException classified = errorClassifier.classifyAndCreateException(exception, null, null);
            return classified.isRetryable();
        } catch (Exception e) {
            // 分类失败时，默认认为是技术异常（可重试）
            log.warn("异常分类失败，默认为可重试异常: {}", exception.getClass().getSimpleName());
            return true;
        }
    }
    
    /**
     * 判断业务失败是否需要通知
     * 根据错误码判断严重程度
     */
    private boolean shouldNotifyForBusinessFailure(String errorCode) {
        if (errorCode == null) {
            return false;
        }
        
        // 需要通知的严重业务错误
        String[] criticalErrorCodes = {
            "DATA_INCONSISTENCY",           // 数据不一致
            "BUSINESS_RULE_VIOLATION",      // 严重业务规则违反
            "SYSTEM_INTEGRATION_FAILURE",   // 系统集成失败
            "FINANCIAL_CALCULATION_ERROR",  // 财务计算错误
            "INVENTORY_SYNC_FAILURE",       // 库存同步失败
            "ORDER_STATUS_CONFLICT",        // 订单状态冲突
            "PAYMENT_PROCESSING_ERROR"      // 支付处理错误
        };
        
        for (String criticalCode : criticalErrorCodes) {
            if (errorCode.contains(criticalCode)) {
                return true;
            }
        }
        
        // 一般验证错误不需要通知
        String[] ignorableErrorCodes = {
            "VALIDATION_ERROR",           // 验证错误
            "FIELD_FORMAT_ERROR",         // 字段格式错误
            "PARAMETER_MISSING",          // 参数缺失
            "USER_INPUT_ERROR"            // 用户输入错误
        };
        
        for (String ignorableCode : ignorableErrorCodes) {
            if (errorCode.contains(ignorableCode)) {
                return false;
            }
        }
        
        // 未知错误码，保守起见发送通知
        return true;
    }
    
    /**
     * 判断是否需要性能预警
     * 根据执行时间判断是否需要预警
     */
    private boolean shouldWarnForPerformance(Long duration) {
        if (duration == null) {
            return false;
        }
        
        // 执行时间超过30秒时预警
        return duration > 30000;
    }
}