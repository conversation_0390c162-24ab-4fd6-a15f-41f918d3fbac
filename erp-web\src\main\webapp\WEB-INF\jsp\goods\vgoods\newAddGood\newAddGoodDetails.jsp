<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@taglib prefix="shiro" uri="http://shiro.apache.org/tags" %>
<c:set var="title" value="" scope="application"/>
<title>商品整合查询页</title>
<%@ include file="../../../common/common.jsp"%>
 <style>
     .vd-icon{
         background: none;
     }
     .whitetable tr{
        background:none !important;
     }
     .whitetable tr:hover{
        background:none !important;
     }

 </style>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/sku/sku_composite_view.css?rnd=${resourceVersionKey}">

<div class="detail-wrap">
    <input type="hidden" name="spuLevel" value="${coreSpuDto.spuLevel}">
    <input type="hidden" name="skuType" value="${command.skuType}">
    <input type="hidden" id="isSupplyAssistant" value="${isSupplyAssistant}">
    <input type="hidden" id="checkStatus" value="${skuGenerate.checkStatus}">
    <input type="hidden" id="currentUserName" value="${sessionScope.curr_user.username}">
    <c:choose>
        <c:when test="${type == 1}">
            <div class="detail-title">查看SKU：${skuGenerate.showName}
            </div>
            <div class="tab-nav">
                <c:if test="${skuGenerate.skuId == null}">
                    <a class="tab-item" href="${pageContext.request.contextPath}/goods/vgoods/viewSpu.do?spuId=${skuGenerate.spuId}&&pageType=0">基本信息</a>
                    <a class="tab-item" href="/vgoods/operate/viewOperate.do?spuId=${skuGenerate.spuId}">运营信息</a>
                </c:if>
                <c:if test="${skuGenerate.skuId != null}">
                    <a class="tab-item" href="${pageContext.request.contextPath}/goods/vgoods/viewSku.do?skuId=${skuGenerate.skuId}&spuId=${coreOperateInfoGenerateVo.upSpuId}&&pageType=0">基本信息</a>
                    <a class="tab-item" href="/vgoods/operate/viewOperate.do?skuId=${skuGenerate.skuId}">运营信息</a>
                    <a class="tab-item current"   href="/goods/vgoods/viewSku.do?skuId=${skuGenerate.skuId}&spuId=${skuGenerate.spuId}&&pageType=1&type=1">商品信息整合</a>
                </c:if>
            </div>
            <div class="mytitle" style="height:40px;">
                <div class="detail-title" style="padding:10px 20px;">${skuGenerate.showName}</div>
                <div class="page-title" style="top: 122px">简略：</div>
                <div class="page-toggle J-page-toggle" style="top: 118px">
                    <div class="toggle-btn"></div>

                </div>
            </div>
        </c:when>
        <c:otherwise>
            <div class="mytitle" style="height:40px;">
                <div class="detail-title" style="padding:10px 20px;">${skuGenerate.showName}</div>
                <div class="page-title">简略：</div>
                <div class="page-toggle J-page-toggle">
                    <div class="toggle-btn"></div>

                </div>
            </div>
        </c:otherwise>
    </c:choose>


    <%--页头--%>
    <div class="detail-title">
        <div style="display: inline-block; margin-left: 20px;font-size: 13px;font-weight: 600;color: #666">
            <span>订货号：</span>
            ${skuGenerate.skuNo}
        </div>
        <div style="display: inline-block;margin-left: 50px;font-size: 13px;font-weight: 600;color: #666">
            <span>分类：</span>
            ${categoryFullPath}
        </div>
        <div style="display: inline-block;margin-left: 50px;font-size: 13px;font-weight: 600;color: #666">
            <span>产品归属：</span>
            <c:if test="${not empty coreSpuDto.productMgrName}">
                ${coreSpuDto.productMgrName}
            </c:if>
            <c:if test="${not empty coreSpuDto.productAssistantName and not empty coreSpuDto.productAssistantName}">
                &
            </c:if>
            <c:if test="${not empty coreSpuDto.productAssistantName}">
                ${coreSpuDto.productAssistantName}
            </c:if>
<%--            ${coreSpuDto.productMgrName}&${coreSpuDto.productAssistantName}--%>
        </div>
        <div style="display: inline-block;margin-left: 50px;font-size: 13px;font-weight: 600;color: #666">
            <i class="vd-tip-icon vd-icon icon-problem1" style="color: #2E8AE6;"></i>
            <span>商品等级：</span>
            <c:if test="${command.goodsLevelVo ne null}">
                ${command.goodsLevelVo.levelName}
            </c:if>
            <c:if test="${command.goodsLevelVo eq null}">
                无
            </c:if>
        </div>
        <div style="display: inline-block;margin-left: 50px;font-size: 13px;font-weight: 600;color: #666">
            <i class="vd-tip-icon vd-icon icon-problem1" style="color: #2E8AE6;"></i>
            <span>商品档位：</span>
            <c:if test="${command.goodsPositionVo ne null}">
                ${command.goodsPositionVo.positionName}
            </c:if>
            <c:if test="${command.goodsPositionVo eq null}">
                无档位
            </c:if>
        </div>
        <c:if test="${command ne null and command.skuId ne null}">
            <div id="goodsInfoContainer" style="display: inline-block;margin-left: 50px;font-size: 13px;font-weight: 600;color: #666">
                <span>商品资料库：</span>
                <c:if test="${docOfGoodsDo eq null or docOfGoodsDo.id eq null}">
                    <a href="javascript:void(0)" disabled style="color: gray">
                        未维护资料
                    </a>
                </c:if>
                <c:if test="${docOfGoodsDo ne null and docOfGoodsDo.id ne null}">
                    <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewdocgoods${docOfGoodsDo.id}",
                                "link":"${docUrl}/data/collection/goodsDetailForSales?id=${docOfGoodsDo.id}",
                                "title":"查看商品资料信息"}'>
                        点击查看
                    </a>
                </c:if>
            </div>
        </c:if>
    </div>

    <c:if test="${not empty unitList }">
        <c:forEach var="unit" items="${unitList}">
            <c:if test="${ skuGenerate.baseUnitId ==unit.unitId  }">
                <c:set var="goodsUnitName" value="${unit.unitName}"/>
            </c:if>
        </c:forEach>
    </c:if>

    <div class="detail-simple-wrap J-detail-simple">
        <div class="detail-simple-list">
            <div class="simple-item">
                <div class="simple-title">商品基础信息</div>
                <div class="simple-cnt">
                    <table class="table table-base">
                        <colgroup>
                            <col width="150px">
                            <col>
                        </colgroup>
                        <tr>
                            <td>SKU商品单位：</td>
                            <td>
                                ${goodsUnitName}
                            </td>
                        </tr>

                        <%--商品类型为"耗材"和"试剂'时显示内含最小商品数量--%>
                        <c:if test="${coreSpuDto.spuType == 317 or coreSpuDto.spuType == 318}">
                        <tr>
                            <td>内含最小商品数量：</td>
                            <td>
                                <c:forEach var="unit" items="${unitList}">
                                    <c:if test="${ skuGenerate.unitId ==unit.unitId  }"> ${ skuGenerate.changeNum}
                                        ${unit.unitName}
                                    </c:if>
                                </c:forEach>
                            </td>
                        </tr>
                        </c:if>

                        <tr>
                            <td>最小起订量：</td>
                            <td>
                                <c:forEach var="unit" items="${unitList}">
                                    <c:if test="${ skuGenerate.baseUnitId == unit.unitId  }">
                                        <fmt:formatNumber value="${ skuGenerate.minOrder}" pattern="0" />
                                        ${goodsUnitName}
                                    </c:if>
                                </c:forEach>
                            </td>
                        </tr>
                        <tr>
                            <td>属性：</td>
                            <td>
                                <c:forEach items="${baseAttributeVoList}" var="attr">
                                    <span>${attr.baseAttributeName}：</span>
                                    <c:forEach items="${attr.attrValue}" var="attrVal" varStatus="stat">
                                        <c:if test="${attrVal.selected}">
                                            ${attrVal.attrValue}${attrVal.unitName}
                                        </c:if>
                                    </c:forEach>
                                    <br>
                                </c:forEach>
                            </td>
                        </tr>

                        <c:if test="${coreSpuDto.spuType == 316}">
                        <tr>
                            <td>配置清单：</td>
                            <td>
                                <c:if test="${not empty configurationMap}">
                                    <c:forEach items="${configurationMap}" var="entry">
                                        <div>${entry.key}:${entry.value}</div>
                                    </c:forEach>
                                </c:if>
                                <c:if test="${empty configurationMap}">
                                    无
                                </c:if>
                            </td>
                        </tr>
                        </c:if>

                        <c:if test="${coreSpuDto.spuType == 317 or coreSpuDto.spuType == 318}">
                            <tr>
                                <td>主属性：</td>
                                <td class="J-spring-filter">
                                    <c:forEach items="${primaryAttributeVoList}" var="primaryAttribute">
                                        <c:if test="${primaryAttribute.isPrimary eq 1}"> ${primaryAttribute.baseAttributeName} 、 </c:if>
                                    </c:forEach>
                                </td>
                            </tr>
                        </c:if>
                        <%--商品类型为"器械类型"和"配件"时显示--%>
                        <c:if test="${coreSpuDto.spuType == 316 or coreSpuDto.spuType == 1008}">
                        <tr>
                            <td>制造商型号：</td>
                            <td>${skuGenerate.model}</td>
                        </tr>
                        </c:if>
                        <%--商品类型为"耗材"和"试剂"时显示--%>
                        <c:if test="${coreSpuDto.spuType == 317 or coreSpuDto.spuType == 318}">
                        <tr>
                            <td>规格：</td>
                            <td>${skuGenerate.spec}</td>
                        </tr>
                        </c:if>
                        <tr>
                            <td>Wiki链接：</td>
                            <td><a href="${skuGenerate.wikiHref}" target="_blank">${skuGenerate.wikiHref}</a></td>
                        </tr>
                    </table>
                </div>
            </div>

            <%--库存信息--%>
            <c:if test="${not empty stockInfo}">
                <div class="simple-item">
                    <div class="simple-title">库存信息</div>
                    <div class="simple-cnt">
                        <table class="table table-base">
                            <tr>
                                <td>可用库存：</td>
                                <td>
                                    <c:if test="${stockInfo.availableStockNum ne null}">
                                        ${stockInfo.availableStockNum}${goodsUnitName}
                                    </c:if>
                                </td>
                            </tr>
                            <tr>
                                <td>库存量：</td>
                                <td>
                                    <c:if test="${stockInfo.stockNum ne null}">
                                        ${stockInfo.stockNum}${goodsUnitName}
                                    </c:if>
                                </td>
                            </tr>
                            <tr>
                                <td>占用库存：</td>
                                <td>
                                    <c:if test="${stockInfo.stockNum ne null}">
                                        ${stockInfo.occupyNum}${goodsUnitName}
                                    </c:if>
                                </td>
                            </tr>
                            <tr>
                                <td>活动锁定库存：</td>
                                <td>
                                    <c:if test="${stockInfo.actionLockNum ne null}">
                                        ${stockInfo.actionLockNum}${goodsUnitName}
                                    </c:if>
                                </td>
                            </tr>
                            <tr>
                                <td>在途（订单/数量/预计）：</td>
                                <td>
                                    <c:forEach var="listw" items="${buyorderVos}" varStatus="n">
                                        ${listw.buyorderNo}&nbsp;/&nbsp;${listw.onWayNum}&nbsp;/&nbsp;
                                        <c:if test="${listw.estimateArrivalTime eq 0 }">--<br /></c:if>
                                        <c:if test="${listw.estimateArrivalTime != 0 }">
                                            <date:date value="${listw.estimateArrivalTime}" format="yyyy-MM-dd" />
                                            <br />
                                        </c:if>
                                    </c:forEach>
                                </td>
                            </tr>
                            <tr>
                                <td>采购到货时长（工作日）：</td>
                                <td>
                                        ${skuGenerate.perchaseTime}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </c:if>


            <div class="simple-item">
                <div class="simple-title">售后政策</div>
                <div class="simple-cnt">
                    <%--<div class="no-result-cnt">敬请期待</div>--%>
                    <table class="table table-base">
                        <tr>
                            <td>
                                产品是否可安装:
                                <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                    <i class="vd-icon icon-info2 ">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">指该供应商的这个SKU是否维护了售后政策
                                            </div>
                                            <span class="arrow arrow-out">
                                                    <span class="arrow arrow-in"></span>
                                                </span>
                                        </div>
                                    </i>
                                </div>
                            </td>
                            <td>
                                <c:if test="${skuGenerate.isInstallable == 1}">可安装</c:if>
                                <c:if test="${skuGenerate.isInstallable == 0}">不可安装</c:if>
                            </td>
                        </tr>

                        <c:if test="${skuGenerate.isInstallable == 1}">
                            <tr>
                                <td>
                                    是否提供上门安装服务:
                                    <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                        <i class="vd-icon icon-info2 ">
                                            <div class="tip arrow-left">
                                                <div class="tip-con">
                                                    1.收费安装：指贝登提供安装服务，但需要收取安装费（另：偏远区域需要另外收取长途费）
                                                    <br>
                                                    2.免费安装：指贝登提供安装服务，且不收取安装费（另：偏远区域需要另外收取长途费）
                                                    <br>
                                                    3.不提供安装：指贝登不提供安装服务
                                                </div>
                                                <span class="arrow arrow-out">
                                                    <span class="arrow arrow-in"></span>
                                                </span>
                                            </div>
                                        </i>
                                    </div>
                                </td>

                                <td>
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '0'}">收费安装</c:if>
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '1'}">免费安装</c:if>
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '2'}">不提供安装</c:if>
                                </td>
                            </tr>
                        </c:if>

                        <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '0'}">
                            <tr>
                                <td>安装费:</td>
                                <td>
                                    ${afterSaleServiceStandardInfoDto.installPolicyInstallFee}
                                </td>
                            </tr>
                        </c:if>

                        <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '0' || afterSaleServiceStandardInfoDto.installPolicyInstallType == '1'}">
                            <tr>
                                <td>安装区域:</td>
                                <td>
                                    <div class="title-click J-area-select" style='float:left;margin:-4px 0 0 0px;'>
                                        <a href="#"> 查看</a>
                                    </div>
                                    <input type="hidden" class="J-area-value" value='${afterSaleServiceStandardInfoDto.installArea.provinceCityJsonvalue}'>
                                </td>
                            </tr>
                        </c:if>

                        <tr>
                            <td>是否免费远程指导装机:</td>
                            <td>
                                <c:if test="${afterSaleServiceStandardInfoDto.installPolicyFreeRemoteInstall == '1'}">是</c:if>
                                <c:if test="${afterSaleServiceStandardInfoDto.installPolicyFreeRemoteInstall == '0'}">否</c:if>
                            </td>
                        </tr>
                        <tr>
                            <td>是否保修:</td>
                            <td>
                                <c:if test="${afterSaleServiceStandardInfoDto.guaranteePolicyIsGuarantee == '1'}">是</c:if>
                                <c:if test="${afterSaleServiceStandardInfoDto.guaranteePolicyIsGuarantee == '0'}">否</c:if>
                            </td>
                        </tr>
                        <tr>
                            <td>是否支持退货:</td>
                            <td>
                                <c:if test="${afterSaleServiceStandardInfoDto.returnPolicySupportReturn == '1'}">是</c:if>
                                <c:if test="${afterSaleServiceStandardInfoDto.returnPolicySupportReturn == '0'}">否</c:if>
                            </td>
                        </tr>
                        <c:if test="${afterSaleServiceStandardInfoDto.returnPolicySupportReturn == '1'}">
                            <tr>
                                <td>退货条件:</td>
                                <td>
                                    ${afterSaleServiceStandardInfoDto.returnPolicyCondition}
                                </td>
                            </tr>
                        </c:if>

                        <tr>
                            <td>可否提供备用机:</td>
                            <td>
                                <c:if test="${afterSaleServiceStandardInfoDto.overduePolicySupplyBackup == '1'}">是</c:if>
                                <c:if test="${afterSaleServiceStandardInfoDto.overduePolicySupplyBackup == '0'}">否</c:if>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="detail-simple-list">
            <div class="simple-item">
                <div class="simple-title">运营信息</div>
                <div class="simple-cnt">
                    <table class="table table-base">
                        <tr>
                            <td>已推送平台:</td>
                            <td>
                                ${pushedPlatformNames}
                            </td>
                        </tr>
                        <tr>
                            <td>上下架:</td>
                            <td>
                                ${onSaleStr}
                            </td>
                        </tr>
                        <tr>
                            <td>商品图片：</td>
                            <td>
                                <div class="info-pic">
                                <c:if test="${not empty goodsImages}">
                                    <c:forEach var="attachments" items="${goodsImages}">
                                        <div class="info-pic-item J-show-big" data-src="${api_http}${attachments.domain }${attachments.uri }">
                                            <img style="width:100%;height:100%" src="${api_http}${attachments.domain }${attachments.uri }" alt="">
                                        </div>
                                    </c:forEach>
                                </c:if>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <%--核价信息--%>

            <div class="simple-item">
                <div class="simple-title">核价信息</div>
                <div class="simple-cnt">
                    <table class="table table-base">
                        <tr>
                            <td>是否需报备：</td>
                            <td>
                                <div style="position: relative;">
                                    <c:choose>
                                        <c:when test="${skuGenerate.isNeedReport == 1}">
                                            是
                                            <div class="tip-wrap">
                                                <i class="vd-icon icon-info2 ">
                                                    <div class="tip arrow-left">
                                                        <div class="tip-con">
                                                            <c:if test="${skuGenerate.isNeedReport eq 1 && skuGenerate.isAuthorized eq 1}">
                                                                <div class="table-item item-col">
                                                                    <div class="table-td">
                                                                        是否获得授权：
                                                                        <c:if test="${skuGenerate.isAuthorized eq 1}">
                                                                            有授权
                                                                        </c:if>
                                                                        <c:if test="${skuGenerate.isAuthorized eq 0}">
                                                                            无授权
                                                                        </c:if>
                                                                    </div>
                                                                </div>
                                                                <div class="table-item item-col">
                                                                    <div class="table-td">
                                                                        授权范围：
                                                                        <c:forEach items="${skuAuthorizationInfo.skuAuthorizationItemVoList}" var="skuAuthorizationItem">
                                                                            <c:choose>
                                                                                <c:when test="${fn:length(skuAuthorizationItem.regionIds) == fn:length(regions)}">
                                                                                    “全国”
                                                                                </c:when>
                                                                                <c:otherwise>
                                                                                    <c:forEach items="${skuAuthorizationItem.regionIds}" var="regionId"  varStatus="regionStatus">
                                                                                        <c:if test="${regionStatus.first}">
                                                                                            "
                                                                                        </c:if>
                                                                                        <c:forEach items="${regions}" var="region">
                                                                                            <c:if test="${region.regionId eq regionId}">
                                                                                                ${region.regionName}
                                                                                            </c:if>
                                                                                        </c:forEach>
                                                                                        <c:if test="${!regionStatus.last}">
                                                                                            、
                                                                                        </c:if>
                                                                                        <c:if test="${regionStatus.last}">
                                                                                            "
                                                                                        </c:if>
                                                                                    </c:forEach>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                            的
                                                                            <c:choose>
                                                                                <c:when test="${fn:length(skuAuthorizationItem.terminalTypeIds) == fn:length(terminalTypes)}">
                                                                                    “全部终端”
                                                                                </c:when>
                                                                                <c:otherwise>
                                                                                    <c:forEach items="${skuAuthorizationItem.terminalTypeIds}" var="terminalTypeId" varStatus="terminalTypeStatus">
                                                                                        <c:if test="${terminalTypeStatus.first}">
                                                                                            "
                                                                                        </c:if>
                                                                                        <c:forEach items="${terminalTypes}" var="terminalType">
                                                                                            <c:if test="${terminalType.sysOptionDefinitionId eq terminalTypeId}">
                                                                                                ${terminalType.title}
                                                                                            </c:if>
                                                                                        </c:forEach>
                                                                                        <c:if test="${!terminalTypeStatus.last}">
                                                                                            、
                                                                                        </c:if>
                                                                                        <c:if test="${terminalTypeStatus.last}">
                                                                                            "
                                                                                        </c:if>
                                                                                    </c:forEach>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                            已获得授权
                                                                            </br>
                                                                        </c:forEach>
                                                                        <label style="color: red;font-weight: bold">注：除以上范围，需要报备。</label>
                                                                    </div>
                                                                </div>
                                                            </c:if>
                                                        </div>
                                                        <span class="arrow arrow-out">
                                                <span class="arrow arrow-in"></span>
                                            </span>
                                                    </div>
                                                </i>
                                            </div>
                                        </c:when>
                                        <c:otherwise>
                                            否
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>主销售部门：</td>
                            <td>
                                ${skuGenerate.mainDept}
                            </td>
                        </tr>
                        <shiro:hasAnyRoles name="供应主管,供应管理组助理,财务总监,财务专员">
                        <tr>
                            <td>采购成本：</td>
                            <td>
                                <c:if test="${skuPriceInfoDetailResponseDto!=null and not empty skuPriceInfoDetailResponseDto.purchaseList}">
                                    ${skuPriceInfoDetailResponseDto.purchaseList[0].purchasePrice}元/${goodsUnitName}&nbsp
                                    <c:if test="${lastestPriceChangeRecord != null
                                             and lastestPriceChangeRecord.purchaseCostsFluctuationDreaction ne 0}">
                                        <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                            <i class="vd-icon icon-info2 ">
                                                <div class="tip arrow-left">
                                                    <div class="tip-con">
                                                        <c:choose>
                                                            <c:when test="${lastestPriceChangeRecord.modPriceType eq 2}">
                                                                <span>原值：${lastestPriceChangeRecord.beforeModPurchaseCosts}&nbsp
                                                                <c:if test="${lastestPriceChangeRecord.purchaseCostsFluctuationDreaction eq 1}">
                                                                     上调
                                                                </c:if>
                                                                <c:if test="${lastestPriceChangeRecord.purchaseCostsFluctuationDreaction eq -1}">
                                                                     下调
                                                                </c:if>
                                                                        ${lastestPriceChangeRecord.purchaseCostsFluctuation}%
                                                                    </span>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <span> 新核价 </span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                    <span class="arrow arrow-out">
                                                        <span class="arrow arrow-in"></span>
                                                    </span>
                                                </div>
                                            </i>
                                        </div>
                                    </c:if>
                                </c:if>
                            </td>
                        </tr>
                        </shiro:hasAnyRoles>
                        <tr>
                            <td>市场价：</td>
                            <td>
                                <c:if test="${skuPriceInfoDetailResponseDto != null}">
                                    ${skuPriceInfoDetailResponseDto.marketPrice}元/${goodsUnitName}&nbsp
                                    <c:if test="${lastestPriceChangeRecord != null
                                             and lastestPriceChangeRecord.marketPriceFluctuationDreaction ne 0}">
                                        <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                            <i class="vd-icon icon-info2 ">
                                                <div class="tip arrow-left">
                                                    <div class="tip-con">
                                                        <c:choose>
                                                            <c:when test="${lastestPriceChangeRecord.modPriceType eq 2}">
                                                                <span>原值：${lastestPriceChangeRecord.beforeModMarketPrice}&nbsp
                                                                <c:if test="${lastestPriceChangeRecord.marketPriceFluctuationDreaction eq 1}">
                                                                    上调
                                                                </c:if>
                                                                <c:if test="${lastestPriceChangeRecord.marketPriceFluctuationDreaction eq -1}">
                                                                    下调
                                                                </c:if>
                                                                        ${lastestPriceChangeRecord.marketPriceFluctuation}%
                                                                    </span>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <span> 新核价 </span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                    <span class="arrow arrow-out">
                                                        <span class="arrow arrow-in"></span>
                                                    </span>
                                                </div>
                                            </i>
                                        </div>
                                    </c:if>
                                </c:if>
                            </td>
                        </tr>
                        <tr>
                            <td>终端价：</td>
                            <td>
                                <c:if test="${skuPriceInfoDetailResponseDto!=null}">
                                    ${skuPriceInfoDetailResponseDto.terminalPrice}元/${goodsUnitName}&nbsp
                                    <c:if test="${lastestPriceChangeRecord != null
                                             and lastestPriceChangeRecord.terminalPriceFluctuationDreaction ne 0}">
                                        <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                            <i class="vd-icon icon-info2 ">
                                                <div class="tip arrow-left">
                                                    <div class="tip-con">
                                                        <c:choose>
                                                            <c:when test="${lastestPriceChangeRecord.modPriceType eq 2}">
                                                                <span>原值：${lastestPriceChangeRecord.beforeModTerminalPrice}&nbsp
                                                                <c:if test="${lastestPriceChangeRecord.terminalPriceFluctuationDreaction eq 1}">
                                                                    上调
                                                                </c:if>
                                                                <c:if test="${lastestPriceChangeRecord.terminalPriceFluctuationDreaction eq -1}">
                                                                    下调
                                                                </c:if>
                                                                        ${lastestPriceChangeRecord.terminalPriceFluctuation}%
                                                                    </span>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <span> 新核价 </span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                    <span class="arrow arrow-out">
                                                        <span class="arrow arrow-in"></span>
                                                    </span>
                                                </div>
                                            </i>
                                        </div>
                                    </c:if>
                                </c:if>
                            </td>
                        </tr>
                        <tr>
                            <td>经销价：</td>
                            <td>
                                <c:if test="${skuPriceInfoDetailResponseDto!=null}">
                                    ${skuPriceInfoDetailResponseDto.distributionPrice}元/${goodsUnitName} &nbsp
                                    <c:if test="${lastestPriceChangeRecord != null
                                             and lastestPriceChangeRecord.distributionPriceFluctuationDreaction ne 0}">
                                        <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                            <i class="vd-icon icon-info2 ">
                                                <div class="tip arrow-left">
                                                    <div class="tip-con">
                                                        <c:choose>
                                                            <c:when test="${lastestPriceChangeRecord.modPriceType eq 2}">
                                                                <span>原值：${lastestPriceChangeRecord.beforeModDistributionPrice}&nbsp
                                                                <c:if test="${lastestPriceChangeRecord.distributionPriceFluctuationDreaction eq 1}">
                                                                    上调
                                                                </c:if>
                                                                <c:if test="${lastestPriceChangeRecord.distributionPriceFluctuationDreaction eq -1}">
                                                                    下调
                                                                </c:if>
                                                                        ${lastestPriceChangeRecord.distributionPriceFluctuation}%
                                                                    </span>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <span> 新核价 </span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                    <span class="arrow arrow-out">
                                                        <span class="arrow arrow-in"></span>
                                                    </span>
                                                </div>
                                            </i>
                                        </div>
                                    </c:if>
                                </c:if>
                                <c:if test="${skuPriceInfoDetailResponseDto.saleContainsFee!=null && skuPriceInfoDetailResponseDto.saleContainsFee == 2}">
                                	<span style="color:red">（不含运费）</span>
                                </c:if>
                            </td>
                        </tr>
                        <tr>
                            <td>电商价：</td>
                            <td>
                                <c:if test="${skuPriceInfoDetailResponseDto!=null}">
                                    ${skuPriceInfoDetailResponseDto.electronicCommercePrice}元/${goodsUnitName} &nbsp
                                    <c:if test="${lastestPriceChangeRecord != null
                                             and lastestPriceChangeRecord.electronicCommercePriceFluctuationDreaction ne 0}">
                                        <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                            <i class="vd-icon icon-info2 ">
                                                <div class="tip arrow-left">
                                                    <div class="tip-con">
                                                        <c:choose>
                                                            <c:when test="${lastestPriceChangeRecord.modPriceType eq 2}">
                                                                <span>原值：${lastestPriceChangeRecord.beforeModElectronicCommercePrice}&nbsp
                                                                <c:if test="${lastestPriceChangeRecord.electronicCommercePriceFluctuationDreaction eq 1}">
                                                                    上调
                                                                </c:if>
                                                                <c:if test="${lastestPriceChangeRecord.electronicCommercePriceFluctuationDreaction eq -1}">
                                                                    下调
                                                                </c:if>
                                                                        ${lastestPriceChangeRecord.electronicCommercePriceFluctuation}%
                                                                    </span>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <span> 新核价 </span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                    <span class="arrow arrow-out">
                                                        <span class="arrow arrow-in"></span>
                                                    </span>
                                                </div>
                                            </i>
                                        </div>
                                    </c:if>
                                </c:if>
                            </td>
                        </tr>
                        <tr>
                            <td>安装费：</td>
                            <td>
                                暂未开放
                            </td>
                        </tr>
                    </table>
                </div>
            </div>


            <%--包装信息--%>
            <div class="simple-item">
                <div class="simple-title">包装信息</div>
                <div class="simple-cnt">
                    <table class="table table-base">
                        <tr>
                            <td>是否启用多级包装：</td>
                            <td>
                                <c:choose>
                                    <c:when test="${goodsPackageInfo.isEnableMultistagePackage == 1}">
                                        是
                                    </c:when>
                                    <c:otherwise>
                                        否
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <td>中包装数量：</td>
                            <td>
                                ${goodsPackageInfo.midPackageNum}
                            </td>
                        </tr>
                        <tr>
                            <td>箱包装数量：</td>
                            <td>
                               ${goodsPackageInfo.boxPackageNum}
                            </td>
                        </tr>
                        <tr>
                            <td>使用年限：</td>
                            <td>
                                ${skuGenerate.serviceLife} &nbsp;年
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
             <%--售后服务等级--%>
            <div class="simple-item">
                <div class="simple-title">售后服务等级   <c:choose>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '5'}">
                                    ★★★★★
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '4'}">
                                    ★★★★☆
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '3'}">
                                    ★★★☆☆
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '2'}">
                                    ★★☆☆☆
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '1'}">
                                    ★☆☆☆☆
                                </c:when>
                                  <c:when test="${skuGenerate.afterSalesServiceLevel == '6'}">
                                    无需评级
                                </c:when>
                                <c:otherwise>
                                    待评级
                                </c:otherwise>
                            </c:choose></div>
                <c:if test="${skuGenerate.afterSalesServiceLevel != '6'}">
                <div class="simple-cnt" >
                      <table class="table table-base  whitetable">
                            <tbody>
 <tr>    <td  style="padding:0"  > </td>
                                    <td style="padding:0"  > </td>
                                     <td   style="padding:0"> </td>
                                      <td  style="padding:0" > </td>
                                       <td  style="padding:0" > </td>
                                        <td style="padding:0"  > </td>

                                  </tr>
                                  <tr>
                                    <td rowspan="2">时效性</td>
                                    <td >响应</td>
                                    <td>技术指导</td>
                                    <td>维修</td>
                                    <td></td>
                                    <td> </td>
                                  </tr>
                                    <tr>
                                    <td  >

                                           <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null}">
                                                ${afterSaleServiceLevelDto.installPolicyResponseTimeShow}
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td><c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null}">
                                                ${afterSaleServiceLevelDto.technicalDirectorEffectTimeShow }
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>

                                            </td>
                                    <td><c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null}">
                                                ${afterSaleServiceLevelDto.guaranteePolicyRepaireTimeShow}
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>

                                            </td>
                                    <td></td>
                                    <td> </td>
                                  </tr>
                                    <tr>
                                    <td rowspan="2">安调资料</td>
                                    <td  >说明书</td>
                                    <td  >操作流程</td>
                                    <td  >安调视频/安装指南</td>
                                    <td  >产品PPT</td>
                                    <td  >装箱清单</td>
                                  </tr>
                                    <tr>

                                    <td  >
                                     <c:choose>
                                            <c:when test="${docInstructionManual}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td  > <c:choose>
                                            <c:when test="${docOperationFlowCard}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                    <td  ><c:choose>
                                            <c:when test="${docSecurityAdjustmentVideo || docGuide  }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                    <td  ><c:choose>
                                            <c:when test="${docProductPPT }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                    <td  ><c:choose>
                                            <c:when test="${docPackingList }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                  </tr>

                                   <tr>
                                    <td rowspan="2">
                                  技术支持
                                     </td>
                                    <td  >技术支持联系方式</td>
                                       <td   > </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                    <td   ><c:choose>
                                            <c:when test="${docTechnicalContact or docAfterSalesManager }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                     <td   > </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>

                                  </tr>
                                   <tr>
                                    <td rowspan="2">维修资料</td>
                                    <td  >维修手册</td>
                                       <td   > 维修案例/FAQ</td>
                                      <td   >维修视频 </td>
                                       <td   >维修配附件 </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                     <td   >
<c:choose>
                                            <c:when test="${ docManual }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                     </td>
                                    <td   ><c:choose>
                                            <c:when test="${docCaseStudy}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                     </td>
                                      <td   ><c:choose>
                                            <c:when test="${docRepairVideo}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                      </td>
                                       <td   >
                                        <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.repairPartsAccessory == 1}">
                                                √
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.repairPartsAccessory == 2}">
                                               X
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                   </td>
                                        <td   > </td>

                                  </tr>

                                        <tr>
                                    <td rowspan="2">培训支持</td>
                                    <td  >安调培训</td>
                                       <td   > 维修培训</td>
                                      <td   >  </td>
                                       <td   > </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                    <td   >
                                    <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.installTraining == 1}">
                                                √
                                            </c:when>
                                                <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.installTraining == 2}">
                                               X
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>

                                    </td>
                                     <td   >
                                      <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.repairTraining == 1}">
                                                √
                                            </c:when>
                                                <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.repairTraining == 2}">
                                               X
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>

                                     </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>

                                  </tr>

                                       <tr>
                                    <td rowspan="2">服务人员等级</td>
                                    <td  >服务人员等级</td>
                                       <td   > </td>
                                      <td   >  </td>
                                       <td   > </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                    <td   >
                                  <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.servicePersonnelLevel == '5'}">
                                                D级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.servicePersonnelLevel == '4'}">
                                                C级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.servicePersonnelLevel == '3'}">
                                                B级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.servicePersonnelLevel == '2'}">
                                                A级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.servicePersonnelLevel == '1'}">
                                                S级
                                            </c:when>
                                            <c:otherwise>
                                               -
                                            </c:otherwise>
                                        </c:choose>
                                        </td>
                                     <td   > </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>

                                  </tr>
                            </tbody>
                        </table>
                </div>
                </c:if>
            </div>
        </div>
        <div class="detail-simple-list">
            <%--首营信息--%>
            <c:if test="${not empty firstEngage}">
                <div class="simple-item">
                    <div class="simple-title">注册证/备案信息</div>
                    <div class="simple-cnt">
                        <table class="table table-base">
                            <tr>
                                <td>注册证号/备案凭证号：</td>
                                <td>
                                    <a id="registrationLink" href="javascript:void(0);" tabTitle='{"num":"firstengage${firstEngage.firstEngageId}","link":"/firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${firstEngage.firstEngageId}","title":"查看首营"}'>${firstEngage.registrationNumber}</a>
                                </td>
                            </tr>
                            <tr>
                                <td>管理类别：</td>
                                <td> ${firstEngage.manageCategoryLevelShow}</td>
                            </tr>
                            <tr>
                                <td>生产厂商：</td>
                                <td>${firstEngage.productCompanyChineseName}</td>
                            </tr>
                            <tr>
                                <td>证件有效期：</td>
                                <td> ${firstEngage.effectStartDate} 至
                                    <c:choose>
                                        <c:when test="${firstEngage.registration.manageCategoryLevel eq 968}">
                                            无限期
                                        </c:when>
                                        <c:otherwise>
                                            ${firstEngage.effectEndDate}
                                        </c:otherwise>
                                    </c:choose>
                                    <c:if test="${firstEngage.registration.manageCategoryLevel ne 968 and overdue}">
                                        <span style="color: red">（已过期）</span>
                                    </c:if>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    注册证附件/备案凭证附件：<br>
                                    <span>
                                        <div class="title-click nobor  pop-new-data" layerParams='{"width":"622px","height":"755px","title":"注册证/备案凭证问题反馈","link":"/registrationFeedbackController/view.do?registrationNumberId=${firstEngage.registrationNumberId}&registrationNumber=${firstEngage.registrationNumber}&levelName=${command.goodsLevelVo.levelName}&goodsLevelNo=${command.goodsLevelNo}&positionName=${command.goodsPositionVo.positionName}&goodsPositionNo=${command.goodsPositionNo}&productMgrName=${coreSpuDto.productMgrName}&assignmentManagerId=${coreSpuDto.assignmentManagerId}&productAssistantName=${coreSpuDto.productAssistantName}&assignmentAssistantId=${coreSpuDto.assignmentAssistantId}&fileType=1&firstEngageId=${firstEngage.firstEngageId}"}'>
                                           问题反馈
                                        </div>
                                    </span>
                                </td>
                                <td>
                                    <div class="info-pic">
                                        <c:if test="${not empty firstEngage.registration.zczAttachments }">
                                            <c:forEach var="attachments" items="${firstEngage.registration.zczAttachments }">
                                                <div class="info-pic-item J-show-big" data-src="${api_http}${attachments.domain }${attachments.uri }">
                                                    <img style="width:100%;height:100%" src="${api_http}${attachments.domain }${attachments.uri }" alt="">
                                                </div>
                                            </c:forEach>
                                        </c:if>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    注册证源文件/备案凭证源文件：<br>
                                    <span>
                                        <div class="title-click nobor  pop-new-data" layerParams='{"width":"622px","height":"755px","title":"注册证/备案凭证问题反馈","link":"/registrationFeedbackController/view.do?registrationNumberId=${firstEngage.registrationNumberId}&registrationNumber=${firstEngage.registrationNumber}&levelName=${command.goodsLevelVo.levelName}&goodsLevelNo=${command.goodsLevelNo}&positionName=${command.goodsPositionVo.positionName}&goodsPositionNo=${command.goodsPositionNo}&productMgrName=${coreSpuDto.productMgrName}&assignmentManagerId=${coreSpuDto.assignmentManagerId}&productAssistantName=${coreSpuDto.productAssistantName}&assignmentAssistantId=${coreSpuDto.assignmentAssistantId}&fileType=2&firstEngageId=${firstEngage.firstEngageId}"}'>
                                            问题反馈
                                        </div>
                                    </span>
                                </td>
                                <td>
                                    <div class="table-item">
                                        <div class="table-td">
                                            <div>
                                                <c:if test="${not empty firstEngage.registration.zczyAttachments }">
                                                    <c:forEach var="attachments" items="${firstEngage.registration.zczyAttachments }" begin="0" varStatus="i">
                                                        <div style="line-height: 30px">
                                                            <a target="_blank" href="${api_http}${attachments.domain }${attachments.uri}">注册证/备案凭证源文件 - ${i.index + 1}</a><br>
                                                        </div>
                                                    </c:forEach>
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>新国标分类：</td>
                                <td>${firstEngage.newStandardCategoryName}</td>
                            </tr>
                            <tr>
                                <td>旧国标分类：</td>
                                <td>${firstEngage.oldStandardCategoryName}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </c:if>
            <c:if test="${empty alreadyPriced or alreadyPriced != 1}">
            <div class="simple-item">
                <div class="simple-title">近一年有效成交数据</div>
                <div class="simple-cnt">
                    <table class="table table-base">
                        <tr>
                            <td>最近一年有效成交均价：</td>
                            <td>
<%--                                <c:if test="${not empty lastSaleGoodsOfTerminalTrader}">--%>
                                    <div>
                                        <c:if test="${not empty agencyPrice}">
                                            <div><span>分销：</span>${agencyPrice}元</div>
                                        </c:if>
                                        <c:if test="${not empty terminalPrice}">
                                            <div><span>终端：</span>${terminalPrice}元</div>
                                        </c:if>
                                    </div>
<%--                                </c:if>--%>
                            </td>
                        </tr>
                        <tr>
                            <td>最近订单（终端）：</td>
                            <td>
                                <c:if test="${not empty lastSaleGoodsOfTerminalTrader}">
                                    <div>
                                        <div>${lastSaleGoodsOfTerminalTrader.price}元</div>
                                        <div><span>单号：</span>${lastSaleGoodsOfTerminalTrader.saleorderNo}</div>
                                        <div><span>归属销售：</span>${lastSaleGoodsOfTerminalTrader.userName}</div>
                                    </div>
                                </c:if>
                            </td>
                        </tr>
                        <tr>
                                <td>最近订单（分销）：</td>
                            <td>
                                <c:if test="${not empty lastSaleGoodsOfAgencyTrader}">
                                    <div>
                                        <div>${lastSaleGoodsOfAgencyTrader.price}元</div>
                                        <div><span>单号：</span>${lastSaleGoodsOfAgencyTrader.saleorderNo}</div>
                                        <div><span>归属销售：</span>${lastSaleGoodsOfAgencyTrader.userName}</div>
                                    </div>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            </c:if>
        </div>
    </div>

    <div class="detail-more-wrap J-detail-more" style="display: none;">

        <div class="side-wrap J-side-wrap">
            <div class="side-item selected">商品基础信息</div>
            <div class="side-item">运营信息</div>
            <c:if test="${not empty firstEngage}">
                <div class="side-item">注册证/备案信息</div>
            </c:if>
            <div class="side-item">库存信息</div>
            <div class="side-item">核价信息</div>
            <div class="side-item">售后服务等级</div>
            <c:if test="${empty alreadyPriced or alreadyPriced != 1}">
            <div class="side-item">近一年有效成交数据</div>
            </c:if>
            <div class="side-item">售后政策</div>
        </div>

        <div class="detail-block">
            <div class="block-title mytitle">商品基础信息</div>
            <div class="goodDetails J-toggle-show-cnt">
                <div class="block-title">基本信息</div>
                <div class="detail-table">
                    <c:if test="${not empty coreSpuDto.spuName}">
                        <div class="table-item">
                            <div class="table-th">产品名称（注册证/备案凭证）：</div>
                            <div class="table-td">
                                    ${coreSpuDto.spuName}
                            </div>
                        </div>
                    </c:if>
                    <c:if test="${not empty coreSpuDto.specsModel}">
                        <div class="table-item">
                            <div class="table-th">规格、型号（注册证/备案凭证）：</div>
                            <div class="table-td">
                                    ${coreSpuDto.specsModel}
                            </div>
                        </div>
                    </c:if>
                    <div class="table-item">
                        <div class="table-th">订货号：</div>
                        <div class="table-td">
                            ${skuGenerate.skuNo}
                        </div>
                    </div>
                    <%--商品类型为"器械类型"和"配件"时显示--%>
                    <c:if test="${coreSpuDto.spuType == 316 or coreSpuDto.spuType == 1008}">
                    <div class="table-item">
                        <div class="table-th">制造商型号：</div>
                        <div class="table-td">
                            ${skuGenerate.model}
                        </div>
                    </div>
                    </c:if>
                    <%--商品类型为"耗材"和"试剂"时显示--%>
                    <c:if test="${coreSpuDto.spuType == 317 or coreSpuDto.spuType == 318}">
                    <div class="table-item">
                        <div class="table-th">规格：</div>
                        <div class="table-td">
                            ${skuGenerate.spec}
                        </div>
                    </div>
                    </c:if>

                    <div class="table-item">
                        <div class="table-th">商品名称：</div>
                        <div class="table-td">
                            ${skuGenerate.skuName}
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">物料编码：</div>
                        <div class="table-td">
                            ${skuGenerate.materialCode}
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">供应商型号：</div>
                        <div class="table-td">
                            ${skuGenerate.supplyModel}
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">是否备货：</div>
                        <div class="table-td">
                            <c:if test="${skuGenerate.isStockup==1}">
                                是
                            </c:if>
                            <c:if test="${skuGenerate.isStockup!=1}">
                                否
                            </c:if>
                        </div>
                    </div>
                    <c:if test="${coreSpuDto.spuType==316}">
                        <div class="table-item">
                            <div class="table-th">定期维护：</div>
                            <div class="table-td">
                                <c:choose>
                                    <c:when test="${skuGenerate.regularMaintainType==0}">
                                        不维护
                                    </c:when>
                                    <c:when test="${skuGenerate.regularMaintainType==1}">
                                        一般维护
                                    </c:when>
                                    <c:when test="${skuGenerate.regularMaintainType==2}">
                                        定期维护
                                    </c:when>
                                    <c:otherwise>
                                        -
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </div>
                        <c:if test="${skuGenerate.regularMaintainType eq 1 or skuGenerate.regularMaintainType eq 2}">
                            <div class="table-item">
                                <div class="table-th">维护原因：</div>
                                <div class="table-td">
                                    ${skuGenerate.regularMaintainReason}
                                </div>
                            </div>
                        </c:if>
                    </c:if>
                    <div class="table-item">
                        <div class="table-th">税收分类编码：
                            <c:if test="${skuGenerate.taxCategoryNoRecord ne null and skuGenerate.taxCategoryNoRecord ne ''}">
                            <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                <i class="vd-icon icon-info2" style="background: none;">
                                    <span style="font-size: smaller;">(记录)</span>
                                    <div class="tip arrow-left" style="margin-top: 30px">
                                        <div class="tip-con">
                                            ${skuGenerate.taxCategoryNoRecord}
                                        </div>
                                        <span class="arrow arrow-out">
                                                <span class="arrow arrow-in"></span>
                                            </span>
                                    </div>
                                </i>
                            </div>
                            </c:if>
                        </div>
                        <div class="table-td">
                            <c:if test="${skuGenerate.taxCategoryNo ne null and skuGenerate.taxCategoryNo ne ''}">
                                <div class="tip-wrap"  style="position: relative;vertical-align: -2px;">
                                    <i class="vd-icon icon-info2" style="background: none;">
                                        <span style="font-size: smaller;">(信息)</span>
                                        <div class="tip arrow-left" style="width: 350px; height: auto; margin-top: 30px">
                                            汇总项名称：${skuGenerate.taxcodeClassificationDto.goodsServicesNameAbbreviation}<br>
                                            汇总项简称：${skuGenerate.taxcodeClassificationDto.goodsServicesClassificationAbbreviation}<br>
                                            货物和劳务名称：${skuGenerate.taxcodeClassificationDto.goodsServicesName}<br>
                                            商品和服务分类简称：${skuGenerate.taxcodeClassificationDto.classificationAbbreviation}<br>
                                            关键字：${skuGenerate.taxcodeClassificationDto.keyword}<br>
                                            说明：${skuGenerate.taxcodeClassificationDto.description}<br>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                            ${skuGenerate.taxCategoryNo}
                        </div>
                    </div>
<%--                    <div class="table-item">--%>
<%--                        <div class="table-th">税收分类编码简称：</div>--%>
<%--                        <div class="table-td">--%>
<%--                            <div class="info-pic">--%>
<%--                                ${skuGenerate.taxCodeSimpleName}--%>
<%--                            </div>--%>
<%--                        </div>--%>
<%--                    </div>--%>
                    <div class="table-item">
                        <div class="table-th">科室：</div>
                        <div class="table-td">
                            ${departmentNameStr}
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">检查项目：</div>
                        <div class="table-td">
                            ${inspectionItemVoStr}
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">机构等级：</div>
                        <div class="table-td">
                            ${skuGenerate.institutionLevel}
                        </div>
                    </div>

                </div>

                <c:if test="${not empty baseAttributeVoList}">
                <div class="detail-block">
                    <div class="block-title">商品属性</div>
                    <div class="detail-table">

                        <div class="table-item">
                            <div class="table-th">选择带入属性：</div>
                            <div class="table-td J-spring-filter">
                                <c:forEach items="${baseAttributeVoList}" var="attr">
                                    ${attr.baseAttributeName}、
                                </c:forEach>
                            </div>
                        </div>
                        <c:if test="${coreSpuDto.spuType == 317 or coreSpuDto.spuType == 318}">
                            <div class="table-item">
                                <div class="table-th">主属性：</div>
                                <div class="table-td J-spring-filter">
                                    <c:forEach items="${primaryAttributeVoList}" var="primaryAttribute">
                                        <c:if test="${primaryAttribute.isPrimary eq 1}"> ${primaryAttribute.baseAttributeName} 、 </c:if>
                                    </c:forEach>
                                </div>
                            </div>
                        </c:if>
                        <c:forEach items="${baseAttributeVoList}" var="attr">
                            <div class="table-item">
                                <div class="table-th">${attr.baseAttributeName}：</div>
                                <div class="table-td">
                                    <c:forEach items="${attr.attrValue}" var="attrVal">
                                        <c:if test="${attrVal.selected}">
                                            ${attrVal.attrValue}${attrVal.unitName}
                                        </c:if>
                                    </c:forEach>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </div>
                </c:if>



                <%--<c:if test="${not empty baseAttributeVoList}">--%>
                <c:if test="${not empty configurationMap}">
                    <div class="detail-block">
                        <div class="block-title">配置清单</div>
                        <div class="detail-table">
                            <c:forEach items="${configurationMap}" var="entry">
                                <div class="table-item ">
                                    <div class="table-th">${entry.key}：</div>
                                    <div class="table-td">${entry.value} </div>
                                </div>
                            </c:forEach>
                        </div>
                    </div>
                </c:if>
                <%--</c:if>--%>




                <c:if test="${not empty command.paramsName1}">
                <div class="detail-block">
                    <div class="block-title">参数信息</div>
                    <div class="detail-table">
                        <div class="table-item item-col">
                            <div class="table-th">技术参数：</div>
                            <div class="table-td">
                                <c:forEach items="${command.paramsName1}" var="params" varStatus="status">
                                    ${command.paramsName1[status.index]} : ${command.paramsValue1[status.index]} <br>
                                </c:forEach>
                            </div>
                        </div>
                    </div>
                </div>
                </c:if>

                <div class="detail-block">
                    <div class="block-title">物流和包装</div>
                    <div class="detail-table">
                        <div class="table-item">
                            <div class="table-th">SKU商品单位：</div>
                            <div class="table-td">
                                <c:if test="${not empty unitList }">
                                    <c:forEach var="unit" items="${unitList}">
                                        <c:if test="${ skuGenerate.baseUnitId ==unit.unitId  }"> ${unit.unitName}</c:if>
                                    </c:forEach>
                                </c:if>
                            </div>
                        </div>

                        <div class="table-item">
                            <div class="table-th">发货方式：</div>
                            <div class="table-td">
                                <c:if test="${skuGenerate.isDirect==0}">
                                    普发
                                </c:if>
                                <c:if test="${skuGenerate.isDirect==1}">
                                    直发
                                </c:if>
                            </div>
                        </div>

                        <div class="table-item">
                            <div class="table-th">是否启用多级包装：</div>
                            <div class="table-td">
                                <c:if test="${goodsPackageInfo.isEnableMultistagePackage == 1}">
                                    是
                                </c:if>
                                <c:if test="${goodsPackageInfo.isEnableMultistagePackage == 0}">
                                    否
                                </c:if>
                            </div>
                        </div>
                        <div class="table-item">
                            <div class="table-th">中包装数量：</div>
                            <div class="table-td">
                                ${goodsPackageInfo.midPackageNum}
                            </div>
                        </div>
                        <div class="table-item">
                            <div class="table-th">箱包装数量：</div>
                            <div class="table-td">
                                ${goodsPackageInfo.boxPackageNum}
                            </div>
                        </div>

                        <%--VDERP-2212 ERP新商品流-新增/编辑SKU（器械设备、配件），增加最小起订量字段--%>
                        <c:if test="${coreSpuDto.spuType == 316 || coreSpuDto.spuType == 1008}">
                            <div class="table-item">
                                <div class="table-th">最小起订量：</div>
                                <div class="table-td">
                                    <c:if test="${not empty unitList }">
                                        <c:forEach var="unit" items="${unitList}">
                                            <c:if test="${ skuGenerate.baseUnitId == unit.unitId  }">
                                                <fmt:formatNumber value="${ skuGenerate.minOrder}" pattern="0" />
                                                ${unit.unitName}
                                            </c:if>
                                        </c:forEach>
                                    </c:if>
                                </div>
                            </div>
                        </c:if>


                        <div class="table-item">
                            <div class="table-th">商品最小单位：</div>
                            <div class="table-td">
                                <c:if test="${not empty unitList }">
                                    <c:forEach var="unit" items="${unitList}">
                                        <c:if test="${ skuGenerate.unitId ==unit.unitId  }"> ${unit.unitName}</c:if>
                                    </c:forEach>
                                </c:if>
                            </div>
                        </div>
                        <%--商品类型为"耗材"和"试剂'时显示内含最小商品数量--%>
                        <c:if test="${coreSpuDto.spuType == 317 or coreSpuDto.spuType == 318}">
                        <div class="table-item">
                            <div class="table-th">内含最小商品数量：</div>
                            <div class="table-td">
                                <c:if test="${not empty unitList }">
                                    <c:forEach var="unit" items="${unitList}">
                                        <c:if test="${ skuGenerate.unitId ==unit.unitId  }"> ${ skuGenerate.changeNum}
                                            ${unit.unitName}
                                        </c:if>
                                    </c:forEach>
                                </c:if>
                            </div>
                        </div>
                        </c:if>
                        <div class="table-item">
                            <div class="table-th">最小起订量：</div>
                            <div class="table-td">

                                <c:if test="${not empty unitList }">
                                    <c:forEach var="unit" items="${unitList}">
                                        <c:if test="${ skuGenerate.baseUnitId ==unit.unitId  }">
                                            <fmt:formatNumber value="${ skuGenerate.minOrder}" pattern="0" />
                                            ${unit.unitName}
                                        </c:if>
                                    </c:forEach>
                                </c:if>
                            </div>
                        </div>


                        <div class="table-item">
                            <div class="table-th">包装体积：</div>
                            <div class="table-td">
                                长度 ${skuGenerate.packageLength}cm 宽度 ${skuGenerate.packageWidth}cm 高度
                                ${skuGenerate.packageHeight}cm
                            </div>
                        </div>

                        <div class="table-item">
                            <div class="table-th">商品体积：</div>
                            <div class="table-td">
                                长度 ${skuGenerate.goodsLength}cm 宽度 ${skuGenerate.goodsWidth}cm 高度 ${skuGenerate.goodsHeight}cm

                            </div>
                        </div>

                        <div class="table-item">
                            <div class="table-th">毛重：</div>
                            <div class="table-td">
                                ${skuGenerate.grossWeight}kg
                            </div>
                        </div>

                        <div class="table-item">
                            <div class="table-th">净重：</div>
                            <div class="table-td">
                                ${skuGenerate.netWeight}kg
                            </div>
                        </div>


                        <div class="table-item">
                            <div class="table-th">包装清单：</div>
                            <div class="table-td">
                                ${skuGenerate.packingList}
                            </div>
                        </div>


                    </div>

                </div>

            <div class="detail-block">
                <div class="block-title">存储与效期</div>
                <div class="detail-table">
                    <div class="table-item item-col">
                        <div class="table-th">存储条件（温度）：</div>
                        <div class="table-td">
                            <c:if test="${ skuGenerate.storageConditionOne ==1 }"> 常温0-30℃ </c:if>
                            <c:if test="${ skuGenerate.storageConditionOne ==2 }"> 阴凉0-20℃ </c:if>
                            <c:if test="${ skuGenerate.storageConditionOne ==3 }"> 冷藏2-10℃   </c:if>
                            <c:if test="${ skuGenerate.storageConditionOne ==4 }">
                                <c:if test="${not empty skuGenerate.storageConditionOneLowerValue and not empty skuGenerate.storageConditionOneUpperValue}">
                                    <c:choose>
                                        <c:when test="${skuGenerate.storageConditionOneLowerValue == skuGenerate.storageConditionOneUpperValue}">
                                            其他温度<fmt:formatNumber value="${skuGenerate.storageConditionOneUpperValue}" type="number"/>℃
                                        </c:when>
                                        <c:otherwise>
                                            其他温度<fmt:formatNumber value="${skuGenerate.storageConditionOneLowerValue}" type="number"/>℃
                                            -
                                            <fmt:formatNumber value="${skuGenerate.storageConditionOneUpperValue}" type="number"/>℃
                                        </c:otherwise>
                                    </c:choose>
                                </c:if>
                            </c:if>
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">存储条件(湿度）：</div>
                        <div class="table-td">
                        <c:if test="${not empty skuGenerate.storageConditionHumidityLowerValue and not empty skuGenerate.storageConditionHumidityUpperValue}">
                            <fmt:formatNumber value="${skuGenerate.storageConditionHumidityLowerValue}" type="number"/>%
                            -
                            <fmt:formatNumber value="${skuGenerate.storageConditionHumidityUpperValue}" type="number"/>%
                        </c:if>
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">存储条件（其他）：</div>
                        <div class="table-td">
                            <c:forEach items="${skuGenerate.storageConditionTwo}" var="item">
                                <c:if test="${fn:contains(item, '1') }"> 通风 </c:if>
                                <c:if test="${fn:contains(item, '2')}"> 干燥 </c:if>
                                <c:if test="${fn:contains(item, '3') }"> 避光 </c:if>
                                <c:if test="${fn:contains(item, '4') }"> 防潮 </c:if>
                                <c:if test="${fn:contains(item, '5') }"> 避热 </c:if>
                                <c:if test="${fn:contains(item, '6')}"> 密封 </c:if>
                                <c:if test="${fn:contains(item, '7') }"> 密闭 </c:if>
                                <c:if test="${fn:contains(item, '8') }"> 严封 </c:if>
                                <c:if test="${fn:contains(item, '9') }"> 遮光 </c:if>
                            </c:forEach>
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">使用年限：</div>
                        <div class="table-td">
                            ${skuGenerate.serviceLife} &nbsp;年
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">有效期：</div>
                        <div class="table-td">
                            <c:if test="${ skuGenerate.effectiveDayUnit==1 and skuGenerate.effectiveDays > 0}"> ${skuGenerate.effectiveDays}天 </c:if>
                            <c:if test="${ skuGenerate.effectiveDayUnit==2 and skuGenerate.effectiveDays > 0}"> ${skuGenerate.effectiveDay}月 </c:if>
                            <c:if test="${ skuGenerate.effectiveDayUnit==3 and skuGenerate.effectiveDays > 0}"> ${skuGenerate.effectiveDays}年 </c:if>
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">近效期预警天数：</div>
                        <div class="table-td">
                            ${skuGenerate.nearTermWarnDays}
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">超近效期预警天数：</div>
                        <div class="table-td">
                            ${skuGenerate.overNearTermWarnDays}
                        </div>
                    </div>
                    <c:if test="${coreSpuDto.spuType == 316}">
                        <div class="table-item">
                            <div class="table-th">养护类型（质管专用）：</div>
                            <div class="table-td">
                                    ${not empty command.curingTypeDesc?command.curingTypeDesc:'不养护'}
                            </div>
                        </div>
                    </c:if>
                    </div>
                </div>

            <div class="detail-block">
                <div class="block-title">产品资料 </div>
                <div class="detail-table">
                    <div class="table-item item-col">
                        <div class="table-th">wiki链接：</div>
                        <div class="table-td">
                            <a href="${skuGenerate.wikiHref}" target="_blank">${skuGenerate.wikiHref}</a>
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">检测报告：</div>
                        <div class="table-td">
                            <div class="info-pic">
                                <c:forEach items="${command.skuCheck}" var="item">
                                    <c:if test="${not item.pdfFlag}">
                                        <div style="width: 50px;height:55px;margin-right: 20px;">
                                            <div class="info-pic-item J-show-big" data-src="${item.ossUrl}">
                                                <img src="${item.ossUrl}">
                                            </div>
                                            <div style="width: 50px;white-space:nowrap;overflow:scroll; text-overflow:string;">
                                                <span>${not empty item.fileName?item.fileName:'检查报告'}</span>
                                            </div>
                                        </div>
                                    </c:if>
                                </c:forEach>
                                <c:forEach items="${command.skuCheck}" var="item">
                                    <c:if test="${item.pdfFlag}">
                                        <div style="width: 50px;height:80px;margin-right: 20px; ">
                                            <div class="info-pic-item J-show-file"  data-src="${item.ossUrl}">
                                                <img src="${pageContext.request.contextPath}/static/images/pdf_icon.png">
                                            </div>
                                            <div style="width: 50px;white-space:nowrap;overflow:scroll; text-overflow:string;">
                                                <span>${not empty item.fileName?item.fileName:'检查报告'}</span>
                                            </div>
                                        </div>
                                    </c:if>
                                </c:forEach>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                <div class="detail-block">
                    <div class="block-title">商品说明</div>
                    <div class="detail-table">
                        <div class="table-item item-col">
                            <div class="table-th">商品备注：</div>
                            <div class="table-td">
                                ${skuGenerate.goodsComments}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="detail-block">
            <div class="block-title  mytitle">
                运营信息
            </div>
                <div class="detail-table">
                    <div class="table-item item-col">
                        <div class="table-th">已推送平台：</div>
                        <div class="table-td">
                            ${pushedPlatformNames}
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">上下架：</div>
                        <div class="table-td">
                            ${onSaleStr}
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">商品图片：</div>
                        <div class="table-td" style="width: 150px;">
                            <div class="info-pic">
                            <c:if test="${not empty goodsImages}">
                                <c:forEach var="attachments" items="${goodsImages}">
                                    <div class="info-pic-item J-show-big" data-src="${api_http}${attachments.domain }${attachments.uri }">
                                    <img style="width:100%;height:100%" src="${api_http}${attachments.domain }${attachments.uri }" alt="">
                                    </div>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>

                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">商品详情：</div>
                        <div class="table-td" style="padding:1px 2px;">
                            <div    style="width: 80%; height: 400px;overflow: scroll;">${operateInfoVo.oprateInfoHtml}</div>
                        </div>
                    </div>
                </div>
        </div>

        <%--首营信息详情--%>
        <c:if test="${not empty firstEngage}">
        <div class="detail-block">
            <div class="block-title  mytitle">
                注册证/备案信息
            </div>
            <div class="goodDetails J-toggle-show-cnt">
                <div class="detail-table">
                    <div class="table-item">
                        <div class="table-th">注册证号/备案凭证号：</div>
                        <div class="table-td">
                            <a id="registrationLink2" href="javascript:void(0);" tabTitle='{"num":"firstengage${firstEngage.firstEngageId}","link":"/firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${firstEngage.firstEngageId}","title":"查看首营"}'>${firstEngage.registrationNumber}</a>
                        </div>
                    </div>

                    <div class="table-item">
                        <div class="table-th">注册证附件/备案凭证附件：<br>
                            <span>
                                <div class="title-click nobor  pop-new-data"
                                     layerParams='{"width":"622px","height":"755px","title":"注册证/备案凭证问题反馈","link":"/registrationFeedbackController/view.do?registrationNumberId=${firstEngage.registrationNumberId}&registrationNumber=${firstEngage.registrationNumber}&levelName=${command.goodsLevelVo.levelName}&goodsLevelNo=${command.goodsLevelNo}&positionName=${command.goodsPositionVo.positionName}&goodsPositionNo=${command.goodsPositionNo}&productMgrName=${coreSpuDto.productMgrName}&assignmentManagerId=${coreSpuDto.assignmentManagerId}&productAssistantName=${coreSpuDto.productAssistantName}&assignmentAssistantId=${coreSpuDto.assignmentAssistantId}&fileType=1&firstEngageId=${firstEngage.firstEngageId}"}'>
                                   问题反馈
                                </div>
                            </span>
                        </div>
                        <div class="table-td">
                            <div class="info-pic">
                                <c:if test="${not empty firstEngage.registration.zczAttachments }">
                                    <c:forEach var="attachments" items="${firstEngage.registration.zczAttachments }">
                                        <div class="info-pic-item J-show-big" data-src="${api_http}${attachments.domain}${attachments.uri }">
                                            <img style="width:100%;height:100%" src="${api_http}${attachments.domain}${attachments.uri }" alt="">
                                        </div>
                                        <a class="printAtta" href="javascript:;">打印</a>
                                    </c:forEach>
                                </c:if>
                            </div>
                        </div>
                    </div>

                    <div class="table-item">
                        <div class="table-th">证件有效期：</div>
                        <div class="table-td">
                            ${firstEngage.effectStartDate} 至
                                <c:choose>
                                    <c:when test="${firstEngage.registration.manageCategoryLevel eq 968}">
                                        无限期
                                    </c:when>
                                    <c:otherwise>
                                        ${firstEngage.effectEndDate}
                                    </c:otherwise>
                                </c:choose>
                                <c:if test="${firstEngage.registration.manageCategoryLevel ne 968 and overdue}">
                                    <span style="color: red">（已过期）</span>
                                </c:if>
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">管理类别：</div>
                        <div class="table-td">
                            ${firstEngage.manageCategoryLevelShow}
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">生产厂商：</div>
                        <div class="table-td">
                            ${firstEngage.productCompanyChineseName}
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">旧国标分类：</div>
                        <div class="table-td">
                            ${firstEngage.oldStandardCategoryName}
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">新国标分类：</div>
                        <div class="table-td">
                            ${firstEngage.newStandardCategoryName}
                        </div>
                    </div>
                    <div class="table-item  ">
                        <div class="table-th">审核状态：</div>
                        <div class="table-td">
                            <c:if test="${firstEngage.status==2}">
                                <span class="title-status status-red">审核不通过</span>
                            </c:if>
                            <c:if test="${firstEngage.status==1}">
                                <span class="title-status status-yellow">审核中</span>
                            </c:if>
                            <c:if test="${firstEngage.status==3}">
                                <span class="title-status status-green">审核通过</span>
                            </c:if>
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">注册证源文件/备案凭证源文件：<br>
                            <span>
                                <div class="title-click nobor  pop-new-data"
                                     layerParams='{"width":"622px","height":"755px","title":"注册证/备案凭证问题反馈","link":"/registrationFeedbackController/view.do?registrationNumberId=${firstEngage.registrationNumberId}&registrationNumber=${firstEngage.registrationNumber}&levelName=${command.goodsLevelVo.levelName}&goodsLevelNo=${command.goodsLevelNo}&positionName=${command.goodsPositionVo.positionName}&goodsPositionNo=${command.goodsPositionNo}&productMgrName=${coreSpuDto.productMgrName}&assignmentManagerId=${coreSpuDto.assignmentManagerId}&productAssistantName=${coreSpuDto.productAssistantName}&assignmentAssistantId=${coreSpuDto.assignmentAssistantId}&fileType=2&firstEngageId=${firstEngage.firstEngageId}"}'>
                                   问题反馈
                                </div>
                            </span>
                        </div>
                            <div class="table-td">
                                <div>
                                    <c:if test="${not empty firstEngage.registration.zczyAttachments }">
                                        <c:forEach var="attachments" items="${firstEngage.registration.zczyAttachments }" begin="0" varStatus="i">
                                            <div style="line-height: 30px">
                                                <a target="_blank" href="${api_http}${attachments.domain }${attachments.uri}">注册证/备案凭证源文件 - ${i.index + 1}</a><br>
                                            </div>
                                        </c:forEach>
                                    </c:if>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
        </c:if>

            <div class="detail-block block-nohidden">
                <div class="block-title  mytitle">库存信息</div>
                <div class="goodDetails J-toggle-show-cnt">
                    <div class="detail-table">
                        <table class="table table-base">
                            <colgroup>
                                <col width="">
                                <col width="">
                                <col width="">
                                <col width="">
                                <col width="">
                                <col width="">
                                <col width="">
                            </colgroup>
                            <tbody>
                                <tr>
                                    <th>产品名称</th>
                                    <th>单位</th>
                                    <th>采购到货时长（工作日）</th>
                                    <th>可用库存</th>
                                    <th>库存量</th>
                                    <th>占用库存</th>
                                    <th>活动锁定库存</th>
                                    <th>在途（订单/数量/预计）</th>
                                </tr>
                                <tr>
                                    <td>${skuGenerate.skuName}</td>
                                    <td>
                                        <c:if test="${not empty unitList }">
                                            <c:forEach var="unit" items="${unitList}">
                                                <c:if test="${ skuGenerate.baseUnitId ==unit.unitId  }"> ${unit.unitName}</c:if>
                                            </c:forEach>
                                        </c:if>
                                    </td>
                                    <td>
                                        ${skuGenerate.perchaseTime}
                                    </td>
                                    <td>${stockInfo.availableStockNum}</td>
                                    <td>${stockInfo.stockNum}</td>
                                    <td>${stockInfo.occupyNum}</td>
                                    <td>${stockInfo.actionLockNum}</td>
                                    <td>
                                        <c:forEach var="listw" items="${buyorderVos}" varStatus="n">
                                            ${listw.buyorderNo}&nbsp;/&nbsp;${listw.onWayNum}&nbsp;/&nbsp;
                                            <c:if test="${listw.estimateArrivalTime eq 0 }">--<br /></c:if>
                                            <c:if test="${listw.estimateArrivalTime != 0 }">
                                                <date:date value="${listw.estimateArrivalTime}" format="yyyy-MM-dd" />
                                                <br />
                                            </c:if>
                                        </c:forEach>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>


            <div class="detail-block block-nohidden">
                <div class="block-title  mytitle">核价信息</div>
                <div class="goodDetails J-toggle-show-cnt">
                    <div class="block-title">报备信息</div>
                    <div class="detail-table">
                        <div class="table-item item-col">
                            <div class="table-th">是否需要报备</div>
                            <div class="table-td">
                                    <c:choose>
                                        <c:when test="${skuGenerate.isNeedReport eq 1}">
                                            需报备
                                        </c:when>
                                        <c:when test="${skuGenerate.isNeedReport eq 0}">
                                            无需报备
                                        </c:when>
                                        <c:otherwise>
                                            -
                                        </c:otherwise>
                                    </c:choose>

                            </div>
                        </div>

                        <c:if test="${skuGenerate.isNeedReport eq 1}">
                            <div class="table-item item-col">
                                <div class="table-th">是否获得授权：</div>
                                <div class="table-td">
                                    <c:if test="${skuGenerate.isAuthorized eq 1}">
                                        有授权
                                    </c:if>
                                    <c:if test="${skuGenerate.isAuthorized eq 0}">
                                        无授权
                                    </c:if>
                                </div>
                            </div>
                        </c:if>
                        <c:if test="${skuGenerate.isNeedReport eq 1 && skuGenerate.isAuthorized eq 1}">
                        <div class="table-item item-col">
                            <div class="table-th">授权范围</div>
                            <div class="table-td">
                                <c:forEach items="${skuAuthorizationInfo.skuAuthorizationItemVoList}" var="skuAuthorizationItem">
                                    <c:choose>
                                        <c:when test="${fn:length(skuAuthorizationItem.regionIds) == fn:length(regions)}">
                                            “全国”
                                        </c:when>
                                        <c:otherwise>
                                            <c:forEach items="${skuAuthorizationItem.regionIds}" var="regionId"  varStatus="regionStatus">
                                                <c:if test="${regionStatus.first}">
                                                    "
                                                </c:if>
                                                <c:forEach items="${regions}" var="region">
                                                    <c:if test="${region.regionId eq regionId}">
                                                        ${region.regionName}
                                                    </c:if>
                                                </c:forEach>
                                                <c:if test="${!regionStatus.last}">
                                                    、
                                                </c:if>
                                                <c:if test="${regionStatus.last}">
                                                    "
                                                </c:if>
                                            </c:forEach>
                                        </c:otherwise>
                                    </c:choose>
                                    的
                                    <c:choose>
                                        <c:when test="${fn:length(skuAuthorizationItem.terminalTypeIds) == fn:length(terminalTypes)}">
                                            “全部终端”
                                        </c:when>
                                        <c:otherwise>
                                            <c:forEach items="${skuAuthorizationItem.terminalTypeIds}" var="terminalTypeId" varStatus="terminalTypeStatus">
                                                <c:if test="${terminalTypeStatus.first}">
                                                    "
                                                </c:if>
                                                <c:forEach items="${terminalTypes}" var="terminalType">
                                                    <c:if test="${terminalType.sysOptionDefinitionId eq terminalTypeId}">
                                                        ${terminalType.title}
                                                    </c:if>
                                                </c:forEach>
                                                <c:if test="${!terminalTypeStatus.last}">
                                                    、
                                                </c:if>
                                                <c:if test="${terminalTypeStatus.last}">
                                                    "
                                                </c:if>
                                            </c:forEach>
                                        </c:otherwise>
                                    </c:choose>
                                    已获得授权
                                    </br>
                                </c:forEach>
                                <label style="color: red;font-weight: bold">注：除以上范围，需要报备。</label>
                            </div>
                        </div>
                    </c:if>
                    </div>

                    <div class="block-title">主销售部门:${skuGenerate.mainDept}</div>
                    <div class="block-title"></div>

                                <shiro:hasAnyRoles name="供应主管,供应管理组助理,财务总监,财务专员">
                                            <div class="detail-table">
                                                <div class="detail-table block-title">采购成本</div>
                                                <table class="table table-base">
                                                    <colgroup>
                                                        <col width="">
                                                        <col width="">
                                                        <col width="">
                                                    </colgroup>
                                                    <tbody>

                                                        <tr>
                                                            <th>供应商名称</th>
                                                            <th>采购成本</th>
                                                            <th>生效时间</th>
                                                        </tr>
                                                        <c:forEach var="purchase" items="${skuPriceInfoDetailResponseDto.purchaseList}">
                                                            <tr>
                                                                <td>${purchase.traderName}</td>
                                                                <td>
                                                                    ${purchase.purchasePrice}元&nbsp
                                                                        <c:if test="${lastestPriceChangeRecord != null
                                                                 and lastestPriceChangeRecord.purchaseCostsFluctuationDreaction ne 0}">
                                                                            <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                                                                <i class="vd-icon icon-info2 ">
                                                                                    <div class="tip arrow-left">
                                                                                        <div class="tip-con">
                                                                                            <c:choose>
                                                                                                <c:when test="${lastestPriceChangeRecord.modPriceType eq 2}">
                                                                                    <span>原值：${lastestPriceChangeRecord.beforeModPurchaseCosts}&nbsp
                                                                                    <c:if test="${lastestPriceChangeRecord.purchaseCostsFluctuationDreaction eq 1}">
                                                                                        上调
                                                                                    </c:if>
                                                                                    <c:if test="${lastestPriceChangeRecord.purchaseCostsFluctuationDreaction eq -1}">
                                                                                        下调
                                                                                    </c:if>
                                                                                            ${lastestPriceChangeRecord.purchaseCostsFluctuation}%
                                                                                        </span>
                                                                                                </c:when>
                                                                                                <c:otherwise>
                                                                                                    <span> 新核价 </span>
                                                                                                </c:otherwise>
                                                                                            </c:choose>
                                                                                        </div>
                                                                                        <span class="arrow arrow-out">
                                                                            <span class="arrow arrow-in"></span>
                                                                        </span>
                                                                                    </div>
                                                                                </i>
                                                                            </div>
                                                                        </c:if>
                                                                </td>
                                                                <td>${purchase.modTime}</td>
                                                            </tr>
                                                        </c:forEach>
                                                    </tbody>
                                                </table>
                                            </div>
                                </shiro:hasAnyRoles>

                                <div class="detail-table">
                                    <div class="detail-table block-title">销售价</div>
                                    <table class="table table-base">
                                        <colgroup>
                                            <col width="">
                                            <col width="">
                                            <col width="">
                                        </colgroup>
                                        <tbody>
                                            <tr>
                                                <th>市场价</th>
                                                <th>终端价</th>
                                                <th>经销价</th>
                                                <th>电商价</th>
                                            </tr>
                                            <c:if test="${skuPriceInfoDetailResponseDto!=null}">
                                                <tr>
                                                    <td>
                                                        ${skuPriceInfoDetailResponseDto.marketPrice}元&nbsp
                                                            <c:if test="${lastestPriceChangeRecord != null
                                                     and lastestPriceChangeRecord.marketPriceFluctuationDreaction ne 0}">
                                                                <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                                                    <i class="vd-icon icon-info2 ">
                                                                        <div class="tip arrow-left">
                                                                            <div class="tip-con">
                                                                                <c:choose>
                                                                                    <c:when test="${lastestPriceChangeRecord.modPriceType eq 2}">
                                                                        <span>原值：${lastestPriceChangeRecord.beforeModMarketPrice}&nbsp
                                                                        <c:if test="${lastestPriceChangeRecord.marketPriceFluctuationDreaction eq 1}">
                                                                            上调
                                                                        </c:if>
                                                                        <c:if test="${lastestPriceChangeRecord.marketPriceFluctuationDreaction eq -1}">
                                                                            下调
                                                                        </c:if>
                                                                                ${lastestPriceChangeRecord.marketPriceFluctuation}%
                                                                            </span>
                                                                                    </c:when>
                                                                                    <c:otherwise>
                                                                                        <span> 新核价 </span>
                                                                                    </c:otherwise>
                                                                                </c:choose>
                                                                            </div>
                                                                            <span class="arrow arrow-out">
                                                                <span class="arrow arrow-in"></span>
                                                            </span>
                                                                        </div>
                                                                    </i>
                                                                </div>
                                                            </c:if>
                                                    </td>
                                                    <td>
                                                        ${skuPriceInfoDetailResponseDto.terminalPrice}元&nbsp
                                                            <c:if test="${lastestPriceChangeRecord != null
                                                     and lastestPriceChangeRecord.terminalPriceFluctuationDreaction ne 0}">
                                                                <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                                                    <i class="vd-icon icon-info2 ">
                                                                        <div class="tip arrow-left">
                                                                            <div class="tip-con">
                                                                                <c:choose>
                                                                                    <c:when test="${lastestPriceChangeRecord.modPriceType eq 2}">
                                                                        <span>原值：${lastestPriceChangeRecord.beforeModTerminalPrice}&nbsp
                                                                        <c:if test="${lastestPriceChangeRecord.terminalPriceFluctuationDreaction eq 1}">
                                                                            上调
                                                                        </c:if>
                                                                        <c:if test="${lastestPriceChangeRecord.terminalPriceFluctuationDreaction eq -1}">
                                                                            下调
                                                                        </c:if>
                                                                                ${lastestPriceChangeRecord.terminalPriceFluctuation}%
                                                                            </span>
                                                                                    </c:when>
                                                                                    <c:otherwise>
                                                                                        <span> 新核价 </span>
                                                                                    </c:otherwise>
                                                                                </c:choose>
                                                                            </div>
                                                                            <span class="arrow arrow-out">
                                                                <span class="arrow arrow-in"></span>
                                                            </span>
                                                                        </div>
                                                                    </i>
                                                                </div>
                                                            </c:if>
                                                    </td>
                                                    <td>
                                                        ${skuPriceInfoDetailResponseDto.distributionPrice}元&nbsp
                                                            <c:if test="${lastestPriceChangeRecord != null
                                                                and lastestPriceChangeRecord.distributionPriceFluctuationDreaction ne 0}">
                                                                <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                                                    <i class="vd-icon icon-info2 ">
                                                                        <div class="tip arrow-left">
                                                                            <div class="tip-con">
                                                                                <c:choose>
                                                                                    <c:when test="${lastestPriceChangeRecord.modPriceType eq 2}">
                                                                                        <span>原值：${lastestPriceChangeRecord.beforeModDistributionPrice}&nbsp
                                                                                        <c:if test="${lastestPriceChangeRecord.distributionPriceFluctuationDreaction eq 1}">
                                                                                            上调
                                                                                        </c:if>
                                                                                        <c:if test="${lastestPriceChangeRecord.distributionPriceFluctuationDreaction eq -1}">
                                                                                            下调
                                                                                        </c:if>
                                                                                                ${lastestPriceChangeRecord.distributionPriceFluctuation}%
                                                                                        </span>
                                                                                    </c:when>
                                                                                    <c:otherwise>
                                                                                        <span> 新核价 </span>
                                                                                    </c:otherwise>
                                                                                </c:choose>
                                                                            </div>
                                                                            <span class="arrow arrow-out">
                                                                                <span class="arrow arrow-in"></span>
                                                                            </span>
                                                                        </div>
                                                                    </i>
                                                                </div>
                                                            </c:if>
                                                            <c:if test="${skuPriceInfoDetailResponseDto.saleContainsFee!=null && skuPriceInfoDetailResponseDto.saleContainsFee == 2}">
                                                                <span style="color:red">（不含运费）</span>
                                                            </c:if>
                                                    </td>
                                                    <td>
                                                        ${skuPriceInfoDetailResponseDto.electronicCommercePrice}元&nbsp
                                                        <c:if test="${lastestPriceChangeRecord != null
                                                                and lastestPriceChangeRecord.electronicCommercePriceFluctuationDreaction ne 0}">
                                                            <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                                                <i class="vd-icon icon-info2 ">
                                                                    <div class="tip arrow-left">
                                                                        <div class="tip-con">
                                                                            <c:choose>
                                                                                <c:when test="${lastestPriceChangeRecord.modPriceType eq 2}">
                                                                                        <span>原值：${lastestPriceChangeRecord.beforeModElectronicCommercePrice}&nbsp
                                                                                        <c:if test="${lastestPriceChangeRecord.electronicCommercePriceFluctuationDreaction eq 1}">
                                                                                            上调
                                                                                        </c:if>
                                                                                        <c:if test="${lastestPriceChangeRecord.electronicCommercePriceFluctuationDreaction eq -1}">
                                                                                            下调
                                                                                        </c:if>
                                                                                                ${lastestPriceChangeRecord.electronicCommercePriceFluctuation}%
                                                                                        </span>
                                                                                </c:when>
                                                                                <c:otherwise>
                                                                                    <span> 新核价 </span>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                        </div>
                                                                        <span class="arrow arrow-out">
                                                                <span class="arrow arrow-in"></span>
                                                            </span>
                                                                    </div>
                                                                </i>
                                                            </div>
                                                        </c:if>
                                                    </td>
                                                </tr>
                                            </c:if>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="detail-table">
                                    <div class="detail-table block-title">安装费用</div>
                                    <table class="table table-base">
                                        <colgroup>
                                            <col width="">
                                            <col width="">
                                        </colgroup>
                                        <tbody>
                                            <tr>
                                                <th>安装区域</th>
                                                <th>安装费合计</th>
                                            </tr>
                                            <tr>
                                                <td>${stockInfo.occupyNum}</td>
                                                <td>${stockInfo.actionLockNum}</td>
                                            </tr>
                                        </tbody>
                                    </table>

                                    <div class="table-item item-col">
                                        <div class="table-td">
                                            注：以上区域之外的地区，不提供安装服务
                                        </div>
                                    </div>

                                </div>
                        </div>
            </div>
            <div class="detail-block block-nohidden">
                <div class="block-title  mytitle">售后服务等级 <c:choose>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '5'}">
                                    ★★★★★
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '4'}">
                                    ★★★★☆
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '3'}">
                                    ★★★☆☆
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '2'}">
                                    ★★☆☆☆
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '1'}">
                                    ★☆☆☆☆
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '6'}">
                                    无需评级
                                </c:when>
                                <c:otherwise>
                                    待评级
                                </c:otherwise>
                            </c:choose></div>
             <c:if test="${skuGenerate.afterSalesServiceLevel != '6'}">
                <div class="goodDetails J-toggle-show-cnt " >
                    <table class="table table-base whitetable">
                            <tbody>
 <tr>    <td  style="padding:0"  > </td>
                                    <td style="padding:0"  > </td>
                                     <td   style="padding:0"> </td>
                                      <td  style="padding:0" > </td>
                                       <td  style="padding:0" > </td>
                                        <td style="padding:0"  > </td>

                                  </tr>
                                  <tr>
                                    <td rowspan="2">时效性</td>
                                    <td >响应</td>
                                    <td>技术指导</td>
                                    <td>维修</td>
                                    <td></td>
                                    <td> </td>
                                  </tr>
                                    <tr>
                                    <td  >   <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null}">
                                                ${afterSaleServiceLevelDto.installPolicyResponseTimeShow}
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>

                                       </td>
                                    <td>
                                           <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null}">
                                                ${afterSaleServiceLevelDto.technicalDirectorEffectTimeShow}
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose> </td>
                                    <td>   <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null}">
                                                ${afterSaleServiceLevelDto.guaranteePolicyRepaireTimeShow}
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose> </td>
                                    <td></td>
                                    <td> </td>
                                  </tr>
                                    <tr>
                                    <td rowspan="2">安调资料</td>
                                    <td  >说明书</td>
                                    <td  >操作流程</td>
                                    <td  >安调视频/安装指南</td>
                                    <td  >产品PPT</td>
                                    <td  >装箱清单</td>
                                  </tr>
                                    <tr>

                                    <td  >
                                     <c:choose>
                                            <c:when test="${docInstructionManual}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td  > <c:choose>
                                            <c:when test="${docOperationFlowCard}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                    <td  ><c:choose>
                                            <c:when test="${docSecurityAdjustmentVideo || docGuide  }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                    <td  ><c:choose>
                                            <c:when test="${docProductPPT }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                    <td  ><c:choose>
                                            <c:when test="${docPackingList }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                  </tr>

                                   <tr>
                                    <td rowspan="2">
                                  技术支持
                                     </td>
                                    <td  >技术支持联系方式</td>
                                       <td   > </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                    <td   ><c:choose>
                                            <c:when test="${docTechnicalContact or docAfterSalesManager }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                     <td   > </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>

                                  </tr>
                                   <tr>
                                    <td rowspan="2">维修资料</td>
                                    <td  >维修手册</td>
                                       <td   > 维修案例/FAQ</td>
                                      <td   >维修视频 </td>
                                       <td   >维修配附件 </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                     <td   >
<c:choose>
                                            <c:when test="${ docManual }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                     </td>
                                    <td   ><c:choose>
                                            <c:when test="${docCaseStudy}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                     </td>
                                      <td   ><c:choose>
                                            <c:when test="${docRepairVideo}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                      </td>
                                       <td   >
                                        <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.repairPartsAccessory == 1}">
                                                √
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.repairPartsAccessory == 2}">
                                               X
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                   </td>
                                        <td   > </td>

                                  </tr>

                                        <tr>
                                    <td rowspan="2">培训支持</td>
                                    <td  >安调培训</td>
                                       <td   > 维修培训</td>
                                      <td   >  </td>
                                       <td   > </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                    <td   >
                                    <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.installTraining == 1}">
                                                √
                                            </c:when>
                                                <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.installTraining == 2}">
                                               X
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>

                                    </td>
                                     <td   >
                                      <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.repairTraining == 1}">
                                                √
                                            </c:when>
                                                <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.repairTraining == 2}">
                                               X
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>

                                     </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>

                                  </tr>

                                       <tr>
                                    <td rowspan="2">服务人员等级</td>
                                    <td  >服务人员等级</td>
                                       <td   > </td>
                                      <td   >  </td>
                                       <td   > </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                    <td   >
                                  <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.servicePersonnelLevel == '5'}">
                                                D级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.servicePersonnelLevel == '4'}">
                                                C级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.servicePersonnelLevel == '3'}">
                                                B级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.servicePersonnelLevel == '2'}">
                                                A级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null && afterSaleServiceLevelDto.servicePersonnelLevel == '1'}">
                                                S级
                                            </c:when>
                                            <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                        </td>
                                     <td   > </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>

                                  </tr>
                            </tbody>
                        </table>
                 </div>
</c:if>

            </div>

        <c:if test="${empty alreadyPriced or alreadyPriced != 1}">
            <div class="detail-block block-nohidden">
                <div class="block-title mytitle">近一年有效成交数据</div>
                <div class="goodDetails J-toggle-show-cnt ">
                    <div class="detail-table">
                        <div class="detail-table block-title">
                            最近一年的有效成交均价：
                            <c:if test="${not empty agencyPrice}">
                                <div style="display: inline-block; margin-left: 5px;">
                                    <span>分销：</span>
                                    ${agencyPrice}元
                                </div>
                            </c:if>
                            <c:if test="${not empty terminalPrice}">
                                <c:if test="${not empty agencyPrice}">
                                    ，
                                </c:if>
                                <div style="display: inline-block;">
                                    <span>终端：</span>
                                    ${terminalPrice}元
                                </div>
                            </c:if>
<%--                            <c:if test="${not empty saleorderGoodsList}">${price}元</c:if>--%>
                        </div>
                        <div class="table-scroll-wrap">
                            <table class="table table-base">
                                <colgroup>
                                    <col width="">
                                    <col width="">
                                    <col width="">
                                    <col width="">
                                    <col width="">
                                    <col width="">
                                </colgroup>
                                <tbody>
                                    <tr>
                                        <th>订单号</th>
                                        <th>客户性质</th>
                                        <th>数量</th>
                                        <th>单价</th>
                                        <th>归属销售</th>
                                        <th>生效时间</th>
                                    </tr>
                                    <c:forEach items="${saleorderGoodsList}" var="saleorderGoods">
                                        <tr>
                                            <td>${saleorderGoods.saleorderNo}</td>
                                            <td>${saleorderGoods.title}</td>
                                            <td>${saleorderGoods.num}</td>
                                            <td>${saleorderGoods.price}元/${goodsUnitName}</td>
                                            <td>${saleorderGoods.userName}</td>
                                            <td>
                                                <date:date value="${saleorderGoods.validTime}" format="yyyy-MM-dd hh:mm:ss" />
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </c:if>


            <div class="detail-block">
                <div class="block-title  mytitle">
                    售后服务标准
                </div>
                <div class="goodDetails J-toggle-show-cnt">
                    <div class="detail-block">
                        <div class="block-title">安装政策</div>
                        <div class="detail-table">
                            <div class="table-item">
                                <div class="table-th">
                                    产品是否可安装：
                                    <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                        <i class="vd-icon icon-info2 ">
                                            <div class="tip arrow-left">
                                                <div class="tip-con">该字段表示商品本身是否具体安装特性，如口罩为“不可安装”,（若信息有误，请联系采购人员到SKU详情页修改。）
                                            </div>
                                            <span class="arrow arrow-out">
                                                <span class="arrow arrow-in"></span>
                                            </span>
                                            </div>
                                        </i>
                                    </div>
                                </div>
                                <div class="table-td">
                                        <c:if test="${skuGenerate.isInstallable == 1}">可安装</c:if>
                                        <c:if test="${skuGenerate.isInstallable == 0}">不可安装</c:if>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">
                                    是否提供上门安装服务：
                                    <div class="tip-wrap" style="position: relative;vertical-align: -2px;">
                                        <i class="vd-icon icon-info2 ">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                            1.收费安装：指贝登提供安装服务，但需要收取安装费
                                            <br>
                                            （另：偏远区域需要另外收取长途费）
                                            <br>
                                            2.免费安装：指贝登提供安装服务，且不收取安装费
                                            <br>
                                            （另：偏远区域需要另外收取长途费）
                                            <br>
                                            3.不提供安装：指贝登不提供安装服务
                                            </div>
                                            <span class="arrow arrow-out">
                                                <span class="arrow arrow-in"></span>
                                            </span>
                                         </div>
                                         </i>
                                    </div>
                                </div>
                                <div class="table-td">
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '0'}">收费安装</c:if>
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '1'}">免费安装</c:if>
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '2'}">不提供安装</c:if>
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">安装区域：</div>
                                <div class="table-td">

                                <div class="title-click J-area-select" style='float:left;margin:-4px 0 0 0px;'>
                                    <a href="#"> 查看</a>
                                </div>
                                    <input type="hidden" class="J-area-value" value='${afterSaleServiceStandardInfoDto.installArea.provinceCityJsonvalue}'>
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">安装费：</div>
                                <div class="table-td">
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '0'}">
                                        ${afterSaleServiceStandardInfoDto.installPolicyInstallFee == null ? 0.00:afterSaleServiceStandardInfoDto.installPolicyInstallFee}
                                    </c:if>
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType != '0'}">-</c:if>
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">是否需要装机资质：</div>
                                <div class="table-td">
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyHaveInstallationQualification == '1'}">是</c:if>
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyHaveInstallationQualification == '0'}">否</c:if>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">是否免费远程指导装机：</div>
                                <div class="table-td">
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyFreeRemoteInstall == '1'}">是</c:if>
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyFreeRemoteInstall == '0'}">否</c:if>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">响应时效：</div>
                                <div class="table-td">
                                        <c:choose>
                                            <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.installPolicyResponseTime, ',') == ''}">
                                                /
                                            </c:when>
                                            <c:otherwise>
                                                ${fn:replace(afterSaleServiceStandardApply.installPolicyResponseTime, ",", "")}
                                            </c:otherwise>
                                        </c:choose>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">上门时效：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.installPolicyVisitTime, ',') == ''}">
                                            /
                                        </c:when>
                                        <c:otherwise>
                                             ${fn:replace(afterSaleServiceStandardApply.installPolicyVisitTime, ",", "")}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="detail-block">
                        <div class="block-title">技术指导</div>
                        <div class="detail-table">
                            <div class="table-item">
                                <div class="table-th">是否提供技术维修指导：</div>
                                <div class="table-td">
                                    <c:if test="${afterSaleServiceStandardInfoDto.technicalDirectSupplyMaintain == '1'}">是</c:if>
                                    <c:if test="${afterSaleServiceStandardInfoDto.technicalDirectSupplyMaintain == '0'}">否</c:if>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">响应时效：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.technicalDirectResponseTime, ',') == ''}">
                                            /
                                        </c:when>
                                        <c:otherwise>
                                            ${fn:replace(afterSaleServiceStandardApply.technicalDirectResponseTime, ",", "")}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">技术指导时效：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.technicalDirectEffectTime, ',') == ''}">
                                            /
                                        </c:when>
                                        <c:otherwise>
                                            ${fn:replace(afterSaleServiceStandardApply.technicalDirectEffectTime, ",", "")}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-block">
                        <div class="block-title">保修政策</div>
                        <div class="detail-table">
                            <div class="table-item">
                                <div class="table-th">是否保修：</div>
                                <div class="table-td">
                                    <c:if test="${afterSaleServiceStandardInfoDto.guaranteePolicyIsGuarantee == '1'}">是</c:if>
                                    <c:if test="${afterSaleServiceStandardInfoDto.guaranteePolicyIsGuarantee == '0'}">否</c:if>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">保修方式：</div>
                                <div class="table-td">
                                    <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.guaranteePolicyGuaranteeType,'1')}">上门维修</c:if>
                                    <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.guaranteePolicyGuaranteeType,'2')}">寄送修</c:if>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">主机保修期：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.guaranteePolicyHostGuaranteePeriod, ',') == ''}">
                                            /
                                        </c:when>
                                        <c:otherwise>
                                            ${fn:replace(afterSaleServiceStandardInfoDto.guaranteePolicyHostGuaranteePeriod, ",", "")}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">配件保修期：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.guaranteePolicyPartsGuaranteePeriod, ',') == ''}">
                                            /
                                        </c:when>
                                        <c:otherwise>
                                            ${fn:replace(afterSaleServiceStandardInfoDto.guaranteePolicyPartsGuaranteePeriod, ",", "")}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">终端签收时间：</div>
                                <div class="table-td">
                                <c:choose>
                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyCycleCaltype == '1'}">
                                        出厂时间
                                    </c:when>
                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyCycleCaltype == '2'}">
                                        终端签收时间
                                    </c:when>
                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyCycleCaltype == '3'}">
                                        发票时间
                                    </c:when>
                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyCycleCaltype == '4'}">
                                        安装时间
                                    </c:when>
                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyCycleCaltype == '5'}">
                                        贝登入库时间
                                    </c:when>
                                    <c:otherwise>
                                        /
                                    </c:otherwise>
                                </c:choose>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">保修区域：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyArea == '1'}">
                                            全国
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyArea == '2'}">
                                            部分区域
                                        </c:when>
                                        <c:otherwise>
                                            /
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">保修区域说明：</div>
                                <div class="table-td">
                                        ${afterSaleServiceStandardInfoDto.guaranteePolicyAreaComment}
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">响应时效：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.guaranteePolicyResponseTime, ',') == ''}">
                                            /
                                        </c:when>
                                        <c:otherwise>
                                            ${fn:replace(afterSaleServiceStandardInfoDto.guaranteePolicyResponseTime, ",", "")}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">上门时效：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.guaranteePolicyVisitTime, ',') == ''}">
                                            /
                                        </c:when>
                                        <c:otherwise>
                                            ${fn:replace(afterSaleServiceStandardInfoDto.guaranteePolicyVisitTime, ",", "")}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">维修时效：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.guaranteePolicyRepaireTime, ',') == ''}">
                                            /
                                        </c:when>
                                        <c:otherwise>
                                            ${fn:replace(afterSaleServiceStandardInfoDto.guaranteePolicyRepaireTime, ",", "")}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">保修备注：</div>
                                <div class="table-td">
                                     ${afterSaleServiceStandardInfoDto.guaranteePolicyRepaireComment}
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="detail-block">
                        <div class="block-title">退货政策</div>

                            <div class="detail-table">
                                <div class="table-item">
                                    <div class="table-th">是否支持退货：</div>
                                    <div class="table-td">
                                        <c:choose>
                                            <c:when test="${afterSaleServiceStandardInfoDto.returnPolicySupportReturn == '1'}">
                                                是
                                            </c:when>
                                            <c:when test="${afterSaleServiceStandardInfoDto.returnPolicySupportReturn == '0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                /
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>

                                <div class="table-item">
                                    <div class="table-th">退货条件：</div>
                                    <div class="table-td">
                                         ${afterSaleServiceStandardInfoDto.returnPolicyCondition}
                                    </div>
                                 </div>

                                <div class="table-item">
                                    <div class="table-th">是否需要鉴定：</div>
                                    <div class="table-td">
                                        <c:choose>
                                            <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyNeedIdentify == '1'}">
                                                是
                                            </c:when>
                                            <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyNeedIdentify == '0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                /
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>

                               <div class="table-item">
                                    <div class="table-th">鉴定方式：</div>
                                    <div class="table-td">

                                        <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.returnPolicyIdentifyType,'0')}">
                                            电话鉴定
                                        </c:if>
                                        <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.returnPolicyIdentifyType,'1')}">
                                            上门鉴定
                                        </c:if>
                                        <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.returnPolicyIdentifyType,'2')}">
                                            返厂鉴定
                                        </c:if>
                                    </div>
                               </div>

                             <div class="table-item">
                                <div class="table-th">退货期限：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.returnPolicyReturnPeriod, ',') == ''}">
                                            /
                                        </c:when>
                                        <c:otherwise>
                                            ${fn:replace(afterSaleServiceStandardInfoDto.returnPolicyReturnPeriod, ",", "")}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                             </div>

                             <div class="table-item">
                                <div class="table-th">周期计算方式：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '1'}">
                                            出厂时间
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '2'}">
                                            终端签收时间
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '3'}">
                                            发票时间
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '4'}">
                                            安装时间
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '5'}">
                                            贝登入库时间
                                        </c:when>
                                        <c:otherwise>
                                            /
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                             </div>

                            <div class="table-item">
                                <div class="table-th">包装要求：</div>
                                <div class="table-td">
                                    ${afterSaleServiceStandardInfoDto.returnPolicyPackagingRequirements}
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">退货备注：</div>
                                <div class="table-td">
                                    ${afterSaleServiceStandardInfoDto.returnPolicyReturnComments}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-block">
                        <div class="block-title">换货政策</div>

                        <div class="detail-table">
                            <div class="table-item">
                                <div class="table-th">是否支持换货：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicySupportChange == '1'}">
                                            是
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicySupportChange == '0'}">
                                            否
                                        </c:when>
                                        <c:otherwise>
                                            /
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="table-item">
                                <div class="table-th">换货条件：</div>
                                <div class="table-td">
                                    ${afterSaleServiceStandardInfoDto.exchangePolicyExchangeContition}
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">是否需要鉴定：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicyNeedIdentify == '1'}">
                                            是
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicyNeedIdentify == '0'}">
                                            否
                                        </c:when>
                                        <c:otherwise>
                                            /
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">鉴定方式：</div>
                                <div class="table-td">
                                    <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.exchangePolicyIdentifyType,'0')}">
                                        电话鉴定
                                    </c:if>
                                    <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.exchangePolicyIdentifyType,'1')}">
                                        上门鉴定
                                    </c:if>
                                    <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.exchangePolicyIdentifyType,'2')}">
                                         返厂鉴定
                                    </c:if>
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">换货期限：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.exchangePolicyExchangePeriod, ',') == ''}">
                                            /
                                        </c:when>
                                        <c:otherwise>
                                            ${fn:replace(afterSaleServiceStandardInfoDto.exchangePolicyExchangePeriod, ",", "")}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">周期计算方式：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '1'}">
                                            出厂时间
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '2'}">
                                            终端签收时间
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '3'}">
                                            发票时间
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '4'}">
                                            安装时间
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '5'}">
                                            贝登入库时间
                                        </c:when>
                                        <c:otherwise>
                                            /
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">包装要求：</div>
                                <div class="table-td">
                                    ${afterSaleServiceStandardInfoDto.returnPolicyPackagingRequirements}
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">退货备注：</div>
                                <div class="table-td">
                                    ${afterSaleServiceStandardInfoDto.returnPolicyReturnComments}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-block">
                        <div class="block-title">保外政策</div>
                            <div class="detail-table">
                            <div class="table-item">
                                <div class="table-th">是否提供有偿维修：</div>
                                <div class="table-td">
                                        <c:choose>
                                            <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupportRepair == '1'}">
                                                是
                                            </c:when>
                                            <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupportRepair == '0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                 /
                                            </c:otherwise>
                                        </c:choose>
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">是否提供翻新服务：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupportRenovation == '1'}">
                                            是
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupportRenovation == '0'}">
                                            否
                                        </c:when>
                                        <c:otherwise>
                                            /
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                             </div>

                             <div class="table-item">
                                <div class="table-th">是否提供纸箱：</div>
                                <div class="table-td">
                                        <c:choose>
                                            <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupplyBox == '1'}">
                                                是
                                            </c:when>
                                            <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupplyBox == '0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                /
                                            </c:otherwise>
                                        </c:choose>
                                </div>
                             </div>

                            <div class="table-item">
                                <div class="table-th">是否提供纸箱：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupplyBox == '1'}">
                                        是
                                    </c:when>
                                    <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupplyBox == '0'}">
                                        否
                                    </c:when>
                                    <c:otherwise>
                                        /
                                    </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-block">
                        <div class="block-title">超期处理政策</div>
                        <div class="detail-table">
                            <div class="table-item">
                                <div class="table-th">可否提供备用机：</div>
                                <div class="table-td">
                                    <c:choose>
                                        <c:when test="${afterSaleServiceStandardInfoDto.overduePolicySupplyBackup == '1'}">
                                            是
                                        </c:when>
                                        <c:when test="${afterSaleServiceStandardInfoDto.overduePolicySupplyBackup == '0'}">
                                            否
                                        </c:when>
                                        <c:otherwise>
                                             /
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>

                            <div class="table-item">
                                <div class="table-th">是否提供翻新服务：</div>
                                <div class="table-td">
                                    ${afterSaleServiceStandardInfoDto.overduePolicyDetail}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-block">
                         <div class="block-title">相关附件</div>
                         <div class="detail-table">
                             <div class="table-item">
                                  <div class="table-th">附件：</div>
                                  <div class="table-td">
                                        <c:forEach var="atttashment" items="${attashmentList}">
                                            <a href="http://${atttashment.domain}${atttashment.uri}" target="_blank">${atttashment.fileName}</a><br/>
                                        </c:forEach>
                                  </div>
                             </div>
                         </div>
                    </div>
            </div>

        </div>


</div>


<script type="text/tmpl" class="J-dlg-tmpl">
        <div class="del-wrap">
            <div class="del-tip">
                <i class="vd-icon icon-caution2"></i><span class="J-dlg-tip"></span>
            </div>
            <form class="J-dlg-form">
                <div class="del-cnt base-form">
                    <textarea name="content" id="" cols="30" rows="10" class="input-textarea J-dlg-cnt" placeholder=""></textarea>
                </div>
            </form>
        </div>

</script>
<script type="text/tmpl" class="J-price-one-tmpl">
        <div class="dlg-price-wrap">
            <div class="dlg-loading J-dlg-loading">
                <img src="${pageContext.request.contextPath}/static/new/img/loading.gif" alt="">
            </div>
            <div class="dlg-cnt J-dlg-cnt" style="display: none;">
                <table class="table table-normal">
                    <colgroup>
                        <col width="50%">
                        <col width="50%">
                    </colgroup>
                    <tbody class="J-dlg-list">
                        <tr>
                            <th>时间</th>
                            <th>价格</th>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

</script>

<script type="text/tmpl" class="J-price-more-tmpl">
        <div class="dlg-price-wrap">
            <div class="dlg-loading J-dlg-loading">
                <img src="${pageContext.request.contextPath}/static/new/img/loading.gif" alt="">
            </div>
            <div class="dlg-cnt J-dlg-cnt" style="display: none;">
                <table class="table table-normal">
                    <colgroup>
                        <col width="50%">
                        <col width="50%">
                    </colgroup>
                    <tbody class="J-dlg-list">
                        <tr>
                            <th>数量</th>
                            <th>价格</th>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

</script>

<script type="text/html"  id="goods-level-desc">
<div class="table-tips" >
    <table>
        <%--<colgroup>--%>
            <%--<col width="150">--%>
            <%--<col width="200">--%>
            <%--<col width="200">--%>
        <%--</colgroup>--%>
        <thead>
        <tr>
            <th>等级</th>
            <th>等级名称</th>
            <th>等级描述</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach items="${goodsLevelList}" var="goodsLevel">
            <tr>
                <td>${goodsLevel.uniqueIdentifier}</td>
                <td>${goodsLevel.levelName}</td>
                <td>${goodsLevel.description}</td>
            </tr>
        </c:forEach>
        </tbody>
    </table>

    <div>
        <b style="font-size: 20px;margin-top: 15px;">服务承诺（员工享有）</b>
    </div>

    <table>
        <%--<colgroup>--%>
            <%--<col width="150">--%>
            <%--<col width="200">--%>
            <%--<col>--%>
        <%--</colgroup>--%>
        <thead>
        <tr>
            <th>简称</th>
            <th>描述</th>
            <th>支持部门</th>
            <th>精选商品</th>
            <th>核心商品</th>
            <th>直购商品</th>
            <th>询价商品</th>
            <th>寻货商品</th>
            <th>内部商品</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>商品定价</td>
            <td>销售对客户报价，以系统定价为准</td>
            <td>供应链</td>
            <td>√</td>
            <td>×</td>
            <td>√</td>
            <td>×</td>
            <td>×</td>
            <td>×</td>
        </tr>
        <%--<tr>
            <td>预计可发货时间</td>
            <td>产品的标准货期，以该数据为参考，特殊情况除外</td>
            <td>供应链</td>
            <td>√</td>
            <td>√</td>
            <td>×</td>
            <td>×</td>
        </tr>--%>
        <tr>
            <td>贝登服务</td>
            <td>销售看到的产品服务，以该字段为准</td>
            <td>售后部门</td>
              <td>√</td>
            <td>√</td>
            <td>√</td>
            <td>√</td>
            <td>×</td>
            <td>×</td>
        </tr>
        <tr>
            <td>授权信息</td>
            <td>可否直接销售和报价，以该字段为准</td>
            <td>供应链</td>
              <td>√</td>
            <td>√</td>
            <td>√</td>
            <td>√</td>
            <td>√</td>
            <td>√</td>
        </tr>
        </tbody>
    </table>

    <div>
        <b style="font-size:20px;margin-top: 15px;">服务承诺（客户享有）</b>
    </div>
    <table>
        <%--<colgroup>--%>
            <%--<col width="150">--%>
            <%--<col width="200">--%>
            <%--<col>--%>
        <%--</colgroup>--%>
        <thead>
        <tr>
        <tr>
            <th>简称</th>
            <th>描述</th>
            <th>支持部门</th>
              <th>精选商品</th>
            <th>核心商品</th>
            <th>直购商品</th>
            <th>询价商品</th>
            <th>寻货商品</th>
            <th>内部商品</th>
        </tr>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>商品定价</td>
            <td>客户可根据价格直接下单</td>
            <td>供应链</td>
              <td>√</td>
            <td>×</td>
            <td>√</td>
            <td>×</td>
            <td>×</td>
            <td>×</td>
        </tr>
       <%-- <tr>
            <td>预计可发货时间</td>
            <td>产品的标准货期，以该数据为参考，特殊情况除外</td>
            <td>供应链</td>
            <td>√</td>
            <td>√</td>
            <td>×</td>
            <td>×</td>
        </tr>--%>
        <tr>
            <td>贝登服务</td>
            <td>客户看到的产品服务，以该信息为准</td>
            <td>售后部门</td>
              <td>√</td>
            <td>√</td>
            <td>√</td>
            <td>√</td>
            <td>×</td>
            <td>×</td>
        </tr>
        <tr>
            <td>授权信息</td>
            <td>需要客户提供终端信息</td>
            <td>供应链</td>
              <td>√</td>
            <td>√</td>
            <td>√</td>
            <td>√</td>
            <td>√</td>
            <td>×</td>
        </tr>
        </tbody>
    </table>
</div>
</script>
<script type="text/html"  id="goods-position-desc">
    <div class="table-tips" >
        <table>
            <thead>
            <tr>
                <th>档位</th>
                <th>档位名称</th>
                <th>档位描述</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${goodsPositionList}" var="goodsPosition">
                <tr>
                    <td>${goodsPosition.id}档</td>
                    <td>${goodsPosition.positionName}</td>
                    <td>${goodsPosition.description}</td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
    </div>
</script>

<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<%--<script type="text/javascript"--%>
        <%--src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>--%>
<%--<script type="text/javascript"--%>
        <%--src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>--%>
<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/jquery.PrintArea.js'></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/newAddGoodDetails.js?rnd2=${resourceVersionKey}"></script>
<%--以下ueditor编辑器需要引用的文件--%>
<script src="${pageContext.request.contextPath}/static/libs/textmodify/ueditor.simple.config.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/libs/textmodify/ueditor.all.min.js?rnd=${resourceVersionKey}"> </script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/dialogSelectAndEdit.css?rnd=${resourceVersionKey}">
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/dialogSelectAndEdit.js?rnd=${resourceVersionKey}"></script>
<script>
    $(function () {
        new SelectEdit({
            button: '.J-area-select',
            url: page_url + '/system/region/getregion.do',
            input: '.J-area-value',
            onlyShow: true
        });
    })
    
</script>
<script type="text/javascript">
		$(document).ready(function(){
    		var now = getNow();
            $(".mask_div").remove();
            watermark({"watermark_txt":"${ sessionScope.curr_user.username}"+now});

            // 获取指定 id 的链接元素
            const link = document.getElementById("registrationLink");
            const link2 = document.getElementById("registrationLink2");

            // 检查页面是否在 iframe 中嵌入
            if (window.self === window.top) {
                // 如果页面不是 iframe 嵌入，将该链接设为不可点击
                link.removeAttribute("href");
                link.style.pointerEvents = "none";
                link.style.color = "rgba(0,0,0,0.67)";
                link2.removeAttribute("href");
                link2.style.pointerEvents = "none";
                link2.style.color = "rgba(0,0,0,0.67)"
                document.getElementById("goodsInfoContainer").style.display = "none";
            }
		})

</script>

</body>
