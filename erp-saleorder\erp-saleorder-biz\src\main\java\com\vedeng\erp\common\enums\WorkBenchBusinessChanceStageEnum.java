package com.vedeng.erp.common.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 商机工作台，商机过程漏斗  枚举
 */
@Getter
public enum WorkBenchBusinessChanceStageEnum {

    PRELIMINARY_NEGOTIATION(1,"初步洽谈","preliminaryNegotiation"),
    
    OPPORTUNITY_VERIFICATION(2,"商机验证","opportunityVerification"),
    
    PRELIMINARY_SCHEME(3,"初步方案","preliminaryPlan"),
    
    FINAL_SCHEME(4,"最终方案","finalPlan"),
    
    WINNING_ORDER(5,"赢单","winOrder")
    ;
    
    private final int code;
    
    private final String desc;
    //http://yapi.ivedeng.com/project/607/interface/api/21222
    private final String resKey;

    WorkBenchBusinessChanceStageEnum(int code, String desc, String resKey) {
        this.code = code;
        this.desc = desc;
        this.resKey=resKey;
    }
    
    public static String getDescByCode(int code) {
        for (WorkBenchBusinessChanceStageEnum businessChanceStageEnum : WorkBenchBusinessChanceStageEnum.values()) {
            if (businessChanceStageEnum.getCode() == code) {
                return businessChanceStageEnum.getDesc();
            }
        }
        return "";
    }
    public static List<Integer> getAllCode() {
        List<Integer> list=new ArrayList<>();
        for (WorkBenchBusinessChanceStageEnum businessChanceStageEnum : WorkBenchBusinessChanceStageEnum.values()) {
            list.add(businessChanceStageEnum.code );
        }
        return list;
    }

}
