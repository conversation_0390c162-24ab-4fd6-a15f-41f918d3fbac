# ERP-Temporal XXL-Job 任务配置说明

## 概述

本文档说明 erp-temporal 模块中 XXL-Job 定时任务的配置和使用方法。所有任务都位于 `com.vedeng.temporal.task` 包下。

**注意：本文档基于实际代码实现，只包含已经开发完成的任务。**

## 任务列表

### 1. 多公司业务流程任务 (MultiCompanyBusinessJob)

**任务标识**: `multiCompanyBusinessJob`
**建议执行频率**: 根据业务需求，建议每小时或每天执行一次
**功能**: 多公司业务流程自动化任务，支持批量启动和单个启动两种模式
**实现类**: `com.vedeng.temporal.task.MultiCompanyBusinessJob`

#### 执行模式说明

##### 批量模式 (Batch Mode)
- **触发条件**: 参数为空、"batch"、"auto" 时启用
- **执行逻辑**: 从数据库读取有效的流程配置，批量启动多个工作流
- **数据来源**: T_FLOW_ORDER 表中的有效流程配置
- **适用场景**: 定时批量处理业务流程，自动化程度高
- **特殊检查**: 会检查采购订单生效状态（validStatus=1）

##### 单个模式 (Single Mode)
- **触发条件**: 传入流转单编号时启用
- **执行逻辑**: 根据流转单编号查询流程配置，启动单个工作流
- **参数格式**: 直接传入流转单编号（如：FO202501010001）
- **适用场景**: 手动触发特定业务流程，精确控制

#### 参数配置

| 参数 | 说明 | 示例 | 默认值 |
|------|------|------|--------|
| 空参数 | 批量模式 | 无参数 | 批量处理所有有效流程 |
| "batch" | 批量模式 | `batch` | 批量处理所有有效流程 |
| "auto" | 批量模式 | `auto` | 批量处理所有有效流程 |
| 流转单编号 | 单个模式 | `FO202501010001` | 启动指定流程的工作流 |

#### 参数组合示例

```bash
# 批量模式（推荐用于定时任务）
# 空参数
（无参数）

# 明确指定批量模式
batch

# 自动模式
auto

# 单个模式 - 流转单编号
FO202501010001

# 单个模式 - 另一个流转单示例
FO202501020002
```

#### XXL-Job 配置建议

```
任务描述: 多公司业务流程自动化任务
Cron: 0 0 */1 * * ?  (每小时执行，可根据业务需求调整)
运行模式: BEAN
JobHandler: multiCompanyBusinessJob
执行参数: batch
路由策略: 第一个
子任务: 无
任务超时时间: 1800秒 (30分钟，考虑批量处理时间)
失败重试次数: 2
```

#### 业务流程说明

##### 批量模式执行流程
1. **查询流程配置**: 从 T_FLOW_ORDER 表查询有效的流程配置
2. **获取公司序列**: 通过 FlowNodeBasedCompanyService 获取每个流程的公司执行顺序
3. **检查生效状态**: 调用采购订单查询接口检查validStatus是否为1
4. **构建请求对象**: 为每个流程配置构建 CompanyBusinessRequest 对象
5. **启动工作流**: 调用 WorkflowOrchestrationService 批量启动 Temporal 工作流
6. **统计结果**: 记录成功和失败的统计信息，输出执行报告

##### 单个模式执行流程
1. **参数校验**: 校验流转单编号参数
2. **查询配置**: 根据流转单编号查询流程配置
3. **获取序列**: 获取公司执行顺序配置
4. **构建请求**: 构建 CompanyBusinessRequest 对象
5. **启动工作流**: 调用 WorkflowOrchestrationService 启动 Temporal 工作流
6. **返回结果**: 返回工作流启动结果和工作流ID

#### 依赖服务

| 服务 | 说明 | 用途 |
|------|------|------|
| WorkflowOrchestrationService | 工作流编排服务 | 启动和管理 Temporal 工作流 |
| FlowNodeBasedCompanyService | 公司序列服务 | 获取业务流程的公司执行顺序 |
| TemporalFlowOrderMapper | 流程配置数据访问 | 查询有效的流程配置数据 |
| SystemApiClient | 系统API客户端 | 调用采购订单查询接口 |

---

### 2. 流转单状态更新任务 (FlowOrderInfoGenerationJob)

**任务标识**: `flowOrderInfoGenerationJob`  
**建议执行频率**: 每10-30分钟执行一次  
**功能**: FlowOrderInfo状态更新定时任务，专注于更新已存在的FlowOrderInfo记录的状态信息
**实现类**: `com.vedeng.temporal.task.FlowOrderInfoGenerationJob`

#### 功能说明

该任务主要负责：
- 基于公司去调用采购接口 `/api/v1/buyorder/query.do`，更新采购单据状态信息
- 调用销售接口 `/api/v1/saleorder/query.do`，更新销售单据状态信息
- 批量检查并更新FlowOrderInfo的完成状态
- **注意**: 记录创建已迁移到SalesOrderStep和PurchaseOrderStep中，本任务不再负责记录创建

#### 执行模式说明

##### 批量模式 (Batch Mode)
- **触发条件**: 参数为空、"batch"、"auto" 时启用
- **执行逻辑**: 更新所有需要更新状态的FlowOrderInfo记录
- **处理步骤**:
  1. 调用服务层更新所有FlowOrderInfo记录状态
  2. 批量检查并更新完成状态
  3. 统计并报告处理结果

##### 单个模式 (Single Mode)
- **触发条件**: 传入流转单编号时启用
- **执行逻辑**: 更新指定流转单的所有FlowOrderInfo记录状态
- **参数格式**: 直接传入流转单编号（如：FO202501010001）

#### 参数配置

| 参数 | 说明 | 示例 | 默认值 |
|------|------|------|--------|
| 空参数 | 批量模式 | 无参数 | 更新所有需要更新的记录 |
| "batch" | 批量模式 | `batch` | 更新所有需要更新的记录 |
| "auto" | 批量模式 | `auto` | 更新所有需要更新的记录 |
| 流转单编号 | 单个模式 | `FO202501010001` | 更新指定流转单的记录 |

#### 参数组合示例

```bash
# 批量模式（推荐用于定时任务）
# 空参数
（无参数）

# 明确指定批量模式
batch

# 自动模式
auto

# 单个模式 - 流转单编号
FO202501010001

# 单个模式 - 另一个流转单示例
FO202501020002
```

#### XXL-Job 配置建议

```
任务描述: FlowOrderInfo状态更新任务
Cron: 0 */15 * * * ?  (每15分钟执行，可根据业务需求调整)
运行模式: BEAN
JobHandler: flowOrderInfoGenerationJob
执行参数: batch
路由策略: 第一个
子任务: 无
任务超时时间: 1200秒 (20分钟，考虑批量处理时间)
失败重试次数: 2
```

#### 处理结果

任务执行后会返回详细的统计信息：
- **状态更新统计**: 总数、成功数、失败数
- **完成状态更新统计**: 总数、成功数、失败数
- **处理耗时**: 任务执行时间
- **异常详情**: 失败记录的详细信息

#### 依赖服务

| 服务 | 说明 | 用途 |
|------|------|------|
| FlowOrderInfoGenerationService | 流转单信息生成服务 | 核心业务逻辑处理 |

---

## 已计划但未实现的任务

以下任务在早期设计中计划开发，但在当前版本中尚未实现。如需开发这些功能，可参考以下设计：

### 工作流恢复任务 (WorkflowRecoveryJob) - 未实现
- **预期功能**: 自动检测和恢复失败的工作流
- **预期标识**: `workflowRecoveryJob`
- **状态**: 待开发

### 检查点清理任务 (CheckpointCleanupJob) - 未实现  
- **预期功能**: 清理过期的工作流检查点数据
- **预期标识**: `checkpointCleanupJob`
- **状态**: 待开发

### 工作流监控任务 (WorkflowMonitorJob) - 未实现
- **预期功能**: 监控工作流执行状态和健康度
- **预期标识**: `workflowMonitorJob`
- **状态**: 待开发

---

## 配置依赖

### 1. 配置文件设置

确保在 `erp-temporal.properties` 中配置了相关参数：

```properties
# 工作流相关配置
temporal.server.host=localhost
temporal.server.port=7233
temporal.namespace=default
temporal.worker.threads=10
temporal.taskQueue.multiCompany=erp-multi-company-queue
```

### 2. XXL-Job 执行器配置

确保在 Spring 配置中正确配置了 XXL-Job 执行器，并扫描了任务包：

```xml
<!-- JobHandler 扫描路径 -->
<context:component-scan base-package="com.vedeng.temporal.task" />
```

## 监控和告警

### 1. 任务执行日志

所有任务都会在 XXL-Job 控制台和应用日志中记录详细的执行信息：

- 任务开始和结束时间
- 参数解析结果
- 执行过程详情
- 统计数据和结果

### 2. 异常处理

任务执行异常时会：

- 记录详细的错误信息
- 返回失败状态给 XXL-Job
- 触发 XXL-Job 的重试机制（如果配置了重试）

### 3. 告警建议

建议为以下情况配置告警：

- 多公司业务流程任务连续失败
- FlowOrderInfo状态更新任务执行异常
- 任务执行时间超过预期
- 批量模式处理量异常

## 最佳实践

### 1. 执行时间安排

```
每15分钟 - FlowOrderInfo状态更新任务（状态同步）
每小时 - 多公司业务流程任务（业务流程自动化）
```

### 2. 参数配置建议

- **生产环境**: 使用默认参数，启用适当的监控
- **测试环境**: 使用单个模式测试特定业务流程
- **开发环境**: 可以调整执行频率和参数进行调试

### 3. 性能考虑

- **多公司业务任务**：
  - 批量模式可能同时启动多个工作流，注意监控系统资源
  - 设置合适的超时时间，避免长时间运行影响其他任务
  - 在业务高峰期适当调整执行频率
  
- **状态更新任务**：
  - 合理设置执行频率，平衡及时性和系统负载
  - 监控API调用频率，避免对业务系统造成压力

## 故障排查

### 1. 常见问题

1. **任务无法启动**: 检查 JobHandler 扫描路径和 Spring 配置
2. **参数解析错误**: 检查参数格式是否正确
3. **执行超时**: 调整 XXL-Job 的超时时间配置
4. **依赖服务不可用**: 检查 Temporal 服务器和数据库连接
5. **多公司业务任务特有问题**:
   - **批量模式无数据**: 检查 T_FLOW_ORDER 表是否有有效的流程配置
   - **公司序列为空**: 检查 T_FLOW_NODE 表的公司配置
   - **工作流启动失败**: 检查 Temporal 服务器状态和工作流定义
   - **流转单不存在**: 确认传入的流转单编号在系统中存在
   - **采购订单未生效**: 检查采购订单的validStatus字段值
6. **FlowOrderInfo更新任务特有问题**:
   - **状态更新失败**: 检查API接口调用是否正常
   - **完成状态判断错误**: 检查业务逻辑配置

### 2. 日志查看

- XXL-Job 控制台：查看任务执行状态和日志
- 应用日志：查看详细的业务执行日志
- 数据库日志：查看工作流执行记录

### 3. 调试方法

1. 使用单个模式测试特定流转单的处理
2. 调整日志级别查看详细信息
3. 在测试环境验证参数配置
4. 检查相关服务的健康状态
5. **调试建议**:
   - 使用单个模式测试特定流转单的业务流程
   - 检查数据库中的流程配置数据（T_FLOW_ORDER表）
   - 验证公司执行顺序配置（T_FLOW_NODE表）
   - 查看 Temporal Web UI 确认工作流状态
   - 检查API接口返回的数据格式和内容