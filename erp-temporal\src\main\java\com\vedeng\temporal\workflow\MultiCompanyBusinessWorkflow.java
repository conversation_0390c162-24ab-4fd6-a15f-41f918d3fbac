package com.vedeng.temporal.workflow;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;

/**
 * 多公司业务流程工作流接口
 * 定义多公司间业务流程自动化的工作流程
 * 
 * <p>
 * 基于 Temporal 工作流引擎，实现跨公司的业务流程自动化处理。
 * 通过并行执行多个业务流程，提高整体处理效率和可靠性。
 * </p>
 * 
 * <p>
 * 执行架构：
 * - 符合 Temporal 最佳实践：Workflow 负责编排，具体业务委托给服务层
 * - 移除了违规的"流程型 Activity"，使用原子 Activity 和业务流程服务
 * - 通过并行执行提高处理效率
 * </p>
 *
 * <AUTHOR>
 * @version 8.0 (销售采购订单流程独立版本)
 * @since 2025-06-20
 */
@WorkflowInterface
public interface MultiCompanyBusinessWorkflow {

    /**
     * 执行多公司业务流程（并行执行模式）
     *
     * <p>
     * 并行执行六个业务流程：
     * 1. 销售订单流程：委托给 SalesOrderProcess，处理销售订单创建和状态更新
     * 2. 采购订单流程：委托给 PurchaseOrderProcess，处理采购订单创建和状态更新
     * 3. 库存入库流程：委托给 InventoryReceiptProcess，处理库存入库业务
     * 4. 同行单流程：委托给 PeerListProcess，处理同行单据流转
     * 5. 发票录入流程：委托给 InvoiceEntryProcess，处理发票录入业务
     * 6. 付款传递流程：委托给 PaymentTransferProcess，处理付款流转业务
     * </p>
     *
     * <p>
     * 执行特点：
     * - 六个流程同时开始执行，提高并发处理能力
     * - 各流程内部会自动检查和等待必要的前置条件
     * - 基于公司执行顺序配置进行跨公司业务处理
     * - 所有流程都成功才算整体成功，任一失败则整体失败
     * - 符合 Temporal 最佳实践：Workflow 编排，Service 执行
     * </p>
     *
     * <p>
     * 数据来源：
     * - 从 T_FLOW_ORDER 表获取流转单配置
     * - 从 T_FLOW_NODE 表获取公司执行顺序
     * - 通过 SystemApiClient 调用各公司的业务接口
     * </p>
     *
     * @param request 业务请求对象，包含流转单ID、业务类型等信息
     * @return 流程执行结果，包含成功状态、消息和详细信息
     */
    @WorkflowMethod
    CompanyBusinessResponse executeMultiCompanyBusiness(CompanyBusinessRequest request);


}
