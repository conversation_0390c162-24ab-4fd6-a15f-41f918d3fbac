package com.vedeng.temporal.config;

import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * Activity 配置管理器
 * 统一管理所有 Activity 的配置选项
 * 支持 Spring 容器注入和手动构造两种方式
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-03
 */
@Component
@Slf4j
public class ActivityConfigManager {

    @Autowired(required = false)
    private TemporalProperties temporalProperties;

    /**
     * 默认构造函数，用于 Spring 容器注入
     */
    public ActivityConfigManager() {
        // Spring 容器会自动注入 temporalProperties
    }

    /**
     * 手动构造函数，用于 Temporal Activity 环境
     * 在 Temporal Activity 中，Spring 的 @Autowired 不会生效，需要手动注入依赖
     *
     * @param temporalProperties Temporal 配置属性
     */
    public ActivityConfigManager(TemporalProperties temporalProperties) {
        this.temporalProperties = temporalProperties;
        log.debug("手动构造 ActivityConfigManager，配置: {}", temporalProperties != null ? "已设置" : "未设置");
    }
    
    /**
     * 获取标准的 Activity 配置选项
     * 适用于大部分业务 Activity
     *
     * @return ActivityOptions
     * @throws IllegalStateException 如果配置未正确初始化
     */
    public ActivityOptions getActivityOptions() {
        validateConfiguration();

        ActivityOptions options = ActivityOptions.newBuilder()
                .setStartToCloseTimeout(temporalProperties.getActivity().getStartToCloseTimeout())
                .setRetryOptions(RetryOptions.newBuilder()
                        .setMaximumAttempts(temporalProperties.getActivity().getRetryMaximumAttempts())
                        .setInitialInterval(temporalProperties.getActivity().getRetryInitialInterval())
                        .setMaximumInterval(temporalProperties.getActivity().getRetryMaximumInterval())
                        .setBackoffCoefficient(temporalProperties.getActivity().getRetryBackoffCoefficient())
                        .build())
                .build();

        log.debug("创建标准Activity配置，超时: {}, 最大重试: {}",
                temporalProperties.getActivity().getStartToCloseTimeout(),
                temporalProperties.getActivity().getRetryMaximumAttempts());

        return options;
    }
    

    /**
     * 获取快速 Activity 的配置选项
     * 适用于简单查询类 Activity，如 CompanySequenceActivity
     * 
     * @return ActivityOptions
     */
    public ActivityOptions getQuickActivityOptions() {
        ActivityOptions options = ActivityOptions.newBuilder()
                .setStartToCloseTimeout(Duration.ofMinutes(5)) // 较短的超时时间
                .setRetryOptions(RetryOptions.newBuilder()
                        .setMaximumAttempts(2) // 较少的重试次数
                        .setInitialInterval(Duration.ofSeconds(5))
                        .setMaximumInterval(Duration.ofSeconds(30))
                        .setBackoffCoefficient(2.0)
                        .build())
                .build();
        
        log.debug("创建快速Activity配置，超时: 5分钟, 最大重试: 2次");
        
        return options;
    }


    // ==================== 配置验证方法 ====================

    /**
     * 验证配置是否正确初始化
     *
     * @throws IllegalStateException 如果配置未正确初始化
     */
    private void validateConfiguration() {
        if (temporalProperties == null) {
            throw new IllegalStateException("TemporalProperties 未初始化，请确保正确注入或手动设置配置");
        }

        // 验证关键配置字段
        try {
            temporalProperties.getActivity().getStartToCloseTimeout();
            temporalProperties.getActivity().getRetryMaximumAttempts();
            temporalProperties.getActivity().getRetryInitialInterval();
            temporalProperties.getActivity().getRetryBackoffCoefficient();
        } catch (Exception e) {
            throw new IllegalStateException("TemporalProperties 配置无效: " + e.getMessage(), e);
        }
    }

    /**
     * 创建默认的 ActivityConfigManager 实例
     * 用于在 Temporal Activity 环境中创建配置管理器
     * 优先从 Spring 上下文动态获取最新配置，支持 Apollo 动态更新
     *
     * @return 配置了默认值的 ActivityConfigManager 实例
     */
    @JsonIgnore
    public static ActivityConfigManager createDefault() {
        TemporalProperties properties;

        try {
            // 优先尝试从 Spring 上下文获取最新的 TemporalProperties Bean
            // 这样可以确保获取到 Apollo 动态更新后的最新配置
            properties = SpringUtil.getBean(TemporalProperties.class);
            log.info("成功从 Spring 上下文获取 TemporalProperties，支持 Apollo 动态配置");

        } catch (Exception e) {
            // 如果 Spring 上下文不可用，使用硬编码默认值作为降级方案
            log.warn("无法从 Spring 上下文获取 TemporalProperties，使用硬编码默认值作为降级方案: {}", e.getMessage());
            properties = createHardcodedDefaultProperties();
        }

        return new ActivityConfigManager(properties);
    }

    /**
     * 创建硬编码默认配置的 TemporalProperties
     * 用作 Spring 上下文不可用时的降级方案
     *
     * @return 配置了硬编码默认值的 TemporalProperties 实例
     */
    private static TemporalProperties createHardcodedDefaultProperties() {
        TemporalProperties properties = new TemporalProperties();

        // 手动设置 Activity 配置默认值
        properties.setActivityStartToCloseTimeoutMinutes(30);  // 30分钟
        properties.setActivityRetryMaximumAttempts(3);         // 3次重试
        properties.setActivityRetryInitialIntervalSeconds(10); // 10秒间隔
        properties.setActivityRetryBackoffCoefficient(2.0);    // 2.0退避系数

        // 手动设置轮询配置默认值
        properties.setPollingEnabled(true);
        properties.setPollingInitialIntervalSeconds(30);
        properties.setPollingMaxIntervalSeconds(300);
        properties.setPollingBackoffCoefficient(1.5);
        properties.setPollingMaxTimeoutDays(7);
        properties.setPollingHeartbeatIntervalMinutes(5);
        properties.setPollingMaxRetryCount(10);
        properties.setPollingEnableVerboseLogging(false);

        log.debug("创建硬编码默认 TemporalProperties 配置");
        return properties;
    }
}
