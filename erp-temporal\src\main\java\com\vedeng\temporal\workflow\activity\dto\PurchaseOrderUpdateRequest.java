package com.vedeng.temporal.workflow.activity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购订单更新请求对象
 * 
 * 用于封装采购订单更新操作的所有参数，与前端传递的参数结构保持一致
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-21
 */
@Data
public class PurchaseOrderUpdateRequest {
    
    /**
     * 是否新建（1=新建，0=更新）
     */
    private Integer isNew;
    
    /**
     * 采购订单号
     */
    private String buyorderNo;
    
    /**
     * 直接交货（0=否，1=是）
     */
    private Integer deliveryDirect;
    
    /**
     * 采购订单ID（必需字段）
     */
    private Long buyorderId;
    
    /**
     * 订单状态
     */
    private Integer status;
    
    /**
     * 要删除的商品ID列表（逗号分隔）
     */
    private String delBuyGoodsIds;
    
    /**
     * 订单类型
     */
    private Integer orderType;
    
    /**
     * 是否礼品（0=否，1=是）
     */
    private Integer isGift;
    
    /**
     * 供应商ID
     */
    private Long traderId;
    
    /**
     * 供应商名称
     */
    private String traderName;
    
    /**
     * 供应商备注
     */
    private String traderComments;
    
    /**
     * 商品清单
     */
    private List<PurchaseOrderGoods> goodsList;
    
    /**
     * 采购费用ID
     */
    private String buyorderExpenseId;
    
    /**
     * 付款方式
     */
    private Integer paymentType;
    
    /**
     * 银行承兑
     */
    private String bankAcceptance;
    
    /**
     * 预付金额
     */
    private String prepaidAmount;
    
    /**
     * 账期金额
     */
    private Integer accountPeriodAmount;
    
    /**
     * 是否有账期（0=否，1=是）
     */
    private Integer haveAccountPeriod;
    
    /**
     * 保证金金额
     */
    private Integer retainageAmount;
    
    /**
     * 保证金期限（月）
     */
    private String retainageAmountMonth;
    
    /**
     * 付款备注
     */
    private String paymentComments;
    
    /**
     * 发票类型
     */
    private Integer invoiceType;
    
    /**
     * 发票备注
     */
    private String invoiceComments;
    
    /**
     * 物流ID
     */
    private Integer logisticsId;
    
    /**
     * 运费说明
     */
    private Integer freightDescription;
    
    /**
     * 物流备注
     */
    private String logisticsComments;
    
    /**
     * 备注
     */
    private String comments;
    
    // 实际操作字段（Act结尾）
    
    /**
     * 实际供应商ID
     */
    private String traderIdAct;
    
    /**
     * 实际供应商名称
     */
    private String traderNameAct;
    
    /**
     * 实际付款方式
     */
    private Integer paymentTypeAct;
    
    /**
     * 实际预付金额
     */
    private String prepaidAmountAct;
    
    /**
     * 实际账期金额
     */
    private Integer accountPeriodAmountAct;
    
    /**
     * 实际保证金金额
     */
    private Integer retainageAmountAct;
    
    /**
     * 实际保证金期限（月）
     */
    private String retainageAmountMonthAct;
    
    /**
     * 实际发票类型
     */
    private Integer invoiceTypeAct;
    
    /**
     * 供应商联系人信息字符串（格式：contactId|name|mobile|telephone）
     */
    private String traderContactStr;
    
    /**
     * 供应商地址信息字符串（格式：addressId|area|address）
     */
    private String traderAddressStr;
    
    /**
     * 采购订单商品信息
     */
    @Data
    public static class PurchaseOrderGoods {
        
        /**
         * 采购订单商品ID
         */
        private Long buyorderGoodsId;
        
        /**
         * 返利金额
         */
        private Integer rebateAmount;
        
        /**
         * 返利价格
         */
        private Integer rebatePrice;
        
        /**
         * 返利后价格
         */
        private Integer rebateAfterPrice;
        
        /**
         * 返利数量
         */
        private String rebateNum;
        
        /**
         * 商品名称
         */
        private String goodsName;
        
        /**
         * 实际采购价格
         */
        private Integer actualPurchasePrice;
        
        /**
         * 是否礼品（0=否，1=是）
         */
        private Integer isGift;
        
        /**
         * 内部备注
         */
        private String insideComments;
        
        /**
         * 交期周期
         */
        private String deliveryCycle;
        
        /**
         * 是否有授权（0=否，1=是）
         */
        private Integer isHaveAuth;
        
        /**
         * 安装信息
         */
        private String installation;
        
        /**
         * 商品备注
         */
        private String goodsComments;
        
        /**
         * 预计发货时间
         */
        private Long sendGoodsTime;
        
        /**
         * 预计收货时间
         */
        private Long receiveGoodsTime;
        
        /**
         * 参考价格
         */
        private BigDecimal referPrice;
    }
}