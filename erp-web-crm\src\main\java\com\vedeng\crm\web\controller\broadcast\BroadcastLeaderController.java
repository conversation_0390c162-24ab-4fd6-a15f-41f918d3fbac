package com.vedeng.crm.web.controller.broadcast;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.broadcast.service.BroadStatisticsService;
import com.vedeng.erp.broadcast.StatisticDataDto;

/**
 * <b>Description:</b><br> 企微群到款播报
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <b>PackageName:</b> com.vedeng.crm.web.controller.profile
 * <b>ClassName:</b> CrmProfileBroadcastController
 * <b>Date:</b> 2025/5/30
 */
@Controller
@RequestMapping("/broadcast")
public class BroadcastLeaderController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(BroadcastLeaderController.class);

    @Autowired
    private BroadStatisticsService broadStatisticsService;
    
    @Value("${leaderboard_img_url}")
    private String leaderboardImgUrl;

    /**
     * <b>Description:</b><br> 到款播报排行榜
     * @param request
     * @return
     * @Note
     * <b>Author:</b> vedeng
     * <b>Date:</b> 2025/5/30
     */
    @RequestMapping(value="/leaderboard")
    public ModelAndView leaderboard(HttpServletRequest request){
        ModelAndView mv = new ModelAndView();
        mv.setViewName("vue/view/crm/profile/broadcast/index");
        mv.addObject("leaderboardImgUrl", leaderboardImgUrl);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("broadcast leaderboard currentUser id: {}, username: {}", currentUser.getId(), currentUser.getUsername());
        return mv;
    }
    
    /**
     * <b>Description:</b><br> 到款播报排行榜
     * @param request
     * @return
     * @Note
     * <b>Author:</b> vedeng
     * <b>Date:</b> 2025/5/30
     */
    @RequestMapping(value="/statisticData")
    @ResponseBody
    public R<StatisticDataDto> statisticData(HttpServletRequest request){
    	//返回的数据
    	try {
    		StatisticDataDto  statisticDataDto  = broadStatisticsService.getBraodCastStatisticData();
        	return R.success(statisticDataDto);
    	}catch(Exception e) {
    		log.error("到款播报排行榜数据获取异常",e);
    		return R.error("到款播报排行榜数据获取异常");
    	}
    	
    	
    }
} 