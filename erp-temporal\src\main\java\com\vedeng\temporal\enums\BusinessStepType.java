package com.vedeng.temporal.enums;

/**
 * 业务步骤类型枚举
 * 用于精确标识和追踪各种业务处理步骤
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20
 */
public enum BusinessStepType {
    
    // ========== 采购-销售-库存流程步骤 ==========
    
    /**
     * 销售订单生成
     */
    SALES_ORDER("SALES_ORDER", "销售订单生成", ""),
    
    /**
     * 采购订单生成
     */
    PURCHASE_ORDER("PURCHASE_ORDER", "采购订单生成", ""),
    
    /**
     * 库存入库单生成
     */
    INVENTORY_RECEIPT("INVENTORY_RECEIPT", "库存入库单生成", ""),
    
    /**
     * 同行单处理
     */
    PASSAGE_RECEIPT("PASSAGE_RECEIPT", "同行单处理", ""),
    
    // ========== 发票录入流程步骤 ==========
    
    /**
     * 销售发票开票
     */
    SALES_INVOICE("SALES_INVOICE", "销售发票开票", ""),
    
    /**
     * 发票录入
     */
    INVOICE_ENTRY("INVOICE_ENTRY", "发票录入", ""),
    
    // ========== 付款转账流程步骤 ==========
    
    /**
     * 付款单生成
     */
    PAYMENT_ORDER("PAYMENT_ORDER", "付款单生成", ""),
    
    /**
     * 资金转账
     */
    FUND_TRANSFER("FUND_TRANSFER", "资金转账", ""),
    
    // ========== 流程级别步骤 ==========

    /**
     * 基础步骤完成（销售单+采购单+入库单）
     */
    BASIC_STEPS_COMPLETED("BASIC_STEPS_COMPLETED", "基础步骤完成", "采购-销售-库存流程的所有基础步骤已完成"),

    /**
     * 发票流程完成
     */
    INVOICE_PROCESS_COMPLETED("INVOICE_PROCESS_COMPLETED", "发票流程完成", "发票录入流程的所有步骤已完成"),

    /**
     * 付款流程完成
     */
    PAYMENT_PROCESS_COMPLETED("PAYMENT_PROCESS_COMPLETED", "付款流程完成", "付款转账流程的所有步骤已完成"),

    /**
     * 公司业务完成（所有流程）
     */
    COMPANY_BUSINESS_COMPLETED("COMPANY_BUSINESS_COMPLETED", "公司业务完成", "指定公司的所有业务流程处理完成");
    
    /**
     * 步骤代码
     */
    private final String code;
    
    /**
     * 步骤名称
     */
    private final String name;
    
    /**
     * 步骤描述
     */
    private final String description;
    
    BusinessStepType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举值
     */
    public static BusinessStepType fromCode(String code) {
        for (BusinessStepType type : BusinessStepType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的业务步骤类型代码: " + code);
    }
    
    /**
     * 判断是否为基础业务步骤（采购-销售-库存流程）
     */
    public boolean isBasicStep() {
        return this == SALES_ORDER || this == PURCHASE_ORDER || this == INVENTORY_RECEIPT;
    }
    
    /**
     * 判断是否为发票相关步骤
     */
    public boolean isInvoiceStep() {
        return this == SALES_INVOICE || this == INVOICE_ENTRY;
    }
    
    /**
     * 判断是否为付款相关步骤
     */
    public boolean isPaymentStep() {
        return this == PAYMENT_ORDER || this == FUND_TRANSFER;
    }
    
    /**
     * 判断是否为流程完成标记
     */
    public boolean isProcessCompletion() {
        return this == BASIC_STEPS_COMPLETED ||
               this == INVOICE_PROCESS_COMPLETED ||
               this == PAYMENT_PROCESS_COMPLETED ||
               this == COMPANY_BUSINESS_COMPLETED;
    }
    
    @Override
    public String toString() {
        return String.format("%s(%s)", name, code);
    }
}
