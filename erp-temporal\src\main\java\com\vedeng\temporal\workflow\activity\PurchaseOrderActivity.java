package com.vedeng.temporal.workflow.activity;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.exception.BusinessProcessException;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

/**
 * 采购订单Activity接口
 * 
 * 设计理念：
 * - 将PurchaseOrderFlowV2中的子步骤拆分为独立的Activity方法
 * - 支持细粒度重试，失败时只重试失败的步骤
 * - 采购团队独立维护，与其他业务Activity完全独立
 * 
 * 核心功能：
 * - 采购订单创建：生成采购订单并返回订单ID
 * - 提交审核：将订单提交到审核流程
 * - 执行审批：完成订单的审批操作
 * 
 * 重试策略：
 * - 创建操作：重试3次，适合网络异常恢复
 * - 审批操作：重试1次，避免重复审批
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-17
 */
@ActivityInterface
public interface PurchaseOrderActivity {
    
    /**
     * 创建采购订单
     * 
     * 功能说明：
     * - 根据业务请求创建采购订单
     * - 返回生成的订单ID
     * - 支持幂等性，重试时不会重复创建
     * 
     * 重试策略：
     * - 最大重试次数：3次
     * - 初始间隔：10秒
     * - 退避系数：2.0
     * 
     * @param request 业务请求，包含订单创建所需的数据
     * @return 创建结果，成功时包含订单ID
     */
    @ActivityMethod
    CompanyBusinessResponse createPurchaseOrder(CompanyBusinessRequest request) throws BusinessProcessException;

    /**
     * 更新采购订单
     *
     * 功能说明：
     * - 更新已存在的采购订单信息
     * - 支持更新供应商、金额、交货日期等关键字段
     * - 支持幂等性，重试时不会产生副作用
     *
     * 重试策略：
     * - 最大重试次数：1次
     * - 初始间隔：5秒
     * - 避免重复更新
     *
     * @param request 业务请求，包含要更新的订单ID和更新数据
     * @return 更新结果，成功时返回更新后的订单状态
     */
    @ActivityMethod
    CompanyBusinessResponse updatePurchaseOrder(CompanyBusinessRequest request) throws BusinessProcessException;
    
    /**
     * 提交采购订单审核
     * 
     * 功能说明：
     * - 将已创建的采购订单提交到审核流程
     * - 需要订单处于可提交状态
     * - 支持幂等性，重复提交不会产生副作用
     * 
     * 重试策略：
     * - 最大重试次数：1次
     * - 初始间隔：5秒
     * 
     * @param request 业务请求，包含要提交审核的订单信息
     * @return 提交结果，成功时订单状态变为待审核
     */
    @ActivityMethod
    CompanyBusinessResponse submitPurchaseOrderForApproval(CompanyBusinessRequest request) throws BusinessProcessException;
    
    /**
     * 执行采购订单审批
     * 
     * 功能说明：
     * - 对待审核的采购订单执行审批操作
     * - 需要订单处于待审核状态
     * - 审批通过后订单状态变为已审批
     * 
     * 重试策略：
     * - 最大重试次数：1次
     * - 初始间隔：5秒
     * - 避免重复审批
     * 
     * @param request 业务请求，包含要审批的订单信息
     * @return 审批结果，成功时订单状态变为已审批
     */
    @ActivityMethod
    CompanyBusinessResponse approvePurchaseOrder(CompanyBusinessRequest request) throws BusinessProcessException;
    
    /**
     * 创建FlowOrderInfo记录
     * 
     * 功能说明：
     * - 在采购订单审核成功后创建对应的FlowOrderInfo记录
     * - 查询获取采购单号作为业务单号传入FlowOrderInfo
     * - 支持幂等性，避免重复创建
     * 
     * 重试策略：
     * - 最大重试次数：2次
     * - 初始间隔：5秒
     * - 支持网络异常恢复
     * 
     * @param request 业务请求，包含订单ID和公司信息
     * @return 创建结果，成功时返回FlowOrderInfo创建状态
     */
    @ActivityMethod
    CompanyBusinessResponse createFlowOrderInfoRecord(CompanyBusinessRequest request) throws BusinessProcessException;

    /**
     * 检查是否存在下一个流程节点
     * 
     * 功能说明：
     * - 检查当前公司是否在流程中存在下一个节点
     * - 用于判断当公司是最后一个时是否应该跳过生成采购单
     * - 通过FlowNodeBasedCompanyService获取流程节点信息
     * 
     * @param request 业务请求，包含业务ID和当前公司信息
     * @return 如果存在下一个节点返回true，否则返回false
     */
    @ActivityMethod
    boolean hasNextFlowNode(CompanyBusinessRequest request) throws BusinessProcessException;

}
