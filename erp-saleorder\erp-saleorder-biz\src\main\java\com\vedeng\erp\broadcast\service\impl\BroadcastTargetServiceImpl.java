package com.vedeng.erp.broadcast.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.broadcast.domain.dto.*;
import com.vedeng.erp.broadcast.domain.entity.BroadcastTargetEntity;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastTargetMapper;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity;
import com.vedeng.erp.broadcast.service.BroadcastTargetService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 业绩目标管理服务实现
 */
@Slf4j
@Service
public class BroadcastTargetServiceImpl implements BroadcastTargetService {

    @Autowired
    private BroadcastTargetMapper broadcastTargetMapper;

    @Autowired
    private BroadcastDeptMapper broadcastDeptMapper;

    @Autowired
    private UserApiService userApiService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BroadcastTargetImportResultDto importExcel(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("请选择要导入的文件");
        }

        // 验证文件格式
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename) || !originalFilename.toLowerCase().endsWith(".xlsx")) {
            throw new ServiceException("请上传Excel文件（.xlsx格式）");
        }

        BroadcastTargetImportResultDto result = BroadcastTargetImportResultDto.builder()
                .totalCount(0)
                .successCount(0)
                .failCount(0)
                .successRecords(new ArrayList<>())
                .failRecords(new ArrayList<>())
                .build();

        CurrentUser currentUser = CurrentUser.getCurrentUser();

        try {
            // 读取Sheet1：个人目标
            processSheet(file, 0, "个人目标", 1, result, currentUser);
            
            // 读取Sheet2：小组目标
            processSheet(file, 1, "小组目标", 2, result, currentUser);
            
            // 读取Sheet3：部门目标
            processSheet(file, 2, "部门目标", 3, result, currentUser);

        } catch (Exception e) {
            log.error("导入Excel文件失败", e);
            throw new ServiceException("导入失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            
            int currentYear = LocalDate.now().getYear();
            String fileName = URLEncoder.encode("业绩目标导入模板_" + currentYear, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 生成模板数据
            List<BroadcastTargetImportDto> personalTemplateData = generatePersonalTemplateData(currentYear);
            List<BroadcastTargetImportDto> groupTemplateData = generateGroupTemplateData(currentYear);
            List<BroadcastTargetImportDto> deptTemplateData = generateDeptTemplateData(currentYear);

            // 使用EasyExcel写入多个Sheet
            com.alibaba.excel.ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), BroadcastTargetImportDto.class).build();

            // 写入个人目标Sheet
            com.alibaba.excel.write.metadata.WriteSheet personalSheet = EasyExcel.writerSheet(0, "个人目标").build();
            excelWriter.write(personalTemplateData, personalSheet);

            // 写入小组目标Sheet
            com.alibaba.excel.write.metadata.WriteSheet groupSheet = EasyExcel.writerSheet(1, "小组目标").build();
            excelWriter.write(groupTemplateData, groupSheet);

            // 写入部门目标Sheet
            com.alibaba.excel.write.metadata.WriteSheet deptSheet = EasyExcel.writerSheet(2, "部门目标").build();
            excelWriter.write(deptTemplateData, deptSheet);

            // 关闭写入器
            excelWriter.finish();

        } catch (Exception e) {
            log.error("下载模板失败", e);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println("{\"status\":\"failure\",\"message\":\"下载模板失败：" + e.getMessage() + "\"}");
        }
    }

    @Override
    public Map<String, List<BroadcastTargetRespDto>> queryTargets(BroadcastTargetQueryDto queryDto) {
        if (queryDto.getTargetYear() == null) {
            throw new ServiceException("年度不能为空");
        }

        log.info("开始查询业绩目标，年度：{}，目标类型：{}，目标名称：{}",
                queryDto.getTargetYear(), queryDto.getTargetType(), queryDto.getTargetName());

        // 查询目标数据（targetType可以为空，表示查询所有类型）
        // 查询条件已包含 IS_DELETED=0，确保只查询有效数据
        List<BroadcastTargetEntity> entities = broadcastTargetMapper.selectTargetsWithName(
                queryDto.getTargetYear(),
                queryDto.getTargetType(),
                queryDto.getTargetName()
        );

        if (CollUtil.isEmpty(entities)) {
            log.info("未查询到符合条件的业绩目标数据");
            return new HashMap<>();
        }

        log.info("查询到{}条业绩目标记录", entities.size());

        // 按目标对象分组，合并12个月的数据
        Map<String, List<BroadcastTargetEntity>> groupedByTarget = entities.stream()
                .collect(Collectors.groupingBy(entity ->
                    entity.getTargetBuzId() + "_" + entity.getTargetName()));

        // 构建响应DTO列表
        List<BroadcastTargetRespDto> allTargets = new ArrayList<>();
        for (Map.Entry<String, List<BroadcastTargetEntity>> entry : groupedByTarget.entrySet()) {
            List<BroadcastTargetEntity> monthlyTargets = entry.getValue();
            if (CollUtil.isNotEmpty(monthlyTargets)) {
                BroadcastTargetRespDto respDto = buildTargetRespDto(monthlyTargets);
                allTargets.add(respDto);
            }
        }

        // 按targetType分组返回结果
        Map<String, List<BroadcastTargetRespDto>> result = allTargets.stream()
                .collect(Collectors.groupingBy(target -> getTargetTypeDescription(target.getTargetType())));

        log.info("业绩目标查询完成，返回{}个分组，总计{}条记录", result.size(), allTargets.size());
        return result;
    }

    @Override
    public List<Integer> getAvailableYears() {
        List<Integer> years = broadcastTargetMapper.selectAvailableYears();
        return years != null ? years : new ArrayList<>();
    }

    /**
     * 处理单个Sheet的数据
     */
    private void processSheet(MultipartFile file, int sheetIndex, String sheetName, 
                             Integer targetType, BroadcastTargetImportResultDto result, 
                             CurrentUser currentUser) throws IOException {
        
        AtomicInteger rowNum = new AtomicInteger(1);
        List<BroadcastTargetImportDto> sheetData = new ArrayList<>();

        try {
            // 读取指定Sheet的数据
            EasyExcel.read(file.getInputStream(), BroadcastTargetImportDto.class, 
                    new PageReadListener<BroadcastTargetImportDto>(dataList -> {
                        sheetData.addAll(dataList);
                    })).sheet(sheetIndex).doRead();

            if (CollUtil.isEmpty(sheetData)) {
                log.info("Sheet[{}]无数据，跳过处理", sheetName);
                return;
            }

            // 获取年度信息用于覆盖式导入
            Set<Integer> processedYears = new HashSet<>();

            // 处理每行数据
            for (BroadcastTargetImportDto importDto : sheetData) {
                rowNum.incrementAndGet();
                result.setTotalCount(result.getTotalCount() + 1);

                try {
                    processImportRow(importDto, targetType, sheetName, rowNum.get(), result, currentUser, processedYears);
                } catch (Exception e) {
                    log.error("处理Sheet[{}]第{}行数据失败", sheetName, rowNum.get(), e);
                    addFailRecord(result, sheetName, rowNum.get(), getTargetName(importDto, targetType),
                                 importDto.getTargetYear(), targetType, e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("读取Sheet[{}]失败", sheetName, e);
            throw new ServiceException("读取Sheet[" + sheetName + "]失败：" + e.getMessage());
        }
    }

    /**
     * 处理单行导入数据
     */
    private void processImportRow(BroadcastTargetImportDto importDto, Integer targetType,
                                 String sheetName, Integer rowNum,
                                 BroadcastTargetImportResultDto result, CurrentUser currentUser,
                                 Set<Integer> processedYears) {

        // 验证必填字段
        validateImportData(importDto, targetType);

        // 获取目标对象ID
        Integer targetBuzId = getTargetBuzId(importDto, targetType);
        String targetName = getTargetName(importDto, targetType);

        // 覆盖式导入：每个年度+目标类型组合只删除一次
        String yearTypeKey = importDto.getTargetYear() + "_" + targetType;
        if (!processedYears.contains(yearTypeKey.hashCode())) {
            broadcastTargetMapper.deleteByYearAndType(importDto.getTargetYear(), targetType, currentUser.getId());
            processedYears.add(yearTypeKey.hashCode());
            log.info("删除年度[{}]目标类型[{}]的历史数据", importDto.getTargetYear(), targetType);
        }

        // 准备插入的目标记录
        List<BroadcastTargetEntity> targetEntities = new ArrayList<>();
        int monthDataCount = 0;

        for (int month = 1; month <= 12; month++) {
            BigDecimal monthTarget = importDto.getMonthTarget(month);
            if (monthTarget != null && monthTarget.compareTo(BigDecimal.ZERO) > 0) {
                BroadcastTargetEntity entity = new BroadcastTargetEntity();
                entity.setTargetType(targetType);
                entity.setTargetBuzId(targetBuzId);
                entity.setTargetAmount(monthTarget);
                entity.setTargetMonth(month);
                entity.setTargetYear(importDto.getTargetYear());
                entity.setIsDeleted(0);
                entity.setCreator(currentUser.getId());
                entity.setAddTime(new Date());
                entity.setModTime(new Date());
                entity.setUpdater(currentUser.getId());
                
                targetEntities.add(entity);
                monthDataCount++;
            }
        }

        // 批量插入
        if (CollUtil.isNotEmpty(targetEntities)) {
            broadcastTargetMapper.batchInsert(targetEntities);
        }

        // 添加成功记录
        addSuccessRecord(result, sheetName, rowNum, targetName,
                        importDto.getTargetYear(), targetType, monthDataCount);
    }

    /**
     * 验证导入数据
     */
    private void validateImportData(BroadcastTargetImportDto importDto, Integer targetType) {
        if (importDto.getTargetYear() == null) {
            throw new ServiceException("年度不能为空");
        }

        // 验证年度格式（应该是4位数字）
        if (importDto.getTargetYear() < 2000 || importDto.getTargetYear() > 2100) {
            throw new ServiceException("年度格式不正确，应为2000-2100之间的年份");
        }

        // 根据目标类型验证对应的名称字段
        String targetName = getTargetName(importDto, targetType);
        if (StrUtil.isBlank(targetName)) {
            String fieldName = getTargetFieldName(targetType);
            throw new ServiceException(fieldName + "不能为空");
        }

        // 验证至少有一个月的目标数据
        boolean hasMonthData = false;
        for (int month = 1; month <= 12; month++) {
            BigDecimal monthTarget = importDto.getMonthTarget(month);
            if (monthTarget != null && monthTarget.compareTo(BigDecimal.ZERO) > 0) {
                hasMonthData = true;
                break;
            }
        }
        if (!hasMonthData) {
            throw new ServiceException("至少需要填写一个月的目标数据");
        }
    }

    /**
     * 获取目标业务ID
     */
    private Integer getTargetBuzId(BroadcastTargetImportDto importDto, Integer targetType) {
        String targetName = getTargetName(importDto, targetType);

        switch (targetType) {
            case 1: // 个人
                return getUserIdByName(targetName);
            case 2: // 小组
                return getGroupIdByName(targetName);
            case 3: // 部门
                return getDeptIdByName(targetName);
            default:
                throw new ServiceException("不支持的目标类型：" + targetType);
        }
    }

    /**
     * 获取目标名称
     */
    private String getTargetName(BroadcastTargetImportDto importDto, Integer targetType) {
        switch (targetType) {
            case 1: return importDto.getEmployeeName();
            case 2: return importDto.getGroupName();
            case 3: return importDto.getDeptName();
            default: return null;
        }
    }

    /**
     * 获取目标字段名称
     */
    private String getTargetFieldName(Integer targetType) {
        switch (targetType) {
            case 1: return "员工";
            case 2: return "小组";
            case 3: return "部门";
            default: return "目标对象";
        }
    }

    /**
     * 根据用户名获取用户ID
     */
    private Integer getUserIdByName(String username) {
        List<UserDto> users = userApiService.getAllUserNotDisabled(username);
        if (CollUtil.isEmpty(users)) {
            throw new ServiceException("用户不存在：" + username);
        }

        // 精确匹配用户名
        Optional<UserDto> exactMatch = users.stream()
                .filter(user -> username.equals(user.getUsername()))
                .findFirst();

        if (exactMatch.isPresent()) {
            return exactMatch.get().getUserId();
        } else {
            throw new ServiceException("用户不存在：" + username);
        }
    }

    /**
     * 根据小组名获取小组ID
     * 小组是parentId不为null的BroadcastDept记录
     */
    private Integer getGroupIdByName(String groupName) {
        List<BroadcastDeptEntity> groups = broadcastDeptMapper.selectByDeptName(groupName, 1); // 传入非null值表示查询小组
        if (CollUtil.isEmpty(groups)) {
            throw new ServiceException("小组不存在：" + groupName);
        }

        if (groups.size() > 1) {
            throw new ServiceException("存在多个同名小组：" + groupName);
        }

        return groups.get(0).getId();
    }

    /**
     * 根据部门名获取部门ID
     * 部门是parentId为null的BroadcastDept记录
     */
    private Integer getDeptIdByName(String deptName) {
        List<BroadcastDeptEntity> depts = broadcastDeptMapper.selectByDeptName(deptName, null); // 传入null表示查询顶级部门
        if (CollUtil.isEmpty(depts)) {
            throw new ServiceException("部门不存在：" + deptName);
        }

        if (depts.size() > 1) {
            throw new ServiceException("存在多个同名部门：" + deptName);
        }

        return depts.get(0).getId();
    }

    /**
     * 生成个人目标模板数据
     */
    private List<BroadcastTargetImportDto> generatePersonalTemplateData(int currentYear) {
        List<BroadcastTargetImportDto> templateData = new ArrayList<>();

        // 获取部分用户作为模板示例
        List<UserDto> users = userApiService.getAllUserNotDisabled("");
        if (CollUtil.isNotEmpty(users)) {
            // 取前10个用户作为示例
            users.stream().limit(10).forEach(user -> {
                BroadcastTargetImportDto dto = new BroadcastTargetImportDto();
                dto.setTargetYear(currentYear);
                dto.setEmployeeName(user.getUsername());
                // 设置示例数据
                dto.setMonth1(new BigDecimal("100000"));
                dto.setMonth2(new BigDecimal("100000"));
                templateData.add(dto);
            });
        }

        // 如果没有用户数据，添加空行作为模板
        if (templateData.isEmpty()) {
            BroadcastTargetImportDto dto = new BroadcastTargetImportDto();
            dto.setTargetYear(currentYear);
            dto.setEmployeeName("示例员工");
            dto.setMonth1(new BigDecimal("100000"));
            templateData.add(dto);
        }

        return templateData;
    }

    /**
     * 生成小组目标模板数据
     */
    private List<BroadcastTargetImportDto> generateGroupTemplateData(int currentYear) {
        List<BroadcastTargetImportDto> templateData = new ArrayList<>();

        // 获取小组数据作为模板示例
        List<BroadcastDeptEntity> groups = broadcastDeptMapper.selectAllGroups();
        if (CollUtil.isNotEmpty(groups)) {
            // 取前5个小组作为示例
            groups.stream().limit(5).forEach(group -> {
                BroadcastTargetImportDto dto = new BroadcastTargetImportDto();
                dto.setTargetYear(currentYear);
                dto.setGroupName(group.getDeptName());
                dto.setMonth1(new BigDecimal("500000"));
                dto.setMonth2(new BigDecimal("500000"));
                templateData.add(dto);
            });
        }

        // 如果没有小组数据，添加示例数据
        if (templateData.isEmpty()) {
            BroadcastTargetImportDto dto = new BroadcastTargetImportDto();
            dto.setTargetYear(currentYear);
            dto.setGroupName("示例小组");
            dto.setMonth1(new BigDecimal("500000"));
            templateData.add(dto);
        }

        return templateData;
    }

    /**
     * 生成部门目标模板数据
     */
    private List<BroadcastTargetImportDto> generateDeptTemplateData(int currentYear) {
        List<BroadcastTargetImportDto> templateData = new ArrayList<>();

        // 获取部门数据作为模板示例
        List<BroadcastDeptEntity> depts = broadcastDeptMapper.selectAllDepts();
        if (CollUtil.isNotEmpty(depts)) {
            // 取前5个部门作为示例
            depts.stream().limit(5).forEach(dept -> {
                BroadcastTargetImportDto dto = new BroadcastTargetImportDto();
                dto.setTargetYear(currentYear);
                dto.setDeptName(dept.getDeptName());
                dto.setMonth1(new BigDecimal("1000000"));
                dto.setMonth2(new BigDecimal("1000000"));
                templateData.add(dto);
            });
        }

        // 如果没有部门数据，添加示例数据
        if (templateData.isEmpty()) {
            BroadcastTargetImportDto dto = new BroadcastTargetImportDto();
            dto.setTargetYear(currentYear);
            dto.setDeptName("示例部门");
            dto.setMonth1(new BigDecimal("1000000"));
            templateData.add(dto);
        }

        return templateData;
    }

    /**
     * 构建目标响应DTO
     */
    private BroadcastTargetRespDto buildTargetRespDto(List<BroadcastTargetEntity> monthlyTargets) {
        if (CollUtil.isEmpty(monthlyTargets)) {
            return null;
        }

        BroadcastTargetEntity firstEntity = monthlyTargets.get(0);
        BroadcastTargetRespDto respDto = new BroadcastTargetRespDto();
        respDto.setTargetBuzId(firstEntity.getTargetBuzId());
        respDto.setTargetYear(firstEntity.getTargetYear());
        respDto.setTargetType(firstEntity.getTargetType());
        respDto.setAddTime(firstEntity.getAddTime());
        respDto.setModTime(firstEntity.getModTime());

        // 设置目标名称（从第一个实体获取）
        respDto.setTargetName(firstEntity.getTargetName());

        // 设置12个月的目标数据
        for (BroadcastTargetEntity entity : monthlyTargets) {
            if (entity.getTargetMonth() != null) {
                respDto.setMonthTarget(entity.getTargetMonth(), entity.getTargetAmount());
            }
        }

        return respDto;
    }

    /**
     * 添加成功记录
     */
    private void addSuccessRecord(BroadcastTargetImportResultDto result, String sheetName,
                                 Integer rowNum, String targetName, Integer targetYear,
                                 Integer targetType, Integer monthDataCount) {
        BroadcastTargetImportResultDto.ImportRecordDetail record =
                BroadcastTargetImportResultDto.ImportRecordDetail.builder()
                        .rowNum(rowNum)
                        .sheetName(sheetName)
                        .targetName(targetName)
                        .targetYear(targetYear)
                        .targetType(targetType)
                        .monthDataCount(monthDataCount)
                        .build();

        result.getSuccessRecords().add(record);
        result.setSuccessCount(result.getSuccessCount() + 1);
    }

    /**
     * 添加失败记录
     */
    private void addFailRecord(BroadcastTargetImportResultDto result, String sheetName,
                              Integer rowNum, String targetName, Integer targetYear,
                              Integer targetType, String errorMessage) {
        BroadcastTargetImportResultDto.ImportRecordDetail record =
                BroadcastTargetImportResultDto.ImportRecordDetail.builder()
                        .rowNum(rowNum)
                        .sheetName(sheetName)
                        .targetName(targetName)
                        .targetYear(targetYear)
                        .targetType(targetType)
                        .errorMessage(errorMessage)
                        .build();

        result.getFailRecords().add(record);
        result.setFailCount(result.getFailCount() + 1);
    }

    /**
     * 获取目标类型描述
     * 用于按targetType分组时的key值
     *
     * @param targetType 目标类型（1-个人，2-小组，3-部门）
     * @return 目标类型描述（person/group/department）
     */
    private String getTargetTypeDescription(Integer targetType) {
        if (targetType == null) {
            return "unknown";
        }

        switch (targetType) {
            case 1:
                return "person";
            case 2:
                return "group";
            case 3:
                return "department";
            default:
                return "unknown";
        }
    }
}
