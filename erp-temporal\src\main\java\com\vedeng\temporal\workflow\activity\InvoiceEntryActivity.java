package com.vedeng.temporal.workflow.activity;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.Map;

/**
 * 发票录入Activity接口
 * 
 * 设计理念：
 * - 将InvoiceEntryFlow中的业务方法拆分为独立的Activity方法
 * - 支持细粒度重试，失败时只重试失败的步骤
 * - 保持架构简单，统一包结构便于管理
 * 
 * 核心功能：
 * - 发票录入：执行完整的发票录入流程（查询详情 + 录入 + 审核）
 * - 发票详情查询：查询发票详情，用于流程控制
 * - 发票审核：执行发票审核操作
 * 
 * 业务流程：
 * 1. 前置检查：查询采购订单状态、供应商信息、发票规则
 * 2. 数据准备：组装发票数据、计算税额、验证业务规则
 * 3. 发票录入：调用录入接口、查询录入状态、等待录入完成
 * 4. 提交审核：检查提交条件、调用提交接口、查询提交状态
 * 5. 审核处理：检查审核权限、调用审核接口、查询最终状态
 * 
 * API接口：
 * - 发票详情查询：/api/v1/receiptInvoice/queryInvoiceDetail.do
 * - 发票录入：/api/v1/receiptInvoice/create.do
 * - 发票审核：/api/v1/receiptInvoice/approve.do
 * 
 * 重试策略：
 * - 创建操作：重试3次，适合网络异常恢复
 * - 查询操作：重试5次，适合临时性查询失败
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-21
 */
@ActivityInterface
public interface InvoiceEntryActivity {
    

    
    /**
     * 审核发票
     *
     * 功能说明：
     * - 对已录入的发票执行审核操作
     * - 支持自动审核通过
     * - 支持幂等性，重复审核不会产生副作用
     *
     * 重试策略：
     * - 最大重试次数：1次
     * - 初始间隔：5秒
     * - 避免重复审核
     *
     * @param request 业务请求，包含要审核的发票信息
     * @return 审核结果，成功时发票状态变为已审核
     */
    @ActivityMethod
    CompanyBusinessResponse approveInvoice(CompanyBusinessRequest request,Map<String, Object> invoiceDetailData);

    /**
     * 基于预查询的发票详情创建发票录入
     *
     * 功能说明：
     * - 使用预先查询好的发票详情数据创建发票录入
     * - 避免重复查询，提高效率
     * - 支持幂等性，重试时不会重复创建
     *
     * 重试策略：
     * - 最大重试次数：3次
     * - 初始间隔：10秒
     * - 退避系数：2.0
     *
     * @param request 业务请求
     * @param invoiceDetailData 发票详情查询结果数据
     * @return 创建结果，成功时包含发票ID
     */
    @ActivityMethod
    CompanyBusinessResponse createInvoiceWithDetails(CompanyBusinessRequest request,
                                                     Map<String, Object> invoiceDetailData);

    /**
     * 更新发票InvoiceHref字段
     *
     * 功能说明：
     * - 根据发票号和采购订单号更新t_Invoice表中的InvoiceHref字段
     * - 支持幂等性，重复更新不会产生副作用
     * - 业务逻辑是将发票链接信息保存到数据库
     *
     * 重试策略：
     * - 最大重试次数：3次
     * - 初始间隔：5秒
     * - 退避系数：2.0
     *
     * @param request 业务请求，需要包含发票号invoiceNo和采购订单号buyOrderNo
     * @return 更新结果，成功时InvoiceHref字段已更新
     */
    @ActivityMethod
    CompanyBusinessResponse updateInvoiceHref(CompanyBusinessRequest request);
    
    /**
     * 检查采购单录票完成状态
     * 
     * 功能说明：
     * - 检查采购单是否已完成所有发票的录入
     * - 基于采购单已录发票金额与销售单开票金额对比
     * - 用于循环录票的终止条件判断
     * 
     * 重试策略：
     * - 最大重试次数：3次
     * - 初始间隔：5秒
     * - 退避系数：2.0
     * 
     * @param request 业务请求，包含采购单号和销售单号信息
     * @return 录票状态信息，包括已录金额、开票金额、是否完成等
     */
    @ActivityMethod
    CompanyBusinessResponse checkPurchaseOrderRecordingStatus(CompanyBusinessRequest request);
    
    /**
     * 查询发票Href链接
     * 
     * 功能说明：
     * - 查询指定发票号的InvoiceHref字段值
     * - 用于检查发票链接是否已准备就绪
     * - 支持轮询等待InvoiceHref字段有值且不为空
     * 
     * 重试策略：
     * - 最大重试次数：3次
     * - 初始间隔：3秒
     * - 退避系数：2.0
     * 
     * @param request 业务请求，包含发票号
     * @return 查询结果，包含InvoiceHref字段信息
     */
    @ActivityMethod
    CompanyBusinessResponse queryInvoiceHref(CompanyBusinessRequest request);

    /**
     * 查询发票详情
     * 
     * 功能说明：
     * - 查询销售单的发票详情信息
     * - 用于获取发票录入所需的完整数据
     * - 支持基于销售单号和已录发票列表的查询
     * 
     * 重试策略：
     * - 最大重试次数：3次
     * - 初始间隔：5秒
     * - 退避系数：2.0
     * 
     * @param request 业务请求，包含销售单号和已录发票列表
     * @return 发票详情查询结果，包含发票商品信息等
     */
    @ActivityMethod
    CompanyBusinessResponse queryInvoiceDetail(CompanyBusinessRequest request);

    /**
     * 查询发票录入记录
     * 
     * 功能说明：
     * - 查询指定发票号在采购单中的录入记录
     * - 用于检查发票是否已经录入，避免重复录入
     * - 支持按采购单号和发票号精确查询
     * 
     * 重试策略：
     * - 最大重试次数：3次
     * - 初始间隔：3秒
     * - 退避系数：2.0
     * 
     * @param request 业务请求，包含采购单号和发票号
     * @return 查询结果，如果已录入则返回记录信息，否则返回空
     */
    @ActivityMethod
    CompanyBusinessResponse queryInvoiceRecord(CompanyBusinessRequest request);
}
