<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="/static/vue/vd-ui/common.css">
<link rel="stylesheet" href="/static/vue/vd-ui/ui.css">
<link rel="stylesheet" href="/static/vue/receiptnotice/list.css">
<link rel="stylesheet" href="/static/vue/receiptnotice/broadcast.css">
<title>播报管理</title>

<div class="user-container list-container" id="page-container">

    <div class="list-top">
        <h1 class="title">播报管理</h1>
        <div class="list-top-options">
            <ui-button width="100px" @click="showAddDialog">上传图片</ui-button>
            <ui-button width="100px" @click="openBroadcastEdit">播报配置</ui-button>
            <ui-button width="100px" @click="openDepartmentEdit">部门配置</ui-button>
        </div>
    </div>

    <div class="list-filter form-wrap">
        <div class="list-row">
            <div class="margin-r20 min-width250">
                <ui-form-item label="图片" label-width="72px">
                    <ui-input v-model="filter_picName" clearable></ui-input> 
                </ui-form-item>
            </div>
            <div class="margin-r20 min-width250">
                <ui-form-item label="专属类型" label-width="72px">
                    <ui-select 
                        :data="typeList" 
                        placeholder="全部"
                        v-model="filter_type" 
                        clearable
                    ></ui-select>
                </ui-form-item>
            </div>
            <div class="margin-r20">
                <ui-form-item label="专属目标" label-width="74px">
                    <ui-input v-model="filter_target" clearable></ui-input> 
                </ui-form-item>
            </div>
            <div class="filter-btns">
                <ui-button type="primary" width="100px" @click="search">搜索</ui-button>
                <ui-button type="link" class="margin-l10" width="100px" @click="reset">重置</ui-button>
            </div>
        </div>
    </div>

    <div class="list-wrap">
        <el-table 
            ref="userData" 
            :data="list" 
            border 
            :header-cell-style="{ textAlign: 'center', background: '#ECF1F5' }"
            :cell-style="{ textAlign: 'center' }"
            size="small"
            style="width: 100%"
        >
            <el-table-column prop="id" label="ID" width="100"></el-table-column>
            <el-table-column prop="username" label="图片" width="">
                <template slot-scope="scope">
                    <div class="td-img-wrap">
                        <img :src="scope.row.picUrl" alt="" @click="showBigImg(scope.row.picUrl)">
                        <div class="img-name">{{ scope.row.picName }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="exclusiveTypeName" label="专属类型" width="120"></el-table-column>
            <el-table-column prop="exclusiveTargetLabels" label="专属目标" width="200"></el-table-column>
            <el-table-column prop="creatorName" label="创建人" width="120"></el-table-column>
            <el-table-column prop="addTime" label="创建时间" width="200"></el-table-column>
            <el-table-column label="操作" width="150">
                <template slot-scope="scope">
                    <el-button @click.native.prevent="editItem(scope.row)" type="text" size="mini">编辑</el-button>
                    <el-button @click.native.prevent="deleteItem(scope.row, scope.$index)" type="text" size="mini">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            class="list-pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
        </el-pagination>
    </div>

    <ui-dialog
        title="上传图片"
        :visible.sync="isShowAddDialog"
        width="480px"
        align="center"
        scroll="in"
    >
        <div v-if="isShowAddDialog" class="upload-add-wrap">
            <ui-button @click="triggerAddUpload">点击上传</ui-button>
            <input type="file" name="" accept="image/*" ref="uploadAddTrigger" multiple style="display: none;" @change="uploadFile">
            <div class="upload-add-tips">
                -单次最多上传10张，超过10张取前10张<br>
                -单张图片不超过10MB，大于10MB会跳过该文件;<br>
                -仅支持: jpg, bmp, gif, png, jpeg, webp;
            </div>
        </div>
    </ui-dialog>

    <ui-dialog
        title="编辑"
        :visible.sync="isShowEditDialog"
        width="680px"
        align="center"
        scroll="in"
    >
        <div v-if="isShowEditDialog" class="form-wrap broadcast-edit-wrap label-width-2">
            <ui-form-item label="专属类型" :must="true">
                <ui-select
                    :data="typeList"
                    placeholder="请选择" 
                    v-model="edit_type"
                    @change="handlerEditTypeChange"
                ></ui-select>
            </ui-form-item>
            <ui-form-item label="专属目标" :must="true">
                <template v-if="edit_type == 1">
                    <ui-select :remote="true" placeholder="请选择" v-model="edit_target" :default-label="edit_targetLabel" clearable :remote-info="userRemoteInfo" @change="handlerEditTargetChange"></ui-select>
                </template>
                <template v-if="edit_type == 2">
                    <el-cascader
                        placeholder="请选择"
                        :options="departmentList"
                        :props="{ checkStrictly: true }"
                        size="small"
                        filterable
                        :show-all-levels="false"
                        style="width: 300px;"
                        v-model="edit_target"
                        ref="departmentSelect"
                        @change="handlerEditTargetChange"
                    >
                    </el-cascader>
                </template>
                <template v-if="edit_type == 3">
                    <ui-select :data="targetProjectOptions" placeholder="请选择" v-model="edit_target" clearable @change="handlerEditTargetChange"></ui-select>
                </template>
                <div class="vd-ui-input-error" v-if="editTargetErrorMsg">
                    <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i> 
                    <p class="vd-ui-input-error--errmsg">{{ editTargetErrorMsg }}</p>
                </div>
            </ui-form-item>
        </div>
        <template slot="footer">
            <div class="dlg-form-footer">
                <ui-button @click="submitEdit" type="primary">确定</ui-button>
                <ui-button @click="isShowEditDialog = false" class="close">取消</ui-button>
            </div>
        </template>
    </ui-dialog>
</div>

<script src="${pageContext.request.contextPath}/static/vue/vd-ui/ui.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/receiptnotice/broadcast.js?rnd=${resourceVersionKey}"></script>