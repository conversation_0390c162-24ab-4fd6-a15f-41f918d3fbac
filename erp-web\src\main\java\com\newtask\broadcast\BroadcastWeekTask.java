package com.newtask.broadcast;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.broadcast.statistics.project.BroadcastWeek;
import com.vedeng.erp.common.broadcast.param.TimePeriod;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;


import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@JobHandler(value = "broadcastWeekTask")
@Component
public class BroadcastWeekTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(BroadcastWeekTask.class);
    
    @Autowired
    private BroadcastWeek broadcastWeek;
    
    @Override
    public ReturnT<String> doExecute(String params) throws Exception {
        XxlJobLogger.log("到款周播报 start-----------");
        logger.info("到款周播报  start-----------");
        try {
        	TimePeriod timePeriod = getWeekTimeRange(params);
        	if(Objects.isNull(timePeriod)  && StringUtils.isEmpty(params)) {
        		broadcastWeek.invocation(null,true);
        	}else {
        		broadcastWeek.invocation(timePeriod,false);
        	}
        }catch(Exception e) {
        	XxlJobLogger.log("周到款播报失败");
        	logger.error("周到款播报失败");
        	return FAIL;
        }
        logger.info("到款周播报 end-----------");
        XxlJobLogger.log("到款周播报 end-----------");
        return SUCCESS;
    }
    
    public static TimePeriod getWeekTimeRange(String params) {
    	if(StringUtils.isEmpty(params)) {
    		return null;
    	}
        String[] parts = params.split("-");
        if (parts.length != 3) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        int year = Integer.parseInt(parts[0]);
        int month = Integer.parseInt(parts[1]) - 1;
        int day = Integer.parseInt(parts[2]);
        
        // 设置日期为给定日期
        cal.set(year, month, day);
        
        // 获取该周的周一和周日
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        int diffToMonday = (dayOfWeek == Calendar.SUNDAY) ? -6 : Calendar.MONDAY - dayOfWeek;
        cal.add(Calendar.DAY_OF_YEAR, diffToMonday);
        
        // 计算周一0点
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        Date weekStart = cal.getTime();
        
        // 计算周日23:59:59
        cal.add(Calendar.DAY_OF_YEAR, 6);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        Date weekEnd = cal.getTime();
        
        TimePeriod period = new TimePeriod(weekStart,weekEnd);
        return period;
    }
    
    public static void main(String[] args) {
    	TimePeriod result = getWeekTimeRange("2025-06-01");
    	System.out.println(JSON.toJSONString(result));
	}
}
