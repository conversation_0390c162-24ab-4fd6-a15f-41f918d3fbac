package com.vedeng.goodsSearch.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品搜索请求体
 *
 * <AUTHOR>
 */
@Data
public class GoodsSearchReqDto {

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 最小销售价格
     */
    private BigDecimal minSalePrice;

    /**
     * 最大销售价格
     */
    private BigDecimal maxSalePrice;

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 属性Id，逗号分隔
     */
    private String attributeId;

    /**
     * 属性值Id，逗号分隔
     */
    private String attributeValueId;

    /**
     * 机构Id，逗号分隔
     */
    private String institutionId;

    /**
     * 科室，逗号分隔
     */
    private String departmentId;

    /**
     * 商品等级，逗号分隔
     */
    private String goodsLevelId;

    /**
     * 商品类型
     */
    private Integer spuType;

    /**
     * 商品档位，逗号分隔
     */
    private String goodsPositionId;

    /**
     * 品牌性质，1国产，2进口
     */
    private Integer brandNatureId;

    /**
     * 是否已核价，1已核价，0未核价
     */
    private Integer hasVerifiedPrice;

    /**
     * 是否有库存，1有库存，0无库存
     */
    private Integer hasStock;

    /**
     * 是否已授权，1已授权，0未授权
     */
    private Integer hasAuthorized;

    /**
     * 是否支持安装，1支持安装，0不支持安装
     */
    private Integer isSupportInstallation;

    /**
     * 品牌Id 逗号隔开
     */
    private String brandId;

    /**
     * 页数
     */
    private Integer pageNo = 1;

    /**
     * 页面条数
     */
    private Integer pageSize = 30;

    /**
     * 自定义排序字段（es索引库字段名，见系统设计）  逗号分隔
     */
    private String sortColoum;

    /**
     * 0 ：升序 1:降序  逗号分隔
     */
    private String sortAsc;

    /**
     * 科室场景对应的分类ID，支持多个以逗号分割
     * -LXCRM 新增
     */
    private String skuSceneCategoryId;
    
    /**销售价是否含运费  1 不含运费*/
    private Integer saleNotContainsFee;
    
    /**成本价是否含运费  1 不含运费*/
    private Integer purchaseNotContainsFee;

    /**
     * 售后，逗号分隔
     */
    private String afterSalesServiceLevels;
}
