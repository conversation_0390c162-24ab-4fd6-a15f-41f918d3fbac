package com.vedeng.api.standard.core;

import java.io.Serializable;

/**
 * API统一响应格式类
 * 用于标准化所有API接口的响应格式
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public class ApiResponse<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 响应码，0表示成功，非0表示失败
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 请求ID，用于链路追踪
     */
    private String requestId;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 私有构造函数，防止直接实例化
     */
    private ApiResponse() {
        this.timestamp = System.currentTimeMillis();
    }
    
    /**
     * 私有构造函数
     */
    private ApiResponse(Integer code, String message, T data) {
        this();
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    /**
     * 成功响应，无数据
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(BaseResponseCode.SUCCESS.getCode(), BaseResponseCode.SUCCESS.getMessage(), null);
    }
    
    /**
     * 成功响应，带数据
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(BaseResponseCode.SUCCESS.getCode(), BaseResponseCode.SUCCESS.getMessage(), data);
    }
    
    /**
     * 成功响应，自定义消息
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(BaseResponseCode.SUCCESS.getCode(), message, data);
    }
    
    /**
     * 失败响应，使用默认错误码
     */
    public static <T> ApiResponse<T> error() {
        return new ApiResponse<>(BaseResponseCode.SYSTEM_BUSY.getCode(), BaseResponseCode.SYSTEM_BUSY.getMessage(), null);
    }
    
    /**
     * 失败响应，自定义消息
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(BaseResponseCode.OPERATION_ERROR.getCode(), message, null);
    }
    
    /**
     * 失败响应，自定义错误码和消息
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return new ApiResponse<>(code, message, null);
    }
    
    /**
     * 失败响应，使用响应码枚举
     */
    public static <T> ApiResponse<T> error(BaseResponseCode responseCode) {
        return new ApiResponse<>(responseCode.getCode(), responseCode.getMessage(), null);
    }
    
    /**
     * 失败响应，使用响应码枚举和自定义消息
     */
    public static <T> ApiResponse<T> error(BaseResponseCode responseCode, String customMessage) {
        return new ApiResponse<>(responseCode.getCode(), customMessage, null);
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return BaseResponseCode.SUCCESS.getCode().equals(this.code);
    }
    
    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
    
    // Getter and Setter methods
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    @Override
    public String toString() {
        return "ApiResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", requestId='" + requestId + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
