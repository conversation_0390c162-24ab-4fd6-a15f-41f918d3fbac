package com.vedeng.temporal.exception;

/**
 * 业务流程异常（精简版）
 *
 * 核心设计：
 * - 只保留最常用的4个字段，提高性能和易用性
 * - 简化静态工厂方法，提供清晰的API
 * - 支持业务上下文信息，便于问题诊断
 * - 基于重试策略的异常分类
 *
 * <AUTHOR> 4.0 sonnet
 * @version 5.0 (精简版)
 * @since 2025-01-26
 */
public class BusinessProcessException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final String errorCode;

    /**
     * 是否可重试（核心字段）
     */
    private final boolean retryable;

    /**
     * 业务上下文（合并原businessId、stepName、companyCode等信息）
     */
    private final String businessContext;
    /**
     * 主要构造函数
     */
    public BusinessProcessException(String message, String errorCode, boolean retryable, String businessContext) {
        super(message);
        this.errorCode = errorCode != null ? errorCode : "UNKNOWN_ERROR";
        this.retryable = retryable;
        this.businessContext = businessContext;
    }

    /**
     * 带原因的构造函数
     */
    public BusinessProcessException(String message, String errorCode, boolean retryable, 
                                  String businessContext, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode != null ? errorCode : "UNKNOWN_ERROR";
        this.retryable = retryable;
        this.businessContext = businessContext;
    }

    // ========== 静态工厂方法（简化版） ==========

    /**
     * 创建可重试异常（技术异常）
     */
    public static BusinessProcessException retryable(String message, String errorCode) {
        return new BusinessProcessException(message, errorCode, true, null);
    }

    /**
     * 创建可重试异常，带业务上下文
     */
    public static BusinessProcessException retryable(String message, String errorCode, String businessContext) {
        return new BusinessProcessException(message, errorCode, true, businessContext);
    }

    /**
     * 创建不可重试异常（业务异常）
     */
    public static BusinessProcessException nonRetryable(String message, String errorCode) {
        return new BusinessProcessException(message, errorCode, false, null);
    }

    /**
     * 创建不可重试异常，带业务上下文
     */
    public static BusinessProcessException nonRetryable(String message, String errorCode, String businessContext) {
        return new BusinessProcessException(message, errorCode, false, businessContext);
    }

    /**
     * 从原始异常创建业务异常
     */
    public static BusinessProcessException from(Throwable cause, String errorCode, boolean retryable) {
        String message = cause.getMessage() != null ? cause.getMessage() : cause.getClass().getSimpleName();
        return new BusinessProcessException(message, errorCode, retryable, null, cause);
    }

    // ========== Getter 方法 ==========

    public String getErrorCode() {
        return errorCode;
    }

    public boolean isRetryable() {
        return retryable;
    }

    public String getBusinessContext() {
        return businessContext;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("BusinessProcessException{");
        sb.append("message='").append(getMessage()).append('\'');
        sb.append(", errorCode='").append(errorCode).append('\'');
        sb.append(", retryable=").append(retryable);
        if (businessContext != null) {
            sb.append(", context='").append(businessContext).append('\'');
        }
        sb.append('}');
        return sb.toString();
    }
}