package com.vedeng.erp.broadcast.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 播报内容配置响应DTO
 */
@Getter
@Setter
public class BroadcastContentConfigRespDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 图片URL
     */
    private String picUrl;

    /**
     * 图片名称
     */
    private String picName;

    /**
     * 专属类型：1=个人，2=团队，3=项目
     */
    private Integer exclusiveType;

    /**
     * 专属类型名称
     */
    private String exclusiveTypeName;

    /**
     * 专属目标，逗号分隔：个人 用户ID; 部门 部门ID; 项目 1=月度AED TOP，2=月度自有品牌TOP
     */
    private String exclusiveTargetValues;

    /**
     * 用于搜索的文本，根据专属类型，专属目标保存相关文本
     */
    private String exclusiveTargetLabels;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 修改者
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;
}
