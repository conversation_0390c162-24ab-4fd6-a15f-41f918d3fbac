package com.vedeng.erp.common.broadcast.config;

import java.util.List;

import com.vedeng.erp.common.broadcast.MessageSubjectEnum;
import com.vedeng.erp.common.broadcast.StatDateRangeEnum;
import com.vedeng.erp.common.broadcast.StatisticsTypeEnum;

import lombok.Data;

/**
 * 全局配置 
 * @ClassName:  BroadcastGlobalConfigStatistic   
 * @author: <PERSON>.yang
 * @date:   2025年6月9日 上午9:17:04    
 * @Copyright:
 */
@Data
public class BroadcastGlobalConfigStatistics {

	/**过滤销售*/
    private List<String> excludeSaleIds;

    /**AED商品ID列表*/
    private List<String> aedSkuIds;

    /**过滤客户ID*/
    private List<String> excludeTraderIds;

    /**个人榜单TopN*/
    private Integer topnUser;

    /**团队榜单TopN*/
    private Integer topnDept;

    /**日常到款播报标题*/
    private String broadcastTitleDay;

    /**周度到款播报标题*/
    private String broadcastTitleWeek;

    /**月度到款播报标题*/
    private String broadcastTitleMonth;

    /**AED到款播报标题*/
    private String broadcastTitleAed;

    /**自有到款播报标题*/
    private String broadcastTitleZy;

    /**自定义到款播报标题*/
    private String broadcastTitleCustom;

    /**统计维度1.结款金额，2.出库数量 3.出库金额*/
    private StatisticsTypeEnum statisticsType;

    /**统计时间范围1.本日 2.本周 3.本月*/
    private StatDateRangeEnum statDateRange;

    /**播报对象1.个人 2.小组 3 部门*/
    private List<MessageSubjectEnum> messageSubjectList;

    /**统计商品ID列表*/
    private List<String> statSkuIds;

    /**统计品牌ID列表*/
    private List<String> statBrandIds;

    /**统计分类ID列表*/
    private List<String> statCategoryIds;
    
}
