<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.buyorder.mapper.BuyorderMapper" >
    	<!-- 在途商品信息 -->
	<select id="getBuyorderVoList" resultType="com.vedeng.erp.buyorder.dto.BuyOrderDto" parameterType="java.lang.Integer">
		SELECT
		*
		FROM
		(
		SELECT
		a.GOODS_ID,
		COALESCE (SUM((a.NUM - IFNULL(a.ARRIVAL_NUM,0) - IFNULL(TT.SHNUM,0))),0) ONWAYNUM,
		b.BUYORDER_NO,
		a.ESTIMATE_ARRIVAL_TIME,
		b.VALID_TIME,
		b.BUYORDER_ID
		FROM
		T_BUYORDER_GOODS a
		LEFT JOIN T_BUYORDER b ON a.BUYORDER_ID = b.BUYORDER_ID
		LEFT JOIN
		(
		SELECT
		IFNULL(SUM(d.NUM), 0) SHNUM,
		c.ORDER_ID
		FROM
		T_AFTER_SALES c
		LEFT JOIN T_AFTER_SALES_GOODS d ON c.AFTER_SALES_ID = d.AFTER_SALES_ID
		WHERE
		c.TYPE = 546
		AND d.GOODS_ID = #{skuId,jdbcType=INTEGER}
		AND c.VALID_STATUS = 1
		AND c.ATFER_SALES_STATUS = 2
		AND c.SUBJECT_TYPE = 536
		GROUP BY
		c.ORDER_ID
		) TT ON a.BUYORDER_ID = TT.ORDER_ID
		WHERE
		b.VALID_STATUS = 1
		AND b.PAYMENT_STATUS IN (1, 2)
		AND b.DELIVERY_DIRECT = 0
		AND b.ARRIVAL_STATUS IN (0, 1)
		AND b. STATUS != 3
		AND a.GOODS_ID = #{skuId,jdbcType=INTEGER}
		GROUP BY
		b.BUYORDER_ID
		) T
		WHERE
		T.ONWAYNUM > 0
	</select>

	<select id="getNeedEditBuyGoods" parameterType="java.lang.Integer" resultType="com.vedeng.erp.buyorder.domain.entity.PeerListBuyorderGoods">
		SELECT
			b.EXPRESS_DETAIL_ID,
			b.NUM DNUM,
		    b.NUM oldNum,
			c.BUYORDER_ID,
			c.BUYORDER_GOODS_ID,
			c.SKU,
			c.GOODS_ID,
			c.GOODS_NAME,
			(case e.SPU_TYPE
				 WHEN 316 THEN
					 c.MODEL
				 WHEN 1008 THEN
					 c.MODEL
				 ELSE c.SPEC END
				) as MODEL,
			c.UNIT_NAME,
			IFNULL(c.MANUFACTURER_NAME,h.MANUFACTURER_NAME) as MANUFACTURER_NAME,
			IFNULL(c.REGISTRATION_NUMBER,g.REGISTRATION_NUMBER) as REGISTRATION_NUMBER,
			e.MEDICAL_INSTRUMENT_CATALOG_INCLUDED,
			d.IS_MANAGE_VEDENG_CODE,
			d.IS_ENABLE_VALIDITY_PERIOD,
			e.SPU_TYPE,
			(case
				d.STORAGE_CONDITION_ONE
				 when 	4 THEN
					 CONCAT(d.STORAGE_CONDITION_ONE_LOWER_VALUE,'-',d.STORAGE_CONDITION_ONE_UPPER_VALUE,'℃')
				 when 	3 THEN
					 '冷藏2-10℃'
				 when 	2 THEN
					 '阴凉0-20℃'
				 when 	1 THEN
					 '常温0-30℃'
				 else '' end
				) as storageCondition,
		if(h.PRODUCT_COMPANY_LICENCE='/' or h.PRODUCT_COMPANY_LICENCE is null or h.PRODUCT_COMPANY_LICENCE = '',h.RECORD_CERTIFICATE_LICENCE,h.PRODUCT_COMPANY_LICENCE ) as PRODUCT_COMPANY_LICENCE
		FROM
			T_EXPRESS a
				LEFT JOIN T_EXPRESS_DETAIL b ON a.EXPRESS_ID = b.EXPRESS_ID
				LEFT JOIN T_BUYORDER_GOODS c ON c.BUYORDER_GOODS_ID = b.RELATED_ID
				left join V_CORE_SKU d on d.SKU_ID = c.GOODS_ID
				left join V_CORE_SPU e on e.SPU_ID = d.SPU_ID
				left join T_FIRST_ENGAGE f ON e.FIRST_ENGAGE_ID = f.FIRST_ENGAGE_ID
				left join T_REGISTRATION_NUMBER g ON g.REGISTRATION_NUMBER_ID = f.REGISTRATION_NUMBER_ID
				left join T_MANUFACTURER h on h.MANUFACTURER_ID = g.MANUFACTURER_ID
				INNER JOIN T_R_BUYORDER_J_SALEORDER i on  i.BUYORDER_GOODS_ID = b.RELATED_ID
		WHERE
			a.EXPRESS_ID = #{expressId}
		  AND c.IS_DELETE = 0
	</select>

	<select id="getDetailsByExpressDetailId" parameterType="java.lang.Integer" resultType="com.vedeng.erp.buyorder.domain.entity.PeerListBuyorderGoods">
		SELECT
			b.EXPRESS_DETAIL_ID,
			b.NUM DNUM,
			b.NUM oldNum,
			c.BUYORDER_ID,
			c.BUYORDER_GOODS_ID,
			c.SKU,
			c.GOODS_ID,
			c.GOODS_NAME,
			c.MODEL,
			c.UNIT_NAME,
			IFNULL(c.MANUFACTURER_NAME,h.MANUFACTURER_NAME) as MANUFACTURER_NAME,
			IFNULL(c.REGISTRATION_NUMBER,g.REGISTRATION_NUMBER) as REGISTRATION_NUMBER,
			e.MEDICAL_INSTRUMENT_CATALOG_INCLUDED,
			d.IS_MANAGE_VEDENG_CODE,
			d.IS_ENABLE_VALIDITY_PERIOD,
			e.SPU_TYPE,
			h.PRODUCT_COMPANY_LICENCE
		FROM
			T_EXPRESS_DETAIL b
				LEFT JOIN T_BUYORDER_GOODS c ON c.BUYORDER_GOODS_ID = b.RELATED_ID
				left join V_CORE_SKU d on d.SKU_ID = c.GOODS_ID
				left join V_CORE_SPU e on e.SPU_ID = d.SPU_ID
				left join T_FIRST_ENGAGE f ON e.FIRST_ENGAGE_ID = f.FIRST_ENGAGE_ID
				left join T_REGISTRATION_NUMBER g ON g.REGISTRATION_NUMBER_ID = f.REGISTRATION_NUMBER_ID
				left join T_MANUFACTURER h on h.MANUFACTURER_ID = g.MANUFACTURER_ID
				INNER JOIN T_R_BUYORDER_J_SALEORDER i on  i.BUYORDER_GOODS_ID = b.RELATED_ID
		<where>
				b.EXPRESS_DETAIL_ID in
				<foreach collection="list" close=")" open="(" separator="," item="expressDetailId">
					#{expressDetailId}
				</foreach>
			AND c.IS_DELETE = 0
		</where>

	</select>

	<resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.Buyorder">
		<id column="BUYORDER_ID" jdbcType="INTEGER" property="buyorderId" />
		<result column="BUYORDER_NO" jdbcType="VARCHAR" property="buyorderNo" />
		<result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
		<result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType" />
		<result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
		<result column="USER_ID" jdbcType="INTEGER" property="userId" />
		<result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus" />
		<result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
		<result column="STATUS" jdbcType="INTEGER" property="status" />
		<result column="LOCKED_STATUS" jdbcType="INTEGER" property="lockedStatus" />
		<result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus" />
		<result column="INVOICE_TIME" jdbcType="BIGINT" property="invoiceTime" />
		<result column="PAYMENT_STATUS" jdbcType="INTEGER" property="paymentStatus" />
		<result column="PAYMENT_TIME" jdbcType="BIGINT" property="paymentTime" />
		<result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus" />
		<result column="DELIVERY_TIME" jdbcType="BIGINT" property="deliveryTime" />
		<result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus" />
		<result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
		<result column="SERVICE_STATUS" jdbcType="INTEGER" property="serviceStatus" />
		<result column="HAVE_ACCOUNT_PERIOD" jdbcType="INTEGER" property="haveAccountPeriod" />
		<result column="DELIVERY_DIRECT" jdbcType="INTEGER" property="deliveryDirect" />
		<result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
		<result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
		<result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
		<result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
		<result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
		<result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile" />
		<result column="TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="traderContactTelephone" />
		<result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId" />
		<result column="TRADER_AREA" jdbcType="VARCHAR" property="traderArea" />
		<result column="TRADER_ADDRESS" jdbcType="VARCHAR" property="traderAddress" />
		<result column="TRADER_COMMENTS" jdbcType="VARCHAR" property="traderComments" />
		<result column="TAKE_TRADER_ID" jdbcType="INTEGER" property="takeTraderId" />
		<result column="TAKE_TRADER_NAME" jdbcType="VARCHAR" property="takeTraderName" />
		<result column="TAKE_TRADER_CONTACT_ID" jdbcType="INTEGER" property="takeTraderContactId" />
		<result column="TAKE_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="takeTraderContactName" />
		<result column="TAKE_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="takeTraderContactMobile" />
		<result column="TAKE_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="takeTraderContactTelephone" />
		<result column="TAKE_TRADER_ADDRESS_ID" jdbcType="INTEGER" property="takeTraderAddressId" />
		<result column="TAKE_TRADER_AREA" jdbcType="VARCHAR" property="takeTraderArea" />
		<result column="TAKE_TRADER_ADDRESS" jdbcType="VARCHAR" property="takeTraderAddress" />
		<result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType" />
		<result column="PREPAID_AMOUNT" jdbcType="DECIMAL" property="prepaidAmount" />
		<result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount" />
		<result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay" />
		<result column="RETAINAGE_AMOUNT" jdbcType="DECIMAL" property="retainageAmount" />
		<result column="RETAINAGE_AMOUNT_MONTH" jdbcType="INTEGER" property="retainageAmountMonth" />
		<result column="LOGISTICS_ID" jdbcType="INTEGER" property="logisticsId" />
		<result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
		<result column="FREIGHT_DESCRIPTION" jdbcType="INTEGER" property="freightDescription" />
		<result column="PAYMENT_COMMENTS" jdbcType="VARCHAR" property="paymentComments" />
		<result column="LOGISTICS_COMMENTS" jdbcType="VARCHAR" property="logisticsComments" />
		<result column="INVOICE_COMMENTS" jdbcType="VARCHAR" property="invoiceComments" />
		<result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
		<result column="ADDITIONAL_CLAUSE" jdbcType="VARCHAR" property="additionalClause" />
		<result column="STATUS_COMMENTS" jdbcType="INTEGER" property="statusComments" />
		<result column="SATISFY_DELIVERY_TIME" jdbcType="BIGINT" property="satisfyDeliveryTime" />
		<result column="ESTIMATE_ARRIVAL_TIME" jdbcType="VARCHAR" property="estimateArrivalTime" />
		<result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
		<result column="CREATOR" jdbcType="INTEGER" property="creator" />
		<result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
		<result column="UPDATER" jdbcType="INTEGER" property="updater" />
		<result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
		<result column="REAL_PAY_AMOUNT" jdbcType="DECIMAL" property="realPayAmount" />
		<result column="REAL_RETURN_AMOUNT" jdbcType="DECIMAL" property="realReturnAmount" />
		<result column="REAL_TOTAL_AMOUNT" jdbcType="DECIMAL" property="realTotalAmount" />
		<result column="NOPAYBACK_AMOUNT" jdbcType="DECIMAL" property="nopaybackAmount" />
		<result column="REAL_INVOICE_AMOUNT" jdbcType="DECIMAL" property="realInvoiceAmount" />
		<result column="IS_RISK" jdbcType="INTEGER" property="isRisk" />
		<result column="RISK_COMMENTS" jdbcType="VARCHAR" property="riskComments" />
		<result column="RISK_TIME" jdbcType="BIGINT" property="riskTime" />
		<result column="EXPEDITING_STATUS" jdbcType="INTEGER" property="expeditingStatus" />
		<result column="EXPEDITING_FOLLOW_STATUS" jdbcType="INTEGER" property="expeditingFollowStatus" />
		<result column="NEW_FLOW" jdbcType="INTEGER" property="newFlow" />
		<result column="SUB_STATUS" jdbcType="VARCHAR" property="subStatus" />
		<result column="VERIFY_STATUS" jdbcType="INTEGER" property="verifyStatus" />
		<result column="IS_NEW" jdbcType="TINYINT" property="isNew" />
		<result column="SALEORDER_NOS" jdbcType="VARCHAR" property="saleorderNos" />
		<result column="CONTRACT_URL" jdbcType="VARCHAR" property="contractUrl" />
		<result column="URGED_MAINTAIN_BATCH_INFO" jdbcType="TINYINT" property="urgedMaintainBatchInfo" />
	</resultMap>

	<sql id="Base_Column_List">
		BUYORDER_ID, BUYORDER_NO, COMPANY_ID, ORDER_TYPE, ORG_ID, USER_ID, VALID_STATUS,
    VALID_TIME, `STATUS`, LOCKED_STATUS, INVOICE_STATUS, INVOICE_TIME, PAYMENT_STATUS,
    PAYMENT_TIME, DELIVERY_STATUS, DELIVERY_TIME, ARRIVAL_STATUS, ARRIVAL_TIME, SERVICE_STATUS,
    HAVE_ACCOUNT_PERIOD, DELIVERY_DIRECT, TOTAL_AMOUNT, TRADER_ID, TRADER_NAME, TRADER_CONTACT_ID,
    TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID,
    TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, TAKE_TRADER_ID, TAKE_TRADER_NAME, TAKE_TRADER_CONTACT_ID,
    TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE,
    TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, PAYMENT_TYPE, PREPAID_AMOUNT,
    ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, LOGISTICS_ID,
    INVOICE_TYPE, FREIGHT_DESCRIPTION, PAYMENT_COMMENTS, LOGISTICS_COMMENTS, INVOICE_COMMENTS,
    COMMENTS, ADDITIONAL_CLAUSE, STATUS_COMMENTS, SATISFY_DELIVERY_TIME, ESTIMATE_ARRIVAL_TIME,
    ADD_TIME, CREATOR, MOD_TIME, UPDATER, UPDATE_DATA_TIME, REAL_PAY_AMOUNT, REAL_RETURN_AMOUNT,
    REAL_TOTAL_AMOUNT, NOPAYBACK_AMOUNT, REAL_INVOICE_AMOUNT, IS_RISK, RISK_COMMENTS,
    RISK_TIME, EXPEDITING_STATUS, EXPEDITING_FOLLOW_STATUS, NEW_FLOW, SUB_STATUS, VERIFY_STATUS,
    IS_NEW, SALEORDER_NOS, CONTRACT_URL, URGED_MAINTAIN_BATCH_INFO
	</sql>
	<select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from T_BUYORDER
		where BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
	</select>

	<insert id="insert" keyColumn="BUYORDER_ID" keyProperty="buyorderId" parameterType="com.vedeng.erp.buyorder.domain.entity.Buyorder" useGeneratedKeys="true">
		insert into T_BUYORDER (BUYORDER_NO, COMPANY_ID, ORDER_TYPE,
								ORG_ID, USER_ID, VALID_STATUS,
								VALID_TIME, `STATUS`, LOCKED_STATUS,
								INVOICE_STATUS, INVOICE_TIME, PAYMENT_STATUS,
								PAYMENT_TIME, DELIVERY_STATUS, DELIVERY_TIME,
								ARRIVAL_STATUS, ARRIVAL_TIME, SERVICE_STATUS,
								HAVE_ACCOUNT_PERIOD, DELIVERY_DIRECT, TOTAL_AMOUNT,
								TRADER_ID, TRADER_NAME, TRADER_CONTACT_ID,
								TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE,
								TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID,
								TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS,
								TAKE_TRADER_ID, TAKE_TRADER_NAME, TAKE_TRADER_CONTACT_ID,
								TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_MOBILE,
								TAKE_TRADER_CONTACT_TELEPHONE, TAKE_TRADER_ADDRESS_ID,
								TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, PAYMENT_TYPE,
								PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY,
								RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH,
								LOGISTICS_ID, INVOICE_TYPE, FREIGHT_DESCRIPTION,
								PAYMENT_COMMENTS, LOGISTICS_COMMENTS, INVOICE_COMMENTS,
								COMMENTS, ADDITIONAL_CLAUSE, STATUS_COMMENTS,
								SATISFY_DELIVERY_TIME, ESTIMATE_ARRIVAL_TIME,
								ADD_TIME, CREATOR, MOD_TIME,
								UPDATER, UPDATE_DATA_TIME, REAL_PAY_AMOUNT,
								REAL_RETURN_AMOUNT, REAL_TOTAL_AMOUNT, NOPAYBACK_AMOUNT,
								REAL_INVOICE_AMOUNT, IS_RISK, RISK_COMMENTS,
								RISK_TIME, EXPEDITING_STATUS, EXPEDITING_FOLLOW_STATUS,
								NEW_FLOW, SUB_STATUS, VERIFY_STATUS,
								IS_NEW, SALEORDER_NOS, CONTRACT_URL,
								URGED_MAINTAIN_BATCH_INFO)
		values (#{buyorderNo,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, #{orderType,jdbcType=INTEGER},
				#{orgId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{validStatus,jdbcType=INTEGER},
				#{validTime,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{lockedStatus,jdbcType=INTEGER},
				#{invoiceStatus,jdbcType=INTEGER}, #{invoiceTime,jdbcType=BIGINT}, #{paymentStatus,jdbcType=INTEGER},
				#{paymentTime,jdbcType=BIGINT}, #{deliveryStatus,jdbcType=INTEGER}, #{deliveryTime,jdbcType=BIGINT},
				#{arrivalStatus,jdbcType=INTEGER}, #{arrivalTime,jdbcType=BIGINT}, #{serviceStatus,jdbcType=INTEGER},
				#{haveAccountPeriod,jdbcType=INTEGER}, #{deliveryDirect,jdbcType=INTEGER}, #{totalAmount,jdbcType=DECIMAL},
				#{traderId,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR}, #{traderContactId,jdbcType=INTEGER},
				#{traderContactName,jdbcType=VARCHAR}, #{traderContactMobile,jdbcType=VARCHAR},
				#{traderContactTelephone,jdbcType=VARCHAR}, #{traderAddressId,jdbcType=INTEGER},
				#{traderArea,jdbcType=VARCHAR}, #{traderAddress,jdbcType=VARCHAR}, #{traderComments,jdbcType=VARCHAR},
				#{takeTraderId,jdbcType=INTEGER}, #{takeTraderName,jdbcType=VARCHAR}, #{takeTraderContactId,jdbcType=INTEGER},
				#{takeTraderContactName,jdbcType=VARCHAR}, #{takeTraderContactMobile,jdbcType=VARCHAR},
				#{takeTraderContactTelephone,jdbcType=VARCHAR}, #{takeTraderAddressId,jdbcType=INTEGER},
				#{takeTraderArea,jdbcType=VARCHAR}, #{takeTraderAddress,jdbcType=VARCHAR}, #{paymentType,jdbcType=INTEGER},
				#{prepaidAmount,jdbcType=DECIMAL}, #{accountPeriodAmount,jdbcType=DECIMAL}, #{periodDay,jdbcType=INTEGER},
				#{retainageAmount,jdbcType=DECIMAL}, #{retainageAmountMonth,jdbcType=INTEGER},
				#{logisticsId,jdbcType=INTEGER}, #{invoiceType,jdbcType=INTEGER}, #{freightDescription,jdbcType=INTEGER},
				#{paymentComments,jdbcType=VARCHAR}, #{logisticsComments,jdbcType=VARCHAR}, #{invoiceComments,jdbcType=VARCHAR},
				#{comments,jdbcType=VARCHAR}, #{additionalClause,jdbcType=VARCHAR}, #{statusComments,jdbcType=INTEGER},
				#{satisfyDeliveryTime,jdbcType=BIGINT}, #{estimateArrivalTime,jdbcType=VARCHAR},
				#{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT},
				#{updater,jdbcType=INTEGER}, #{updateDataTime,jdbcType=TIMESTAMP}, #{realPayAmount,jdbcType=DECIMAL},
				#{realReturnAmount,jdbcType=DECIMAL}, #{realTotalAmount,jdbcType=DECIMAL}, #{nopaybackAmount,jdbcType=DECIMAL},
				#{realInvoiceAmount,jdbcType=DECIMAL}, #{isRisk,jdbcType=INTEGER}, #{riskComments,jdbcType=VARCHAR},
				#{riskTime,jdbcType=BIGINT}, #{expeditingStatus,jdbcType=INTEGER}, #{expeditingFollowStatus,jdbcType=INTEGER},
				#{newFlow,jdbcType=INTEGER}, #{subStatus,jdbcType=VARCHAR}, #{verifyStatus,jdbcType=INTEGER},
				#{isNew,jdbcType=TINYINT}, #{saleorderNos,jdbcType=VARCHAR}, #{contractUrl,jdbcType=VARCHAR},
				#{urgedMaintainBatchInfo,jdbcType=TINYINT})
	</insert>

	<insert id="insertSelective" keyColumn="BUYORDER_ID" keyProperty="buyorderId" parameterType="com.vedeng.erp.buyorder.domain.entity.Buyorder" useGeneratedKeys="true">
		insert into T_BUYORDER
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="buyorderNo != null">
				BUYORDER_NO,
			</if>
			<if test="companyId != null">
				COMPANY_ID,
			</if>
			<if test="orderType != null">
				ORDER_TYPE,
			</if>
			<if test="orgId != null">
				ORG_ID,
			</if>
			<if test="userId != null">
				USER_ID,
			</if>
			<if test="validStatus != null">
				VALID_STATUS,
			</if>
			<if test="validTime != null">
				VALID_TIME,
			</if>
			<if test="status != null">
				`STATUS`,
			</if>
			<if test="lockedStatus != null">
				LOCKED_STATUS,
			</if>
			<if test="invoiceStatus != null">
				INVOICE_STATUS,
			</if>
			<if test="invoiceTime != null">
				INVOICE_TIME,
			</if>
			<if test="paymentStatus != null">
				PAYMENT_STATUS,
			</if>
			<if test="paymentTime != null">
				PAYMENT_TIME,
			</if>
			<if test="deliveryStatus != null">
				DELIVERY_STATUS,
			</if>
			<if test="deliveryTime != null">
				DELIVERY_TIME,
			</if>
			<if test="arrivalStatus != null">
				ARRIVAL_STATUS,
			</if>
			<if test="arrivalTime != null">
				ARRIVAL_TIME,
			</if>
			<if test="serviceStatus != null">
				SERVICE_STATUS,
			</if>
			<if test="haveAccountPeriod != null">
				HAVE_ACCOUNT_PERIOD,
			</if>
			<if test="deliveryDirect != null">
				DELIVERY_DIRECT,
			</if>
			<if test="totalAmount != null">
				TOTAL_AMOUNT,
			</if>
			<if test="traderId != null">
				TRADER_ID,
			</if>
			<if test="traderName != null">
				TRADER_NAME,
			</if>
			<if test="traderContactId != null">
				TRADER_CONTACT_ID,
			</if>
			<if test="traderContactName != null">
				TRADER_CONTACT_NAME,
			</if>
			<if test="traderContactMobile != null">
				TRADER_CONTACT_MOBILE,
			</if>
			<if test="traderContactTelephone != null">
				TRADER_CONTACT_TELEPHONE,
			</if>
			<if test="traderAddressId != null">
				TRADER_ADDRESS_ID,
			</if>
			<if test="traderArea != null">
				TRADER_AREA,
			</if>
			<if test="traderAddress != null">
				TRADER_ADDRESS,
			</if>
			<if test="traderComments != null">
				TRADER_COMMENTS,
			</if>
			<if test="takeTraderId != null">
				TAKE_TRADER_ID,
			</if>
			<if test="takeTraderName != null">
				TAKE_TRADER_NAME,
			</if>
			<if test="takeTraderContactId != null">
				TAKE_TRADER_CONTACT_ID,
			</if>
			<if test="takeTraderContactName != null">
				TAKE_TRADER_CONTACT_NAME,
			</if>
			<if test="takeTraderContactMobile != null">
				TAKE_TRADER_CONTACT_MOBILE,
			</if>
			<if test="takeTraderContactTelephone != null">
				TAKE_TRADER_CONTACT_TELEPHONE,
			</if>
			<if test="takeTraderAddressId != null">
				TAKE_TRADER_ADDRESS_ID,
			</if>
			<if test="takeTraderArea != null">
				TAKE_TRADER_AREA,
			</if>
			<if test="takeTraderAddress != null">
				TAKE_TRADER_ADDRESS,
			</if>
			<if test="paymentType != null">
				PAYMENT_TYPE,
			</if>
			<if test="prepaidAmount != null">
				PREPAID_AMOUNT,
			</if>
			<if test="accountPeriodAmount != null">
				ACCOUNT_PERIOD_AMOUNT,
			</if>
			<if test="periodDay != null">
				PERIOD_DAY,
			</if>
			<if test="retainageAmount != null">
				RETAINAGE_AMOUNT,
			</if>
			<if test="retainageAmountMonth != null">
				RETAINAGE_AMOUNT_MONTH,
			</if>
			<if test="logisticsId != null">
				LOGISTICS_ID,
			</if>
			<if test="invoiceType != null">
				INVOICE_TYPE,
			</if>
			<if test="freightDescription != null">
				FREIGHT_DESCRIPTION,
			</if>
			<if test="paymentComments != null">
				PAYMENT_COMMENTS,
			</if>
			<if test="logisticsComments != null">
				LOGISTICS_COMMENTS,
			</if>
			<if test="invoiceComments != null">
				INVOICE_COMMENTS,
			</if>
			<if test="comments != null">
				COMMENTS,
			</if>
			<if test="additionalClause != null">
				ADDITIONAL_CLAUSE,
			</if>
			<if test="statusComments != null">
				STATUS_COMMENTS,
			</if>
			<if test="satisfyDeliveryTime != null">
				SATISFY_DELIVERY_TIME,
			</if>
			<if test="estimateArrivalTime != null">
				ESTIMATE_ARRIVAL_TIME,
			</if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="modTime != null">
				MOD_TIME,
			</if>
			<if test="updater != null">
				UPDATER,
			</if>
			<if test="updateDataTime != null">
				UPDATE_DATA_TIME,
			</if>
			<if test="realPayAmount != null">
				REAL_PAY_AMOUNT,
			</if>
			<if test="realReturnAmount != null">
				REAL_RETURN_AMOUNT,
			</if>
			<if test="realTotalAmount != null">
				REAL_TOTAL_AMOUNT,
			</if>
			<if test="nopaybackAmount != null">
				NOPAYBACK_AMOUNT,
			</if>
			<if test="realInvoiceAmount != null">
				REAL_INVOICE_AMOUNT,
			</if>
			<if test="isRisk != null">
				IS_RISK,
			</if>
			<if test="riskComments != null">
				RISK_COMMENTS,
			</if>
			<if test="riskTime != null">
				RISK_TIME,
			</if>
			<if test="expeditingStatus != null">
				EXPEDITING_STATUS,
			</if>
			<if test="expeditingFollowStatus != null">
				EXPEDITING_FOLLOW_STATUS,
			</if>
			<if test="newFlow != null">
				NEW_FLOW,
			</if>
			<if test="subStatus != null">
				SUB_STATUS,
			</if>
			<if test="verifyStatus != null">
				VERIFY_STATUS,
			</if>
			<if test="isNew != null">
				IS_NEW,
			</if>
			<if test="saleorderNos != null">
				SALEORDER_NOS,
			</if>
			<if test="contractUrl != null">
				CONTRACT_URL,
			</if>
			<if test="urgedMaintainBatchInfo != null">
				URGED_MAINTAIN_BATCH_INFO,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="buyorderNo != null">
				#{buyorderNo,jdbcType=VARCHAR},
			</if>
			<if test="companyId != null">
				#{companyId,jdbcType=INTEGER},
			</if>
			<if test="orderType != null">
				#{orderType,jdbcType=INTEGER},
			</if>
			<if test="orgId != null">
				#{orgId,jdbcType=INTEGER},
			</if>
			<if test="userId != null">
				#{userId,jdbcType=INTEGER},
			</if>
			<if test="validStatus != null">
				#{validStatus,jdbcType=INTEGER},
			</if>
			<if test="validTime != null">
				#{validTime,jdbcType=BIGINT},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="lockedStatus != null">
				#{lockedStatus,jdbcType=INTEGER},
			</if>
			<if test="invoiceStatus != null">
				#{invoiceStatus,jdbcType=INTEGER},
			</if>
			<if test="invoiceTime != null">
				#{invoiceTime,jdbcType=BIGINT},
			</if>
			<if test="paymentStatus != null">
				#{paymentStatus,jdbcType=INTEGER},
			</if>
			<if test="paymentTime != null">
				#{paymentTime,jdbcType=BIGINT},
			</if>
			<if test="deliveryStatus != null">
				#{deliveryStatus,jdbcType=INTEGER},
			</if>
			<if test="deliveryTime != null">
				#{deliveryTime,jdbcType=BIGINT},
			</if>
			<if test="arrivalStatus != null">
				#{arrivalStatus,jdbcType=INTEGER},
			</if>
			<if test="arrivalTime != null">
				#{arrivalTime,jdbcType=BIGINT},
			</if>
			<if test="serviceStatus != null">
				#{serviceStatus,jdbcType=INTEGER},
			</if>
			<if test="haveAccountPeriod != null">
				#{haveAccountPeriod,jdbcType=INTEGER},
			</if>
			<if test="deliveryDirect != null">
				#{deliveryDirect,jdbcType=INTEGER},
			</if>
			<if test="totalAmount != null">
				#{totalAmount,jdbcType=DECIMAL},
			</if>
			<if test="traderId != null">
				#{traderId,jdbcType=INTEGER},
			</if>
			<if test="traderName != null">
				#{traderName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactId != null">
				#{traderContactId,jdbcType=INTEGER},
			</if>
			<if test="traderContactName != null">
				#{traderContactName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactMobile != null">
				#{traderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="traderContactTelephone != null">
				#{traderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="traderAddressId != null">
				#{traderAddressId,jdbcType=INTEGER},
			</if>
			<if test="traderArea != null">
				#{traderArea,jdbcType=VARCHAR},
			</if>
			<if test="traderAddress != null">
				#{traderAddress,jdbcType=VARCHAR},
			</if>
			<if test="traderComments != null">
				#{traderComments,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderId != null">
				#{takeTraderId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderName != null">
				#{takeTraderName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactId != null">
				#{takeTraderContactId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderContactName != null">
				#{takeTraderContactName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactMobile != null">
				#{takeTraderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactTelephone != null">
				#{takeTraderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderAddressId != null">
				#{takeTraderAddressId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderArea != null">
				#{takeTraderArea,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderAddress != null">
				#{takeTraderAddress,jdbcType=VARCHAR},
			</if>
			<if test="paymentType != null">
				#{paymentType,jdbcType=INTEGER},
			</if>
			<if test="prepaidAmount != null">
				#{prepaidAmount,jdbcType=DECIMAL},
			</if>
			<if test="accountPeriodAmount != null">
				#{accountPeriodAmount,jdbcType=DECIMAL},
			</if>
			<if test="periodDay != null">
				#{periodDay,jdbcType=INTEGER},
			</if>
			<if test="retainageAmount != null">
				#{retainageAmount,jdbcType=DECIMAL},
			</if>
			<if test="retainageAmountMonth != null">
				#{retainageAmountMonth,jdbcType=INTEGER},
			</if>
			<if test="logisticsId != null">
				#{logisticsId,jdbcType=INTEGER},
			</if>
			<if test="invoiceType != null">
				#{invoiceType,jdbcType=INTEGER},
			</if>
			<if test="freightDescription != null">
				#{freightDescription,jdbcType=INTEGER},
			</if>
			<if test="paymentComments != null">
				#{paymentComments,jdbcType=VARCHAR},
			</if>
			<if test="logisticsComments != null">
				#{logisticsComments,jdbcType=VARCHAR},
			</if>
			<if test="invoiceComments != null">
				#{invoiceComments,jdbcType=VARCHAR},
			</if>
			<if test="comments != null">
				#{comments,jdbcType=VARCHAR},
			</if>
			<if test="additionalClause != null">
				#{additionalClause,jdbcType=VARCHAR},
			</if>
			<if test="statusComments != null">
				#{statusComments,jdbcType=INTEGER},
			</if>
			<if test="satisfyDeliveryTime != null">
				#{satisfyDeliveryTime,jdbcType=BIGINT},
			</if>
			<if test="estimateArrivalTime != null">
				#{estimateArrivalTime,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				#{updater,jdbcType=INTEGER},
			</if>
			<if test="updateDataTime != null">
				#{updateDataTime,jdbcType=TIMESTAMP},
			</if>
			<if test="realPayAmount != null">
				#{realPayAmount,jdbcType=DECIMAL},
			</if>
			<if test="realReturnAmount != null">
				#{realReturnAmount,jdbcType=DECIMAL},
			</if>
			<if test="realTotalAmount != null">
				#{realTotalAmount,jdbcType=DECIMAL},
			</if>
			<if test="nopaybackAmount != null">
				#{nopaybackAmount,jdbcType=DECIMAL},
			</if>
			<if test="realInvoiceAmount != null">
				#{realInvoiceAmount,jdbcType=DECIMAL},
			</if>
			<if test="isRisk != null">
				#{isRisk,jdbcType=INTEGER},
			</if>
			<if test="riskComments != null">
				#{riskComments,jdbcType=VARCHAR},
			</if>
			<if test="riskTime != null">
				#{riskTime,jdbcType=BIGINT},
			</if>
			<if test="expeditingStatus != null">
				#{expeditingStatus,jdbcType=INTEGER},
			</if>
			<if test="expeditingFollowStatus != null">
				#{expeditingFollowStatus,jdbcType=INTEGER},
			</if>
			<if test="newFlow != null">
				#{newFlow,jdbcType=INTEGER},
			</if>
			<if test="subStatus != null">
				#{subStatus,jdbcType=VARCHAR},
			</if>
			<if test="verifyStatus != null">
				#{verifyStatus,jdbcType=INTEGER},
			</if>
			<if test="isNew != null">
				#{isNew,jdbcType=TINYINT},
			</if>
			<if test="saleorderNos != null">
				#{saleorderNos,jdbcType=VARCHAR},
			</if>
			<if test="contractUrl != null">
				#{contractUrl,jdbcType=VARCHAR},
			</if>
			<if test="urgedMaintainBatchInfo != null">
				#{urgedMaintainBatchInfo,jdbcType=TINYINT},
			</if>
		</trim>
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.Buyorder">
		update T_BUYORDER
		<set>
			<if test="buyorderNo != null">
				BUYORDER_NO = #{buyorderNo,jdbcType=VARCHAR},
			</if>
			<if test="companyId != null">
				COMPANY_ID = #{companyId,jdbcType=INTEGER},
			</if>
			<if test="orderType != null">
				ORDER_TYPE = #{orderType,jdbcType=INTEGER},
			</if>
			<if test="orgId != null">
				ORG_ID = #{orgId,jdbcType=INTEGER},
			</if>
			<if test="userId != null">
				USER_ID = #{userId,jdbcType=INTEGER},
			</if>
			<if test="validStatus != null">
				VALID_STATUS = #{validStatus,jdbcType=INTEGER},
			</if>
			<if test="validTime != null">
				VALID_TIME = #{validTime,jdbcType=BIGINT},
			</if>
			<if test="status != null">
				`STATUS` = #{status,jdbcType=INTEGER},
			</if>
			<if test="lockedStatus != null">
				LOCKED_STATUS = #{lockedStatus,jdbcType=INTEGER},
			</if>
			<if test="invoiceStatus != null">
				INVOICE_STATUS = #{invoiceStatus,jdbcType=INTEGER},
			</if>
			<if test="invoiceTime != null">
				INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
			</if>
			<if test="paymentStatus != null">
				PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER},
			</if>
			<if test="paymentTime != null">
				PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
			</if>
			<if test="deliveryStatus != null">
				DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
			</if>
			<if test="deliveryTime != null">
				DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
			</if>
			<if test="arrivalStatus != null">
				ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
			</if>
			<if test="arrivalTime != null">
				ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
			</if>
			<if test="serviceStatus != null">
				SERVICE_STATUS = #{serviceStatus,jdbcType=INTEGER},
			</if>
			<if test="haveAccountPeriod != null">
				HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=INTEGER},
			</if>
			<if test="deliveryDirect != null">
				DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
			</if>
			<if test="totalAmount != null">
				TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
			</if>
			<if test="traderId != null">
				TRADER_ID = #{traderId,jdbcType=INTEGER},
			</if>
			<if test="traderName != null">
				TRADER_NAME = #{traderName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactId != null">
				TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
			</if>
			<if test="traderContactName != null">
				TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactMobile != null">
				TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="traderContactTelephone != null">
				TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="traderAddressId != null">
				TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
			</if>
			<if test="traderArea != null">
				TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
			</if>
			<if test="traderAddress != null">
				TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
			</if>
			<if test="traderComments != null">
				TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderId != null">
				TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderName != null">
				TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactId != null">
				TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderContactName != null">
				TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactMobile != null">
				TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactTelephone != null">
				TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderAddressId != null">
				TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderArea != null">
				TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderAddress != null">
				TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
			</if>
			<if test="paymentType != null">
				PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
			</if>
			<if test="prepaidAmount != null">
				PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
			</if>
			<if test="accountPeriodAmount != null">
				ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
			</if>
			<if test="periodDay != null">
				PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
			</if>
			<if test="retainageAmount != null">
				RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
			</if>
			<if test="retainageAmountMonth != null">
				RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
			</if>
			<if test="logisticsId != null">
				LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
			</if>
			<if test="invoiceType != null">
				INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
			</if>
			<if test="freightDescription != null">
				FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
			</if>
			<if test="paymentComments != null">
				PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
			</if>
			<if test="logisticsComments != null">
				LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
			</if>
			<if test="invoiceComments != null">
				INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
			</if>
			<if test="comments != null">
				COMMENTS = #{comments,jdbcType=VARCHAR},
			</if>
			<if test="additionalClause != null">
				ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
			</if>
			<if test="statusComments != null">
				STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
			</if>
			<if test="satisfyDeliveryTime != null">
				SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
			</if>
			<if test="estimateArrivalTime != null">
				ESTIMATE_ARRIVAL_TIME = #{estimateArrivalTime,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null">
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
			<if test="updateDataTime != null">
				UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
			</if>
			<if test="realPayAmount != null">
				REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
			</if>
			<if test="realReturnAmount != null">
				REAL_RETURN_AMOUNT = #{realReturnAmount,jdbcType=DECIMAL},
			</if>
			<if test="realTotalAmount != null">
				REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL},
			</if>
			<if test="nopaybackAmount != null">
				NOPAYBACK_AMOUNT = #{nopaybackAmount,jdbcType=DECIMAL},
			</if>
			<if test="realInvoiceAmount != null">
				REAL_INVOICE_AMOUNT = #{realInvoiceAmount,jdbcType=DECIMAL},
			</if>
			<if test="isRisk != null">
				IS_RISK = #{isRisk,jdbcType=INTEGER},
			</if>
			<if test="riskComments != null">
				RISK_COMMENTS = #{riskComments,jdbcType=VARCHAR},
			</if>
			<if test="riskTime != null">
				RISK_TIME = #{riskTime,jdbcType=BIGINT},
			</if>
			<if test="expeditingStatus != null">
				EXPEDITING_STATUS = #{expeditingStatus,jdbcType=INTEGER},
			</if>
			<if test="expeditingFollowStatus != null">
				EXPEDITING_FOLLOW_STATUS = #{expeditingFollowStatus,jdbcType=INTEGER},
			</if>
			<if test="newFlow != null">
				NEW_FLOW = #{newFlow,jdbcType=INTEGER},
			</if>
			<if test="subStatus != null">
				SUB_STATUS = #{subStatus,jdbcType=VARCHAR},
			</if>
			<if test="verifyStatus != null">
				VERIFY_STATUS = #{verifyStatus,jdbcType=INTEGER},
			</if>
			<if test="isNew != null">
				IS_NEW = #{isNew,jdbcType=TINYINT},
			</if>
			<if test="saleorderNos != null">
				SALEORDER_NOS = #{saleorderNos,jdbcType=VARCHAR},
			</if>
			<if test="contractUrl != null">
				CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR},
			</if>
			<if test="urgedMaintainBatchInfo != null">
				URGED_MAINTAIN_BATCH_INFO = #{urgedMaintainBatchInfo,jdbcType=TINYINT},
			</if>
		</set>
		where BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
	</update>

	<update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.Buyorder">
		update T_BUYORDER
		set BUYORDER_NO = #{buyorderNo,jdbcType=VARCHAR},
			COMPANY_ID = #{companyId,jdbcType=INTEGER},
			ORDER_TYPE = #{orderType,jdbcType=INTEGER},
			ORG_ID = #{orgId,jdbcType=INTEGER},
			USER_ID = #{userId,jdbcType=INTEGER},
			VALID_STATUS = #{validStatus,jdbcType=INTEGER},
			VALID_TIME = #{validTime,jdbcType=BIGINT},
			`STATUS` = #{status,jdbcType=INTEGER},
			LOCKED_STATUS = #{lockedStatus,jdbcType=INTEGER},
			INVOICE_STATUS = #{invoiceStatus,jdbcType=INTEGER},
			INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
			PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER},
			PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
			DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
			DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
			ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
			ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
			SERVICE_STATUS = #{serviceStatus,jdbcType=INTEGER},
			HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=INTEGER},
			DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
			TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
			TRADER_ID = #{traderId,jdbcType=INTEGER},
			TRADER_NAME = #{traderName,jdbcType=VARCHAR},
			TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
			TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
			TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
			TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
			TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
			TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
			TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
			TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
			TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
			TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
			TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
			TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
			TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
			TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
			TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
			TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
			TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
			PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
			PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
			ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
			PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
			RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
			RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
			LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
			INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
			FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
			PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
			LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
			INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
			COMMENTS = #{comments,jdbcType=VARCHAR},
			ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
			STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
			SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
			ESTIMATE_ARRIVAL_TIME = #{estimateArrivalTime,jdbcType=VARCHAR},
			ADD_TIME = #{addTime,jdbcType=BIGINT},
			CREATOR = #{creator,jdbcType=INTEGER},
			MOD_TIME = #{modTime,jdbcType=BIGINT},
			UPDATER = #{updater,jdbcType=INTEGER},
			UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
			REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
			REAL_RETURN_AMOUNT = #{realReturnAmount,jdbcType=DECIMAL},
			REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL},
			NOPAYBACK_AMOUNT = #{nopaybackAmount,jdbcType=DECIMAL},
			REAL_INVOICE_AMOUNT = #{realInvoiceAmount,jdbcType=DECIMAL},
			IS_RISK = #{isRisk,jdbcType=INTEGER},
			RISK_COMMENTS = #{riskComments,jdbcType=VARCHAR},
			RISK_TIME = #{riskTime,jdbcType=BIGINT},
			EXPEDITING_STATUS = #{expeditingStatus,jdbcType=INTEGER},
			EXPEDITING_FOLLOW_STATUS = #{expeditingFollowStatus,jdbcType=INTEGER},
			NEW_FLOW = #{newFlow,jdbcType=INTEGER},
			SUB_STATUS = #{subStatus,jdbcType=VARCHAR},
			VERIFY_STATUS = #{verifyStatus,jdbcType=INTEGER},
			IS_NEW = #{isNew,jdbcType=TINYINT},
			SALEORDER_NOS = #{saleorderNos,jdbcType=VARCHAR},
			CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR},
			URGED_MAINTAIN_BATCH_INFO = #{urgedMaintainBatchInfo,jdbcType=TINYINT}
		where BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
	</update>

	<select id="queryInfoByNo" resultType="com.vedeng.erp.buyorder.dto.BuyOrderDto">
		SELECT A.*,B.ORG_NAME,C.USERNAME AS creatorName,D.GRADE
		FROM T_BUYORDER A
		LEFT JOIN T_ORGANIZATION B ON A.ORG_ID = B.ORG_ID
		LEFT JOIN T_USER C ON A.CREATOR = C.USER_ID
		LEFT JOIN T_TRADER_SUPPLIER D ON A.TRADER_ID = D.TRADER_ID
		WHERE BUYORDER_NO = #{buyorderNo,jdbcType=VARCHAR}
	</select>

	<select id="selectSalorderGoodsBySkuBuyOrderId" resultType="java.lang.Integer">
		SELECT
			a.SALEORDER_GOODS_ID
		FROM T_SALEORDER_GOODS a
				 LEFT JOIN T_R_BUYORDER_J_SALEORDER b ON a.SALEORDER_GOODS_ID = b.SALEORDER_GOODS_ID
				 LEFT JOIN T_BUYORDER_GOODS c ON b.BUYORDER_GOODS_ID = c.BUYORDER_GOODS_ID
		where c.BUYORDER_ID=#{buyOrderId,jdbcType=INTEGER} and c.SKU in
		<foreach collection="skus" item="sku" separator="," open="(" close=")">
			#{sku}
		</foreach>
 	</select>

	<update id="updateSaleOrderGoodsSpecialDevlivery" parameterType="java.lang.Integer">
		update T_SALEORDER_GOODS a set a.SPECIAL_DELIVERY = 1 where a.SALEORDER_GOODS_ID in
		<foreach collection="list" item="goodsId" separator="," close=")" open="(">
			#{goodsId}
		</foreach>
	</update>

	<select id="selectSalorderIdBySaleOrderGoodsId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		SELECT
		a.SALEORDER_ID
		FROM T_SALEORDER_GOODS a
		where a.SALEORDER_GOODS_ID = #{salerOrderGoodsId}
	</select>
	<update id="updateComponentRelation" >
		update T_COMPONENT_RELATION a set a.COMPONENT_ID = 16 where a.RELATION_ID = #{saleOrderId} and a.COMPONENT_ID=17 and a.SKU_NO in
		<foreach collection="skus" item="sku" open="(" close=")" separator=",">
			#{sku}
		</foreach>
	</update>

	<select id="getSaleorderIdListByBuyorderId" resultType="java.lang.Integer">
		SELECT
				a.SALEORDER_ID
		FROM T_SALEORDER_GOODS a
					 inner JOIN T_R_BUYORDER_J_SALEORDER b ON a.SALEORDER_GOODS_ID = b.SALEORDER_GOODS_ID
					 LEFT JOIN T_BUYORDER_GOODS c ON b.BUYORDER_GOODS_ID = c.BUYORDER_GOODS_ID
			LEFT JOIN T_SALEORDER d on d.SALEORDER_ID = a.SALEORDER_ID
		where c.BUYORDER_ID=#{buyorderId,jdbcType=INTEGER} and d.ORDER_TYPE !=2
		GROUP BY a.SALEORDER_ID
	</select>

	<select id="getBuyorderIdListBySaleorderIds" resultType="java.lang.Integer">
		SELECT
				c.BUYORDER_ID
		FROM T_SALEORDER_GOODS a
					 inner JOIN T_R_BUYORDER_J_SALEORDER b ON a.SALEORDER_GOODS_ID = b.SALEORDER_GOODS_ID
					 LEFT JOIN T_BUYORDER_GOODS c ON b.BUYORDER_GOODS_ID = c.BUYORDER_GOODS_ID
					 LEFT JOIN T_SALEORDER d on d.SALEORDER_ID = a.SALEORDER_ID
		where d.SALEORDER_ID in
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
		and d.ORDER_TYPE !=2
		GROUP BY c.BUYORDER_ID
	</select>
    <select id="getBuyOrderGoodsPriceByGoodsId" resultType="java.math.BigDecimal">
		SELECT
			tbg.PRICE
		FROM
			T_BUYORDER_GOODS tbg
		LEFT JOIN T_BUYORDER tb ON
			tbg.BUYORDER_ID = tb.BUYORDER_ID
		WHERE
			tbg.GOODS_ID = #{goodsId,jdbcType=INTEGER}
			AND tb.VALID_STATUS = 1
			AND tbg.IS_DELETE = 0
		ORDER BY
			tb.VALID_TIME DESC
		LIMIT 1
	</select>
    <select id="getCommunicateBuyOrderInfo" resultType="java.util.Map">
		SELECT C.BUYORDER_ID AS RELATED_ID,
		C.BUYORDER_NO AS ORDER_NO
		FROM T_BUYORDER C
		WHERE C.BUYORDER_ID IN
		<foreach item="item" index="index" collection="list"
				 open="(" separator="," close=")">
			#{item,jdbcType=INTEGER}
		</foreach>
	</select>
	<select id="getByBuyOrderIdAndSkuNo" resultType="java.lang.Integer">
		SELECT
			tbg.BUYORDER_GOODS_ID
		FROM
			T_BUYORDER_GOODS tbg
		LEFT JOIN V_CORE_SKU vcs ON tbg.GOODS_ID = vcs.SKU_ID
		WHERE
			tbg.IS_DELETE = 0
			AND vcs.SKU_NO = #{skuNo,jdbcType=VARCHAR}
			AND tbg.BUYORDER_ID = #{buyOrderId,jdbcType=INTEGER}
	</select>

<!--auto generated by MybatisCodeHelper on 2023-12-02-->
	<update id="updatePaymentStatusAndInvoiceStatusByBuyorderId">
		update T_BUYORDER
		set PAYMENT_STATUS=#{updatedPaymentStatus,jdbcType=INTEGER},
		INVOICE_STATUS=#{updatedInvoiceStatus,jdbcType=INTEGER},
		PAYMENT_TIME=unix_timestamp()*1000,
		INVOICE_TIME=unix_timestamp()*1000
		where BUYORDER_ID=#{buyorderId,jdbcType=INTEGER}
	</update>

<!--auto generated by MybatisCodeHelper on 2023-12-07-->
	<select id="findByBuyorderId" resultType="com.vedeng.erp.buyorder.dto.BuyOrderApiDto">
		select
		TB.*,
		TTS.TRADER_SUPPLIER_ID as traderSupplierId
		from T_BUYORDER TB
		left join T_TRADER_SUPPLIER TTS on TTS.TRADER_ID=TB.TRADER_ID
		where BUYORDER_ID=#{buyorderId,jdbcType=INTEGER}
	</select>

	<!--auto generated by MybatisCodeHelper on 2023-12-07-->
	<select id="findByBuyorderNo" resultType="com.vedeng.erp.buyorder.dto.BuyOrderApiDto">
		select
		TB.*,
		TTS.TRADER_SUPPLIER_ID as traderSupplierId
		from T_BUYORDER TB
		left join T_TRADER_SUPPLIER TTS on TTS.TRADER_ID=TB.TRADER_ID
		where BUYORDER_NO=#{buyorderNo,jdbcType=VARCHAR}
	</select>

	<select id="getBuyOrderInfo" resultType="com.vedeng.erp.buyorder.dto.BuyOrderInfoDto">
		select BUYORDER_ID,BUYORDER_NO,STATUS, VALID_STATUS,VALID_TIME, PAYMENT_STATUS,TRADER_ID,ORDER_TYPE,LOCKED_STATUS,INVOICE_STATUS,ARRIVAL_STATUS
		from T_BUYORDER where BUYORDER_ID = #{buyorderId,jdbcType=INTEGER};
	</select>

	<select id="getContractReviewStatus" resultType="java.lang.Integer">
		SELECT tvi.STATUS
		FROM T_BUYORDER tb
				 INNER JOIN T_VERIFIES_INFO tvi
							ON tb.BUYORDER_ID = tvi.RELATE_TABLE_KEY
								AND tvi.RELATE_TABLE = 'T_BUYORDER_CONTRACT'
		WHERE tb.BUYORDER_ID = #{relatedId,jdbcType=INTEGER}
		ORDER BY tvi.VERIFIES_INFO_ID DESC
    </select>

	<select id="kingDeePageSelect" parameterType="com.vedeng.erp.buyorder.dto.BuyOrderKingDeeRequestDto" resultType="com.vedeng.erp.buyorder.dto.BuyOrderKingDeeDto">
		select
			a.BUYORDER_ID,
			a.BUYORDER_NO,
			a.VALID_TIME,
			a.TAKE_TRADER_CONTACT_NAME,
			a.TAKE_TRADER_CONTACT_MOBILE,
			a.TAKE_TRADER_CONTACT_TELEPHONE,
			a.TAKE_TRADER_AREA,
			a.TAKE_TRADER_ADDRESS,
			a.INVOICE_TYPE,
			a.TRADER_ID,
			b.BUYORDER_ACTUAL_SUPPLIER_ID,
			b.TRADER_ID as ACTUAL_TRADER_ID,
		    c.TRADER_SUPPLIER_ID as ACTUAL_TRADER_SUPPLIER_ID,
			b.TRADER_NAME as ACTUAL_TRADER_NAME,
			b.TRADER_CONTACT_ID,
			b.TRADER_CONTACT_NAME,
			b.TRADER_CONTACT_MOBILE ,
			b.TRADER_CONTACT_TELEPHONE,
			b.PAYMENT_TYPE,
			b.PREPAID_AMOUNT,
			b.ACCOUNT_PERIOD_AMOUNT,
			b.RETAINAGE_AMOUNT,
			b.RETAINAGE_AMOUNT_MONTH,
			b.INVOICE_TYPE as ACTUAL_INVOICE_TYPE,
		    b.NEED_INVOICE
		from T_BUYORDER a
		left join T_BUYORDER_ACTUAL_SUPPLIER b on a.BUYORDER_ID = b.BUYORDER_ID
		left join T_TRADER_SUPPLIER c on b.TRADER_ID = c.TRADER_ID
		where a.COMPANY_ID = 1
		and a.STATUS in (1,2)
		and a.TRADER_ID = 613042
		and a.VALID_STATUS = 1
		<if test="buyOrderNo != null">
			and a.BUYORDER_NO = #{buyOrderNo,jdbcType=VARCHAR}
		</if>
		<if test="buyOrderTimeStart != null">
			and a.VALID_TIME >= #{buyOrderTimeStart,jdbcType=BIGINT}
		</if>
		<if test="buyOrderTimeEnd != null">
			and a.VALID_TIME <![CDATA[ <= ]]> #{buyOrderTimeEnd,jdbcType=BIGINT}
		</if>
		order by a.ADD_TIME desc
	</select>

	<select id="getContractAuditInfo" resultType="com.vedeng.erp.buyorder.dto.BuyorderContractAuditDto">
		SELECT tvi.STATUS, tvi.ADD_TIME
		FROM T_BUYORDER tb
				 INNER JOIN T_VERIFIES_INFO tvi
							ON tb.BUYORDER_ID = tvi.RELATE_TABLE_KEY
								AND tvi.RELATE_TABLE = 'T_BUYORDER_CONTRACT'
		WHERE tb.BUYORDER_ID = #{buyOrderId,jdbcType=INTEGER}
		ORDER BY tvi.VERIFIES_INFO_ID DESC
	</select>

	<select id="getAttachmentInfo" resultType="com.vedeng.erp.buyorder.dto.BuyorderContractAttachmentDto">
		select RELATED_ID, ADD_TIME, CONCAT('http://',DOMAIN, URI) URL
		from T_ATTACHMENT
		where RELATED_ID = #{buyOrderId,jdbcType=INTEGER}
		  and ATTACHMENT_FUNCTION = 514
		  and IS_DELETED = 0
		  and ADD_TIME in
		<foreach collection="addTimeList" item="addTime" open="(" close=")" separator=",">
			#{addTime,jdbcType=BIGINT}
		</foreach>
	</select>


	<select id="getBdSaleOrderListByBuyOrderNo" resultType="java.util.Map">
		SELECT
		BUYORDER.BUYORDER_NO as "originNo",
		GROUP_CONCAT(distinct SALEORDER.SALEORDER_NO) as "bdSaleOrderNo"
		FROM T_BUYORDER BUYORDER
		JOIN T_BUYORDER_GOODS BUYORDER_GOODS ON BUYORDER.BUYORDER_ID = BUYORDER_GOODS.BUYORDER_ID
		JOIN T_R_BUYORDER_J_SALEORDER R_J ON R_J.BUYORDER_GOODS_ID = BUYORDER_GOODS.BUYORDER_GOODS_ID
		JOIN T_SALEORDER_GOODS SALEORDER_GOODS ON SALEORDER_GOODS.SALEORDER_GOODS_ID = R_J.SALEORDER_GOODS_ID
		JOIN T_SALEORDER SALEORDER ON SALEORDER_GOODS.SALEORDER_ID = SALEORDER.SALEORDER_ID
		WHERE BUYORDER.BUYORDER_NO IN
		<foreach item="item" index="index" collection="buyOrderNoList" open="(" separator="," close=")">
			#{item}
		</foreach>

		group by BUYORDER.BUYORDER_NO
	</select>
    
	<select id="getExpressGoodsList" resultType="com.vedeng.erp.buyorder.dto.ExpressGoodsDto">
		select c.LOGISTICS_NO,
			   c.LOGISTICS_ID,
			   c.LOGISTICS_COMMENTS,
			   b.AMOUNT,
			   c.DELIVERY_TIME,
			   a.BUYORDER_GOODS_ID buyOrderGoodsId,
			   a.BUYORDER_ID       buyOrderId,
			   a.SKU,
			   a.PRICE,
			   a.NUM,
			   b.NUM               sendNum
		from T_BUYORDER o
				 left join T_BUYORDER_GOODS a on o.BUYORDER_ID = a.BUYORDER_ID
				 left join T_EXPRESS_DETAIL b on a.BUYORDER_GOODS_ID = b.RELATED_ID
				 left join T_EXPRESS c on c.EXPRESS_ID = b.EXPRESS_ID
		where a.IS_DELETE = 0
		  and b.BUSINESS_TYPE = 515
		  and o.BUYORDER_NO = #{buyOrderNo,jdbcType=VARCHAR}
    </select>

	<select id="getExpressGoodsNotInList" resultType="com.vedeng.erp.buyorder.dto.ExpressGoodsDto">
		select c.LOGISTICS_NO,
			   c.LOGISTICS_ID,
			   c.LOGISTICS_COMMENTS,
			   b.AMOUNT,
			   c.DELIVERY_TIME,
			   a.BUYORDER_GOODS_ID buyOrderGoodsId,
			   a.BUYORDER_ID       buyOrderId,
			   a.SKU,
			   a.PRICE,
			   a.NUM,
			   b.NUM               sendNum
		from T_BUYORDER o
				 left join T_BUYORDER_GOODS a on o.BUYORDER_ID = a.BUYORDER_ID
				 left join T_EXPRESS_DETAIL b on a.BUYORDER_GOODS_ID = b.RELATED_ID
				 left join T_EXPRESS c on c.EXPRESS_ID = b.EXPRESS_ID
		where a.IS_DELETE = 0
		  and b.BUSINESS_TYPE = 515
		<if test="notInExpressNoList != null and notInExpressNoList.size > 0" >
			and c.LOGISTICS_NO not in
			<foreach item="item" index="index" collection="notInExpressNoList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		  and o.BUYORDER_NO = #{buyOrderNo,jdbcType=VARCHAR}
	</select>

    <!-- 根据采购单号查询所有快递单号 -->
    <select id="getAllExpressList" resultType="java.util.Map">
        select distinct c.LOGISTICS_NO as logisticsNo,
               o.BUYORDER_NO as buyOrderNo,
               o.BUYORDER_NO as nextBuyOrderNo
        from T_BUYORDER o
                 left join T_BUYORDER_GOODS a on o.BUYORDER_ID = a.BUYORDER_ID
                 left join T_EXPRESS_DETAIL b on a.BUYORDER_GOODS_ID = b.RELATED_ID
                 left join T_EXPRESS c on c.EXPRESS_ID = b.EXPRESS_ID
        where a.IS_DELETE = 0
          and b.BUSINESS_TYPE = 515
          and c.LOGISTICS_NO is not null
          and c.LOGISTICS_NO != ''
          and o.BUYORDER_NO = #{buyOrderNo,jdbcType=VARCHAR}
        order by c.LOGISTICS_NO
    </select>

    <!-- 根据采购单ID查询发票信息列表 -->
    <select id="getInvoiceListByBuyorderId" resultType="com.vedeng.erp.buyorder.dto.BuyOrderInvoiceDto">
        SELECT
            TI.INVOICE_NO as invoiceNo,
            TI.INVOICE_HREF as invoiceHref
        FROM T_INVOICE TI
        WHERE TI.RELATED_ID = #{buyorderId,jdbcType=INTEGER}
            AND TI.VALID_STATUS = 1 
            AND TI.COLOR_TYPE = 2 
            AND TI.IS_ENABLE = 1 
            AND TI.TYPE = 503
        ORDER BY TI.ADD_TIME DESC
    </select>
</mapper>
