package com.vedeng.erp.broadcast.mapdao;

import com.vedeng.erp.broadcast.domain.dto.BroadCastDeptListDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 通知部门小组Mapper
 */
public interface BroadcastDeptMapper {

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 删除记录数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insert(BroadcastDeptEntity record);

    /**
     * 选择性插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insertSelective(BroadcastDeptEntity record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    BroadcastDeptEntity selectByPrimaryKey(Integer id);

    /**
     * 选择性更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKeySelective(BroadcastDeptEntity record);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKey(BroadcastDeptEntity record);

    // ========== 自定义查询方法 ==========
    /**
     * 根据父级部门ID查询部门列表
     *
     * @param parentId 父级部门ID（null表示查询所有部门，非null表示子部门）
     * @return 部门列表
     */
    List<BroadcastDeptEntity> listByParentId(@Param("parentId")Integer parentId,
                                             @Param("deptName") String deptName);


    /**
     * 根据部门名称查询部门（精确匹配）
     *
     * @param deptName 部门名称
     * @param parentId 父级部门ID（null表示查询顶级部门，非null表示查询小组）
     * @return 部门列表
     */
    List<BroadcastDeptEntity> selectByDeptName(@Param("deptName") String deptName,
                                              @Param("parentId") Integer parentId);

    /**
     * 查询所有小组（parentId不为null且不为0）
     *
     * @return 小组列表
     */
    List<BroadcastDeptEntity> selectAllGroups();

    /**
     * 查询所有部门（parentId为null或为0）
     *
     * @return 部门列表
     */
    List<BroadcastDeptEntity> selectAllDepts();

    /**
     * 分页查询播报部门列表（支持两级部门结构）
     * 支持一级部门名称、二级小组名称模糊查询和AED用户筛选
     * 
     * @param deptName 一级部门名称（模糊查询）
     * @param groupName 二级小组名称（模糊查询）
     * @param aedUserId AED用户ID（包含查询）
     * @return 播报部门列表
     */
    List<BroadCastDeptListDto> selectBroadcastDeptListPage(@Param("deptName") String deptName, @Param("groupName") String groupName, @Param("aedUserId") Integer aedUserId);
}
