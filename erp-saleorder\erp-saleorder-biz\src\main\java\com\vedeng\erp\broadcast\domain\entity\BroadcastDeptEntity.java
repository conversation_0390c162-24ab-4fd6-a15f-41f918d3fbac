package com.vedeng.erp.broadcast.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 通知部门小组表
 */
@Getter
@Setter
public class BroadcastDeptEntity extends BaseEntity {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 父级部门ID（顶级为0）
     */
    private Integer parentId;

    /**
     * 配置的AED区域经理对应的用户ID
     */
    private String aedUserId;

    /**
     * 是否已删除（0=否，1=是）
     */
    private Integer isDeleted;
}
