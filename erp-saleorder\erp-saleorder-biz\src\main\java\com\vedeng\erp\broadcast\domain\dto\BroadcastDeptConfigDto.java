package com.vedeng.erp.broadcast.domain.dto;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BroadcastDeptConfigDto {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 一级部门ID 1.代表到款大群
     */
    private Integer broadcastDeptId;
    private String broadcastDeptName;
    /**
     * 日常标志（0/1）
     */
    private Integer dayFlag;
    // 周榜标志（0/1）
    private Integer weekFlag;
    // 月榜标志（0/1）
    private Integer monthFlag;
    // aed榜标志（0/1）
    private Integer aedFlag;
    // 自有品牌榜标志（0/1）
    private Integer zyFlag;
    // 自定义榜标志（0/1）
    private Integer customFlag;
    //  到款阶梯
    private Integer amountStep;
    //webhook
    private String  webhook;
}
