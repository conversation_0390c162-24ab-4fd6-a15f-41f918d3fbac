var VD_UI_GLOBAL = {
    // 全局加载状态
    showGlobalLoading(font) {
        console.log('font:', font);
        let loadingEle = document.createElement('div');
        loadingEle.setAttribute('class', 'global__loading__wrap');
        loadingEle.setAttribute('id', 'J-global-loading-wrap');
        loadingEle.innerHTML = `<i class="vd-ui_icon icon-loading"></i>`;
        if (font) {
            let fontEle = document.createElement('p');
            fontEle.setAttribute('class', 'global__loading__p');
            fontEle.innerText = font;
            loadingEle.appendChild(fontEle);
        }
        document.body.appendChild(loadingEle);
    },
    hideGlobalLoading() {
        var element = document.getElementById('J-global-loading-wrap');
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    },
    //获取路由参数
    getQuery(key) {
        let params = new URLSearchParams(window.location.search);
        return params.get(key) || '';
    },
    openLink(link, params) {
        if(window.parent != window) {
            if(window.parent.closableTab) {
                var item = { 
                    'id': params.id ? params.id : new Date().getTime(), 
                    'name': params.name, 
                    'url': link, 
                    'closable': params.noclose ? false : true 
                };

                window.parent.closableTab.addTab(item);
                window.parent.closableTab.resizeMove();
            }
        } else {
            console.log(link)
            window.open(link);
        }
    },
    defaultImg: '/static/images/prod-img-placeholder.png'
}

//页面加载之后展示vue渲染，隐藏vue渲染之前的页面
window.onload = () => {
    if(document.querySelector('#page-container')) {
        document.querySelector('#page-container').classList.add('show');
    }
}

Vue.component('ui-button', {
    template: `<div class="vd-ui-button"
        @click="handleClick"
        :disabled="buttonDisabled"
        :class="[
            type ? 'vd-ui-button--' + type : '',
            buttonSize ? 'vd-ui-button--' + buttonSize : '',
            {
                'is-disabled': buttonDisabled,
                'is-loading': loading,
            }
        ]"
        :style="{'width': width}"
    >
        <i 
            ref="loading" 
            class="vd-ui_icon icon-loading"
            :class="{'loading':loading}"
            v-if="loading"
        ></i>
        <i class="vd-ui_icon"
            :class="icon"
            v-if="icon && !loading"
        ></i>
        <span v-if="$slots.default">
            <slot></slot>
        </span>
    </div>`,
    props: {
        // 按钮类型
        type: {
            type: String,
            default: ''
        },
        // 按钮尺寸 large small
        size: String,
        width: {
            type: String,
            default: 'auto'
        },
        icon: {
            type: String,
            default: ''
        },
        loading: Boolean,
        disabled: Boolean,
    },
    computed: {
        buttonSize() {
            return this.size
        },
        buttonDisabled() {
            return this.disabled
        }
    },
    mounted(){
       
    },
    methods: {
        handleClick(event) {
            this.$emit('click', event)
        },
    }
})

Vue.component('ui-select-button', {
    template: `<div class="vd-ui-select-button" :class="{open: isShowMoreAddBtns}" v-if="type==='button'">
        <div class="ui-select-btn-txt">
            <slot></slot>
        </div>
        <div class="ui-select-btn-more" @click="showMore">
            <i class="vd-ui_icon icon-down"></i>
        </div>
        <div class="ui-select-btn-drop">
            <slot name="drop"><slot>
        </div>
    </div>
    <div class="vd-ui-select-link" :class="{open: isShowMoreAddBtns}" v-else>
        <div class="ui-select-link-trigger" @click="showMore">
            <div class="ui-link-txt">{{ placeholder }}</div>
            <i class="vd-ui_icon icon-down"></i>
        </div>
        <div class="ui-select-btn-drop">
            <slot><slot>
        </div>
    </div>`,
    props: {
       type: {
            type: String,
            default: 'button' //'button' or 'text'
       },
       placeholder: {
            type: String,
            default: '请选择'
       }
    },
    data() {
        return {
            isShowMoreAddBtns: false
        }
    },
    computed: {
        
    },
    mounted(){
        document.addEventListener('click', () => {
            setTimeout(() => {
                this.isShowMoreAddBtns = false;
            }, 50)
        })
    },
    methods: {
        handleClick(event) {
            this.$emit('click', event)
        },
        showMore() {
            setTimeout(() => {
                this.isShowMoreAddBtns = !this.isShowMoreAddBtns;
            }, 100)
        }
    }
})

Vue.component('ui-cascader-multiple', {
    template: `
        <div class="menu-wrap">
            <ul 
                v-if="list.length"
                class="vd-ui-cascader-node multiple"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in list" 
                    :key="index"
                    @click="openL1(item)"
                    :class="{'selected': tempChoose[0] && tempChoose[0].value == item.value }"
                    :datatt="JSON.stringify(item)"
                >
                    <div class="ui-cascader-checkbox-wrap"
                        :class="{'active': item.checked || item.indeterminate}"
                        @click.stop="clickCheckbox(item, 1)"
                    >
                        <i class="vd-ui_icon icon-selected2" v-if="item.checked"></i>
                        <i class="vd-ui_icon icon-deduct" v-else-if="item.indeterminate"></i>
                    </div>
                    
                    <p>{{item.label}}</p>
                    <i class="vd-ui_icon icon-app-right icon-right" v-if="item.children && item.children.length"></i>
                </li>
            </ul>

            <ul 
                v-if="level2List.length"
                class="vd-ui-cascader-node multiple"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in level2List" 
                    :key="index"
                    @click="openL2(item)"
                    :class="{'selected': tempChoose[1] && tempChoose[1].value == item.value }"
                >
                    <div class="ui-cascader-checkbox-wrap"
                        :class="{'active': item.checked || item.indeterminate}"
                        @click.stop="clickCheckbox(item, 2)"

                    >
                        <i class="vd-ui_icon icon-selected2" v-if="item.checked"></i>
                        <i class="vd-ui_icon icon-deduct" v-else-if="item.indeterminate"></i>
                    </div>
                    <p>{{item.label}}</p>
                    <i class="vd-ui_icon icon-app-right icon-right" v-if="item.children && item.children.length"></i>
                </li>
            </ul>

            <ul 
                v-if="level3List.length"
                class="vd-ui-cascader-node multiple"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in level3List" 
                    :key="index"
                    @click="openL3(item)"
                    :class="{'selected': tempChoose[2] && tempChoose[2].value == item.value }"
                >
                    <div class="ui-cascader-checkbox-wrap"
                        :class="{'active': item.checked}"
                        @click.stop="clickCheckbox(item, 3)"
                    >
                        <i class="vd-ui_icon icon-selected2" v-if="item.checked"></i>
                    </div>
                    <p>{{item.label}}</p>
                </li>
            </ul>
        </div>
    `,

    props: {
        data: {
            type:Array,
            default:()=> {
                return []
            }
        },
        selectObj: {
            type: Array
        },
        styles: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        multiple: {
            type: Boolean,
            default: false
        },
        multipleLabel: {
            type: Array
        }
    },
    data () {
        return {
            list: [], // 全部数据
            level2List: [],
            level3List: [],
            tempChoose: [], // 选中项记录
        }
    },
    computed: {
    },
    watch: {
        data: {
            handler () {
                console.log('组件内 data', this.data);
            },
            deep: true,
        }
    },
    mounted() {
        this.list = this.data;
    },
    methods: {
        openL1 (val) {
            this.tempChoose = [val];
            this.level2List = val.children || [];
            this.level3List = [];
        },
        openL2 (val) {
            this.tempChoose = [this.tempChoose[0], val];
            this.level3List = val.children || [];
        },
        openL3 (val) {
            this.tempChoose = [this.tempChoose[0], this.tempChoose[1], val];
        },


        clickCheckbox (item, ind) {
            ind == 1 && this.openL1(item);
            ind == 2 && this.openL2(item);
            ind == 3 && this.openL3(item);
            this.changeCheckbox(item, ind);
        },
        // 修改复选框的值
        changeCheckbox (item, ind) {
            let childs = item.children || [];
            if (item.indeterminate) {
                item.indeterminate = false;
                item.checked = true;
                childs.length && this.changeChildCheckbox(childs, true); // 子集全部选中
            } else if (item.checked) {
                item.indeterminate = false;
                item.checked = false;
                childs.length && this.changeChildCheckbox(childs, false); // 子集全部取消选中
            } else {
                item.indeterminate = false;
                item.checked = true;
                childs.length && this.changeChildCheckbox(childs, true); // 子集全部选中
            }

            this.$emit('pick', this.list);
        },

        // 子集: 全选/全不选
        changeChildCheckbox (data, bool) {
            data.forEach(item=> {
                this.$set(item, 'checked', bool);
                this.$set(item, 'indeterminate', false);

                if (item.children && item.children.length) {
                    this.changeChildCheckbox(item.children, bool);
                }
            })
        },
    }
})
Vue.component('ui-cascader-panel', {
    template: `
        <div class="menu-wrap">
            <ul 
                v-if="level1List.length"
                class="vd-ui-cascader-node"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in level1List" 
                    :key="index"
                    @click="openL1(item)"
                    :class="{'selected': tempChoose[0] && tempChoose[0].value == item.value }"
                >
                    <p v-html="item.label"></p>
                    <i class="vd-ui_icon icon-app-right icon-right" v-if="item.children && item.children.length"></i>
                    <i class="vd-ui_icon icon-selected2 icon-right" v-else-if="tempChoose[0] && tempChoose[0].value == item.value"></i>
                </li>
            </ul>

            <ul 
                v-if="level2List.length"
                class="vd-ui-cascader-node"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in level2List" 
                    :key="index"
                    @click="openL2(item)"
                    :class="{'selected': tempChoose[1] && tempChoose[1].value == item.value }"
                >
                    <p v-html="item.label"></p>
                    <i class="vd-ui_icon icon-app-right icon-right" v-if="item.children && item.children.length"></i>
                    <i class="vd-ui_icon icon-selected2 icon-right" v-else-if="tempChoose[1] && tempChoose[1].value == item.value"></i>
                </li>
            </ul>

            <ul 
                v-if="level3List.length"
                class="vd-ui-cascader-node"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in level3List" 
                    :key="index"
                    @click="openL3(item)"
                    :class="{'selected': tempChoose[2] && tempChoose[2].value == item.value }"
                >
                    <p v-html="item.label"></p>
                    <i class="vd-ui_icon icon-selected2 icon-right" v-if="tempChoose[2] && tempChoose[2].value == item.value"></i>
                </li>
            </ul>
        </div>
    `,

    props: {
        data: {
            type:Array,
            default:()=> {
                return []
            }
        },
        selectObj: {
            type: Array
        },
        styles: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        multiple: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            level1List: [],
            level2List: [],
            level3List: [],
            tempChoose: [],
        }
    },
    computed:{
    },
    watch: {
        selectObj: {
            deep: true,
            handler (newV) {
                if (newV.length) {
                    this.tempChoose = JSON.parse(JSON.stringify(newV));
                    let l1Info = this.data.filter(item=> item.value == newV[0].value)[0] || {};
                    let l2List = l1Info.children || [];
                    this.level2List = l2List;

                    if (newV[1] && newV[1].value) {
                        let l2Info = l2List.filter(item=> item.value == newV[1].value)[0] || {};
                        let l3List = l2Info.children || [];
                        this.level3List = l3List;
                    } else {
                        this.level3List = [];
                    }
                }
            },
            immediate: true
        }
    },
    mounted() {
        this.level1List = JSON.parse(JSON.stringify(this.data));
    },
    methods: {
        openL1 (val) {
            this.tempChoose = [val];
            this.level2List = val.children || [];
            this.level3List = [];

            if (!this.level2List.length) { // 直接选中
                let arr =  [{
                    label: val.label,
                    value: val.value,
                    level: val.level
                }]
                this.$emit('pick', arr);
            } 
        },
        openL2 (val) {
            this.tempChoose = [this.tempChoose[0], val];
            this.level3List = val.children || [];

            if (!this.level3List.length) { // 直接选中
                this.$emit('pick', this.tempChoose);
            }
        },
        openL3 (val) {
            this.tempChoose = [this.tempChoose[0], this.tempChoose[1], val];
            this.$emit('pick', this.tempChoose);
        },
    }
})
var Minxin_cascader_transition = {
    mounted() {
        window.addEventListener('scroll',this.cascaderListScroll,true) //改为true是从外到里，事件捕获，false是从里到外，事件冒泡
        window.addEventListener('resize',this.cascaderListScroll)
    },
    methods: {
        beforeLeave(el) {
            el.style.webkitTransform = 'scale(1,1)';
            el.style.opacity = 1;
        },
        leave(el) {
            if (el.scrollHeight !== 0) {
                el.style.transition = '0.19s all ease-in';
                el.style.webkitTransform = 'scale(1,0)';
                el.style.opacity = 0;
            }
        },
        afterLeave(el) {
            el.style.transition = '';
            el.style.webkitTransform = '';
            el.style.opacity = '';
        },
        topScroll() {
            let client = this.$el.getBoundingClientRect();
            let height = this.$refs.cascaderList && this.$refs.cascaderList.clientHeight;
            let clientHeight = document.body.clientHeight;
            if (client.bottom + height + 7 > clientHeight && client.top >= height+2) {
                this.animation = 'appear-up';
                if (this.$refs.cascaderList) {
                    this.$refs.cascaderList.style.top = `-${height+2}px`;
                    this.$refs.cascaderList.style.boxShadow = 'rgba(0, 0, 0, 0.1) 0px -5px 10px';
                }
            } else {
                this.animation = 'appear-down';
                if (this.$refs.cascaderList) {
                    this.$refs.cascaderList.style.top = "";
                    this.$refs.cascaderList.style.boxShadow = '';
                }
            }
        },
        cascaderListScroll() {
            if (this.$refs.cascaderList) {
                this.topScroll()
            }
        },
    }
}

Vue.component('ui-cascader', {
    template: `
        <div 
            class="vd-ui-cascader" 
            :style="{'width':width}" 
            @mouseenter="handleEnter" 
            @mouseleave="handleLeave" 
        >
            <div 
                class="vd-ui-cascader-wrapper" 
                @click="handleClick" 
            >
                <div v-if="multiple" class="multiple-wrap">
                    <div v-if="multipleTags.length" class="multiple-tags">
                        <span class="tag text-line-1">{{oneTagShow.show}}
                            <span class="tag-del" @click="clearSelect(oneTagShow.value)"><i class="vd-ui_icon icon-delete"></i></span>
                        </span>
                        <span class="tag-add" v-if="multipleTags.length > 1">+{{multipleTags.length-1}}</span>
                    </div>
                    <div class="input">
                        <input 
                            v-model="multipleInput"
                            :placeholder="multipleTags.length? '': holder"
                            :disabled="disabled"
                            @focus="focus" 
                            @blur="blur" 
                            @input="nativeOninput"
                            @keydown.tab="rotate=false" 
                            :readonly="!filterable" 
                            autocomplete="off" 
                        />
                    </div>
                    <div class="vd-ui-input__suffix">
                        <span class="vd-ui-input__suffix-inner">
                            <i v-if="filterable" class="vd-ui_icon icon-search icon"></i>
                            <i v-else class="j2 vd-ui_icon icon-down icon" :class="[rotate? 'rotate': '']"></i>
                        </span>
                    </div>
                </div>
                <template v-else>
                    <ui-input 
                        v-model="showLabel" 
                        :placeholder="holder" 
                        :disabled="disabled" 
                        @focus="focus" 
                        @blur="blur" 
                        @input="oninput"
                        @clear="clearData" 
                        @keydown.native.tab="rotate=false" 
                        width="100%"
                        :readonly="!filterable" 
                        autocomplete="off" 
                        :clearable="clearable" 
                        :select-clear="selectClear" 
                    >
                        <template v-if="filterable">
                            <i slot="suffix" class="vd-ui_icon icon-search icon" v-if="iconClear"></i>
                        </template>
                        <template v-else>
                            <i slot="suffix" class="vd-ui_icon icon-down icon" v-if="iconClear" :class="[rotate? 'rotate': '']"></i>
                        </template>
                    </ui-input>
                </template>
            </div>
            <div class="vd-ui-input-error" v-if="errorable && !disabled">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
            <transition 
                @before-leave="beforeLeave"
                @leave="leave"
                @after-leave="afterLeave"
            >
                <div v-if="rotate" class="vd-ui-cascader-menu" ref="cascaderList" :class="[animation ? animation:'', {'width': filtering}]" @click.stop>
                    <div v-if="filtering" class="suggestion-list">
                        <template v-if="loading">
                            <div class="loading-li" :style="{'width':width}">
                                <p>
                                    <i class="vd-ui_icon icon-loading" ref="loading"></i>
                                    <span>加载中...</span>
                                </p>
                            </div>
                        </template>
                        <div v-if="filterResult.length" class="filter-list">
                            <div 
                                class="filter-item" 
                                v-for="(item, index) in filterResult" :key="index"
                                @click.stop="handleSuggestionClick(item)"
                            >
                                <div v-if="multiple" class="ui-cascader-checkbox-wrap" :class="{'active': isCheck(item)}">
                                    <i class="vd-ui_icon icon-selected2" v-if="isCheck(item)"></i>
                                </div>
                                <div v-html="suggestShow(item)"></div>
                            </div>
                        </div>
                        <div class="no-filter" v-else>无匹配数据</div>
                    </div>
                    <div v-else-if="multiple">
                        <ui-cascader-multiple
                            ref="multiplPanel"
                            :data="list"
                            :selectObj="selectObj"
                            :styles="style"
                            :multiple="multiple"
                            @pick="handleMultipleChange"
                        ></ui-cascader-multiple>
                    </div>
                    <div v-else>
                        <ui-cascader-panel
                            :data="list"
                            :selectObj="selectObj"
                            :styles="style"
                            @pick="handleChange"
                        ></ui-cascader-panel>
                    </div>
                </div>
            </transition>
        </div>
    `,

    mixins: [Minxin_cascader_transition],
    props: {
        placeholder: {
            type: String,
            default: '请选择'
        },
        data: {
            type:Array,
            default:()=> {
                return []
            }
        },
        showVal: {
            default: '',
        },
        disabled: {
            type: Boolean,
            default: false
        },
        width: {
            type: String,
            default: '300px'
        },
        maxHeight: {
            type: String,
            default: '300px'
        },
        listWidth: {
            type: String,
            default: '150px'
        },
        clearable: {
            type: Boolean,
            default: false
        },
        // 可搜索
        filterable: {
            type: Boolean,
            default: false
        },
        // 多选
        multiple: {
            type: Boolean,
            default: false
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        valid: {
            type: String,
            default: ''
        }
    },
    model: {
        prop:'showVal',
        event:'value'
    },
    provide() {
        return {
            casca: this
        }
    },
    data() {
        return {
            rotate: false,     // 下拉框
            showLabel: '',
            animation: '',
            holder: this.placeholder,
            selectObj: [],    // 选中的对象
            entered: false,   // 鼠标进入选择框为true
            clearFlag: false,
            liList: [],

            filterResult: [],
            filtering: false,
            loading: false, // 正在搜索
            multipleTags: [], // 多选结果
            multipleInput: '',
        }
    },
    computed: {
        // 多选时 首个tag展示
        oneTagShow () {
            let tag = this.multipleTags[0] || [];
            if (!tag.length) return {show: ''};

            let l1 = this.list.filter(item => item.value == tag[0])[0] || {};
            let name = l1.label || '';
            let tagValue = [l1.value];
            if (tag[1]) {
                let l2 = l1.children.filter(item => item.value == tag[1])[0] || {};
                name += ` / ${l2.label}`;
                tagValue.push(l2.value);

                if (tag[2]) {
                    let l3 = l2.children.filter(item => item.value == tag[2])[0] || {};
                    name += ` / ${l3.label}`;
                    tagValue.push(l3.value);
                }
            }

            return {
                show: name,
                value: tagValue
            }
        },
        list() {
            return this.saveIn(JSON.parse(JSON.stringify(this.data)), 1);
        },
        style() {
            let style = {};
            style.maxHeight = this.maxHeight;
            style.width = this.listWidth;
            return style;
        },
        iconClear() {
            return !this.clearable || !(this.selectObj && this.selectObj.length) || !(this.entered || this.clearFlag);
        },
        selectClear() {
            return this.selectObj && this.selectObj.length ? true : false;
        },
        selectStr () {
            let str = '';
            this.selectObj.forEach(item=> {
                str += str ? ' / '+ item.label : item.label;
            })
            return str || '';
        }
    },
    watch: {
        rotate() {
            if (this.rotate) {
                this.holder = this.showLabel || this.holder;
                if (!this.filterable) {
                    this.showLabel = '';
                }
                this.$nextTick(()=>{
                    this.topScroll(); // 下拉框动效
                })
            } else {
                this.multipleInput = ''; // 多选input清空
                this.filtering = false;  // 关闭搜索面板
                this.holder = this.placeholder;
                this.showLabel = '';
                this.selectObj.forEach(item=> {
                    this.showLabel += this.showLabel ? ' / '+ item.label : item.label;
                })
            }
        },
        data: {
            handler () {
                if (this.showVal && this.showVal.length) {
                    this.showLabel = '';
                    this.selectObj = [];
    
                    if (this.multiple) { // 多选
                        if (this.showVal && Object.keys(this.showVal)) {
                            this.echoMultiple(this.list, this.showVal);
                        }
                    } else if (this.showVal && this.showVal.length) {
                        this.liList = [];
                        this.echoObj(this.list, this.showVal, 0);
                    }
                }
            },
            deep: true,
            immediate: true,
        },
        showVal: {
            handler (newV) {
                this.showLabel = '';
                this.selectObj = [];

                if (this.multiple) { // 多选
                    if (newV && Object.keys(newV)) {
                        this.echoMultiple(this.list, newV);
                    }
                } else if (newV && newV.length) {
                    this.liList = [];
                    this.echoObj(this.list, this.showVal, 0);
                }
            },
            immediate: true,
            deep: true,
        }
    },
    mounted() {
        this.$form.setValidEl(this);
        document.addEventListener('click',(e)=>{
            if (!this.$el.contains(e.target)) {
                this.rotate = false;
                this.filtering = false;
            }
        })
    },
    methods: {
        saveIn(arr, level) { // 数组增加是否打开字段
            for (let i in arr) {
                this.$set(arr[i], 'level', level);
                // 多选初始化
                // if (this.multiple) {
                //     需判断子集是否全部选中，以及子集是否需要全部选中等???
                //     this.$set(arr[i], 'checked', falses);
                //     this.$set(arr[i], 'indeterminate', false);
                // }

                if (arr[i].children && arr[i].children.length) {
                    this.saveIn(arr[i].children, level + 1);
                }
            }
            return arr;
        },
        echoObj(list,arr,j) { // 框的初始值回显
            // 根据数组， 计算出初始selectObj ???
            for ( let i in list) {
                if (list[i].value == arr[j]) {
                    if ( list[i].children && list[i].children.length ) {
                        this.liList.push(JSON.parse(JSON.stringify(list[i])))
                        this.echoObj(list[i].children,arr,j+1)
                    } else {
                        this.liList.push(JSON.parse(JSON.stringify(list[i])))

                        this.liList.forEach(item=>{ // 将选择的层级名称保存
                            this.selectObj.push({
                                label:item.label,
                                value:item.value,
                                level:item.level
                            })
                        })
                        this.selectObj.forEach(item=>{
                            this.showLabel += this.showLabel ? ' / '+ item.label : item.label;
                        })
                    }
                }
            }
        },
        // 回显多选值
        echoMultiple (list, data) {
            let class1 = data.level1 || [];
            let class2 = data.level2 || [];
            let class3 = data.level3 || [];
            

            list.forEach(item1 => {
                let childs1 = item1.children || [];
                if (class1.includes(String(item1.value)) || class1.includes(Number(item1.value))) { // 一级选中
                    this.$set(item1, 'checked', true);
                    this.$set(item1, 'indeterminate', false);
                    this.changeChildCheckbox(childs1, true);
                } else {
                    // 循环一级的chlld，
                    childs1.forEach(item2=> {
                        let childs2 = item2.children || [];
                        if (class2.includes(String(item2.value)) || class2.includes(Number(item2.value))) { // 二级选中
                            this.$set(item2, 'checked', true);
                            this.$set(item2, 'indeterminate', false);
                            this.changeChildCheckbox(childs2, true);
                        } else {
                            // 循环二级下的child
                            childs2.forEach(item3 => {
                                if (class3.includes(String(item3.value)) || class3.includes(Number(item3.value))) { // 三级选中
                                    this.$set(item3, 'checked', true);
                                }
                            })
                        }
                    })
                }
            })
            this.checkAllStatus();
            this.dealChooseTag();
        },
        focus(event) {
            this.clearFlag = this.clearable;
            this.$emit('focus', event);

            // if (this.filterable) {
            //     this.$nextTick(()=>{
            //         this.topScroll(); // 下拉框动效
            //     })
            // } ???
        },
        handleClick() {
            if (this.disabled) return;
            if (this.filterable) {
                this.rotate = true;
                return;
            }
            this.rotate = !this.rotate;
        },
        blur(event) {
            this.clearFlag = false;
            this.$emit('blur', event);
            this.checkValid(this.selectObj);
        },
        clearData() {
            this.showLabel = '';
            this.holder = this.placeholder;
            this.clearFlag = false;
            this.selectObj = [];
            this.selectValue([]);
            this.changeVal([]);
            this.rotate = false;
        },
        handleEnter() {
            this.entered = true;
        },
        handleLeave() {
            this.entered = false;
        },
        // 更改v-model绑定的值
        selectValue(val) {
            this.$emit('value',val);
        },
        changeVal(newValue) {
            this.$emit('change', newValue);
            this.checkValid(this.selectObj);
        },
        handleReload() {
            this.$emit('reload');
        },
        handleChange (val) {
            this.selectObj = val;
            this.rotate = false;
            this.$emit('change', val);
            this.checkValid(this.selectObj);
        },


        /* 搜索面板 */
        nativeOninput (event) {
            let val = event.target.value;
            this.oninput(val);
        },
        oninput (event) {
            let val = event;
            if (!val) {
                this.filterResult = [];
                this.filtering = false;
                return;
            };
            if (val == this.selectStr) return;

            this.filterResult = this.getSuggestions(val);
            this.filtering = true;
            this.cascaderListScroll();
        },
        getSuggestionWord (val) {
            let splitArr = val.split('/');
            let words = [];
            if (splitArr.length > 1) {
                splitArr.forEach(item=> {
                    if (item.trim()) {
                        words.push(item.trim());
                    }
                })
            } else {
                words.push(splitArr[0].trim());
            }
            return words;
        },
        getSuggestions (val) {
            let words = this.getSuggestionWord(val);
            let searchRes = []; // 匹配结果

            if (words.length > 1) {
                this.list.forEach(L1 => {
                    // 匹配到第一级， 注: words第一级 不一定是 list第一级别
                    let level = words.length;
                    if (L1.label.includes(words[0])) {
                        // 继续匹配下一级
                        level--;
                        if (level && L1.children && L1.children.length) {
                            L1.children.forEach(L2 => {
                                if (L2.label.includes(words[words.length - level])) {
                                    level--;
                                    if (L2.children && L2.children.length) {
                                        if (level) {
                                            L2.children.forEach(L3=> {
                                                if (L3.label.includes(words[words.length - level])) {
                                                    searchRes.push({
                                                        l1: L1,
                                                        l2: L2,
                                                        l3: L3
                                                    })
                                                }
                                            })
                                        } else {
                                            L2.children.forEach(L3=> {
                                                searchRes.push({
                                                    l1: L1,
                                                    l2: L2,
                                                    l3: L3
                                                })
                                            })
                                        }
                                    } else {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                        })
                                    }
                                }
                            })
                        }
                    } else {
                        // 一级没匹配到, 继续从第二级比较
                        L1.children && L1.children.length && L1.children.forEach(L2 => {
                            if (L2.label.includes(words[0])) {
                                level--;
                                if (level && L2.children && L2.children.length) {
                                    L2.children.forEach(L3 => {
                                        if (L3.label.includes(words[words.length - level])) {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2,
                                                l3: L3
                                            })
                                        }
                                    })
                                }
                            }
                        })
                    }
                })
            } else if (words.length == 1) {
                let word = words[0].trim();

                this.list.forEach(L1 => {
                    // 一级匹配, 则匹配结果包含所有子集
                    if (L1.label.includes(word)) {
                        if (L1.children) {
                            if (L1.children && L1.children.length) {
                                L1.children.forEach(L2 => {
                                    if (L2.children && L2.children.length) {
                                        L2.children.forEach(L3 => {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2,
                                                l3: L3
                                            })
                                        })
                                    } else {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2
                                        })
                                    }
                                })
                            } else {
                                searchRes.push({
                                    l1: L1,
                                })
                            }
                        }
                    }
                    // 一级不匹配, 继续轮循下面二级
                    else {
                        L1.children && L1.children.length && L1.children.forEach(L2 => {
                            if (L2.label.includes(word)) {
                                if (L2.children && L2.children.length) {
                                    L2.children.forEach(L3 => {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                            l3: L3
                                        })
                                    })
                                } else {
                                    searchRes.push({
                                        l1: L1,
                                        l2: L2
                                    })
                                }
                            } 
                            // 二级不匹配, 继续轮循下面三级
                            else {
                                L2.children && L2.children.length && L2.children.forEach(L3 => {
                                    if (L3.label.includes(word)) {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                            l3: L3
                                        })
                                    }
                                })
                            }
                        })
                    }
                })
            }

            if (searchRes.length > 100) {
                searchRes = searchRes.slice(0, 100);
            }
            return searchRes;
        },
        suggestShow(item) {
            let str = '';
            if (item.l1) {
                str += item.l1.label;
            }
            if (item.l2) {
                str += ` / ${item.l2.label}`;
            }
            if (item.l3) {
                str += ` / ${item.l3.label}`;
            }

            const keywords = this.getSuggestionWord(this.showLabel);
            keywords.sort((a, b) => b.length - a.length);
            for (const keyword of keywords) {
                const regExp = new RegExp(keyword, 'g');
                str = str.replace(regExp, `<font color='#FF6600'">${keyword}</font>`);
            }
            return str;
        },
        // 搜索面板选中
        handleSuggestionClick (item) {
            if (this.multiple) { // 多选
                this.handlerMultipleClick(item);
                return;
            }

            // 单选
            let arr = [];
            if (item.l1) {
                arr.push({ label: item.l1.label, level: 1, value: item.l1.value });
            }
            if (item.l2) {
                arr.push({ label: item.l2.label, level: 2, value: item.l2.value });
            }
            if (item.l3) {
                arr.push({ label: item.l3.label, level: 3, value: item.l3.value });
            }
            
            this.selectObj = arr;
            this.rotate = false;
            this.handleChange(arr);
        },


        /* 以下多选逻辑 **/
        // 子集: 全选/全不选
        changeChildCheckbox (data, bool) {
            if (!(data && data.length)) return;
            data.forEach(item=> {
                this.$set(item, 'checked', bool);
                this.$set(item, 'indeterminate', false);

                if (item.children && item.children.length) {
                    this.changeChildCheckbox(item.children, bool);
                }
            })
        },
        // 验证搜索面板中选中状态
        isCheck(item) {
            arr = [];
            if (item.l1) {
                arr.push(item.l1.value);
            }
            if (item.l2) {
                arr.push(item.l2.value);
            }
            if (item.l3) {
                arr.push(item.l3.value);
            }

            let list = this.multipleTags.map(item=> {
                return item.join('_');
            })
            return list.includes(arr.join('_'));
        },
        // 搜索面板 选中某项
        handlerMultipleClick (item) {
            if (item.l3) {
                // 初始化set后 此处无需再判断，用注释语句即可???
                if (!item.l3.checked) {
                    this.$set(item.l3, 'checked', true);
                } else {
                    this.$set(item.l3, 'checked', false);
                }
            } else if (item.l2) {
                if (!item.l2.checked) {
                    this.$set(item.l2, 'checked', true);
                } else {
                    this.$set(item.l2, 'checked', false);
                }
            } else {
                if (!item.l1.checked) {
                    this.$set(item.l1, 'checked', true);
                } else {
                    this.$set(item.l1, 'checked', false);
                }
            }
            this.checkAllStatus();
            this.handleMultipleChange();
        },
        // 清除选项
        clearSelect() {
            let a = this.multipleTags.shift(); // 改成查询当前项
            let l1Info = this.list.filter(IETM1 => IETM1.value == a[0])[0] || {};

            if (a[1] && l1Info.children) {
                let l2Info = l1Info.children.filter(ITEM2=>ITEM2.value == a[1])[0] || {};

                if (a[2] && l2Info.children) {
                    let l3Info = l2Info.children.filter(IETM3 => IETM3.value == a[2])[0] || {};
                    l3Info.checked = false;
                    l3Info.indeterminate = false;
                } else {
                    l2Info.checked = false;
                    l2Info.indeterminate = false;
                }
            } else {
                l1Info.checked = false;
                l1Info.indeterminate = false;
            }
            this.checkAllStatus();
            this.handleMultipleChange();
        },
        // 计算所有选中项【回显到框中】  //有时间dealChooseTag可改递归 ???
        dealChooseTag () {
            let arr = [];

            let filter1 = this.list.filter(f1 => f1.checked || f1.indeterminate);
            filter1.forEach(item => {  // 选中/未全部选中的一级列表
                let childs1 = item.children || []; // childs1 二级列表
                if (item.checked) {// 一级选中 轮循取下面所有的
                    childs1.length && childs1.forEach(item2 => {
                        let child2 = item2.children || [];
                        child2.length && child2.forEach(item3 => {
                            arr.push([item.value, item2.value, item3.value]);
                        })
                        !child2.length && arr.push([item.value, item2.value]);
                    })
                    !childs1.length && arr.push([item.value]);
                } else if (item.indeterminate) { // 一级未全部选中
                    if (childs1.length) {
                        let filter2 = childs1.filter(f2 => f2.checked || f2.indeterminate); // 选中/未全部选中的二级列表
                        filter2.forEach(item2 => {
                            if (item2.checked) { // 二级选中 轮循取以下所有三级
                                let childs2 = item2.children || []; // 三级列表
                                childs2.length && childs2.forEach(item3 => {
                                    arr.push([item.value, item2.value, item3.value]);
                                })
                                !childs2.length && (arr.push([item.value, item2.value]));
                            } else if (item2.indeterminate) {
                                let child2 = item2.children || [];
                                child2.length && item2.children.forEach(item3 => {
                                    if (item3.checked) {
                                        arr.push([item.value, item2.value, item3.value])
                                    }
                                })
                            }
                        });
                    }
                }
            })
            this.multipleTags = arr;
        },
        // 组件内传出来的数据
        handleMultipleChange () {
            this.checkAllStatus();
            this.dealChooseTag();
            let final = this.handlerQuery();
            this.$emit('change', final);
        },

        // 验证所有层级 全选/部分选状态 递归???
        checkAllStatus() {
            this.list.forEach(item1 => {
                if (item1.children && item1.children.length) {
                    // 先判断二级的状态
                    item1.children.forEach(item2 => {
                        if (item2.children && item2.children.length) {
                            let arr = item2.children.filter(f => f.checked);
                            if (arr.length == item2.children.length) {
                                item2.checked = true;
                                item2.indeterminate = false;
                            } else if (arr.length >= 1) {
                                item2.checked = false;
                                item2.indeterminate = true;
                            } else {
                                item2.checked = false;
                                item2.indeterminate = false;
                            }
                        }
                    })

                    // 判断一级的状态                            
                    let item1_check = item1.children.filter(f => f.checked);
                    let item1_inde = item1.children.filter(f => f.checked || f.indeterminate);

                    if (item1_check.length == item1.children.length) {
                        item1.checked = true;
                        item1.indeterminate = false;
                    } else if (item1_inde.length) {
                        item1.checked = false;
                        item1.indeterminate = true;
                    } else {
                        item1.checked = false;
                        item1.indeterminate = false;
                    }
                }
            })
        },

        // 入参格式
        handlerQuery() {
            class1 = [];
            class2 = [];
            class3 = [];

            this.list.forEach(item1 => {
                if (item1.checked) {
                    class1.push(item1.value);
                } else if (item1.indeterminate) {
                    let item1Child = item1.children || [];
                    item1Child.forEach(item2 => {
                        if (item2.checked) {
                            class2.push(item2.value);
                        } else if (item2.indeterminate) {
                            let item2Child = item2.children || [];
                            item2Child.forEach(item3=> {
                                if (item3.checked) {
                                    class3.push(item3.value);
                                }
                            })
                        }
                    })
                }
            })
            return {
                level1: class1,
                level2: class2,
                level3: class3,
            };
        },
        checkValid(newValue) {
            if (this.validKey && this.validValue) {
                if (this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if (validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    }
})
Vue.component('ui-checkbox', {
    template: `
        <div
            class="vd-ui-checkbox-item"
            :class="{
                'vd-ui-checkbox-item-checked': currentChecked,
                'vd-ui-checkbox-item-disabled': disabled,
                'vd-ui-checkbox-item-progress': isSelectedAll && onProgress === 2,
            }"
            @click="handlerClick(!currentChecked)"
            :title="label"
        >
            <div class="vd-ui-checkbox-inner">
                <div class="vd-ui-checkbox-icon">
                    <div class="vd-ui-checkbox-icon-selected2">
                        <i class="vd-ui_icon icon-selected2"></i>
                    </div>
                </div>
                <span v-html="label"></span>
            </div>
        </div>
    `,
    props: {
        label: {
            type: String,
            default: "",
        },
        checked: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        onProgress: {
            type: Number,
            default: 1,
        },
        isSelectedAll: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            currentChecked: false,
        };
    },
    mounted() {
        this.currentChecked = this.checked;
    },
    watch: {
        onProgress() {
            this.currentChecked = this.onProgress === 3;
            this.$emit("update:checked", this.onProgress === 3);
        },
        checked() {
            this.currentChecked = this.checked;
        },
    },
    methods: {
        handlerClick() {
            if (this.isSelectedAll) {
                if (this.onProgress === 3) {
                    this.$emit("update:onProgress", 1);
                } else {
                    this.$emit("update:onProgress", 3);
                }
            }
            
            if (!this.disabled) {
                this.currentChecked = !this.currentChecked;
                this.$emit("update:checked", this.currentChecked);
                this.$emit("change", this.currentChecked);
            }
        },
    },
})
Vue.component('ui-checkbox-group', {
    template: `
        <div class="vd-ui-checkbox-group">
            <template v-for="(item, index) in boxList">
                <ui-checkbox
                    :key="index"
                    :label="item.label"
                    :checked.sync="item.checked"
                    :disabled="item.disabled"
                    @change="handlerChange"
                ></ui-checkbox>
            </template>
            <div class="vd-ui-input-error" v-if="errorable">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,
    watch: {
        list() {
            this.setList();
        },
        onProgress() {
            this.boxList.forEach((item, index) => {
                if (this.onProgress === 3) {
                    this.$set(this.boxList[index], "checked", true);
                } else if (this.onProgress === 1) {
                    this.$set(this.boxList[index], "checked", false);
                }
            });

            if (this.onProgress !== 2) {
                this.handlerChange(null, true);
            }
        },
    },
    data() {
        return {
            boxList: [],
        };
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        values: {
            type: Array,
            default() {
                return [];
            },
        },
        onProgress: {
            type: Number,
            default: 1,
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
    },
    mounted() {
        this.$form.setValidEl(this);
        this.setList();
    },
    methods: {
        setList() {
            this.boxList = JSON.parse(JSON.stringify(this.list));

            this.boxList.forEach((item) => {
                this.values.forEach((value) => {
                    if (item.value == value) {
                        item.checked = true;
                    }
                });
            });

            this.checkOnProgress();
        },
        handlerChange(data, silent) {
            console.log('boxList:', this.boxList);
            let values = [];
            this.boxList.forEach((item) => {
                if (item.checked) {
                    values.push(item.value);
                }
            });

            if (values.join('__') !== this.values.join('__')) {
                this.$emit("update:values", values);
                this.$emit("change", values);
                
                this.checkValid(values);

                if (!silent) {
                    this.$nextTick(() => {
                        this.checkOnProgress();
                    });
                }
            }
        },
        checkOnProgress() {
            if (
                this.values.length &&
                this.values.length === this.boxList.length
            ) {
                this.$emit("update:onProgress", 3);
            } else if (!this.values.length) {
                this.$emit("update:onProgress", 1);
            } else {
                this.$emit("update:onProgress", 2);
            }
        },
        selectAll() {
            this.boxList.forEach((item) => {
                item.checked = true;
            });

            this.handlerChange();
        },
        checkValid(newValue) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    },
})
Vue.component('ui-date-poper', {
    template: `<div class="ui-poper-wrap" ref="popwrap" :style="'width:' + dropPosition.width + ';left:' +  dropPosition.left + 'px;top:' +  dropPosition.top + 'px;z-index:' + dropPosition.zindex">
        <slot></slot>
    </div>`,
    props: {
        show: {
            type: Boolean,
            default: false
        },
        errorable: {
            type: Boolean,
            default: false
        },
        el: {
            type: Object,
            default() {
                return null
            }
        },
        panel: {
            type: Object,
            default() {
                return null
            }
        },
        zindex: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            dropPosition: {},
            parent: null
        }
    },
    watch: {
        show() {
            if (this.show) {
                document.body.append(this.$refs.popwrap);
                console.log(this.parent)
                this.$nextTick(()=> {
                    this.calcPosition();
                })
            } else {
                this.$refs.popwrap.remove();
            }
        },
    },
    computed: {

    },
    mounted() {
        let parent = this.getScrollParent(this.$parent.$el) || window;
        
        parent.addEventListener('scroll', this.calcPosition);
        window.addEventListener('resize', this.calcPosition);

        this.parent = parent;
    },
    methods: {
        calcPosition () {
            if(!this.show) {
                return;
            }

            let bodyWidth = document.body.clientWidth;
            let bodyHeight = document.body.clientHeight;
            let panel = this.panel || this.$parent.$refs.pickers; // 面板
            let panelWidth = panel.clientWidth;
            let panelHeight = panel.clientHeight;

            let el = this.el || this.$parent.$el; // 输入框
            let inputPosition = el.getBoundingClientRect();

            // 水平
            let left = inputPosition.left;
            let diff = left + panelWidth - bodyWidth;
            if ((left + panelWidth) > bodyWidth) {
                if (left > diff) { // 左侧距离足够放面板
                    left = left - (panelWidth - inputPosition.width)
                } else {
                    left = `-${diff}px`;
                }
            }

            // 垂直
            let top = inputPosition.top + inputPosition.height;
            if ((top+panelHeight) > bodyHeight) {
                if (top >= panelHeight) { // top够放panel
                    top = top - panelHeight - inputPosition.height - 2;
                } else { // top也不够放panel -- 不处理
                    if (this.errorable) {
                        top = top - 26;
                    }
                }
            } else { // 下
                if (this.errorable) {
                    top = top - 26;
                }
            }

            let width = el.offsetWidth + 'px';

            this.dropPosition = {
                width: width,
                top: top,
                left: left,
                zindex: this.zindex || (this.parent === window ? 20 : 3000)
            }

            // 出场动效
            if (top >= inputPosition.top) {
                this.$parent.animation = 'appear-down';
            } else {
                this.$parent.animation = 'appear-up';
            }
        },
        // }, 100),
        getScrollParent(element) {
            if (!element) {
                return null;
            }

            const overflowRegex = /(scroll|auto)/;
            const parent = element.parentElement;

            if (parent && overflowRegex.test(window.getComputedStyle(parent).overflow + window.getComputedStyle(parent).overflowY + window.getComputedStyle(parent).overflowX)) {
                return parent;
            }

            return this.getScrollParent(parent);
        }
    }
})

// 日期公共方法
const util_date = {
    DEFAULT_FORMATS: {
        date: 'yyyy-MM-dd',
        month: 'yyyy-MM',
        datetime: 'yyyy-MM-dd HH:mm:ss',
        time: 'HH:mm:ss',
        week: 'yyyywWW',
        timerange: 'HH:mm:ss',
        daterange: 'yyyy-MM-dd',
        monthrange: 'yyyy-MM',
        datetimerange: 'yyyy-MM-dd HH:mm:ss',
        year: 'yyyy'
    },

    HAVE_TRIGGER_TYPES: [
        'date',
        'datetime',
        'time',
        'time-select',
        'week',
        'month',
        'year',
        'daterange',
        'monthrange',
        'timerange',
        'datetimerange',
        'dates'
    ],

    /* 日期部分 */
    weeks: ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'],
    months: ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
    monthView: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
    regDatetime: /^\s*[0-9]{1,4}-[0-9]{1,2}-[0-9]{1,2}\s*[0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2}\s*$/,

    // 小于10拼接0
    beautify: (str, max = 10000, min = 1)=> {
        if (!str) return '';
        str = parseInt(str);
        if (isNaN(str)) return '';
        if (max && typeof(max) != 'number') throw new Error('type error')

        if (str < 10) {
            return '0' + str
        } else if (str > max) {
            return ''
        } else {
            return str
        }
    },

    // 数组转化时间
    arrayToDate: (arr) => {
        if (!Object.prototype.toString.call(arr)) {
            return null;
        }
        let finalArr = arr.map(item => {
            if (util_date.isDate(item)) {
                return item
            }
            let d1 = util_date.toDate(item);
            if (util_date.isDate(d1) && d1 != 'Invalid Date') {
                return d1
            } else {
                return null
            }
        });

        return finalArr;
    },

    // 转化成日期格式
    toDate: function(date) {
        typeof(date) !== 'string' && ( date += '' );
        let val = new Date(date) || null;
        return val
    },

    // 判断是否是日期格式
    isDate: (val) =>  {
        return Object.prototype.toString.call(val) == '[object Date]';
    },

    // 返回默认当天的时间格式
    timeToDate: function (time) {
        if (!util_date.checkToTime(time)) return '';
        let date = new Date();
        let Y = date.getFullYear();
        let M = date.getMonth() + 1;
        let D = date.getDate();

        return new Date(`${ Y }-${ M }-${ D } ${ time }`)
    },

    // 返回日期事件格式
    timeToDatetime: (val) => {
        if (!util_date.checkToDatetime(val)) return '';

        let t = new Date(val);
        if (t == 'Invalid Date') return '';
        
        return new Date(`${ util_date.getDateStr(t) } ${ util_date.getTimeStr(t) }`);
    },

    // 获取当前月份 有几天
    getDayCountOfMonth: function(year, month) {
        if (month === 3 || month === 5 || month === 8 || month === 10) {
            return 30;
        }
        if (month === 1) {
            if (year % 4 === 0 && year % 100 !== 0 || year % 400 === 0) {
                return 29;
            } else {
                return 28;
            }
        }
        return 31;
    },

    getDayCountOfYear: function(year) {
        const isLeapYear = year % 400 === 0 || (year % 100 !== 0 && year % 4 === 0);
        return isLeapYear ? 366 : 365;
    },

    getFirstDayOfMonth: function(date) {
        const temp = new Date(date.getTime());
        temp.setDate(1);
        return temp.getDay();
    },

    prevDate: function(date, amount = 1) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate() - amount);
    },

    nextDate: function(date, amount = 1) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate() + amount);
    },

    getStartDateOfMonth: function(year, month) {
        const result = new Date(year, month, 1);
        const day = result.getDay();
    
        if (day === 0) {
            return util_date.prevDate(result, 7);
        } else {
            return util_date.prevDate(result, day);
        }
    },

    range: function(n) {
        return Array.apply(null, {length: n}).map((_, n) => n);
    },

    modifyDate: function(date, y, m, d) {
        return new Date(y, m, d, date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());
    },

    modifyTime: function(date, h, m, s) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate(), h, m, s, date.getMilliseconds());
    },

    clearTime: function(date) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    },

    changeYearMonthAndClampDate: function(date, year, month) {
        // clamp date to the number of days in `year`, `month`
        // eg: (2010-1-31, 2010, 2) => 2010-2-28
        const monthDate = Math.min(date.getDate(), util_date.getDayCountOfMonth(year, month));
        return util_date.modifyDate(date, year, month, monthDate);
    },

    prevMonth: function(date) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return month === 0
            ? util_date.changeYearMonthAndClampDate(date, year - 1, 11)
            : util_date.changeYearMonthAndClampDate(date, year, month - 1);
    },

    nextMonth: function(date) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return month === 11
            ? util_date.changeYearMonthAndClampDate(date, year + 1, 0)
            : util_date.changeYearMonthAndClampDate(date, year, month + 1);
    },

    prevYear: function(date, amount = 1) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return util_date.changeYearMonthAndClampDate(date, year - amount, month);
    },

    nextYear: function(date, amount = 1) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return util_date.changeYearMonthAndClampDate(date, year + amount, month);
    },


    // 自定义方法
    // 验证用户输入 能否转化成日期
    checkStringCanToDate: (value, type )=> {
        if (!value) return '';
        if (util_date.isDate(value)) return value;

        if ( type == 'date' ) {
            return util_date.checkToDate(value);
        }
        else if (type == 'year') {
            return util_date.checkToYear(value);
        }
        else if (type == 'month') {
            return util_date.checkToMonth(value);
        }
        else if (type == 'time') {
            return util_date.checkToTime(value);
        }
        else if (type == 'datetime') {
            return util_date.checkToDatetime(value);
        }
        return '';
    },

    // 验证用户输入是否符合 日期格式
    checkToDate: (value)=> {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!~value.indexOf('-')) return '';            // 不含关键字符

        let arr = value.split('-');
        let getY = parseInt(arr[0]);
        let arrM = parseInt(arr[1]);
        let arrD = parseInt(arr[2]);
        let year = util_date.beautify(getY, 10000, 1970);
        let month = util_date.beautify(arrM, 31);
        let day = util_date.beautify(arrD, 31);
        if (year && month && day) {
            return `${ year }-${ month }-${ day }`;
        }
        else {
            return '';
        }
    },

    // 验证用户输入是否符合年份格式
    checkToYear: (value) => {
        if (!value) return '';
        if (util_date.isDate(value)) return value;   // 如果已经是时间格式: 不处理
        let intY = parseInt(value);
        let year = intY >= 1970 && intY < 10000 ? intY : '';
        return `${ year }`;
    },

    // 验证用户输入是否符合月份格式
    checkToMonth: (value) => {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!~value.indexOf('-')) return '';            // 不含关键字符

        let arr = value.split('-');
        let arrY = parseInt(arr[0]);
        let arrM = parseInt(arr[1]);
        let year = arrY >= 1970 && arrY < 10000 ? arrY : '';
        let month = arrM > 0 && arrM < 10 ? '0' + arrM : (arrM >= 10 && arrM <= 12 ? arrM : '');
        if (year && month) {
            return `${ year }-${ month }`;
        }
        else {
            return '';
        }
    },

    checkToTime: (value)=> {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!~value.indexOf(':')) return '';            // 不含关键字符

        let arr = value.split(':');
        let arrH = parseInt(arr[0]);
        let arrM = parseInt(arr[1]);
        let arrS = parseInt(arr[2]);
        let hour = arrH > 0 && arrH < 10 ? '0' + arrH : (arrH >= 10 && arrH <= 23 ? arrH : '');
        let minute = arrM > 0 && arrM < 10 ? '0' + arrM : (arrM >= 10 && arrM <= 59 ? arrM : '');
        let second = arrS > 0 && arrS < 10 ? '0' + arrS : (arrS >= 10 && arrS <= 59 ? arrS : '');

        if (hour && minute && second) {
            return `${ hour }:${ minute }:${ second }`;
        }
        else {
            return '';
        }
    },

    checkToDatetime: (value)=> {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!util_date.regDatetime.test(value)) return '';

        let step = value.split(' ');
        let date = step[0].split('-');
        let time = step[1].split(':');

        let Y = parseInt(date[0]);
        let M = parseInt(date[1]);
        let D = parseInt(date[2]);
        let h = parseInt(time[0]);
        let m = parseInt(time[1]);
        let s = parseInt(time[2]);

        Y = (Y >= 1970 && Y < 10000) ? Y : '';
        M = M > 0 && M < 10 ? '0' + M : (M >= 10 && M <= 12 ? M : '');
        D = D > 0 && D < 10 ? '0' + D : (D >= 10 && D <= 31 ? D : '');
        h = h > 0 && h < 10 ? '0' + h : (h >= 10 && h <= 23 ? h : '');
        m = m > 0 && m < 10 ? '0' + m : (m >= 10 && m <= 59 ? m : '');
        s = s > 0 && s < 10 ? '0' + s : (s >= 10 && s <= 59 ? s : '');

        if (Y && M && D && h && m && s) {
            return `${ Y }-${ M }-${ D } ${ h }:${ m }:${ s }`;
        }
        else {
            return ''
        }
    },


    // 格式化日期
    format: (val, type, ) => {
        if (type == 'year') {
            return util_date.formatYear(val);
        }
        else if (type == 'month') {
            return util_date.formatMonth(val);
        }
        else if (type == 'date') {
            return util_date.formatDate(val);
        }
        else if ( type == 'datetime' ) {
            return util_date.formatDatetime(val);
        }
        else if ( type == 'time' ) {
            return util_date.formatTime(val);
        }
        else if ( type == 'daterange' ) {
            return util_date.formatDateRange(val);
        }
    },

    // 格式化日期
    formatDate: (val)=> {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';
        return util_date.getDateStr(t);
    },

    // 格式化年份
    formatYear: (val) => {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        let Y = t.getFullYear();
        return Y;
    },

    // 格式化月份
    formatMonth: (val) => {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        let M = t.getMonth() + 1;
        M < 10 && ( M = '0' + M);

        return `${ t.getFullYear() }-${ M }`
    },

    // 格式化日期时间
    formatDatetime: (val) => {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        return `${ util_date.getDateStr(t) } ${ util_date.getTimeStr(t) }`
    },

    formatTime: (val)=> {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        return util_date.getTimeStr(t);
    },

    formatDateRange: (val)=> {
        if (!val.length) return [];
        let D1 = util_date.formatDate(val[0]) || '';
        let D2 = util_date.formatDate(val[1]) || '';
        console.log('D1:', D1);
        console.log('D2:', D2);
        return [D1, D2];
    },

    // 获取年月日 （val一定是date类型）
    getDateStr: (val) => {
        if (!val) return '';
        if (!util_date.isDate(val)) return '';

        let date = new Date(val);
        let Y = date.getFullYear();
        let M = date.getMonth() + 1;
        let D = date.getDate();

        M < 10 && ( M = '0' + M);
        D < 10 && ( D = '0' + D);

        return `${ Y }-${ M }-${ D }`;
    },

    // 获取时分秒 (val必须是date类型)
    getTimeStr: (val) => {
        if (!val) return '';
        if (!util_date.isDate(val)) return '';
        let time = new Date(val);

        let h = time.getHours();
        let m = time.getMinutes();
        let s = time.getSeconds();

        h < 10 && ( h = '0' + h);
        m < 10 && ( m = '0' + m);
        s < 10 && ( s = '0' + s);

        return `${ h }:${ m }:${ s }`;
    },

    modifyWithTimeString: (date, time) => {
        if (date == null || !time) {
            return date;
        }

        if (!util_date.isDate(time)) return '';
        time = new Date(time);
        return util_date.modifyTime(date, time.getHours(), time.getMinutes(), time.getSeconds());
    },

    valueEquals: function(a, b) {
        const dateEquals = function(a, b) {
            const aIsDate = a instanceof Date;
            const bIsDate = b instanceof Date;
            if (aIsDate && bIsDate) {
                return a.getTime() === b.getTime();
            }
            if (!aIsDate && !bIsDate) {
                return a === b;
            }
            return false;
        };
    
        const aIsArray = a instanceof Array;
        const bIsArray = b instanceof Array;
        if (aIsArray && bIsArray) {
            if (a.length !== b.length) {
            return false;
            }
            return a.every((item, index) => dateEquals(item, b[index]));
        }
        if (!aIsArray && !bIsArray) {
            return dateEquals(a, b);
        }
        return false;
    },

    isString: function(val) {
        return typeof val === 'string' || val instanceof String;
    },

    arrayFindIndex: function(arr, pred) {
        for (let i = 0; i !== arr.length; ++i) {
            if (pred(arr[i])) {
                return i;
            }
        }
        return -1;
    },

    arrayFind: function(arr, pred) {
        const idx = util_date.arrayFindIndex(arr, pred);
        return idx !== -1 ? arr[idx] : undefined;
    },

    coerceTruthyValueToArray: function(val) {
        if (Array.isArray(val)) {
            return val;
        } else if (val) {
            return [val];
        } else {
            return [];
        }
    },
    getDateTimestamp: function(time) {
        if (typeof time === 'number' || typeof time === 'string') {
            return util_date.clearTime(new Date(time)).getTime();
        } else if (time instanceof Date) {
            return util_date.clearTime(time).getTime();
        } else {
            return NaN;
        }
    },
    hasClass (el, cls) {
        if (!el || !cls) return false;

        if (cls.indexOf(' ') !== -1) throw new Error('className should not contain space.');
    
        if (el.classList) {
            return el.classList.contains(cls);
        } else {
            return (' ' + el.className + ' ').indexOf(' ' + cls + ' ') > -1;
        }
    },
}

const validator = function(val) {
    // either: String, Array of String, null / undefined
    return (
        val === null || val === undefined || util_date.isString(val) || 
        (Array.isArray(val) && val.length === 2 && val.every(util_date.isString))
    );
};

const Mixin_uiDate_transition = {
    mounted () {
        window.addEventListener('scroll', this.pageScroll, true);
        window.addEventListener('resize', this.pageScroll);
    },
    methods: {
        enter (el) {
            el.style.transition = '0.19s all ease-in';
            el.style.webkitTransform = 'scale(1, 0)';
            el.style.opacity = 0;
        },
        afterEnter (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },

        beforeLeave (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },
        leave (el) {
            if (el.scrollHeight !== 0) {
                el.style.transition = '0.19s all ease-in';
                el.style.webkitTransform = 'scale(1, 0)';
                el.style.opacity = 0;
            }
        },
        afterLeave (el) {
            el.style.transition = '';
            el.style.webkitTransform = '';
            el.style.opacity = ''
        },
        
        pageScroll () {
            if (this.$refs.pickers) {
                this.topScroll();
            }
        },
        topScroll () {           
            let client = this.$el.getBoundingClientRect();
            let height = this.$refs.pickers.clientHeight;
            let clientHeight = document.body.clientHeight;
            if ( client.bottom + height + 7 > clientHeight && client.top >= height + 2 ) {
                this.animation = 'appear-up';
                this.$refs.pickers.style.top = `-${height+2}px`;
                this.$refs.pickers.style.boxShadow = 'rgba(0, 0, 0, 0.1) 0px -5px 10px';
            } else {
                this.animation = 'appear-down';
                this.$refs.pickers.style.top = "";
                this.$refs.pickers.style.boxShadow = '';
            }
        }
    }
}

Vue.component('ui-date-picker1', {
    template: `
        <div class="vd-ui-date">
            <div 
                v-if="!ranged"
                class="vd-ui-date-editor"
                :class="'vd-ui-date-editor--' + type"
            >
                <ui-input
                    :placeholder="placeholder"
                    v-bind="$attrs"
                    ref="vdInput"
                    :disabled="disabled"
                    :value="displayValue"
                    @input="value => userInput = value"

                    @focus="handleFocus"
                    @clear="handleClear"
                    @keydown.native="handleKeydown"
                    @change="handleChange"

                    :size="size"
                    width="100%"
                    :readonly="!editable || readonly"
                    autocomplete="off"
                    :clearable="clearValue"
                >
                    <i v-if="type=='time'" slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-time"></i>
                    <i v-else slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-date"></i>
                </ui-input>
            </div>

            <!-- 日期范围选择 -->
            <div
                v-else 
                class="vd-ui-range"
                @mouseenter="rangeMouseenter"
                @mouseleave="rangeMouseleave"
                @click="handleRangeClick"
            >
                <i class="vd-ui_icon icon-date"></i>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="startPlaceholder"
                        :value="displayValue && displayValue[0]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <span class="split">至</span>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="endPlaceholder"
                        :value="displayValue && displayValue[1]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <div class="range-error2">
                    <i 
                        v-show="haveTrigger"
                        class="vd-ui_icon icon-error2" 
                        @click.stop="handleRangeClear"
                    ></i>
                </div>
            </div>


            <transition
                @enter="enter"
                @after-enter="afterEnter"
                @before-leave="beforeLeave"
                @leave="leave"
                @after-leave="afterLeave"
            >
                <div 
                    class="vd-ui-date-wrapper"
                    :class="[animation ? animation : '', 'vd-ui-date-wrapper--' + type]" 
                    v-show="pickerVisible"
                    ref="pickers"
                    @click.stop
                >
                    <!-- 范围 -->
                    <ui-date-range-panel
                        v-if="type === 'daterange'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :value="baseValue"
                        :unlinkPanels="unlinkPanels"
                        :shortcuts="shortcuts"
                        v-bind="pickerOptions"
                        :showTime="type === 'datetime'"
                        @pick="pickDateRange"
                    ></ui-date-range-panel>

                    <!-- 时间 -->
                    <ui-time-panel
                        v-else-if="type === 'time'"
                        :type="type"
                        :value="baseValue"
                        @pick="pickTime"
                        v-bind="pickerOptions"
                    ></ui-time-panel>

                    <!-- 日期时间 -->
                    <ui-date-time-panel
                        v-else-if="type === 'datetime'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :type="type"
                        :value="baseValue"
                        @pick="pickDateTime"
                        :pickerOptions="pickerOptions"
                        :selectionMode="selectionMode"
                    ></ui-date-time-panel>

                    <ui-date-panel
                        v-else
                        :type="type"
                        :value="baseValue"
                        :firstDayOfWeek="firstDayOfWeek"
                        :shortcuts="shortcuts"
                        @pick="pickDate"
                        :selectionMode="selectionMode"
                        v-bind="pickerOptions"
                    ></ui-date-panel>
                </div>
            </transition>

            <div class="vd-ui-input-error" v-if="errorable && !disabled">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,
    props: {
        value: {},
        readonly: Boolean,   // 是否只读
        disabled: {  // 是否禁用
            type: Boolean,
            default: false
        },
        editable: {  // 是否显示清除按钮
            type: Boolean,
            default: true
        },
        clearable: {
            type: Boolean,
            default: true
        },
        size: String,   // large, small, mini
        placeholder: String,
        startPlaceholder: String,
        endPlaceholder: String,
        type: {   // date, year, month, time, datetime, daterange
            type: String,
            default: 'date'
        },
        appendToBody: {
            type: Boolean,
            default: false,
        },
        format: String,
        popperClass: String,
        pickerOptions: {},
        rangeSeparator: {
            type: String,
            default: '-'
        },
        defaultValue: {}, // 可选，选择器打开时默认显示的时间
        defaultTime: {},  // 范围选择时选中日期所使用的当日内具体时刻
        valueFormat: String,   // 绑定值的格式。不指定则绑定值为 Date 对象
        name: {
            default: '',
            validator
        },
        unlinkPanels: {   // 在范围选择器里取消两个日期面板之间的联动
            type: Boolean,
            default: false
        },
        prefixIcon: String,
        clearIcon: {
            type: String,
            default: 'vd-ui-icon-circle-close'
        },
        validateEvent: {    // 输入时是否触发表单的校验
            type: Boolean,
            default: true
        },
        id: {
            default: '',
            validator
        },
        shortcuts: Array, // 快捷选项
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
    },
    mixins: [ Mixin_uiDate_transition ],

    data () {
        return {
            pickerVisible: false,   // panel是否展示
            showClose: false,
            valueOnOpen: null, // 选择器打开时的值，用于确认是否发生变化
            unwatchPickerOptions: null,

            baseValue: null,    // 真实选中的日期 【一定是日期格式】 【可能是数组中多个日期】 同 ele parsedValue
            userInput: null,    // 输入框实时输入值
            animation: '',
            realDate: '',    // 实际上选择的时间（日期，年月，月都放在这里），显示都根据这个来
            rangeFocus: false,  // 范围选择器是否获取焦点
        }
    },
    watch: {
        pickerVisible (val) {
            if (this.readonly || this.pickerDisabled) return;
            if (val) {
                this.$nextTick(()=>{
                    this.pageScroll();   // 下拉展开动画
                })
            }
        },
    },
    computed: {
        // 是否范围选择
        ranged () {
            return this.type.indexOf('range') > -1;
        },

        // 同 ele reference
        vdInput () {
            const vdInput = this.$refs.vdInput;
            if (vdInput) return vdInput.$el || vdInput;
            else return '';
        },

        refInput () {
            if (this.vdInput) {
                return [].slice.call(this.vdInput.querySelectorAll('input'));
            }
            return [];
        },

        // 是否显示清除按钮【需要显示 且 有值】
        clearValue () {
            return this.clearable && this.valueOnOpen && true;
        },

        selectionMode () {
            if (this.type == 'month') {
                return 'month';
            } else if (this.type == 'year') {
                return 'year';
            } else if (this.type === 'dates') {
                return 'dates';
            } else if (this.type === 'week') {
                return 'week';
            }
            return 'day';
        },

        // 显示范围清除按钮
        haveTrigger () {
            // 框子获焦 + 至少有一个值
            let zhi = this.baseValue && this.baseValue[0] && this.baseValue[1];
            return zhi && this.rangeFocus
        },

        // 回显示在input中的值 【需要处理显示格式】
        displayValue () {
            if (this.userInput !== null) {
                return this.userInput;
            }
            if (Array.isArray(this.baseValue)) {
                return [
                    util_date.formatDate(this.baseValue[0]),
                    util_date.formatDate(this.baseValue[1])
                ];
            }
            else if (this.baseValue !== null) {
                return util_date.format(this.baseValue, this.type);
            }
            else {
                return ''
            }
        },

    },
    mounted () {
        this.$form.setValidEl(this);

        // 关闭 当前日期窗口之外的其他日期窗口（全局一次只能打开一个）
        let _this = this;
        document.addEventListener('click', (e) => {
           if (!this.$el.contains(e.target)) _this.pickerVisible = false;
        })

        this.initDate();
    },
    methods: {
        // 初始化日期
        initDate () {
            if (this.value) {
                if (util_date.isDate(this.value)) {
                    this.baseValue = this.value;
                }
                else if (this.type == 'daterange') {
                    let test = util_date.arrayToDate(this.value)
                    this.baseValue = test;
                }
                else if (typeof(this.value) == 'string' && util_date.checkStringCanToDate(this.value, this.type)) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(this.value);
                    } else if (this.type == 'datetime') {
                        this.baseValue = util_date.timeToDatetime(this.value);
                    } else {
                        this.baseValue = util_date.toDate(this.value);
                    }
                }
            } else {
                this.baseValue = null;
            }
        },

        // 展示panel
        showPanel () {
            if (this.readonly || this.pickerDisabled) return;
            this.pickerVisible = true;
            this.valueOnOpen = Array.isArray(this.baseValue) ? [...this.baseValue] : this.baseValue;
        },
        // 关闭panel
        hidePanel () {
            this.pickerVisible = false;
            this.userInput = null;
            this.blur();
        },

        // input获焦
        handleFocus (e) {
            // const type = this.type;
            if (util_date.HAVE_TRIGGER_TYPES.indexOf('date') !== -1 && !this.pickerVisible) {
                this.showPanel();
            }
            this.$emit('focus', e);
        },
        // 主动 失去焦点
        blur() {
            this.refInput.forEach(input => input.blur());
            this.$emit('blur', this);
        },
        // 单选-清除
        handleClear () {
            this.emitInput(null);
        },
        // 范围清除
        handleRangeClear (event) {
            this.baseValue = null;
            this.userInput = null;
        },
        // change
        handleChange () {

            // 如果有值
            if (this.userInput) {

                const value = util_date.checkStringCanToDate(this.userInput, this.type);
                // 验证合法性
                if (value) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(value);   // 年和月是否合法
                    } else {
                        this.baseValue = util_date.toDate(value);   // 年和月是否合法
                    }
                    this.emitInput(this.baseValue);
                    this.emitChange(this.baseValue);
                }
                else {
                    // 年份，月份输入时，重新处理把时间给user-input，不然后被赋一次年月或月份后，不好对比时间
                    this.baseValue = this.valueOnOpen || null;
                }

                this.hidePanel();   // 关闭弹窗
            }

            if (this.userInput === '') {
                this.emitInput(null);
                this.emitChange(null);
                this.baseValue = null;
                this.userInput = null;
            }


        },
        // input 触发按键
        handleKeydown (event) {
            const keyCode = event.keyCode;

            // ESC
            if (keyCode === 27) {
                this.hidePanel();
                event.stopPropagation();
                return;
            }

            // Tab
            if (keyCode === 9) {
                if (!this.ranged) {
                    this.handleChange();
                    this.hidePanel();
                    event.stopPropagation();
                } else {
                    // user may change focus between two input
                    setTimeout(() => {
                        if (this.refInput.indexOf(document.activeElement) === -1) {
                            this.hidePanel();
                            event.stopPropagation();
                        }
                    }, 0);
                }
                return;
            }

            if (keyCode === 13) {
                if (this.userInput) {
                    this.handleChange();
                    this.hidePanel();
                }
                event.stopPropagation();
                return;
            }

            // if user is typing, do not let picker handle key input
            if (this.userInput) {
                event.stopPropagation();
                return;
            }
        },
        // change事件
        emitChange(val) {
            // determine user real change only
            if (!util_date.valueEquals(val, this.valueOnOpen)) {
                // this.$emit('change', val);
                this.$emit('change', util_date.format(val, this.type));
                this.valueOnOpen = val;
                this.checkValid(val);
            }
        },
        // input事件
        emitInput(val) {
            console.log('util_date.format(val, this.type):::', util_date.format(val, this.type));
            this.$emit('input', util_date.format(val, this.type));
            this.checkValid(val);
        },
        // 选中日期  [关闭panel => emit值]
        pickDate (val) {
            this.baseValue = val;
            this.userInput = null;
            this.hidePanel();
            this.emitInput(val);
            this.emitChange(val);
        },
        // 选中时间  [关闭panel => emit值]
        pickTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期时间
        pickDateTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期范围  [关闭panel => emit值]
        pickDateRange (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
                this.emitChange(val);
            }
        },
        handleRangeClick() {
            const type = this.type;

            if (util_date.HAVE_TRIGGER_TYPES.indexOf(type) !== -1 && !this.pickerVisible) {
                this.pickerVisible = true;
            }
            this.$emit('focus', this);
        },
        
        rangeMouseenter () {
            this.rangeFocus = true;
        },
        rangeMouseleave () {
            this.rangeFocus = false;
        },
        checkValid(val) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, val, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        },
    }
})

const Mixin_uiDate_transition2 = {
    mounted () {
        window.addEventListener('scroll', this.pageScroll, true);
        window.addEventListener('resize', this.pageScroll);
    },
    methods: {
        enter (el) {
            el.style.transition = '0.19s all ease-in';
            el.style.webkitTransform = 'scale(1, 0)';
            el.style.opacity = 0;
        },
        afterEnter (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },

        beforeLeave (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },
        leave (el) {
            if (el.scrollHeight !== 0) {
                el.style.transition = '0.19s all ease-in';
                el.style.webkitTransform = 'scale(1, 0)';
                el.style.opacity = 0;
            }
        },
        afterLeave (el) {
            el.style.transition = '';
            el.style.webkitTransform = '';
            el.style.opacity = ''
        },
        
        pageScroll () {
            if (this.$refs.pickers) {
                this.topScroll();
            }
        },
        topScroll () {           
            let client = this.$el.getBoundingClientRect();
            let height = this.$refs.pickers.clientHeight;
            let clientHeight = document.body.clientHeight;
            if ( client.bottom + height + 7 > clientHeight && client.top >= height + 2 ) {
                this.animation = 'appear-up';
                this.$refs.pickers.style.top = `-${height+2}px`;
                this.$refs.pickers.style.boxShadow = 'rgba(0, 0, 0, 0.1) 0px -5px 10px';
            } else {
                this.animation = 'appear-down';
                this.$refs.pickers.style.top = "";
                this.$refs.pickers.style.boxShadow = '';
            }
        }
    }
}

Vue.component('ui-date-picker2', {
    template: `
        <div class="vd-ui-date">
            <div 
                v-if="!ranged"
                ref="input-wrap1"
                class="vd-ui-date-editor"
                :class="'vd-ui-date-editor--' + type"
            >
                <ui-input
                    :placeholder="placeholder"
                    v-bind="$attrs"
                    ref="vdInput"
                    :disabled="disabled"
                    :value="displayValue"
                    @input="value => userInput = value"

                    @focus="handleFocus"
                    @blur="handleBlur"
                    @clear="handleClear"
                    @keydown.native="handleKeydown"
                    @change="handleChange"

                    :size="size"
                    width="100%"
                    :readonly="!editable || readonly"
                    autocomplete="off"
                    :clearable="clearValue"
                >
                    <i v-if="type=='time'" slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-time"></i>
                    <i v-else slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-date"></i>
                </ui-input>
            </div>

            <!-- 日期范围选择 -->
            <div
                v-else 
                ref="input-wrap2"
                class="vd-ui-range"
                @mouseenter="rangeMouseenter"
                @mouseleave="rangeMouseleave"
                @click="handleRangeClick"
            >
                <i class="vd-ui_icon icon-date"></i>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="startPlaceholder"
                        :value="displayValue && displayValue[0]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <span class="split">至</span>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="endPlaceholder"
                        :value="displayValue && displayValue[1]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <div class="range-error2">
                    <i 
                        v-show="haveTrigger"
                        class="vd-ui_icon icon-error2" 
                        @click.stop="handleRangeClear"
                    ></i>
                </div>
            </div>

            <ui-poper :show="pickerVisible" :position="animation === 'appear-up' ? 'top' : ''" ref="dropwrap" :errorable="errorable">
                <div 
                    v-show="pickerVisible"
                    class="vd-ui-date-wrapper"
                    :class="[animation ? animation : '', 'vd-ui-date-wrapper--' + type]" 
                    ref="pickers"
                    @click.stop
                >
                    <!-- 范围 -->
                    <ui-date-range-panel
                        v-if="type === 'daterange'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :value="baseValue"
                        :unlinkPanels="unlinkPanels"
                        :shortcuts="shortcuts"
                        v-bind="pickerOptions"
                        :showTime="type === 'datetime'"
                        @pick="pickDateRange"
                    ></ui-date-range-panel>

                    <!-- 时间 -->
                    <ui-time-panel
                        v-else-if="type === 'time'"
                        :type="type"
                        :value="baseValue"
                        @pick="pickTime"
                        v-bind="pickerOptions"
                    ></ui-time-panel>

                    <!-- 日期时间 -->
                    <ui-date-time-panel
                        v-else-if="type === 'datetime'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :type="type"
                        :value="baseValue"
                        @pick="pickDateTime"
                        :pickerOptions="pickerOptions"
                        :selectionMode="selectionMode"
                    ></ui-date-time-panel>

                    <ui-date-panel
                        v-else
                        :type="type"
                        :value="baseValue"
                        :firstDayOfWeek="firstDayOfWeek"
                        :shortcuts="shortcuts"
                        @pick="pickDate"
                        :selectionMode="selectionMode"
                        v-bind="pickerOptions"
                    ></ui-date-panel>
                </div>
            </ui-poper>

            <!-- 表单校验报错 -->
            <div class="vd-ui-input-error" v-if="errorable && !disabled">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,
    props: {
        value: {},
        readonly: Boolean,   // 是否只读
        disabled: {  // 是否禁用
            type: Boolean,
            default: false
        },
        editable: {  // 是否显示清除按钮
            type: Boolean,
            default: true
        },
        clearable: {
            type: Boolean,
            default: true
        },
        size: String,   // large, small, mini
        placeholder: String,
        startPlaceholder: String,
        endPlaceholder: String,
        type: {   // date, year, month, time, datetime, daterange
            type: String,
            default: 'date'
        },
        appendToBody: {
            type: Boolean,
            default: false,
        },
        format: String,
        popperClass: String,
        pickerOptions: {},
        rangeSeparator: {
            type: String,
            default: '-'
        },
        defaultValue: {}, // 可选，选择器打开时默认显示的时间
        defaultTime: {},  // 范围选择时选中日期所使用的当日内具体时刻
        valueFormat: String,   // 绑定值的格式。不指定则绑定值为 Date 对象
        name: {
            default: '',
            validator
        },
        unlinkPanels: {   // 在范围选择器里取消两个日期面板之间的联动
            type: Boolean,
            default: false
        },
        prefixIcon: String,
        clearIcon: {
            type: String,
            default: 'vd-ui-icon-circle-close'
        },
        validateEvent: {    // 输入时是否触发表单的校验
            type: Boolean,
            default: true
        },
        id: {
            default: '',
            validator
        },
        shortcuts: Array, // 快捷选项
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
    },
    mixins: [ Mixin_uiDate_transition2 ],

    data () {
        return {
            pickerVisible: false,   // panel是否展示
            showClose: false,
            valueOnOpen: null, // 选择器打开时的值，用于确认是否发生变化
            unwatchPickerOptions: null,

            baseValue: null,    // 真实选中的日期 【一定是日期格式】 【可能是数组中多个日期】 同 ele parsedValue
            userInput: null,    // 输入框实时输入值
            animation: '',
            realDate: '',    // 实际上选择的时间（日期，年月，月都放在这里），显示都根据这个来
            rangeFocus: false,  // 范围选择器是否获取焦点
        }
    },
    watch: {
        pickerVisible (val) {
            if (this.readonly || this.pickerDisabled) return;
            if (val) {
                this.$nextTick(()=>{
                    this.pageScroll();   // 下拉展开动画
                })
            }
        },
    },
    computed: {
        // 是否范围选择
        ranged () {
            return this.type.indexOf('range') > -1;
        },

        // 同 ele reference
        vdInput () {
            const vdInput = this.$refs.vdInput;
            if (vdInput) return vdInput.$el || vdInput;
            else return '';
        },

        refInput () {
            if (this.vdInput) {
                return [].slice.call(this.vdInput.querySelectorAll('input'));
            }
            return [];
        },

        // 是否显示清除按钮【需要显示 且 有值】
        clearValue () {
            return this.clearable && this.valueOnOpen && true;
        },

        selectionMode () {
            if (this.type == 'month') {
                return 'month';
            } else if (this.type == 'year') {
                return 'year';
            } else if (this.type === 'dates') {
                return 'dates';
            } else if (this.type === 'week') {
                return 'week';
            }
            return 'day';
        },

        // 显示范围清除按钮
        haveTrigger () {
            // 框子获焦 + 至少有一个值
            let zhi = this.baseValue && this.baseValue[0] && this.baseValue[1];
            return zhi && this.rangeFocus
        },

        // 回显示在input中的值 【需要处理显示格式】
        displayValue () {
            if (this.userInput !== null) {
                return this.userInput;
            }
            if (Array.isArray(this.baseValue)) {
                return [
                    util_date.formatDate(this.baseValue[0]),
                    util_date.formatDate(this.baseValue[1])
                ];
            }
            else if (this.baseValue !== null) {
                return util_date.format(this.baseValue, this.type);
            }
            else {
                return ''
            }
        },

    },
    mounted () {
        this.$form.setValidEl(this);

        // 关闭 当前日期窗口之外的其他日期窗口（全局一次只能打开一个）
        let _this = this;
        document.addEventListener('click', (e) => {
            let inputWrap1 = _this.$refs['input-wrap1']; // input
            let inputWrap2 = _this.$refs['input-wrap2']; // 范围input
            let panelWrap = _this.$refs['pickers']; // 日期panel层
            let include1 = false; // input 是否包含点击元素
            let include2 = false; // 范围input 是否包含点击元素
            let include3 = false; // 面板是否包含点击元素

            if (inputWrap1 && inputWrap1.contains(e.target)) {
                include1 = true;
            }
            if (inputWrap2 && inputWrap2.contains(e.target)) {
                include2 = true;
            }
            if (panelWrap && panelWrap.contains(e.target)) {
                include3 = true;
            }

            if (_this.pickerVisible && !include1 && !include2 && !include3) {
                _this.pickerVisible = false;
            }
        }, true); // true: 从外向内 由根节点向点击元素执行

        this.initDate();
    },
    methods: {
        // 初始化日期
        initDate () {
            if (this.value) {
                if (util_date.isDate(this.value)) {
                    this.baseValue = this.value;
                }
                else if (this.type == 'daterange') {
                    let test = util_date.arrayToDate(this.value)
                    this.baseValue = test;
                }
                else if (typeof(this.value) == 'string' && util_date.checkStringCanToDate(this.value, this.type)) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(this.value);
                    } else if (this.type == 'datetime') {
                        this.baseValue = util_date.timeToDatetime(this.value);
                    } else {
                        this.baseValue = util_date.toDate(this.value);
                    }
                }
            } else {
                this.baseValue = null;
            }
        },

        // 展示panel
        showPanel () {
            if (this.readonly || this.pickerDisabled) return;
            this.pickerVisible = true;
            this.valueOnOpen = Array.isArray(this.baseValue) ? [...this.baseValue] : this.baseValue;
        },
        // 关闭panel
        hidePanel () {
            this.pickerVisible = false;
            this.userInput = null;
            this.dealBlur();
        },

        // input获焦
        handleFocus (e) {
            // const type = this.type;
            if (util_date.HAVE_TRIGGER_TYPES.indexOf('date') !== -1 && !this.pickerVisible) {
                this.showPanel();
            }
            this.$emit('focus', e);
        },
        // input 失焦
        handleBlur () {
            // this.pickerVisible = false;
            // this.dealBlur();
        },
        // 主动 失去焦点
        dealBlur() {
            console.log('blur');
            this.refInput.forEach(input => input.blur());
            this.$emit('blur', this);
        },
        // 单选-清除
        handleClear () {
            this.emitInput(null);
            this.emitChange(null);
        },
        // 范围清除
        handleRangeClear (event) {
            this.baseValue = null;
            this.userInput = null;
            this.emitInput('');
            this.emitChange('');
        },
        // change
        handleChange () {

            // 如果有值
            if (this.userInput) {

                const value = util_date.checkStringCanToDate(this.userInput, this.type);
                // 验证合法性
                if (value) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(value);   // 年和月是否合法
                    } else {
                        this.baseValue = util_date.toDate(value);   // 年和月是否合法
                    }
                    this.emitInput(this.baseValue);
                    this.emitChange(this.baseValue);
                }
                else {
                    // 年份，月份输入时，重新处理把时间给user-input，不然后被赋一次年月或月份后，不好对比时间
                    this.baseValue = this.valueOnOpen || null;
                }

                this.hidePanel();   // 关闭弹窗
            }

            if (this.userInput === '') {
                this.emitInput(null);
                this.emitChange(null);
                this.baseValue = null;
                this.userInput = null;
            }


        },
        // input 触发按键
        handleKeydown (event) {
            const keyCode = event.keyCode;

            // ESC
            if (keyCode === 27) {
                this.hidePanel();
                event.stopPropagation();
                return;
            }

            // Tab
            if (keyCode === 9) {
                if (!this.ranged) {
                    this.handleChange();
                    this.hidePanel();
                    event.stopPropagation();
                } else {
                    setTimeout(() => {
                        if (this.refInput.indexOf(document.activeElement) === -1) {
                            this.hidePanel();
                            event.stopPropagation();
                        }
                    }, 0);
                }
                return;
            }

            if (keyCode === 13) {
                if (this.userInput) {
                    this.handleChange();
                    this.hidePanel();
                }
                event.stopPropagation();
                return;
            }

            if (this.userInput) {
                event.stopPropagation();
                return;
            }
        },
        // change事件
        emitChange(val) {
            if (!util_date.valueEquals(val, this.valueOnOpen)) {
                this.$emit('change', util_date.format(val, this.type));
                this.valueOnOpen = val;
                this.checkValid(val);
            }
        },
        // input事件
        emitInput(val) {
            this.$emit('input', util_date.format(val, this.type));
            this.checkValid(val);
        },
        // 选中日期  [关闭panel => emit值]
        pickDate (val) {
            this.baseValue = val;
            this.userInput = null;
            this.hidePanel();
            this.emitInput(val);
            this.emitChange(val);
        },
        // 选中时间  [关闭panel => emit值]
        pickTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期时间
        pickDateTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期范围  [关闭panel => emit值]
        pickDateRange (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
                this.emitChange(val);
            }
        },
        handleRangeClick() {
            const type = this.type;

            if (util_date.HAVE_TRIGGER_TYPES.indexOf(type) !== -1 && !this.pickerVisible) {
                this.pickerVisible = true;
            }
            this.$emit('focus', this);
        },
        
        rangeMouseenter () {
            this.rangeFocus = true;
        },
        rangeMouseleave () {
            this.rangeFocus = false;
        },
        checkValid(val) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, val, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
            this.$refs.dropwrap.calcPosition();
        },
    }
})
Vue.component('ui-date-picker', {
    template: `
        <div class="vd-ui-date">
            <div 
                v-if="!ranged"
                ref="input-wrap1"
                class="vd-ui-date-editor"
                :class="'vd-ui-date-editor--' + type"
            >
                <ui-input
                    :placeholder="placeholder"
                    v-bind="$attrs"
                    ref="vdInput"
                    :disabled="disabled"
                    :value="displayValue"
                    @input="value => userInput = value"

                    @focus="handleFocus"
                    @blur="handleBlur"
                    @clear="handleClear"
                    @keydown.native="handleKeydown"
                    @change="handleChange"

                    :size="size"
                    width="100%"
                    :readonly="!editable || readonly"
                    autocomplete="off"
                    :clearable="clearValue"
                >
                    <i v-if="type=='time'" slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-time"></i>
                    <i v-else slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-date"></i>
                </ui-input>
            </div>

            <!-- 日期范围选择 -->
            <div
                v-else 
                ref="input-wrap2"
                class="vd-ui-range"
                @mouseenter="rangeMouseenter"
                @mouseleave="rangeMouseleave"
                @click="handleRangeClick"
            >
                <i class="vd-ui_icon icon-date"></i>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="startPlaceholder"
                        :value="displayValue && displayValue[0]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <span class="split">至</span>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="endPlaceholder"
                        :value="displayValue && displayValue[1]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <div class="range-error2">
                    <i 
                        v-show="haveTrigger"
                        class="vd-ui_icon icon-error2" 
                        @click.stop="handleRangeClear"
                    ></i>
                </div>
            </div>

            <ui-date-poper :show="pickerVisible" ref="dropwrap" :panel="$refs.pickers" :errorable="calcerror && errorable" :zindex="zindex">
                <div 
                    v-show="pickerVisible"
                    class="vd-ui-date-wrapper"
                    :class="[animation ? animation : '', 'vd-ui-date-wrapper--' + type]" 
                    ref="pickers"
                    @click.stop
                >
                    <!-- 范围 -->
                    <ui-date-range-panel
                        v-if="type === 'daterange'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :value="baseValue"
                        :unlinkPanels="unlinkPanels"
                        :shortcuts="shortcuts"
                        :disabled-date="disabledDate"
                        v-bind="pickerOptions"
                        :showTime="type === 'datetime'"
                        @pick="pickDateRange"
                    ></ui-date-range-panel>

                    <!-- 时间 -->
                    <ui-time-panel
                        v-else-if="type === 'time'"
                        :type="type"
                        :value="baseValue"
                        @pick="pickTime"
                        :disabled-date="disabledDate"
                        v-bind="pickerOptions"
                    ></ui-time-panel>

                    <!-- 日期时间 -->
                    <ui-date-time-panel
                        v-else-if="type === 'datetime'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :type="type"
                        :value="baseValue"
                        @pick="pickDateTime"
                        :disabled-date="disabledDate"
                        :pickerOptions="pickerOptions"
                        :selectionMode="selectionMode"
                    ></ui-date-time-panel>

                    <ui-date-panel
                        v-else
                        :type="type"
                        :value="baseValue"
                        :firstDayOfWeek="firstDayOfWeek"
                        :shortcuts="shortcuts"
                        @pick="pickDate"
                        :disabled-date="disabledDate"
                        :selectionMode="selectionMode"
                        v-bind="pickerOptions"
                    ></ui-date-panel>
                </div>
            </ui-date-poper>

            <!-- 表单校验报错 -->
            <div class="vd-ui-input-error" v-if="errorable && !disabled">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,
    props: {
        value: {},
        readonly: Boolean,   // 是否只读
        disabled: {  // 是否禁用
            type: Boolean,
            default: false
        },
        editable: {  // 是否显示清除按钮
            type: Boolean,
            default: true
        },
        clearable: {
            type: Boolean,
            default: true
        },
        size: String,   // large, small, mini
        placeholder: String,
        startPlaceholder: String,
        endPlaceholder: String,
        type: {   // date, year, month, time, datetime, daterange
            type: String,
            default: 'date'
        },
        appendToBody: {
            type: Boolean,
            default: false,
        },
        format: String,
        popperClass: String,
        pickerOptions: {},
        rangeSeparator: {
            type: String,
            default: '-'
        },
        defaultValue: {}, // 可选，选择器打开时默认显示的时间
        defaultTime: {},  // 范围选择时选中日期所使用的当日内具体时刻
        valueFormat: String,   // 绑定值的格式。不指定则绑定值为 Date 对象
        name: {
            default: '',
            validator
        },
        unlinkPanels: {   // 在范围选择器里取消两个日期面板之间的联动
            type: Boolean,
            default: false
        },
        prefixIcon: String,
        clearIcon: {
            type: String,
            default: 'vd-ui-icon-circle-close'
        },
        validateEvent: {    // 输入时是否触发表单的校验
            type: Boolean,
            default: true
        },
        id: {
            default: '',
            validator
        },
        shortcuts: Array, // 快捷选项
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        disabledDate: Function,
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        valid: String,
        zindex: String,
        calcerror: {
            type: Boolean,
            default: true
        }
    },

    data () {
        return {
            pickerVisible: false,   // panel是否展示
            showClose: false,
            valueOnOpen: null, // 选择器打开时的值，用于确认是否发生变化
            unwatchPickerOptions: null,

            baseValue: null,    // 真实选中的日期 【一定是日期格式】 【可能是数组中多个日期】 同 ele parsedValue
            userInput: null,    // 输入框实时输入值
            animation: '',
            realDate: '',    // 实际上选择的时间（日期，年月，月都放在这里），显示都根据这个来
            rangeFocus: false,  // 范围选择器是否获取焦点
        }
    },
    watch: {
        value () {
            this.initDate();
        },
        // pickerVisible (val) {
        //     if (this.readonly || this.pickerDisabled) return;
        //     if (val) {
        //         this.$nextTick(()=>{
        //             this.pageScroll();   // 下拉展开动画
        //         });
        //     }
        // },
    },
    computed: {
        // 是否范围选择
        ranged () {
            return this.type.indexOf('range') > -1;
        },

        // 同 ele reference
        vdInput () {
            const vdInput = this.$refs.vdInput;
            if (vdInput) return vdInput.$el || vdInput;
            else return '';
        },

        refInput () {
            if (this.vdInput) {
                return [].slice.call(this.vdInput.querySelectorAll('input'));
            }
            return [];
        },

        // 是否显示清除按钮【需要显示 且 有值】
        clearValue () {
            return this.clearable && this.valueOnOpen && true;
        },

        selectionMode () {
            if (this.type == 'month') {
                return 'month';
            } else if (this.type == 'year') {
                return 'year';
            } else if (this.type === 'dates') {
                return 'dates';
            } else if (this.type === 'week') {
                return 'week';
            }
            return 'day';
        },

        // 显示范围清除按钮
        haveTrigger () {
            // 框子获焦 + 至少有一个值
            let zhi = this.baseValue && this.baseValue[0] && this.baseValue[1];
            return zhi && this.rangeFocus
        },

        // 回显示在input中的值 【需要处理显示格式】
        displayValue () {
            if (this.userInput !== null) {
                return this.userInput;
            }
            if (Array.isArray(this.baseValue)) {
                return [
                    util_date.formatDate(this.baseValue[0]),
                    util_date.formatDate(this.baseValue[1])
                ];
            }
            else if (this.baseValue !== null) {
                return util_date.format(this.baseValue, this.type);
            }
            else {
                return ''
            }
        },

    },
    mounted () {
        this.$form.setValidEl(this);

        // 关闭 当前日期窗口之外的其他日期窗口（全局一次只能打开一个）
        let _this = this;
        document.addEventListener('click', (e) => {
            let inputWrap1 = _this.$refs['input-wrap1']; // input
            let inputWrap2 = _this.$refs['input-wrap2']; // 范围input
            let panelWrap = _this.$refs['pickers']; // 日期panel层
            let include1 = false; // input 是否包含点击元素
            let include2 = false; // 范围input 是否包含点击元素
            let include3 = false; // 面板是否包含点击元素

            if (inputWrap1 && inputWrap1.contains(e.target)) {
                include1 = true;
            }
            if (inputWrap2 && inputWrap2.contains(e.target)) {
                include2 = true;
            }
            if (panelWrap && panelWrap.contains(e.target)) {
                include3 = true;
            }

            if (_this.pickerVisible && !include1 && !include2 && !include3) {
                _this.pickerVisible = false;
            }
        }, true); // true: 从外向内 由根节点向点击元素执行

        this.initDate();
    },
    methods: {
        // 初始化日期
        initDate () {
            if (this.value) {
                if (util_date.isDate(this.value)) {
                    this.baseValue = this.value;
                }
                else if (this.type == 'daterange') {
                    let test = util_date.arrayToDate(this.value)
                    this.baseValue = test;
                }
                else if (typeof(this.value) == 'string' && util_date.checkStringCanToDate(this.value, this.type)) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(this.value);
                    } else if (this.type == 'datetime') {
                        this.baseValue = util_date.timeToDatetime(this.value);
                    } else {
                        this.baseValue = util_date.toDate(this.value);
                    }
                }
            } else {
                this.baseValue = null;
            }
        },

        // 展示panel
        showPanel () {
            if (this.readonly || this.pickerDisabled) return;
            this.pickerVisible = true;
            this.valueOnOpen = Array.isArray(this.baseValue) ? [...this.baseValue] : this.baseValue;
        },
        // 关闭panel
        hidePanel () {
            this.pickerVisible = false;
            this.userInput = null;
            this.dealBlur();
        },

        // input获焦
        handleFocus (e) {
            // const type = this.type;
            if (util_date.HAVE_TRIGGER_TYPES.indexOf('date') !== -1 && !this.pickerVisible) {
                this.showPanel();
            }
            this.$emit('focus', e);
        },
        // input 失焦
        handleBlur () {
            // this.pickerVisible = false;
            // this.dealBlur();
        },
        // 主动 失去焦点
        dealBlur() {
            console.log('blur');
            this.refInput.forEach(input => input.blur());
            this.$emit('blur', this);
        },
        // 单选-清除
        handleClear () {
            this.emitInput(null);
            this.emitChange(null);
        },
        // 范围清除
        handleRangeClear (event) {
            this.baseValue = null;
            this.userInput = null;
            this.emitInput('');
            this.emitChange('');
        },
        // change
        handleChange () {
            // 如果有值
            if (this.userInput) {

                const value = util_date.checkStringCanToDate(this.userInput, this.type);
                // 验证合法性
                if (value) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(value);   // 年和月是否合法
                    } else {
                        this.baseValue = util_date.toDate(value);   // 年和月是否合法
                    }
                    this.emitInput(this.baseValue);
                    this.emitChange(this.baseValue);
                }
                else {
                    // 年份，月份输入时，重新处理把时间给user-input，不然后被赋一次年月或月份后，不好对比时间
                    this.baseValue = this.valueOnOpen || null;
                }

                this.hidePanel();   // 关闭弹窗
            }

            if (this.userInput === '') {
                this.emitInput(null);
                this.emitChange(null);
                this.baseValue = null;
                this.userInput = null;
            }


        },
        // input 触发按键
        handleKeydown (event) {
            const keyCode = event.keyCode;

            // ESC
            if (keyCode === 27) {
                this.hidePanel();
                event.stopPropagation();
                return;
            }

            // Tab
            if (keyCode === 9) {
                if (!this.ranged) {
                    this.handleChange();
                    this.hidePanel();
                    event.stopPropagation();
                } else {
                    setTimeout(() => {
                        if (this.refInput.indexOf(document.activeElement) === -1) {
                            this.hidePanel();
                            event.stopPropagation();
                        }
                    }, 0);
                }
                return;
            }

            if (keyCode === 13) {
                if (this.userInput) {
                    this.handleChange();
                    this.hidePanel();
                }
                event.stopPropagation();
                return;
            }

            if (this.userInput) {
                event.stopPropagation();
                return;
            }
        },
        // change事件
        emitChange(val) {
            if (!util_date.valueEquals(val, this.valueOnOpen)) {
                this.$emit('change', util_date.format(val, this.type));
                this.valueOnOpen = val;
                this.checkValid(util_date.format(val, this.type));
            }
        },
        // input事件
        emitInput(val) {
            this.$emit('input', util_date.format(val, this.type));
            this.checkValid(util_date.format(val, this.type));
        },
        // 选中日期  [关闭panel => emit值]
        pickDate (val) {
            this.baseValue = val;
            this.userInput = null;
            this.hidePanel();
            this.emitInput(val);
            this.emitChange(val);
        },
        // 选中时间  [关闭panel => emit值]
        pickTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            this.emitInput(val);
            this.emitChange(val);
            if (isFinal) {
                this.hidePanel();
            }
        },
        // 选中日期时间
        pickDateTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            this.emitInput(val);
            this.emitChange(val);
            if (isFinal) {
                this.hidePanel();
            }
        },
        // 选中日期范围  [关闭panel => emit值]
        pickDateRange (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
                this.emitChange(val);
            }
        },
        handleRangeClick() {
            const type = this.type;

            if (util_date.HAVE_TRIGGER_TYPES.indexOf(type) !== -1 && !this.pickerVisible) {
                this.pickerVisible = true;
            }
            this.$emit('focus', this);
        },
        
        rangeMouseenter () {
            this.rangeFocus = true;
        },
        rangeMouseleave () {
            this.rangeFocus = false;
        },
        checkValid(val) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, val, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
            this.$refs.dropwrap.calcPosition();
        },
    }
})
// 日期面板
Vue.component('ui-date-panel', {
    template: `
        <div class="vd-ui-panel">
            <!-- 头部控制器 -->
            <div class="vd-ui-panel-header">
                <ul>
                    <li 
                        label="前一年"
                        class="arrow arrow-left1"
                        @click="prevYear">
                        <i class="vd-ui_icon icon-slide-up"></i>
                    </li>

                    <li label="上个月"
                        class="arrow"
                        v-show="currentView === 'date'"
                        @click="prevMonth">
                        <i class="vd-ui_icon icon-app-left"></i>
                    </li>

                    <li class="year-month">
                        <span
                            label="年份"
                            class="choose-year"
                            @click="showYearPicker"
                        >
                            {{ yearLabel }}
                        </span>

                        <span
                            label="月份"
                            v-show="currentView === 'date'"
                            class="choose-month"
                            @click="showMonthPicker"
                        >
                            {{ month + 1 }}月
                        </span>
                    </li>

                    <li 
                        label="下个月"
                        class="arrow"
                        v-show="currentView === 'date'"
                        @click="nextMonth">
                        <i class="vd-ui_icon icon-app-right"></i>
                    </li>

                    <li 
                        label="后一年"
                        class="arrow arrow-right1"
                        @click="nextYear">
                        <i class="vd-ui_icon icon-slide-up"></i>
                    </li>
                </ul>
            </div>

            <!-- table面板 -->
            <div class="vd-ui-panel-body">
                <ui-date-table
                    v-show="currentView == 'date'"
                    @pick="handleDatePick"
                    :selection-mode="selectionMode"
                    :firstDayOfWeek="firstDayOfWeek"
                    :value="value"
                    :defaultValue="defaultValue ? new Date(defaultValue) : null"
                    :date="date"
                    :cell-class-name="cellClassName"
                    :disabled-date="disabledDate"
                ></ui-date-table>
                
                <ui-year-table
                    v-show="currentView === 'year'"
                    @pick="handleYearPick"
                    :value="value"
                    :default-value="defaultValue ? new Date(defaultValue) : null"
                    :date="date"
                    :disabled-date="disabledDate"
                ></ui-year-table>
                
                <ui-month-table
                    v-show="currentView === 'month'"
                    @pick="handleMonthPick"
                    :value="value"
                    :default-value="defaultValue ? new Date(defaultValue) : null"
                    :date="date"
                    :disabled-date="disabledDate"
                ></ui-month-table>
            </div>

            <div class="date-shortcuts" v-if="shortcuts && shortcuts.length">
                <span 
                    v-for="(item, key) in shortcuts" 
                    :key="key" 
                    class="item-sc"
                    @click="handleShortcutClick(item)"
                >{{ item.text }}</span>
            </div>

        </div>
    `,
    props: {
        type: {
            type: String,
            default: 'date'
        },
        // 此层value接收到的是上层 userInput
        value: {
            type: [Date, String]
        },
        firstDayOfWeek: Number,
        disabledDate: {},
        cellClassName: {},
        rangeState: {
            default() {
                return {
                    endDate: null,
                    selecting: false
                };
            }
        },
        defaultValue: {},
        // week dates range
        selectionMode: {
            type: String,
            default: 'day'
        },
        shortcuts: {
            type: Array,
            default: ()=> {
                return []
            }
        },
    },
    data () {
        return { 
            date: new Date(),
            tableRows: [ [], [], [], [], [], [] ],
            lastRow: null,
            lastColumn: null,
            
            currentView: "date"
        }
    },
    watch: {
        value: {
            handler (val) {
                if (this.selectionMode === 'dates' && this.value) return;

                if (util_date.isDate(val)) {
                    this.date = val;
                }
                else if (typeof(val) == 'string' && util_date.checkToDate(val)) {
                    this.date = new Date(val);
                } else {
                    this.date = this.getDefaultValue();
                }
            },
            immediate: true
        },
        selectionMode (newVal) {
            if (newVal === 'month') {
                if (this.currentView !== 'year' || this.currentView !== 'month') {
                    this.currentView = 'month';
                }
            } else if (newVal === 'dates') {
                this.currentView = 'date';
            }
        }
    },
    computed: {
        year () {
            return this.date.getFullYear();
        },
        month () {
            return this.date.getMonth();
        },

        yearLabel () {
            const yearTranslation = '年';
            if (this.currentView === 'year') {
                const startYear = Math.floor(this.year / 12) * 12;
                if (yearTranslation) {
                    return startYear + ' ' + yearTranslation + ' - ' + (startYear + 11) + ' ' + yearTranslation;
                }
                return startYear + ' - ' + (startYear + 11);
            }
            return this.year + ' ' + yearTranslation;
        },
    },
    mounted () {
        this.resetView(); // 初始化类型
    },
    methods: {
        prevMonth () {
            this.date = util_date.prevMonth(this.date);
        },
        nextMonth () {
            this.date = util_date.nextMonth(this.date);
        },
        prevYear () {
            if (this.currentView === 'year') {
                this.date = util_date.prevYear(this.date, 12);
            } else {
                this.date = util_date.prevYear(this.date);
            }
        },
        nextYear() {
            if (this.currentView === 'year') {
                this.date = util_date.nextYear(this.date, 12);
            } else {
                this.date = util_date.nextYear(this.date);
            }
        },
        showYearPicker() {
            this.currentView = 'year';
        },
        showMonthPicker() {
            this.currentView = 'month';
        },

        // 处理点击日期 emit
        handleDatePick (val) {
            if (this.selectionMode === 'day') {
                this.date = val;
                this.$emit('pick', val);
            }
        },
        // 处理点击年 emit
        handleYearPick (year) {
            if (this.selectionMode === 'year') {
                this.date = util_date.modifyDate(this.date, year, 0, 1);
                this.$emit('pick', this.date);
            } else {
                // 是点击日期头部 弹出的年份选择，应该继续选择月份，而不是emit年份
                this.date = util_date.changeYearMonthAndClampDate(this.date, year, this.month);
                this.currentView = 'month';
            }
        },

        handleMonthPick (month) {
            if (this.selectionMode === 'month') {
                this.date = util_date.modifyDate(this.date, this.year, month, 1);
                this.$emit('pick', this.date);
            } else {
                this.date = util_date.changeYearMonthAndClampDate(this.date, this.year, month);
                this.currentView = 'date';
            }
        },

        getDefaultValue () {
            return this.defaultValue ? new Date(this.defaultValue) : new Date();
        },

        resetView () {
            if (this.selectionMode === 'month') {
                this.currentView = 'month';
            } else if (this.selectionMode === 'year') {
                this.currentView = 'year';
            } else {
                this.currentView = 'date';
            }
        },

        handleShortcutClick (shortcut) {
            if (shortcut.onClick) {
                shortcut.onClick(this);
            }
        },
    }
})
// 日期面板
Vue.component('ui-date-range-panel', {
    template: `
        <div class="vd-ui-daterange-panel" :style="{ width: shortcuts && shortcuts.length ? '685px': '588px' }">

            <div class="vd-ui-panel__body-wrapper">

                <!-- 快捷时间 -->
                <slot name="sidebar" class="el-picker-panel__sidebar"></slot>
                <div class="vd-ui-panel__sidebar" v-if="shortcuts && shortcuts.length">
                    <button
                        class="vd-ui-panel__shortcut"
                        v-for="(shortcut, key) in shortcuts"
                        :key="key"
                        @click="handleShortcutClick(shortcut)">{{ shortcut.text }}</button>
                </div>

                <!-- 日期面板 -->
                <div class="vd-ui-panel__body" :style="{ marginLeft: shortcuts && shortcuts.length ? '100px' : '0' }">

                    <div class="el-picker-panel__content is-left">

                        <div class="el-date-range-picker__header">
                            <button
                                @click="leftPrevYear"
                                class="arrow arrow1"
                                label="前一年">
                                <i class="vd-ui_icon icon-slide-up"></i>
                            </button>
                            <button
                                @click="leftPrevMonth"
                                class="arrow arrow2"
                                label="上个月">
                                <i class="vd-ui_icon icon-app-left"></i>
                            </button>
                            <div class="header-label" label="开始日期">{{ leftLabel }}</div>
                            <button 
                                v-if="unlinkPanels"
                                :disabled="!enableMonthArrow"
                                @click="leftNextMonth"
                                :class="{ 'disable': !enableMonthArrow }"
                                class="arrow arrow3"
                                label="下个月">
                                <i class="vd-ui_icon icon-app-right"></i>
                            </button>
                            <button 
                                v-if="unlinkPanels"
                                :disabled="!enableYearArrow"
                                @click="leftNextYear"
                                :class="{ 'disable': !enableYearArrow }"
                                class="arrow arrow4"
                                label="后一年">
                                <i class="vd-ui_icon icon-slide-up"></i>
                            </button>
                        </div>

                        <ui-date-table
                            selection-mode="range"
                            :date="leftDate"
                            :default-value="defaultValue"

                            :min-date="minDate"
                            :max-date="maxDate"
                            :range-state="rangeState"

                            :disabled-date="disabledDate"
                            :cell-class-name="cellClassName"
                            @changerange="handleChangeRange"
                            :firstDayOfWeek="firstDayOfWeek"
                            @pick="handleRangePick"
                        ></ui-date-table>

                    </div>

                    <div class="el-picker-panel__content is-right">
                        <div class="el-date-range-picker__header">

                            <button
                                v-if="unlinkPanels"
                                :disabled="!enableYearArrow" 
                                @click="rightPrevYear"
                                :class="{ 'disable': !enableYearArrow }"
                                class="arrow arrow1"
                                label="前一年">
                                <i class="vd-ui_icon icon-slide-up"></i>
                            </button>
                            <button
                                v-if="unlinkPanels" 
                                :disabled="!enableMonthArrow"
                                @click="rightPrevMonth"
                                :class="{ 'disable': !enableMonthArrow }"
                                class="arrow arrow2"
                                label="上个月">
                                <i class="vd-ui_icon icon-app-left"></i>
                            </button>
                            <div class="header-label" label="结束日期">{{ rightLabel }}</div>
                            <button 
                                @click="rightNextMonth"
                                class="arrow arrow3"
                                label="下个月">
                                <i class="vd-ui_icon icon-app-right"></i>
                            </button>
                            <button 
                                @click="rightNextYear"
                                class="arrow arrow4"
                                label="后一年">
                                <i class="vd-ui_icon icon-slide-up"></i>
                            </button>
                        </div>

                        <ui-date-table
                            selection-mode="range"
                            :date="rightDate"
                            :default-Value="defaultValue"

                            :min-date="minDate"
                            :max-date="maxDate"
                            :range-state="rangeState"

                            :disabled-date="disabledDate"
                            :cell-class-name="cellClassName"
                            @changerange="handleChangeRange"
                            :firstDayOfWeek="firstDayOfWeek"
                            @pick="handleRangePick"
                        ></ui-date-table>

                    </div>

                </div>

            </div>

        </div>
    `,

    props: {
        value: Array,
        firstDayOfWeek: {
            default: 1,
            type: Number,
            validator: val => val >= 1 && val <= 7
        },
        disabledDate: {},
        cellClassName: {},

        unlinkPanels: Boolean,  // 取消两个面板之间的联动
        // 快捷时间
        shortcuts: {
            type: Array,
            default: ()=> {
                return []
            }
        }
    },
    computed: {
        leftLabel () {
            return `${ this.leftDate.getFullYear() } 年 ${ this.leftDate.getMonth() + 1 } 月`;
        },
        rightLabel () {
            return `${ this.rightDate.getFullYear() } 年 ${ this.rightDate.getMonth() + 1 } 月`;
        },
        leftYear () {
            return this.leftDate.getFullYear();
        },
        leftMonth () {
            return this.leftDate.getMonth();
        },
        rightYear () {
            return this.rightDate.getFullYear();
        },
        rightMonth () {
            return this.rightDate.getMonth();
        },

        enableMonthArrow () {
            const nextMonth = (this.leftMonth + 1) % 12;
            const yearOffset = this.leftMonth + 1 >= 12 ? 1 : 0;
            return this.unlinkPanels && new Date(this.leftYear + yearOffset, nextMonth) < new Date(this.rightYear, this.rightMonth);
        },
        enableYearArrow () {
            return this.unlinkPanels && this.rightYear * 12 + this.rightMonth - (this.leftYear * 12 + this.leftMonth + 1) >= 12;
        }

    },
    data () {
        return {
            defaultValue: null,

            minDate: '',
            maxDate: '',
            leftDate: new Date(),
            rightDate: util_date.nextMonth(new Date()),
            rangeState: {
                endDate: null,
                selecting: false,
                row: null,
                column: null
            },

        }
    },
    watch: {
        value: {
            handler (newVal) {
                if (!newVal) {
                    this.minDate = null;
                    this.maxDate = null;
                } else if (Array.isArray(newVal)) {
                    this.minDate = util_date.isDate(newVal[0]) ? new Date(newVal[0]) : null;
                    this.maxDate = util_date.isDate(newVal[1]) ? new Date(newVal[1]) : null;
                    if (this.minDate) {
                        this.leftDate = this.minDate;
                        if (this.unlinkPanels && this.maxDate) {
                            const minDateYear = this.minDate.getFullYear();
                            const minDateMonth = this.minDate.getMonth();
                            const maxDateYear = this.maxDate.getFullYear();
                            const maxDateMonth = this.maxDate.getMonth();

                            this.rightDate = minDateYear === maxDateYear && minDateMonth === maxDateMonth
                                ? util_date.nextMonth(this.maxDate)
                                : this.maxDate;
                        }
                        else {
                            this.rightDate = util_date.nextMonth(this.leftDate);
                        }
                    }
                    else {
                        this.leftDate = this.calcDefaultValue(this.defaultValue)[0];
                        this.rightDate = util_date.nextMonth(this.leftDate);
                    }
                }
            },
            immediate: true
        },
        defaultValue (val) {
            if (!Array.isArray(this.value)) {
                const [left, right] = this.calcDefaultValue(val);
                this.leftDate = left;
                this.rightDate = val && val[1] && this.unlinkPanels
                    ? right
                    : util_date.nextMonth(this.leftDate);
            }
        },
    },
    mounted () {
    },
    methods: {
        // 计算默认值
        calcDefaultValue (defaultValue) {
            if (Array.isArray(defaultValue)) {
                return [new Date(defaultValue[0]), new Date(defaultValue[1])];
            } else if (defaultValue) {
                return [new Date(defaultValue), util_date.nextDate(new Date(defaultValue), 1)];
            } else {
                return [new Date(), util_date.nextDate(new Date(), 1)];
            }
        },

        handleChangeRange (val) {
            this.minDate = val.minDate;
            this.maxDate = val.maxDate;
            this.rangeState = val.rangeState;
        },

        handleShortcutClick (shortcut) {
            if (shortcut.onClick) {
                shortcut.onClick(this);
            }
        },

        // 范围值change
        handleRangePick (val, close = true) {
            const minDate = val.minDate;
            const maxDate = val.maxDate;

            if (this.maxDate === maxDate && this.minDate === minDate) {
                return;
            }

            this.maxDate = maxDate;
            this.minDate = minDate;

            setTimeout(() => {
                this.maxDate = maxDate;
                this.minDate = minDate;
            }, 10);
            if (!close ) return;
            this.handleConfirm();
        },
        handleConfirm () {
            if (this.isValidValue([this.minDate, this.maxDate])) {
                this.$emit('pick', [this.minDate, this.maxDate], true);  // true:关闭选择面板
            }
        },
        isValidValue (value) {
            return Array.isArray(value) &&
                value && value[0] && value[1] &&
                util_date.isDate(value[0]) && util_date.isDate(value[1]) &&
                value[0].getTime() <= value[1].getTime() && (
                    typeof this.disabledDate === 'function'
                        ? !this.disabledDate(value[0]) && !this.disabledDate(value[1])
                        : true
                );
        },

        leftPrevYear () {
            this.leftDate = util_date.prevYear(this.leftDate);
            if (!this.unlinkPanels) {
                this.rightDate = util_date.nextMonth(this.leftDate);
            }
        },
        leftPrevMonth () {
            this.leftDate = util_date.prevMonth(this.leftDate);
            if (!this.unlinkPanels) {
                this.rightDate = util_date.nextMonth(this.leftDate);
            }
        },
        rightNextMonth () {
            if (!this.unlinkPanels) {
                this.leftDate = util_date.nextMonth(this.leftDate);
                this.rightDate = util_date.nextMonth(this.leftDate);
            } else {
                this.rightDate = util_date.nextMonth(this.rightDate);
            }
        },
        rightNextYear () {
            if (!this.unlinkPanels) {
                this.leftDate = util_date.nextYear(this.leftDate);
                this.rightDate = util_date.nextMonth(this.leftDate);
            } else {
                this.rightDate = util_date.nextYear(this.rightDate);
            }
        },

        leftNextYear () {
            this.leftDate = util_date.nextYear(this.leftDate);
        },
        leftNextMonth () {
            this.leftDate = util_date.nextMonth(this.leftDate);
        },
        rightPrevYear () {
            this.rightDate = util_date.prevYear(this.rightDate);
        },
        rightPrevMonth () {
            this.rightDate = util_date.prevMonth(this.rightDate);
        },
        

    }
})
Vue.component('ui-date-time-panel', {
    template: `
        <div class="vd-ui-datetime-panel">
            <div class="datetime-content">
                <div class="datetime-date-panel">
                    <ui-date-panel
                        :type="type"
                        :value="value"
                        @pick="pickDate"
                        :firstDayOfWeek="firstDayOfWeek"
                        :selectionMode="selectionMode"
                        v-bind="pickerOptions"
                    ></ui-date-panel>
                </div>
                <div class="datetime-time-panel">
                    <ui-time-panel
                        :type="type"
                        :value="value"
                        @pick="pickTime"
                        v-bind="pickerOptions"
                    ></ui-time-panel>
                </div>
            </div>

            <div class="datetime-btn">
                <button
                    type="button"
                    class="time-cancel"
                    @click="handleNow"
                >此时</button>
                <button
                    type="button"
                    class="time-confirm"
                    @click="handleConfirm"
                >确定</button>
            </div>
        </div>
    `,

    props: {
        type: {
            type: String,
            default: 'date'
        },
        // 此层value接收到的是上层 userInput
        value: {
            type: [Date, String]
        },
        selectionMode: String,
        firstDayOfWeek: Number,
        pickerOptions: {},
    },
    data() {
        return {
            datetime: '',
        }
    },
    watch: {
        value: {
            handler (val) {
                if (util_date.isDate(val)) {
                    this.datetime = val;
                }
                else if (typeof(val) == 'string' &&  util_date.checkToDatetime(val)) {
                    this.datetime = util_date.timeToDatetime(val);
                } else {
                    this.datetime = this.getDefaultValue();
                }
            },
            immediate: true
        },
    },
    computed: {
        date () {
            let t = new Date(this.datetime);
            let Y = t.getFullYear();
            let M = t.getMonth();
            let D = t.getDate();
            return `${ Y }-${ M }-${ D }`;
        },
        time () {
            let t = new Date(this.datetime);
            let h = t.getHours();
            let m = t.getMinutes();
            let s = t.getSeconds();
            return `${ h }-${ m }-${ s }`;
        }
    },
    created () {
    },
    mounted () {
    },
    methods:{
        getDefaultValue () {
            return this.defaultValue ? new Date(this.defaultValue) : new Date();
        },
        pickDate (val) {
            let pVal = new Date(`${ util_date.getDateStr(val) } ${ util_date.getTimeStr(this.value) }`);
            this.$emit('pick', pVal);
        },
        pickTime (val) {
            let date = util_date.getDateStr(this.value);
            !date && (date = util_date.getDateStr(new Date()));

            let pVal = new Date(`${ date } ${ util_date.getTimeStr(val) }`);
            this.$emit('pick', pVal);
        },
        handleNow () {
            let pVal = new Date();
            this.$emit('pick', pVal, true);
        },
        handleConfirm () {
            let pVal = this.datetime || null;
            this.$emit('pick', pVal, true);  // 最后一个true 标识，关闭弹窗
        }
    }
})
Vue.component('ui-time-panel', {
    template: `
        <div class="vd-ui-time-panel">
            <!-- 头 只在日期+时间选择器显示 -->
            <div class="vd-ui-time-panel__header" v-if="type=='datetime'">
                {{ showTime }}
            </div>

            <!-- 内容 -->
            <div 
                class="vd-ui-time-panel__content"
                :style="{borderBottom: type=='time'? 'solid 1px #E1E5E8' : 'none'}"
            >
                <ui-time-table
                    :type="type"
                    :date="date"
                    @pick="pickTime"
                ></ui-time-table>
            </div>

            <!-- 脚 -->
            <div class="vd-ui-time-panel__footer" v-if="type=='time'">
                <button
                    type="button"
                    class="time-cancel"
                    @click="handleNow"
                >此时</button>
                <button
                    type="button"
                    class="time-confirm"
                    @click="handleConfirm"
                >确定</button>
            </div>
        </div>
    `,

    props: {
        type: {
            type: String,
            default: 'time'
        },
        // 此层value接收到的是上层 userInput
        value: {
            type: [Date, String]
        },
        selectableRange: String,   // 时间范围
        defaultValue: {},


        disabledDate: {},
        cellClassName: {},
        minDate: {},
        maxDate: {},
    },

    data() {
        return {
            val: '',
            // format: 'HH:mm:ss',
            selectionRange: [0, 2],
            needInitAdjust: true,

            date: new Date(),
        };
    },

    computed: {
        // 整体时间
        showTime () {
            return util_date.format(this.date, 'time');   // 这里要用点击后随着动的值，不能用选中的固定value值
        },
    },

    watch: {
        value: {
            handler (val) {
                if (util_date.isDate(val)) {
                    this.date = val;
                }
                else if (typeof(val) == 'string' && util_date.checkToTime(val)) {
                    this.date = util_date.timeToDate(val);
                } else {
                    this.date = this.getDefaultValue();
                }
            },
            immediate: true
        },

        defaultValue(val) {
            if (!util_date.isDate(this.value)) {
                this.date = val ? new Date(val) : new Date();
            }
        }
    },

    created () {
    },

    methods: {
        getDefaultValue () {
            return this.defaultValue ? new Date(this.defaultValue) : new Date();
        },
        // 选中时分秒
        pickTime (val) {
            this.date = val;
            this.$emit('pick', this.date);
        },
        // 此刻
        handleNow () {
            let date = new Date();
            this.$emit('pick', date, true);
        },
        // 确认
        handleConfirm () {
            let date = this.date || null;
            this.$emit('pick', date, true);  // 最后一个true 标识，关闭弹窗
        },
    },
})
const getDateTimestamp = function(time) {
    if (typeof time === 'number' || typeof time === 'string') {
        return util_date.clearTime(new Date(time)).getTime();
    } else if (time instanceof Date) {
        return util_date.clearTime(time).getTime();
    } else {
        return NaN;
    }
};

// 日期表格
Vue.component('ui-date-table', {
    template: `
        <table
            cellspacing="0"
            cellpadding="0"
            class="vd-ui-date-table "
            @click="handleClick"
            @mousemove="handleMouseMove"
            :class="{'is-week-mode': selectionMode === 'week'}"
        >
            <tbody>
                <tr>
                    <th v-for="(week, key) in WEEKS" :key="key">{{ WEEKS_SHOW[week] }}</th>
                </tr>
                <tr
                    class="vd-ui-date-table__row"
                    v-for="(row, key) in rows"
                    :key="key">
                    <td
                        v-for="(cell, key) in row"
                        :class="getCellClasses(cell)"
                        :key="key">
                        <div>
                            <span>{{ cell.text }}</span>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    `,
    props: {
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        value: {},
        defaultValue: {
            validator(val) {
                return val === null || util_date.isDate(val) || (Array.isArray(val) && val.every(util_date.isDate));
            }
        },
        date: {},
        // week  dates
        selectionMode: {
            default: 'day'
        },
        disabledDate: {},
        cellClassName: {},
        minDate: {},
        maxDate: {},
        // 范围
        rangeState: {
            default () {
                return {
                    endDate: null,
                    selecting: false
                };
            }
        },
    },
    data () {
        return {
            WEEKS_: ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'],
            WEEKS_SHOW: {
                sun: '日',
                mon: '一',
                tue: '二',
                wed: '三',
                thu: '四',
                fri: '五',
                sat: '六',
            },
            tableRows: [ [], [], [], [], [], [] ],
            lastRow: null,
            lastColumn: null,
        }
    },
    watch: {
        'rangeState.endDate'(newVal) {
            this.markRange(this.minDate, newVal);
        },
        minDate(newVal, oldVal) {
            if (getDateTimestamp(newVal) !== getDateTimestamp(oldVal)) {
                this.markRange(this.minDate, this.maxDate);
            }
        },

        maxDate(newVal, oldVal) {
            if (getDateTimestamp(newVal) !== getDateTimestamp(oldVal)) {
                this.markRange(this.minDate, this.maxDate);
            }
        }
    },
    computed: {
        offsetDay () {
            const week = this.firstDayOfWeek;
            // 周日为界限，左右偏移的天数，3217654 例如周一就是 -1，目的是调整前两行日期的位置
            return week > 3 ? 7 - week : -week;
        },
        WEEKS () {
            const week = this.firstDayOfWeek;
            return this.WEEKS_.concat(this.WEEKS_).slice(week, week + 7);
        },
        year () {
            return this.date.getFullYear();
        },
        month () {
            return this.date.getMonth();
        },

        startDate () {
            return util_date.getStartDateOfMonth(this.year, this.month);
        },

        // 面板日期数据
        rows () {
            try {
                const date = new Date(this.year, this.month, 1);
                let day = util_date.getFirstDayOfMonth(date); // day of first day   获取每月第一天
                const dateCountOfMonth = util_date.getDayCountOfMonth(date.getFullYear(), date.getMonth());   // 当月有几天
                const dateCountOfLastMonth = util_date.getDayCountOfMonth(date.getFullYear(), (date.getMonth() === 0 ? 11 : date.getMonth() - 1));  // 上个月有多少天

                day = (day === 0 ? 7 : day);
                const offset = this.offsetDay;
                const rows = this.tableRows;
                let count = 1;

                const startDate = this.startDate;
                const disabledDate = this.disabledDate;
                const cellClassName = this.cellClassName;
                const selectedDate = [];
                const now = getDateTimestamp(new Date());

                for (let i = 0; i < 6; i++) {
                    const row = rows[i];

                    for (let j = 0; j < 7; j++) {
                        let cell = row[j];
                        if (!cell) {
                            cell = { row: i, column: j, type: 'normal', inRange: false, start: false, end: false };
                        }

                        cell.type = 'normal';

                        const index = i * 7 + j;

                        const time = util_date.nextDate(startDate, index - offset).getTime();

                        cell.inRange = time >= getDateTimestamp(this.minDate) && time <= getDateTimestamp(this.maxDate);
                        cell.start = this.minDate && time === getDateTimestamp(this.minDate);
                        cell.end = this.maxDate && time === getDateTimestamp(this.maxDate);

                        const isToday = time === now;

                        if (isToday) {
                            cell.type = 'today';
                        }

                        if (i >= 0 && i <= 1) {
                            const numberOfDaysFromPreviousMonth = day + offset < 0 ? 7 + day + offset : day + offset;

                            if (j + i * 7 >= numberOfDaysFromPreviousMonth) {
                                cell.text = count++;
                            } else {
                                cell.text = dateCountOfLastMonth - (numberOfDaysFromPreviousMonth - j % 7) + 1 + i * 7;
                                cell.type = 'prev-month';
                            }
                        } else {
                            if (count <= dateCountOfMonth) {
                                cell.text = count++;
                            } else {
                                cell.text = count++ - dateCountOfMonth;
                                cell.type = 'next-month';
                            }
                        }

                        let cellDate = new Date(time);
                        cell.disabled = typeof disabledDate === 'function' && disabledDate(cellDate);
                        cell.selected = util_date.arrayFind(selectedDate, date => date.getTime() === cellDate.getTime());
                        cell.customClass = typeof cellClassName === 'function' && cellClassName(cellDate);
                        this.$set(row, j, cell);
                    }
                }
                return rows;
            } catch (err) {
                console.log('table数据 rowsErr', err);
            }
        }
    },
    methods: {
        cellMatchesDate(cell, date) {
            const value = new Date(date);
            return (this.year === value.getFullYear() && this.month === value.getMonth() && Number(cell.text) === value.getDate());
        },

        getCellClasses (cell) {
            const selectionMode = this.selectionMode;
            const defaultValue = this.defaultValue ? Array.isArray(this.defaultValue) ? this.defaultValue : [this.defaultValue] : [];

            let classes = [];
            if ((cell.type === 'normal' || cell.type === 'today') && !cell.disabled) {
                classes.push('available');
                if (cell.type === 'today') {
                    classes.push('today');
                }
            } else {
                classes.push(cell.type);
            }

            if (cell.type === 'normal' && defaultValue.some(date => this.cellMatchesDate(cell, date))) {
                classes.push('default');
            }

            if (selectionMode === 'day' && (cell.type === 'normal' || cell.type === 'today') && this.cellMatchesDate(cell, this.value)) {
                classes.push('current');
            }

            if (cell.inRange && ((cell.type === 'normal' || cell.type === 'today') || this.selectionMode === 'week')) {
                classes.push('in-range');

                if (cell.start) {
                    classes.push('start-date');
                }

                if (cell.end) {
                    classes.push('end-date');
                }
            }

            if (cell.disabled) {
                classes.push('disabled');
            }

            if (cell.selected) {
                classes.push('selected');
            }

            if (cell.customClass) {
                classes.push(cell.customClass);
            }

            return classes.join(' ');
        },

        getDateOfCell (row, column) {
            const offsetFromStart = row * 7 + (column - (this.showWeekNumber ? 1 : 0)) - this.offsetDay;
            return util_date.nextDate(this.startDate, offsetFromStart);
        },

        markRange (minDate, maxDate) {
            minDate = getDateTimestamp(minDate);
            maxDate = getDateTimestamp(maxDate) || minDate;
            [minDate, maxDate] = [Math.min(minDate, maxDate), Math.max(minDate, maxDate)];

            const startDate = this.startDate;
            const rows = this.rows;
            for (let i = 0, k = rows.length; i < k; i++) {
                const row = rows[i];
                for (let j = 0, l = row.length; j < l; j++) {
                    if (this.showWeekNumber && j === 0) continue;

                    const cell = row[j];
                    const index = i * 7 + j + (this.showWeekNumber ? -1 : 0);
                    const time = util_date.nextDate(startDate, index - this.offsetDay).getTime();

                    cell.inRange = minDate && time >= minDate && time <= maxDate;
                    cell.start = minDate && time === minDate;
                    cell.end = maxDate && time === maxDate;
                }
            }
        },

        // 鼠标移动，针对范围选择
        handleMouseMove (event) {
            if (!this.rangeState.selecting) return;   // 只针对range范围类型

            let target = event.target;
            if (target.tagName === 'SPAN') {
                target = target.parentNode.parentNode;
            }
            if (target.tagName === 'DIV') {
                target = target.parentNode;
            }
            if (target.tagName !== 'TD') return;

            const row = target.parentNode.rowIndex - 1;
            const column = target.cellIndex;

            // can not select disabled date
            if (this.rows[row][column].disabled) return;

            // only update rangeState when mouse moves to a new cell
            // this avoids frequent Date object creation and improves performance
            if (row !== this.lastRow || column !== this.lastColumn) {
                this.lastRow = row;
                this.lastColumn = column;
                this.$emit('changerange', {
                    minDate: this.minDate,
                    maxDate: this.maxDate,
                    rangeState: {
                        selecting: true,
                        endDate: this.getDateOfCell(row, column)
                    }
                });
            }
        },

        // 点击每个日期
        handleClick (event) {
            try {
                let target = event.target;
                if (target.tagName === 'SPAN') {
                    target = target.parentNode.parentNode;
                }
                if (target.tagName === 'DIV') {
                    target = target.parentNode;
                }

                if (target.tagName !== 'TD') return;   // 如果点击的不是每一个小格子 td，不处理

                const row = target.parentNode.rowIndex - 1;
                const column = this.selectionMode === 'week' ? 1 : target.cellIndex;
                const cell = this.rows[row][column];

                if (cell.disabled || cell.type === 'week') return;

                const newDate = this.getDateOfCell(row, column);

                if (this.selectionMode === 'range') {
                    // 范围选择点击
                    if (!this.rangeState.selecting) {
                        this.$emit('pick', { minDate: newDate, maxDate: null });
                        this.rangeState.selecting = true;
                    } else {
                        if (newDate >= this.minDate) {
                            this.$emit('pick', { minDate: this.minDate, maxDate: newDate });
                        } else {
                            this.$emit('pick', { minDate: newDate, maxDate: this.minDate });
                        }
                        this.rangeState.selecting = false;
                    }
                } else if (this.selectionMode === 'day') {
                    this.$emit('pick', newDate);
                }
            } catch (err) {
                console.log('表格内数据点击 handleClick ', err);
            }
        },
    }
})
// 月份表格
Vue.component('ui-month-table', {
    template: `
        <table @click="handleMonthTableClick" class="vd-ui-month-table">
            <tbody>
                <tr v-for="(row, key) in rows" :key="key">
                    <td :class="getCellStyle(cell)" v-for="(cell, key) in row" :key="key">
                        <div>
                            <a class="cell">{{ monthsShow[cell.text] }}</a>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    `,

    props: {
        disabledDate: {},
        value: {},
        selectionMode: {
            default: 'month'
        },
        defaultValue: {
            validator(val) {
                // null or valid Date Object
                return val === null || util_date.isDate(val) || (Array.isArray(val) && val.every(util_date.isDate));
            }
        },
        date: {},
    },

    data() {
        return {
            months: ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
            monthsShow: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
            tableRows: [ [], [], [] ],
            lastRow: null,
            lastColumn: null
        };
    },

    computed: {
        rows() {
            // TODO: refactory rows / getCellClasses
            const rows = this.tableRows;
            const disabledDate = this.disabledDate;
            const selectedDate = [];
            const now = this.getMonthTimestamp(new Date());

            for (let i = 0; i < 3; i++) {
                const row = rows[i];
                for (let j = 0; j < 4; j++) {
                    let cell = row[j];
                    if (!cell) {
                        cell = { row: i, column: j, type: 'normal', inRange: false, start: false, end: false };
                    }

                    cell.type = 'normal';

                    const index = i * 4 + j;
                    const time = new Date(this.date.getFullYear(), index).getTime();
                    // cell.inRange = time >= this.getMonthTimestamp(this.minDate) && time <= this.getMonthTimestamp(this.maxDate);
                    // cell.start = this.minDate && time === this.getMonthTimestamp(this.minDate);
                    // cell.end = this.maxDate && time === this.getMonthTimestamp(this.maxDate);
                    const isToday = time === now;

                    if (isToday) {
                        cell.type = 'today';
                    }
                    cell.text = index;
                    let cellDate = new Date(time);
                    cell.disabled = typeof disabledDate === 'function' && disabledDate(cellDate);
                    cell.selected = util_date.arrayFind(selectedDate, date => date.getTime() === cellDate.getTime());

                    this.$set(row, j, cell);
                }
            }
            return rows;
        }
    },

    methods: {
        datesInMonth (year, month) {
            const numOfDays = util_date.getDayCountOfMonth(year, month);
            const firstDay = new Date(year, month, 1);
            return util_date.range(numOfDays).map(n => util_date.nextDate(firstDay, n));
        },
        clearDate (date) {
            return new Date(date.getFullYear(), date.getMonth());
        },
        getMonthTimestamp (time) {
            if (typeof time === 'number' || typeof time === 'string') {
                return this.clearDate(new Date(time)).getTime();
            } else if (time instanceof Date) {
                return this.clearDate(time).getTime();
            } else {
                return NaN;
            }
        },

        cellMatchesDate(cell, date) {
            const value = new Date(date);
            return this.date.getFullYear() === value.getFullYear() && Number(cell.text) === value.getMonth();
        },
        getCellStyle(cell) {
            const style = {};
            const year = this.date.getFullYear();
            const today = new Date();
            const month = cell.text;
            const defaultValue = this.defaultValue ? Array.isArray(this.defaultValue) ? this.defaultValue : [this.defaultValue] : [];
            style.disabled = typeof this.disabledDate === 'function'
                ? this.datesInMonth(year, month).every(this.disabledDate)
                : false;
            style.current = util_date.arrayFindIndex(util_date.coerceTruthyValueToArray(this.value), date => date.getFullYear() === year && date.getMonth() === month) >= 0;
            style.today = today.getFullYear() === year && today.getMonth() === month;
            style.default = defaultValue.some(date => this.cellMatchesDate(cell, date));

            if (cell.inRange) {
                style['in-range'] = true;

                if (cell.start) {
                    style['start-date'] = true;
                }

                if (cell.end) {
                    style['end-date'] = true;
                }
            }
            return style;
        },
        getMonthOfCell(month) {
            const year = this.date.getFullYear();
            return new Date(year, month, 1);
        },

        handleMonthTableClick (event) {
            let target = event.target;
            if (target.tagName === 'A') {
                target = target.parentNode.parentNode;
            }
            if (target.tagName === 'DIV') {
                target = target.parentNode;
            }
            if (target.tagName !== 'TD') return;
            if (util_date.hasClass(target, 'disabled')) return;


            const column = target.cellIndex;
            const row = target.parentNode.rowIndex;
            const month = row * 4 + column;
            this.$emit('pick', month);
        }
    },
})
Vue.component('ui-time-table', {
    template: `
        <div class="spinner-list">
            <div class="spinner-item">
                <ul class="">
                    <li
                        v-for="t in arrY" :key="t"
                        :class="{ 'active': hour == t }"
                        @click="chooseHour(t)"
                    >{{ t }}</li>
                </ul>
            </div>
            <div class="spinner-item">
                <ul class="">
                    <li
                        v-for="t in arrMD" :key="t"
                        :class="{ 'active': minute == t }"
                        @click="chooseMinute(t)"
                        >{{ t }}</li>
                    </ul>
                </div>
                <div class="spinner-item">
                    <ul class="">
                        <li
                        v-for="t in arrMD" :key="t"
                        :class="{ 'active': second == t }"
                        @click="chooseSecond(t)"
                    >{{ t }}</li>
                </ul>
            </div>
        </div>
    `,
    props: {
        type: String,
        date: Date
    },
    data () {
        return {
            arrY: [
                '00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11',
                '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'
            ],
            arrMD: [
                '00', '01', '02', '03', '04', '05', '06', '07', '08', '09',
                '10', '11', '12', '13', '14', '15', '16', '17', '18', '19',
                '20', '21', '22', '23', '24', '25', '26', '27', '28', '29',
                '30', '31', '32', '33', '34', '35', '36', '37', '38', '39',
                '40', '41', '42', '43', '44', '45', '46', '47', '48', '49',
                '50', '51', '52', '53', '54', '55', '56', '57', '58', '59',
            ]
        }
    },
    computed: {
        year () {
            return this.date.getFullYear();
        },
        month () {
            return this.date.getMonth();
        },
        day () {
            return this.date.getDate();
        },
        // 时
        hour () {
            let h = this.date.getHours();
            return h < 10 ? '0' + h : h
        },
        // 分
        minute () {
            let m = this.date.getMinutes();
            return m < 10 ? '0' + m : m;
        },
        // 秒
        second () {
            let s = this.date.getSeconds();
            return s < 10 ? '0' + s : s;
        },
    },
    methods: {
        chooseHour (t) {
            let date = new Date(this.year, this.month, this.day, t, this.minute, this.second);
            this.$emit('pick', date);
        },
        chooseMinute (t) { 
            let date = new Date(this.year, this.month, this.day, this.hour, t, this.second);
            this.$emit('pick', date);
        },
        chooseSecond (t) { 
            let date = new Date(this.year, this.month, this.day, this.hour, this.minute, t);
            this.$emit('pick', date);
        }
    }

})

// 年份表格
Vue.component('ui-year-table', {
    template: `
        <table @click="handleYearTableClick" class="vd-ui-year-table">
            <tbody>
                <tr>
                    <td class="available" :class="getCellStyle(startYear + 0)">
                        <a class="cell">{{ startYear }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 1)">
                        <a class="cell">{{ startYear + 1 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 2)">
                        <a class="cell">{{ startYear + 2 }}</a>
                    </td>
                </tr>

                <tr>
                    <td class="available" :class="getCellStyle(startYear + 3)">
                        <a class="cell">{{ startYear + 3 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 4)">
                        <a class="cell">{{ startYear + 4 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 5)">
                        <a class="cell">{{ startYear + 5 }}</a>
                    </td>
                </tr>

                <tr>
                    <td class="available" :class="getCellStyle(startYear + 6)">
                        <a class="cell">{{ startYear + 6 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 7)">
                        <a class="cell">{{ startYear + 7 }}</a>
                    </td>

                    <td class="available" :class="getCellStyle(startYear + 8)">
                        <a class="cell">{{ startYear + 8 }}</a>
                    </td>
                </tr>

                <tr>
                    <td class="available" :class="getCellStyle(startYear + 9)">
                        <a class="cell">{{ startYear + 9 }}</a>
                    </td>
                    <td  class="available" :class="getCellStyle(startYear + 10)">
                        <a class="cell">{{ startYear + 10 }}</a>
                    </td>
                    <td  class="available" :class="getCellStyle(startYear + 11)">
                        <a class="cell">{{ startYear + 11 }}</a>
                    </td>
                </tr>
            </tbody>
        </table>
    `,

    props: {
        disabledDate: {},
        value: {},
        defaultValue: {
            validator(val) {
                return val === null || (val instanceof Date && util_date.isDate(val));
            }
        },
        date: {}
    },

    computed: {
        startYear () {
            return Math.floor(this.date.getFullYear() / 12) * 12;
        }
    },

    methods: {
        datesInYear (year) {
            const numOfDays = util_date.getDayCountOfYear(year);
            const firstDay = new Date(year, 0, 1);
            return util_date.range(numOfDays).map(n => util_date.nextDate(firstDay, n));
        },

        getCellStyle (year) {
            const style = {};
            const today = new Date();

            style.disabled = typeof this.disabledDate === 'function'
                ? this.datesInYear(year).every(this.disabledDate)
                : false;

            style.current = util_date.arrayFindIndex(util_date.coerceTruthyValueToArray(this.value), date => date.getFullYear() === year ) >= 0;
            style.today = today.getFullYear() === year;
            style.default = this.defaultValue && this.defaultValue.getFullYear() === year;

            return style;
        },

        handleYearTableClick (event) {
            const target = event.target;

            if (target.tagName === 'A') {

                if (util_date.hasClass(target.parentNode, 'disabled')) return;

                const year = target.textContent || target.innerText;
                this.$emit('pick', Number(year));
            }
        }
    }
})
Vue.component('ui-dialog', {
    template: `
        <div class="vd-ui-dialog_wrapper" ref="dialogWrap" v-show="visible" @click.prevent="modalClose" :style="{'z-index': zIndex}">
            <div class="vd-ui-dialog" :class="{'vd-ui-dialog--in': scroll=='in'}" :style="style" @click.stop>
                <div class="vd-ui-dialog_head">
                    <span class="vd-ui-dialog_head_title">{{title}}</span>
                    <div class="vd-ui-dialog_head_headerBtn" @click="handleClose('close')" v-show="showClose">
                        <i class="vd-ui_icon icon-close1"></i>
                    </div>
                </div>
                <div class="vd-ui-dialog_content">
                    <slot></slot>
                </div>
                <div class="vd-ui-dialog_footer" :style="{'text-align': align}" v-if="$slots.footer" >
                    <slot name="footer"></slot>
                </div>
            </div> 
        </div>
    `,
    props: {
        title: {
            type: String,
            default: '标题'
        },
        visible: {
            type: Boolean,
            default: false
        },
        size: String,
        width: String,
        maxHeight: String,
        clickModal: {
            type: Boolean,
            default: false
        },
        showClose: {
            type: Boolean,
            default: true
        },
        align: {
            type: String,
            default: ''
        },
        scroll: {
            type: String,
            default: 'in'
        }
    },
    data () {
        return {
            zIndex: '',
        }
    },
    computed: {
        style() {
            let style = {};
            switch (this.size) {
                case 'small':
                   style.width = '480px';
                   break;
                case 'middle':
                   style.width = '720px';
                   break;
                case 'large':
                   style.width = '960px';
                   break;
            }
            if (this.width) style.width = this.width;
            style.maxHeight = this.maxHeight;
            return style;
        }
    },
    watch: {
        visible (val) {
            if (val) {
                document.body.append(this.$refs.dialogWrap);

                // if (document.querySelector(".vd-ui-modal")) {
                //     let index = Number(document.querySelector(".vd-ui-modal").style.zIndex);
                //     document.querySelector(".vd-ui-modal").style.zIndex = index + 2;
                //     this.zIndex = index + 3;
                // } else {
                //     const div = document.createElement("div");
                //     div.className="vd-ui-modal vd-ui-modal-enter";
                //     document.body.appendChild(div);
                //     document.body.style.overflow = 'hidden';
                //     document.querySelector(".vd-ui-modal").style.zIndex = 2000;
                //     this.zIndex = 2001
                // }
                this.$emit('open');
            } else {
                this.$refs.dialogWrap.remove();
                // this.closeModal();
            }
        }
    },
    mounted() {
        if (this.visible) {
            this.$emit('open');
            if (document.querySelector(".vd-ui-modal")) {
                let index = Number(document.querySelector(".vd-ui-modal").style.zIndex);
                document.querySelector(".vd-ui-modal").style.zIndex = index + 2;
                this.zIndex = index + 3;
            } else {
                const div = document.createElement("div");
                div.className="vd-ui-modal vd-ui-modal-enter";
                document.body.appendChild(div);
                document.body.style.overflow = 'hidden';
                document.querySelector(".vd-ui-modal").style.zIndex = 2000;
                this.zIndex = 2001;
            }
        }
    },
    methods: {
        modalClose() {
            if (!this.clickModal) return;
            this.handleClose();
        },
        handleClose() {
            this.$emit('update:visible', false);
            this.$emit('close')
        },
        closeModal() {
            // if (document.querySelector(".vd-ui-modal")) {
            //     let index = Number(document.querySelector(".vd-ui-modal").style.zIndex);
            //     if (index > 2000) {
            //         document.querySelector(".vd-ui-modal").style.zIndex = index -2;
            //     } else {
            //         let dom = document.querySelector(".vd-ui-modal");
            //         dom.classList.remove('vd-ui-modal-enter'); //删除类名
            //         dom.classList.add('vd-ui-modal-leave'); //添加类名
            //         document.body.style.overflow = 'auto';
            //         setTimeout(()=> {
            //             dom.parentNode.removeChild(dom);
            //         }, 180)
            //     }
            // }
            // this.$emit('close');
        }
    }
})
Vue.prototype.$form = {
    rules(params, key, data) {
        let validList = [];
        for (let item in params) {
            validList.push(item);
        }

        this.validList[key] = validList;

        this.ruleList[key] = params;
        this.pages[key] = data;
    },
    ruleList: {},
    validList: {},
    elList: {},
    pages: {},
    checkValid(key, value, formName) {
        let ruleList = this.ruleList[formName];

        if (!ruleList || !ruleList[key]) {
            return {
                result: true
            }
        }

        let ruleItem = ruleList[key];

        if (ruleItem.required) {
            let type = typeof value;

            if (!value || (type === 'string' && !value.trim()) || (Array.isArray(value) && !value.length)) {
                return {
                    result: false,
                    message: ruleItem.required
                }
            }
        }

        // 小数点位
        if (ruleItem.toFixed) {
            const reg1 = /^\d+(\.\d+)?$/; // 数字类型
            const reg2 = new RegExp(`^\\d+(\\.\\d{1,${ruleItem.toFixed.max}})?$`); // 小数点

            if (!reg1.test(value)) {
                return {
                    result: false,
                    message: ruleItem.toFixed.message
                }
            } else if (!reg2.test(value)) {
                return {
                    result: false,
                    message: ruleItem.toFixed.message
                }
            }
        }

        if (ruleItem.custom) {
            if (!ruleItem.custom.valid(value)) {
                return {
                    result: false,
                    message: ruleItem.custom.message
                }
            }
        }

        return {
            result: true
        }
    },
    validForm(formName) {
        let validList = this.validList[formName];

        if (!validList || !validList.length) {
            return true;
        }

        let flag = true;
        let firstEl = null;
        validList.forEach(item => {
            let itemResult = this.checkValid(item, this.pages[formName][item], formName);

            if (!itemResult.result) {
                flag = false;

                let elList = this.elList[formName];
                if (elList[item]) {
                    if(!firstEl) {
                        firstEl = elList[item];
                        let rect = firstEl.$el.getBoundingClientRect();
                        let parent = this.getScrollParent(firstEl.$el);

                        if(!parent || parent == window) {
                            if(rect.top < 200 || rect.top > window.innerHeight) {
                                window.scrollTo(0, window.screenY + rect.top - 200);
                            }
                        } else {
                            let parentRect = parent.getBoundingClientRect();
                            if(rect.top + rect.height - parentRect.top > parentRect.height) {
                                parent.scrollTo(0, firstEl.$el.offsetTop)
                            }
                        }
                    }
                    elList[item].triggerError(itemResult)
                }
            }
        })

        return flag;
    },
    getScrollParent(element) {
        if (!element) {
            return null;
        }

        const overflowRegex = /(scroll|auto)/;
        const parent = element.parentElement;

        if (parent && overflowRegex.test(window.getComputedStyle(parent).overflow + window.getComputedStyle(parent).overflowY + window.getComputedStyle(parent).overflowX)) {
            return parent;
        }

        return this.getScrollParent(parent);
    },
    setValidEl(el) {
        if (el.$attrs.valid || el.valid) {
            let valid = (el.$attrs.valid || el.valid).split('_');

            el.validKey = valid[0];
            el.validValue = valid[1];

            if (el.validKey && el.validValue) {
                if (!this.elList[el.validKey]) {
                    this.elList[el.validKey] = {};
                }

                this.elList[el.validKey][el.validValue] = el;
            }
        }
    },
    validEl(name) {
        let formName_elName = name.split('_');
        let formName = formName_elName[0];
        let elName = formName_elName[1];

        let validList = this.validList[formName];

        if (!validList || !validList.length) {
            return true;
        }

        let flag = true;

        validList.forEach(item => {
            if(item == elName) {
                let itemResult = this.checkValid(item, this.pages[formName][item], formName);

                flag = itemResult.result;

                let elList = this.elList[formName];
                if (elList[item]) {
                    elList[item].triggerError(itemResult)
                }
            }
        })

        return flag;
    }
};
Vue.component('ui-form-item', {
    template: `<div class="form-item" :class="{'form-item-text': text}">
        <div class="form-label" :style="{'width': labelWidth}"><span class="must" v-if="must">*</span>{{label}}<template v-if="label">：</template></div>
        <div class="form-fields">
            <slot></slot>
        </div>
    </div>`,
    props: {
        label: {
            type: String,
            default: ''
        },
        must: {
            type: Boolean,
            default: false
        },
        text: {
            type: Boolean,
            default: false
        },
        labelWidth: {
            type: String,
            default: ''
        }
    },
    mounted(){
       
    },
    methods: {
       
    }
})
Vue.component('ui-import', {
    template: `<div class="vd-ui-upload-wrap">
        <div class="vd-ui-upload-file-wrap">
            <ui-button @click="triggerInputClick">{{ file ? '重新上传' : '本地上传' }}</ui-button>
            <div class="vd-ui-file-list" v-if="file">
                <div class="vd-ui-file-item">
                    <div class="vd-ui-file-info">
                        <div class="vd-ui-file-icon">
                            <img :src="'/static/image/upload-icon/' + (typeParse[file.type] || 'other') + '.svg'"></img>
                        </div>
                        <div class="vd-ui-file-name">{{file.name}}</div>
                        <div class="vd-ui-file-size">{{file.size}}</div>
                        <div class="vd-ui-file-option" @click="clear">
                            <i class="vd-ui_icon icon-recycle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="vd-ui-input-error" v-if="errorMsg">
            <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
            <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
        </div>
        <!-- 提示 -->
        <div class="vd-ui-upload-tips"> 
            <slot name="tips"></slot>
        </div>
        <input 
            type="file" 
            ref="fileInput" 
            :accept="accept" 
            @change="handlerFileInputChange" 
            style="display:none"/>
    </div>`,
    props: {
        label: {
            type: String,
            default: '本地上传'
        },
        errorMsg: String,
        limitType: String,
    },
    data() {
        return {
            accept: '',
            acceptParse: {
                gif: 'image/gif',
                jpg: 'image/jpeg',
                jpeg: 'image/jpeg',
                png: 'image/png',
                pdf: 'application/pdf',
                doc: 'application/msword',
                xls: 'application/vnd.ms-excel',
                xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            },
            typeParse: {
                xls: 'excel',
                xlsx: 'excel',
                pdf: 'pdf',
                png: 'pic',
                jpg: 'pic',
                jpeg: 'pic',
                bmp: 'pic',
                gif: 'pic',
                ppt: 'ppt',
                pptx: 'ppt',
                doc: 'word',
                docx: 'word',
            },
            file: null
        }
    },
    watch: {},
    mounted() {
        this.init();
    },
    methods: {
        init() {
            if (this.limitType) {
                let acceptList = [];
                let isAll = false;
                this.limitType.split(',').forEach(item => {
                    if(!this.acceptParse[item]) {
                        isAll = true;
                    } else {
                        acceptList.push(this.acceptParse[item]);
                    }
                })

                if(!isAll) {
                    this.accept = acceptList.join(',')
                } else if (this.type == 'img') {
                    this.accept = 'image/*';
                }
            }
        },
        triggerInputClick() {
            this.$refs.fileInput.click();
        },
        getSize(size) {
            if(size > 1024 * 1024) {
                return parseFloat((size/1024/1024).toFixed(1)) + 'MB';
            } else {
                return parseInt(size/1024) + 'KB';
            }
        },
        handlerFileInputChange() {
            let files = this.$refs.fileInput.files;

            if(files.length) {
                let file = files[0];
                let fileName = file.name;
                let fileNameTxt = fileName.substring(0, fileName.lastIndexOf('.'));
                let fileType = fileName.substring(fileName.lastIndexOf('.') + 1, fileName.length);

                this.file = {
                    name: fileNameTxt,
                    type: fileType,
                    fullName: fileName,
                    size: this.getSize(file.size)
                }

                this.$refs.fileInput.value = "";
                this.$emit('change', file);
            }
        },
        clear() {
            this.$refs.fileInput.value = "";
            this.file = null;
            this.$emit('change', null);
        }
    }
})
Vue.component('ui-input', {
    template: `<div :class="[
                type === 'textarea' ? 'vd-ui-textarea' : 'vd-ui-input',
                size ? 'vd-ui-input--' + size : '',
                {
                    'is-focus': focused,
                    'is-disabled': disabled,
                    'vd-ui-input-group': $slots.prepend || $slots.append || prepend || append,
                    'vd-ui-input-group--prepend': $slots.prepend || prepend,
                    'vd-ui-input-group--append': $slots.append || append,
                    'vd-ui-input--prefix': $slots.prefix || prefixIcon,
                    'vd-ui-input--suffix': $slots.suffix || suffixIcon || clearable || showPwdVisible,
                    'vd-ui-input--error': errorable && !disabled
                }
            ]"
            :style="style"
            @mouseenter="handleEnter"
            @mouseleave="handleLeave"
        >
            <template v-if="type !== 'textarea'">
                <!-- 前置元素 -->
                <div class="vd-ui-input-group__prepend" v-if="$slots.prepend || prepend">
                    <slot name="prepend">
                        <span>{{prepend}}</span>
                    </slot>
                </div>
                
                <input
                    class="vd-ui-input__inner"
                    :type="trueType"
                    v-bind="$attrs"
                    :disabled="disabled"
                    :readonly="readonly"
                    ref="input"
                    @input="handleInput"
                    @focus="handleFocus"
                    @blur="handleBlur"
                    @change="handleChange"
                    @compositionend="commentPressEnd"
                    @compositionstart="commentPressStart"
                    @keyup="handleKeyup"
                >

                <!-- 前置内容 -->
                <span class="vd-ui-input__prefix" v-if="$slots.prefix || prefixIcon">
                    <slot name="prefix">
                        <i class="vd-ui-input__icon vd-ui_icon"
                            v-if="prefixIcon"
                            :class="prefixIcon">
                        </i>
                    </slot>               
                </span>
                <!-- 后置内容 -->
                <span class="vd-ui-input__suffix"
                    v-if="getSuffixVisible"
                >
                <span class="vd-ui-input__suffix-inner">
                    <i v-if="showClear"
                        class="vd-ui-input__icon vd-ui_icon icon-error2"
                        @mousedown.prevent
                        @click.prevent.stop="clear"
                    ></i>
                    <template v-if="showPwdVisible">
                        <i class="vd-ui-input__icon vd-ui_icon icon-hide"
                            v-if="!passwordVisible"
                            @click="handlePasswordVisible"
                            @mousedown.prevent
                        ></i>
                        <i class="vd-ui-input__icon vd-ui_icon icon-display"
                            v-else
                            @click="handlePasswordVisible"
                            @mousedown.prevent
                        ></i>
                    </template>
                </span>
                <span class="vd-ui-input__suffix-slot" v-if="$slots.suffix || suffixIcon">
                    <template>
                        <slot name="suffix">
                            <i class="vd-ui-input__icon vd-ui_icon"
                                v-if="suffixIcon"
                                :class="suffixIcon">
                            </i>
                        </slot>                       
                    </template>
                </span>    
            </span>
            <!-- 后置元素 -->
            <div class="vd-ui-input-group__append" v-if="$slots.append || append">
                <slot name="append"> 
                    <span>{{append}}</span>
                </slot>
            </div>
            <!-- 后面自定义内容 -->
            <div class="icon-type" v-if="$slots.hintIcon && !disabled">
                <slot name="hintIcon">
                </slot>
            </div>
            <!-- 下面文案说明 -->
            <template v-if="text">
            <p class="vd-ui-input-text">{{text}}</p>
            </template>
            <!-- 报错提示 -->
            <div class="vd-ui-input-error" v-if="errorable && !disabled">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </template>
        <template v-else>
            <div class="vd-ui-textarea-place">
                <textarea
                    ref="textarea"
                    class="vd-ui-textarea__inner"
                    v-bind="$attrs"
                    :disabled="disabled"
                    :readonly="readonly"
                    :style="textareaStyle"
                    @input="handleInput"
                    @compositionend="commentPress"

                    @focus="handleFocus"
                    @blur.trim="handleBlur"
                    @change="handleChange"
                ></textarea>
                <span
                    v-if="isWordLimitVisible && type === 'textarea'"
                    class="vd-ui-input__count"
                    :class="{
                        'upper-limit': textLength == upperLimit
                    }"
                >
                    {{ textLength }}/{{ upperLimit }}
                </span>
            </div>
            <div class="vd-ui-input-error" v-if="errorable && !disabled && errorMsg">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </template>
    </div>`,
    data() {
        return {
            focused: false,
            entered: false,
            // 密码可见
            passwordVisible: false,
            validKey: '',
            validValue: '',
            onCompressing: false
        }
    },
    props: {
        value: [String, Number],
        // 输入框尺寸 large small
        size: String,
        type: {
            type: String,
            default: 'text'
        },
        // none, both, horizontal, vertical
        // resize: String,
        disabled: {
            type: Boolean,
            default: false,
        },
        readonly: {
            type: Boolean,
            default: false,
        },
        prepend: {
            type: String,
            default: ''
        },
        append: {
            type: String,
            default: ''
        },
        // 前置图标
        prefixIcon: String,
        // 后置图标
        suffixIcon: String,
        clearable: {
            type: Boolean,
            default: false
        },
        showWordLimit: {
            type: Boolean,
            default: false
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        text: String,
        selectAll: {
            type: Boolean,
            default: false
        },
        showPassword: {
            type: Boolean,
            default: false
        },
        heightAuto: {
            type: Boolean,
            default: false
        },
        selectClear: {//select组件清除按钮判断
            type: Boolean,
            default: false
        }
    },

    computed: {
        style() {
            let style = {}
            if (this.type == 'textarea') {
                style.width = '';
            } else {
                style.width = this.$attrs.width ? this.$attrs.width : '100%'
            }
            return style

        },
        parent() {
            return this.$parent
        },
        textareaStyle() {
            let style = {}
            style.width = this.$attrs.width
            // style.height = this.$attrs.height
            !this.disabled ? style.resize = this.$attrs.resize : style.resize = 'none'
            style['min-height'] = this.$attrs['min-height']
            style['max-height'] = this.$attrs['max-height']
            style['min-width'] = this.$attrs['min-width']
            style['max-width'] = this.$attrs['max-width']
            this.errorable ? style['border-color'] = '#e64545' : ''
            return style

        },
        nativeInputValue() {
            return this.value === null || this.value === undefined ? '' : String(this.value);
        },
        showClear() {
            return this.clearable &&
                !this.disabled &&
                (this.selectClear || this.nativeInputValue) &&
                (this.entered || this.focused)
        },
        showPwdVisible() {
            return !this.disabled &&
                !this.readonly &&
                this.type == 'password' &&
                this.showPassword &&
                (this.entered || this.focused || this.nativeInputValue)
        },
        isWordLimitVisible() {
            return this.showWordLimit &&
                this.$attrs.maxlength &&
                (this.type === 'text' || this.type === 'textarea') &&
                !this.disabled &&
                !this.readonly &&
                !this.showPassword
        },
        upperLimit() {
            return this.$attrs.maxlength;
        },
        textLength() {
            if (typeof this.value === 'number') {
                return String(this.value).length;
            }

            return (this.value || '').length;
        },
        getSuffixVisible() {
            return this.$slots.suffix ||
                this.suffixIcon ||
                this.showClear ||
                this.type == 'password'
        },
        trueType() {
            if (this.passwordVisible || this.type === 'number') {
                return 'text';
            } else {
                return this.type;
            }
        }
    },
    watch: {
        nativeInputValue() {
            this.setNativeInputValue();
        },
    },
    mounted() {
        this.setNativeInputValue()
        if (this.type == 'textarea') {
            if (this.value && this.heightAuto) {
                this.getInput().style.height = this.$refs.textarea.scrollHeight + 2 + 'px' || this.$attrs.height || '';
            } else {
                this.getInput().style.height = this.$attrs.height || '';
            }
        }
        this.$form.setValidEl(this);
    },
    methods: {
        focus() {
            this.getInput().focus()
        },
        blur() {
            this.focused = false
            this.getInput().blur()
        },
        select() {
            this.getInput().select()
        },
        handleInput(event) {
            if(this.onCompressing) {
                return;
            }

            // textarea 中文输入完才计算高度
            if (this.type == 'textarea' && event.inputType == 'insertCompositionText') {
                return;
            }

            this.type == 'textarea' && this.heightAuto == true
                ? this.getInput().style.height = this.$refs.textarea.scrollHeight + 2 + 'px' : '';

            if (this.type == 'number') {
                console.log(event.target.value)
                event.target.value = event.target.value.replace(/[^\d]/g, '');
            }

            if (event.target.value === this.nativeInputValue) return;
            this.$emit('input', event.target.value)
            this.$nextTick(this.setNativeInputValue);
        },
        commentPressStart() {
            this.onCompressing = true;
        },
        commentPressEnd(e) {
            this.onCompressing = false;
            this.handleInput(e)
        },
        // 中文结束
        commentPress(event) {
            if (this.type == 'textarea' && this.heightAuto == true) {
                this.getInput().style.height = this.$refs.textarea.scrollHeight + 2 + 'px';
            }
            
            if (this.type == 'number') {
                event.target.value = event.target.value.replace(/[^\d]/g, '');
            }

            if (event.target.value === this.nativeInputValue) return;
            this.$emit('input', event.target.value)
            this.$nextTick(this.setNativeInputValue);
        },

        handleFocus(event) {
            this.focused = true
            this.selectAll ? this.getInput().select() : ''
            this.$emit('focus', event)
        },
        handleBlur(event) {
            this.cancelFocus()
            let prevValue = event.target.value;
            let trimValue = event.target.value.trim();

            if(prevValue !== trimValue) {
                event.target.value = trimValue;
                this.$emit('input', event.target.value);
            }
            
            this.$emit('blur', event);
            setTimeout(() => {
                this.checkValid();
            })
        },
        cancelFocus() {
            this.focused = false
        },
        handleChange(event) {
            this.$emit('change', event)
        },
        handleEnter() {
            this.entered = true
        },
        handleLeave() {
            this.entered = false
        },
        handlePasswordVisible() {
            this.focus();
            setTimeout(() => {
                this.passwordVisible = !this.passwordVisible
            })
        },
        // 选中赋值 阻止触发blur事件
        preventFunc(e) {
            e.preventDefault()
        },
        clear() {
            if (!this.selectClear) {
                this.focus()
            }
            this.$emit('input', '');
            this.$emit('change', '');
            this.$emit('clear');
        },
        setNativeInputValue() {
            const input = this.getInput();
            if (!input) return;
            if (input.value === this.nativeInputValue) return;
            // console.log(this.nativeInputValue)
            input.value = this.nativeInputValue;
        },
        getInput() {
            return this.$refs.input || this.$refs.textarea
        },
        checkValid() {
            if (this.validKey && this.validValue) {
                if (this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, this.value, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if (validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        },
        handleKeyup(e) {
            if(e.keyCode === 13) {
                this.$emit('enter')
            } 
        }
    },
})

Vue.component('ui-range-input', {
    template: `<div class="vd-ui-number-range">
        <ui-input v-model="min" :placeholder="placeholderMin || placeholder" @blur="checkRangeData('min')"></ui-input>
        <span class="range-gap">-</span>
        <ui-input v-model="max" :placeholder="placeholderMax || placeholder"  @blur="checkRangeData('max')"></ui-input>
    </div>`,
    props: {
        max: {
            type: String || Number,
            default: ''
        },
        min: {
            type: String || Number,
            default: ''
        },
        placeholderMin: {
            type: String,
            default: ''
        },
        placeholderMax: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: ''
        }
    },
    methods: {
        checkRangeData(type) {
            if (type === 'min') {
                if(!this.checkValue(this.min.trim())) {
                    this.min = "";
                } else if(this.max.trim() && parseFloat(this.min) > parseFloat(this.max)) {
                    this.min = this.max;
                }
                
                this.changeValueMin();
            }

            if (type === 'max') {
                if(!this.checkValue(this.max.trim())) {
                    this.max = "";
                } else if(this.min.trim() && parseFloat(this.min) > parseFloat(this.max)) {
                    this.max = this.min;
                }
                
                this.changeValueMax();
            }
        },
        checkValue(value) {
            if(!/^\d+(\.\d+)?$/.test(value)) {
                return false;
            }

            return true;
        },
        changeValueMin() {
            this.$emit('update:min', this.min.trim());
        },
        changeValueMax() {
            this.$emit('update:max', this.max.trim());
        }
    }
})

Vue.component('ui-number-input', {
    template: `<div class="vd-ui-number-input">
        <ui-button class="left" :disabled="minDisabled" @click="minus">-</ui-button>
        <ui-input type="number" @blur="handlerBlur" @focus="handlerFocus" @input="handlerInput" size="small" width="auto" v-model="number"></ui-input>
        <ui-button class="right" :disabled="maxDisabled" @click="plus">+</ui-button>
        <div class="bubble-tip-wrap top" :class="{show: isShowTip}">最大可输入{{ max }}</div>
    </div>`,
    props: {
        size: String,
        max: {
            type: Number,
            default: 99999
        },
        min: {
            type: Number,
            default: 1
        },
        value: {
            type: Number,
            default: 0,
        },
    },
    computed: {
        minDisabled() {
            return this.min >= parseInt(this.number);
        },
        maxDisabled() {
            return this.max <= parseInt(this.number);
        }
    },
    data() {
        return {
            number: 1,
            isShowTip: false,
            timeout: null
        }
    },
    watch: {
        value() {
            this.number = this.value;
        }
    },
    mounted() {
        this.number = this.value || this.min;
    },
    methods: {
        minus() {
            if (!this.minDisabled) {
                this.number = parseInt(this.number) - 1;
                this.valueChange();
            }
        },
        plus() {
            if (!this.maxDisabled) {
                this.number = parseInt(this.number) + 1;
                this.valueChange();
            }
        },
        handlerBlur() {
            let num = parseInt(this.number);

            if (num < this.min) {
                this.number = this.min;
                return;
            }

            this.number = parseInt(this.number) || this.min;

            this.valueChange();
            this.$emit('blur');
        },
        handlerFocus() {
            this.$emit('focus')
        },
        valueChange() {
            this.$emit('input', this.number);
            this.$emit('change', this.number);
        },
        handlerInput() {
            let num = parseInt(this.number);

            if (num > this.max) {
                this.number = this.max;
                this.isShowTip = true;
                this.timeout && clearTimeout(this.timeout);
                this.timeout = setTimeout(() => {
                    this.isShowTip = false;
                }, 3000)
            }

            this.$emit('input', this.number)
        }
    }
})
let messageVue = Vue.component('ui-message', {
    template: `<transition name='globalToast'>
        <div class='ui-global-toast' :class="'ui-message-' + type" v-if='isShow'>
            <i class='vd-ui_icon' :class='icon'></i>
            <span>{{message}}</span>
        </div>
    </transition>`,
    data() {
        return {
            isShow: false,
            type: 'success',
            message: '暂无提示弹框类型',
            duration: 3000,
            iconType: {
                'success': 'icon-yes2',
                'error': 'icon-error2',
                'warn': 'icon-caution2',
                'info': 'icon-info2'
            },
        }
    },
    computed: {
        icon(){
            return this.iconType[this.type];
        }
    },
    methods: {
        show() {
            this.isShow = true;
        },
        hide() {
            this.isShow = false;
        }
    }
})

let installMessageComponents = () => {
    const VdMsg = Vue.extend(messageVue);
        let vm = null;
        
        const showMessage = ({message, type, duration, callback}) => {
            //console.log(type,opt);
            return new Promise((resolve, reject) => {
                if(!vm){
                    vm = new VdMsg();
                    vm.$mount();
                    document.body.appendChild(vm.$el);
                }else{
                    vm.hide();
                    clearTimeout(showMessage.hideTimer,showMessage.showTimer);
                }
                
                vm.message = message || vm.message;
                vm.type = type || vm.type;
                vm.duration = duration || vm.duration;
    
                showMessage.showTimer = setTimeout(() => {
                    vm.show();
                });
                
                showMessage.hideTimer = setTimeout(() => {
                    vm.hide();
                    clearTimeout(showMessage.hideTimer,showMessage.showTimer);
                    callback && callback();
                    resolve();
                },vm.duration);
            });
        }
        
        const fn = (type,message,duration,callback) => {
            return Promise.resolve(showMessage({
                type,
                message,
                duration,
                callback
            }));
        };
    
        // const fn = (type,opt) => {
        //     return Promise.resolve(showMessage(type,opt));
        // };
        
        showMessage.success = fn.bind(null,'success');
        showMessage.error = fn.bind(null,'error')
        showMessage.warn = fn.bind(null,'warn')
        showMessage.info = fn.bind(null,'info')
    
        Vue.prototype.$message = showMessage;
}

installMessageComponents();
Vue.component('ui-more-contact', {
    template: `
        <div class="more-contact-component">
            <div class="contact-list">
                <div class="contact-item" v-for="(item, index) in list" :key="item.id + '' + index">
                    <ui-form-item :label="item.label" style="margin-bottom: 20px;">
                        <ui-input 
                            width="323px" maxlength="50"
                            v-model="item.value"
                            @input="onInput"
                        ></ui-input>
                    </ui-form-item>
                    <div class="delete" @click="handlerDel(index)">
                        <i class="vd-ui_icon icon-delete"></i>
                    </div>
                </div>
            </div>

            <div class="add-contact" v-if="list.length < size">
                <a @click="increase">+ 添加其他联系方式（{{list.length}}/{{size}}）</a>
            </div>

            <ui-dialog
                :visible.sync="rotate"
                title="添加联系方式"
                width="720px"
            >
                <div v-if="rotate" class="form-wrap label-width-2">
                    <ui-form-item label="联系方式" :must="true" :text="true">
                        <div class="ui-col-8">
                            <ui-radio-group
                                :list="Radios"
                                :value.sync="radioVal"
                            ></ui-radio-group>
                        </div>
                        <div v-if="radioVal == 3" class="other">
                            <ui-input 
                                width="170px" 
                                maxlength="7"
                                v-model="inputKey"
                                :placeholder="placeholder"
                                :errorable="Boolean(inputError)" 
                                :error-msg="inputError" 
                                @blur="onBlur"
                            ></ui-input>
                            <p class="tips">- 建议名称设置简约、直接、准确，最多可输入7个字</p>
                        </div>
                    </ui-form-item>
                </div>
                <template slot="footer">
                    <div class="dlg-form-footer">
                        <ui-button @click="confirm" type="primary">确定</ui-button>
                        <ui-button @click="rotate=false" class="close">取消</ui-button>
                    </div>
                </template>
            </ui-dialog>
        </div>
    `,
    props: {
        value: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
        size: {
            type: Number,
            default: 5
        },
        placeholder: {
            type: String
        }
    },
    data () {
        return {
            rotate: false,
            list: [],
            Radios: [
                { label: "微信", value: 1 },
                { label: "QQ", value: 2 },
                { label: "其他联系方式", value: 3 },
            ],
            radioVal: '', // 选项值
            inputKey: '',
            inputError: '',
            Labels: {
                1: '微信',
                2: 'QQ',
                3: '其他'
            }
        }
    },
    watch: {
        rotate (newV) {
            if (!newV) {
                this.radioVal = '';
                this.inputKey = '';
            }
        },
    },
    computed: {},
    mounted () {
        if (this.value) {
            this.initList();
        }
    },
    methods: {
        // 初始化
        initList() {
            let vals = this.value.split('##');
            let arr = [];
            vals.forEach(item => {
                let str = item.split(':');
                arr.push({
                    label: str[0],
                    value: str[1],
                    id: Math.random()
                })
            })
            this.list = arr;
        },

        increase () {
            if (this.list.length < this.size) {
                this.inputKey = ''
                this.inputError = ''
                this.rotate = true;
            }
        },
        // 新加
        add () {
            let label = this.Labels[this.radioVal];
            this.radioVal == 3 && (label = this.inputKey.trim());
            this.list.push({
                label: label,
                value: '',
                id: Math.random()
            })

            this.rotate = false;
        },
        // 删除
        handlerDel(index) {
            console.log('index:::', index);
            this.list.splice(index, 1);
            this.pick();
        },
        onInput () {
            this.pick();
        },
        onBlur () {
            if (this.inputKey.trim()) {
                this.inputError = '';
            }
        },
        // 保存
        confirm () {
            if (!this.radioVal) return;
            if (this.radioVal == 3) {
                if (!this.inputKey.trim()) {
                    this.inputError = '请输入联系方式名称';
                    return;
                } else {
                    this.inputError = '';
                    this.add(this.radioVal);
                }
            } else {
                this.add();
            }
        },
        pick () {
            let arr = [];
            this.list.forEach(item => {
                if (item.value.trim()) {
                    arr.push(`${item.label}:${item.value.trim()}`);
                }
            });
            let str = arr.join('##');
            this.$emit("input", str); // 修改外层v-model值
            this.$emit('change', arr)
        },
    }
})
Vue.component('ui-card-switch', {
    template: `<div class="ui-card-switch-wrap">
       <div class="ui-card-swich-item line-1" title="一列布局" :class="{active: line == 1}" @click="setLine(1)"></div>
       <div class="ui-card-swich-item line-2" title="两列布局" :class="{active: line == 2}" @click="setLine(2)"></div>
    </div>`,
    props: {
        //页面唯一标识，根据这个标识进行当前页面样式切换的缓存存取。
        name: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            line: 1
        }
    },
    created() {
        this.getCardLine();
    },
    mounted(){
       
    },
    methods: {
        getCardLine() {
            let cardLineData = localStorage.getItem('card_line_data');

            if(cardLineData) {
                let lineObj = JSON.parse(cardLineData);

                if(lineObj[this.name]) {
                    this.line = lineObj[this.name];
                }
            }

            this.$emit('input', this.line);
        },
        setLine(num) {
            if(num == this.line) {
                return;
            }

            let lineObj = {};
            let cardLineData = localStorage.getItem('card_line_data');

            if(cardLineData) {
                lineObj = JSON.parse(cardLineData);
            }

            lineObj[this.name] = num;

            localStorage.setItem('card_line_data', JSON.stringify(lineObj));
            this.line = num;

            this.$emit('input', this.line);
        }
    }
})
Vue.component('ui-pagination', {
    template: `<div class="vd-ui-page" v-if="totalCount > 1">
        <div
            class="vd-ui-page-total-txt"
            :class="{ 'vd-ui-page-total-small': size === 'small' }"
            v-if="type==='combine'"
        >
            共{{ total }}{{ totalTxt }}
        </div>
        <div
            class="vd-ui-page-list"
            :class="{ 'vd-ui-page-small': size === 'small' }"
        >
            <a
                href="javascript:void(0)"
                class="vd-ui-page-item vd-ui-page-prev"
                :class="{
                    'vd-ui-page-disabled': current === 1,
                    'vd-ui-page-item-wider': prevTxt,
                }"
                :title="prevTxt"
                @click="changeCurrentPage(current - 1)"
                rel="nofollow"
                ><i class="vd-ui_icon icon-app-left"></i>
                <template v-if="prevTxt">
                    {{ prevTxt }}
                </template>
            </a>

            <template v-if="start > 0">
                <a
                    href="javascript:void(0)"
                    class="vd-ui-page-item"
                    :class="{ 'vd-ui-page-active': current === 1 }"
                    rel="nofollow"
                    @click="changeCurrentPage(1)"
                    >1</a
                >
            </template>

            <template v-if="start > 1">
                <strong class="vd-ui-page-omit">...</strong>
            </template>

            <template v-for="i in end - start">
                <template v-if="i + start === current">
                    <a
                        href="javascript:void(0)"
                        rel="nofollow"
                        class="vd-ui-page-item vd-ui-page-active"
                        :class="{ 'vd-ui-page-item-wider': current > 9 }"
                        >{{ current }}</a
                    >
                </template>
                <template v-else>
                    <a
                        href="javascript:void(0)"
                        rel="nofollow"
                        class="vd-ui-page-item"
                        :class="{ 'vd-ui-page-item-wider': i + start > 9 }"
                        @click="changeCurrentPage(i + start)"
                        >{{ i + start }}</a
                    >
                </template>
            </template>

            <template v-if="end < totalCount - 1">
                <strong class="vd-ui-page-omit">...</strong>
            </template>

            <template
                v-if="
                    (end < totalCount && needEnd) ||
                    (!needEnd && end === totalCount - 1)
                "
            >
                <a
                    href="javascript:void(0)"
                    rel="nofollow"
                    class="vd-ui-page-item"
                    :class="[
                        { 'vd-ui-page-active': current === totalCount },
                        { 'vd-ui-page-item-wider': totalCount > 9 },
                    ]"
                    @click="changeCurrentPage(totalCount)"
                    >{{ totalCount }}</a
                >
            </template>

            <a
                href="javascript:void(0)"
                class="vd-ui-page-item vd-ui-page-next"
                :class="{
                    'vd-ui-page-disabled': current === totalCount,
                    'vd-ui-page-item-wider': nextTxt,
                }"
                :title="nextTxt"
                @click="changeCurrentPage(current + 1)"
                rel="nofollow"
            >
                <template>
                    {{ nextTxt }}
                </template>
                <i class="vd-ui_icon icon-app-right"></i
            ></a>
        </div>
        <div class="vd-ui-page-jump" v-if="jump">
            <ui-input v-model="jumpNum" type="number" placeholder="输入页码"></ui-input>
            <ui-button @click="pageJump">跳转</ui-button>  
        </div>
    </div>`,
    data() {
        return {
            start: 0,
            end: 0,
            totalCount: 0,
            current: 1,
            jumpNum: ''
        };
    },
    props: {
        currentPage: {
            type: Number,
            default: 1,
        },
        prevTxt: {
            type: String,
            default: "",
        },
        nextTxt: {
            type: String,
            default: "下一页",
        },
        total: {
            type: Number,
            default: 0,
        },
        pageSize: {
            type: Number,
            default: 10,
        },
        pageCount: {
            type: Number,
            default: 7,
        },
        size: {
            type: String,
            default: "", //可传 small
        },
        type: {
            type: String,
            default: "", //可传 combine
        },
        needEnd: {
            type: Boolean,
            default: true,
        },
        totalTxt: {
            type: String,
            default: "条结果",
        },
        jump: {
            type: Boolean,
            default: false,
        }
    },
    watch: {
        currentPage() {
            this.init();
        },
        total() {
            this.init();
        },
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            this.totalCount = Math.ceil(this.total / this.pageSize);
            this.current = this.currentPage;

            if(this.totalCount > 1) {
                this.computedPages();
            }
        },
        computedPages: function () {
            let start = this.current - 1 - Math.floor(this.pageCount / 2);
            start = start < 1 ? 1 : start;

            let end = start + this.pageCount;
            end = end > this.totalCount - 1 ? this.totalCount - 1 : end;

            start = end - this.pageCount;
            start = start < 1 ? 1 : start;

            this.start = start;
            this.end = end;
        },
        changPage(index) {
            if(index > this.totalCount) {
                index = this.totalCount;
            }
            this.current = index;
            this.jumpNum = "";
            this.computedPages();
            this.$emit("update:currentPage", index);
            this.$emit("change", index);
        },
        changeCurrentPage(index) {
            index = parseInt(index);
            if (
                index < 1 ||
                index > this.totalCount ||
                index === this.current
            ) {
                return false;
            }

            this.changPage(index);
        },
        pageJump() {
            if (!this.jumpNum.trim()) {
                return;
            }

            this.changPage(parseInt(this.jumpNum));
        }
    },
})
// 手机联想 联系人
Vue.component('ui-phone-related', {
    template: `
        <div class="vd-ui-phone-related">
            <div class="vd-ui-input" ref="input-wrap" :style="{'width': width}">
                <ui-input
                    type="number"
                    :maxlength="maxlength"
                    :placeholder="placeholder"
                    v-bind="$attrs"
                    v-model="inputValue"
                    @blur="handlerBlur"
                    @input.native="handleInput"
                    width="100%"
                    autocomplete="off"
                    :errorable="!!errorMsg" 
                    :error-msg="errorMsg" 
                ></ui-input>
            </div>

            <transition>
                <ul
                    class="vd-ui-search-related"
                    @click.stop
                    v-show="rotate"
                    :style="{'width': width}"
                >
                    <template v-if="loading">
                        <li class="loading">
                            <i class="vd-ui_icon icon-loading" ref="loading"></i>
                            <span>加载中...</span>
                        </li>
                    </template>

                    <template v-else-if="loadingFail">
                        <li class="failed-li">
                            <i class="vd-ui_icon icon-error2"></i>
                            <span>加载失败</span>
                            <span class="reload" @click="handleReload">重新加载</span>
                        </li>
                    </template>

                    <template v-else>
                        <div class="search-list" v-if="relateList.length">
                            <div 
                                class="sr-item" 
                                v-for="(item, index) in relateList" :key="index"
                                @click.stop="chooseErp(item)"
                            >
                                <div class="name text-line-1">{{item.name}}</div>
                                <div class="mobile" v-html="light(item.mobile)"></div>
                            </div>
                        </div>
                        <li class="empty-li" v-else>
                            <p>暂无数据</p>
                        </li>
                    </template>
                </ul>
            </transition>
        </div>
    `,

    props: {
        value: {
            type: String,
            default: ''
        },
        maxlength: {
            type: Number,
            default: 11
        },
        // 已建档客户id： 有id才联想，否则只做简单输入
        traderId: {
            type: [String, Number],
            default: ''
        },

        placeholder: {
            type: String,
            default: '手机：仅支持11位手机号'
        },
        width: {
            type: String,
            default: '300px'
        },
        errorMsg: {
            type: String
        },
        // 是否精确匹配:  true精确 [输入满11位才查询]  false非精确
        accurateMatch: {
            type: Boolean,
            default: true,
        }
    },
    data() {
        return {
            inputValue: '',
            // erp本地匹配
            rotate: false,
            relateList: [], // 搜索联想
            loading: false,
            loadingFail: false,
            timer: null,
        }
    },
    watch: {
        value (newV) {
            this.inputValue = newV;
        },
        rotate (newV) {
            if (!newV) {
                this.loading = false;
                this.loadingFail = false;
            }
        }
    },
    computed: {
        light () {
            return (name) => {
                if (!this.inputValue) return name;
                const regExp = new RegExp(this.inputValue, 'g');
                name = name.replace(regExp, `<font color='#FF6600'">${this.inputValue}</font>`);
                return name;
            }
        },
        tags () {
            let arr = this.companyInfo.tags.split(';') || [];
            return arr
        },
        category_Big_Middle_Small () {
            let arr = [];
            this.companyInfo.category && arr.push(this.companyInfo.category); 
            this.companyInfo.categoryBig && arr.push(this.companyInfo.categoryBig); 
            this.companyInfo.categoryMiddle && arr.push(this.companyInfo.categoryMiddle); 
            this.companyInfo.categorySmall && arr.push(this.companyInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    mounted() {
        this.inputValue = this.value;
        // document.addEventListener('click', (e)=>{
        //     if (!this.$el.contains(e.target)) {
        //         console.log('rotate:', this.rotate);
        //         this.rotate = false;
        //     }
        // })

        let _this = this;
        document.addEventListener('click', (e) => {
            let inputWrap1 = _this.$refs['input-wrap']; // input
            let panelWrap = _this.$refs['pickers']; // 日期panel层
            let include1 = false; // input 是否包含点击元素
            let include2 = false; // 面板是否包含点击元素

            if (inputWrap1 && inputWrap1.contains(e.target)) {
                include1 = true;
            }
            if (panelWrap && panelWrap.contains(e.target)) {
                include2 = true;
            }

            if (_this.rotate && !include1 && !include2) {
                _this.rotate = false;
            }
        }, true); // true: 从外向内 由根节点向点击元素执行
    },
    methods: {
        handlerBlur () {
            this.$emit('blur');
        },
        // 输入停顿300毫秒后搜素
        handleInput (event) {
            // if (event.inputType != 'insertCompositionText') {
                let val = event.target.value.trim();
                this.$emit('input', val);
                this.$emit('change', {});

                this.timer && clearTimeout(this.timer);
                if (this.traderId && val) {
                    this.timer = setTimeout(()=> {
                        this.getRelatelist(val);
                    }, 300)
                }
            // }
        },

        getRelatelist (val) {
            this.rotate = true;
            this.loading = true;
            this.loadingFail = false;

            let query = `?traderId=${this.traderId}&mobile=${val}&pageSize=100&accurateMatch=${this.accurateMatch? 1: 0}`;
            axios.post(`/crm/traderContact/profile/page${query}`).then(({data}) => {
            // axios.post(`/crm/traderContact/profile/page?traderId=${this.traderId}&mobile=${val}&pageSize=100`, {
            //     headers: {
            //         timeStramp: new Date().getTime()
            //     }
            // }).then(({data}) => {
                this.loading = false;
                console.log('data:', data);
                if (data.success) {
                    this.relateList = data.data.list || [];
                } else {
                    this.loadingFail = true;
                }
            }).catch(err=> {
                this.loading = false;
                this.loadingFail = true;
            })
        },

        // 重新加载
        handleReload () {
            this.getRelatelist(this.inputValue);
        },

        // 选择已建档客户
        async chooseErp (item) {
            console.log('item：', item);
            this.inputValue = item.mobile || '';
            this.rotate = false;

            this.$emit("input", item.mobile || '');
            this.$emit('change', {
                traderId: item.traderId,
                mobile: item.mobile,
                traderContactId: item.traderContactId,
                traderContactName: item.name,
                choosed: true
            });
        },
    }
})
Vue.component('ui-poper', {
    template: `<div class="ui-poper-wrap" ref="popwrap" :class="{hidden: !show}" :style="stylePosition">
        <slot></slot>
    </div>`,
    props: {
        show: {
            type: Boolean,
            default: false
        },
        errorable: {
            type: Boolean,
            default: false
        },
        position: {
            type: String,
            default: ''
        },
        el: {
            type: Object,
            default() {
                return null
            }
        },
        autoWidth: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            dropPosition: {},
            parent: null
        }
    },
    watch: {
        show() {
            if (this.show) {
                document.body.append(this.$refs.popwrap);
                setTimeout(() => {
                    this.calcPosition();
                }, 0)
            } else {
                this.$refs.popwrap.remove();
            }
        },
        position() {
            setTimeout(() => {
                this.calcPosition();
            }, 0)
        }
    },
    computed: {
        stylePosition() {
            let dropPosition = this.dropPosition;
            return 'width:' + dropPosition.width + ';left:' +  (dropPosition.left ? dropPosition.left + 'px' : 'auto') + ';right:' +  (dropPosition.right ? dropPosition.right + 'px;' : 'auto;') +  'top:' +  dropPosition.top + 'px;z-index:' + dropPosition.zindex
        }
    },
    mounted() {
        let parent = this.getScrollParent(this.$parent.$el);
        
        if(parent) {
            parent.addEventListener('scroll', this.calcPosition);
        }
        window.addEventListener('scroll', this.calcPosition);
        window.addEventListener('resize', this.calcPosition);

        this.parent = parent;
    },
    methods: {
        calcPosition() {
            if(!this.show) {
                return;
            }

            let el = this.el ||  this.$parent.$el;
            let elPosition = el.getBoundingClientRect();

            let top = elPosition.top + elPosition.height;
            if(this.position === 'top') {
                top = elPosition.top
            }
            console.log(this.errorable)
            if(this.errorable) {
                top = top - 26;
            }
            console.log(elPosition.top, this.$refs.popwrap.offsetHeight, document.body.scrollHeight)
            if(elPosition.top + this.$refs.popwrap.offsetHeight > window.innerHeight) {
                top =  elPosition.top - this.$refs.popwrap.offsetHeight
            }
            
            let width = this.listWidth || (el.offsetWidth + 'px');

            if(this.position === 'middle') {
                let poperWidth = this.$refs.popwrap.offsetWidth;
                let elWidth = el.offsetWidth
                width = (poperWidth > elWidth + 82 ? poperWidth : elWidth + 82) + 'px';
                top =  elPosition.top;
            }

            if(this.autoWidth) {
                width = "auto"
            }

            if(window.innerWidth - elPosition.left < this.$refs.popwrap.offsetWidth + 10) {
                this.dropPosition = {
                    width: width,
                    top: top,
                    left: elPosition.left + elPosition.width - this.$refs.popwrap.offsetWidth,
                    zindex: this.parent === window ? 20 : 3000
                }
            } else {
                this.dropPosition = {
                    width: width,
                    top: top,
                    left: elPosition.left,
                    zindex: this.parent === window ? 20 : 3000
                }
            }

            
        },
        getScrollParent(element) {
            if (!element) {
                return null;
            }

            const overflowRegex = /(scroll|auto)/;
            const parent = element.parentElement;

            if (parent && overflowRegex.test(window.getComputedStyle(parent).overflow + window.getComputedStyle(parent).overflowY + window.getComputedStyle(parent).overflowX)) {
                return parent;
            }

            return this.getScrollParent(parent);
        }
    }
})
let uiPopupVue = Vue.component('ui-popup', {
    template: `
    <transition name="popup-fade">
        <div class="ui-popup-message-box-wrapper" v-show="isShow">
            <transition name="popup-move">
                <div class="ui-popup-message-box" v-show="isShow">
                    <div class="msg-title">
                        <div class="msg-fork" @click="deleteButton">
                            <i class="vd-ui_icon icon-delete"></i>
                        </div>
                    </div>
                    <div class="msg-content">
                        <i class="vd-ui_icon"
                           :class='icon'
                        ></i>
                        <div class="msg-tip">
                            <div class="msg-tip-title" v-show='title'>{{title}}</div>
                            <div class="msg-tip-word" v-show='message' v-html="message"></div>
                        </div>
                    </div>
                    <div class="msg-button-choice">
                        <div
                            class='vd-button'
                            v-for='(item, index) in buttons'
                            :class='item.btnClass'
                            @click="handleButton(item)"
                            :key="index"
                        >{{item.txt}}</div>
                    </div>
                </div>
            </transition>
        </div>
    </transition>`,
    data() {
        return {
            isShow: false,
            type: '',
            title: '',
            message: '',
            buttons: [],
            defaultClass: '',  // 按钮默认class
            iconType: {
                'success': 'icon-yes2',
                'error': 'icon-error2',
                'warn': 'icon-caution2',
                'info': 'icon-info2'
            }
        }
    },
    computed: {
        icon() {
            return this.iconType[this.type];
        }
    },
    methods: {
        show() {
            this.isShow = true;
        },
        hide() {
            this.isShow = false;
        },
        handleButton(item) {
            this.isShow = false;
            if (item.callback) {
                item.callback();
            }
        },
        deleteButton() {
            this.isShow = false;
            this.handleDelete && this.handleDelete();
        }
    }
})

let installPopupComponents = () => {
    const VdPop = Vue.extend(uiPopupVue);
    let vm = null;

    const showPopup = (type, opt) => {
        return new Promise((resolve, reject) => {
            if (!vm) {
                vm = new VdPop();
                vm.$mount();
                document.body.appendChild(vm.$el);
            } else {
                vm.hide();
            }

            vm.type = type || vm.type;
            vm.title = opt.title || '';
            vm.message = opt.message || '';
            vm.buttons = opt.buttons || vm.buttons;
            vm.handleDelete = opt.handleDelete || vm.handleDelete;
            vm.defaultClass = opt.defaultClass || vm.defaultClass;

            vm.show();
        });
    }

    const fn = (type, opt) => {
        return Promise.resolve(showPopup(type, opt));
    };

    showPopup.success = fn.bind(null, 'success');
    showPopup.error = fn.bind(null, 'error');
    showPopup.warn = fn.bind(null, 'warn');
    showPopup.info = fn.bind(null, 'info');

    Vue.prototype.$popup = showPopup;
}

installPopupComponents();

Vue.component('ui-radio-group', {
    template: `
        <div class="vd-ui-radio-group">
            <template v-for="(item, index) in boxList">
                <ui-radio
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :tip="item.tip"
                    :checked.sync="item.checked"
                    :disabled="disabled || item.disabled"
                    :clearable="clearable"
                    @change="handlerChange(index, $event)"
                ></ui-radio>
            </template>
            <div class="vd-ui-input-error" v-if="errorable">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,

    watch: {
        list: {
            handler () {
                this.setList();
            },
            deep: true,
            immediate: true
        },
        value (newV) {
            this.setList();
        },
    },

    data() {
        return {
            boxList: [],
        };
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        value: "",
        clearable: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
    },
    mounted() {
        this.$form.setValidEl(this);
        this.setList();
    },
    methods: {
        setList() {
            this.boxList = JSON.parse(JSON.stringify(this.list));

            this.boxList.forEach((item) => {
                if (item.value == this.value) {
                    item.checked = true;
                }
            });
        },
        handlerChange(index, data) {
            let valueItem = null;
            this.boxList.forEach((item, i) => {
                if(index === i) {
                    this.$set(this.boxList[i], "checked", data);
                } else {
                    this.$set(this.boxList[i], "checked", false);
                }
                if (item.checked) {
                    valueItem = item;
                }
            });

            let value = valueItem ? valueItem.value : '';

            if (value !== this.value) {
                this.checkValid(value);
                this.$emit("update:value", value);
                this.$emit("change", valueItem);
            }
        },
        checkValid(newValue) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    }
})


Vue.component('ui-radio', {
    template: `
        <div
            class="vd-ui-radio-item"
            :class="{
                'vd-ui-radio-item-checked': currentChecked,
                'vd-ui-radio-item-disabled': disabled
            }"
            @click="handlerClick()"
        >
            <div class="vd-ui-radio-inner">
                <div class="vd-ui-radio-icon">
                    <div class="vd-ui-radio-icon-selected"></div>
                </div>
                <span class="vd-ui-raiod-label">{{ label }}</span>
                <span class="vd-ui-raiod-tip">{{ tip }}</span>
            </div>
        </div>
    `,

    props: {
        label: {
            type: String,
            default: "",
        },
        value: {
            type: String,
        },
        tip: {
            type: String,
        },
        checked: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        clearable: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            currentChecked: false,
        };
    },
    mounted() {
        this.currentChecked = this.checked;
    },
    watch: {
        checked() {
            this.currentChecked = this.checked;
        },
    },
    methods: {
        handlerClick() {
            if (!this.disabled && (this.clearable || !this.currentChecked)) {
                this.currentChecked = !this.currentChecked;
                this.$emit("update:checked", this.currentChecked);
                this.$emit("change", this.currentChecked);
            }
        },
    },
})

Vue.component('ui-search-item', {
    template: `<div class="search-item" :class="{hidden: !isShow}" v-show="settingShow">
        <div class="search-label">{{label}}：</div>
        <div class="search-content">
            <slot></slot>
        </div>
    </div>`,
    name: 'ui-search-item',
    props: {
        label: {
            type: String,
            default: '标签'
        },
        lock: {
            type: Boolean,
            default: false
        },
        isShow: {
            type: Boolean,
            default: true
        },
        settingShow: {
            type: Boolean,
            default: true
        }
    }
})

Vue.component('ui-search-container', {
    template: `<div class="ui-list-wrap">
        <div class="vd-ui-search-wrap no-left">
            <div class="vd-ui-search-tab" v-if="tabList && tabList.length">
                <ui-tab :active="currentTabId || tabList[0].id" ref="searchTab" :tabList="tabList" @customOption="customOption" @change="handlerTabChange"></ui-tab>
            </div>
            <div class="vd-ui-search-container" :class="{hidden: isCalcingSearch}">
                <div class="vd-ui-search-list" ref="searchList" :style="'max-height:' + maxHeight + 'px;'" :class="{'show-line': isShowLine}">
                    <template v-if="!isLoadingSearch">
                        <slot name="filter-list"></slot>
                    </template>
                </div>
                <div class="vd-ui-search-btns">
                    <div class="search-btn-inner">
                        <ui-button type="primary" @click="listSearch">搜索</ui-button>
                        <ui-button @click="handlerReset">重置</ui-button>
                        <div class="vd-ui-search-toggle" @click="toggleSearchShow" v-if="needToggle">
                            <template v-if="!isShowLine">
                                展开全部<i class="vd-ui_icon icon-down"></i>
                            </template>
                            <template v-else>
                                收起<i class="vd-ui_icon icon-up"></i>
                            </template>
                        </div>
                        <div class="vd-ui-search-option">
                            <div class="search-option-item" @click="addToCustom" v-if="needCustom">
                                <i class="vd-ui_icon icon-add"></i>添加为自定义筛选
                            </div> 
                            <div class="search-option-item item-setting" @click="showSetting('QUERY')" v-if="needSetting">
                                <i class="vd-ui_icon icon-setting"></i>设置
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="vd-ui-list-container" v-if="!isFirst">
            <div class="vd-ui-list-inner" :class="{'no-border-top': !listOption}">
                <div class="list-container-top" v-if="listOption">
                    <div class="list-container-top-buttons">
                        <slot name="list-button"></slot>
                    </div>
                    <div class="list-select-num" v-if="listSelectData && listSelectData.length">已选{{ listSelectData.length }}项</div>
                </div>
                <div class="list-container-content">
                    <div class="list-container-setting" @click="showSetting('RESULT')" v-if="needSetting">
                        <i class="vd-ui_icon icon-setting"></i>
                    </div>
                    <div class="list-container-table">
                        <ui-table ref="listTable" :oneline="true" @tableItemEdit="tableItemEdit" @tablescroll="tableScroll" :headers="showHeaders" :list="list" :right-fixed="true" :can-choose="canChoose" @selectchange="handlerListSelect" @handlersort="listSort" :left-fixed-number="canChoose ? leftFixedNumber + 1 : leftFixedNumber" :fixed-top="fixedTop" @call="handlerCallNumber" :listSort="isListSort" :editValid="editValid" :editValidMsg="editValidMsg">
                            <template v-for="(slot, name) in $scopedSlots" :keys="name" #[name]="{ row }">
                                <slot :name="name" :row="row" v-if="name !== 'list-button' && name !== 'filter-list'"></slot>
                            </template>
                        </ui-table>
                    </div>
                    <div class="list-container-pager">
                        <div class="list-container-total">共 {{totalCount}} 条</div>
                        <ui-pagination
                            :total="totalCount"
                            :pageSize="pageSize"
                            :currentPage="pageNo"
                            :jump="true"
                            v-if="!refreshPager"
                            @change="handlerPageChange"
                        ></ui-pagination>
                        <ui-select
                            :data="pageOptionList"
                            v-model="pageSize"
                            shadows
                            width="108px"
                            @change="handlerPagesizeChange"
                        ></ui-select>
                    </div>
                </div>
            </div>
        </div>
        <ui-dialog
            :visible.sync="isShowSettingDialog"
            :title="settingDialogTitle"
            width="960px"
        >
            <div class="search-setting-dlg">
                <div class="search-setting-top">
                    <div class="setting-select-all">
                        <ui-checkbox
                            :checked.sync="isSettingSelectAll"
                            label="全选"
                            @change="handlerSettingAllChange"
                        ></ui-checkbox>
                    </div>
                     <div class="setting-refresh" @click="resetSettingDialog">
                         <i class="vd-ui_icon icon-rotate"></i>恢复系统默认
                     </div>
                </div>
                <draggable v-model="tempSettingList" class="dlg-setting-list" ghost-class="placehodler" v-if="isShowSettingDialog" @sort="handlerSettingSort">
                    <div class="dlg-setting-item" v-for="(item, index) in tempSettingList" :key="index">
                        <ui-checkbox
                            :checked.sync="item.isShow"
                            :disabled="item.disabled"
                            :label="item.label"
                            @change="checkSettingSelectAll"
                        ></ui-checkbox>
                    </div>
                </draggable>
                <!--<div class="dlg-setting-list sortable" v-if="settingType === 'QUERY'">
                    <div class="dlg-setting-item" v-for="(item, index) in tempSettingList" :key="index">
                        <ui-checkbox
                            :checked.sync="item.isShow"
                            :disabled="item.disabled"
                            :label="item.label"
                            @change="checkSettingSelectAll"
                        ></ui-checkbox>
                    </div>
                </div>-->
            </div>
            <template slot="footer">
                <ui-button @click="handlerSettingChange" type="primary">确定</ui-button>
                <ui-button @click="isShowSettingDialog=false" class="close">取消</ui-button>
            </template>
        </ui-dialog>
        <ui-dialog
            :visible.sync="isShowCustomDialog"
            title="自定义筛选"
            width="720px"
        >
            <div class="form-wrap label-width-2" v-if="isShowCustomDialog">
                <ui-form-item label="标题名称" :must="true">
                    <div class="ui-col-4">
                        <ui-input v-model="customName" maxlength="7" valid="customSearchForm_customName"></ui-input>
                    </div>
                    <div class="form-tip">- 建议标题设置简约、直接、准确，最多可输入7个字</div>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer">
                    <ui-button @click="saveCustomSearch" type="primary">保存</ui-button>
                    <ui-button @click="isShowCustomDialog=false" class="close">取消</ui-button>
                </div>
            </template>
        </ui-dialog>
    </div>`,
    props: {
        line: {
            type: Number,
            default: 2
        },
        listName: {
            type: String,
            default: ''
        },
        needCustom: {
            type: Boolean,
            default: true
        },
        needSetting: {
            type: Boolean,
            default: true
        },
        defaultTab: {
            type: Array,
            default() {
                return []
            }
        },
        list: {
            type: Array,
            default: () => {
                return [];
            },
        },
        headers: {
            type: Array,
            default: () => {
                return [];
            },
        },
        url: {
            type: String,
            default: ''
        },
        leftFixedNumber: {
            type: Number,
            default: 0
        },
        searchParams: {
            type: Object,
            default: () => {
                return {};
            },
        },
        defaultSearchParams: {
            type: Object,
            default: () => {
                return {};
            },
        },
        isListSort: {
            type: Boolean,
            default: false
        },
        canChoose: {
            type: Boolean,
            default: true
        },
        listOption: {
            type: Boolean,
            default: true
        },
        currentTabId: {
            type: String,
            default: ''
        },
        editValid: {
            type: Function,
            default: null
        },
        editValidMsg: {
            type: String,
            default: ''
        },
        customReset: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            maxHeight: '',
            isShowLine: false,
            needToggle: false,
            settingList: {}, //筛选设置
            tableSettingList: {},
            isShowSettingDialog: false,
            isSettingSelectAll: true,
            tempSettingList: {},
            isShowCustomDialog: false,
            customName: '',
            tabList: [],
            settingDialogTitle: '',
            settingType: '', //QUERY 查询条件 RESULT查询结果
            showHeaders: [],
            pageOptionList: [
                { label: '50条/页', value: '50' },
                { label: '100条/页', value: '100' },
                { label: '200条/页', value: '200' }
            ],
            pageSize: 50,
            listSelectData: [],
            fixedTop: 0, //表头悬浮高度，根据有没有头部判断
            pageNo: 1,
            totalCount: 0,
            prevSearchParams: {}, //搜索参数
            isFirst: true, //第一次请求不加载列表，防止默认出现空列表
            refreshPager: false, //更新分页器,
            conditionList: [],
            isLoadingSearch: false,
            isCalcingSearch: false,
            customEditId: '',
            querySettingId: '',
            resultSettingId: '',
            sortValue: ''
        }
    },
    computed: {

    },
    created() {
        this.maxHeight = this.line * 43;
        this.showHeaders = JSON.parse(JSON.stringify(this.headers));
    },
    mounted() {
        window.addEventListener('resize', this.checkNeedToggle)
        this.initTab();
        if(this.needSetting) {
            this.getSettingList();
        } else {
            this.$nextTick(() => {
                this.checkNeedToggle();
            })
        }
        this.listSearch();
    },
    methods: {
        initTab() {
            this.tabList = this.defaultTab.concat([])
        },
        toggleSearchShow() {
            this.isShowLine = !this.isShowLine;
            this.checkSearchItemStatus();

            this.$nextTick(() => {
                let event = new Event('scroll');
                window.dispatchEvent(event);
            })
        },
        checkNeedToggle() {
            let scrollH = this.$refs.searchList.scrollHeight;

            if (scrollH > this.maxHeight + 10) {
                this.needToggle = true;
            } else {
                this.isShowLine = false;
                this.needToggle = false;
            }

            this.checkSearchItemStatus();
        },
        checkSearchItemStatus() {
            this.$children.forEach(item => {
                if (item.$options.name == 'ui-search-item') {
                    if (item.$el.offsetTop > 43 * (this.line - 1) + 10 && !this.isShowLine) {
                        item.isShow = false;
                    } else {
                        item.isShow = true
                    }
                }
            })

            this.$nextTick(() => {
                this.isCalcingSearch = false;
            })
        },
        getSettingList() {
            this.$children.forEach((searchItem, index) => {
                if (searchItem.$options.name === 'ui-search-item' && searchItem.label) {
                    this.settingList[searchItem.label] = {
                        isShow: true,
                        disabled: searchItem.lock ? true : false,
                        sort: index
                    }
                }
            });

            this.checkNeedToggle();

            this.headers.forEach((item, index) => {
                if (item.key !== 'option') {
                    this.tableSettingList[item.label] = {
                        isShow: true,
                        disabled: item.lock || item.key === 'option' ? true : false,
                        sort: index
                    }
                }
            })

            //获取后台存储设置
            axios.post('/crm/common/profile/searchList', {
                searchFromEnum: this.listName
            }).then(({ data }) => {
                if (data.success) {
                    let customList = data.data.conditionList || [];

                    customList.forEach(item => {
                        this.tabList.push({
                            label: item.searchName,
                            id: item.id,
                            isCustom: true,
                            customData: JSON.parse(item.searchContent)
                        })
                    })

                    this.conditionList = customList;

                    let querySettingData = data.data.queryMap || {};
                    this.querySettingId = querySettingData.id || '';

                    if (querySettingData.searchContent) {
                        try {
                            let settingList = JSON.parse(querySettingData.searchContent);
                            if (settingList && settingList.length) {
                                settingList.forEach(item => {
                                    if (this.settingList[item.label]) {
                                        if (!item.isShow) {
                                            this.settingList[item.label].isShow = item.isShow;
                                        }
                                        this.settingList[item.label].sort = (item.sort || item.sort === 0) ? item.sort : this.tableSettingList[item.label].sort;
                                    }
                                });
                            }

                            console.log(this.settingList)

                            this.checkSettingShow();
                        } catch (error) {
                            console.error(error)
                        }
                    }

                    let resultSettingData = data.data.resultMap || {};
                    this.resultSettingId = resultSettingData.id || '';

                    if (resultSettingData.searchContent) {
                        try {
                            let settingList = JSON.parse(resultSettingData.searchContent);
                            if (settingList && settingList.length) {
                                settingList.forEach(item => {
                                    if (this.tableSettingList[item.label]) {
                                        this.tableSettingList[item.label].isShow = item.isShow;
                                        this.tableSettingList[item.label].sort = (item.sort || item.sort === 0) ? item.sort : this.tableSettingList[item.label].sort;
                                    }
                                });
                            }

                            this.checkTableSettingShow();
                        } catch (error) {
                            console.error(error)
                        }
                    }
                }
            });
        },
        showSetting(type) {
            this.isShowSettingDialog = true;
            this.settingType = type;
            let tempSettingList = [];
            if (type === 'QUERY') {
                this.settingDialogTitle = '自定义筛选字段';
                for (let item in this.settingList) {
                    tempSettingList.push({
                        label: item,
                        ...this.settingList[item]
                    })
                }
            }
            if (type === 'RESULT') {
                this.settingDialogTitle = '自定义显示列';
                for (let item in this.tableSettingList) {
                    tempSettingList.push({
                        label: item,
                        ...this.tableSettingList[item]
                    })
                }
            }

            tempSettingList.sort((a, b) => {
                return a.sort - b.sort;
            })
            this.tempSettingList = tempSettingList;

            this.$nextTick(() => {
                this.checkSettingSelectAll();
            })
        },
        handlerSettingAllChange() {
            for (let item in this.tempSettingList) {
                if (!this.tempSettingList[item].disabled) {
                    this.tempSettingList[item].isShow = this.isSettingSelectAll;
                }
            }
        },
        checkSettingSelectAll() {
            let flag = true;
            for (let item in this.tempSettingList) {
                if (!this.tempSettingList[item].isShow) {
                    flag = false;
                }
            }
            this.isSettingSelectAll = flag;
        },
        handlerSettingChange() {
            // for (let item in this.tempSettingList) {
            //     if (!this.tempSettingList[item].isShow) {
            //         hiddenList[item] = 1;
            //     }
            // }

            this.isShowSettingDialog = false;

            let id = "";
            if (this.settingType === 'QUERY') {
                id = this.querySettingId || '';
                this.tempSettingList.forEach((item, index) => {
                    for (let key in this.settingList) {
                        if (item.label == key) {
                            this.settingList[key].sort = item.sort;
                            this.settingList[key].isShow = item.isShow;
                        }
                    }
                })
            } else if (this.settingType === 'RESULT') {
                id = this.resultSettingId || '';

                this.tempSettingList.forEach((item, index) => {
                    for (let key in this.tableSettingList) {
                        if (item.label == key) {
                            this.tableSettingList[key].sort = item.sort;
                            this.tableSettingList[key].isShow = item.isShow;
                        }
                    }
                })
            }

            //将修改的设置存储到后台
            axios.post('/crm/common/profile/saveSearchConfig', {
                searchFromEnum: this.listName,
                searchType: this.settingType,
                searchContent: JSON.stringify(this.tempSettingList),
                id: id
            }).then(({ data }) => {
                if (data.success) {
                    if (this.settingType === 'QUERY') {
                        this.checkSettingShow();
                    }

                    if (this.settingType === 'RESULT') {
                        this.checkTableSettingShow();
                    }
                } else {
                    this.$message.warn(data.message);
                }
            })
        },
        //恢复系统默认
        resetSettingDialog() {
            let setting = [];

            if (this.settingType === 'RESULT') {
                this.headers.forEach((item, index) => {
                    if (item.key !== 'option') {
                        setting.push({
                            label: item.label,
                            isShow: true,
                            disabled: item.lock ? true : false,
                            sort: index
                        })
                    }
                })
            }

            if (this.settingType === 'QUERY') {
                let num = 0;
                this.$children.forEach((searchItem, index) => {
                    if (searchItem.$options.name === 'ui-search-item' && searchItem.label) {
                        setting.push({
                            isShow: true,
                            disabled: searchItem.lock ? true : false,
                            sort: num,
                            label: searchItem.label
                        })
                        num++;
                    }
                })
            }

            this.tempSettingList = setting;
            this.checkSettingSelectAll();
        },
        checkSettingShow() {
            let list = [];
            let num = 0;

            if(this.needSetting) {
                this.$children.forEach((searchItem, index) => {
                    if (searchItem.$options.name && searchItem.label) {
                        if (this.settingList[searchItem.label] && !this.settingList[searchItem.label].isShow) {
                            searchItem.settingShow = false;
                        } else {
                            searchItem.settingShow = true;
                        }
    
                        list.push({
                            el: searchItem.$el,
                            sort: (this.settingList[searchItem.label].sort || this.settingList[searchItem.label].sort === 0) ? this.settingList[searchItem.label].sort : num
                        })
    
                        num++;
                    }
                });
    
                list.sort((a, b) => {
                    return a.sort - b.sort;
                })

                list.forEach(item => {
                    this.$refs.searchList.append(item.el);
                })
            }

            this.$nextTick(() => {
                this.checkNeedToggle();
            })
        },
        checkTableSettingShow() {
            let newHeaders = [];
            let option = [];
            this.headers.forEach(item => {
                if (this.tableSettingList[item.label] && this.tableSettingList[item.label].isShow) {
                    newHeaders.push({
                        sort: this.tableSettingList[item.label].sort,
                        ...item
                    });
                }

                if (item.key === 'option') {
                    option = item;
                }
            })

            newHeaders.sort((a, b) => {
                return a.sort - b.sort;
            })
            newHeaders = newHeaders.concat(option);

            this.showHeaders = newHeaders;

            this.$nextTick(() => {
                this.$refs.listTable && this.$refs.listTable.initTable();
            })
        },
        addToCustom() {
            let num = 0;
            this.tabList.forEach(item => {
                if (item.isCustom) {
                    num++;
                }
            })
            if (num >= 5) {
                this.$message.warn('最多支持创建5个固定查询');
                return;
            }

            this.customEditId = '';
            this.showCustomDialog();
        },
        showCustomDialog() {
            this.isShowCustomDialog = true;
            this.customName = "";

            this.$form.rules({
                customName: {
                    required: '请填写标题名称'
                },
            }, 'customSearchForm', this)
        },
        saveCustomSearch() {
            if (this.$form.validForm('customSearchForm')) {
                axios.post('/crm/common/profile/saveSearchConfig', {
                    searchName: this.customName,
                    searchFromEnum: this.listName,
                    searchType: "LIST",
                    searchContent: JSON.stringify(this.searchParams),
                    id: this.customEditId || ''
                }).then(({ data }) => {
                    if (data.success) {
                        this.$message({
                            message: this.customEditId ? '操作成功' : '新增成功'
                        })

                        if (this.customEditId) {
                            this.tabList.forEach(item => {
                                if (item.id == this.customEditId) {
                                    item.label = this.customName;
                                }
                            })
                        } else {
                            this.tabList.push({
                                label: this.customName,
                                id: data.data,
                                isCustom: true,
                                customData: this.searchParams
                            })
                        }


                        this.isShowCustomDialog = false;
                    } else {
                        this.$message.warn(data.message);
                    }
                })
            }
        },
        deleteConfirm() {
            let _this = this;
            this.$popup.warn({
                message: '删除后将无法恢复，您确定需要删除吗？',
                buttons: [{
                    txt: '删除',
                    btnClass: 'delete',
                    callback() {
                        _axios.post('/crm/common/profile/deleteSearchConfig', {
                            id: _this.customEditId
                        }).then(({ data }) => {
                            if (data.success) {
                                _this.tabList.forEach((item, index) => {
                                    if (item.id == _this.customEditId) {
                                        _this.tabList.splice(index, 1);
                                        if (_this.$refs.searchTab.current == item.id) {
                                            _this.$refs.searchTab.handlerChangeCurrent(_this.tabList[0]);
                                        }
                                    }
                                })

                                _this.$message.success('删除成功');
                            } else {
                                _this.$message.warn(data.message);
                            }
                        })
                    }
                },
                {
                    txt: '取消',
                }]
            })
        },
        customOption(params) {
            this.customEditId = params.item.id;
            if (params.type === 'rename') {
                this.showCustomDialog();
                this.customName = params.item.label;
            }

            if (params.type === 'delete') {
                this.deleteConfirm();
            }
        },
        handlerTabChange(id, item) {
            document.body.click()

            setTimeout(() => {
                if (item.isCustom) {
                    this.isLoadingSearch = true;
                    this.isCalcingSearch = true;
                    this.searchParams = JSON.parse(JSON.stringify(item.customData));
                    this.$parent.searchParams = JSON.parse(JSON.stringify(item.customData));
                } else if (item.id === 'all') {
                    this.isLoadingSearch = true;
                    this.isCalcingSearch = true;
                    this.clearParams();
                } else {
                    this.$emit('listtabchange', item)
                    return;
                }

                setTimeout(() => {
                    this.isLoadingSearch = false;
                    this.listSearch();
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.checkSearchItemStatus();
                            this.checkSettingShow();
                        })
                    })
                })
            })
        },
        clearParams() {
            for (let item in this.searchParams) {
                if (typeof this.searchParams[item] === 'string') {
                    this.searchParams[item] = '';
                } else if (Array.isArray(this.searchParams[item])) {
                    this.searchParams[item] = [];
                } else if (typeof this.searchParams[item] === 'object') {
                    this.searchParams[item] = {};
                }
            }
        },
        handlerReset() {
            if(this.customReset) {
                this.$emit('reset');
            } else {
                this.clearParams();
                this.isLoadingSearch = true;
    
                setTimeout(() => {
                    this.isLoadingSearch = false;
                    this.listSearch();
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.checkSearchItemStatus();
                            this.checkSettingShow();
                        })
                    })
                })
    
                this.$emit('reset');
            }
        },
        handlerListSelect(data) {
            this.listSelectData = data;
        },
        getSelectedData(key) {
            let ids = [];

            if (key) {
                this.listSelectData.forEach(item => {
                    ids.push(item[key]);
                })

                return ids;
            } else {
                return this.listSelectData;
            }
        },
        listSort(data) {
            if (this.isListSort) {
                let item = data.item;
                let key = item.sortKey ? item.sortKey : data.key;
                let orderPrase = {
                    up: key + ' asc',
                    down: key + ' desc',
                };

                this.sortValue = orderPrase[data.sort] || '';
                this.listSearch({isSort: 1});
                this.$emit('handlersort', data)
            }
        },
        handlerPageChange(num) {
            this.pageNo = num;

            let isSort = this.sortValue ? 1 : 0;

            this.listSearch({ isPage: 1, isSort })
        },
        handlerPagesizeChange() {
            this.pageNo = 1;
            this.listSearch()
        },
        refresh() {
            this.listSearch({ isRefresh: 1 });
        },
        listSearch(searchData) {
            VD_UI_GLOBAL.showGlobalLoading();

            let { isPage, isSort, isRefresh } = searchData || {};

            let params = {};

            if (!(isPage || isSort || isRefresh)) {
                this.prevSearchParams = JSON.parse(JSON.stringify(this.searchParams));
                this.pageNo = 1;
                this.sortValue = "";
                params = this.searchParams;
            } else {
                params = this.prevSearchParams;
            }

            for (let item in params) {
                if (typeof params[item] === 'string') {
                    params[item] = params[item].trim();
                }
            }

            axios.post(this.url, {
                pageNum: this.pageNo,
                pageSize: this.pageSize,
                orderBy: this.sortValue,
                param: {
                    ...params,
                    ...this.defaultSearchParams,
                }
            }).then(({ data }) => {
                VD_UI_GLOBAL.hideGlobalLoading();
                if(this.$refs.listTable) {
                    if (!(isSort || isRefresh)) {
                        this.$refs.listTable.clearSort();
                    }
    
                    if(!isRefresh) {
                        this.$refs.listTable.clearFilter();
                    }
                }

                if (data.success) {
                    let list = data.data.list;
                    
                    list.forEach((item, index) => {
                        this.headers.forEach(headerItem => {
                            if (headerItem.parse) {
                                item[headerItem.key] = headerItem.parse.data[item[headerItem.parse.key]];
                            }
                        })

                        item.ui_sort_value = index + 1;
                        item.prevSort = item.sort;
                    })
                    this.list = list;
                    this.totalCount = data.data.total;
                    this.isFirst = false;


                    //更新分页器
                    this.refreshPager = true;
                    setTimeout(() => {
                        this.refreshPager = false;
                    }, 100)
                } else {
                    this.$message.warn(data.message)
                }
            })
        },
        tableItemEdit(data) {
            console.log(data)
            this.$emit('edit', data)
        },
        tableScroll() {
            this.$emit('scroll')
        },
        handlerCallNumber(data) {
            this.$emit('call', data)
        },
        handlerSettingSort(data) {
            this.tempSettingList.forEach((item, index) => {
                item.sort = index
            })
        }
    }
})
// 三级分类 - 下拉联想
Vue.component('ui-search-related', {
    template: `
        <div class="vd-ui-search-category" :style="{width: Number(width)+40+'px'}">
            <div class="vd-ui-search-category-form">
                <div
                    class="vd-ui-search-category-item"
                    v-for="(item, index) in list" :key="index"
                >
                    <div >
                        <ui-input
                            :width="width+'px'"
                            :clearable="clearable"
                            v-model="item.val"
                            @input.native="oninput"
                            @compositionend.native="commentPress"
                            @clear="clear"
                            @focus="onFocus(index)"
                            @blur="onBlur"
                        ></ui-input>
                        <div class="del" v-if="index > 0" @click="del(index)">
                            <i class="vd-ui_icon icon-delete"></i>
                        </div>
                    </div>

                    <transition name="fade">
                        <div class="search-panel" v-if="rotate == index" :style="{width: width+'px'}">
                            <div class="search-panel-inner">
                                <div class="search-loading" v-if="loading">
                                    <i class="vd-ui_icon icon-loading"></i>
                                    <span>加载中...</span>
                                </div>
                                <div class="search-panel-list" v-else-if="searchList && searchList.length">
                                    <div class="search-list-content">
                                        <div class="label">
                                            <span class="num">分类</span>
                                            <span class="num">产品数量</span>
                                        </div>
                                        <div class="search-panel-item"
                                            v-for="search in searchList" :key="search[_value_]"
                                            @click.stop="selectThis(item, search)"
                                        >
                                            <span v-html="light(search[_label_])"></span>
                                            <span v-if="search.countNum" class="num">{{ search.countNum }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class="null-data">
                                    <p>无匹配数据</p>
                                </div>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>
            <div class="vd-ui-input-error" v-if="errorable">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
            <div class="vd-ui-search-category-btn" v-if="list.length < size">
                <a class="btn" @click="add">+ {{ btn }}</a>
            </div>
        </div>
    `,
    props: {
        value: {
            type: String,
            default: ''
        },
        // 是否需要清空功能
        clearable: {
            type: Boolean,
            default: false,
        },
        size: {
            type: Number,
            default: 5,
        },
        btn: {
            type: String,
            default: '添加分类'
        },
        width: {
            type: String,
            default: '300px'
        },
        _label_: {
            type: String,
            default: 'categoryName'
        },
        _value_: {
            type: String,
            default: 'categoryId'
        },
        baseUrl: {
            type: String,
            default: '/crm/category/public/findThreeCategory',
        },
        errorable: {
            type: Boolean,
            default: false
        },
    },
    data () {
        return {
            rotate: null,
            list: [
                { val: '', id: 1 },
            ],
            errorable: false,
            errorMsg: '',
            inputTimeout: null,
            loading: false,

            indexFlag: null, // 当前操作下标
            searchList: [], // 当前搜索推荐
        }
    },
    watch: {
        rotate (newV) {
            if (newV == null) {
               this.searchList = [];
            }
        }
    },
    computed: {
        // 高亮
        light () {
            return (name) => {
                let inputValue = this.list[this.indexFlag].val;
                console.log('inputValue:', inputValue);
                if (!inputValue) return name;
                const regExp = new RegExp(inputValue, 'g');
                name = name.replace(regExp, `<font color='#FF6600'">${inputValue}</font>`);
                return name;
            }
        },
    },
    mounted () {
        if (this.value) {
            this.init();
        }
        this.$form.setValidEl(this);
        document.addEventListener('click',(e)=>{
            if(!this.$el.contains(e.target)) {
                this.rotate = null;
            }
        })
    },
    methods: {
        init () {
            if (!this.value) {
                this.list = [{val: '', id: 1, searchList: []}];
            } else {
                let arr = this.value.split('&&');
                let list_ = [];
                arr.forEach((item, index) => {
                    list_.push({
                        val: item,
                        id: list_.length + 1,
                        searchList: [],
                    })
                })
                this.list = list_;
            }
        },

        clear () {
            this.pick();
        },
        onFocus(index) {
            this.indexFlag = index;
        },
        onBlur() {
            setTimeout(()=> {
                console.log('this.value:', this.value);
                this.checkValid(this.value || '');
            }, 500)
        },

        // 节流
        // handleInput: _.throttle(function (index) {
        //     this.oninput(index);
        // }, 200),
        oninput (event) {
            this.rotate = this.indexFlag;
            if (event.inputType != 'insertCompositionText') {
                this.pick();

                this.inputTimeout && clearTimeout(this.inputTimeout);
                this.inputTimeout = setTimeout(() => {
                    this.searchRelated(event.target.value);
                }, 300);
            }
        },
        commentPress(event) {
            this.rotate = this.indexFlag;
            this.pick();

            this.inputTimeout && clearTimeout(this.inputTimeout);
            this.inputTimeout = setTimeout(() => {
                this.searchRelated(event.target.value);
            }, 300);
        },
        async searchRelated (keyword) {
            if (!keyword.trim()) return;
            this.loading = true;
            try {
                let {data: searchRes} = await axios.post(this.baseUrl, {
                    keyword: keyword,
                    pageNum: 1,
                    pageSize: 100
                })

                this.loading = false;
                if (searchRes.success) {
                    let res = searchRes.data.list|| [];
                    this.searchList = res;
                } else {
                    this.searchList = [];
                }
            } catch (err) {
                console.log('err:', err);
            }
        },
        add () {
            if (this.list.length < this.size) {
                this.list.push({
                    val: '',
                    id: this.list.length + 1,
                })
            }
        },
        del (index) {
            this.list.splice(index, 1);
            this.pick();
        },
        selectThis(item, search) {
            item.val = search[this._label_];
            this.rotate = null;

            this.pick();
        },
        pick () {
            let arr = [];
            this.list.forEach(item => {
                if (item.val.trim()) {
                    arr.push(item.val.trim());
                }
            })
            let str = arr.join('&&');
            this.$emit("input", str); // 修改外层v-model值
            this.$emit('change', arr)
        },
        checkValid(newValue) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    }
})
Vue.component('ui-option', {
    template: `
        <div v-if="!shadow">
            <li
                :class="['ui-select-option-li', disabled?'disabled':'',selected?'selected':'',select.multipleType?'multiple':'']"
                @click="selectFT"
                @mouseup="mouseUp"
            >
                <template>
                    <i class="vd-ui_icon icon-checkbox1" v-if="select.multipleType && !selected"></i>
                    <i class="vd-ui_icon icon-checkbox2" v-if="select.multipleType && selected"></i>
                    <div class="li-p li-p-made">
                        <template v-if="!custom">
                            <div class="li-avatar" v-if="avatar"><img :src="avatarUrl || '/static/image/crm-user-avatar.svg'" onerror="this.classList.add('error')"/></div>
                            <p class="li-label" :title="label" v-html="showlabel || label"></p>
                            <p class="li-name" v-if="name" v-html="name" :style="'width:' + (nameWidth || '50%')"></p>
                        </template>
                        <slot v-else></slot>
                    </div>
                </template>
            </li>
        </div>
    `,
    props: {
        value: String,
        label: String,
        name: String,
        avatar: {
            type: Boolean,
            defalut: false
        },
        avatarUrl: String,
        showlabel: String,
        nameWidth: String,
        disabled: {
            type: Boolean,
            default: false
        },
        shadows: {
            type: Boolean,
            default: false
        },
        custom: {
            type: Boolean,
            default: false
        }
    },
    inject: {
        select: {
            default: ''
        }
    },
    computed: {
        selected() {
            if (this.select.multipleType) {
                for (let i of this.select.showVal) {
                    if (i == this.value) {
                        return true;
                    }
                }
            } else {
                if (this.select.showVal == this.value) {
                    return true;
                }
            }
        },
        shadow() {
            if (this.select.multipleType) {
                for(let i of this.select.showVal) {
                    if (i == this.value) {
                        return this.shadows;
                    }
                }
            } else {
                if (this.select.showVal == this.value) {
                    return this.shadows;
                }
            }
            return false;
        }
    },
    methods: {
        selectFT() {
            if (this.disabled) return
            if (this.select.multipleType) {
                if (this.selected) {
                    for(let i in this.select.mulSeleList) {
                        if (this.select.mulSeleList[i].value == this.value) {
                            this.select.mulSeleList.splice(i,1)
                            break
                        }
                    }
                } else {
                    this.select.mulSeleList.push({
                        label:this.label,
                        value:this.value
                    })
                }
                this.select.changeMulList();
                if (this.select.multipleType == 'fixed') {
                    this.select.tagLabelNum()
                }
            } else {
                this.select.showLabel = this.label;
                this.select.selectValue(this.value);
                this.select.selectObj = {
                    label:this.label,
                    value:this.value
                }
                this.select.changeVal(this.select.selectObj.value,this.value); // change事件
            }
        },
        mouseUp(event) {
            if (this.disabled) return;
            if (!this.select.multipleType) {
                setTimeout(() => {
                    this.select.rotate = false;
                    try {
                        this.$parent.$parent.$refs.vdInput.blur();
                    } catch (error) {
                        
                    }
                }, 100)
            };
            if ( this.select.clearable == true ) this.select.clearFlag = true;
        },
        initLabel() {
            if (this.select.multipleType) {
                let arr = new Array();
                this.select.showVal.forEach((val)=>{
                    if (val == this.value) {
                        this.select.mulSeleList.push({
                            label:this.label,
                            value:this.value
                        })
                    }
                })
                this.select.mulList = JSON.parse(JSON.stringify(this.select.mulSeleList));
            } else {
                if (this.select.showVal == this.value) {
                    this.select.showLabel = this.label;
                    this.select.selectObj = {
                        label:this.label,
                        value:this.value
                    }
                }
            }
        },
    }
})
Vue.component('ui-option-group', {
    template: `
        <div class="vd-ui-group">
            <li class="vd-ui-group-title"><p>{{ group }}</p></li>
            <ui-option
                v-for="(item,index) in list"
                :key="index"
                :value="item.value"
                :label="item.label"
                :name="item.name"
                :disabled="item.disabled">
            </ui-option>
            <slot></slot>
        </div>
    `,
    props: {
        group: String,
        data: {
            type: Array,
            default: ()=> {
                return []
            }
        }
    },
    inject: {
        select: {
            default: ''
        }
    },
    computed: {
        list() {
            return this.data;
        },
    }
})

var Mixin_uiSelect_transition = {
    data() {
        return {}
    },
    mounted() {
        // window.addEventListener('scroll', this.vdListScroll, true) // 改为true是从外到里，事件捕获，false是从里到外，事件冒泡
        // window.addEventListener('resize', this.vdListScroll)
    },
    methods: {
        beforeLeave(el) {
            el.style.webkitTransform = 'scale(1,1)';
            el.style.opacity = 1;
        },
        leave(el) {
            if (el.scrollHeight !== 0) {
                // el.style.transition = '0.19s all ease-in';
                el.style.webkitTransform = 'scale(1,0)';
                el.style.opacity = 0;
            }
        },
        afterLeave(el) {
            el.style.transition = '';
            el.style.webkitTransform = '';
            el.style.opacity = '';
        },
        topScroll() {
            console.log('111')
            let client = this.$el.getBoundingClientRect();
            let height = this.$refs.vdList.clientHeight;
            let clientHeight = document.body.clientHeight;
            if (client.bottom + height + 7 > clientHeight && client.top >= height + 2) {
                this.animation = 'appear-up';
                this.$refs.vdList.style.top = `-${height + 2}px`;
                this.$refs.vdList.style.boxShadow = 'rgba(0, 0, 0, 0.1) 0px -5px 10px';
            } else {
                this.animation = 'appear-down';
                this.$refs.vdList.style.top = "";
                this.$refs.vdList.style.boxShadow = '';
            }
        },
        vdListScroll() {
            console.log('asd')
            if (this.$refs.vdList) {
                this.topScroll();
            }
        },
    }
};


var Mixin_uiSelect_multipleSelect = {
    data() {
        return {
            mulList: [],
            mulNumber: 0,
            mulSeleList: [],
            showTop: false,
        }
    },
    mounted() {
        if (this.defaultMulti && this.defaultMulti.length) {
            this.mulSeleList = this.defaultMulti;
            this.mulList = JSON.parse(JSON.stringify(this.mulSeleList));
        }

        if (this.multipleType == 'fixed') {
            this.tagLabelNum();
        }

        window.addEventListener('resize', () => {
            if (this.multipleType && this.multipleType == 'fixed' && this.mulSeleList && this.mulSeleList.length) {
                this.mulList = JSON.parse(JSON.stringify(this.mulSeleList));
                this.tagLabelNum();
            }
        })
    },
    computed: {
        moreTag() {
            if (this.mulNumber === 0) {
                return false
            } else {
                return true
            }
        }
    },
    methods: {
        arrayContra(arr, className) {
            let newArr = new Array();
            for (var i = 0, len = arr.length; i < len; i++) {
                if (arr[i]._prevClass && arr[i]._prevClass.indexOf(className) >= 0) {
                    newArr.push(arr[i]);
                }
            }
            return newArr;
        },
        tagLabelNum() {
            this.$nextTick(() => {
                if (!this.$refs.tagBox) {
                    return;
                }

                let all = this.$refs.tagBox.offsetWidth;
                let offsetWidth = 0;
                let arr = this.arrayContra(this.$refs.tagBox.children, "tag-label");
                this.mulNumber = 0;
                this.showTop = false;

                try {
                    arr.forEach((item, index) => {
                        offsetWidth += item.offsetWidth + 5;
                        if (offsetWidth > all) { // 最大宽度不会超出，第一个对象肯定放得下
                            offsetWidth -= item.offsetWidth - 5;
                            this.mulNumber = this.mulList.length - index;
                            this.mulList = this.mulList.slice(0, index);
                            throw new Error("overNative");
                        }
                    })
                } catch (e) {
                    if (e.message != 'overNative') throw e
                }
                if (this.mulList.length == 1 && this.mulNumber != 0) {
                    this.$nextTick(() => {
                        if (offsetWidth + this.$refs.tagMore.offsetWidth + 5 > all) {
                            this.showTop = true
                        }
                    })
                }
                if (this.mulList.length > 1 && this.mulNumber != 0) {
                    this.$nextTick(() => {
                        let more = this.$refs.tagMore.offsetWidth + 5;
                        let arrNew = this.arrayContra(this.$refs.tagBox.children, "tag-label");
                        try {
                            arrNew.reverse().forEach((item, index) => {//数组反转
                                if (offsetWidth + more <= all) {//已经在一行
                                    throw new Error("kill");
                                }

                                if (arrNew.length === index + 1) { //到第一项不删除
                                    this.mulList = this.mulList.slice(0, 1);
                                    this.mulNumber += index;
                                    throw new Error("kill");
                                }

                                offsetWidth = offsetWidth - item.offsetWidth - 5;
                                if (offsetWidth + more <= all) {
                                    this.mulNumber += index + 1;
                                    this.mulList = this.mulList.slice(0, this.mulList.length - (index + 1));
                                    throw new Error("kill");
                                }
                            })
                        } catch (e) {
                            if (e.message != 'kill') throw e
                        }
                    })
                }
            })
        },
        removeTag(obj) {
            for (let i in this.mulSeleList) {
                if (this.mulSeleList[i].value == obj.value) {
                    this.mulSeleList.splice(i, 1);
                    this.changeMulList();
                    if (this.multipleType == 'fixed') {
                        this.tagLabelNum();
                    }
                    return;
                }
            }
        },
        changeMulList() {
            let arr = new Array();
            this.mulSeleList.forEach(item => {
                arr.push(Number(item.value));
            })
            this.changeVal(this.showVal, arr); // change事件
            this.mulList = JSON.parse(JSON.stringify(this.mulSeleList));
            this.selectValue(arr);
        },
    },
}

Vue.component('ui-select', {
    template: `
        <div
            class="vd-ui-select"
            :style="{'width':width}"
            @click.stop="handleClick"
            @mouseenter="handleEnter"
            @mouseleave="handleLeave"
        >
            <div
                v-if="!multipleType"
                class="vd-ui-select-wrapper"
                :class="[disabled? 'vd-ui-select-wrapper__disabled': '', errorable? 'vd-ui-select-wrapper__error': '']"
            >
                <ui-input
                    :placeholder="holder"
                    v-bind="inputattr"
                    :disabled="disabled"
                    v-model="showLabel"
                    ref="vdInput"
                    @focus="focus"
                    @blur="blur"
                    @clear="clear"
                    @keydown.native.tab="rotate=false"
                    @input.native="handleInput"
                    @compositionend.native="commentPress"
                    :size="size"
                    width="100%"
                    :readonly="readonly"
                    autocomplete="off"
                    :clearable="clearValue"
                    :selectClear="selectClear"
                >
                    <i slot="suffix" class="vd-ui_icon icon-down icon" :class="[rotate? 'rotate': '', size? size: '']" v-if="iconDown"></i>
                    <i slot="suffix" class="vd-ui_icon icon-search icon" :class="[size? size: '']" v-if="iconSearch"></i>
                </ui-input>
            </div>
            <div 
                v-if="multipleType"
                class="vd-ui-select-wrapper vd-ui-select-multiple-wrapper"
                :class="[
                    disabled? 'vd-ui-select-multiple-wrapper__disabled': '',
                    size? 'vd-ui-select-multiple-wrapper-'+size: '',
                    rotate? 'is-focus': '',
                    errorable? 'vd-ui-select-multiple-wrapper__error': ''
                ]"
                
            >
                <div class="vd-ui-tag " :class="[ multipleType == 'auto'? 'vd-ui-tag-auto': '', {'has-more': moreTag}, readonly? 'vd-ui-readonly': '']" ref="tagBox">
                    <span class="placeholder" v-show="!mulList.length && readonly">{{placeholder || '请选择'}}</span>
                        <span class="vd-ui-select-tag tag-label" v-for="item in mulList" :key="item.value">
                        <span class="tag-text">{{item.label}}</span>
                        <span class="tag-icon" @click.stop v-if="!disabled"><i class="vd-ui_icon icon-delete" @click.stop="removeTag(item)"></i></span>
                    </span>

                    <span ref="tagMore" class="vd-ui-select-tag tag-more" :class="{'marginTop':showTop}" v-if="moreTag">
                        <span class="tag-text">+{{mulNumber}}</span>
                    </span>

                    <input
                        v-if="multipleType == 'auto' && !readonly"
                        type="text"
                        class="vd-ui-input-multiple"
                        ref="multipleInput" 
                        :placeholder="!disabled && !mulList.length ? (placeholder || '请输入'): ''"
                        :disabled="disabled"
                        v-model="showLabel"
                        @focus="focus"
                        @blur="blur"
                        @input="handleInput"
                    />
                </div>

                <input
                    v-if="multipleType == 'fixed' && !readonly"
                    type="text"
                    class="vd-ui-input-multiple"
                    ref="multipleInput" 
                    :placeholder="!disabled && !mulList.length ? placeholder || '请输入': ''"
                    :disabled="disabled"
                    v-model="showLabel"
                    @focus="focus"
                    @blur="blur"
                    @input="handleInput"
                />
                <i class="vd-ui_icon icon-down mul-icon" :class="[rotate? 'rotate': '',size? size: '']" v-if="mulDown"></i>
                <i class="vd-ui_icon icon-search mul-icon" v-if="mulSearch"></i>
                <i  v-if="mulClear"
                    class="vd-ui_icon icon-error2 mul-icon"
                    @click.prevent.stop="handleClear"
                ></i>
            </div>
            <ui-poper :show="rotate" :position="animation === 'appear-up' ? 'top' : ''" ref="dropwrap" :errorable="!!errorMsg">
            
                    <ul 
                        class="vd-ui-select-list"
                        :class="[animation? animation:'']"
                        ref="vdList"
                        :style="style"
                        @click.stop
                        v-show="rotate"
                        @mousedown.prevent
                    >
                        <template v-if="!noLoad">
                            <template v-if="group">
                                <ui-option-group
                                    v-for="(item,index) in list"
                                    :key="index" 
                                    :data="item.list"
                                    :group="item.group"
                                >
                                </ui-option-group>
                            </template>
                            <template v-else>
                                <ui-option
                                    v-for="(item,index) in list"
                                    :key="index"
                                    :value="item.value"
                                    :label="item.label"
                                    :showlabel="item.showLabel || ''"
                                    :name="item.name"
                                    :shadows="shadows"
                                    :disabled="item.disabled"
                                    :avatar="avatar"
                                    :custom="!!$scopedSlots.option"
                                    :nameWidth="nameWidth"
                                    :avatar-url="avatar ? item.avatar : ''"
                                >
                                    <template v-if="$scopedSlots.option">
                                        <slot name="option" :item="item"></slot>
                                    </template>
                                </ui-option>
                                <li class="empty-li" v-if="showdow">
                                    <p>暂无数据</p>
                                </li>
                            </template>
                            <slot v-if="!remote"></slot>
                        </template>
                        
                        <template v-if="loading">
                            <li class="loading-li">
                                <p>
                                    <i class="vd-ui_icon icon-loading" ref="loading"></i>
                                    <span>加载中...</span>
                                </p>
                            </li>
                        </template>

                        <template v-if="loadingFail">
                            <li class="failed-li">
                                <p>
                                    <i class="vd-ui_icon icon-error2"></i>
                                    <span>加载失败，</span>
                                    <span class="reload" @click="handleReload">重新加载</span>
                                </p>
                            </li>
                        </template>

                        <template v-if="!list.length && !noLoad && !$slots.default">
                            <li class="empty-li" v-if="remote">
                                <p>无匹配数据</p>
                            </li>
                            <li class="empty-li" v-else>
                                <p>暂无数据</p>
                            </li>
                        </template>
                    </ul>
            </ui-poper>
            <div class="vd-ui-input-error" v-if="errorable && !disabled && errorMsg">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,

    mixins: [Mixin_uiSelect_transition, Mixin_uiSelect_multipleSelect],
    props: {
        placeholder: {
            type: String,
            default: '请选择'
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        clearable: {
            type: Boolean,
            default: false,
        },
        width: String,
        listWidth: String,
        nameWidth: String,
        size: {
            type: String,
            default: 'middle'
        },
        data: {
            type: Array,
            default: () => {
                return []
            }
        },
        showVal: {
            default: '',
        },
        listAlign: {
            type: String,
            default: ''
        },
        group: {
            type: Boolean,
            default: false
        },
        loading: {
            type: Boolean,
            default: false
        },
        loadingFail: {
            type: Boolean,
            default: false
        },
        firstRemote: {
            type: Boolean,
            default: false
        },
        remote: {
            type: Boolean,
            default: false
        },
        remoteInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        // 是否多选
        multipleType: { //auto  fixed
            type: String,
            default: ''
        },
        shadows: {
            type: Boolean,
            default: false
        },
        errorable: {
            type: Boolean,
            default: false
        },
        errorMsg: {
            type: String,
            default: ''
        },
        avatar: {
            type: Boolean,
            default: false
        },
        maxHeight: {
            type: String,
            default: '300px'
        },
        defaultMulti: {
            type: Array,
            default: () => {
                return [];
            }
        },
        defaultLabel: {
            type: String,
            default: ""
        },
        //是否需要从字典表取值，需要的话，统一传字典表id
        dictionary: {
            type: String,
            default: ''
        },
        nameKeys: {
            type: Array,
            default() {
                return [];
            }
        },
        remoteDataParse: {
            type: Function,
            default: () => { }
        },
        //获焦的时候默认搜索全部（为了兼容有些展示字段是多个字段拼接的，调用搜索接口没有结果）
        searchDefaultAll: {
            type: Boolean,
            default: false
        },
        cansearch: {
            type: Boolean,
            default: false
        }
    },
    provide() {
        return {
            'select': this
        }
    },
    computed: {
        list() {
            return this.data;
        },
        style() {
            let style = {};
            style.width = this.listWidth
            style.maxHeight = this.maxHeight
            this.listAlign ?
                this.listAlign === 'right' ?
                    style.right = 0 :
                    style.left = this.listAlign : ''
            return style;
        },
        noLoad() {
            return this.loading || this.loadingFail;
        },
        readonly() {
            return !this.remote && !this.cansearch;
        },
        clearValue() {
            return this.clearable && this.selectClear;
        },
        selectClear() {
            return this.showVal ? true : false;
        },
        iconDown() {
            return !((this.entered || this.clearFlag) && this.clearValue && !this.disabled) && !this.iconSearch;
        },
        iconSearch() {
            return (this.remote || this.cansearch) && this.rotate && !this.clearValue;
        },
        mulClear() {
            return (this.clearable && this.showVal.length ? true : false) && (this.entered || this.rotate) && !this.disabled;
        },
        mulDown() {
            return !this.mulClear && !this.mulSearch;
        },
        mulSearch() {
            return (this.remote || this.cansearch) && !this.showVal.length && this.rotate;
        }
    },
    model: {
        prop: 'showVal',
        event: 'value'
    },
    data() {
        return {
            rotate: false,//下拉框
            showLabel: '',
            clearFlag: false,//清空按钮,获焦状态
            animation: '',
            holder: this.placeholder,
            selectObj: '',//选中的对象
            entered: false,
            showdow: false,
            inputTimeout: null,
            inputattr: {},
            dropPosition: {},
            sourceList: []
        }
    },
    created() {
        this.$form.setValidEl(this);
        this.inputattr = this.$attrs;
        delete this.inputattr.valid;

        if (this.dictionary) {
            this.firstRemote = true;
            this.remoteInfo = {
                url: '/crm/sysOption/public/getByParentId?parentId=' + this.dictionary,
                parseLabel: 'title',
                parseValue: 'sysOptionDefinitionId'
            }
        }
    },
    mounted() {
        if(this.cansearch && this.data.length) {
            this.sourceList = JSON.parse(JSON.stringify(this.data));
        }

        document.addEventListener('click', (e) => {
            if (!this.$el.contains(e.target)) this.rotate = false;
        })

        this.$nextTick(() => {
            setTimeout(() => {
                if (!this.defaultLabel) {
                    this.$refs.dropwrap.$children.forEach(item => {
                        // console.log('item:', item.$options);
                        if (item.$options.name == 'ui-option') {
                            item.initLabel();
                        }
                    })
                } else {
                    this.showLabel = this.defaultLabel;
                    this.selectObj = {
                        label: this.defaultLabel,
                        value: this.showVal
                    }
                }
            }, 200)
        })

        // if (!this.defaultLabel) {
        //     this.$refs.dropwrap.$children.forEach(item => {
        //         // console.log('item:', item.$options);
        //         if (item.$options.name == 'ui-option') {
        //             item.initLabel();
        //         }
        //     })
        // } else {
        //     this.showLabel = this.defaultLabel;
        //     this.selectObj = {
        //         label: this.defaultLabel,
        //         value: this.showVal
        //     }
        // }

        if (!this.$refs.vdList.children.length) this.showdow = true;

        if (this.firstRemote && !this.data.length && ((!this.multipleType && this.showVal) || (this.multipleType && this.showVal && this.showVal.length))) {
            this.getFirstList();
        }


    },
    watch: {
        defaultLabel(newV) {
            this.showLabel = newV;
            this.selectObj = {
                label: newV,
                value: this.showVal
            }
        },
        rotate() {
            if (this.rotate) {
                this.$nextTick(() => {
                    this.topScroll();
                })
                if (this.remote) {
                    this.showLabel ? this.holder = this.showLabel : '';
                    this.showLabel = '';
                }
            } else {
                this.holder = this.placeholder;
                this.showLabel = this.selectObj.label ? this.selectObj.label : '';
            }
        }
    },
    methods: {
        focus(event) {
            this.clearFlag = this.clearValue ? true : false;
            this.getSearchList('focus');
            this.$emit('focus', event);
        },
        handleClick() {
            if (this.disabled) return;

            if (this.firstRemote && !this.data.length) {
                this.getFirstList(1);
            } else {
                this.remote ? this.rotate = true : this.rotate = !this.rotate;
                if (this.multipleType) {
                    if (!this.readonly) this.$refs.multipleInput.focus();
                }
            }

        },
        blur(event) {
            this.rotate = false;
            this.clearFlag = false;
            this.$emit('blur', event);
        },
        clear() {
            this.clearFlag = false;
            if (this.multipleType) {
                this.selectValue([]);
            } else {
                this.selectValue('');
            }
            this.selectObj = "";
            this.holder = this.placeholder;

            setTimeout(() => {
                this.$refs.vdInput.blur();
            }, 100)
        },
        handleClear() {
            this.changeVal(this.showVal, []);
            this.mulSeleList = [];
            this.mulList = [];
            this.mulNumber = 0;

            this.selectValue([]);
        },
        handleInput(val) {
            console.log('inputType:', val.inputType);
            if (val.inputType != 'insertCompositionText') {
                this.inputTimeout && clearTimeout(this.inputTimeout);

                this.inputTimeout = setTimeout(() => {
                    this.getSearchList();
                }, 300)

                if (this.showLabel) {
                    this.$emit('search', this.showLabel);
                }
            }
        },
        commentPress(e) {
            if (this.showLabel) {
                this.inputTimeout && clearTimeout(this.inputTimeout);

                this.inputTimeout = setTimeout(() => {
                    this.getSearchList();
                }, 300)
                this.$emit('search', this.showLabel);
            }
        },
        handleClose() {
            // this.$refs.vdInput.focus();
        },
        handleEnter() {
            this.entered = true;
        },
        handleLeave() {
            this.entered = false;
        },
        clearData() {
            this.showLabel = '';
            this.clearFlag = false;
            if (this.multipleType) {
                this.selectValue([]);
            } else {
                this.selectValue('');
            }
        },
        //更改v-model绑定的值
        selectValue(val) {
            this.$emit('value', val);

            let resData = {
                value: val,
            };

            if (this.multipleType) {
                resData.list =  this.mulSeleList || [];

                if(this.multiSelectClear) {
                    if(this.showLabel) {
                        this.showLabel = '';
                        this.handleInput({})
                    }
                }
            } else {
                let choosed = this.data.filter(item => item.value == val)[0] || {};
                resData.selected = Object.assign({}, choosed)
            }
            this.$emit('change', resData);
            console.log('change1', resData);
            this.checkValid(val || '');
        },
        changeVal(oldValue, newValue) {
            setTimeout(() => {
                if (!this.$refs.vdList.children.length) this.showdow = true;
                else this.showdow = false;
            })

            let resData = {
                value: newValue,
            };

            if (this.multipleType) {
                resData.list = this.mulSeleList;
            } else {
                console.log('data:', this.data);
                console.log('selectObj:', this.selectObj);
                let choosed = this.data.filter(item => item.value == this.selectObj.value)[0] || {};
                console.log('choosed:', this.choosed);
                resData.selected = Object.assign(this.selectObj, choosed)
            }
            this.$emit('change', resData);
        },
        handleReload() {
            this.$emit('reload');
        },
        getSearchList(type) {
            if (this.remote && this.remoteInfo.url) {
                let value = this.showLabel.trim();

                //如果展示字段是拼接的，为了能让搜索有结果，默认搜索全部
                if(type == 'focus' && this.searchDefaultAll) {
                    value = '';
                }
                
                this.loading = true;

                this.$nextTick(() => {
                    this.$refs.dropwrap.calcPosition();
                })

                let params = {};
                let url = this.remoteInfo.url;

                if (this.remoteInfo.paramsType == 'url') {
                    if (this.remoteInfo.url.indexOf('?') !== -1) {
                        url += '&' + this.remoteInfo.paramsKey + '=' + value;
                    } else {
                        url += '?' + this.remoteInfo.paramsKey + '=' + value;
                    }
                } else {
                    params[this.remoteInfo.paramsKey] = value;
                }

                let reqMethod = this.remoteInfo.paramsMethod || 'post';

                let reqData = {
                    url,
                    method: reqMethod,
                }

                if (reqMethod == 'get') {
                    reqData.params = params
                } else if (reqMethod == 'post') {
                    reqData.data = params;
                }

                axios(reqData).then(({ data }) => {
                    if (data.code === 0) {
                        let list = [];
                        let resList = data.data || [];

                        let remoteParseData = this.remoteDataParse(resList)
                        if (remoteParseData) {
                            resList = remoteParseData;
                        }

                        resList.forEach(item => {
                            let reg = new RegExp('(' + value + ')', 'ig');

                            let itemData = {
                                label: item[this.remoteInfo.parseLabel],
                                value: item[this.remoteInfo.parseValue] + '',
                                avatar: this.avatar ? item[this.remoteInfo.parseAvatar] : '',
                                showLabel: value && (item[this.remoteInfo.showLabel] || item[this.remoteInfo.parseLabel] || item.label) ? (item[this.remoteInfo.showLabel] || item[this.remoteInfo.parseLabel] || item.label).replace(reg, '<span class="strong">$1</span>') : '',
                                ...item
                            }

                            if(!value && this.remoteInfo.showLabel) {
                                itemData.showLabel = item[this.remoteInfo.showLabel] || '';
                            }

                            if (this.nameKeys.length) {
                                let names = [];
                                this.nameKeys.forEach(key => {
                                    if (item[key]) {
                                        names.push(item[key])
                                    }
                                })

                                itemData.name = names.join(' / ');
                            } else {
                                delete itemData.name
                            }

                            list.push(itemData);
                        });
                        this.data = list;
                        this.loading = false;

                        this.$nextTick(() => {
                            this.$refs.dropwrap.calcPosition();
                        })
                    }
                })
            } else if(this.cansearch){
                let list = [];
                let value = this.showLabel.trim();

                if(value) {

                    this.sourceList.forEach(item => {
                        let reg = new RegExp('(' + value + ')', 'ig');
    
                        if(item.label.toUpperCase().indexOf(value.toUpperCase()) !== -1) {
                            let itemData = JSON.parse(JSON.stringify(item))
                            itemData.showLabel = item.label.replace(reg, '<span class="strong">$1</span>')
                            list.push(itemData);
                        }
                    });

                    this.data = list;
                } else {
                    this.data = JSON.parse(JSON.stringify(this.sourceList));
                }

                this.$nextTick(() => {
                    this.$refs.dropwrap.calcPosition();
                })
            }
        },
        getFirstList(trigger) {
            this.loading = true;

            let reqMethod = this.remoteInfo.paramsMethod || 'post';

            let reqData = {
                url,
                method: reqMethod,
            }

            axios(reqData).then(({ data }) => {
                if (data.code === 0) {
                    let list = [];
                    let resList = data.data;

                    let remoteParseData = this.remoteDataParse(resList)
                    if (remoteParseData) {
                        resList = remoteParseData;
                    }

                    resList.forEach(item => {
                        
                        let itemData = {
                            label: item[this.remoteInfo.parseLabel],
                            value: item[this.remoteInfo.parseValue] + '',
                            ...item
                        }

                        if(this.remoteInfo.showLabel) {
                            itemData.showLabel = item[this.remoteInfo.showLabel] || '';
                        }

                        if (this.nameKeys.length) {
                            let names = [];
                            this.nameKeys.forEach(key => {
                                if (item[key]) {
                                    names.push(item[key])
                                }
                            })

                            itemData.name = names.join(' / ');
                        } else {
                            delete itemData.name
                        }

                        list.push(itemData);
                    });
                    this.data = list;
                    this.loading = false;

                    this.$nextTick(() => {
                        if ((!this.multipleType && this.showVal) || (this.multipleType && this.showVal && this.showVal.length)) {
                            this.$refs.dropwrap.$children.forEach(item => {
                                if (item.$options.name == 'ui-option') {
                                    item.initLabel();
                                }
                            })
                        }
                    })

                    if (trigger) {
                        this.remote ? this.rotate = true : this.rotate = !this.rotate;
                        if (this.multipleType) {
                            if (!this.readonly) this.$refs.multipleInput.focus();
                        }
                    }

                }
            })
        },
        checkValid(newValue) {
            if (this.validKey && this.validValue) {
                if (this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if (validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }

            this.$refs.dropwrap.calcPosition();
        }
    }
})

Vue.component('ui-custom-search-select', {
    template: `<div class="vd-ui-custom-select-wrap">
        <div class="vd-ui-custom-placeholder" :class="{open: isShowDrop}" @click.stop="showDrop" ref="placeholder">
            <slot name="placeholder" :selected="multi ? multiSelectedObj : selected"></slot>
        </div>
        <ui-poper :show="isShowDrop" ref="dropwrap" :auto-width="true">
            <div class="vd-ui-custom-select-drop" @click.stop :style="'width:' + width ">
                <div class="vd-ui-custom-select-search" v-if="!noSearch">
                    <ui-input suffixIcon="icon-search" v-model="searchVal" @input="handlerInput" placeholder="请输入关键词搜索"></ui-input>
                </div>
                <div class="vd-ui-custom-loading-wrap" v-if="isloading">
                    <i class="vd-ui_icon icon-loading"></i>
                    <span>加载中...</span>
                </div>
                <div class="vd-ui-custom-select-list" :style="'minHeight:' + minHeight" v-else-if="showList && showList.length">
                    <div class="vd-ui-custom-select-item" v-for="(item, index) in showList" :key="index" @click="selectItem(item)">
                        <div class="item-checkbox" v-if="multi">
                            <i class="vd-ui_icon icon-checkbox1" v-if="!tempSelectedObj[item.value]"></i>
                            <i class="vd-ui_icon icon-checkbox2" v-else></i>
                        </div>
                        <div class="item-avatar" v-if="item.avatar">
                            <img :src="item.avatar" />
                        </div>
                        <div class="item-label text-line-1" v-html="item.showLabel || item.label" :title="item.label"></div>
                    </div>
                </div>
                <div class="vd-ui-custom-empty" v-else>无匹配数据</div>
                <div class="vd-ui-custom-footer" v-if="multi">
                    <ui-button type="primary" @click="handlerMultiSelect">确定</ui-button>
                    <ui-button @click="isShowDrop=false">取消</ui-button>
                </div>
            </div>
        </ui-poper>
    </div>`,
    props: {
        remoteInfo: {
            type: Object,
            default() {
                return {}
            }
        },
        list: {
            type: Array,
            default() {
                return []
            }
        },
        searchKey: {
            type: String,
            default: 'name'
        },
        avatar: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
        label: {
            type: String,
            default: ''
        },
        value: {
            type: String,
            default: ''
        },
        containHeight: {
            type: Boolean,
            default: false
        },
        multi: {
            type: Boolean,
            default: false
        },
        noSearch: {
            type: Boolean,
            default: false
        },
        default: {
            type: String,
        }
    },
    data() {
        return {
            isloading: false,
            showList: [],
            reqt: 0,
            searchVal: '',
            timeout: null,
            minHeight: '',
            selected: null,
            multiSelectedObj: {},
            tempSelectedObj: {},
            isShowDrop: false,
        }
    },
    mounted() {
        this.init();
        document.addEventListener('click', () => {
            this.isShowDrop = false;
        })

        this.showList = this.list;
    },
    watch: {
        default() {
            this.init();
        },
        isShowDrop() {
            console.log(this.isShowDrop)
        }
    },
    methods: {
        init() {
            if(this.default) {
                if(!this.multi) {
                    this.list.forEach(item => {
                        if(item.value == this.default) {
                            this.selected = item;
                        }
                    })
                }
            }
        },
        showDrop() {
            document.body.click();
            this.$emit('open')
            this.tempSelectedObj = JSON.parse(JSON.stringify(this.multiSelectedObj));
            this.$nextTick(() => {
                this.isShowDrop = true;

                this.search();
            })
        },
        hide() {
            this.isShowDrop = false;
        },
        handlerInput() {
            this.timeout && clearTimeout(this.timeout);

            this.timeout = setTimeout(() => {
                this.search()
            }, 300)
        },
        search(){
            if (this.remoteInfo.url) {
                this.isloading = true;
                let reqt = new Date().getTime();
                this.reqt = reqt;
                axios.post(this.remoteInfo.url, ["V253742", "V148722", 'V253790'], {
                    headers: {
                        reqt
                    }
                }).then((res) => {
                    let reqt = res.config.headers.reqt;
                    console.log(reqt, this.reqt)
                    if(reqt >= this.reqt) {
                        console.log('first')
                        this.isloading = false;
                        let data = res.data;
                        if(data.success) {
                            let list = [];

                            data.data.forEach(item => {
                                list.push({
                                    label: item[this.label],
                                    value: item[this.value],
                                    avatar: this.avatar ? item[this.avatar] : ''
                                })
                            })
                            this.showList = list;
                        }
                    }
                })
            } else {
                if(this.containHeight) {
                    this.minHeight = (this.list.length > 10 ? 330 : 33*this.list.length) + 'px';
                }
                let searchValue = this.searchVal.trim();

                if(!searchValue) {
                    this.showList = this.list;
                } else {
                    let reg = new RegExp('(' + searchValue + ')', 'ig');
                    let list = [];
                    this.list.forEach(item => {
                        if(item.label.toUpperCase().indexOf(searchValue.toUpperCase()) !== -1) {
                            let itemData = JSON.parse(JSON.stringify(item))
                            itemData.showLabel = itemData.label.replace(reg, '<span class="strong">$1</span>');
                            list.push(itemData);
                        }
                    })

                    this.showList = list;
                }
            }
        },
        selectItem(item) {
            if(!this.multi) {
                this.selected = item;
                this.onselect(item);
                this.isShowDrop = false;
            } else {
                if(this.tempSelectedObj[item.value]) {
                    this.$set(this.tempSelectedObj, item.value, null)
                    delete this.tempSelectedObj[item.value];
                } else {
                    this.$set(this.tempSelectedObj, item.value, item)
                }
            }
        },
        handlerMultiSelect() {
            this.multiSelectedObj = JSON.parse(JSON.stringify(this.tempSelectedObj));
            this.isShowDrop = false;
            this.onselect(this.multiSelectedObj);
        },
        onselect(data) {
            this.$emit('select', data);
        }
    }
})
// 下拉联想 - 可输入
Vue.component('ui-select-related', {
    template: `
        <div class="vd-ui-select-related">
            <div class="vd-ui-input" :style="{'width': width}">
                <ui-input
                    :type="type"
                    :maxlength="maxlength"
                    :placeholder="placeholder"
                    :disabled="disabled"
                    v-bind="$attrs"
                    v-model="inputValue"

                    @focus="focus"
                    @blur="handlerBlur"
                    @input.native="handleInput"
                    @compositionend.native="commentPress"
                    width="100%"
                    autocomplete="off"
                    :errorable="!!errorMsg" 
                    :error-msg="errorMsg" 
                ></ui-input>
            </div>

            <transition>
                <ul
                    class="vd-ui-search-related"
                    @click.stop
                    v-show="rotate"
                    :style="{'width': width}"
                >
                    <template v-if="loading">
                        <li class="loading">
                            <i class="vd-ui_icon icon-loading" ref="loading"></i>
                            <span>加载中...</span>
                        </li>
                    </template>

                    <template v-else-if="loadingFail">
                        <li class="failed-li">
                            <i class="vd-ui_icon icon-error2"></i>
                            <span>加载失败</span>
                            <span class="reload" @click="handleReload">重新加载</span>
                        </li>
                    </template>

                    <template v-else>
                        <div class="search-list" v-if="relateList.length">
                            <div class="local-data">本地数据匹配</div>
                            <div 
                                class="sr-item" 
                                v-for="(item, index) in relateList" :key="index"
                                @click.stop="chooseItem(item)"
                            >
                                <div class="sr-item-left">
                                    <p class="text-line-1" v-html="light(item[remoteInfo.parseLabel])"></p>
                                </div>
                                <div class="sr-item-right">{{ item[remoteInfo.parseLabelRight] }}</div>
                            </div>
                        </div>
                        <li class="empty-li" v-else>
                            <p>暂无数据</p>
                        </li>
                    </template>
                </ul>
            </transition>
        </div>
    `,

    props: {
        value: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        width: {
            type: String,
            default: '300px'
        },
        // input类型
        type: {
            type: String,
            default: 'text'
        },
        placeholder: {
            type: String,
            default: ''
        },
        maxlength: {
            type: [String, Number],
            default: 5000
        },
        errorMsg: {
            type: String
        },

        
        // 已建档客户id： 有id才联想，否则只做简单输入
        traderId: {
            type: [String, Number],
            default: ''
        },
        remoteInfo: {
            type: Object,
            default: () => {
                return {}
            }
            // 可选参数
            // 请求路径    url
            // 请求方式    paramsMethod: 'POST'
            // 请求入参    paramsKey: 'name',
            // 面板label   parseLabel: 'username',
            // 面板value   parseValue: 'userId',
            // 面板头像字段 parseAvatar: 'aliasHeadPicture', 不传不展示
            // 是否需要分页 - 默认第一页100条 hasPage
        },
        // 接口取值层级 - 外层控制return需要展示的数组
        remoteDataParse: {
            type: Function,
            default: ()=> {}
        },
        // 需要展示头像
        avatar: {
            type: Boolean,
            default: false
        },
        // 右侧展示内容是否需要拼接多个字段
        rightKeys: {
            type: Array,
            default() {
                return [];
            }
        },
    },
    data() {
        return {
            inputValue: '',
            // erp本地匹配
            rotate: false,
            relateList: [], // 搜索联想
            loading: false,
            loadingFail: false,
            timer: null,
        }
    },
    watch: {
        value (newV) {
            this.inputValue = newV;
        },
        rotate (newV) {
            if (!newV) {
                this.loading = false;
                this.loadingFail = false;
            }
        }
    },
    computed: {
        light () {
            return (name) => {
                if (!this.inputValue) return name;
                const regExp = new RegExp(this.inputValue, 'g');
                name = name.replace(regExp, `<font color='#FF6600'">${this.inputValue}</font>`);
                return name;
            }
        },
        tags () {
            let arr = this.companyInfo.tags.split(';') || [];
            return arr
        },
        category_Big_Middle_Small () {
            let arr = [];
            this.companyInfo.category && arr.push(this.companyInfo.category); 
            this.companyInfo.categoryBig && arr.push(this.companyInfo.categoryBig); 
            this.companyInfo.categoryMiddle && arr.push(this.companyInfo.categoryMiddle); 
            this.companyInfo.categorySmall && arr.push(this.companyInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    filters: {
        // 高亮
        filterLight (val) {
            if (!val) return '-';
            let year = new Date(val).getFullYear();
            let month = new Date(val).getMonth() + 1;
            let day = new Date(val).getDate();
            return `${year}-${month > 9? month: '0'+month}-${day > 9? day: '0'+day}`;
        },
    },
    mounted() {
        this.inputValue = this.value;
        document.addEventListener('click', (e)=>{
            if (!this.$el.contains(e.target)) {
                this.rotate = false;
            }
        })
    },
    methods: {
        focus () {
            // this.clearFlag = this.clearValue ? true : false;
            // this.getSearchList(this.inputValue);
        },
        handlerBlur () {
            this.$emit('blur');
        },

        // 输入停顿300毫秒后搜素
        handleInput (e) {
            if (e.inputType != 'insertCompositionText') {
                this.$emit('input', e.target.value);
                this.$emit('change', {});

                this.timer && clearTimeout(this.timer);
                this.timer = setTimeout(()=> {
                    this.getSearchList(e.target.value);
                }, 300);
            }
        },
        commentPress(e) {
            if (e.target.value) {
                this.$emit('input', e.target.value);

                this.timer && clearTimeout(this.timer);
                this.timer = setTimeout(()=> {
                    this.getSearchList(e.target.value);
                }, 300);
            }
        },
        getSearchList (str) {
            console.log('搜索', str);
            if (this.remoteInfo.url) {
                let value = str.trim();
                this.rotate = true;
                this.loading = true;
                this.loadingFail = false;

                let params = {};
                let url = this.remoteInfo.url;
                let reqMethod = this.remoteInfo.paramsMethod || 'post';

                if (this.remoteInfo.paramsType == 'url') { // get入参
                    if(this.remoteInfo.url.indexOf('?') !== -1) {
                        url += '&' + this.remoteInfo.paramsKey + '=' + value;
                    } else {
                        url += '?' + this.remoteInfo.paramsKey + '=' + value;
                    }
                } else { // post入参
                    params[this.remoteInfo.paramsKey] = value;

                    // 分页
                    if (this.remoteInfo.hasPage) {
                        params['pageNum'] = 1;
                        params['pageSize'] = 100;
                    }
                }


                let reqData = {
                    url, 
                    method: reqMethod,
                }

                if (reqMethod == 'get') {
                    reqData.params = params
                } else if (reqMethod == 'post') {
                    reqData.data = params;
                }

                // 增加timestramp时间戳 判断最新一次 ???
                axios(reqData).then(({ data }) => {
                    if (data.code === 0) {
                        let list = [];
                        let resList = data.data;

                        let remoteParseData = this.remoteDataParse(resList)
                        if (remoteParseData) {
                            resList = remoteParseData;
                        }

                        resList.forEach(item => {
                            let itemData = {
                                avatar: this.avatar ? item[this.remoteInfo.parseAvatar] : '',
                                ...item
                            }

                            if(this.rightKeys.length) {
                                let names = [];
                                this.rightKeys.forEach(key => {
                                    if(item[key]) {
                                        names.push(item[key])
                                    }
                                })
    
                                itemData.rightContent = names.join(' / ');
                            }
    
                            list.push(itemData);
                        });
                        this.relateList = list;
                        this.loading = false;
                    } else {
                        this.loadingFail = true;
                    }
                }).catch(err=> {
                    this.loading = false;
                    this.loadingFail = true;
                })
            }
        },

        // 重新加载
        handleReload () {
            this.getSearchList(this.inputValue);
        },

        // 选择已建档客户
        async chooseItem (item) {
            console.log('item：', item);

            this.inputValue = item[this.remoteInfo.parseLabel] || '';
            this.rotate = false;

            this.$emit("input", item[this.remoteInfo.parseValue] || '');
            this.$emit('change', Object.assign({}, item, {isChoosed: true}));
        },
    }
})
Vue.component('ui-tab', {
    template: `<div class="vd-ui-tab">
        <div
            class="vd-ui-tab-list"
            :class="{ 'vd-ui-tab-list-more': type === 'with-more' }"
            ref="tabList"
        >
            <div
                class="vd-ui-tab-item"
                :class="[
                    { 'vd-ui-tab-item-active': item.id === current },
                    { 'vd-ui-tab-item-line': type === 'with-line' },
                ]"
                v-for="(item, index) in list"
                :key="index"
                @click="handlerChangeCurrent(item)"
                :ref="'tabItem' + item.id"
                v-show="!hiddenList['hide' + item.id]"
            >
                <div class="vd-ui-tab-item-inner">
                    <div class="vd-ui-tab-item-txt">
                        {{ item.label }}
                    </div>
                    <div v-if="item.isCustom" class="vd-ui-tab-custom-wrap"> 
                        <i class="vd-ui_icon icon-app-more" @click.stop="showCustomOption(index)"></i>
                        <div class="vd-ui-tab-custom-option" @click.stop v-show="index == customActiveIndex">
                            <div class="vd-ui-tab-custom-option-item" @click="customOption(item, 'rename')">重命名</div>
                            <div class="vd-ui-tab-custom-option-item red" @click="customOption(item, 'delete')">删除</div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="vd-ui-tab-item vd-ui-tab-item-more"
                @mouseenter="isHideMore = false"
                @mouseleave="isHideMore = true"
                :class="[
                    {
                        'vd-ui-tab-item-more-active':
                            hiddenList['hide' + current],
                    },
                ]"
                v-show="Object.keys(hiddenList).length"
            >
                <span class="vd-ui-tab-item-txt">{{
                    hiddenList['hide' + current]
                        ? hiddenList['hide' + current].label
                        : "更多"
                }}</span>
                <i class="vd-ui_icon icon-down" :class="{hover: !isHideMore}"></i>
                <div
                    class="vd-ui-tab-more-list"
                    ref="moreList"
                    v-show="!isHideMore"
                >
                    <template v-for="(item, index) in hiddenList">
                        <div
                            class="vd-ui-tab-more-item"
                            :key="index"
                            @click="handlerChangeCurrent(item)"
                        >
                            {{ item.label }}
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <div
            class="vd-ui-tab-bar"
            :style="'width:' + barWidth + 'px;left:' + barLeft + 'px;'"
            v-show="isShowTabBar && type !== 'with-line'"
        ></div>
    </div>`,
    data() {
        return {
            barWidth: null, //下滑线宽
            barLeft: null, //下滑线左定位
            current: null,
            list: [],
            hiddenList: {},
            isShowTabBar: false,
            isHideMore: true,
            customActiveIndex: -1
        };
    },
    props: {
        tabList: {
            type: Array,
            default: () => [],
        },
        active: {
            type: Number,
            default: 0,
        },
        type: {
            type: String,
            default: "normal", //normal,with-more,with-line
        },
    },
    watch: {
        current() {
            this.$nextTick(() => {
                this.checkShowTabBar();
            });
        },
    },
    mounted() {
        this.parseList();
        this.current = this.active;
        if (this.type === "with-more") {
            this.getItemWidth();

            window.addEventListener('resize', () => {
                this.calcTabShow();
                this.checkShowTabBar();
                this.calcBarStyle();
            })
        }
        this.calcBarStyle();
        document.onclick = () => {
            this.customActiveIndex = -1;
        }
    },
    methods: {
        checkShowTabBar() {
            if (this.hiddenList[`hide${this.current}`]) {
                this.isShowTabBar = false;
            } else {
                setTimeout(() => {
                    this.isShowTabBar = true;
                }, 50);
            }
        },
        getItemWidth() {
            this.$nextTick(() => {
                this.list.forEach((item) => {
                    item.width = this.$refs[`tabItem${item.id}`][0].offsetWidth;
                    item.left = this.$refs[`tabItem${item.id}`][0].offsetLeft;
                });

                this.calcTabShow();
                this.checkShowTabBar();
            });
        },
        parseList() {
            let list = this.tabList;

            list.forEach((item, index) => {
                item.id = item.id || index;
            });

            this.list = this.tabList;
        },
        calcTabShow() {
            let tabWidth = this.$refs.tabList.offsetWidth;
            let showWidth = 0;
            let hideObj = {};
            this.list.forEach((item) => {
                showWidth += item.width + 40;
                let moreWidth = 102;
                if (this.hiddenList[`hide${this.current}`]) {
                    moreWidth =
                        60 + this.hiddenList[`hide${this.current}`].width;
                }
                if (showWidth > tabWidth - moreWidth) {
                    hideObj[`hide${item.id}`] = item;
                }
            });

            this.hiddenList = hideObj;
        },
        calcBarStyle() {
            this.$nextTick(() => {
                if(this.$refs[`tabItem${this.current}`]){
                    let currentDom = this.$refs[`tabItem${this.current}`][0];
                    this.barLeft = currentDom.offsetLeft;
                    this.barWidth = currentDom.offsetWidth;
                }
            });
        },
        handlerChangeCurrent(item) {
            this.current = item.id;
            if (this.type === "with-more") {
                this.calcTabShow();
                this.isHideMore = true;
            }
            this.calcBarStyle();
            this.$emit("change", item.id, item);
        },
        showCustomOption(index) {
            this.customActiveIndex = index;
        },
        customOption(item, type) {
            this.customActiveIndex = -1;
            this.$emit('customOption', {
                item,
                type
            })
        }
    },
})
Vue.component('ui-table', {
    template: `<div class="vd-ui-table-wrap" v-if="!isLoading">
        <div class="vd-ui-table-container" :class="{'vd-table-width-border': widthBorder}" ref="container">
            <div
                class="vd-ui-table-header"
                v-if="scroll || isHeaderFixed"
                :class="{
                    'vd-ui-wrap-is-left': scroll && leftFixed,
                    'vd-ui-wrap-is-right': scroll && rightFixed,
                    'vd-ui-wrap-right-scroll-fixed': scroll && rightFixed && scrollBarWidth,
                    'header-fixed': isHeaderFixed
                }"
                :style="isHeaderFixed ? 'width:' + (widthBorder ? containerWidth - 2 : containerWidth) + 'px;top:' + fixedTop + 'px;' : ''"
                ref="header"
            >
                <table
                    class="vd-ui-table"
                    :class="{
                        'vd-ui-table-left-fixed': scroll && isLeftFixed,
                        'vd-ui-table-right-fixed': scroll && isRightFixed,
                    }"
                    :style="tableStyle"
                    ref="table"
                >
                    <colgroup>
                        <template v-if="canChoose">
                            <col :width="customList || tableSize === 'small' ? '36px' : '56px'"/>
                        </template>
                        <template v-for="(item, index) in headers">
                            <col :key="index" :width="item.width || ''" />
                        </template>
                        <template v-if="scrollBarWidth">
                            <col :width="scrollBarWidth + 'px'" />
                        </template>
                    </colgroup>
                    <thead>
                        <tr class="vd-ui-tr">
                            <th class="vd-ui-th" :class="{'sticky-item': leftFixedNumber, 'last-fixed': leftFixedNumber == 1}" v-if="canChoose">
                                <div class="vd-ui-checkbox-wrap">
                                    <ui-checkbox :checked.sync="isSelectedAll" @change="selectAllChange"></ui-checkbox>
                                </div>
                            </th>
                            <template v-for="(item, index) in headers">
                                <th
                                    class="vd-ui-th"
                                    :key="index"
                                    :class="{
                                        'vd-ui-th-bar-prev':
                                            scrollBarWidth &&
                                            index == headers.length - 1,
                                        'vd-ui-th-sortable': item.sortable,
                                        'sticky-item': canChoose ? leftFixedNumber - 1 > index : leftFixedNumber > index,
                                        'last-fixed': canChoose ? leftFixedNumber - 2 == index : leftFixedNumber - 1 == index,
                                        'align-right': item.align === 'right',
                                        'with-info': item.tipinfo
                                    }"
                                    :style="
                                        (rightFixed
                                            ? scrollBarWidth &&
                                            index == headers.length - 1
                                                ? 'right:' +
                                                (scrollBarWidth +
                                                    (isRightFixed ? 1 : 0)) +
                                                'px;padding-left:' +
                                                (10 +
                                                    (isRightFixed ? 1 : 0)) +
                                                'px'
                                                : ''
                                            : '') + stickyStyle(index)
                                    "
                                >
                                    <template v-if="item.sortable || item.filter">
                                        <div class="vd-ui-table-th-cnt">
                                            <div class="vd-ui-table-th-inner" @click="sortItem(item)">
                                                <div class="vd-ui-table-th-txt">{{ item.label }}</div>
                                                <div
                                                    class="vd-ui-table-sort"
                                                    v-if="item.sortable"
                                                >
                                                    <span
                                                        class="vd-ui-sort-icon"
                                                        :class="{
                                                            'vd-ui-sort-icon-active':
                                                                sortObj.key === item.key &&
                                                                sortObj.sort === 'up',
                                                        }"
                                                    ></span>
                                                    <span
                                                        class="
                                                            vd-ui-sort-icon
                                                            vd-ui-sort-icon-down
                                                        "
                                                        :class="{
                                                            'vd-ui-sort-icon-active':
                                                                sortObj.key === item.key &&
                                                                sortObj.sort === 'down',
                                                        }"
                                                    ></span>
                                                </div>
                                            </div>
                                            <div class="vd-ui-table-filter" :class="{active:filterObj.key === item.key, selected: hasFilterObj[item.key]}" v-if="item.filter">
                                                <i class="vd-ui_icon icon-filter" @click.stop="showFilterItem(item, $event)"></i>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else-if="item.tipinfo">
                                        <div class="ui-th-tip-wrap">
                                            {{ item.label }}
                                            <ui-tip position="r" icon="info2" width="500px">{{ item.tipinfo }}</ui-tip>
                                        </div>
                                    </template>
                                    <template v-else>
                                        {{ item.label }}
                                    </template>
                                    <div
                                        class="vd-ui-table-setting"
                                        @click="handlerSetting"
                                        :class="{
                                            'vd-ui-table-setting-right':
                                                scrollBarWidth,
                                        }"
                                        v-if="
                                            setting &&
                                            index == headers.length - 1
                                        "
                                    >
                                        <i
                                            class="
                                                vd-ui-table-setting-icon vd-ui_icon
                                                icon-app-more
                                            "
                                        ></i>
                                    </div>
                                </th>
                            </template>
                            <template v-if="scrollBarWidth">
                                <th class="vd-ui-th vd-ui-th-bar"></th>
                            </template>
                        </tr>
                    </thead>
                </table>
            </div>
            <div class="placeolder" v-if="isHeaderFixed && scroll" style="height:38px;width:100%"></div>
            <div
                class="vd-ui-table-body"
                :style="'max-height:'+containerHeight"
                :class="{
                    'vd-ui-wrap-is-left': leftFixed,
                    'vd-ui-wrap-is-right': rightFixed,
                    'vd-ui-wrap-scroll': containerHeight,
                }"
                @scroll="handlerTableTrueScroll"
                ref="body"
            >
                <table
                    class="vd-ui-table"
                    :class="{
                        'vd-ui-table-left-fixed': isLeftFixed,
                        'vd-ui-table-right-fixed': isRightFixed,
                    }"
                    :style="'width:' + (scroll && !(!autoScroll && containerHeight) ? tableWidth + 'px' : '')"
                    ref="table"
                >
                    <colgroup>
                        <template v-if="canChoose">
                            <col :width="customList || tableSize === 'small' ? '36px' : '56px'"/>
                        </template>
                        <template v-for="(item, index) in headers">
                            <col :key="index" :width="item.width || ''" />
                        </template>
                    </colgroup>
                    <thead v-if="!scroll">
                        <tr class="vd-ui-tr">
                            <th class="vd-ui-th" :class="{'sticky-item': leftFixedNumber, 'last-fixed': leftFixedNumber == 1}" v-if="canChoose">
                                <div class="vd-ui-checkbox-wrap">
                                    <ui-checkbox :checked.sync="isSelectedAll" @change="selectAllChange"></ui-checkbox>
                                </div>
                            </th>
                            <template v-for="(item, index) in headers">
                                <th
                                    class="vd-ui-th"
                                    :class="{
                                        'vd-ui-th-sortable': item.sortable,
                                        'sticky-item': canChoose ? leftFixedNumber - 1 > index : leftFixedNumber > index,
                                        'last-fixed': canChoose ? leftFixedNumber - 2 == index : leftFixedNumber - 1 == index,
                                        'align-right': item.align === 'right',
                                        'with-info': item.tipinfo
                                    }"
                                    :key="index"
                                    :style="stickyStyle(index)"
                                >   
                                    <template v-if="item.sortable || item.filter">
                                        <div class="vd-ui-table-th-cnt">
                                            <div class="vd-ui-table-th-inner" @click="sortItem(item)">
                                                <div class="vd-ui-table-th-txt">{{ item.label }}</div>
                                                <div
                                                    class="vd-ui-table-sort"
                                                    v-if="item.sortable"
                                                >
                                                    <span
                                                        class="vd-ui-sort-icon"
                                                        :class="{
                                                            'vd-ui-sort-icon-active':
                                                                sortObj.key === item.key &&
                                                                sortObj.sort === 'up',
                                                        }"
                                                    ></span>
                                                    <span
                                                        class="
                                                            vd-ui-sort-icon
                                                            vd-ui-sort-icon-down
                                                        "
                                                        :class="{
                                                            'vd-ui-sort-icon-active':
                                                                sortObj.key === item.key &&
                                                                sortObj.sort === 'down',
                                                        }"
                                                    ></span>
                                                </div>
                                            </div>
                                            <div class="vd-ui-table-filter" :class="{active:filterObj.key === item.key, selected: hasFilterObj[item.key]}" v-if="item.filter">
                                                <i class="vd-ui_icon icon-filter"  @click.stop="showFilterItem(item, $event)"></i>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else-if="item.tipinfo">
                                        <div class="ui-th-tip-wrap">
                                            {{ item.label }}
                                            <ui-tip position="r" icon="info2" width="500px">{{ item.tipinfo }}</ui-tip>
                                        </div>
                                    </template>
                                    <template v-else>
                                        {{ item.label }}
                                    </template>
                                    <div
                                        class="vd-ui-table-setting"
                                        @click="handlerSetting"
                                        :class="{
                                            'vd-ui-table-setting-right':
                                                scrollBarWidth,
                                        }"
                                        v-if="
                                            setting &&
                                            index == headers.length - 1
                                        "
                                    >
                                        <i
                                            class="
                                                vd-ui-table-setting-icon vd-ui_icon
                                                icon-app-more
                                            "
                                        ></i>
                                    </div>
                                </th>
                            </template>
                        </tr>
                    </thead>
                    <tbody>
                        <template v-if="tableList.length">
                            <template v-for="(tr, index) in tableList">
                                <tr class="vd-ui-tr" :key="index" :class="{'on-select': index == tableSelectedIndex}" @click="tableSelectedIndex = index">
                                    <td class="vd-ui-td vertical-center" v-if="canChoose" :class="{'sticky-item': leftFixedNumber, 'last-fixed': leftFixedNumber == 1}">
                                        <slot name="left_tip" v-bind:row="tr"></slot>
                                        <div class="vd-ui-checkbox-wrap">
                                            <ui-checkbox :checked.sync="tr.checked" :disabled="tr.disabled" @change="checkSelectedAll"></ui-checkbox>
                                        </div>
                                    </td>
                                    <template v-if="customList">
                                        <slot
                                            name="tr"
                                            v-bind:row="tr"
                                        ></slot>
                                    </template>
                                    <template v-else>
                                        <template
                                            v-for="(header, kIndex) in headers"
                                        >
                                            <td 
                                                class="vd-ui-td" 
                                                :class="{
                                                    'sticky-item': canChoose ? leftFixedNumber - 1 > kIndex : leftFixedNumber > kIndex,
                                                    'last-fixed': canChoose ? leftFixedNumber - 2 == kIndex : leftFixedNumber - 1 == kIndex,
                                                    'can-edit': header.edit,
                                                    'align-right': header.align === 'right',
                                                    'vertical-center': header.vertical === 'center'
                                                }" 
                                                :key="kIndex" 
                                                :style="stickyStyle(kIndex)"
                                            >
                                                <template
                                                    v-if="!customSlot[header.key.toLowerCase()]"
                                                >
                                                    <template v-if="header.avatar">
                                                        <div class="avatar-item-wrap" v-if="tr[header.key]">
                                                            <div class="avatar-img">
                                                                <img :src="tr[header.avatar]" onerror="this.classList.add('error')"/>
                                                            </div>
                                                            <div class="item-text" :class="{'text-line-1': oneline}" :title="tr[header.key]">{{tr[header.key]}}</div>
                                                        </div>
                                                        <template v-else>-</template>
                                                    </template>
                                                    <template v-else-if="header.tel">
                                                        <div class="tel-item-wrap" :class="{normal: !layout_hidden_value}" v-if="tr[header.key]" @click="callNumber(tr[header.key], tr)">
                                                            <i class="vd-ui_icon icon-call"></i>
                                                            <div class="item-text" :class="{'text-line-1': oneline}" :title="tr[header.key]">{{tr[header.key]}}</div>
                                                        </div>
                                                        <template v-else>-</template>
                                                    </template>
                                                    <template v-else-if="oneline">
                                                        <span class="text-line-1" :title="tr[header.key]">{{ tr[header.key] || "-" }}</span>
                                                    </template>
                                                    <template v-else>
                                                        {{ tr[header.key] || "-" }}
                                                    </template>
                                                </template>
                                                <template v-else>
                                                    <slot
                                                        :name="header.key.toLowerCase()"
                                                        v-bind:row="tr"
                                                    ></slot>
                                                </template>
                                                <template  v-if="header.edit">
                                                    <div class="ui-table-td-edit-wrap" @click="handlerTableEdit(tr, header.key, $event)" v-if="!editValid || (editValid && editValid(tr))">
                                                        <i class="vd-ui_icon icon-edit"></i>
                                                    </div>
                                                    <div class="ui-table-td-edit-wrap disabled" :title="editValidMsg" v-else>
                                                        <i class="vd-ui_icon icon-edit"></i>
                                                    </div>
                                                </template>
                                            </td>
                                        </template>
                                    </template>
                                </tr>
                            </template>
                        </template>
                    </tbody>
                </table>
            </div>
            <div class="ui-table-list-empty" v-if="!tableList || !tableList.length">
                <template v-if="customEmpty">
                    <slot name="customempty"></slot>
                </template>
                <template v-else>
                    <div class="empty-img"></div>
                    <div class="empty-txt">暂无数据</div>
                </template>
            </div>
        </div>
        <div class="ui-table-footer-scroll" :class="{ hidden: !isFooterFixed }" ref="footerBar" @scroll="footerBarScroll" :style="'width:' + containerWidth + 'px;'">
            <div class="ui-table-footer-scroll-inner" :style="'width:' + tableWidth + 'px;'"></div>
        </div>
        <div class="ui-table-filter-drop J-table_drop_wrap" v-show="filterList && filterList.length" @click.stop ref="tableFilterDrop" :style="'left:' + dropX + 'px;top:' + dropY + 'px;'">
            <div class="ui-table-filter-drop-search">
                <ui-input placeholder="请输入关键词搜索" @input="handlerDropSearch" v-model="filterDropSearchValue" suffixIcon="icon-search"></ui-input>
            </div>
            <div class="ui-table-filter-drop-list" v-if="filerShowList.length">
                <div class="ui-table-filter-drop-inner">
                    <div class="ui-table-filter-item">
                        <ui-checkbox :checked.sync="isFilterSelectedAll" :label="filterSelectedAllTitle" @change="hadlerFilerChangeAll"></ui-checkbox>
                    </div>                               
                    <div class="ui-table-filter-item" v-for="(item, index) in filerShowList":key="index">
                        <ui-checkbox :checked.sync="item.checked" :label="item.showLabel || item.label" @change="checkFilterSelectAll"></ui-checkbox>
                    </div>
                </div>
            </div>
            <div class="ui-table-filter-drop-empty" v-else>暂无匹配数据</div>
            <div class="ui-table-filter-drop-footer">
                <ui-button type="primary" @click="setFilterData">确定</ui-button>
                <ui-button @click="clearDrop">取消</ui-button>
            </div>
        </div>
    </div>`,
    data() {
        return {
            isLeftFixed: false,
            isRightFixed: false,
            customSlot: {},
            tableWidth: 0,
            scrollBarWidth: 0,
            sortObj: {},
            filterObj: {},
            filterList: [],
            filerShowList: [],
            hasFilterObj: {},
            scroll: false,
            tableList: [],
            isSelectedAll: false,
            isLoading: true,
            isHeaderFixed: false,
            isFooterFixed: false,
            containerWidth: 0,
            leftFixed: true,
            currentDropTarget: null, //当前表头筛选的Dom target，计算定位用
            dropX: 0, //筛选下拉定位left
            dropY: 0, //筛选下拉定位top
            isFilterSelectedAll: true,
            filterSelectedAllTitle: '全选',
            filterDropSearchValue: '',
            tableSelectedIndex: -1 //点击单行选中样式
        };
    },
    props: {
        list: {
            type: Array,
            default: () => {
                return [];
            },
        },
        fixedTop: {
            type: Number,
            default: 0,
        },
        headers: {
            type: Array,
            default: () => {
                return [];
            },
        },
        leftFixedNumber: {
            type: Number,
            default: 1,
        },
        rightFixed: {
            type: Boolean,
            default: false,
        },
        autoScroll: {
            type: Boolean,
            default: true,
        },
        containerHeight: {
            type: String,
            default: "",
        },
        setting: {
            type: Boolean,
            default: false,
        },
        canChoose: {
            type: Boolean,
            default: false,
        },
        //是否开启列表模式。开启后，表头会吸顶，同时页面下方会有悬浮滚动条。
        isFullPage: {
            type: Boolean,
            default: false
        },
        widthBorder: {
            type: Boolean,
            default: false
        },
        //表格数据是不是都是一行展示
        oneline: {
            type: Boolean,
            default: false
        },
        //自定义单行内容
        customList: {
            type: Boolean,
            default: false
        },
        //是否全局排序
        listSort: {
            type: Boolean,
            default: true
        },
        editValid: {
            type: Function,
            default: null
        },
        editValidMsg: {
            type: String,
            default: ''
        },
        tableSize: {
            type: String,
            default: 'large'
        },
        // 自定义空样式
        customEmpty: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        tableStyle() {
            if (this.scroll) {
                if (!this.autoScroll && this.containerHeight) {
                    return `width:${this.containerWidth}px;table-layout: fixed;`
                } else {
                    return `width:${this.tableWidth}px;table-layout: fixed;`
                }
            } else {
                return '';
            }
        }
    },
    watch: {
        list() {
            this.initListData();
        }
    },
    create() {
    },
    mounted() {
        let cunstomObj = {};
        for (let item in this.$scopedSlots) {
            cunstomObj[item] = 1;
        }
        this.customSlot = cunstomObj;


        this.initListData();

        if (this.canChoose) {
            this.checkSelectedAll();
        }

        this.isLoading = false;

        this.$nextTick(() => {
            this.initTable();
            if (this.autoScroll) {
                window.addEventListener('resize', this.checkTableScroll)
                window.addEventListener('scroll', this.checkBodyScroll)
            }
            document.addEventListener('click', () => {
                this.clearDrop();
            })
            this.checkScrollBarWidth();
        })

        // if (this.scroll) {
        //     this.handlerTableScroll();
        // }
    },
    methods: {
        initListData() {
            this.list.forEach((item, index) => {
                item.listUiIndex = index;
                if (this.canChoose) {
                    item.checked = item.checked || false;
                    item.disabled = item.disabled || false;
                }

                this.checkSelectedAll();
            })

            this.tableList = JSON.parse(JSON.stringify(this.list));
            this.tableSelectedIndex = -1;

            if(Object.keys(this.hasFilterObj).length) {
                this.getFilterList();
            } 

            if(Object.keys(this.sortObj).length) {
                this.sortItem(this.sortObj.item, 1);
            }

            this.$nextTick(() => {
                this.initTable()
            })
        },
        stickyStyle(index) {
            if (this.isLeftFixed) {
                if (this.canChoose) {
                    if (this.leftFixedNumber == 1) {
                        return "";
                    }

                    if (index == 0) {
                        return this.customList || this.tableSize === 'small' ? 'left: 36px' : 'left: 56px';
                    } else if (index < this.leftFixedNumber - 1) {
                        return 'left:' + (parseInt(this.headers[index - 1].width.replace('px', '')) + (this.customList || this.tableSize === 'small' ? 36 : 56)) + 'px';
                    }
                } else if (!this.canChoose && index > 0 && index < this.leftFixedNumber) {
                    return 'left:' + parseInt(this.headers[index - 1].width.replace('px', '')) + 'px';
                }

                return ''
            }

            return ''
        },
        initTable() {
            this.setTableWidth();
            if (this.autoScroll) {
                this.checkTableScroll();
                this.checkSelectedAll();
                setTimeout(() => {
                    this.checkBodyScroll();
                })
            }
            if (this.containerHeight && !this.autoScroll) {
                this.scroll = true;
                this.containerWidth = this.$refs.container.offsetWidth;
            }
        },
        checkSelectedAll() {
            let flag = true;

            if (!this.tableList.length) {
                flag = false;
            } else {
                this.tableList.forEach(item => {
                    if (!item.disabled && !item.checked) {
                        flag = false;
                    }
                })
            }

            this.isSelectedAll = flag;
            this.$forceUpdate();

            this.handlerSelectChange();
        },
        selectAllChange() {
            this.tableList.forEach(item => {
                if(!item.disabled) {
                    item.checked = this.isSelectedAll;
                }
            })
            this.$forceUpdate();
            this.handlerSelectChange();
        },
        handlerSelectChange() {
            this.$emit("selectchange", this.getSelectData());
        },
        getSelectData() {
            let selected = [];
            this.tableList.forEach(item => {
                if (item.checked) {
                    selected.push(item);
                }
            })

            return selected;
        },
        handlerTableTrueScroll(){
            this.$emit('truescroll')
            this.handlerTableScroll();
        },
        handlerTableScroll(e) {
            if (this.scroll) {
                this.$nextTick(() => {
                    let target = (e && e.target) || this.$refs.body;
                    if (this.leftFixedNumber) {
                        this.isLeftFixed = target.scrollLeft > 0;
                    }

                    if (this.rightFixed) {
                        this.isRightFixed =
                            target.offsetWidth +
                            target.scrollLeft -
                            this.scrollBarWidth <
                            this.$refs.table.offsetWidth - 2;
                    }

                    this.$refs.header.scrollLeft = target.scrollLeft;
                    this.$refs.footerBar.scrollLeft = target.scrollLeft;
                });
                this.$emit('tablescroll')
            }
            this.clearDrop();

        },
        checkTableScroll() {
            if (!this.$refs.container) {
                return;
            }
            let containerWidth = this.$refs.container.offsetWidth;
            this.containerWidth = containerWidth;

            if (containerWidth >= this.tableWidth) {
                this.scroll = false;
                this.isLeftFixed = false;
                this.isRightFixed = false;
            } else {
                this.scroll = true;
                this.handlerTableScroll();
            }
            this.clearDrop();
        },
        checkBodyScroll() {
            let containerTop = this.$refs.container.getBoundingClientRect().top;
            let containerBottom = this.$refs.container.getBoundingClientRect().bottom;
            console.log(this.fixedTop)
            if (containerTop < this.fixedTop) {
                if (!this.isHeaderFixed) {
                    this.clearDrop()
                }
                this.isHeaderFixed = true;
            } else {
                if (this.isHeaderFixed) {
                    this.clearDrop()
                }
                this.isHeaderFixed = false;
            }

            if (this.tableList.length && containerBottom > window.innerHeight + 10 && containerTop < window.innerHeight - 90) {
                this.isFooterFixed = true;
            } else {
                this.isFooterFixed = false;
            }

            this.calcDropPotion();
        },
        setTableWidth() {
            let width = 0;
            this.headers.forEach((item, index) => {
                width += parseInt(item.width);
            });

            if (this.canChoose) {
                width += (this.customList || this.tableSize === 'small' ? 36 : 56);
            }

            this.tableWidth = width;
        },
        checkScrollBarWidth() {
            this.scrollBarWidth =
                this.$refs.body.offsetWidth - this.$refs.body.clientWidth;

                console.log(this.$refs.body.offsetWidth, this.$refs.body.clientWidth)
        },
        handlerSetting() {
            this.$emit("handlerSetting");
        },
        sortItem(item, refresh) {
            if (!item.sortable) {
                return false;
            }

            let sortObj = JSON.parse(JSON.stringify(this.sortObj));

            if(!refresh == 1) {
                if (sortObj.key !== item.key) {
                    sortObj.sort = "down";
                } else {
                    if (!sortObj.sort) {
                        sortObj.sort = "down";
                    } else if (sortObj.sort === "up") {
                        sortObj.sort = "";
                    } else if (sortObj.sort === "down") {
                        sortObj.sort = "up";
                    }
                }
            }


            if(!this.listSort) {
    
                let sortType = item.sortType || 'normal';
    
                if (!sortObj.sort) {
                    this.tableList = this.tableList.sort((a, b) => {
                        return a.listUiIndex - b.listUiIndex;
                    })
                } else {
                    if (sortType == 'normal') {
                        this.tableList = this.tableList.sort((a, b) => {
                            if (sortObj.sort === "up") {
                                return (a[item.sortId || item.key] || '').toString().localeCompare((b[item.sortId || item.key] || '').toString());
                            } else {
                                return (b[item.sortId || item.key] || '').toString().localeCompare((a[item.sortId || item.key] || '').toString());
                            }
                        })
                    } else if (sortType == 'number') {
                        this.tableList = this.tableList.sort((a, b) => {
                            if (sortObj.sort === "up") {
                                return parseFloat(a[item.sortId || item.key]) - parseFloat(b[item.sortId || item.key]);
                            } else {
                                return parseFloat(b[item.sortId || item.key]) - parseFloat(a[item.sortId || item.key]);
                            }
                        })
                    } else if (sortType == 'time') {
                        this.tableList = this.tableList.sort((a, b) => {
                            if (sortObj.sort === "up") {
                                return new Date(a[item.sortId || item.key]).getTime() - new Date(b[item.sortId || item.key]).getTime();
                            } else {
                                return new Date(b[item.sortId || item.key]).getTime() - new Date(a[item.sortId || item.key]).getTime();
                            }
                        })
                    } else if (sortType == 'SABC') {
                        this.tableList = this.tableList.sort((a, b) => {
                            const level = {
                                S: 1,
                                A: 2,
                                B: 3,
                                C: 4
                            };
    
                            if (sortObj.sort === "up") {
                                return (level[b[item.key]] || 99) - (level[a[item.key]] || 99)
                            } else {
                                return (level[a[item.key]] || 99) - (level[b[item.key]] || 99)
                            }
                        })
                    } else if (sortType == 'address') {
                        this.tableList = this.tableList.sort((a, b) => {
                            let aValue = a[item.sortId || item.key] || '';
                            let bValue = b[item.sortId || item.key] || '';

                            let aAreaId = parseInt(aValue ? aValue.split(',')[2] : '0');
                            let bAreaId = parseInt(bValue ? bValue.split(',')[2] : '0');
    
                            if (sortObj.sort === "up") {
                                return aAreaId - bAreaId;
                            } else {
                                return bAreaId - aAreaId;
                            }
                        })
                    }
                }
            }

            sortObj.key = item.key;
            sortObj.item = item;
            if(!refresh == 1) {
                this.sortObj = sortObj;
                this.$emit("handlersort", this.sortObj);
            }
        },
        clearSort(){
            this.sortObj = {};
        },
        clearFilter(){
            this.filterObj = {};
            this.filterList = [];
            this.filerShowList = [];
            this.hasFilterObj = {};
        },
        showFilterItem(item, e) {
            let isSlef = this.filterObj.key == item.key;
            this.clearDrop();

            if (!isSlef) {
                let filterSet = new Set();
                this.list.forEach(data => {
                    if (item.filterType === 'array') {
                        let list = data[item.key] || [];

                        if(list.length) {
                            list.forEach(listItem => {
                                filterSet.add(listItem[item.filterKey]);
                            })
                        } else {
                            filterSet.add('(空值)')
                        }
                    } else {
                        if (data[item.key]) {
                            filterSet.add(data[item.key])
                        } else {
                            filterSet.add('(空值)')
                        }
                    }
                })

                let keyFilterObj = this.hasFilterObj[item.key];

                let filterList = []
                filterSet.forEach(label => {
                    filterList.push({
                        label: label,
                        checked: keyFilterObj ? !!keyFilterObj.data[label] : true
                    })
                })

                this.filterObj = item;
                this.filterList = filterList;
                this.currentDropTarget = e.target;

                this.calcDropPotion();

                this.filerShowList = this.filterList;
                this.checkFilterSelectAll();

                document.body.append(this.$refs.tableFilterDrop);
            }
        },
        calcDropPotion() {
            if (!this.currentDropTarget) {
                return;
            }
            let targetPosition = this.currentDropTarget.getBoundingClientRect();

            let winTop = window.scrollY;
            let winLeft = window.scrollX;

            if (window.innerWidth - targetPosition.x > 320) {
                this.dropX = winLeft + targetPosition.x;
            } else {
                this.dropX = winLeft + targetPosition.x - 300 + targetPosition.width;
            }

            this.dropY = winTop + targetPosition.y + 37;
        },
        footerBarScroll() {
            let scrollLeft = this.$refs.footerBar.scrollLeft;

            this.$refs.body.scrollLeft = scrollLeft;
            this.handlerTableScroll();
        },
        hadlerFilerChangeAll() {
            this.filerShowList.forEach(item => {
                item.checked = this.isFilterSelectedAll;
            })
        },
        checkFilterSelectAll() {
            let flag = true;

            this.filerShowList.forEach(item => {
                if (!item.checked) {
                    flag = false;
                }
            })

            this.isFilterSelectedAll = flag;
        },
        handlerDropSearch() {
            let value = this.filterDropSearchValue.trim();

            if (value) {
                let searchList = [];

                this.filterList.forEach(item => {
                    if (item.label.toUpperCase().indexOf(value.toUpperCase()) != -1) {
                        // let searchItem = JSON.parse(JSON.stringify(item));
                        let searchItem = item;
                        let reg = new RegExp('(' + value + ')', 'ig');
                        searchItem.showLabel = item.label.replace(reg, '<span class="strong">$1</span>');
                        searchList.push(searchItem);
                    }
                })

                this.filerShowList = searchList;
                this.filterSelectedAllTitle = "选中全部搜索项";
            } else {
                this.filterList.forEach(item => {
                    item.showLabel = '';
                })
                this.filterSelectedAllTitle = "全部";
                this.filerShowList = this.filterList;
            }

            this.checkFilterSelectAll();
        },
        setFilterData() {
            let filterItemObj = {};
            this.filterList.forEach(item => {
                if (item.checked) {
                    filterItemObj[item.label] = 1;
                }
            })

            if (Object.keys(filterItemObj).length !== this.filterList.length) {
                this.hasFilterObj[this.filterObj.key] = {
                    type: this.filterObj,
                    data: filterItemObj
                };
            } else {
                delete this.hasFilterObj[this.filterObj.key];
            }
            this.clearDrop();
            this.$forceUpdate();

            this.getFilterList();
        },
        getFilterList() {
            let filterList = [];

            if (Object.keys(this.hasFilterObj).length) {
                this.list.forEach(item => {
                    let isInFilter = true;

                    for (let key in this.hasFilterObj) {
                        let filterItem = this.hasFilterObj[key];

                        if (!item[key] && !filterItem.data['(空值)']) {
                            isInFilter = false;
                            return;
                        }

                        if(item[key]) {
                            if (filterItem.type.filterType === 'array') {
                                let hasInArray = false;
                                if (item[key].length) {
                                    item[key].forEach(ki => {
                                        if (filterItem.data[ki[filterItem.type.filterKey]]) {
                                            hasInArray = true;
                                        }
                                    })
                                } else if(filterItem.data['(空值)']){
                                    hasInArray = true;
                                }
    
                                if(!hasInArray) {
                                    isInFilter = hasInArray;
                                }
                            } else if (!filterItem.data[item[key]]) {
                                isInFilter = false;
                            }
                        }
                    }

                    if (isInFilter) {
                        filterList.push(item);
                    }
                })

                this.tableList = filterList;
            } else {
                this.tableList = JSON.parse(JSON.stringify(this.list));
            }

            if(Object.keys(this.sortObj).length) {
                this.sortItem(this.sortObj.item, 1);
            }
        },
        clearDrop() {
            if (document.querySelector('.J-table_drop_wrap')) {
                document.querySelector('.J-table_drop_wrap').remove();
            }
            this.filterObj = {};
            this.filterList = [];
            this.currentDropTarget = null;
            this.filterDropSearchValue = "";
        },
        handlerTableEdit(item, key, e) {
            let $td = e.target.parentElement;

            this.$emit('tableItemEdit', {
                data: item,
                el: $td,
                key: key
            });
        },
        callNumber(tel, data) {
            this.$emit('call', {
                phone: tel,
                data: data
            })
        }
    },
})
Vue.component('ui-tip', {
    template: `<div class="vd-ui-tip-wrap" :class="[position, iconParse[icon] || 'warn']" ref="tipWrap" :style="positionStyle" @mouseover="showCnt" @mouseleave="hideCnt">
        <i class="vd-ui_icon" :class="'icon-' + icon"></i>
        <div class="vd-ui-tip-cnt" :class="[{nowrap: isCalcing}]" :style="widthStyle" ref="tipCnt">
            <div class="vd-ui-tip-inner" :class="{small: font=='small'}">
                <slot></slot>
            </div>
        </div>
    </div>`,
    props: {
        icon: {
            type: String,
            default: 'caution2'
        },
        font: {
            type: String,
            default: ''
        },
        width: {
            type: Number,
            default: 300
        },
        position: {
            type: String,
            default: 'lt'
        },
        nofixed: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            iconParse: {
                caution2: 'warn',
                info2: 'info'
            },
            isShow: true,
            isCalcing: true,
            widthStyle: '',
            positionStyle: ''
        }
    },
    mounted() {
        window.addEventListener('scroll', this.hideCnt)
    },
    methods: {
        showCnt() {
            this.isShow = true;
            
            if(!this.nofixed) {
                let wrapPosition = this.$refs.tipWrap.getBoundingClientRect();
                this.positionStyle = `overflow:visible;position:fixed;top:${wrapPosition.top}px;left:${wrapPosition.left}px;z-index:9999;`
            }

            if(this.$refs.tipCnt.offsetWidth > this.width) {
                this.isCalcing = false;
                this.widthStyle = `width:${this.width}px;`;
            }
        },
        hideCnt() {
            this.widthStyle = "";
            this.positionStyle = "";
            this.isCalcing = true;
        }
    }
})

Vue.component('ui-title-tip', {
    template: `<div class="vd-ui-title-tip-wrap" ref="tipWrap" @mouseover="showCnt" @mouseleave="hideCnt">
        <slot></slot>
        <div class="vd-ui-title-tip-cnt" :class="{show: isShow}" ref="tipCnt" :style="positionStyle">{{ title }}</div>
    </div>`,
    props: {
        title: {
            type: String,
            default: ''
        },
        position: {
            type: String,
            default: 'top'
        },
        y: {
            type: Number,
            default: 0
        },
        nofixed: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            positionStyle: '',
            isShow: false
        }
    },
    mounted() {
        window.addEventListener('scroll', this.hideCnt)
    },
    methods: {
        showCnt() {
            if(!this.nofixed) {
                document.body.append(this.$refs.tipCnt);
                let wrapPosition = this.$refs.tipWrap.getBoundingClientRect();
                console.log(wrapPosition)
                this.positionStyle = `top:${wrapPosition.top + this.y}px;left:${wrapPosition.left + wrapPosition.width/2}px;`
            }

            this.isShow = true;
        },
        hideCnt() {
            this.isShow = false;
        }
    }
})
Vue.component('ui-tyc', {
    template: `
        <div class="vd-ui-tianyancha">
            <div class="vd-ui-tyc-input" :style="{'width': width}">
                <ui-input
                    :placeholder="placeholder"
                    v-bind="$attrs"
                    v-model="inputValue"
                    @blur="handlerBlur"
                    @input.native="handleInput"
                    @compositionend.native="commentPress"
                    width="100%"
                    :maxlength="maxlength"
                    autocomplete="off"
                ></ui-input>
            </div>


            <div class="vd-ui-tyc-right" v-if="needTyc">
                <template v-if="tycInfo && tycInfo.tycFlag != 'Y'">
                    <div class="vd-ui-tyc-warn-icon" v-if="inputValue">
                        <i class="vd-ui_icon icon-caution1"></i>
                        <div class="vd-ui-tyc-icon-tip"><div class="tip-txt">该公司未匹配天眼查数据，可能存在不准确</div></div>
                    </div>
                    <div class="vd-ui-tyc-search-icon" @click="handlerSearch">
                        <i class="vd-ui_icon icon-search"></i>
                        <span>天眼查查询</span>
                    </div>
                </template>
                <i class="vd-ui_icon icon-tyc" v-else @click="showDetail"></i>
            </div>

            <transition>
                <ul
                    class="vd-ui-tyc-search-related"
                    @click.stop
                    v-show="rotate1"
                    :style="{'width': width}"
                >
                    <template v-if="loading">
                        <li class="loading">
                            <i class="vd-ui_icon icon-loading" ref="loading"></i>
                            <span>加载中...</span>
                        </li>
                    </template>

                    <template v-else-if="loadingFail">
                        <li class="failed-li">
                            <i class="vd-ui_icon icon-error2"></i>
                            <span>加载失败</span>
                            <span class="reload" @click="handleReload">重新加载</span>
                        </li>
                    </template>

                    <template v-else>
                        <div class="search-related-list" v-if="relateList.length">
                            <div class="local-data">本地数据匹配</div>
                            <div 
                                class="sr-item" 
                                :class="{'disabled': needDisable && !item.belong}"
                                v-for="(item, index) in relateList" :key="index"
                                @click.stop="chooseErp(item)"
                            >
                                <div class="sr-item-left">
                                    <span class="icon" v-if="item.tycFlag == 'Y'"></span>
                                    <p v-if="needDisable && !item.belong">{{ item.traderName }}</p>
                                    <p v-else v-html="lightName(item.traderName)"></p>
                                </div>
                                <div class="sr-item-right">{{ item.saleName }}</div>
                            </div>
                        </div>
                        <li class="empty-li" v-else>
                            <p>暂无数据</p>
                        </li>
                    </template>
                </ul>
            </transition>


            <transition>
                <ui-dialog
                    :visible.sync="rotate2"
                    width="960px"
                    title="天眼查查询"
                >
                    <div class="vd-ui-table-wrap with-border" v-if="companyList.length">
                        <div class="vd-ui-table-body">
                            <table class="vd-ui-table">
                                <colgroup>
                                    <col width="">
                                    <col width="120px">
                                    <col width="120px">
                                    <col width="120px">
                                    <col width="140px">
                                    <col width="100px">
                                </colgroup>
                                <thead>
                                    <tr class="vd-ui-tr">
                                        <th class="vd-ui-th">公司名称</th>
                                        <th class="vd-ui-th">机构类型</th>
                                        <th class="vd-ui-th">法人</th>
                                        <th class="vd-ui-th">省份</th>
                                        <th class="vd-ui-th">成立日期</th>
                                        <th class="vd-ui-th">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="vd-ui-tr" v-for="(item, index) in companyList" :key="index">
                                        <td class="vd-ui-td">{{ item.name }}</td>
                                        <td class="vd-ui-td">{{ item.companyType | filterCompanyType }}</td>
                                        <td class="vd-ui-td">{{ item.legalPersonName }}</td>
                                        <td class="vd-ui-td">{{ item.base }}</td>
                                        <td class="vd-ui-td">{{ item.estiblishTime | filterDateTime }}</td>
                                        <td class="vd-ui-td">
                                            <a class="option-item" class="tyc-choose" @click="chooseThis(item)">选择</a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="tyc-empty" v-else>
                        <div class="empty-img"></div>
                        <div class="empty-txt">抱歉，未能匹配到天眼查公司数据，建议您检查关键词重新搜索</div>
                    </div>
                </ui-dialog>
            </transition>

            <transition>
                <ui-dialog
                    :visible.sync="rotate3"
                    width="960px"
                    title="天眼查查询"
                >
                    <div class="tyc-info-wrap">
                        <div class="tyc-info-wrap">
                            <div class="tyc-info-cnt J-tyc-info-cnt">
                                <template v-if="companyInfo && Object.keys(companyInfo).length">
                                    <div class="tyc-info-title">
                                        <div class="tyc-info-title-txt">{{ companyInfo.name }}</div>
                                        <div class="tyc-info-tags" v-if="companyInfo.tags && companyInfo.tags.length">
                                            <div class="tag-item" v-for="tag in companyInfo.tags" :key="tag">{{ tag }}</div>
                                        </div>
                                    </div>
                                    <div class="tyc-info-list">
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">地区</div>
                                            <div class="tyc-info-txt">
                                                <template v-if="companyInfo.base">{{ companyInfo.base }}</template>
                                                <template v-if="companyInfo.base && companyInfo.city"> / </template>
                                                <template v-if="companyInfo.city">{{ companyInfo.city }}</template>
                                                <template v-if="companyInfo.city && companyInfo.district"> / </template>
                                                <template v-if="companyInfo.district">{{ companyInfo.district }}</template>
                                            </div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">注册地址</div>
                                            <div class="tyc-info-txt">{{ companyInfo.regLocation || '-' }}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">曾用名</div>
                                            <div class="tyc-info-txt">{{ companyInfo.historyNames || '-' }}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">法人</div>
                                            <div class="tyc-info-txt">{{companyInfo.legalPersonName || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">注册资本</div>
                                            <div class="tyc-info-txt">{{companyInfo.regCapital || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">纳税人识别号</div>
                                            <div class="tyc-info-txt">{{companyInfo.taxNumber || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">企业联系方式</div>
                                            <div class="tyc-info-txt">{{companyInfo.phoneNumber || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">行业</div>
                                            <div class="tyc-info-txt">{{companyInfo.industry || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">国民经济行业分类</div>
                                            <div class="tyc-info-txt">{{ category_Big_Middle_Small || '-' }}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">成立日期</div>
                                            <div class="tyc-info-txt">{{companyInfo.estiblishTime | filterDetailDateTime}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">网址</div>
                                            <div class="tyc-info-txt">{{companyInfo.websiteList || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">经营范围</div>
                                            <div class="tyc-info-txt">{{companyInfo.businessScope || '-'}}</div>
                                        </div>
                                    </div>
                                </template>
                                <div class="tyc-empty" v-else>
                                    <div class="empty-img"></div>
                                    <div class="empty-txt">抱歉，未能匹配到天眼查公司数据</div>
                                </div>
                            </div>
                            <div class="tyc-info-loading J-tyc-info-loading" style="display: none;">
                                <i class="bd-icon icon-loading"></i>
                            </div>
                        </div>
                    </div>
                </ui-dialog>
            </transition>
        </div>
    `,

    props: {
        value: {
            type: String,
            default: ''
        },
        traderId: {
            type: [String, Number],
        },
        tycFlag: String,

        // 是否启用天眼查
        needTyc: {
            type: Boolean,
            default: true
        },
        // 选项中 启用禁用
        needDisable: {
            type: Boolean,
            default: false
        },

        placeholder: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
        maxlength: {
            type: [Number, String],
            default: 1000,
        },
    },
    data() {
        return {
            inputValue: this.value,
            // erp本地匹配
            rotate1: false,
            relateList: [], // 搜索联想
            loading: false,
            loadingFail: false,
            timer: null,

            // 天眼查列表
            rotate2: false,
            companyList: [],
            canAjax: true,

            // 天眼查详情
            rotate3: false,
            companyInfo: null, // 公司信息-确定选择后
            tycInfo: {
                traderId: '',
                tycFlag: 'N', // Y:天眼查客户  N:不是天眼查客户 
            },
        }
    },
    computed: {
        lightName () {
            return (name) => {
                if (!this.inputValue) return name;
                const regExp = new RegExp(this.inputValue, 'g');
                name = name.replace(regExp, `<font color='#FF6600'">${this.inputValue}</font>`);
                return name;
            }
        },
        category_Big_Middle_Small () {
            let arr = [];
            this.companyInfo.category && arr.push(this.companyInfo.category);
            this.companyInfo.categoryBig && arr.push(this.companyInfo.categoryBig);
            this.companyInfo.categoryMiddle && arr.push(this.companyInfo.categoryMiddle);
            this.companyInfo.categorySmall && arr.push(this.companyInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    filters: {
        filterDateTime (val) {
            if (/^\d{4}-\d{2}-\d{2}/.test(val)) {
                return val.substr(0, 10);
            } else {
                return '-'
            }
        },
        filterDetailDateTime (val) {
            if (/\d{10,}/.test(val)) {
                return moment(val).format('YYYY-MM-DD');
            } else {
                return '-'
            }
        },
        filterCompanyType (val) {
            let TYPES = {
                1: '公司',
                2: '香港企业',
                3: '社会组织',
                4: '律所',
                5: '事业单位',
                6: '基金会',
                7: '不存在法人、注册资本、统一社会信用代码、经营状态',
                8: '台湾企业',
                9: '新机构',
            }
            return TYPES[val] || ''
        }
    },
    watch: {
        value (newV) {
            this.inputValue = newV;
        },
        
        traderId: {
            handler (newV) {
                this.tycInfo.traderId = newV || '';
            },
            immediate: true
        },
        tycFlag: {
            handler (newV) {
                this.tycInfo.tycFlag = newV || '';
            },
            immediate: true
        },
        rotate1 (newV) {
            if (!newV) {
                this.loading = false;
                this.loadingFail = false;
            }
        }
    },
    mounted() {
        document.addEventListener('click', (e)=>{
            if (!this.$el.contains(e.target)) {
                this.rotate1 = false;
            }
        })
    },
    methods: {
        handlerBlur () {},

        // 清空公司信息
        clearCompany () {
            this.companyInfo = null;
            this.tycInfo = {
                traderId: '',
                tycFlag: 'N',
            }
            this.$emit('change', {});
        },

        // 节流搜索/300毫秒
        // handleInput: _.throttle(function (event) {
        //     this.companyInfo = null;
        //     this.$emit('input', event.target.value); // 修改外层v-model值this.$emit('change', {});
        //     this.getRelatelist(event.target.value);
        // }, 300),

        // 输入停顿300毫秒后搜素
        handleInput (e) {
            if (e.inputType != 'insertCompositionText') {
                let val = e.target.value.trim();
                this.clearCompany();
                this.$emit('input', val);

                this.timer && clearTimeout(this.timer);
                this.timer = setTimeout(()=> {
                    this.getRelatelist(val);
                }, 300)
            }
        },
        // 中文结束
        commentPress(e) {
            let val = e.target.value.trim();
            if (val) {
                this.$emit('input', val);

                this.timer && clearTimeout(this.timer);
                this.timer = setTimeout(()=> {
                    this.getRelatelist(val);
                }, 300);
            }
        },

        getRelatelist (name) {
            this.rotate1 = true;
            this.loading = true;
            this.loadingFail = false;

            axios.post(`/crm/trader/profile/queryTrader?name=${name}`).then(({data}) => {
                this.loading = false;
                if (data.success) {
                    this.relateList = data.data || [];
                } else {
                    this.loadingFail = true;
                }
            }).catch(err=> {
                this.loading = false;
                this.loadingFail = true;
            })
        },

        // 重新加载
        handleReload () {
            this.getRelatelist(this.inputValue);
        },

        // 选择已建档客户
        async chooseErp (item) {
            this.tycInfo = {
                tycFlag: item.tycFlag || 'N',
                traderId: item.traderId || '',
                // name: item.traderName,
            }
            this.inputValue = item.traderName || '';
            this.rotate1 = false;

            this.$emit("input", item.traderName || ''); // 修改外层v-model值

            let emitData = Object.assign({}, item, this.tycInfo);
            console.log('已建档信息', emitData);
            this.$emit('change', emitData);

            if (item.tycFlag == 'Y') {
                this.companyInfo = await this.getDetail(item.traderName);
            }
        },

        // 天眼查列表
        handlerSearch () {
            if (!this.inputValue) {
                this.$message.warn('请输入公司名称后进行查询')
                return false;
            }
            this.canAjax = false;
            this.rotate1 = false;
            VD_UI_GLOBAL.showGlobalLoading();

            axios.post(`/crm/trader/profile/queryTycList?traderName=${this.inputValue}`).then(({data}) => {
                if (data.success) {
                    this.canAjax = true;
                    this.companyList = data.data || [];
                    this.rotate2 = true;
                } else {
                    this.$message.warn(data.message || '网络异常');
                    this.canAjax = true;
                }
                VD_UI_GLOBAL.hideGlobalLoading();
            }).catch(err=> {
                VD_UI_GLOBAL.hideGlobalLoading();
            })
        },
        // 选择天眼查公司
        async chooseThis(item) {
            let fetxhDetail = await this.getDetail(item.name);
            if (fetxhDetail.success) {
                this.companyInfo = fetxhDetail.data || {};
                this.inputValue = this.companyInfo.name || '';

                this.tycInfo = {
                    tycFlag: 'Y',
                    traderId: '',
                }
                let emitData = Object.assign({}, this.companyInfo, this.tycInfo)
                this.$emit("input", this.companyInfo.name || ''); // 修改外层v-model值
                console.log('天眼查信息', emitData);
                this.$emit('change', emitData);
            }

            this.rotate1 = false;
            this.rotate2 = false;
        },

        // 获取详情信息
        getDetail (name) {
            return new Promise((resolve, reject) => {
                VD_UI_GLOBAL.showGlobalLoading();
                axios.post(`/crm/trader/profile/queryTycDetail?traderName=${name}`).then(({data}) => {
                    VD_UI_GLOBAL.hideGlobalLoading();
                    resolve(data || {});
                }).catch(errr=> {
                    VD_UI_GLOBAL.hideGlobalLoading();
                    reject(errr || {});
                    console.log('fetch error', errr);
                })
            })
        },

        async showDetail () {
            if (!(this.companyInfo && Object.keys(this.companyInfo).length)) {
                let fetxhDetail = await this.getDetail(this.inputValue);
                if (fetxhDetail.success) {
                    this.companyInfo = fetxhDetail.data || {};
                }
            }
            this.rotate3 = true;
        }
    }
})
Vue.component('ui-tyc-detail', {
    template: `
        <div class="vd-ui-tianyancha">
            <transition>
                <ui-dialog
                    :visible.sync="rotate"
                    width="960px"
                    title="天眼查查询"
                >
                    <div class="tyc-info-wrap">
                        <div class="tyc-info-wrap">
                            <div class="tyc-info-cnt J-tyc-info-cnt">
                                <template v-if="companyInfo && Object.keys(companyInfo).length">
                                    <div class="tyc-info-title">
                                        <div class="tyc-info-title-txt">{{ companyInfo.name }}</div>
                                        <div class="tyc-info-tags" v-if="companyInfo.tags && companyInfo.tags.length">
                                            <div class="tag-item" v-for="tag in companyInfo.tags" :key="tag">{{ tag }}</div>
                                        </div>
                                    </div>
                                    <div class="tyc-info-list">
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">地区</div>
                                            <div class="tyc-info-txt">
                                                <template v-if="companyInfo.base">{{ companyInfo.base }}</template>
                                                <template v-if="companyInfo.base && companyInfo.city"> / </template>
                                                <template v-if="companyInfo.city">{{ companyInfo.city }}</template>
                                                <template v-if="companyInfo.city && companyInfo.district"> / </template>
                                                <template v-if="companyInfo.district">{{ companyInfo.district }}</template>
                                            </div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">注册地址</div>
                                            <div class="tyc-info-txt">{{ companyInfo.regLocation || '-' }}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">曾用名</div>
                                            <div class="tyc-info-txt">{{ companyInfo.historyNames || '-' }}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">法人</div>
                                            <div class="tyc-info-txt">{{companyInfo.legalPersonName || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">注册资本</div>
                                            <div class="tyc-info-txt">{{companyInfo.regCapital || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">纳税人识别号</div>
                                            <div class="tyc-info-txt">{{companyInfo.taxNumber || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">企业联系方式</div>
                                            <div class="tyc-info-txt">{{companyInfo.phoneNumber || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">行业</div>
                                            <div class="tyc-info-txt">{{companyInfo.industry || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">国民经济行业分类</div>
                                            <div class="tyc-info-txt">{{ category_Big_Middle_Small || '-' }}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">成立日期</div>
                                            <div class="tyc-info-txt">{{companyInfo.estiblishTime | filterDetailDateTime}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">网址</div>
                                            <div class="tyc-info-txt">{{companyInfo.websiteList || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">经营范围</div>
                                            <div class="tyc-info-txt">{{companyInfo.businessScope || '-'}}</div>
                                        </div>
                                    </div>
                                </template>
                                <div class="tyc-info-empty" v-else>
                                    <div class="empty-img"></div>
                                    <div class="empty-txt">抱歉，未能匹配到天眼查公司数据</div>
                                </div>
                            </div>
                            <div class="tyc-info-loading J-tyc-info-loading" style="display: none;">
                                <i class="bd-icon icon-loading"></i>
                            </div>
                        </div>
                    </div>
                </ui-dialog>
            </transition>
        </div>
    `,

    props: {
        name: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
    },
    data() {
        return {
            traderName: '', // 名称
            companyInfo: null, // 公司信息-确定选择后
            rotate: false,
        }
    },
    computed: {
        // 国民经济行业分类
        category_Big_Middle_Small () {
            let arr = [];
            this.companyInfo.category && arr.push(this.companyInfo.category); 
            this.companyInfo.categoryBig && arr.push(this.companyInfo.categoryBig); 
            this.companyInfo.categoryMiddle && arr.push(this.companyInfo.categoryMiddle); 
            this.companyInfo.categorySmall && arr.push(this.companyInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    filters: {
        filterDetailDateTime (val) {
            if (/\d{10,}/.test(val)) {
                return moment(val).format('YYYY-MM-DD');
            } else {
                return '-'
            }
        },
    },
    mounted() {
    },
    methods: {
        open (name) {
            this.traderName = name;
            this.showDetail();
            this.rotate = true;
        },
        showDetail () {
            VD_UI_GLOBAL.showGlobalLoading();
            axios.post(`/crm/trader/profile/queryTycDetail?traderName=${this.traderName}`).then(({data}) => {
                VD_UI_GLOBAL.hideGlobalLoading();
                if (data.success) {
                    this.companyInfo = data.data || {};
                }
            }).catch(errr=> {
                VD_UI_GLOBAL.hideGlobalLoading();
            })
        }
    }
})
Vue.component('ui-upload', {
    template: `<div class="vd-ui-upload-wrap">
        <div class="vd-ui-upload-img-wrap" v-if="type=='img'">
            <template v-if="files && files.length">
                <div class="vd-ui-upload-img-item" v-for="(item, index) in files">
                    <img :src="item.url"/>
                    <div class="vd-ui-upload-img-options">
                        <div class="vd-ui-upload-img-option-item" @click="previewImg(item.url)">查看</div>
                        <div class="vd-ui-upload-img-option-item" @click="deleteItem(index)">删除</div>
                    </div>
                </div>
            </template>
            <div class="vd-ui-upload-img-btn" @click="triggerInputClick" v-if="!tempFile && files.length < limit">
                <i class="vd-ui_icon icon-add"></i>
            </div>
            <div class="vd-ui-upload-img-loading" v-if="tempFile">
                <i class="vd-ui_icon icon-loading"></i>
            </div>
        </div>
        <div class="vd-ui-upload-file-wrap" :class="{'no-info': noInfo}" v-if="type=='file'">
            <ui-button @click="triggerInputClick" :type="btnType" :disabled="!!tempFile">{{ label }}</ui-button>
            <template v-if="!noInfo">
                <div class="vd-ui-file-list" v-if="tempFile || files.length">
                    <div class="vd-ui-file-item" v-for="(item, index) in files" :key="index">
                        <div class="vd-ui-file-info">
                            <div class="vd-ui-file-icon">
                                <img :src="'/static/image/upload-icon/' + (typeParse[item.type] || 'other') + '.svg'"></img>
                            </div>
                            <div class="vd-ui-file-name">{{item.name}}</div>
                            <div class="vd-ui-file-size">{{item.size}}</div>
                            <div class="vd-ui-file-option">
                                <i class="vd-ui_icon icon-recycle"></i>
                            </div>
                        </div>
                    </div>
                    <div class="vd-ui-file-item" v-if="tempFile">
                        <div class="vd-ui-file-info">
                            <div class="vd-ui-file-icon">
                                <img :src="'/static/image/upload-icon/' + (typeParse[tempFile.type] || 'other') + '.svg'"></img>
                            </div>
                            <div class="vd-ui-file-name text-line-1">{{tempFile.name}}</div>
                        </div>
                        <div class="vd-ui-file-progress"></div>
                    </div>
                </div>
            </template>
        </div>
        <!-- 提示 -->
        <div class="vd-ui-upload-tips"> 
            <slot name="tips"></slot>
        </div>
        <input 
            type="file" 
            ref="fileInput" 
            :accept="accept" 
            @change="handlerFileInputChange" 
            style="display:none"/>
    </div>`,
    props: {
        label: {
            type: String,
            default: '本地上传'
        },
        type: {
            type: String,
            default: 'file'
        },
        limitType: {
            type: String,
            default: ''
        },
        limitSize: {
            type: Number,
            default: 2
        },
        limit: {
            type: Number,
            default: 1
        },
        errorMsg: {
            type: Object,
            default: {
                limitType: '格式不对',
                limitSize: '图片大小不超过{}MB'
            }
        },
        action: {
            type: String,
            default: '/crm/common/public/uploadFile'
        },
        needValid: {
            type: Boolean,
            default: true
        },
        noInfo: {
            type: Boolean,
            default: false
        },
        btnType: {
            type: String,
            default: ''
        },
        multi: {
            type: Boolean,
            default: false
        },
        list: {
            type: Array,
            default() {
                return []
            }
        }
    },
    data() {
        return {
            accept: '',
            acceptParse: {
                gif: 'image/gif',
                jpg: 'image/jpeg',
                jpeg: 'image/jpeg',
                png: 'image/png',
                pdf: 'application/pdf',
                doc: 'application/msword',
                xls: 'application/vnd.ms-excel',
                xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            },
            typeParse: {
                xls: 'excel',
                xlsx: 'excel',
                pdf: 'pdf',
                png: 'pic',
                jpg: 'pic',
                jpeg: 'pic',
                bmp: 'pic',
                gif: 'pic',
                ppt: 'ppt',
                pptx: 'ppt',
                doc: 'word',
                docx: 'word',
            },
            files: [],
            tempFile: null
        }
    },
    watch: {

    },
    mounted() {
        this.init()
    },
    methods: {
        init() {
            if (this.limitType) {
                let acceptList = [];
                let isAll = false;
                this.limitType.split(',').forEach(item => {
                    if (!this.acceptParse[item]) {
                        isAll = true;
                    } else {
                        acceptList.push(this.acceptParse[item]);
                    }
                })

                if (!isAll) {
                    this.accept = acceptList.join(',')
                } else if (this.type == 'img') {
                    this.accept = 'image/*';
                }
            }

            if(this.list && this.list.length) {
                this.files = this.list;
            }
        },
        triggerInputClick() {
            if (!this.tempFile && this.limit > this.files.length) {
                this.$refs.fileInput.click();
            }
        },
        getSize(size) {
            if (size > 1024 * 1024) {
                return parseFloat((size / 1024 / 1024).toFixed(1)) + 'MB';
            } else {
                return parseInt(size / 1024) + 'KB';
            }
        },
        handlerFileInputChange() {
            let files = this.$refs.fileInput.files;

            if (files.length) {
                let file = files[0];

                if (this.multi) {

                }
                let fileName = file.name;
                let fileNameTxt = fileName.substring(0, fileName.lastIndexOf('.'));
                let fileType = fileName.substring(fileName.lastIndexOf('.') + 1, fileName.length);

                if (this.needValid) {
                    if (this.limitType.split(',').indexOf(fileType) === -1) {
                        if (!this.customError) {
                            this.showError(this.errorMsg.limitType);
                        }

                        return false;
                    }

                    if (file.size > this.limitSize * 1024 * 1024) {
                        if (!this.customError) {
                            this.showError(this.errorMsg.limitSize.replace('{}', this.limitSize));
                        }

                        return false;
                    }
                }

                let form = new FormData();
                form.append('file', file);

                this.tempFile = {
                    name: fileNameTxt,
                    type: fileType,
                    fullName: fileName
                }

                axios.post(this.action, form, {
                    headers: { 'Content-Type': 'multipart/form-data' }
                }).then(({ data }) => {
                    this.tempFile = null;
                    if (data.code === 0) {
                        if (this.type === 'file') {
                            this.files.push({
                                name: fileNameTxt,
                                type: fileType,
                                size: this.getSize(file.size),
                                fullName: fileName,
                            })
                        } else {
                            this.files.push({
                                url: data.scheme + data.ossUrl
                            })
                        }

                        this.$emit('change', this.files);
                        this.clear();

                        // if (this.limit == 1 && this.files.length == 1) {
                        //     this.label = '重新上传';
                        // }
                    } else {
                        this.showError(data.message || '上传失败');
                        this.$emit('onerror', data)
                    }
                })
            }
        },
        clear() {
            this.$refs.fileInput.value = "";
        },
        showError(txt) {
            this.$popup.warn({
                message: txt,
                buttons: [{
                    txt: '我知道了',
                    btnClass: 'confirm',
                }]
            })

            this.clear();
        },
        deleteItem(index) {
            this.files.splice(index, 1);
            
            this.$emit('change', this.files);
        },
        previewImg(url) {
            window.open(url)
        }
    }
})

Vue.component('ui-upload-attachment', {
    template: `<div class="vd-ui-upload-wrap">
        <div class="vd-ui-upload-file-wrap no-info">
            <ui-button @click="showConfirmDialog" type="primary">{{ label }}</ui-button>
        </div>

        <input 
            type="file" 
            ref="fileInput" 
            :accept="accept" 
            multiple
            @change="handlerFileInputChange" 
            style="display:none"/>
    </div>`,
    props: {
        label: {
            type: String,
            default: '添加附件'
        },
        limitType: {
            type: String,
            default: ''
        },
        limitNumber: {
            type: Number,
            default: 20
        },
        limitSize: {
            type: Number,
            default: 10
        },
        bizType: {
            type: String,
            default: '03'
        },
        bizId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            accept: '',
            files: [],
            errorSize: false,
            errorNumber: false,
            uploadList: [],
            isShowConfirmDialog: false
        }
    },
    watch: {

    },
    mounted() {
        // this.init()
    },
    methods: {
        init() {
            // if (this.limitType) {
            //     let acceptList = [];
            //     let isAll = false;
            //     this.limitType.split(',').forEach(item => {
            //         if (!this.acceptParse[item]) {
            //             isAll = true;
            //         } else {
            //             acceptList.push(this.acceptParse[item]);
            //         }
            //     })

            //     if (!isAll) {
            //         this.accept = acceptList.join(',')
            //     } else if (this.type == 'img') {
            //         this.accept = 'image/*';
            //     }
            // }
        },
        showConfirmDialog() {
            let _this = this;

            this.$popup.info({
                message: `支持批量添加文件，单个大小不超过${this.limitSize}MB，最多添加${this.limitNumber}个文件。`,
                buttons: [{
                    txt: '本地上传',
                    btnClass: 'confirm',
                    callback() {
                       _this.triggerInputClick();
                    }
                }, {
                    txt: '取消'
                }]
            })
        },
        triggerInputClick() {
            this.$refs.fileInput.click();
        },
        handlerFileInputChange() {
            let files = this.$refs.fileInput.files;

            if (files.length) {
                VD_UI_GLOBAL.showGlobalLoading('文件上传中，请不要关闭当前页面');
                this.upload(files, 0);
            }
        },
        upload(files, index) {
            let file = files[index];

            if (file.size > this.limitSize * 1024 * 1024) {
                this.errorSize = true;

                this.checkFinish(files, index);

                return false;
            }
            console.log(file)
            let form = new FormData();
            form.append('bizId', this.bizId);
            form.append('bizType', this.bizType);
            form.append('file', file);
            axios.post('/crm/common/public/uploadBusinessFile', form, {
                headers: { 'Content-Type': 'multipart/form-data' }
            }).then(({ data }) => {
                if (data.success) {
                    this.uploadList.push(data.data);
                } else if (data.code == '50001') {
                    this.errorNumber = true;
                    index = files.length - 1;
                } else {
                    this.errorSize = true;
                }

                this.checkFinish(files, index)
            })
        },
        checkFinish(files, index) {
            if (index < files.length - 1) {
                this.upload(files, index + 1)
            } else {
                VD_UI_GLOBAL.hideGlobalLoading();

                let errMsg = [];

                if (this.errorNumber) {
                    errMsg.push(`最多上传${this.limitNumber}个附件；`)
                }


                if (this.errorSize) {
                    errMsg.push(`单个文件大小不能超过${this.limitSize}MB；`)
                }

                if (errMsg.length) {
                    this.$popup.warn({
                        message: errMsg.join('<br/>'),
                        buttons: [{
                            txt: '我知道了',
                            btnClass: 'confirm'
                        }]
                    });
                }

                this.$emit('finish', this.uploadList);

                if (this.uploadList.length) {
                    let reqData = [];

                    this.uploadList.forEach(item => {
                        reqData.push({
                            attachmentId: item.attachmentId,
                            bizType: this.bizType,
                            bizId: this.bizId
                        })
                    })

                    axios.post('/crm/common/public/saleUploadFileLog', reqData)
                }
            }
        },
        clear() {
            this.$refs.fileInput.value = "";
        }
    }
})
