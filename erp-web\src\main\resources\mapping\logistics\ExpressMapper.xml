<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.logistics.dao.ExpressMapper" >
	<resultMap id="SyncExpressDtoResultMap" type="com.vedeng.logistics.model.SyncExpressDto">
		<id property="expressId" column="EXPRESS_ID"/>
		<result property="companyId" column="COMPANY_ID"/>
		<result property="logisticsId" column="LOGISTICS_ID"/>
		<result property="logisticsNo" column="LOGISTICS_NO"/>
		<result property="deliveryTime" column="DELIVERY_TIME"/>
		<result property="batchNo" column="BATCH_NO"/>
		<result property="creator" column="CREATOR"/>
		<result property="updater" column="UPDATER"/>
		<collection property="expressDetailDtoList" ofType="com.vedeng.logistics.model.SyncExpressDetailDto">
			<id property="expressDetailId" column="EXPRESS_DETAIL_ID"/>
			<result property="expressId" column="DETAIL_EXPRESS_ID"/>
			<result property="businessType" column="BUSINESS_TYPE"/>
			<result property="relatedId" column="RELATED_ID"/>
			<result property="num" column="NUM"/>
			<result property="nonAllArrivalReason" column="NON_ALL_ARRIVAL_REASON"/>
		</collection>
	</resultMap>
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.Express" >
    <id column="EXPRESS_ID" property="expressId" jdbcType="INTEGER" />
    <result column="LOGISTICS_ID" property="logisticsId" jdbcType="INTEGER" />
    <result column="LOGISTICS_NO" property="logisticsNo" jdbcType="VARCHAR" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
    <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT" />
    <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
    <result column="DELIVERY_FROM" property="deliveryFrom" jdbcType="INTEGER" />
    <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="LOGISTICS_NAME" property="logisticsName" jdbcType="VARCHAR" />
    <result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER" />
    <result column="CARD_NUMBER" property="cardnumber" jdbcType="VARCHAR" />
    <result column="BUSINESS_TYPE" property="business_Type" jdbcType="INTEGER" />
    <result column="REAL_WEIGHT" property="realWeight" jdbcType="DECIMAL" />
    <result column="NUM" property="j_num" jdbcType="INTEGER" />
    <result column="DNUM" property="num" jdbcType="INTEGER" />
    <result column="AMOUNT_WEIGHT" property="amountWeight" jdbcType="DECIMAL" />
    <result column="MAIL_GOODS" property="mailGoods" jdbcType="VARCHAR" />
    <result column="MAIL_GOODS_NUM" property="mailGoodsNum" jdbcType="INTEGER" />
    <result column="IS_PROTECT_PRICE" property="isProtectPrice" jdbcType="INTEGER" />
    <result column="PROTECT_PRICE" property="protectPrice" jdbcType="DECIMAL" />
    <result column="IS_RECEIPT" property="isReceipt" jdbcType="INTEGER" />
    <result column="MAIL_COMMTENTS" property="mailCommtents" jdbcType="VARCHAR" />
    <result column="SENT_SMS" property="sentSms" jdbcType="BIT" />
    <result column="CODE" property="code" jdbcType="VARCHAR" />
	  <result column="IS_INTERCEPTED" property="isIntercepted" jdbcType="BIT" />
	  <result column="INTERCEPT_TIME" property="interceptTime" jdbcType="BIGINT" />
    <result column="INVOICE_AMOUNT" property="invoiceAmount" jdbcType="DECIMAL" />
    <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR" />
	  <result column="WMS_ORDER_NO" property="wmsOrderNo" jdbcType="VARCHAR" />
	  <result  column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"/>
	  <result column="ONLINE_RECEIPT_ID" property="onlineReceiptId" jdbcType="INTEGER" />
	  <result column="IS_INVOICING" property="isInvoicing" jdbcType="INTEGER" />
	  <result column="ENABLE_RECEIVE" property="enableReceive" jdbcType="INTEGER" />
	  <result column="COMMUNICATE_RECORDER_IDS" property="communicateRecorderIds" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap type="com.vedeng.logistics.model.Express" id="VoResultMap" extends="BaseResultMap">
  	<result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
    <result column="EXPRESSNAME" property="logisticsCompanyName" jdbcType="VARCHAR" />
    <result column="DELIVERY_TIME" property="fhTime" jdbcType="BIGINT" />
     <result column="BUSINESS_TYPE" property="businessType" jdbcType="INTEGER" />
    <result column="CONSIGNEENAME" property="sjName" jdbcType="VARCHAR" />
    <result column="BUSINESSNAMENO" property="xsNo" jdbcType="VARCHAR" />
    <result column="YWID" property="ywId" jdbcType="INTEGER" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="ISOVERTIME" property="isovertime" jdbcType="INTEGER" />
    <result column="CNT" property="cnt" jdbcType="INTEGER" />
     <result column="YW_TYPE" property="ywType" jdbcType="INTEGER" />
     <result column="ALLNUM" property="allnum" jdbcType="INTEGER" />
     <result column="FNUM" property="fnum" jdbcType="INTEGER" />
  </resultMap>

  <resultMap id="ExpressResultMap" type="com.vedeng.logistics.model.Express" extends="BaseResultMap">
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="BUSINESSTYPE" property="business_Type" jdbcType="INTEGER" />
	  <result  column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"/>
	  <result column="ONLINE_RECEIPT_ID" property="onlineReceiptId" jdbcType="INTEGER" />
	  <result column="IS_INVOICING" property="isInvoicing" jdbcType="INTEGER" />
	  <result column="COMMUNICATE_RECORDER_IDS" property="communicateRecorderIds" jdbcType="VARCHAR" />

  	<collection property="expressDetail" ofType="com.vedeng.logistics.model.ExpressDetail">
	    <id column="EXPRESS_DETAIL_ID" property="expressDetailId" jdbcType="INTEGER" />
    	<result column="EXPRESS_ID" property="expressId" jdbcType="INTEGER" />
    	<result column="BUSINESS_TYPE" property="businessType" jdbcType="INTEGER" />
    	<result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    	<result column="DNUM" property="num" jdbcType="INTEGER" />
    	<result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    	<result column="GOOD_NAME" property="goodName" jdbcType="VARCHAR" />
    	<result column="GOOD_ID" property="goodsId" jdbcType="VARCHAR" />
    	<result column="UNIT_NAME" property="unitName" jdbcType="VARCHAR" />
		<result column="SKU" property="sku" jdbcType="VARCHAR" />
		<result column="MODEL" property="model" jdbcType="VARCHAR" />
		<result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
  	</collection>
  </resultMap>
	<resultMap id="ExpressDetailResultMap" type="com.vedeng.logistics.model.ExpressDetail" >
		<id column="EXPRESS_DETAIL_ID" property="expressDetailId" jdbcType="INTEGER" />
		<result column="EXPRESS_ID" property="expressId" jdbcType="INTEGER" />
		<result column="BUSINESS_TYPE" property="businessType" jdbcType="INTEGER" />
		<result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
		<result column="NUM" property="num" jdbcType="INTEGER" />
		<result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
	</resultMap>
  <sql id="Base_Column_List" >
    EXPRESS_ID, LOGISTICS_ID, LOGISTICS_NO, COMPANY_ID, DELIVERY_TIME, ARRIVAL_STATUS, ARRIVAL_TIME,
    DELIVERY_FROM, LOGISTICS_COMMENTS, ADD_TIME, CREATOR, MOD_TIME, UPDATER,BATCH_NO
  </sql>

	<update id="updateExpressArrivalStatusById" parameterType="com.vedeng.logistics.model.Express" >
		update T_EXPRESS
		<set>
			<if test="arrivalStatus != null" >
				ARRIVAL_STATUS = #{arrivalStatus,jdbcType=VARCHAR},
			</if>
		</set>
		where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
	</update>
	<update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.Express" >
		update T_EXPRESS
		<set >
			<if test="logisticsId != null" >
				LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
			</if>
			<if test="logisticsNo != null and logisticsNo!=''" >
				LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
			</if>
			<if test="companyId != null" >
				COMPANY_ID = #{companyId,jdbcType=INTEGER},
			</if>
			<if test="deliveryTime != null" >
				DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
			</if>
			<if test="arrivalStatus != null" >
				ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BIT},
			</if>
			<if test="arrivalTime != null" >
				ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
			</if>
			<if test="deliveryFrom != null" >
				DELIVERY_FROM = #{deliveryFrom,jdbcType=INTEGER},
			</if>
			<if test="logisticsComments != null" >
				LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null" >
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null" >
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null" >
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null" >
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
			<if test="isEnable != null" >
				IS_ENABLE =  #{isEnable,jdbcType=BIT},
			</if>
			<if test="batchNo != null" >
				BATCH_NO =  #{batchNo,jdbcType=VARCHAR},
			</if>
			<if test="oldLogisticsNo != null and oldLogisticsNo!=''" >
				OLD_LOGISTICS_NO = #{oldLogisticsNo,jdbcType=VARCHAR},
			</if>
			<if test="sentSms != null">
				SENT_SMS = #{sentSms,jdbcType=INTEGER},
			</if>
			<if test="enableReceive != null">
				ENABLE_RECEIVE = #{enableReceive,jdbcType=INTEGER},
			</if>
			<if test="isIntercepted != null">
				IS_INTERCEPTED = #{isIntercepted,jdbcType=INTEGER},
			</if>
			<if test="interceptTime != null" >
				INTERCEPT_TIME = #{interceptTime,jdbcType=BIGINT},
			</if>
			<if test="communicateRecorderIds != null" >
				COMMUNICATE_RECORDER_IDS = #{communicateRecorderIds,jdbcType=VARCHAR},
			</if>
		</set>
		where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
	</update>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_EXPRESS
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </delete>
  <!-- 查询未到货的订单的物流单号  -->
	<select id="getExpressInfoListForBuyorder" resultMap="BaseResultMap"   >
		SELECT
			T.*, c. NAME LOGISTICS_NAME,
			c. CODE
		FROM
			T_EXPRESS T
				LEFT JOIN T_LOGISTICS L ON T.LOGISTICS_ID = L.LOGISTICS_ID
				LEFT JOIN T_LOGISTICS_CODE c ON L. NAME = c. NAME
				LEFT JOIN T_EXPRESS_DETAIL TT ON T.EXPRESS_ID = TT.EXPRESS_ID
				AND TT.BUSINESS_TYPE = 515
				LEFT JOIN T_BUYORDER_GOODS a ON TT.RELATED_ID = a.BUYORDER_GOODS_ID
				LEFT JOIN T_BUYORDER b ON a.BUYORDER_ID = b.BUYORDER_ID
				left join T_LOGISTICS_DETAIL DETAIL ON DETAIL.LOGISTICS_NO=T.LOGISTICS_NO and T.LOGISTICS_ID = DETAIL.LOGISTICS_ID
		WHERE
			1 = 1
		  AND T.IS_ENABLE = 1 and L.SYNC_KUAIDI=1
		  AND T.COMPANY_ID = 1
		  AND T.ARRIVAL_STATUS = 0
		  AND TT.BUSINESS_TYPE = 515
		  AND b.DELIVERY_DIRECT = 1
		  and b.COMPANY_ID=1 and b.STATUS!=3
		  and T.DELIVERY_TIME
			between unix_timestamp(date_add(curdate(),interval -30 day))*1000
			and unix_timestamp(now())*1000
		  AND T.LOGISTICS_NO IS NOT NULL AND T.LOGISTICS_NO != ''
		  AND T.LOGISTICS_NO NOT LIKE 'XN%'
		  AND T.LOGISTICS_NO NOT LIKE 'SL%'
		  AND T.LOGISTICS_NO NOT LIKE 'BD%'
		  and IFNULL(DETAIL.CONTENT,'') not like '%不支持此快递公司%'
		  AND L.NAME!='虚拟出入库专用承运商'
		  and T.LOGISTICS_COMMENTS != '虚拟快递单'
		GROUP BY
			T.EXPRESS_ID
		LIMIT 5000
	</select>
  <select id="getExpressInfoListForSaleorder" resultMap="BaseResultMap" >
	  SELECT
		  a.*, c. NAME LOGISTICS_NAME,
		  c. CODE
	  FROM
		  T_EXPRESS a
			  LEFT JOIN T_LOGISTICS b ON a.LOGISTICS_ID = b.LOGISTICS_ID
			  LEFT JOIN T_LOGISTICS_CODE c ON b. NAME = c. NAME
			  LEFT JOIN T_EXPRESS_DETAIL d ON a.EXPRESS_ID = d.EXPRESS_ID
			  left join T_LOGISTICS_DETAIL DETAIL ON DETAIL.LOGISTICS_NO=a.LOGISTICS_NO
	  WHERE
		  a.ARRIVAL_STATUS != 2 and b.SYNC_KUAIDI=1
		AND a.IS_ENABLE = 1
		AND a.COMPANY_ID = 1
		AND d.BUSINESS_TYPE != 515
		AND a.LOGISTICS_NO IS NOT NULL AND a.LOGISTICS_NO != ''
		and IFNULL(DETAIL.CONTENT,'') not like '%不支持此快递公司%'
		and a.DELIVERY_TIME
		  between unix_timestamp(date_add(curdate(),interval -30 day))*1000
		  and unix_timestamp(now())*1000
		AND ( a.LOGISTICS_NO NOT LIKE 'XN%'
		AND a.LOGISTICS_NO NOT LIKE 'SL%'
		AND a.LOGISTICS_NO NOT LIKE 'BD%' )
		AND b.NAME!='虚拟出入库专用承运商'
and a.LOGISTICS_COMMENTS != '虚拟快递单'
 	  GROUP BY
		  a.EXPRESS_ID
	  LIMIT 5000
  </select>


	<select id="getExpressInfoBylogisticsNo" resultMap="BaseResultMap" parameterType="com.vedeng.logistics.model.Express" >
   SELECT
		a.*, c. NAME LOGISTICS_NAME,
		c. CODE
	FROM
		T_EXPRESS a
	LEFT JOIN T_LOGISTICS b ON a.LOGISTICS_ID = b.LOGISTICS_ID
	LEFT JOIN T_LOGISTICS_CODE c ON b. NAME = c. NAME
	LEFT JOIN T_EXPRESS_DETAIL d ON a.EXPRESS_ID = d.EXPRESS_ID
	WHERE a.LOGISTICS_NO =#{logisticsNo,jdbcType=VARCHAR}
	and a.LOGISTICS_ID =#{logisticsId,jdbcType=INTEGER}
	limit 1
  </select>

   <select id="getLendOutExpressInfo" parameterType="com.vedeng.logistics.model.Express" resultMap="ExpressResultMap">
	  select a.*,b.EXPRESS_DETAIL_ID,b.BUSINESS_TYPE,b.RELATED_ID,b.NUM DNUM,b.AMOUNT,c.NAME as LOGISTICS_NAME,l.CONTENT
	  	<if test="businessType!=null and (businessType== 496 or businessType== 515 )">
	    ,d.GOODS_NAME as GOOD_NAME,d.UNIT_NAME,d.GOODS_ID as GOOD_ID,d.SKU as SKU
	  	</if>
	  	<if test="businessType!=null and (businessType== 497)">
	    ,d.AMOUNT AS INVOICE_AMOUNT,d.INVOICE_NO AS INVOICE_NO
	  	</if>
	  	<if test="businessType!=null and (businessType== 582)">
	    ,e.GOODS_NAME AS GOOD_NAME,e.SKU AS SKU
	  	</if>
	  	<if test="businessType!=null and (businessType== 660)">
	    ,d.GOODS_NAME AS GOOD_NAME,
        e.UNIT_NAME,
        d.GOODS_ID AS GOOD_ID
	  	</if>
	    from T_EXPRESS a
	    left join T_EXPRESS_DETAIL b on a.EXPRESS_ID=b.EXPRESS_ID
	    left join T_LOGISTICS c on a.LOGISTICS_ID = c.LOGISTICS_ID and c.IS_ENABLE =1
	    left join T_LOGISTICS_DETAIL  l on l.LOGISTICS_NO = a.LOGISTICS_NO and a.LOGISTICS_ID = l.LOGISTICS_ID
	    <!-- 销售 -->
	    <if test="businessType!=null and businessType== 496">
	    left join T_SALEORDER_GOODS d on d.SALEORDER_GOODS_ID = b.RELATED_ID
	    AND d.IS_DELETE =0
	    </if>
	    <!-- 发票寄送 -->
	    <if test="businessType!=null and businessType== 497">
	    left join T_INVOICE d on d.EXPRESS_ID = a.EXPRESS_ID
	    </if>
	    <!-- 采购 -->
	    <if test="businessType!=null and businessType== 515">
	    left join T_BUYORDER_GOODS d on d.BUYORDER_GOODS_ID = b.RELATED_ID
	    AND d.IS_DELETE =0
	    </if>
	    <!-- 售后 -->
	    <if test="businessType!=null and businessType== 582">
	    left join T_AFTER_SALES_GOODS d on d.AFTER_SALES_GOODS_ID = b.RELATED_ID
	    AND d.GOODS_TYPE =0 LEFT JOIN T_GOODS e ON d.GOODS_ID = e.GOODS_ID
	    </if>
	    <!-- 外接单  -->
	    <if test="businessType!=null and businessType== 660">
		LEFT JOIN T_LEND_OUT h
		ON h.`LEND_OUT_ID` = b.RELATED_ID
		LEFT JOIN T_GOODS d
		ON d.`GOODS_ID` = h.`GOODS_ID`
		LEFT JOIN T_UNIT e
		ON d.`UNIT_ID` = e.`UNIT_ID`
	    </if>
	    where 1=1
	        and a.IS_ENABLE=1
		<if test="expressId!=null and expressId!='-1'">
			and a.EXPRESS_ID=#{expressId,jdbcType=INTEGER}
		</if>
		<if test="companyId!=null and companyId!='-1'">
			and a.COMPANY_ID=#{companyId,jdbcType=INTEGER}
		</if>
		<if test="logisticsId!=null and logisticsId!='-1'">
			and a.LOGISTICS_ID=#{logisticsId,jdbcType=INTEGER}
		</if>
		<if test="logisticsNo!=null and logisticsNo!=''">
			and a.LOGISTICS_NO like CONCAT('%',#{logisticsNo,jdbcType=VARCHAR},'%' )
		</if>
		<if test="deliveryFrom!=null and deliveryFrom!='-1'">
			and a.DELIVERY_FROM=#{deliveryFrom,jdbcType=INTEGER}
		</if>
		<if test="creator!=null and creator!='-1'">
			and a.CREATOR=#{creator,jdbcType=INTEGER}
		</if>
		<if test="updater!=null and updater!='-1'">
			and a.UPDATER=#{updater,jdbcType=INTEGER}
		</if>
		<if test="expressDetailId!=null and expressDetailId!='-1'">
			and b.EXPRESS_DETAIL_ID=#{expressDetailId,jdbcType=INTEGER}
		</if>
		<if test="businessType!=null and businessType!='-1'">
			and b.BUSINESS_TYPE=#{businessType,jdbcType=INTEGER}
		</if>
		<if test="relatedId!=null and relatedId!='-1'">
			and b.RELATED_ID = #{relatedId,jdbcType=INTEGER}
		</if>
		<if test="relatedIds !=null and relatedIds.size()>0">
			and b.RELATED_ID in
			<foreach collection="relatedIds" item="relatedId" index="index"
	            open="(" close=")" separator=",">
	            #{relatedId}
	        </foreach>
		</if>
		<if test="num!=null and num!=''">
			and b.NUM=#{num,jdbcType=INTEGER}
		</if>
		<if test="amount!=null and amount!=''">
			and b.AMOUNT=#{amount,jdbcType=INTEGER}
		</if>

	    order by
	  		a.ADD_TIME desc
  </select>
    <!--发货提醒-->
<!--    <select id="shipmentToRemind" resultType="com.vedeng.logistics.model.ShipmentToRemind">
     SELECT l.LOGISTICS_NO AS logIsTicsNo,s.GOODS_NAME goodsName,f.NUM AS afterSalesNum, s.NUM AS goodsNum,s.DELIVERY_NUM AS DeliveryNum,ts.VALID_TIME AS validTime,TRADER_CONTACT_NAME AS taaderContactName
     ,TRADER_CONTACT_MOBILE AS traderContactMobile,lo.NAME ligisticsName,ts.SALEORDER_NO SaleOrderNo
     FROM
     T_EXPRESS l
     LEFT JOIN
     T_EXPRESS_DETAIL t
     ON l.EXPRESS_ID=t.EXPRESS_ID
     LEFT JOIN T_LOGISTICS lo
     ON l.LOGISTICS_ID=lo.LOGISTICS_ID
     LEFT JOIN T_SALEORDER_GOODS s
     ON t.RELATED_ID=s.SALEORDER_GOODS_ID
     LEFT JOIN T_AFTER_SALES_GOODS f
     ON  f.ORDER_DETAIL_ID=s.SALEORDER_GOODS_ID
     LEFT JOIN T_SALEORDER ts
     ON ts.SALEORDER_ID=s.SALEORDER_ID
    </select>-->

   <!--根据快递单号查询发货的商品数量和sku-->
	<select id="selectExpressGood" resultType="com.vedeng.logistics.model.LogisticsOrderGoodsData">
		 SELECT tg.SKU skuNo,td.NUM num FROM T_EXPRESS te
		 LEFT JOIN T_EXPRESS_DETAIL td
		 ON te.EXPRESS_ID=td.EXPRESS_ID
		 LEFT JOIN T_SALEORDER_GOODS tg
		 ON td.RELATED_ID =tg.SALEORDER_GOODS_ID
		 WHERE te.EXPRESS_ID=#{expressId}
	</select>

	<insert id="batchInsert" parameterType="java.util.List">
		insert into T_LOGISTICS_DETAIL( LOGISTICS_NO, CONTENT, MOD_TIME,LOGISTICS_ID)
		values
		<foreach collection="list" item="data" index="index" separator=",">
			(
			#{data.logisticsNo,jdbcType=VARCHAR},
			#{data.content,jdbcType=VARCHAR},
			#{data.modTime,jdbcType=BIGINT},
			#{data.logisticsId,jdbcType=INTEGER}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		LOGISTICS_NO = VALUES(LOGISTICS_NO),
		CONTENT = VALUES(CONTENT),
		MOD_TIME = VALUES(MOD_TIME),
		LOGISTICS_ID = VALUES(LOGISTICS_ID)
	</insert>
	<select id="getExpressDetailList" parameterType="com.vedeng.order.model.Saleorder" resultMap="ExpressDetailResultMap">
		SELECT
			A.*
		FROM
			T_EXPRESS_DETAIL A
			LEFT JOIN T_SALEORDER_GOODS B ON A.RELATED_ID = B.SALEORDER_GOODS_ID
			LEFT JOIN T_SALEORDER C ON B.SALEORDER_ID = C.SALEORDER_ID
			LEFT JOIN T_EXPRESS D ON A.EXPRESS_ID = D.EXPRESS_ID
		WHERE C.SALEORDER_ID= #{saleorderId,jdbcType=INTEGER}
		AND D.IS_ENABLE=1
		AND B.IS_DELETE=0
		AND D.LOGISTICS_COMMENTS != '虚拟快递单'
		<if test="expressId != null">
			AND D.EXPRESS_ID != #{expressId,jdbcType=INTEGER}
		</if>
		<if test="logisticsNo != null">
			AND D.LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
		</if>
	</select>
	<select id="getExpressDetailNumByExpressId" parameterType="com.vedeng.logistics.model.ExpressDetail" resultMap="ExpressDetailResultMap">
		SELECT A.* FROM T_EXPRESS_DETAIL A WHERE
		 A.EXPRESS_ID= #{expressId,jdbcType=INTEGER}
		AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
	</select>

	<select id="getExpressDetailByExpressId" resultMap="ExpressDetailResultMap">
		SELECT A.* FROM T_EXPRESS_DETAIL A WHERE
		 A.EXPRESS_ID= #{expressId,jdbcType=INTEGER}
	</select>

	<!--查询是否第一次物流-->
	<select id="getFirst" resultMap="BaseResultMap">
		select DISTINCT a.EXPRESS_ID  FROM `T_EXPRESS` a left join T_EXPRESS_DETAIL b on a.EXPRESS_ID=b.EXPRESS_ID
		left join T_SALEORDER_GOODS c on c.SALEORDER_GOODS_ID=b.RELATED_ID
		left join T_SALEORDER d on c.SALEORDER_ID=d.SALEORDER_ID
		  where d.TRADER_ID=#{traId} and a.IS_ENABLE=1 limit 1
	</select>
	<select id="getCountOfExpressNotAllReceived" resultType="java.lang.Integer">
		SELECT
			COUNT(*)
		FROM
			T_SALEORDER_GOODS SG
				JOIN T_EXPRESS_DETAIL ED ON SG.SALEORDER_GOODS_ID = ED.RELATED_ID
				JOIN T_EXPRESS E ON ED.EXPRESS_ID = E.EXPRESS_ID
		WHERE
			SG.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
		  AND E.ARRIVAL_STATUS != 2;
	</select>
	<select id="getSaleorderGoodCountHasReceived" resultType="java.lang.Integer">
		SELECT
			IFNULL(SUM( ED.NUM ),0)
		FROM
			T_EXPRESS_DETAIL ED
				JOIN T_EXPRESS E ON ED.EXPRESS_ID = E.EXPRESS_ID
		WHERE
			E.ARRIVAL_STATUS != 0
		  AND RELATED_ID =(
			SELECT
				RELATED_ID
			FROM
				T_EXPRESS_DETAIL
			WHERE
				EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
		);
	</select>
	<select id="getSaleorderGoodsNumOfExpress" resultType="java.lang.Integer">
		SELECT
			IFNULL( SUM( NUM ), 0 )
		FROM
			T_EXPRESS_DETAIL
		WHERE
			RELATED_ID = #{saleorderGoodsId,jdbcType=INTEGER};
	</select>

	<!--改变是否开据发票的状态-->
	<update id="changeIsinvoicing" parameterType="java.lang.Integer">
			UPDATE T_EXPRESS SET IS_INVOICING=2 WHERE EXPRESS_ID=(SELECT EXPRESS_ID FROM T_INVOICE_APPLY WHERE INVOICE_APPLY_ID = #{invoiceApplyId}) AND IS_INVOICING=1
	</update>

	<!--改变是否开据发票的状态-->
	<update id="updateIsinvoicing" parameterType="java.lang.Integer">
			UPDATE T_EXPRESS SET IS_INVOICING=1 WHERE EXPRESS_ID=#{expressId} AND IS_INVOICING=0
	</update>

	<!--改变是否开据发票的状态-->
	<update id="updateIsinvoicingNo" parameterType="java.lang.Integer">
			UPDATE T_EXPRESS SET IS_INVOICING=0 WHERE EXPRESS_ID=#{expressId} AND IS_INVOICING=1
	</update>
	<select id="getExpressIdByWlogId" resultType="integer">
		SELECT
			A.EXPRESS_ID
		FROM
			T_EXPRESS A
			LEFT JOIN T_EXPRESS_DETAIL B ON A.EXPRESS_ID = B.EXPRESS_ID
			LEFT JOIN V_E_W_EXPRESS_WAREHOUSE C ON C.EXPRESS_DETAIL_ID = B.EXPRESS_DETAIL_ID
		WHERE
			A.IS_ENABLE = 1 AND C.IS_ENABLE= 0
			AND C.WAREHOUSE_GOODS_OPERATE_LOG_ID = #{wlogId}
	</select>
	<!-- 查询销售产品已签收的快递数 -->
	<select id="getSEGoodsNum" parameterType="com.vedeng.logistics.model.Express" resultMap="VoResultMap">
	   SELECT
			(A.ALLNUM - IFNULL(T.SHNUM, 0)) ALLNUM,
			IFNULL(B.ALLNUM, 0) FNUM
		FROM
			(
				SELECT
					a.SALEORDER_GOODS_ID,
					IFNULL(a.NUM, 0) ALLNUM
				FROM
					T_SALEORDER_GOODS a
				LEFT JOIN T_EXPRESS_DETAIL b ON a.SALEORDER_GOODS_ID = b.RELATED_ID
				AND b.BUSINESS_TYPE = 496
				LEFT JOIN T_EXPRESS c ON b.EXPRESS_ID = c.EXPRESS_ID
				AND c.IS_ENABLE = 1
				WHERE
					1 = 1
				AND a.SALEORDER_GOODS_ID = #{orderGoodsId,jdbcType=INTEGER}
				GROUP BY
					a.SALEORDER_GOODS_ID
			) A
		LEFT JOIN (
			SELECT
				a.SALEORDER_GOODS_ID,
				IFNULL(SUM(b.NUM), 0) ALLNUM
			FROM
				T_SALEORDER_GOODS a
			LEFT JOIN T_EXPRESS_DETAIL b ON a.SALEORDER_GOODS_ID = b.RELATED_ID
			AND b.BUSINESS_TYPE = 496
			LEFT JOIN T_EXPRESS c ON b.EXPRESS_ID = c.EXPRESS_ID
			AND c.IS_ENABLE = 1
			WHERE
				1 = 1
			AND a.SALEORDER_GOODS_ID = #{orderGoodsId,jdbcType=INTEGER}
			AND c.ARRIVAL_STATUS = 2
			GROUP BY
				a.SALEORDER_GOODS_ID
		) B ON A.SALEORDER_GOODS_ID = B.SALEORDER_GOODS_ID
		LEFT JOIN (
			SELECT
				SUM(b.NUM) SHNUM,
				b.ORDER_DETAIL_ID
			FROM
				T_AFTER_SALES_GOODS b
			LEFT JOIN T_AFTER_SALES c ON b.AFTER_SALES_ID = c.AFTER_SALES_ID
			WHERE
				b.GOODS_TYPE = 0
			AND b.ORDER_DETAIL_ID = #{orderGoodsId,jdbcType=INTEGER}
			AND c.TYPE = 539
			AND c.SUBJECT_TYPE = 535
			AND c.ATFER_SALES_STATUS != 3
			AND b.GOODS_ID NOT IN (
				SELECT
					COMMENTS
				FROM
					T_SYS_OPTION_DEFINITION
				WHERE
					PARENT_ID = 693
			)
			GROUP BY
				b.ORDER_DETAIL_ID
		) T ON A.SALEORDER_GOODS_ID = T.ORDER_DETAIL_ID

  </select>

	<select id="getExpressDetailListByBuyorderId" resultMap="ExpressDetailResultMap">
	SELECT
	C.NUM,D.EXPRESS_ID,C.RELATED_ID
	FROM
		 T_BUYORDER A
 	LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_ID=B.BUYORDER_ID
	LEFT JOIN T_EXPRESS_DETAIL C ON C.RELATED_ID=B.BUYORDER_GOODS_ID
	JOIN T_EXPRESS D ON C.EXPRESS_ID=D.EXPRESS_ID
	WHERE
	C.BUSINESS_TYPE=515
	AND A.BUYORDER_ID= #{buyorderId,jdbcType=INTEGER}
	</select>
	<!-- 根据快递单号查询快递详情 -->
	<select id="getExpressDetailsList" parameterType="com.vedeng.logistics.model.Express" resultType="com.vedeng.logistics.model.ExpressDetail">
		SELECT
		a.EXPRESS_ID AS expressId,
		b.BUSINESS_TYPE AS businessType,
		b.RELATED_ID  AS relatedId,
		b.NUM,
		b.EXPRESS_DETAIL_ID
		FROM
		T_EXPRESS a
		LEFT JOIN T_EXPRESS_DETAIL b ON a.EXPRESS_ID = b.EXPRESS_ID
		WHERE
		 a.IS_ENABLE = 1
		AND a.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
	</select>

    <select id="getExpressInfoByWmsNo" resultType="com.vedeng.logistics.model.Express">
	SELECT
	A.*
FROM
	T_EXPRESS A
	LEFT JOIN T_EXPRESS_DETAIL B ON A.EXPRESS_ID = B.EXPRESS_ID
	LEFT JOIN T_SALEORDER_GOODS C ON B.RELATED_ID = C.SALEORDER_GOODS_ID
WHERE
	A.WMS_ORDER_NO =  #{wmsOrderNo,jdbcType=VARCHAR}
	AND B.BUSINESS_TYPE = 496
	AND C.SALEORDER_ID = #{orderId,jdbcType=INTEGER}
	AND A.LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
	GROUP BY A.EXPRESS_ID
	</select>
    <select id="getExpressInfoByLogNoAndComments" resultType="com.vedeng.logistics.model.Express">
	SELECT
	A.*
FROM
	T_EXPRESS A
WHERE
	A.WMS_ORDER_NO =  #{wmsOrderNo,jdbcType=VARCHAR}
	AND A.LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
	AND A.LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR}
	AND A.IS_ENABLE = 1
	</select>
	<select id="getExpressInfoByLogAndComments" resultType="com.vedeng.logistics.model.Express">
		SELECT
	A.*
FROM
	T_EXPRESS A
WHERE
	 A.LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
	AND A.LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR}
	</select>
	<select id="getExpressInfoById" resultType="com.vedeng.logistics.model.Express">
		SELECT
			a.*,
		    c. NAME LOGISTICS_NAME,
			c. CODE
		FROM
			T_EXPRESS a
				LEFT JOIN T_LOGISTICS b ON a.LOGISTICS_ID = b.LOGISTICS_ID
				LEFT JOIN T_LOGISTICS_CODE c ON b. NAME = c. NAME
		WHERE a.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
		GROUP BY a.EXPRESS_ID
	</select>
    <select id="getExpressDetailsListByBuyorderGoodsId" resultType="com.vedeng.logistics.model.ExpressDetail">
		SELECT NUM AS num,EXPRESS_DETAIL_ID AS expressDetailId,EXPRESS_ID AS expressId
		FROM T_EXPRESS_DETAIL
		WHERE RELATED_ID = #{buyorderGoodsId,jdbcType = INTEGER}
		and BUSINESS_TYPE = 515
	</select>

	<select id="getGoodsAmountByExpressId" resultType="java.math.BigDecimal">
		SELECT
		IF
			(
				T2.BUSINESS_TYPE = 496,
				SUM( T2.NUM * T3.PRICE ),
				SUM( T2.NUM * T5.PRICE )
			)
		FROM
			T_EXPRESS T1
			LEFT JOIN T_EXPRESS_DETAIL T2 ON T1.EXPRESS_ID = T2.EXPRESS_ID
			LEFT JOIN T_SALEORDER_GOODS T3 ON T2.RELATED_ID = T3.SALEORDER_GOODS_ID
			LEFT JOIN T_R_BUYORDER_J_SALEORDER T4 ON T2.RELATED_ID = T4.BUYORDER_GOODS_ID
			LEFT JOIN T_SALEORDER_GOODS T5 ON T4.SALEORDER_GOODS_ID = T5.SALEORDER_GOODS_ID
		WHERE
				T1.IS_ENABLE = 1
			AND T1.EXPRESS_ID = #{expressId,jdbcType = INTEGER}
			AND T1.LOGISTICS_COMMENTS != '虚拟快递单'
	</select>
	<select id="selectAmountByExpressId" resultType="com.vedeng.logistics.model.Express">
		SELECT
			a.LOGISTICS_NO,
			( b.NUM * c.PRICE ) AS AMOUNT
		FROM
			T_EXPRESS a
			LEFT JOIN T_EXPRESS_DETAIL b ON a.EXPRESS_ID = b.EXPRESS_ID
			LEFT JOIN T_SALEORDER_GOODS c ON b.RELATED_ID = c.SALEORDER_GOODS_ID
		WHERE a.EXPRESS_ID = #{expressId,jdbcType=INTEGER}

	</select>

	<select id="getExpressIdsThisDeliveryBefore" resultType="java.lang.Integer">
		SELECT
			P.EXPRESS_ID
		FROM
			(
			SELECT
				T3.EXPRESS_ID
			FROM
				T_SALEORDER T1
				LEFT JOIN T_SALEORDER_GOODS T2 ON T1.SALEORDER_ID = T2.SALEORDER_ID
				AND T2.IS_DELETE = 0
				LEFT JOIN T_EXPRESS_DETAIL T3 ON T2.SALEORDER_GOODS_ID = T3.RELATED_ID
				AND T3.BUSINESS_TYPE = 496
				LEFT JOIN T_EXPRESS T4 ON T3.EXPRESS_ID = T4.EXPRESS_ID
			WHERE
				T1.SALEORDER_ID = #{orderId,jdbcType=INTEGER}
				AND T4.LOGISTICS_COMMENTS != '虚拟快递单'
			GROUP BY
				T3.EXPRESS_ID UNION ALL
			SELECT
				T14.EXPRESS_ID
			FROM
				T_SALEORDER T11
				LEFT JOIN T_SALEORDER_GOODS T12 ON T11.SALEORDER_ID = T12.SALEORDER_ID
				AND T12.IS_DELETE = 0
				LEFT JOIN T_R_BUYORDER_J_SALEORDER T13 ON T12.SALEORDER_GOODS_ID = T13.SALEORDER_GOODS_ID
				LEFT JOIN T_EXPRESS_DETAIL T14 ON T13.BUYORDER_GOODS_ID = T14.RELATED_ID
				AND T14.BUSINESS_TYPE = 515
				LEFT JOIN T_EXPRESS T15 ON T14.EXPRESS_ID = T15.EXPRESS_ID
			WHERE
				T11.SALEORDER_ID = #{orderId,jdbcType=INTEGER}
				AND T15.LOGISTICS_COMMENTS != '虚拟快递单'
			GROUP BY
				T14.EXPRESS_ID
			) P
		WHERE
			P.EXPRESS_ID IS NOT NULL
			AND P.EXPRESS_ID != #{expressId,jdbcType=INTEGER}
		GROUP BY
			P.EXPRESS_ID
	</select>

	<select id="getExpressInfo" parameterType="com.vedeng.logistics.model.Express" resultMap="ExpressResultMap">
		select a.*,b.EXPRESS_DETAIL_ID,b.BUSINESS_TYPE,b.RELATED_ID,b.NUM DNUM,b.AMOUNT,c.NAME as LOGISTICS_NAME,l.CONTENT
		<if test="businessType!=null and (businessType== 496 or businessType== 515 )">
			,d.GOODS_NAME as GOOD_NAME,d.UNIT_NAME,d.GOODS_ID as GOOD_ID,d.SKU as SKU
		</if>
		<if test="businessType!=null and businessType== 496 ">
			,d.IS_ACTION_GOODS
		</if>

		<if test="businessType!=null and (businessType== 497)">
			,d.AMOUNT AS INVOICE_AMOUNT,d.INVOICE_NO AS INVOICE_NO
		</if>
		<if test="businessType!=null and (businessType== 582)">
			,e.GOODS_NAME AS GOOD_NAME,e.SKU AS SKU
		</if>
		from T_EXPRESS a
		left join T_EXPRESS_DETAIL b on a.EXPRESS_ID=b.EXPRESS_ID
		left join T_LOGISTICS c on a.LOGISTICS_ID = c.LOGISTICS_ID and c.IS_ENABLE =1
		left join T_LOGISTICS_DETAIL  l on l.LOGISTICS_NO = a.LOGISTICS_NO and a.LOGISTICS_ID = l.LOGISTICS_ID
		<!-- 销售 -->
		<if test="businessType!=null and businessType== 496">
			left join T_SALEORDER_GOODS d on d.SALEORDER_GOODS_ID = b.RELATED_ID
			AND d.IS_DELETE =0
		</if>
		<!-- 发票寄送 -->
		<if test="businessType!=null and businessType== 497">
			left join T_INVOICE d on d.EXPRESS_ID = a.EXPRESS_ID
		</if>
		<!-- 采购 -->
		<if test="businessType!=null and businessType== 515">
			left join T_BUYORDER_GOODS d on d.BUYORDER_GOODS_ID = b.RELATED_ID
			AND d.IS_DELETE =0
		</if>
		<!-- 售后 -->
		<if test="businessType!=null and businessType== 582">
			left join T_AFTER_SALES_GOODS d on d.AFTER_SALES_GOODS_ID = b.RELATED_ID
			AND d.GOODS_TYPE =0 LEFT JOIN T_GOODS e ON d.GOODS_ID = e.GOODS_ID
		</if>
		where 1=1
		and (a.LOGISTICS_COMMENTS IS NULL OR a.LOGISTICS_COMMENTS !='虚拟快递单')
		and a.IS_ENABLE=1
		<if test="expressId!=null and expressId!='-1'">
			and a.EXPRESS_ID=#{expressId,jdbcType=INTEGER}
		</if>
		<if test="companyId!=null and companyId!='-1'">
			and a.COMPANY_ID=#{companyId,jdbcType=INTEGER}
		</if>
		<if test="logisticsId!=null and logisticsId!='-1'">
			and a.LOGISTICS_ID=#{logisticsId,jdbcType=INTEGER}
		</if>
		<if test="logisticsNo!=null and logisticsNo!=''">
			and a.LOGISTICS_NO like CONCAT('%',#{logisticsNo,jdbcType=VARCHAR},'%' )
		</if>
		<if test="deliveryFrom!=null and deliveryFrom!='-1'">
			and a.DELIVERY_FROM=#{deliveryFrom,jdbcType=INTEGER}
		</if>
		<if test="deliveryQuryTime!=null and deliveryQuryTime!='-1'">
			and a.DELIVERY_TIME=#{deliveryQuryTime,jdbcType=BIGINT}
		</if>
		<if test="creator!=null and creator!='-1'">
			and a.CREATOR=#{creator,jdbcType=INTEGER}
		</if>
		<if test="updater!=null and updater!='-1'">
			and a.UPDATER=#{updater,jdbcType=INTEGER}
		</if>
		<if test="expressDetailId!=null and expressDetailId!='-1'">
			and b.EXPRESS_DETAIL_ID=#{expressDetailId,jdbcType=INTEGER}
		</if>
		<if test="businessType!=null and businessType!='-1'">
			and b.BUSINESS_TYPE=#{businessType,jdbcType=INTEGER}
		</if>
		<if test="relatedId!=null and relatedId!='-1'">
			and b.RELATED_ID = #{relatedId,jdbcType=INTEGER}
		</if>
		<if test="relatedIds !=null and relatedIds.size()>0">
			and b.RELATED_ID in
			<foreach collection="relatedIds" item="relatedId" index="index"
					 open="(" close=")" separator=",">
				#{relatedId}
			</foreach>
		</if>
		<if test="num!=null and num!=''">
			and b.NUM=#{num,jdbcType=INTEGER}
		</if>
		<if test="amount!=null and amount!=''">
			and b.AMOUNT=#{amount,jdbcType=INTEGER}
		</if>

		order by
		a.ADD_TIME desc
	</select>


	<insert id="insertSelective" parameterType="com.vedeng.logistics.model.Express" useGeneratedKeys="true" keyProperty="expressId">
		insert into T_EXPRESS
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="expressId != null" >
				EXPRESS_ID,
			</if>
			<if test="logisticsId != null" >
				LOGISTICS_ID,
			</if>
			<if test="logisticsNo != null" >
				LOGISTICS_NO,
			</if>
			<if test="companyId != null" >
				COMPANY_ID,
			</if>
			<if test="deliveryTime != null" >
				DELIVERY_TIME,
			</if>
			<if test="arrivalStatus != null" >
				ARRIVAL_STATUS,
			</if>
			<if test="arrivalTime != null" >
				ARRIVAL_TIME,
			</if>
			<if test="deliveryFrom != null" >
				DELIVERY_FROM,
			</if>
			<if test="logisticsComments != null" >
				LOGISTICS_COMMENTS,
			</if>
			<if test="addTime != null" >
				ADD_TIME,
			</if>
			<if test="creator != null" >
				CREATOR,
			</if>
			<if test="modTime != null" >
				MOD_TIME,
			</if>
			<if test="updater != null" >
				UPDATER,
			</if>
			<if test="isEnable != null" >
				IS_ENABLE,
			</if>
			<if test="paymentType != null" >
				PAYMENT_TYPE,
			</if>
			<if test="cardnumber != null" >
				CARD_NUMBER,
			</if>
			<if test="business_Type != null" >
				BUSINESS_TYPE,
			</if>
			<if test="realWeight != null" >
				REAL_WEIGHT,
			</if>
			<if test="j_num != null" >
				NUM,
			</if>
			<if test="amountWeight != null" >
				AMOUNT_WEIGHT,
			</if>
			<if test="mailGoods != null" >
				MAIL_GOODS,
			</if>
			<if test="mailGoodsNum != null" >
				MAIL_GOODS_NUM,
			</if>
			<if test="isProtectPrice != null" >
				IS_PROTECT_PRICE,
			</if>
			<if test="protectPrice != null" >
				PROTECT_PRICE,
			</if>
			<if test="isReceipt != null" >
				IS_RECEIPT,
			</if>
			<if test="mailCommtents != null" >
				MAIL_COMMTENTS,
			</if>
			<if test="travelingByTicket != null" >
				TRAVELING_BY_TICKET,
			</if>
			<if test="isInvoicing != null">
				IS_INVOICING,
			</if>
			<if test="wmsOrderNo != null">
				WMS_ORDER_NO,
			</if>
			<if test="batchNo != null">
				BATCH_NO,
			</if>
			<if test="enableReceive != null">
				ENABLE_RECEIVE,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides="," >
			<if test="expressId != null" >
				#{expressId,jdbcType=INTEGER},
			</if>
			<if test="logisticsId != null" >
				#{logisticsId,jdbcType=INTEGER},
			</if>
			<if test="logisticsNo != null" >
				#{logisticsNo,jdbcType=VARCHAR},
			</if>
			<if test="companyId != null" >
				#{companyId,jdbcType=INTEGER},
			</if>
			<if test="deliveryTime != null" >
				#{deliveryTime,jdbcType=BIGINT},
			</if>
			<if test="arrivalStatus != null" >
				#{arrivalStatus,jdbcType=BIT},
			</if>
			<if test="arrivalTime != null" >
				#{arrivalTime,jdbcType=BIGINT},
			</if>
			<if test="deliveryFrom != null" >
				#{deliveryFrom,jdbcType=INTEGER},
			</if>
			<if test="logisticsComments != null" >
				#{logisticsComments,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null" >
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null" >
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null" >
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null" >
				#{updater,jdbcType=INTEGER},
			</if>
			<if test="isEnable != null" >
				#{isEnable,jdbcType=BIT},
			</if>
			<if test="paymentType != null" >
				#{paymentType,jdbcType=INTEGER},
			</if>
			<if test="cardnumber != null" >
				#{cardnumber,jdbcType=VARCHAR},
			</if>
			<if test="business_Type != null" >
				#{business_Type,jdbcType=INTEGER},
			</if>
			<if test="realWeight != null" >
				#{realWeight,jdbcType=DECIMAL},
			</if>
			<if test="j_num != null" >
				#{j_num,jdbcType=INTEGER},
			</if>
			<if test="amountWeight != null" >
				#{amountWeight,jdbcType=DECIMAL},
			</if>
			<if test="mailGoods != null" >
				#{mailGoods,jdbcType=VARCHAR},
			</if>
			<if test="mailGoodsNum != null" >
				#{mailGoodsNum,jdbcType=INTEGER},
			</if>
			<if test="isProtectPrice != null" >
				#{isProtectPrice,jdbcType=INTEGER},
			</if>
			<if test="protectPrice != null" >
				#{protectPrice,jdbcType=DECIMAL},
			</if>
			<if test="isReceipt != null" >
				#{isReceipt,jdbcType=INTEGER},
			</if>
			<if test="mailCommtents != null" >
				#{mailCommtents,jdbcType=VARCHAR},
			</if>
			<if test="travelingByTicket != null" >
				#{travelingByTicket,jdbcType=INTEGER},
			</if>
			<if test="isInvoicing != null">
				#{isInvoicing,jdbcType=INTEGER},
			</if>
			<if test="wmsOrderNo != null">
				#{wmsOrderNo,jdbcType=VARCHAR},
			</if>
			<if test="batchNo != null">
				#{batchNo,jdbcType=VARCHAR},
			</if>
			<if test="enableReceive != null">
				#{enableReceive,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>

	<select id="getExpressById" parameterType="com.vedeng.logistics.model.Express" resultType="com.vedeng.logistics.model.Express">
		select
	    	a.EXPRESS_ID, a.LOGISTICS_ID, a.LOGISTICS_NO, a.COMPANY_ID, a.DELIVERY_TIME, a.ARRIVAL_STATUS, a.ARRIVAL_TIME,
    		a.DELIVERY_FROM, a.LOGISTICS_COMMENTS, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,a.BUSINESS_TYPE,a.COMMUNICATE_RECORDER_IDS,
    		b.NAME as logisticsName
	    from T_EXPRESS a
	    left join
	    	T_LOGISTICS b
	    on
	    	a.LOGISTICS_ID = b.LOGISTICS_ID
	    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
	</select>
	<select id="getBuyorderByExpressId" resultType="java.lang.Integer">
		select  bg.BUYORDER_ID
		from T_EXPRESS  ex
		inner join T_EXPRESS_DETAIL  ed on ex.EXPRESS_ID = ed.EXPRESS_ID
		LEFT JOIN T_BUYORDER_GOODS bg on bg.BUYORDER_GOODS_ID = ed.RELATED_ID
		WHERE ex.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
		and bg.BUYORDER_ID is not null
		limit 1
	</select>
	<select id="getEnableExpressDetailListByBuyorderId" resultType="com.vedeng.logistics.model.ExpressDetail">
		SELECT
		C.NUM,D.EXPRESS_ID,C.RELATED_ID
		FROM
			 T_BUYORDER A
		LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_ID=B.BUYORDER_ID
		LEFT JOIN T_EXPRESS_DETAIL C ON C.RELATED_ID=B.BUYORDER_GOODS_ID
		JOIN T_EXPRESS D ON C.EXPRESS_ID=D.EXPRESS_ID
		WHERE
		C.BUSINESS_TYPE=515
		AND D.IS_ENABLE = 1
		AND C.EXPRESS_DETAIL_ID IS NOT NULL
		AND A.BUYORDER_ID= #{buyorderId,jdbcType=INTEGER}
	</select>


	<update id="logicalDeleteExpress">
		UPDATE T_EXPRESS
		SET IS_ENABLE = 0
		WHERE EXPRESS_ID = #{expressId,jdbcType=INTEGER}
	</update>
	<update id="updateArrivalStatusByRelatedKey">
		UPDATE T_EXPRESS a
		SET a.ARRIVAL_STATUS = 2,
			a.ARRIVAL_TIME = unix_timestamp(now()) * 1000
		WHERE
			a.EXPRESS_ID IN
				<foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
					#{item.expressId,jdbcType=INTEGER}
				</foreach>
	</update>

	<select id="getTotalNumInExpressDetailByBuyorderId" resultType="java.lang.Integer">
		SELECT
		SUM( ED.NUM )
		FROM
		T_EXPRESS_DETAIL ED
		JOIN T_EXPRESS E ON ED.EXPRESS_ID = E.EXPRESS_ID
		JOIN T_BUYORDER_GOODS BG ON BG.BUYORDER_GOODS_ID = ED.RELATED_ID
		WHERE
		E.IS_ENABLE = 1
		AND ED.BUSINESS_TYPE = 515
		AND BG.BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
		AND BG.IS_DELETE = 0
	</select>
    <select id="selectExpressByPrimaryKey" resultType="com.vedeng.logistics.model.Express">
		select * from T_EXPRESS where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
	</select>
	<select id="getAllGoodsAmountByExpressId" resultType="java.math.BigDecimal">
		SELECT
		IF
			(
				T2.BUSINESS_TYPE = 496,
				SUM( T2.NUM * T3.PRICE ),
				SUM( T2.NUM * T5.PRICE )
			)
		FROM
			T_EXPRESS T1
			LEFT JOIN T_EXPRESS_DETAIL T2 ON T1.EXPRESS_ID = T2.EXPRESS_ID
			LEFT JOIN T_SALEORDER_GOODS T3 ON T2.RELATED_ID = T3.SALEORDER_GOODS_ID
			LEFT JOIN T_R_BUYORDER_J_SALEORDER T4 ON T2.RELATED_ID = T4.BUYORDER_GOODS_ID
			LEFT JOIN T_SALEORDER_GOODS T5 ON T4.SALEORDER_GOODS_ID = T5.SALEORDER_GOODS_ID
		WHERE
				T1.EXPRESS_ID = #{expressId,jdbcType = INTEGER}
			AND T1.LOGISTICS_COMMENTS != '虚拟快递单'
	</select>

	<select id="getBuyorderInfoByExpressId" resultType="com.vedeng.order.model.Buyorder" parameterType="java.lang.Integer">

	SELECT DISTINCT
		b.BUYORDER_ID bid,
		b.*
	FROM
		T_EXPRESS_DETAIL ed
		INNER JOIN T_BUYORDER_GOODS bg ON ed.RELATED_ID = bg.BUYORDER_GOODS_ID
		INNER JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
	WHERE
		EXPRESS_ID = #{expressId,jdbcType = INTEGER}
	</select>
	<select id="getPurchaseDeliveryDirectBatchDetail" resultType="java.lang.Integer" parameterType="java.lang.Integer">
	SELECT
		count(*)
	FROM
		T_EXPRESS_DETAIL ed
		INNER JOIN T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL pd ON ed.EXPRESS_DETAIL_ID = pd.EXPRESS_DETAIL_ID
		WHERE ed.EXPRESS_ID = #{expressId,jdbcType = INTEGER}

	</select>

	<select id="getExpressListByOrderNo" resultType="com.vedeng.logistics.model.Express">
		SELECT
			IF
				( EX.EXPRESS_ID IS NOT NULL, EX.EXPRESS_ID, BE.EXPRESS_ID ) EXPRESS_ID,
			IF
				( EX.EXPRESS_ID IS NOT NULL, EX.LOGISTICS_NO, BE.LOGISTICS_NO ) LOGISTICS_NO,
			IF
				( EX.ONLINE_RECEIPT_ID IS NOT NULL, EX.ONLINE_RECEIPT_ID, BE.ONLINE_RECEIPT_ID ) ONLINE_RECEIPT_ID
		FROM
			T_SALEORDER A
				LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID = B.SALEORDER_ID
				LEFT JOIN T_EXPRESS_DETAIL ED ON ED.RELATED_ID = B.SALEORDER_GOODS_ID
				AND ED.BUSINESS_TYPE = 496
				LEFT JOIN T_EXPRESS EX ON ED.EXPRESS_ID = EX.EXPRESS_ID
				AND EX.LOGISTICS_COMMENTS != '虚拟快递单'
				AND EX.IS_ENABLE = 1
				LEFT JOIN T_R_BUYORDER_J_SALEORDER BS ON BS.SALEORDER_GOODS_ID = B.SALEORDER_GOODS_ID
				AND B.DELIVERY_DIRECT = 1
				LEFT JOIN T_EXPRESS_DETAIL BED ON BED.RELATED_ID = BS.BUYORDER_GOODS_ID
				AND BED.BUSINESS_TYPE = 515
				LEFT JOIN T_EXPRESS BE ON BE.EXPRESS_ID = BED.EXPRESS_ID
				AND BE.LOGISTICS_COMMENTS != '虚拟快递单'
				AND BE.IS_ENABLE = 1
		WHERE
			A.SALEORDER_NO = #{orderNo,jdbcType=VARCHAR}
		  AND ( BE.EXPRESS_ID IS NOT NULL OR EX.EXPRESS_ID IS NOT NULL )
		GROUP BY
			A.SALEORDER_ID,
			EX.EXPRESS_ID,
			BE.EXPRESS_ID,
			B.SALEORDER_GOODS_ID
	</select>

	<update id="updateOnLineReceiptIdByExpressIds">
		UPDATE T_EXPRESS
		SET ONLINE_RECEIPT_ID = #{onlineReceiptId,jdbcType = INTEGER}
		WHERE
			EXPRESS_ID IN
		<foreach collection="expressIdList" item="expressId" index="index"
				 open="(" close=")" separator=",">
			#{expressId,jdbcType=INTEGER}
		</foreach>
	</update>
	<update id="updateExpressArrivalStatusByLogisticsNo">
		UPDATE T_EXPRESS
		SET ARRIVAL_STATUS = 2
		WHERE LOGISTICS_NO = #{logisticsNo,jdbcType = VARCHAR}
		  AND EXPRESS_ID IN
		<foreach collection="expressIdList" item="expressId" index="index"
				 open="(" close=")" separator=",">
			#{expressId,jdbcType=INTEGER}
		</foreach>
		  AND ARRIVAL_STATUS != 2
	</update>

	<select id="getExpressOnlineReceiptListByOrderId"
			resultType="com.vedeng.order.model.vo.ExpressOnlineReceiptVo">
		SELECT
			T2.LOGISTICS_NO,
			T3.`NAME` LOGISTICS_NAME,
			T1.TRADER_NAME,
			T1.TRADER_ID,
			T1.MOBILE,
			T1.USER_ID,
			T1.SIGN_TIME,
			T1.COMMENTS
		FROM
			T_EXPRESS_ONLINE_RECCEIPT_REORD T1
				INNER JOIN T_EXPRESS T2 ON T1.EXPRESS_ONLINE_RECCEIPT_REORD_ID = T2.ONLINE_RECEIPT_ID
				AND T2.LOGISTICS_COMMENTS != '虚拟快递单'
	LEFT JOIN T_LOGISTICS T3 ON T2.LOGISTICS_ID = T3.LOGISTICS_ID
		WHERE
			T1.ORDER_ID = #{orderId,jdbcType=INTEGER}
	GROUP BY
		T2.EXPRESS_ID
	</select>

	<select id="getExpressOnlineReceiptListByOnlineReceiptId"
			resultType="com.vedeng.order.model.vo.ExpressOnlineReceiptVo">
		SELECT
		T2.LOGISTICS_NO,
		T3.`NAME` LOGISTICS_NAME,
		T1.TRADER_NAME,
		T1.TRADER_ID,
		T1.MOBILE,
		T1.USER_ID,
		T1.SIGN_TIME,
		T1.COMMENTS,
		T1.ORDER_ID,
		T1.EXPRESS_ONLINE_RECCEIPT_REORD_ID,
		T4.SALEORDER_NO,
		T4.TRADER_CONTACT_NAME,
		T2.EXPRESS_ID
		FROM
		T_EXPRESS_ONLINE_RECCEIPT_REORD T1
		INNER JOIN T_EXPRESS T2 ON T1.EXPRESS_ONLINE_RECCEIPT_REORD_ID = T2.ONLINE_RECEIPT_ID
		AND T2.LOGISTICS_COMMENTS != '虚拟快递单'
		LEFT JOIN T_LOGISTICS T3 ON T2.LOGISTICS_ID = T3.LOGISTICS_ID
		left join T_SALEORDER T4 on T1.ORDER_ID = T4.SALEORDER_ID
		WHERE
		T1.EXPRESS_ONLINE_RECCEIPT_REORD_ID = #{onlineReceiptId,jdbcType=INTEGER}
		GROUP BY
		T2.EXPRESS_ID
	</select>



	<select id="getExpressOnlineReceiptById" resultType="com.vedeng.order.model.vo.ExpressOnlineReceiptVo">
		SELECT
			T1.SIGN_TIME,
			T1.COMMENTS
		FROM
			T_EXPRESS_ONLINE_RECCEIPT_REORD T1
		WHERE
			T1.EXPRESS_ONLINE_RECCEIPT_REORD_ID = #{Id,jdbcType=INTEGER}
	</select>


	<select id="getPhoneByBusinessTypeSaleOrder" resultType="java.lang.String" parameterType="java.lang.Integer">
		SELECT DISTINCT
			s.TAKE_TRADER_CONTACT_MOBILE
		FROM
			T_EXPRESS e
			LEFT JOIN T_EXPRESS_DETAIL ed ON e.EXPRESS_ID = ed.EXPRESS_ID
			LEFT JOIN T_SALEORDER_GOODS sg ON ed.RELATED_ID = sg.SALEORDER_GOODS_ID
			LEFT JOIN T_SALEORDER s ON sg.SALEORDER_ID = s.SALEORDER_ID
		WHERE
			e.EXPRESS_ID = #{expressId,jdbcType = INTEGER}
	</select>

	<select id="getPhoneByBusinessTypeInvoice" resultType="java.lang.String" parameterType="java.lang.Integer">
		SELECT DISTINCT
			s.INVOICE_TRADER_CONTACT_MOBILE
		FROM
			T_EXPRESS e
			LEFT JOIN T_EXPRESS_DETAIL ed ON e.EXPRESS_ID = ed.EXPRESS_ID
			LEFT JOIN T_INVOICE_DETAIL id ON ed.RELATED_ID = id.INVOICE_ID
			LEFT JOIN T_SALEORDER_GOODS sg ON id.DETAILGOODS_ID = sg.SALEORDER_GOODS_ID
			LEFT JOIN T_SALEORDER s ON sg.SALEORDER_ID = s.SALEORDER_ID
		WHERE
			e.EXPRESS_ID = #{expressId,jdbcType = INTEGER}
	</select>

	<select id="getExpressListByOrderIds" resultType="com.vedeng.logistics.model.Express">
		SELECT
		    A.SALEORDER_ID,
			IF
				( EX.EXPRESS_ID IS NOT NULL, EX.EXPRESS_ID, BE.EXPRESS_ID ) EXPRESS_ID,
			IF
				( EX.EXPRESS_ID IS NOT NULL, EX.LOGISTICS_NO, BE.LOGISTICS_NO ) LOGISTICS_NO,
			IF
				( EX.ONLINE_RECEIPT_ID IS NOT NULL, EX.ONLINE_RECEIPT_ID, BE.ONLINE_RECEIPT_ID ) ONLINE_RECEIPT_ID
		FROM
			T_SALEORDER A
				LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID = B.SALEORDER_ID
				LEFT JOIN T_EXPRESS_DETAIL ED ON ED.RELATED_ID = B.SALEORDER_GOODS_ID
				AND ED.BUSINESS_TYPE = 496
				LEFT JOIN T_EXPRESS EX ON ED.EXPRESS_ID = EX.EXPRESS_ID
				AND EX.LOGISTICS_COMMENTS != '虚拟快递单'
				AND EX.IS_ENABLE = 1
				LEFT JOIN T_R_BUYORDER_J_SALEORDER BS ON BS.SALEORDER_GOODS_ID = B.SALEORDER_GOODS_ID
			AND B.DELIVERY_DIRECT = 1
			LEFT JOIN T_EXPRESS_DETAIL BED ON BED.RELATED_ID = BS.BUYORDER_GOODS_ID
			AND BED.BUSINESS_TYPE = 515
			LEFT JOIN T_EXPRESS BE ON BE.EXPRESS_ID = BED.EXPRESS_ID
			AND BE.LOGISTICS_COMMENTS != '虚拟快递单'
			AND BE.IS_ENABLE = 1
		WHERE
			A.SALEORDER_ID IN
		<foreach collection="orderIds" item="orderId" index="index" open="(" close=")" separator=",">
			#{orderId,jdbcType=INTEGER}
		</foreach>
		  AND ( BE.EXPRESS_ID IS NOT NULL OR EX.EXPRESS_ID IS NOT NULL )
		GROUP BY
			A.SALEORDER_ID,
			EX.EXPRESS_ID,
			BE.EXPRESS_ID,
			B.SALEORDER_GOODS_ID
	</select>
	<select id="getPhoneByBusinessTypeBuyOrder" resultType="java.lang.String">
		SELECT DISTINCT
			s.TAKE_TRADER_CONTACT_MOBILE
		FROM
			T_EXPRESS e
				LEFT JOIN T_EXPRESS_DETAIL ed ON e.EXPRESS_ID = ed.EXPRESS_ID
				LEFT JOIN T_BUYORDER_GOODS sg ON ed.RELATED_ID = sg.BUYORDER_GOODS_ID
				LEFT JOIN T_BUYORDER s ON sg.BUYORDER_ID = s.BUYORDER_ID
		WHERE
			e.EXPRESS_ID = #{expressId,jdbcType = INTEGER}
	</select>
	<select id="getExpressByLogisticsNo" resultType="com.vedeng.logistics.model.Express">
		SELECT a.*
		FROM T_EXPRESS a
				 LEFT JOIN T_LOGISTICS b ON a.LOGISTICS_ID = b.LOGISTICS_ID
				 LEFT JOIN T_LOGISTICS_CODE c ON b. NAME = c. NAME
				 LEFT JOIN T_EXPRESS_DETAIL d ON a.EXPRESS_ID = d.EXPRESS_ID
		WHERE LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
		and    b.SYNC_KUAIDI=1
		AND a.IS_ENABLE = 1
		AND a.COMPANY_ID = 1
<!--		  AND d.BUSINESS_TYPE != 515-->
		  AND ( a.LOGISTICS_NO NOT LIKE 'XN%'
			AND a.LOGISTICS_NO NOT LIKE 'SL%'
			AND a.LOGISTICS_NO NOT LIKE 'BD%' )
		  AND b.NAME!='虚拟出入库专用承运商'

and a.LOGISTICS_COMMENTS != '虚拟快递单'
group by a.EXPRESS_ID
		LIMIT 1
	</select>
	<select id="getExpressByLogisticsNoNoLimit" resultType="com.vedeng.logistics.model.Express">
		SELECT a.*,
			   d.RELATED_ID,
			   d.BUSINESS_TYPE businessType,
			   b.NAME          logisticsCompanyName
		FROM T_EXPRESS a
				 LEFT JOIN T_LOGISTICS b ON a.LOGISTICS_ID = b.LOGISTICS_ID
				 LEFT JOIN T_LOGISTICS_CODE c ON b.NAME = c.NAME
				 LEFT JOIN T_EXPRESS_DETAIL d ON a.EXPRESS_ID = d.EXPRESS_ID
		WHERE LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
		  and b.SYNC_KUAIDI = 1
		  AND a.IS_ENABLE = 1
		  AND a.COMPANY_ID = 1
		  AND (a.LOGISTICS_NO NOT LIKE 'XN%'
			AND a.LOGISTICS_NO NOT LIKE 'SL%'
			AND a.LOGISTICS_NO NOT LIKE 'BD%')
		  AND b.NAME != '虚拟出入库专用承运商'
		  and a.LOGISTICS_COMMENTS != '虚拟快递单'
		group by a.EXPRESS_ID
		LIMIT 1
	</select>
	<!--新商品流-->
	<select id="getExpressInfoNew" parameterType="com.vedeng.logistics.model.Express" resultMap="ExpressResultMap">
		select a.*,b.EXPRESS_DETAIL_ID,b.BUSINESS_TYPE,b.RELATED_ID,b.NUM DNUM,b.AMOUNT,c.NAME as LOGISTICS_NAME,l.CONTENT
		<if test="businessType!=null and (businessType== 496 or businessType== 515 )">
			,k.SHOW_NAME as GOOD_NAME,y.UNIT_NAME,k.SKU_ID as GOOD_ID,k.SKU_NO as SKU
		</if>
		<if test="businessType!=null and businessType== 496 ">
			,d.IS_ACTION_GOODS
		</if>

		<if test="businessType!=null and (businessType== 497)">
			,d.AMOUNT AS INVOICE_AMOUNT,d.INVOICE_NO AS INVOICE_NO
		</if>
		<if test="businessType!=null and (businessType== 582)">
			,k.SHOW_NAME as GOOD_NAME,y.UNIT_NAME,k.SKU_NO as SKU
		</if>
		from T_EXPRESS a
		left join T_EXPRESS_DETAIL b on a.EXPRESS_ID=b.EXPRESS_ID
		left join T_LOGISTICS c on a.LOGISTICS_ID = c.LOGISTICS_ID and c.IS_ENABLE =1
		left join T_LOGISTICS_DETAIL  l on l.LOGISTICS_NO = a.LOGISTICS_NO and a.LOGISTICS_ID = l.LOGISTICS_ID
		<!-- 销售 -->
		<if test="businessType!=null and businessType== 496">
			left join T_SALEORDER_GOODS d on d.SALEORDER_GOODS_ID = b.RELATED_ID
			left join V_CORE_SKU k on d.GOODS_ID=k.SKU_ID
			left join V_CORE_SPU p on k.SPU_ID=p.SPU_ID
			LEFT JOIN T_BRAND x ON p.BRAND_ID = x.BRAND_ID
			LEFT JOIN T_UNIT y ON k.BASE_UNIT_ID = y.UNIT_ID
			AND d.IS_DELETE =0
		</if>
		<!-- 发票寄送 -->
		<if test="businessType!=null and businessType== 497">
			left join T_INVOICE d on d.EXPRESS_ID = a.EXPRESS_ID
		</if>
		<!-- 采购 -->
		<if test="businessType!=null and businessType== 515">
			left join T_BUYORDER_GOODS d on d.BUYORDER_GOODS_ID = b.RELATED_ID
			AND d.IS_DELETE =0
		</if>
		<!-- 售后 -->
		<if test="businessType!=null and businessType== 582">
			left join T_AFTER_SALES_GOODS d on d.AFTER_SALES_GOODS_ID = b.RELATED_ID
			AND d.GOODS_TYPE =0 LEFT JOIN V_CORE_SKU k ON d.GOODS_ID = k.SPU_ID
			left join V_CORE_SPU p on k.SPU_ID=p.SPU_ID
			LEFT JOIN T_BRAND x ON p.BRAND_ID = x.BRAND_ID
			LEFT JOIN T_UNIT y ON k.BASE_UNIT_ID = y.UNIT_ID
		</if>
		where 1=1
		and a.IS_ENABLE=1
		<if test="expressId!=null and expressId!='-1'">
			and a.EXPRESS_ID=#{expressId,jdbcType=INTEGER}
		</if>
		<if test="companyId!=null and companyId!='-1'">
			and a.COMPANY_ID=#{companyId,jdbcType=INTEGER}
		</if>
		<if test="logisticsId!=null and logisticsId!='-1'">
			and a.LOGISTICS_ID=#{logisticsId,jdbcType=INTEGER}
		</if>
		<if test="logisticsNo!=null and logisticsNo!=''">
			and a.LOGISTICS_NO like CONCAT('%',#{logisticsNo,jdbcType=VARCHAR},'%' )
		</if>
		<if test="deliveryFrom!=null and deliveryFrom!='-1'">
			and a.DELIVERY_FROM=#{deliveryFrom,jdbcType=INTEGER}
		</if>
		<if test="creator!=null and creator!='-1'">
			and a.CREATOR=#{creator,jdbcType=INTEGER}
		</if>
		<if test="updater!=null and updater!='-1'">
			and a.UPDATER=#{updater,jdbcType=INTEGER}
		</if>
		<if test="expressDetailId!=null and expressDetailId!='-1'">
			and b.EXPRESS_DETAIL_ID=#{expressDetailId,jdbcType=INTEGER}
		</if>
		<if test="businessType!=null and businessType!='-1'">
			and b.BUSINESS_TYPE=#{businessType,jdbcType=INTEGER}
		</if>
		<if test="relatedId!=null and relatedId!='-1'">
			and b.RELATED_ID = #{relatedId,jdbcType=INTEGER}
		</if>
		<if test="relatedIds !=null and relatedIds.size()>0">
			and b.RELATED_ID in
			<foreach collection="relatedIds" item="relatedId" index="index"
					 open="(" close=")" separator=",">
				#{relatedId}
			</foreach>
		</if>
		<if test="num!=null and num!=''">
			and b.NUM=#{num,jdbcType=INTEGER}
		</if>
		<if test="amount!=null and amount!=''">
			and b.AMOUNT=#{amount,jdbcType=INTEGER}
		</if>

		order by
		a.ADD_TIME desc
	</select>
	<!-- 销售单下的直发采购单的快递单信息 -->
	<select id="getBuyExpressList" parameterType="com.vedeng.logistics.model.Express" resultMap="ExpressResultMap">
		SELECT
		a.*, b.EXPRESS_DETAIL_ID,
		b.BUSINESS_TYPE BUSINESSTYPE,
		b.RELATED_ID,
		b.NUM DNUM,
		b.AMOUNT,
		c.NAME AS LOGISTICS_NAME,
		l.CONTENT,
		d.GOODS_ID as GOOD_ID,
		d.GOODS_NAME AS GOOD_NAME,
		d.BRAND_NAME AS BRAND_NAME,
		d.MODEL AS MODEL,
		d.UNIT_NAME,
		d.SKU
		FROM
		T_EXPRESS a
		LEFT JOIN T_EXPRESS_DETAIL b ON a.EXPRESS_ID = b.EXPRESS_ID AND b.BUSINESS_TYPE = 515
		LEFT JOIN T_LOGISTICS c ON a.LOGISTICS_ID = c.LOGISTICS_ID
		AND c.IS_ENABLE = 1
		LEFT JOIN T_LOGISTICS_DETAIL l ON l.LOGISTICS_NO = a.LOGISTICS_NO and a.LOGISTICS_ID = l.LOGISTICS_ID
		LEFT JOIN T_BUYORDER_GOODS d ON d.BUYORDER_GOODS_ID = b.RELATED_ID
		LEFT JOIN T_BUYORDER e ON d.BUYORDER_ID = e.BUYORDER_ID
		WHERE
		1 = 1
		AND e.DELIVERY_DIRECT = 1
		AND a.IS_ENABLE = 1
		<if test="batchNo!=null and batchNo!=''">
			and a.BATCH_NO=#{batchNo,jdbcType=VARCHAR}
		</if>
		AND b.RELATED_ID IN (
		SELECT
		BUYORDER_GOODS_ID
		FROM
		T_R_BUYORDER_J_SALEORDER
		WHERE 1=1
		<if test="relatedIds !=null and relatedIds.size()>0">
			and SALEORDER_GOODS_ID in
			<foreach collection="relatedIds" item="relatedId" index="index"
					 open="(" close=")" separator=",">
				#{relatedId}
			</foreach>
		</if>
		)
		order by
		a.ADD_TIME desc
	</select>
	<select id="getExpressListByBatchNo" resultType="com.vedeng.logistics.model.Express">
		SELECT a.*
		FROM T_EXPRESS a
				 LEFT JOIN T_EXPRESS_DETAIL d ON a.EXPRESS_ID = d.EXPRESS_ID
		WHERE a.BATCH_NO = #{batchNo,jdbcType=VARCHAR}
		  AND a.IS_ENABLE = 1
	</select>
	<select id="getBatchExpressByIds" resultType="com.vedeng.logistics.model.vo.BatchExpressVo">
		select
			k.SKU_NO as sku,
			k.SHOW_NAME  as goodsName,
			x.BRAND_NAME as brand,
			k.MODEL as model,
			SUM(d.DELIVERY_NUM) as num ,
			GROUP_CONCAT(a.LOGISTICS_NO SEPARATOR ',') logisticsOrderNo
		from
			T_EXPRESS a
				left join T_EXPRESS_DETAIL b on
				a.EXPRESS_ID = b.EXPRESS_ID
				left join T_SALEORDER_GOODS d on
				d.SALEORDER_GOODS_ID = b.RELATED_ID
				left join V_CORE_SKU k on
				d.GOODS_ID = k.SKU_ID
				left join V_CORE_SPU p on
				k.SPU_ID = p.SPU_ID
				LEFT JOIN T_BRAND x ON
				p.BRAND_ID = x.BRAND_ID
				LEFT JOIN T_UNIT y ON
						k.BASE_UNIT_ID = y.UNIT_ID
					AND d.IS_DELETE = 0
		where
			1 = 1
		  and a.IS_ENABLE = 1
		  AND a.EXPRESS_ID IN
		<foreach collection="expressIds" item="expressId" index="index" open="(" close=")" separator=",">
			#{expressId,jdbcType=INTEGER}
		</foreach>
		  and b.BUSINESS_TYPE = 496
		GROUP BY
			k.SKU_NO
	</select>
	<select id="getExpressInfoConfirmation" resultType="com.vedeng.logistics.model.Express">
		select a.*,b.EXPRESS_DETAIL_ID,b.BUSINESS_TYPE,b.RELATED_ID,b.NUM DNUM,b.AMOUNT,c.NAME as LOGISTICS_NAME,l.CONTENT
		,d.GOODS_NAME as GOOD_NAME,d.UNIT_NAME,d.GOODS_ID as GOOD_ID,d.SKU as SKU
		,d.IS_ACTION_GOODS
		from T_EXPRESS a
		left join T_EXPRESS_DETAIL b on a.EXPRESS_ID=b.EXPRESS_ID
		left join T_LOGISTICS c on a.LOGISTICS_ID = c.LOGISTICS_ID and c.IS_ENABLE =1
		left join T_LOGISTICS_DETAIL  l on l.LOGISTICS_NO = a.LOGISTICS_NO and a.LOGISTICS_ID = l.LOGISTICS_ID
		left join T_SALEORDER_GOODS d on d.SALEORDER_GOODS_ID = b.RELATED_ID
		AND d.IS_DELETE =0
		where 1=1
		and (a.LOGISTICS_COMMENTS IS NULL OR a.LOGISTICS_COMMENTS !='虚拟快递单')
		and a.IS_ENABLE=1
			and d.SALEORDER_ID=#{saleorderId,jdbcType=INTEGER}
		order by
		a.ADD_TIME desc
	</select>

  <select id="selectBatchNosByExpressIds" resultType="java.lang.String">
	  select
	  distinct a.BATCH_NO
	  from
	  T_EXPRESS a
	  left join T_EXPRESS_DETAIL b on a.EXPRESS_ID = b.EXPRESS_ID
	  where
	  a.IS_ENABLE = 1
	  and a.BATCH_NO is not null
	  and a.BATCH_NO != ''
	  and a.EXPRESS_ID IN
	  <foreach collection="expressIds" item="expressId" index="index" open="(" close=")" separator=",">
		  #{expressId,jdbcType=INTEGER}
	  </foreach>
	</select>

  <select id="verifyAllOnlineConfirmation" resultType="java.lang.Integer">
	  SELECT
		  IF (COUNT(IF(te.ONLINE_RECEIPT_ID>0, te.EXPRESS_ID, NULL)) = COUNT(te.EXPRESS_ID),
			  1,
			  0) allOnlineConfirmation
	  FROM
		  T_EXPRESS te
	  WHERE
		  te.IS_ENABLE = 1
		  AND te.BATCH_NO = #{batchNo,jdbcType=VARCHAR}
	  GROUP BY
		  te.BATCH_NO
	</select>

  <select id="selectFirstEnableReceiveList" resultMap="BaseResultMap">
	  select
			 te.EXPRESS_ID,
			 te.ARRIVAL_STATUS,
			 te.ENABLE_RECEIVE,
			 te.LOGISTICS_NO,
			 te.LOGISTICS_ID
	  from T_EXPRESS te
	  left join T_LOGISTICS tl on te.LOGISTICS_ID = tl.LOGISTICS_ID
	  left join T_LOGISTICS_CODE tlc on tl.NAME = tlc.NAME
	  where te.ENABLE_RECEIVE = 0
		and te.ARRIVAL_STATUS = 0
	    and te.IS_ENABLE = 1
	    and tlc.LOGISTICS_CODE_ID is not null
	  <if test="logisticsNo != null and logisticsNo != ''">
		  and te.LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
	  </if>
	  order by te.EXPRESS_ID
	  limit #{limitSize,jdbcType=INTEGER}
    </select>

	<update id="updateEnableReceiveById">
		update T_EXPRESS
		set ENABLE_RECEIVE = 1
		where ENABLE_RECEIVE = 0
		  and EXPRESS_ID = #{expressId,jdbcType=INTEGER}
	</update>

  <select id="selectExpressListByBuyOrderId" resultMap="BaseResultMap">
	  select te.EXPRESS_ID, te.LOGISTICS_NO,te.ENABLE_RECEIVE,te.ARRIVAL_STATUS
	  from T_BUYORDER tb
			   left join T_BUYORDER_GOODS bg on tb.BUYORDER_ID = bg.BUYORDER_ID
			   left join T_EXPRESS_DETAIL ed on ed.RELATED_ID = bg.BUYORDER_GOODS_ID and ed.BUSINESS_TYPE = 515
			   left join T_EXPRESS te on ed.EXPRESS_ID = te.EXPRESS_ID
	  where tb.BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
	  and te.EXPRESS_ID is not null
	  and te.IS_ENABLE = 1
	  group by te.EXPRESS_ID;
	</select>

  <select id="selectContinueEnableReceiveList" resultMap="BaseResultMap">
	  select
	  te.EXPRESS_ID,
	  te.ARRIVAL_STATUS,
	  te.ENABLE_RECEIVE,
	  te.LOGISTICS_NO,
	  te.LOGISTICS_ID
	  from T_EXPRESS te
	  left join T_LOGISTICS tl on te.LOGISTICS_ID = tl.LOGISTICS_ID
	  left join T_LOGISTICS_CODE tlc on tl.NAME = tlc.NAME
	  where te.ENABLE_RECEIVE = 0
	  and te.ARRIVAL_STATUS = 0
	  and te.IS_ENABLE = 1
	  and tlc.LOGISTICS_CODE_ID is not null
	  and te.EXPRESS_ID > #{lastExpressID,jdbcType=INTEGER}
	  order by te.EXPRESS_ID
	  limit #{limitSize,jdbcType=INTEGER}
	</select>

<!--auto generated by MybatisCodeHelper on 2023-05-10-->
  <select id="findByWmsOrderNo" resultMap="BaseResultMap">
		select
	  	*
		from T_EXPRESS TE
	  left join T_EXPRESS_DETAIL TED on TE.EXPRESS_ID = TED.EXPRESS_ID
	  where TE.WMS_ORDER_NO=#{wmsOrderNo,jdbcType=VARCHAR}
	  and TE.IS_ENABLE = 1
	</select>

  <select id="getExpressSkuDataByExpressId" resultType="com.vedeng.order.model.vo.ExpressSkuDataVo">
	  select TED.EXPRESS_ID,
	  if(TED.BUSINESS_TYPE = 515,ifnull(TBG.SKU,'') ,ifnull(TSG.SKU,''))as SKU,
	  if(TED.BUSINESS_TYPE = 515,ifnull(TBG.GOODS_NAME,'') ,ifnull(TSG.GOODS_NAME,''))as skuName,
	  if(TED.BUSINESS_TYPE = 515,ifnull(TBG.MODEL,'') ,ifnull(TSG.MODEL,''))as model,
	  if(TED.BUSINESS_TYPE = 515,ifnull(TBG.BRAND_NAME,'') ,ifnull(TSG.BRAND_NAME,''))as brand,
	  if(TED.BUSINESS_TYPE = 515,ifnull(TBG.UNIT_NAME,'') ,ifnull(TSG.UNIT_NAME,''))as unit,
			 TED.NUM
	  from T_EXPRESS_DETAIL TED
			   left join T_SALEORDER_GOODS TSG on
		  TED.RELATED_ID = TSG.SALEORDER_GOODS_ID
	  left join T_BUYORDER_GOODS TBG on TBG.BUYORDER_GOODS_ID = TED.RELATED_ID
	  where
		  TED.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </select>


    <!-- 分页查询快递单列表 -->
	<select id="getExpresslistPage" resultType="com.vedeng.logistics.model.Express">
		SELECT T.EXPRESS_ID,
			   T.LOGISTICS_NO,
			   T.LOGISTICS_ID,
			   T.DELIVERY_TIME,
			   T.ARRIVAL_STATUS,
			   L.NAME AS EXPRESSNAME,
			   TED.RELATED_ID,
			   TED.BUSINESS_TYPE
		FROM T_EXPRESS T
				 LEFT JOIN
			 T_LOGISTICS L
			 ON T.LOGISTICS_ID = L.LOGISTICS_ID
				 LEFT JOIN
			 T_EXPRESS_DETAIL TED
			 ON TED.EXPRESS_ID = T.EXPRESS_ID
		<where>
			and T.IS_ENABLE = 1
				  and T.COMPANY_ID = 1
			<if test="express.logisticsNo != null and express.logisticsNo != ''">
				and T.LOGISTICS_NO = #{express.logisticsNo}
			</if>
			and T.ARRIVAL_STATUS = 2
			and TED.BUSINESS_TYPE in (496, 515, 582)
			<if test="express.deliveryTime != null">
				and T.DELIVERY_TIME >= #{express.deliveryTime}
			</if>
			<if test="beginTime != null and endTime != null">
				and T.DELIVERY_TIME between #{beginTime} and #{endTime}
			</if>

			AND NOT EXISTS(
					SELECT 1
					FROM T_LOGISTICS_INFO_FILE TLIF
					WHERE TLIF.EXPRESS_ID = T.EXPRESS_ID
				)
		</where>
	</select>

	<!-- 查询需要生成物流信息文件的快递单ID列表（避免分页数据丢失） -->
	<select id="getExpressIdsForGeneration" resultType="java.lang.Integer">
		SELECT T.EXPRESS_ID
		FROM T_EXPRESS T
		WHERE T.IS_ENABLE = 1
		  AND T.COMPANY_ID = 1
		  AND T.ARRIVAL_STATUS = 2
		  <if test="express.deliveryTime != null">
		  AND T.DELIVERY_TIME >= #{express.deliveryTime}
		  </if>
		  <if test="beginTime != null and endTime != null">
		  AND T.DELIVERY_TIME BETWEEN #{beginTime} AND #{endTime}
		  </if>
		  AND EXISTS (
		      SELECT 1 
		      FROM T_EXPRESS_DETAIL TED 
		      WHERE TED.EXPRESS_ID = T.EXPRESS_ID 
		        AND TED.BUSINESS_TYPE IN (496, 515, 582)
		  )
		  AND NOT EXISTS (
		      SELECT 1
		      FROM T_LOGISTICS_INFO_FILE TLIF
		      WHERE TLIF.EXPRESS_ID = T.EXPRESS_ID
		  )
		ORDER BY T.EXPRESS_ID
	</select>

	<!-- 根据快递单号数组查询需要生成物流信息文件的快递单ID列表 -->
	<select id="getExpressIdsByLogisticsNos" resultType="java.lang.Integer">
		SELECT T.EXPRESS_ID
		FROM T_EXPRESS T
		WHERE T.IS_ENABLE = 1
		  AND T.COMPANY_ID = 1
		  AND T.ARRIVAL_STATUS = 2
		  AND T.LOGISTICS_NO IN
		<foreach collection="logisticsNos" item="logisticsNo" open="(" separator="," close=")">
			#{logisticsNo}
		</foreach>
		  AND EXISTS (
		      SELECT 1 
		      FROM T_EXPRESS_DETAIL TED 
		      WHERE TED.EXPRESS_ID = T.EXPRESS_ID 
		        AND TED.BUSINESS_TYPE IN (496, 515, 582)
		  )
		  AND NOT EXISTS (
		      SELECT 1
		      FROM T_LOGISTICS_INFO_FILE TLIF
		      WHERE TLIF.EXPRESS_ID = T.EXPRESS_ID
		  )
		ORDER BY T.EXPRESS_ID
	</select>

	<!-- 根据ID列表批量查询快递单详情 -->
	<select id="getExpressByIds" resultType="com.vedeng.logistics.model.Express">
		SELECT T.EXPRESS_ID,
			   T.LOGISTICS_NO,
			   T.LOGISTICS_ID,
			   T.DELIVERY_TIME,
			   T.ARRIVAL_STATUS,
			   L.NAME AS EXPRESSNAME,
			   TED.RELATED_ID,
			   TED.BUSINESS_TYPE
		FROM T_EXPRESS T
		LEFT JOIN T_LOGISTICS L ON T.LOGISTICS_ID = L.LOGISTICS_ID
		LEFT JOIN T_EXPRESS_DETAIL TED ON TED.EXPRESS_ID = T.EXPRESS_ID
		WHERE T.EXPRESS_ID IN
		<foreach collection="expressIds" item="expressId" open="(" separator="," close=")">
			#{expressId}
		</foreach>
		AND T.IS_ENABLE = 1
		AND T.COMPANY_ID = 1
		AND T.ARRIVAL_STATUS = 2
		AND TED.BUSINESS_TYPE in (496, 515, 582)
		ORDER BY T.EXPRESS_ID
	</select>


	<select id="getSyncExpressListForSaleOrder" resultMap="SyncExpressDtoResultMap">
		select
			a.EXPRESS_ID,
			a.LOGISTICS_ID,
			a.LOGISTICS_NO,
			a.COMPANY_ID,
			a.DELIVERY_TIME,
			a.BATCH_NO,
			a.CREATOR,
			a.UPDATER,
			b.EXPRESS_DETAIL_ID,
			b.EXPRESS_ID,
			b.BUSINESS_TYPE,
			b.RELATED_ID,
			b.NUM,
			CONCAT(k.SKU,'-',k.IS_GIFT) AS NON_ALL_ARRIVAL_REASON
		from
			T_EXPRESS a
				left join T_EXPRESS_DETAIL b on a.EXPRESS_ID = b.EXPRESS_ID
				left join T_LOGISTICS c on a.LOGISTICS_ID = c.LOGISTICS_ID
				and c.IS_ENABLE = 1
				left join T_LOGISTICS_DETAIL l on l.LOGISTICS_NO = a.LOGISTICS_NO
				and a.LOGISTICS_ID = l.LOGISTICS_ID
				left join T_SALEORDER_GOODS k on k.SALEORDER_GOODS_ID = b.RELATED_ID
				AND k.IS_DELETE = 0
				LEFT JOIN T_SALEORDER ts on ts.SALEORDER_ID = k.SALEORDER_ID
		where
			1 = 1
		  and (
			a.LOGISTICS_COMMENTS IS NULL
				OR a.LOGISTICS_COMMENTS != '虚拟快递单'
			)
		  and a.IS_ENABLE = 1
		  and a.COMPANY_ID = 1
		  AND ts.SALEORDER_NO = #{saleOrderNo,jdbcType=VARCHAR}
		  and b.BUSINESS_TYPE = 496
		  and k.DELIVERY_DIRECT = 0
		UNION ALL
		select
			a.EXPRESS_ID,
			a.LOGISTICS_ID,
			a.LOGISTICS_NO,
			a.COMPANY_ID,
			a.DELIVERY_TIME,
			a.BATCH_NO,
			a.CREATOR,
			a.UPDATER,
			b.EXPRESS_DETAIL_ID,
			b.EXPRESS_ID,
			b.BUSINESS_TYPE,
			b.RELATED_ID,
			b.NUM,
			CONCAT(k.SKU,'-',k.IS_GIFT) AS NON_ALL_ARRIVAL_REASON
		from
			T_EXPRESS a
				left join T_EXPRESS_DETAIL b on a.EXPRESS_ID = b.EXPRESS_ID
				left join T_LOGISTICS c on a.LOGISTICS_ID = c.LOGISTICS_ID
				and c.IS_ENABLE = 1
				left join T_LOGISTICS_DETAIL l on l.LOGISTICS_NO = a.LOGISTICS_NO
				and a.LOGISTICS_ID = l.LOGISTICS_ID
				left join T_BUYORDER_GOODS d on d.BUYORDER_GOODS_ID  = b.RELATED_ID AND d.IS_DELETE = 0
				left join T_R_BUYORDER_J_SALEORDER j on d.BUYORDER_GOODS_ID = j.BUYORDER_GOODS_ID
				left join T_BUYORDER tb ON d.BUYORDER_ID  = tb.BUYORDER_ID
				left join T_SALEORDER_GOODS k on j.SALEORDER_GOODS_ID  = k.SALEORDER_GOODS_ID
				left join T_SALEORDER ts  on k.SALEORDER_ID = ts.SALEORDER_ID

		where
			1 = 1
		  and (
			a.LOGISTICS_COMMENTS IS NULL
				OR a.LOGISTICS_COMMENTS != '虚拟快递单'
			)
		  and a.IS_ENABLE = 1
		  and a.COMPANY_ID = 1
		  AND  j.NUM >0
		  AND tb.STATUS!=3
		  AND d.IS_DELETE=0
		  and ts.SALEORDER_NO = #{saleOrderNo,jdbcType=VARCHAR}
		  and b.BUSINESS_TYPE = 515
		  and k.DELIVERY_DIRECT =1
	</select>

	<select id="getSyncExpressListForBuyOrder" resultMap="SyncExpressDtoResultMap">
		select
			a.EXPRESS_ID,
			a.LOGISTICS_ID,
			a.LOGISTICS_NO,
			a.COMPANY_ID,
			a.DELIVERY_TIME,
			a.BATCH_NO,
			a.CREATOR,
			a.UPDATER,
			b.EXPRESS_DETAIL_ID,
			b.EXPRESS_ID,
			b.BUSINESS_TYPE,
			b.RELATED_ID,
			b.NUM,
			CONCAT(d.SKU,'-',d.IS_GIFT) AS NON_ALL_ARRIVAL_REASON
		from
			T_EXPRESS a
				left join T_EXPRESS_DETAIL b on a.EXPRESS_ID = b.EXPRESS_ID
				left join T_LOGISTICS c on a.LOGISTICS_ID = c.LOGISTICS_ID
				and c.IS_ENABLE = 1
				left join T_LOGISTICS_DETAIL l on l.LOGISTICS_NO = a.LOGISTICS_NO
				and a.LOGISTICS_ID = l.LOGISTICS_ID
				left join T_BUYORDER_GOODS d on d.BUYORDER_GOODS_ID  = b.RELATED_ID AND d.IS_DELETE = 0
				left join T_BUYORDER tb ON d.BUYORDER_ID  = tb.BUYORDER_ID
		where
			1 = 1
		  and (
			a.LOGISTICS_COMMENTS IS NULL
				OR a.LOGISTICS_COMMENTS != '虚拟快递单'
			)
		  and a.IS_ENABLE = 1
		  and a.COMPANY_ID = 1
		  AND d.IS_DELETE=0
		  and tb.BUYORDER_NO = #{buyOrderNo,jdbcType=VARCHAR}
		  and b.BUSINESS_TYPE = 515
	</select>


	<select id="getSyncExpressListForSaleOrderForAutoCreateConfirmOrder" resultMap="SyncExpressDtoResultMap">
		select
			a.EXPRESS_ID,
			a.LOGISTICS_ID,
			a.LOGISTICS_NO,
			a.COMPANY_ID,
			a.DELIVERY_TIME,
			a.BATCH_NO,
			a.CREATOR,
			a.UPDATER,
			b.EXPRESS_DETAIL_ID,
			b.EXPRESS_ID,
			b.BUSINESS_TYPE,
			b.RELATED_ID,
			b.NUM,
			CONCAT(k.SKU,'-',k.IS_GIFT) AS NON_ALL_ARRIVAL_REASON
		from
			T_EXPRESS a
				left join T_EXPRESS_DETAIL b on a.EXPRESS_ID = b.EXPRESS_ID
				left join T_LOGISTICS c on a.LOGISTICS_ID = c.LOGISTICS_ID
				and c.IS_ENABLE = 1
				left join T_LOGISTICS_DETAIL l on l.LOGISTICS_NO = a.LOGISTICS_NO
				and a.LOGISTICS_ID = l.LOGISTICS_ID
				left join T_SALEORDER_GOODS k on k.SALEORDER_GOODS_ID = b.RELATED_ID
				AND k.IS_DELETE = 0
				LEFT JOIN T_SALEORDER ts on ts.SALEORDER_ID = k.SALEORDER_ID
		where
			1 = 1
		  and (
			a.LOGISTICS_COMMENTS IS NULL
				OR a.LOGISTICS_COMMENTS != '虚拟快递单'
			)
		  and a.IS_ENABLE = 1
		  and a.COMPANY_ID = 1
		  AND ts.SALEORDER_NO = #{saleOrderNo,jdbcType=VARCHAR}
		  and b.BUSINESS_TYPE = 496
		  and k.DELIVERY_DIRECT = 0
		UNION ALL
		select
			a.EXPRESS_ID,
			a.LOGISTICS_ID,
			a.LOGISTICS_NO,
			a.COMPANY_ID,
			a.DELIVERY_TIME,
			a.BATCH_NO,
			a.CREATOR,
			a.UPDATER,
			b.EXPRESS_DETAIL_ID,
			b.EXPRESS_ID,
			b.BUSINESS_TYPE,
			b.RELATED_ID,
			b.NUM,
			CONCAT(k.SKU,'-',k.IS_GIFT) AS NON_ALL_ARRIVAL_REASON
		from
			T_EXPRESS a
				left join T_EXPRESS_DETAIL b on a.EXPRESS_ID = b.EXPRESS_ID
				left join T_LOGISTICS c on a.LOGISTICS_ID = c.LOGISTICS_ID
				and c.IS_ENABLE = 1
				left join T_LOGISTICS_DETAIL l on l.LOGISTICS_NO = a.LOGISTICS_NO
				and a.LOGISTICS_ID = l.LOGISTICS_ID
				left join T_BUYORDER_GOODS d on d.BUYORDER_GOODS_ID  = b.RELATED_ID AND d.IS_DELETE = 0
				left join T_R_BUYORDER_J_SALEORDER j on d.BUYORDER_GOODS_ID = j.BUYORDER_GOODS_ID
				left join T_BUYORDER tb ON d.BUYORDER_ID  = tb.BUYORDER_ID
				left join T_SALEORDER_GOODS k on j.SALEORDER_GOODS_ID  = k.SALEORDER_GOODS_ID
				left join T_SALEORDER ts  on k.SALEORDER_ID = ts.SALEORDER_ID

		where
			1 = 1
		  and (
			a.LOGISTICS_COMMENTS IS NULL
				OR a.LOGISTICS_COMMENTS != '虚拟快递单'
			)
		  and a.IS_ENABLE = 1
		  and a.COMPANY_ID = 1
		  AND  j.NUM >0
		  AND tb.STATUS!=3
		  AND d.IS_DELETE=0
		  and ts.SALEORDER_NO = #{saleOrderNo,jdbcType=VARCHAR}
		  and b.BUSINESS_TYPE = 515
		  and k.DELIVERY_DIRECT =1
	</select>



	<select id="getSendedNum" resultType="java.lang.Integer">
		select SUM(NUM) from T_EXPRESS_DETAIL where RELATED_ID = #{buyOrderGoodsId,jdbcType=INTEGER} and BUSINESS_TYPE = 515;
    </select>

	<select id="getDetail" resultType="com.vedeng.logistics.model.SyncExpressDetailDto">
		select b.EXPRESS_DETAIL_ID,b.NUM
		from T_BUYORDER_GOODS a
				 left join T_EXPRESS_DETAIL b on a.BUYORDER_GOODS_ID = b.RELATED_ID
				 left join T_EXPRESS c on c.EXPRESS_ID = b.EXPRESS_ID
		where a.IS_DELETE = 0
		  and b.BUSINESS_TYPE = 515
		  and a.BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
		  and a.SKU = #{sku,jdbcType=VARCHAR}
		  and c.LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
		  limit 1
    </select>


	<select id="getExpressByNo" resultType="com.vedeng.logistics.model.SyncExpressDetailDto">
		select e.EXPRESS_ID,c.BUYORDER_ID as relatedId,e.ARRIVAL_STATUS
		from T_EXPRESS e
				 left join T_EXPRESS_DETAIL a on e.EXPRESS_ID = a.EXPRESS_ID
				 left join T_BUYORDER_GOODS b on a.RELATED_ID = b.BUYORDER_GOODS_ID
				 left join T_BUYORDER c on b.BUYORDER_ID = c.BUYORDER_ID
		where e.LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
		  and c.BUYORDER_NO = #{buyOrderNo,jdbcType=VARCHAR} limit 1
	</select>

	<!-- 根据快递单号和采购单号查询快递信息，用于快递签收操作 -->
	<select id="getExpressByLogisticsNoAndBuyOrderNo" resultType="com.vedeng.logistics.model.Express">
		select e.*
		from T_EXPRESS e
				 left join T_EXPRESS_DETAIL a on e.EXPRESS_ID = a.EXPRESS_ID
				 left join T_BUYORDER_GOODS b on a.RELATED_ID = b.BUYORDER_GOODS_ID
				 left join T_BUYORDER c on b.BUYORDER_ID = c.BUYORDER_ID
		where e.LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
		  and c.BUYORDER_NO = #{buyOrderNo,jdbcType=VARCHAR}
		limit 1
	</select>
</mapper>
