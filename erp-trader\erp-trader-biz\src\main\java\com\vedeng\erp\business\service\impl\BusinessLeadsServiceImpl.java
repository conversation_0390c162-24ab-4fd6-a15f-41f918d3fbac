package com.vedeng.erp.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.base.ResultInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.ErpDateUtils;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.BizLogEnum;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.crm.feign.category.CategoryThreeApiService;
import com.vedeng.erp.business.common.constant.BusinessChanceConstant;
import com.vedeng.erp.business.common.enums.BusinessLeadsFollowStatusEnums;
import com.vedeng.erp.business.common.enums.TerminalTraderNatureEnum;
import com.vedeng.erp.business.domain.dto.AssignLeadsDto;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadMergeDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.domain.entity.BusinessLeadsEntity;
import com.vedeng.erp.business.mapper.BusinessLeadsMapper;
import com.vedeng.erp.business.mapstruct.BusinessLeadsConvertor;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.leads.dto.MergeLeadsDto;
import com.vedeng.erp.system.common.enums.CustomDataLogSaveTypeEnums;
import com.vedeng.erp.system.common.enums.CustomDataOperBizTypeEnums;
import com.vedeng.erp.system.dto.*;
import com.vedeng.erp.system.service.*;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.dto.PushBusinessLeadsDto;
import com.vedeng.erp.trader.mapper.RSalesJBusinessOrderMapper;
import com.vedeng.erp.trader.service.BusinessLeadsApiService;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.goods.dto.BusinessOrderCategoryDto;
import com.vedeng.goods.service.BusinessOrderCategoryApiService;
import com.vedeng.infrastructure.feign.message.MessageApiService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.infrastructure.message.dto.MessageDto;
import com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc;
import com.vedeng.infrastructure.tyc.mapper.TycMapper;
import com.vedeng.uac.api.dto.UserInfoDto;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BusinessLeadsServiceImpl implements BusinessLeadsService, BusinessLeadsApiService {


    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    @Autowired
    private CustomDataLogApiService customDataLogApiService;



    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Autowired
    private BusinessLeadsMapper businessLeadsMapper;


    @Autowired
    private BusinessLeadsConvertor businessLeadsConvertor;


    @Autowired
    private UserApiService userApiService;

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    @Autowired
    private CustomTagApiService customTagApiService;

    @Autowired
    private TycMapper tycMapper;

    @Autowired
    BusinessOrderCategoryApiService businessOrderCategoryApiService;

    @Autowired
    CategoryThreeApiService categoryThreeApiService;

    @Value("${zongjiUserIdList:1411}")
    private String zongjiUserIdList;

    @Value("${crmJumpErpUrl}")
    private String crmJumpErpUrl;

    @Autowired
    private RSalesJBusinessOrderMapper rSalesJBusinessOrderMapper;
    
    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;
    
    @Value("${crmApplicationMessageJumpUrl}")
    private String crmApplicationMessageJumpUrl;  // qa.lxcrm.vedeng.com
    
    @Value("${erpUrl}")
    private String erpUrl;

    @Value("${lxcrmUrl:}")
    private String lxcrmUrl;

    private static final String STATIC_JUMP_URL = "/crm/wx/transfer?targetUrl=";

    @Override
    public List<UserDto> findAllCreateUser(String name) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();

        UserDto userDto = UserDto.builder().isAdmin(currentUser.getIsAdmin()==null?0:currentUser.getIsAdmin())
                .companyId(currentUser.getCompanyId())
                .username(currentUser.getUsername())
                .positType(currentUser.getPositType())
                .userId(currentUser.getId())
                .build();

        List<Integer> zongjiList = Arrays.stream(zongjiUserIdList.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());

        if (zongjiList.contains(userDto.getUserId())) {
            return businessLeadsMapper.findAllCreateUser(name);
        }else{
            List<UserDto> userDtoList = userApiService.queryUserSubFromUac(userDto.getUserId());
            List<Integer> allSubordinateUserIdList = userDtoList.stream().map(UserDto::getUserId).collect(Collectors.toList());
//            PageParam<BusinessLeadsDto> businessLeadsDto = new PageParam<>();
//            businessLeadsDto.getParam().setUserIdList(allSubordinateUserIdList);
//            businessLeadsDto.getParam().setAllSubordinateUserIdList(allSubordinateUserIdList);
//            businessLeadsDto.getParam().setCurrentUserId(currentUser.getId());
//            businessLeadsDto.getParam().setQueryAll(0);
            return businessLeadsMapper.findAllCreatorUserForLeads(name,allSubordinateUserIdList,currentUser.getId());

        }
    }

    @Override
    public List<UserDto> findAllBelongUser(String name) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();

        UserDto userDto = UserDto.builder().isAdmin(currentUser.getIsAdmin()==null?0:currentUser.getIsAdmin())
                .companyId(currentUser.getCompanyId())
                .username(currentUser.getUsername())
                .positType(currentUser.getPositType())
                .userId(currentUser.getId())
                .build();

        List<Integer> zongjiList = Arrays.stream(zongjiUserIdList.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());


        if (zongjiList.contains(userDto.getUserId())) {
            return businessLeadsMapper.findAllBelongUser(name);
        } else {
            List<UserDto> userDtoList = userApiService.queryUserSubFromUac(currentUser.getId());
            List<Integer> allSubordinateUserIdList = userDtoList.stream().map(UserDto::getUserId).collect(Collectors.toList());//全部的下属ID
            if(!allSubordinateUserIdList.contains(currentUser.getId())){
                allSubordinateUserIdList.add(currentUser.getId());
            }
            return businessLeadsMapper.findAllBelongUserForLeads(name,allSubordinateUserIdList, currentUser.getId());
        }

    }

    @Override
    public List<UserDto> findAllShareUser(String name) {
        return businessLeadsMapper.findAllShareUser(name);
    }


    @Override
    public boolean checkUserIsZongji(Integer userId) {
        List<Integer> zongjiList = Arrays.stream(zongjiUserIdList.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        return zongjiList.contains(userId);
    }




    @Override
    public PageInfo<BusinessLeadsDto> page(PageParam<BusinessLeadsDto> businessLeadsDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();

        UserDto userDto = UserDto.builder().isAdmin(currentUser.getIsAdmin()==null?0:currentUser.getIsAdmin())
                .companyId(currentUser.getCompanyId())
                .username(currentUser.getUsername())
                .positType(currentUser.getPositType())
                .userId(currentUser.getId())
                .build();
        // 当前登录人及其所有下级
        List<Integer> zongjiList = Arrays.stream(zongjiUserIdList.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());

        BusinessLeadsDto dtoParam = businessLeadsDto.getParam();
        if(dtoParam == null){
            businessLeadsDto.setParam(new BusinessLeadsDto());
        }
        if (zongjiList.contains(userDto.getUserId())) {
            businessLeadsDto.getParam().setQueryAll(1);
            businessLeadsDto.getParam().setCurrentUserId(currentUser.getId());
        } else {
            List<UserDto> userDtoList = userApiService.queryUserSubFromUac(userDto.getUserId());
            List<Integer> allSubordinateUserIdList = userDtoList.stream().map(UserDto::getUserId).collect(Collectors.toList());
            businessLeadsDto.getParam().setUserIdList(allSubordinateUserIdList);
            businessLeadsDto.getParam().setAllSubordinateUserIdList(allSubordinateUserIdList);
            businessLeadsDto.getParam().setCurrentUserId(currentUser.getId());
            businessLeadsDto.getParam().setQueryAll(0);
            //CRM-834 需求开发
            if(Objects.nonNull(dtoParam) && !CollectionUtils.isEmpty(dtoParam.getOrganizationIdList())) {
            	List<Integer> belongOrgUserIdList = getUserIdByOrgParamList(dtoParam.getOrganizationIdList());
            	if(!CollectionUtils.isEmpty(belongOrgUserIdList)) {
            		businessLeadsDto.getParam().setBelongOrgUserIdList(belongOrgUserIdList);
            	}
            }
        }

        
        PageInfo<BusinessLeadsDto> businessLeadsDtoPageInfo = PageHelper.startPage(businessLeadsDto)
                .doSelectPageInfo(() -> businessLeadsMapper.findByAllToPage(businessLeadsDto.getParam()));
        businessLeadsDtoPageInfo.getList().forEach(dto -> {

            List<BusinessOrderCategoryDto> businessOrderCategoryDtoList = businessOrderCategoryApiService.getByBusinessIdAndType(dto.getId(),0);
            if(CollUtil.isNotEmpty(businessOrderCategoryDtoList)){
                List<String> categoryList = new ArrayList<>();
                businessOrderCategoryDtoList.forEach(ids -> {
                    R<String> fullPathNameById = categoryThreeApiService.getFullPathNameById(ids.getCategoryId());
                    if (fullPathNameById != null && fullPathNameById.getCode() == 0) {
                        categoryList.add(fullPathNameById.getData());
                    }
                });
                dto.setCategoryList(categoryList);
            }

            if(dto.getTraderId() != null && dto.getTraderId()>0){
                try{
                    String targetTabUrl = ("/trader/customer/new/portrait.do?traderId="+dto.getTraderId()+"&traderCustomerId="+dto.getTraderCustomerId()+"&customerNature="+dto.getCustomerNature());
                    dto.setTraderNameInnerLink(targetTabUrl);
                    if(crmJumpErpUrl.indexOf("sso")>-1){
                        String encodetargetTabUrl = URLEncoder.encode("/index.do?target="+URLEncoder.encode(targetTabUrl,"UTF-8")+"&title=客户360","UTF-8");
                        dto.setTraderNameLink(crmJumpErpUrl+encodetargetTabUrl);
                    }else{
                        String encodetargetTabUrl = "/index.do?target="+ URLEncoder.encode(targetTabUrl,"UTF-8")+"&title="+URLEncoder.encode("客户360","UTF-8");
                        dto.setTraderNameLink(crmJumpErpUrl+encodetargetTabUrl);
                    }
                }catch(Exception e ){
                    log.error("客户链接转换给前端时失败，需要检查",e);
                }
            }
        });
        return businessLeadsDtoPageInfo;
    }

    private List<Integer> getUserIdByOrgParamList(List<Integer> organizationIdList) {
    	List<Integer> belongOrgUserIdList = new ArrayList<>();
    	RestfulResult<List<UserInfoDto>> result = uacWxUserInfoApiService.getUserListByDepartmentIds(organizationIdList);
        if (result.isSuccess()) {
        	List<UserInfoDto> userInfoDtoList = result.getData();
        	belongOrgUserIdList = userInfoDtoList.stream().map(UserInfoDto::getId).distinct().collect(Collectors.toList()); 
        }
		return belongOrgUserIdList;
	}

	@Override
    public Integer add(BusinessLeadsDto businessLeadsDto){
        BusinessLeadsEntity businessLeadsEntity = businessLeadsConvertor.toEntity(businessLeadsDto);

        if (Objects.nonNull(businessLeadsEntity.getBelongerId()) && businessLeadsEntity.getBelongerId()>0) {
            checkUser(businessLeadsDto.getBelongerId());
            Integer newUserId = checkBusiness2OtherSaleUser(businessLeadsDto.getBelongerId());//查询该员工是否请假了
            if(newUserId > 0 && !newUserId.equals(businessLeadsDto.getBelongerId())) {
                checkUser(newUserId);
                businessLeadsEntity.setBelongerId(newUserId); //将新的销售人员id赋值给assignLeadsDto
                UserDto otherUser = userWorkApiService.getUserByUserId(newUserId);
                businessLeadsEntity.setBelonger(otherUser.getUsername());
            }
            businessLeadsEntity.setAssignTime(new Date());
            businessLeadsEntity.setFollowStatus(BusinessLeadsFollowStatusEnums.NO_PROCESS.getType());
        }else{
            businessLeadsEntity.setFollowStatus(BusinessLeadsFollowStatusEnums.NO_FENGPEI.getType());
        }
        businessLeadsEntity.setStatus(1);

        //businessLeadsEntity.setFollowStatus(BusinessLeadsFollowStatusEnums.NO_PROCESS.getType());
        businessLeadsMapper.insertSelective(businessLeadsEntity);

        if(StrUtil.isNotEmpty(businessLeadsDto.getCategoryIds())){
            String[] categoryIds = businessLeadsDto.getCategoryIds().split(",");
            for(String categoryId : categoryIds){
                BusinessOrderCategoryDto businessOrderCategoryDto = new BusinessOrderCategoryDto();
                businessOrderCategoryDto.setBusinessId(businessLeadsEntity.getId());
                businessOrderCategoryDto.setBusinessType(0);
                businessOrderCategoryDto.setCategoryId(Integer.parseInt(categoryId));
                businessOrderCategoryDto.setKeywords(businessLeadsDto.getKeywords());
                businessOrderCategoryApiService.save(businessOrderCategoryDto);
            }
        }

        Integer id = businessLeadsEntity.getId();
        if (StrUtil.isBlank(businessLeadsEntity.getLeadsNo())) {
            BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.BUSINESS_LEADS, NoGeneratorBean.builder().id(id).numberOfDigits(8).build());
            String code = new BillNumGenerator().distribution(billGeneratorBean);
            businessLeadsEntity.setLeadsNo(code);
            businessLeadsMapper.updateByPrimaryKeySelective(businessLeadsEntity);

            //给线索编号赋上值
            businessLeadsDto.setLeadsNo(code);
        }


        //VDERP-17057  【客户档案】ERP客户档案时间轴 创建线索
        track(businessLeadsEntity,EventTrackingEnum.PRE_SALE_NEW_BUSINESS_LEADS,new Date());


        // 记录操作日志
        OperationLogDto logDto = new OperationLogDto();
        logDto.setCreator(businessLeadsDto.getCreator());
        logDto.setCreatorName(businessLeadsDto.getCreatorName());
        logDto.setOperationTime(new Date());
        HashMap<String,String> params = new HashMap<>();
        params.put("userName",businessLeadsDto.getCreatorName());
        logDto.setParams(params);
        logDto.setBizId(businessLeadsDto.getId()==null?businessLeadsEntity.getId():businessLeadsDto.getId());
        operationLogApiService.save(logDto, BizLogEnum.BUSINESSlEADS_NEW);

        if (Objects.nonNull(businessLeadsEntity.getBelongerId())) {
            String url = "./businessLeads/details.do?id=" + id;
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("", "");
            log.info("新增线索,分配线索发送站内信start,线索id:{}", id);
            sendMessage(221, Collections.singletonList(businessLeadsEntity.getBelongerId()), paramMap, url);
            log.info("新增线索,分配线索发送站内信end,线索id:{}", id);
        }
        
        try {
            boolean merge = mergeLeads(businessLeadsEntity.getLeadsNo());
            if (!merge && Objects.nonNull(businessLeadsEntity.getBelongerId()) && "Y".equals(businessLeadsDto.getSendVx())) {
                sendVxMessage(businessLeadsEntity, businessLeadsEntity.getBelongerId());
            }
        } catch (Exception e) {
            log.info("线索合并异常", e);
        }
        
        // 计算线下销售和产线关系，发送协作人消息提醒
        sendAutoShareNotifications(businessLeadsEntity.getId(), businessLeadsEntity.getLeadsNo());
        
        return id;
    }

    /**
     * 发送自动共享关系的消息通知
     * @param leadsId 线索ID
     * @param leadsNo 线索编号
     */
    private void sendAutoShareNotifications(Integer leadsId, String leadsNo) {
        try {
            CurrentUser currentUser = CurrentUser.getCurrentUser();
            if (currentUser == null) {
                log.warn("获取当前用户信息失败，无法发送协作人消息提醒");
                return;
            }
            
            // 直接构建线索详情页链接
            String url = getJumpUrl(lxcrmUrl+ "/crm/businessLeads/profile/detail?id=" + leadsId, JumpErpTitleEnum.BUSSINESS_LEAD_DETAIL); // 2是线索详情页面的编码
            
            // 查询线下销售和产线负责人
            List<RSalesJBusinessOrderDto> shareList = rSalesJBusinessOrderMapper.calculateShareTagsWithoutBusinessJOrder(leadsId);
            if (CollectionUtils.isEmpty(shareList)) {
                log.info("线索{}没有自动计算的协作人关系", leadsNo);
                return;
            }
            
            for (RSalesJBusinessOrderDto share : shareList) {
                // 只处理自动共享关系
                if (share.getShareTag() == null || share.getShareTag() == 3) {
                    continue; // 标签3是手动添加，跳过
                }
                
                Integer userId = share.getSaleUserId();
                if (userId == null || userId.equals(currentUser.getId())) {
                    continue; // 跳过空值或当前用户自己
                }
                
                String content;
                String title;
                // 检查当前用户是否为admin，如果是则显示"商城客户"，否则显示用户名
                String displayName = ErpConstant.DEFAULT_USER_ID == currentUser.getId() ? "商城客户" : currentUser.getUsername();
                if (share.getShareTag() == 1) {
                    // 线下销售
                    title = "线下销售协作人提醒";
                    content = String.format("%s 新建线索%s，作为线下销售您可查看。",
                            displayName, leadsNo);
                    sendCardMessage(userId, title, content, url, "查看详情");
                    log.info("已向线下销售协作人(ID:{})发送线索{}新建提醒,内容{}", userId, leadsNo, content);
                } else if (share.getShareTag() == 2) {
                    // 产线负责人
                    title = "产线协作人提醒";
                    content = String.format("%s 新建线索%s中可能存在跟您产线相关的产品，请查看。",
                            displayName, leadsNo);
                    sendCardMessage(userId, title, content, url, "查看详情");
                    log.info("已向产线协作人(ID:{})发送线索{}新建提醒,内容{}", userId, leadsNo,content);
                }
            }
        } catch (Exception e) {
            log.error("发送线索{}自动共享消息提醒失败: {}", leadsNo, e.getMessage(), e);
        }
    }
    
    /**
     * 获取跳转链接
     * @param messageUrl 消息URL
     * @param JumpErpTitleEnum 跳转标题编码
     * @return 完整跳转URL
     */
    private String getJumpUrl(String messageUrl, JumpErpTitleEnum jumpNameEnum) {
        try {
            return crmApplicationMessageJumpUrl + STATIC_JUMP_URL + 
                   URLEncoder.encode(erpUrl + "/index.do?target=" + 
                   URLEncoder.encode(messageUrl,"UTF-8") + 
                   "&ct=" + jumpNameEnum.getCode(), "UTF-8");
        } catch (Exception e) {
            log.error("getjumpUrl error", e);
            return messageUrl; // 出错时返回原始URL
        }
    }
    
    /**
     * 发送卡片消息
     * @param targetUserId 目标用户ID
     * @param title 标题
     * @param content 内容
     * @param url 跳转链接
     * @param btnText 按钮文本
     */
    private void sendCardMessage(Integer targetUserId, String title, String content, String url, String btnText) {
        try {
            if (targetUserId == null) {
                log.warn("目标用户ID为空，无法发送消息");
                return;
            }
            
            // 获取用户工号
            UserDto userDto = userApiService.getUserById(targetUserId);
            if (userDto == null) {
                log.warn("无法获取用户信息，用户ID: {}", targetUserId);
                return;
            }
            
            String userNumber = userDto.getNumber();
            if (org.apache.commons.lang3.StringUtils.isEmpty(userNumber)) {
                log.warn("用户工号为空，用户ID: {}", targetUserId);
                return;
            }
            
            // 创建企业微信卡片消息
            WxCpMessage wxCpMessage = new WxCpMessage();
            wxCpMessage.setToUser(userNumber);
            wxCpMessage.setMsgType("textcard");
            wxCpMessage.setTitle(title);
            wxCpMessage.setDescription(content);
            wxCpMessage.setUrl(url);
            wxCpMessage.setBtnTxt(btnText);
            
            log.info("发送企业微信卡片消息: 用户={}, 标题={}, 内容={}", userNumber, title, content);
            uacWxUserInfoApiService.sendToUser(wxCpMessage);
            
            // 记录日志
            log.info("成功发送企业微信卡片消息: 目标用户ID={}, 工号={}, 标题={}", targetUserId, userNumber, title);
        } catch (Exception e) {
            log.error("发送企业微信卡片消息失败，目标用户ID: {}, 原因: {}", targetUserId, e.getMessage(), e);
        }
    }

    /**
     * 线索合并
     *
     * 相同手机号&&线索类型是【总机询价】or【自主询价】&&线索的新建时间范围：同一天（00:00-24:00），会自动对线索进行合并
     *
     * 合并原则，最新的线索并入，第一条线索中。
     * @param leadsNo
     */
    @Override
    @Transactional
    public boolean mergeLeads(String leadsNo) {
        log.info("尝试线索合并:{}",leadsNo);
        if (StringUtils.isEmpty(leadsNo)){
            throw new ServiceException("合并时线索编号不能为空");
        }
        // 查询该商机信息
        BusinessLeadsEntity queryParam = new BusinessLeadsEntity();
        queryParam.setLeadsNo(leadsNo);
        BusinessLeadsEntity businessLeadsInfo = businessLeadsMapper.selectOneByAll(queryParam);
        List<Integer> typeList = new ArrayList();
        typeList.add(391);//总机询价
        typeList.add(394);//自主询价
        if(businessLeadsInfo!=null &&  businessLeadsInfo.getClueType() != null && !typeList.contains(businessLeadsInfo.getClueType())){
            log.info("相同手机号&&线索类型是【总机询价】or【自主询价】才会合并，此单不符合线索类型的条件");
            return false;
        }


        String phone = Optional.ofNullable(businessLeadsInfo).map(BusinessLeadsEntity::getPhone).orElseThrow(() -> new ServiceException("商机手机号为空"));
        if (StrUtil.isBlank(phone)) {
            log.info("无需合并，手机号为空");
            return false;
        }
        MergeLeadsDto mergeLeadsDto = new MergeLeadsDto();
        mergeLeadsDto.setBeginTime(ErpDateUtils.getStartOfDay(businessLeadsInfo.getAddTime()));
        mergeLeadsDto.setEndTime(ErpDateUtils.getEndOfDay(businessLeadsInfo.getAddTime()));
        mergeLeadsDto.setPhone(phone);
        // 根据合并规则查询所有线索
        List<BusinessLeadsEntity> waitForMergeList = businessLeadsMapper.findWaitForMergeList(mergeLeadsDto);
        if (CollectionUtils.isEmpty(waitForMergeList) || waitForMergeList.size() < 2){
            log.info("无需合并:{}",JSON.toJSON(waitForMergeList));
            return false;
        }
        BusinessLeadsEntity parentLeads = waitForMergeList.get(0);
        List<BusinessLeadsEntity> childLeads = waitForMergeList.subList(1, waitForMergeList.size());
        // 修改父线索 -合并其他
        if (parentLeads.getMergeStatus() != 2){
            BusinessLeadsEntity updateParentParam = new BusinessLeadsEntity();
            updateParentParam.setId(parentLeads.getId());
            updateParentParam.setMergeStatus(2);
            updateParentParam.setModTime(new Date());
            log.info("线索合并，更新父线索：{}",JSON.toJSON(updateParentParam));
            businessLeadsMapper.updateByPrimaryKeySelective(updateParentParam);
        }

        // 修改子线索 -被合并
        BusinessLeadsEntity updateChildParam = new BusinessLeadsEntity();
        List<Integer> ids = childLeads.stream()
                .filter(e ->
                        Objects.nonNull(e.getId()) &&
                        e.getMergeStatus() == 0 && 
                        (e.getFollowStatus().equals(0)|| e.getFollowStatus().equals(1)) &&
                        StringUtils.isEmpty(e.getParentLeadsNo()))
                .map(BusinessLeadsEntity::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ids)){
            updateChildParam.setIds(ids);
//            updateChildParam.setBelonger(parentLeads.getBelonger());
//            updateChildParam.setBelongerId(parentLeads.getBelongerId());
            updateChildParam.setParentLeadsNo(parentLeads.getLeadsNo());
            updateChildParam.setMergeStatus(1);
            updateChildParam.setModTime(new Date());
//            updateChildParam.setFollowStatus(parentLeads.getFollowStatus());
            log.info("线索合并,更新子线索：{}",JSON.toJSON(updateChildParam));
            businessLeadsMapper.updateByPrimaryKeyListSelective(updateChildParam);
        }
        log.info("线索合并完成");
        return true;
    }


    @Override
	public void track(BusinessLeadsEntity businessLeadsEntity,EventTrackingEnum eventTrackingEnum, Date addTime) {
        try {
        	Map<String, Object> trackParams = new HashMap<>();
            //${username}(${number})新建线索${leadsNo}给${belonger}(${belongerNumber})
            trackParams.put("leadsNo", businessLeadsEntity.getLeadsNo());
            //创建人信息
            UserDto user = userApiService.getUserById(businessLeadsEntity.getCreator());
            trackParams.put("track_user", user);
            //客户ID
            trackParams.put("traderId", businessLeadsEntity.getTraderId());
            //分配給的人
            UserDto belongerUser = userApiService.getUserById(businessLeadsEntity.getBelongerId());
            if(Objects.nonNull(belongerUser)){
                trackParams.put("belonger", belongerUser.getUsername());
                trackParams.put("belongerNumber", belongerUser.getNumber());
            }
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(eventTrackingEnum);
            TrackParamsData trackParamsData = new TrackParamsData();
            trackParamsData.setTrackParams(trackParams);
        	trackParamsData.setTrackTime(addTime);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackParamsData.setEventTrackingEnum(eventTrackingEnum);
            trackStrategy.track(trackParamsData);
        }catch(Exception e) {
        	log.error("埋点：{}，失败，不影响正常业务",eventTrackingEnum.getArchivedName(),e);
        }
	}

    @Autowired
    private RegionApiService regionApi;

    private void terminalTraderRegion(BusinessLeadsDto businessLeadsDto) {
        String terminalTraderRegion = businessLeadsDto.getTerminalTraderRegion();
        if (StrUtil.isNotBlank(terminalTraderRegion)) {
            String[] regionArr = terminalTraderRegion.split(",");
            String minRegion = regionArr[regionArr.length - 1].trim();
            if (StrUtil.isNotBlank(minRegion)) {
                businessLeadsDto.setTerminalTraderRegionStr(regionApi.getThisRegionToParentRegionP(Integer.valueOf(minRegion)));
            }
        }
    }

    @Override
    public String getCloseReasonTypeName(Integer closeReasonType) {
        if(closeReasonType == null){
            return "";
        }
        switch (closeReasonType) {
            case 1:
                return "无法取得联系";

            case 2:
                return "招投标无授权";

            case 3:
                return "产品不在经营范围";

            case 4:
                return "产品不在经营区域";

            case 5:
                return "仅咨询技术问题";

            case 6:
                return "客户没有购买意愿";

            case 7:
                return "没有竞争力、价格没优势";

            case 8:
                return "其他";

            default:
                return ""; // 处理未知类型

        }
    }

    @Override
    public BusinessLeadsDto getOne(Integer id) {
        BusinessLeadsEntity businessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(id);
        BusinessLeadsDto businessLeadsDto = businessLeadsConvertor.toDto(businessLeadsEntity);
        businessLeadsDto.setCloseReasonTypeName(getCloseReasonTypeName(businessLeadsDto.getCloseReasonType()));
        businessLeadsDto.setTerminalTraderNatureStr(TerminalTraderNatureEnum.getTitleByCode(businessLeadsDto.getTerminalTraderNature()));
        terminalTraderRegion(businessLeadsDto);
        /**
         * （业务类型）商机类型-字典值(小产品、大单品、综合项目、AED、应急)
         */
        Integer businessType = businessLeadsEntity.getBusinessType();
        if(businessType != null && businessType>0){
            SysOptionDefinitionDto businessTypeOption =   sysOptionDefinitionApiService.getOptionDefinitionById(businessType);
            businessLeadsDto.setBusinessTypeStr(businessTypeOption==null?"":businessTypeOption.getTitle());
        }
        //biddingPhase字段按以上同样的逻辑
        Integer purchasingType = businessLeadsEntity.getPurchasingType();
        if(purchasingType != null && purchasingType>0){
            SysOptionDefinitionDto purchasingTypeOption =   sysOptionDefinitionApiService.getOptionDefinitionById(purchasingType);
            businessLeadsDto.setPurchasingTypeStr(purchasingTypeOption==null?"":purchasingTypeOption.getTitle());
        }
        //biddingPhase字段按以上同样的逻辑-招标阶段
        Integer biddingPhase = businessLeadsEntity.getBiddingPhase();
        if(biddingPhase != null && biddingPhase>0){
            SysOptionDefinitionDto biddingPhaseOption =   sysOptionDefinitionApiService.getOptionDefinitionById(biddingPhase);
            businessLeadsDto.setBiddingPhaseStr(biddingPhaseOption==null?"":biddingPhaseOption.getTitle());
        }


        if (StrUtil.isNotBlank(businessLeadsDto.getTagIds())) {
            String[] tagArr = businessLeadsDto.getTagIds().split(",");
            List<Integer> ids = Arrays.stream(tagArr).map(Integer::valueOf).collect(Collectors.toList());
            List<CustomTagDto> tags = customTagApiService.getByIdList(ids);
            businessLeadsDto.setTags(tags);
        }

        // 客情关系
        if (StrUtil.isNotBlank(businessLeadsDto.getCustomerRelationship())) {
            String[] tagArr = businessLeadsDto.getCustomerRelationship().split(",");
            List<Integer> ids = Arrays.stream(tagArr).map(Integer::valueOf).collect(Collectors.toList());
            List<String> customerRelationshipStr = new ArrayList<>();
            for (Integer tagid : ids) {
                if (ErpConstant.ONE.equals(tagid)) {
                    customerRelationshipStr.add("决策人");
                }
                if (ErpConstant.TWO.equals(tagid)) {
                    customerRelationshipStr.add("使用人");
                }
            }
            businessLeadsDto.setCustomerRelationshipStr(customerRelationshipStr);
        }
        if (StrUtil.isNotBlank(businessLeadsDto.getTraderName())) {
            TraderInfoTyc tycInfoQuery = new TraderInfoTyc();
            tycInfoQuery.setName(businessLeadsDto.getTraderName());
            TraderInfoTyc traderInfoTyc = tycMapper.getTraderInfoTycByTraderName(tycInfoQuery);
            businessLeadsDto.setTycFlag(Objects.nonNull(traderInfoTyc) ? "Y" : "N");
        }

        Integer entrances = businessLeadsEntity.getEntrances();//咨询入口
        if(entrances != null && entrances>0){
            SysOptionDefinitionDto entrancesOption =   sysOptionDefinitionApiService.getOptionDefinitionById(entrances);
            businessLeadsDto.setEntrancesName(entrancesOption==null?"":entrancesOption.getTitle());
        }

        Integer functions = businessLeadsEntity.getFunctions();//渠道类型
        if(functions != null && functions>0){
            SysOptionDefinitionDto functionsOption =   sysOptionDefinitionApiService.getOptionDefinitionById(functions);
            businessLeadsDto.setFunctionsName(functions==null?"":functionsOption.getTitle());
        }





        UserDto createUser = userApiService.getUserBaseInfo(businessLeadsDto.getCreator());
        businessLeadsDto.setCreatorName(createUser.getUsername());
        businessLeadsDto.setCreatorPic(createUser.getAliasHeadPicture());

        if(businessLeadsDto.getBelongerId() != null){
            UserDto belongUser = userApiService.getUserBaseInfo(businessLeadsDto.getBelongerId());
            businessLeadsDto.setBelonger(belongUser.getUsername());
            businessLeadsDto.setBelongerPic(belongUser.getAliasHeadPicture());
        }

        businessLeadsDto.setClueTypeName(sysOptionDefinitionApiService.getOptionDefinitionById(businessLeadsDto.getClueType()).getTitle());

        businessLeadsDto.setSourceName(sysOptionDefinitionApiService.getOptionDefinitionById(businessLeadsDto.getSource()).getTitle());
        businessLeadsDto.setCommunicationName(sysOptionDefinitionApiService.getOptionDefinitionById(businessLeadsDto.getCommunication()).getTitle());
        businessLeadsDto.setInquiryName(sysOptionDefinitionApiService.getOptionDefinitionById(businessLeadsDto.getInquiry()).getTitle());
        businessLeadsDto.setEntrancesName(sysOptionDefinitionApiService.getOptionDefinitionById(businessLeadsDto.getEntrances()).getTitle());
        businessLeadsDto.setFunctionsName(sysOptionDefinitionApiService.getOptionDefinitionById(businessLeadsDto.getFunctions()).getTitle());
        try{
            if(businessLeadsDto.getTraderId() != null && businessLeadsDto.getTraderId()>0){
                try{
                    TraderCustomerInfoVo traderCustomerInfoVo =  traderCustomerBaseService.getTraderCustomerInfo(businessLeadsDto.getTraderId());
                    businessLeadsDto.setCustomerNature(traderCustomerInfoVo.getCustomerNature());
                    String targetTabUrl = ("/trader/customer/new/portrait.do?traderId="+businessLeadsDto.getTraderId()+"&traderCustomerId="+traderCustomerInfoVo.getTraderCustomerId()+"&customerNature="+traderCustomerInfoVo.getCustomerNature());
                    businessLeadsDto.setTraderNameInnerLink(targetTabUrl);
                    if(crmJumpErpUrl.indexOf("sso")>-1){
                        String encodetargetTabUrl = URLEncoder.encode("/index.do?target="+URLEncoder.encode(targetTabUrl,"UTF-8")+"&title=客户360","UTF-8");
                        businessLeadsDto.setTraderNameLink(crmJumpErpUrl+encodetargetTabUrl);
                    }else{
                        String encodetargetTabUrl = "/index.do?target="+ URLEncoder.encode(targetTabUrl,"UTF-8")+"&title="+URLEncoder.encode("客户360","UTF-8");
                        businessLeadsDto.setTraderNameLink(crmJumpErpUrl+encodetargetTabUrl);
                    }
                }catch(Exception e ){
                    log.error("客户链接转换给前端时失败，需要检查",e);
                }
            }
        }catch(Exception e){

        }


        return businessLeadsDto;
    }


    @Override
    public void closedLeads(BusinessLeadsDto businessLeadsDto) {
        BusinessLeadsEntity businessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(businessLeadsDto.getId());
        businessLeadsEntity.setCloseTime(new Date());
        businessLeadsEntity.setCloseReasonType(businessLeadsDto.getCloseReasonType());
        businessLeadsEntity.setCloseReasonTypeName(businessLeadsDto.getCloseReasonTypeName());
        businessLeadsEntity.setCloseReason(businessLeadsDto.getCloseReason());
        businessLeadsEntity.setFollowStatus(BusinessLeadsFollowStatusEnums.CLOSE.getType());
        businessLeadsEntity.setUpdater(businessLeadsDto.getUpdater());
        businessLeadsEntity.setUpdaterName(businessLeadsDto.getUpdaterName());
        businessLeadsEntity.setModTime(new Date());
        businessLeadsMapper.updateByPrimaryKeySelective(businessLeadsEntity);


        // 记录操作日志
        OperationLogDto logDto = new OperationLogDto();
        logDto.setCreator(businessLeadsDto.getUpdater());
        logDto.setCreatorName(businessLeadsDto.getUpdaterName());
        logDto.setOperationTime(new Date());
        HashMap<String,String> params = new HashMap<>();
        params.put("username",businessLeadsDto.getUpdaterName());
        logDto.setParams(params);
        logDto.setBizId(businessLeadsDto.getId());
        operationLogApiService.save(logDto, BizLogEnum.BUSINESSlEADS_CLOSE);


    }

    @Override
    @Transactional
    public List<Integer> importExcel(MultipartFile file) throws IOException {
        List<BusinessLeadsEntity> leadsEntities = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), BusinessLeadsDto.class, new PageReadListener<BusinessLeadsDto>(dataList ->
                dataList.forEach(data -> {
                    data.setBelongerId(CurrentUser.getCurrentUser().getId());
                    data.setBelonger(CurrentUser.getCurrentUser().getUsername());
                    leadsEntities.add(businessLeadsConvertor.toEntity(data));
                }))).sheet().doRead();
        // 清除上一次导入记录
        CustomDataLogDto lastEfficientCustomDataLog = this.getLastEfficientCustomDataLog();
        String[] ids = null;
        if (Objects.nonNull(lastEfficientCustomDataLog) && !StringUtils.isEmpty(lastEfficientCustomDataLog.getRelatedIds())) {
            ids = lastEfficientCustomDataLog.getRelatedIds().split(StrUtil.COMMA);
        }

        if (!CollectionUtils.isEmpty(leadsEntities)) {
            leadsEntities.forEach(c -> {
                ValidatorUtils.validate(c, AddGroup.class);
            });
            leadsEntities.forEach(c -> {
                businessLeadsMapper.insertSelective(c);

                BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.BUSINESS_LEADS, NoGeneratorBean.builder().id(c.getId()).numberOfDigits(8).build());
                String code = new BillNumGenerator().distribution(billGeneratorBean);
                c.setLeadsNo(code);
                businessLeadsMapper.updateByPrimaryKeySelective(c);
            });
        } else {
            throw new ServiceException("上传文件为空");
        }

        if (ArrayUtil.isNotEmpty(ids)) {
            Arrays.stream(ids).map(Integer::valueOf).forEach(id -> businessLeadsMapper.deleteByPrimaryKey(id));
        }


        return leadsEntities.stream().map(BusinessLeadsEntity::getId).collect(Collectors.toList());
    }

    @Override
    public void update(BusinessLeadsDto businessLeadsDto) {
        businessLeadsDto.setProvinceId(ObjectUtil.defaultIfNull(businessLeadsDto.getProvinceId(), 0));
        businessLeadsDto.setCityId(ObjectUtil.defaultIfNull(businessLeadsDto.getCityId(), 0));
        businessLeadsDto.setCountyId(ObjectUtil.defaultIfNull(businessLeadsDto.getCountyId(), 0));
        businessLeadsDto.setProvince(ObjectUtil.defaultIfNull(businessLeadsDto.getProvince(), ""));
        businessLeadsDto.setCity(ObjectUtil.defaultIfNull(businessLeadsDto.getCity(), ""));
        businessLeadsDto.setCounty(ObjectUtil.defaultIfNull(businessLeadsDto.getCounty(), ""));
        if(businessLeadsDto.getTraderId() == null){
            businessLeadsDto.setTraderId(0);
        }
        //将traderContactId进行上面一行同样的判断操作
        if(businessLeadsDto.getTraderContactId() == null){
            businessLeadsDto.setTraderContactId(0);
        }


        BusinessLeadsEntity businessLeadsEntity = businessLeadsConvertor.toEntity(businessLeadsDto);
        Integer id = businessLeadsEntity.getId();
        Integer belongerId = businessLeadsEntity.getBelongerId();
        boolean needNoticeFlag = false;
        BusinessLeadsEntity oldBusinessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(id);
        businessLeadsEntity.setLeadsNo(oldBusinessLeadsEntity.getLeadsNo());
        if (Objects.nonNull(belongerId) && !Objects.equals(oldBusinessLeadsEntity.getBelongerId(), belongerId)) {
            Integer newUserId = checkBusiness2OtherSaleUser(belongerId);
            if(newUserId > 0 && !newUserId.equals(belongerId)) {
                businessLeadsEntity.setBelongerId(newUserId);
                UserDto otherUser = userApiService.getUserById(newUserId);
                businessLeadsEntity.setBelonger(otherUser.getUsername());
            }
            businessLeadsEntity.setAssignTime(new Date());
            businessLeadsEntity.setFollowStatus(BusinessLeadsFollowStatusEnums.NO_PROCESS.getType());
            needNoticeFlag = true;
        }

      	//VDERP-17057  【客户档案】ERP客户档案时间轴 编辑线索（追溯）
        //处理：线索创建时间可能大于客户创建时间，即先创建了线索，然后再次编辑才关联客户
        //判断线索更新前是否已有客户ID
        Integer traderId = businessLeadsDto.getTraderId();
        if(Objects.nonNull(traderId) && traderId!=0) {
        	//客户ID不为空，判断上次编辑是否为空，如果上次为空，则本次需要追溯创建线索的埋点,或者上次不为空，且上次与本次客户ID不同追溯创建线索的埋点
        	if(Objects.isNull(oldBusinessLeadsEntity.getTraderId()) || (Objects.nonNull(oldBusinessLeadsEntity.getTraderId()) && oldBusinessLeadsEntity.getTraderId()==0)
        			||(Objects.nonNull(oldBusinessLeadsEntity.getTraderId()) && !oldBusinessLeadsEntity.getTraderId().equals(traderId))) {
        		//判断线索来源，来源前台自主询价，来源后台创建文案不一样
                if(oldBusinessLeadsEntity.getClueType().equals(ErpConstant.ID_394)) {
        			track(businessLeadsEntity,EventTrackingEnum.PRE_SALE_NEW_BUSINESS_LEADS_FRONT,oldBusinessLeadsEntity.getAddTime());
        		}else {
        			track(businessLeadsEntity,EventTrackingEnum.PRE_SALE_NEW_BUSINESS_LEADS,oldBusinessLeadsEntity.getAddTime());
        		}

        	}
        }
        businessLeadsMapper.updateByPrimaryKeySelective(businessLeadsEntity);

        if(StrUtil.isNotEmpty(businessLeadsDto.getCategoryIds())){

            businessOrderCategoryApiService.deleteByBusinessIdAndType(businessLeadsEntity.getId(),ErpConstant.ZERO);

            String[] categoryIds = businessLeadsDto.getCategoryIds().split(",");
            for(String categoryId : categoryIds){
                BusinessOrderCategoryDto businessOrderCategoryDto = new BusinessOrderCategoryDto();
                businessOrderCategoryDto.setBusinessId(businessLeadsEntity.getId());
                businessOrderCategoryDto.setBusinessType(ErpConstant.ZERO);
                businessOrderCategoryDto.setCategoryId(Integer.parseInt(categoryId));
                businessOrderCategoryDto.setKeywords(businessLeadsDto.getKeywords());
                businessOrderCategoryApiService.save(businessOrderCategoryDto);
            }
        }else{
            businessOrderCategoryApiService.deleteByBusinessIdAndType(businessLeadsEntity.getId(),ErpConstant.ZERO);
        }

         // 记录操作日志
        OperationLogDto logDto = new OperationLogDto();
        logDto.setCreator(businessLeadsDto.getUpdater());
        logDto.setCreatorName(businessLeadsDto.getUpdaterName());
        logDto.setOperationTime(new Date());
        HashMap<String,String> params = new HashMap<>();
        params.put("username",businessLeadsDto.getUpdaterName());
        logDto.setParams(params);
        logDto.setBizId(businessLeadsDto.getId());
        operationLogApiService.save(logDto, BizLogEnum.BUSINESSlEADS_EDIT);

        if (needNoticeFlag) {
            String targetUserName = StringUtils.isEmpty(businessLeadsEntity.getBelonger())?oldBusinessLeadsEntity.getBelonger():businessLeadsEntity.getBelonger();
            // 记录操作日志
            OperationLogDto logDtoAssign = new OperationLogDto();
            logDtoAssign.setCreator(businessLeadsDto.getUpdater());
            logDtoAssign.setCreatorName(businessLeadsDto.getUpdaterName());
            logDtoAssign.setOperationTime(new Date());
            HashMap<String,String> paramsAssign = new HashMap<>();
            paramsAssign.put("username",businessLeadsDto.getUpdaterName());
            paramsAssign.put("targetUserName",targetUserName);
            logDtoAssign.setParams(paramsAssign);
            logDtoAssign.setBizId(businessLeadsDto.getId());
            operationLogApiService.save(logDtoAssign, BizLogEnum.BUSINESSlEADS_ASSIGN);


            try {
                // 站内信
                String url = "./businessLeads/details.do?id=" + id;
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("", "");
                log.info("编辑线索,分配线索发送站内信start,线索id:{}", id);
                sendMessage(221, Collections.singletonList(businessLeadsEntity.getBelongerId()), paramMap, url);
                log.info("编辑线索,分配线索发送站内信end,线索id:{}", id);

                // 企微消息
                sendVxMessage(businessLeadsEntity, businessLeadsEntity.getBelongerId());
            } catch (Exception e) {
                log.info("编辑线索,分配线索发送站内信异常,线索id:{}", id, e);
            }
        }

    }



    @Autowired
    private OperationLogApiService operationLogApiService;

    @Autowired
    private UserWorkApiService userWorkApiService;

    public Integer checkBusiness2OtherSaleUser(Integer saleUserId){
        try{
            String dateTime = ErpDateUtils.getStringDateNow2();
            Integer newUserId = userWorkApiService.getUserBusiness2OtherSaleUser(saleUserId,dateTime);
            log.info("分配商机时，检查线索是否需要调整为另一个员工：{},{}",new Object[]{saleUserId,newUserId});
            return newUserId;
        }catch (Exception e){
            log.error("checkBusiness2OtherSaleUser error",e);
            return saleUserId;
        }

    }


    public void checkUser(Integer userId){
        if(userId == null || userId<1){
            return ;
        }
        UserDto checkUser = userApiService.getUserById( userId);
        if(checkUser !=null && checkUser.getUserId()>0){
            if(checkUser.getIsDisabled() == 1){
                throw new ServiceException("用户已被禁用，请重新选择或刷新当前页面。");
            }
        }else{
            throw new ServiceException("请重新选择或刷新当前页面。");
        }

        //再次校验UAC
        UserDto userDto = userApiService.searchByUserIdFromUac(userId);
        if(userDto !=null && userDto.getUserId()>0){
            if(checkUser.getIsDisabled() == 1){
                throw new ServiceException("用户已被禁用，请重新选择或刷新当前页面。");
            }
        }else{
            throw new ServiceException("请重新选择或刷新当前页面。");
        }

    }

    @Override
    public void assign(AssignLeadsDto assignLeadsDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        checkUser(assignLeadsDto.getUserId());
        assignLeadsDto.getIds().forEach(id -> {
            BusinessLeadsEntity oldBusinessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(id);
            if (oldBusinessLeadsEntity.getFollowStatus().equals(BusinessLeadsFollowStatusEnums.CLOSE.getType())
                    || oldBusinessLeadsEntity.getFollowStatus().equals(BusinessLeadsFollowStatusEnums.OPPORTUNITY.getType())) {
                throw new ServiceException("已关闭，跟进中，已商机的线索无法再次分配，请核实线索状态。");
            }

            if (oldBusinessLeadsEntity.getTraderId() != null && oldBusinessLeadsEntity.getTraderId() != 0) {
                throw new ServiceException(oldBusinessLeadsEntity.getLeadsNo() + "线索已确认客户且客户归属人≠线索归属人，请重新选择");
            }
        });

        assignLeadsDto.getIds().forEach(id -> {
            BusinessLeadsEntity oldBusinessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(id);
//            if (oldBusinessLeadsEntity.getFollowStatus().equals(BusinessLeadsFollowStatusEnums.CLOSE.getType())
//                    || oldBusinessLeadsEntity.getFollowStatus().equals(BusinessLeadsFollowStatusEnums.OPPORTUNITY.getType())) {
//                throw new ServiceException("已关闭，跟进中，已商机的线索无法再次分配，请核实线索状态。");
//            }
//
//            if (oldBusinessLeadsEntity.getTraderId() != null && oldBusinessLeadsEntity.getTraderId() != 0) {
//                throw new ServiceException(oldBusinessLeadsEntity.getLeadsNo() + "线索已确认客户且客户归属人≠线索归属人，请重新选择");
//            }

            Integer newUserId = checkBusiness2OtherSaleUser(assignLeadsDto.getUserId());//查询该员工是否请假了
            if(newUserId > 0 && !newUserId.equals(assignLeadsDto.getUserId())){
                assignLeadsDto.setUserId(newUserId); //将新的销售人员id赋值给assignLeadsDto
            }

            UserDto userDto = userApiService.getUserById(assignLeadsDto.getUserId());
            oldBusinessLeadsEntity.setBelongerId(assignLeadsDto.getUserId());
            oldBusinessLeadsEntity.setBelonger(userDto.getUsername());
            oldBusinessLeadsEntity.setAssignTime(new Date());
            oldBusinessLeadsEntity.setFollowStatus(BusinessLeadsFollowStatusEnums.NO_PROCESS.getType());
            businessLeadsMapper.updateByPrimaryKeySelective(oldBusinessLeadsEntity);

            OperationLogDto logDto = new OperationLogDto();
            Map<String,String> params = new HashMap<>();
            params.put("userName",currentUser.getUsername());
            params.put("targetUserName",userDto.getUsername());
            logDto.setCreator(currentUser.getId());
            logDto.setCreatorName(currentUser.getUsername());
            logDto.setOperationTime(new Date());
            logDto.setParams(params);
            logDto.setBizId(oldBusinessLeadsEntity.getId());

            operationLogApiService.save(logDto, BizLogEnum.BUSINESSlEADS_ASSIGN);


            String url = "./businessLeads/details.do?id=" + id;
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("", "");
            log.info("分配线索发送站内信start， 线索id：{}", id);
            sendMessage(221, Collections.singletonList(oldBusinessLeadsEntity.getBelongerId()), paramMap, url);
            log.info("分配线索发送站内信end， 线索id：{}", id);




            sendVxMessage(oldBusinessLeadsEntity,assignLeadsDto.getUserId());

        });
    }

    @Autowired
    private MessageApiService messageApiService;

    public void sendMessage(Integer messageTemplateId,List<Integer> userIds,Map<String, String> params,String url,String... str){
        MessageDto messageDto = new MessageDto();
        messageDto.setUserIds(userIds);
        messageDto.setMessageTemplateId(messageTemplateId);
        messageDto.setParams(params);
        messageDto.setUrl(url);
        try{
            messageApiService.send(messageDto);
        }catch (Exception e){
            log.error("推送消息到erp失败",e);
        }
    }

    private void sendVxMessage(BusinessLeadsEntity oldBusinessLeadsEntity,Integer newSaleUserId){
        Long time = ErpDateUtils.sysTimeMillis();
        BussinessChanceMessageVo messageVo = new BussinessChanceMessageVo();
        messageVo.setBussinessNo(oldBusinessLeadsEntity.getLeadsNo());
        messageVo.setCustomerName(org.apache.commons.lang.StringUtils.isEmpty(oldBusinessLeadsEntity.getTraderName())?"":oldBusinessLeadsEntity.getTraderName());
        messageVo.setMobile((org.apache.commons.lang.StringUtils.isEmpty(oldBusinessLeadsEntity.getContact())?"":oldBusinessLeadsEntity.getContact())+" "+  (org.apache.commons.lang.StringUtils.isEmpty(oldBusinessLeadsEntity.getPhone())?"":oldBusinessLeadsEntity.getPhone()));
        messageVo.setSendTime(ErpDateUtils.convertString(time,null));
        messageVo.setRemark(
                (org.apache.commons.lang.StringUtils.isEmpty(oldBusinessLeadsEntity.getGoodsInfo())?"":oldBusinessLeadsEntity.getGoodsInfo())+ "/"+
                        (org.apache.commons.lang.StringUtils.isEmpty(oldBusinessLeadsEntity.getRemark())?"":oldBusinessLeadsEntity.getRemark()));
        try{
            userWorkApiService.sendMsgForXs(newSaleUserId,messageVo);
        }catch (Exception e){
            log.error("非工作日推送消息失败",e);
        }
    }



    @Override
    public BusinessChanceDto getLeadsToChance(Integer id) {

        BusinessLeadsEntity businessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(id);
        BusinessChanceDto businessChanceDto = new BusinessChanceDto();
        businessChanceDto.setTraderId(ErpConstant.DEFAULT_ID == businessLeadsEntity.getTraderId() ? null : businessLeadsEntity.getTraderId());
        businessChanceDto.setTraderName(businessLeadsEntity.getTraderName());
        businessChanceDto.setTraderContactId(ErpConstant.DEFAULT_ID == businessLeadsEntity.getTraderContactId() ? null : businessLeadsEntity.getTraderContactId());
        businessChanceDto.setTraderContactName(businessLeadsEntity.getContact());
        businessChanceDto.setMobile(businessLeadsEntity.getPhone());
        businessChanceDto.setTelephone(businessLeadsEntity.getTelephone());
        businessChanceDto.setType(BusinessChanceConstant.TYPE_LEADS);
        businessChanceDto.setSource(BusinessChanceConstant.SOURCE_LEADS);
        businessChanceDto.setCommunication(BusinessChanceConstant.COMMUNICATION_LEADS);
        businessChanceDto.setTagIds(StringUtils.isNotBlank(businessLeadsEntity.getTagIds())?businessLeadsEntity.getTagIds():"");


        String areaStr = "{}{}{}";
        String province = StrUtil.isBlank(businessLeadsEntity.getProvince()) ? "" : "中国" + StrUtil.SPACE + businessLeadsEntity.getProvince();
        String city = StrUtil.isBlank(businessLeadsEntity.getCity()) ? "" : StrUtil.SPACE + businessLeadsEntity.getCity();
        String county = StrUtil.isBlank(businessLeadsEntity.getCounty()) ? "" : StrUtil.SPACE + businessLeadsEntity.getCounty();
        businessChanceDto.setCheckTraderArea(StrUtil.isBlank(StrUtil.format(areaStr, province, city, county))? null : StrUtil.format(areaStr, province, city, county));

        String areaIdStr = "{}{}{}";
        String provinceId = ErpConstant.DEFAULT_ID == businessLeadsEntity.getProvinceId() ? "" : businessLeadsEntity.getProvinceId().toString();
        String cityId = ErpConstant.DEFAULT_ID == businessLeadsEntity.getCityId() ? "" : StrUtil.COMMA + businessLeadsEntity.getCityId();
        String countyId = ErpConstant.DEFAULT_ID == businessLeadsEntity.getCountyId() ? "" : StrUtil.COMMA + businessLeadsEntity.getCountyId();
        businessChanceDto.setAreaIds(StrUtil.isBlank(StrUtil.format(areaIdStr, provinceId, cityId, countyId))? null : StrUtil.format(areaIdStr, provinceId, cityId, countyId));

        if (ErpConstant.DEFAULT_ID != businessLeadsEntity.getCountyId()) {
            businessChanceDto.setAreaId(businessLeadsEntity.getCountyId());
        }
        businessChanceDto.setProductCommentsSale(businessLeadsEntity.getGoodsInfo() + " " + businessLeadsEntity.getRemark());
        businessChanceDto.setUserId(businessLeadsEntity.getBelongerId());
        businessChanceDto.setBusinessChanceGoodsDtos(Collections.emptyList());
        return businessChanceDto;
    }

    @Override
    public List<BusinessLeadMergeDto> getLeadMerge(String leadNo) {
        log.info("线索合并查询：{}",leadNo);
        List<BusinessLeadsEntity> leadMergeList = businessLeadsMapper.getLeadMergeByParentLeadNo(leadNo);
        log.info("查询到合并商机：{}", JSON.toJSON(leadMergeList));
        List<BusinessLeadMergeDto> collect = leadMergeList.stream().map(e -> {
            BusinessLeadMergeDto dto = new BusinessLeadMergeDto();
            dto.setLeadsNo(e.getLeadsNo());
            dto.setBelonger(e.getBelonger());
            dto.setThreeClass(e.getContent());
            dto.setInquireSku(e.getGoodsInfo());
            dto.setInquireType(Optional.ofNullable(sysOptionDefinitionApiService.getOptionDefinitionById(e.getClueType())).map(SysOptionDefinitionDto::getTitle).orElse(""));
            dto.setInquireTime(DateUtils.format(e.getAddTime(), DateUtils.defaultDateFormat));
            UserDto userDto = userApiService.getUserBaseInfo(dto.getBelongerId());
            dto.setBelongerPic(userDto!=null?userDto.getAliasHeadPicture():"");
            dto.setBelongerId(dto.getBelongerId());
            return dto;
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public List<BusinessLeadsDto> getLeadsListByDto(BusinessLeadsDto businessLeadsDto) {
        return businessLeadsMapper.getLeadsListByDto(businessLeadsDto);
    }

    @Override
    public List<BusinessLeadsDto> getLeadsListByDtoToday(BusinessLeadsDto businessLeadsDto) {
        return businessLeadsMapper.getLeadsListByDto(businessLeadsDto);
    }

    @Override
    public void updateLeadsFirstFollowTime(Integer id) {
        BusinessLeadsEntity businessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(id);
        if (Objects.nonNull(businessLeadsEntity) && Objects.isNull(businessLeadsEntity.getFirstFollowTime())) {
            businessLeadsEntity.setFirstFollowTime(new Date());
            businessLeadsMapper.updateByPrimaryKeySelective(businessLeadsEntity);
        }
    }

    @Override
    public void updateLeadsFollowStatus(Integer id) {
        BusinessLeadsEntity businessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(id);
//        CommunicateRecordDto communicateRecordDto = new CommunicateRecordDto();
//        communicateRecordDto.setCommunicateType(4109);
//        communicateRecordDto.setRelatedId(id);
        if (BusinessLeadsFollowStatusEnums.NO_PROCESS.getType().equals(businessLeadsEntity.getFollowStatus()) ) {
            businessLeadsEntity.setFollowStatus(BusinessLeadsFollowStatusEnums.FOLLOWING.getType());
            businessLeadsMapper.updateByPrimaryKeySelective(businessLeadsEntity);
        }
    }

    @Override
    public void updateLeadsFollowStatusAndFirstFollowTime(Integer id) {
        BusinessLeadsEntity businessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(id);

        boolean updateFlag = false;
        if (BusinessLeadsFollowStatusEnums.NO_PROCESS.getType().equals(businessLeadsEntity.getFollowStatus()) ) {
            businessLeadsEntity.setFollowStatus(BusinessLeadsFollowStatusEnums.FOLLOWING.getType());
            updateFlag =true;
        }

        if (Objects.nonNull(businessLeadsEntity) && Objects.isNull(businessLeadsEntity.getFirstFollowTime())) {
            businessLeadsEntity.setFirstFollowTime(new Date());
            updateFlag =true;
        }
        if(updateFlag ){
            businessLeadsMapper.updateByPrimaryKeySelective(businessLeadsEntity);
        }else{
            log.info("无需更新");
        }

    }




    @Override
    public void updateLeadsFollowStatusOpportunity(Integer leadsId, Integer businessChanceId) {
        BusinessLeadsEntity businessLeadsEntity = new BusinessLeadsEntity();
        businessLeadsEntity.setId(leadsId);
        businessLeadsEntity.setFollowStatus(BusinessLeadsFollowStatusEnums.OPPORTUNITY.getType());
        businessLeadsEntity.setBusinessChanceId(businessChanceId);
        businessLeadsEntity.setTurnBusinessChanceTime(new Date());
        businessLeadsMapper.updateByPrimaryKeySelective(businessLeadsEntity);
    }

    @Override
    public void updateLeadsStatus(BusinessLeadsDto businessLeadsDto) {
        BusinessLeadsEntity businessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(businessLeadsDto.getId());
        businessLeadsEntity.setStatus(businessLeadsDto.getStatus());
        businessLeadsEntity.setInvalidReason(businessLeadsDto.getInvalidReason());
        businessLeadsMapper.updateByPrimaryKeySelective(businessLeadsEntity);
    }


    /**
     * 获取最后一次有效操作日志记录
     *
     * @return CustomDataLogDto
     */
    private CustomDataLogDto getLastEfficientCustomDataLog() {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        CustomDataLogDto customDataLogDto = CustomDataLogDto.builder()
                .belongerId(currentUser.getId())
                .successFlag(true)
                .saveType(CustomDataLogSaveTypeEnums.EXCEL_IMPORT.getType())
                .type(CustomDataOperBizTypeEnums.BUSINESS_LEADS.getType())
                .build();
        return customDataLogApiService.getByAllOrderByAddTime(customDataLogDto);
    }

	@Override
	public List<BusinessLeadsDto> getLeadsListByParams(Integer bussinessChanceId) {
		BusinessLeadsDto businessLeadsDto = new BusinessLeadsDto();
    	businessLeadsDto.setBusinessChanceId(bussinessChanceId);
    	return businessLeadsMapper.getLeadsListByParams(businessLeadsDto);
	}

    @Override
    public BusinessLeadsDto findByBusinessChanceId(Integer relatedId) {
        return businessLeadsConvertor.toDto(businessLeadsMapper.findByBusinessChanceId(relatedId));
    }

    @Override
    public BusinessLeadsDto findById(Integer relatedId) {
        return businessLeadsConvertor.toDto(businessLeadsMapper.selectByPrimaryKey(relatedId));
    }

    @Override
    public BusinessLeadsDto findByLeadsNo(String leadsNo){
        BusinessLeadsEntity businessLeadsEntity =  businessLeadsMapper.findByLeadsNo(leadsNo);
        if(businessLeadsEntity==null){
            return  null;
        }
        return businessLeadsConvertor.toDto(businessLeadsEntity);
    }


    @Override
    public void pushSave(PushBusinessLeadsDto dto) {

        BusinessLeadsDto businessLeadsDto = new BusinessLeadsDto();
        BeanUtil.copyProperties(dto, businessLeadsDto);
        add(businessLeadsDto);
    }

    @Override
    public String checkBusinessLeadsIfCurrentCurrentCanSee(Integer id,Integer uesrId,List<RSalesJBusinessOrderDto> list){

        BusinessLeadsDto dto = getOne(id);
        String errorMessage = null;
        if(dto !=null && !checkUserIsZongji(uesrId) ){
            List<Integer> userIdList = new ArrayList<>();
            userIdList.add(dto.getCreator());
            userIdList.add(dto.getBelongerId());
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)){
                List<Integer> saleUserIds = list.stream()
                        .map(RSalesJBusinessOrderDto::getSaleUserId)
                        .collect(Collectors.toList());
                userIdList.addAll(saleUserIds);
            }
            if(!userIdList.contains(uesrId)){
                List<Integer> sonList = userApiService.queryUserIdListSubFromUac(uesrId);
                boolean  containSon =  userIdList.stream().anyMatch(sonList::contains);
                if(!containSon){
                    boolean notAssign =  dto.getBelongerId()==null ||  dto.getBelongerId()<1;
                    if(notAssign){
                        errorMessage = "您无该条单据的查看权限。";
                    }else{
                        errorMessage = "您无该条单据的查看权限，可联系归属销售" + dto.getBelonger()+ "申请查看。";
                    }

                }
            }
        }
        return errorMessage;
    }

    @Override
    public String getBusinessLeadsNoById(Integer id) {
        BusinessLeadsEntity businessLeadsEntity = businessLeadsMapper.selectByPrimaryKey(id);
        return businessLeadsEntity.getLeadsNo();
    }

}
