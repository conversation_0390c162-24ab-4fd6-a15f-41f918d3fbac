<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.business.mapper.BusinessLeadsMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.business.domain.entity.BusinessLeadsEntity">
        <!--@mbg.generated-->
        <!--@Table T_BUSINESS_LEADS-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="LEADS_NO" jdbcType="VARCHAR" property="leadsNo"/>
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
        <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName"/>
        <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId"/>
        <result column="CONTACT" jdbcType="VARCHAR" property="contact"/>
        <result column="PHONE" jdbcType="VARCHAR" property="phone"/>
        <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone"/>
        <result column="GOODS_INFO" jdbcType="VARCHAR" property="goodsInfo"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="FOLLOW_STATUS" jdbcType="INTEGER" property="followStatus"/>
        <result column="FOLLOW_PIC" jdbcType="VARCHAR" property="followPic"/>
        <result column="TYPE" jdbcType="INTEGER" property="type"/>
        <result column="STATUS" jdbcType="INTEGER" property="status"/>
        <result column="INVALID_REASON" jdbcType="VARCHAR" property="invalidReason"/>
        <result column="TAG_IDS" jdbcType="VARCHAR" property="tagIds"/>
        <result column="BELONGER_ID" jdbcType="INTEGER" property="belongerId"/>
        <result column="BELONGER" jdbcType="VARCHAR" property="belonger"/>
        <result column="PROVINCE_ID" jdbcType="INTEGER" property="provinceId"/>
        <result column="CITY_ID" jdbcType="INTEGER" property="cityId"/>
        <result column="COUNTY_ID" jdbcType="INTEGER" property="countyId"/>
        <result column="PROVINCE" jdbcType="VARCHAR" property="province"/>
        <result column="CITY" jdbcType="VARCHAR" property="city"/>
        <result column="COUNTY" jdbcType="VARCHAR" property="county"/>
        <result column="ADDRESS" jdbcType="VARCHAR" property="address"/>
        <result column="TURN_BUSINESS_CHANCE_TIME" jdbcType="TIMESTAMP" property="turnBusinessChanceTime"/>
        <result column="CLOSE_TIME" jdbcType="TIMESTAMP" property="closeTime"/>
        <result column="ASSIGN_TIME" jdbcType="TIMESTAMP" property="assignTime"/>
        <result column="CLOSE_REASON" jdbcType="VARCHAR" property="closeReason"/>
        <result column="BUSINESS_CHANCE_ID" jdbcType="INTEGER" property="businessChanceId"/>
        <result column="FIRST_FOLLOW_TIME" jdbcType="TIMESTAMP" property="firstFollowTime"/>
        <result column="CLUE_TYPE" jdbcType="INTEGER" property="clueType"/>
        <result column="INQUIRY" jdbcType="INTEGER" property="inquiry"/>
        <result column="SOURCE" jdbcType="INTEGER" property="source"/>
        <result column="COMMUNICATION" jdbcType="INTEGER" property="communication"/>
        <result column="CONTENT" jdbcType="VARCHAR" property="content"/>
        <result column="OTHER_CONTACT_INFO" jdbcType="VARCHAR" property="otherContactInfo"/>
        <result column="MERGE_STATUS" jdbcType="INTEGER" property="mergeStatus"/>
        <result column="PARENT_LEADS_NO" jdbcType="VARCHAR" property="parentLeadsNo"/>
        <result column="SEND_VX" jdbcType="VARCHAR" property="sendVx"/>
        <result column="TYC_FLAG" jdbcType="VARCHAR" property="tycFlag"/>
        <result column="CLOSE_REASON_TYPE" jdbcType="INTEGER" property="closeReasonType"/>
        <result column="CLOSE_REASON_TYPE_NAME" jdbcType="VARCHAR" property="closeReasonTypeName"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
        <result column="ORDER_TIME" jdbcType="BIGINT" property="orderTime" />
        <result column="PURCHASING_TYPE" jdbcType="INTEGER" property="purchasingType" />
        <result column="TERMINAL_TRADER_NAME" jdbcType="VARCHAR" property="terminalTraderName" />
        <result column="TERMINAL_TRADER_NATURE" jdbcType="INTEGER" property="terminalTraderNature" />
        <result column="TERMINAL_TRADER_REGION" jdbcType="VARCHAR" property="terminalTraderRegion" />
        <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
        <result column="CUSTOMER_RELATIONSHIP" jdbcType="VARCHAR" property="customerRelationship" />
        <result column="BIDDING_PHASE" jdbcType="INTEGER" property="biddingPhase" />
        <result column="BIDDING_PARAMETER" jdbcType="INTEGER" property="biddingParameter" />

    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, LEADS_NO, TRADER_ID, TRADER_NAME, TRADER_CONTACT_ID,
        CONTACT, PHONE, TELEPHONE, GOODS_INFO, REMARK, FOLLOW_STATUS, FOLLOW_PIC, `TYPE`,
        `STATUS`, INVALID_REASON, TAG_IDS, BELONGER_ID, BELONGER, PROVINCE_ID, CITY_ID, COUNTY_ID,
        PROVINCE, CITY, COUNTY, ADDRESS, TURN_BUSINESS_CHANCE_TIME, CLOSE_TIME, ASSIGN_TIME,
        CLOSE_REASON, BUSINESS_CHANCE_ID, FIRST_FOLLOW_TIME, CLUE_TYPE, INQUIRY, SOURCE, COMMUNICATION, CONTENT, OTHER_CONTACT_INFO,ENTRANCES, FUNCTIONS,MERGE_STATUS,PARENT_LEADS_NO,SEND_VX,TYC_FLAG,CLOSE_REASON_TYPE,CLOSE_REASON_TYPE_NAME,AMOUNT,ORDER_TIME,PURCHASING_TYPE,TERMINAL_TRADER_NAME,TERMINAL_TRADER_NATURE,TERMINAL_TRADER_REGION,BUSINESS_TYPE,CUSTOMER_RELATIONSHIP,BIDDING_PHASE,BIDDING_PARAMETER

    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_BUSINESS_LEADS
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_BUSINESS_LEADS
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.erp.business.domain.entity.BusinessLeadsEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BUSINESS_LEADS (ADD_TIME, MOD_TIME, CREATOR,
                                      UPDATER, LEADS_NO, TRADER_ID,
                                      TRADER_NAME, TRADER_CONTACT_ID, CONTACT,
                                      PHONE, TELEPHONE, GOODS_INFO,
                                      REMARK, FOLLOW_STATUS, FOLLOW_PIC,
                                      `TYPE`, `STATUS`, INVALID_REASON,
                                      TAG_IDS, BELONGER_ID, BELONGER,
                                      PROVINCE_ID, CITY_ID, COUNTY_ID,
                                      PROVINCE, CITY, COUNTY,
                                      ADDRESS, TURN_BUSINESS_CHANCE_TIME, CLOSE_TIME,
                                      ASSIGN_TIME, CLOSE_REASON, BUSINESS_CHANCE_ID,
                                      FIRST_FOLLOW_TIME,SEND_VX,TYC_FLAG,CLOSE_REASON_TYPE,CLOSE_REASON_TYPE_NAME,
                                        AMOUNT,ORDER_TIME,PURCHASING_TYPE,TERMINAL_TRADER_NAME,TERMINAL_TRADER_NATURE,TERMINAL_TRADER_REGION,BUSINESS_TYPE,CUSTOMER_RELATIONSHIP,BIDDING_PHASE,BIDDING_PARAMETER

        )
        values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
                #{updater,jdbcType=INTEGER}, #{leadsNo,jdbcType=VARCHAR}, #{traderId,jdbcType=INTEGER},
                #{traderName,jdbcType=VARCHAR}, #{traderContactId,jdbcType=INTEGER}, #{contact,jdbcType=VARCHAR},
                #{phone,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR}, #{goodsInfo,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR}, #{followStatus,jdbcType=INTEGER}, #{followPic,jdbcType=VARCHAR},
                #{type,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{invalidReason,jdbcType=VARCHAR},
                #{tagIds,jdbcType=VARCHAR}, #{belongerId,jdbcType=INTEGER}, #{belonger,jdbcType=VARCHAR},
                #{provinceId,jdbcType=INTEGER}, #{cityId,jdbcType=INTEGER}, #{countyId,jdbcType=INTEGER},
                #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{county,jdbcType=VARCHAR},
                #{address,jdbcType=VARCHAR}, #{turnBusinessChanceTime,jdbcType=TIMESTAMP},
                #{closeTime,jdbcType=TIMESTAMP},
                #{assignTime,jdbcType=TIMESTAMP}, #{closeReason,jdbcType=VARCHAR}, #{businessChanceId,jdbcType=INTEGER},
                #{firstFollowTime,jdbcType=TIMESTAMP},#{sendVx,jdbcType=VARCHAR},#{tycFlag,jdbcType=VARCHAR} ,#{closeReasonType,jdbcType=INTEGER},#{closeReasonTypeName,jdbcType=VARCHAR},
            #{amount,jdbcType=DECIMAL}, #{orderTime,jdbcType=BIGINT}, #{purchasingType,jdbcType=INTEGER}, #{terminalTraderName,jdbcType=VARCHAR}, #{terminalTraderNature,jdbcType=INTEGER}, #{terminalTraderRegion,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER}, #{customerRelationship,jdbcType=VARCHAR}, #{biddingPhase,jdbcType=INTEGER}, #{biddingParameter,jdbcType=INTEGER}
        )
    </insert>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.erp.business.domain.entity.BusinessLeadsEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BUSINESS_LEADS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="leadsNo != null">
                LEADS_NO,
            </if>
            <if test="traderId != null">
                TRADER_ID,
            </if>
            <if test="traderName != null">
                TRADER_NAME,
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID,
            </if>
            <if test="contact != null">
                CONTACT,
            </if>
            <if test="phone != null">
                PHONE,
            </if>
            <if test="telephone != null">
                TELEPHONE,
            </if>
            <if test="goodsInfo != null">
                GOODS_INFO,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="followStatus != null">
                FOLLOW_STATUS,
            </if>
            <if test="followPic != null">
                FOLLOW_PIC,
            </if>
            <if test="type != null">
                `TYPE`,
            </if>
            <if test="status != null">
                `STATUS`,
            </if>
            <if test="invalidReason != null">
                INVALID_REASON,
            </if>
            <if test="tagIds != null">
                TAG_IDS,
            </if>
            <if test="belongerId != null">
                BELONGER_ID,
            </if>
            <if test="belonger != null">
                BELONGER,
            </if>
            <if test="provinceId != null">
                PROVINCE_ID,
            </if>
            <if test="cityId != null">
                CITY_ID,
            </if>
            <if test="countyId != null">
                COUNTY_ID,
            </if>
            <if test="province != null">
                PROVINCE,
            </if>
            <if test="city != null">
                CITY,
            </if>
            <if test="county != null">
                COUNTY,
            </if>
            <if test="address != null">
                ADDRESS,
            </if>
            <if test="turnBusinessChanceTime != null">
                TURN_BUSINESS_CHANCE_TIME,
            </if>
            <if test="closeTime != null">
                CLOSE_TIME,
            </if>
            <if test="assignTime != null">
                ASSIGN_TIME,
            </if>
            <if test="closeReason != null">
                CLOSE_REASON,
            </if>
            <if test="businessChanceId != null">
                BUSINESS_CHANCE_ID,
            </if>
            <if test="firstFollowTime != null">
                FIRST_FOLLOW_TIME,
            </if>
            <if test="clueType != null">
                CLUE_TYPE,
            </if>
            <if test="inquiry != null">
                INQUIRY,
            </if>
            <if test="source != null">
                SOURCE,
            </if>
            <if test="communication != null">
                COMMUNICATION,
            </if>
            <if test="content != null">
                CONTENT,
            </if>
            <if test="otherContactInfo != null">
                OTHER_CONTACT_INFO,
            </if>
            <if test="entrances != null">
                ENTRANCES,
            </if>
            <if test="functions != null">
                FUNCTIONS,
            </if>
            <if test="sendVx !=null">
                SEND_VX,
            </if>
            <if test="tycFlag !=null">
                TYC_FLAG,
            </if>
            <if test="closeReasonType !=null">
                CLOSE_REASON_TYPE,
            </if>
            <if test="closeReasonTypeName !=null">
                CLOSE_REASON_TYPE,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="orderTime != null">
                ORDER_TIME,
            </if>
            <if test="purchasingType != null">
                PURCHASING_TYPE,
            </if>
            <if test="terminalTraderName != null">
                TERMINAL_TRADER_NAME,
            </if>
            <if test="terminalTraderNature != null">
                TERMINAL_TRADER_NATURE,
            </if>
            <if test="terminalTraderRegion != null">
                TERMINAL_TRADER_REGION,
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE,
            </if>
            <if test="customerRelationship != null">
                CUSTOMER_RELATIONSHIP,
            </if>
            <if test="biddingPhase != null">
                BIDDING_PHASE,
            </if>
            <if test="biddingParameter != null">
                BIDDING_PARAMETER,
            </if>
    </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="leadsNo != null">
                #{leadsNo,jdbcType=VARCHAR},
            </if>
            <if test="traderId != null">
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderName != null">
                #{traderName,jdbcType=VARCHAR},
            </if>
            <if test="traderContactId != null">
                #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="contact != null">
                #{contact,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="goodsInfo != null">
                #{goodsInfo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="followStatus != null">
                #{followStatus,jdbcType=INTEGER},
            </if>
            <if test="followPic != null">
                #{followPic,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="invalidReason != null">
                #{invalidReason,jdbcType=VARCHAR},
            </if>
            <if test="tagIds != null">
                #{tagIds,jdbcType=VARCHAR},
            </if>
            <if test="belongerId != null">
                #{belongerId,jdbcType=INTEGER},
            </if>
            <if test="belonger != null">
                #{belonger,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                #{provinceId,jdbcType=INTEGER},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=INTEGER},
            </if>
            <if test="countyId != null">
                #{countyId,jdbcType=INTEGER},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="turnBusinessChanceTime != null">
                #{turnBusinessChanceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="closeTime != null">
                #{closeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="assignTime != null">
                #{assignTime,jdbcType=TIMESTAMP},
            </if>
            <if test="closeReason != null">
                #{closeReason,jdbcType=VARCHAR},
            </if>
            <if test="businessChanceId != null">
                #{businessChanceId,jdbcType=INTEGER},
            </if>
            <if test="firstFollowTime != null">
                #{firstFollowTime,jdbcType=TIMESTAMP},
            </if>
            <if test="clueType != null">
                #{clueType,jdbcType=INTEGER},
            </if>
            <if test="inquiry != null">
                #{inquiry,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="communication != null">
                #{communication,jdbcType=INTEGER},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="otherContactInfo != null">
                #{otherContactInfo,jdbcType=VARCHAR},
            </if>
            <if test="entrances != null">
                #{entrances,jdbcType=INTEGER},
            </if>
            <if test="functions != null">
                #{functions,jdbcType=INTEGER},
            </if>
            <if test="sendVx !=null">
                #{sendVx,jdbcType=VARCHAR},
            </if>
            <if test="tycFlag !=null">
                #{tycFlag,jdbcType=VARCHAR} ,
            </if>
            <if test="closeReasonType !=null">
                #{closeReasonType,jdbcType=INTEGER},
            </if>
            <if test="closeReasonTypeName !=null">
                #{closeReasonTypeName,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="orderTime != null">
                #{orderTime,jdbcType=BIGINT},
            </if>
            <if test="purchasingType != null">
                #{purchasingType,jdbcType=INTEGER},
            </if>
            <if test="terminalTraderName != null">
                #{terminalTraderName,jdbcType=VARCHAR},
            </if>
            <if test="terminalTraderNature != null">
                #{terminalTraderNature,jdbcType=INTEGER},
            </if>
            <if test="terminalTraderRegion != null">
                #{terminalTraderRegion,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="customerRelationship != null">
                #{customerRelationship,jdbcType=VARCHAR},
            </if>
            <if test="biddingPhase != null">
                #{biddingPhase,jdbcType=INTEGER},
            </if>
            <if test="biddingParameter != null">
                #{biddingParameter,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.business.domain.entity.BusinessLeadsEntity">
        <!--@mbg.generated-->
        update T_BUSINESS_LEADS
        <set>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="leadsNo != null">
                LEADS_NO = #{leadsNo,jdbcType=VARCHAR},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderName != null">
                TRADER_NAME = #{traderName,jdbcType=VARCHAR},
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="contact != null">
                CONTACT = #{contact,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                PHONE = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                TELEPHONE = #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="goodsInfo != null">
                GOODS_INFO = #{goodsInfo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="followStatus != null">
                FOLLOW_STATUS = #{followStatus,jdbcType=INTEGER},
            </if>
            <if test="followPic != null">
                FOLLOW_PIC = #{followPic,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `TYPE` = #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `STATUS` = #{status,jdbcType=INTEGER},
            </if>
            <if test="invalidReason != null">
                INVALID_REASON = #{invalidReason,jdbcType=VARCHAR},
            </if>
            <if test="tagIds != null">
                TAG_IDS = #{tagIds,jdbcType=VARCHAR},
            </if>
            <if test="belongerId != null">
                BELONGER_ID = #{belongerId,jdbcType=INTEGER},
            </if>
            <if test="belonger != null">
                BELONGER = #{belonger,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                PROVINCE_ID = #{provinceId,jdbcType=INTEGER},
            </if>
            <if test="cityId != null">
                CITY_ID = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="countyId != null">
                COUNTY_ID = #{countyId,jdbcType=INTEGER},
            </if>
            <if test="province != null">
                PROVINCE = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                CITY = #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                COUNTY = #{county,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                ADDRESS = #{address,jdbcType=VARCHAR},
            </if>
            <if test="turnBusinessChanceTime != null">
                TURN_BUSINESS_CHANCE_TIME = #{turnBusinessChanceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="closeTime != null">
                CLOSE_TIME = #{closeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="assignTime != null">
                ASSIGN_TIME = #{assignTime,jdbcType=TIMESTAMP},
            </if>
            <if test="closeReason != null">
                CLOSE_REASON = #{closeReason,jdbcType=VARCHAR},
            </if>
            <if test="businessChanceId != null">
                BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER},
            </if>
            <if test="firstFollowTime != null">
                FIRST_FOLLOW_TIME = #{firstFollowTime,jdbcType=TIMESTAMP},
            </if>
            <if test="clueType != null">
                CLUE_TYPE = #{clueType,jdbcType=INTEGER},
            </if>
            <if test="inquiry != null">
                INQUIRY = #{inquiry,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                SOURCE = #{source,jdbcType=INTEGER},
            </if>
            <if test="communication != null">
                COMMUNICATION = #{communication,jdbcType=INTEGER},
            </if>
            <if test="content != null">
                CONTENT = #{content,jdbcType=VARCHAR},
            </if>
            <if test="otherContactInfo != null">
                OTHER_CONTACT_INFO = #{otherContactInfo,jdbcType=VARCHAR},
            </if>
            <if test="mergeStatus != null">
                MERGE_STATUS = #{mergeStatus,jdbcType=INTEGER},
            </if>
            <if test="entrances != null">
                ENTRANCES = #{entrances,jdbcType=INTEGER},
            </if>
            <if test="functions != null">
                FUNCTIONS = #{functions,jdbcType=INTEGER},
            </if>
            <if test="sendVx != null">
                SEND_VX = #{sendVx,jdbcType=VARCHAR},
            </if>
            <if test="tycFlag != null">
                TYC_FLAG = #{tycFlag,jdbcType=VARCHAR},
            </if>
            <if test="closeReasonType !=null">
                CLOSE_REASON_TYPE=#{closeReasonType,jdbcType=INTEGER},
            </if>
            <if test="closeReasonTypeName !=null">
                CLOSE_REASON_TYPE_NAME=#{closeReasonTypeName,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="orderTime != null">
                ORDER_TIME = #{orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="purchasingType != null">
                PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER},
            </if>
            <if test="terminalTraderName != null">
                TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
            </if>
            <if test="terminalTraderNature != null">
                TERMINAL_TRADER_NATURE = #{terminalTraderNature,jdbcType=INTEGER},
            </if>
            <if test="terminalTraderRegion != null">
                TERMINAL_TRADER_REGION = #{terminalTraderRegion,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="customerRelationship != null">
                CUSTOMER_RELATIONSHIP = #{customerRelationship,jdbcType=VARCHAR},
            </if>
            <if test="biddingPhase != null">
                BIDDING_PHASE = #{biddingPhase,jdbcType=INTEGER},
            </if>
            <if test="biddingParameter != null">
                BIDDING_PARAMETER = #{biddingParameter,jdbcType=INTEGER},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKeyListSelective" parameterType="com.vedeng.erp.business.domain.entity.BusinessLeadsEntity">
        <!--@mbg.generated-->
        update T_BUSINESS_LEADS
        <set>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="leadsNo != null">
                LEADS_NO = #{leadsNo,jdbcType=VARCHAR},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderName != null">
                TRADER_NAME = #{traderName,jdbcType=VARCHAR},
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="contact != null">
                CONTACT = #{contact,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                PHONE = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                TELEPHONE = #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="goodsInfo != null">
                GOODS_INFO = #{goodsInfo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="followStatus != null">
                FOLLOW_STATUS = #{followStatus,jdbcType=INTEGER},
            </if>
            <if test="followPic != null">
                FOLLOW_PIC = #{followPic,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `TYPE` = #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `STATUS` = #{status,jdbcType=INTEGER},
            </if>
            <if test="invalidReason != null">
                INVALID_REASON = #{invalidReason,jdbcType=VARCHAR},
            </if>
            <if test="tagIds != null">
                TAG_IDS = #{tagIds,jdbcType=VARCHAR},
            </if>
            <if test="belongerId != null">
                BELONGER_ID = #{belongerId,jdbcType=INTEGER},
            </if>
            <if test="belonger != null">
                BELONGER = #{belonger,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                PROVINCE_ID = #{provinceId,jdbcType=INTEGER},
            </if>
            <if test="cityId != null">
                CITY_ID = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="countyId != null">
                COUNTY_ID = #{countyId,jdbcType=INTEGER},
            </if>
            <if test="province != null">
                PROVINCE = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                CITY = #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                COUNTY = #{county,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                ADDRESS = #{address,jdbcType=VARCHAR},
            </if>
            <if test="turnBusinessChanceTime != null">
                TURN_BUSINESS_CHANCE_TIME = #{turnBusinessChanceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="closeTime != null">
                CLOSE_TIME = #{closeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="assignTime != null">
                ASSIGN_TIME = #{assignTime,jdbcType=TIMESTAMP},
            </if>
            <if test="closeReason != null">
                CLOSE_REASON = #{closeReason,jdbcType=VARCHAR},
            </if>
            <if test="businessChanceId != null">
                BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER},
            </if>
            <if test="firstFollowTime != null">
                FIRST_FOLLOW_TIME = #{firstFollowTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mergeStatus != null">
                MERGE_STATUS = #{mergeStatus,jdbcType=INTEGER},
            </if>
            <if test="parentLeadsNo != null">
                PARENT_LEADS_NO = #{parentLeadsNo,jdbcType=VARCHAR},
            </if>
            <if test="sendVx != null">
                SEND_VX = #{sendVx,jdbcType=VARCHAR},
            </if>
            <if test="tycFlag != null">
                TYC_FLAG = #{tycFlag,jdbcType=VARCHAR},
            </if>
            <if test="closeReasonType !=null">
                CLOSE_REASON_TYPE=#{closeReasonType,jdbcType=INTEGER},
            </if>
            <if test="closeReasonTypeName !=null">
                CLOSE_REASON_TYPE_NAME=#{closeReasonTypeName,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="orderTime != null">
                ORDER_TIME = #{orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="purchasingType != null">
                PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER},
            </if>
            <if test="terminalTraderName != null">
                TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
            </if>
            <if test="terminalTraderNature != null">
                TERMINAL_TRADER_NATURE = #{terminalTraderNature,jdbcType=INTEGER},
            </if>
            <if test="terminalTraderRegion != null">
                TERMINAL_TRADER_REGION = #{terminalTraderRegion,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="customerRelationship != null">
                CUSTOMER_RELATIONSHIP = #{customerRelationship,jdbcType=VARCHAR},
            </if>
            <if test="biddingPhase != null">
                BIDDING_PHASE = #{biddingPhase,jdbcType=INTEGER},
            </if>
            <if test="biddingParameter != null">
                BIDDING_PARAMETER = #{biddingParameter,jdbcType=INTEGER},
            </if>
        </set>
        where ID in
        <foreach item="id" index="index" collection="ids" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>

    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.business.domain.entity.BusinessLeadsEntity">
        <!--@mbg.generated-->
        update T_BUSINESS_LEADS
        set ADD_TIME                  = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME                  = #{modTime,jdbcType=TIMESTAMP},
            CREATOR                   = #{creator,jdbcType=INTEGER},
            UPDATER                   = #{updater,jdbcType=INTEGER},
            LEADS_NO                  = #{leadsNo,jdbcType=VARCHAR},
            TRADER_ID                 = #{traderId,jdbcType=INTEGER},
            TRADER_NAME               = #{traderName,jdbcType=VARCHAR},
            TRADER_CONTACT_ID         = #{traderContactId,jdbcType=INTEGER},
            CONTACT                   = #{contact,jdbcType=VARCHAR},
            PHONE                     = #{phone,jdbcType=VARCHAR},
            TELEPHONE                 = #{telephone,jdbcType=VARCHAR},
            GOODS_INFO                = #{goodsInfo,jdbcType=VARCHAR},
            REMARK                    = #{remark,jdbcType=VARCHAR},
            FOLLOW_STATUS             = #{followStatus,jdbcType=INTEGER},
            FOLLOW_PIC                = #{followPic,jdbcType=VARCHAR},
            `TYPE`                    = #{type,jdbcType=INTEGER},
            `STATUS`                  = #{status,jdbcType=INTEGER},
            INVALID_REASON            = #{invalidReason,jdbcType=VARCHAR},
            TAG_IDS                   = #{tagIds,jdbcType=VARCHAR},
            BELONGER_ID               = #{belongerId,jdbcType=INTEGER},
            BELONGER                  = #{belonger,jdbcType=VARCHAR},
            PROVINCE_ID               = #{provinceId,jdbcType=INTEGER},
            CITY_ID                   = #{cityId,jdbcType=INTEGER},
            COUNTY_ID                 = #{countyId,jdbcType=INTEGER},
            PROVINCE                  = #{province,jdbcType=VARCHAR},
            CITY                      = #{city,jdbcType=VARCHAR},
            COUNTY                    = #{county,jdbcType=VARCHAR},
            ADDRESS                   = #{address,jdbcType=VARCHAR},
            TURN_BUSINESS_CHANCE_TIME = #{turnBusinessChanceTime,jdbcType=TIMESTAMP},
            CLOSE_TIME                = #{closeTime,jdbcType=TIMESTAMP},
            ASSIGN_TIME               = #{assignTime,jdbcType=TIMESTAMP},
            CLOSE_REASON              = #{closeReason,jdbcType=VARCHAR},
            BUSINESS_CHANCE_ID        = #{businessChanceId,jdbcType=INTEGER},
            FIRST_FOLLOW_TIME         = #{firstFollowTime,jdbcType=TIMESTAMP},
            SEND_VX                   = #{sendVx,jdbcType=VARCHAR},
            TYC_FLAG                  = #{tycFlag,jdbcType=VARCHAR},
            CLOSE_REASON_TYPE         =#{closeReasonType,jdbcType=INTEGER},
            CLOSE_REASON_TYPE_NAME    =#{closeReasonTypeName,jdbcType=VARCHAR},
            AMOUNT = #{amount,jdbcType=DECIMAL},
            ORDER_TIME = #{orderTime,jdbcType=TIMESTAMP},
            PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER},
            TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
            TERMINAL_TRADER_NATURE = #{terminalTraderNature,jdbcType=INTEGER},
            TERMINAL_TRADER_REGION = #{terminalTraderRegion,jdbcType=VARCHAR},
            BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
            CUSTOMER_RELATIONSHIP = #{customerRelationship,jdbcType=VARCHAR},
            BIDDING_PHASE = #{biddingPhase,jdbcType=INTEGER},
            BIDDING_PARAMETER = #{biddingParameter,jdbcType=INTEGER}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update T_BUSINESS_LEADS
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ADD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.addTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MOD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.modTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CREATOR = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creator != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updater != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LEADS_NO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.leadsNo != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.leadsNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="TRADER_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.traderId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="TRADER_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.traderName != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.traderName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="TRADER_CONTACT_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.traderContactId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.traderContactId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CONTACT = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contact != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.contact,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="PHONE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.phone != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.phone,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="TELEPHONE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.telephone != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.telephone,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="GOODS_INFO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.goodsInfo != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.goodsInfo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="REMARK = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="FOLLOW_STATUS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.followStatus != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.followStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="FOLLOW_PIC = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.followPic != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.followPic,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`TYPE` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.type != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.type,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`STATUS` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="INVALID_REASON = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.invalidReason != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.invalidReason,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="TAG_IDS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tagIds != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.tagIds,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BELONGER_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.belongerId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.belongerId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BELONGER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.belonger != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.belonger,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="PROVINCE_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.provinceId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.provinceId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CITY_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cityId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.cityId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="COUNTY_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.countyId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.countyId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="PROVINCE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.province != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.province,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CITY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.city != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.city,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="COUNTY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.county != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.county,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ADDRESS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.address != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.address,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="TURN_BUSINESS_CHANCE_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.turnBusinessChanceTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.turnBusinessChanceTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CLOSE_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.closeTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.closeTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ASSIGN_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.assignTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.assignTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CLOSE_REASON = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.closeReason != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.closeReason,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BUSINESS_CHANCE_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.businessChanceId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.businessChanceId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="FIRST_FOLLOW_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.firstFollowTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.firstFollowTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BUSINESS_LEADS
        (ADD_TIME, MOD_TIME, CREATOR, UPDATER, LEADS_NO, TRADER_ID, TRADER_NAME, TRADER_CONTACT_ID,
         CONTACT, PHONE, TELEPHONE, GOODS_INFO, REMARK, FOLLOW_STATUS, FOLLOW_PIC, `TYPE`,
         `STATUS`, INVALID_REASON, TAG_IDS, BELONGER_ID, BELONGER, PROVINCE_ID, CITY_ID,
         COUNTY_ID, PROVINCE, CITY, COUNTY, ADDRESS, TURN_BUSINESS_CHANCE_TIME, CLOSE_TIME,
         ASSIGN_TIME, CLOSE_REASON, BUSINESS_CHANCE_ID, FIRST_FOLLOW_TIME,SEND_VX,TYC_FLAG,CLOSE_REASON_TYPE,CLOSE_REASON_TYPE_NAME
        ,AMOUNT,ORDER_TIME,PURCHASING_TYPE,TERMINAL_TRADER_NAME,TERMINAL_TRADER_NATURE,TERMINAL_TRADER_REGION,BUSINESS_TYPE,CUSTOMER_RELATIONSHIP,BIDDING_PHASE,BIDDING_PARAMETER
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
             #{item.updater,jdbcType=INTEGER}, #{item.leadsNo,jdbcType=VARCHAR}, #{item.traderId,jdbcType=INTEGER},
             #{item.traderName,jdbcType=VARCHAR}, #{item.traderContactId,jdbcType=INTEGER},
             #{item.contact,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, #{item.telephone,jdbcType=VARCHAR},
             #{item.goodsInfo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.followStatus,jdbcType=INTEGER},
             #{item.followPic,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER},
             #{item.invalidReason,jdbcType=VARCHAR}, #{item.tagIds,jdbcType=VARCHAR},
             #{item.belongerId,jdbcType=INTEGER},
             #{item.belonger,jdbcType=VARCHAR}, #{item.provinceId,jdbcType=INTEGER}, #{item.cityId,jdbcType=INTEGER},
             #{item.countyId,jdbcType=INTEGER}, #{item.province,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR},
             #{item.county,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR},
             #{item.turnBusinessChanceTime,jdbcType=TIMESTAMP},
             #{item.closeTime,jdbcType=TIMESTAMP}, #{item.assignTime,jdbcType=TIMESTAMP},
             #{item.closeReason,jdbcType=VARCHAR},
             #{item.businessChanceId,jdbcType=INTEGER}, #{item.firstFollowTime,jdbcType=TIMESTAMP},
            #{item.sendVx,jdbcType=VARCHAR},#{tycFlag,jdbcType=VARCHAR},#{closeReasonType,jdbcType=INTEGER},#{closeReasonTypeName,jdbcType=VARCHAR}
            , #{amount,jdbcType=DECIMAL}, #{orderTime,jdbcType=BIGINT}, #{purchasingType,jdbcType=INTEGER}, #{terminalTraderName,jdbcType=VARCHAR}, #{terminalTraderNature,jdbcType=INTEGER}, #{terminalTraderRegion,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER}, #{customerRelationship,jdbcType=VARCHAR}, #{biddingPhase,jdbcType=INTEGER}, #{biddingParameter,jdbcType=INTEGER}

            )
        </foreach>
    </insert>
    

    <!--auto generated by MybatisCodeHelper on 2022-07-19-->
    <select id="findByAllToPage" resultType="com.vedeng.erp.business.domain.dto.BusinessLeadsDto">
        select TBL.id,
               TBL.add_time,
               TBL.mod_time,
               TBL.creator,
               TBL.updater,
               TBL.leads_no,
               TBL.trader_id,
               TBL.trader_name,
               CUSTOMER.TRADER_CUSTOMER_ID,
               CUSTOMER.CUSTOMER_NATURE,
               TBL.trader_contact_id,
               TBL.contact,
               TBL.phone,
               TBL.telephone,
               TBL.goods_info,
               TBL.remark,
               TBL.follow_status,
               TBL.follow_pic,
               TBL.type,
               TBL.status,
               TBL.invalid_reason,
               TBL.tag_ids,
               TBL.belonger_id,
               TBL.belonger,
        BELONG_DETAIL.ALIAS_HEAD_PICTURE AS 'belongerPic',
               TBL.province_id,
               TBL.city_id,
               TBL.county_id,
               TBL.province,
               TBL.city,
               TBL.county,
               TBL.address,
               TBL.turn_business_chance_time,
               TBL.close_time,
               TBL.assign_time,
               TBL.first_follow_time,
               TBL.merge_status,
               TBL.CLUE_TYPE,
               S.TITLE as clueTypeName,
               S0.TITLE as inquiryName,
               TBL.INQUIRY,
               TBL.SOURCE,
               TBL.COMMUNICATION,
               TBL.CONTENT,
               TBL.OTHER_CONTACT_INFO,
               TBL.SEND_VX,
               TBL.TYC_FLAG,
               TBL.CLOSE_REASON_TYPE,
               TBL.CLOSE_REASON_TYPE_NAME,
               S1.TITLE as sourceName,
               S2.TITLE as communicationName,
               S3.TITLE as entrancesName,
               S4.TITLE as functionsName,
               TU.USERNAME as 'creatorName',
               DETAIL.ALIAS_HEAD_PICTURE AS 'creatorPic',
               TCDO.ID          as 'customDataOperDto.id',
               TCDO.ADD_TIME    as 'customDataOperDto.addTime',
               TCDO.CREATOR     as 'customDataOperDto.creator',
               TCDO.BELONGER_ID as 'customDataOperDto.belongerId',
               TCDO.BELONGER    as 'customDataOperDto.belonger',
               TCR.COMMUNICATE_RECORD_ID AS   'communicateRecordDto.communicateRecordId',
               TCR.CONTENT_SUFFIX      AS   'communicateRecordDto.contentSuffix',
               TCR.NEXT_CONTACT_CONTENT AS   'communicateRecordDto.nextContactContent'
        from T_BUSINESS_LEADS TBL
        LEFT JOIN T_TRADER_CUSTOMER CUSTOMER ON TBL.TRADER_ID = CUSTOMER.TRADER_ID
                 left join T_CUSTOM_DATA_OPER TCDO on TCDO.RELATED_ID = TBL.ID and TCDO.BIZ_TYPE = 1
                      and TCDO.OPER_TYPE = 1 and TCDO.BELONGER_ID = #{currentUserId,jdbcType=INTEGER}
        left join T_USER TU ON TU.USER_ID = TBL.CREATOR
        LEFT JOIN T_USER_DETAIL DETAIL ON TU.USER_ID = DETAIL.USER_ID
        LEFT JOIN T_USER_DETAIL BELONG_DETAIL ON TBL.BELONGER_ID = BELONG_DETAIL.USER_ID

        left join T_SYS_OPTION_DEFINITION S on TBL.CLUE_TYPE = S.SYS_OPTION_DEFINITION_ID
        left join T_SYS_OPTION_DEFINITION S0 on TBL.INQUIRY = S0.SYS_OPTION_DEFINITION_ID
        left join T_SYS_OPTION_DEFINITION S1 on TBL.SOURCE = S1.SYS_OPTION_DEFINITION_ID
        left join T_SYS_OPTION_DEFINITION S2 on TBL.COMMUNICATION = S2.SYS_OPTION_DEFINITION_ID
        left join T_SYS_OPTION_DEFINITION S3 on TBL.ENTRANCES = S3.SYS_OPTION_DEFINITION_ID
        left join T_SYS_OPTION_DEFINITION S4 on TBL.FUNCTIONS = S4.SYS_OPTION_DEFINITION_ID
        LEFT JOIN (
            SELECT RELATED_ID, MAX(COMMUNICATE_RECORD_ID) AS MaxID
            FROM T_COMMUNICATE_RECORD
            WHERE COMMUNICATE_TYPE = '4109'
            GROUP BY RELATED_ID
        )  TCR_TEMP ON TBL.ID=TCR_TEMP.RELATED_ID
        LEFT JOIN T_COMMUNICATE_RECORD   TCR ON TCR_TEMP.MaxID = TCR.COMMUNICATE_RECORD_ID

        <where>
            <if test="queryAll != null and queryAll == 0">
                (
                <!-- 归属人 -->
                EXISTS (
                    SELECT 1 
                    FROM (
                        <foreach item="userId" index="index" collection="userIdList" separator=" UNION ALL " >
                            SELECT #{userId,jdbcType=INTEGER} AS USER_ID
                        </foreach>
                    ) TEMP_USERS 
                    WHERE TEMP_USERS.USER_ID = TBL.BELONGER_ID
                )
                
                <!-- 创建人 -->
                <if test="allSubordinateUserIdList != null and allSubordinateUserIdList.size() > 0">
                    or EXISTS (
                        SELECT 1 
                        FROM (
                            <foreach item="subordinateId" index="index" collection="allSubordinateUserIdList" separator="UNION ALL">
                                SELECT #{subordinateId,jdbcType=INTEGER} AS USER_ID
                            </foreach>
                        ) t 
                        WHERE TBL.CREATOR = t.USER_ID
                    )
                </if>


                <!-- 修改协作人条件，包含所有三种类型的协作人 -->
                OR EXISTS (
                    /* 1. 手动添加的协作人 */
                    SELECT 1 FROM T_R_SALES_J_BUSINESS_ORDER
                    WHERE BUSINESS_ID = TBL.ID 
                    AND BUSINESS_TYPE = 5 
                    AND IS_DELETED = 0 
                    AND SALE_USER_ID = #{currentUserId,jdbcType=INTEGER}
                )
                
                /* 2. 线下销售作为协作人 - 根据归属销售(线上销售)查询对应的线下销售 */
                OR EXISTS (
                    SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
                    WHERE RUR.ONLINE_SALES_ID = TBL.BELONGER_ID  
                    AND RUR.OFFLINE_SALES_ID = #{currentUserId,jdbcType=INTEGER}
                )

                /* 3. 产线负责人作为协作人 */
                OR EXISTS(
                       SELECT 1
                       FROM ROLE_USER_CATEGORY_CONFIG RUC
                       WHERE RUC.USER_ID = #{currentUserId,jdbcType=INTEGER}
                         AND EXISTS(
                               SELECT 1
                               FROM T_BUSINESS_ORDER_CATEGORY TBOC
                               WHERE TBOC.BUSINESS_ID = TBL.ID
                                 AND TBOC.BUSINESS_TYPE = 0
                                 AND FIND_IN_SET(TBOC.CATEGORY_ID, RUC.CATEGORY_IDS) > 0
                           )
                         AND (
                               NOT EXISTS(
                                       SELECT 1
                                       FROM ROLE_USER_REGION_CONFIG RUR2
                                       WHERE RUR2.PRODUCTION_USER_ID = RUC.USER_ID
                                   )
                               OR EXISTS(
                                       SELECT 1
                                       FROM ROLE_USER_REGION_CONFIG RUR3
                                       WHERE RUR3.PRODUCTION_USER_ID = RUC.USER_ID
                                         AND RUR3.ONLINE_SALES_ID = TBL.BELONGER_ID
                                   )
                           )
                   )

            
                )
            </if>

            <if test="traderNameList != null and traderNameList.size() > 0">
                AND TBL.TRADER_NAME IN
                <foreach item="traderNameItem" index="index" collection="traderNameList" separator="," open="(" close=")">
                    #{traderNameItem,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="creatorUserIdList != null and creatorUserIdList.size() > 0">
                AND TBL.CREATOR IN
                <foreach item="creatorItem" index="index" collection="creatorUserIdList" separator="," open="(" close=")">
                    #{creatorItem,jdbcType=INTEGER}
                </foreach>
            </if>

            <if test="shareUserIdList != null and shareUserIdList.size() > 0">
                AND TBL.ID IN
                (
                SELECT BUSINESS_ID FROM T_R_SALES_J_BUSINESS_ORDER
                WHERE BUSINESS_TYPE=5 AND IS_DELETED=0 AND  SALE_USER_ID IN
                <foreach item="shareUserIdItm" index="index" collection="shareUserIdList" separator="," open="(" close=")">
                    #{shareUserIdItm,jdbcType=INTEGER}
                </foreach>
                )
            </if>
            and TBL.MERGE_STATUS IN (0,2)
            <if test="leadsNo != null and leadsNo != ''">
                and TBL.LEADS_NO like concat('%', #{leadsNo,jdbcType=VARCHAR}, '%')
            </if>
            <if test="traderId != null">
                and TBL.TRADER_ID = #{traderId,jdbcType=INTEGER}
            </if>
            <if test="traderName != null and traderName != ''">
                and TBL.TRADER_NAME like concat('%', #{traderName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="traderContactId != null">
                and TBL.TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER}
            </if>
            <if test="contactWay != null and contactWay != ''">
                and (TBL.CONTACT like concat('%', #{contactWay,jdbcType=VARCHAR}, '%')
                or TBL.PHONE like concat('%', #{contactWay,jdbcType=VARCHAR}, '%')
                or TBL.TELEPHONE like concat('%', #{contactWay,jdbcType=VARCHAR}, '%')
                or TBL.OTHER_CONTACT_INFO like concat('%', #{contactWay,jdbcType=VARCHAR}, '%'))
            </if>
            <if test="communicateContent != null and communicateContent != ''">
                and (TCR.CONTENT_SUFFIX like concat('%', #{communicateContent,jdbcType=VARCHAR}, '%')
                or TCR.COMMENTS like concat('%', #{communicateContent,jdbcType=VARCHAR}, '%')
                )
            </if>
            
            <!-- 添加商品分类ID过滤条件 -->
            <if test="categoryIdList != null and categoryIdList.size() > 0">
                AND EXISTS (
                    SELECT 1 FROM T_BUSINESS_ORDER_CATEGORY TBOC
                    WHERE TBOC.BUSINESS_ID = TBL.ID 
                    AND TBOC.BUSINESS_TYPE = 0  <!-- 0表示线索 -->
                    AND TBOC.CATEGORY_ID IN
                    <foreach item="categoryId" index="index" collection="categoryIdList" separator="," open="(" close=")">
                        #{categoryId,jdbcType=INTEGER}
                    </foreach>
                )
            </if>
            
            <!-- 添加省份和城市过滤条件，根据销售关系配置 -->
            <if test="provinceIds != null and provinceIds.size() > 0">
                AND EXISTS (
                    SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
                    WHERE RUR.PROVINCE_ID IN
                    <foreach item="provinceId" index="index" collection="provinceIds" separator="," open="(" close=")">
                        #{provinceId,jdbcType=INTEGER}
                    </foreach>
                    AND TBL.BELONGER_ID IN (
                        SELECT DISTINCT 
                            CASE 
                                WHEN RUR.ONLINE_SALES_ID > 0 THEN RUR.ONLINE_SALES_ID
                                WHEN RUR.OFFLINE_SALES_ID > 0 THEN RUR.OFFLINE_SALES_ID
                                ELSE 0
                            END AS SALES_ID
                        FROM ROLE_USER_REGION_CONFIG RUR
                        WHERE RUR.PROVINCE_ID IN
                        <foreach item="provinceId" index="index" collection="provinceIds" separator="," open="(" close=")">
                            #{provinceId,jdbcType=INTEGER}
                        </foreach>
                        AND (RUR.ONLINE_SALES_ID > 0 OR RUR.OFFLINE_SALES_ID > 0)
                    )
                )
            </if>
            
            <if test="cityIds != null and cityIds.size() > 0">
                AND EXISTS (
                    SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
                    WHERE RUR.CITY_ID IN
                    <foreach item="cityId" index="index" collection="cityIds" separator="," open="(" close=")">
                        #{cityId,jdbcType=INTEGER}
                    </foreach>
                    AND TBL.BELONGER_ID IN (
                        SELECT DISTINCT 
                            CASE 
                                WHEN RUR.ONLINE_SALES_ID > 0 THEN RUR.ONLINE_SALES_ID
                                WHEN RUR.OFFLINE_SALES_ID > 0 THEN RUR.OFFLINE_SALES_ID
                                ELSE 0
                            END AS SALES_ID
                        FROM ROLE_USER_REGION_CONFIG RUR
                        WHERE RUR.CITY_ID IN
                        <foreach item="cityId" index="index" collection="cityIds" separator="," open="(" close=")">
                            #{cityId,jdbcType=INTEGER}
                        </foreach>
                        AND (RUR.ONLINE_SALES_ID > 0 OR RUR.OFFLINE_SALES_ID > 0)
                    )
                )
            </if>
            
            <if test="contact != null and contact != ''">
                and TBL.CONTACT like concat('%', #{contact,jdbcType=VARCHAR}, '%')
            </if>
            <if test="phone != null and phone != ''">
                and TBL.PHONE = #{phone,jdbcType=VARCHAR}
            </if>
            <if test="telephone != null and telephone != ''">
                and TBL.TELEPHONE = #{telephone,jdbcType=VARCHAR}
            </if>
            <if test="contactMerge != null and contactMerge != ''">
                and (TBL.TELEPHONE = #{contactMerge,jdbcType=VARCHAR}
                or TBL.PHONE = #{contactMerge,jdbcType=VARCHAR}
                or TBL.OTHER_CONTACT_INFO like concat('%', #{contactMerge,jdbcType=VARCHAR}, '%')
                )
            </if>
            <if test="goodsInfo != null and goodsInfo != ''">
                and TBL.GOODS_INFO like concat('%', #{goodsInfo,jdbcType=VARCHAR}, '%')
            </if>
            <if test="remark != null and remark != ''">
                and TBL.REMARK = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="followStatus != null">
                and TBL.FOLLOW_STATUS = #{followStatus,jdbcType=INTEGER}
            </if>
            <if test="type != null">
                and TBL.`TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="status != null">
                and TBL.`STATUS` = #{status,jdbcType=INTEGER}
            </if>
            <if test="invalidReason != null and invalidReason != ''">
                and TBL.INVALID_REASON = #{invalidReason,jdbcType=VARCHAR}
            </if>

            <if test="belongerId != null">
                and TBL.BELONGER_ID = #{belongerId,jdbcType=INTEGER}
            </if>
            <if test="belonger != null and belonger != ''">
                and TBL.BELONGER = #{belonger,jdbcType=VARCHAR}
            </if>

            <if test="provinceId != null">
                and TBL.PROVINCE_ID = #{provinceId,jdbcType=INTEGER}
            </if>
            <if test="cityId != null">
                and TBL.CITY_ID = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="countyId != null">
                and TBL.COUNTY_ID = #{countyId,jdbcType=INTEGER}
            </if>
            <if test="id != null">
                and TBL.ID = #{id,jdbcType=INTEGER}
            </if>
            <if test="creator != null">
                and TBL.CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="nextCommunicationDateStart != null and nextCommunicationDateEnd != null">
                and TCR.NEXT_CONTACT_DATE between #{nextCommunicationDateStart,jdbcType=TIMESTAMP} and #{nextCommunicationDateEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="searchTime != null and startTime != null and endTime != null">
                <if test="searchTime == 'addTime'">
                    and TBL.ADD_TIME between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
                </if>
                <if test="searchTime == 'turnBusinessChanceTime'">
                    and TBL.TURN_BUSINESS_CHANCE_TIME between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
                </if>
                <if test="searchTime == 'closeTime'">
                    and TBL.CLOSE_TIME between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
                </if>
                <if test="searchTime == 'assignTime'">
                    and TBL.ASSIGN_TIME between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
                </if>
            </if>
            <if test="startAddTime != null and startAddTime !='' ">
                AND TBL.ADD_TIME &gt;= #{startAddTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endAddTime != null  and endAddTime !='' ">
                AND TBL.ADD_TIME &lt;= #{endAddTime,jdbcType=TIMESTAMP}
            </if>
            <if test="startTurnBusinessChanceTime != null and startTurnBusinessChanceTime != ''">
                AND TBL.TURN_BUSINESS_CHANCE_TIME &gt;= #{startTurnBusinessChanceTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTurnBusinessChanceTime != null and endTurnBusinessChanceTime != ''">
                AND TBL.TURN_BUSINESS_CHANCE_TIME &lt;= #{endTurnBusinessChanceTime,jdbcType=TIMESTAMP}
            </if>

            <if test="startCloseTime != null and startCloseTime != ''">
                AND TBL.CLOSE_TIME &gt;= #{startCloseTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endCloseTime != null and endCloseTime != ''">
                AND TBL.CLOSE_TIME &lt;= #{endCloseTime,jdbcType=TIMESTAMP}
            </if>

            <if test="startAssignTime != null and startAssignTime != ''">
                AND TBL.ASSIGN_TIME &gt;= #{startAssignTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endAssignTime != null and endAssignTime != ''">
                AND TBL.ASSIGN_TIME &lt;= #{endAssignTime,jdbcType=TIMESTAMP}
            </if>

            <if test="(startLastCommunicationTime != null and  startLastCommunicationTime != '') or ( endLastCommunicationTime != null and endLastCommunicationTime!='')">

                <if test="startLastCommunicationTime != null and startLastCommunicationTime != ''">
                    AND TCR.ADD_TIME &gt;= UNIX_TIMESTAMP(#{startLastCommunicationTime,jdbcType=TIMESTAMP})*1000
                </if>
                <if test="endLastCommunicationTime != null and endLastCommunicationTime != ''">
                    AND TCR.ADD_TIME &lt;= UNIX_TIMESTAMP(#{endLastCommunicationTime,jdbcType=TIMESTAMP})*1000
                </if>

            </if>

            <if test="quickSearchDate != null">
                and TBL.FOLLOW_STATUS = 1
                and TBL.ADD_TIME &lt;= #{quickSearchDate,jdbcType=TIMESTAMP}
            </if>

            <if test="tagIdList != null and tagIdList.size() > 0">
                <foreach item="item" index="index" collection="tagIdList">
                    and find_in_set(#{item}, TBL.TAG_IDS)
                </foreach>
            </if>

            <if test="followStatusList != null  and followStatusList.size() > 0">
                and TBL.FOLLOW_STATUS in
                <foreach item="item" index="index" collection="followStatusList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>

            <if test="clueTypeList != null  and clueTypeList.size() > 0">
                and TBL.CLUE_TYPE in
                <foreach item="item" index="index" collection="clueTypeList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>


            <if test="belongerIdList != null and belongerIdList.size() > 0">
                and TBL.BELONGER_ID in
                <foreach item="item" index="index" collection="belongerIdList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            
            <if test="belongOrgUserIdList != null and belongOrgUserIdList.size() > 0">
                and TBL.BELONGER_ID in
                <foreach item="item" index="index" collection="belongOrgUserIdList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>

            <if test="provinceIdList != null and provinceIdList.size() > 0 or cityIdList != null and cityIdList.size() > 0 or countyIdList != null and countyIdList.size() > 0">
                AND (
                <if test="provinceIdList != null and provinceIdList.size() > 0">
                    TBL.PROVINCE_ID IN
                    <foreach item="item" index="index" collection="provinceIdList"
                             open="(" separator="," close=")">
                        #{item,jdbcType=INTEGER}
                    </foreach>
                    <if test="cityIdList != null and cityIdList.size() > 0 or countyIdList != null and countyIdList.size() > 0">
                        OR
                    </if>
                </if>

                <if test="cityIdList != null and cityIdList.size() > 0">
                    TBL.CITY_ID IN
                    <foreach item="item" index="index" collection="cityIdList"
                             open="(" separator="," close=")">
                        #{item,jdbcType=INTEGER}
                    </foreach>
                    <if test="countyIdList != null and countyIdList.size() > 0">
                        OR
                    </if>
                </if>

                <if test="countyIdList != null and countyIdList.size() > 0">
                    TBL.COUNTY_ID IN
                    <foreach item="item" index="index" collection="countyIdList"
                             open="(" separator="," close=")">
                        #{item,jdbcType=INTEGER}
                    </foreach>
                </if>
                )
            </if>
            <if test="clueType != null">
                and TBL.CLUE_TYPE = #{clueType,jdbcType=INTEGER}
            </if>
            <if test="inquiry != null">
                and TBL.INQUIRY = #{inquiry,jdbcType=INTEGER}
            </if>
            <if test="inquiryList != null and inquiryList.size() > 0">
                and TBL.INQUIRY in
                <foreach item="item" index="index" collection="inquiryList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="assignOrNot != null and assignOrNot == 1">
                and TBL.BELONGER_ID = 0
            </if>
            <if test="assignOrNot != null and assignOrNot == 2">
                and TBL.BELONGER_ID != 0
            </if>
            <if test="entrances != null">
                and TBL.ENTRANCES = #{entrances,jdbcType=INTEGER}
            </if>
            <if test="entrancesList != null and entrancesList.size() != 0">
                and TBL.ENTRANCES in
                <foreach item="item" index="index" collection="entrancesList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="functions != null">
                and TBL.FUNCTIONS = #{functions,jdbcType=INTEGER}
            </if>
            <if test="functionsList != null and functionsList.size() != 0">
                and TBL.FUNCTIONS in
                <foreach item="item" index="index" collection="functionsList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="entrancesList != null and entrancesList.size() != 0">
                and TBL.ENTRANCES in
                <foreach item="item" index="index" collection="entrancesList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="content != null and content != ''">
                and TBL.CONTENT like concat('%',#{content,jdbcType=VARCHAR},'%')
            </if>
            <if test="(communicationIdList != null and communicationIdList.size() != 0) or (sourceIdList != null and sourceIdList.size() != 0)">
                AND (
                <choose>
                    <when test="communicationIdList != null and communicationIdList.size() != 0
                    and sourceIdList != null and sourceIdList.size() != 0">
                        TBL.COMMUNICATION IN
                        <foreach item="item" index="index" collection="communicationIdList"
                                 open="(" separator="," close=")">
                            #{item, jdbcType=INTEGER}
                        </foreach>
                        OR
                        TBL.SOURCE IN
                        <foreach item="item" index="index" collection="sourceIdList"
                                 open="(" separator="," close=")">
                            #{item, jdbcType=INTEGER}
                        </foreach>
                    </when>
                    <when test="communicationIdList != null and communicationIdList.size() != 0">
                        TBL.COMMUNICATION IN
                        <foreach item="item" index="index" collection="communicationIdList"
                                 open="(" separator="," close=")">
                            #{item, jdbcType=INTEGER}
                        </foreach>
                    </when>
                    <when test="sourceIdList != null and sourceIdList.size() != 0">
                        TBL.SOURCE IN
                        <foreach item="item" index="index" collection="sourceIdList"
                                 open="(" separator="," close=")">
                            #{item, jdbcType=INTEGER}
                        </foreach>
                    </when>
                </choose>
                )
            </if>
        </where>

        group by TBL.ID
        order by TCDO.OPER_TIME desc, TBL.ID desc
    </select>

    <select id="findAllCreateUser" resultType="com.vedeng.erp.system.dto.UserDto">
        select
        DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
        a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
        c.CC_NUMBER,c.ALIAS_HEAD_PICTURE
        from
        T_USER a
        left join
        T_USER_DETAIL c on a.USER_ID = c.USER_ID
        where a.USER_ID IN (
            select distinct
            TBL.CREATOR
            from T_BUSINESS_LEADS TBL
        )
        AND a.USER_ID >1
        AND a.USERNAME LIKE CONCAT('%',#{name},'%')
        ORDER BY a.USERNAME  ASC    LIMIT 100
    </select>

    <select id="findAllBelongUser" resultType="com.vedeng.erp.system.dto.UserDto">
        select
            DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
                     a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
                     c.CC_NUMBER,c.ALIAS_HEAD_PICTURE
        from
            T_USER a
                left join
            T_USER_DETAIL c on a.USER_ID = c.USER_ID
        where a.USER_ID IN (
            select distinct
                TBL.BELONGER_ID
            from T_BUSINESS_LEADS TBL
        )
          AND a.USER_ID >1
          AND a.USERNAME LIKE CONCAT('%',#{name},'%')
        ORDER BY a.USERNAME  ASC    LIMIT 100

    </select>

    <select id="findAllShareUser" resultType="com.vedeng.erp.system.dto.UserDto">
        select
            DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
                     a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
                     c.CC_NUMBER,c.ALIAS_HEAD_PICTURE
        from
            T_USER a
                left join
            T_USER_DETAIL c on a.USER_ID = c.USER_ID
        where a.USER_ID IN (
            select distinct
                TBL.SALE_USER_ID
            from T_R_SALES_J_BUSINESS_ORDER TBL
            WHERE IS_DELETED=0 AND BUSINESS_TYPE=5
        )
          AND a.USER_ID >1
          AND a.USERNAME LIKE CONCAT('%',#{name},'%')

        ORDER BY a.USERNAME  ASC    LIMIT 100
    </select>

    <select id="getLeadsListByDtoToday" resultType="com.vedeng.erp.business.domain.dto.BusinessLeadsDto">
        select
        <include refid="Base_Column_List"/>
        from T_BUSINESS_LEADS
        where 1 = 1


                <if test="phone != null and phone != ''">
                    and PHONE = #{phone,jdbcType=VARCHAR}
                </if>
                <if test="telephone != null and telephone != ''">
                    and TELEPHONE = #{telephone,jdbcType=VARCHAR}
                </if>
                AND ADD_TIME BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        LIMIT 1


    </select>


    <select id="getLeadsListByDto" resultType="com.vedeng.erp.business.domain.dto.BusinessLeadsDto">
        select
        <include refid="Base_Column_List"/>
        from T_BUSINESS_LEADS
        where ADD_TIME BETWEEN CONCAT(CURDATE(), ' 00:00:00') AND CONCAT(CURDATE(), ' 23:59:59')
        <choose>
            <when test="mergeFiled != null and mergeFiled != ''">
                and (
                            PHONE = #{mergeFiled,jdbcType=VARCHAR}
                        or
                            TELEPHONE = #{mergeFiled,jdbcType=VARCHAR}
                    )
            </when>
            <otherwise>
                <if test="phone != null and phone != ''">
                    and PHONE = #{phone,jdbcType=VARCHAR}
                </if>
                <if test="telephone != null and telephone != ''">
                    and TELEPHONE = #{telephone,jdbcType=VARCHAR}
                </if>
                <if test="id != null ">
                    AND ID NOT IN (#{id,jdbcType=INTEGER})
                </if>
                and MERGE_STATUS IN (0,2)
            </otherwise>


        </choose>
    </select>
    <select id="getCommunicateLeadsInfo" resultType="java.util.Map">
        SELECT C.BUSINESS_CLUES_ID AS RELATED_ID,
        C.BUSINESS_CLUES_NO AS ORDER_NO
        FROM T_BUSINESS_CLUES C
        WHERE C.BUSINESS_CLUES_ID IN
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2024-05-29-->
    <select id="getLeadMergeByParentLeadNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BUSINESS_LEADS
        where PARENT_LEADS_NO=#{leadNo,jdbcType=VARCHAR}
        order by ID DESC
    </select>

<!--auto generated by MybatisCodeHelper on 2024-05-29-->
    <select id="findWaitForMergeList" parameterType="com.vedeng.erp.leads.dto.MergeLeadsDto" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BUSINESS_LEADS
             where 1 = 1
             and PHONE=#{phone,jdbcType=VARCHAR}
             and ADD_TIME >= #{beginTime,jdbcType=TIMESTAMP}
             and ADD_TIME <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        order by ADD_TIME ASC
    </select>

<!--auto generated by MybatisCodeHelper on 2024-05-29-->
    <select id="findByLeadsNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BUSINESS_LEADS
        where LEADS_NO=#{leadsNo,jdbcType=VARCHAR}
    </select>

<!--auto generated by MybatisCodeHelper on 2024-05-29-->
    <select id="selectOneByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BUSINESS_LEADS
        <where>
            <if test="id != null">
                and ID=#{id,jdbcType=INTEGER}
            </if>
            <if test="leadsNo != null">
                and LEADS_NO=#{leadsNo,jdbcType=VARCHAR}
            </if>
            <if test="traderId != null">
                and TRADER_ID=#{traderId,jdbcType=INTEGER}
            </if>
            <if test="traderName != null">
                and TRADER_NAME=#{traderName,jdbcType=VARCHAR}
            </if>
            <if test="traderContactId != null">
                and TRADER_CONTACT_ID=#{traderContactId,jdbcType=INTEGER}
            </if>
            <if test="contact != null">
                and CONTACT=#{contact,jdbcType=VARCHAR}
            </if>
            <if test="phone != null">
                and PHONE=#{phone,jdbcType=VARCHAR}
            </if>
            <if test="telephone != null">
                and TELEPHONE=#{telephone,jdbcType=VARCHAR}
            </if>
            <if test="goodsInfo != null">
                and GOODS_INFO=#{goodsInfo,jdbcType=VARCHAR}
            </if>
            <if test="remark != null">
                and REMARK=#{remark,jdbcType=VARCHAR}
            </if>
            <if test="followStatus != null">
                and FOLLOW_STATUS=#{followStatus,jdbcType=INTEGER}
            </if>
            <if test="firstFollowTime != null">
                and FIRST_FOLLOW_TIME=#{firstFollowTime,jdbcType=TIMESTAMP}
            </if>
            <if test="followPic != null">
                and FOLLOW_PIC=#{followPic,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and `TYPE`=#{type,jdbcType=INTEGER}
            </if>
            <if test="status != null">
                and `STATUS`=#{status,jdbcType=INTEGER}
            </if>
            <if test="invalidReason != null">
                and INVALID_REASON=#{invalidReason,jdbcType=VARCHAR}
            </if>
            <if test="tagIds != null">
                and TAG_IDS=#{tagIds,jdbcType=VARCHAR}
            </if>
            <if test="belongerId != null">
                and BELONGER_ID=#{belongerId,jdbcType=INTEGER}
            </if>
            <if test="belonger != null">
                and BELONGER=#{belonger,jdbcType=VARCHAR}
            </if>
            <if test="provinceId != null">
                and PROVINCE_ID=#{provinceId,jdbcType=INTEGER}
            </if>
            <if test="cityId != null">
                and CITY_ID=#{cityId,jdbcType=INTEGER}
            </if>
            <if test="countyId != null">
                and COUNTY_ID=#{countyId,jdbcType=INTEGER}
            </if>
            <if test="province != null">
                and PROVINCE=#{province,jdbcType=VARCHAR}
            </if>
            <if test="city != null">
                and CITY=#{city,jdbcType=VARCHAR}
            </if>
            <if test="county != null">
                and COUNTY=#{county,jdbcType=VARCHAR}
            </if>
            <if test="address != null">
                and ADDRESS=#{address,jdbcType=VARCHAR}
            </if>
            <if test="turnBusinessChanceTime != null">
                and TURN_BUSINESS_CHANCE_TIME=#{turnBusinessChanceTime,jdbcType=TIMESTAMP}
            </if>
            <if test="closeTime != null">
                and CLOSE_TIME=#{closeTime,jdbcType=TIMESTAMP}
            </if>
            <if test="closeReason != null">
                and CLOSE_REASON=#{closeReason,jdbcType=VARCHAR}
            </if>
            <if test="assignTime != null">
                and ASSIGN_TIME=#{assignTime,jdbcType=TIMESTAMP}
            </if>
            <if test="businessChanceId != null">
                and BUSINESS_CHANCE_ID=#{businessChanceId,jdbcType=INTEGER}
            </if>
            <if test="addTime != null">
                and ADD_TIME=#{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modTime != null">
                and MOD_TIME=#{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                and CREATOR=#{creator,jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                and UPDATER=#{updater,jdbcType=INTEGER}
            </if>
            <if test="creatorName != null">
                and null=#{creatorName}
            </if>
            <if test="updaterName != null">
                and null=#{updaterName}
            </if>
        </where>
    </select>

    <select id="getLeadsListByParams" resultType="com.vedeng.erp.business.domain.dto.BusinessLeadsDto">
        select
        <include refid="Base_Column_List"/>
        from T_BUSINESS_LEADS
        <where>
        	<if test="businessChanceId != null and businessChanceId != 0">
              	AND BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER}
            </if>
            <if test="leadsNo != null and leadsNo != ''">
              	AND LEADS_NO = #{leadsNo,jdbcType=VARCHAR}
            </if>
            <if test="leadsNo != null and leadsNo != ''">
              	AND LEADS_NO = #{leadsNo,jdbcType=VARCHAR}
            </if>
            <if test="traderId != null and traderId != 0">
              	AND TRADER_ID = #{traderId,jdbcType=INTEGER}
            </if>
        </where>
    </select>
    
<!--auto generated by MybatisCodeHelper on 2024-07-29-->
    <select id="findByBusinessChanceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BUSINESS_LEADS
        where BUSINESS_CHANCE_ID=#{businessChanceId,jdbcType=INTEGER}
    </select>

    <select id="findAllBelongUserForLeads" resultType="com.vedeng.erp.system.dto.UserDto">
        select
        DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
        a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
        c.CC_NUMBER,c.ALIAS_HEAD_PICTURE
        from
        T_USER a
        left join
        T_USER_DETAIL c on a.USER_ID = c.USER_ID
        where a.USER_ID IN (
        select distinct
        TBC.BELONGER_ID
        from T_BUSINESS_LEADS TBC
        where
        BELONGER_ID IN
        <foreach collection="allSubordinateUserIdList" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        UNION ALL
        select distinct
        TBC.BELONGER_ID
        from T_BUSINESS_LEADS TBC JOIN T_R_SALES_J_BUSINESS_ORDER RELATE
        ON TBC.ID = RELATE.BUSINESS_ID AND RELATE.BUSINESS_TYPE=5
        WHERE
        RELATE.SALE_USER_ID = #{userId,jdbcType=INTEGER}
        )
        AND a.USER_ID >1
        <if test="name != null and name != ''">
            AND a.USERNAME LIKE CONCAT('%',#{name},'%')
        </if>
        ORDER BY a.USERNAME  ASC    LIMIT 100
    </select>

    <select id="findAllCreatorUserForLeads" resultType="com.vedeng.erp.system.dto.UserDto">
        select
        DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER,a.PARENT_ID,
        a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
        c.CC_NUMBER,c.ALIAS_HEAD_PICTURE
        from
        T_USER a
        left join
        T_USER_DETAIL c on a.USER_ID = c.USER_ID
        where a.USER_ID IN (
        select
            TBL.creator
        from T_BUSINESS_LEADS TBL

            <where>

                    (
                    <!-- 归属人 -->
                        EXISTS (
                        SELECT 1
                        FROM (
                        <foreach item="userId" index="index" collection="allSubordinateUserIdList" separator=" UNION ALL " >
                            SELECT #{userId,jdbcType=INTEGER} AS USER_ID
                        </foreach>
                        ) TEMP_USERS
                        WHERE TEMP_USERS.USER_ID = TBL.BELONGER_ID
                    )

                    <!-- 创建人 -->
                    <if test="allSubordinateUserIdList != null and allSubordinateUserIdList.size() > 0">
                        or EXISTS (
                        SELECT 1
                        FROM (
                        <foreach item="subordinateId" index="index" collection="allSubordinateUserIdList" separator="UNION ALL">
                            SELECT #{subordinateId,jdbcType=INTEGER} AS USER_ID
                        </foreach>
                        ) t
                        WHERE TBL.CREATOR = t.USER_ID
                        )
                    </if>


                    <!-- 修改协作人条件，包含所有三种类型的协作人 -->
                    OR EXISTS (
                        /* 1. 手动添加的协作人 */
                        SELECT 1 FROM T_R_SALES_J_BUSINESS_ORDER
                        WHERE BUSINESS_ID = TBL.ID
                        AND BUSINESS_TYPE = 5
                        AND IS_DELETED = 0
                        AND SALE_USER_ID = #{currentUserId,jdbcType=INTEGER}
                    )

                    /* 2. 线下销售作为协作人 - 根据归属销售(线上销售)查询对应的线下销售 */
                    OR EXISTS (
                        SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR
                        WHERE RUR.ONLINE_SALES_ID = TBL.BELONGER_ID
                        AND RUR.OFFLINE_SALES_ID = #{currentUserId,jdbcType=INTEGER}
                    )

                    /* 3. 产线负责人作为协作人 */
                    OR EXISTS(
                        SELECT 1
                        FROM ROLE_USER_CATEGORY_CONFIG RUC
                        WHERE RUC.USER_ID = #{currentUserId,jdbcType=INTEGER}
                        AND EXISTS(
                            SELECT 1
                            FROM T_BUSINESS_ORDER_CATEGORY TBOC
                            WHERE TBOC.BUSINESS_ID = TBL.ID
                            AND TBOC.BUSINESS_TYPE = 0
                            AND FIND_IN_SET(TBOC.CATEGORY_ID, RUC.CATEGORY_IDS) > 0
                        )
                    AND (
                        NOT EXISTS(
                            SELECT 1
                            FROM ROLE_USER_REGION_CONFIG RUR2
                            WHERE RUR2.PRODUCTION_USER_ID = RUC.USER_ID
                        )
                        OR EXISTS(
                            SELECT 1
                            FROM ROLE_USER_REGION_CONFIG RUR3
                            WHERE RUR3.PRODUCTION_USER_ID = RUC.USER_ID
                            AND RUR3.ONLINE_SALES_ID = TBL.BELONGER_ID
                        )
                    )
                    )
                    )

            </where>
        )
        AND a.USER_ID >1
        <if test="name != null and name != ''">
            AND a.USERNAME LIKE CONCAT('%',#{name},'%')
        </if>
        ORDER BY a.USERNAME  ASC    LIMIT 100
    </select>
</mapper>