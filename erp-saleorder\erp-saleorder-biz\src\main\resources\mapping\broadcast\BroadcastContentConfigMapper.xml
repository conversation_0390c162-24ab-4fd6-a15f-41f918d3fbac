<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.broadcast.mapdao.BroadcastContentConfigMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="PIC_URL" jdbcType="VARCHAR" property="picUrl"/>
        <result column="PIC_NAME" jdbcType="VARCHAR" property="picName"/>
        <result column="EXCLUSIVE_TYPE" jdbcType="TINYINT" property="exclusiveType"/>
        <result column="EXCLUSIVE_TARGET_VALUES" jdbcType="VARCHAR" property="exclusiveTargetValues"/>
        <result column="EXCLUSIVE_TARGET_LABELS" jdbcType="VARCHAR" property="exclusiveTargetLabels"/>
        <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, PIC_URL, PIC_NAME, EXCLUSIVE_TYPE, EXCLUSIVE_TARGET_VALUES, EXCLUSIVE_TARGET_LABELS,
        IS_DELETED, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_CONTENT_CONFIG
        where ID = #{id,jdbcType=INTEGER}
        and IS_DELETED = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update T_BROADCAST_CONTENT_CONFIG
        set IS_DELETED = 1, MOD_TIME = now()
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_CONTENT_CONFIG (PIC_URL, PIC_NAME, EXCLUSIVE_TYPE,
        EXCLUSIVE_TARGET_VALUES, EXCLUSIVE_TARGET_LABELS,
        IS_DELETED, ADD_TIME, MOD_TIME,
        CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME)
        values (#{picUrl,jdbcType=VARCHAR}, #{picName,jdbcType=VARCHAR}, #{exclusiveType,jdbcType=TINYINT},
        #{exclusiveTargetValues,jdbcType=VARCHAR}, #{exclusiveTargetLabels,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR})
    </insert>

    <insert id="insertSelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_CONTENT_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="picUrl != null">
                PIC_URL,
            </if>
            <if test="picName != null">
                PIC_NAME,
            </if>
            <if test="exclusiveType != null">
                EXCLUSIVE_TYPE,
            </if>
            <if test="exclusiveTargetValues != null">
                EXCLUSIVE_TARGET_VALUES,
            </if>
            <if test="exclusiveTargetLabels != null">
                EXCLUSIVE_TARGET_LABELS,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="creatorName != null">
                CREATOR_NAME,
            </if>
            <if test="updaterName != null">
                UPDATER_NAME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="picUrl != null">
                #{picUrl,jdbcType=VARCHAR},
            </if>
            <if test="picName != null">
                #{picName,jdbcType=VARCHAR},
            </if>
            <if test="exclusiveType != null">
                #{exclusiveType,jdbcType=TINYINT},
            </if>
            <if test="exclusiveTargetValues != null">
                #{exclusiveTargetValues,jdbcType=VARCHAR},
            </if>
            <if test="exclusiveTargetLabels != null">
                #{exclusiveTargetLabels,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null">
                #{updaterName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity">
        update T_BROADCAST_CONTENT_CONFIG
        <set>
            <if test="picUrl != null">
                PIC_URL = #{picUrl,jdbcType=VARCHAR},
            </if>
            <if test="picName != null">
                PIC_NAME = #{picName,jdbcType=VARCHAR},
            </if>
            <if test="exclusiveType != null">
                EXCLUSIVE_TYPE = #{exclusiveType,jdbcType=TINYINT},
            </if>
            <if test="exclusiveTargetValues != null">
                EXCLUSIVE_TARGET_VALUES = #{exclusiveTargetValues,jdbcType=VARCHAR},
            </if>
            <if test="exclusiveTargetLabels != null">
                EXCLUSIVE_TARGET_LABELS = #{exclusiveTargetLabels,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity">
        update T_BROADCAST_CONTENT_CONFIG
        set PIC_URL = #{picUrl,jdbcType=VARCHAR},
        PIC_NAME = #{picName,jdbcType=VARCHAR},
        EXCLUSIVE_TYPE = #{exclusiveType,jdbcType=TINYINT},
        EXCLUSIVE_TARGET_VALUES = #{exclusiveTargetValues,jdbcType=VARCHAR},
        EXCLUSIVE_TARGET_LABELS = #{exclusiveTargetLabels,jdbcType=VARCHAR},
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER},
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <!-- 分页查询播报内容配置 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_CONTENT_CONFIG
        where IS_DELETED = 0
        <if test="picName != null and picName != ''">
            and PIC_NAME like concat('%', #{picName}, '%')
        </if>
        <if test="exclusiveType != null">
            and EXCLUSIVE_TYPE = #{exclusiveType}
        </if>
        <if test="exclusiveTargetValues != null and exclusiveTargetValues != ''">
            and EXCLUSIVE_TARGET_VALUES like concat('%', #{exclusiveTargetValues}, '%')
        </if>
        <if test="exclusiveTargetLabels != null and exclusiveTargetLabels != ''">
            and EXCLUSIVE_TARGET_LABELS like concat('%', #{exclusiveTargetLabels}, '%')
        </if>
        <if test="creatorList != null and creatorList.size() > 0">
            and CREATOR in
            <foreach collection="creatorList" item="creator" open="(" close=")" separator=",">
                #{creator}
            </foreach>
        </if>
        <if test="startAddTime != null and startAddTime != ''">
            and ADD_TIME >= #{startAddTime}
        </if>
        <if test="endAddTime != null and endAddTime != ''">
            and ADD_TIME &lt;= #{endAddTime}
        </if>
    </select>

    <!-- 批量删除（逻辑删除） -->
    <update id="batchDelete">
        update T_BROADCAST_CONTENT_CONFIG
        set IS_DELETED = 1, MOD_TIME = now(), UPDATER = #{updater}
        where ID in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

</mapper>
