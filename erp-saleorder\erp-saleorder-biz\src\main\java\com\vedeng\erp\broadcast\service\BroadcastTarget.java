package com.vedeng.erp.broadcast.service;


import java.util.List;

import com.vedeng.erp.common.broadcast.MessageSubjectEnum;
import com.vedeng.erp.common.broadcast.config.GlobalConfig;
import com.vedeng.erp.common.broadcast.param.TargetOrgAndUser;

/**
 * 播报目标接口
 * @ClassName:  BroadcastTarget   
 * @author: <PERSON>.yang
 * @date:   2025年6月6日 下午3:54:28    
 * @Copyright:
 */
public interface BroadcastTarget {
	
	/**
	 * 播报目标中，要发送的消息类型，例如播报大群需要播报，个人最佳、小组最佳、部门最佳等，华北大区只播报个人最佳
	 * @param integer 
	 * @return
	 */
	List<MessageSubjectEnum> getMessageSubjectList(Integer deptId);
	
    /**
     * 按部门分组，获取所有erp组织ID,只有大群使用
     * @return
     */
    List<TargetOrgAndUser> getOrgIdByDept(Integer deptId);

    /**
     * 按小组分组，获取所有erp组织ID,针对个人在大群和大区中数据,大群和大区使用
     * @param deptId 
     * @return
     */
    List<TargetOrgAndUser> getOrgIdBySingle(Integer deptId);
    
    /**
     * 按小组进行区分,只有大群使用
     * @param deptId
     * @return
     */
    List<TargetOrgAndUser> getOrgIdByTeam(Integer deptId);

    /**
     * 根据配置信息获取，企微发送的webHook地址
     * @param configs
     * @return
     */
	String getWebHook(GlobalConfig configs,Integer deptId);
	
	/**
	 * 获取播报梯度
	 * @param configs
	 * @param deptId
	 * @return
	 */
	Integer getAmountStep(GlobalConfig configs,Integer deptId);

	
}
