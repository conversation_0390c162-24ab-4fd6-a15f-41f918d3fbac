package com.vedeng.crm.visitrecord.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vdurmont.emoji.EmojiParser;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.business.business.service.CrmBusinessChanceService;
import com.vedeng.crm.business.quote.domain.dto.SendMessageDto;
import com.vedeng.crm.common.enums.TaskTypeEnum;
import com.vedeng.crm.common.service.JumpService;
import com.vedeng.crm.common.util.CrmEmojiUtils;
import com.vedeng.crm.follow.service.FollowUpRecordService;
import com.vedeng.crm.task.domain.dto.TaskDto;
import com.vedeng.crm.task.domain.dto.TaskHandleDto;
import com.vedeng.crm.task.domain.dto.TaskQueryDto;
import com.vedeng.crm.task.domain.vo.MyTaskVo;
import com.vedeng.crm.task.service.TaskService;
import com.vedeng.crm.visitrecord.domain.dto.*;
import com.vedeng.crm.visitrecord.domain.entity.CrmVisitRecordEntity;
import com.vedeng.crm.visitrecord.domain.vo.*;
import com.vedeng.crm.visitrecord.enums.VisitOperationTypeEnum;
import com.vedeng.crm.visitrecord.mapper.*;
import com.vedeng.crm.visitrecord.service.*;
import com.vedeng.erp.business.common.enums.BusinessChanceStageEnum;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.RSalesJTraderDto;
import com.vedeng.erp.trader.dto.TraderUserDto;
import com.vedeng.erp.trader.dto.VisitConfigDto;
import com.vedeng.erp.trader.feign.OneDataTraderTagApi;
import com.vedeng.erp.trader.mapper.WebAccountMapper;
import com.vedeng.erp.trader.service.RSalesJTraderApiService;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc;
import com.vedeng.infrastructure.tyc.mapper.TycMapper;
import com.vedeng.onedataapi.api.tradertag.req.TraderTagApiReq;
import com.vedeng.onedataapi.api.tradertag.res.TraderDecisionTag;
import com.vedeng.onedataapi.api.tradertag.res.TraderTransactionTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CrmVisitRecordServiceImpl implements CrmVisitRecordService {

    @Value("${mobile.visitConfigList:[]}")
    private String visitConfigList; //拜访计划类型

    @Value("${lxcrmUrl}")
    protected String lxcrmUrl;

    @Autowired
    protected JumpService jumpService;

    @Autowired
    private OneDataTraderTagApi oneDataTraderTagApi;

    @Autowired
    private BusinessLeadsService businessLeadsService;

    @Autowired
    private BusinessChanceService businessChanceService;


    @Autowired
    private CrmVisitRecordMapper visitRecordMapper;

    @Autowired
    private CrmCommunicateRecordMapper communicateRecordMapper;

    @Autowired
    private VisitTongxingUserService visitTongxingUserService;

    @Autowired
    private VisitRecordCardService visitRecordCardService;

    @Autowired
    private VisitTongxingUserMapper visitTongxingUserMapper;

    @Autowired
    @Qualifier("newWebAccountMapper")
    private WebAccountMapper webAccountMapper;


    @Autowired
    private CrmVisitRecordLogService crmVisitRecordLogService;

    @Autowired
    private FollowUpRecordService followUpRecordService;

    @Autowired
    private RSalesJTraderApiService rSalesJTraderApiService;

    @Autowired
    private TaskService taskService;

    @Value("${accessAllVisitUserIds:1}")
    private String accessAllVisitUserIds;

    @Autowired
    @Qualifier(value = "CrmVisitCreateMessageServiceImpl")
    private CrmVisitMessageService crmVisitCreateMessageServiceImpl;

    @Autowired
    @Qualifier(value = "CrmVisitModifyMessageServiceImpl")
    private CrmVisitMessageService crmVisitModifyMessageServiceImpl;

    @Value("${crmApplicationMessageJumpUrl}")
    protected String crmApplicationMessageJumpUrl;  //qa.lxcrm.vedeng.com  一定是公网地址，需要在企业微信中打开的

    @Autowired
    @Qualifier(value ="CrmVisitSubmitRecordMessageServiceImpl" )
    private CrmVisitSubmitRecordMessageServiceImpl crmVisitSubmitRecordMessageServiceImpl;

    @Autowired
    @Qualifier(value = "CrmVisitNotCardMessageServiceImpl")
    private  CrmVisitNotCardMessageServiceImpl crmVisitNotCardMessageServiceImpl;

    @Autowired
    @Qualifier(value = "CrmVisitNotRecordMessageServiceImpl")
    private CrmVisitNotRecordMessageServiceImpl crmVisitNotRecordMessageServiceImpl;

    @Autowired
    private CrmVisitRecordMapper crmVisitRecordMapper;
    @Autowired
    private VisitRecordCardMapper visitRecordCardMapper;


    @Override
    public  CrmVisitRecordEntity selectByPrimaryKey(Integer id ){
        return visitRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public  List<VisitRecordVo> selectVisitRecordByRelateNo(String relateNo, Integer relateType){
        return visitRecordMapper.selectVisitRecordByRelateNo(relateNo,relateType);
    }
    @Override
    public  List<VisitRecordVo> selectVisitRecordByRelateId(Integer relateId, Integer relateType){
        return visitRecordMapper.selectVisitRecordByRelateId(relateId,relateType);
    }


    @Override
    public PageInfo<VisitRecordVo> visitRecordPage(PageParam<VisitRecordQueryDto> pageParam) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        VisitRecordQueryDto visitRecordQueryDto = pageParam.getParam();
        visitRecordQueryDto.setCurrentUserId(currentUser.getId());
        PageHelper.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        if(StringUtils.isNotBlank(pageParam.getOrderBy())){
            visitRecordQueryDto.setOrderBy(pageParam.getOrderBy());
        }else{
            visitRecordQueryDto.setOrderBy("ADD_TIME DESC");//默认按创建时间的倒序
        }
        getQueryParams(visitRecordQueryDto);
        List<VisitRecordVo> list = visitRecordMapper.selectVisitRecordList(visitRecordQueryDto);
        if(CollectionUtils.isNotEmpty(list)){
            for(VisitRecordVo visitRecordVo:list){
                dealTargetNameStr(visitRecordVo);

                VisitCustomerVo visitCustomerVo = getVisitCustomerForVisitSimple(visitRecordVo.getTraderId(),visitRecordVo.getTraderCustomerId(),visitRecordVo.getCustomerName(),visitRecordVo.getTycFlag(),visitRecordVo.getCustomerGrade());
                visitRecordVo.setVisitCustomerVo(visitCustomerVo);
            }
        }
        return new PageInfo<>(list);
    }

    @Override
    public  VisitUserDetailForPageVo pageDetail(CurrentUser currentUser){

        VisitRecordQueryDto visitRecordQueryDto = new VisitRecordQueryDto();
        visitRecordQueryDto.setCurrentUserId(currentUser.getId());
        getQueryParams(visitRecordQueryDto);
        VisitUserDetailForPageVo visitUserDetailForPageVo   =   new VisitUserDetailForPageVo();
        //分别查询这三个人员
        List<UserForVisitDto> createUserList = visitRecordMapper.selectVisitRecordCreateUserList(visitRecordQueryDto);
        List<UserForVisitDto> visitorUserList = visitRecordMapper.selectVisitRecordVisitUserList(visitRecordQueryDto);
        List<UserForVisitDto> tongxingUserList = visitRecordMapper.selectVisitRecordTongXingUserList(visitRecordQueryDto);
        //将查询到的人员信息封装成vo对象，返回给页面
        visitUserDetailForPageVo.setCreateUserList(createUserList);
        visitUserDetailForPageVo.setVisitUserList(visitorUserList);
        visitUserDetailForPageVo.setTongXingUserList(tongxingUserList);
        return  visitUserDetailForPageVo;

    }

    private boolean isAccessAll(CurrentUser currentUser){

        List<Integer> accessAll = Arrays.stream(accessAllVisitUserIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        if(accessAll.contains(currentUser.getId())){
            return true;
        }
        return false;
    }



    private void getQueryParams(VisitRecordQueryDto visitRecordQueryDto){


        List<Integer> accessAll = Arrays.stream(accessAllVisitUserIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(accessAll) && accessAll.contains(visitRecordQueryDto.getCurrentUserId())){//账号是特殊权限,能看到所有人
            visitRecordQueryDto.setSeeAll(1);
        }else{
            List<UserDto> userDtoList = userApiService.queryUserSubFromUac(visitRecordQueryDto.getCurrentUserId());
            visitRecordQueryDto.setSeeAll(0);//不看所有人,只看自己及下属
            List<Integer> allSubordinateUserIdList = userDtoList.stream().map(UserDto::getUserId).collect(Collectors.toList());
            visitRecordQueryDto.setAllSubordinateUserIdList(allSubordinateUserIdList);
        }





    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VisitRecordVo saveNextVisitRecord(CurrentUser currentUser, VisitRecordInputDto inputDto){
        VisitRecordInputDto newInputDto = new VisitRecordInputDto();
        BeanUtils.copyProperties(inputDto, newInputDto);
        newInputDto.setId(null);
        VisitRecordVo visitRecordVo = saveOrUpdate(currentUser, inputDto);//创建一个新的拜访计划
        crmVisitRecordLogService.addOperationLog(inputDto.getPreId(), VisitOperationTypeEnum.CREATE_NEXT,"创建了下次拜访计划，编号："+visitRecordVo.getVisitRecordNo(), currentUser); //新增一条拜访计划的日志
        return visitRecordVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBusinessChanceForVisit(VisitRecordInputDto.RelateBizData relateBizData,Integer visitId,CurrentUser currentUser){
        VisitRecordVo record = new VisitRecordVo();
        record.setId(visitId);

        record.setRelateType(relateBizData.getRelateType());
        record.setBussinessChanceId(relateBizData.getBizId());
        record.setBussinessChanceNo(relateBizData.getBizNo());
        record.setModUserId(currentUser.getId());
        record.setModTime(new Date());
        visitRecordMapper.update(record);
        crmVisitRecordLogService.addOperationLog(visitId, VisitOperationTypeEnum.RELATE_BIZ, "关联了商机，商机编号："+relateBizData.getBizNo(), currentUser);
    }

    @Value(value="${jobNumberList:[]}")
    private String jobNumberList;

    public String getNowJobNumber(String wxJobNumberFromOld) {
        Map<String,String> wxToWoQujobNumberMap = new HashMap<>();
        if(org.apache.commons.lang3.StringUtils.isNotBlank(jobNumberList)){
            JSONArray jsonArray = JSONArray.parseArray(jobNumberList);
            for(int i=0;i<jsonArray.size();i++){
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String woquJobNumber = jsonObject.getString("woquJobNumber");
                String wxJobNumber = jsonObject.getString("wxJobNumber");//qy01fe81290eeb0db09c3194f895
                wxToWoQujobNumberMap.put(wxJobNumber,woquJobNumber);//qy为
            }
        }
        String nowJobNumber = wxToWoQujobNumberMap.get(wxJobNumberFromOld);
        if(StringUtils.isNotBlank(nowJobNumber)){
            return nowJobNumber;
        }
        return wxJobNumberFromOld;

    }


    @Override
    public void dealVisitInputDto(VisitRecordInputDto inputDto){
        String visitorJobNumber = inputDto.getVisitorJobNumber();
        UserDto userDto = userApiService.getUserBaseInfoByJobNumber(visitorJobNumber);
        if(userDto==null){
            throw new ServiceException("人员信息不完整");
        }
        inputDto.setVisitorId(userDto.getUserId());
        if(CollectionUtil.isNotEmpty(inputDto.getTongxingJobNumbers() )){
            List<Integer> tongxingIds = new ArrayList<>();
            for(String jobNumber:inputDto.getTongxingJobNumbers()){
                String currentJobNumber = getNowJobNumber(jobNumber);
                UserDto tongxing = userApiService.getUserBaseInfoByJobNumber(currentJobNumber);
                if(tongxing==null){
                    continue;
                }
                tongxingIds.add(tongxing.getUserId());
            }
            inputDto.setTongxingIds(tongxingIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VisitRecordVo saveOrUpdate(CurrentUser currentUser, VisitRecordInputDto inputDto) {

        String remark = CrmEmojiUtils.removeAllEmojis(inputDto.getRemark());//表情符替换
        inputDto.setRemark(remark);

        String otherContact = CrmEmojiUtils.removeAllEmojis(inputDto.getOtherContact());//表情符替换
        inputDto.setOtherContact(otherContact);

        CrmVisitRecordEntity oldVisitRecordVo = null;
        VisitRecordVo record = new VisitRecordVo();
        String visitTargetStr =  dealTargetNameStr(inputDto.getVisitTarget());
        inputDto.setVisitTargetStr(visitTargetStr);

        inputDto.setCustomerFrom(inputDto.getTraderId()!=null?1:3);

        BeanUtils.copyProperties(inputDto, record);
        // 设置商机相关信息
        if (inputDto.getRelateBizData() != null) {
            record.setRelateType(inputDto.getRelateBizData().getRelateType());
            record.setBussinessChanceId(inputDto.getRelateBizData().getBizId());
            record.setBussinessChanceNo(inputDto.getRelateBizData().getBizNo());
        }

        // 设置当前用户信息
        record.setModUserId(currentUser.getId());
        record.setModTime(new Date());
        record.setVisitSuccess("N");
        record.setOrgId(0);
        record.setOrgGroup("");

        TaskDto taskDto = new TaskDto();
        taskDto.setMainTaskType(ErpConstant.SEVEN);
        taskDto.setCommitTime(new Date());
        taskDto.setDeadline(DateUtil.endOfDay(record.getPlanVisitDate()));
        List<Integer> visitUserIds = new ArrayList<>();
        visitUserIds.addAll(CollectionUtil.isEmpty(inputDto.getTongxingIds())?new ArrayList<>()
                :inputDto.getTongxingIds());
        visitUserIds.add(record.getVisitorId());
        taskDto.setTodoUserList(visitUserIds);

        if (inputDto.getId() == null) {
            String visitRecordNo = new BillNumGenerator().distribution(new BillGeneratorBean(BillType.VISIT_RECORD));
            record.setVisitRecordNo(visitRecordNo);
            record.setAddUserId(currentUser.getId());
            record.setAddTime(new Date());
            // 新增
            record.setVisitRecordStatus(1); // 待拜访状态
            visitRecordMapper.insert(record);
            crmVisitRecordLogService.addOperationLog(record.getId(), VisitOperationTypeEnum.CREATE, "创建了拜访计划", currentUser);
            String visitTargetStrFormart = inputDto.getVisitTargetStr().replaceAll("，","、");
            log.info("CRM-拜访计划，添加待办任务，拜访计划编号:{}", visitRecordNo);
            String format = String.format(visitTargetStrFormart+(StringUtils.isNotBlank(inputDto.getRemark())?("，"+inputDto.getRemark()):""));
            taskDto.setTaskContent(format);
            taskDto.setBizNo(visitRecordNo);
            taskDto.setBizType(Integer.parseInt(TaskTypeEnum.VISIT_FOLLOW.getValue()));
            taskDto.setBizId(record.getId());

            taskService.save(taskDto);


            inputDto.setVisitRecordNo(visitRecordNo);
            crmVisitCreateMessageServiceImpl.sendMessage(inputDto,record.getId(),inputDto.getTongxingIds(),currentUser);


        } else {
            oldVisitRecordVo = visitRecordMapper.selectByPrimaryKey(inputDto.getId());
            if(oldVisitRecordVo!=null && oldVisitRecordVo.getVisitRecordStatus() !=null && oldVisitRecordVo.getVisitRecordStatus()>1){
                throw new ServiceException("拜访计划状态已变化，请刷新页面后重试");
            }
            if("Y".equals(record.getNoContract())){
//                /** 拜访内容-联系人信息-姓名 */
//                private String contactName;
//
//                /** 拜访内容-联系人信息-手机 */
//                private String contactMobile;
//
//                /** 拜访内容-联系人信息-电话 */
//                private String contactTele;
//
//                /** 拜访内容-联系人信息-职位 */
//                private String contactPosition;
//                /** 其它联系方式 */
//                private String otherContact;
                //将以上字段赋空

                record.setContactName("");
                record.setContactMobile("");
                record.setContactTele("");
                record.setContactPosition("");
                record.setOtherContact("");
            }



            // 更新
            visitRecordMapper.update(record);
            crmVisitRecordLogService.addOperationLog(record.getId(), VisitOperationTypeEnum.EDIT, "编辑了拜访计划", currentUser);


            String visitRecordNo = oldVisitRecordVo.getVisitRecordNo();

            String visitTargetStrFormart = inputDto.getVisitTargetStr().replaceAll("，","、");
            log.info("CRM-拜访计划，添加待办任务，拜访计划编号:{}", visitRecordNo);
            String format = String.format(visitTargetStrFormart+(StringUtils.isNotBlank(inputDto.getRemark())?("，"+inputDto.getRemark()):""));
            taskDto.setTaskContent(format);
            taskDto.setBizNo(visitRecordNo);
            taskDto.setBizId(record.getId());
            taskDto.setBizType(Integer.parseInt(TaskTypeEnum.VISIT_FOLLOW.getValue()));
            taskService.updateTaskForVisit(taskDto);

            List<Integer> userIdList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(inputDto.getTongxingIds())){
                userIdList.addAll(inputDto.getTongxingIds());
            }
            userIdList.add(oldVisitRecordVo.getAddUserId());//添加修改前的创建人
            userIdList.add(oldVisitRecordVo.getVisitorId());//添加修改前的拜访人
            userIdList.add(inputDto.getVisitorId());//当前的拜访人
            List<VisitTongxingUserVo> existingTongxingUsers = visitTongxingUserMapper.selectByVisitRecordId(inputDto.getId());
            Set<Integer> existingUserIds = existingTongxingUsers.stream()
                    .map(VisitTongxingUserVo::getTongxingUserId)
                    .collect(Collectors.toSet());
            userIdList.addAll(existingUserIds);//添加当前已存在的同行人

            inputDto.setVisitRecordNo(oldVisitRecordVo.getVisitRecordNo());
            //推送消息
            crmVisitModifyMessageServiceImpl.sendMessage(inputDto,inputDto.getId(),userIdList,currentUser);

        }
        if (inputDto.getRelateBizData() != null &&
                (oldVisitRecordVo ==null || !inputDto.getRelateBizData().getBizNo().equals(oldVisitRecordVo.getBussinessChanceNo()))) {
            //如果是新增拜访计划，或者修改了关联的单号。两种情况均满足添加日志的情况
            //① 单据类型=商机，文案为“关联了商机，商机编号：X”
            //② 单据类型=线索，文案为“关联了线索，线索编号：X”
            String logContent = "关联了"+(inputDto.getRelateBizData().getRelateType().equals(1)?"线索，线索编号：":"商机，商机编号：")+inputDto.getRelateBizData().getBizNo();
            crmVisitRecordLogService.addOperationLog(record.getId(), VisitOperationTypeEnum.RELATE_BIZ,logContent, currentUser);
        }

        //处理同行人信息
        handleTongxingUsers(record.getId(),inputDto.getTongxingIds(),currentUser.getId());
        return record;


    }

    /**
     * 处理同行人信息
     * @param recordId 拜访记录ID
     * @param newTongxingIds 新的同行人ID列表
     * @param currentUserId 当前用户ID
     */
    private void handleTongxingUsers(Integer recordId, List<Integer> newTongxingIds, Integer currentUserId) {
        // 如果新的同行人列表为空，则删除所有同行人
        if (newTongxingIds == null || newTongxingIds.isEmpty()) {
            if(recordId != null){
                visitTongxingUserMapper.deleteByRecordId(recordId);
            }
            return ;
        }

        // 获取现有同行人列表
        List<VisitTongxingUserVo> existingTongxingUsers = visitTongxingUserMapper.selectByVisitRecordId(recordId);
        Set<Integer> existingUserIds = existingTongxingUsers.stream()
                .map(VisitTongxingUserVo::getTongxingUserId)
                .collect(Collectors.toSet());

        // 找出需要新增的同行人
        List<VisitTongxingUserVo> toInsert = newTongxingIds.stream()
                .filter(id -> !existingUserIds.contains(id))
                .map(id -> {
                    VisitTongxingUserVo vo = new VisitTongxingUserVo();
                    vo.setRecordId(recordId);
                    vo.setTongxingUserId(id);
                    vo.setAddUserId(currentUserId);
                    return vo;
                })
                .collect(Collectors.toList());

        // 批量插入新增的同行人
        if (!toInsert.isEmpty()) {
            visitTongxingUserMapper.batchInsert(toInsert);
        }

        // 删除不在新列表中的同行人
        visitTongxingUserMapper.deleteNotInList(recordId, newTongxingIds);
    }

    @Autowired
    private UserApiService userApiService;


    /**
     * 拜访计划的详情-按钮点击展示对象
     * @param visitRecordVo 拜访计划
     * @param currentUser 当前人
     * @return
     */
    private VisitDetailButtonDto getVisitDetailButtonDto(VisitRecordVo visitRecordVo,CurrentUser currentUser) {

        List<UserDto> userDtoList = userApiService.queryUserSubFromUac(currentUser.getId());
        List<Integer> allSubUserIdsList = userDtoList.stream().map(UserDto::getUserId).collect(Collectors.toList());

        //
        //1、创建下次拜访------------------------------------------------------
        boolean createNextVisitBtn = true;

        //2、编辑计划------------------------------------------------------
        boolean editPlanBtn =false;
        Integer createId = visitRecordVo.getAddUserId();
        Integer visitId = visitRecordVo.getVisitorId();
        if( visitRecordVo.getVisitRecordStatus().equals(1) &&
                (allSubUserIdsList.contains(createId) || allSubUserIdsList.contains(visitId))){
            editPlanBtn = true;//创建人+创建人上级 +拜访人+拜访人上级
        }
        //3、添加拜访记录------------------------------------------------------
        boolean addVisitRecordBtn =false;
        //拜访计划的状态：1.待拜访；2.拜访中；3.已拜访；4.已关闭
        if(visitId!=null && visitId.equals(currentUser.getId()) //拜访人
                && (visitRecordVo.getVisitRecordStatus().equals(1) || visitRecordVo.getVisitRecordStatus().equals(2))//且状态是待拜访或拜访中
            ){
            addVisitRecordBtn = true;
        }

        //4、添加沟通记录------------------------------------------------------
        boolean addCommuncateRecordBtn=false;
        //取同行人集合
        Set<Integer> existingUserIds = CollectionUtil.isEmpty(visitRecordVo.getTongxingUserList())?new HashSet<>():
                visitRecordVo.getTongxingUserList().stream()
                .map(VisitTongxingUserVo::getTongxingUserId)
                .collect(Collectors.toSet());
        if(visitRecordVo.getVisitRecordStatus().equals(3)//已拜访
                && (visitId.equals(currentUser.getId()) || existingUserIds.contains(currentUser.getId()))
            ){
            addCommuncateRecordBtn = true;
        }


        //5、创建商机------------------------------------------------------
        boolean createBusinessChanceBtn = false;
        if( (visitRecordVo.getRelateType() == null || visitRecordVo.getRelateType()==0)
                && visitRecordVo.getVisitRecordStatus().equals(3)//已拜访
                && (    // 创建人+创建人上级 +拜访人+拜访人上级
                        currentUser.getId().equals(createId)
                        || allSubUserIdsList.contains(createId)
                        || currentUser.getId().equals(visitId)
                        || allSubUserIdsList.contains(visitId)
                    )
        ){
            createBusinessChanceBtn = true;
        }

        //6、打卡
        boolean addCardBtn = false;
        //7、编辑打卡
        boolean editCardBtn = false;

        if( (visitRecordVo.getVisitRecordStatus().equals(1) || visitRecordVo.getVisitRecordStatus().equals(2))//待拜访或拜访中
            && ( currentUser.getId().equals(visitId) || existingUserIds.contains(currentUser.getId()))//创建人或同行人
        ){
            boolean currentUserCard = false;
            //以上条件满足按钮其一显示条件
            for(VisitRecordCardVo visitRecordCardVo:visitRecordVo.getCardList()){
                if(visitRecordCardVo.getVisitUserId().equals(currentUser.getId()) && visitRecordCardVo.getCardTime()  != null){
                    currentUserCard = true;
                    break;
                }
            }
            if(currentUserCard){//当前人打过卡了，即为编辑打卡
                editCardBtn = true;
            }else{
                addCardBtn = true;
            }
        }



        //8、关闭
        boolean closeVisitBtn = false;
        if( (visitRecordVo.getVisitRecordStatus().equals(1) || visitRecordVo.getVisitRecordStatus().equals(2))//待拜访或拜访中
                && (    // 创建人+创建人上级 +拜访人+拜访人上级
                currentUser.getId().equals(createId)
                        || allSubUserIdsList.contains(createId)
                        || currentUser.getId().equals(visitId)
                        || allSubUserIdsList.contains(visitId)
        )
        ){
            closeVisitBtn = true;
        }



        VisitDetailButtonDto visitDetailButtonDto = VisitDetailButtonDto.builder().createNextVisitBtn(createNextVisitBtn)
                .editPlanBtn(editPlanBtn)
                .addVisitRecordBtn(addVisitRecordBtn)
                .addCommuncateRecordBtn(addCommuncateRecordBtn)
                .createBusinessChanceBtn(createBusinessChanceBtn)
                .addCardBtn(addCardBtn)
                .editCardBtn(editCardBtn)
                .closeVisitBtn(closeVisitBtn).build();
        return visitDetailButtonDto;
    }

    @Override
    public boolean checkVisitRecordVisable(VisitRecordVo visitRecordVo,CurrentUser currentUser){
        //检查
        if(visitRecordVo == null){
            log.error("拜访记录对象为空");
            return false;
        }
        //首先先判断是不是大权限的人
        boolean isAccessAll = isAccessAll(currentUser);
        if(isAccessAll){
            return true;
        }
        List<Integer> allUser = new ArrayList<>();
        //1、创建人
        if(visitRecordVo.getAddUserId()!=null){
            allUser.add(visitRecordVo.getAddUserId());
        }
        //2、拜访人
        if(visitRecordVo.getVisitorId()!=null){
            allUser.add(visitRecordVo.getVisitorId());
        }
        //3、同行人
        if(CollectionUtil.isNotEmpty(visitRecordVo.getTongxingUserList())){
            allUser.addAll(visitRecordVo.getTongxingUserList().stream().map(VisitTongxingUserVo::getTongxingUserId).collect(Collectors.toList()));
        }
        //4、再添加归属销售
        if(visitRecordVo.getTraderId()!=null && visitRecordVo.getVisitCustomerVo() !=null
            && visitRecordVo.getVisitCustomerVo().getBelongerId()!=null){
            allUser.add(visitRecordVo.getVisitCustomerVo().getBelongerId());
        }
        if(allUser.contains(currentUser.getId())){//当前用户不在创建人+拜访人+同行人集合中，则不显示
            return true;
        }

        //获取这个人的所有下属，如果下属都没有，直接不允许查看
        List<UserDto> userDtoList = userApiService.queryUserSubFromUac(currentUser.getId());
        if(CollectionUtils.isEmpty(userDtoList)){
            return false;
        }

        List<Integer> allSubUserIdsList = userDtoList.stream().map(UserDto::getUserId).collect(Collectors.toList());
        //判断allSubUserIdsList是否有其中一个在allUser中
        if(allSubUserIdsList.stream().anyMatch(allUser::contains)){
            return true;
        }
        return false;
    }



    @Override
    public VisitRecordVo detail(Integer id,CurrentUser currentUser) {
        return detail(id, currentUser, true);

    }

    @Override
    public VisitRecordVo detail(Integer id,CurrentUser currentUser,boolean checkPermission){


        // 获取拜访记录基本信息

        CrmVisitRecordEntity crmVisitRecordEntity = visitRecordMapper.selectByPrimaryKey(id);


        if(crmVisitRecordEntity ==null){
            log.error("拜访计划不存在:"+id);
            return null;
        }

        VisitRecordVo visitRecordVo = new VisitRecordVo();
        BeanUtils.copyProperties(crmVisitRecordEntity,visitRecordVo);

        visitRecordVo.setId(id);
        UserDto userDto = userApiService.getUserBaseInfo(visitRecordVo.getVisitorId());
        visitRecordVo.setVisitorPic(userDto.getAliasHeadPicture());//先将头像进行补全
        visitRecordVo.setVisitorJobNumber(userDto.getNumber());

        if(visitRecordVo.getAddUserId() !=null){
            UserDto userCreatorDto = userApiService.getUserBaseInfo(visitRecordVo.getAddUserId());
            visitRecordVo.setAddUserPic(userCreatorDto.getAliasHeadPicture());//先将头像进行补全
            visitRecordVo.setAddUserName(userCreatorDto.getUsername());//补全创建人名称

        }




        //1、处理拜访目标类型
        dealTargetNameStr(visitRecordVo);

        //2、处理关闭原因类型
        dealCloseReasonTypeNameStr(visitRecordVo);

        //3、处理拜访计划状态
        dealVisitStatusNameStr(visitRecordVo);

        //4、处理拜访客户信息
        VisitCustomerVo visitCustomerVo = getVisitCustomerForVisit(visitRecordVo.getTraderId(),visitRecordVo.getTraderCustomerId(),visitRecordVo.getCustomerName(),null);
        visitRecordVo.setVisitCustomerVo(visitCustomerVo);
        dealTraderBelongAndShare(visitCustomerVo,currentUser);

        //4、查询同行人列表
        List<VisitTongxingUserVo> tongxingUserList = visitTongxingUserService.getTongxingUserList(id);
        visitRecordVo.setTongxingUserList(tongxingUserList);

        //6、查询打卡信息
        List<VisitRecordCardVo> visitRecordCardList = new ArrayList<>();//打卡信息列表
        List<VisitRecordCardVo> tempVisitRecordCardList = visitRecordCardService.getLatestCardList(id);
        if(CollectionUtil.isNotEmpty(tempVisitRecordCardList)){
            //已打卡人：判断拜访人和同行人，是否已经有了打卡信息，没有的话，添加一条打卡信息到visitRecordCardList
            List<Integer> visitUserIds = CollectionUtil.isEmpty(tempVisitRecordCardList)?new ArrayList<>():(tempVisitRecordCardList.stream().map(VisitRecordCardVo::getVisitUserId).collect(Collectors.toList())) ;
            if(!visitUserIds.contains(visitRecordVo.getVisitorId())){
                VisitRecordCardVo recordCardVo = new VisitRecordCardVo();
                recordCardVo.setVisitUserId(visitRecordVo.getVisitorId());//只返回名称和头像，其他值为空
                recordCardVo.setAliasHeadPicture(visitRecordVo.getVisitorPic());
                recordCardVo.setUserName(visitRecordVo.getVisitorName());//
                recordCardVo.setCardTime(null);
                recordCardVo.setCardLocation(null);
                visitRecordCardList.add(recordCardVo);

            }else {//取出打卡记录中，打卡人为拜访人的这条记录
                for(VisitRecordCardVo cardVo : tempVisitRecordCardList){
                    if(cardVo.getVisitUserId().equals(visitRecordVo.getVisitorId())){
                        visitRecordCardList.add(cardVo);
                        tempVisitRecordCardList.remove(cardVo);
                        break;
                    }
                }
            }
            visitRecordCardList.addAll(tempVisitRecordCardList);//将其他打卡人的记录添加到visitRecordCardList中

            //再补全未打卡人，此部分信息按姓名排序，A-Z
            if(CollectionUtils.isNotEmpty(tongxingUserList)){
                List<VisitRecordCardVo> unVisitRecordCardList = new ArrayList<>();
                //补全同行人的打卡信息
                for(VisitTongxingUserVo tongxingUserVo : tongxingUserList){
                    if(!visitUserIds.contains(tongxingUserVo.getTongxingUserId())){
                        //如果打卡信息中，无同行人的，创建一条未打卡的记录返回给前端做展示，标识就是cardTime为null
                        VisitRecordCardVo recordCardVo = new VisitRecordCardVo();
                        recordCardVo.setRecordId(id);
                        recordCardVo.setVisitUserId(tongxingUserVo.getTongxingUserId());//只返回名称和头像，其他值为空
                        recordCardVo.setAliasHeadPicture(tongxingUserVo.getAliasHeadPicture());
                        recordCardVo.setUserName(tongxingUserVo.getUserName());//
                        recordCardVo.setCardTime(null);
                        recordCardVo.setCardLocation(null);
                        unVisitRecordCardList.add(recordCardVo);
                    }
                }
                //将unVisitRecordCardList中的对象，按uesrName由A-Z进行排序 ，并添加到visitRecordCardList中去
                unVisitRecordCardList.sort(Comparator.comparing(VisitRecordCardVo::getUserName));
                visitRecordCardList.addAll(unVisitRecordCardList);

            }
            visitRecordVo.setCardList(visitRecordCardList);
        }else{
            visitRecordVo.setCardList(visitRecordCardList);
        }


        //7、处理沟通记录信息
        List<CommunicateRecordVo> communicateRecordVoList = communicateRecordMapper.selectCommunicateByRecordId(id);
        visitRecordVo.setCommunicateList(communicateRecordVoList);



        //8\如果关联了商机或线索:关联单据类型默认商机线索类型 1线索2商机
        if(crmVisitRecordEntity.getRelateType() != null && crmVisitRecordEntity.getRelateType() == 1){//线索的情况
            BusinessLeadsDto dto = businessLeadsService.getOne(crmVisitRecordEntity.getBussinessChanceId());
            visitRecordVo.setBusinessLeadsForVisitDto(getBusinessLeadsForVisit(dto));
            if(dto.getTraderId() != null){
                TraderCustomerInfoVo infoVo = traderCustomerBaseService.getTraderCustomerInfo(dto.getTraderId());
                getTraderLink(visitRecordVo.getBusinessLeadsForVisitDto(),infoVo);

            }



        }else if(crmVisitRecordEntity.getRelateType() != null && crmVisitRecordEntity.getRelateType() == 2) {//商机的情况
            BusinessChanceDto businessChanceDto = businessChanceService.viewDetail(crmVisitRecordEntity.getBussinessChanceId());
            visitRecordVo.setBusinessChanceForVisitDto(getBusinessChanceForVisit(businessChanceDto));
            if(businessChanceDto.getTraderId() != null){
                TraderCustomerInfoVo infoVo = traderCustomerBaseService.getTraderCustomerInfo(businessChanceDto.getTraderId());
                getTraderLink(visitRecordVo.getBusinessChanceForVisitDto(),infoVo);

            }

        }


        VisitDetailButtonDto visitDetailButtonDto = getVisitDetailButtonDto(visitRecordVo,currentUser);
        visitRecordVo.setVisitDetailButtonDto(visitDetailButtonDto);
        if(checkPermission){
            boolean isVisable = checkVisitRecordVisable(visitRecordVo,currentUser);
            if(!isVisable){
                throw new ServiceException("您无该条单据的查看权限，可联系拜访人" + visitRecordVo.getVisitorName() + "申请查看");
            }
        }

        return visitRecordVo;
    }


    private void dealTraderBelongAndShare(VisitCustomerVo infoVo,CurrentUser currentUser){
        if(infoVo == null || infoVo.getBelongerId() == null ){
            return ;
        }

        if(infoVo.getBelongerId().equals(currentUser.getId())){//归属销售为当前人，即归属为自己为true
            infoVo.setBelong(true);
            infoVo.setShare(false);
        }else{
            RSalesJTraderDto rSalesJTraderDto =  rSalesJTraderApiService.getShareTraderByUserId(infoVo.getTraderId(),currentUser.getId());//分享的记录
            if(rSalesJTraderDto!=null){
                infoVo.setBelong(false);
                infoVo.setShare(true);
            }else{
                infoVo.setBelong(false);
                infoVo.setShare(false);
            }

        }
    }




    private BusinessLeadsForVisitDto getBusinessLeadsForVisit(BusinessLeadsDto dto){
        BusinessLeadsForVisitDto visitDto = new BusinessLeadsForVisitDto();
        visitDto.setId(dto.getId());
        visitDto.setLeadsNo(dto.getLeadsNo());
        visitDto.setTraderId(dto.getTraderId());
        visitDto.setTraderName(dto.getTraderName());
        visitDto.setTycFlag(dto.getTycFlag());
        visitDto.setCreateTime(dto.getAddTime());
        visitDto.setBelongerId(dto.getBelongerId());
        visitDto.setBelonger(dto.getBelonger());
        visitDto.setBelongerPic(dto.getBelongerPic());
        visitDto.setFollowStatus(dto.getFollowStatus());
        //0未分配，1.未处理、2.跟进中、3.已关闭、4.已商机 followStatusStr按这个转换展示
        switch (dto.getFollowStatus()){
            case 0:
                visitDto.setFollowStatusStr("未分配");
                break;
            case 1:
                visitDto.setFollowStatusStr("未处理");
                break;
            case 2:
                visitDto.setFollowStatusStr("跟进中");
                break;
            case 3:
                visitDto.setFollowStatusStr("已关闭");
                break;
            case 4:
                visitDto.setFollowStatusStr("已商机");
                break;
            default:
                //do nothing
                break;
        }
        visitDto.setClueType(dto.getClueType());

        visitDto.setClueTypeName(dto.getClueTypeName());

        return visitDto;
    }



    private BusinessChanceForVisitDto getBusinessChanceForVisit(BusinessChanceDto dto){
        BusinessChanceForVisitDto visitDto = new BusinessChanceForVisitDto();
        visitDto.setBussinessChanceId(dto.getBussinessChanceId());
        visitDto.setBussinessChanceNo(dto.getBussinessChanceNo());
        visitDto.setTraderId(dto.getTraderId());
        visitDto.setTraderName(dto.getTraderName());
        visitDto.setTycFlag(dto.getTycFlag());
        visitDto.setCreateTime(new Date(dto.getAddTime()));
        visitDto.setAmount(dto.getAmount());
        visitDto.setOrderTime(new Date(dto.getOrderTime()));

        visitDto.setBelongerId(dto.getUserId());
        visitDto.setBelonger(dto.getUsername());
        visitDto.setBelongPic(dto.getBelongPic());

        visitDto.setBusinessType(dto.getBusinessType());
        visitDto.setBusinessTypeName(dto.getBusinessTypeStr());

        visitDto.setProductCommentsSale(dto.getProductCommentsSale());

        visitDto.setStage(dto.getStage());
        visitDto.setStageStr(BusinessChanceStageEnum.getDescByCode(dto.getStage()));


        return visitDto;
    }




    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSelective(CurrentUser currentUser, VisitEditInputDto editDto) {
        String customerRequires = CrmEmojiUtils.removeAllEmojis(editDto.getCustomerRequires());//表情符替换
        editDto.setCustomerRequires(customerRequires);

        String recordOtherContact  = CrmEmojiUtils.removeAllEmojis(editDto.getRecordOtherContact());
        editDto.setRecordOtherContact(recordOtherContact);


        // 1. 检查记录是否存在
        VisitRecordVo existRecord = visitRecordMapper.selectVisitRecordById(editDto.getId());
        if (existRecord == null) {
            throw new RuntimeException("拜访记录不存在");
        }

        // 2. 设置更新内容
        VisitRecordVo record = new VisitRecordVo();
        BeanUtils.copyProperties(editDto, record);
        record.setModUserId(currentUser.getId());
        record.setCompleteDatetime(new Date());
        record.setVisitRecordStatus(3);// 已拜访状态

        
        // 3. 执行更新
        visitRecordMapper.updateSelective(record);
        crmVisitRecordLogService.addOperationLog(record.getId(), VisitOperationTypeEnum.SUBMIT_VISIT, "添加了拜访记录", currentUser);

        CommunicateRecordDto communicateRecordDto = new CommunicateRecordDto();


        //取主拜访人的打卡时间
        String lastCardTime =  visitRecordCardMapper.selectLastestCheckInTime(existRecord.getVisitorId(),existRecord.getId());
        if(StringUtils.isNotBlank(lastCardTime)){
            try {
                Date lastCardDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(lastCardTime);
                //转换成时间Date类型
                communicateRecordDto.setBeginTimeDate(lastCardDate);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }

        communicateRecordDto.setCommunicateType(5503);//拜访计划-沟通记录中映射的业务类型
        communicateRecordDto.setRelatedId(record.getId());//拜访计划的ID
        //填充其他字段的值
        communicateRecordDto.setContentSuffix(editDto.getCustomerRequires());
        if(!"Y".equals(editDto.getRecordNoContract())){
            communicateRecordDto.setFollowUpType(5903);
            communicateRecordDto.setContact(editDto.getRecordContactName());
            communicateRecordDto.setContactMob(editDto.getRecordContactMobile());
            communicateRecordDto.setTelephone(editDto.getRecordContactTele());
        }
        followUpRecordService.add(communicateRecordDto);



        List<VisitTongxingUserVo> list  = visitTongxingUserService.getTongxingUserList(existRecord.getId());
        //取list中每个对象的tongxingUserId，转换成List<Integer>
        List<Integer> tongXingUserIdList = CollectionUtils.isNotEmpty(list)?list.stream().map(VisitTongxingUserVo::getTongxingUserId).collect(Collectors.toList()):new ArrayList<>();
        crmVisitSubmitRecordMessageServiceImpl.sendMessage(existRecord,editDto.getId(),tongXingUserIdList,editDto,currentUser);


        TaskQueryDto dto = new TaskQueryDto();
//        dto.setTodoUserIdList(Arrays.asList(currentUser.getId()));
        dto.setBizType(Integer.parseInt(TaskTypeEnum.VISIT_FOLLOW.getValue()));
        dto.setListType(1);
        dto.setBizId(editDto.getId());
        dto.setDoneStatus(0);
        List<MyTaskVo> myTask = taskService.getMyTask(dto);
        log.info("当前账号：{}，待处理待办：{}",currentUser.getUsername(), JSON.toJSONString(myTask));
        if (CollUtil.isNotEmpty(myTask)){
            // 完成本人全部待办
            TaskHandleDto taskHandleDto = new TaskHandleDto();
            taskHandleDto.setDoneStatus(1);
            taskHandleDto.setDoneRemark("拜访已完成");
            for (MyTaskVo taskItem : myTask) {
                taskHandleDto.setTaskItemId(taskItem.getTaskItemId());
                taskHandleDto.setTaskId(taskItem.getTaskId());
                log.info("完成拜访待办：{}",JSON.toJSON(taskHandleDto));
                taskService.handle(taskHandleDto);
            }
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeVisitRecord(CurrentUser currentUser, VisitCloseDto closeDto) {
        // 1. 检查记录是否存在
        VisitRecordVo existRecord = visitRecordMapper.selectVisitRecordById(closeDto.getId());
        if (existRecord == null) {
            throw new RuntimeException("拜访记录不存在");
        }

        // 2. 设置更新内容
        VisitRecordVo record = new VisitRecordVo();
        BeanUtils.copyProperties(closeDto, record);
        record.setModUserId(currentUser.getId());
        record.setVisitRecordStatus(4);// 已关闭
        // 3. 执行更新
        visitRecordMapper.updateClose(record);
        crmVisitRecordLogService.addOperationLog(record.getId(), VisitOperationTypeEnum.CLOSE, "关闭了拜访计划", currentUser);

        List<VisitTongxingUserVo> list  = visitTongxingUserService.getTongxingUserList(existRecord.getId());
        //取list中每个对象的tongxingUserId，转换成List<Integer>
        List<Integer> userIdList = CollectionUtils.isNotEmpty(list)?list.stream().map(VisitTongxingUserVo::getTongxingUserId).collect(Collectors.toList()):new ArrayList<>();

        String closeReason = dealCloseReasonTypeNameStr(closeDto.getCloseReasonType());

        crmVisitCloseMessageServiceImpl.sendMessage(existRecord,existRecord.getId(),userIdList,""+closeReason+"-"+closeDto.getCloseReasonContent(),currentUser);


        TaskQueryDto dto = new TaskQueryDto();
//        dto.setTodoUserIdList(Arrays.asList(currentUser.getId()));
        dto.setBizType(Integer.parseInt(TaskTypeEnum.VISIT_FOLLOW.getValue()));
        dto.setListType(1);
        dto.setBizId(closeDto.getId());
        dto.setDoneStatus(0);
        List<MyTaskVo> myTask = taskService.getMyTask(dto);
        log.info("当前账号：{}，待处理待办：{}",currentUser.getUsername(), JSON.toJSONString(myTask));
        if (CollUtil.isNotEmpty(myTask)){
            // 完成本人全部待办
            TaskHandleDto taskHandleDto = new TaskHandleDto();
            taskHandleDto.setDoneStatus(2);
            taskHandleDto.setDoneRemark("拜访已关闭");
            for (MyTaskVo taskItem : myTask) {
                taskHandleDto.setTaskItemId(taskItem.getTaskItemId());
                taskHandleDto.setTaskId(taskItem.getTaskId());
                log.info("完成拜访待办：{}",JSON.toJSON(taskHandleDto));
                taskService.handle(taskHandleDto);
            }
        }
    }

    @Autowired
    private CrmVisitCloseMessageServiceImpl crmVisitCloseMessageServiceImpl;



    /**
     * 处理拜访目标类型
     * @param visitRecordVo
     */
    private void dealTargetNameStr(VisitRecordVo visitRecordVo){
        if (StringUtils.isNotBlank(visitRecordVo.getVisitTarget())) {
            List<VisitConfigDto> visitConfigDtoList = JSONObject.parseArray(visitConfigList, VisitConfigDto.class);
            //将visitConfigDtoList转换为map，key为visitType，value为visitName
            Map<String, String> visitConfigMap = visitConfigDtoList.stream().collect(Collectors.toMap(VisitConfigDto::getVisitType, VisitConfigDto::getVisitName));
            //将VisitInputDto.getVisitTarget()按英文逗号分割，并将visitConfigList转换成map，获取对应的中文名称
            String[] visitTargetArr = visitRecordVo.getVisitTarget().split(",");
            List<String> visitTargetNameList = new ArrayList<>();
            for (String visitTarget : visitTargetArr) {
                visitTargetNameList.add(visitConfigMap.get(visitTarget));
            }
            String visitTargetNames = StringUtils.join(visitTargetNameList, "，");
            visitRecordVo.setVisitTargetStr(visitTargetNames);
        }
    }

    private String dealTargetNameStr(String visitTarget){
        if (StringUtils.isNotBlank(visitTarget)) {
            List<VisitConfigDto> visitConfigDtoList = JSONObject.parseArray(visitConfigList, VisitConfigDto.class);
            //将visitConfigDtoList转换为map，key为visitType，value为visitName
            Map<String, String> visitConfigMap = visitConfigDtoList.stream().collect(Collectors.toMap(VisitConfigDto::getVisitType, VisitConfigDto::getVisitName));
            //将VisitInputDto.getVisitTarget()按英文逗号分割，并将visitConfigList转换成map，获取对应的中文名称
            String[] visitTargetArr = visitTarget.split(",");
            List<String> visitTargetNameList = new ArrayList<>();
            for (String visitTargetObj : visitTargetArr) {
                visitTargetNameList.add(visitConfigMap.get(visitTargetObj));
            }
            String visitTargetNames = StringUtils.join(visitTargetNameList, "，");
            return visitTargetNames;
        }
        return null;
    }

    /**
     * 处理关闭原因
     * @param visitRecordVo
     * @return
     */
    private String dealCloseReasonTypeNameStr(VisitRecordVo visitRecordVo){
        //将visitRecordVo 的closeReasonType与CloseReasonTypeEnum枚举做比较
        Integer closeReasonType = visitRecordVo.getCloseReasonType();
        if(closeReasonType != null){
            //根据closeReasonType，取CloseReasonTypeEnum的值做比较
            for (CloseReasonTypeEnum closeReasonTypeEnum : CloseReasonTypeEnum.values()) {
                if(closeReasonTypeEnum.getCloseReasonType().equals(closeReasonType)){
                    String closeReasonTypeName = closeReasonTypeEnum.getCloseReasonTypeName();
                    visitRecordVo.setCloseReasonTypeName(closeReasonTypeName);
                    return closeReasonTypeName;
                }
            }
        }
        return "";
    }

    private String dealCloseReasonTypeNameStr(Integer closeReasonType ){

        if(closeReasonType != null){
            //根据closeReasonType，取CloseReasonTypeEnum的值做比较
            for (CloseReasonTypeEnum closeReasonTypeEnum : CloseReasonTypeEnum.values()) {
                if(closeReasonTypeEnum.getCloseReasonType().equals(closeReasonType)){
                    return closeReasonTypeEnum.getCloseReasonTypeName();
                }
            }
        }
        return "";
    }

    /**
     * 处理拜访计划的状态名称
     * @param visitRecordVo
     * @return
     */
    private String dealVisitStatusNameStr(VisitRecordVo visitRecordVo){
        Integer visitRecordStatus = visitRecordVo.getVisitRecordStatus();
        if(visitRecordStatus != null){
            //根据visitRecordStatus，取VisitRecordStatusEnum的值做比较
            for (VisitRecordStatusEnum visitRecordStatusEnum : VisitRecordStatusEnum.values()) {
                if(visitRecordStatusEnum.getRecordStatus().equals(visitRecordStatus)){
                    return visitRecordStatusEnum.getRecordStatusName();
                }
            }
        }
        return "";
    }

    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    public VisitCustomerVo getVisitCustomerForVisitSimple(Integer traderId,Integer traderCustomerId,String traderName,String tycFlag,String customerGrade){
        if(traderId ==null || traderId < 1){
            return null;
        }
        VisitCustomerVo visitCustomerVo = new VisitCustomerVo();
        visitCustomerVo.setTraderId(traderId);
        visitCustomerVo.setTraderName(traderName);
        visitCustomerVo.setTraderCustomerId(traderCustomerId);
//        TraderUserDto traderUserDto =  traderCustomerBaseService.getTraderUserByTraderId(traderId);
//        CurrentUser currentUser = CurrentUser.getCurrentUser();
//        if(traderUserDto != null){
//            visitCustomerVo.setBelongerId(traderUserDto.getUserId());
//            visitCustomerVo.setBelongerName(traderUserDto.getUserName());
//            if(currentUser.getId().equals(traderUserDto.getUserId())){
//                visitCustomerVo.setBelong(true);
//            }else{
//                RSalesJTraderDto rSalesJTraderDto =  rSalesJTraderApiService.getShareTraderByUserId(traderId,currentUser.getId());//分享的记录
//                if(rSalesJTraderDto!=null){
//                    visitCustomerVo.setBelong(false);
//                    visitCustomerVo.setShare(true);
//                }
//
//            }
//        }
        //客户基本信息
        TraderCustomerInfoVo infoVo = traderCustomerBaseService.getTraderCustomerInfo(traderId);
        if(infoVo != null){
            visitCustomerVo.setTraderCustomerId(infoVo.getTraderCustomerId());
            visitCustomerVo.setTraderName(infoVo.getTraderName());
        }
        getTraderLink(visitCustomerVo,infoVo);

        // 决策标签-不查，列表中用不上
//
        //天眼查标识
        if(StringUtils.isEmpty(tycFlag)){
            visitCustomerVo.setTycFlag( "N");
        }else{
            visitCustomerVo.setTycFlag(tycFlag);
        }
        //客户等级
        if(customerGrade != null){//从列表中关联查出来
            if("其他".equals(customerGrade)){
                customerGrade = "D";
            }
            visitCustomerVo.setCustomerGrade(customerGrade);
        }

        return visitCustomerVo;
    }

    public String checkVisitCustomerCanClick(Integer traderId){
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        TraderUserDto traderUserDto =  traderCustomerBaseService.getTraderUserByTraderId(traderId);
        if(traderUserDto != null){
            if(currentUser.getId().equals(traderUserDto.getUserId())){
                return StringUtils.EMPTY;
            }else{
                RSalesJTraderDto rSalesJTraderDto =  rSalesJTraderApiService.getShareTraderByUserId(traderId,currentUser.getId());//分享的记录
                if(rSalesJTraderDto!=null){
                    return StringUtils.EMPTY;
                }

                List<UserDto> userDtoList = userApiService.queryUserSubFromUac(currentUser.getId());
                List<Integer> allSubordinateUserIdList = userDtoList.stream().map(UserDto::getUserId).collect(Collectors.toList());
                if(allSubordinateUserIdList.contains(traderUserDto.getUserId())){//如果下属在归属人里
                    return StringUtils.EMPTY;
                }

            }
        }

        String template = "尚无查看权限，可联系客户归属销售%s，将其进行分享。";
        return String.format(template,(
                (traderUserDto == null || StringUtils.isNotBlank(traderUserDto.getUserName()))
                        ?traderUserDto.getUserName():""));
    }


    public VisitCustomerVo getVisitCustomerForVisit(Integer traderId,Integer traderCustomerId,String traderName,String tycFlag){
        if(traderId ==null || traderId < 1){
            return null;
        }
        VisitCustomerVo visitCustomerVo = new VisitCustomerVo();
        visitCustomerVo.setTraderId(traderId);
        visitCustomerVo.setTraderName(traderName);
        visitCustomerVo.setTraderCustomerId(traderCustomerId);

        TraderUserDto traderUserDto =  traderCustomerBaseService.getTraderUserByTraderId(traderId);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if(traderUserDto != null){
            visitCustomerVo.setBelongerId(traderUserDto.getUserId());
            visitCustomerVo.setBelongerName(traderUserDto.getUserName());
            if(currentUser.getId().equals(traderUserDto.getUserId())){
                visitCustomerVo.setBelong(true);
            }else{
                RSalesJTraderDto rSalesJTraderDto =  rSalesJTraderApiService.getShareTraderByUserId(traderId,currentUser.getId());//分享的记录
                if(rSalesJTraderDto!=null){
                    visitCustomerVo.setBelong(false);
                    visitCustomerVo.setShare(true);
                }

            }
        }
        TraderCustomerInfoVo infoVo = traderCustomerBaseService.getTraderCustomerInfo(traderId);
        if(infoVo != null){
            visitCustomerVo.setTraderCustomerId(infoVo.getTraderCustomerId());
            visitCustomerVo.setTraderName(infoVo.getTraderName());
        }
        getTraderLink(visitCustomerVo,infoVo);

        // 决策标签
        TraderTagApiReq traderTagApiReq = new TraderTagApiReq();
        traderTagApiReq.setTraderId(traderId);
        try {
            RestfulResult<TraderDecisionTag> traderDecisionTag = oneDataTraderTagApi.getTraderDecisionTag(traderTagApiReq);
            TraderDecisionTag data = Optional.ofNullable(traderDecisionTag.getData()).orElse(new TraderDecisionTag());
            String customerGrade = data.getCustomerLevel();
            if("其他".equals(customerGrade)){
                customerGrade = "D";
            }
            visitCustomerVo.setCustomerGrade(customerGrade);
        } catch (Exception e) {
            log.error("大数据接口查询客户决策标签异常,traderId:{}", traderId);
        }

        TraderTransactionTag traderTransactionTag = getTraderTransactionTagApi(traderTagApiReq);
        if (traderTransactionTag != null) {
            visitCustomerVo.setHistoryTransactionNum(traderTransactionTag.getHistoryTransactionNum());//交易次数：24
            visitCustomerVo.setHistoryTransactionAmount(traderTransactionTag.getHistoryTransactionAmount());//总交易额：150.00W
            visitCustomerVo.setLastOrderTime(traderTransactionTag.getLastOrderTime());//最近下单：2024-10-10
        }

        if(infoVo!=null ){
            if(StringUtils.isEmpty(tycFlag)){
                TraderInfoTyc tycInfoQuery = new TraderInfoTyc();
                tycInfoQuery.setName(infoVo.getTraderName());
                TraderInfoTyc traderInfoTyc = tycMapper.getTraderInfoTycByTraderName(tycInfoQuery);
                visitCustomerVo.setTycFlag(Objects.nonNull(traderInfoTyc) ? "Y" : "N");
            }else{
                visitCustomerVo.setTycFlag(tycFlag);
            }
        }else {
            visitCustomerVo.setTycFlag("N");
        }


        return visitCustomerVo;
    }

    @Autowired
    private TycMapper tycMapper;

    private void getTraderLink(VisitCustomerVo visitCustomerVo,TraderCustomerInfoVo infoVo ) {
        if(visitCustomerVo == null || infoVo == null){
            return ;
        }
            try {
                String targetTabUrl = ("/trader/customer/new/portrait.do?traderId=" + infoVo.getTraderId() + "&traderCustomerId=" + infoVo.getTraderCustomerId() + "&customerNature=" + infoVo.getCustomerNature());
                visitCustomerVo.setTraderNameInnerLink(targetTabUrl);
                if (crmJumpErpUrl.indexOf("sso") > -1) {
                    String encodetargetTabUrl = URLEncoder.encode("/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=客户360", "UTF-8");
                    visitCustomerVo.setTraderNameLink(crmJumpErpUrl + encodetargetTabUrl);
                } else {
                    String encodetargetTabUrl = "/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=" + URLEncoder.encode("客户360", "UTF-8");
                    visitCustomerVo.setTraderNameLink(crmJumpErpUrl + encodetargetTabUrl);
                }
            } catch (Exception e) {
                log.error("客户链接转换给前端时失败，需要检查", e);
            }
    }

    private void getTraderLink(BusinessChanceForVisitDto businessChanceForVisitDto,TraderCustomerInfoVo infoVo ) {
        if(businessChanceForVisitDto == null || infoVo == null){
            return ;
        }
        try {
            String targetTabUrl = ("/trader/customer/new/portrait.do?traderId=" + infoVo.getTraderId() + "&traderCustomerId=" + infoVo.getTraderCustomerId() + "&customerNature=" + infoVo.getCustomerNature());
            businessChanceForVisitDto.setTraderNameInnerLink(targetTabUrl);
            if (crmJumpErpUrl.indexOf("sso") > -1) {
                String encodetargetTabUrl = URLEncoder.encode("/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=客户360", "UTF-8");
                businessChanceForVisitDto.setTraderNameLink(crmJumpErpUrl + encodetargetTabUrl);
            } else {
                String encodetargetTabUrl = "/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=" + URLEncoder.encode("客户360", "UTF-8");
                businessChanceForVisitDto.setTraderNameLink(crmJumpErpUrl + encodetargetTabUrl);
            }
        } catch (Exception e) {
            log.error("客户链接转换给前端时失败，需要检查", e);
        }
    }

    private void getTraderLink(BusinessLeadsForVisitDto businessLeadsForVisitDto,TraderCustomerInfoVo infoVo ) {
        if(businessLeadsForVisitDto == null || infoVo == null){
            return ;
        }
        try {
            String targetTabUrl = ("/trader/customer/new/portrait.do?traderId=" + infoVo.getTraderId() + "&traderCustomerId=" + infoVo.getTraderCustomerId() + "&customerNature=" + infoVo.getCustomerNature());
            businessLeadsForVisitDto.setTraderNameInnerLink(targetTabUrl);
            if (crmJumpErpUrl.indexOf("sso") > -1) {
                String encodetargetTabUrl = URLEncoder.encode("/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=客户360", "UTF-8");
                businessLeadsForVisitDto.setTraderNameLink(crmJumpErpUrl + encodetargetTabUrl);
            } else {
                String encodetargetTabUrl = "/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=" + URLEncoder.encode("客户360", "UTF-8");
                businessLeadsForVisitDto.setTraderNameLink(crmJumpErpUrl + encodetargetTabUrl);
            }
        } catch (Exception e) {
            log.error("客户链接转换给前端时失败，需要检查", e);
        }
    }


    @Value("${crmJumpErpUrl}")
    private String crmJumpErpUrl;


    private TraderTransactionTag getTraderTransactionTagApi(TraderTagApiReq traderTagApiReq) {
        try {
            RestfulResult<TraderTransactionTag> traderTransactionTag = oneDataTraderTagApi.getTraderTransactionTag(traderTagApiReq);
            return Optional.ofNullable(traderTransactionTag.getData()).orElse(new TraderTransactionTag());
        } catch (Exception e) {
            log.error("获取大数据客户交易标签异常，traderId:{}", traderTagApiReq.getTraderId());
        }
        return null;
    }



    @Override
    public Boolean checkMobileExists(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return false;
        }
        Integer count = webAccountMapper.checkMobileExists(mobile);
        return count != null && count > 0;
    }

    @Override
    public void remindVisitToday(){
        // 获取今天的开始和结束时间
        Date today = DateUtil.beginOfDay(new Date());
        String todayFormat = DateUtil.format(today,"yyyy-MM-dd");
        // 查询今天的拜访计划
        List<CrmVisitRecordEntity> visitRecords = visitRecordMapper.selectVisitRecordListForToday();
        
        if(CollectionUtil.isEmpty(visitRecords)) {
            return;
        }
        
        // 按拜访人和同行人分组统计
        Map<Integer, List<CrmVisitRecordEntity>> visitUserMap = new HashMap<>();
        for(CrmVisitRecordEntity record : visitRecords) {
            // 主拜访人
            visitUserMap.computeIfAbsent(record.getVisitorId(), k -> new ArrayList<>()).add(record);
            List<VisitTongxingUserVo> tongxingUserVoList = visitTongxingUserService.getTongxingUserList(record.getId().intValue());
            // 同行人
            if(CollectionUtils.isNotEmpty(tongxingUserVoList)) {
                for(VisitTongxingUserVo  txVo : tongxingUserVoList) {
                     visitUserMap.computeIfAbsent(Integer.valueOf(txVo.getTongxingUserId()), k -> new ArrayList<>()).add(record);
                }
            }
        }

        //取所有visitUserMap，转换为List
        List<Integer> visitUserIdList = new ArrayList<>(visitUserMap.keySet());
        List<UserDto> visitUserList = userApiService.getUserInfoByUserIds(visitUserIdList);
        //将visitUserList 转换为userId为key的HashMap
        Map<Integer, UserDto> visitUserInfoMap = visitUserList.stream().collect(Collectors.toMap(UserDto::getUserId, Function.identity()));


        // 处理每个人的拜访提醒
        for(Map.Entry<Integer, List<CrmVisitRecordEntity>> entry : visitUserMap.entrySet()) {
            Integer userId = entry.getKey();
            UserDto userDto = visitUserInfoMap.get(userId);
            List<CrmVisitRecordEntity> userVisits = entry.getValue();
            
            // 拼接客户名称
            String customerNames = userVisits.stream()
                    .map(CrmVisitRecordEntity::getCustomerName)
                    .collect(Collectors.joining(","));
                    
            // TODO: 发送消息通知
            log.info("用户{}今天需要拜访{}个客户: {}", userId, userVisits.size(), customerNames);

            SendMessageDto sendMessageDto = new SendMessageDto();
            sendMessageDto.setUrl(
                    jumpService.getMjumpUrl(
                            crmApplicationMessageJumpUrl + "/crm/visitRecord/m/index?planVisitDateStart=" + todayFormat+"&planVisitDateEnd="+todayFormat+"&visitId="+userId,
                        lxcrmUrl + "/crm/visitRecord/profile/index?planVisitDateStart=" + todayFormat+"&planVisitDateEnd="+todayFormat+"&visitId="+userId,
                        JumpErpTitleEnum.VISIT_RECORD_DETAIL
                    )
            );
            sendMessageDto.setUserNumber(userDto.getNumber());
            sendMessageDto.setFormat("拜访客户："+customerNames);
            taskService.sendCardMsg(sendMessageDto, "今日有"+userVisits.size()+"条拜访计划");

        }
    }

    @Override
    public void sendTodayVisitNotCardReminder() {   //发送今天未打卡的拜访提醒
        //查询当天待拜访的记录(拜访人未打卡:状态为待拜访1或拜访中2，且拜访人是在当天无打卡记录)-防止提前打卡，并填写了拜访记录的情况
        List<CrmVisitRecordEntity> todayVisitList = crmVisitRecordMapper.selectVisitRecordListForTodayVisitorNotCard();

        // 给每个待拜访的人发送提醒消息
        if(CollectionUtils.isNotEmpty(todayVisitList)){
            for (CrmVisitRecordEntity visit : todayVisitList) {
                int id = visit.getId().intValue();
                VisitRecordVo visitRecordVo = crmVisitRecordMapper.selectVisitRecordById(id);
                dealTargetNameStr(visitRecordVo);
                List<VisitTongxingUserVo> tongxingUserList  = visitTongxingUserService.getTongxingUserList(id);
                //给当前的拜访人（未打卡的拜访人，发消息）
                crmVisitNotCardMessageServiceImpl.sendMessage(visitRecordVo,id,tongxingUserList,visitRecordVo.getVisitorId());
            }
        }


        // 查询同行人数据-未打卡
        List<VisitTongXingNotCardVo> todayTongXingList = crmVisitRecordMapper.selectVisitRecordListForTodayTongXingNotCard();
        if(CollectionUtils.isNotEmpty(todayTongXingList)){
            for(VisitTongXingNotCardVo visit : todayTongXingList) {
                int id = visit.getId();
                VisitRecordVo visitRecordVo = crmVisitRecordMapper.selectVisitRecordById(id);
                dealTargetNameStr(visitRecordVo);
                List<VisitTongxingUserVo> tongxingUserList  = visitTongxingUserService.getTongxingUserList(id);
                //给当前的同行人（未打卡的人，发消息）
                crmVisitNotCardMessageServiceImpl.sendMessage(visitRecordVo,id,tongxingUserList,visit.getTongXingUserId());
            }
        }
    }

    @Override
    public void sendYestodayNotRecordReminder(){
        //查询前一天未填写拜访记录(拜访人 拜访中2  最近一次的拜访时间为昨天)-防止提前打卡，并填写了拜访记录的情况
        List<CrmVisitRecordEntity> yestodayCardList = crmVisitRecordMapper.sendYestodayNotRecordReminder();
        for(CrmVisitRecordEntity crmVisitRecordEntity :yestodayCardList){
            int id = crmVisitRecordEntity.getId().intValue();
            VisitRecordVo visitRecordVo = crmVisitRecordMapper.selectVisitRecordById(id);
            List<VisitTongxingUserVo> tongxingUserList  = visitTongxingUserService.getTongxingUserList(id);

            String cardTimeLastOne = visitRecordCardMapper.selectLastestCheckInTime(visitRecordVo.getVisitorId(),visitRecordVo.getId());
            //给当前的同行人（未打卡的人，发消息）
            crmVisitNotRecordMessageServiceImpl.sendMessage(visitRecordVo,id,tongxingUserList,cardTimeLastOne);

        }
    }




}