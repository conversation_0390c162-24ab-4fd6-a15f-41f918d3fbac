package com.vedeng.temporal.domain.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.util.Map;
import java.util.HashMap;

/**
 * 多公司业务请求对象
 * 用于在工作流中传递业务数据
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class CompanyBusinessRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务流转单ID
     */
    private String businessId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 源公司代码 0
     */
    private String sourceCompanyCode;

    /**
     * 目标公司代码 1
     */
    private String targetCompanyCode;

    /**
     * 业务数据（JSON格式）
     */
    private String businessData;

    /**
     * 扩展属性
     */
    private Map<String, Object> extendedProperties;

    /**
     * 创建时间戳
     */
    private Long createTimestamp;

    /**
     * 工作流执行ID
     */
    private String workflowExecutionId;

    /**
     * 流程节点ID
     * 用于标识当前业务在流程中的节点位置，便于获取业务数据和状态跟踪
     */
    private Long flowNodeId;

    /**
     * 用户ID
     * 用于API调用时的用户身份认证
     */
    private Long userId;

    /**
     * 用户名
     * 用于API调用时的用户身份认证（可选，与userId二选一）
     */
    private String userName;

    /**
     * 请求头信息
     * 用于传递幂等性相关的元数据，如公司代码、流程ID等
     */
    private Map<String, String> headers;


    // ========== 辅助方法 ==========

    /**
     * 创建指定公司的请求副本
     *
     * @param companyCode 公司代码
     * @return 新的请求对象
     */
    public CompanyBusinessRequest withCompany(String companyCode) {
        return this.toBuilder()
                .sourceCompanyCode(companyCode)
                .targetCompanyCode(companyCode)
                .build();
    }

    /**
     * 创建指定源公司和目标公司的请求副本
     *
     * @param sourceCompany 源公司代码
     * @param targetCompany 目标公司代码
     * @return 新的请求对象
     */
    public CompanyBusinessRequest withSourceAndTargetCompany(String sourceCompany, String targetCompany) {
        return this.toBuilder()
                .sourceCompanyCode(sourceCompany)
                .targetCompanyCode(targetCompany)
                .build();
    }



    /**
     * 获取请求头，如果为空则返回空的HashMap
     *
     * @return 请求头集合
     */
    public Map<String, String> getHeaders() {
        return this.headers != null ? this.headers : new HashMap<>();
    }


}
