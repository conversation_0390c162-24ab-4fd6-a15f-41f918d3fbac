package com.vedeng.erp.broadcast.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 批量上传结果DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchUploadResultDto {

    /**
     * 上传成功的文件数量
     */
    private Integer successCount;

    /**
     * 上传失败的文件数量
     */
    private Integer failCount;

    /**
     * 总文件数量
     */
    private Integer totalCount;

    /**
     * 成功上传的文件信息列表
     */
    private List<UploadFileInfo> successFiles;

    /**
     * 失败的文件信息列表
     */
    private List<UploadFileInfo> failFiles;

    /**
     * 创建的配置记录ID列表
     */
    private List<Integer> configIds;

    /**
     * 上传文件信息
     */
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UploadFileInfo {
        /**
         * 原始文件名
         */
        private String originalFileName;

        /**
         * 文件大小（字节）
         */
        private Long fileSize;

        /**
         * 上传后的文件URL
         */
        private String fileUrl;

        /**
         * 配置记录ID（成功时有值）
         */
        private Integer configId;

        /**
         * 错误信息（失败时有值）
         */
        private String errorMessage;
    }
}
