package com.vedeng.crm.business.quote.facade.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.common.core.enums.PatternEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.ErpDateUtils;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import com.vedeng.common.trace.enums.BizLogEnum;
import com.vedeng.crm.business.business.domain.dto.CrmBusinessChanceDto;
import com.vedeng.crm.business.business.domain.entity.CrmBusinessChanceEntity;
import com.vedeng.crm.business.business.mapstruct.CrmBusinessChanceConvertor;
import com.vedeng.crm.business.business.service.CrmBusinessChanceService;
import com.vedeng.crm.business.quote.domain.dto.*;
import com.vedeng.crm.business.quote.domain.entity.*;
import com.vedeng.crm.business.quote.domain.enums.ReportStatusEnum;
import com.vedeng.crm.business.quote.facade.QuoteFacade;
import com.vedeng.crm.business.quote.mapstruct.*;
import com.vedeng.crm.business.quote.service.*;
import com.vedeng.crm.business.quote.service.impl.NeedsImportReadListener;
import com.vedeng.crm.common.service.JumpService;
import com.vedeng.crm.feign.terminal.TerminalApiService;
import com.vedeng.crm.shard.service.ShareService;
import com.vedeng.crm.task.domain.dto.TaskDto;
import com.vedeng.crm.task.domain.dto.TaskHandleDto;
import com.vedeng.crm.task.domain.dto.TaskQueryDto;
import com.vedeng.crm.task.domain.vo.MyTaskVo;
import com.vedeng.crm.task.service.TaskService;
import com.vedeng.erp.business.common.enums.BusinessChanceStageEnum;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.system.dto.OperationLogDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.OperationLogApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.domain.entity.QuoteorderEntity;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.service.QuoteOrderService;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.goods.dto.BusinessOrderCategoryDto;
import com.vedeng.goods.dto.CoreSkuDto;
import com.vedeng.goods.dto.CoreSkuInfoDto;
import com.vedeng.goods.dto.ProductManageAndAsistDto;
import com.vedeng.goods.feign.price.RemotePriceApiService;
import com.vedeng.goods.service.BusinessOrderCategoryApiService;
import com.vedeng.goods.service.CoreSkuService;
import com.vedeng.goods.service.GoodsApiService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.infrastructure.file.domain.GlobalFileDto;
import com.vedeng.infrastructure.file.domain.GlobalFileRequest;
import com.vedeng.infrastructure.file.manager.GlobalFileService;
import com.vedeng.price.api.price.dto.ResultInfo;
import com.vedeng.price.api.price.dto.price.*;

import com.vedeng.uac.api.dto.AppChatReqDto;
import com.vedeng.uac.api.dto.AppchatResDto;
import com.vedeng.uac.api.dto.MessageTempleteDto;
import com.vedeng.uac.api.dto.MessageTempleteDto.Button;
import com.vedeng.uac.api.dto.MessageTempleteDto.ButtonSelectionQuestionOption;
import com.vedeng.uac.api.dto.UserInfoDto;
import com.vedeng.uac.api.dto.WxUserDto;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.WxCpChat;
import me.chanjar.weixin.cp.bean.message.WxCpAppChatMessage;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.redisson.api.RBucket;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class QuoteFacadeImpl implements QuoteFacade {

    private final Integer MAX_DATA_LENGTH = 1000;

    private final String KEY_PREFIX_QUOTE = "crm:quote:";
    private final String KEY_PREFIX_LOCAL = "crm:quote:local:";


    @Value("${crmJumpErpUrl}")
    private String crmJumpErpUrl;
    
    @Value("${businessUrl}")
    private String businessUrl;

    @Value("${crmApplicationMessageJumpUrl}")
    private String crmApplicationMessageJumpUrl;

    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;

    @Autowired
    private CoreSkuService coreSkuService;

    @Autowired
    private CrmQuoteOrderService crmQuoteOrderService;

    @Autowired
    private CrmQuoteOrderGoodsService crmQuoteOrderGoodsService;

    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    @Autowired
    private QuoteOrderNeedsService quoteOrderNeedsService;

    @Autowired
    private RQuoteNeedsJGoodsService rQuoteNeedsJGoodsService;

    @Autowired
    private QuoteGoodsAddConvertor quoteGoodsAddConvertor;

    @Autowired
    private CrmCoreSkuInfoConvertor crmCoreSkuInfoConvertor;

    @Autowired
    private QuoteGoodsUpdateConvertor  quoteGoodsUpdateConvertor;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private GoodsApiService goodsApiService;

    @Autowired
    private OperationLogApiService operationLogApiService;

    @Autowired
    private BusinessChanceService businessChanceService;

    @Autowired
    private CrmQuoteOrderSkuInfoConvertor crmQuoteOrderSkuInfoConvertor;

    @Autowired
    private CrmBusinessChanceConvertor crmBusinessChanceConvertor;

    @Autowired
    private TaskService taskService;

    @Autowired
    private ShareService shareService;

    @Autowired
    private QuoteOrderGoodsRemarkService quoteOrderGoodsRemarkService;
    
    @Autowired
    private TerminalApiService terminalApiService;
    

    //1007	1	Caesar.lv
    //1409	1	Jez.zhang
    //1411	1	Kerwin.wang
    //1869	1	Macro.zhang
    //1891	1	Ives.hua
    //1909	1	Aadi.chen
    /**
     * 群成员
     * test环境 1409jez,1411kerwin,1891ives,1948gatlin,1909aadi,1007caesar
     */
    @Value("${app_chat_numbers}")
    private String userNumbers;//注意，这里配置的是User_id

    @Value("${key_prefix_chat:businessChanceIdtest1234}")
    private String KEY_PREFIX_CHAT;

    private static final String url = "/crm/businessChance/profile/detail?id=";

    @Value("${lxcrmUrl}")
    private String lxcrmUrl;
    
    @Value("${testParentBelongId}")
    private String testParentBelongId;
    
    @Value("${testParentBelongIdInt}")
    private Integer testParentBelongIdInt;
    
    @Value("${testParentBelongName}")
    private String testParentBelongName;
    
    //测试环境是否开启
    @Value("${isTurnOnTest:0}")
    private String isTurnOnTest;
    
    //发布环境
    @Value("${PROJECT_ENV}")
    private String projectEnv;


    @Autowired
    private BusinessOrderCategoryApiService businessOrderCategoryService;


    @Override
    public QuoteCreateAppChat createAppChat(CreateAppChatDto createAppChatDto,CurrentUser currentUser) {
        if (StrUtil.isBlank(userNumbers)){
            log.info("群人员配置为空，查询真实人");
            return createAppChatSelect(createAppChatDto,currentUser);
        }else{
            log.info("走测试桩，群人员配置：{}",JSON.toJSON(userNumbers));
            List<String> list = Arrays.asList(userNumbers.split(","));
            createAppChatDto.setUserIds(list.stream().map(Integer::valueOf).collect(Collectors.toList()));
            List<UserDto> userDtoList = userApiService.getUserInfoByUserIds(Collections.singletonList(Integer.valueOf(list.get(0))));
            currentUser.setNumber(userDtoList.get(0).getNumber());
            return createAppChatSelect(createAppChatDto,currentUser);
        }
    }

    public QuoteCreateAppChat createAppChatSelect(CreateAppChatDto createAppChatDto,CurrentUser currentUser) {
        log.info("创建群聊入参:{}",JSON.toJSON(createAppChatDto));
        List<Integer> userIdList = createAppChatDto.getUserIds();
        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(createAppChatDto.getQuoteorderId());
        if (Objects.isNull(crmQuoteOrderDto)){
            throw new ServiceException("报价单不存在");
        }
        Integer businessChanceId = crmQuoteOrderDto.getBussinessChanceId();

        BusinessChanceDto queryDto = new BusinessChanceDto();
        queryDto.setBussinessChanceId(businessChanceId);
        BusinessChanceDto businessChanceDto = businessChanceService.selectOne(queryDto);
        if (Objects.isNull(businessChanceDto) || StrUtil.isBlank(businessChanceDto.getBussinessChanceNo())){
            throw new ServiceException("商机不存在");
        }
        List<Integer> hasOwnUserList = new ArrayList<>();
        hasOwnUserList.addAll(userIdList);
        //商机的userId就是归属销售
    	Integer belongId = businessChanceDto.getUserId();
        hasOwnUserList.add(belongId);
        List<UserDto> userDtoList = userApiService.getUserInfoByUserIds(hasOwnUserList);
        List<String> userNumbers = userDtoList.stream().map(UserDto::getNumber).distinct().collect(Collectors.toList());
        Map<Integer,String> userIdToNumberMap  = userDtoList.stream()
                .collect(Collectors.toMap(
                        UserDto::getUserId, // Key: userId
                        UserDto::getNumber, // Value: number
                        (existingValue, newValue) -> newValue // 重复时覆盖
                ));

        AppChatReqDto reqDto = new AppChatReqDto();
        reqDto.setChatId(getChatId(businessChanceDto.getBussinessChanceId()));
        reqDto.setName("商机协同群-"+businessChanceDto.getBussinessChanceNo());
        reqDto.setOwner(userIdToNumberMap.get(belongId));
        reqDto.setUsers(userNumbers);
        log.info("创建群聊入参：{}",JSON.toJSON(reqDto));
        RestfulResult<AppchatResDto> appChat = uacWxUserInfoApiService.create(reqDto);
        log.info("创建群聊结果,入参：{},出参:{}",JSON.toJSON(reqDto),JSON.toJSON(appChat));
        if (!appChat.isSuccess()){
            log.info("创建群聊失败:{}",JSON.toJSON(appChat));
            AppChatReqDto req = new AppChatReqDto();
            req.setChatId(getChatId(businessChanceId));
            RestfulResult<WxCpChat> wxCpChatRestfulResult = uacWxUserInfoApiService.get(req);
            if (wxCpChatRestfulResult.isSuccess()){
                log.info("重复创建，查询群聊结果:{}",JSON.toJSON(wxCpChatRestfulResult));
                QuoteCreateAppChat result = new QuoteCreateAppChat();
                result.setChatid(getChatId(businessChanceId));
                result.setName(wxCpChatRestfulResult.getData().getName());
                return result;
            }
            throw new ServiceException("创建群聊失败");
        }
        // 更新建群状态
        CrmQuoteOrderDto updateOrderDto = new CrmQuoteOrderDto();
        updateOrderDto.setQuoteorderId(createAppChatDto.getQuoteorderId());
        updateOrderDto.setIsBuildChat(1);
        log.info("更新建群状态：{}",JSON.toJSON(updateOrderDto));
        crmQuoteOrderService.updateQuoteOrder(updateOrderDto,currentUser);

        // 发送卡片消息
        sendAppMessageCard(businessChanceId,"商机协作提醒",
                businessChanceDto.getBussinessChanceNo()+"商机协同群已创建，可打开企业微信查看跟进");

        // 记录操作日志
        OperationLogDto logDto = new OperationLogDto();
        logDto.setBizId(createAppChatDto.getQuoteorderId());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        operationLogApiService.save(logDto, BizLogEnum.INITIATE_GROUP_CHAT);

        QuoteCreateAppChat quoteCreateAppChat = new QuoteCreateAppChat();
        quoteCreateAppChat.setChatid(reqDto.getChatId());
        quoteCreateAppChat.setName(reqDto.getName());
        return quoteCreateAppChat;
    }

    private String getChatId(Integer businessChanceId){
        return KEY_PREFIX_CHAT+businessChanceId;
    }

    @Override
    public List<UserDto> getChatGroupUser(Integer businessChanceId){
        AppChatReqDto req = new AppChatReqDto();
        req.setChatId(getChatId(businessChanceId));
        RestfulResult<WxCpChat> wxCpChatRestfulResult = uacWxUserInfoApiService.get(req);
        if (!wxCpChatRestfulResult.isSuccess()){
            return null;
        }
        List<String> numbers = wxCpChatRestfulResult.getData().getUsers();
        List<Integer> numberList = numbers.stream().distinct().map(Integer::valueOf).collect(Collectors.toList());
        List<UserDto> userByNumberlist = userApiService.getUserByNumberlist(numberList);
        log.info("查询群聊用户结果:{}",JSON.toJSON(userByNumberlist));
        return userByNumberlist;

    }

    @Override
    public QuoteTaskTipsDto taskTips(CurrentUser currentUser,Integer quoteorderId) {
        int taskCount = taskService.getTaskCount(quoteorderId,currentUser.getId(),3);

        CrmQuoteOrderEntity quoteorderEntity = crmQuoteOrderService.selectByPrimaryKey(quoteorderId);
        if(quoteorderEntity!=null && quoteorderEntity.getBussinessChanceId() != null && quoteorderEntity.getBussinessChanceId() > 0){
            int taskCount1 = taskService.getTaskCount(quoteorderEntity.getBussinessChanceId(),currentUser.getId(),3);
            taskCount = taskCount+taskCount1;
        }

        // 报价商品
        List<CrmQuoteOrderCoreSkuDto> crmQuoteorderGoodsList = this.findQuoteOrderGoods(quoteorderId);
        int i = 0;
        for (CrmQuoteOrderCoreSkuDto coreSkuDto : crmQuoteorderGoodsList) {
            List<UserDto> productManager = coreSkuDto.getProductManager();
            if(CollectionUtil.isEmpty(productManager)){
                continue;
            }
            List<Integer> userIds = productManager.stream().map(UserDto::getUserId).collect(Collectors.toList());
            if (userIds.contains(currentUser.getId()) && checkGoodsHasTask(coreSkuDto)){
                i++;
            }
        }
        QuoteTaskTipsDto tipsDto = new QuoteTaskTipsDto();
        tipsDto.setTaskNum(taskCount);
        tipsDto.setProductNum(i);
        return tipsDto;
    }

    private boolean checkGoodsHasTask(CrmQuoteOrderCoreSkuDto coreSkuDto){
        if(coreSkuDto.getIsConsulDeliveryCycle()!=null && coreSkuDto.getIsConsulDeliveryCycle()>0){
            return true;
        }
        if(coreSkuDto.getIsConsulPrice() != null && coreSkuDto.getIsConsulPrice()>0){
            return true;
        }
        if(coreSkuDto.getIsConsulReport() != null && coreSkuDto.getIsConsulReport()>0){
            return true;
        }
        return false;
    }

    @Override
    public QuoteSummaryInfoDto summaryInfo(Integer quoteorderId) {
        QuoteSummaryInfoDto dto = new QuoteSummaryInfoDto();
        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteorderId);
        if (Objects.isNull(crmQuoteOrderDto)){
            return dto;
        }
        List<QuoteorderNeedsEntity> needsList = quoteOrderNeedsService.selectByQuoteorderId(quoteorderId);
        dto.setValidStatus(crmQuoteOrderDto.getValidStatus());
        dto.setRequirementStatus(CollUtil.isEmpty(needsList) ? 0 : 1);
        dto.setRequiredProductNumber(needsList.size());
//        List<CrmQuoteorderGoods> crmQuoteorderGoods = crmQuoteOrderGoodsService.selectByQuoteorderId(quoteorderId);

        List<CrmQuoteOrderCoreSkuDto> quoteOrderGoods = findQuoteOrderGoods(quoteorderId);

        // 授权书相关
        QuoteApplyDto quoteApplyDto = queryQuoteApply(quoteorderId,quoteOrderGoods);
        dto.setQuoteApplyDto(quoteApplyDto);

        if (CollUtil.isEmpty(quoteOrderGoods)){
            return dto;
        }

        List<RQuoteorderNeedsJGoodsEntity> rQuoteorderNeedsJGoodsEntities = rQuoteNeedsJGoodsService.selectByQuoteorderId(quoteorderId);
        // 绑定需求的商品
        List<Long> hasRelagtionGoodsList = rQuoteorderNeedsJGoodsEntities.stream().map(RQuoteorderNeedsJGoodsEntity::getQuoteorderNeedsId).distinct().collect(Collectors.toList());
        int a = hasRelagtionGoodsList.size();
        int b = needsList.size();
        if (b != 0) {
            double result = (double) a / b * 100; // 进行除法运算
            DecimalFormat df = new DecimalFormat("#.#"); // 定义格式，保留一位小数
            String formattedResult = df.format(result); // 格式化结果
            dto.setRequiredSatisfactionRate(formattedResult + "%");
        }

        dto.setQuoteProductNumber(quoteOrderGoods.size());

        List<CrmQuoteOrderCoreSkuDto> exclusiveList = quoteOrderGoods.stream().filter(e -> Objects.equals(e.getGoodsPositionNo(),5)).collect(Collectors.toList());
        dto.setExclusiveProductNumber(CollUtil.isEmpty(exclusiveList) ? 0 : exclusiveList.size());

        List<CrmQuoteOrderCoreSkuDto> needReportedList = quoteOrderGoods.stream().filter(e -> Objects.equals(e.getIsNeedReport(),1)).collect(Collectors.toList());
        dto.setNeedReportedNumber(CollUtil.isEmpty(needReportedList) ? 0 : needReportedList.size());

        List<CrmQuoteOrderCoreSkuDto> dealPrice = quoteOrderGoods.stream().filter(e -> Objects.nonNull(e.getDealerPrice())).collect(Collectors.toList());

        dto.setAuditedProductNumber(CollUtil.isEmpty(dealPrice) ? 0 : dealPrice.size());

        CrmQuoteOrderCoreSkuDto coreSkuDto = quoteOrderGoods.stream()
                .filter(e->StrUtil.isNotBlank(e.getExpectDeliveryTime()))
                .max(Comparator.comparing(e -> Integer.valueOf(e.getExpectDeliveryTime())))
                .orElse(null);
        if (Objects.nonNull(coreSkuDto)){
            dto.setEstimatedDeliveryTime(coreSkuDto.getExpectDeliveryTime());
        }

        dto.setTotalQuotedAmount(calculateTotalPrice(quoteOrderGoods));
        return dto;
    }

    private  BigDecimal calculateTotalPrice(List<CrmQuoteOrderCoreSkuDto> quoteOrderGoods) {
        return quoteOrderGoods.stream()
                .map(goods -> Optional.ofNullable(goods.getSalePrice())
                        .map(price -> price.multiply(BigDecimal.valueOf(goods.getNum())))
                        .orElse(null)) // 如果 salePrice 为 null，则返回 null
                .filter(Objects::nonNull) // 过滤掉 null 值
                .reduce(BigDecimal.ZERO, BigDecimal::add); // 求和
    }

    @Override
    public void sendAppChat(Integer businessChanceId) {
        String businessChanceNo = "241142605";
        sendAppMessageCard(businessChanceId,"完成咨询","Ives.hua完成了报价处理工作");
        updateAppChatName(businessChanceId,"【赢单】商机 "+businessChanceNo+"协同支持");

    }

    @Autowired
    private JumpService jumpService;

    @Override
    @Async
    public void sendAppMessageCard(Integer businessChanceId,String title,String description){
        String targetUrl = lxcrmUrl+url+businessChanceId;
        WxCpAppChatMessage wxCpAppChatMessage = new WxCpAppChatMessage();
        wxCpAppChatMessage.setChatId(getChatId(businessChanceId));
        wxCpAppChatMessage.setMsgType("textcard");
        wxCpAppChatMessage.setTitle(title);
        wxCpAppChatMessage.setDescription(description);
        wxCpAppChatMessage.setUrl(jumpService.getjumpUrl(targetUrl, JumpErpTitleEnum.BUSSINESS_CHANCE_DETAIL));
        wxCpAppChatMessage.setBtnTxt("查看详情");
        RestfulResult<Void> send = uacWxUserInfoApiService.send(wxCpAppChatMessage);
        log.info("发送结果：{}",JSON.toJSON(send));
    }


    @Override
    @Async
    public void updateAppChatName(Integer businessChanceId,String newName){
        AppChatReqDto appChatReqDto = new AppChatReqDto();
        appChatReqDto.setChatId(getChatId(businessChanceId));
        appChatReqDto.setName(newName);
        RestfulResult<Void> send = uacWxUserInfoApiService.update(appChatReqDto);
        log.info("修改结果：{}",JSON.toJSON(send));
    }

    @Override
    public CrmQuoteShareDto getQuoteShardInfoById(Integer quoteorderId) {
        return crmQuoteOrderService.getQuoteShardInfoById(quoteorderId);
    }

    @Override
    public Integer getQuoteWithNoSkuInfoById(Integer quoteorderId) {
        return crmQuoteOrderService.getQuoteWithNoSkuInfoById(quoteorderId);
    }

    @Override
    public String checkQuoteSkuCheckStatus(Integer quoteorderId) {
        List<Map<String,Object>> notCheckStatus3List = crmQuoteOrderService.checkSkuCheckStatusForQuoteShard(quoteorderId);
        if(CollectionUtils.isEmpty(notCheckStatus3List)){
            return null;
        }
        String result = notCheckStatus3List.stream()
                .map(map -> "" + map.get("skuNo")) //skuNo拼接一个空字符串
                .collect(Collectors.joining("/"));  // 用 "/" 拼接所有值
        return result;
    }

    @Override
    @Transactional
    public String importNeedsExcelFile(MultipartFile file, Integer quoteorderId, CurrentUser currentUser) throws Exception{
        log.info("导入需求excel文件,报价单：{},文件名：{}",quoteorderId,file.getOriginalFilename());
        checkValidQuote(quoteorderId);
        if (Objects.isNull(quoteorderId)){
            throw new ServiceException("报价单id不能为空");
        }

        // 文件类型检查
        String fileName = file.getOriginalFilename().toLowerCase();
        if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls")) {
            throw new ServiceException("文件格式错误，请使用模板整理数据后重新导入");
        }

        // 文件大小检查
        if (file.getSize() >= 5 * 1024 * 1024) { // 5MB
            throw new ServiceException("请上传文件大小不超过5M，数据不超过1000行的表格文件");
        }

        List<NeedsImportInfoDto> newList = ListUtils.newArrayList();
        List<NeedsImportInfoDto> repeatList = ListUtils.newArrayList();
        Set<String> productNeedsSet = new HashSet<>();
        AtomicInteger row = new AtomicInteger(1);
        // 重复
        EasyExcel.read(file.getInputStream(), NeedsImportInfoDto.class, new NeedsImportReadListener<NeedsImportInfoDto>(dataList ->
                dataList.forEach(data -> {
                    row.getAndIncrement();
                    if(row.get() > MAX_DATA_LENGTH){
                        throw new ServiceException(StrUtil.format("请上传文件大小不超过5M，数据不超过1000行的表格文件", row.get()));
                    }
                    if (StringUtils.isNotBlank(data.getProductNeeds())){
                        if (productNeedsSet.add(data.getProductNeeds())){
                            newList.add(data);
                        }else {
                            repeatList.add(data);
                        }
                    }
                }))).sheet().doRead();
        if(CollectionUtil.isEmpty(newList)){
            return "已添加0条，失败0条，有0条重复自动跳过";
        }
        log.info("【导入】过滤后有{}条数据待处理：{}",newList.size(),JSON.toJSON(newList));
//      // 判断是否是第一次导入
        List<QuoteorderNeedsEntity> oldList = quoteOrderNeedsService.selectByQuoteorderId(quoteorderId);
        if(CollectionUtil.isNotEmpty(oldList)){//非第一次导入的情况，将本将导入的需求里的，去掉已存在的需求。
            Iterator<NeedsImportInfoDto> iterator = newList.iterator();
            while (iterator.hasNext()) {
                NeedsImportInfoDto newItem = iterator.next();
                boolean existsInOldList = oldList.stream()
                        .anyMatch(oldItem -> oldItem.getProductNeeds().equals(newItem.getProductNeeds().trim()));
                if (existsInOldList) {
                    repeatList.add(newItem); // 假设repeatList 已经定义
                    iterator.remove(); // 从 newList 中移除
                }
            }
        }


//        Date date = new Date();
//
//        int commonSize = Math.min(oldList.size(), newList.size());
//        // 找出新老需求相同行
//        for (int i = 0; i < Math.min(oldList.size(), newList.size()); i++) {
//            // 直接更新老需求
//            NeedsImportInfoDto newDto = newList.get(i);
//            QuoteorderNeedsEntity oldDto = oldList.get(i);
//            QuoteorderNeedsEntity updateDto = new QuoteorderNeedsEntity();
//            updateDto.setQuoteorderNeedsId(oldDto.getQuoteorderNeedsId());
//            updateDto.setExtraNeeds(newDto.getExtraNeeds());
//            updateDto.setNumNeeds(newDto.getNumNeeds());
//            updateDto.setProductNeeds(newDto.getProductNeeds());
//            updateDto.setBudgetaryNeeds("");
//            updateDto.setDistributeBudget(newDto.getDistributeBudget());
//            updateDto.setTerminalBudget(newDto.getTerminalBudget());
//            updateDto.setHeadUserId(0);
//            updateDto.setModTime(date);
//            updateDto.setAddTime(date);
//            log.info("【导入】更新需求：{}",JSON.toJSON(updateDto));
//            quoteOrderNeedsService.updateByPrimaryKeySelective(updateDto);
//        }
//
//        // 新列表多出的行
//        List<NeedsImportInfoDto> extraNewList = ListUtils.newArrayList();
//        for (int i = commonSize; i < newList.size(); i++) {
//            extraNewList.add(newList.get(i));
//        }

        if (CollUtil.isNotEmpty(newList)) {
            for (NeedsImportInfoDto dto : newList) {
                ValidatorUtils.validate(dto, DefaultGroup.class);
            }
        }

        if (CollUtil.isNotEmpty(newList)){
            // 先插入需求
            QuoteorderNeedsEntity quoteorderNeedsEntity = new QuoteorderNeedsEntity();
            quoteorderNeedsEntity.setCreator(currentUser.getId());
            quoteorderNeedsEntity.setCreatorName(currentUser.getUsername());
            quoteorderNeedsEntity.setQuoteorderId(quoteorderId);
            log.info("【导入】新增需求,共{}条：{}",newList.size(),JSON.toJSON(newList));
            int insert = quoteOrderNeedsService.batchInsert(newList, quoteorderNeedsEntity);

            // 判断是否是在添加商品后导入需求
//            List<CrmQuoteorderGoods> crmQuoteorderGoods = crmQuoteOrderGoodsService.selectByQuoteorderId(quoteorderId);
//            List<QuoteorderNeedsEntity> quoteorderNeedsEntities = quoteOrderNeedsService.selectByQuoteorderId(quoteorderId);
//            List<RQuoteorderNeedsJGoodsEntity> rQuoteorderNeedsJGoodsEntities = rQuoteNeedsJGoodsService.selectByQuoteorderId(quoteorderId);
//            if (CollUtil.isNotEmpty(crmQuoteorderGoods) && CollUtil.isEmpty(rQuoteorderNeedsJGoodsEntities)){
//                // 有商品无绑定关系，则依次绑定关系
//                int min = Math.min(crmQuoteorderGoods.size(), quoteorderNeedsEntities.size());
//                for (int i = 0; i < min; i++) {
//                    RQuoteorderNeedsJGoodsEntity rQuoteorderNeedsJGoodsEntity = new RQuoteorderNeedsJGoodsEntity();
//                    rQuoteorderNeedsJGoodsEntity.setQuoteorderNeedsId(quoteorderNeedsEntities.get(i).getQuoteorderNeedsId());
//                    rQuoteorderNeedsJGoodsEntity.setQuoteorderGoodsId(crmQuoteorderGoods.get(i).getQuoteorderGoodsId());
//                    rQuoteorderNeedsJGoodsEntity.setQuoteorderId(quoteorderId);
//                    rQuoteNeedsJGoodsService.insertSelective(rQuoteorderNeedsJGoodsEntity, currentUser);
//                }
//
//            }
        }

        // 老列表多出的元素
//        List<QuoteorderNeedsEntity> extraOldList = ListUtils.newArrayList();
//        for (int i = commonSize; i < oldList.size(); i++) {
//            // 删除老需求以及绑定关系
//            extraOldList.add(oldList.get(i));
//        }
//        if (CollUtil.isNotEmpty(extraOldList)) {
//            log.info("【导入】删除需求和关系，共{}条：{}",extraOldList.size(),JSON.toJSON(extraOldList));
//            quoteOrderNeedsService.deleteByQuoteorderNeedsId(extraOldList);
//            rQuoteNeedsJGoodsService.batchDeleteByQuoteorderNeedsId(extraOldList);
//        }
        return "已添加"+newList.size()+"条，失败0条，有"+repeatList.size()+"条重复自动跳过";
    }

    /**
     * 获取客户信息
     * @param quoteorderId
     * @return
     */
    private TraderCustomerInfoVo getInfoVo(Integer quoteorderId) {
        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteorderId);
        if (Objects.isNull(crmQuoteOrderDto)){
            throw new ServiceException("报价单不存在");
        }
        TraderCustomerInfoVo traderCustomerInfoVo = traderCustomerBaseService.getTraderCustomerInfoVo(crmQuoteOrderDto.getTraderId());
        if (Objects.isNull(traderCustomerInfoVo)){
            throw new ServiceException("客户信息不存在");
        }
        return traderCustomerInfoVo;
    }

    @Autowired
    private QuoteExportIgnoreConvertor quoteExportIgnoreConvertor;

    private String checkNullValue(String obj){
        if(StringUtils.isEmpty(obj)){
            return StringUtils.EMPTY;
        }
        return obj;
    }

    private List<String> checkNullValue(List<String> strList){
        if(CollectionUtil.isEmpty(strList)){
            return new ArrayList<>();
        }
        return strList;
    }

    @Override
    public void exportExcel(HttpServletResponse response,Integer quoteorderId,CurrentUser currentUser) throws IOException {
        // 日志
        OperationLogDto logDto = new OperationLogDto();
        logDto.setBizId(quoteorderId);
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        operationLogApiService.save(logDto, BizLogEnum.EXPORT_QUOTATION);

        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteorderId);
        BusinessChanceDto businessChanceDto = new BusinessChanceDto();
        businessChanceDto.setBussinessChanceId(crmQuoteOrderDto.getBussinessChanceId());
        BusinessChanceDto businessChanceData = businessChanceService.selectOne(businessChanceDto);
        // 归属销售
        UserDto userDto = userApiService.searchByUserIdFromUac(businessChanceData.getUserId());

        UserDto userErp = userApiService.getUserBaseInfo(businessChanceData.getUserId());

        // 构建表头
        QuoteOrderGoodsHeadExportVo quoteOrderGoodsHeadExportVo = new QuoteOrderGoodsHeadExportVo();
        quoteOrderGoodsHeadExportVo.setLogo("logo.jpg");
        quoteOrderGoodsHeadExportVo.setConcatName(Optional.ofNullable(userDto.getRealName()).filter(s -> !s.isEmpty()).orElse("-"));
        quoteOrderGoodsHeadExportVo.setConcatPhone(Optional.ofNullable(userErp.getMobile()).filter(s -> !s.isEmpty()).orElse("-"));
        quoteOrderGoodsHeadExportVo.setConcatEmail(Optional.ofNullable(userDto.getEmail()).filter(s -> !s.isEmpty()).orElse("-"));
        quoteOrderGoodsHeadExportVo.setQuoteNo(crmQuoteOrderDto.getQuoteorderNo());
        quoteOrderGoodsHeadExportVo.setQuoteDate(DateUtil.format(new Date(), "yyyy-MM-dd"));

        // 判断是否有需求
        List<QuoteorderNeedsEntity> needsList = quoteOrderNeedsService.selectByQuoteorderId(quoteorderId);
        
        List<QuoteOrderGoodsExportVo> quoteOrderGoodsExportVos = new ArrayList<>();
        if (CollUtil.isNotEmpty(needsList)){
            quoteOrderGoodsExportVos = crmQuoteOrderService.selectQuoteOrderGoodsExportVo(quoteorderId,true);
        }else {
            quoteOrderGoodsExportVos = crmQuoteOrderService.selectQuoteOrderGoodsExportVo(quoteorderId,false);
        }

        // 构建表尾
        quoteOrderGoodsHeadExportVo.setTotalAmount(
        	    quoteOrderGoodsExportVos.stream()
        	        .filter(e -> e.getSalePrice() != null && e.getNum() != null) 
        	        .map(e -> {
        	            BigDecimal num = Optional.ofNullable(e.getNum()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO);
        	            BigDecimal salePrice = Optional.ofNullable(e.getSalePrice()).orElse(BigDecimal.ZERO);
        	            return num.multiply(salePrice); 
        	        })
        	        .reduce(BigDecimal.ZERO, BigDecimal::add)
        	);
        quoteOrderGoodsHeadExportVo.setSkuCount((int) quoteOrderGoodsExportVos.stream().map(QuoteOrderGoodsExportVo::getSkuName).distinct().count());
        quoteOrderGoodsHeadExportVo.setProductCount(
        	    quoteOrderGoodsExportVos.stream()
        	        .mapToInt(e -> Optional.ofNullable(e.getNum()).orElse(0)) 
        	        .sum()
        	);

        List<String> skuNos=quoteOrderGoodsExportVos.stream().map(QuoteOrderGoodsExportVo::getSkuNo).distinct().collect(Collectors.toList());
        SkuPriceInfoBatchQueryDto param=new SkuPriceInfoBatchQueryDto();

        param.setSkuNos(skuNos);
        Map<String, SkuPriceInfoDetailResponseDto> skuPriceInfoMap = new HashMap<>();
        try {
            RestfulResult<List<SkuPriceInfoDetailResponseDto>> result = remotePriceApiService.batchFindSkuPriceInfoBySkuNos(param);
            if (result.isSuccess()) {
                //result.getData()转成map
                // skuNo作为key，SkuPriceInfoDetailResponseDto作为value
                skuPriceInfoMap = result.getData().stream().collect(Collectors.toMap(SkuPriceInfoDetailResponseDto::getSkuNo, Function.identity()));
            }
        }catch (Exception e){
            log.error("报价单导出获取市场价异常{}",JSON.toJSONString(skuNos),e);
        }

        int i = 1;
        for (QuoteOrderGoodsExportVo quoteOrderGoodsExportVo : quoteOrderGoodsExportVos) {
            quoteOrderGoodsExportVo.setId(i);
            i++;
            String distributeBudget = quoteOrderGoodsExportVo.getDistributeBudget();
            String terminalBudget = quoteOrderGoodsExportVo.getTerminalBudget();
            // 合并+回车
            if (StrUtil.isNotBlank(distributeBudget) && StrUtil.isNotBlank(terminalBudget)){
                quoteOrderGoodsExportVo.setBudgetaryNeeds("经销预算(元)："+distributeBudget+"\n"+"终端预算(元)："+terminalBudget);
            }else if (StrUtil.isNotBlank(distributeBudget)){
                quoteOrderGoodsExportVo.setBudgetaryNeeds("经销预算(元)："+distributeBudget);
            }else if (StrUtil.isNotBlank(terminalBudget)){
                quoteOrderGoodsExportVo.setBudgetaryNeeds("终端预算(元)："+terminalBudget);
            }
            if(Objects.nonNull(quoteOrderGoodsExportVo.getSalePrice())) {
            	quoteOrderGoodsExportVo.setTotal(BigDecimal.valueOf(quoteOrderGoodsExportVo.getNum()).multiply(quoteOrderGoodsExportVo.getSalePrice()));
            }else {
            	quoteOrderGoodsExportVo.setTotal(BigDecimal.ZERO);
            }
            if (StrUtil.isNotBlank(quoteOrderGoodsExportVo.getSkuNo())){
                CoreSkuInfoDto coreSkuInfo = coreSkuService.getCoreSkuInfoDtoBySkuNo(quoteOrderGoodsExportVo.getSkuNo(),null);
                quoteOrderGoodsExportVo.setMainParameter(String.join("\n", checkNullValue(coreSkuInfo.getMainParam())));
                quoteOrderGoodsExportVo.setUseLife(coreSkuInfo.getUseLife());
                quoteOrderGoodsExportVo.setUnitName(coreSkuInfo.getUnitName());
                List<String> userNames = coreSkuInfo.getProductManager().stream().map(UserDto::getUsername).collect(Collectors.toList());
                quoteOrderGoodsExportVo.setProductManager(String.join("\n",checkNullValue(userNames)));
                quoteOrderGoodsExportVo.setIsInstall(Objects.equals(coreSkuInfo.getIsInstall(),1) ? "可安装" : "不可安装");
                quoteOrderGoodsExportVo.setReportStatusStr(ReportStatusEnum.getDescByStatus(quoteOrderGoodsExportVo.getReportStatus()));
                quoteOrderGoodsExportVo.setWarrantyInfo(coreSkuInfo.getWarrantyInfo());
                quoteOrderGoodsExportVo.setCompanyName(coreSkuInfo.getCompanyName());
                quoteOrderGoodsExportVo.setRegistrationNumber(coreSkuInfo.getRegistrationNumber());
                if(skuPriceInfoMap.containsKey(quoteOrderGoodsExportVo.getSkuNo())
                        &&skuPriceInfoMap.get(quoteOrderGoodsExportVo.getSkuNo()).getVerifyStatus()==2 //审核通过的价格
                        &&skuPriceInfoMap.get(quoteOrderGoodsExportVo.getSkuNo()).getMarketPrice() != null // 没有被禁用
                        &&skuPriceInfoMap.get(quoteOrderGoodsExportVo.getSkuNo()).getMarketPrice().compareTo(BigDecimal.ZERO)>0){ //价格大于0
                    SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = skuPriceInfoMap.get(quoteOrderGoodsExportVo.getSkuNo());
                    quoteOrderGoodsExportVo.setMarketPrice(skuPriceInfoDetailResponseDto.getMarketPrice());
                }
            }
        }


        // todo 导出：需求模式
        // EasyExcel文档：https://easyexcel.opensource.alibaba.com/docs/current/quickstart/fill
        if (CollUtil.isNotEmpty(needsList)){
            log.info("【导出】有需求，导出需求模式");
            // 有需求为需求模式
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String fileName = URLEncoder.encode(this.getExportName(quoteorderId), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            InputStream resourceAsStream = QuoteFacadeImpl.class.getClassLoader().getResourceAsStream("报价单模版-需求模式.xlsx");

            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(getHorizontalCellStyleStrategy()).withTemplate(resourceAsStream).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                // 列表数据
                excelWriter.fill(quoteOrderGoodsExportVos,fillConfig, writeSheet);
                excelWriter.fill(quoteOrderGoodsHeadExportVo, writeSheet);
            }
        }else {
            // todo 导出：普通模式
            log.info("【导出】无需求，导出普通模式");
            // 普通模式
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String fileName = URLEncoder.encode(this.getExportName(quoteorderId), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            InputStream resourceAsStream = QuoteFacadeImpl.class.getClassLoader().getResourceAsStream("报价单模版-普通模式.xlsx");

            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(getHorizontalCellStyleStrategy()).withTemplate(resourceAsStream).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                // 列表数据
                excelWriter.fill(quoteOrderGoodsExportVos,fillConfig, writeSheet);
                excelWriter.fill(quoteOrderGoodsHeadExportVo, writeSheet);
                
            }
        }
    }

    /**
     * 单元格样式策略
     */
    public static HorizontalCellStyleStrategy getHorizontalCellStyleStrategy() {
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置边框
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.NONE);

        // 配置字体
        WriteFont contentWriteFont = new WriteFont();
        // 字体
//        contentWriteFont.setFontName("宋体");
        // 字体大小
//        contentWriteFont.setFontHeightInPoints(fontHeightInPoints);
        // 设置加粗
        contentWriteFont.setBold(false);
        contentWriteCellStyle.setWriteFont(contentWriteFont);

        // 【水平居中需要使用以下两行】
        // 设置文字左右居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        // 设置文字上下居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

        // 样式策略
        return new HorizontalCellStyleStrategy(null, contentWriteCellStyle);
    }

    private String getExportName(Integer quoteorderId){
        // 客户名称+导出日期+商机编号
        String traderName = getInfoVo(quoteorderId).getTraderName();
        String format = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String businessChanceNo = crmQuoteOrderService.selectBusinessChanceByQuoteorderId(quoteorderId);
        return traderName+"报价单" + format + "("+businessChanceNo+")";
    }

    @Override
    public QuoteSyncResponseDto sync(QuoteSyncRequestDto quoteSyncRequestDto, CurrentUser currentUser) {
        Integer quoteorderId = quoteSyncRequestDto.getQuoteorderId();
        String quoteKey = getQuoteKey(quoteorderId);
        // redis1格式：key:报价单id  value:用户id  score:时间戳
        // 过期时间：60s
        RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(quoteKey);
        scoredSortedSet.add(quoteSyncRequestDto.getTimestamp(),currentUser.getUsername());
        scoredSortedSet.expire(60, TimeUnit.SECONDS);

        // 正在编辑：近10秒
        double min = quoteSyncRequestDto.getTimestamp() - 10 * 1000;
        double max = quoteSyncRequestDto.getTimestamp();
        // 转成用户集合
        List<String> editUserName = scoredSortedSet.valueRange(min, true, max, true)
                .stream()
                .filter(e->!e.equals(currentUser.getUsername()))
                .map(Objects::toString)
                .collect(Collectors.toList());

        // redis2格式：key:报价单id+用户id  value:位置坐标
        // 过期时间：10s
        if (StrUtil.isNotBlank(quoteSyncRequestDto.getLocal())){
            String localKey = getLocalKey(quoteorderId,currentUser.getUsername());
            RBucket<Object> bucket = redissonClient.getBucket(localKey);
            bucket.set(quoteSyncRequestDto.getLocal(), 10, TimeUnit.SECONDS);
            log.info("上报成功:{}",localKey);
        }

        if (Objects.isNull(quoteorderId)){
            throw new ServiceException("报价单id不能为空");
        }

        List<QuoteSyncResponseDto.EditUserDto> editUserDtoList = new ArrayList<>();
        for (String userName : editUserName) {
            Double score = scoredSortedSet.getScore(userName);
            QuoteSyncResponseDto.EditUserDto editUserDto = new QuoteSyncResponseDto.EditUserDto();
            editUserDto.setUserName(userName);
            editUserDto.setTime(score == null ? 0 : score);

            String editLocalKey = getLocalKey(quoteorderId,userName);
            RBucket<Object> editBucket = redissonClient.getBucket(editLocalKey);
            if (Objects.isNull(editBucket) || Objects.isNull(editBucket.get())){
                continue;
            }
            editUserDto.setLocal(editBucket.get().toString());
            editUserDtoList.add(editUserDto);
        }
        // 组装返回
        QuoteSyncResponseDto responseDto = new QuoteSyncResponseDto();
        responseDto.setTimestamp(quoteSyncRequestDto.getTimestamp());
        // 发现相同local
        List<QuoteSyncResponseDto.EditUserDto> editUserDtos = new ArrayList<>(editUserDtoList
                .stream()
                .filter(e -> Objects.nonNull(e.getTime()))
                .collect(Collectors.toMap(
                        QuoteSyncResponseDto.EditUserDto::getLocal, // 根据 local 字段去重
                        user -> user,
                        (user1, user2) -> user1.getTime() >= user2.getTime() ? user1 : user2 // 选择 time 最大的对象
                ))
                .values());// 收集为 List.

        responseDto.setEditUserLocal(editUserDtos);
        return responseDto;
    }

    @Override
    public void updateQuoteGoods(QuoteGoodsUpdateRequestDto updateRequestDto, CurrentUser currentUser) {
        // 更新商品
//        crmQuoteOrderGoodsService.updateSelective(updateRequestDto, currentUser);
        // 绑定关系或更新关系
        this.insertOrUpdateBind(updateRequestDto.getQuoteorderNeedsId(), updateRequestDto.getQuoteorderGoodsId(), currentUser);
    }

    private void insertOrUpdateBind(Long quoteOrderGoodsNeedsId, Integer quoteorderGoodsId, CurrentUser currentUser) {
        // 查询是否有绑定关系
        RQuoteorderNeedsJGoodsEntity rQuoteorderNeedsJGoodsEntity = rQuoteNeedsJGoodsService.selectByQuoteGoodsId(quoteorderGoodsId);
        if (Objects.nonNull(rQuoteorderNeedsJGoodsEntity) && !rQuoteorderNeedsJGoodsEntity.getQuoteorderNeedsId().equals(quoteOrderGoodsNeedsId)){
            // 有绑定关系，不一致，更新关系
            rQuoteNeedsJGoodsService.updateByPrimaryKeySelective(rQuoteorderNeedsJGoodsEntity.getRQuoteorderNeedsJGoodsId(),quoteOrderGoodsNeedsId,currentUser);
            return;
        }
        RQuoteorderNeedsJGoodsEntity insertEntity = new RQuoteorderNeedsJGoodsEntity();
//        insertEntity.setQuoteorderId();
//         绑定关系
//        rQuoteNeedsJGoodsService.insertSelective(quoteOrderGoodsNeedsId, quoteorderGoodsId, currentUser);
    }

    /**
     * 更新和关系绑定
     * @param insertRequestDto
     * @param currentUser
     */
    @Override
    @Transactional
    public void insertQuoteGoods(QuoteGoodsInsertRequestDto insertRequestDto, CurrentUser currentUser,boolean checkRepeat) {
        log.info("更新和绑定关系：{}",JSON.toJSON(insertRequestDto));
        if(checkRepeat){
            // 判断是否存在
            List<CrmQuoteorderGoods> quoteorderGoodsList = crmQuoteOrderGoodsService.selectByQuoteorderId(insertRequestDto.getQuoteorderId());
            List<String> goodsList = quoteorderGoodsList.stream().map(CrmQuoteorderGoods::getSku).distinct().collect(Collectors.toList());
            if (goodsList.contains(insertRequestDto.getSku())){
                throw new ServiceException("该商品已存在"+insertRequestDto.getSku());
            }
        }

        // 新增报价商品
        CrmQuoteorderGoods crmQuoteorderGoods = crmQuoteOrderGoodsService.insertSelectiveDto(insertRequestDto, currentUser);
        if (Objects.nonNull(insertRequestDto.getQuoteorderNeedsId()) && insertRequestDto.getQuoteorderNeedsId() > 0L){
            // 绑定关系
            RQuoteorderNeedsJGoodsEntity insertEntity = new RQuoteorderNeedsJGoodsEntity();
            insertEntity.setQuoteorderId(crmQuoteorderGoods.getQuoteorderId());
            insertEntity.setQuoteorderNeedsId(insertRequestDto.getQuoteorderNeedsId());
            insertEntity.setQuoteorderGoodsId(crmQuoteorderGoods.getQuoteorderGoodsId());
            rQuoteNeedsJGoodsService.insertSelective(insertEntity, currentUser);
        }

        // 日志
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        params.put("skuNo",crmQuoteorderGoods.getGoodsName());
        logDto.setBizId(crmQuoteorderGoods.getQuoteorderId());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        operationLogApiService.save(logDto, BizLogEnum.ADD_PRODUCT);
    }

    @Override
    public void insertOrUpdate(QuoteGoodsRequestDto quoteGoodsRequestDto, CurrentUser currentUser) {

        checkValidQuote(quoteGoodsRequestDto.getQuoteorderId());

        // 编辑商品行
        if (Objects.nonNull(quoteGoodsRequestDto.getQuoteorderGoodsId())){
            QuoteGoodsUpdateRequestDto dto = quoteGoodsUpdateConvertor.toDto(quoteGoodsRequestDto);
            // 修改数量
            tryUpdateNum(dto,currentUser);
            // 修改价格
            tryUpdatePrice(dto,currentUser);
            // 修改货期
            tryUpdateDeliveryCycle(dto,currentUser);
            return;
        }
        Integer needsUserId = quoteGoodsRequestDto.getNeedsUserId();
        Long quoteorderNeedsId = quoteGoodsRequestDto.getQuoteorderNeedsId();
        if (Objects.nonNull(needsUserId) && Objects.nonNull(quoteorderNeedsId)){
            // 更新需求负责人
            QuoteorderNeedsEntity updateDto = new QuoteorderNeedsEntity();
            updateDto.setQuoteorderNeedsId(quoteorderNeedsId);
            updateDto.setHeadUserId(needsUserId);
            log.info("更新需求负责人:{}",JSON.toJSON(updateDto));
            quoteOrderNeedsService.updateByPrimaryKeySelective(updateDto);
            return;
        }
        // 添加insert
//        QuoteGoodsInsertRequestDto dto = quoteGoodsInsertConvertor.toDto(quoteGoodsRequestDto);
//        this.insertQuoteGoods(dto,currentUser);
    }

    @Override
    public void updateReportStatus(QuoteGoodsRequestDto quoteGoodsRequestDto, CurrentUser currentUser) {

        checkValidQuote(quoteGoodsRequestDto.getQuoteorderId());

        if (Objects.isNull(quoteGoodsRequestDto.getQuoteorderGoodsId())){
            throw new ServiceException("quoteorderGoodsId必传");
        }
        QuoteGoodsUpdateRequestDto dto = quoteGoodsUpdateConvertor.toDto(quoteGoodsRequestDto);
        // 修改报备状态
        tryUpdateReportStatus(dto,currentUser);
    }


    private void tryUpdateReportStatus(QuoteGoodsUpdateRequestDto dto, CurrentUser currentUser) {
        Integer reportStatus = dto.getReportStatus();
        if (Objects.isNull(reportStatus)){
            log.info("跳过报价状态更新");
            return;
        }
        QuoteGoodsUpdateLockDto updateLockDto = new QuoteGoodsUpdateLockDto();
        updateLockDto.setReportStatus(dto.getReportStatus());
        updateLockDto.setOldReportStatus(dto.getOldReportStatus());
        updateLockDto.setQuoteorderGoodsId(dto.getQuoteorderGoodsId());
        updateLockDto.setReportComments(dto.getReportComments());
        int i = crmQuoteOrderGoodsService.updateReportStatus(updateLockDto, currentUser);
        if (i == 0){
            log.info("协同更新报备状态失败");
            throw new ServiceException("更新报备状态失败");
        }

        // 日志
        String reportStatusStr;
        if (Objects.equals(reportStatus,2)){
            reportStatusStr = "报备成功";
        }else if (Objects.equals(reportStatus,0)){
            reportStatusStr = "无需报备";
        }else {
            reportStatusStr = "报备失败";
        }
        // 日志
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        if(StringUtils.isNotBlank(dto.getSku())){
            CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(dto.getQuoteorderGoodsId());
            params.put("skuNo",goods.getGoodsName());//CRM-661 调整为商品名
        }else{
            CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(dto.getQuoteorderGoodsId());
            params.put("skuNo",goods.getGoodsName());
        }
        params.put("result",reportStatusStr);
        logDto.setBizId(dto.getQuoteorderId());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        operationLogApiService.save(logDto, BizLogEnum.MODIFY_REPORT_STATUS);
    }

    private void tryUpdateDeliveryCycle(QuoteGoodsUpdateRequestDto dto, CurrentUser currentUser) {
        String deliveryCycle = dto.getDeliveryCycle();
        if (Objects.isNull(deliveryCycle)){
            log.info("跳过货期更新");
            return;
        }
        QuoteGoodsUpdateLockDto updateLockDto = new QuoteGoodsUpdateLockDto();
        updateLockDto.setDeliveryCycle(dto.getDeliveryCycle());
        updateLockDto.setOldDeliveryCycle(dto.getOldDeliveryCycle());
        updateLockDto.setQuoteorderGoodsId(dto.getQuoteorderGoodsId());
        int i = crmQuoteOrderGoodsService.updateDeliveryCycle(updateLockDto, currentUser);
        if (i == 0){
            log.error("协同更新报价单货期失败，报价商品id:{}",dto.getQuoteorderGoodsId());
            throw new ServiceException("更新货期失败");
        }

        // 日志
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        if(StringUtils.isNotBlank(dto.getSku())){
            CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(dto.getQuoteorderGoodsId());
            params.put("skuNo",goods.getGoodsName());//CRM-661 调整为商品名
        }else{
            CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(dto.getQuoteorderGoodsId());
            params.put("skuNo",goods.getGoodsName());
        }
        params.put("deliveryDays",dto.getDeliveryCycle());
        logDto.setBizId(dto.getQuoteorderId());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        operationLogApiService.save(logDto, BizLogEnum.PROCESS_DELIVERY_TIME);
    }

    private void tryUpdateNum(QuoteGoodsUpdateRequestDto dto, CurrentUser currentUser) {
        Integer num = dto.getNum();
        if (Objects.isNull(num)){
            log.info("跳过数量更新");
            return;
        }
        QuoteGoodsUpdateLockDto updateLockDto = new QuoteGoodsUpdateLockDto();
        updateLockDto.setNum(dto.getNum());
        updateLockDto.setOldNum(dto.getOldNum());
        updateLockDto.setQuoteorderGoodsId(dto.getQuoteorderGoodsId());
        int i = crmQuoteOrderGoodsService.updateNum(updateLockDto, currentUser);
        if (i == 0){
            log.error("协同更新报价单数量失败，报价商品id:{}",dto.getQuoteorderGoodsId());
            throw new ServiceException("更新数量失败");
        }

        // 日志
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        if(StringUtils.isNotBlank(dto.getSku())){
            CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(dto.getQuoteorderGoodsId());
            params.put("skuNo",goods.getGoodsName());//CRM-661 调整为商品名
        }else{
            CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(dto.getQuoteorderGoodsId());
            params.put("skuNo",goods.getGoodsName());
        }
//        params.put("skuNo",Optional.ofNullable(dto.getSku()).orElseThrow(()->new ServiceException("sku必传")));
        params.put("num",String.valueOf(dto.getNum()));

        logDto.setBizId(dto.getQuoteorderId());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        operationLogApiService.save(logDto, BizLogEnum.MODIFY_NUMBER);
    }

    private void tryUpdatePrice(QuoteGoodsUpdateRequestDto dto, CurrentUser currentUser) {
        BigDecimal price = dto.getPrice();
        if (Objects.isNull(price)){
            log.info("跳过价格更新");
            return;
        }
        QuoteGoodsUpdateLockDto updateLockDto = new QuoteGoodsUpdateLockDto();
        updateLockDto.setPrice(dto.getPrice());
        updateLockDto.setOldPrice(dto.getOldPrice());
        updateLockDto.setQuoteorderGoodsId(dto.getQuoteorderGoodsId());
        int i = crmQuoteOrderGoodsService.updatePrice(updateLockDto, currentUser);
        if (i == 0){
            log.error("协同更新报价单价格失败，报价商品id:{}",dto.getQuoteorderGoodsId());
            throw new ServiceException("更新价格失败");
        }

        // 日志
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        if(StringUtils.isNotBlank(dto.getSku())){
            CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(dto.getQuoteorderGoodsId());
            params.put("skuNo",goods.getGoodsName());//CRM-661 调整为商品名
        }else{
            CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(dto.getQuoteorderGoodsId());
            params.put("skuNo",goods.getGoodsName());
        }
        params.put("quoteorderGoodsId",dto.getQuoteorderGoodsId()+"");
        params.put("price",price.toPlainString());

        logDto.setBizId(dto.getQuoteorderId());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        operationLogApiService.save(logDto, BizLogEnum.MODIFY_PRICE);
    }

    @Override
    @Transactional
    public void deleteQuoteGoods(QuoteGoodsDeleteRequestDto deleteRequestDto, CurrentUser currentUser) {

        checkValidQuote(deleteRequestDto.getQuoteorderId());

        List<QuoteGoodsDeleteRequestDto.DeleteDetail> deleteDetailList = deleteRequestDto.getDeleteDetailList();
        for (QuoteGoodsDeleteRequestDto.DeleteDetail deleteDetail : deleteDetailList) {

            // 有无商品绑定关系
            CrmQuoteorderGoods crmQuoteorderGoods = crmQuoteOrderGoodsService.selectById(deleteDetail.getQuoteorderGoodsId());
            if (Objects.isNull(crmQuoteorderGoods)){
                log.info("商品不存在，跳过删除");
                return;
            }

            OperationLogDto logDto = new OperationLogDto();
            Map<String,String> params = new HashMap<>();
            if(StringUtils.isNotBlank(deleteDetail.getSku())){
//                params.put("skuNo",StringUtils.isEmpty(deleteDetail.getSku())?" ":deleteDetail.getSku());
                CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(deleteDetail.getQuoteorderGoodsId());
                params.put("skuNo",goods.getGoodsName());//CRM-661 调整为商品名
            }else{
                CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(deleteDetail.getQuoteorderGoodsId());
                params.put("skuNo",goods.getGoodsName());
            }


            // 删除商品
            crmQuoteOrderGoodsService.deleteByPrimaryKey(deleteDetail.getQuoteorderGoodsId(),currentUser);
            // 删除绑定关系
            rQuoteNeedsJGoodsService.deleteByQuoteGoodsId(deleteDetail.getQuoteorderGoodsId(),currentUser);

            // 日志 可能存在空sku, 需要处理，将skuNo按skuName记录即可
//            if (StrUtil.isBlank(deleteDetail.getSku())){
//                log.info("无SKU，跳过记录删除日志");
//                continue;
//            }


            logDto.setBizId(deleteRequestDto.getQuoteorderId());
            logDto.setCreator(currentUser.getId());
            logDto.setCreatorName(currentUser.getUsername());
            logDto.setOperationTime(new Date());
            logDto.setParams(params);
            operationLogApiService.save(logDto, BizLogEnum.REMOVE_PRODUCT);
        }

        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(deleteRequestDto.getQuoteorderId());
        if (Objects.isNull(crmQuoteOrderDto)){
            throw new ServiceException("报价单不存在");
        }
        businessChanceService.updateLevel(crmQuoteOrderDto.getBussinessChanceId());
        
        // 更新SKU删除后的商机商品分类
        updateBusinessCategory(deleteRequestDto.getQuoteorderId());
    }

    @Override
    @Transactional
    public void deleteQuoteNeeds(QuoteNeedsDeleteRequestDto deleteRequestDto, CurrentUser currentUser) {
        checkValidQuote(deleteRequestDto.getQuoteorderId());
        List<Long> needIdList = deleteRequestDto.getDeleteDetailList().stream().map(QuoteNeedsDeleteRequestDto.DeleteDetail::getQuoteorderNeedsId).collect(Collectors.toList());
        List<CrmQuoteorderGoods> crmQuoteorderGoods = crmQuoteOrderGoodsService.selectByQuoteNeedsIdList(needIdList);
        if(CollectionUtils.isNotEmpty(crmQuoteorderGoods)){
            for (CrmQuoteorderGoods deleteDetail : crmQuoteorderGoods) {
                OperationLogDto logDto = new OperationLogDto();
                Map<String,String> params = new HashMap<>();
                if(StringUtils.isNotBlank(deleteDetail.getSku())){
                    CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(deleteDetail.getQuoteorderGoodsId());
                    params.put("skuNo",goods.getGoodsName());//CRM-661 调整为商品名
                }else{
                    CrmQuoteorderGoods goods = crmQuoteOrderGoodsService.selectById(deleteDetail.getQuoteorderGoodsId());
                    params.put("skuNo",goods.getGoodsName());
                }

                // 删除商品
                crmQuoteOrderGoodsService.deleteByPrimaryKey(deleteDetail.getQuoteorderGoodsId(),currentUser);
                // 删除绑定关系
                rQuoteNeedsJGoodsService.deleteByQuoteGoodsId(deleteDetail.getQuoteorderGoodsId(),currentUser);

                // 日志 可能存在空sku
//                if (StrUtil.isBlank(deleteDetail.getSku())){
//                    log.info("无SKU，跳过记录删除日志");
//                    continue;
//                }

                logDto.setBizId(deleteRequestDto.getQuoteorderId());
                logDto.setCreator(currentUser.getId());
                logDto.setCreatorName(currentUser.getUsername());
                logDto.setOperationTime(new Date());
                logDto.setParams(params);
                operationLogApiService.save(logDto, BizLogEnum.REMOVE_PRODUCT);
            }
        }


        List<QuoteorderNeedsEntity> needsEntityList = quoteOrderNeedsService.selectByQuoteNeedsIdList(needIdList);
        if(CollectionUtils.isNotEmpty(needsEntityList)){
            for(QuoteorderNeedsEntity needsEntity : needsEntityList){
                OperationLogDto logDto = new OperationLogDto();
                Map<String,String> params = new HashMap<>();
                params.put("productNeeds",needsEntity.getProductNeeds());
                logDto.setBizId(deleteRequestDto.getQuoteorderId());
                logDto.setCreator(currentUser.getId());
                logDto.setCreatorName(currentUser.getUsername());
                logDto.setOperationTime(new Date());
                logDto.setParams(params);
                operationLogApiService.save(logDto, BizLogEnum.ADD_NEED);
            }
        }
        quoteOrderNeedsService.deleteByQuoteorderNeedsId(needsEntityList);

        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(deleteRequestDto.getQuoteorderId());
        if (Objects.isNull(crmQuoteOrderDto)){
            throw new ServiceException("报价单不存在");
        }
        businessChanceService.updateLevel(crmQuoteOrderDto.getBussinessChanceId());
        
        // 更新商机商品分类
        updateBusinessCategory(deleteRequestDto.getQuoteorderId());
    }

    /**
     * 更新商机的商品分类
     * 在添加/更新/删除报价单SKU后调用此方法，更新商机关联的商品分类
     * 
     * @param quoteorderId 报价单ID
     */
    private void updateBusinessCategory(Integer quoteorderId) {
        try {
            // 获取报价单信息
            CrmQuoteOrderDto quoteOrderDto = crmQuoteOrderService.selectOne(quoteorderId);
            if (Objects.isNull(quoteOrderDto) || Objects.isNull(quoteOrderDto.getBussinessChanceId())) {
                log.error("更新商机分类失败：报价单{}不存在或未关联商机", quoteorderId);
                return;
            }
            
            Integer businessChanceId = quoteOrderDto.getBussinessChanceId();
            
            // 获取报价单中的所有商品
            List<CrmQuoteorderGoods> goods = crmQuoteOrderGoodsService.selectByQuoteorderId(quoteorderId);
            if (CollUtil.isEmpty(goods)) {
                log.info("报价单{}中没有商品，删除商机{}的所有分类", quoteorderId, businessChanceId);
                // 如果报价单中没有商品，则删除商机关联的所有分类
                businessOrderCategoryService.deleteByBusinessIdAndType(businessChanceId, 1); // 1表示商机
                return;
            }
            
            // 获取商品SKU列表
            List<String> skuList = goods.stream()
                .filter(g -> StringUtils.isNotBlank(g.getSku()))
                .map(CrmQuoteorderGoods::getSku)
                .distinct()
                .collect(Collectors.toList());
                
            if (CollUtil.isEmpty(skuList)) {
                log.info("报价单{}中没有有效SKU，删除商机{}的所有分类", quoteorderId, businessChanceId);
                businessOrderCategoryService.deleteByBusinessIdAndType(businessChanceId, 1);
                return;
            }
            
            // 查询SKU关联的分类信息
            List<CoreSkuDto> coreSkuDtos = coreSkuService.getInfoBySkuNos(skuList);
            if (CollUtil.isEmpty(coreSkuDtos)) {
                log.warn("未找到SKU{}对应的分类信息", JSON.toJSONString(skuList));
                return;
            }
            
            // 收集所有有效的分类ID
            Set<Integer> newCategoryIds = coreSkuDtos.stream()
                .filter(sku -> sku.getCategoryId() != null)
                .map(CoreSkuDto::getCategoryId)
                .collect(Collectors.toSet());
                
            if (CollUtil.isEmpty(newCategoryIds)) {
                log.info("SKU未关联有效分类，不对商机{}的分类进行更新", businessChanceId);
                return;
            }
            
            // 获取商机现有分类
            List<BusinessOrderCategoryDto> existingCategories = businessOrderCategoryService.getByBusinessIdAndType(businessChanceId, 1);
            Set<Integer> existingCategoryIds = existingCategories.stream()
                .map(BusinessOrderCategoryDto::getCategoryId)
                .collect(Collectors.toSet());
            
            log.info("商机{}现有分类IDs: {}, 新分类IDs: {}", businessChanceId, JSON.toJSONString(existingCategoryIds), JSON.toJSONString(newCategoryIds));
            
            // 找出需要新增的分类ID (在新集合中存在但不在旧集合中)
            Set<Integer> categoriesToAdd = new HashSet<>(newCategoryIds);
            categoriesToAdd.removeAll(existingCategoryIds);
            
            if (!CollUtil.isEmpty(categoriesToAdd)) {
                // 添加新的分类关联
                for (Integer categoryId : categoriesToAdd) {
                    BusinessOrderCategoryDto categoryDto = new BusinessOrderCategoryDto();
                    categoryDto.setBusinessId(businessChanceId);
                    categoryDto.setBusinessType(1); // 1表示商机
                    categoryDto.setCategoryId(categoryId);
                    businessOrderCategoryService.save(categoryDto);
                }
                log.info("成功添加商机{}的新分类关联，分类IDs: {}", businessChanceId, JSON.toJSONString(categoriesToAdd));
            }
        } catch (Exception e) {
            log.error("更新商机分类时发生异常", e);
        }
    }

    /**
     * @Description: 验证生效条件
     * @param quoteValidRequestDto
     * @param currentUser
     */
    @Transactional
    @Override
    public void validQuote(QuoteValidRequestDto quoteValidRequestDto, CurrentUser currentUser) {
        // 检查生效条件
        validQuoteCheck(quoteValidRequestDto);
        int i = crmQuoteOrderService.updateQuoteOrderValid(quoteValidRequestDto, currentUser);
        if (i != 1){
            throw new ServiceException("更新报价单状态失败");
        }

        if (quoteValidRequestDto.isValid()){
            try {
                CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteValidRequestDto.getQuoteorderId());
                businessChanceService.updateStageAndStageTime(crmQuoteOrderDto.getBussinessChanceId(), BusinessChanceStageEnum.FINAL_SCHEME);
                businessChanceService.updateLevel(crmQuoteOrderDto.getBussinessChanceId());
            }catch (Exception e){
                log.error("更新商机阶段失败",e);
            }
        }

        // 日志
        OperationLogDto logDto = new OperationLogDto();

        logDto.setBizId(quoteValidRequestDto.getQuoteorderId());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        operationLogApiService.save(logDto, quoteValidRequestDto.isValid() ? BizLogEnum.ACTIVATE_QUOTATION  : BizLogEnum.REVOKE_ACTIVATION);
    }

    /**
     * 校验
     * @param quoteValidRequestDto
     */
    private void validQuoteCheck(QuoteValidRequestDto quoteValidRequestDto) {
        // 1、如果报价单中没有sku数据则提示”请确认报价单中已添加产品“
        //2、如果报价单中SKU的”销售单价/数量“没填写，则提示”V156866/V1369756的单价或数量没维护，请维护后生效。“
        List<CrmQuoteorderGoods> crmQuoteorderGoods = crmQuoteOrderGoodsService.selectByQuoteorderId(quoteValidRequestDto.getQuoteorderId());
        if (CollectionUtils.isEmpty(crmQuoteorderGoods) && quoteValidRequestDto.isValid()){
            throw new ServiceException("请确认报价单中已添加产品");
        }
        List<CrmQuoteorderGoods> exData = crmQuoteorderGoods.stream().filter(e ->
                        StrUtil.isNotBlank(e.getSku())
                                &&
                                (
                                 Objects.isNull(e.getPrice())
                                || Objects.isNull(e.getNum())
                                || Objects.equals(e.getNum(), 0))
                                )
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(exData) && quoteValidRequestDto.isValid()){
            throw new ServiceException(exData.stream().map(CrmQuoteorderGoods::getSku).collect(Collectors.joining("/")) + "的单价或数量没维护，请维护后生效。");
        }

        List<CrmQuoteorderGoods> exDataNoSku = crmQuoteorderGoods.stream().filter(e ->
                        StrUtil.isBlank(e.getSku())
                                &&
                            (
                                    Objects.isNull(e.getPrice())
                                            || Objects.isNull(e.getNum())
                                            || Objects.equals(e.getNum(), 0))
                            )
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(exDataNoSku) && quoteValidRequestDto.isValid()){
            throw new ServiceException(exDataNoSku.stream().map(CrmQuoteorderGoods::getGoodsName).collect(Collectors.joining("/")) + "的单价或数量没维护，请维护后生效。");
        }

        // 撤销生效时校验 商机已关闭/已赢单
        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteValidRequestDto.getQuoteorderId());
        BusinessChanceDto chanceDto = new BusinessChanceDto();
        chanceDto.setBussinessChanceId(crmQuoteOrderDto.getBussinessChanceId());
        BusinessChanceDto businessChanceDto = businessChanceService.selectOne(chanceDto);
        List<Integer> statusList = Arrays.asList(BusinessChanceStageEnum.WINNING_ORDER.getCode(), BusinessChanceStageEnum.LOSE_ORDER.getCode());
        if (!quoteValidRequestDto.isValid() && statusList.contains(businessChanceDto.getStage())){
            throw new ServiceException("该报价单对应的商机已关闭/已赢单，不能撤销生效");
        }
    }

    @Override
    public String batchAddQuoteGoods(BatchAddQuoteGoods batchAddQuoteGoods, CurrentUser currentUser) {
        Integer quoteOrderId = Optional.ofNullable(batchAddQuoteGoods)
                .map(BatchAddQuoteGoods::getQuoteorderId)
                .orElseThrow(() -> new ServiceException("报价单ID不能为空"));

        checkValidQuote(quoteOrderId);

        String skuNos = Optional.ofNullable(batchAddQuoteGoods)
                .map(BatchAddQuoteGoods::getSkuNos)
                .orElseThrow(() -> new ServiceException("sku不能为空"));
        // 处理字符串，skus去回车，去空格，且转大写
        String newSkuNos = processSkuNos(skuNos);
        List<String> skuIdList = Arrays.asList(newSkuNos.split("V"))
                .stream()
                .filter(sku->StrUtil.isNotBlank(sku) && sku.length() >=6 )
                .map(sku->sku.substring(0, 6))
                .collect(Collectors.toList());

        List<CrmQuoteorderGoods> allGoods = crmQuoteOrderGoodsService.selectByQuoteorderId(quoteOrderId);
        List<String> allSkus = allGoods.stream().map(CrmQuoteorderGoods::getSku).collect(Collectors.toList());
        List<String> notExistSkus = new ArrayList<>();

        List<CoreSkuInfoDto> coreSkuInfoDtoList = skuIdList.stream()
                .map(skuId -> {
                    CoreSkuInfoDto dto = new CoreSkuInfoDto();
                    dto.setSkuNo("V" + skuId); //将SKU构造出来一个集合
                    return dto;
                })
                .collect(Collectors.toList());

        TraderCustomerInfoVo traderCustomerInfoVo = this.getInfoVo(quoteOrderId);
        Integer customerNature = traderCustomerInfoVo.getCustomerNature();
        coreSkuService.setSaleSkuPrice(coreSkuInfoDtoList,customerNature);
        //将coreSkuInfoDtoList转换为skuNo为key的HashMap，以下需要考虑重复的情况，覆盖
        Map<String,CoreSkuInfoDto> coreSkuInfoDtoMap = new HashMap<>();
        for (CoreSkuInfoDto dto : coreSkuInfoDtoList){
             coreSkuInfoDtoMap.put(dto.getSkuNo(),dto);
        }
        List<String> skuNoList = skuIdList.stream()
                .map(skuId ->   "V" + skuId )
                .collect(Collectors.toList());
        List<CoreSkuDto> coreSkuBos = coreSkuService.getInfoBySkuNos(skuNoList);
        // 使用 Stream 过滤并提取 skuNo
        List<String> skuCheckStatusNot3List = coreSkuBos.stream()
                .filter(dto -> dto.getCheckStatus() == null || dto.getCheckStatus() != 3) // 过滤 checkStatus为null或等于3
                .map(CoreSkuDto::getSku) // 提取 skuNo 字段
                .collect(Collectors.toList()); // 收集为 List<String>
        int success = 0;
        int notStatus3Size = 0;
        List<String> _notStatus3List = new ArrayList<>();

        int existSize = 0;
        List<String> _existList = new ArrayList<>();

        for (String skuNo : skuNoList) {
            try{
                if (allSkus.contains(skuNo)){
                    existSize ++;
                    _existList.add(skuNo);
                    continue;
                }
                if(skuCheckStatusNot3List.contains(skuNo)){//先判断不通过的sku
                    notStatus3Size ++;
                    _notStatus3List.add(skuNo);
                    continue;
                }
                QuerySkuRequestDto requestDto = new QuerySkuRequestDto();
                requestDto.setSkuNo(skuNo);
                requestDto.setQuoteorderId(quoteOrderId);
                CrmCoreSkuInfoDto crmCoreSkuInfoDto = this.queryInfoBySkuNo(requestDto);
                CrmQuoteorderGoods crmQuoteorderGoods = quoteGoodsAddConvertor.toDto(crmCoreSkuInfoDto);
                crmQuoteorderGoods.setQuoteorderId(quoteOrderId);

                // 从coreSkuInfoDtoList取出价格
                //根据skuNo，从coreSkuInfoDtoMap取出
                CoreSkuInfoDto coreTemp = coreSkuInfoDtoMap.get(skuNo);
                if(coreTemp != null && coreTemp.getDealerPrice() != null){
                    crmQuoteorderGoods.setPrice(coreTemp.getDealerPrice());// 设置销售价格，经销商取经销价，终端取终端价，且不需要设置价格改为记录，作为初始的价格
                }
                crmQuoteOrderGoodsService.insertSelective(crmQuoteorderGoods, currentUser);

                // 日志
                OperationLogDto logDto = new OperationLogDto();
                Map<String,String> params = new HashMap<>();
//                params.put("skuNo", Optional.ofNullable(crmQuoteorderGoods.getSku()).orElseThrow(()->new ServiceException("skuNo必传")));
                params.put("skuNo",crmQuoteorderGoods.getGoodsName());
                logDto.setBizId(crmQuoteorderGoods.getQuoteorderId());
                logDto.setCreator(currentUser.getId());
                logDto.setCreatorName(currentUser.getUsername());
                logDto.setOperationTime(new Date());
                logDto.setParams(params);
                operationLogApiService.save(logDto, BizLogEnum.ADD_PRODUCT);
                success++;

            }catch (ServiceException e){
                if (BaseResponseCode.CRM_NO_DATA_NOT_EXIST.getCode() == e.getCode()){
                    log.error("skuNo:{}不存在",skuNo);
                    notExistSkus.add(skuNo);
                    continue;
                }
                log.info("已知异常{},e:{}",skuNo,e.getMessage());
            }catch (Exception e){
                log.info("未知异常:{}",skuNo);
                log.error("未知异常",e);
            }
        }
        int notExistSkuSize = notExistSkus.size();
        StringBuilder sb = new StringBuilder();
        if(existSize >0 || notExistSkuSize>0 || notStatus3Size>0){
            //存在三个变量需要提示导入不成功，existSize已存在，notExistSkuSize SKU不存在，notStatus3Size，未审核通过，
            List<String> errorTips = new ArrayList<>();
            if(existSize>0){
                errorTips.add(existSize + "个产品“"+
                         String.join("/", _existList)+"”已存在，未添加成功；");
            }
            if(notExistSkuSize>0){
                errorTips.add(notExistSkuSize + "个产品“"+
                        String.join("/", notExistSkus)+"”不存在，未添加成功；");
            }
            if(notStatus3Size>0){
                errorTips.add(notStatus3Size + "个产品“"+
                        String.join("/", _notStatus3List)+"”未审核通过，未添加成功；");
            }
            sb.append(String.join("<br/>",errorTips));
            if (success > 0){
                sb.append("<br/>")
                        .append(success)
                        .append("个产品添加成功");
            }
            return sb.toString();
        }

        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteOrderId);
        if (Objects.isNull(crmQuoteOrderDto)){
            throw new ServiceException("报价单不存在");
        }
        businessChanceService.updateLevel(crmQuoteOrderDto.getBussinessChanceId());
        
        // 更新SKU添加后的商机商品分类
        if (success > 0) {
            updateBusinessCategory(quoteOrderId);
        }
        
        return "添加成功";
    }

    @Override
    public String addQuoteGoodsNoSku(CrmQuoteOrderGoodsNoSkuDto crmQuoteOrderGoodsNoSkuDto, CurrentUser currentUser) {
        Integer quoteOrderId = Optional.ofNullable(crmQuoteOrderGoodsNoSkuDto)
                .map(CrmQuoteOrderGoodsNoSkuDto::getQuoteorderId)
                .orElseThrow(() -> new ServiceException("报价单ID不能为空"));
        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteOrderId);
        if (Objects.isNull(crmQuoteOrderDto)){
            throw new ServiceException("报价单不存在");
        }
        checkValidQuote(quoteOrderId);

        try{
//            QuerySkuRequestDto requestDto = new QuerySkuRequestDto();
//            requestDto.setSkuNo(skuNo);
//            requestDto.setQuoteorderId(quoteOrderId);
//            CrmCoreSkuInfoDto crmCoreSkuInfoDto = this.queryInfoBySkuNo(requestDto);
//            CrmQuoteorderGoods crmQuoteorderGoods = quoteGoodsAddConvertor.toDto(crmCoreSkuInfoDto);
//            crmQuoteorderGoods.setQuoteorderId(quoteOrderId);
//            if (allSkus.contains(skuNo)){
//                continue;
//            }
            boolean modifyQuoteorderGoods = false;
            CrmQuoteorderGoodsNoSku crmCoreSkuInfoDtoNoSku = new CrmQuoteorderGoodsNoSku();
            crmCoreSkuInfoDtoNoSku.setQuoteorderId(crmQuoteOrderGoodsNoSkuDto.getQuoteorderId());
            crmCoreSkuInfoDtoNoSku.setGoodsName(crmQuoteOrderGoodsNoSkuDto.getGoodsName());
            crmCoreSkuInfoDtoNoSku.setBrandName(crmQuoteOrderGoodsNoSkuDto.getBrandName());
            crmCoreSkuInfoDtoNoSku.setModel(crmQuoteOrderGoodsNoSkuDto.getModel());
            //将brandId赋值
            crmCoreSkuInfoDtoNoSku.setBrandId(crmQuoteOrderGoodsNoSkuDto.getBrandId());
            crmCoreSkuInfoDtoNoSku.setUnitName(crmQuoteOrderGoodsNoSkuDto.getUnitName());
            crmCoreSkuInfoDtoNoSku.setImgUrl(crmQuoteOrderGoodsNoSkuDto.getImgUrl());
            crmCoreSkuInfoDtoNoSku.setParamContent(crmQuoteOrderGoodsNoSkuDto.getParamContent());
            if (Objects.nonNull(crmQuoteOrderGoodsNoSkuDto.getQuoteorderGoodsId()) && crmQuoteOrderGoodsNoSkuDto.getQuoteorderGoodsId() > 0L){
                //如果是编辑
                modifyQuoteorderGoods = true;
                crmCoreSkuInfoDtoNoSku.setQuoteorderGoodsId(crmQuoteOrderGoodsNoSkuDto.getQuoteorderGoodsId());
                crmQuoteOrderGoodsService.updateQuoteOrderGoodsNoSku(crmCoreSkuInfoDtoNoSku, currentUser);
            }else{
                crmQuoteOrderGoodsService.insertQuoteOrderGoodsNoSku(crmCoreSkuInfoDtoNoSku, currentUser);
                if (Objects.nonNull(crmQuoteOrderGoodsNoSkuDto.getQuoteorderNeedsId()) && crmQuoteOrderGoodsNoSkuDto.getQuoteorderNeedsId() > 0L){
                    // 绑定关系
                    RQuoteorderNeedsJGoodsEntity insertEntity = new RQuoteorderNeedsJGoodsEntity();
                    insertEntity.setQuoteorderId(crmQuoteOrderGoodsNoSkuDto.getQuoteorderId());
                    insertEntity.setQuoteorderNeedsId(crmQuoteOrderGoodsNoSkuDto.getQuoteorderNeedsId());
                    insertEntity.setQuoteorderGoodsId(crmCoreSkuInfoDtoNoSku.getQuoteorderGoodsId());
                    rQuoteNeedsJGoodsService.insertSelective(insertEntity, currentUser);
                }
            }
            // 日志
            OperationLogDto logDto = new OperationLogDto();
            Map<String,String> params = new HashMap<>();
            params.put("skuName",crmQuoteOrderGoodsNoSkuDto.getGoodsName());
            logDto.setBizId(crmQuoteOrderGoodsNoSkuDto.getQuoteorderId());
            logDto.setCreator(currentUser.getId());
            logDto.setCreatorName(currentUser.getUsername());
            logDto.setOperationTime(new Date());
            logDto.setParams(params);
            operationLogApiService.save(logDto, modifyQuoteorderGoods?BizLogEnum.MODIFY_NO_SKU_PRODUCT:BizLogEnum.ADD_NO_SKU_PRODUCT);

        }catch (Exception e){
            log.error("未知异常:{}",crmQuoteOrderGoodsNoSkuDto.getGoodsName(),e);
        }

        businessChanceService.updateLevel(crmQuoteOrderDto.getBussinessChanceId());
        return "1个自定义产品添加成功";
    }

    private List<AppChatUserDto> getConfigUser(){
        List<AppChatUserDto> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(crmSkuAskUserIds)){
            RestfulResult<List<UserInfoDto>> uacResult = uacWxUserInfoApiService.getUserByUserIds(crmSkuAskUserIds);
            if (uacResult.isSuccess() && CollUtil.isNotEmpty(uacResult.getData())){
                List<UserInfoDto> data = uacResult.getData();
                for (UserInfoDto userInfo : data) {
                    AppChatUserDto dto = new AppChatUserDto();
                    dto.setUserId(userInfo.getId());
                    dto.setUserName(userInfo.getDisplayName());
                    dto.setDepartment(userInfo.getMainDepartmentName());
                    dto.setPosition(userInfo.getPositionName());
                    dto.setNumber(userInfo.getJobNumber());
                    dto.setWorkStatus((userInfo.getWorkingStatus() != null && userInfo.getWorkingStatus().equals(5))?"N":"Y");
                    dto.setAliasHeadPicture(userInfo.getAliasHeadPicture());
                    result.add(dto);
                }
            }
        }
        return result;
    }




    @Override
    public List<AppChatUserDto> queryUserByQuoteorderId(Integer quoteorderId) {
        List<CrmQuoteorderGoods> crmQuoteorderGoods = crmQuoteOrderGoodsService.selectByQuoteorderId(quoteorderId);
        List<String> skuNos = crmQuoteorderGoods.stream()
                .filter(e -> StrUtil.isNotBlank(e.getSku()))
                .map(CrmQuoteorderGoods::getSku)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuNos)){
            return getConfigUser();
        }

        // 根据SKU查询归属销售和助理
        UpdatePriceChangeAuditorDto updatePriceChangeAuditorDto = new UpdatePriceChangeAuditorDto();
        updatePriceChangeAuditorDto.setSkuPriceChangeIds(
                skuNos.stream()
                        .map(sku -> sku.substring(1)) // 去掉首个字母
                        .map(Long::parseLong) // 转换为Integer
                        .collect(Collectors.toList())
        );
        // 判断是否核价
        ResultInfo<List<SkuPriceInfoPurchaseDetailDto>>  listResultInfo = remotePriceApiService.listSkuPriceChangeApplyBySkuIds(updatePriceChangeAuditorDto);

        Set<String> hejiaSkuList = new HashSet<>();
        if(CollectionUtils.isNotEmpty(listResultInfo.getData())){
            //skuNos以V+skuId组成
            listResultInfo.getData().stream().forEach(skuPriceInfoPurchaseDetailDto -> {
                hejiaSkuList.add("V"+skuPriceInfoPurchaseDetailDto.getSkuId().intValue());
            });
        }
        // 查询价格中心
        List<ProductManageAndAsistDto> manageAndAssistDtos  = goodsApiService.batchQueryProductManageAndAsist(skuNos);
        if (CollUtil.isEmpty(manageAndAssistDtos)){
            return getConfigUser();
        }
        Map<String,ProductManageAndAsistDto> manageAndAsistDtoMap = manageAndAssistDtos.stream()
                .collect(Collectors.toMap(ProductManageAndAsistDto::getSkuNo, Function.identity()));
       if (MapUtil.isEmpty(manageAndAsistDtoMap)){
           return getConfigUser();
       }

        Set<Integer> resultUserId = new HashSet<>();
        for(String skuNo : skuNos) {
            ProductManageAndAsistDto andAssistDto = manageAndAsistDtoMap.get(skuNo);
            if (Objects.isNull(andAssistDto)){
                log.info("无产品负责人：{}", skuNo);
                continue;
            }
            if(hejiaSkuList.contains(skuNo)){
                //已核价
                resultUserId.add(andAssistDto.getProductAssitUserId());
            }else{//未核价
                resultUserId.add(andAssistDto.getProductManageUserId());
            }
        }

        List<AppChatUserDto> result = new ArrayList<>();
        List<AppChatUserDto> configUser = getConfigUser();
        List<Integer> list = configUser.stream().map(AppChatUserDto::getUserId).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(resultUserId)){
            List<Integer> resultUserIdList = new ArrayList<>(resultUserId);
            RestfulResult<List<UserInfoDto>> uacResult = uacWxUserInfoApiService.getUserByUserIds(resultUserIdList);
            log.info("uacResult:{}",JSON.toJSON(uacResult));
            if (uacResult.isSuccess() && CollUtil.isNotEmpty(uacResult.getData())){
                List<UserInfoDto> data = uacResult.getData();
                for (UserInfoDto userInfo : data) {
                    if (list.contains(userInfo.getId())){
                        continue;
                    }
                    AppChatUserDto dto = new AppChatUserDto();
                    dto.setUserId(userInfo.getId());
                    dto.setUserName(userInfo.getDisplayName());
                    dto.setDepartment(userInfo.getMainDepartmentName());
                    dto.setPosition(userInfo.getPositionName());
                    dto.setNumber(userInfo.getJobNumber());
                    dto.setWorkStatus((userInfo.getWorkingStatus() != null && userInfo.getWorkingStatus().equals(5))?"N":"Y");
                    dto.setAliasHeadPicture(userInfo.getAliasHeadPicture());
                    result.add(dto);
                }
            }
        }
        result.addAll(configUser);
        return result.stream().distinct().collect(Collectors.toList());
    }



    @Value("${departmentId:1873019265,1873025599}")
    private List<Integer> departmentIds;

    @Override
    public List<CrmUserDto> querySupplierUser() {
        List<CrmUserDto> userDtoList = new ArrayList<>();
        for (Integer departmentId : departmentIds){
            RestfulResult<List<UserInfoDto>> userListByDepartmentId = uacWxUserInfoApiService.getUserByDepartment(departmentId);
            if (userListByDepartmentId.isSuccess()) {
                List<UserInfoDto> userInfoDtoList = userListByDepartmentId.getData();
                if (userInfoDtoList != null) {
                    userInfoDtoList.forEach(userInfoDto -> {
                        CrmUserDto userDto = new CrmUserDto();
                        userDto.setUserId(userInfoDto.getId());
                        userDto.setUserName(userInfoDto.getDisplayName());
                        userDto.setHeadPic(userInfoDto.getAliasHeadPicture());
                        userDtoList.add(userDto);
                    });
                }
            }
        }
        // 添加 molly
        crmSkuAskUserIds.stream().forEach(userId->{
            UserDto userDto = userApiService.getUserBaseInfo(userId);
            if(userDto != null && userDto.getUserId() !=null){
                CrmUserDto crmUserDto = new CrmUserDto();
                crmUserDto.setUserId(userDto.getUserId());
                crmUserDto.setUserName(userDto.getUsername());
                crmUserDto.setHeadPic(userDto.getAliasHeadPicture());
                userDtoList.add(crmUserDto);
            }
        });

        List<CrmUserDto> crmUserDtos = userDtoList.stream().distinct().collect(Collectors.toList());
        return crmUserDtos;
    }

    @Override
    public void goConsultationReport(ConsultationReportDto consultationReportDto, CurrentUser currentUser) {
        log.info("当前账号：{}",currentUser.getUsername());
        // 批量更新咨询信息
        crmQuoteOrderGoodsService.batchUpdateConsultationReport(consultationReportDto, currentUser);

        // 发信息
        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(consultationReportDto.getQuoteorderId());
        Integer businessChanceId = crmQuoteOrderDto.getBussinessChanceId();
        BusinessChanceDto dto = new BusinessChanceDto();
        dto.setBussinessChanceId(businessChanceId);
        BusinessChanceDto businessChanceDto = businessChanceService.selectOne(dto);

        Integer businessType = Optional.ofNullable(businessChanceDto)
                .map(BusinessChanceDto::getBusinessType)
                .orElseThrow(() -> new ServiceException("未查询到商机信息"+businessChanceId));

        List<UserDto> userInfoByUserIds = userApiService.getUserInfoByUserIds(consultationReportDto.getToUserList());
        List<String> numberList = userInfoByUserIds.stream().map(UserDto::getNumber).distinct().collect(Collectors.toList());
        SendMessageDto sendMessageDto = new SendMessageDto();
        int mainTaskType ;
        if (Objects.equals(businessType,5703)){
            sendMessageDto.setType("综合询价");
            mainTaskType = 3;
        }else{
            sendMessageDto.setType("单品询价");
            mainTaskType = 2;
        }
        sendMessageDto.setBusinessNo(businessChanceDto.getBussinessChanceNo());
        sendMessageDto.setCreator(currentUser.getUsername());
        sendMessageDto.setUserNumberList(numberList);

        if (StrUtil.isNotBlank(userNumbers)){
            log.info("走测试桩，群人员配置：{}",JSON.toJSON(userNumbers));
            List<Integer> list = Arrays.asList(userNumbers.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
            List<UserDto> userDtoList = userApiService.getUserInfoByUserIds(list);
            List<String> numbers = userDtoList.stream().map(UserDto::getNumber).collect(Collectors.toList());
            sendMessageDto.setUserNumberList(numbers);
        }


        String targetUrl = lxcrmUrl+url+businessChanceId;
        log.info("targetUrl:{}",targetUrl);
        sendMessageDto.setUrl(targetUrl);


        List<String> contentList = getStrings(consultationReportDto);

        TaskDto taskDto = new TaskDto();
        taskDto.setBizId(businessChanceDto.getBussinessChanceId());
        taskDto.setBizNo(businessChanceDto.getBussinessChanceNo());
        taskDto.setTaskContent("商机"+businessChanceDto.getBussinessChanceNo()+"请求咨询"+ String.join("、", contentList));
        taskDto.setMainTaskType(mainTaskType);
        taskDto.setBizType(3);
        taskDto.setCommitTime(new Date());
        taskDto.setTodoUserList(consultationReportDto.getToUserList());

        // 创建待办
        taskService.save(taskDto);

        List<Integer> toUserList = consultationReportDto.getToUserList();
        for (Integer userId : toUserList) {
            RSalesJBusinessOrderDto rSalesJBusinessOrderDto = new RSalesJBusinessOrderDto();
            rSalesJBusinessOrderDto.setBusinessType(ErpConstant.ONE);
            rSalesJBusinessOrderDto.setBusinessId(businessChanceDto.getBussinessChanceId());
            rSalesJBusinessOrderDto.setBusinessNo(businessChanceDto.getBussinessChanceNo());
            rSalesJBusinessOrderDto.setSaleUserId(userId);

            UserDto user = userApiService.getUserBaseInfo(userId);
            rSalesJBusinessOrderDto.setSaleUserName(user.getUsername());

            Date currentDate = new Date();
            rSalesJBusinessOrderDto.setAddTime(currentDate);
            rSalesJBusinessOrderDto.setModTime(currentDate);

            // 设置协作人
            shareService.shareBusiness(rSalesJBusinessOrderDto,currentUser);
        }
        // 发送给对应的人
        this.sendMarkDown(sendMessageDto,taskDto.getDeadline());
    }

    private List<String> getStrings(ConsultationReportDto consultationReportDto) {
        Integer isConsulDeliveryCycle = consultationReportDto.getIsConsulDeliveryCycle();
        Integer isConsulPrice = consultationReportDto.getIsConsulPrice();
        Integer isConsulReport = consultationReportDto.getIsConsulReport();
        List<String> contentList = new ArrayList<>();

        if (Objects.equals(isConsulDeliveryCycle,1)){
            contentList.add("货期");
        }
        if (Objects.equals(isConsulPrice,1)){
            contentList.add("价格");
        }
        if (Objects.equals(isConsulReport,1)){
            contentList.add("报备");
        }
        return contentList;
    }

    @Override
    @Transactional
    public void goFinishConsultationReport(Integer quoteorderId, CurrentUser currentUser) {
        // 清空咨询内容
        List<CrmQuoteorderGoods> crmQuoteorderGoods = crmQuoteOrderGoodsService.selectByQuoteorderId(quoteorderId);
        if (CollUtil.isNotEmpty(crmQuoteorderGoods)){
            // 查询产品负责人
            List<String> skuNos = crmQuoteorderGoods.stream().map(CrmQuoteorderGoods::getSku).collect(Collectors.toList());
            List<ProductManageAndAsistDto> productManageAndAssistDto = goodsApiService.batchQueryProductManageAndAsist(skuNos);
            // sku map
            Map<String,ProductManageAndAsistDto> skuNoMap = productManageAndAssistDto.stream()
                    .collect(Collectors.toMap(ProductManageAndAsistDto::getSkuNo, Function.identity()));
            // 关于当前账号负责的产品
            List<CrmQuoteorderGoods> aboutMyGoods = new ArrayList<>();
            for (CrmQuoteorderGoods crmQuoteorderGood : crmQuoteorderGoods) {
                String skuNo = crmQuoteorderGood.getSku();
                if(StringUtils.isEmpty(skuNo)){
                    continue;
                }
                ProductManageAndAsistDto productManageAndAsistDto = skuNoMap.get(skuNo);
                if(productManageAndAsistDto == null){
                    continue;
                }
                List<Integer> userList = Arrays.asList(productManageAndAsistDto.getProductManageUserId(), productManageAndAsistDto.getProductAssitUserId());
                if (userList.contains(currentUser.getId())){
                    aboutMyGoods.add(crmQuoteorderGood);
                }
            }
            if (CollectionUtils.isNotEmpty(aboutMyGoods)){
                // 清空咨询内容
                CleanConsultationContentDto cleanDto = new CleanConsultationContentDto();
                cleanDto.setQuoteorderGoodsList(aboutMyGoods);
                cleanDto.setUpdater(currentUser.getId());
                crmQuoteOrderGoodsService.cleanConsultationContent(cleanDto);
            }else {
                // 处理sku针对当前待办人
                log.info("当前账号：{}，没有需要处理的sku",currentUser.getUsername());
            }
        }

        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteorderId);
        // 发送群聊消息
        sendAppMessageCard(crmQuoteOrderDto.getBussinessChanceId(),"完成咨询",CurrentUser.getCurrentUser().getUsername()+"完成了报价处理工作");

        // 清理任务针对当前登录人
        TaskQueryDto dto = new TaskQueryDto();
        dto.setTodoUserIdList(Arrays.asList(currentUser.getId()));
        dto.setBizType(3);
        dto.setListType(1);
        dto.setBizId(crmQuoteOrderDto.getBussinessChanceId()!=null?crmQuoteOrderDto.getBussinessChanceId():0);
        dto.setDoneStatus(0);
        List<MyTaskVo> myTask = taskService.getMyTask(dto);
        log.info("当前账号：{}，待处理待办：{}",currentUser.getUsername(),JSON.toJSONString(myTask));
        if (CollUtil.isNotEmpty(myTask)){
            // 完成本人全部待办
            TaskHandleDto taskHandleDto = new TaskHandleDto();
            taskHandleDto.setDoneStatus(1);
            taskHandleDto.setDoneRemark("");
            for (MyTaskVo taskItem : myTask) {
                taskHandleDto.setTaskItemId(taskItem.getTaskItemId());
                taskHandleDto.setTaskId(taskItem.getTaskId());
                log.info("完成咨询完成待办：{}",JSON.toJSON(taskHandleDto));
                taskService.handle(taskHandleDto);
            }
        }
    }

    private void dealWithSkuNameLink(List<CrmQuoteOrderGoodsDto> crmQuoteorderGoodsList){
        if(CollectionUtils.isNotEmpty(crmQuoteorderGoodsList)){
            crmQuoteorderGoodsList.stream().forEach(goodsItem->{
                if(goodsItem.getGoodsId() !=null){
                    try {
                        String targetTabUrl = ("/goods/vgoods/viewSku.do?skuId="+goodsItem.getGoodsId()+"&pageType=1");
                        goodsItem.setSkuNameInnerLink(targetTabUrl);
                        if(crmJumpErpUrl.indexOf("sso")>-1){
                            String encodetargetTabUrl =URLEncoder.encode( "/index.do?target="+ URLEncoder.encode(targetTabUrl,"UTF-8")+"&title=商品信息整合页","UTF-8");
                            goodsItem.setSkuNameLink(crmJumpErpUrl+encodetargetTabUrl);
                        }else{
                            String encodetargetTabUrl = "/index.do?target="+ URLEncoder.encode(targetTabUrl,"UTF-8")+"&title="+URLEncoder.encode("商品信息整合页","UTF-8");
                            goodsItem.setSkuNameLink(crmJumpErpUrl+encodetargetTabUrl);
                        }

                    }catch (Exception e){

                    }
                }
            });
        }
    }

    @Override
    public QuoteSyncInfoDto queryQuoteDetail(Integer quoteorderId) {
        if (Objects.isNull(quoteorderId)){
            throw new ServiceException("报价单id不能为空");
        }
        QuoteSyncInfoDto quoteSyncInfoDto = new QuoteSyncInfoDto();
        // 报价商品
        List<CrmQuoteOrderCoreSkuDto> crmQuoteorderGoodsList = this.findQuoteOrderGoods(quoteorderId);
        quoteSyncInfoDto.setCrmQuoteorderGoodsList(crmQuoteorderGoodsList);

        // 报价单主表
        CrmQuoteOrderDto crmQuoteOrderDto = this.findQuoteOrderDto(quoteorderId,crmQuoteorderGoodsList);
        quoteSyncInfoDto.setCrmQuoteOrderDto(crmQuoteOrderDto);

        // 报价需求
        List<QuoteorderNeedsDto> quoteorderNeedsList = this.findQuoteNeeds(quoteorderId);
        quoteSyncInfoDto.setQuoteorderNeedsList(quoteorderNeedsList);
        // 商机信息
        CrmBusinessChanceDto crmBusinessChanceDto = this.findBusinessChance(crmQuoteOrderDto.getBussinessChanceId());
        quoteSyncInfoDto.setCrmBusinessChanceDto(crmBusinessChanceDto);
        // 配置信息
        QuoteConfigDto quoteConfigDto = this.findQuoteConfigDto();
        quoteSyncInfoDto.setQuoteConfigDto(quoteConfigDto);
        return quoteSyncInfoDto;
    }

    @Value("${helpUrl:}")
    private String helpUrl;

    /**
     * todo 配置
     * @return
     */
    private QuoteConfigDto findQuoteConfigDto() {
        QuoteConfigDto configDto = new QuoteConfigDto();
        configDto.setSyncCycle(5);
        configDto.setSyncLocal(5);
        // 获取所有用户信息
        List<UserDto> userDtos = userApiService.getUserInfoByUserIds(crmSkuAskUserIds);

        // 将 UserDto 转换为 CrmUserDto
        List<CrmUserDto> defaultUser = userDtos.stream()
                .filter(Objects::nonNull)  // 过滤掉可能为 null 的 UserDto
                .map(userDto -> {
                    CrmUserDto crmUserDto = new CrmUserDto();
                    crmUserDto.setUserId(userDto.getUserId());
                    crmUserDto.setUserName(userDto.getUsername());
                    crmUserDto.setHeadPic(userDto.getAliasHeadPicture());
                    return crmUserDto;
                })
                .collect(Collectors.toList());

        configDto.setDefaultUser(defaultUser);
        configDto.setHelpUrl(helpUrl);

//        List<CrmUserDto> defaultUser = crmSkuAskUserIds.stream().map(userId -> {
//            UserDto userDto = userApiService.getUserBaseInfo(userId);
//            if (Objects.nonNull(userDto)) {
//                CrmUserDto crmUserDto = new CrmUserDto();
//                crmUserDto.setUserId(userDto.getUserId());
//                crmUserDto.setUserName(userDto.getUsername());
//                crmUserDto.setHeadPic(userDto.getAliasHeadPicture());
//                return crmUserDto;
//            }
//            return null;
//        }).collect(Collectors.toList());

        configDto.setDefaultUser(defaultUser);
        return configDto;
    }

    @Override
    public QuoteSyncInfoDto syncEdit(Integer quoteorderId) {
        QuoteSyncInfoDto quoteSyncInfoDto = new QuoteSyncInfoDto();
        // 报价商品表
        List<CrmQuoteOrderCoreSkuDto> crmQuoteorderGoodsList = this.findQuoteOrderEditGoods(quoteorderId);
        quoteSyncInfoDto.setCrmQuoteorderGoodsList(crmQuoteorderGoodsList);

        // 报价单主表
        CrmQuoteOrderDto crmQuoteOrderDto = this.findQuoteOrderDto(quoteorderId,crmQuoteorderGoodsList);
        quoteSyncInfoDto.setCrmQuoteOrderDto(crmQuoteOrderDto);



//        QuoteSyncInfoDto quoteSyncInfoDto = new QuoteSyncInfoDto();
//        // 报价单主表
//        CrmQuoteOrderDto crmQuoteOrderDto = this.findQuoteOrderDto(quoteorderId);
//        List<CrmQuoteOrderCoreSkuDto> crmQuoteorderGoodsListTemp = (crmQuoteOrderDto.getQuoteApplyDto()!=null)
//                ?crmQuoteOrderDto.getQuoteApplyDto().getQuoteGoodsList() : new ArrayList<>();
//        if(CollectionUtils.isEmpty(crmQuoteorderGoodsListTemp)){
//            // 报价商品
//            List<CrmQuoteOrderCoreSkuDto> crmQuoteorderGoodsList = this.findQuoteOrderGoods(quoteorderId);
//            quoteSyncInfoDto.setCrmQuoteorderGoodsList(crmQuoteorderGoodsList);
//            //将crmQuoteOrderDto.getQuoteApplyDto()中的QuoteGoodsList清空,避免json返回内容过大
//            crmQuoteOrderDto.getQuoteApplyDto().setQuoteGoodsList(new ArrayList<>());
//        }else{
//            // 报价商品
//            quoteSyncInfoDto.setCrmQuoteorderGoodsList(crmQuoteorderGoodsListTemp);
//        }
//        //报价单信息
//        quoteSyncInfoDto.setCrmQuoteOrderDto(crmQuoteOrderDto);

        // 报价需求
        List<QuoteorderNeedsDto> quoteorderNeedsList = this.findQuoteNeeds(quoteorderId);
        quoteSyncInfoDto.setQuoteorderNeedsList(quoteorderNeedsList);
        // 商机信息
        CrmBusinessChanceDto crmBusinessChanceDto = this.findBusinessChance(crmQuoteOrderDto.getBussinessChanceId());
        quoteSyncInfoDto.setCrmBusinessChanceDto(crmBusinessChanceDto);
        return quoteSyncInfoDto;
    }

    @Value("${crmSkuAskUserIds:398,269}")
    private List<Integer> crmSkuAskUserIds;  //当选中行为客户需求，且没有对应“产品负责人”时默认增加”molly.fang和gina.chen“

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private RemotePriceApiService remotePriceApiService;


    @Override
    public List<CrmUserDto > queryAskUser(List<String> skuNos) {
        List<CrmUserDto > crmUserDtos =  new ArrayList<>();
        if(CollectionUtils.isEmpty(skuNos)){//没有商品时，默认按供应链的两个人返回
            crmSkuAskUserIds.stream().forEach(userId->{
                UserDto userDto = userApiService.getUserBaseInfo(userId);
                if(userDto != null && userDto.getUserId() !=null){
                    CrmUserDto crmUserDto = new CrmUserDto();
                    crmUserDto.setUserId(userDto.getUserId());
                    crmUserDto.setUserName(userDto.getUsername());
                    crmUserDto.setHeadPic(userDto.getAliasHeadPicture());
                    crmUserDtos.add(crmUserDto);
                }
            });
            return crmUserDtos;
        }
        BatchSkuPriceInfoDetailRequestDto batchSkuPriceInfoDetailRequestDto = new BatchSkuPriceInfoDetailRequestDto();
        batchSkuPriceInfoDetailRequestDto.setSkuNo(skuNos);
        RestfulResult<List<SkuPriceInfoDetailResponseDto>> result = remotePriceApiService.findSkuPriceInfoBySkuNos(batchSkuPriceInfoDetailRequestDto);

        Set<String> hejiaSkuList = new HashSet<>();
        if(CollectionUtils.isNotEmpty(result.getData())){
            //skuNos以V+skuId组成
            result.getData().stream().forEach(e -> {
                hejiaSkuList.add(e.getSkuNo());
            });
        }
        //查询所有skuNos对应的全部产品经理和产品助理
        //数据源：根据顶部批量或者单行的SKU找到"咨询报备"人员
        //1、当前选中的SKU行如果是未核价则显示产品经理，已核价则显示产品助理。（ERP价格中心-基础价格维护-状态"核价"）
        //2、当选中行为客户需求，且没有对应"产品负责人"时默认增加"molly.fang和gina.chen"
        //3、当选中的为客户需求，已经分配了产品负责人，则咨询逻辑会发给对应分配的产品负责人
        //4、根据选中所有的商品显示的人员需要去重。
        List<ProductManageAndAsistDto> manageAndAsistDtos  = goodsApiService.batchQueryProductManageAndAsist(skuNos);
        //将manageAndAsistDtos按key为skuNo，转换为HashSet
        Map<String,ProductManageAndAsistDto> manageAndAsistDtoMap = manageAndAsistDtos.stream().collect(Collectors.toMap(ProductManageAndAsistDto::getSkuNo, Function.identity()));
        Set<Integer> resultUserId = new HashSet<>();
        for(String skuNo:skuNos) {
            ProductManageAndAsistDto andAsistDto = manageAndAsistDtoMap.get(skuNo);
            if(hejiaSkuList.contains(skuNo)){//已核价
                if(andAsistDto == null) {//如果没找到归属产品经理和产品助理-20250121咨询人默认产品助理+产品经理
                    resultUserId.addAll(crmSkuAskUserIds);
                }else {
                    if(andAsistDto.getProductAssitUserId() !=null && andAsistDto.getProductAssitUserId()>0){
                        resultUserId.add(andAsistDto.getProductAssitUserId());
                    }
                    if(andAsistDto.getProductManageUserId() !=null && andAsistDto.getProductManageUserId()>0){
                        resultUserId.add(andAsistDto.getProductManageUserId());
                    }
                }
            }else{//未核价
                if(andAsistDto == null) {//如果没找到归属产品经理和产品助理-20250121咨询人默认产品助理+产品经理
                    resultUserId.addAll(crmSkuAskUserIds);
                }else {
                    if(andAsistDto.getProductAssitUserId() !=null && andAsistDto.getProductAssitUserId()>0){
                        resultUserId.add(andAsistDto.getProductAssitUserId());
                    }
                    if(andAsistDto.getProductManageUserId() !=null && andAsistDto.getProductManageUserId()>0){
                        resultUserId.add(andAsistDto.getProductManageUserId());
                    }
                }
            }
        }
        if(CollectionUtils.isNotEmpty(resultUserId)){
            for(Integer userId:resultUserId){
                UserDto userDto = userApiService.getUserBaseInfo(userId);
                if(userDto != null && userDto.getUserId() !=null){
                    CrmUserDto crmUserDto = new CrmUserDto();
                    crmUserDto.setUserId(userDto.getUserId());
                    crmUserDto.setUserName(userDto.getUsername());
                    crmUserDto.setHeadPic(userDto.getAliasHeadPicture());
                    crmUserDtos.add(crmUserDto);
                }
            }
        }
        return crmUserDtos;

    }

    @Override
    public void singleAddGoods(SingleAddGoodsRequestDto dto, CurrentUser currentUser) {

        checkValidQuote(dto.getQuoteorderId());

        // 更新商品
        tryUpdateGoods(dto,currentUser);
        // 添加商品
        tryFirstAddGoods(dto,currentUser);
        // 更新等级
        updateLevelByQuoteOrder(dto.getQuoteorderId());
    }

    private void updateLevelByQuoteOrder(Integer quoteorderId) {
        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteorderId);
        if (Objects.isNull(crmQuoteOrderDto)) {
            throw new ServiceException("报价单不存在");
        }
        businessChanceService.updateLevel(crmQuoteOrderDto.getBussinessChanceId());
    }

    private Map<String,BigDecimal> getPriceForNeedsSku(BatchAddGoodsNeedsRequestDto dto){
        if(CollectionUtils.isEmpty(dto.getSkuNos())){
            return new HashMap<>();
        }
        List<CoreSkuInfoDto> resultList = new ArrayList<>();
        for(String skuNo:dto.getSkuNos()) {
            CoreSkuInfoDto coreSkuInfoDto = new CoreSkuInfoDto();
            coreSkuInfoDto.setSkuNo(skuNo);
            resultList.add(coreSkuInfoDto);
        }

        Map<String,BigDecimal> priceForNeedsSku = new HashMap<>();
        TraderCustomerInfoVo traderCustomerInfoVo = this.getInfoVo(dto.getQuoteorderId());
        Integer customerNature = traderCustomerInfoVo.getCustomerNature();
        coreSkuService.setSaleSkuPrice(resultList,customerNature);
        if(CollectionUtils.isNotEmpty(resultList)){
            for(CoreSkuInfoDto coreSkuInfoDto:resultList){
                if(coreSkuInfoDto.getDealerPrice()!=null){
                    priceForNeedsSku.put(coreSkuInfoDto.getSkuNo(),coreSkuInfoDto.getDealerPrice());
                }
            }
        }
        return priceForNeedsSku;
    }

    /**
     * 批量添加商品
     * @param dto
     * @param currentUser
     */
    @Override
    @Transactional
    public String batchAddGoodsNeeds(BatchAddGoodsNeedsRequestDto dto, CurrentUser currentUser) {
        checkValidQuote(dto.getQuoteorderId());

        checkRepeatGoods(dto.getSkuNos(),dto.getQuoteorderNeedsId());

        List<CoreSkuDto> coreSkuBos = coreSkuService.getInfoBySkuNos(dto.getSkuNos());
        // 使用 Stream 过滤并提取 skuNo
        List<String> skuCheckStatusNot3List = coreSkuBos.stream()
                .filter(dto2 -> dto2.getCheckStatus() == null || dto2.getCheckStatus() != 3) // 过滤 checkStatus为null或等于3
                .map(CoreSkuDto::getSku) // 提取 skuNo 字段
                .collect(Collectors.toList()); // 收集为 List<String>

        StringBuilder errorTips = new StringBuilder();

        if(skuCheckStatusNot3List.size()>0){
            errorTips.append(skuCheckStatusNot3List.size() + "个产品“"+
                    String.join("/", skuCheckStatusNot3List)+"”未审核通过，未添加成功；<br/>");
        }
        Map<String,BigDecimal> priceForNeedsSku = getPriceForNeedsSku(dto);

        // 添加商品
        int success = batchAddGoods(dto,currentUser,false,skuCheckStatusNot3List,priceForNeedsSku);
        if(success> 0){
            updateLevelByQuoteOrder(dto.getQuoteorderId());
            // 更新商机分类
            updateBusinessCategory(dto.getQuoteorderId());
            errorTips.append(success+"个产品添加成功。");
        }
        String tips = errorTips.toString();
        return tips.length()==0?"无符合条件的产品":tips;

    }

    private void checkRepeatGoods(List<String> skuNos, Long quoteorderNeedsId) {
        List<CrmQuoteorderGoods> goodsList = quoteOrderNeedsService.queryQuoteGoodsByNeedsIdAndSkuNos(skuNos,quoteorderNeedsId);
        if (CollUtil.isNotEmpty(goodsList)){
            List<String> sku = goodsList.stream().map(CrmQuoteorderGoods::getSku).distinct().collect(Collectors.toList());
            throw new ServiceException("商品重复添加"+sku);
        }
    }

    private int batchAddGoods(BatchAddGoodsNeedsRequestDto dto, CurrentUser currentUser,boolean checkRepeat,List<String> skuCheckStatusNot3List,Map<String,BigDecimal> priceForNeedsSku) {
        List<String> skuNos = dto.getSkuNos();
        int result = 0;
        for (String skuNo : skuNos){
            if(skuCheckStatusNot3List !=null && skuCheckStatusNot3List.contains(skuNo)){
                continue;
            }
            CrmQuoteorderGoods crmQuoteorderGoods = getCrmQuoteorderGoods(skuNo, dto.getQuoteorderId());
            if(priceForNeedsSku.containsKey(crmQuoteorderGoods.getSku())){
                crmQuoteorderGoods.setPrice(priceForNeedsSku.get(crmQuoteorderGoods.getSku()));
            }
            QuoteGoodsInsertRequestDto insertRequestDto = BeanUtil.copyProperties(crmQuoteorderGoods, QuoteGoodsInsertRequestDto.class);
            insertRequestDto.setQuoteorderNeedsId(dto.getQuoteorderNeedsId());
            insertRequestDto.setQuoteorderId(dto.getQuoteorderId());
            insertRequestDto.setSku(skuNo);
            // 添加商品
            this.insertQuoteGoods(insertRequestDto,currentUser,checkRepeat);
            result ++;
        }
        return result;
    }

    private CrmQuoteorderGoods getCrmQuoteorderGoods(String skuNo,Integer quoteorderId){
        QuerySkuRequestDto requestDto = new QuerySkuRequestDto();
        requestDto.setSkuNo(skuNo);
        requestDto.setQuoteorderId(quoteorderId);
        CrmCoreSkuInfoDto crmCoreSkuInfoDto = this.queryInfoBySkuNo(requestDto);
        CrmQuoteorderGoods crmQuoteorderGoods = quoteGoodsAddConvertor.toDto(crmCoreSkuInfoDto);
        return crmQuoteorderGoods;
    }

    private void tryUpdateGoods(SingleAddGoodsRequestDto dto, CurrentUser currentUser){
        if (StringUtils.isBlank(dto.getOldSkuNo())){
            log.info("oldSkuNo is null,skip update goods");
            return;
        }
        // 判断报价单中是否存在该商品
        List<CrmQuoteorderGoods> quoteorderGoodsList = crmQuoteOrderGoodsService.selectByQuoteorderId(dto.getQuoteorderId());
        List<String> goodsList = quoteorderGoodsList.stream().map(CrmQuoteorderGoods::getSku).distinct().collect(Collectors.toList());
        if (goodsList.contains(dto.getSkuNo())){
            throw new ServiceException("订货号"+dto.getSkuNo()+"商品已选择，请检查并处理后重新提交");
        }

        CrmQuoteorderGoods crmQuoteorderGoods = this.getCrmQuoteorderGoods(dto.getSkuNo(), dto.getQuoteorderId());
        crmQuoteorderGoods.setQuoteorderGoodsId(dto.getQuoteorderGoodsId());
        // 初始化其他值
        crmQuoteorderGoods.setPrice(BigDecimal.ZERO);
        crmQuoteorderGoods.setDeliveryCycle(crmQuoteorderGoods.getDeliveryCycle());
        crmQuoteorderGoods.setReportStatus(null);
        crmQuoteorderGoods.setReportComments("");
        crmQuoteorderGoods.setIsConsulPrice(0);
        crmQuoteorderGoods.setIsConsulDeliveryCycle(0);
        crmQuoteorderGoods.setIsConsulReport(0);
        crmQuoteorderGoods.setAddTime(System.currentTimeMillis());
        crmQuoteorderGoods.setUpdater(currentUser.getId());
        crmQuoteorderGoods.setModTime(System.currentTimeMillis());
        crmQuoteorderGoods.setUpdater(currentUser.getId());
        crmQuoteOrderGoodsService.updateSelective(crmQuoteorderGoods,currentUser);

        // 日志
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        params.put("oldSkuNo",dto.getOldSkuNo());
        params.put("newSkuNo",dto.getSkuNo());

        logDto.setBizId(dto.getQuoteorderId());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        operationLogApiService.save(logDto, BizLogEnum.MODIFY_PRODUCT);
    }

    private void tryFirstAddGoods(SingleAddGoodsRequestDto dto, CurrentUser currentUser){
        if (StringUtils.isNotBlank(dto.getOldSkuNo())){
            log.info("oldSkuNo is not null,skip add goods");
            return;
        }
        CrmQuoteorderGoods crmQuoteorderGoods = getCrmQuoteorderGoods(dto.getSkuNo(), dto.getQuoteorderId());
        QuoteGoodsInsertRequestDto insertRequestDto = BeanUtil.copyProperties(crmQuoteorderGoods, QuoteGoodsInsertRequestDto.class);
        insertRequestDto.setQuoteorderNeedsId(dto.getQuoteorderNeedsId());
        insertRequestDto.setQuoteorderId(dto.getQuoteorderId());
        insertRequestDto.setSku(dto.getSkuNo());
        // 添加商品
        this.insertQuoteGoods(insertRequestDto,currentUser,false);
    }


    //申请中
    private static Integer AUTHORIZATION_REVIEW=1;
    //驳回
    private static Integer AUTHORIZATION_REJECT=2;
    //通过
    private static Integer AUTHORIZATION_PASS=3;
    //取消
    private static Integer AUTHORIZATION_CANCEL=4;
    //废弃
    private static Integer AUTHORIZATION_ABANDON=5;

    private void checkValidQuote(Integer quoteorderId){
        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteorderId);
        if (Objects.isNull(crmQuoteOrderDto)){
            throw new ServiceException("该报价单不存在");
        }
        if(Objects.equals(crmQuoteOrderDto.getValidStatus(),1)){
            throw new ServiceException("该报价单已生效,无法操作");
        }
    }

    @Override
    public CheckAuthorityRepDto checkQuoteApply(Integer quoteorderId) {
        if (Objects.isNull(quoteorderId)){
            throw new ServiceException("报价单id不能为空");
        }
        CheckAuthorityRepDto resultInfo=new CheckAuthorityRepDto();

        // 1
        int authorizationReviewSum=crmQuoteOrderService.getAuthorizationSum(quoteorderId,AUTHORIZATION_REVIEW);
        // 2
        int authorizationRejectSum=crmQuoteOrderService.getAuthorizationSum(quoteorderId,AUTHORIZATION_REJECT);
        // 3
        int authorizationPassSum=crmQuoteOrderService.getAuthorizationSum(quoteorderId,AUTHORIZATION_PASS);
        // 已提交
        int submitted = authorizationReviewSum+authorizationRejectSum+authorizationPassSum;
        List<CrmQuoteOrderCoreSkuDto> quoteGoodsList = this.findQuoteOrderGoods(quoteorderId);
        int count = quoteGoodsList.size();

        resultInfo.setNum(count);
        resultInfo.setAppliedNum(submitted);

        String checkInnerLink = "/order/quote/authorizationView.do?quoteorderId="+quoteorderId;
        String submitInnerLink = "/order/quote/apply.do?quoteorderId="+quoteorderId;

        try {
            if(crmJumpErpUrl.contains("sso")){
                String checkLinkButton =URLEncoder.encode( "/index.do?target="+ URLEncoder.encode(checkInnerLink,"UTF-8")+"&title=","UTF-8");//title=授权书查看
                String submitLinkButton =URLEncoder.encode( "/index.do?target="+ URLEncoder.encode(submitInnerLink,"UTF-8")+"&title=","UTF-8");//title=授权书申请
                resultInfo.setCheckLink(crmJumpErpUrl +checkLinkButton);
                resultInfo.setSubmitLink(crmJumpErpUrl +submitLinkButton);
            }else{
                String checkLinkButton = "/index.do?target="+ URLEncoder.encode(checkInnerLink,"UTF-8")+"&title="+URLEncoder.encode("授权书查看","UTF-8");
                String submitLinkButton = "/index.do?target="+ URLEncoder.encode(submitInnerLink,"UTF-8")+"&title="+URLEncoder.encode("授权书申请","UTF-8");
                resultInfo.setCheckLink(crmJumpErpUrl +checkLinkButton);
                resultInfo.setSubmitLink(crmJumpErpUrl +submitLinkButton);
            }
        }catch (Exception e){
            log.error("授权书链接组装报错",e);
        }

        resultInfo.setCheckInnerLink(checkInnerLink);
        resultInfo.setSubmitInnerLink(submitInnerLink);

        return resultInfo;
    }

    @Override
    public QuoteApplyDto queryQuoteApply(Integer quoteorderId,List<CrmQuoteOrderCoreSkuDto> crmQuoteorderGoodsList) {
        if (Objects.isNull(quoteorderId)){
            throw new ServiceException("报价单id不能为空");
        }
        QuoteApplyDto quoteApplyDto = new QuoteApplyDto();
        quoteApplyDto.setQuoteorderId(quoteorderId);
//        List<CrmQuoteOrderCoreSkuDto> quoteGoodsList = this.findQuoteOrderGoods(quoteorderId);
//        quoteApplyDto.setQuoteGoodsList(quoteGoodsList);//此处将商品信息一并放入到QuoteApplyDto对象中
        //判断界面有效的产品信息数量
        List<CrmQuoteOrderCoreSkuDto> countQuoteGoodsList=crmQuoteorderGoodsList.stream().collect(Collectors.toList());

        int authorizationReviewSum= crmQuoteOrderService.getAuthorizationSum(quoteorderId,AUTHORIZATION_REVIEW);
        int authorizationRejectSum=crmQuoteOrderService.getAuthorizationSum(quoteorderId,AUTHORIZATION_REJECT);
        int authorizationPassSum=crmQuoteOrderService.getAuthorizationSum(quoteorderId,AUTHORIZATION_PASS);
        int flag = 1;
        String quoteApplyInnerLink = "/order/quote/apply.do?quoteorderId="+quoteorderId;
        if (countQuoteGoodsList != null){
            //界面有效的产品信息数量
            int sumQuoteGoodsList=countQuoteGoodsList.size();
            if (sumQuoteGoodsList==0){
                flag=1;
                quoteApplyDto.setApplyName("申请授权书");
                quoteApplyInnerLink = "/order/quote/apply.do?quoteorderId="+quoteorderId;
            }else if ((authorizationRejectSum+authorizationReviewSum !=0) && (sumQuoteGoodsList==authorizationRejectSum+authorizationReviewSum+authorizationPassSum)){
                flag=2;
                quoteApplyDto.setApplyName("查看授权书");
                quoteApplyInnerLink = "/order/quote/authorizationView.do?quoteorderId="+quoteorderId;
            }else if (sumQuoteGoodsList==authorizationPassSum){
                flag=3;
                quoteApplyDto.setApplyName("打印授权书");
                quoteApplyInnerLink = "/order/quote/authorizationView.do?quoteorderId="+quoteorderId;
            }else{
//                其他情况均展示申请授权书 flag = 1
                flag= 1;
                quoteApplyDto.setApplyName("申请授权书");
                quoteApplyInnerLink = "/order/quote/apply.do?quoteorderId="+quoteorderId;
            }
            quoteApplyDto.setFlag(flag);
            quoteApplyDto.setSqnum(sumQuoteGoodsList);
            try {
                //String quoteApplyLink = quoteApplyInnerLink;

                quoteApplyDto.setQuoteApplyInnerLink(quoteApplyInnerLink);

                if(crmJumpErpUrl.indexOf("sso")>-1){
                    String encodetargetTabUrl =URLEncoder.encode( "/index.do?target="+ URLEncoder.encode(quoteApplyInnerLink,"UTF-8")+"&title=授权书申请","UTF-8");
                    quoteApplyDto.setQuoteApplyLink(crmJumpErpUrl +encodetargetTabUrl);
                }else{
                    String encodetargetTabUrl = "/index.do?target="+ URLEncoder.encode(quoteApplyInnerLink,"UTF-8")+"&title="+URLEncoder.encode("授权书申请","UTF-8");
                    quoteApplyDto.setQuoteApplyLink(crmJumpErpUrl +encodetargetTabUrl);
                }




            }catch (Exception e){
                log.error("授权书链接组装报错",e);
            }
        }
        return quoteApplyDto;
    }


    @Override
    public void addQuoteNeedsDesc(QuoteNeedsDescReqDto requestDto, CurrentUser currentUser) {
        CrmQuoteOrderDto dto = new CrmQuoteOrderDto();
        dto.setQuoteorderId(requestDto.getQuoteorderId());
        dto.setNeedsDesc(requestDto.getNeedsDesc());
        crmQuoteOrderService.updateQuoteOrder(dto,currentUser);
    }

    @Override
    public void addQuoteGoodsRemark(QuoteGoodsRemarkReqDto requestDto, CurrentUser currentUser) {
        QuoteorderGoodsRemarkEntity entity = new QuoteorderGoodsRemarkEntity();
        entity.setQuoteorderGoodsId(requestDto.getQuoteorderGoodsId());
        entity.setRemark(requestDto.getRemark());
        entity.setAddTime(new Date());
        entity.setCreator(currentUser.getId());
        entity.setCreatorName(currentUser.getUsername());
        quoteOrderGoodsRemarkService.insert(entity);
    }

    @Override
    public List<QuoteorderGoodsRemarkDto> queryAllRemark(Integer quoteorderGoodsId) {
        List<QuoteorderGoodsRemarkEntity> list = quoteOrderGoodsRemarkService.queryList(quoteorderGoodsId);
        if (CollUtil.isEmpty(list)){
            return null;
        }
        List<QuoteorderGoodsRemarkDto> dtoList = new ArrayList<>();
        for (QuoteorderGoodsRemarkEntity entity : list) {
            UserDto userDto = userApiService.getUserBaseInfo(entity.getCreator());
            if(userDto != null && userDto.getUserId() !=null){
                QuoteorderGoodsRemarkDto dto = new QuoteorderGoodsRemarkDto();
                dto.setUserId(userDto.getUserId());
                dto.setUserName(userDto.getUsername());
                dto.setHeadPic(userDto.getAliasHeadPicture());

                dto.setAddTime(DateUtil.format(entity.getAddTime(), DatePattern.NORM_DATETIME_FORMAT));
                dto.setRemark(entity.getRemark());
                dto.setQuoteorderGoodsId(entity.getQuoteorderGoodsId());
                dtoList.add(dto);
            }
        }
        return dtoList;
    }


    private CrmBusinessChanceDto findBusinessChance(Integer businessChanceId) {
        BusinessChanceDto businessChanceDto = new BusinessChanceDto();
        businessChanceDto.setBussinessChanceId(businessChanceId);
        BusinessChanceDto dto = businessChanceService.selectOne(businessChanceDto);

        CrmBusinessChanceDto crmBusinessChanceDto = crmBusinessChanceConvertor.toDto(dto);

        R<OrderTerminalDto> query = terminalApiService.query(businessChanceId, 1);
        if (query.getSuccess()){
            OrderTerminalDto data = query.getData();
            crmBusinessChanceDto.setOrderTerminalDto(data);
        }
        return crmBusinessChanceDto;
    }

    @Override
    public CrmCoreSkuInfoDto queryInfoBySkuNo(QuerySkuRequestDto requestDto) {
        Integer quoteorderId = requestDto.getQuoteorderId();
        String skuNo = requestDto.getSkuNo();
        if (StrUtil.isBlank(skuNo)){
            return new CrmCoreSkuInfoDto();
        }

        TraderCustomerInfoVo traderCustomerInfoVo = this.getInfoVo(quoteorderId);
        Integer customerNature = traderCustomerInfoVo.getCustomerNature();
        CoreSkuInfoDto coreSkuInfoDto = coreSkuService.getCoreSkuInfoDtoBySkuNo(skuNo,customerNature);
        CrmCoreSkuInfoDto crmCoreSkuInfoDto = crmCoreSkuInfoConvertor.toDto(coreSkuInfoDto);
        crmCoreSkuInfoDto.setReportStatusDesc(ReportStatusEnum.getDescByStatus(crmCoreSkuInfoDto.getReportStatus()));
        dealWithSkuNameLink(crmCoreSkuInfoDto);//添加要跳转的超链接
        return crmCoreSkuInfoDto;
    }

    @Override
    public List<CrmCoreSkuInfoDto> queryInfoBySkuNoFast(QuerySkusRequestDto requestDto) {
        Integer quoteorderId = requestDto.getQuoteorderId();
        List<String> skuNos = requestDto.getSkuNos();
        if (CollectionUtils.isEmpty(skuNos)){
            return new ArrayList<>();
        }

        TraderCustomerInfoVo traderCustomerInfoVo = this.getInfoVo(quoteorderId);
        Integer customerNature = traderCustomerInfoVo.getCustomerNature();
        List<CoreSkuInfoDto> coreSkuInfoDtoList = coreSkuService.getCoreSkuInfoDtoBySkuNo(skuNos,customerNature);
        // 使用 Stream API 进行转换
        List<CrmCoreSkuInfoDto> crmCoreSkuInfoDtoList = coreSkuInfoDtoList.stream()
            .map(coreSkuInfoDto -> {
                // 转换 CoreSkuInfoDto 为 CrmCoreSkuInfoDto
                CrmCoreSkuInfoDto crmCoreSkuInfoDto = crmCoreSkuInfoConvertor.toDto(coreSkuInfoDto);
                // 设置 reportStatusDesc
                crmCoreSkuInfoDto.setReportStatusDesc(ReportStatusEnum.getDescByStatus(crmCoreSkuInfoDto.getReportStatus()));
                crmCoreSkuInfoDto.setCheckStatus(coreSkuInfoDto.getCheckStatus());
                // 处理 skuName 链接
                dealWithSkuNameLink(crmCoreSkuInfoDto);
                return crmCoreSkuInfoDto;
            })
            .collect(Collectors.toList());
        return crmCoreSkuInfoDtoList;
    }


    private void dealWithSkuNameLink(CrmCoreSkuInfoDto crmCoreSkuInfoDto){
        if(crmCoreSkuInfoDto==null || crmCoreSkuInfoDto.getSkuId()==null){
            return ;
        }
        try {
            String targetTabUrl = ("/goods/vgoods/viewSku.do?skuId="+crmCoreSkuInfoDto.getSkuId()+"&pageType=1");
            crmCoreSkuInfoDto.setSkuNameInnerLink(targetTabUrl);
            if(crmJumpErpUrl.indexOf("sso")>-1){
                String encodetargetTabUrl =URLEncoder.encode( "/index.do?target="+ URLEncoder.encode(targetTabUrl,"UTF-8")+"&title=商品信息整合页","UTF-8");
                crmCoreSkuInfoDto.setSkuNameLink(crmJumpErpUrl+encodetargetTabUrl);
            }else{
                String encodetargetTabUrl = "/index.do?target="+ URLEncoder.encode(targetTabUrl,"UTF-8")+"&title="+URLEncoder.encode("商品信息整合页","UTF-8");
                crmCoreSkuInfoDto.setSkuNameLink(crmJumpErpUrl+encodetargetTabUrl);
            }

        }catch (Exception e){

        }
    }

    private List<QuoteorderNeedsDto> findQuoteNeeds(Integer quoteorderId) {
        List<QuoteorderNeedsEntity> quoteorderNeedEntities = quoteOrderNeedsService.selectByQuoteorderId(quoteorderId);
        // 查询关联关系
        List<RQuoteorderNeedsJGoodsEntity> rQuoteNeedsJGoodsEntities = rQuoteNeedsJGoodsService.selectByQuoteorderId(quoteorderId);
        Map<Long,List<RQuoteorderNeedsJGoodsEntity>> goodsMap = rQuoteNeedsJGoodsEntities.stream()
                .collect(Collectors.groupingBy(e -> e.getQuoteorderNeedsId()));
        if (MapUtil.isEmpty(goodsMap)){
            log.info("报价单商品关系表为空");
            goodsMap = new HashMap<>();
        }

        List<Integer> tempUserIdList = quoteorderNeedEntities.stream()
                .filter(entity -> entity.getHeadUserId() != null).map(QuoteorderNeedsEntity::getHeadUserId).collect(Collectors.toList());
        List<UserDto> userBaseInfoList = userApiService.getUserInfoByUserIds(tempUserIdList);
        // 使用 Stream API 进行转换
        Map<Integer, UserDto> userIdToDtoMap = userBaseInfoList.stream()
                .collect(Collectors.toMap(
                        UserDto::getUserId,
                        dto -> dto
                ));

        List<QuoteorderNeedsDto> result = new ArrayList<>();
        for (QuoteorderNeedsEntity quoteorderNeedEntity : quoteorderNeedEntities) {
            QuoteorderNeedsDto dto = new QuoteorderNeedsDto();
            dto.setQuoteorderId(quoteorderNeedEntity.getQuoteorderId());
            dto.setQuoteorderNeedsId(quoteorderNeedEntity.getQuoteorderNeedsId());
            dto.setProductNeeds(quoteorderNeedEntity.getProductNeeds());
            dto.setNumNeeds(quoteorderNeedEntity.getNumNeeds());
            dto.setDistributeBudget(quoteorderNeedEntity.getDistributeBudget());
            dto.setTerminalBudget(quoteorderNeedEntity.getTerminalBudget());
            dto.setExtraNeeds(quoteorderNeedEntity.getExtraNeeds());
            dto.setAddTime(ErpDateUtils.format(quoteorderNeedEntity.getAddTime(), PatternEnum.YYYY_MM_DD_HH_MM_SS));

            List<RQuoteorderNeedsJGoodsEntity> relationGoods = goodsMap.get(quoteorderNeedEntity.getQuoteorderNeedsId());
            if (CollUtil.isNotEmpty(relationGoods)){
                List<Integer> quoteorderGoodsIds = relationGoods.stream()
                        .map(RQuoteorderNeedsJGoodsEntity::getQuoteorderGoodsId).collect(Collectors.toList());
                dto.setQuoteorderGoodsIds(quoteorderGoodsIds);
            }

            Integer headUserId = quoteorderNeedEntity.getHeadUserId();
            if (Objects.nonNull(headUserId)){
                UserDto userBaseInfo = userIdToDtoMap.get(headUserId);
                if(userBaseInfo != null){
                    dto.setHeadUserList(Arrays.asList(userBaseInfo));
                }
            }
            result.add(dto);
        }
        return result;
    }




    public List<CrmQuoteOrderCoreSkuDto> findQuoteOrderGoods(Integer quoteorderId) {
        return findQuoteOrderGoodsFast(quoteorderId);
    }

    private List<CrmQuoteOrderCoreSkuDto> findQuoteOrderGoodsFast(Integer quoteorderId) {
        List<CrmQuoteorderGoods> crmQuoteorderGoodsList = crmQuoteOrderGoodsService.selectByQuoteorderId(quoteorderId);
        List<QuoteorderGoodsRemarkEntity> goodsRemarkList = quoteOrderGoodsRemarkService.queryListByQuoteId(quoteorderId);
        Map<Integer, List<QuoteorderGoodsRemarkEntity>> quoteorderMap = goodsRemarkList.stream()
                .collect(Collectors.groupingBy(QuoteorderGoodsRemarkEntity::getQuoteorderGoodsId));
        if (MapUtil.isEmpty(quoteorderMap)){
            quoteorderMap = new HashMap<>();
        }

        List<String> skuNos = crmQuoteorderGoodsList.stream()
                .map(CrmQuoteorderGoods::getSku) // 提取 sku
                .filter(Objects::nonNull) // 过滤掉 null 值
                .collect(Collectors.toList()); // 收集到 List
        // 规格型号
        QuerySkusRequestDto requestDto = new QuerySkusRequestDto();
        requestDto.setQuoteorderId(quoteorderId);
        requestDto.setSkuNos(skuNos);
        List<CrmCoreSkuInfoDto> crmCoreSkuInfoDtoList = this.queryInfoBySkuNoFast(requestDto);
        Map<String,CrmCoreSkuInfoDto> skuToInfoMap =  crmCoreSkuInfoDtoList.stream().collect(Collectors.toMap(
                CrmCoreSkuInfoDto::getSkuNo,
                dto -> dto
        ));


        List<CrmQuoteOrderCoreSkuDto> result = new ArrayList<>();
        for (CrmQuoteorderGoods crmQuoteOrderCoreSkuDto : crmQuoteorderGoodsList) {
            // 向上取整
            String deliveryCycle = crmQuoteOrderCoreSkuDto.getDeliveryCycle();
            crmQuoteOrderCoreSkuDto.setDeliveryCycle(formatDeliveryCycle(deliveryCycle));
            CrmQuoteOrderCoreSkuDto coreSkuDto = crmQuoteOrderSkuInfoConvertor.toDto(crmQuoteOrderCoreSkuDto);
            coreSkuDto.setReportStatusDesc(ReportStatusEnum.getDescByStatus(crmQuoteOrderCoreSkuDto.getReportStatus()));
            // 规格型号
//            QuerySkuRequestDto requestDto = new QuerySkuRequestDto();
//            requestDto.setQuoteorderId(quoteorderId);
//            requestDto.setSkuNo(crmQuoteOrderCoreSkuDto.getSku());
            // 选型查询
            if (StrUtil.isBlank(crmQuoteOrderCoreSkuDto.getSku())){
                coreSkuDto.setImageUrl(crmQuoteOrderCoreSkuDto.getImgUrl());
                coreSkuDto.setMainParam(Arrays.asList(crmQuoteOrderCoreSkuDto.getParamContent()));
                coreSkuDto.setBrandId(crmQuoteOrderCoreSkuDto.getBrandId());
                coreSkuDto.setModelOrSpec(crmQuoteOrderCoreSkuDto.getModel());
                coreSkuDto.setUnitName(crmQuoteOrderCoreSkuDto.getUnitName());
            }else {
                CrmCoreSkuInfoDto crmCoreSkuInfoDto = skuToInfoMap.get(crmQuoteOrderCoreSkuDto.getSku());
                coreSkuDto.setIsInstall(crmCoreSkuInfoDto.getIsInstall());
                coreSkuDto.setIsNeedReport(crmCoreSkuInfoDto.getIsNeedReport());
                coreSkuDto.setMainParam(crmCoreSkuInfoDto.getMainParam());
                coreSkuDto.setAvailableStockNum(crmCoreSkuInfoDto.getAvailableStockNum());
                coreSkuDto.setProductManager(crmCoreSkuInfoDto.getProductManager());
                coreSkuDto.setImageUrl(crmCoreSkuInfoDto.getImageUrl());
                coreSkuDto.setWarrantyInfo(crmCoreSkuInfoDto.getWarrantyInfo());
                coreSkuDto.setUseLife(crmCoreSkuInfoDto.getUseLife());
                coreSkuDto.setModelOrSpec(crmCoreSkuInfoDto.getModelOrSpec());
                coreSkuDto.setDealerPrice(crmCoreSkuInfoDto.getDealerPrice());
                coreSkuDto.setGoodsPositionNo(crmCoreSkuInfoDto.getGoodsPositionNo());
                coreSkuDto.setSkuNameLink(crmCoreSkuInfoDto.getSkuNameLink());
                coreSkuDto.setSkuNameInnerLink(crmCoreSkuInfoDto.getSkuNameInnerLink());
                coreSkuDto.setRemarkCount(0);
                coreSkuDto.setCheckStatus(crmCoreSkuInfoDto.getCheckStatus());//审核状态

            }


            List<QuoteorderGoodsRemarkEntity> quoteorderGoodsRemarkEntities = quoteorderMap.get(crmQuoteOrderCoreSkuDto.getQuoteorderGoodsId());
            if (CollUtil.isNotEmpty(quoteorderGoodsRemarkEntities)){
                // 获取id最大的一条记录
                QuoteorderGoodsRemarkEntity quoteorderGoodsRemarkEntity = quoteorderGoodsRemarkEntities.stream()
                        .max(Comparator.comparing(QuoteorderGoodsRemarkEntity::getQuoteorderGoodsRemarkId)).get();
                coreSkuDto.setRemark(quoteorderGoodsRemarkEntity.getRemark());
                coreSkuDto.setRemarkAddTime(ErpDateUtils.format(quoteorderGoodsRemarkEntity.getAddTime(), PatternEnum.MM_DD_HH_MM));
                coreSkuDto.setRemarkUserId(quoteorderGoodsRemarkEntity.getCreator());
                coreSkuDto.setRemarkUserName(quoteorderGoodsRemarkEntity.getCreatorName());
                coreSkuDto.setRemarkCount(quoteorderGoodsRemarkEntities.size());
            }
            result.add(coreSkuDto);
        }

        return result;
    }

    private String formatDeliveryCycle(String deliveryCycle){
        if (StrUtil.isBlank(deliveryCycle)){
            return "";
        }
        // 将字符串转换为 BigDecimal
        BigDecimal deliveryCycleBigDecimal = new BigDecimal(deliveryCycle);

        // 向上取整
        BigDecimal roundedUpValue = deliveryCycleBigDecimal.setScale(0, RoundingMode.CEILING);

        return roundedUpValue.toPlainString();
    }

    private List<CrmQuoteOrderCoreSkuDto> findQuoteOrderEditGoods(Integer quoteorderId) {
        List<CrmQuoteorderGoods> crmQuoteorderGoodsList = crmQuoteOrderGoodsService.selectByQuoteorderId(quoteorderId);
        HashSet<String> skuNos = new HashSet<>();
        crmQuoteorderGoodsList.forEach(e->{
            // 向上取整
            String deliveryCycle = e.getDeliveryCycle();
            e.setDeliveryCycle(formatDeliveryCycle(deliveryCycle));
            if(e.getGoodsId() != null && e.getGoodsId() >0){
                skuNos.add(e.getSku());
            }
        });
        List<CrmQuoteOrderCoreSkuDto> dto = crmQuoteOrderSkuInfoConvertor.toDto(crmQuoteorderGoodsList);
        if(CollectionUtils.isEmpty(skuNos)){
            return dto;
        }
        List<CoreSkuDto> coreSkuDtos = coreSkuService.getInfoBySkuNos(new ArrayList<>(skuNos));
        Map<String, CoreSkuDto> skuNoToSkuMap = coreSkuDtos.stream()
                .collect(Collectors.toMap(
                        CoreSkuDto::getSku, // 键提取函数
                        sku -> sku,          // 值提取函数
                        (existing, replacement) -> {//重复的取第一条
                            return existing;
                        }
                ));
        dto.forEach(obj->{
            if(obj.getSkuId() !=null && obj.getSkuId() >0){
                String skuNo = obj.getSkuNo();
                CoreSkuDto coreSkuDto = skuNoToSkuMap.get(skuNo);
                obj.setCheckStatus(coreSkuDto != null?coreSkuDto.getCheckStatus():0);//未取到时返回0，待完善
            }
        });
        return dto;
    }


    @Autowired
    private GlobalFileService globalFileService;

    private CrmQuoteOrderDto findQuoteOrderDto(Integer quoteorderId,List<CrmQuoteOrderCoreSkuDto> crmQuoteorderGoodsList) {
        CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteorderId);
        // 授权书相关
        QuoteApplyDto quoteApplyDto = queryQuoteApply(quoteorderId,crmQuoteorderGoodsList);
        crmQuoteOrderDto.setQuoteApplyDto(quoteApplyDto);

        AppChatReqDto reqDto = new AppChatReqDto();
        reqDto.setChatId(getChatId(crmQuoteOrderDto.getBussinessChanceId()));
        RestfulResult<WxCpChat> wxCpChatRestfulResult = uacWxUserInfoApiService.get(reqDto);
        log.info("查询群信息，入参：{},出参：{}",JSON.toJSON(reqDto),JSON.toJSON(wxCpChatRestfulResult));
        if (wxCpChatRestfulResult.isSuccess()){
            WxCpChat data = wxCpChatRestfulResult.getData();
            crmQuoteOrderDto.setChatName(data.getName());
        }
        GlobalFileRequest globalFileRequest = new GlobalFileRequest();
        globalFileRequest.setBizId(quoteorderId);
        globalFileRequest.setBizType("03");
        List<GlobalFileDto> fileList =  globalFileService.get(globalFileRequest);
        crmQuoteOrderDto.setFileCount(CollectionUtil.isEmpty(fileList)?0:fileList.size());


        return crmQuoteOrderDto;
    }

    private void sendMarkDown(SendMessageDto sendMessageDto,Date deadline){
        // 去重
        List<String> userNumberList = sendMessageDto.getUserNumberList().stream().distinct().collect(Collectors.toList());
        String targetUrl = jumpService.getjumpUrl(sendMessageDto.getUrl(), JumpErpTitleEnum.BUSSINESS_CHANCE_DETAIL);
        String currentTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        for (String toUserNumber : userNumberList) {
            // 生成任务
            String businessNo = sendMessageDto.getBusinessNo();
            String type = sendMessageDto.getType();
            String creator = sendMessageDto.getCreator();
            String deadLine = DateUtil.format(deadline, "yyyy-MM-dd HH:mm:ss");

            StringBuilder msg = new StringBuilder();
            msg.append("<div class=\"gray\">"+currentTime+"</div> <div class=\"normal\">").append(businessNo).append(type).append("</div>");
            msg.append("<div class=\"normal\">发起人：").append(creator).append("</div>");
            msg.append("<div class=\"normal\">截止时间：").append(deadLine).append("</div>");
            msg.append("<div class=\"highlight\">若截止时间内未能完成任务，将会发送超时提醒至主管处</div>");

            WxCpMessage wxCpMessage = new WxCpMessage();
            wxCpMessage.setToUser(toUserNumber);
            wxCpMessage.setMsgType("textcard");
            wxCpMessage.setTitle("任务提醒");
            wxCpMessage.setDescription(msg.toString());
            wxCpMessage.setUrl(targetUrl);
            wxCpMessage.setBtnTxt("详情");

            uacWxUserInfoApiService.sendToUser(wxCpMessage);


//            WxCpMessage wxCpMessage2 = new WxCpMessage();
//            wxCpMessage2.setToUser(toUserNumber);
//            wxCpMessage2.setMsgType("textcard");
//            wxCpMessage2.setTitle("通知：商机"+businessNo+type+"任务通知");
//            wxCpMessage2.setDescription("若截止时间内未能完成任务，会将发送超时提醒至主管处");
//            wxCpMessage2.setUrl(targetUrl);
//            wxCpMessage2.setBtnTxt("点击查看详情");
//            uacWxUserInfoApiService.sendToUser(wxCpMessage2);
        }
    }

    private void sendCardMsg(){
        // 生成任务
        String businessNo = "BD12345";
        String type = "综合询价";
        String now = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        WxCpMessage wxCpMessage = new WxCpMessage();
        wxCpMessage.setToUser("1505");
        wxCpMessage.setMsgType("textcard");
        wxCpMessage.setTitle("提醒");
        wxCpMessage.setDescription("<div class=\"gray\">"+now+"</div> <div class=\"normal\">商机"+businessNo+type+"任务即将截止，请您关注</div><div class=\"highlight\">若截止时间内未能完成任务，将会发送超时提醒至主管处</div>");
        wxCpMessage.setUrl("http://www.baidu.com");// todo
        wxCpMessage.setBtnTxt("详情");
        log.info("入参：{}",JSON.toJSON(wxCpMessage));
        uacWxUserInfoApiService.sendToUser(wxCpMessage);
    }


    private String processSkuNos(String skuNos){
        return skuNos.replaceAll("\r\n", "").replaceAll(" ", "").toUpperCase();
    }

    private String getQuoteKey(Integer quoteorderId){
        if (Objects.isNull(quoteorderId)){
            throw new ServiceException("quoteorderId不能为空");
        }
        return KEY_PREFIX_QUOTE + quoteorderId;
    }

    private String getLocalKey(Integer quoteorderId, String userName){
        if (StrUtil.isBlank(userName)){
            return null;
        }
        return KEY_PREFIX_LOCAL + quoteorderId + ":" + userName;
    }
    @Override
    public  Integer selectQuoteorderIdByBusinessChanceId(Integer bussinessChanceId){
        return businessChanceService.selectQuoteorderIdByBusinessChanceId(bussinessChanceId);
    }

    @Override
    public void close(Integer quoteorderId) {
        businessChanceService.close(quoteorderId);
    }

	@Override
	@Transactional
	public QuoteCreateAppChat approverAppChat(ApproverAppChatDto approverAppChatDto, CurrentUser currentUser) throws Exception {
		//审批状态【0：驳回；1：审批通过；2：提交审批】
		Integer approverStatus = approverAppChatDto.getApproverStatus();
		Integer quoteorderId = approverAppChatDto.getQuoteorderId();
		
		// 更新建群状态
        CrmQuoteOrderDto updateOrderDto = new CrmQuoteOrderDto();
        updateOrderDto.setQuoteorderId(quoteorderId);
        updateOrderDto.setIsBuildChat(approverStatus);
        if(CollectionUtils.isNotEmpty(approverAppChatDto.getUserIdList())) {
        	updateOrderDto.setBuildChatUserIds(approverAppChatDto.getUserIdList().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        
		//如果是提交审批，需要发送企微消息
        //如果是审批通过，需要建群
        QuoteCreateAppChat quoteCreateAppChat = null;
    	CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteorderId);
    	BusinessChanceDto businessChanceDto = businessChanceService.viewDetail(crmQuoteOrderDto.getBussinessChanceId());
    	
    	Integer isBuildChat = crmQuoteOrderDto.getIsBuildChat();
    	//群聊已创建，不可驳回
		if(approverAppChatDto.getApproverStatus() == 0 && isBuildChat == 1) {
			throw new Exception("群聊已创建，不可驳回...");
		}
		//群聊已驳回，不可审核通过
		if(approverAppChatDto.getApproverStatus() == 1 && isBuildChat == 0) {
			throw new Exception("群聊已驳回，不可审核通过...");
		}
    	
		//商机的userId就是归属销售
    	Integer belongId = businessChanceDto.getUserId();
    	String belongUserName = businessChanceDto.getUsername();
    	//归属销售上级
    	String parentBelongId = null;
    	Integer parentBelongIdInt = null;
		//根据这个归属销售，到uac查询他的parent_user_id既是他的主管
    	UserDto userDto = userApiService.searchByUserIdFromUac(belongId);
    	if(Objects.nonNull(userDto) && Objects.nonNull(userDto.getParentUserId())) {
    		parentBelongIdInt = userDto.getParentUserId();	
    		UserDto userDtoParent = userApiService.searchByUserIdFromUac(parentBelongIdInt);
    		parentBelongId = userDtoParent.getNumber();
    		
    	}
    	//测试环境使用测试的账号
    	if(!StringUtils.isEmpty(testParentBelongId)) {
    		parentBelongId = testParentBelongId;
    		parentBelongIdInt = testParentBelongIdInt;
    	}
    	
        if(approverStatus == 2) {
        	MessageTempleteDto weChatMessage = combineQwMessage(parentBelongId,businessChanceDto.getTraderName(),businessChanceDto.getBussinessChanceNo(),crmQuoteOrderDto.getBussinessChanceId(),belongUserName,belongId,belongUserName,quoteorderId);
        	log.info("消息发送入参:{}",JSON.toJSONString(weChatMessage));
        	
            //开发环境或者测试开启状态，执行
        	if("PRO".equals(projectEnv) || "1".equals(isTurnOnTest)) {
        		RestfulResult<WxUserDto> result = uacWxUserInfoApiService.sendMsgTempleteForLxCrm(weChatMessage);
        		if(!result.isSuccess()){
        			throw new Exception("消息发送失败...");
        		}
        	}
        	
        }else if(approverStatus == 1) {
        	//获取需要建群的用户信息
        	List<AppChatUserDto> appChatUserDtoList = queryUserByQuoteorderId(quoteorderId);
        	if(CollectionUtil.isEmpty(appChatUserDtoList)){
        		//无用户无法发起群聊
        		return quoteCreateAppChat;
        	}
        	CreateAppChatDto createAppChatDto = new CreateAppChatDto();
        	createAppChatDto.setQuoteorderId(quoteorderId);
        	List<Integer> userIdList = new ArrayList<>();
        	if(StringUtils.isNotBlank(crmQuoteOrderDto.getBuildChatUserIds())){
        		userIdList = Arrays.stream(crmQuoteOrderDto.getBuildChatUserIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
        	}
        	if(Objects.nonNull(parentBelongIdInt)){
        		userIdList.add(parentBelongIdInt);
        	}
        	createAppChatDto.setUserIds(userIdList);
        	quoteCreateAppChat = createAppChat(createAppChatDto,currentUser);
        }
        log.info("更新建群状态：{}",JSON.toJSON(updateOrderDto));
        crmQuoteOrderService.updateQuoteOrderForBuildUserIds(updateOrderDto,currentUser);
		return quoteCreateAppChat;
	}
	
	/**
	 * 拼接企微消息
	 * @param parentBelongId 
	 * @param belongUserName 
	 * @param belongId 
	 * @param quoteorderId 
	 * @return
	 */
	private MessageTempleteDto combineQwMessage(String parentBelongId,String traderName,String bussinessChanceNo,Integer bussinessChanceId,String belongName, Integer belongId, String belongUserName, Integer quoteorderId) {
		MessageTempleteDto weChatMessage = new MessageTempleteDto();
		weChatMessage.setToUser(parentBelongId);
		weChatMessage.setCardType("button_interaction");	
		weChatMessage.setMainTitleTitle("报价单企微建群申请");
		weChatMessage.setMainTitleDesc(traderName+bussinessChanceNo+"商机，"+belongUserName+"请求协同建群");	
		weChatMessage.setHorizontalContentListType(1);	
		weChatMessage.setHorizontalContentListKeyname("商机详情");
		weChatMessage.setHorizontalContentListValue("查看更多");
		weChatMessage.setHorizontalContentListUrl(businessUrl+bussinessChanceId);
		weChatMessage.setTask_id(UUID.randomUUID().toString());	
		weChatMessage.setButtonSelectionQuestionKey("1");
		weChatMessage.setButtonSelectionTitle("选择");
		weChatMessage.setButtonSelectionSelectedId("1");
		List<ButtonSelectionQuestionOption> buttonSelectionQuestionOptionList = new ArrayList<>();
		ButtonSelectionQuestionOption buttonSelectionQuestionOptionTy = new ButtonSelectionQuestionOption();
		buttonSelectionQuestionOptionTy.setButtonSelectionQuestionOptionId(belongId+"_"+belongUserName+"_"+quoteorderId);
		buttonSelectionQuestionOptionTy.setButtonSelectionQuestionOptionText("同意");
		buttonSelectionQuestionOptionList.add(buttonSelectionQuestionOptionTy);
		ButtonSelectionQuestionOption buttonSelectionQuestionOptionBh = new ButtonSelectionQuestionOption();
		buttonSelectionQuestionOptionBh.setButtonSelectionQuestionOptionId("0");
		buttonSelectionQuestionOptionBh.setButtonSelectionQuestionOptionText("驳回");
		buttonSelectionQuestionOptionList.add(buttonSelectionQuestionOptionBh);
		weChatMessage.setButtonSelectionQuestionOptionList(buttonSelectionQuestionOptionList);
		List<Button> buttonList = new ArrayList<>();
		Button buttonTy = new Button();
		if(!StringUtils.isEmpty(testParentBelongId)) {
			buttonTy.setButtonListKey(quoteorderId+"_"+testParentBelongId+"_"+testParentBelongName+"_1"+"_"+parentBelongId);
		}else {
			buttonTy.setButtonListKey(quoteorderId+"_"+belongId+"_"+belongUserName+"_1"+"_"+parentBelongId);
		}
		buttonTy.setButtonListStyle(1);
		buttonTy.setButtonListText("同意");
		buttonList.add(buttonTy);
		Button buttonBh = new Button();
		if(!StringUtils.isEmpty(testParentBelongId)) {
			buttonBh.setButtonListKey(quoteorderId+"_"+testParentBelongId+"_"+testParentBelongName+"_0"+"_"+parentBelongId);
		}else {
			buttonBh.setButtonListKey(quoteorderId+"_"+belongId+"_"+belongUserName+"_0"+"_"+parentBelongId);
		}
		buttonBh.setButtonListStyle(3);
		buttonBh.setButtonListText("驳回");
		buttonList.add(buttonBh);
		weChatMessage.setButtonList(buttonList);
		
		return weChatMessage;
	}

	public static void main(String[] args) {
		String ss =UUID.randomUUID().toString();
		System.out.println(ss);
	}

	@Override
	public UserDto queryChargeInfo(Integer quoteorderId) {
		CrmQuoteOrderDto crmQuoteOrderDto = crmQuoteOrderService.selectOne(quoteorderId);
    	BusinessChanceDto businessChanceDto = businessChanceService.viewDetail(crmQuoteOrderDto.getBussinessChanceId());
		//商机的userId就是归属销售
    	Integer belongId = businessChanceDto.getUserId();
		//根据这个归属销售，到uac查询他的parent_user_id既是他的主管
    	UserDto userDto = userApiService.searchByUserIdFromUac(belongId);
    	Integer parentBelongId = userDto.getParentUserId();
    	UserDto userDtoParent = null;
    	if(Objects.nonNull(userDto)) {
    		userDtoParent = userApiService.searchByUserIdFromUac(parentBelongId);
    	}
		return userDtoParent;
	}

	@Override
	public void updateQwMessageButton(String responseCode,String toUserId) {
		log.info("更新企微模板消息的按钮入参:{}",responseCode);
    	RestfulResult<WxUserDto> result = uacWxUserInfoApiService.updateMsgTempleteForLxCrm(responseCode,toUserId);
        if(!result.isSuccess()){
        	log.error("更新企微模板消息的按钮失败...",result.getMessage());
        }
	}
}
