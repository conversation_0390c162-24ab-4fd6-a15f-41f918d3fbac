package com.vedeng.trader.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.newtask.model.YXGTraderAptitude;
import com.rabbitmq.ErpMsgProducer;
import com.rabbitmq.ImMsgProducer;
import com.rabbitmq.OpMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Position;
import com.vedeng.authorization.model.User;
import com.vedeng.call.feign.RemoteCrmTaskApiService;
import com.vedeng.call.service.CallService;
import com.vedeng.common.constant.*;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.listenerEvent.PublicCustomerEvent;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.model.ResultInfo4Op;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.service.impl.HttpSendUtil;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.common.util.*;
import com.vedeng.customerbillperiod.dto.BillPeriodItem;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodAvailableAmountDto;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodAvailableAmountQueryDto;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodService;
import com.vedeng.erp.business.dto.PublicCustomerRecordDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.service.KingDeeSupplierApiService;
import com.vedeng.erp.market.api.MarketPlanApiService;
import com.vedeng.erp.trader.dto.*;
import com.vedeng.erp.trader.service.*;
import com.vedeng.finance.dao.CapitalBillMapper;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.model.AccountPeriod;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.TraderAccountPeriodApply;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.FileDelivery;
import com.vedeng.message.api.dto.request.ModuleMessageAddRequest;
import com.vedeng.message.api.enums.MessageSystemEnum;
import com.vedeng.message.api.enums.UserTypeEnum;
import com.vedeng.order.dao.BussinessChanceMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.enums.PaymentTypeEnum;
import com.vedeng.order.model.BussinessChance;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.dto.AssociatedCustomerGroupCountDto;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.phoneticWriting.dao.PhoneticWritingMapper;
import com.vedeng.phoneticWriting.model.PhoneticWriting;
import com.vedeng.system.dao.VerifiesInfoMapper;
import com.vedeng.system.model.Tag;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.system.model.vo.AccountSalerToGo;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.*;
import com.vedeng.trader.dto.TraderContactDto;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.dto.*;
import com.vedeng.trader.model.model.vo.CustomerBillPeriodDetailVo;
import com.vedeng.trader.model.vo.*;
import com.vedeng.trader.service.CommunicateService;
import com.vedeng.trader.service.TraderCustomerService;
import com.vedeng.trader.service.TraderDataService;
import com.vedeng.trader.service.search.CustomerExtInfoAggregator;
import com.vedeng.trader.service.search.CustomerSearchConditionsAssembler;
import com.vedeng.trader.service.search.ListCustomerQuery;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service("traderCustomerService")
public class TraderCustomerServiceImpl extends BaseServiceimpl implements TraderCustomerService {
    public static Logger logger = LoggerFactory.getLogger(TraderCustomerServiceImpl.class);


    @Autowired
    @Qualifier("customerAptitudeCommentMapper")
    private CustomerAptitudeCommentMapper customerAptitudeCommentMapper;

    @Autowired
    private TraderContactTransferRecordApiService traderContactTransferRecordApiService;
    @Autowired
    @Qualifier("traderMapper")
    private TraderMapper traderMapper;

    @Autowired
    @Qualifier("traderCustomerMapper")
    private TraderCustomerMapper traderCustomerMapper;

    @Autowired
    @Qualifier("rTraderJUserMapper")
    private RTraderJUserMapper rTraderJUserMapper;

    @Autowired
    @Qualifier("userMapper")
    private UserMapper userMapper;

    @Autowired
    private HttpSendUtil httpSendUtil;

    @Autowired
    @Qualifier("communicateRecordMapper")
    private CommunicateRecordMapper communicateRecordMapper;

    @Autowired
    @Qualifier("regionService")
    private RegionService regionService;

    @Autowired
    @Qualifier("communicateService")
    private CommunicateService communicateService;

    @Autowired
    @Qualifier("traderAddressMapper")
    private TraderAddressMapper traderAddressMapper;

    @Autowired
    @Qualifier("bussinessChanceMapper")
    private BussinessChanceMapper bussinessChanceMapper;

    @Autowired
    @Qualifier("phoneticWritingMapper")
    private PhoneticWritingMapper phoneticWritingMapper;

    @Autowired
    @Qualifier("webAccountMapper")
    private WebAccountMapper webAccountMapper;

    @Autowired
    @Qualifier("saleorderMapper")
    private SaleorderMapper saleorderMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    @Qualifier("goodsMapper")
    private GoodsMapper goodsMapper;

    @Autowired
    private VerifiesInfoMapper verifiesInfoMapper;

    @Autowired
    private TraderCustomerBidingInfoMapper traderCustomerBidingInfoMapper;

    @Autowired
    @Qualifier("mjxAccountAddressMapper")
    private MjxAccountAddressMapper mjxAccountAddressMapper;
    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
    @Autowired
    @Qualifier("traderCertificateMapper")
    private TraderCertificateMapper traderCertificateMapper;
    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    private MarketPlanApiService marketPlanApiService;

    @Value("${yxg_host_url}")
    private String yxgHostUrl;

    @Value("${b2b_business_division_id}")
    private Integer b2bbusinessDivisionId;
    @Value("${yi_xie_purchase_id}")
    private Integer YiXiePurchaseId;
    @Value("${scientific_research_training_id}")
    private Integer scientificResearchTrainingId;

    @Autowired
    private UserService userService;

    @Autowired
    private TraderContactGenerateMapper traderContactGenerateMapper;

    @Autowired
    private OpMsgProducer opMsgProducer;

    @Autowired
    private WebAccountInvitationLogMapper invitationLogMapper;

    @Autowired
    private TraderCustomerCategoryMapper traderCustomerCategoryMapper;

    @Autowired
    private RiskCheckService riskCheckService;

    @Autowired
    private TraderCertificateHistoryMapper traderCertificateHistoryMapper;

    @Autowired
    private WebAccountCertificateMapper certificateMapper;

    @Autowired
    private TraderMedicalCategoryMapper traderMedicalCategoryMapper;

    @Resource
    private TraderSupplierMapper traderSupplierMapper;

    @Resource
    private TraderDataService traderDataService;

    @Autowired
    private RTraderJUserModifyRecordMapper rTraderJUserModifyRecordMapper;

    @Resource
    private CustomerBillPeriodService customerBillPeriodService;

    @Autowired
    private AfterSalesMapper afterSalesMapper;

    @Autowired
    private CapitalBillMapper capitalBillMapper;

    @Autowired
    private ExpressMapper expressMapper;

    @Autowired
    private InvoiceMapper invoiceMapper;
    @Autowired
    private TraderContactMapper traderContactMapper;

    @Autowired
    ErpMsgProducer erpMsgProducer;
    @Resource
    private CustomerSearchConditionsAssembler customerSearchConditionsAssembler;
    @Resource
    private CustomerExtInfoAggregator customerExtInfoAggregator;

    @Value("${file_url}")
    protected String domain;

    @Value("${api_http}")
    protected String api_http;

    @Autowired
    private PublicCustomerRecordApiService publicCustomerRecordApiService;

    @Autowired
    private CallService callService;

    @Autowired
    private TraderSupplierApiService traderSupplierApiService;

    @Autowired
    private KingDeeSupplierApiService kingDeeSupplierApiService;

    @Autowired
    private TraderApiService traderApiService;

    @Autowired
    private TraderCustomerErpApiService traderCustomerErpApiService;


    @Autowired
    private TraderFinanceApiService traderFinanceApiService;

    @Autowired
    private TraderCustomerMarketingApiService traderCustomerMarketingApiService;

    @Autowired
    private TraderCustomerMarketingPrincipalApiService traderCustomerMarketingPrincipalApiService;

    @Autowired
    private CommunicateSummaryApiService communicateSummaryApiService;

    @Autowired
    private TraderWorkAreaApiService traderWorkAreaApiService;

    @Autowired
    RCommunicateTodoJAiApiService rCommunicateTodoJAiApiService;

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    @Autowired
    private CommunicateRecordApiService communicateRecordApiService;

    @Autowired
    RemoteCrmTaskApiService remoteCrmTaskApiService;

    @Autowired
    BusinessLeadsApiService businessLeadsApiService;


    
    @Override
    public void sendMessage2Mjx(TraderCustomer traderCustomer) {
        if (traderCustomer == null
                || traderCustomer.getTraderId() == null
                || !SysOptionConstant.ID_465.equals(traderCustomer.getCustomerNature())) {
            return;
        }
        WebAccount query = new WebAccount();
        query.setTraderId(traderCustomer.getTraderId());
        query.setBelongPlatform(TraderConstants.WEBACCOUNT_BELONG_BEDENG);
        List<WebAccount> webAccounts = webAccountMapper.getWebAccountListByParam(query);
        if (CollectionUtils.isEmpty(webAccounts)) {
            return;
        }
        Set<String> mobiles = new HashSet<>();
        for (WebAccount w : webAccounts) {
            if (w == null || StringUtil.isBlank(w.getMobile())) {
                continue;
            }
            mobiles.add(w.getMobile());
        }
        if (CollectionUtils.isEmpty(mobiles)) {
            return;
        }
        ModuleMessageAddRequest addRequest = initMjxMessge(mobiles);
        logger.info("客户资质审核通过，向消息中心发送消息", addRequest);
        try {
            String url = messageCenterUrl + "/message/sendBusinessMessage";
            com.alibaba.fastjson.TypeReference<ResultInfo4Op> typeReference = new com.alibaba.fastjson.TypeReference<ResultInfo4Op>() {
            };
            ResultInfo4Op resultInfo4Op = HttpRestClientUtil.restPost(url, typeReference, null, addRequest);
            if (resultInfo4Op == null || !resultInfo4Op.isSuccess()) {
                logger.error("客户资质审核通过，向消息中心发送消息失败", resultInfo4Op);
            }

        } catch (Exception ex) {
            logger.error("客户资质审核通过，向消息中心发送消息失败", ex);
        }
    }

    private ModuleMessageAddRequest initMjxMessge(Set<String> mobiles) {
        ModuleMessageAddRequest request = new ModuleMessageAddRequest();
        request.setModuleId(1);
        request.setMessageSystemId(MessageSystemEnum.MJX.getSystemId());
        Map<String, Object> appPushTarget = new HashMap<>();
        appPushTarget.put("intentType", "-1");
        request.setAppPushTarget(JsonUtils.convertObjectToJsonStr(appPushTarget));
        request.setFromUser(0);
        request.setToUserType(UserTypeEnum.CUSTOMER_USER.getType());
        request.setToUserMobiles(mobiles);
        return request;
    }

    @Override
    public TraderFinanceVo getTraderFinance(TraderFinanceVo finance) {
        String url = httpUrl + ErpConst.GET_TRADER_FINANCE;
        final TypeReference<ResultInfo<TraderFinanceVo>> TypeRef2 = new TypeReference<ResultInfo<TraderFinanceVo>>() {
        };
        try {
            ResultInfo<TraderFinanceVo> result2 = (ResultInfo<TraderFinanceVo>) HttpClientUtils.post(url, finance, clientId,
                    clientKey, TypeRef2);
            return (TraderFinanceVo) result2.getData();
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public ResultInfo syncTraderFinanceToPlatformOfYxg(TraderFinance traderFinance) {
        String taxNum = traderFinance.getTaxNum() == null ? "" : traderFinance.getTaxNum();
        String url = yxgHostUrl + ErpConst.SYNC_TRADER_FINANCE_TO_YXG + "?traderId=" + traderFinance.getTraderId() + "&taxNum=" + taxNum;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo> TypeRef2 = new TypeReference<ResultInfo>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.getForYxg(url, null, clientId,
                    clientKey, TypeRef2);
            return result2;
        } catch (IOException e) {
            logger.error("同步至医械购平台错误：", e);
            return new ResultInfo(-1, "操作失败");
        }
    }

    @Override
    public int changeTraderOwner(RTraderJUser rTraderJUser, User currentUser) {
        RTraderJUser traderJUser = new RTraderJUser();
        traderJUser.setTraderType(ErpConst.ONE);
        traderJUser.setTraderId(rTraderJUser.getTraderId());
        Integer preUserId = null;
        RTraderJUser ru = rTraderJUserMapper.getUserByTraderId(rTraderJUser.getTraderId());
        if (ru != null) {
            preUserId = ru.getUserId();
        }
        rTraderJUserMapper.deleteRTraderJUser(traderJUser);
        logger.info("【过期交易者归属变更记录】删除了交易者归属绑定关系 - traderId:{},oldUser:{}",
                traderJUser.getTraderId(), preUserId);
        Long nowTimeMillisecond = DateUtil.gainNowDate();
        deleteOldRTraderJUserModifyRecord(rTraderJUser, currentUser.getUserId(), nowTimeMillisecond);

        int insert = rTraderJUserMapper.insert(rTraderJUser);
        //记录交易者归属变更记录(新增最新的记录)
        logger.info("【新增交易者归属变更记录】新增了交易者归属绑定关系 - traderId:{},fromUser:{}, toUser:{}",
                rTraderJUser.getTraderId(), rTraderJUser.getUserId(), traderJUser.getUserId());
        insertRTraderJUserModifyRecordWhenUpdateTraderJUser(rTraderJUser, preUserId, currentUser.getUserId(), nowTimeMillisecond);

        //VDERP-3164 可合并商机已确认客户，归属销售逻辑优化 , 将客户的商机迁移到的新的归属销售名下
        logger.info("批量分配客户：{}的归属销售为：{}", rTraderJUser.getTraderId(), rTraderJUser.getUserId());
        bussinessChanceMapper.updateSaleUserOfBussinessChanceByTraderList(rTraderJUser.getUserId(), Collections.singletonList(rTraderJUser.getTraderId()));
        Trader trader = new Trader();
        trader.setTraderId(rTraderJUser.getTraderId());

        //VDERP-17057  【客户档案】ERP客户档案时间轴 客户划拨
        if (insert > 0) {
            try {
                TrackStrategy trackStrategy = null;
                TrackParamsData trackParamsData = new TrackParamsData();
                Map<String, Object> trackParams = new HashMap<>();
                trackParams.put("track_user", currentUser);
                //获取原归属销售和分配后的归属销售
                User newUser = userMapper.selectByPrimaryKey(rTraderJUser.getUserId());
                trackParams.put("newUserName", newUser.getUsername());
                trackParams.put("newUserNumber", newUser.getNumber());
                trackParams.put("traderId", rTraderJUser.getTraderId());
                //客户没有原始销售，展示文案与有原始销售不一样
                if (Objects.isNull(preUserId)) {
                    trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_NEW);
                    trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_NEW);
                    trackParamsData.setTrackParams(trackParams);
                } else {
                    User oldUser = userMapper.selectByPrimaryKey(preUserId);
                    trackParams.put("oldUserName", oldUser.getUsername());
                    trackParams.put("oldUserNumber", oldUser.getNumber());
                    trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_OLD);
                    trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_OLD);
                    trackParamsData.setTrackParams(trackParams);
                }
                trackParamsData.setTrackResult(ResultInfo.success());
                trackStrategy.track(trackParamsData);
            } catch (Exception e) {
                logger.error("埋点：{}，失败，不影响正常业务", EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_NEW.getArchivedName(), e);
            }
        }

        if (insert > 0) {
            sendMsgToImIfChangeTraderBelongUser(rTraderJUser.getTraderId(), preUserId, rTraderJUser.getUserId());
            WebAccountVo webAccountVo = new WebAccountVo();
            webAccountVo.setUserId(rTraderJUser.getUserId());
            webAccountVo.setTraderId(rTraderJUser.getTraderId());
            webAccountVo.setModTime(new Date());
            webAccountMapper.updateErpUserId(webAccountVo);
            return 1;
        }
        return 0;
    }

    /**
     * 同步客户档案
     * @param rTraderJUser
     * @param publicAssignDto
     * @param currentUser
     */
    public void async(RTraderJUser rTraderJUser,PublicAssignDto publicAssignDto,User currentUser){
        List<User> userList = userMapper.getUserByUserIds(Arrays.asList(publicAssignDto.getUserId(), publicAssignDto.getOriginUserId()));
        Map<Integer, User> userIdMap = userList.stream().collect(Collectors.toMap(User::getUserId, Function.identity()));
        try {
            TrackStrategy trackStrategy;
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            trackParams.put("track_user", currentUser);
            //获取原归属销售和分配后的归属销售
            trackParams.put("newUserName", userIdMap.get(rTraderJUser.getUserId()).getUsername());
            trackParams.put("newUserNumber", userIdMap.get(rTraderJUser.getUserId()).getNumber());
            trackParams.put("traderId", rTraderJUser.getTraderId());
            //客户没有原始销售，展示文案与有原始销售不一样
            trackParams.put("oldUserName", userIdMap.get(publicAssignDto.getOriginUserId()).getUsername());
            trackParams.put("oldUserNumber", userIdMap.get(publicAssignDto.getOriginUserId()).getNumber());
            trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_PUBLIC_CUSTOMER_CHANGE_BY_OLD);
            trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_PUBLIC_CUSTOMER_CHANGE_BY_OLD);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackStrategy.track(trackParamsData);
        }catch(Exception e) {
            logger.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.BASE_INFO_PUBLIC_CUSTOMER_CHANGE_BY_OLD.getArchivedName(),e);
        }
    }

    private void insertRTraderJUserModifyRecordWhenUpdateTraderJUser(RTraderJUser traderJUser, Integer oldUserId, Integer creator, Long nowTime) {
        //记录交易者归属变更记录(新增最新的记录)
        RTraderJUserModifyRecord rTraderJUserModifyRecordAdd = new RTraderJUserModifyRecord();
        rTraderJUserModifyRecordAdd.setTraderType(traderJUser.getTraderType());
        rTraderJUserModifyRecordAdd.setUserId(traderJUser.getUserId());
        rTraderJUserModifyRecordAdd.setOldUserId(oldUserId);
        rTraderJUserModifyRecordAdd.setCreator(creator);
        rTraderJUserModifyRecordAdd.setStartTime(nowTime);
        rTraderJUserModifyRecordAdd.setTraderId(traderJUser.getTraderId());
        rTraderJUserModifyRecordMapper.insert(rTraderJUserModifyRecordAdd);
    }

    private void deleteOldRTraderJUserModifyRecord(RTraderJUser rTraderJUser, Integer updater, Long nowTime) {
        //记录交易者归属变更记录(删除掉以前的记录)
        RTraderJUserModifyRecord rTraderJUserModifyRecord = new RTraderJUserModifyRecord();
        rTraderJUserModifyRecord.setTraderId(rTraderJUser.getTraderId());
        rTraderJUserModifyRecord.setEndTime(nowTime);
        rTraderJUserModifyRecord.setUpdater(updater);
        rTraderJUserModifyRecordMapper.updateEndTimeByTraderId(rTraderJUserModifyRecord);
    }

    @Override
    public String getOrderMedicalOfficeStr(TraderOrderGoods param) {

        List<String> skus = saleorderMapper.getOrderGoodsSkuByTraderId(param);
        if (CollectionUtils.isEmpty(skus)) {
            return null;
        }
        List<String> departments = goodsMapper.getGoodsMedicalOffice(skus);
        if (CollectionUtils.isEmpty(departments)) {
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < departments.size(); i++) {

            if (StringUtil.isBlank(departments.get(i))) {
                continue;
            }
            if (i == departments.size() - 1) {
                stringBuilder.append(departments.get(i));
                continue;
            }
            stringBuilder.append(departments.get(i) + "、");

        }
        return stringBuilder.toString();
    }

    @Override
    public CustomerAptitudeComment getCustomerAptitudeCommentByTraderId(Integer traderId) {
        return customerAptitudeCommentMapper.selectByCustomerId(traderId);
    }

    @Override
    public int addCustomerAptitudeComment(CustomerAptitudeComment addComment) {
        CustomerAptitudeComment comment = customerAptitudeCommentMapper.selectByCustomerId(addComment.getTraderCustomerId());
        if (comment != null && comment.getCommentId() != null) {
            customerAptitudeCommentMapper.updateByTraderId(addComment);
        } else if (StringUtil.isNotBlank(addComment.getComment())) {
            customerAptitudeCommentMapper.insert(addComment);
        }
        return 1;
    }

    @Override
    public User getPersonalUser(Integer traderId) {
        User user = userMapper.getUserByTraderId(traderId, ErpConst.ONE);
        return user;
    }

    @Override
    public List<TraderCustomerCategory> getTraderCustomerCategoryByParentId(
            TraderCustomerCategory traderCustomerCategory) {
        String url = httpUrl + "trader/gettradercustomercategory.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<TraderCustomerCategory>>> TypeRef2 = new TypeReference<ResultInfo<List<TraderCustomerCategory>>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomerCategory, clientId,
                    clientKey, TypeRef2);
            List<TraderCustomerCategory> categoryList = (List<TraderCustomerCategory>) result2.getData();
            return categoryList;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public List<TraderCustomerAttributeCategory> getTraderCustomerAttributeByCategoryId(
            TraderCustomerAttributeCategory traderCustomerAttributeCategory) {
        String url = httpUrl + "trader/gettradercustomerattribute.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<TraderCustomerAttributeCategory>>> TypeRef2 = new TypeReference<ResultInfo<List<TraderCustomerAttributeCategory>>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomerAttributeCategory, clientId,
                    clientKey, TypeRef2);
            List<TraderCustomerAttributeCategory> attributeList = (List<TraderCustomerAttributeCategory>) result2
                    .getData();
            return attributeList;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public List<TraderCustomerAttributeCategory> getTraderCustomerChildAttribute(
            TraderCustomerAttributeCategory traderCustomerAttributeCategory) {
        String url = httpUrl + "trader/gettradercustomerchildattribute.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<TraderCustomerAttributeCategory>>> TypeRef2 = new TypeReference<ResultInfo<List<TraderCustomerAttributeCategory>>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomerAttributeCategory, clientId,
                    clientKey, TypeRef2);
            List<TraderCustomerAttributeCategory> attributeList = (List<TraderCustomerAttributeCategory>) result2
                    .getData();
            return attributeList;
        } catch (IOException e) {
            return null;
        }
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public TraderCustomer saveCustomer(Trader trader, HttpServletRequest request, HttpSession session) {
        logger.info("saveCustomer trader:{}", JSON.toJSONString(trader));
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();

        // 交易者基本信息
        trader.setCompanyId(user.getCompanyId());
        trader.setIsEnable(ErpConst.ONE);
        TraderDealerFrontDto traderDealerFrontDto = null;

        if (StringUtil.isNotEmpty(trader.getTheDataOfThePage())) {
            traderDealerFrontDto = JSON.parseObject(trader.getTheDataOfThePage(), TraderDealerFrontDto.class);
            TraderDealerFrontDto.Principal principal = traderDealerFrontDto.getPrincipal();
            if (Objects.nonNull(principal)) {
                principal.setEffectiveness(trader.getEffectiveness());
            }
        }


        if (Integer.parseInt(request.getParameter("zone")) > 0) {
            trader.setAreaId(Integer.parseInt(request.getParameter("zone")));
            trader.setAreaIds(request.getParameter("province") + "," + request.getParameter("city") + "," + request.getParameter("zone"));
        } else {
            trader.setAreaId(Integer.parseInt(request.getParameter("city")));
            trader.setAreaIds(request.getParameter("province") + "," + request.getParameter("city") + "," + request.getParameter("zone"));
        }

        if (Integer.parseInt(request.getParameter("warehouseZone")) > 0) {
            trader.setWarehouseAreaId(Integer.parseInt(request.getParameter("warehouseZone")));
            trader.setWarehouseAreaIds(request.getParameter("warehouseProvince") + "," + request.getParameter("warehouseCity") + "," + request.getParameter("warehouseZone"));
        } else {
            trader.setWarehouseAreaId(Integer.parseInt(request.getParameter("warehouseCity")));
            trader.setWarehouseAreaIds(request.getParameter("warehouseProvince") + "," + request.getParameter("warehouseCity") + "," + request.getParameter("warehouseZone"));
        }

        trader.setAddTime(time);
        trader.setCreator(user.getUserId());
        trader.setModTime(time);
        trader.setUpdater(user.getUserId());


        // 客户
        TraderCustomer traderCustomer = trader.getTraderCustomer();
        boolean isTerminal = false;
        boolean isDealer = false;
        Integer traderCustomerMarketingType = null;
        //冗余字段 客户类型 客户性质
        if (null != request.getParameter("traderCustomerCategoryIds")) {
            String ids = request.getParameter("traderCustomerCategoryIds");
            String[] idsArr = ids.split(",");
            if (null != idsArr[0]) {
                if (idsArr[0].equals("1")) {
                    traderCustomer.setCustomerType(SysOptionConstant.ID_426);
                }
                if (idsArr[0].equals("2")) {
                    traderCustomer.setCustomerType(SysOptionConstant.ID_427);
                }
            }
            if (null != idsArr[1]) {
                if (idsArr[1].equals("3") || idsArr[1].equals("5")) {
                    traderCustomer.setCustomerNature(SysOptionConstant.ID_465);
                }
                if (idsArr[1].equals("4") || idsArr[1].equals("6")) {
                    traderCustomer.setCustomerNature(SysOptionConstant.ID_466);
                }
                if (idsArr[1].equals("6")) {
                    isTerminal = true;
                    if (idsArr.length >= 3) {
                        traderCustomerMarketingType = Integer.parseInt(idsArr[2]);
                    }

                }
                if (idsArr[1].equals("5")) {
                    isDealer = true;
                }
            }
        }

        traderCustomer.setIsEnable(ErpConst.ONE);

        if (StringUtil.isNotEmpty(traderCustomer.getRegisteredDateStr())) {
            SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
            try {
                traderCustomer.setRegisteredDate(dateformat.parse(traderCustomer.getRegisteredDateStr()));
            } catch (ParseException e) {
                return null;
            }
        }

        traderCustomer.setAddTime(time);
        traderCustomer.setCreator(user.getUserId());
        traderCustomer.setModTime(time);
        traderCustomer.setUpdater(user.getUserId());

        // 客户代理品牌
        List<TraderCustomerAgentBrand> agentBrands = new ArrayList<>();
        if (null != request.getParameterValues("agentBrandId")) {
            String[] agentBrandIds = request.getParameterValues("agentBrandId");

            for (String brandId : agentBrandIds) {
                TraderCustomerAgentBrand traderCustomerAgentBrand = new TraderCustomerAgentBrand();
                traderCustomerAgentBrand.setBrandId(Integer.parseInt(brandId));
                agentBrands.add(traderCustomerAgentBrand);
            }

            traderCustomer.setTraderCustomerAgentBrands(agentBrands);
        }

        //基础医疗经销商
        Integer basicMedicalDealer = 0;
        String lcfx = null;
        if (null != request.getParameter("isLcfx")) {
            lcfx = request.getParameter("isLcfx");
        }
        // 客户属性
        List<TraderCustomerAttribute> customerAttributes = new ArrayList<>();
        if (null != request.getParameterValues("attributeId")) {
            String[] attributeIds = request.getParameterValues("attributeId");

            for (String attIds : attributeIds) {

                TraderCustomerAttribute customerAttribute = new TraderCustomerAttribute();
                String[] attArr = attIds.split("_");

                customerAttribute.setAttributeCategoryId(Integer.parseInt(attArr[0]));
                customerAttribute.setAttributeId(Integer.parseInt(attArr[1]));

                String pName = "attributedesc_" + attIds;
                if (null != request.getParameter(pName)) {
                    customerAttribute.setAttributeOther((String) request.getParameter(pName));
                }

                customerAttributes.add(customerAttribute);

                //基础医疗经销商
                if (lcfx != null && lcfx.equals("1")
                        && (Integer.parseInt(attArr[1]) == 95
                        || Integer.parseInt(attArr[1]) == 96
                        || Integer.parseInt(attArr[1]) == 97
                        || Integer.parseInt(attArr[1]) == 99
                        || Integer.parseInt(attArr[1]) == 100)) {
                    basicMedicalDealer = 1;
                }
            }

            traderCustomer.setTraderCustomerAttributes(customerAttributes);

        }

        traderCustomer.setBasicMedicalDealer(basicMedicalDealer);

        // 客户经营区域
        List<TraderCustomerBussinessArea> bussinessAreas = new ArrayList<>();
        if (null != request.getParameterValues("bussinessAreaId")) {
            String[] bussinessAreaIds = request.getParameterValues("bussinessAreaId");
            String[] bussinessAreaIdsStr = request.getParameterValues("bussinessAreaIds");

            for (Integer i = 0; i < bussinessAreaIds.length; i++) {
                TraderCustomerBussinessArea traderCustomerBussinessArea = new TraderCustomerBussinessArea();
                traderCustomerBussinessArea.setAreaId(Integer.parseInt(bussinessAreaIds[i]));
                traderCustomerBussinessArea.setAreaIds(bussinessAreaIdsStr[i]);
                bussinessAreas.add(traderCustomerBussinessArea);
            }

            traderCustomer.setTraderCustomerBussinessAreas(bussinessAreas);
        }

        // 客户经营品牌
        List<TraderCustomerBussinessBrand> bussinessBrands = new ArrayList<>();
        if (null != request.getParameterValues("bussinessBrandId")) {
            String[] bussinessBrandIds = request.getParameterValues("bussinessBrandId");
            for (String brandId : bussinessBrandIds) {
                TraderCustomerBussinessBrand traderCustomerBussinessBrand = new TraderCustomerBussinessBrand();
                traderCustomerBussinessBrand.setBrandId(Integer.parseInt(brandId));
                bussinessBrands.add(traderCustomerBussinessBrand);
            }

            traderCustomer.setTraderCustomerBussinessBrands(bussinessBrands);
        }

        // 财务信息
        TraderFinance traderFinance = trader.getTraderFinance();
        if (traderFinance != null) {
            traderFinance.setTraderType(1);
            traderFinance.setAddTime(time);
            traderFinance.setCreator(user.getUserId());
            traderFinance.setModTime(time);
            traderFinance.setUpdater(user.getUserId());
        }
        if (Objects.isNull(trader.getBelongPlatform()) || trader.getBelongPlatform() == 0) {
            trader.setBelongPlatform(userService.getBelongPlatformOfUser(user.getUserId(), user.getCompanyId()));
        }


        TraderDto newTraderDto = new TraderDto();
        BeanUtil.copyProperties(trader, newTraderDto, true);

        TraderCustomerErpDto traderCustomerErpDto = new TraderCustomerErpDto();
        BeanUtil.copyProperties(traderCustomer, traderCustomerErpDto, true);
        // 特殊字段处理
        traderCustomerErpDto.setOrganizationCode(traderCustomer.getOrgNumber());
        traderCustomerErpDto.setUnifiedSocialCreditIdentifier(traderCustomer.getCreditCode());

        traderCustomerErpDto.setTraderCustomerAttributeDtoList(customerAttributes.stream().map(x -> {
            TraderCustomerAttributeDto data = new TraderCustomerAttributeDto();
            data.setAttributeId(x.getAttributeId());
            data.setAttributeOther(x.getAttributeOther());
            data.setAttributeCategoryId(x.getAttributeCategoryId());
            data.setSubCategoryIds(x.getSubCategoryIds());
            return data;
        }).collect(Collectors.toList()));
        traderCustomerErpDto.setTraderCustomerAgentBrandDtoList(agentBrands.stream().map(x -> {
            TraderCustomerAgentBrandDto data = new TraderCustomerAgentBrandDto();
            data.setBrandId(x.getBrandId());
            return data;
        }).collect(Collectors.toList()));
        traderCustomerErpDto.setTraderCustomerBussinessAreaDtoList(bussinessAreas.stream().map(x -> {
            TraderCustomerBussinessAreaDto data = new TraderCustomerBussinessAreaDto();
            data.setAreaId(x.getAreaId());
            data.setAreaIds(x.getAreaIds());
            return data;
        }).collect(Collectors.toList()));
        traderCustomerErpDto.setTraderCustomerBussinessBrandDtoList(bussinessBrands.stream().map(x -> {
            TraderCustomerBussinessBrandDto data = new TraderCustomerBussinessBrandDto();
            data.setBrandId(x.getBrandId());
            return data;
        }).collect(Collectors.toList()));

        TraderCustomerErpDto queryByName = traderCustomerErpApiService.queryByName(newTraderDto.getTraderName());
        if (Objects.nonNull(queryByName) && Objects.nonNull(queryByName.getTraderCustomerId())) {
            throw new ServiceException("客户已存在");
        }
        if (Objects.nonNull(queryByName) && Objects.isNull(queryByName.getTraderCustomerId()) && Objects.nonNull(queryByName.getTraderId())) {
            newTraderDto.setTraderId(queryByName.getTraderId());
            trader.setTraderId(queryByName.getTraderId());
        }


        if (Objects.isNull(newTraderDto.getTraderId())) {
            traderApiService.add(newTraderDto);
        }

        TraderWorkareaDto traderWorkareaDto = new TraderWorkareaDto();
        if (Objects.nonNull(trader.getWork_zone()) && trader.getWork_zone() > 0) {
            traderWorkareaDto.setAreaId(trader.getWork_zone());
            traderWorkareaDto.setTraderId(newTraderDto.getTraderId());
            traderWorkAreaApiService.add(traderWorkareaDto);
        }
        traderCustomerErpDto.setTraderId(newTraderDto.getTraderId());
        traderCustomerErpDto.setInvalidReason(trader.getInvalidReason());
        traderCustomerErpDto.setOtherReason(trader.getOtherReason());
        traderCustomerErpApiService.addContainsOtherData(traderCustomerErpDto);

        if (Objects.nonNull(traderFinance)) {
            TraderFinanceDto traderFinanceDto = new TraderFinanceDto();
            BeanUtil.copyProperties(traderFinance, traderFinanceDto, true);
            traderFinanceDto.setTraderId(newTraderDto.getTraderId());
            traderFinanceApiService.add(traderFinanceDto);
        }

        traderCustomer.setTraderId(traderCustomerErpDto.getTraderId());
        traderCustomer.setTraderCustomerId(traderCustomerErpDto.getTraderCustomerId());

        // 新表
        if (isTerminal) {
            TraderCustomerMarketingTerminalDto traderCustomerMarketingTerminalDto = traderCustomer.getTraderCustomerMarketingDto();
            traderCustomerMarketingTerminalDto.setTraderCustomerId(traderCustomer.getTraderCustomerId());
            traderCustomerMarketingTerminalDto.setTraderId(traderCustomer.getTraderId());
            traderCustomerMarketingTerminalDto.setTraderCustomerMarketingType(traderCustomerMarketingType);
            traderCustomerMarketingApiService.add(traderCustomerMarketingTerminalDto);
        }
        if (isDealer && Objects.nonNull(traderDealerFrontDto)) {
            traderDealerFrontDto.setTraderCustomerId(traderCustomerErpDto.getTraderCustomerId());
            traderDealerFrontDto.setTraderId(traderCustomerErpDto.getTraderId());


            TraderCustomerMarketingPrincipalDto principalDto = traderCustomerMarketingPrincipalApiService.processFrontData(traderDealerFrontDto);

            if (Objects.nonNull(principalDto)) {
                traderCustomerMarketingPrincipalApiService.add(principalDto);
            }

            List<TraderCustomerMarketingTerminalDto> terminalDtoList = traderCustomerMarketingApiService.processFrontData(traderDealerFrontDto);
            if (CollUtil.isNotEmpty(terminalDtoList)) {
                traderCustomerMarketingApiService.addAll(terminalDtoList);
            }
        }

        // 保存归属
        RTraderJUser rTraderJUser = new RTraderJUser();
        rTraderJUser.setTraderId(newTraderDto.getTraderId());
        rTraderJUser.setUserId(user.getUserId());
        rTraderJUser.setTraderType(ErpConst.ONE);

        rTraderJUserMapper.insert(rTraderJUser);

        logger.info("【新增交易者归属变更记录】新增了交易者归属绑定关系 - traderId:{},fromUser:{}, toUser:{}",
                rTraderJUser.getTraderId(), "无", rTraderJUser.getUserId());
        insertRTraderJUserModifyRecordWhenInsertrTraderJUser(rTraderJUser);

        //发送消息 trader
        Trader traderByTrader = traderMapper.getTraderByTraderId(newTraderDto.getTraderId());
        TraderReception traderReception = new TraderReception();

        traderReception.setTraderName(traderByTrader.getTraderName());
        traderReception.setTraderId(traderByTrader.getTraderId());
        // 如果客户属性不为空 就是集采的客户 推送给前台
        // begin VDERP-10111 等前台创建账号后再置为集采
        if (needSyncToJC(traderByTrader)) {
            if (traderByTrader.getParentId() == null || traderByTrader.getParentId() == 0) {
                traderReception.setTraderType(0); //0总公司  1分公司
                traderReception.setParentTraderId(null);
            } else {
                traderReception.setTraderType(1);
                traderReception.setParentTraderId(traderByTrader.getParentId());
            }
            String traderReceptionStr = com.alibaba.fastjson.JSONObject.toJSONString(traderReception, SerializerFeature.WriteMapNullValue);
            try {
                logger.info("创建客户发送消息到前台 start=======" + JSON.toJSONString(traderReceptionStr));
                erpMsgProducer.sendMsg(RabbitConfig.JC_ERP_ORG_EXCHANGE, RabbitConfig.JC_ERP_ORG_ROUTINGKEY, traderReceptionStr);
            } catch (Exception e) {
                logger.error("创建客户发送消息到前台失败" + JSON.toJSONString(traderReceptionStr));

            }
        }

        // AI助手
        if (trader.getIsAiAssistant() != null && trader.getIsAiAssistant().equals(1)) {
            RCommunicateTodoJAiDto dto = new RCommunicateTodoJAiDto();
            dto.setTraderId(traderCustomerErpDto.getTraderId());
            dto.setCommunicateRecordId(trader.getAiCommunicateRecordId());
            rCommunicateTodoJAiApiService.updateTraderSign(dto);
        }

        return traderCustomer;
    }

    /**
     * @return void
     * @Description 新增交易者归属信息更改记录
     * <AUTHOR>
     * @Date 17:09 2021/8/4
     * @Param [rTrader]
     **/
    private void insertRTraderJUserModifyRecordWhenInsertrTraderJUser(RTraderJUser rTrader) {
        RTraderJUserModifyRecord rTraderJUserModifyRecord = new RTraderJUserModifyRecord();
        rTraderJUserModifyRecord.setTraderType(rTrader.getTraderType());
        rTraderJUserModifyRecord.setUserId(rTrader.getUserId());
        rTraderJUserModifyRecord.setCreator(rTrader.getUserId());
        rTraderJUserModifyRecord.setStartTime(DateUtil.gainNowDate());
        rTraderJUserModifyRecord.setTraderId(rTrader.getTraderId());
        rTraderJUserModifyRecordMapper.insert(rTraderJUserModifyRecord);
    }

    @Override
    public TraderCustomerVo getTraderCustomerBaseInfo(TraderCustomer traderCustomer) {
        // 接口调用
        String url = httpUrl + "trader/getcustomerbaseinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderCustomerVo>> TypeRef2 = new TypeReference<ResultInfo<TraderCustomerVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomer, clientId, clientKey,
                    TypeRef2);
            TraderCustomerVo res = (TraderCustomerVo) result2.getData();
            if (null != res.getTraderSupplierVo()) {
                TraderSupplierVo traderSupplierVo = res.getTraderSupplierVo();
                User sale = userMapper.getUserByTraderId(traderSupplierVo.getTraderId(), ErpConst.TWO);
                if (null != sale) {
                    traderSupplierVo.setOwnerSale(sale.getUsername());
                }
            }
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderCustomerVo getTraderCustomerManageInfo(TraderCustomer traderCustomer, HttpSession session) {
        User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
        // 接口调用
        String url = httpUrl + "trader/getcustomermanageinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderCustomerVo>> TypeRef2 = new TypeReference<ResultInfo<TraderCustomerVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomer, clientId, clientKey,
                    TypeRef2);
            TraderCustomerVo res = (TraderCustomerVo) result2.getData();
            if (null != res) {
                User sale = userMapper.getUserByTraderId(res.getTraderId(), ErpConst.ONE);
                if (null != sale) {
                    res.setOwnerSale(sale.getUsername());
                }

                CommunicateRecord communicateRecord = new CommunicateRecord();
                communicateRecord.setTraderId(res.getTraderId());
                communicateRecord.setCompanyId(session_user.getCompanyId());
                communicateRecord.setTraderType(ErpConst.ONE);

                CommunicateRecord customerCommunicateCount = communicateRecordMapper.getTraderCommunicateCount(communicateRecord);
                if (null != customerCommunicateCount) {
                    res.setCommuncateCount(customerCommunicateCount.getCommunicateCount());
                    res.setLastCommuncateTime(customerCommunicateCount.getLastCommunicateTime());
                }
                res.setCustomerProperty(getCustomerCategory(res.getTraderCustomerId()));
                User user = userMapper.getUserByTraderId(res.getTraderId(), ErpConst.ONE);
                if (null != user) {
                    res.setPersonal(user.getUsername());
                }
				/*//客户
				List<Integer> traderCustomerIds = new ArrayList<>();
				traderCustomerIds.add(res.getTraderCustomerId());
				communicateRecord.setTraderCustomerIds(traderCustomerIds);
				//商机
				if(null != res.getEnquiryOrderIds() && res.getEnquiryOrderIds().size() > 0){
					communicateRecord.setEnquiryOrderIds(res.getEnquiryOrderIds());
				}
				//报价
				if(null != res.getQuoteOrderIds() && res.getQuoteOrderIds().size() > 0){
					communicateRecord.setQuoteOrderIds(res.getQuoteOrderIds());
				}
				//订单
				if(null != res.getSaleOrderIds() && res.getSaleOrderIds().size() > 0){
					communicateRecord.setSaleOrderIds(res.getSaleOrderIds());
				}
				//售后
				if(null != res.getServiceOrderIds() && res.getServiceOrderIds().size() > 0){
					communicateRecord.setServiceOrderIds(res.getServiceOrderIds());
				}

				CommunicateRecord customerCommunicateCount = communicateRecordMapper.getCustomerCommunicateCount(communicateRecord);
				 */

            }
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderCustomerVo getOrderCoverInfo(TraderOrderGoods traderOrderGoods) {
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderCustomerVo>> TypeRef = new TypeReference<ResultInfo<TraderCustomerVo>>() {
        };
        String url = httpUrl + "trader/getordercoverinfo.htm";
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, traderOrderGoods, clientId, clientKey, TypeRef);
            TraderCustomerVo res = (TraderCustomerVo) result.getData();

            //去重订单覆盖品类，订单覆盖品牌，订单覆盖产品+
            List<String> categoryNameList = new ArrayList<>();
            List<String> brandNameList = new ArrayList<>();
            List<String> areaList = new ArrayList<>();
            List<Integer> areaIdList = new ArrayList<>();

            List<TraderOrderGoods> traderOrderGoodsList = res.getTraderOrderGoodsList();
            if (traderOrderGoodsList != null && traderOrderGoodsList.size() > 0) {
                for (TraderOrderGoods traderOrderGoods1 : traderOrderGoodsList) {
                    categoryNameList.add(traderOrderGoods1.getCategoryName());
                    brandNameList.add(traderOrderGoods1.getBrandName());

                    if (null != traderOrderGoods1.getAreaId() && traderOrderGoods1.getAreaId() > 0) {
                        areaIdList.add(traderOrderGoods1.getAreaId());

                    }

//					if(null != traderOrderGoods1.getAreaName().trim() && !"".equals(traderOrderGoods1.getAreaName().trim())){
//						areaList.add(traderOrderGoods1.getAreaName());
//					}

//					if(traderOrderGoods1.getAreaId() != null && traderOrderGoods1.getAreaId() > 0){
//						areaList.add((String)regionService.getRegion(traderOrderGoods1.getAreaId(), 2));
//					}
                }
                areaIdList = new ArrayList(new HashSet(areaIdList));
                if (areaIdList.size() > 0) {
                    for (Integer areaId : areaIdList) {
                        String regionName = (String) regionService.getRegion(areaId, 2);
                        if (null != regionName) {
                            areaList.add(regionName);
                        }
                    }
                }
            }
            res.setCategoryNameList(new ArrayList(new HashSet(categoryNameList)));
            res.setBrandNameList(new ArrayList(new HashSet(brandNameList)));
            res.setAreaList(new ArrayList(new HashSet(areaList)));

            return res;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return null;
    }

    @Override
    public TraderCustomer saveEditManageInfo(TraderCustomer traderCustomer, HttpServletRequest request,
                                             HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();

        Trader trader = new Trader();
        trader.setCompanyId(user.getCompanyId());

        traderCustomer.setModTime(time);
        traderCustomer.setUpdater(user.getUserId());
        traderCustomer.setTrader(trader);

        // 客户属性(战略合作伙伴)
        if (null != request.getParameterValues("attributeId")) {
            String[] attributeIds = request.getParameterValues("attributeId");
            List<TraderCustomerAttribute> customerAttributes = new ArrayList<>();
            for (String attIds : attributeIds) {
                TraderCustomerAttribute customerAttribute = new TraderCustomerAttribute();

                customerAttribute.setAttributeCategoryId(SysOptionConstant.ID_CUSTOMER_ATTRIBUTE_CATEGORY_26);
                customerAttribute.setAttributeId(Integer.parseInt(attIds));
                customerAttribute.setTraderCustomerId(traderCustomer.getTraderCustomerId());
                customerAttributes.add(customerAttribute);
            }

            traderCustomer.setTraderCustomerAttributes(customerAttributes);

        }

        // 标签
        if (null != request.getParameterValues("tagId")) {// 标签库标签
            String[] tagIds = request.getParameterValues("tagId");
            List<Tag> tagList = new ArrayList<>();
            for (String tagId : tagIds) {
                Tag tag = new Tag();
                tag.setTagType(SysOptionConstant.ID_30);
                tag.setTagId(Integer.parseInt(tagId));
                tag.setCompanyId(user.getCompanyId());
                tagList.add(tag);
            }

            traderCustomer.setTag(tagList);
        }
        if (null != request.getParameterValues("tagName")) {// 自定义标签
            String[] tagNames = request.getParameterValues("tagName");
            List<String> tagNameList = new ArrayList<>();
            for (String tagName : tagNames) {
                tagNameList.add(tagName);
            }

            traderCustomer.setTagName(tagNameList);
        }

        // 接口调用
        String url = httpUrl + "trader/saveeditmanageinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderCustomer>> TypeRef2 = new TypeReference<ResultInfo<TraderCustomer>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomer, clientId, clientKey,
                    TypeRef2);
            TraderCustomer res = (TraderCustomer) result2.getData();

            return res;
        } catch (IOException e) {
            return null;
        }
    }


    @Transactional(readOnly = true)
    @Override
    public Map<String, Object> getTraderCustomerVoPage(TraderCustomerVo traderCustomerVo, Page page, List<User> userList) {
        if (traderCustomerVo == null || page == null) {
            return Collections.emptyMap();
        }

        List<TraderCustomerVo> traderCustomerVoList = listTraderCustomerVoByPage(traderCustomerVo, page, userList);

        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("list", traderCustomerVoList);
        resultMap.put("page", page);

        return resultMap;
    }


    @Value(value = "${customerSearchOrderBy:N}")
    private String customerSearchOrderBy;

    private List<TraderCustomerVo> listTraderCustomerVoByPage(TraderCustomerVo traderCustomerVo, Page page, List<User> userList) {
        ListCustomerQuery listCustomerQuery = new ListCustomerQuery();
        listCustomerQuery.setCustomerSearchOrderBy(customerSearchOrderBy);
        customerSearchConditionsAssembler.assemble(listCustomerQuery, traderCustomerVo);

        final List<TraderCustomerVo> customerVoList = traderCustomerMapper.getCustomerVolistpage(listCustomerQuery, page);

        if (CollectionUtils.isNotEmpty(customerVoList)) {
            List<Integer> userIdList = Optional.ofNullable(userList).orElse(Collections.emptyList()).stream().map(User::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
            customerExtInfoAggregator.aggregate(customerVoList, userIdList, traderCustomerVo.getAptitudeStatus(), traderCustomerVo.getCustomerStatus());
        }

        return customerVoList;
    }


    /**
     * <b>Description:</b><br>
     * 是否置顶
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:41:36
     */
    @SuppressWarnings("rawtypes")
    @Override
    public ResultInfo isStick(TraderCustomer traderCustomer, User user) {
        //TraderCustomer ts = new TraderCustomer();
//		ts.setTraderCustomerId(id);
//		ts.setIsTop(isTop);
        traderCustomer.setUpdater(user.getUserId());
        traderCustomer.setModTime(DateUtil.sysTimeMillis());
        return update(traderCustomer, httpUrl + ErpConst.TRADER_CUSTOMER_TOP);
    }

    /**
     * <b>Description:</b><br>
     * 是否禁用客户
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:41:36
     */
    @SuppressWarnings("rawtypes")
    @Override
    public ResultInfo isDisabled(User user, TraderCustomer traderCustomer) {
//		TraderCustomer ts = new TraderCustomer();
//		ts.setTraderCustomerId(id);
//		ts.setIsEnable(isDisabled);
        traderCustomer.setUpdater(user.getUserId());
        traderCustomer.setModTime(System.currentTimeMillis());
        if (traderCustomer.getIsEnable() == 0) {
            traderCustomer.setDisableTime(System.currentTimeMillis());
            //ts.setDisableReason(disabledReason);
        } else {
            //ts.setDisableTime((long) 0);
            //ts.setDisableReason("");
        }
        return update(traderCustomer, httpUrl + ErpConst.TRADER_CUSTOMER_DISABLED);
    }

    @Override
    public TraderCustomer selectByPrimaryKey(Integer customerId) {
        if (customerId == null) {
            return null;
        }
        return traderCustomerMapper.selectByPrimaryKey(customerId);
    }

    /**
     * <b>Description:</b><br>
     * 更新
     *
     * @param url
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午11:47:29
     */
    @SuppressWarnings("rawtypes")
    public ResultInfo update(TraderCustomer tc, String url) {
        final TypeReference<ResultInfo<TraderCustomer>> TypeRef = new TypeReference<ResultInfo<TraderCustomer>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, tc, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public TraderCustomer getTraderCustomerEditBaseInfo(TraderCustomer traderCustomer) {
        // 接口调用
        String url = httpUrl + "trader/getcustomereditbaseinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderCustomer>> TypeRef2 = new TypeReference<ResultInfo<TraderCustomer>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomer, clientId, clientKey,
                    TypeRef2);
            TraderCustomer res = (TraderCustomer) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderCustomer saveEditBaseInfo(Trader trader, HttpServletRequest request, HttpSession session) {
        logger.info("saveEditBaseInfo trader:{}", JSON.toJSONString(trader));
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();
        String customerNatureBefore = request.getParameter("customerNatureBefore");
        TraderDealerFrontDto traderDealerFrontDto = null;

        if (StringUtil.isNotEmpty(trader.getTheDataOfThePage())) {
            traderDealerFrontDto = JSON.parseObject(trader.getTheDataOfThePage(), TraderDealerFrontDto.class);
            TraderDealerFrontDto.Principal principal = traderDealerFrontDto.getPrincipal();
            if (Objects.nonNull(principal)) {
                principal.setEffectiveness(trader.getEffectiveness());
            }
        }
        if (request.getParameter("zone") != null && !"".equals(request.getParameter("zone"))) {
            if (Integer.parseInt(request.getParameter("zone")) > 0) {
                trader.setAreaId(Integer.parseInt(request.getParameter("zone")));
                trader.setAreaIds(request.getParameter("province") + "," + request.getParameter("city") + "," + request.getParameter("zone"));
            } else {
                trader.setAreaId(Integer.parseInt(request.getParameter("city")));
                trader.setAreaIds(request.getParameter("province") + "," + request.getParameter("city") + "," + request.getParameter("zone"));
            }
        }

        if (Integer.parseInt(request.getParameter("warehouseZone")) > 0) {
            trader.setWarehouseAreaId(Integer.parseInt(request.getParameter("warehouseZone")));
            trader.setWarehouseAreaIds(request.getParameter("warehouseProvince") + "," + request.getParameter("warehouseCity") + "," + request.getParameter("warehouseZone"));
        } else {
            trader.setWarehouseAreaId(Integer.parseInt(request.getParameter("warehouseCity")));
            trader.setWarehouseAreaIds(request.getParameter("warehouseProvince") + "," + request.getParameter("warehouseCity") + "," + request.getParameter("warehouseZone"));
        }

        trader.setCompanyId(user.getCompanyId());

        trader.setModTime(time);
        trader.setUpdater(user.getUserId());

        // 客户
        TraderCustomer traderCustomer = trader.getTraderCustomer();
        traderCustomer.setTraderId(trader.getTraderId());
        boolean isTerminal = false;
        boolean isDealer = false;
        Integer traderCustomerMarketingType = null;

        if (request.getParameter("traderCustomer.registeredCapital") != null && request.getParameter("traderCustomer.registeredCapital").equals("")) {
            traderCustomer.setRegisteredCapital("0");
        }
        //冗余字段 客户类型 客户性质
        if (null != request.getParameter("traderCustomerCategoryIds")) {
            String ids = request.getParameter("traderCustomerCategoryIds");
            String[] idsArr = ids.split(",");
            if (idsArr != null && idsArr.length > 0 && null != idsArr[0]) {
                if (idsArr[0].equals("1")) {
                    traderCustomer.setCustomerType(SysOptionConstant.ID_426);
                }
                if (idsArr[0].equals("2")) {
                    traderCustomer.setCustomerType(SysOptionConstant.ID_427);
                }
            }
            if (idsArr != null && idsArr.length > 1 && null != idsArr[1]) {
                if (idsArr[1].equals("3") || idsArr[1].equals("5")) {
                    traderCustomer.setCustomerNature(SysOptionConstant.ID_465);
                }
                if (idsArr[1].equals("4") || idsArr[1].equals("6")) {
                    traderCustomer.setCustomerNature(SysOptionConstant.ID_466);
                }
                if (idsArr[1].equals("6")) {
                    isTerminal = true;
                    if (idsArr.length >= 3) {
                        traderCustomerMarketingType = Integer.parseInt(idsArr[2]);
                    }

                }
                if (idsArr[1].equals("5")) {
                    isDealer = true;
                }
            }
        }
        if (StringUtil.isNotEmpty(traderCustomer.getRegisteredDateStr())) {
            SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
            try {
                traderCustomer.setRegisteredDate(dateformat.parse(traderCustomer.getRegisteredDateStr()));
            } catch (ParseException e) {
                return null;
            }
        }

        traderCustomer.setModTime(time);
        traderCustomer.setUpdater(user.getUserId());

        // 客户代理品牌
        List<TraderCustomerAgentBrand> agentBrands = new ArrayList<>();
        if (null != request.getParameterValues("agentBrandId")) {
            String[] agentBrandIds = request.getParameterValues("agentBrandId");
            for (String brandId : agentBrandIds) {
                TraderCustomerAgentBrand traderCustomerAgentBrand = new TraderCustomerAgentBrand();
                traderCustomerAgentBrand.setBrandId(Integer.parseInt(brandId));
                agentBrands.add(traderCustomerAgentBrand);
            }

            traderCustomer.setTraderCustomerAgentBrands(agentBrands);
        }

        //基础医疗经销商
        Integer basicMedicalDealer = 0;
        String lcfx = null;
        if (null != request.getParameter("isLcfx")) {
            lcfx = request.getParameter("isLcfx");
        }
        TraderWorkareaDto update = new TraderWorkareaDto();
        update.setAreaId(trader.getWork_zone());
        update.setTraderId(trader.getTraderId());
        traderWorkAreaApiService.update(update);
        // 客户属性
        List<TraderCustomerAttribute> customerAttributes = new ArrayList<>();
        if (null != request.getParameterValues("attributeId")) {
            String[] attributeIds = request.getParameterValues("attributeId");
            for (String attIds : attributeIds) {
                TraderCustomerAttribute customerAttribute = new TraderCustomerAttribute();
                String[] attArr = attIds.split("_");

                customerAttribute.setAttributeCategoryId(Integer.parseInt(attArr[0]));
                customerAttribute.setAttributeId(Integer.parseInt(attArr[1]));

                String pName = "attributedesc_" + attIds;
                if (null != request.getParameter(pName)) {
                    customerAttribute.setAttributeOther((String) request.getParameter(pName));
                }

                customerAttributes.add(customerAttribute);

                //基础医疗经销商
                if (lcfx != null && lcfx.equals("1")
                        && (Integer.parseInt(attArr[1]) == 95
                        || Integer.parseInt(attArr[1]) == 96
                        || Integer.parseInt(attArr[1]) == 97
                        || Integer.parseInt(attArr[1]) == 99
                        || Integer.parseInt(attArr[1]) == 100)) {
                    basicMedicalDealer = 1;
                }
            }

            traderCustomer.setTraderCustomerAttributes(customerAttributes);

        }

        traderCustomer.setBasicMedicalDealer(basicMedicalDealer);

        // 客户经营区域
        List<TraderCustomerBussinessArea> bussinessAreas = new ArrayList<>();
        if (null != request.getParameterValues("bussinessAreaId")) {
            String[] bussinessAreaIds = request.getParameterValues("bussinessAreaId");
            String[] bussinessAreaIdsStr = request.getParameterValues("bussinessAreaIds");
            for (Integer i = 0; i < bussinessAreaIds.length; i++) {
                TraderCustomerBussinessArea traderCustomerBussinessArea = new TraderCustomerBussinessArea();
                traderCustomerBussinessArea.setAreaId(Integer.parseInt(bussinessAreaIds[i]));
                traderCustomerBussinessArea.setAreaIds(bussinessAreaIdsStr[i]);
                bussinessAreas.add(traderCustomerBussinessArea);
            }

            traderCustomer.setTraderCustomerBussinessAreas(bussinessAreas);
        }

        // 客户经营品牌
        List<TraderCustomerBussinessBrand> bussinessBrands = new ArrayList<>();
        if (null != request.getParameterValues("bussinessBrandId")) {
            String[] bussinessBrandIds = request.getParameterValues("bussinessBrandId");
            for (String brandId : bussinessBrandIds) {
                TraderCustomerBussinessBrand traderCustomerBussinessBrand = new TraderCustomerBussinessBrand();
                traderCustomerBussinessBrand.setBrandId(Integer.parseInt(brandId));
                bussinessBrands.add(traderCustomerBussinessBrand);
            }

            traderCustomer.setTraderCustomerBussinessBrands(bussinessBrands);
        }
        // 财务信息
        TraderFinance traderFinance = trader.getTraderFinance();
        if (traderFinance != null) {
            traderFinance.setTraderType(1);
            traderFinance.setAddTime(time);
            traderFinance.setCreator(user.getUserId());
            traderFinance.setModTime(time);
            traderFinance.setUpdater(user.getUserId());
        }

        TraderDto newTraderDto = new TraderDto();
        BeanUtil.copyProperties(trader, newTraderDto, true);

        TraderCustomerErpDto traderCustomerErpDto = new TraderCustomerErpDto();
        BeanUtil.copyProperties(traderCustomer, traderCustomerErpDto, true);
        BeanUtil.copyProperties(traderCustomer, traderCustomerErpDto,true);
        traderCustomerErpDto.setInvalidReason(trader.getInvalidReason());
        traderCustomerErpDto.setOtherReason(trader.getOtherReason());
        
        // 特殊字段处理
        traderCustomerErpDto.setOrganizationCode(traderCustomer.getOrgNumber());
        traderCustomerErpDto.setUnifiedSocialCreditIdentifier(traderCustomer.getCreditCode());

        traderCustomerErpDto.setTraderCustomerAttributeDtoList(customerAttributes.stream().map(x -> {
            TraderCustomerAttributeDto data = new TraderCustomerAttributeDto();
            data.setAttributeId(x.getAttributeId());
            data.setAttributeOther(x.getAttributeOther());
            data.setAttributeCategoryId(x.getAttributeCategoryId());
            data.setSubCategoryIds(x.getSubCategoryIds());
            return data;
        }).collect(Collectors.toList()));
        traderCustomerErpDto.setTraderCustomerAgentBrandDtoList(agentBrands.stream().map(x -> {
            TraderCustomerAgentBrandDto data = new TraderCustomerAgentBrandDto();
            data.setBrandId(x.getBrandId());
            return data;
        }).collect(Collectors.toList()));
        traderCustomerErpDto.setTraderCustomerBussinessAreaDtoList(bussinessAreas.stream().map(x -> {
            TraderCustomerBussinessAreaDto data = new TraderCustomerBussinessAreaDto();
            data.setAreaId(x.getAreaId());
            data.setAreaIds(x.getAreaIds());
            return data;
        }).collect(Collectors.toList()));
        traderCustomerErpDto.setTraderCustomerBussinessBrandDtoList(bussinessBrands.stream().map(x -> {
            TraderCustomerBussinessBrandDto data = new TraderCustomerBussinessBrandDto();
            data.setBrandId(x.getBrandId());
            return data;
        }).collect(Collectors.toList()));

        TraderCustomerErpDto queryByName = traderCustomerErpApiService.queryByName(newTraderDto.getTraderName());

        //不存在 || 存在不是原来的
        if (Objects.nonNull(queryByName) && Objects.nonNull(queryByName.getTraderCustomerId()) && !queryByName.getTraderId().equals(newTraderDto.getTraderId())) {
            return null;
        }

        if (Objects.nonNull(newTraderDto.getTraderId())) {
            traderApiService.update(newTraderDto);
        }
        TraderCustomerErpDto oldData = traderCustomerErpApiService.queryByTraderCustomerId(traderCustomerErpDto.getTraderCustomerId());
        boolean oldTerminal = Integer.valueOf("6").equals(oldData.getTraderCustomerCategoryId());
        boolean oldDealer = Integer.valueOf("5").equals(oldData.getTraderCustomerCategoryId());


        traderCustomerErpApiService.updateContainsOtherData(traderCustomerErpDto);


        // 针对 非此两种的先清除，产品主要关注未改类型的改动记录
        if ((isDealer != oldDealer) || (oldTerminal != isTerminal)) {
            traderCustomerMarketingPrincipalApiService.clearByTraderCustomer(traderCustomerErpDto.getTraderCustomerId());
            traderCustomerMarketingApiService.clearByTraderCustomer(traderCustomerErpDto.getTraderCustomerId());
        }


        if (isDealer) {
            //VDERP-17057 记录cpm变化前数据，进行比较，再决定是否埋点
            //主营属性(旧)
            TraderCustomerMarketingPrincipalDto traderCustomerMarketingPrincipalDtoOld = traderCustomerMarketingPrincipalApiService.getByTraderId(trader.getTraderId());
            //终端属性(旧)
            List<TraderCustomerMarketingTerminalDto> traderCustomerMarketingTerminalDtoListOld = traderCustomerMarketingApiService.getTraderCustomerMarketing(traderCustomerErpDto.getTraderCustomerId());

            traderDealerFrontDto = Objects.isNull(traderDealerFrontDto) ? new TraderDealerFrontDto() : traderDealerFrontDto;
            traderDealerFrontDto.setTraderCustomerId(traderCustomer.getTraderCustomerId());
            traderDealerFrontDto.setTraderId(traderCustomer.getTraderId());
            TraderCustomerMarketingPrincipalDto principalDto = traderCustomerMarketingPrincipalApiService.processFrontData(traderDealerFrontDto);
            traderCustomerMarketingPrincipalApiService.update(principalDto);
            List<TraderCustomerMarketingTerminalDto> terminalDtoList = traderCustomerMarketingApiService.processFrontData(traderDealerFrontDto);
            traderCustomerMarketingApiService.updateAll(terminalDtoList, traderCustomer.getTraderCustomerId());

            //主营属性(新)
            TraderCustomerMarketingPrincipalDto traderCustomerMarketingPrincipalDtoNew = traderCustomerMarketingPrincipalApiService.getByTraderId(trader.getTraderId());
            //终端属性(新)
            List<TraderCustomerMarketingTerminalDto> traderCustomerMarketingTerminalDtoListNew = traderCustomerMarketingApiService.getByTraderCustomerId(traderCustomerErpDto.getTraderCustomerId());
            trackCustomerCpmChange(user, trader, traderCustomerMarketingPrincipalDtoOld, traderCustomerMarketingTerminalDtoListOld, traderCustomerMarketingPrincipalDtoNew, traderCustomerMarketingTerminalDtoListNew);

        }

        // 财务信息
        if (Objects.nonNull(traderFinance)) {
            TraderFinanceDto traderFinanceDto = new TraderFinanceDto();
            BeanUtil.copyProperties(traderFinance, traderFinanceDto, true);
            TraderFinanceDto dbData = traderFinanceApiService.selectByTraderIdAndTraderType(traderFinanceDto.getTraderId(), 1);
            if (Objects.isNull(dbData)) {
                traderFinanceDto.setTraderId(newTraderDto.getTraderId());
                traderFinanceDto.setTraderType(1);
                traderFinanceApiService.add(traderFinanceDto);
            } else {
                traderFinanceDto.setTraderFinanceId(dbData.getTraderFinanceId());
                traderFinanceDto.setTraderId(dbData.getTraderId());
                traderFinanceDto.setTraderType(1);
                traderFinanceApiService.update(traderFinanceDto);
            }

        }

        // 新表
        if (isTerminal) {
            TraderCustomerMarketingTerminalDto traderCustomerMarketingTerminalDto = Optional.ofNullable(traderCustomer.getTraderCustomerMarketingDto()).orElse(new TraderCustomerMarketingTerminalDto());
            traderCustomerMarketingTerminalDto.setTraderCustomerId(traderCustomer.getTraderCustomerId());
            traderCustomerMarketingTerminalDto.setTraderId(traderCustomer.getTraderId());
            traderCustomerMarketingTerminalDto.setTraderCustomerMarketingType(traderCustomerMarketingType);
            traderCustomerMarketingApiService.update(traderCustomerMarketingTerminalDto);
        }

        try {
            //更新贝登商城会员
            updateVedengMember();
            traderCustomer.setTraderId(trader.getTraderId());

            //发送消息 trader
            Trader traderByTrader = traderMapper.getTraderByTraderId(trader.getTraderId());
            TraderReception traderReception = new TraderReception();

            traderReception.setTraderName(traderByTrader.getTraderName());
            traderReception.setTraderId(traderByTrader.getTraderId());
            // 如果客户属性不为空 就是集采的客户 推送给前台
            if (needSyncToJC(traderByTrader)) {
                if (traderByTrader.getParentId() == null || traderByTrader.getParentId() == 0) {
                    //0总公司  1分公司
                    traderReception.setTraderType(0);
                    traderReception.setParentTraderId(null);
                } else {
                    traderReception.setTraderType(1);
                    traderReception.setParentTraderId(traderByTrader.getParentId());
                }
                String traderReceptionStr = com.alibaba.fastjson.JSONObject.toJSONString(traderReception, SerializerFeature.WriteMapNullValue);
                try {
                    logger.info("编辑客户发送消息到前台 start=======" + JSON.toJSONString(traderReceptionStr));
                    erpMsgProducer.sendMsg(RabbitConfig.JC_ERP_ORG_EXCHANGE, RabbitConfig.JC_ERP_ORG_ROUTINGKEY, traderReceptionStr);
                } catch (Exception e) {
                    logger.error("编辑客户发送消息到前台失败" + JSON.toJSONString(traderReceptionStr));

                }
            }
            sendMsgIfNatureChange(traderCustomer, customerNatureBefore);

            // AI助手
            if (trader.getIsAiAssistant() != null && trader.getIsAiAssistant().equals(1)) {
                RCommunicateTodoJAiDto dto = new RCommunicateTodoJAiDto();
                dto.setTraderId(traderCustomerErpDto.getTraderId());
                dto.setCommunicateRecordId(trader.getAiCommunicateRecordId());
                rCommunicateTodoJAiApiService.updateTraderSign(dto);
            }

            return traderCustomer;
        } catch (Exception e) {
            logger.error("更新客户异常信息：", e);
            return null;
        }
    }


    private void trackCustomerCpmChange(User user, Trader trader, TraderCustomerMarketingPrincipalDto traderCustomerMarketingPrincipalDtoOld, List<TraderCustomerMarketingTerminalDto> traderCustomerMarketingTerminalDtoListOld,
                                        TraderCustomerMarketingPrincipalDto traderCustomerMarketingPrincipalDtoNew, List<TraderCustomerMarketingTerminalDto> traderCustomerMarketingTerminalDtoListNew) {
        try {
            //主营属性是否变化
            boolean isPrincipalChange = true;
            //判断 T_TRADER_CUSTOMER_MARKETING_PRINCIPAL（主营属性表）、T_TRADER_CUSTOMER_MARKETING_TERMINAL（终端属性表），2个表中数据是否有变动，存在变动则记录cpm变化
            //主营属性表  判断10个字段：SKU_TYPE 商品类型、SKU_SCOPE 商品范畴 、SKU_CATEGORY 商品分类、SALES_TYPE 销售类别、AGENCY_BRAND 代理品牌、GOVERNMENT_RELATION 政府关系、
            //OTHER_AGENCY_BRAND 其他代理品牌、OTHER_GOVERNMENT_RELATION 其他政府关系、AGENCY_SKU 代理商品、OTHER_AGENCY_SKU 其他代理商品
            if (Objects.isNull(traderCustomerMarketingPrincipalDtoOld) && Objects.isNull(traderCustomerMarketingPrincipalDtoNew)) {
                isPrincipalChange = false;
            }
            if (Objects.nonNull(traderCustomerMarketingPrincipalDtoOld) && Objects.nonNull(traderCustomerMarketingPrincipalDtoNew)) {
                //判断字段，拼接旧字段
                StringBuilder stbOld = new StringBuilder();
                stbOld.append(traderCustomerMarketingPrincipalDtoOld.getSkuType()).append(traderCustomerMarketingPrincipalDtoOld.getSkuScope()).append(traderCustomerMarketingPrincipalDtoOld.getSkuCategory())
                        .append(traderCustomerMarketingPrincipalDtoOld.getSalesType()).append(traderCustomerMarketingPrincipalDtoOld.getAgencyBrand()).append(traderCustomerMarketingPrincipalDtoOld.getGovernmentRelation())
                        .append(traderCustomerMarketingPrincipalDtoOld.getOtherGovernmentRelation()).append(traderCustomerMarketingPrincipalDtoOld.getOtherAgencyBrand()).append(traderCustomerMarketingPrincipalDtoOld.getOtherAgencySku());
                //判断字段，拼接新字段
                StringBuilder stbNew = new StringBuilder();
                stbNew.append(traderCustomerMarketingPrincipalDtoNew.getSkuType()).append(traderCustomerMarketingPrincipalDtoNew.getSkuScope()).append(traderCustomerMarketingPrincipalDtoNew.getSkuCategory())
                        .append(traderCustomerMarketingPrincipalDtoNew.getSalesType()).append(traderCustomerMarketingPrincipalDtoNew.getAgencyBrand()).append(traderCustomerMarketingPrincipalDtoNew.getGovernmentRelation())
                        .append(traderCustomerMarketingPrincipalDtoNew.getOtherGovernmentRelation()).append(traderCustomerMarketingPrincipalDtoNew.getOtherAgencyBrand()).append(traderCustomerMarketingPrincipalDtoNew.getOtherAgencySku());
                if (stbOld.toString().equals(stbNew.toString())) {
                    isPrincipalChange = false;
                }
            }
            //终端属性表，判断 INSTITUTION_NATURE 机构性质、 INSTITUTION_LEVEL 机构评级、INSTITUTION_TYPE 机构类型、机构类型子集 INSTITUTION_TYPE_CHILD、其他机构类型 OTHER_INSTITUTION_TYPE
            boolean isTerminalChange = true;
            //如果主营属性不需要埋点，再判断终端属性
            if (!isPrincipalChange) {
                //终端属性是否变化
                if (CollectionUtils.isEmpty(traderCustomerMarketingTerminalDtoListOld) && CollectionUtils.isEmpty(traderCustomerMarketingTerminalDtoListNew)) {
                    isTerminalChange = false;
                }
                if (CollectionUtils.isNotEmpty(traderCustomerMarketingTerminalDtoListOld) && CollectionUtils.isNotEmpty(traderCustomerMarketingTerminalDtoListNew)
                        && traderCustomerMarketingTerminalDtoListOld.size() == traderCustomerMarketingTerminalDtoListNew.size()) {
                    //机构性质字符串，null值不需要处理，会返回字符串“null”
                    String institutionOldStr = traderCustomerMarketingTerminalDtoListOld.stream().map(TraderCustomerMarketingTerminalDto::getInstitutionNature).collect(Collectors.joining(", "));
                    String institutionLevelOldStr = traderCustomerMarketingTerminalDtoListOld.stream().map(TraderCustomerMarketingTerminalDto::getInstitutionLevel).collect(Collectors.joining(", "));
                    String institutionTypeOldStr = traderCustomerMarketingTerminalDtoListOld.stream().map(TraderCustomerMarketingTerminalDto::getInstitutionType).collect(Collectors.joining(", "));
                    String institutionTypeChildOldStr = traderCustomerMarketingTerminalDtoListOld.stream().map(TraderCustomerMarketingTerminalDto::getInstitutionTypeChild).collect(Collectors.joining(", "));
                    String otherInstitutionTypeOldStr = traderCustomerMarketingTerminalDtoListOld.stream().map(TraderCustomerMarketingTerminalDto::getOtherInstitutionType).collect(Collectors.joining(", "));
                    StringBuilder stbOld = new StringBuilder();
                    stbOld.append(institutionOldStr).append(institutionLevelOldStr).append(institutionTypeOldStr).append(institutionTypeChildOldStr).append(otherInstitutionTypeOldStr);

                    String institutionNewStr = traderCustomerMarketingTerminalDtoListNew.stream().map(TraderCustomerMarketingTerminalDto::getInstitutionNature).collect(Collectors.joining(", "));
                    String institutionLevelNewStr = traderCustomerMarketingTerminalDtoListNew.stream().map(TraderCustomerMarketingTerminalDto::getInstitutionLevel).collect(Collectors.joining(", "));
                    String institutionTypeNewStr = traderCustomerMarketingTerminalDtoListNew.stream().map(TraderCustomerMarketingTerminalDto::getInstitutionType).collect(Collectors.joining(", "));
                    String institutionTypeChildNewStr = traderCustomerMarketingTerminalDtoListNew.stream().map(TraderCustomerMarketingTerminalDto::getInstitutionTypeChild).collect(Collectors.joining(", "));
                    String otherInstitutionTypeNewStr = traderCustomerMarketingTerminalDtoListNew.stream().map(TraderCustomerMarketingTerminalDto::getOtherInstitutionType).collect(Collectors.joining(", "));
                    StringBuilder stbNew = new StringBuilder();
                    stbNew.append(institutionNewStr).append(institutionLevelNewStr).append(institutionTypeNewStr).append(institutionTypeChildNewStr).append(otherInstitutionTypeNewStr);
                    if (stbOld.toString().equals(stbNew.toString())) {
                        isTerminalChange = false;
                    }
                }
            }

            //都没有变化，直接跳过
            if (!isPrincipalChange && !isTerminalChange) {
                logger.info("cpm无变化，不需要进行cpm变化埋点");
                return;
            }
            Integer traderId = trader.getTraderId();
            //VDERP-17057  【客户档案】ERP客户档案时间轴 客户CPM标签变更
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            trackParams.put("track_user", user);
            trackParams.put("traderId", traderId);
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_CPM);
            trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_CPM);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackStrategy.track(trackParamsData);
        } catch (Exception e) {
            logger.error("埋点：{}，失败，不影响正常业务", EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_CPM.getArchivedName(), e);
        }
    }

    /**
     * <b>Description:</b>客户性质发生变化，发送消息<br>
     *
     * @param
     * @return
     * @Note <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/9/29
     */
    private void sendMsgIfNatureChange(TraderCustomer traderCustomer, String customerNatureBefore) {
        Integer preNature = getNature(customerNatureBefore);
        if (preNature.equals(traderCustomer.getCustomerNature())) {
            return;
        }
        JSONObject json = new JSONObject();
        json.put("traderId", traderCustomer.getTraderId());
        json.put("nowNature", traderCustomer.getCustomerNature());
        json.put("preNature", preNature);
        Timer time = new Timer();
        time.schedule(new TimerTask() {
            @Override
            public void run() {
                opMsgProducer.sendMsg(RabbitConfig.TRADER_NATURE_EXCHANGE, null, json.toString());
            }
        }, 5000);

    }

    /**
     * <b>Description:</b>根据分类id获取客户性质<br>
     *
     * @param
     * @return
     * @Note <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/9/29
     */
    private Integer getNature(String customerNatureBefore) {
        if (StringUtil.isBlank(customerNatureBefore)) {
            return ErpConst.ZERO;
        }
        if (customerNatureBefore.equals("3") || customerNatureBefore.equals("5")) {
            return SysOptionConstant.ID_465;
        }
        if (customerNatureBefore.equals("4") || customerNatureBefore.equals("6")) {
            return SysOptionConstant.ID_466;
        }
        return ErpConst.ZERO;
    }

    @Override
    public Trader getTraderByTraderName(Trader trader, HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        trader.setCompanyId(user.getCompanyId());

        // 接口调用
        String url = httpUrl + "trader/gettraderbytradername.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Trader>> TypeRef2 = new TypeReference<ResultInfo<Trader>>() {
        };
        try {
            ResultInfo<Trader> result2 = (ResultInfo<Trader>) HttpClientUtils.post(url, trader, clientId, clientKey, TypeRef2);
            if (null == result2) {
                return null;
            }

            Trader res = (Trader) result2.getData();

            return res;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    /**
     * <b>Description:</b><br>
     * 获取当前客户的联系人
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 上午10:00:55
     */
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getTraderContactVoList(TraderContactVo traderContactVo) {
        logger.info("获取当前客户的联系人旧方法");
        try {
            if (traderContactVo.getTraderId() == null || traderContactVo.getTraderId() == 0) {
                Map<String, Object> traderMap = new HashMap<>();
                List<TraderAddressVo> tavList = new ArrayList<>();
                List<TraderContactVo> contactList = new ArrayList<>();
                traderMap.put("contact", contactList.toString());
                traderMap.put("address", tavList);
                return traderMap;
            }
            ResultInfo<?> result = getContactsAddress(traderContactVo);
            Map<String, Object> map = (Map<String, Object>) result.getData();
            String tastr = (String) map.get("address");
            if (!"[]".equals(tastr)) {
//                net.sf.json.JSONArray json = net.sf.json.JSONArray.fromObject(tastr);
//                List<TraderAddress> list = (List<TraderAddress>) json.toCollection(json, TraderAddress.class);
                List<TraderAddress> list = com.alibaba.fastjson.JSONArray.parseArray(tastr,TraderAddress.class);
                if (null != list && list.size() > 0) {
                    List<TraderAddressVo> tavList = new ArrayList<>();
                    TraderAddressVo tav = null;
                    for (TraderAddress ta : list) {
                        if (ta == null || ta.getAreaId() == null) {
                            continue;
                        }
                        tav = new TraderAddressVo();
                        tav.setTraderAddress(ta);
                        tav.setIsEnable(ta.getIsEnable());
                        if (ta.getAreaId() > 0) {
                            tav.setArea(ta.getAreaString());
                        }
                        tavList.add(tav);
                    }
                    map.put("address", tavList);
                }
            } else {
                List<TraderAddressVo> tavList = new ArrayList<>();
                map.put("address", tavList);
            }
            return map;
        } catch (Exception e) {
            logger.error("查询客户联系人地址信息失败  traderId: {}", traderContactVo.getTraderId());
            throw new IllegalStateException("查询客户联系人地址信息失败", e);
        }

    }

    @Override
    public TraderContactDto getTraderContactDto(TraderContactVo traderContactVo) {
        try {
            TraderContactDto dto = new TraderContactDto();
            if (traderContactVo.getTraderId() == null || traderContactVo.getTraderId() == 0) {
                dto.setContactList(new ArrayList<>());
                dto.setAddressList(new ArrayList<>());
                return dto;
            }
            List<TraderContactVo> tcvList = traderContactMapper.getTraderContactVoList(traderContactVo);
            dto.setContactList(CollUtil.isNotEmpty(tcvList) ? tcvList : new ArrayList<>());
            TraderAddress ta = new TraderAddress();
            ta.setTraderId(traderContactVo.getTraderId());
            ta.setIsEnable(traderContactVo.getIsEnable());
            ta.setTraderType(traderContactVo.getTraderType());
            List<TraderAddress> taList = traderAddressMapper.getTraderAddressInfoList(ta);
            List<TraderAddressVo> addressVoList = taList.stream()
                    .filter(e -> Objects.nonNull(e.getAreaId())
                            && e.getAreaId() > 0)
                    .map(e -> {
                        TraderAddressVo tv = new TraderAddressVo();
                        tv.setTraderAddress(e);
                        tv.setIsEnable(e.getIsEnable());
                        tv.setArea(e.getAreaString());
                        return tv;
                    }).collect(Collectors.toList());
            dto.setAddressList(CollUtil.isNotEmpty(addressVoList) ? addressVoList : new ArrayList<>());
            return dto;
        } catch (Exception e) {
            logger.error("查询客户联系人地址信息失败  traderId: {}", traderContactVo.getTraderId());
            throw new IllegalStateException("查询客户联系人地址信息失败", e);
        }
    }

    @Override
    public List<TraderContactVo> getTraderContactVoListNew(TraderContactVo traderContactVo){
        return traderContactMapper.getTraderContactVoList(traderContactVo);
    }

    @Override
    public List<TraderAddress> getTraderAddressInfoListNew(TraderContactVo tcv){
        if (null != tcv && null != tcv.getTraderId()) {
            TraderAddress ta = new TraderAddress();
            ta.setTraderId(tcv.getTraderId());
            ta.setIsEnable(tcv.getIsEnable());
            ta.setTraderType(tcv.getTraderType());
            List<TraderAddress> taList = traderAddressMapper.getTraderAddressInfoList(ta);
            return taList;
        }
        return new ArrayList<>();
    }
    @Deprecated
    public ResultInfo getContactsAddress(TraderContactVo tcv) {
        Map<String, Object> map = new HashMap<>();
        List<TraderContactVo> tcvList = traderContactMapper.getTraderContactVoList(tcv);
        map.put("contact",  com.alibaba.fastjson.JSONArray.toJSONString(tcvList));
        if (null != tcv && null != tcv.getTraderId()) {
            TraderAddress ta = new TraderAddress();
            ta.setTraderId(tcv.getTraderId());
            ta.setIsEnable(tcv.getIsEnable());
            ta.setTraderType(tcv.getTraderType());
            List<TraderAddress> taList = traderAddressMapper.getTraderAddressInfoList(ta);
            map.put("address", com.alibaba.fastjson.JSONArray.toJSONString(taList));
        }
        return ResultInfo.success("查询成功", map);
    }

    /**
     * <b>Description:</b><br>
     * 保存联系人
     *
     * @param traderContact
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 下午3:56:30
     */
    @Override
    public Integer saveTraderContact(TraderContact traderContact, User user) {
        if (null == traderContact.getTraderContactId()) {
            traderContact.setAddTime(System.currentTimeMillis());
            traderContact.setCreator(user.getUserId());
            traderContact.setModTime(System.currentTimeMillis());
            traderContact.setUpdater(user.getUserId());
            traderContact.setIsOnJob(1);
            traderContact.setIsDefault(0);
            traderContact.setIsEnable(1);
            if (traderContact.getSex() == null) {
                traderContact.setSex(2);
            }
        } else {
            traderContact.setUpdater(user.getUserId());
            traderContact.setModTime(System.currentTimeMillis());
        }
        if (StringUtil.isNotBlank(traderContact.getOtherPosition())){
            // 判断是否和勾选项重复
            List<String> list = new ArrayList<>(Arrays.asList(traderContact.getPosition().split(",")));
            if (list.contains(traderContact.getOtherPosition())){
                // 去掉其他和其他勾选项
                traderContact.setOtherPosition("");
                List<String> collect = list.stream().filter(o -> !Objects.equals(o, "其他")).collect(Collectors.toList());
                traderContact.setPosition(String.join(",",collect));
            }
            
            
            String position = StringUtil.isNotBlank(traderContact.getOtherPosition()) ? traderContact.getPosition()+"," : traderContact.getPosition();
            String positionAll = position + traderContact.getOtherPosition();
            traderContact.setPosition(positionAll);
        }
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.TRADER_CONTACTS_SAVE,
                    traderContact, clientId, clientKey, TypeRef);
            if (result == null) {
                return 0;
            } else if (result != null && result.getCode() == 1) {
                return -1;
            }
            return Integer.valueOf(result.getData().toString());
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public ResultInfo saveTraderBusinessCard(List<BusinessCard> businessCards, Integer userId, Integer traderContactId) {
        TraderCertificate certificate = new TraderCertificate();
        certificate.setRelatedId(traderContactId);
        certificate.setSysOptionDefinitionId(SysOptionConstant.BUSINESS_CARDS);
        certificate.setTraderType(ErpConst.ONE);
        traderCertificateMapper.updatTraderBusinessCard(certificate);
        if (CollectionUtils.isNotEmpty(businessCards)) {
            for (BusinessCard businessCard : businessCards) {
                TraderCertificate traderCertificate = new TraderCertificate();
                traderCertificate.setRelatedId(traderContactId);
                traderCertificate.setTraderType(ErpConst.ONE);
                traderCertificate.setSysOptionDefinitionId(SysOptionConstant.BUSINESS_CARDS);
                traderCertificate.setOssResourceId(FileUtil.getOssResourceIdFromStr(businessCard.getRelativePath()));
                traderCertificate.setDomain(domain);
                traderCertificate.setUri(businessCard.getRelativePath());
                traderCertificate.setName(businessCard.getDisplayName());
                traderCertificate.setAddTime(System.currentTimeMillis());
                traderCertificate.setCreator(userId);
                traderCertificateMapper.insertSelective(traderCertificate);
            }
        }
        return ResultInfo.success("保存个人名片成功");
    }

    /**
     * <b>Description:</b><br>
     * 保存地址
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 下午3:56:30
     */
    @Override
    public ResultInfo saveTraderAddress(TraderAddress traderAddress, User user) {
        if (null == traderAddress.getTraderAddressId()) {
            traderAddress.setAddTime(System.currentTimeMillis());
            traderAddress.setCreator(user.getUserId());
            traderAddress.setModTime(System.currentTimeMillis());
            traderAddress.setUpdater(user.getUserId());
            traderAddress.setIsDefault(0);
            traderAddress.setIsEnable(1);
        } else {
            traderAddress.setUpdater(user.getUserId());
            traderAddress.setModTime(System.currentTimeMillis());
        }
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.TRADER_ADDRESS_SAVE,
                    traderAddress, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 获取联系人
     *
     * @param traderContactId
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月24日 下午2:28:20
     */
    @Override
    public TraderContact getTraderContactById(Integer traderContactId) {
        TraderContact traderContact = new TraderContact();
        traderContact.setTraderContactId(traderContactId);
        final TypeReference<ResultInfo<TraderContact>> TypeRef = new TypeReference<ResultInfo<TraderContact>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.TRADER_CONTACTS_QUERY,
                    traderContact, clientId, clientKey, TypeRef);
            TraderContact tc = (TraderContact) result.getData();
            return tc;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 转移联系人
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:41:36
     */
    @Override
    public ResultInfo transferContact(TraderContact traderContact, User user) {
        //记录联系人转移记录
        insertTransferRecord(traderContact);
        traderContact.setUpdater(user.getUserId());
        traderContact.setModTime(System.currentTimeMillis());
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_TRANSFER_CONTACTS,
                    traderContact, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    private void insertTransferRecord(TraderContact traderContact) {
        TraderContactTransferRecordDto traderContactTransferRecordDto = new TraderContactTransferRecordDto();
        Integer traderId = traderContact.getTraderId();
        Integer traderContactId = traderContact.getTraderContactId();
        Trader afterTrader = traderMapper.getTraderInfoByTraderId(traderId);
        TraderContact tcone = traderContactMapper.selectByPrimaryKey(traderContactId);
        Trader beforeTrader = traderMapper.getTraderInfoByTraderId(tcone.getTraderId());
        traderContactTransferRecordDto.setTraderId(traderId);
        traderContactTransferRecordDto.setTraderContactId(traderContactId);
        traderContactTransferRecordDto.setTraderCustomerId(beforeTrader.getTraderCustomerId());
        traderContactTransferRecordDto.setTraderCustomerName(beforeTrader.getTraderName());
        traderContactTransferRecordDto.setAfterTraderCustomerId(afterTrader.getTraderCustomerId());
        traderContactTransferRecordDto.setAfterTraderCustomerName(afterTrader.getTraderName());
        traderContactTransferRecordApiService.insertTraderContactTransferRecord(traderContactTransferRecordDto);
    }

    /**
     * <b>Description:</b><br>
     * 是否禁用联系人
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:41:36
     */
    @Override
    public ResultInfo isDisabledContact(TraderContact traderContact, User user) {
        traderContact.setUpdater(user.getUserId());
        traderContact.setModTime(System.currentTimeMillis());
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_DISABLED_CONTACTS,
                    traderContact, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 设置默认联系人
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:41:36
     */
    @Override
    public ResultInfo isDefaultContact(TraderContact traderContact, User user) {
        traderContact.setUpdater(user.getUserId());
        traderContact.setModTime(System.currentTimeMillis());
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_DEFAULT_CONTACTS,
                    traderContact, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 是否禁用地址
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:41:36
     */
    @Override
    public ResultInfo isDisabledAddress(TraderAddress traderAddresst, User user) {
        traderAddresst.setUpdater(user.getUserId());
        traderAddresst.setModTime(System.currentTimeMillis());
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_DISABLED_ADDRESS,
                    traderAddresst, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br>
     * 设置默认地址
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:41:36
     */
    @Override
    public ResultInfo isDefaultAddress(TraderAddress traderAddress, User user) {
        traderAddress.setUpdater(user.getUserId());
        traderAddress.setModTime(System.currentTimeMillis());
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_DEFAULT_ADDRESS,
                    traderAddress, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public List<CommunicateRecord> getCommunicateRecordListPage(CommunicateRecord communicateRecord, Page page) {
		/*//客户沟通记录
		if (communicateRecord.getTraderCustomerId() != null && communicateRecord.getTraderCustomerId() > 0) {
			List<Integer> traderCustomerIdList = new ArrayList<>();
			traderCustomerIdList.add(communicateRecord.getTraderCustomerId());
			communicateRecord.setTraderCustomerIds(traderCustomerIdList);
		}
		//商机沟通记录
		if(null != communicateRecord.getBussinessChanceId() && communicateRecord.getBussinessChanceId() > 0){
			communicateRecord.setBussinessChanceId(communicateRecord.getBussinessChanceId());
		}
		//报价
		if(null != communicateRecord.getQuoteorderId() && communicateRecord.getQuoteorderId() > 0){
			communicateRecord.setQuoteorderId(communicateRecord.getQuoteorderId());
		}
		//订单
		if(null != communicateRecord.getSaleorderId() && communicateRecord.getSaleorderId() > 0){
			communicateRecord.setQuoteorderId(communicateRecord.getSaleorderId());
		}*/

        // 后期调用接口查询询价、报价、订单、售后 沟通记录
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("communicateRecord", communicateRecord);
        map.put("page", page);
        List<CommunicateRecord> communicateRecordList = communicateRecordMapper.getCommunicateRecordList(map);

        // 调用接口补充信息（联系人，沟通目的、方式 ，沟通内容（标签））、商机、报价、订单
        String url = httpUrl + "trader/tradercommunicaterecord.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<CommunicateRecord>>> TypeRef2 = new TypeReference<ResultInfo<List<CommunicateRecord>>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, communicateRecordList, clientId, clientKey, TypeRef2);


            List<Integer> relatedList = new ArrayList<>();
            List<CommunicateRecord> list = (List<CommunicateRecord>) result2.getData();
            for (CommunicateRecord newcom : list) {
                for (CommunicateRecord oldcom : communicateRecordList) {
                    if (newcom.getCommunicateRecordId().equals(oldcom.getCommunicateRecordId())) {
                        if (StringUtil.isNotBlank(oldcom.getContact()) && StringUtil.isNotBlank(oldcom.getContactMob())) {
                            newcom.setContactName(oldcom.getContact());
                            newcom.setPhone(oldcom.getContactMob());
                            newcom.setContactContent(oldcom.getContactContent());
                        }
                        if (oldcom.getCommunicateAiSummaryApiDto() != null && oldcom.getCommunicateAiSummaryApiDto().getCommunicateSummaryId() != null) {
                            CommunicateAiSummaryApiDto communicateAiSummaryApiDto = communicateSummaryApiService
                                    .getByCommunicateSummaryId(oldcom.getCommunicateAiSummaryApiDto().getCommunicateSummaryId());
                            newcom.setCommunicateAiSummaryApiDto(communicateAiSummaryApiDto);
                        }
                        break;
                    }
                }
                relatedList.add(newcom.getCommunicateRecordId());


                /*//判断是否转译完成
                PhoneticWriting phoneticWriting = phoneticWritingMapper.getPhoneticWriting(newcom.getCommunicateRecordId());
                if(phoneticWriting != null){
                    if(StringUtils.isNotBlank(phoneticWriting.getOriginalContent())){
                        newcom.setIsTranslation(1);
                    }else {
                        newcom.setIsTranslation(0);
                    }
                }else{
                    newcom.setIsTranslation(0);
                }*/
            }

            if (relatedList != null && relatedList.size() > 0) {
                List<PhoneticWriting> phoneticWritingList = phoneticWritingMapper.getPhoneticWritingList(relatedList);
                if (phoneticWritingList != null && phoneticWritingList.size() > 0) {
                    for (int i = 0; i < list.size(); i++) {
                        for (int j = 0; j < phoneticWritingList.size(); j++) {
                            if (list.get(i).getCommunicateRecordId().equals(phoneticWritingList.get(j).getRelatedId())) {
                                if (phoneticWritingList.get(j) != null) {
                                    if (StringUtils.isNotBlank(phoneticWritingList.get(j).getOriginalContent())) {
                                        list.get(i).setIsTranslation(1);
                                    } else {
                                        list.get(i).setIsTranslation(0);
                                    }
                                } else {
                                    list.get(i).setIsTranslation(0);
                                }
                            }
                        }
                    }
                }
            }
            return list;
        } catch (IOException e) {
            return null;
        }

    }

    @Override
    public List<CommunicateRecord> getCommunicateRecordListPageNew(CommunicateRecord communicateRecord, Page page) {
        // 后期调用接口查询询价、报价、订单、售后 沟通记录
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("communicateRecord", communicateRecord);
        map.put("page", page);
        List<CommunicateRecord> communicateRecordList = communicateRecordMapper.getCommunicateRecordListPage(map);

        // 调用接口补充信息（联系人，沟通目的、方式 ，沟通内容（标签））、商机、报价、订单
        String url = httpUrl + "trader/tradercommunicaterecord.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<CommunicateRecord>>> TypeRef2 = new TypeReference<ResultInfo<List<CommunicateRecord>>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, communicateRecordList, clientId, clientKey, TypeRef2);


            List<Integer> relatedList = new ArrayList<>();
            List<CommunicateRecord> list = (List<CommunicateRecord>) result2.getData();
            for (CommunicateRecord newcom : list) {
                for (CommunicateRecord oldcom : communicateRecordList) {
                    if (newcom.getCommunicateRecordId().equals(oldcom.getCommunicateRecordId())) {
                        if (StringUtil.isNotBlank(oldcom.getContact()) && StringUtil.isNotBlank(oldcom.getContactMob())) {
                            newcom.setContactName(oldcom.getContact());
                            newcom.setPhone(oldcom.getContactMob());
                            newcom.setContactContent(oldcom.getContactContent());
                        }
                        if (oldcom.getCommunicateAiSummaryApiDto() != null && oldcom.getCommunicateAiSummaryApiDto().getCommunicateSummaryId() != null) {
                            CommunicateAiSummaryApiDto communicateAiSummaryApiDto = communicateSummaryApiService
                                    .getByCommunicateSummaryId(oldcom.getCommunicateAiSummaryApiDto().getCommunicateSummaryId());
                            newcom.setCommunicateAiSummaryApiDto(communicateAiSummaryApiDto);
                        }
                        break;
                    }
                }
                relatedList.add(newcom.getCommunicateRecordId());
            }

            if (relatedList != null && relatedList.size() > 0) {
                List<PhoneticWriting> phoneticWritingList = phoneticWritingMapper.getPhoneticWritingList(relatedList);
                if (phoneticWritingList != null && phoneticWritingList.size() > 0) {
                    for (int i = 0; i < list.size(); i++) {
                        for (int j = 0; j < phoneticWritingList.size(); j++) {
                            if (list.get(i).getCommunicateRecordId().equals(phoneticWritingList.get(j).getRelatedId())) {
                                if (phoneticWritingList.get(j) != null) {
                                    if (StringUtils.isNotBlank(phoneticWritingList.get(j).getOriginalContent())) {
                                        list.get(i).setIsTranslation(1);
                                    } else {
                                        list.get(i).setIsTranslation(0);
                                    }
                                } else {
                                    list.get(i).setIsTranslation(0);
                                }
                            }
                        }
                    }
                }
            }
            return list;
        } catch (IOException e) {
            return null;
        }

    }

    @Override
    public List<TraderContact> getTraderContact(TraderContact traderContact) {
        String url = httpUrl + "trader/gettradercontact.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<TraderContact>>> TypeRef2 = new TypeReference<ResultInfo<List<TraderContact>>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderContact, clientId, clientKey,
                    TypeRef2);

            List<TraderContact> list = (List<TraderContact>) result2.getData();
            return list;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderAddress getTraderAddress(TraderAddress traderAddress) {
        String url = httpUrl + ErpConst.TRADER_ADDRESS_QUERY;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderAddress>> TypeRef2 = new TypeReference<ResultInfo<TraderAddress>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderAddress, clientId, clientKey,
                    TypeRef2);

            TraderAddress ta = (TraderAddress) result2.getData();
            return ta;
        } catch (IOException e) {
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public Boolean saveAddCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request,
                                      HttpSession session) throws Exception {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();
        //VDERP-15625
        //销售管理-商机库/商机列表添加沟通记录中增加商机精准度
        if (communicateRecord.getBusinessChanceAccuracy() != null) {
            BussinessChance bussinessChanceEntity = new BussinessChance();
            bussinessChanceEntity.setBussinessChanceId(communicateRecord.getBussinessChanceId());
            bussinessChanceEntity.setModTime(DateUtil.sysTimeMillis());
            bussinessChanceEntity.setBusinessChanceAccuracy(communicateRecord.getBusinessChanceAccuracy());
            bussinessChanceMapper.updateByPrimaryKeySelective(bussinessChanceEntity);
        }

        communicateRecord.setCompanyId(user.getCompanyId());
        String begin = request.getParameter("begin");
        String end = request.getParameter("end");
        if (StringUtils.isNotBlank(end)) {
            communicateRecord.setEndtime(DateUtil.convertLong(end, "yyyy-MM-dd HH:mm:ss"));
        }
        communicateRecord.setBegintime(DateUtil.convertLong(begin, "yyyy-MM-dd HH:mm:ss"));

        if (request.getParameter("nextDate") != "") {
            communicateRecord.setNextContactDate(request.getParameter("nextDate"));
            communicateRecord.setIsDone(ErpConst.ZERO);
        }

        communicateRecord.setTraderType(ErpConst.ONE);
        communicateRecord.setAddTime(time);
        communicateRecord.setCreator(user.getUserId());
        communicateRecord.setModTime(time);
        communicateRecord.setUpdater(user.getUserId());
        communicateRecord.setContactContent(request.getParameter("content"));

        if (null != request.getParameterValues("tagId")) {
            communicateRecord.setContactContent("1");
        }
        if (null != request.getParameterValues("tagName")) {
            communicateRecord.setContactContent("1");
        }

        communicateRecord.setContact(request.getParameter("name"));
        communicateRecord.setContactMob(request.getParameter("telephone"));

        //历史沟通信息处理
        CommunicateRecord old = new CommunicateRecord();

        old.setCommunicateType(communicateRecord.getCommunicateType());
        old.setRelatedId(communicateRecord.getRelatedId());
        logger.info("saveCommunicate updateCommunicateDone");
        communicateService.updateCommunicateDone(communicateRecord, session);
        logger.info("saveCommunicate insert");
        communicateRecordMapper.insert(communicateRecord);
        //工作台-精准营销活动，需要更新活动-销售任务中的是否已沟通更新为已沟通。-Kerwin VDERP-15559
        marketPlanApiService.updateMarketPlanTraderSendMsg(communicateRecord.getTraderId());

        Integer communicateRecordId = communicateRecord.getCommunicateRecordId();
        logger.info("saveCommunicate insert END");
        //VDERP-10080新增商机，解锁公海锁定客户start
        if (StringUtils.isNotBlank(communicateRecord.getCoid())) {
            List<Map<String, Object>> coidInfo = callService.getRecordListByCoid(Collections.singletonList(communicateRecord.getCoid()));
            if (coidInfo.size() > 0) {
                int coidLength = coidInfo.stream().map(item -> Integer.valueOf(item.get("FILELEN").toString())).reduce(0, Math::max);
                if (coidLength > 120) {
                    publicCustomerRecordApiService.unLockTrader(communicateRecord.getTraderId(), communicateRecordId, ErpConst.TWO, user.getUserId());
                }
            }
        }
        //VDERP-10080新增商机，解锁公海锁定客户end

        if (communicateRecordId > 0) {
            // 标签
            if (null != request.getParameterValues("tagId")) {// 标签库标签
                String[] tagIds = request.getParameterValues("tagId");
                List<Tag> tagList = new ArrayList<>();
                for (String tagId : tagIds) {
                    Tag tag = new Tag();
                    tag.setTagType(SysOptionConstant.ID_32);
                    tag.setTagId(Integer.parseInt(tagId));
                    tag.setCompanyId(user.getCompanyId());
                    tagList.add(tag);
                }

                communicateRecord.setTag(tagList);
            }
            if (null != request.getParameterValues("tagName")) {// 自定义标签
                String[] tagNames = request.getParameterValues("tagName");
                List<String> tagNameList = new ArrayList<>();
                for (String tagName : tagNames) {
                    tagNameList.add(tagName);
                }

                communicateRecord.setTagName(tagNameList);
            }
            if (StringUtils.isBlank(request.getParameter("content"))) {
                // 接口调用
                String url = httpUrl + "trader/saveaddcommunicatetag.htm";

                // 定义反序列化 数据格式
                final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
                };
                try {
                    ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, communicateRecord, clientId,
                            clientKey, TypeRef2);
                    Integer res = (Integer) result2.getCode();

                    if (res.equals(0)) {
                        return true;
                    }
                    return false;
                } catch (IOException e) {
                    return false;
                }
            }

        }
        return true;
    }

    @Override
    public CommunicateRecord getCommunicate(CommunicateRecord communicateRecord) {
        CommunicateRecord communicate = communicateRecordMapper.getCommunicate(communicateRecord);
        // 接口调用
        String url = httpUrl + "trader/tradercommunicateinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<CommunicateRecord>> TypeRef2 = new TypeReference<ResultInfo<CommunicateRecord>>() {
        };
        try {
            ResultInfo<CommunicateRecord> result2 = (ResultInfo<CommunicateRecord>) HttpClientUtils.post(url,
                    communicate, clientId, clientKey, TypeRef2);
            CommunicateRecord res1 = (CommunicateRecord) result2.getData();
            CommunicateRecord res = new CommunicateRecord();
            res = communicateRecordMapper.getCommunicate(res1);
            res.setTag(res1.getTag());
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public Boolean saveEditCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request,
                                       HttpSession session) throws Exception {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();
        //VDERP-15625
        //销售管理-商机库/商机列表添加沟通记录中增加商机精准度
        if (communicateRecord.getBusinessChanceAccuracy() != null) {
            BussinessChance bussinessChanceEntity = new BussinessChance();
            bussinessChanceEntity.setBussinessChanceId(communicateRecord.getRelatedId());
            bussinessChanceEntity.setModTime(DateUtil.sysTimeMillis());
            bussinessChanceEntity.setBusinessChanceAccuracy(communicateRecord.getBusinessChanceAccuracy());
            bussinessChanceMapper.updateByPrimaryKeySelective(bussinessChanceEntity);
        }
        if (StrUtil.isNotBlank(communicateRecord.getPhone())) {
            communicateRecord.setContactMob(communicateRecord.getPhone());
            communicateRecord.setFollowUpType(5901);
        }
        if (Objects.nonNull(communicateRecord.getTraderContactId())) {
            TraderContact traderContact = traderContactMapper.selectByPrimaryKey(communicateRecord.getTraderContactId());
            if (Objects.nonNull(traderContact)) {
                communicateRecord.setContact(traderContact.getName());
            }
        }


        communicateRecord.setCompanyId(user.getCompanyId());
        String begin = request.getParameter("begin");
        String end = request.getParameter("end");
        communicateRecord.setBegintime(DateUtil.convertLong(begin, "yyyy-MM-dd HH:mm:ss"));
        if (StringUtils.isNotBlank(end)) {
            communicateRecord.setEndtime(DateUtil.convertLong(end, "yyyy-MM-dd HH:mm:ss"));
        }

        if (!Objects.equals(request.getParameter("nextDate"), "")) {
            communicateRecord.setNextContactDate(request.getParameter("nextDate"));
        }

        communicateRecord.setModTime(time);
        communicateRecord.setUpdater(user.getUserId());

        if (StringUtil.isBlank(communicateRecord.getContactContent())) {
            communicateRecord.setContactContent(request.getParameter("content"));
        }

        if (null != request.getParameterValues("tagId")) {
            communicateRecord.setContactContent("1");
        }
        if (null != request.getParameterValues("tagName")) {
            communicateRecord.setContactContent("1");
        }

        Integer succ = 0;
        if (null != communicateRecord.getCoid() && !Objects.equals(communicateRecord.getCoid(), "")) {//呼叫中心编辑沟通记录
            communicateRecord.setCreator(user.getUserId());
            succ = communicateRecordMapper.updateByCoidAUserIdNew(communicateRecord);
        } else {
            succ = communicateRecordMapper.updateNew(communicateRecord);
        }

//        if (communicateRecord.getTraderId() != null  && communicateRecord.getCommunicateRecordId() != null && communicateRecord.getTraderId() != 0) {
//            Map<String, Object> byTraderIdGetSaleInfo = traderMapper.findByTraderIdGetSaleInfo(communicateRecord.getTraderId());
//            byTraderIdGetSaleInfo.put("communicateRecordId", communicateRecord.getCommunicateRecordId());
//            communicateRecordMapper.updateBelongUser(byTraderIdGetSaleInfo);
//        }else{
//            Map<String, Object> byTraderIdGetSaleInfo = new HashMap<>();
//            byTraderIdGetSaleInfo.put("userId",user.getUserId());
//            byTraderIdGetSaleInfo.put("userName",user.getUsername());
//            byTraderIdGetSaleInfo.put("traderName","");
//            byTraderIdGetSaleInfo.put("communicateRecordId", communicateRecord.getCommunicateRecordId());
//            communicateRecordMapper.updateBelongUser(byTraderIdGetSaleInfo);
//        }

        if (communicateRecord.getTraderId() != null  && communicateRecord.getCommunicateRecordId() != null && communicateRecord.getTraderId() != 0) {
            Map<String, Object> byTraderIdGetSaleInfo = traderMapper.findByTraderIdGetSaleInfo(communicateRecord.getTraderId());
            if(MapUtils.isNotEmpty(byTraderIdGetSaleInfo)){
                byTraderIdGetSaleInfo.put("communicateRecordId", communicateRecord.getCommunicateRecordId());
                communicateRecordMapper.updateBelongUser(byTraderIdGetSaleInfo);
            }else{
                Map<String, Object> byTraderIdGetSaleInfo2 = new HashMap<>();
                byTraderIdGetSaleInfo2.put("userId",user.getUserId());
                byTraderIdGetSaleInfo2.put("userName",user.getUsername());
                byTraderIdGetSaleInfo2.put("traderName","");
                byTraderIdGetSaleInfo2.put("communicateRecordId", communicateRecord.getCommunicateRecordId());
                communicateRecordMapper.updateBelongUser(byTraderIdGetSaleInfo2);
            }
        }else{
            Map<String, Object> byTraderIdGetSaleInfo = new HashMap<>();
            byTraderIdGetSaleInfo.put("userId",user.getUserId());
            byTraderIdGetSaleInfo.put("userName",user.getUsername());
            byTraderIdGetSaleInfo.put("traderName","");
            byTraderIdGetSaleInfo.put("communicateRecordId", communicateRecord.getCommunicateRecordId());
            communicateRecordMapper.updateBelongUser(byTraderIdGetSaleInfo);
        }

        marketPlanApiService.updateMarketPlanTraderSendMsg(communicateRecord.getTraderId());

        if (Objects.nonNull(communicateRecord.getTraderContactId())){
            String positionAll = communicateRecord.getPosition();
            // 更新联系人职位
            if (StringUtil.isNotBlank(communicateRecord.getOtherPosition())){
                String position = StringUtil.isNotBlank(communicateRecord.getPosition()) ? communicateRecord.getPosition()+"," : "";
                positionAll = position + communicateRecord.getOtherPosition();
            }
            logger.info("更新联系人职位信息position：{},TraderContactId:{}", positionAll, communicateRecord.getTraderContactId());
            traderContactMapper.updatePosition(positionAll, communicateRecord.getTraderContactId());
        }
        
        if (succ > 0) {

            // 标签
            if (null != request.getParameterValues("tagId")) {// 标签库标签
                String[] tagIds = request.getParameterValues("tagId");
                List<Tag> tagList = new ArrayList<>();
                for (String tagId : tagIds) {
                    Tag tag = new Tag();
                    tag.setTagType(SysOptionConstant.ID_32);
                    tag.setTagId(Integer.parseInt(tagId));
                    tag.setCompanyId(user.getCompanyId());
                    tagList.add(tag);
                }

                communicateRecord.setTag(tagList);
            }
            if (null != request.getParameterValues("tagName")) {// 自定义标签
                String[] tagNames = request.getParameterValues("tagName");
                List<String> tagNameList = new ArrayList<>();
                for (String tagName : tagNames) {
                    tagNameList.add(tagName);
                    communicateRecord.setContactContent(tagName + ",");
                }

                communicateRecord.setTagName(tagNameList);
            }

            // 接口调用
            String url = httpUrl + "trader/saveaddcommunicatetag.htm";

            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
            };
            try {
                ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, communicateRecord, clientId, clientKey, TypeRef2);
                Integer res = (Integer) result2.getCode();

                if (res.equals(0)) {
                    Integer communicateType = communicateRecord.getCommunicateType();
                    //商机 线索
                    if (SysOptionConstant.ID_244.equals(communicateType) || SysOptionConstant.ID_4109.equals(communicateType)) {
                        RemoteCrmTaskApiService.TaskDto taskDto = new RemoteCrmTaskApiService.TaskDto();
                        String format = String.format("%s\n 联系人：%s\n 手机：%s\n",
                                communicateRecord.getNextContactContent(),
                                communicateRecord.getContact(),
                                communicateRecord.getContactMob());
                        taskDto.setTaskContent(format);
                        if (SysOptionConstant.ID_244.equals(communicateType)) {
                            taskDto.setBizType(ErpConstant.ONE);
                            BussinessChance bussinessChance = bussinessChanceMapper.selectByPrimaryKey(communicateRecord.getRelatedId());
                            if(bussinessChance !=null){
                                taskDto.setBizNo(bussinessChance.getBussinessChanceNo());
                            }

                        }
                        if (SysOptionConstant.ID_4109.equals(communicateType)) {
                            taskDto.setBizType(ErpConstant.TWO);
                            String leadsNo = businessLeadsApiService.getBusinessLeadsNoById(communicateRecord.getRelatedId());
                            taskDto.setBizNo(leadsNo);
                        }

                        taskDto.setBizId(communicateRecord.getRelatedId());
                        taskDto.setMainTaskType(ErpConstant.FIVE);
                        if(communicateRecord.getNextContactDate() != null){//如果没有下次沟通日期，则没有任务
                            taskDto.setCommitTime(new Date());
                            DateTime dateTime = cn.hutool.core.date.DateUtil.parseDate(communicateRecord.getNextContactDate());
                            taskDto.setDeadline(cn.hutool.core.date.DateUtil.endOfDay(dateTime));
                            taskDto.setTodoUserList(Collections.singletonList(user.getUserId()));
                            taskDto.setApiUserId(user.getUserId());
                            taskDto.setApiUserName(user.getUsername());
                            remoteCrmTaskApiService.saveTask(taskDto);
                        }else{
                            taskDto.setApiUserId(user.getUserId());
                            taskDto.setApiUserName(user.getUsername());
                            remoteCrmTaskApiService.autoHandle(taskDto);
                        }
                    }
                    commuicateTrack(communicateRecord);
                    return true;
                }
                return false;
            } catch (IOException e) {
                return false;
            }
        }
        return false;
    }

    //${username}(${number})从${callType}模块给客户去电
    private void commuicateTrack(CommunicateRecord communicateRecord) {
        try {
            TrackStrategy trackStrategy = null;
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            Integer userId = communicateRecord.getCreator();
            User user = userService.getUserById(userId);
            if (Objects.isNull(user)) {
                return;
            }
            trackParams.put("track_user", user);
            trackParams.put("username", user.getUsername());
            trackParams.put("number", user.getNumber());
            trackParams.put("traderId", communicateRecord.getTraderId());
            //呼叫中心COID,通过COID获取，获取通话类型
            String coid = communicateRecord.getCoid();
            //通过COID获取
            CommunicateRecordApiDto communicateRecordApiDto = communicateRecordApiService.getCommunicateRecordByCoid(coid);
            if (Objects.isNull(communicateRecordApiDto)) {
                return;
            }
            Integer communicateType = communicateRecordApiDto.getCommunicateType();
            //商机/报价/订单/售后/客户联系人/底部拨号
            String callType = "";
            //商机库列表/详情
            if (SysOptionConstant.ID_244.equals(communicateType)) {
                callType = "商机";
            }
            //报价单详情
            else if (SysOptionConstant.ID_245.equals(communicateType)) {
                callType = "报价";
            }
            //订单详情
            else if (SysOptionConstant.ID_246.equals(communicateType)) {
                callType = "订单";
            }
            //售后详情
            else if (SysOptionConstant.ID_248.equals(communicateType)) {
                callType = "售后";
            }
            //线索
            else if (SysOptionConstant.ID_4083.equals(communicateType)) {
                callType = "线索";
            }
            //商机线索
            else if (SysOptionConstant.ID_4109.equals(communicateType)) {
                callType = "商机线索";
            }
            //营销任务
            else if (SysOptionConstant.ID_COMMNCATE_TYPE_5501.equals(communicateType)) {
                callType = "营销任务";
            }
            //客户详情
            else if (SysOptionConstant.ID_COMMNCATE_TYPE_5502.equals(communicateType)) {
                callType = "客户联系人";
            }
            //其他
            else {
                callType = "底部拨号";
            }
            trackParams.put("callType", callType);
            trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.COMMUNICATE_NEW_RECORD);
            trackParamsData.setEventTrackingEnum(EventTrackingEnum.COMMUNICATE_NEW_RECORD);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackStrategy.track(trackParamsData);
        } catch (Exception e) {
            logger.error("埋点：{}，失败，不影响正常业务", EventTrackingEnum.COMMUNICATE_NEW_RECORD.getArchivedName(), e);
        }
    }

    /**
     * <b>Description:</b><br>
     * 获取客户的财务与资质信息
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月31日 上午10:27:30
     */
    @Override
    public Map<String, Object> getFinanceAndAptitudeByTraderId(TraderCertificateVo traderCertificateVo, String queryType) {

        // 此处赋值参数仅作为编辑时区分不同查询，1-单独查询资质信息；2-单独查询财务信息；3-申请账期
        if ("all".equals(queryType)) {
            traderCertificateVo.setCreator(0);
        } else if ("zz".equals(queryType)) {// 资质
            traderCertificateVo.setCreator(1);
        } else if ("cw".equals(queryType)) {// 财务
            traderCertificateVo.setCreator(2);
        } else {// 账期
            traderCertificateVo.setCreator(3);
        }

        try {
            Map<String, Object> map = getNewFinanceAndAptitude(traderCertificateVo);
            //帐期列表
            if (map != null && map.containsKey("billList")) {
                JSONArray jsonArray = JSONArray.fromObject(map.get("billList"));
                List<TraderAccountPeriodApply> billList = (List<TraderAccountPeriodApply>) JSONArray.toCollection(jsonArray, TraderAccountPeriodApply.class);
                if (billList != null && billList.size() > 0) {
                    for (TraderAccountPeriodApply tb : billList) {
                        tb.setCreatorNm(getUserNameByUserId(tb.getCreator()));
                    }
                    map.put("billList", billList);
                }
            }

            return map;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException();
        }
    }


    @Override
    public Map<String, Object> getNewFinanceAndAptitude(TraderCertificateVo traderCertificate) throws Exception {
        if (traderCertificate == null || traderCertificate.getTraderId() == null || traderCertificate.getTraderId() == 0) {
            logger.error("getNewFinanceAndAptitude ERROR   trader_id{}", JsonUtils.translateToJson(traderCertificate));
        }
        Map<String, Object> map = new HashMap<>();
        TraderCertificateVo tc = null;
        if (traderCertificate != null && (traderCertificate.getCreator() == 0 || traderCertificate.getCreator() == 1)) {
            // 纳税人附件
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_3900);
            List<TraderCertificateVo> taxPayerList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            map.put("taxPayer", taxPayerList);
            // 营业执照
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_25);
            List<TraderCertificateVo> tcList2 = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            map.put("business", tcList2);
            // 税务登记证
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_26);
            tc = traderCertificateMapper.getTraderCertificatePageVo(traderCertificate);
            map.put("tax", tc);
            // 组织机构代码证
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_27);
            tc = traderCertificateMapper.getTraderCertificatePageVo(traderCertificate);
            map.put("orga", tc);
            if (null == traderCertificate.getCustomerType() || traderCertificate.getCustomerType() == 2) {
                // 二类医疗资质
                traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_28);
                List<TraderCertificateVo> tcList1 = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
                map.put("twoMedical", tcList1);
                // 三类医疗资质
                traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_29);
                List<TraderCertificateVo> tList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
                map.put("threeMedical", tList);
                // 医疗资质信息
                TraderMedicalCategory tcmc = new TraderMedicalCategory();
                tcmc.setTraderId(traderCertificate.getTraderId());
                tcmc.setTraderType(traderCertificate.getTraderType());
                tcmc.setMedicalCategoryLevel(239);
                List<TraderMedicalCategoryVo> two = traderMedicalCategoryMapper.getTraderMedicalCategoryList(tcmc);
                map.put("two", two);
                tcmc.setMedicalCategoryLevel(240);
                List<TraderMedicalCategoryVo> three = traderMedicalCategoryMapper.getTraderMedicalCategoryList(tcmc);
                map.put("three", three);
                tcmc.setMedicalCategoryLevel(250);
                List<TraderMedicalCategoryVo> newTwo = traderMedicalCategoryMapper.getTraderMedicalCategoryList(tcmc);
                map.put("newTwo", newTwo);
                tcmc.setMedicalCategoryLevel(251);
                List<TraderMedicalCategoryVo> newThree = traderMedicalCategoryMapper.getTraderMedicalCategoryList(tcmc);
                map.put("newThree", newThree);
            } else if (traderCertificate.getCustomerType() == 1) {
                // 医疗机构执业许可证
                traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_438);
                List<TraderCertificateVo> tcList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
                map.put("practice", tcList);

                //中医诊所备案证
                traderCertificate.setSysOptionDefinitionId(SysOptionConstant.CHINESE_MEDICAL_CLINIC);
                TraderCertificateVo clinic = traderCertificateMapper.getTraderCertificatePageVo(traderCertificate);
                map.put("clinic", clinic);

                // 动物诊疗许可证
                traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ANIMAL_CLINIC);
                TraderCertificateVo animal = traderCertificateMapper.getTraderCertificatePageVo(traderCertificate);
                map.put("animal", animal);

                // 其他
                traderCertificate.setSysOptionDefinitionId(SysOptionConstant.OTHER);
                List<TraderCertificateVo> others = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
                map.put("others", others);
            }
            // 医疗器械生产许可证
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_439);
            List<TraderCertificateVo> productList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            if (productList.size() > 0) {
                map.put("product", productList.get(0));
                map.put("productList", productList);
            }
            // 医疗器械经营许可证
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_678);
            List<TraderCertificateVo> operateList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            if (operateList.size() > 0) {
                map.put("operate", operateList.get(0));
                map.put("operateList", operateList);
            }
            // begin by franlin for[3865 供应商资质中，增加销售人授权书，销售人信息]  at 2018-06-21
            // 销售授权书 与 销售人信息
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_1100);
            List<TraderCertificateVo> saleAuthList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            if (saleAuthList.size() > 0) {
                map.put("saleAuth", saleAuthList.get(0));
                map.put("saleAuthList", saleAuthList);
            }

            // 第一类医疗器械生产备案凭证
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_1101);
            List<TraderCertificateVo> firstCategoryCertificateList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            if (firstCategoryCertificateList.size() > 0) {
                map.put("firstCategoryCertificate", firstCategoryCertificateList.get(0));
                map.put("firstCategoryCertificateList", firstCategoryCertificateList);
            }

            // 生产企业生产产品登记表
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_1102);
            List<TraderCertificateVo> productRegistrationList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            if (productRegistrationList.size() > 0) {
                map.put("productRegistration", productRegistrationList.get(0));
                map.put("productRegistrationList", productRegistrationList);
            }
            // end by franlin for[3865 供应商资质中，增加销售人授权书，销售人信息]  at 2018-06-21

            //品牌授权书
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_894);
            List<TraderCertificateVo> brandList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            map.put("brandBookList", brandList);
            //其他授权书
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_895);
            List<TraderCertificateVo> otherList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            map.put("otherList", otherList);

            //随货同行模板
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_896);
            List<TraderCertificateVo> goodWithTemList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            map.put("goodWithTemList", goodWithTemList);

            //质量保证协议
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_897);
            List<TraderCertificateVo> qualityAssuranceList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            map.put("qualityAssuranceList", qualityAssuranceList);

            //售后服务承诺书
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_898);
            List<TraderCertificateVo> afterSalesBookList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            map.put("afterSalesBookList", afterSalesBookList);

            //质量体系调查表或合格供应商档案
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_899);
            List<TraderCertificateVo> qualityAndTraderList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            map.put("qualityAndTraderList", qualityAndTraderList);

            // add by fralin.wu for[耗材商城的客户管理] at 2018-11-22 begin
            // 代付款证明
            traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_905);
            List<TraderCertificateVo> pofPayList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
            map.put("pofPayList", pofPayList);
            // add by fralin.wu for[耗材商城的客户管理] at 2018-11-22 end

        }
        if (traderCertificate != null && (traderCertificate.getCreator() == 0 || traderCertificate.getCreator() == 2)) {
            // 财务信息
            TraderFinanceVo traderFinance = new TraderFinanceVo();
            traderFinance.setTraderId(traderCertificate.getTraderId());
            if (traderCertificate.getTraderType() == 1) {
                traderFinance.setTraderType(1);
            } else if (traderCertificate.getTraderType() == 2) {
                traderFinance.setTraderType(2);
            }
            if (traderCertificate.getTraderId() == null || traderCertificate.getTraderId() == 0) {
                logger.error("getNewFinanceAndAptitude ERROR   trader_id{}", JsonUtils.translateToJson(traderCertificate));
            }
            traderFinance = traderCustomerMapper.getTraderCustomerFinance(traderFinance);
            if (traderFinance == null) {
                traderFinance = new TraderFinanceVo();
            }
            if (traderCertificate.getTraderType() == 1) {
                TraderCustomerVo tcv = traderCustomerMapper.getCustomerByTraderId(traderCertificate.getTraderId());
                if (tcv != null) {
                    traderFinance.setAmount(tcv.getAmount());
                    traderFinance.setPeriodAmount(tcv.getPeriodAmount());
                    traderFinance.setPeriodDay(tcv.getPeriodDay());
                    map.put("customer", tcv);
                } else {
                    logger.warn("查询资质时查不到该客户", JSON.toJSON(traderCertificate));
                }
            } else if (traderCertificate.getTraderType() == 2) {
                TraderSupplierVo tsv = traderSupplierMapper.getSupplierByTraderId(traderCertificate.getTraderId());
                traderFinance.setAmount(tsv.getAmount());
                traderFinance.setPeriodAmount(tsv.getPeriodAmount());
                traderFinance.setPeriodDay(tsv.getPeriodDay());
                map.put("supplier", tsv);

                TraderPeriodData traderPeriodData = traderDataService.getTraderPeriodData(traderCertificate.getTraderId(), traderCertificate.getTraderType());
                if (traderPeriodData != null && traderPeriodData.getPeriodAmount() != null && traderPeriodData.getPeriodAmountOccupy() != null && traderPeriodData.getPeriodAmountUsed() != null) {
                    traderFinance.setBalanceAccount(traderPeriodData.getPeriodAmount().subtract(traderPeriodData.getPeriodAmountOccupy()).subtract(traderPeriodData.getPeriodAmountUsed()));
                }
                BigDecimal periodAmountUsed = traderDataService.getPeriodAmountUsed(traderCertificate.getTraderId(), traderCertificate.getTraderType());
                if (periodAmountUsed != null && periodAmountUsed.compareTo(new BigDecimal(0)) > 0) {
                    traderFinance.setCanApplyPeriod(0);
                } else {
                    traderFinance.setCanApplyPeriod(1);
                }
            }
            map.put("finance", traderFinance);
        }
        // 账期
        List<TraderAccountPeriodApply> billList = traderCustomerMapper.getTraderAccountPeriodApplyList(traderCertificate.getTraderId(), traderCertificate.getTraderType());
        if (billList != null && billList.size() > 0) {
            map.put("billList", billList);
        }
        //银行帐号
        TraderFinance tf = new TraderFinance();
        tf.setTraderId(traderCertificate.getTraderId());
        tf.setTraderType(traderCertificate.getTraderType());// 客户
        if (traderCertificate.getTraderId() == null || traderCertificate.getTraderId() == 0) {
            logger.error("getNewFinanceAndAptitude ERROR trader_id{}", JsonUtils.translateToJson(traderCertificate));
        }
        List<TraderFinance> bankList = traderCustomerMapper.getTraderCustomerFinanceList(tf);
        if (bankList != null && bankList.size() > 0) {
            map.put("bankList", bankList);
        }

        // add by fralin.wu for[耗材商城的客户管理] at 2018-11-22 begin
        // 客户基础信息
        Trader trader = traderMapper.selectByPrimaryKey(traderCertificate.getTraderId());
        map.put("trader", trader);
        // add by fralin.wu for[耗材商城的客户管理] at 2018-11-22 end
        return map;
    }

    /**
     * <b>Description:</b><br> 保存资质
     *
     * @param tcvList
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年6月5日 下午4:58:30
     */
    @Override
    @Transactional
    public ResultInfo saveMedicalAptitude(TraderVo traderVo, List<TraderCertificateVo> tcvList,
                                          List<TraderMedicalCategory> twomcList, List<TraderMedicalCategory> threemcList,
                                          List<TraderMedicalCategory> newTwoMCList, List<TraderMedicalCategory> newThreeMCList) {
        try {
            for (TraderCertificateVo tcv : tcvList) {
                if (StringUtil.isNotBlank(tcv.getUri()) && tcv.getUri().contains("resourceId")) {
                    tcv.setDomain(this.picUrl);
                } else {
                    tcv.setDomain(this.fileUrl);
                }
            }
            Integer traderId = traderVo.getTraderId();
            Integer traderType = traderVo.getTraderType();
            Integer threeInOne = traderVo.getThreeInOne();
            Integer medicalQualification = traderVo.getMedicalQualification();
            if (traderId != 0) {
                //先删除资质
                traderCertificateMapper.delTraderCertificateByTraderIdAndTraderType(traderId, traderType);
                if (traderType != 0) {
                    // 先删除医疗资质详情
                    TraderMedicalCategory tc = new TraderMedicalCategory();
                    tc.setTraderId(traderId);
                    tc.setTraderType(traderType);
                    traderMedicalCategoryMapper.delObject(tc);
                }
            }
            if (CollectionUtils.isNotEmpty(tcvList)) {
                for (TraderCertificateVo tcv : tcvList) {
                    tcv.setOssResourceId(FileUtil.getOssResourceIdFromStr(tcv.getUri()));
                    tcv.setTraderType(traderType);
                    if (tcv.getUri() != null) {
                        //文件上传兼容做法
                        if (tcv.getUri() != null && !tcv.getUri().contains("resourceId")) {
                            tcv.setDomain(fileUrl);
                        }

                        traderCertificateMapper.insertSelective(tcv);//先将第一张图片插入到数据库
                    }
                    if (tcv.getUris() != null) {//判断数组不为null
                        for (int i = 0; i < tcv.getUris().length; i++) {//循环更改tcv父类中的URI
                            if (tcv.getUris()[i] != null && !tcv.getUris()[i].equals("")) {//判断元素不为空且不为null（页面中的input的value有值）
                                tcv.setUri(tcv.getUris()[i]);

                                //文件上传兼容做法
                                if (tcv.getUri() != null && !tcv.getUri().contains("resourceId")) {
                                    tcv.setDomain(fileUrl);
                                }

                                tcv.setOssResourceId(FileUtil.getOssResourceIdFromStr(tcv.getUri()));
                                String name = "";

                                if (tcv.getNames() == null || tcv.getNames().length == 0 || tcv.getNames().length != tcv.getUris().length) {
                                    name = tcv.getUris()[i].substring(tcv.getUris()[i].lastIndexOf("image/") + 6);
                                } else {
                                    name = tcv.getNames()[i];
                                }
                                tcv.setName(name);
                                traderCertificateMapper.insertSelective(tcv);//插入
                            }
                        }
                    }
                }
                Trader trader = new Trader();
                trader.setTraderId(tcvList.get(0).getTraderId());
                trader.setModTime(System.currentTimeMillis());
                trader.setUpdater(tcvList.get(0).getUpdater());
                if (threeInOne == null || "".equals(threeInOne) || "0".equals(threeInOne)) {
                    trader.setThreeInOne(0);
                } else {
                    trader.setThreeInOne(Integer.valueOf(threeInOne));
                }
                if (medicalQualification == null || "".equals(medicalQualification) || "0".equals(medicalQualification)) {
                    trader.setMedicalQualification(0);
                } else {
                    trader.setMedicalQualification(Integer.valueOf(medicalQualification));
                }
                traderMapper.updatePartBySelective(trader);
            }
            if (CollectionUtils.isNotEmpty(twomcList)) {

                for (TraderMedicalCategory list : twomcList) {
                    // 在添加
                    traderMedicalCategoryMapper.insertSelective(list);
                }
            }
            if (CollectionUtils.isNotEmpty(threemcList)) {

                for (TraderMedicalCategory list : threemcList) {
                    // 在添加
                    traderMedicalCategoryMapper.insertSelective(list);
                }
            }
            if (CollectionUtils.isNotEmpty(newThreeMCList)) {

                for (TraderMedicalCategory list : newThreeMCList) {
                    // 在添加
                    traderMedicalCategoryMapper.insertSelective(list);
                }
            }
            if (CollectionUtils.isNotEmpty(newTwoMCList)) {

                for (TraderMedicalCategory list : newTwoMCList) {
                    // 在添加
                    traderMedicalCategoryMapper.insertSelective(list);
                }
            }

            return ResultInfo.success();
        } catch (Exception e) {
            logger.error("", e);
            throw new RuntimeException("保存供应商资质信息失败");
        }

    }

    @Override
    public ResultInfo<?> saveNewMedicaAptitude(TraderVo traderVo, List<TraderCertificateVo> tcvList, List<TraderMedicalCategory> twomcList, List<TraderMedicalCategory> threemcList, List<TraderMedicalCategory> newTwomcList, List<TraderMedicalCategory> newThreemcList) {
        Map<String, Object> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(tcvList)) {
            for (TraderCertificateVo tcv : tcvList) {
                if (tcv == null) {
                    continue;
                }
                if (StringUtil.isBlank(tcv.getUri())) {
                    tcv.setDomain(ossUrl);
                } else if (tcv.getUri().startsWith("/file/display")) {
                    tcv.setDomain(ossUrl);
                } else {
                    tcv.setDomain(picUrl);
                }

            }
        }
        map.put("traderVo", traderVo);
        map.put("tcv", tcvList);
        map.put("two", twomcList);
        map.put("three", threemcList);
        map.put("newTwo", newTwomcList);
        map.put("newThree", newThreemcList);
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_APTITUDE,
                    map, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public Map<String, Object> getUserTraderByTraderNameListPage(RTraderJUser rTraderJUser, Page page) {
        List<RTraderJUser> rTraderJUserList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        try {

            List<TraderCustomer> traderList = null;
            Trader trader = new Trader();
            trader.setCompanyId(rTraderJUser.getCompanyId());
            trader.setTraderName(rTraderJUser.getTraderName());

            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<TraderCustomer>>> TypeRef = new TypeReference<ResultInfo<List<TraderCustomer>>>() {
            };
            String url = httpUrl + "tradercustomer/getusercustomerbytradernamelistpage.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, trader, clientId, clientKey, TypeRef, page);
            traderList = (List<TraderCustomer>) result.getData();
            page = result.getPage();

            if (traderList.size() > 0) {
                for (TraderCustomer t : traderList) {
                    RTraderJUser traderJUser = new RTraderJUser();
                    traderJUser.setTraderId(t.getTraderId());
                    traderJUser.setTraderName(t.getTrader().getTraderName());
                    traderJUser.setChangeTimes(t.getBuyCount());
                    traderJUser.setLevel(t.getCustomerLevelStr());

                    String region = (String) regionService.getRegion(t.getTrader().getAreaId(), 2);
                    if (null != region) {
                        traderJUser.setAreaStr(region);
                    }

                    User sale = userMapper.getUserByTraderId(t.getTraderId(), ErpConst.ONE);
                    if (null != sale) {
                        traderJUser.setOwnerUser(sale.getUsername());
                    }

                    rTraderJUserList.add(traderJUser);
                }
            }

            map.put("list", rTraderJUserList);
            map.put("page", page);
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return map;
    }


    @Override
    public AssignCustomerInfo getUserTraderByTraderNameList(Integer traderId) {

        AssignCustomerInfo assignCustomerInfo = traderCustomerMapper.queryAssignInfo(traderId);
        String region = (String) regionService.getRegion(assignCustomerInfo.getAreaId(), 2);
        if (StringUtil.isNotBlank(region)) {
            assignCustomerInfo.setAreaStr(region);
        }
        PublicCustomerRecordDto publicCustomerRecordDto = publicCustomerRecordApiService.getPublicCustomerRecordDto(assignCustomerInfo.getTraderCustomerId());
        assignCustomerInfo.setOrgBelongUser(Optional.ofNullable(publicCustomerRecordDto).map(PublicCustomerRecordDto::getOriginUserName).orElse("-"));
        assignCustomerInfo.setPublicCustomer(Objects.equals(assignCustomerInfo.getBelongUser(),"Bei") ? "是" : "否");
        
        logger.info("公海客户信息{}", JSONUtil.toJsonStr(assignCustomerInfo));
        return assignCustomerInfo;
    }

    @Override
    public AssignUserInfo getAssignUserInfo(AssignUserInfoReqDto assignUserInfoReqDto) {
        RTraderJUser rTraderJUser = new RTraderJUser();
        rTraderJUser.setUserId(assignUserInfoReqDto.getUserId());
        rTraderJUser.setCompanyId(CommonConstants.COMPANY_ID_1);
        if (Objects.nonNull(assignUserInfoReqDto.getProvinceId())){
            rTraderJUser.setAreaId(assignUserInfoReqDto.getProvinceId());
        }
        if (Objects.nonNull(assignUserInfoReqDto.getCityId())){
            rTraderJUser.setAreaId(assignUserInfoReqDto.getCityId());
        }
        if (Objects.nonNull(assignUserInfoReqDto.getRegionId())){
            rTraderJUser.setAreaId(assignUserInfoReqDto.getRegionId());
        }
//        Integer userSupplierNum = this.getUserCustomerNum(rTraderJUser, assignUserInfoReqDto.getUserId()).size();
        List<RTraderJUser> traderJUsers = rTraderJUserMapper.getUserCustomerNum(rTraderJUser.getUserId(),rTraderJUser.getAreaId());
        Integer publicNum = publicCustomerRecordApiService.getPublicNum(assignUserInfoReqDto.getUserId(),rTraderJUser.getAreaId());
        
        AssignUserInfo assignUserInfo = new AssignUserInfo();
        assignUserInfo.setUserName(userService.getUserById(assignUserInfoReqDto.getUserId()).getUsername());
        assignUserInfo.setBelongCustomerNum(CollUtil.isEmpty(traderJUsers) ? 0 : (int) traderJUsers.stream().distinct().count());
        assignUserInfo.setPublicCustomerNum(publicNum);
        return assignUserInfo;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Boolean assignSingleCustomer(Integer traderId, Integer single_to_user, Integer companyId, User currentUser, Integer assignReasonType) {
        RTraderJUser rTraderJUser = new RTraderJUser();
        rTraderJUser.setTraderType(ErpConst.ONE);
        rTraderJUser.setTraderId(traderId);
        RTraderJUser ru = rTraderJUserMapper.getUserByTraderId(traderId);
        Integer preUserId = null;
        if (ru != null) {
            preUserId = ru.getUserId();
        }
        rTraderJUserMapper.deleteRTraderJUser(rTraderJUser);

        logger.info("【过期交易者归属变更记录】删除了交易者归属绑定关系 - traderId:{},oldUser:{}",
                rTraderJUser.getTraderId(), preUserId);
        Long nowTimeMillisecond = DateUtil.gainNowDate();
        deleteOldRTraderJUserModifyRecord(rTraderJUser, currentUser.getUserId(), nowTimeMillisecond);

        RTraderJUser traderJUser = new RTraderJUser();
        traderJUser.setTraderType(ErpConst.ONE);
        traderJUser.setTraderId(traderId);
        traderJUser.setUserId(single_to_user);

        int insert = rTraderJUserMapper.insert(traderJUser);

        //记录交易者归属变更记录(新增最新的记录)
        logger.info("【新增交易者归属变更记录】新增了交易者归属绑定关系 - traderId:{},fromUser:{}, toUser:{}",
                traderJUser.getTraderId(), preUserId, traderJUser.getUserId());
        insertRTraderJUserModifyRecordWhenUpdateTraderJUser(traderJUser, preUserId, currentUser.getUserId(), nowTimeMillisecond);


        //VDERP-3164 可合并商机已确认客户，归属销售逻辑优化 将客户的商机，迁移到新的销售名下
        logger.info("修改客户{}的归属销售为{}，并迁移商机归属", traderId, single_to_user);
        bussinessChanceMapper.updateSaleUserOfBussinessChanceByTraderList(single_to_user, Collections.singletonList(traderId));

        //VDERP-17057  【客户档案】ERP客户档案时间轴 客户划拨
        if (insert > 0 && Objects.nonNull(assignReasonType)) {
            try {
                //客户分配页面操作
                if (assignReasonType == 0) {
                    TrackStrategy trackStrategy = null;
                    TrackParamsData trackParamsData = new TrackParamsData();
                    Map<String, Object> trackParams = new HashMap<>();
                    trackParams.put("track_user", currentUser);
                    //获取原归属销售和分配后的归属销售
                    User newUser = userMapper.selectByPrimaryKey(single_to_user);
                    trackParams.put("newUserName", newUser.getUsername());
                    trackParams.put("newUserNumber", newUser.getNumber());
                    trackParams.put("traderId", traderId);
                    //客户没有原始销售，展示文案与有原始销售不一样
                    if (Objects.isNull(preUserId)) {
                        trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_NEW);
                        trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_NEW);
                        trackParamsData.setTrackParams(trackParams);
                    } else {
                        User oldUser = userMapper.selectByPrimaryKey(preUserId);
                        trackParams.put("oldUserName", oldUser.getUsername());
                        trackParams.put("oldUserNumber", oldUser.getNumber());
                        trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_OLD);
                        trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_OLD);
                        trackParamsData.setTrackParams(trackParams);
                    }
                    trackParamsData.setTrackResult(ResultInfo.success());
                    trackStrategy.track(trackParamsData);
                }
                //VDERP-17057  【客户档案】ERP客户档案时间轴  落入公海分配虚拟销售
                if (assignReasonType == 1) {
                    Map<String, Object> trackParams = new HashMap<>();
                    trackParams.put("traderId", traderId);
                    if (Objects.nonNull(preUserId)) {
                        User oldUser = userMapper.selectByPrimaryKey(preUserId);
                        trackParams.put("oldUserName", oldUser.getUsername());
                        trackParams.put("oldUserNumber", oldUser.getNumber());
                    }
                    TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_CUSTOMER_LOST_PUBLIC);
                    TrackParamsData trackParamsData = new TrackParamsData();
                    trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_CUSTOMER_LOST_PUBLIC);
                    trackParamsData.setTrackParams(trackParams);
                    trackParamsData.setTrackResult(ResultInfo.success());
                    trackStrategy.track(trackParamsData);
                }
            } catch (Exception e) {
                logger.error("埋点：{}，失败，不影响正常业务", EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_NEW.getArchivedName(), e);
            }
        }
        if (insert > 0) {
            WebAccountVo webAccountVo = new WebAccountVo();
            webAccountVo.setUserId(single_to_user);
            webAccountVo.setTraderId(traderId);
            webAccountVo.setModTime(new Date());
            webAccountMapper.updateErpUserId(webAccountVo);
            SendMessageTreader(null, single_to_user, traderId);
            sendMsgToImIfChangeTraderBelongUser(traderId, preUserId, single_to_user);
            return true;
        }
        return false;
    }

    /**
     * 客户的归属平台发生变更推送消息
     */
    private void sendMsgIfTraderBelongPlatformChange(Integer traderId, Integer nowPlatform, Integer prePlatform) {
        if (null != prePlatform && prePlatform.equals(nowPlatform)) {
            return;
        }
        com.alibaba.fastjson.JSONObject json = new com.alibaba.fastjson.JSONObject();
        json.put("traderId", traderId);
        json.put("nowPlatform", nowPlatform);
        json.put("prePlatform", prePlatform);
        Timer time = new Timer();
        time.schedule(new TimerTask() {
            @Override
            public void run() {
                opMsgProducer.sendMsg(RabbitConfig.TRADER_BELONG_PLATFORM_EXCHANGE, null, json.toJSONString());
            }
        }, 5000);

    }


    /**
     * @Description: 归属平台Id
     * @Param:
     * @return:
     * @Author: addis
     * @Date: 2020/3/2
     */
    @Override
    public Integer userIdPlatFromId(Integer userId, Integer companyId) {
        List<Position> positionList = userMapper.userPositionOrganization(userId, companyId);
        if (positionList != null && positionList.size() > 0) {
            Map<String, Integer> orgId = new HashMap<>();
            orgId.put("B2B事业部", b2bbusinessDivisionId);
            orgId.put("医械购诊所业务部", YiXiePurchaseId);
            orgId.put("科研购业务部", scientificResearchTrainingId);
            Integer orgIdTwo = positionList.get(0).getOrgId();

            if (orgIdTwo.equals(b2bbusinessDivisionId)) {
                return 1;//贝登医疗
            } else if (orgIdTwo.equals(YiXiePurchaseId)) {
                return 2;//医械购
            } else if (orgIdTwo.equals(scientificResearchTrainingId)) {
                return 3;//科研购
            }
            Organization organization = new Organization();
            organization.setOrgId(orgIdTwo);
            Map<String, Integer> orgMap = childOrganization(organization, orgId);
            if (orgMap == null) {
                return 5;
            }
            for (Map.Entry<String, Integer> map : orgMap.entrySet()) {
                if ("B2B事业部".equals(map.getKey())) {
                    return 1;//贝登医疗
                } else if ("医械购诊所业务部".equals(map.getKey())) {
                    return 2;//医械购
                } else if ("科研购业务部".equals(map.getKey())) {
                    return 3;//科研购
                } else {
                    return 5;//其他
                }
            }
        }
        return 5;
    }


    @Override
    public List<Integer> assignBatchCustomer(User currentUser, Integer from_user, Integer batch_to_user, Integer areaId) {
        Trader trader = new Trader();
        Integer companyId = currentUser.getCompanyId();
        trader.setCompanyId(companyId);
        trader.setAreaId(areaId);

        List<Integer> rTraderJUserIds = new ArrayList<>();
        List<Integer> traderIdList = new ArrayList<>();

        Integer belongPlatform = userService.getBelongPlatformOfUser(batch_to_user, companyId);

            RTraderJUser traderJUser = new RTraderJUser();
            traderJUser.setTraderType(ErpConst.ONE);
            traderJUser.setAreaId(areaId);
            traderJUser.setUserId(from_user);

            List<RTraderJUser> traderList = rTraderJUserMapper.getUserTraderForAssign(traderJUser);
            if (traderList.size() == 0) {
                return null;
            }

            for (RTraderJUser r : traderList) {
                rTraderJUserIds.add(r.getrTraderJUserId());
                traderIdList.add(r.getTraderId());
            }

        List<RTraderJUser> rTraderJUserList = rTraderJUserMapper.getRTraderJUserListByRTraderJUserIds(rTraderJUserIds);
        int update = rTraderJUserMapper.updateByKey(batch_to_user, rTraderJUserIds);
        for (RTraderJUser rTraderJUser : rTraderJUserList) {

            logger.info("【过期交易者归属变更记录】删除了交易者归属绑定关系 - traderId:{},oldUser:{}",
                    rTraderJUser.getTraderId(), rTraderJUser.getUserId());
            Long nowTimeMillisecond = DateUtil.gainNowDate();
            deleteOldRTraderJUserModifyRecord(rTraderJUser, batch_to_user, nowTimeMillisecond);

            logger.info("【新增交易者归属变更记录】新增了交易者归属绑定关系 - traderId:{},fromUser:{}, toUser:{}",
                    rTraderJUser.getTraderId(), rTraderJUser.getUserId(), batch_to_user);
            //记录交易者归属变更记录(新增最新的记录)
            RTraderJUserModifyRecord rTraderJUserModifyRecordAdd = new RTraderJUserModifyRecord();
            rTraderJUserModifyRecordAdd.setTraderType(rTraderJUser.getTraderType());
            rTraderJUserModifyRecordAdd.setUserId(batch_to_user);
            rTraderJUserModifyRecordAdd.setOldUserId(rTraderJUser.getUserId());
            rTraderJUserModifyRecordAdd.setCreator(batch_to_user);
            rTraderJUserModifyRecordAdd.setStartTime(nowTimeMillisecond);
            rTraderJUserModifyRecordAdd.setTraderId(rTraderJUser.getTraderId());
            rTraderJUserModifyRecordMapper.insert(rTraderJUserModifyRecordAdd);
        }

        //VDERP-3164 可合并商机已确认客户，归属销售逻辑优化 将客户的商机，迁移到新的销售名下
        logger.info("批量分配客户：{}的归属销售为：{}", traderIdList.toString(), batch_to_user);
        bussinessChanceMapper.updateSaleUserOfBussinessChanceByTraderList(batch_to_user, traderIdList);

        //VDERP-17057  【客户档案】ERP客户档案时间轴 客户划拨
        if(update > 0) {
        	try {
        		for (Integer traderId : traderIdList) {
        			TrackParamsData trackParamsData = new TrackParamsData();
        			Map<String, Object> trackParams = new HashMap<>();
        			trackParams.put("track_user", currentUser);
        			//获取原归属销售和分配后的归属销售
        			User newUser = userMapper.selectByPrimaryKey(batch_to_user);
        			trackParams.put("newUserName", newUser.getUsername());
        			trackParams.put("newUserNumber", newUser.getNumber());
        			trackParams.put("traderId", traderId);
        			//客户没有原始销售，展示文案与有原始销售不一样
        			User oldUser = userMapper.selectByPrimaryKey(from_user);
        			trackParams.put("oldUserName", oldUser.getUsername());
        			trackParams.put("oldUserNumber", oldUser.getNumber());
        			TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_OLD);
        			trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_OLD);
        			trackParamsData.setTrackParams(trackParams);
        			trackParamsData.setTrackResult(ResultInfo.success());
        			trackStrategy.track(trackParamsData);
        		}
        	}catch(Exception e) {
        		logger.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.BASE_INFO_CUSTOMER_CHANGE_BY_OLD.getArchivedName(),e);
        	}

        }

        if (update > 0) {
            for (Integer traderId : traderIdList) {

                WebAccountVo webAccountVo = new WebAccountVo();
                webAccountVo.setUserId(batch_to_user);
                webAccountVo.setTraderId(traderId);
                //VDERP-5598 如果客户目前归属平台是集采，那么变更归属销售时，不同步更新其归属平台
                Trader trader1 = traderMapper.getTraderByTraderId(traderId);
                if (trader1 != null) {
                    webAccountVo.setBelongPlatform(trader1.getBelongPlatform());
                }
                webAccountVo.setModTime(new Date());
                webAccountMapper.updateErpUserId(webAccountVo);
                
                //给IM发送归属销售变更的消息
                sendMsgToImIfChangeTraderBelongUser(traderId, from_user, batch_to_user);
            }
            //更新贝登会员-重构了，增加了traderIdList参数
            updateVedengMember(traderIdList);
            SendMessageTreader(rTraderJUserIds, batch_to_user, null);
            return traderIdList;
        }
        return null;
    }

    @Autowired
    private ImMsgProducer msgProducer;

    /**
     * <b>Description:</b>IM发送客户归属销售变更信息<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/7/17
     */
    private void sendMsgToImIfChangeTraderBelongUser(Integer traderId, Integer preUserId, Integer nowUserId) {
        ChangeTraderBelongUserMsg msg = new ChangeTraderBelongUserMsg();
        msg.setTraderId(traderId);
        msg.setPreUserId(preUserId);
        msg.setNowUserId(nowUserId);
        try {
            logger.info("归属销售变更给Im发送消息,traderId:{},preUserId:{},nowUserId:{}", traderId, preUserId, nowUserId);
            msgProducer.sendMsg(RabbitConfig.ERP_TRADER_SERVICE_EXCHANGE, RabbitConfig.ERP_TRADER_ROUTINGKEY, JsonUtils.translateToJson(msg));
        } catch (Exception ex) {
            logger.error("变更归属销售给Im发送消息失败,traderId:{},preUserId:{},nowUserId:{},error:{}", traderId, preUserId, nowUserId, ex);
        }
    }

    @Override
    public List<RTraderJUser> getUserCustomerNum(RTraderJUser rTraderJUser, Integer userId) {
        Trader trader = new Trader();
        trader.setCompanyId(rTraderJUser.getCompanyId());
        trader.setAreaId(rTraderJUser.getAreaId());
        trader.setAreaIds(rTraderJUser.getAreaIds());

        RTraderJUser traderJUser = new RTraderJUser();
        traderJUser.setTraderType(ErpConst.ONE);
//            traderJUser.setTraderIds(traderIds);
        traderJUser.setUserId(userId);
        traderJUser.setAreaId(rTraderJUser.getAreaId());
        traderJUser.setAreaIds(rTraderJUser.getAreaIds());

        List<RTraderJUser> traderList = rTraderJUserMapper.getUserTraderForAssign(traderJUser);

        return traderList;
    }

    /**
     * <b>Description:</b><br> 获取客户财务信息
     *
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年6月7日 下午3:16:24
     */
    @Override
    public TraderFinanceVo getTraderFinanceByTraderId(TraderFinanceVo traderFinance) {
        final TypeReference<ResultInfo<TraderFinanceVo>> TypeRef = new TypeReference<ResultInfo<TraderFinanceVo>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_CUSTOMER_FINANCE, traderFinance,
                    clientId, clientKey, TypeRef);
            TraderFinanceVo tf = (TraderFinanceVo) result.getData();
            return tf;
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br> 保存客户财务信息
     *
     * @param traderFinance
     * @param user
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年6月7日 下午4:12:18
     */
    @Override
    public ResultInfo saveCustomerFinance(TraderFinance traderFinance, User user) {
        traderFinance.setAverageTaxpayerDomain(picUrl);
        if (traderFinance.getTraderFinanceId() == null || "".equals(traderFinance.getTraderFinanceId())) {
            traderFinance.setCreator(user.getUserId());
            traderFinance.setAddTime(System.currentTimeMillis());
            traderFinance.setModTime(System.currentTimeMillis());
            traderFinance.setUpdater(user.getUserId());
        } else {
            traderFinance.setUpdater(user.getUserId());
            traderFinance.setModTime(System.currentTimeMillis());
        }

        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_CUSTOMER_FINANCE,
                    traderFinance, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                // 供应商新增财务信息后，推送供应商信息至金蝶
                if (traderFinance.getTraderType() == 2) {
                    TraderSupplier supplierInfo = traderSupplierMapper.getSuplierInfoByTraderId(traderFinance.getTraderId());
                    KingDeeSupplierDto kingDeeSupplierInfo = traderSupplierApiService.getKingDeeSupplierInfo(supplierInfo.getTraderSupplierId());
                    kingDeeSupplierApiService.register(kingDeeSupplierInfo);
                    logger.info("供应商财务信息提交同步推送供应商信息至金蝶，供应商id：{}", supplierInfo.getTraderSupplierId());
                }
            }
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public TraderCustomer getTraderCustomerForAccountPeriod(TraderCustomer traderCustomer) {
        // 接口调用
        String url = httpUrl + "trader/gettradercustomerforaccountperiod.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderCustomer>> TypeRef2 = new TypeReference<ResultInfo<TraderCustomer>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomer, clientId, clientKey,
                    TypeRef2);
            TraderCustomer res = (TraderCustomer) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderAccountPeriodApply getTraderCustomerLastAccountPeriodApply(Integer traderId) {
        TraderAccountPeriodApply accountPeriodApply = new TraderAccountPeriodApply();
        accountPeriodApply.setTraderId(traderId);
        accountPeriodApply.setTraderType(ErpConst.ONE);
        // 接口调用
        String url = httpUrl + "trader/gettraderlastaccountperiodapply.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderAccountPeriodApply>> TypeRef2 = new TypeReference<ResultInfo<TraderAccountPeriodApply>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, accountPeriodApply, clientId, clientKey,
                    TypeRef2);
            TraderAccountPeriodApply res = (TraderAccountPeriodApply) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public ResultInfo saveAccountPeriodApply(TraderAccountPeriodApply traderAccountPeriodApply, HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();

        traderAccountPeriodApply.setAddTime(time);
        traderAccountPeriodApply.setModTime(time);
        traderAccountPeriodApply.setCreator(user.getUserId());
        traderAccountPeriodApply.setUpdater(user.getUserId());

        // 接口调用
        String url = httpUrl + "trader/saveaccountperiodapply.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Integer>> TypeRef2 = new TypeReference<ResultInfo<Integer>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderAccountPeriodApply, clientId, clientKey,
                    TypeRef2);
            //ResultInfo res = (ResultInfo) result2.getData();
            return result2;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public Integer getCustomerCategory(Integer traderCustomerId) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_CUSTOMER_CATEGORY, traderCustomerId, clientId, clientKey,
                    TypeRef2);
            if (result2 == null || result2.getCode() != 0) {
                return 0;
            }
            return (Integer) result2.getData();
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderCustomerVo getCustomerBussinessInfo(Integer traderId) {
        // 接口调用
        String url = httpUrl + "tradercustomer/getcustomerbussinessinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderCustomerVo>> TypeRef2 = new TypeReference<ResultInfo<TraderCustomerVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderId, clientId, clientKey, TypeRef2);
            TraderCustomerVo res = (TraderCustomerVo) result2.getData();

            if (null != res) {
                //数据处理(地区)
                Integer areaId = res.getAreaId();
                if (areaId > 0) {
                    String region = (String) regionService.getRegion(areaId, 2);
                    res.setAddress(region);
                }
                //归属销售
                User sale = userMapper.getUserByTraderId(res.getTraderId(), ErpConst.ONE);
                if (null != sale) {
                    res.setOwnerSale(sale.getUsername());
                }
                // TODO 26_V02 客户账期信息调整为新账期逻辑
                setAccountPeriodInfo(res, null, res.getCompanyId());
            }

            return res;
        } catch (NullPointerException | IOException ex) {
            return null;
        }
    }

    @Override
    public Map<String, Object> getTerminalPageList(TraderCustomerVo vo, Page page) {
        List<TraderCustomerVo> terminalList = null;
        Map<String, Object> map = new HashMap<>();
        String url = httpUrl + "tradercustomer/getterminalpagelist.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<TraderCustomerVo>>> TypeRef2 = new TypeReference<ResultInfo<List<TraderCustomerVo>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, vo, clientId, clientKey, TypeRef2, page);
            terminalList = (List<TraderCustomerVo>) result.getData();
            page = result.getPage();

            if (!terminalList.isEmpty()) {
                for (int i = 0; i < terminalList.size(); i++) {
                    if (terminalList.get(i) != null) {
                        if (terminalList.get(i).getAreaId() != null) {
                            if (regionService.getRegion(terminalList.get(i).getAreaId(), 2) != null) {
                                terminalList.get(i).setAddress(regionService.getRegion(terminalList.get(i).getAreaId(), 2).toString());
                            }
                        }
                        if (terminalList.get(i).getTraderId() != null) {
                            terminalList.get(i).setPersonal(userMapper.getUserNameByTraderId(terminalList.get(i).getTraderId()));
                        }
                    }
                }
            }
            map.put("terminalList", terminalList);
            map.put("page", page);
        } catch (IOException e) {
            return map;
        }
        return map;
    }

    /**
     * <b>Description:</b><br> 根据traderId查询客户信息
     *
     * @param traderCustomer
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月5日 下午1:40:05
     */
    @Override
    public TraderCustomerVo getTraderCustomerVo(TraderCustomer traderCustomer) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_CUSTOMER_INFO, traderCustomer, clientId, clientKey,
                    TypeRef2);
            JSONObject json = JSONObject.fromObject(result2.getData());
            TraderCustomerVo tcv = (TraderCustomerVo) JSONObject.toBean(json, TraderCustomerVo.class);
            tcv.setAddress(getAddressByAreaId(tcv.getAreaId()));
            tcv.setCustomerProperty(getCustomerCategory(tcv.getTraderCustomerId()));
            return tcv;
        } catch (IOException e) {
            return null;
        }
    }

    /**
     * <b>Description:</b><br> 查看联系人的详情包括行业背景
     *
     * @param traderContact
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月5日 下午2:49:21
     */
    @Override
    public Map<String, Object> viewTraderContact(TraderContact traderContact) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_CONTACT_DETAIL, traderContact, clientId, clientKey,
                    TypeRef2);
            Map<String, Object> map = (Map<String, Object>) result2.getData();
            if (map.containsKey("experience")) {
                JSONArray jsonArray = JSONArray.fromObject(map.get("experience"));
                List<TraderContactExperienceVo> tceList = (List<TraderContactExperienceVo>) JSONArray.toCollection(jsonArray, TraderContactExperienceVo.class);
                for (TraderContactExperienceVo traderContactExperienceVo : tceList) {
                    if (traderContactExperienceVo.getAreas() != null) {
                        String[] areaId = traderContactExperienceVo.getAreas().split(",");
                        StringBuffer sb = new StringBuffer();
                        for (String areaid : areaId) {
                            sb.append(getAddressByAreaId(Integer.valueOf(areaid))).append("、");
                            traderContactExperienceVo.setAddress(sb.toString());
                        }
                    }
                }
                map.put("experience", tceList);
            }
            return map;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br> 保存新增客户联系人的行业背景
     *
     * @param traderContactExperience
     * @param tcebaList
     * @param tcebbList
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月6日 下午5:14:59
     */
    @Override
    public ResultInfo saveAddContactExperience(TraderContactExperience traderContactExperience, User user,
                                               List<TraderContactExperienceBussinessArea> tcebaList,
                                               List<TraderContactExperienceBussinessBrand> tcebbList) {
        if (traderContactExperience != null && (traderContactExperience.getTraderContactExperienceId() == null || traderContactExperience.getTraderContactExperienceId() == 0)) {
            traderContactExperience.setAddTime(DateUtil.sysTimeMillis());
            traderContactExperience.setCreator(user.getUserId());
        }
        traderContactExperience.setModTime(DateUtil.sysTimeMillis());
        traderContactExperience.setUpdater(user.getUserId());
        Map<String, Object> map = new HashMap<>();
        map.put("traderContactExperience", traderContactExperience);
        if (tcebaList != null && tcebaList.size() > 0) {
            map.put("tcebaList", tcebaList);
        }
        if (tcebbList != null && tcebbList.size() > 0) {
            map.put("tcebbList", tcebbList);
        }
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_CONTACT_EXPERIENCR, map, clientId, clientKey,
                    TypeRef2);

            return result2;
        } catch (IOException e) {
            return null;
        }
    }


    /**
     * <b>Description:</b><br> 联系人背景
     *
     * @param traderContactExperience
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年5月23日 下午3:21:28
     */
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getTraderContactExperience(TraderContactExperience traderContactExperience) {
        String url = httpUrl + ErpConst.TRADER_CONTACTS_EXPERIENCE;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderContactExperience, clientId, clientKey,
                    TypeRef2);


            Map<String, Object> map = (Map<String, Object>) result2.getData();
            if (map.containsKey("tcebaList")) {
                List<TraderContactExperienceBussinessAreaVo> tcebaList = new ArrayList<>();
                JSONArray jsonArray = JSONArray.fromObject(map.get("tcebaList"));
                List<TraderContactExperienceBussinessArea> list =
                        (List<TraderContactExperienceBussinessArea>) JSONArray.toCollection(jsonArray, TraderContactExperienceBussinessArea.class);
                TraderContactExperienceBussinessAreaVo tcebav = null;
                for (TraderContactExperienceBussinessArea tceba : list) {
                    tcebav = new TraderContactExperienceBussinessAreaVo();
                    tcebav.setAreaId(tceba.getAreaId());
                    tcebav.setAreaIds(tceba.getAreaIds());
                    tcebav.setTraderContactExperienceBussinessAreaId(tceba.getTraderContactExperienceBussinessAreaId());
                    tcebav.setTraderContactExperienceId(tceba.getTraderContactExperienceId());
                    tcebav.setAddress(getAddressByAreaId(tceba.getAreaId()));
                    tcebaList.add(tcebav);
                }
                map.put("tcebaList", tcebaList);
            }
            return map;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br> 删除联系人的行业背景
     *
     * @param traderContactExperience
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月6日 下午5:14:59
     */
    @Override
    public ResultInfo<?> delContactExperience(TraderContactExperience traderContactExperience) {
        String url = httpUrl + ErpConst.DEL_CONTACTS_EXPERIENCE;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderContactExperience, clientId, clientKey,
                    TypeRef2);
            return result2;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public Map<String, Object> searchCustomerPageList(TraderCustomerVo vo, Page page) {
        List<TraderCustomerVo> searchCustomerList = null;
        Map<String, Object> map = new HashMap<>();
        String url = httpUrl + "tradercustomer/searchcustomerpagelist.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<TraderCustomerVo>>> TypeRef2 = new TypeReference<ResultInfo<List<TraderCustomerVo>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, vo, clientId, clientKey, TypeRef2, page);
            searchCustomerList = (List<TraderCustomerVo>) result.getData();
            page = result.getPage();

            setCustomerSomeInfo(searchCustomerList);
            map.put("searchCustomerList", searchCustomerList);
            map.put("page", page);
        } catch (IOException e) {
            return map;
        }
        return map;
    }

    @Override
    public TraderCustomerVo getCustomerInfoByTraderCustomer(TraderCustomer traderCustomer) {
        // 接口调用
        String url = httpUrl + "tradercustomer/getcustomerinfobytradercustomer.htm";
        if (NumberUtils.toInt(traderCustomer.getTraderId() + "") == 0
                && NumberUtils.toInt(traderCustomer.getTraderCustomerId() + "") == 0) {
            return null;
        }
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderCustomerVo>> TypeRef2 = new TypeReference<ResultInfo<TraderCustomerVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomer, clientId, clientKey,
                    TypeRef2);
            TraderCustomerVo res = (TraderCustomerVo) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderCustomerVo getTraderCustomerInfo(Integer traderId) {
        // 接口调用
        String url = httpUrl + "tradercustomer/gettradercustomerinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderCustomerVo>> TypeRef = new TypeReference<ResultInfo<TraderCustomerVo>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderId, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                TraderCustomerVo res = (TraderCustomerVo) result.getData();
                if (res != null) {
                    //数据处理(地区)
                    if (null != res.getAreaId() && res.getAreaId() > 0) {
                        String region = (String) regionService.getRegion(res.getAreaId(), 2);
                        res.setAddress(region);
                    }
                    //归属销售
                    User sale = userMapper.getUserByTraderId(res.getTraderId(), ErpConst.ONE);
                    if (null != sale) {
                        res.setOwnerSale(sale.getUsername());
                    }
                    // TODO 26_V02 客户账期信息调整为新账期逻辑
                    setAccountPeriodInfo(res, null, res.getCompanyId());

                }
                return res;
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
        return null;
    }

    @Override
    public List<CommunicateRecord> getCommunicateRecordList(CommunicateRecord communicateRecord) {
        // 后期调用接口查询询价、报价、订单、售后 沟通记录
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("communicateRecord", communicateRecord);
        List<CommunicateRecord> communicateRecordList = communicateRecordMapper.getCommunicateRecordList(map);

        // 调用接口补充信息（联系人，沟通目的、方式 ，沟通内容（标签））、商机、报价、订单
        String url = httpUrl + "trader/tradercommunicaterecord.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<CommunicateRecord>>> TypeRef2 = new TypeReference<ResultInfo<List<CommunicateRecord>>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, communicateRecordList, clientId,
                    clientKey, TypeRef2);

            List<CommunicateRecord> list = (List<CommunicateRecord>) result2.getData();
            for (int i = 0; i < list.size(); i++) {
                //判断是否转译完成
                PhoneticWriting phoneticWriting = phoneticWritingMapper.getPhoneticWriting(list.get(i).getCommunicateRecordId());
                if (phoneticWriting != null) {
                    if (StringUtils.isNotBlank(phoneticWriting.getOriginalContent())) {
                        list.get(i).setIsTranslation(1);
                    } else {
                        list.get(i).setIsTranslation(0);
                    }
                } else {
                    list.get(i).setIsTranslation(0);
                }
            }
            return list;
        } catch (IOException e) {
            return null;
        }

    }

    /**
     * <b>Description:</b><br> 查询交易者的账期分页信息
     *
     * @param traderVo
     * @param page
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年11月10日 下午4:13:12
     */
    @Override
    public Map<String, Object> getAmountBillListPage(TraderVo traderVo, Page page) {
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_AMOUNT_BILL_PAGE, traderVo, clientId, clientKey, TypeRef, page);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            Map<String, Object> map = new HashMap<>();
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            List<TraderAmountBillVo> billList = (List<TraderAmountBillVo>) JSONArray.toCollection(jsonArray, TraderAmountBillVo.class);
            if (null != billList && billList.size() > 0) {
                for (TraderAmountBillVo amountBillVo : billList) {
                    amountBillVo.setCreatorUser(getUserNameByUserId(amountBillVo.getCreator()));
                }
            }
            page = result.getPage();
            map.put("page", page);
            map.put("list", billList);
            return map;
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    /**
     * <b>Description:</b><br> 查询交易者的交易流水分页信息
     *
     * @param traderVo
     * @param page
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年11月10日 下午4:13:12
     */
    @Override
    public Map<String, Object> getCapitalBillListPage(TraderVo traderVo, Page page) {
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_CAPITAL_BILL_PAGE, traderVo, clientId, clientKey, TypeRef, page);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            Map<String, Object> map = new HashMap<>();
            List<TraderAmountBillVo> list = (List<TraderAmountBillVo>) result.getData();
            page = result.getPage();
            map.put("page", page);
            map.put("list", list);
            return map;
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    @Override
    public ResultInfo saveUplodeBatchCustomer(List<Trader> list, User user) {
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            String url = httpUrl + "trader/saveuplodebatchcustomer.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, list, clientId, clientKey, TypeRef);
            //VDERP-17057  执行成功，如果有新增的客户ID，则进行埋点
            if (result.getCode() == 0 && Objects.nonNull(result.getData())) {
                saveUpdateBatchCustomerTrack(user, result);
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    private void saveUpdateBatchCustomerTrack(User user, ResultInfo<?> result) {
        try {
            List<Integer> traderIdList = (List) result.getData();
            for (Integer traderId : traderIdList) {
                TrackParamsData trackParamsData = new TrackParamsData();
                Map<String, Object> trackParams = new HashMap<>();
                trackParams.put("track_user", user);
                trackParams.put("traderId", traderId);
                TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_NEW_CUSTOMER);
                trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_NEW_CUSTOMER);
                trackParamsData.setTrackParams(trackParams);
                trackParamsData.setTrackResult(ResultInfo.success());
                trackStrategy.track(trackParamsData);
            }
        } catch (Exception e) {
            logger.error("埋点：{}，失败，不影响正常业务", EventTrackingEnum.BASE_INFO_NEW_CUSTOMER.getArchivedName(), e);
        }
    }

    @Override
    public TraderAccountPeriodApply getAccountPeriodDaysApplyInfo(Integer accountPeriodDaysApplyId) {
        // 接口调用
        String url = httpUrl + "trader/getaccountperioddaysapplybyid.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderAccountPeriodApply>> TypeRef = new TypeReference<ResultInfo<TraderAccountPeriodApply>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, accountPeriodDaysApplyId, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                TraderAccountPeriodApply res = (TraderAccountPeriodApply) result.getData();
                return res;
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
        return null;
    }

    /**
     * <b>Description:</b><br> 删除供应商的银行帐号
     *
     * @param traderFinance
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年12月21日 下午5:06:45
     */
    @Override
    public ResultInfo<?> delSupplierBank(TraderFinance traderFinance) {
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.DEL_SUPPLIER_BANK, traderFinance, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    @Override
    public List<TraderFinance> getTraderCustomerFinanceList(TraderFinance tf) {
        List<TraderFinance> list = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<TraderFinance>>> TypeRef = new TypeReference<ResultInfo<List<TraderFinance>>>() {
        };
        String url = httpUrl + "tradercustomer/gettradercustomerfinancelist.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, tf, clientId, clientKey, TypeRef);
            list = (List<TraderFinance>) result.getData();
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return list;
    }


    @Override
    public TraderInfoTyc getTycInfo(int type, String traderName) {
        String url = httpUrl + "trader/gettraderinfotycbytradername.htm";
        TraderInfoTyc res = new TraderInfoTyc();
        int dayCnt = 0;
        final TypeReference<ResultInfo<TraderInfoTyc>> TypeRef = new TypeReference<ResultInfo<TraderInfoTyc>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderName, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                res = (TraderInfoTyc) result.getData();
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        if (res != null) {
            dayCnt = (int) ((res.getSyncTime() - DateUtil.sysTimeMillis()) / (1000 * 60 * 60 * 24));
        }
        if (res == null || dayCnt >= 15) {
            // 记录调用天眼查API的原因
            if (res == null) {
                logger.info("天眼查API调用：企业信息不存在于缓存中，首次查询企业：{}", traderName);
            } else {
                logger.info("天眼查API调用：缓存已过期，企业：{}，缓存时间：{}天，重新查询", traderName, dayCnt);
            }
            //超过15天查询天眼查接口
            String result = httpSendUtil.queryDetails(2, traderName);
            JSONObject jsonObject = JSONObject.fromObject(result);
            String code = jsonObject.getString("error_code");
            if ("300000".equals(code)) {
                res = new TraderInfoTyc();
                res.setCodeType(2);//查无数据
                return res;
            } else if ("300006".equals(code)) {
                res = new TraderInfoTyc();
                res.setCodeType(3);//余额不足
                return res;
            } else {
                JSONObject json = JSONObject.fromObject(jsonObject.get("result"));

                TraderInfoTyc tycInfo = new TraderInfoTyc();
                if (json.containsKey("updatetime") && StringUtils.isNumeric(json.getString("updatetime"))) {
                    tycInfo.setUpdateTime(Long.parseLong(json.getString("updatetime")));
                }
                if (json.containsKey("fromTime") && StringUtils.isNumeric(json.getString("fromTime"))) {
                    tycInfo.setFromTime(Long.parseLong(json.getString("fromTime")));
                }
                if (json.containsKey("categoryScore") && StringUtils.isNumeric(json.getString("categoryScore"))) {
                    tycInfo.setCategoryScore(Integer.parseInt(json.getString("categoryScore")));
                }
                if (json.containsKey("type") && StringUtils.isNumeric(json.getString("type"))) {
                    tycInfo.setType(Integer.parseInt(json.getString("type")));
                }
                if (json.containsKey("id") && StringUtils.isNumeric(json.getString("id"))) {
                    tycInfo.setId(Long.parseLong(json.getString("id")));
                }
                if (json.containsKey("percentileScore") && StringUtils.isNumeric(json.getString("percentileScore"))) {
                    tycInfo.setPercentileScore(Integer.parseInt(json.getString("percentileScore")));
                }
                if (json.containsKey("regNumber")) {
                    tycInfo.setRegNumber(json.getString("regNumber"));
                }
                if (json.containsKey("phoneNumber")) {
                    tycInfo.setPhoneNumber(json.getString("phoneNumber"));
                }
                if (json.containsKey("regCapital")) {
                    tycInfo.setRegCapital(json.getString("regCapital"));
                }
                if (json.containsKey("name")) {
                    tycInfo.setName(json.getString("name"));
                }
                if (json.containsKey("regInstitute")) {
                    tycInfo.setRegInstitute(json.getString("regInstitute"));
                }
                if (json.containsKey("regLocation")) {
                    tycInfo.setRegLocation(json.getString("regLocation"));
                }
                if (json.containsKey("industry")) {
                    tycInfo.setIndustry(json.getString("industry"));
                }
                if (json.containsKey("approvedTime") && StringUtils.isNumeric(json.getString("approvedTime"))) {
                    tycInfo.setApprovedTime(Long.parseLong(json.getString("approvedTime")));
                }
                if (json.containsKey("orgApprovedInstitute")) {
                    tycInfo.setOrgApprovedInstitute(json.getString("orgApprovedInstitute"));
                }
                if (json.containsKey("logo")) {
                    tycInfo.setLogo(json.getString("logo"));
                }
                if (json.containsKey("taxNumber")) {
                    tycInfo.setTaxNumber(json.getString("taxNumber"));
                }
                if (json.containsKey("businessScope")) {
                    tycInfo.setBusinessScope(json.getString("businessScope"));
                }
                if (json.containsKey("orgNumber")) {
                    tycInfo.setOrgNumber(json.getString("orgNumber"));
                }
                if (json.containsKey("estiblishTime") && StringUtils.isNumeric(json.getString("estiblishTime"))) {
                    tycInfo.setEstiblishTime(Long.parseLong(json.getString("estiblishTime")));
                }
                if (json.containsKey("regStatus")) {
                    tycInfo.setRegStatus(json.getString("regStatus"));
                }
                if (json.containsKey("legalPersonName")) {
                    tycInfo.setLegalPersonName(json.getString("legalPersonName"));
                }
                if (json.containsKey("websiteList")) {
                    tycInfo.setWebsiteList(json.getString("websiteList"));
                }
                if (json.containsKey("toTime") && StringUtils.isNumeric(json.getString("toTime"))) {
                    tycInfo.setToTime(Long.parseLong(json.getString("toTime")));
                }
                if (json.containsKey("legalPersonId") && StringUtils.isNumeric(json.getString("legalPersonId"))) {
                    tycInfo.setLegalPersonId(Long.parseLong(json.getString("legalPersonId")));
                }
                if (json.containsKey("sourceFlag")) {
                    tycInfo.setSourceFlag(json.getString("sourceFlag"));
                }
                if (json.containsKey("actualCapital")) {
                    tycInfo.setActualCapital(json.getString("actualCapital"));
                }
                if (json.containsKey("flag") && StringUtils.isNumeric(json.getString("flag"))) {
                    tycInfo.setFlag(Integer.parseInt(json.getString("flag")));
                }
                if (json.containsKey("correctCompanyId")) {
                    tycInfo.setCorrectCompanyId(json.getString("correctCompanyId"));
                }
                if (json.containsKey("companyOrgType")) {
                    tycInfo.setCompanyOrgType(json.getString("companyOrgType"));
                }
                if (json.containsKey("updateTimes") && StringUtils.isNumeric(json.getString("updateTimes"))) {
                    //新的tyc api没有updateTime属性
                    tycInfo.setUpdateTime(Long.parseLong(json.getString("updateTimes")));
                    tycInfo.setUpdateTimes(Long.parseLong(json.getString("updateTimes")));
                }
                if (json.containsKey("base")) {
                    tycInfo.setBase(json.getString("base"));
                }
                if (json.containsKey("companyType") && StringUtils.isNumeric(json.getString("companyType"))) {
                    tycInfo.setCompanyType(Integer.parseInt(json.getString("companyType")));
                }
                if (json.containsKey("creditCode")) {
                    tycInfo.setCreditCode(json.getString("creditCode"));
                }
                if (json.containsKey("companyId") && StringUtils.isNumeric(json.getString("companyId"))) {
                    tycInfo.setCompanyId(Long.parseLong(json.getString("companyId")));
                }
                if (json.containsKey("historyNames")) {
                    tycInfo.setHistoryNames(json.getString("historyNames"));
                }
                if (json.containsKey("socialStaffNum") && StringUtils.isNumeric(json.getString("socialStaffNum"))) {
                    tycInfo.setSocialStaffNum(Integer.parseInt(json.getString("socialStaffNum")));
                }
                tycInfo.setJsonData(json.toString());
                tycInfo.setSyncTime(DateUtil.sysTimeMillis());
                //更新天眼查信息
                final TypeReference<ResultInfo<Integer>> TypeRef2 = new TypeReference<ResultInfo<Integer>>() {
                };
                String uri = httpUrl + "trader/savetycinfo.htm";
                ResultInfo<?> rs = null;
                try {
                    rs = (ResultInfo<?>) HttpClientUtils.post(uri, tycInfo, clientId, clientKey, TypeRef2);
                } catch (IOException e) {
                    logger.error(Contant.ERROR_MSG, e);
                }
                if (rs != null && rs.getCode() == 0) {
                    tycInfo.setTraderInfoTycId((Integer) rs.getData());
                    return tycInfo;
                } else {
                    return null;
                }
            }

        } else {
            logger.info("天眼查缓存命中，公司名称：{}，已缓存天数：{}天", traderName, dayCnt);
            return res;
        }
    }

    @Override
    public ResultInfo restVerify(TraderCustomer traderCustomer) {
        //更新天眼查信息
        final TypeReference<ResultInfo> TypeRef2 = new TypeReference<ResultInfo>() {
        };
        String uri = httpUrl + "verifiesrecord/restverify.htm";
        ResultInfo<?> rs = null;
        try {
            VerifiesInfo verifiesInfo = new VerifiesInfo();
            verifiesInfo.setRelateTable("T_TRADER_CUSTOMER");
            verifiesInfo.setRelateTableKey(traderCustomer.getTraderCustomerId());
            rs = (ResultInfo<?>) HttpClientUtils.post(uri, verifiesInfo, clientId, clientKey, TypeRef2);

        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }

        return rs;
    }

    @Override
    public Map<String, Object> getBusinessListPage(SaleorderGoodsVo saleorderGoodsVo, Page page) {
        // 调用接口
        String url = httpUrl + "tradercustomer/getbusinesslistpage.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<SaleorderGoodsVo>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoodsVo>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderGoodsVo, clientId, clientKey,
                    TypeRef, page);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<SaleorderGoodsVo> list = (List<SaleorderGoodsVo>) result.getData();
            page = result.getPage();
            Map<String, Object> map = new HashMap<>();
            map.put("list", list);
            map.put("page", page);
            return map;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public TraderCustomerVo getTraderCustomerManageInfoSeconed(TraderCustomer traderCustomer, HttpSession session) {
        // TODO Auto-generated method stub
        User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
        // 接口调用
        String url = httpUrl + "trader/getcustomermanageinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderCustomerVo>> TypeRef2 = new TypeReference<ResultInfo<TraderCustomerVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomer, clientId, clientKey,
                    TypeRef2);
            TraderCustomerVo res = (TraderCustomerVo) result2.getData();
            if (null != res) {
                User sale = userMapper.getUserByTraderId(res.getTraderId(), ErpConst.ONE);
                if (null != sale) {
                    res.setOwnerSale(sale.getUsername());
                }

                CommunicateRecord communicateRecord = new CommunicateRecord();
                communicateRecord.setTraderId(res.getTraderId());
                communicateRecord.setCompanyId(session_user.getCompanyId());
                communicateRecord.setTraderType(ErpConst.ONE);

                CommunicateRecord customerCommunicateCount = communicateRecordMapper.getTraderCommunicateCount(communicateRecord);
                if (null != customerCommunicateCount) {
                    res.setCommuncateCount(customerCommunicateCount.getCommunicateCount());
                    res.setLastCommuncateTime(customerCommunicateCount.getLastCommunicateTime());
                }
                res.setCustomerProperty(getCustomerCategory(res.getTraderCustomerId()));
                User user = null;
                user = userMapper.getUserByTraderId(res.getTraderId(), ErpConst.ONE);
                if (null != user) {
                    res.setPersonal(user.getUsername());
                }

            }
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public ResultInfo saveTraderName(Trader trader) {
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            // 接口调用
            String url = httpUrl + "trader/savetradername.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, trader, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    @Override
    public TraderCustomerVo getCustomerInfo(Integer traderId) {
        // 接口调用
        String url = httpUrl + "tradercustomer/getcustomerinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderCustomerVo>> TypeRef = new TypeReference<ResultInfo<TraderCustomerVo>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderId, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                TraderCustomerVo res = (TraderCustomerVo) result.getData();
                if (res != null) {
                    //数据处理(地区)
                    if (null != res.getAreaId() && res.getAreaId() > 0) {
                        String region = (String) regionService.getRegion(res.getAreaId(), 2);
                        res.setAddress(region);
                    }
                    // TODO 26_V02 客户账期信息调整为新账期逻辑
                    setAccountPeriodInfo(res, null, res.getCompanyId());
                }
                return res;
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
        return null;
    }

    @Override
    public TraderContact getTraderContactInfo(FileDelivery fileDelivery) {
        //联系人默认联系电话
        TraderAddress traderInfo = traderAddressMapper.getTraderContactInfo(fileDelivery);
        //联系人默认地址
        TraderAddress traderAddressInfo = traderAddressMapper.getTraderContactLxInfo(fileDelivery);
        TraderContact tc = new TraderContact();
        //联系信息赋值
        if (traderInfo != null) {
            tc.setName(traderInfo.getUserName());
            tc.setMobile(traderInfo.getMobile());
            tc.setTelephone(traderInfo.getTelephone());
        }
        //联系地址赋值
        if (traderAddressInfo != null) {
            tc.setAreaId(traderAddressInfo.getAreaId());
        }
        return tc;
    }

    @Override
    public Trader getBaseTraderByTraderId(Integer traderId) {
        if (null == traderId) {
            return null;
        }
        return traderMapper.getTraderByTraderId(traderId);
    }

    @Override
    public ResultInfo<?> putTraderSaleUserIdtoHC(Map<String, Object> map) {
        logger.info("客户分配推送至耗材商城接口请求参数");
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        ResultInfo<?> resultInfo = new ResultInfo<>();
        // map转json对象
        JSONObject jsonObject = JSONObject.fromObject(map);
        //System.out.println(jsonObject.toString());
        // 调用接口推送数据
        // 请求头
        Map<String, String> header = new HashMap<String, String>();
        String url = apiUrl + ApiUrlConstant.API_TRADER_SALER_PUT_TO_HC;
        header.put("version", "v1");
        try {
            resultInfo = (ResultInfo<?>) HttpClientUtils.put(url, jsonObject.toString(), header, TypeRef);
        } catch (Exception e) {
            logger.error("客户分配推送至耗材商城异常：", e);
        }
        return resultInfo;
    }

    @Override
    public List<AccountSalerToGo> getAccountSaler(List<Integer> traderIdList) {
        return traderMapper.getAccountSaler(traderIdList);
    }

    @Override
    public Trader getTraderByTraderName(Trader trader, Integer comId) {
        trader.setCompanyId(comId);
        String url = httpUrl + "trader/gettraderbytradername.htm";
        final TypeReference<ResultInfo<Trader>> TypeRef2 = new TypeReference<ResultInfo<Trader>>() {
        };
        try {
            ResultInfo<Trader> result2 = (ResultInfo<Trader>) HttpClientUtils.post(url, trader, clientId, clientKey,
                    TypeRef2);
            if (null == result2) {
                return null;
            }

            Trader res = (Trader) result2.getData();

            return res;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    public void SendMessageTreader(List<Integer> rTraderJUserIds, Integer batch_to_user, Integer traderId) {
        WebAccount webAccount = new WebAccount();
        List<Integer> rTrader = new ArrayList<>();
        if (null != rTraderJUserIds && rTraderJUserIds.size() > 0) {
            webAccount.setTraders(rTraderJUserIds);
        }
        if (traderId != null) {
            rTrader.add(traderId);
            webAccount.setTraders(rTrader);
        }
        List<WebAccount> webAccountList = webAccountMapper.getWebAccontTrader(webAccount);
        List<String> phone = new ArrayList<>();
        if (null != webAccountList && webAccountList.size() > 0) {
            for (WebAccount webAccount1 : webAccountList) {
                if (webAccount1.getUserId() != batch_to_user) {
                    if (StringUtil.isNotBlank(webAccount1.getMobile())) {
                        phone.add(webAccount1.getMobile());
                    }
                }
            }
            Map<String, String> hashMap = new HashMap<>();
            List<Saleorder> saleorderList = new ArrayList<>();
            Saleorder saleorder = new Saleorder();
            saleorder.setCreateMobileList(phone);
            if (null != saleorder && phone.size() > 0) {
                saleorderList = saleorderMapper.selectSaleorderNo(saleorder);
            }
            if (null != saleorderList && saleorderList.size() > 0) {
                List<Integer> userList = new ArrayList<>();
                userList.add(batch_to_user);
                for (Saleorder saleorder1 : saleorderList) {
                    hashMap.put("saleorderNo", saleorder1.getSaleorderNo());
                    MessageUtil.sendMessage2(96, userList, hashMap, "./order/saleorder/view.do?saleorderId=" + saleorder1.getSaleorderId());
                }
            }
        }
    }

    @Override
    public ResultInfo saveMjxContactAdders(TraderMjxContactAdderss t) {
        ResultInfo resultInfo = new ResultInfo<>();
        try {
            WebAccount web = webAccountMapper.getWenAccountInfoByMobile(t.getPhone());
            if (web == null || (web.getTraderId() == 0 && web.getTraderContactId() == 0)) {
                resultInfo.setCode(0);
                resultInfo.setMessage("没有关联erp客户");
                return resultInfo;
            } else {
                MjxAccountAddress mjx = new MjxAccountAddress();
                mjx.setTitleName(t.getTitleName());
                mjx.setTraderId(web.getTraderId());
                mjx.setAddress(t.getDeliveryUserAddress());
                mjx.setDeliveryName(t.getDeliveryUserName());
                String areaIds = t.getDeliveryLevel1Id() + "," + t.getDeliveryLevel2Id() + "," + t.getDeliveryLevel3Id();
                mjx.setAreaIds(areaIds);
                mjx.setIsDeliveryDefault(t.getIsDeliveryDefault());
                mjx.setIsInvoiceDefault(t.getIsInvoiceDefault());
                mjx.setTelphone(t.getDeliveryUserPhone());
                mjx.setModTime(DateUtil.gainNowDate());
                String area = regionService.getRegionNameStringByMinRegionIds(areaIds);
                mjx.setArea(area);
                mjx.setIsEnable(t.getIsEnable());
                mjx.setMjxContactAdderssId(t.getMjxContactAdderssId());
                MjxAccountAddress mjxAccountAddress = mjxAccountAddressMapper.getAddressInfoByParam(mjx);
                if (mjxAccountAddress == null) {
                    mjx.setCreator(t.getPhone());
                    mjx.setAddTime(DateUtil.gainNowDate());
                    int insertCount = mjxAccountAddressMapper.insertSelective(mjx);
                    resultInfo.setCode(0);
                    resultInfo.setMessage("新增成功");
                    resultInfo.setData(insertCount);
                } else {
                    mjx.setAddressId(mjxAccountAddress.getAddressId());
                    int updateCount = mjxAccountAddressMapper.updateByPrimaryKeySelective(mjx);
                    resultInfo.setCode(0);
                    resultInfo.setMessage("更新成功");
                    resultInfo.setData(updateCount);
                }
                logger.info("saveMjxContactAdders: pojo:" + mjx);
            }
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            logger.info("saveMjxContactAddersErro:");
        }
        return resultInfo;
    }

    @Override
    public List<MjxAccountAddress> getMjxAccountAddressList(TraderContactVo traderContactVo) {
        List<MjxAccountAddress> list = null;
        try {
            MjxAccountAddress mjx = new MjxAccountAddress();
            mjx.setTraderId(traderContactVo.getTraderId());
            mjx.setIsEnable(ErpConst.ONE);
            list = mjxAccountAddressMapper.getMjxAccountAddressList(mjx);
            String deliveryDefault = null;
            String invoiceDefault = null;
            for (MjxAccountAddress m : list) {
                deliveryDefault = (m.getIsDeliveryDefault() == 1 ? "默认的收货地址;" : "");
                invoiceDefault = (m.getIsInvoiceDefault() == 1 ? "默认的收票地址;" : "");
                m.setDefaultAddress(deliveryDefault + invoiceDefault);
            }
        } catch (Exception e) {
            logger.info("getMjxAccountAddressListErro:" + e);
        }
        return list;
    }


    @Override
    public int updateCustomerIsProfit(Integer traderId, Integer isProfit) {
        return traderCustomerMapper.updateCustomerIsProfit(traderId, isProfit);
    }

    @Override
    public ResultInfo initCustomerAptitudeCheck(HttpServletRequest request, Page page) {
//        try {
        int count = 0;
        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        TraderCustomer traderCustomer = new TraderCustomer();
        traderCustomer.setCustomerNature(465);
        params.put("trader", traderCustomer);
        List<TraderCustomer> traders = traderCustomerMapper.getTraderCustomerIdsListPage(params);
        for (TraderCustomer t : traders) {
            if (isVerify(t)) {
                count++;
                startCheckAptitude(request, t, null);
            }
        }

        int pageSum = page.getTotalPage();
        if (pageSum > 1) {
            for (int i = 2; i <= pageSum; i++) {
                page.setPageNo(i);
                params.put("page", page);
                List<TraderCustomer> traderCustomers = traderCustomerMapper.getTraderCustomerIdsListPage(params);
                for (TraderCustomer t : traderCustomers) {
                    if (isVerify(t)) {
                        count++;
                        startCheckAptitude(request, t, null);
                    }
                }
            }
        }
//        }catch (Exception ex){
//            return new ResultInfo(-1,"操作失败");
//        }

        return new ResultInfo(1, "操作成功" + count);
    }

    @Override
    public TraderCustomer getTraderCustomerId(Integer traderId) {
        return traderCustomerMapper.getTraderCustomerById(traderId);
    }

    @Override
    public Map childOrganization(Organization organization, Map<String, Integer> orgHashMap) {
        if (organization != null && orgHashMap != null) {
            Map returnMap = new HashMap();
            for (Map.Entry<String, Integer> entry : orgHashMap.entrySet()) {
                List<Organization> organizationList = organizationMapper.childOrganization(entry.getValue());
                for (Organization organization1 : organizationList) {
                    returnMap = getTree(organization, organizationList, entry.getKey(), entry.getValue());
                    if (returnMap != null) {
                        return returnMap;
                    }
                }
            }

        }
        return null;
    }

    private Map getTree(Organization organization, List<Organization> orgIds, String key, Integer value) {
        Integer orgId = organization.getOrgId();
        List<Organization> childs = null;
        Iterator<Organization> iterator = orgIds.iterator();
        while (iterator.hasNext()) {
            Organization m = iterator.next();
            if (m.getOrgId().equals(orgId)) {
                return returnMap(key, value);
            }
            childs = m.getOrganizations();
            if (childs != null) {
                getTree(organization, childs, key, value);
            }
        }
        return null;
    }

    public Map returnMap(String key, Integer value) {
        Map<String, Integer> hashMap = new HashMap<>();
        hashMap.put(key, value);
        return hashMap;
    }

    /**
     * <b>Description:验证客户资质是否全</b><br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> ${date} ${time}
     */
    private boolean isVerify(TraderCustomer customer) {
        List<TraderCertificate> certificates = traderCertificateMapper.getTraderCertificatesByTraderId(customer);
        if (CollectionUtils.isEmpty(certificates)) {
            return false;
        }
        boolean isVerify = false;
        for (TraderCertificate certificate : certificates) {
            if (certificate.getSysOptionDefinitionId() == SysOptionConstant.ID_25
                    && certificate.getBegintime() != null && StringUtil.isNotBlank(certificate.getUri())) {
                isVerify = true;
            }
        }
        return isVerify;
    }


    /**
     * <b>Description:</b><br> 开始审核资质
     *
     * @param traderCustomer 客户信息
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:2019/9/5</b>
     */
    public ResultInfo startCheckAptitude(HttpServletRequest request, TraderCustomer traderCustomer, String taskId) {
        try {
            Map<String, Object> variableMap = new HashMap<String, Object>();
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            // 查询当前订单的一些状态
            /*TraderCustomerVo traderBaseInfo = traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);*/
            //开始生成流程(如果没有taskId表示新流程需要生成)
            if (StringUtil.isBlank(taskId) || taskId.equals("0")) {
                variableMap.put("traderCustomer", traderCustomer);
                variableMap.put("currentAssinee", user.getUsername());
                variableMap.put("processDefinitionKey", "customerAptitudeVerify");
                variableMap.put("businessKey", "customerAptitude_" + traderCustomer.getTraderCustomerId());
                variableMap.put("relateTableKey", traderCustomer.getTraderCustomerId());
                variableMap.put("relateTable", "T_CUSTOMER_APTITUDE");
                actionProcdefService.createProcessInstance(request, "customerAptitudeVerify", "customerAptitude_" + traderCustomer.getTraderCustomerId(), variableMap);
            }
            //默认申请人通过
            //根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "customerAptitude_" + traderCustomer.getTraderCustomerId());
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                taskId = taskInfo.getId();
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "", user.getUsername(), variableMap);
                //如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfoForTrader(taskId, 0);
                }

            }

            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("editApplyValidCustomer:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }

    @Override
    public void setCustomerAptitudeUncheck(Integer traderCustomerId) {
        VerifiesInfo verifiesInfo = getCustomerAptitudeVerifiesInfo(traderCustomerId);
        if (verifiesInfo == null || verifiesInfo.getStatus() == null || verifiesInfo.getVerifiesInfoId() == null) {
            return;
        }
        Trader trader = getTraderByCustomerId(traderCustomerId);
        if (verifiesInfo.getStatus() == 0) {
            String taskId = getCustomerAptitudeTaskId(traderCustomerId);
            if (StringUtil.isBlank(taskId)) {
                verifiesInfoMapper.deleteByPrimaryKey(verifiesInfo.getVerifiesInfoId());
                return;
            }
            recallChangeAptitude(taskId);
            sendMsgIfAptitudeStatusChange(trader.getTraderId(), -1, trader.getBelongPlatform());
        } else {
            verifiesInfoMapper.deleteByPrimaryKey(verifiesInfo.getVerifiesInfoId());
            sendMsgIfAptitudeStatusChange(trader.getTraderId(), -1, trader.getBelongPlatform());
        }
    }

    private void sendMsgIfAptitudeStatusChange(Integer traderId, Integer status, Integer belongPlatform) {
        com.alibaba.fastjson.JSONObject json = new com.alibaba.fastjson.JSONObject();
        json.put("traderId", traderId);
        json.put("status", status);
        json.put("belongPlatform", belongPlatform);
        Timer time = new Timer();
        time.schedule(new TimerTask() {
            @Override
            public void run() {
                opMsgProducer.sendMsg(RabbitConfig.TRADER_APTITUDE_STATUS_EXCHANGE, null, json.toJSONString());
            }
        }, 5000);

    }

    @Override
    public ResultInfo recallChangeAptitude(String taskId) {
        ResultInfo resultUpdateTable = verifiesRecordService.saveVerifiesInfoForTrader(taskId, 3);
        ResultInfo resultDeleteInstance = actionProcdefService.deleteProcessInstance(taskId);
        if (resultUpdateTable.getCode() == 0 && resultDeleteInstance.getCode() == 0) {
            return resultDeleteInstance;
        }
        return new ResultInfo(-1, "操作失败");
    }

    public String getCustomerAptitudeTaskId(Integer traderCustomerId) {
        try {
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "customerAptitude_" + traderCustomerId);
            if (historicInfo != null) {
                TaskInfo taskInfo = (TaskInfo) historicInfo.get("taskInfo");
                return taskInfo.getId();
            }
        } catch (Exception ex) {

        }
        return null;
    }

    @Override
    public List<Integer> updateVedengMember(List<Integer> traderIds){
        if(CollectionUtils.isEmpty(traderIds)){
            return new ArrayList<>();
        }
        List<Integer> webAccountList = webAccountMapper.getWebAccountIsMemberForAssign(traderIds);
        if(CollectionUtils.isNotEmpty(webAccountList)){
            for(Integer acc:webAccountList){
                List<Integer> temp = new ArrayList<>();
                temp.add(acc);
                webAccountMapper.updateVedengMember(temp);
                webAccountMapper.updateVedengMemberTime(temp);
            }
        }

        //判断已经为会员的现在是否满足条件
        removeVedengMember(traderIds);
        //更新客户是否是会员
        addCustomerMember(traderIds);
        return webAccountList;
    }

    @Override
    public List<Integer> updateVedengMember() {
        logger.info("更新贝登会员信息开始");
        //全部满足则为贝登会员
        //（1）注册账号归属平台为“贝登医疗”
        //（2）注册账号在ERP中已关联公司
        //（3）关联公司的客户性质为“分销”
        //（4）关联公司的“客户资质审核”为“审核通过”（注意是“资质审核状态”，而不是“公司审核状态”）
        //获取符合变更为贝登会员的用户

        //VDERP-6407客户资质审核流程变更  关联联公司的“资质审核状态”为“审核通过”或“公章审核中”或“复审中”
        List<Integer> webAccountList = new ArrayList<>();
        try {
            webAccountList = webAccountMapper.getWebAccountIsMember();
            if (CollectionUtils.isNotEmpty(webAccountList)) {
                if (webAccountList.size() > 5000) {
                    logger.error("数据量过大，该处理了。updateVedengMember");
                }
                //死锁了，改成一条条执行
                webAccountList.forEach(acc -> {
                    List<Integer> temp = new ArrayList<>();
                    temp.add(acc);
                    webAccountMapper.updateVedengMember(temp);
                    webAccountMapper.updateVedengMemberTime(temp);
                });
            }
            //判断已经为会员的现在是否满足条件
            removeVedengMember();
            //更新客户是否是会员
            addCustomerMember();
        } catch (Exception e) {
            logger.error("updateVedengMember  error", e);
        }
        logger.info("更新贝登会员信息结束");
        return webAccountList;
    }

    private void addCustomerMember(List<Integer> traderIds) {
        //获取符合条件的客户会员
        List<Integer> customerIdList = traderCustomerMapper.getTraderCustomerIsMemberForAssign(traderIds);
        if (CollectionUtils.isNotEmpty(customerIdList)) {
            customerIdList.forEach(item -> {
                List<Integer> temp = new ArrayList<>();
                temp.add(item);
                traderCustomerMapper.updateIsVedengMember(temp, 1);
            });
            // traderCustomerMapper.updateIsVedengMember(customerIdList,1);
        }
        //将不符合条件的客户置为非会员
        removeCustomerMember(traderIds);

    }

    private void addCustomerMember() {
        //获取符合条件的客户会员
        List<Integer> customerIdList = traderCustomerMapper.getTraderCustomerIsMember();
        if (CollectionUtils.isNotEmpty(customerIdList)) {
            customerIdList.forEach(item -> {
                List<Integer> temp = new ArrayList<>();
                temp.add(item);
                traderCustomerMapper.updateIsVedengMember(temp, 1);
            });
            // traderCustomerMapper.updateIsVedengMember(customerIdList,1);
        }
        //将不符合条件的客户置为非会员
        removeCustomerMember();

    }

    private void removeCustomerMember(List<Integer> traderIds) {
        //获取是贝登会员的客户信息
        List<WebAccount> list = webAccountMapper.getTraderCustomerMemberInfoForAssign(traderIds);
        // List<Integer> noMemberidList = getnoMemberIds(list);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> {
                List<Integer> temp = new ArrayList<>();
                temp.add(item.getErpAccountId());
                traderCustomerMapper.updateIsVedengMember(temp, 0);
            });
            //traderCustomerMapper.updateIsVedengMember(noMemberidList,0);
        }
    }

    private void removeCustomerMember() {
        //获取是贝登会员的客户信息
        List<WebAccount> list = webAccountMapper.getTraderCustomerMemberInfo();
        // List<Integer> noMemberidList = getnoMemberIds(list);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> {
                List<Integer> temp = new ArrayList<>();
                temp.add(item.getErpAccountId());
                traderCustomerMapper.updateIsVedengMember(temp, 0);
            });
            //traderCustomerMapper.updateIsVedengMember(noMemberidList,0);
        }
    }

    private List<Integer> getnoMemberIds(List<WebAccount> list) {
        List<Integer> noMemberidList = new ArrayList<>();
        for (WebAccount webAccount : list) {
            if (isVedengMember(webAccount)) {
                continue;
            }
            noMemberidList.add(webAccount.getErpAccountId());
        }
        return noMemberidList;
    }

    /**
     * 从账号维度，去除贝登客户
     */
    private void removeVedengMember() {
        //获取是贝登会员的用户信息
        List<WebAccount> list = webAccountMapper.getWebAccountMemberInfo();
        //判断哪些用户不符合条件
        // List<Integer> noMemberidList = getnoMemberIds(list);
        if (CollectionUtils.isNotEmpty(list)) {

            list.forEach(item -> {
                List<Integer> temp = new ArrayList<>();
                temp.add(item.getErpAccountId());
                webAccountMapper.updateVedengNoMember(temp);
            });
            //  Integer num = webAccountMapper.updateVedengNoMember(noMemberidList);
        }
    }
    private void removeVedengMember(List<Integer> traderIds) {
        //获取是贝登会员的用户信息
        List<WebAccount> list = webAccountMapper.getWebAccountMemberInfoForAssign(traderIds);
        //判断哪些用户不符合条件
        // List<Integer> noMemberidList = getnoMemberIds(list);
        if (CollectionUtils.isNotEmpty(list)) {

            list.forEach(item -> {
                List<Integer> temp = new ArrayList<>();
                temp.add(item.getErpAccountId());
                webAccountMapper.updateVedengNoMember(temp);
            });
            //  Integer num = webAccountMapper.updateVedengNoMember(noMemberidList);
        }
    }

    private boolean isVedengMember(WebAccount webAccount) {
        if (webAccount.getBelongPlatform() == null || !webAccount.getBelongPlatform().equals(1)) {
            return false;
        }
        if (!webAccount.getCustomerNature().equals(465)) {
            return false;
        }
        if (!webAccount.getStatus().equals(1) && !webAccount.getStatus().equals(0) && !webAccount.getStatus().equals(5)) {
            return false;
        }
        if (webAccount.getTraderId() == null || webAccount.getTraderId().equals(0)) {
            return false;
        }
        return true;
    }

    @Override
    public VerifiesInfo getCustomerAptitudeVerifiesInfo(Integer traderCustomerId) {
        VerifiesInfo verifiesInfo = new VerifiesInfo();
        verifiesInfo.setRelateTable("T_CUSTOMER_APTITUDE");
        verifiesInfo.setRelateTableKey(traderCustomerId);
        List<VerifiesInfo> verifiesInfos = verifiesRecordService.getVerifiesList(verifiesInfo);
        if (!org.springframework.util.CollectionUtils.isEmpty(verifiesInfos)) {
            verifiesInfo = verifiesInfos.get(0);
            if (verifiesInfo.getVerifiesInfoId() > 0) {
                return verifiesInfo;
            }
        }
        return null;
    }

    @Override
    public TraderCustomer getSimpleCustomer(Integer traderId) {
        return traderCustomerMapper.getBaseCustomerByTraderId(traderId);
    }

    @Override
    public ResultInfo syncYxgCustomerStatus(YXGTraderAptitude statusInfo) {
        if (statusInfo == null || statusInfo.getTraderId() == null
                || statusInfo.getStatus() == null) {
            return new ResultInfo(-1, "操作失败，客户标识或审核状态为空");
        }
        Trader trader = new Trader();
        trader.setTraderId(statusInfo.getTraderId());
        trader.setPayofStatus(statusInfo.getStatus());
        trader.setPayofCheckMsg(statusInfo.getMsg() == null ? "" : statusInfo.getMsg());
        traderMapper.updatePartBySelective(trader);
        return ResultInfo.success();
    }

    @Override
    public void updateYxgTraderStatusComplete(Integer traderId){
        Trader trader = new Trader();
        trader.setTraderId(traderId);
        trader.setTraderStatus(2);
        traderMapper.updatePartBySelective(trader);
    }

    @Override
    public ResultInfo syncYxgTraderStatus(YXGTraderAptitude aptitudeStatus) {

        TraderCustomer traderCustomer = traderCustomerMapper.getBaseCustomerByTraderId(aptitudeStatus.getTraderId());
        if (traderCustomer == null || traderCustomer.getTraderCustomerId() == null) {
            return new ResultInfo(-1, "该客户不存在");
        }
        Trader trader = new Trader();
        trader.setTraderId(aptitudeStatus.getTraderId());
        trader.setTraderCheckMsg(aptitudeStatus.getMsg() == null ? "" : aptitudeStatus.getMsg());
        int traderStatus = aptitudeStatus.getStatus().intValue();
        trader.setTraderStatus(++traderStatus);
        traderMapper.updatePartBySelective(trader);
        VerifiesInfo verifiesInfo = getCustomerAptitudeVerifiesInfo(traderCustomer.getTraderCustomerId());
        if (verifiesInfo == null || verifiesInfo.getVerifiesInfoId() == null) {
            verifiesRecordService.saveVerifiesInfoDirect(initAptitudeVerifiesInfo(null,
                    traderCustomer.getTraderCustomerId(), aptitudeStatus.getStatus()));
        } else {
            verifiesRecordService.saveVerifiesInfoDirect(initAptitudeVerifiesInfo(verifiesInfo.getVerifiesInfoId(),
                    traderCustomer.getTraderCustomerId(), aptitudeStatus.getStatus()));
        }
        //客户风控
        riskCheckService.checkTraderTodo(aptitudeStatus.getTraderId());
        return new ResultInfo(0, "操作成功");
    }

    /**
     * <b>Description:</b>初始化客户资质审核记录信息<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/3/23
     */
    private VerifiesInfo initAptitudeVerifiesInfo(Integer verifiesId, Integer traderCustomerId, Integer status) {
        VerifiesInfo verifiesInfo = new VerifiesInfo();
        verifiesInfo.setRelateTableKey(traderCustomerId);
        verifiesInfo.setVerifiesInfoId(verifiesId);
        verifiesInfo.setRelateTable("T_CUSTOMER_APTITUDE");
        verifiesInfo.setStatus(status);
        long time = System.currentTimeMillis();
        verifiesInfo.setAddTime(time);
        verifiesInfo.setModTime(time);
        verifiesInfo.setVerifyUsername("医械购");
        return verifiesInfo;
    }

    @Override
    public TraderCustomer getTraderByPayApply(Integer payApplyId) {
        return traderCustomerMapper.getTraderCustomerByPayApply(payApplyId);
    }


    @Override
    public void updateTraderAmount(Integer traderId, BigDecimal amount) {
        TraderCustomer tc = new TraderCustomer();
        tc.setTraderId(traderId);
        tc.setAmount(amount);
        tc.setModTime(DateUtil.sysTimeMillis());
        traderCustomerMapper.updateTraderCustomerAmount(tc);
    }

    @Override
    public TraderContactGenerate getTraderContactByTraderContactId(Integer takeTraderContactId) {
        return traderContactGenerateMapper.selectByPrimaryKey(takeTraderContactId);
    }

    @Override
    public TraderAddress getTraderAddressById(Integer takeTraderAddressId) {
        return traderAddressMapper.getAddressInfoById(takeTraderAddressId, 1);
    }

    @Override
    public Trader getTraderByTraderId(Integer traderId) {
        return traderMapper.getTraderByTraderId(traderId);
    }

    @Override
    public List<TraderCustomerVo> getLimitPriceTraderName(String traderName) {
        return traderMapper.getLimitPriceTraderName(traderName);
    }

    @Override
    public void batchUpdateListPrice(List<TraderCustomerVo> traders) throws Exception {

        if (CollectionUtils.isEmpty(traders)) {
            throw new Exception("客户列表不能为空");
        }

        traderCustomerMapper.updatePriceLimtList(traders);

    }

    @Override
    public ResultInfo<?> delPriceLimitTrader(TraderCustomerVo traderCustomerVo) {
        int i = traderCustomerMapper.delPriceLimitTrader(traderCustomerVo);
        if (i > 0) {
            return new ResultInfo(0, "操作成功");
        }
        return new ResultInfo();
    }


    @Override
    public List<TraderCustomerVo> setIsBelong(List<TraderCustomerVo> searchCustomerList, User user) {
        List<TraderCustomerVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(searchCustomerList) || user == null || user.getUserId() == null) {
            return result;
        }
        Trader trader = new Trader();
        for (TraderCustomerVo traderCustomerVo : searchCustomerList) {
            traderCustomerVo.setIsBelong(0);
            trader.setTraderId(traderCustomerVo.getTraderId());
            User user1 = userMapper.getNewUserByTraderId(trader);
            if (user1 != null && user.getUserId().equals(user1.getUserId())) {
                traderCustomerVo.setIsBelong(1);
                result.add(traderCustomerVo);
            }
        }
        return result;
    }

    @Override
    public Map<String, Object> searchNewCustomerPageList(TraderCustomerVo traderCustomerVo, Page page) {
        List<TraderCustomerVo> searchCustomerList = null;
        Map<String, Object> map = new HashMap<>();
        try {
            map.put("page", page);
            map.put("traderCustomerVo", traderCustomerVo);
            searchCustomerList = traderCustomerMapper.searchCustomerListPage(map);
            setCustomerSomeInfo(searchCustomerList);
            map.put("searchCustomerList", searchCustomerList);
            map.put("page", page);
        } catch (Exception e) {
            return map;
        }
        return map;
    }

    /**
     * 设置客户地区和归属销售信息
     *
     * @param searchCustomerList
     */
    private void setCustomerSomeInfo(List<TraderCustomerVo> searchCustomerList) {
        if (!searchCustomerList.isEmpty()) {
            for (int i = 0; i < searchCustomerList.size(); i++) {
                if (searchCustomerList.get(i) != null) {
                    if (searchCustomerList.get(i).getAreaId() != null && searchCustomerList.get(i).getAreaId() != 0) {
                        searchCustomerList.get(i).setAddress(regionService.getRegion(searchCustomerList.get(i).getAreaId(), 2).toString());
                    }
                    if (searchCustomerList.get(i).getTraderId() != null) {
                        searchCustomerList.get(i).setPersonal(userMapper.getUserNameByTraderId(searchCustomerList.get(i).getTraderId()));
                        Trader traderSearch = new Trader();
                        traderSearch.setTraderId(searchCustomerList.get(i).getTraderId());
                        try {
                            User belongUserOfTrader = userMapper.getNewUserByTraderId(traderSearch);
                            if (belongUserOfTrader != null) {
                                searchCustomerList.get(i).setPersonalId(belongUserOfTrader.getUserId());
                            }
                        } catch (Exception e) {
                            logger.error("setCustomerSomeInfo，traderId：{},错误信息：", searchCustomerList.get(i).getTraderId(), e);
                        }
                    }
                }
            }
        }
    }

    @Override
    public Trader getTraderByCustomerId(Integer traderCustomerId) {
        return traderCustomerMapper.getTraderByTraderCustomerId(traderCustomerId);
    }

    @Override
    public ResultInfo addYxgTraderByName(TraderAccountInfo traderInfoVo) {
        String url = httpUrl + ErpConst.YXG_ADD_TRADER_URL;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo> TypeRef2 = new TypeReference<ResultInfo>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderInfoVo, clientId, clientKey, TypeRef2);
            if (result == null) {
                result = new ResultInfo<>(-1, "操作失败");
            }
            if (result.getCode() == 0 && result.getData() != null) {
                WebAccount webAccount = new WebAccount();
                webAccount.setSsoAccountId(traderInfoVo.getSsoAccountId());
                webAccount = webAccountMapper.getWebAccontAuthous(webAccount);
                WebAccountVo webAccountVo = new WebAccountVo();
                webAccountVo.setTraderId(webAccount.getTraderId());
                webAccountVo.setErpAccountId(webAccount.getErpAccountId());
                sendMsgIfFirstChangeTrader(webAccountVo, false);

                addYxgTraderTrack(result);
            }

            return result;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return new ResultInfo(1100, "操作失败");
        }
    }

    private void addYxgTraderTrack(ResultInfo<?> result) {
        try {
            //VDERP-17057  【客户档案】ERP客户档案时间轴 医械购客户新增
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            trackParams.put("traderId", ((LinkedHashMap) result.getData()).get("traderId"));
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_NEW_CUSTOMER_FROM_FRONT);
            trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_NEW_CUSTOMER_FROM_FRONT);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackStrategy.track(trackParamsData);
        } catch (Exception e) {
            logger.error("埋点：{}，失败，不影响正常业务", EventTrackingEnum.BASE_INFO_NEW_CUSTOMER_FROM_FRONT.getArchivedName(), e);
        }
    }


    public void sendMsgIfFirstChangeTrader(WebAccountVo webAccountVo, boolean isErpLink) {
        if (webAccountVo.getTraderId() == null || webAccountVo.getTraderId() == 0) {
            return;
        }
        WebAccount webAccount = webAccountMapper.getWebAccontAuthous(webAccountVo);
        int firstLink = 1;
        if (isErpLink && webAccount.getTraderId() != null
                && webAccount.getTraderId().equals(webAccountVo.getTraderId())) {
            return;
        }
        if (isErpLink) {
            if (webAccount.getTraderId() != null && webAccount.getTraderId() > 0) {
                firstLink = 0;
            }
        }
        Integer hasInvited = invitationLogMapper.countInvited(webAccount.getMobile());
        if (hasInvited == null || hasInvited == 0) {
            return;
        }
        JSONObject json = new JSONObject();
        Integer status;
        Long addTime = 0L;
        Trader trader = traderMapper.getTraderByTraderId(webAccountVo.getTraderId());
        TraderCustomer traderCustomer = traderCustomerMapper.getBaseCustomerByTraderId(webAccountVo.getTraderId());
        VerifiesInfo verifiesInfo = getCustomerAptitudeVerifiesInfo(traderCustomer.getTraderCustomerId());
        if (verifiesInfo == null) {
            status = -1;
        } else {
            status = verifiesInfo.getStatus();
            if (status == 1) {
                addTime = verifiesInfo.getModTime();
            }
        }
        json.put("traderId", trader.getTraderId());
        json.put("traderName", trader.getTraderName());
        json.put("customerNature", traderCustomer.getCustomerNature());
        json.put("status", status);
        json.put("checkPassTime", addTime);
        json.put("belongPlatform", trader.getBelongPlatform());
        json.put("firstLink", firstLink);
        json.put("mobile", webAccount.getMobile());

        // 判断客户是否已经提交过资质审核，已经提交待迁移、或者已经发起审核
        boolean hasPostCertification = verifiesInfo != null;
        if (!hasPostCertification) {
            hasPostCertification = certificateMapper.getCountByWebAccountId(webAccount.getWebAccountId()) > 0;
        }
        json.put("hasPostCertification", hasPostCertification);

        Timer time = new Timer();
        time.schedule(new TimerTask() {
            @Override
            public void run() {
                opMsgProducer.sendMsg(RabbitConfig.TRADER_LINK_ACCOUNT_EXCHANGE, null, json.toString());
            }
        }, 5000);

    }

    @Override
    public List<TraderAddressVo> getTraderAddressList(Integer traderId) {
        List<TraderAddress> traderAddressVos = traderAddressMapper.getTraderAddressList(traderId);
        if (CollectionUtils.isNotEmpty(traderAddressVos)) {
            List<TraderAddressVo> addressVos = new ArrayList<>();
            traderAddressVos.stream().forEach(item -> {
                TraderAddressVo addressVo = new TraderAddressVo();
                addressVo.setTraderAddress(item);
                if (item.getAreaId() > 0) {
                    addressVo.setArea(getAddressByAreaId(item.getAreaId()));
                }
                addressVos.add(addressVo);
            });
            return addressVos;
        }
        return new ArrayList<>();
    }

    @Override
    public List<TraderContactVo> getTraderContactList(Integer traderId) {
        return traderContactGenerateMapper.getTraderContactList(traderId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer isAddresstStick(TraderAddress traderAddress) {
        return traderAddressMapper.updateAddressStick(traderAddress);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer isContactStick(TraderContact traderContact) {
        return traderContactGenerateMapper.updateContactStick(traderContact);
    }

    @Override
    public Integer getTraderIdByTraderCustomId(Integer traderCustomerId) {

        Trader traderByTraderCustomerId = traderCustomerMapper.getTraderByTraderCustomerId(traderCustomerId);
        return traderByTraderCustomerId.getTraderId();
    }

    @Override
    public void saveAptitudeHistory(TraderVo traderVo, List<TraderCertificateVo> tcvList) {
        if (traderVo.getTraderId() != null) {
            TraderCertificateVo traderCertificateVo = new TraderCertificateVo();
            traderCertificateVo.setTraderId(traderVo.getTraderId());
            List<Integer> sysOptionDefinitionIds = Stream.of(SysOptionConstant.ID_25, SysOptionConstant.ID_28, SysOptionConstant.ID_29, SysOptionConstant.ID_678,
                    SysOptionConstant.ID_439, SysOptionConstant.ID_1101, SysOptionConstant.ID_1102, SysOptionConstant.ID_896, SysOptionConstant.ID_897,
                    SysOptionConstant.ID_898, SysOptionConstant.ID_1100, SysOptionConstant.ID_3900).collect(Collectors.toList());
            Map<Integer, TraderCertificateVo> sysMap = null;
            if (CollectionUtils.isNotEmpty(tcvList)) {
                sysMap = tcvList.stream().collect(Collectors.toMap(TraderCertificateVo::getSysOptionDefinitionId, item -> item));
            }
            for (Integer sysOptionDefinitionId : sysOptionDefinitionIds) {
                if (sysMap != null && sysMap.containsKey(sysOptionDefinitionId)) {
                    //保存的资质信息在数据库中有对应的数据
                    TraderCertificateVo certificateVo = sysMap.get(sysOptionDefinitionId);
                    traderCertificateVo.setSysOptionDefinitionId(sysOptionDefinitionId);
                    traderCertificateVo.setTraderType(traderVo.getTraderType());
                    List<TraderCertificateVo> traderCertificateVoList = traderCertificateMapper.getTraderCertificateVoListByIdAndType(traderCertificateVo);
                    dealWithAptitudeList(certificateVo, traderCertificateVoList);
                } else {
                    //前台保存的资质信息在数据库中没有对应的数据
                    traderCertificateVo.setSysOptionDefinitionId(sysOptionDefinitionId);
                    traderCertificateVo.setTraderType(traderVo.getTraderType());
                    List<TraderCertificateVo> traderCertificateVoList = traderCertificateMapper.getTraderCertificateVoListByIdAndType(traderCertificateVo);
                    if (CollectionUtils.isNotEmpty(traderCertificateVoList)) {
                        traderCertificateVoList.stream().forEach(item -> {
                            TraderCertificateHistory traderCertificateHistory = new TraderCertificateHistory();
                            BeanUtils.copyProperties(item, traderCertificateHistory);
                            traderCertificateHistoryMapper.insertSelective(traderCertificateHistory);
                        });
                    }
                }
            }
        }
    }

    @Override
    public TraderBidingData getBidingDataOfTraderCustomer(Integer traderCustomerId) {
        List<TraderCustomerBidingInfo> bidingInfos = traderCustomerBidingInfoMapper.getBidingInfoByCustomerId(traderCustomerId);
        TraderBidingData bidingData = new TraderBidingData();
        if (bidingInfos.size() > 0) {
            Map<Integer, List<TraderCustomerBidingInfo>> infoMap =
                    bidingInfos.stream().collect(Collectors.groupingBy(TraderCustomerBidingInfo::getType,
                            Collectors.toList()));
            infoMap.keySet().parallelStream().forEach(
                    key -> {
                        String bidingDataStr = infoMap.get(key).stream().map(TraderCustomerBidingInfo::getBidingData).collect(Collectors.joining(
                                "、"));
                        switch (key) {
                            case 1:
                                bidingData.setBidingBrand(bidingDataStr);
                                break;
                            case 2:
                                bidingData.setBidingGoods(bidingDataStr);
                                break;
                            case 3:
                                bidingData.setBidingUnit(bidingDataStr);
                                break;
                            case 4:
                                bidingData.setBidingSource(bidingDataStr);
                                break;
                            default:
                                break;
                        }
                    }
            );
        }
        return bidingData;
    }

    private void dealWithAptitudeList(TraderCertificateVo certificateVo, List<TraderCertificateVo> traderCertificateVoList) {
        List<TraderCertificateVo> list = new ArrayList<>();
        list.add(certificateVo);
        if (certificateVo.getUris() != null && certificateVo.getUris().length > 0) {
            for (int i = 0; i < certificateVo.getUris().length; i++) {
                TraderCertificateVo traderCertificateVo = new TraderCertificateVo();
                BeanUtils.copyProperties(certificateVo, traderCertificateVo);
                traderCertificateVo.setUri(certificateVo.getUris()[i]);
                list.add(traderCertificateVo);
            }
        }
        List<TraderCertificateVo> diff = diffAptitudeList(list, traderCertificateVoList);
        if (CollectionUtils.isNotEmpty(diff)) {
            diff.stream().forEach(item -> {
                TraderCertificateHistory traderCertificateHistory = new TraderCertificateHistory();
                BeanUtils.copyProperties(item, traderCertificateHistory);
                traderCertificateHistoryMapper.insertSelective(traderCertificateHistory);
            });
        }
    }

    private List<TraderCertificateVo> diffAptitudeList(List<TraderCertificateVo> certificateVoList, List<TraderCertificateVo> traderCertificateVoList) {
        List<TraderCertificateVo> diff = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(traderCertificateVoList)) {
            traderCertificateVoList.forEach(
                    item -> {
                        boolean exist = false;
                        for (TraderCertificateVo traderCertificateVo : certificateVoList) {
                            if (item.getUri() != null && item.getUri().equals(traderCertificateVo.getUri())) {
                                exist = true;
                                break;
                            }
                        }
                        if (!exist) {
                            diff.add(item);
                        }
                    }
            );
        }

        return diff;
    }

    @Override
    public Boolean checkIsContainHistoryAptitude(TraderCertificateHistory traderCertificate) {
        return traderCertificateHistoryMapper.getTraderCertificateHistoryList(traderCertificate).size() > 0;
    }

    @Override
    public List<TraderCertificateHistory> getTraderCertificateHistoryListPage(TraderCertificateHistory traderCertificate, Page page) {
        Map<String, Object> map = new HashMap<>();
        map.put("traderCertificate", traderCertificate);
        map.put("page", page);
        return traderCertificateHistoryMapper.getTraderCertificateHistoryListPage(map);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = true)
    public ResultInfo checkBillPeriodForOrder(Saleorder saleorder, Integer traderCustomerId) {
        ResultInfo result = new ResultInfo<>(0, "操作成功");

        if (Objects.isNull(saleorder) || Objects.isNull(saleorder.getSaleorderId()) || Objects.isNull(traderCustomerId) || Objects.isNull(saleorder.getTraderId())) {
            result.setMessage("校验账期失败, 参数为空 ...");
            result.setCode(-1);
            return result;
        }

        if (PaymentTypeEnum.PAY_BEFORE.getType().equals(saleorder.getPaymentType()) ||
                saleorder.getAccountPeriodAmount() == null
                || saleorder.getAccountPeriodAmount().doubleValue() == 0.00 ||
                !ErpConst.ONE.equals(saleorder.getHaveAccountPeriod()) || saleorder.getAccountPeriodAmount().intValue() <= 0) {
            return result;
        }

        // 调用账期服务获取余额  防止用户操作时间过长 重新获取
        TraderCustomerVo customer = new TraderCustomerVo();
        customer.setTraderCustomerId(traderCustomerId);
        setAccountPeriodInfo(customer, saleorder.getSaleorderId().longValue(), saleorder.getCompanyId());

        if (saleorder.getAccountPeriodAmount().compareTo(customer.getAccountPeriodLeft()) > 0) {
            result.setMessage("当前客户账期额度不足，请至客户详情/财务与资质信息中申请");
            result.setCode(-3);

            // 补充字段
            if (CollectionUtils.isNotEmpty(customer.getAllBillPeriod())) {
                List<BillPeriodItem> allBillPeriod = customer.getAllBillPeriod();
                for (BillPeriodItem item : allBillPeriod) {
                    if (Objects.isNull(item.getBillPeriodSettlementType())) {
                        item.setBillPeriodSettlementType(saleorder.getBillPeriodSettlementType());
                    }
                }
            }

            // 展示当前客户账期简要信息 【账期类型，可用额度，结算标准，结算周期（天）】
            CustomerBillPeriodCheckVo tempVo = new CustomerBillPeriodCheckVo();
            tempVo.setAllBillPeriod(customer.getAllBillPeriod());
            tempVo.setTraderId(saleorder.getTraderId());
            tempVo.setTraderCustomerId(traderCustomerId);
            result.setData(tempVo);
        }

        return result;
    }

    @Override
    public void setAccountPeriodInfo(TraderCustomerVo customer, Long saleOrderId, Integer companyId) {
        Objects.requireNonNull(customer, "查询当前客户账期额度失败, customer为空");
        Objects.requireNonNull(customer.getTraderCustomerId(), "查询当前客户账期额度失败, traderCustomerId为空");
        if (companyId == null) {
            companyId = 1;
        }

        CustomerBillPeriodAvailableAmountQueryDto queryParam = new CustomerBillPeriodAvailableAmountQueryDto();
        queryParam.setCompanyId(companyId);
        queryParam.setOrderId(saleOrderId);
        queryParam.setCustomerId(customer.getTraderCustomerId().longValue());

        CustomerBillPeriodAvailableAmountDto result = customerBillPeriodService.getCustomerBillPeriodAvailableAmount(queryParam);

        if (Objects.isNull(result) || Objects.isNull(result.getTotalAvailableAmount())) {
            customer.setAccountPeriodLeft(new BigDecimal("0.00"));
            return;
        }

        customer.setAccountPeriodLeft(result.getTotalAvailableAmount());
        customer.setAllBillPeriod(result.getAllBillPeriod());
        // customer.setPeriodAmount(); // TODO 26_V02 总额度 ???
    }


    @Override
    public BigDecimal getRealTotalAmount(List<Long> orderIdLists) {
        BigDecimal realAmount = new BigDecimal(0);
        for (Long order : orderIdLists) {
            Integer orderId = order.intValue();
            Saleorder saleorder = saleorderMapper.getSaleorderBySaleorderId(orderId);
            if (null != saleorder && null != saleorder.getRealTotalAmount()) {
                realAmount = realAmount.add(saleorder.getRealTotalAmount());
            }

        }
        return realAmount;
    }

    @Override
    public Trader selectTraderNameByTraderId(Integer traderId) {
        if (ObjectUtils.notEmpty(traderId)) {
            return traderMapper.selectByPrimaryKey(traderId);
        }
        return null;
    }

    @Override
    public CapitalBill selectBillInfo(Integer relatedId) {
        return capitalBillMapper.selectByPrimaryKey(relatedId);
    }

    @Override
    public AfterSales selectAfterInfo(Integer relatedId) {
        return afterSalesMapper.selectByPrimaryKey(relatedId);
    }

    @Override
    public Express selectAmountByExpressId(Express express) {
        BigDecimal bigDecimal = expressMapper.getGoodsAmountByExpressId(express.getExpressId());
        List<Express> expressList = expressMapper.selectAmountByExpressId(express);
        Express expressInfo = new Express();
        if (null != expressList && expressList.size() > 0) {
            expressInfo.setAmount(bigDecimal);
            expressInfo.setLogisticsNo(expressList.get(0).getLogisticsNo());
        }
        return expressInfo;
    }

    @Override
    public Invoice selectInvoice(Integer relatedId) {
        return invoiceMapper.selectAmountByPrimaryKey(relatedId);
    }

    @Override
    public List<TraderCustomer> getCustomerByInfo(TraderAccountPeriodApply tapa) {
        List<TraderCustomer> list = traderCustomerMapper.getCustomerByInfo(tapa);
        return list;
    }

    @Override
    public List<CustomerBillPeriodApplyItemVo> getCustomerInfoByCustomerId(List<CustomerBillPeriodApplyItemDto> dtoList) {
        List<CustomerBillPeriodApplyItemVo> voList = new ArrayList<>();
        dtoList.stream().filter(s -> null != s.getCustomerId() && null != s.getCreator()).forEach(dto -> {
            CustomerBillPeriodApplyItemVo vo = traderCustomerMapper.getCustomerInfoByCustomerId(dto.getCustomerId().intValue());
            //申请人
            User user = userMapper.getUserByUserId(dto.getCreator());
            vo.setUserName(user.getUsername());
            //申请ID
            vo.setBillPeriodApplyId(dto.getBillPeriodApplyId());
            //客户ID
            vo.setCustomerId(dto.getCustomerId());
            //账期原额度
            vo.setBeforeApplyAmount(dto.getBeforeApplyAmount());
            //账期可用额
            vo.setCreditUsableAmount(dto.getCreditUsableAmount());
            //之前的账期有效期截止时间
            vo.setBeforeBillPeriodEnd(dto.getBeforeBillPeriodEnd());
            //账期结算周期
            vo.setSettlementType(dto.getSettlementType());
            //申请日期
            vo.setAddTime(dto.getAddTime());
            //账期类型
            vo.setBillPeriodType(dto.getBillPeriodType());
            //申请类型
            vo.setOperateType(dto.getOperateType());
            //本次额度申请（元）
            vo.setApplyAmount(dto.getApplyAmount());
            /*if (null != dto.getOperateType() && dto.getOperateType() ==2){
                vo.setApplyAmount(dto.getBeforeApplyAmount().subtract(dto.getApplyAmount()));
            }*/
            //账期原额度
            vo.setBeforeApplyAmount(dto.getBeforeApplyAmount());
            //账期可用额
            vo.setCreditUsableAmount(dto.getCreditUsableAmount());
            //逾期次数
            vo.setCountOfOverDue(dto.getCountOfOverDue());
            //逾期金额
            vo.setUnReturnedOverDueAmount(dto.getUnReturnedOverDueAmount());
            //原有效期
            vo.setBeforeBillPeriodEnd(dto.getBeforeBillPeriodEnd());
            //本次结算周期（天）
            vo.setSettlementPeriod(dto.getSettlementPeriod());
            //本次申请有效期
            vo.setBillPeriodEnd(dto.getBillPeriodEnd());
            //审核状态
            vo.setCheckStatus(dto.getCheckStatus());
            voList.add(vo);
        });
        return voList;
    }

    @Override
    public List<User> getCreatorNameByCreator(List<Integer> creator) {
        List<User> creatorName = new ArrayList<>();
        creator.stream().forEach(creatorId -> {
            User user = userMapper.getUserByUserId(creatorId);
            creatorName.add(user);
        });

        return creatorName;
    }

    @Override
    public List<CustomerBillPeriodDetailVo> getTraderCustomerInfoNew(List<CustomerBillPeriodItemDto> customerBillPeriodItemDtoList, AccountPeriod ap) {
        List<CustomerBillPeriodDetailVo> voList = new ArrayList<>();
        customerBillPeriodItemDtoList.stream().forEach(list -> {
            CustomerBillPeriodDetailVo vo = new CustomerBillPeriodDetailVo();
            //账期类型
            vo.setBillPeriodType(list.getBillPeriodType());
            //账期额度
            vo.setCreditTotalAmount(list.getCreditTotalAmount());
            //账期可用额
            vo.setCreditUsableAmount(list.getCreditUsableAmount());
            //账期有效期截止时间
            vo.setBillPeriodEnd(list.getBillPeriodEnd());
            //未还账期金额
            vo.setUnReturnedAmount(list.getUnReturnedAmount());
            //逾期金额
            vo.setUnReturnedOverDueAmount(list.getUnReturnedOverDueAmount());
            //逾期次数
            vo.setCountOfOverDue(list.getCountOfOverDue());
            //逾期天数
            vo.setDaysOfOverdue(list.getDaysOfOverdue());
            //已归还账期额
            vo.setReturnedAmount(list.getReturnedAmount());
            //未监管账期额
            vo.setUnSuperViseAmount(list.getUnSuperViseAmount());
            //逾期状态
            vo.setOverdueState(list.getOverdueState());
            Integer relationId = list.getOrderId().intValue();
            //订单 orderId
            vo.setOrderId(list.getOrderId());
            //billPeriodId
            vo.setBillPeriodId(list.getBillPeriodId());
            //客户账期使用明细ID
            vo.setBillPeriodUseDetailId(list.getBillPeriodUseDetailId());
            //账期使用额度
            vo.setPeriodAmountUsed(list.getCreditUseAmount());
            //账期占用额
            vo.setPeriodAmountOccupy(list.getOccupyAmount());
            //冻结金额
            vo.setPeriodAmountFreeze(list.getFreezeAmount());
            //全订单总账期使用额
            if (null == ap.getAccountPeriodAmount()) {
                ap.setAccountPeriodAmount(new BigDecimal(0));
            }
            ap.setAccountPeriodAmount(vo.getPeriodAmountUsed().add(ap.getAccountPeriodAmount()));

            //订单信息
            Saleorder saleOrders = selectBySaleOrderId(relationId);
            if (null != saleOrders) {
                //是否直发
                vo.setDeliveryDirect(saleOrders.getDeliveryDirect());
                //客户名称
                vo.setTraderName(saleOrders.getTraderName());
                vo.setTraderId(saleOrders.getTraderId());
                //orgId
                vo.setOrgId(saleOrders.getOrgId());
                vo.setOrgName(saleOrders.getOrgName());
                //订单号
                vo.setSaleorderNo(saleOrders.getSaleorderNo());
                //订单生效日期
                vo.setValidTime(saleOrders.getValidTime());
                //订单原总金额
                vo.setTotalAmount(saleOrders.getTotalAmount());
                //全订单总额
                if (null == ap.getTotalAmount()) {
                    ap.setTotalAmount(new BigDecimal(0));
                }
                ap.setTotalAmount(saleOrders.getTotalAmount().add(ap.getTotalAmount()));
                //订单实际总金额
                vo.setRealTotalAmount(saleOrders.getRealTotalAmount());
                //质保金
                vo.setRetentionMoney(saleOrders.getRetentionMoney());
                if (ObjectUtils.notEmpty(list.getBillPeriodType()) && !ErpConst.ONE.equals(list.getBillPeriodType())) {
                    if (null != saleOrders.getSaleorderId()) {
                        List<Integer> typeList = traderCustomerMapper.getBillTypeByOrderId(saleOrders.getSaleorderId());
                        typeList.stream().filter(s -> ObjectUtils.notEmpty(s)).forEach(s -> {
                            if (ErpConst.ONE.equals(s)) {
                                vo.setBillPeriodTypeFirst(ErpConst.ONE);
                            } else {
                                if (ErpConst.TWO.equals(s) && ErpConst.THREE.equals(list.getBillPeriodType())) {
                                    vo.setBillPeriodTypeFirst(ErpConst.TWO);
                                }
                            }
                        });
                    }
                }
                //结算标准
                vo.setBillPeriodSettlementType(saleOrders.getBillPeriodSettlementType());
            }
            voList.add(vo);
        });
        return voList;
    }

    @Override
    public Saleorder selectBySaleOrderId(Integer relatedId) {
        return saleorderMapper.selectBySaleOrderId(relatedId);
    }

    @Override
    public List<Saleorder> getSaleOrderInfoBySaleOrderInfo(AccountPeriod ap) {
        Saleorder saleorder = new Saleorder();
        //查询条件
        //开始时间-结束时间
        saleorder.setSearchBegintime(ap.getStartDate());
        saleorder.setSearchEndtime(ap.getEndDate());
        //归属销售
        saleorder.setTraderIdList(ap.getTraderIdList());
        if (StringUtils.isNotBlank(ap.getSaleorderNo())) {
            //订单号
            saleorder.setSaleorderNo(ap.getSaleorderNo());
        }
        if (CollectionUtils.isNotEmpty(ap.getDepartmentOrderInfo())) {
            //部门
            saleorder.setOrgIdList(ap.getDepartmentOrderInfo());
        }
        if (ObjectUtils.notEmpty(ap.getTraderName())) {
            //客户名称
            saleorder.setTraderName(ap.getTraderName());
        }
        saleorder.setCompanyId(ap.getCompanyId());
        //如果入参是traderId，转为traderName
        if (ObjectUtils.notEmpty(ap.getTraderId())) {
            Trader trader = traderMapper.selectByPrimaryKey(ap.getTraderId());
            if (StringUtil.isEmpty(trader.getTraderName())) {
                return null;
            }
            ap.setTraderName(trader.getTraderName());
            saleorder.setTraderName(ap.getTraderName());
        }
        saleorder.setUserIdList(ap.getUserIdList());
        return saleorderMapper.getSaleOrderInfoBySaleOrderInfo(saleorder);
    }

    @Override
    public List<TraderCustomerVo> searchTraderCustomerListPage(TraderCustomerVo traderCustomerVo, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("page", page);
        map.put("traderCustomerVo", traderCustomerVo);
        List<TraderCustomerVo> searchCustomerList = traderCustomerMapper.searchTraderCustomerListPage(map);
        setCustomerSomeInfo(searchCustomerList);
        return searchCustomerList;
    }

    @Override
    public List<TraderFinance> getTraderCustomerFinanceListNew(TraderFinance tf) {

        return traderCustomerMapper.getTraderCustomerFinanceList(tf);
    }

    @Override
    public String queryParentTraderName(Integer parentId) {
        Trader traderByTraderId = traderMapper.getTraderByTraderId(parentId);
        return traderByTraderId.getTraderName();
    }


    @Override
    public List<Map<String, Object>> getTraderBusinessCardById(Integer traderContactId) {
        if (traderContactId == null || traderContactId == 0) {
            return new ArrayList<>();
        }
        //获取联系人名片
        TraderCertificate traderCertificate = new TraderCertificate();
        traderCertificate.setRelatedId(traderContactId);
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.BUSINESS_CARDS);
        traderCertificate.setTraderType(ErpConst.ONE);
        List<TraderCertificate> traderCertificates = traderCertificateMapper.getTraderCertificatesById(traderCertificate);
        List<Map<String, Object>> maps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(traderCertificates)) {
            for (TraderCertificate certificate : traderCertificates) {
                Map<String, Object> traderCertificatesMap = new HashMap<>();
                traderCertificatesMap.put("message", "操作成功");
                traderCertificatesMap.put("httpUrl", api_http + certificate.getDomain());
                // uri
                String uri = certificate.getUri();
                if (EmptyUtils.isEmpty(uri)) {
                    continue;
                }
                String[] uriArray = uri.split("/");
                String fileName = uriArray[uriArray.length - 1];
                String fileNameTemp = "/" + fileName;
                // 文件后缀
                String[] prefixArray = fileNameTemp.split("\\.");
                String prefix = prefixArray[prefixArray.length - 1];
                // 去除路径名
                String filePath = uri.replaceAll(fileNameTemp, "");
                traderCertificatesMap.put("traderCertificateId", certificate.getTraderCertificateId());
                traderCertificatesMap.put("traderContactId", traderCertificate.getTraderId());
                traderCertificatesMap.put("fileName", certificate.getName());
                traderCertificatesMap.put("filePath", uri);
                traderCertificatesMap.put("prefix", prefix);
                traderCertificatesMap.put("domain", certificate.getDomain());
                traderCertificatesMap.put("suffix", certificate.getSuffix());
                maps.add(traderCertificatesMap);
            }
        }
        return maps;
    }

    @Override
    public ResultInfo saveBusinessCards(TraderCertificate traderCertificate, User user) {
        TraderCertificate certificate = new TraderCertificate();
        certificate.setRelatedId(traderCertificate.getRelatedId());
        certificate.setSysOptionDefinitionId(SysOptionConstant.BUSINESS_CARDS);
        certificate.setOssResourceId(traderCertificate.getOssResourceId());
        certificate.setDomain(traderCertificate.getDomain());
        certificate.setUri(traderCertificate.getFilePath());
        certificate.setName(traderCertificate.getFileName());
        certificate.setAddTime(System.currentTimeMillis());
        certificate.setCreator(user.getUserId());

        if (traderCertificateMapper.insertSelective(certificate) == 0) {
            logger.info("保存个人名片错误：" + JSON.toJSONString(certificate));
            throw new IllegalStateException("保存个人名片错误");
        }
        return ResultInfo.success("保存个人名片成功", traderCertificate.getTraderCertificateId());
    }

    @Override
    public ResultInfo updateBusinessCards(TraderCertificate traderCertificate, User user) {
        if (traderCertificate.getTraderCertificateId() != null) {
            TraderCertificate certificate = new TraderCertificate();
            certificate.setTraderCertificateId(traderCertificate.getTraderCertificateId());
            certificate.setIsDelete(ErpConst.ONE);
            traderCertificateMapper.updateByPrimaryKeySelective(certificate);
        }
        return ResultInfo.success("更新个人名片成功");
    }

    @Override
    public void synBusinessCars(Integer traderId) {
        if (traderId == null || traderId == 0) {
            return;
        }
        List<WebAccount> webAccounts = webAccountMapper.getTraderContactVoList(traderId);
        if (CollectionUtils.isEmpty(webAccounts)) {
            return;
        }
        for (WebAccount webAccount : webAccounts) {
            if (webAccount.getTraderContactId() == null || webAccount.getTraderContactId() == 0) {
                continue;
            }
            WebAccountCertificate queryCertificate = new WebAccountCertificate();
            queryCertificate.setWebAccountId(webAccount.getErpAccountId());
            queryCertificate.setType(TraderConstants.BUSINESS_CARDS);
            queryCertificate.setStatus(ErpConst.ZERO);
            List<WebAccountCertificate> certificates = certificateMapper.getCertificateList(queryCertificate);

            if (CollectionUtils.isNotEmpty(certificates)) {
                for (WebAccountCertificate certificate : certificates) {
                    TraderCertificate traderCertificate = new TraderCertificate();
                    traderCertificate.setRelatedId(webAccount.getTraderContactId());
                    traderCertificate.setSysOptionDefinitionId(SysOptionConstant.BUSINESS_CARDS);
                    traderCertificate.setOssResourceId(certificate.getOssId());
                    traderCertificate.setDomain(certificate.getDomain());
                    traderCertificate.setUri(certificate.getUri());
                    traderCertificate.setName(TraderConstants.BUSINESS_CARDS_NAME);
                    traderCertificate.setAddTime(System.currentTimeMillis());
                    traderCertificate.setCreator(2);
                    traderCertificateMapper.insertSelective(traderCertificate);
                    certificate.setStatus(ErpConst.ONE);
                    certificate.setUpdateTime(System.currentTimeMillis());
                    certificateMapper.updateByPrimaryKeySelective(certificate);
                }
            }
            TraderCertificate queryTraderCertificate = new TraderCertificate();
            queryTraderCertificate.setRelatedId(webAccount.getTraderContactId());
            queryTraderCertificate.setSysOptionDefinitionId(SysOptionConstant.BUSINESS_CARDS);
            queryTraderCertificate.setTraderType(ErpConst.ONE);
            List<TraderCertificate> newTraderCertificates = traderCertificateMapper.getTraderCertificatesById(queryTraderCertificate);
            int num = newTraderCertificates.size() - 3;
            if (num > 0) {
                List<TraderCertificate> traderCertificates = newTraderCertificates.stream().sorted(Comparator.comparing(TraderCertificate::getAddTime)).collect(Collectors.toList());
                for (int i = 0; i < num; i++) {
                    TraderCertificate certificate = new TraderCertificate();
                    certificate.setTraderCertificateId(traderCertificates.get(i).getTraderCertificateId());
                    certificate.setIsDelete(ErpConst.ONE);
                    traderCertificateMapper.updateByPrimaryKeySelective(certificate);
                }
            }
        }

    }

    @Override
    public void clearRelationBetweenTraderCustomer(Integer traderId) {
        traderCustomerMapper.clearRelationBetweenTraderCustomer(traderId);

    }

    @Override
    public Map<String, Object> getPublicTraderCustomerVoPage(TraderCustomerVo traderCustomerVo, Page page, List<User> userList) {

        if (traderCustomerVo == null || page == null) {
            return Collections.emptyMap();
        }

        List<TraderCustomerVo> traderCustomerVoList = listPublicTraderCustomerVoByPage(traderCustomerVo, page, userList);
        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("list", traderCustomerVoList);
        resultMap.put("page", page);
        return resultMap;
    }

    @Override
    public List<TraderCustomerCategoryDto> getTraderCustomerTypeTree() {
        List<TraderCustomerCategory> traderCustomerCategoryList = traderCustomerCategoryMapper.getByAll();
        List<TraderCustomerCategoryDto> traderCustomerCategoryDtos = new ArrayList<>();
        traderCustomerCategoryList.forEach(t -> traderCustomerCategoryDtos.add(new TraderCustomerCategoryDto().entityToDto(t)));
        return this.topTreeList(traderCustomerCategoryDtos);
    }

    @Override
    public Integer getIsVedengMemberByTraderMobile(String mobile) {
        if (StringUtil.isBlank(mobile)) {
            return -1;
        }
        return traderContactGenerateMapper.getIsVedengMemberByTraderMobile(mobile);
    }

    @Override
    public void setPublicCustomerPrivatizedByAssign(Integer traderId){
        Trader traderInfo =  traderMapper.getTraderInfoByTraderId(traderId);
        if(traderInfo != null && traderInfo.getTraderCustomerId() != null){
            traderMapper.setPublicCustomerPrivatizedByAssignWithTraderId(traderInfo.getTraderCustomerId());
        }

    }

    /**
     * 逻辑复制于 TraderCustomerController:5199
     * @param publicCustomerEvent
     */
    @Override
    public void assignCustomer(PublicCustomerEvent publicCustomerEvent) {
            Integer type = publicCustomerEvent.getType();
            Integer traderId = publicCustomerEvent.getTraderId();
            Integer single_to_user = publicCustomerEvent.getSingle_to_user();
            Integer from_user = publicCustomerEvent.getFrom_user();
            Integer batch_to_user = publicCustomerEvent.getBatch_to_user();
            Integer provinceId = Optional.of(publicCustomerEvent).map(PublicCustomerEvent::getProvinceId).orElse(0);
            Integer cityId =  Optional.of(publicCustomerEvent).map(PublicCustomerEvent::getCityId).orElse(0);
            Integer zoneId = Optional.of(publicCustomerEvent).map(PublicCustomerEvent::getZoneId).orElse(0);
            logger.info("监听公海分配事件，开始处理:{}",JSON.toJSONString(publicCustomerEvent));
            try {
                User session_user = userService.getUserById(CurrentUser.getCurrentUser().getId());
                ResultInfo<?> resultInfo = new ResultInfo<>();

                Boolean succ = false;
                //推送客户Id列表
                List<Integer> traderIdList = Lists.newArrayList();
                Integer salerId = 0;
                String salerName = userService.getUserNameByUserId(batch_to_user);
                if (type.equals(1)) {
                    logger.info("按客户分配划拨");
                    //VDERP-10066 关联客户分配销售，视情况取消关联
//                    try {
//                        RTraderJUser ru = rTraderJUserMapper.getUserByTraderId(traderId);
//                        if(!single_to_user.equals(ru.getUserId())){
//                            this.clearRelationBetweenTraderCustomer(traderId);
//                        }
//                    } catch (Exception e) {
//                        logger.info("分配单个客户时，取消客户关联关系失败");
//                    }
                    succ = this.assignSingleCustomer(traderId, single_to_user, session_user.getCompanyId(),session_user,0);
                    //如果客户在公海列表，在分配客户时，则将该客户置为私有（IS_PRIVATIZED：3）
                    this.setPublicCustomerPrivatizedByAssign(traderId);

                    traderIdList.add(traderId);
                    salerId = single_to_user;
                    try {
                        salerName = userService.getUserNameByUserId(salerId);
                    } catch (Exception e) {
                        logger.error("TraderCustomerController--saveassign--查询用户名异常", e);
                    }
                }

                if (type.equals(2)) {
                    logger.info("按销售分配划拨");
                    Integer areaId = 0;
                    if (zoneId > 0) {
                        areaId = zoneId;
                    } else {
                        if (provinceId > 0) {
                            areaId = provinceId;
                        }
                        if (cityId > 0) {
                            areaId = cityId;
                        }
                    }
                    List<Integer> assignedTraderIdList = this.assignBatchCustomer(session_user, from_user, batch_to_user, areaId);

                    if (assignedTraderIdList == null) {
                        succ = false;
                    } else {
                        succ = true;
                    }

                    //如果客户在公海列表，在分配客户时，则将该客户置为私有（IS_PRIVATIZED：3）
                    logger.info("按销售划拨，名下客户数据：{}",JSON.toJSONString(assignedTraderIdList));
                    traderIdList = assignedTraderIdList;

                }
                if (succ) {
                    resultInfo.setCode(0);
                    resultInfo.setMessage("操作成功，相关数据处理约需等待30分钟。");
                    //客户分配推送到医械购
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("salerId", salerId);
                    map.put("salerName", salerName);
                    map.put("traderIdList", traderIdList);
                    this.putTraderSaleUserIdtoHC(map);
                }
            } catch (Exception e) {
                logger.error("保存分配客户信息异常：", e);
            }
    }

    /**
     * 构建树型
     *
     * @param resultDto 原始数据
     * @return 树形
     */
    private List<TraderCustomerCategoryDto> topTreeList(List<TraderCustomerCategoryDto> resultDto) {
        // 为分类建立键值对
        Map<Integer, Object> mapNodes = new HashMap<>(resultDto.size());
        for (TraderCustomerCategoryDto treeNode : resultDto) {
            mapNodes.put(treeNode.getId(), treeNode);
        }
        // 多叉树
        List<TraderCustomerCategoryDto> topTree = new ArrayList<>();
        for (TraderCustomerCategoryDto treeNode : resultDto) {
            // 添加根节点（顶级分类）
            if (treeNode.getParentId() != null && treeNode.getParentId() == 0) {
                TraderCustomerCategoryDto rootNode = (TraderCustomerCategoryDto) mapNodes.get(treeNode.getId());
                topTree.add(rootNode);
            } else {
                TraderCustomerCategoryDto parentNode = (TraderCustomerCategoryDto) mapNodes.get(treeNode.getParentId());
                if (parentNode != null) {
                    if (parentNode.getChildren() == null) {
                        parentNode.setChildren(new ArrayList<>());
                    }
                    List<TraderCustomerCategoryDto> children = parentNode.getChildren();
                    children.add(treeNode);
                }
            }
        }
        return topTree;
    }

    private List<TraderCustomerVo> listPublicTraderCustomerVoByPage(TraderCustomerVo traderCustomerVo, Page page, List<User> userList) {

        ListCustomerQuery listCustomerQuery = new ListCustomerQuery();
        customerSearchConditionsAssembler.assemble(listCustomerQuery, traderCustomerVo);
        final List<TraderCustomerVo> customerVoList = traderCustomerMapper.getPublicCustomerVolistpage(listCustomerQuery, page);

        if (CollectionUtils.isNotEmpty(customerVoList)) {
            List<Integer> userIdList = Optional.ofNullable(userList).orElse(Collections.emptyList()).stream().map(User::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
            customerExtInfoAggregator.aggregate(customerVoList, userIdList, traderCustomerVo.getAptitudeStatus(), traderCustomerVo.getCustomerStatus());
        }
        return customerVoList;
    }

    @Override
    public Map<Long, Integer> getTraderCountByAssociatedCustomerGroup(List<Long> associatedCustomerGroupList) {
        if (CollectionUtils.isEmpty(associatedCustomerGroupList)) {
            return new HashMap<>(8);
        }
        return traderCustomerMapper.getTraderCountByAssociatedCustomerGroup(associatedCustomerGroupList).stream()
                .collect(Collectors.toMap(AssociatedCustomerGroupCountDto::getAssociatedCustomerGroup, AssociatedCustomerGroupCountDto::getMyCount));
    }

    @Override
    public List<User> getOriginUsers() {
        return traderCustomerMapper.getPublicCustomerRecord();
    }

    @Override
    public TraderBaseInfoDto getTraderBaseInfo(Integer traderId) {
        return traderCustomerMapper.getTraderBaseInfo(traderId);
    }

    /**
     * 是否需要同步信息至集采
     *
     * @param traderByTrader
     * @return
     */
    private boolean needSyncToJC(Trader traderByTrader) {
        boolean need = (traderByTrader.getBelongPlatform() != null && traderByTrader.getBelongPlatform() == BelongPlatformEnum.JC.getBelong()) ||
                //兼容
                (traderByTrader.getTraderType() != null && !"".equals(String.valueOf(traderByTrader.getTraderType())));
        return need;
    }
}
