package com.vedeng.erp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.system.common.enums.UserOrgEnums;
import com.vedeng.erp.system.domain.dto.PositionDto;
import com.vedeng.erp.system.domain.entity.OrganizationEntity;
import com.vedeng.erp.system.dto.OrganizationDto;
import com.vedeng.erp.system.mapper.OrganizationMapper;
import com.vedeng.erp.system.mapper.PositionMapper;
import com.vedeng.erp.system.mapper.UserMapper;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.OrganizationApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.system.vo.UserSmartQuoteVO;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;

import com.vedeng.uac.api.dto.UserInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/13 10:20
 */
@Service
@Slf4j
public class UserApiServiceImpl implements UserApiService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private OrganizationApiService organizationApiService;
    @Autowired
    private PositionMapper positionMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;

    @Value("${davidUserId:50}")
    private Integer davidUserId;

//    @Value("${bossDaquUserId:1,2,3}")
//    private String bossDaquUserId;


    @Override
    public UserDto searchByUserIdFromUac(Integer userId){
        RestfulResult<UserInfoDto> userInfoDtoRestfulResult =  uacWxUserInfoApiService.getUserByUserId(userId);
        if(userInfoDtoRestfulResult.isSuccess() && userInfoDtoRestfulResult.getData() != null){
            UserDto userDto = new UserDto();
            UserInfoDto result = userInfoDtoRestfulResult.getData();
            userDto.setUserId(result.getId());
            userDto.setRealName(result.getRealName());
            userDto.setEmail(result.getEmail());
            userDto.setUsername(result.getAliasName());
            userDto.setNumber(result.getJobNumber());
            userDto.setParentId(result.getParentUserId());
            userDto.setMobile(result.getMobileNo());
            userDto.setAliasHeadPicture(result.getHeadPicture());
            userDto.setEmail(result.getEmail());
            userDto.setParentUserId(result.getParentUserId());
            userDto.setDisplayName(result.getDisplayName());
            //从UAC判断这三个字段是否为正常状态，有任意一个字段不正常即为禁用
            boolean enable = (1== result.getEnable()) && (0== result.getDeleted()) && (0 == result.getWorkingStatus());
            userDto.setIsDisabled(enable?0:1);
            return userDto;
        }
        return null;

    }

    @Override
    public List<UserDto> searchIsParentFromUac(){
        RestfulResult<List<UserInfoDto>> userInfoDtoRestfulResult =  uacWxUserInfoApiService.searchIsParent();
        if(userInfoDtoRestfulResult.isSuccess()){
            List<UserDto> userList = new ArrayList<>();
            userInfoDtoRestfulResult.getData().stream().filter(userInfoDto -> !davidUserId.equals(userInfoDto.getId())).forEach(userInfoDto -> {
                UserDto userDto = new UserDto();
                userDto.setUserId(userInfoDto.getId());
                userDto.setUsername(userInfoDto.getAliasName());
                userDto.setNumber(userInfoDto.getJobNumber());
                userList.add(userDto);
            });
            return userList;
        }
        //TODO jez封装uac查询下属的接口
        return Collections.emptyList();
    }


    @Override
    public  List<Integer>  queryUserIdListSubFromUac(Integer userId){
        RestfulResult<List<UserInfoDto>> userInfoDtoRestfulResult =  uacWxUserInfoApiService.getAllSubUser(userId);
        if(userInfoDtoRestfulResult.isSuccess()){
            List<Integer> userList = new ArrayList<>();
            userInfoDtoRestfulResult.getData().stream().forEach(userInfoDto -> {
//                UserDto userDto = new UserDto();
//                userDto.setUserId(userInfoDto.getId());
//                userDto.setUsername(userInfoDto.getAliasName());
//                userDto.setNumber(userInfoDto.getJobNumber());
                userList.add(userInfoDto.getId());
            });
            return userList;
        }
        //TODO jez封装uac查询下属的接口
        return Collections.emptyList();
    }


    @Override
    public List<UserDto> queryUserSubFromUac(Integer userId) {
        RestfulResult<List<UserInfoDto>> userInfoDtoRestfulResult =  uacWxUserInfoApiService.getAllSubUser(userId);
        if(userInfoDtoRestfulResult.isSuccess()){
            List<UserDto> userList = new ArrayList<>();
            userInfoDtoRestfulResult.getData().stream().forEach(userInfoDto -> {
                UserDto userDto = new UserDto();
                userDto.setUserId(userInfoDto.getId());
                userDto.setUsername(userInfoDto.getAliasName());
                userDto.setNumber(userInfoDto.getJobNumber());
                userDto.setAliasHeadPicture(userInfoDto.getAliasHeadPicture());
                userList.add(userDto);
            });
            return userList;
        }
        //TODO jez封装uac查询下属的接口
        return Collections.emptyList();
    }

    public List<UserDto> getAllUserNotDisabled(String name ){
//        return userMapper.getAllUserNotDisabled(name);  查uac的用户，不取ERP的用户
        RestfulResult<List<UserInfoDto>> restfulResult =  uacWxUserInfoApiService.search(name);
        if(restfulResult.isSuccess()){
            List<UserDto> userList = new ArrayList<>();
            restfulResult.getData().stream().forEach(userInfoDto -> {
                UserDto userDto = new UserDto();
                userDto.setUserId(userInfoDto.getId());
                userDto.setUsername(userInfoDto.getDisplayName());
                userDto.setNumber(userInfoDto.getJobNumber());
                userDto.setAliasHeadPicture(userInfoDto.getAliasHeadPicture());
                userList.add(userDto);
            });
            return userList;
        }
        return Collections.emptyList();

    }

    @Override
    public List<UserDto> getAllSubUserList(UserDto userDto, List<Integer> positionType, boolean haveDisabledUser) {
        // 返回的用户列表
        List<UserDto> userList;

        // 需要检索的目标用户
        UserDto targetUserDto = new UserDto();
        // 非超级管理员
        if (!userDto.getIsAdmin().equals(ErpConstant.NJADMIN_ID)) {
            targetUserDto.setCompanyId(userDto.getCompanyId());
        }

        // 非禁用用户
        if (!haveDisabledUser) {
            targetUserDto.setIsDisabled(0);
        }

        // 目标职位集合
        if (positionType != null && positionType.size() > 0) {
            targetUserDto.setPositionTypes(positionType);
        }

        List<UserDto> userByPositTypes = userMapper.getAllSubUserList(targetUserDto);

        if (positionType != null && positionType.size() > 0) {
            // 非超级管理员
            if (!userDto.getIsAdmin().equals(ErpConstant.NJADMIN_ID)) {
                // 自己当前职位是否在职位类型内
                boolean isInPosit = false;
                for (Integer p : positionType) {
                    if (p.equals(userDto.getPositType())) {
                        isInPosit = true;
                    }
                }
                // 查询自己下面的用户
                if (isInPosit) {
                    List<UserDto> treeUser;
                    JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(userByPositTypes));
                    List<UserDto> selList = new ArrayList<>();

                    JSONArray jsonList = treeMenuList(jsonArray, userDto.getUserId(), "");
                    treeUser = resetList(jsonList, selList, 0);
                    treeUser.add(userDto);

                    // 名称排序
                    if (treeUser.size() > 1) {
                        List<Integer> userIds = new ArrayList<>();
                        for (UserDto u : treeUser) {
                            userIds.add(u.getUserId());
                        }
                        userList = userMapper.getUserInfoByUserIds(userIds);

                    } else {
                        userList = treeUser;
                    }
                } else {
                    userList = userByPositTypes;
                }
            } else {
                userList = userByPositTypes;
            }
        } else {
            userList = userByPositTypes;
        }

        if (userList != null && userList.size() > 0) {
            boolean isE = false;
            for (UserDto u : userList) {
                if (u.getUserId().equals(userDto.getUserId())) {
                    isE = true;
                    break;
                }
            }
            if (!isE) {
                userList.add(userDto);
            }
        } else {
            Objects.requireNonNull(userList).add(userDto);
        }
        return userList;
    }

    @Override
    public List<UserDto> getMyUserListByUserOrgsList(UserDto userDto,List<Integer> positionType,boolean haveDisabeldUser){
        UserDto userDtoNow = getUserById(userDto.getUserId());//查询当前登录用户的信息
        if(StringUtils.isBlank(userDtoNow.getOrgIdsList())){
            return new ArrayList<>();
        }
        List<Integer> orgIds = Arrays.stream(userDtoNow.getOrgIdsList().split(","))
                .map(Integer::parseInt) // 将字符串转换为整数
                .collect(Collectors.toList()); // 收集为列表
        List<OrganizationEntity>  organizationList = organizationMapper.getAllOrganizationOnly();//所有的组织架构集合
        List<OrganizationEntity> sonList =  getOrganizationsWithChildren(orgIds,organizationList);//以上ID的子集及自己
        if(CollectionUtil.isEmpty(sonList)){
            return new ArrayList<>();
        }
        List<Integer> sonIdList = sonList.stream()
                .map(OrganizationEntity::getOrgId)
                .collect(Collectors.toList());//将所有的ID
        if(CollectionUtil.isEmpty(sonIdList)){
            return new ArrayList<>();
        }
        UserDto userQueryVo = new UserDto(  );

        if (!haveDisabeldUser) {// 非禁用用户
            userQueryVo.setIsDisabled(0);
        }
        if (positionType != null && positionType.size() > 0) {// 目标职位集合
            userQueryVo.setPositionTypes(positionType);
        }
        userQueryVo.setOrgIds(sonIdList);
        List<UserDto> sonUserList = userMapper.getUserByPositTypesAndOrgIdList(userQueryVo);
        return sonUserList;
    }
    public List<OrganizationEntity> getOrganizationsWithChildren(List<Integer> orgIdList, List<OrganizationEntity> allOrgs) {
        Map<Integer, OrganizationEntity> orgMap = new HashMap<>();
        Map<Integer, List<OrganizationEntity>> childMap = new HashMap<>();
        List<OrganizationEntity> sonList = new ArrayList<>();

        // 将所有组织放入映射中，便于快速查找
        for (OrganizationEntity org : allOrgs) {
            orgMap.put(org.getOrgId(), org);
            childMap.put(org.getOrgId(), new ArrayList<>()); // 初始化子组织列表
        }

        // 构建每个组织的子组织列表
        for (OrganizationEntity org : allOrgs) {
            if (org.getParentId() != null && childMap.containsKey(org.getParentId())) {
                childMap.get(org.getParentId()).add(org);
            }
        }

        // 根据给定的组织ID列表，递归地收集组织及其所有子组织
        for (Integer id : orgIdList) {
            if (orgMap.containsKey(id)) {
                OrganizationEntity org = orgMap.get(id);
                sonList.add(org);
                addChildrenToList(org, childMap, sonList);
            }
        }

        return sonList;
    }

    // 辅助方法：递归地将组织及其子组织添加到结果列表中
    private void addChildrenToList(OrganizationEntity org, Map<Integer, List<OrganizationEntity>> childMap, List<OrganizationEntity> list) {
        List<OrganizationEntity> children = childMap.get(org.getOrgId());
        for (OrganizationEntity child : children) {
            list.add(child);
            addChildrenToList(child, childMap, list); // 递归添加子组织
        }
    }

    @Override
    public List<UserDto> getUserByPositionTypeAndOrg(List<Integer> orgIds, List<Integer> positionType, boolean haveDisabledUser) {

        UserDto targetUserDto = new UserDto();
        targetUserDto.setPositionTypes(positionType);
        targetUserDto.setOrgIds(orgIds);
        if (!haveDisabledUser) {
            targetUserDto.setIsDisabled(0);
        }
        return userMapper.getUserByOrgIdsAndPositon(targetUserDto);
    }

    /**
     * 递归组装树形结构
     *
     * @param menuList   menuList
     * @param parentId   parentId
     * @param parentName parentName
     * @return JSONArray
     */
    private JSONArray treeMenuList(JSONArray menuList, int parentId, String parentName) {
        JSONArray childMenu = new JSONArray();
        for (Object object : menuList) {
            JSONObject jsonMenu = (JSONObject) JSONObject.toJSON(object);
            int menuId = jsonMenu.getInteger("userId");
            int pid = jsonMenu.getInteger("parentId");
            if (!"".equals(parentName)) {
                jsonMenu.put("nameArr", parentName + "--" + jsonMenu.getString("username"));
            } else {
                jsonMenu.put("nameArr", jsonMenu.getString("username"));
            }
            if (parentId == pid) {
                JSONArray childNode = treeMenuList(menuList, menuId, jsonMenu.getString("nameArr"));
                jsonMenu.put("childNode", childNode);
                childMenu.add(jsonMenu);
            }
        }
        return childMenu;
    }

    /**
     * 递归分析树状结构
     *
     * @param taskList taskList
     * @param selList  selList
     * @param num      num
     * @return List<UserDto>
     */
    private List<UserDto> resetList(JSONArray taskList, List<UserDto> selList, int num) {
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < (num * 2); i++) {
            str.append("-");
        }
        for (Object obj : taskList) {
            JSONObject jsonMenu = (JSONObject) JSONObject.toJSON(obj);
            UserDto sm = new UserDto();
            sm.setUserId(jsonMenu.getInteger("userId"));
            sm.setUsername(str + "├" + jsonMenu.getString("username"));
            sm.setParentId(jsonMenu.getInteger("parentId"));
            sm.setCcNumber(jsonMenu.getString("ccNumber"));
            selList.add(sm);
            if (jsonMenu.get("childNode") != null) {
                if (JSON.parseArray(jsonMenu.get("childNode").toString()).size() > 0) {
                    num++;
                    resetList(JSON.parseArray(jsonMenu.get("childNode").toString()), selList, num);
                    num--;
                }
            }
        }
        return selList;
    }

    @Override
    public UserDto getUserById(Integer userId) {
        UserDto userDto = userMapper.getUserByUserId(userId);
        return userDto == null ? new UserDto() : userDto;
    }

    @Override
    public UserDto getUserByIdGetMainOrg(Integer userId) {
        UserDto userDto = userMapper.getUserByUserId(userId);
        if(userDto != null && userDto.getOrgId() != null){
            Integer targetParentDepartment = this.findTargetParentDepartment(userDto.getOrgId(), 2);
            userDto.setMainOrgName(organizationApiService.getOrganizationById(targetParentDepartment).getOrgName());
            userDto.setMainOrgId(targetParentDepartment);
        }
        return userDto == null ? new UserDto() : userDto;
    }

    @Override
    public UserDto getUserBaseInfo(Integer userId) {
        UserDto userDto = userMapper.getUserBaseInfo(userId);
        return userDto == null ? new UserDto() : userDto;
    }


    @Override
    public UserDto getUserBaseInfoByJobNumber(String jobNumber){
        return  userMapper.getUserBaseInfoByJobNumber(jobNumber);
    }


    @Override
    public List<UserDto> getUserInfoByUserIds(List<Integer> userIds) {
        if(CollectionUtils.isEmpty(userIds)){
            return new ArrayList<>();
        }
        return userMapper.getUserInfoByUserIds(userIds);
    }

    @Override
    public List<UserDto> getUserList() {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        Integer topOrgIdByOrgId = organizationApiService.getTopOrgIdByOrgId(currentUser.getOrgId());
        List<Integer> orgAllSubId = organizationApiService.getOrgAllSubId(topOrgIdByOrgId);
        List<Integer> positionType = Collections.singletonList(310);
        return getUserByPositionTypeAndOrg(orgAllSubId, positionType, false);
    }

    /**
     * 指定组织ID列表获取用户列表
     * @param orgIdList
     * @return
     */
    @Override
    public List<UserDto> getUserList(List<Integer> orgIdList) {
        List<UserDto> userList = new ArrayList<>();
        for (Integer orgId : orgIdList) {
            OrganizationDto organizationById = organizationApiService.getOrganizationById(orgId);
            List<Integer> orgAllSubId = organizationApiService.getOrgAllSubId(orgId);
            List<UserDto> userListByOrgId = getUserByPositionTypeAndOrg(orgAllSubId, null, false);
            userListByOrgId.forEach(e-> {
                e.setOrgId(orgId);
                e.setOrgName(organizationById.getOrgName());
            });
            if (CollectionUtil.isEmpty(userListByOrgId)){
                continue;
            }
            userList.addAll(userListByOrgId);
        }

        return userList;
    }

    @Override
    public UserDto getUserByTraderId(Integer traderId) {
        return userMapper.getUserByTraderId(traderId);
    }

    @Override
    public List<Integer> getAllSubordinateByUserId(Integer userId) {
        List<UserDto> userList = userMapper.getAllValidUserByCompanyId(1);
        List<Integer> childrenList = new ArrayList<>();
        getAllChildrenByParentId(userList, userId, childrenList);
        childrenList.add(userId);
        return childrenList;
    }

    @Override
    public  List<Integer> getAllSubordinateByUserIdForVisit(Integer userId){

        return userMapper.getAllSubordinateByUserIdForVisit(userId);
    }

    @Override
    public List<Integer> getAllSubordinateHaveDisabledUser(Integer userId) {
        List<UserDto> userList = userMapper.getAllUserByCompanyId(1);
        List<Integer> childrenList = new ArrayList<>();
        getAllChildrenByParentId(userList, userId, childrenList);
        childrenList.add(userId);
        return childrenList;
    }

    @Override
    public List<UserDto> getUserByTraderIdList(List<Integer> traderIdList) {
        return userMapper.getUserByTraderIdList(traderIdList);
    }

    /**
     * 递归查询各个用户关联的子用户
     * @param users
     * @param parentId
     * @param children
     */
    private void getAllChildrenByParentId(List<UserDto> users, Integer parentId, List<Integer> children) {
        for (UserDto item : users) {
            if (parentId != null && parentId.equals(item.getParentId())) {
                children.add(item.getUserId());
                getAllChildrenByParentId(users, item.getUserId(), children);
            }
        }
    }

    @Override
    public List<String> getUserListByPositionId(Integer positionId) {
        return userMapper.getUserListByPositionId(positionId);
    }

    @Override
    public UserSmartQuoteVO getUserPermissionForAIQuote(Integer jobNumber) {
        UserSmartQuoteVO userAIQuoteVO = new UserSmartQuoteVO();
        UserDto userDto = userMapper.getUserByNumber(jobNumber);
        if (ObjectUtil.isNull(userDto)){
            return userAIQuoteVO;
        }
        userAIQuoteVO.setUserId(userDto.getUserId());
        List<PositionDto> positions = positionMapper.getPositionsByUserId(userDto.getUserId());
        if (CollUtil.isEmpty(positions)){
            log.info("根据userId未查询到职位信息，无法判断是否为销售人员，userDto:{}", JSONUtil.toJsonStr(userDto));
            return userAIQuoteVO;
        }
        //职位是否是供应链或销售
        userAIQuoteVO.setGenerateQuote(positions.stream().anyMatch(s -> ErpConstant.ID_310.equals(s.getType())));
        return userAIQuoteVO;
    }

    @Override
    public List<UserDto> getUserByNumberlist(List<Integer> numberList) {
        return userMapper.getUserBynumberList(numberList);
    }

    @Override
    public List<UserDto> getUserByPositionType(Integer positionType) {
        return userMapper.getUserByPositionType(positionType);
    }

    @Value("${visitrecordGroup1:1387,1393,1394,1398,1409,1415}")
    private String orgGroup1;

    @Value("${visitrecordGroup21:1428}")
    private String orgGroup21;//营销中心非公集团客户部

    @Value("${visitrecordGroup22:1384,1383}")
    private String orgGroup22;//营销中心应急业务部、营销中心产品方案部这三个部门

    private static final Integer POISITION_SALE_TYPE = 310;

    public static Integer B2B_ORGID = 38;

    @Value("${im.autoOpenImUserIds:953,1402}")
    private String autoOpenImUserIds;//总机负责售前和售后的两个人，自动打开IM的页面

    @Override
    public boolean checkUserIsB2b(Integer userId,Integer positioType) {
        //将autoOpenImUserIds以逗号分割，并判断 userId是否在其中
        String[] userIds = autoOpenImUserIds.split(","); // 使用逗号分割字符串
        boolean contains123 = Arrays.asList(userIds).contains(String.valueOf(userId)); // 检查是否包含userId
        if(contains123){//先放行总机的两个人
            return true;
        }

        if(!POISITION_SALE_TYPE.equals(positioType)){//再检查是否销售岗位
            return false;
        }
        //第一步，根据当前user查询所有的岗位有多少
        List<PositionDto> positions = positionMapper.getPositionsByUserId(userId);
        if (CollUtil.isEmpty(positions)){
            log.info("根据userId未查询到职位信息，不返回打开IM，userDto:{}", JSONUtil.toJsonStr(userId));
            return false;
        }
        List<Integer> orgIds = positions.stream()
                .filter(p ->POISITION_SALE_TYPE.equals(p.getType())) // 过滤positionId为"A"的PositionDto
                .map(PositionDto::getOrgId)
                .distinct()
                .collect(Collectors.toList());
        for(Integer orgIdCurrent:orgIds){//先判断是否在6个大区中
            List<Integer> departCurrentList = organizationMapper.getParentIdsById(orgIdCurrent);//注意，含当前部门ID
            return (CollectionUtil.isNotEmpty(departCurrentList)) && departCurrentList.contains(B2B_ORGID);
        }
        return false;
    }

    @Override
    public List<String> getOrgNameList(Integer userId) {
        List<String> orgNameList = organizationMapper.getOrgNameList(userId);
        if(CollectionUtil.isNotEmpty(orgNameList)){
            return orgNameList;
        }
        return new ArrayList<>();
    }

    @Override
    public List<Integer> getAllSubUserList(CurrentUser currentUser) {
        UserDto userDto = UserDto.builder()
                .isAdmin(currentUser.getIsAdmin())
                .username(currentUser.getUsername())
                .companyId(currentUser.getCompanyId())
                .positType(currentUser.getPositType())
                .userId(currentUser.getId())
                .build();

        List<UserDto> list = new ArrayList<>();
        List<Integer> positionType = Collections.singletonList(310);
        List<UserDto> allSubUserList = getAllSubUserList(userDto, positionType, false);
        List<UserDto> userListByOrgId = getMyUserListByUserOrgsList(userDto,positionType,false);
        list.addAll(allSubUserList);
        list.addAll(userListByOrgId);
        List<Integer> userList = list.stream().map(UserDto::getUserId).distinct().collect(Collectors.toList());
        return userList;
    }

    @Override
    public List<UserDto> getUserByRoleId(String roleName) {
        List<UserDto> userByRoleName = userMapper.getUserByRoleName(roleName);

        if(CollUtil.isNotEmpty(userByRoleName)){
            userByRoleName.forEach(user->{
                Integer targetParentDepartment = this.findTargetParentDepartment(user.getOrgId(), 2);
                user.setMainOrgName(organizationMapper.selectByPrimaryKey(targetParentDepartment).getOrgName());
                user.setMainOrgId(targetParentDepartment);
            });
        }
        return userByRoleName;
    }

    /**
     * 递归查询上级部门，直到找到 PARENT_ID = targetParentId 的部门
     *
     * @param orgId          当前部门 ID
     * @param targetParentId 目标上级部门 ID
     * @return 目标部门的 ORG_ID，如果未找到则返回 null
     */
    public Integer findTargetParentDepartment(int orgId, int targetParentId) {
        // 获取当前部门的 PARENT_ID
        Integer parentId = organizationMapper.getParentIdById(orgId);
        if (parentId == null) {
            return null;
        }
        if (parentId == targetParentId) {
            return orgId;
        }
        return findTargetParentDepartment(parentId, targetParentId);
    }

    @Override
    public UserOrgEnums getUserOrgEnum(Integer userId) {
        //第一步，根据当前user查询所有的岗位有多少
        List<PositionDto> positions = positionMapper.getPositionsByUserId(userId);
        if (CollUtil.isEmpty(positions)){
            log.info("根据userId未查询到职位信息，无法判断人员所在部门，userDto:{}", JSONUtil.toJsonStr(userId));
            return UserOrgEnums.ORG_OTHER;
        }

        List<Integer> orgIds = positions.stream()
                .map(PositionDto::getOrgId)
                .distinct()
                .collect(Collectors.toList());
        //第二步，一个人有可能有多个岗位，分岗位对应的部门去重后，逐个检查父级部门，是否在6个大区中。
        Map<Integer,List<Integer>> departIdList = new HashMap<>();
        for(Integer orgIdCurrent:orgIds){//先判断是否在6个大区中
            List<Integer> departCurrentList = organizationMapper.getParentIdsById(orgIdCurrent);//注意，含当前部门ID
            departIdList.put(orgIdCurrent,departCurrentList);
            List<Integer> orgGroup1List =  StringUtils.isBlank(orgGroup1)?new ArrayList<>():
                    Arrays.stream(orgGroup1.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            Set<Integer> orgGroup1Set = new HashSet<>(orgGroup1List);
            boolean containsAny = departCurrentList.stream()
                    .anyMatch(orgGroup1Set::contains);
            if(containsAny){
                return UserOrgEnums.ORG_DAQU;
            }
        }
        //第三步，判断当前人的岗位是否在应急这三个部门里
        for(Integer orgIdCurrent:orgIds){//要等6个大区是否都在，全部判断完成后，才能进行应急这三个部门的判断
            List<Integer> departCurrentList = departIdList.get(orgIdCurrent);
            List<Integer> orgGroup21List =  StringUtils.isBlank(orgGroup21)?new ArrayList<>():
                    Arrays.stream(orgGroup21.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            Set<Integer> orgGroup21Set = new HashSet<>(orgGroup21List);
            boolean containsAny = departCurrentList.stream()
                    .anyMatch(orgGroup21Set::contains);
            if(containsAny){
                return UserOrgEnums.ORG_FEIGONG;
            }
        }
        for(Integer orgIdCurrent:orgIds){//要等6个大区是否都在，全部判断完成后，才能进行应急这三个部门的判断
            List<Integer> departCurrentList = departIdList.get(orgIdCurrent);
            List<Integer> orgGroup22List =  StringUtils.isBlank(orgGroup22)?new ArrayList<>():
                    Arrays.stream(orgGroup22.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            Set<Integer> orgGroup22Set = new HashSet<>(orgGroup22List);
            boolean containsAny = departCurrentList.stream()
                    .anyMatch(orgGroup22Set::contains);
            if(containsAny){
                return UserOrgEnums.ORG_YINGJI;
            }
        }
        //第四部都不是，返回其他部门
        return UserOrgEnums.ORG_OTHER;
    }
    


}
