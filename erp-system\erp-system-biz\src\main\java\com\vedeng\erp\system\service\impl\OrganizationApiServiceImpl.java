package com.vedeng.erp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.system.domain.entity.OrganizationEntity;
import com.vedeng.erp.system.dto.OrganizationDto;
import com.vedeng.erp.system.mapper.OrganizationMapper;
import com.vedeng.erp.system.mapstruct.OrganizationConvertor;
import com.vedeng.erp.system.service.OrganizationApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 部门服务类
 * @date 2022/8/29 13:23
 **/
@Service
@Slf4j
public class OrganizationApiServiceImpl implements OrganizationApiService {

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private OrganizationConvertor organizationConvertor;


    @Override
    public OrganizationDto getOrganizationById(Integer orgId) {

        if (Objects.isNull(orgId)) {
            return new OrganizationDto();
        }
        OrganizationEntity organizationEntity = organizationMapper.selectByPrimaryKey(orgId);
        OrganizationDto organizationDto = organizationConvertor.toDto(organizationEntity);

        return organizationDto==null?new OrganizationDto():organizationDto;
    }

    @Override
    public Integer getTopOrgIdByOrgId(Integer orgId) {
        Integer parentOrgId = organizationMapper.getParentIdById(orgId);
        if (parentOrgId == null || parentOrgId == 2) {
            // 所有一级部门的parentId都是2，代表南京分公司
            return orgId;
        }
        return getTopOrgIdByOrgId(parentOrgId);
    }

    @Override
    public List<Integer> getOrgAllSubId(Integer orgId) {
        List<Integer> result = new LinkedList<>();
        result.add(orgId);
        getSubId(orgId,result);
        return result;
    }

    @Override
    public List<OrganizationDto> getOrgAllLeafOrgByParentId(Integer parentId) {
        List<OrganizationEntity> root=organizationMapper.findByParentId(parentId);
        List<OrganizationDto> result=new ArrayList<>();
        if (CollUtil.isNotEmpty(root)) {
            fill(result,root );
            return result;
        }
        return result;
    }
    private void fill(List<OrganizationDto> result,List<OrganizationEntity> root ){
        if (CollUtil.isEmpty(root)) {
            return;
        }
        root.forEach(item->{
            List<OrganizationEntity> child=organizationMapper.findByParentId(item.getOrgId());
            if (CollUtil.isEmpty(child)) {
                // 叶子节点
                OrganizationDto leaf= organizationConvertor.toDto(item);
                result.add(leaf);

            }else{
                fill(result,child );
            }
        });
    }

    private void getSubId(Integer orgId, List<Integer> result) {

        List<Integer> idByParentId = organizationMapper.findIdByParentId(orgId);
        if (CollUtil.isEmpty(idByParentId)) {
            return;
        }
        result.addAll(idByParentId);
        idByParentId.forEach(x-> getSubId(x,result));
    }
}
