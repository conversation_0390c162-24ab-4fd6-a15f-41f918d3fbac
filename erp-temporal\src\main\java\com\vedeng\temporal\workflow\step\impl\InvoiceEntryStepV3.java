package com.vedeng.temporal.workflow.step.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.result.PollingResult;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.enums.BusinessStepType;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.polling.universal.workflow.UniversalPollingWorkflow;
import com.vedeng.temporal.workflow.activity.InvoiceEntryActivity;
import com.vedeng.temporal.workflow.activity.dto.PurchaseOrderRecordingStatusInfo;
import com.vedeng.temporal.workflow.step.BusinessStep;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;

/**
 * 发票录入步骤 V3 - 增量循环录票版本
 * 
 * 架构优化说明：
 * - 基于 InventoryReceiptStepV3 的循环监控模式
 * - 使用 Workflow 状态跟踪处理进度（会被 Temporal 持久化）
 * - 所有外部调用通过 Activity 执行，确保 Temporal 兼容性
 * - 支持可靠的重放和故障恢复
 * 
 * 业务流程：
 * 1. awaitSaleOrderQueryCompletion - 查询销售单号和采购单号并存储到扩展属性
 * 2. monitorAndRecordInvoicesIncrement - 增量监控销售发票并录入采购发票
 *    a. 检查采购单录票完成状态（采购单已录发票金额 >= 销售单总开票金额）
 *    b. 如果未完成，查询销售单是否有新发票可录入
 *    c. 为新发票执行录票流程
 *    d. 等待后重复检查，直到录票数据匹配完成
 * 
 * 功能特性：
 * - 增量处理：每次循环只处理当前可用的新发票
 * - 状态驱动：基于采购单录票完成状态判断循环终止
 * - 避免重复：检查发票是否已录入，避免重复录票
 * - 持续监控：直到所有发票都录入完成
 * 
 * 错误处理：
 * - Activity层：技术异常自动重试（网络、超时等）
 * - Step层：业务异常处理（数据校验、业务规则等）
 * - 完整的状态追踪和日志记录
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 3.0 (增量循环录票版本)
 * @since 2025-01-21
 */
@Slf4j
public class InvoiceEntryStepV3 implements BusinessStep {
    
    private final InvoiceEntryActivity invoiceEntryActivity;
    
    public InvoiceEntryStepV3(InvoiceEntryActivity invoiceEntryActivity) {
        this.invoiceEntryActivity = invoiceEntryActivity;
    }
    
    @Override
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, CompanyExecutionContext context) {
        log.info("【倒序】开始执行发票录入步骤V3（增量循环版），业务ID: {}, 原始目标公司: {}", 
                request.getBusinessId(), request.getTargetCompanyCode());
        
        // 第一个公司跳过执行
        if (context.isLast()) {
            log.info("最后公司无需处理发票录入，跳过执行");
            return CompanyBusinessResponse.success("第一个公司无需处理，跳过执行", request.getBusinessId());
        }
        
        String currentCompany = context.getCurrentCompany();
        String nextCompany = context.getNextCompany();
        
        // 创建工作副本，避免修改原始request对象（Temporal重试兼容性）
        CompanyBusinessRequest workingRequest = buildWorkingRequest(request, currentCompany, nextCompany);
        
        log.info("执行发票录入，当前公司: {}, 目标公司: {}, 业务ID: {}", 
                currentCompany, nextCompany, request.getBusinessId());
        
        // 第1步：根据业务流转单查询销售单号和采购单号
        log.info("开始查询当前公司:{}销售单号,下家公司{}采购单, 业务ID: {}", currentCompany, nextCompany, request.getBusinessId());
        awaitSaleOrderQueryCompletion(currentCompany, workingRequest);
        awaitPurchaseOrderQueryCompletion(nextCompany, workingRequest);

        // 第2步：开始增量发票录入监控（新增核心方法）
        monitorAndRecordInvoicesIncrement(currentCompany, nextCompany, workingRequest);
        
        return CompanyBusinessResponse.success("增量发票录入完成", request.getBusinessId());
    }
    
    @Override
    public String getStepName() {
        return "发票录入步骤V3";
    }

    @Override
    public BusinessStepType getStepType() {
        return BusinessStepType.INVOICE_ENTRY;
    }

    @Override
    public String getStepDescription() {
        return "执行增量循环发票录入流程：持续监控销售单发票 → 检查采购单录票状态 → 录入新发票 → 直至全部完成";
    }

    
    // ========== 私有辅助方法 ==========
    
    /**
     * 构建工作请求副本，避免修改原始request对象
     * 这对Temporal重试兼容性至关重要
     */
    private CompanyBusinessRequest buildWorkingRequest(CompanyBusinessRequest originalRequest, 
                                                      String currentCompany, 
                                                      String nextCompany) {
        return CompanyBusinessRequest.builder()
                .businessId(originalRequest.getBusinessId())
                .businessType(originalRequest.getBusinessType())
                .sourceCompanyCode(currentCompany)
                .targetCompanyCode(nextCompany)
                .workflowExecutionId(originalRequest.getWorkflowExecutionId())
                .flowNodeId(originalRequest.getFlowNodeId())
                .businessData(originalRequest.getBusinessData())
                .extendedProperties(originalRequest.getExtendedProperties() != null ? 
                    new HashMap<>(originalRequest.getExtendedProperties()) : new HashMap<>())
                .build();
    }
    
    /**
     * 等待销售单号查询完成
     */
    private void awaitSaleOrderQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始轮询等待销售单号，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 构建查询参数
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("queryTypes", Arrays.asList("SALE_ORDER"));
            queryParameters.put("currentCompany", companyCode);
            
            // 构建统一轮询请求
            UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(request.getBusinessType())
                    .dataSourceType(DataSourceType.LOCAL_DATABASE)
                    .companyCode(companyCode)
                    .queryType("TEMPORAL_FLOW_ORDER_QUERY")
                    .queryParameters(queryParameters)
                    .completionCheckConfig("saleOrderNo:isNotBlank")
                    .build();
            
            // 执行统一轮询
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);
            
            // 轮询成功后，需要手动获取最终数据并设置到扩展属性
            if (result.isSuccess()) {
                Object saleOrderNo = result.getData().get("saleOrderNo");
                // 存储到扩展属性
                Map<String, Object> extendedProperties = request.getExtendedProperties();
                if (extendedProperties == null) {
                    extendedProperties = new HashMap<>();
                }
                extendedProperties.put("saleOrderNo", saleOrderNo);
                request.setExtendedProperties(extendedProperties);

                log.info("销售单号轮询完成，公司: {}, 业务ID: {}, 销售单号: {}",
                        companyCode, businessId, saleOrderNo);
            } else {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("销售单号轮询未完成", "POLLING_INCOMPLETE", context);
            }

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("销售单号轮询异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("销售单号轮询系统异常", "POLLING_ERROR", context);
        }
    }
    
    /**
     * 等待采购单号查询完成
     * 使用通用化枚举设计的轮询机制等待采购单号准备就绪
     */
    private void awaitPurchaseOrderQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始轮询等待采购单号，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 构建查询参数
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("queryTypes", Arrays.asList("BUY_ORDER"));
            queryParameters.put("currentCompany", companyCode);

            // 构建统一轮询请求
            UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.LOCAL_DATABASE)
                    .companyCode(companyCode)
                    .queryType("TEMPORAL_FLOW_ORDER_QUERY")
                    .queryParameters(queryParameters)
                    .completionCheckConfig("buyOrderNo:isNotBlank")
                    .build();

            // 执行统一轮询
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);

            // 轮询成功后，需要手动获取最终数据并设置到扩展属性
            if (result.isSuccess()) {
                // 由于轮询已完成，直接查询一次获取最终数据
                Object buyOrderNo = result.getData().get("buyOrderNo");

                // 存储到扩展属性
                Map<String, Object> extendedProperties = request.getExtendedProperties();
                if (extendedProperties == null) {
                    extendedProperties = new HashMap<>();
                }
                extendedProperties.put("buyOrderNo", buyOrderNo);
                request.setExtendedProperties(extendedProperties);

                log.info("采购单号轮询完成，公司: {}, 业务ID: {}, 采购单号: {}",
                        companyCode, businessId, buyOrderNo);
            } else {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("采购单号轮询未完成", "POLLING_INCOMPLETE", context);
            }

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("采购单号轮询异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("采购单号轮询系统异常", "POLLING_ERROR", context);
        }
    }
    
    /**
     * V3 核心方法：增量监控销售发票并录入采购发票（基于采购单录票完成状态判断版）
     * <p>
     * 重构后的核心逻辑：
     * 1. 检查采购单录票完成状态（采购单已录发票金额 >= 销售单总开票金额）
     * 2. 如果未完成，查询销售单是否有新发票可录入
     * 3. 为新发票执行录票流程
     * 4. 等待后重复检查，直到录票数据匹配完成
     * <p>
     * Temporal 兼容性保证：
     * - 使用 Workflow 状态跟踪处理进度（会被 Temporal 持久化）
     * - 所有外部调用通过 Activity 执行
     * - 支持可靠的重放和故障恢复
     *
     * @param currentCompany 当前公司代码（销售单公司）
     * @param nextCompany    下游公司代码（采购单公司）
     * @param request        业务请求
     */
    private void monitorAndRecordInvoicesIncrement(String currentCompany, String nextCompany, CompanyBusinessRequest request) {
        // Workflow 状态：跟踪发票录入完成情况（会被 Temporal 持久化）
        boolean allInvoicesRecorded = false;

        try {
            log.info("开始增量发票录入，基于采购单录票完成状态判断");
            
            // 持续处理直到录票完成
            while (!allInvoicesRecorded) {
                // 1. 检查采购单录票完成状态（核心改进）
                CompanyBusinessResponse statusResponse = invoiceEntryActivity.checkPurchaseOrderRecordingStatus(request);
                log.info("检查采购单录票完成状态，statusResponse:{}",JSON.toJSON(statusResponse));
                PurchaseOrderRecordingStatusInfo statusInfo = parsePurchaseOrderRecordingStatusInfo(statusResponse);
                request.getExtendedProperties().put("invoiceNoList",  statusInfo.getInvoiceNoList());

                log.info("采购单录票完成状态：{}", JSON.toJSON(statusInfo));

                // 2. 判断是否完成（基于采购单录票状态）
                allInvoicesRecorded = statusInfo.isCompleted();

                if (!allInvoicesRecorded) {
                    
                    // 3. 查询销售单当前发票信息
                    log.info("开始查询销售单发票信息，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());
                    List<Object> saleInvoiceData = awaitInvoiceDetailQueryCompletion(currentCompany, request);
                    log.info("销售单发票信息查询完成，公司: {}, 业务ID: {},出参：{}", currentCompany, request.getBusinessId(),JSON.toJSON(saleInvoiceData));

                   
                    for (Object obj : saleInvoiceData) {
                        Map<String, Object> invoiceDetailData = (Map<String, Object>) obj;
                        log.info("开始录票，发票数据: {}", JSON.toJSON(invoiceDetailData));
                        if ( Objects.isNull(invoiceDetailData.get("invoiceGoods")) ||
                                Objects.isNull(invoiceDetailData.get("invoiceNo")) || 
                                Objects.equals(invoiceDetailData.get("invoiceNo"),"")){
                            log.info("发票数据为空，跳过");
                            continue;
                        }

                        // 创建查询请求副本，使用当前公司代码
                        CompanyBusinessRequest safeRequest = CompanyBusinessRequest.builder()
                                .businessId(request.getBusinessId())
                                .businessType(request.getBusinessType())
                                .sourceCompanyCode(request.getSourceCompanyCode())
                                .targetCompanyCode(request.getTargetCompanyCode())
                                .workflowExecutionId(request.getWorkflowExecutionId())
                                .flowNodeId(request.getFlowNodeId())
                                .businessData(request.getBusinessData())
                                .extendedProperties(new HashMap<>(request.getExtendedProperties()))
                                .build();
                        // 将发票号存储到业务数据中，供后续步骤使用
                        updateBusinessDataWithInvoiceNo(safeRequest, invoiceDetailData);
                        
                        // 4. 录票 - 基于预查询数据创建发票录入
                        CompanyBusinessResponse createResult = invoiceEntryActivity.createInvoiceWithDetails(safeRequest, invoiceDetailData);
                        log.info("发票录入结果：{}", JSON.toJSON(createResult));
                        if (!createResult.getSuccess()){
                            log.info("录入发票失败");
                            continue;
                        }

                        // 5. 审核 - 审核发票
                        log.info("开始审核发票，发票数据: {}", JSON.toJSON(invoiceDetailData));
                        CompanyBusinessResponse approveResult = invoiceEntryActivity.approveInvoice(safeRequest, invoiceDetailData);
                        log.info("审核发票结果：{}", JSON.toJSON(approveResult));

                        // 6. 存储发票附件 - 等待发票InvoiceHref字段有值且不为空，然后更新
                        log.info("开始检查发票InvoiceHref字段，业务ID: {}", request.getBusinessId());
                        awaitInvoiceHrefReady(currentCompany, request);
                        log.info("发票InvoiceHref字段检查完成，业务ID: {}", request.getBusinessId());

                        // 7.调用invoiceEntryActivity更新InvoiceHref字段
                        log.info("开始更新发票InvoiceHref字段，业务ID: {}", request.getBusinessId());
                        CompanyBusinessResponse updateResult = invoiceEntryActivity.updateInvoiceHref(request);
                        log.info("更新发票InvoiceHref字段结果：{}", JSON.toJSON(updateResult));
                    }
                   
                    
                    // 7. 等待后继续检查（仿照原模式）
                    log.info("等待 {} 分钟后继续检查发票状态", 3);
                    Workflow.sleep(Duration.ofMinutes(3));
                } else {
                    log.info("所有发票录入完成，完成增量发票录入监控");
                }
            }
            
        } catch (Exception e) {
            log.error("增量发票录入监控异常", e);
            throw BusinessProcessException.retryable("增量发票录入监控失败", "INVOICE_MONITORING_ERROR", 
                "CurrentCompany=" + currentCompany + ", NextCompany=" + nextCompany);
        }
    }
    
    /**
     * 第2步：等待发票信息查询完成，判断销售单是否已经开票
     * 从 InvoiceEntryStep 迁移的逻辑，用于轮询等待发票详情准备就绪
     * 重构：直接调用 InvoiceEntryActivity.queryInvoiceDetail 方法
     *
     * @return
     */
    private List<Object> awaitInvoiceDetailQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始查询发票信息，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 从扩展属性中获取销售单号
            Map<String, Object> extendedProperties = request.getExtendedProperties();
            if (extendedProperties == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("扩展属性为空，无法获取销售单号", "MISSING_EXTENDED_PROPERTIES", context);
            }

            String saleOrderNo = (String) extendedProperties.get("saleOrderNo");
            if (saleOrderNo == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("未找到销售单号", "MISSING_SALE_ORDER_NO", context);
            }

            log.info("查询发票详情，销售单号: {}", saleOrderNo);

            // 创建查询请求副本，使用当前公司代码
            CompanyBusinessRequest queryRequest = CompanyBusinessRequest.builder()
                    .businessId(request.getBusinessId())
                    .businessType(request.getBusinessType())
                    .sourceCompanyCode(companyCode)
                    .targetCompanyCode(companyCode)
                    .workflowExecutionId(request.getWorkflowExecutionId())
                    .flowNodeId(request.getFlowNodeId())
                    .businessData(request.getBusinessData())
                    .extendedProperties(new HashMap<>(request.getExtendedProperties()))
                    .build();

            // 直接调用Activity方法查询发票详情
            CompanyBusinessResponse response = invoiceEntryActivity.queryInvoiceDetail(queryRequest);

            // 验证查询结果
            if (!response.getSuccess()) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("发票信息查询失败: " + response.getMessage(), 
                    "QUERY_FAILED", context);
            }

            // 解析返回的数据
            Map<String, Object> data = (Map<String, Object>) response.getResultData();
            List<Object> list = (List<Object>) data.get("list");
            
            log.info("发票信息查询完成，公司: {}, 业务ID: {}, 结果: {}", companyCode, businessId, JSON.toJSON(data));
            
            return list;

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("查询发票信息异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("发票信息查询系统异常", "POLLING_ERROR", context);
        }
    }
    
    /**
     * 解析采购单录票状态信息
     */
    private PurchaseOrderRecordingStatusInfo parsePurchaseOrderRecordingStatusInfo(CompanyBusinessResponse response) {
        try {
            if (response.getSuccess()) {
                Map<String, Object> data = (Map<String, Object>) response.getResultData();
                
                PurchaseOrderRecordingStatusInfo statusInfo = new PurchaseOrderRecordingStatusInfo();
                statusInfo.setCompleted(Boolean.parseBoolean(data.get("completed").toString()));
                
                return statusInfo;
            }
            
            // 默认返回未完成状态
            return PurchaseOrderRecordingStatusInfo.notCompleted();
            
        } catch (Exception e) {
            log.error("解析录票状态信息失败: {}", response.getResultData(), e);
            return PurchaseOrderRecordingStatusInfo.notCompleted();
        }
    }
    
    /**
     * 检查发票是否已录入采购单
     */
    private boolean checkIfInvoiceAlreadyRecorded(String companyCode, String invoiceNo, CompanyBusinessRequest request) {
        try {
            String buyOrderNo = (String) request.getExtendedProperties().get("buyOrderNo");
            
            // 调用查询API检查发票是否已录入
            CompanyBusinessResponse response = invoiceEntryActivity.queryInvoiceRecord(
                request.toBuilder()
                    .sourceCompanyCode(companyCode)
                    .targetCompanyCode(companyCode)
                    .businessData(JSON.toJSONString(createRequestMap(buyOrderNo, invoiceNo)))
                    .build()
            );
            
            if (response.getSuccess() && response.getResultData() != null && StringUtils.isNotBlank(response.getResultData().toString())) {
                log.debug("发票{}已录入采购单{}", invoiceNo, buyOrderNo);
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            log.warn("检查发票录入状态异常: invoiceNo={}", invoiceNo, e);
            return false; // 异常时认为未录入，会尝试录入
        }
    }
    
    /**
     * 处理新发票录入
     */
    private void processNewInvoiceRecording(CompanyBusinessRequest request, Map<String, Object> saleInvoiceData) {
        try {
            // 将发票号存储到业务数据中，供后续步骤使用
            updateBusinessDataWithInvoiceNo(request, saleInvoiceData);
            
            // 第3步：基于预查询数据创建发票录入
            CompanyBusinessResponse createResult = invoiceEntryActivity.createInvoiceWithDetails(request, saleInvoiceData);
            log.info("发票录入结果：{}", JSON.toJSON(createResult));
            
            // 第4步：审核发票
            CompanyBusinessResponse approveResult = invoiceEntryActivity.approveInvoice(request, saleInvoiceData);
            log.info("审核发票结果：{}", JSON.toJSON(approveResult));
            
            // 第5步：等待发票InvoiceHref字段有值且不为空
            log.info("开始检查发票InvoiceHref字段，业务ID: {}", request.getBusinessId());
            awaitInvoiceHrefReady(request.getSourceCompanyCode(), request);
            log.info("发票InvoiceHref字段检查完成，业务ID: {}", request.getBusinessId());
            
            // 第6步：调用invoiceEntryActivity更新InvoiceHref字段
            log.info("开始更新发票InvoiceHref字段，业务ID: {}", request.getBusinessId());
            CompanyBusinessResponse updateResult = invoiceEntryActivity.updateInvoiceHref(request);
            log.info("更新发票InvoiceHref字段结果：{}", JSON.toJSON(updateResult));
            
        } catch (Exception e) {
            log.error("处理新发票录入失败", e);
            throw BusinessProcessException.retryable("新发票录入处理失败", "NEW_INVOICE_RECORDING_ERROR", 
                "InvoiceData=" + JSON.toJSONString(saleInvoiceData));
        }
    }
    
    /**
     * 提取发票号
     */
    private String extractInvoiceNo(Map<String, Object> invoiceDetailData) {
        try {
            if (invoiceDetailData == null || invoiceDetailData.isEmpty()) {
                return null;
            }
            
            Map<String, Object> data = (Map<String, Object>) invoiceDetailData.get("data");
            if (data != null) {
                return (String) data.get("invoiceNo");
            }
            
            return null;
            
        } catch (Exception e) {
            log.error("提取发票号失败: {}", JSON.toJSON(invoiceDetailData), e);
            return null;
        }
    }
    
    /**
     * 将发票号存储到业务数据中，供后续步骤使用
     */
    private void updateBusinessDataWithInvoiceNo(CompanyBusinessRequest request, Map<String, Object> invoiceDetailData) {
        try {
            // 从发票详情数据中提取发票号
            Map<String, Object> data = (Map<String, Object>) invoiceDetailData.get("data");
            if (data != null) {
                String invoiceNo = (String) data.get("invoiceNo");
                if (invoiceNo != null) {
                    // 解析现有的业务数据
                    Map<String, Object> businessData = new HashMap<>();
                    if (request.getBusinessData() != null) {
                        try {
                            businessData = JSON.parseObject(request.getBusinessData(), Map.class);
                        } catch (Exception e) {
                            log.warn("解析现有业务数据失败，使用空Map: {}", request.getBusinessData(), e);
                        }
                    }
                    
                    // 添加发票号
                    businessData.put("invoiceNo", invoiceNo);
                    
                    // 更新业务数据
                    request.setBusinessData(JSON.toJSONString(businessData));
                    
                    log.info("已将发票号存储到业务数据中，业务ID: {}, 发票号: {}", request.getBusinessId(), invoiceNo);
                } else {
                    log.warn("从发票详情数据中未找到发票号，业务ID: {}", request.getBusinessId());
                }
            } else {
                log.warn("发票详情数据中data字段为空，业务ID: {}", request.getBusinessId());
            }
        } catch (Exception e) {
            log.error("更新业务数据中的发票号失败，业务ID: {}", request.getBusinessId(), e);
            // 这里不抛出异常，因为这不是关键流程
        }
    }

    /**
     * 第3步：等待发票InvoiceHref字段有值且不为空
     * 使用轮询机制检查t_Invoice表中的InvoiceHref字段
     * 重构：直接调用 InvoiceEntryActivity.queryInvoiceHref 方法
     */
    private void awaitInvoiceHrefReady(String currentCompany, CompanyBusinessRequest request) {
        log.info("开始轮询等待发票InvoiceHref字段，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 从业务数据中获取发票号
            Map<String, Object> businessData = new HashMap<>();
            if (request.getBusinessData() != null) {
                try {
                    businessData = JSON.parseObject(request.getBusinessData(), Map.class);
                } catch (Exception e) {
                    log.warn("解析业务数据失败: {}", request.getBusinessData(), e);
                }
            }
            
            String invoiceNo = (String) businessData.get("invoiceNo");
            if (invoiceNo == null) {
                String context = "Step=" + getStepName() + ", Company=" + currentCompany + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("业务数据中未找到发票号", "MISSING_INVOICE_NO", context);
            }

            log.info("查询发票InvoiceHref字段，发票号: {}", invoiceNo);

            // 创建查询请求副本，使用当前公司代码
            CompanyBusinessRequest queryRequest = CompanyBusinessRequest.builder()
                    .businessId(request.getBusinessId())
                    .businessType(request.getBusinessType())
                    .sourceCompanyCode(currentCompany)
                    .targetCompanyCode(currentCompany)
                    .workflowExecutionId(request.getWorkflowExecutionId())
                    .flowNodeId(request.getFlowNodeId())
                    .businessData(request.getBusinessData())
                    .extendedProperties(request.getExtendedProperties() != null ? 
                        new HashMap<>(request.getExtendedProperties()) : new HashMap<>())
                    .build();

            // 轮询等待InvoiceHref字段有值且不为空
            boolean hrefReady = false;
            int maxRetries = 10; // 最大重试次数
            int retryCount = 0;
            
            while (!hrefReady && retryCount < maxRetries) {
                // 直接调用Activity方法查询InvoiceHref
                CompanyBusinessResponse response = invoiceEntryActivity.queryInvoiceHref(queryRequest);

                // 验证查询结果
                if (response.getSuccess() && response.getResultData() != null) {
                    Map<String, Object> data = (Map<String, Object>) response.getResultData();
                    Map<String, Object> invoiceData = (Map<String, Object>) data.get("data");
                    
                    if (invoiceData != null) {
                        String invoiceHref = (String) invoiceData.get("invoiceHref");
                        if (StringUtils.isNotBlank(invoiceHref)) {
                            hrefReady = true;
                            log.info("发票InvoiceHref字段已准备就绪，公司: {}, 业务ID: {}, 发票号: {}, InvoiceHref: {}", 
                                currentCompany, businessId, invoiceNo, invoiceHref);
                        }
                    }
                }
                
                if (!hrefReady) {
                    retryCount++;
                    if (retryCount < maxRetries) {
                        log.debug("发票InvoiceHref字段尚未准备就绪，等待30秒后重试，重试次数: {}/{}", retryCount, maxRetries);
                        Workflow.sleep(Duration.ofSeconds(30));
                    }
                }
            }
            
            if (!hrefReady) {
                String context = "Step=" + getStepName() + ", Company=" + currentCompany + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("发票InvoiceHref字段轮询超时", "POLLING_TIMEOUT", context);
            }

            log.info("发票InvoiceHref字段轮询完成，公司: {}, 业务ID: {}, 发票号: {}",
                    currentCompany, businessId, invoiceNo);

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("发票InvoiceHref字段轮询异常，公司: {}, 业务ID: {}", currentCompany, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + currentCompany + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("发票InvoiceHref字段轮询系统异常", "POLLING_ERROR", context);
        }
    }
    
    /**
     * 创建请求Map（Java 8 兼容）
     */
    private Map<String, Object> createRequestMap(String buyOrderNo, String invoiceNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("buyOrderNo", buyOrderNo);
        map.put("invoiceNo", invoiceNo);
        return map;
    }
}
