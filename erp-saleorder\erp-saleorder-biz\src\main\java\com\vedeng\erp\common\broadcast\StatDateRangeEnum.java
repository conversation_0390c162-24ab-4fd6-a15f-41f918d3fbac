package com.vedeng.erp.common.broadcast;

/**
 * 统计类型枚举
 * @ClassName:  StatisticsTypeEnum   
 * @author: <PERSON><PERSON>ya<PERSON>
 * @date:   2025年6月6日 下午4:05:33    
 * @Copyright:
 */
public enum StatDateRangeEnum {
	
	STATISTICS_DAY(1,"本日"),
	STATISTICS_WEEK(2,"本周"),
	STATISTICS_MONTH(3,"本月");

	private StatDateRangeEnum(Integer type, String typeName) {
		this.type = type;
		this.typeName = typeName;
	}

	private Integer type;

    private String typeName;

	public Integer getType() {
		return type;
	}

	public String getTypeName() {
		return typeName;
	}

}
