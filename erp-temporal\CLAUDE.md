# CLAUDE.md - ERP Temporal 工作流引擎模块

本文件为 `erp-temporal` 模块提供 Claude Code 工作指南，帮助 AI 助手理解项目结构和开发规范。

## 项目概述

`erp-temporal` 是基于 Temporal 工作流引擎实现的 ERP 多公司业务流程自动化模块，支持数据库驱动的动态工作流配置和跨公司业务单据同步处理。

### 核心设计理念
- **分布式工作流引擎**：基于 Temporal 提供可靠的分布式工作流解决方案
- **多公司业务自动化**：支持跨公司业务流程的自动化处理和状态同步
- **数据库驱动配置**：基于 T_FLOW_ORDER、T_FLOW_NODE 表的动态流程配置
- **Spring 4.1.9 集成**：与现有 ERP 系统（Spring 4.1.9 + XXL-Job）无缝集成

## 构建和测试命令

### 基础构建命令
```bash
# 构建模块
mvn clean compile

# 打包
mvn clean package

# 安装到本地仓库
mvn clean install

# 运行测试
mvn test

# 跳过测试构建
mvn clean install -DskipTests
```

### 与其他模块一起构建
```bash
# 在项目根目录构建整个项目
mvn clean install

# 构建当前模块及其依赖
mvn clean install -pl erp-temporal -am
```

## 技术架构

### 核心技术栈

| 技术组件 | 版本 | 用途 |
|---------|------|------|
| **Spring Framework** | 4.1.9 | 核心应用框架 |
| **Temporal SDK** | 1.22.0 | 工作流引擎 |
| **XXL-Job** | 2.1.1 | 定时任务调度 |
| **MySQL** | 5.1.47 | 数据存储 |
| **Apollo** | 1.4.0 | 配置中心 |

### 系统架构层次

```
调度层 (XXL-Job)
    ↓
应用服务层 (Spring + Temporal Worker)
    ↓
工作流引擎层 (Temporal Server)
    ↓
业务编排层 (Workflow Implementation)
    ↓
Activity执行层 (Business Activities)
    ↓
数据访问层 (MyBatis Mappers)
    ↓
数据存储层 (MySQL)
```

### 关键包结构

```
com.vedeng.temporal/
├── config/                    # 配置管理
│   ├── TemporalConfig.java     # Temporal 主配置类
│   ├── TemporalProperties.java # 配置属性
│   └── ActivityConfigManager.java # Activity 配置管理
├── workflow/                   # 工作流定义
│   ├── MultiCompanyBusinessWorkflow.java # 主工作流接口
│   ├── impl/                   # 工作流实现
│   ├── activity/               # Activity 接口和实现
│   ├── process/                # 业务流程处理
│   └── step/                   # 业务步骤实现
├── domain/                     # 领域对象
│   ├── dto/                    # 数据传输对象
│   ├── entity/                 # 数据库实体
│   └── enums/                  # 枚举定义
├── service/                    # 业务服务
├── mapper/                     # 数据访问接口
├── task/                       # 定时任务
├── util/                       # 工具类
├── validation/                 # 验证框架
├── exception/                  # 异常处理
├── notification/               # 通知服务
├── context/                    # 执行上下文
└── polling/                    # 轮询框架
    └── universal/              # 通用轮询框架
```

## 核心功能模块

### 1. 工作流引擎集成

**Temporal 配置**：
```java
@Configuration
@ComponentScan(basePackages = "com.vedeng.temporal")
public class TemporalConfig {
    
    @Bean
    public WorkflowServiceStubs workflowServiceStubs() {
        return WorkflowServiceStubs.newServiceStubs(
            WorkflowServiceStubsOptions.newBuilder()
                .setTarget(serverHost + ":" + serverPort)
                .build()
        );
    }
    
    @Bean
    public WorkflowClient workflowClient(WorkflowServiceStubs serviceStubs) {
        return WorkflowClient.newInstance(serviceStubs,
            WorkflowClientOptions.newBuilder()
                .setNamespace(namespace)
                .build());
    }
}
```

**Worker 注册**：
```java
@Bean
public Worker multiCompanyBusinessWorker(WorkerFactory workerFactory) {
    Worker worker = workerFactory.newWorker("erp-multi-company-queue");
    
    // 注册工作流实现
    worker.registerWorkflowImplementationTypes(
        MultiCompanyBusinessWorkflowImpl.class
    );
    
    // 注册 Activity 实现
    worker.registerActivitiesImplementations(
        new CompanySequenceActivityImpl(),
        new InventoryReceiptActivityImpl(),
        new InvoiceEntryActivityImpl(),
        new PaymentActivityImpl(),
        new PurchaseOrderActivityImpl(),
        new SalesOrderActivityImpl()
    );
    
    return worker;
}
```

### 2. 多公司业务流程

**主工作流定义**：
```java
@WorkflowInterface
public interface MultiCompanyBusinessWorkflow {
    @WorkflowMethod
    CompanyBusinessResponse processMultiCompanyBusiness(CompanyBusinessRequest request);
    
    @QueryMethod
    String getWorkflowStatus();
    
    @SignalMethod
    void pauseWorkflow(String reason);
}
```

**业务流程实现**：
```java
@Component
public class MultiCompanyBusinessWorkflowImpl implements MultiCompanyBusinessWorkflow {
    
    private final CompanySequenceActivity companySequenceActivity = 
        Workflow.newActivityStub(CompanySequenceActivity.class, 
            ActivityOptions.newBuilder()
                .setScheduleToCloseTimeout(Duration.ofMinutes(10))
                .setRetryOptions(RetryOptions.newBuilder()
                    .setMaximumAttempts(3)
                    .build())
                .build());
    
    @Override
    public CompanyBusinessResponse processMultiCompanyBusiness(CompanyBusinessRequest request) {
        // 获取公司执行顺序
        CompanySequenceInfo sequenceInfo = companySequenceActivity.getCompanySequence(request);
        
        // 按顺序执行业务流程
        for (String companyCode : sequenceInfo.getCompanyOrder()) {
            processCompanyBusiness(request, companyCode);
        }
        
        return CompanyBusinessResponse.success();
    }
}
```

### 3. Activity 实现模式

**标准 Activity 接口**：
```java
@ActivityInterface
public interface InventoryReceiptActivity {
    @ActivityMethod
    InventoryReceiptResult processInventoryReceipt(InventoryReceiptRequest request);
    
    @ActivityMethod
    void updateInventoryStatus(String orderId, String status);
}
```

**Activity 实现示例**：
```java
@Component
public class InventoryReceiptActivityImpl implements InventoryReceiptActivity {
    
    @Autowired
    private SystemApiClient systemApiClient;
    
    @Override
    public InventoryReceiptResult processInventoryReceipt(InventoryReceiptRequest request) {
        try {
            // 调用 ERP 系统接口
            String response = systemApiClient
                .withCompany(request.getCompanyCode())
                .postToSystemApi("/api/inventory/receipt", request);
                
            return InventoryReceiptResult.success(response);
        } catch (Exception e) {
            logger.error("库存入库处理失败", e);
            return InventoryReceiptResult.failure(e.getMessage());
        }
    }
}
```

### 4. 通用轮询框架

**通用轮询 Activity**：
```java
@ActivityInterface
public interface UniversalPollingActivity {
    @ActivityMethod 
    UniversalPollingResult poll(UniversalPollingRequest request);
}
```

**轮询请求配置**：
```java
@Data
public class UniversalPollingRequest {
    private CheckerType checkerType;        // API 或 DATABASE
    private DataSourceType dataSourceType;  // 数据源类型
    private ApiConfig apiConfig;            // API 配置
    private DatabaseConfig databaseConfig;  // 数据库配置
    private CompletionCondition condition;  // 完成条件
    private Duration timeout;               // 超时设置
    private Duration pollInterval;          // 轮询间隔
}
```

### 5. 系统 API 调用

**多公司 API 客户端**：
```java
@Component
public class SystemApiClient {
    
    @Autowired
    private CompanyApiConfigService companyApiConfigService;
    
    private final ThreadLocal<String> currentCompanyCode = new ThreadLocal<>();
    
    public SystemApiClient withCompany(String companyCode) {
        this.currentCompanyCode.set(companyCode);
        return this;
    }
    
    public String postToSystemApi(String apiPath, Object requestData) {
        String companyCode = currentCompanyCode.get();
        String baseUrl = companyApiConfigService.getApiBaseUrl(companyCode);
        String fullUrl = baseUrl + apiPath;
        
        // HTTP 调用实现
        return restTemplate.postForObject(fullUrl, requestData, String.class);
    }
}
```

### 6. 业务步骤实现

**步骤接口定义**：
```java
public interface BusinessStep {
    BusinessStepResult execute(BusinessStepRequest request);
    String getStepName();
    BusinessStepType getStepType();
}
```

**库存入库步骤实现**：
```java
@Component
public class InventoryReceiptStepV3 implements BusinessStep {
    
    @Override
    public BusinessStepResult execute(BusinessStepRequest request) {
        try {
            // 1. 数据准备和验证
            validateRequest(request);
            
            // 2. 执行业务逻辑
            String result = systemApiClient
                .withCompany(request.getCompanyCode())
                .postToSystemApi("/api/inventory/receipt", request.getData());
            
            // 3. 处理结果
            return BusinessStepResult.success(result);
            
        } catch (Exception e) {
            logger.error("库存入库步骤执行失败", e);
            return BusinessStepResult.failure(e.getMessage());
        }
    }
    
    @Override
    public String getStepName() {
        return "InventoryReceiptV3";
    }
    
    @Override
    public BusinessStepType getStepType() {
        return BusinessStepType.INVENTORY_RECEIPT;
    }
}
```

## 数据库设计

### 核心表结构

| 表名 | 用途 | 关键字段 |
|------|------|----------|
| T_FLOW_ORDER | 业务流转单主表 | flowOrderId, baseOrderId, baseBusinessType |
| T_FLOW_ORDER_INFO | 流转单详情表 | infoId, flowOrderId, companyCode, stepStatus |
| T_FLOW_NODE | 流程节点配置表 | flowNodeId, traderId, nodeLevel |
| T_WORKFLOW_CHECKPOINT | 工作流检查点表 | checkpointId, workflowId, status |
| T_WORKFLOW_RECOVERY | 工作流恢复表 | recoveryId, workflowId, recoveryData |

### 数据访问层

**MyBatis Mapper 示例**：
```java
@Mapper
public interface TemporalFlowOrderMapper {
    
    @Select("SELECT * FROM T_FLOW_ORDER WHERE flowOrderId = #{flowOrderId}")
    FlowOrderEntity selectByPrimaryKey(Integer flowOrderId);
    
    @Insert("INSERT INTO T_FLOW_ORDER (baseOrderId, baseBusinessType, createTime) " +
            "VALUES (#{baseOrderId}, #{baseBusinessType}, #{createTime})")
    @Options(useGeneratedKeys = true, keyProperty = "flowOrderId")
    int insert(FlowOrderEntity record);
    
    List<FlowOrderEntity> selectPendingOrders(@Param("businessType") String businessType,
                                            @Param("status") String status);
}
```

## 异常处理和监控

### 异常处理体系

**业务异常定义**：
```java
public class BusinessProcessException extends RuntimeException {
    private String errorCode;
    private BusinessStepType stepType;
    private String companyCode;
    
    public BusinessProcessException(String errorCode, String message, 
                                  BusinessStepType stepType, String companyCode) {
        super(message);
        this.errorCode = errorCode;
        this.stepType = stepType;
        this.companyCode = companyCode;
    }
}
```

**错误分类器**：
```java
@Component
public class ErrorClassifier {
    
    public boolean isRetryable(Throwable error) {
        if (error instanceof BusinessProcessException) {
            BusinessProcessException bpe = (BusinessProcessException) error;
            return isRetryableErrorCode(bpe.getErrorCode());
        }
        
        if (error instanceof ConnectException || error instanceof SocketTimeoutException) {
            return true;
        }
        
        return false;
    }
    
    private boolean isRetryableErrorCode(String errorCode) {
        // 定义可重试的错误码
        return Arrays.asList("NETWORK_ERROR", "TIMEOUT", "SERVICE_UNAVAILABLE")
                    .contains(errorCode);
    }
}
```

### 通知服务

**通知实现**：
```java
@Component
public class TemporalNotificationService {
    
    public void sendWorkflowCompletionNotification(String workflowId, String status) {
        NotificationContext context = NotificationContext.builder()
            .workflowId(workflowId)
            .status(status)
            .timestamp(System.currentTimeMillis())
            .level(NotificationLevel.INFO)
            .build();
            
        sendNotification(context);
    }
    
    public void sendWorkflowFailureAlert(String workflowId, String errorMessage) {
        NotificationContext context = NotificationContext.builder()
            .workflowId(workflowId)
            .status("FAILED")
            .message(errorMessage)
            .timestamp(System.currentTimeMillis())
            .level(NotificationLevel.ERROR)
            .build();
            
        sendAlert(context);
    }
}
```

## 定时任务集成

### XXL-Job 任务

**多公司业务任务**：
```java
@Component
public class MultiCompanyBusinessJob {
    
    @Autowired
    private WorkflowOrchestrationService workflowService;
    
    @XxlJob("multiCompanyBusinessSyncJob")
    public void execute() {
        logger.info("开始执行多公司业务同步任务");
        
        try {
            // 查询待处理的业务单据
            List<FlowOrderEntity> pendingOrders = getPendingOrders();
            
            for (FlowOrderEntity order : pendingOrders) {
                // 启动工作流处理
                CompanyBusinessRequest request = buildBusinessRequest(order);
                workflowService.startMultiCompanyWorkflow(request);
            }
            
            logger.info("多公司业务同步任务执行完成，处理单据数量: {}", pendingOrders.size());
            
        } catch (Exception e) {
            logger.error("多公司业务同步任务执行失败", e);
        }
    }
}
```

### 流转单信息生成任务

```java
@Component
public class FlowOrderInfoGenerationJob {
    
    @Autowired
    private FlowOrderInfoGenerationService generationService;
    
    @XxlJob("flowOrderInfoGenerationJob")
    public void execute() {
        logger.info("开始执行流转单信息生成任务");
        
        try {
            generationService.generateFlowOrderInfo();
            logger.info("流转单信息生成任务执行完成");
        } catch (Exception e) {
            logger.error("流转单信息生成任务执行失败", e);
        }
    }
}
```

## 开发指南

### 创建新的 Activity

1. **定义 Activity 接口**：
```java
@ActivityInterface
public interface YourBusinessActivity {
    @ActivityMethod
    YourBusinessResult processYourBusiness(YourBusinessRequest request);
}
```

2. **实现 Activity**：
```java
@Component
public class YourBusinessActivityImpl implements YourBusinessActivity {
    
    @Override
    public YourBusinessResult processYourBusiness(YourBusinessRequest request) {
        // 业务逻辑实现
        return YourBusinessResult.success();
    }
}
```

3. **在 Worker 中注册**：
```java
worker.registerActivitiesImplementations(new YourBusinessActivityImpl());
```

### 创建新的工作流

1. **定义工作流接口**：
```java
@WorkflowInterface
public interface YourWorkflow {
    @WorkflowMethod
    YourResult execute(YourRequest request);
    
    @QueryMethod
    String getStatus();
}
```

2. **实现工作流**：
```java
@Component
public class YourWorkflowImpl implements YourWorkflow {
    
    private final YourActivity activity = Workflow.newActivityStub(
        YourActivity.class,
        ActivityOptions.newBuilder()
            .setScheduleToCloseTimeout(Duration.ofMinutes(10))
            .build()
    );
    
    @Override
    public YourResult execute(YourRequest request) {
        return activity.processYourBusiness(request);
    }
}
```

### 使用通用轮询框架

```java
// API 轮询配置
UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
    .checkerType(CheckerType.API)
    .apiConfig(ApiConfig.builder()
        .url("http://company.com/api/status")
        .method("GET")
        .headers(Map.of("Authorization", "Bearer token"))
        .build())
    .condition(CompletionCondition.builder()
        .field("status")
        .expectedValue("COMPLETED")
        .build())
    .timeout(Duration.ofMinutes(30))
    .pollInterval(Duration.ofSeconds(10))
    .build();

// 执行轮询
UniversalPollingResult result = universalPollingActivity.poll(pollingRequest);
```

## 配置管理

### 核心配置参数

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| temporal.server.host | localhost | Temporal 服务器地址 |
| temporal.server.port | 7233 | Temporal 服务器端口 |
| temporal.namespace | default | 工作流命名空间 |
| temporal.worker.threads | 10 | Worker 线程数 |
| temporal.taskQueue.multiCompany | erp-multi-company-queue | 任务队列名称 |

### Apollo 配置集成

```java
@Component
@RefreshScope
public class TemporalProperties {
    
    @Value("${temporal.server.host:localhost}")
    private String serverHost;
    
    @Value("${temporal.server.port:7233}")
    private int serverPort;
    
    @Value("${temporal.namespace:default}")
    private String namespace;
    
    // getters and setters...
}
```

## 监控和日志

### 执行日志记录

```java
@Component
public class WorkflowExecutionLogger {
    
    public void logWorkflowStart(String workflowId, String businessType) {
        logger.info("工作流启动: workflowId={}, businessType={}", workflowId, businessType);
    }
    
    public void logActivityExecution(String activityName, String companyCode, String result) {
        logger.info("Activity执行: activity={}, company={}, result={}", 
                   activityName, companyCode, result);
    }
    
    public void logWorkflowCompletion(String workflowId, Duration executionTime) {
        logger.info("工作流完成: workflowId={}, executionTime={}ms", 
                   workflowId, executionTime.toMillis());
    }
}
```

### 性能监控

```java
@Component
public class WorkflowMetrics {
    
    private final MeterRegistry meterRegistry;
    
    public void recordWorkflowExecution(String workflowType, Duration duration, boolean success) {
        Timer.builder("workflow.execution.time")
            .tag("type", workflowType)
            .tag("success", String.valueOf(success))
            .register(meterRegistry)
            .record(duration);
    }
    
    public void recordActivityExecution(String activityType, String companyCode, 
                                      Duration duration, boolean success) {
        Timer.builder("activity.execution.time")
            .tag("type", activityType)
            .tag("company", companyCode)
            .tag("success", String.valueOf(success))
            .register(meterRegistry)
            .record(duration);
    }
}
```

## 部署和运维

### 依赖服务
- **Temporal Server**: 需要独立部署的工作流服务器
- **MySQL**: 业务数据库和 Temporal 数据库
- **XXL-Job**: 定时任务调度平台
- **Apollo**: 配置中心（可选）

### 环境配置
```properties
# Temporal 配置
temporal.server.host=temporal-server.company.com
temporal.server.port=7233
temporal.namespace=erp-production

# 数据库配置
spring.datasource.url=*******************************************
spring.datasource.username=erp_temporal
spring.datasource.password=${DB_PASSWORD}

# XXL-Job 配置
xxl.job.admin.addresses=http://xxl-job-admin:8080/xxl-job-admin
xxl.job.executor.appname=erp-temporal-executor
```

## 故障排除

### 常见问题

1. **工作流连接失败**
   - 检查 Temporal Server 是否正常运行
   - 验证网络连接和端口配置
   - 检查命名空间配置是否正确

2. **Activity 执行超时**
   - 调整 Activity 超时配置
   - 检查目标系统响应时间
   - 优化业务逻辑性能

3. **数据同步失败**
   - 检查目标系统接口状态
   - 验证数据格式和参数
   - 查看详细错误日志

### 日志级别配置
```properties
# Temporal 相关日志
logging.level.io.temporal=INFO
logging.level.com.vedeng.temporal=DEBUG

# 业务流程日志
logging.level.com.vedeng.temporal.workflow=INFO
logging.level.com.vedeng.temporal.activity=DEBUG
```

---

**注意**：本模块专注于分布式工作流编排和跨公司业务流程自动化。开发时应遵循 Temporal 最佳实践，确保工作流的可靠性和可观测性。