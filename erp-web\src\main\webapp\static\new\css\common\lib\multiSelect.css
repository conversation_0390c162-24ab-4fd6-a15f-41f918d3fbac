.multi-select-wrap {
  color: #333;
  position: relative;
}
.multi-select-wrap .multi-select-label {
  display: flex;
  align-items: center;
  border: 1px solid #BABFC2;
  border-radius: 3px;
  padding-left: 10px;
  height: 31px;
  cursor: pointer;
}
.multi-select-wrap .multi-select-label .icon-down {
  padding: 0 5px;
  transition: transform 0.22s ease;
  color: #666;
}
.multi-select-wrap .multi-select-label .icon-error2 {
  padding: 0 5px;
  display: none;
  color: #666;
  cursor: pointer;
}
.multi-select-wrap .multi-select-label .icon-error2:hover {
  color: #333;
}
.multi-select-wrap .multi-select-label.on-select:hover .icon-down {
  display: none;
}
.multi-select-wrap .multi-select-label.on-select:hover .icon-error2 {
  display: block;
}
.multi-select-wrap .multi-select-label .multi-select-label-txt {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  height: 22px;
  overflow: hidden;
  line-height: 22px;
}
.multi-select-wrap .multi-select-label .multi-select-label-txt.placeholder {
  color: #999;
}
.multi-select-wrap .multi-select-label .multi-select-label-txt .select-label-item {
  display: flex;
  height: 22px;
  align-items: center;
  border: 1px solid #E1E5E8;
  background: #F5F7FA;
  padding-left: 5px;
  border-radius: 3px;
  margin-right: 5px;
  line-height: 1.5;
}
.multi-select-wrap .multi-select-label .multi-select-label-txt .select-label-item.item-num {
  padding-right: 5px;
}
.multi-select-wrap .multi-select-label .multi-select-label-txt .select-label-item .icon-delete {
  width: 22px;
  height: 22px;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.multi-select-wrap .multi-select-label .multi-select-label-txt .select-label-item .icon-delete:hover {
  color: #09f;
}
.multi-select-wrap .multi-select-label:hover {
  border-color: #969B9E;
}
.multi-select-wrap .multi-select-label.small {
  height: 26px;
}
.multi-select-wrap .multi-select-label.small .multi-select-label-txt {
  height: 20px;
}
.multi-select-wrap .multi-select-label.small .multi-select-label-txt .select-label-item {
  height: 20px;
}
.multi-select-wrap .multi-select-label.open {
  border-color: #09f;
}
.multi-select-wrap .multi-select-label.open .icon-down {
  transform: rotate(180deg);
}
.multi-select-wrap .multi-select-label.open.on-select .icon-down {
  display: none;
}
.multi-select-wrap .multi-select-label.open.on-select .icon-error2 {
  display: block;
}
.multi-select-wrap .multi-select-drop {
  position: absolute;
  background: #fff;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
  padding: 5px 0;
  min-width: 100%;
  border: 1px solid #BABFC2;
  border-radius: 3px;
  display: none;
}
.multi-select-wrap .multi-select-drop .multi-select-list {
  max-height: 330px;
  overflow: auto;
}
.multi-select-wrap .multi-select-drop .multi-select-list .multi-select-option {
  height: 33px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.multi-select-wrap .multi-select-drop .multi-select-list .multi-select-option:hover {
  background: #e6ecf2;
}
.multi-select-wrap .multi-select-drop .multi-select-list .multi-select-option .vd-icon {
  font-size: 16px;
  margin-right: 5px;
}
.multi-select-wrap .multi-select-drop .multi-select-list .multi-select-option .vd-icon.icon-checkbox1 {
  color: #999;
}
.multi-select-wrap .multi-select-drop .multi-select-list .multi-select-option .vd-icon.icon-checkbox2 {
  color: #09f;
  display: none;
}
.multi-select-wrap .multi-select-drop .multi-select-list .multi-select-option .multi-select-option-txt {
  flex: 1;
}
.multi-select-wrap .multi-select-drop .multi-select-list .multi-select-option.checked .vd-icon.icon-checkbox1 {
  display: none;
}
.multi-select-wrap .multi-select-drop .multi-select-list .multi-select-option.checked .vd-icon.icon-checkbox2 {
  display: block;
}
.multi-select-wrap .multi-select-drop .multi-select-list .multi-select-option.checked .multi-select-option-txt {
  color: #09f;
}
