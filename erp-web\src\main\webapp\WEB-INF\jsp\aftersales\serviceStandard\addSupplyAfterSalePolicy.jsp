<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="维护供应商售后政策" scope="application" />
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    function save() {

        if($("input[type='file']").length > 10){
            layer.alert("文件上传的个数不能超过10个");
            return;
        }

        if(!validatorValue()){
            return;
        }

        $("#addForm").submit();
    }
    
    function validatorValue() {

        if($("#traderId").val() == ""){
            layer.alert("供应商名称不能为空");
            return false;
        }

        if($("input[name='serviceProviderType']:checked").val() == undefined){
            layer.alert("请选择服务提供商");
            return false;
        }

        var exsit = supplyPolicyIsExsit($("#traderId").val(),'${skuNo}');
        if(exsit){
            layer.alert("该供应商的售后政策已经存在，请选择其他供应商");
            return;
        }


        if('${spuType}' == '316' || '${spuType}' == '1008'){
            //如果产品可以安装
            if('${skuGenerate.isInstallable}' == 1){
                var installPolicyInstallTypeValue = $("input[name='installPolicyInstallType']:checked").val();
                if(installPolicyInstallTypeValue == undefined){
                    layer.alert("请选择是否提供上门安装服务");
                    return false;
                }

                if(installPolicyInstallTypeValue == 0 || installPolicyInstallTypeValue == 1){
                    if($("input[name='installPolicyFreeRemoteInstall']:checked").val()== undefined){
                        layer.alert("请选择是否免费远程指导装机");
                        return false;
                    }
                }

            }


            //如果选择的保修
            if($("input[name='guaranteePolicyIsGuarantee']:checked").val() == 1){
                //保修方式必填
                if($("input[name='guaranteePolicyGuaranteeType']:checked").val() == undefined){
                    layer.alert("请选择保修方式");
                    return false;
                }

                if($("#guaranteePolicyHostGuaranteePeriodNum").val() == ""){
                    layer.alert("请输入主机保修期");
                    return false;
                }

                if($("input[name='guaranteePolicyCycleCaltype']:checked").val() == undefined){
                    layer.alert("请选择周期计算方式");
                    return false;
                }
                if($("input[name='guaranteePolicyArea']:checked").val() == undefined){
                    layer.alert("请选择保修区域");
                    return false;
                }

            }
        }

        if('${spuType}' == '316' || '${spuType}' == '1008' || '${spuType}' == '317' || '${spuType}' == '318'){
            //支持退货
            if($("input[name='returnPolicySupportReturn']:checked").val() == 1){
                if($("#returnPolicyCondition").val() == ""){
                    layer.alert("请输入退货条件");
                    return false;
                }
            }

            //支持换货
            if($("input[name='exchangePolicySupportChange']:checked").val() == 1){
                if($("#exchangePolicyExchangeContition").val() == ""){
                    layer.alert("请输入换货条件");
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 校验数字框输入合法性
     */
    function checkValue(obj) {
        if ($(obj).val() != undefined && $(obj).val() != '') {
            var reg = /^\d+(?=\.{0,1}\d+$|$)/;
            if (!reg.test($(obj).val())) {
                layer.alert('请输入数字!');
                $(obj).val('');
            }
        }
    }
    
    function toServiceContract(span){

        if($("#traderId").val() == ""){
            layer.alert("请先选择供应商");
            return;
        }

        var tabTitleObj = JSON.parse($(span).attr("tabTitle"));

        tabTitleObj.link = "${pageContext.request.contextPath}/trader/supplier/baseinfo.do?traderId=" + $("#traderId").val();

        $(span).attr("tabTitle",JSON.stringify(tabTitleObj));

        openTab(span);
    }

    function openTab(span){
        var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
        var newPageId;
        var tabTitle = $(span).attr('tabTitle');
        if (typeof(tabTitle) == 'undefined') {
            alert('参数错误');
        } else {
            tabTitle = $.parseJSON(tabTitle);
        }
        var id = tabTitle.num;
        // var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
        var name = tabTitle.title;
        var uri = tabTitle.link;
        var closable = 1;
        var item = { 'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false };
        self.parent.closableTab.addTab(item);
        self.parent.closableTab.resizeMove();
        $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    }

    function supplyPolicyIsExsit(traderId,skuNo){

        var exsit = false;

        $.ajax({
            url:page_url+'/aftersale/serviceStandard/supplyPolicyIsExsit.do',
            data:{"traderId":traderId,"skuNo":skuNo},
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data)
            {
                if(data.code==1){
                    exsit = true;
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

        return exsit;
    }

    function serviceProviderTypeChange(serviceProviderType){
        //如果选择的是原厂服务 请求下后台看是否有对应的原厂服务
        if(serviceProviderType == '1'){
            $.ajax({
                async:true,
                url:page_url+'/aftersale/serviceStandard/haveFactoryServicesPolicy.do',
                data:{"skuNo":'${skuNo}'},
                type:"POST",
                dataType : "json",
                success:function(data){
                    if(data.code==0){

                        index = layer.confirm("该SKU已有原厂服务的售后政策，是否需要覆盖？", {
                            btn : [ '确定', '取消' ]
                            //按钮
                        }, function() {

                            if($("#traderId").val() == ""){
                                layer.alert("供应商名称不能为空");
                                return false;
                            }

                            var exsit = supplyPolicyIsExsit($("#traderId").val(),'${skuNo}');
                            if(exsit){
                                layer.alert("该供应商的售后政策已经存在，请选择其他供应商");
                                return;
                            }

                            $.ajax({
                                url:page_url+'/aftersale/serviceStandard/copyOrginalFactorySupplyPolicy.do',
                                data:{"traderId":$("#traderId").val(),"skuNo":"${skuNo}"},
                                type:"POST",
                                dataType : "json",
                                async: false,
                                success:function(data)
                                {
                                    debugger;
                                    if(data.code==0){
                                        layer.close(index);
                                        window.location.href= page_url + "/aftersale/serviceStandard/toModifySupplyPolicyPage.do?supplyPolicyId=" + data.data;
                                    }else{
                                        layer.alert(data.message);
                                    }
                                },
                                error:function(data){
                                    if(data.status ==1001){
                                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                                    }
                                }
                            });
                        }, function() {

                        });
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }
    }


    function installPolicyInstallTypeChange(installTypeValue) {

        var trs = $("tr[class='installPolicy']");
        for(i = 0; i < trs.length; i++){

            if(installTypeValue == 2){
                $(trs[i]).hide();
            }else{
                $(trs[i]).show();
            }
        }
    }

    function technicalDirectSupplyMaintainChange(technicalDirectValue){
        var trs = $("tr[class='technicalDirect']");
        for(i = 0; i < trs.length; i++){
            if(technicalDirectValue == 0){
                $(trs[i]).hide();
            }else{
                $(trs[i]).show();
            }
        }
    }
    
    function guaranteePolicyIsGuaranteeChange(guaranteePolicyIsGuaranteeValue) {
        var trs = $("tr[class='guaranteePolicy']");
        for(i = 0; i < trs.length; i++){
            if(guaranteePolicyIsGuaranteeValue == 0){
                $(trs[i]).hide();
            }else{
                $(trs[i]).show();
            }
        }
    }

    function returnPolicySupportReturnChange(returnPolicySupportReturnValue) {
        var trs = $("tr[class='returnPolicy']");
        for(i = 0; i < trs.length; i++){
            if(returnPolicySupportReturnValue == 0){
                $(trs[i]).hide();
            }else{
                $(trs[i]).show();
            }
        }
    }

    function exchangePolicySupportChangge(exchangePolicySupportReturnValue) {
        var trs = $("tr[class='exchangePolicy']");
        for(i = 0; i < trs.length; i++){
            if(exchangePolicySupportReturnValue == 0){
                $(trs[i]).hide();
            }else{
                $(trs[i]).show();
            }
        }
    }

    function returnPolicyNeedIdentifyChange(checkbox,tr) {
        //免费安装
        if(checkbox.value == 1){
            $("#"+tr).show();
        }else{
            $("#"+tr).hide();
        }
    }
    function setSupplyModelInfo(modelstring,model) {
        $(":input[ name = '"+modelstring+"' ]").val(model);
    }
    function setSupplyModelTime(numstring,unitstring,num,unit) {
        $(":input[ name = '"+numstring+"' ]").val(num);
        $("select[ name = '"+unitstring+"' ]").val(unit).attr("selected","selected");
    }
</script>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/aftersale/serviceStandard/addSupplyAfterSalePolicy.do">
            <input type="hidden" name="skuNo" value="${skuNo}"/>
            <ul class="payplan">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">基础售后内容</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>类型</td>
                                    <td>供应商售后政策</td>
                                </tr>
                                <tr>
                                    <td>订货号/商品名称</td>
                                    <td>${skuGenerate.skuNo}/${skuGenerate.showName}</td>
                                </tr>
                                <tr>
                                    <td><font color="red">*</font>供应商名称</td>
                                    <td align="center">
                                        <div class="f_center ">
                                            <div class="form-blanks">
                                                <span class="none" id="name"></span>
                                                <input type="text" placeholder="请输入供应商名称" class="input-middle" name="searchTraderName" id="searchTraderName" value="${traderName}">
                                                <label class="bt-bg-style bg-light-blue bt-small" onclick="searchSupplier();" id="errorMes" style='margin-top:-3px;'>搜索</label>
                                                <label class="bt-bg-style bg-light-blue bt-small none" onclick="research();" id="research" style='margin-top:-3px;'>重新搜索</label>
                                                <span style="display:none;">
                                                    <!-- 弹框 -->
                                                    <div class="title-click nobor  pop-new-data" id="popSupplier"></div>
                                                </span>
                                                <input type="hidden" id="traderId" name="traderId" value="${traderId}"/>
                                                <input type="hidden" id="traderName" name="traderName" value="${traderName}"/>
                                            </div>
                                            <div class="font-red none"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><font color="red">*</font>售后服务商</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="serviceProviderType" onchange="serviceProviderTypeChange(this.value)"> 原厂服务
                                                <input type="radio" value="2" name="serviceProviderType" onchange="serviceProviderTypeChange(this.value)"> 该供应商提供服务
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                1.原厂服务：指由原厂家提供售后，且售后政策与原厂家提供的售后政策一致；<br>
                                                2.该供应商提供服务：指由供应商自己安排提供售后服务，且供应商的售后政策可能与厂家售后政策有区别。
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                 <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">售后服务等级</div>
                        </div>
                        <table class="table  table-bordered   table-condensed table-centered">
                            <tbody>
                                <tr style="background:none">
                                    <td>维修资料</td>
                                    <td> 维修配附件  </td>
                                     <td style="width:50%">
                                        <input type="radio" value="1" name="repairPartsAccessory"    > 有配附件
                                        <input type="radio" value="2" name="repairPartsAccessory"    > 无配附件
                                     </td>
                                </tr>
                                 <tr style="background:none">
                                    <td rowspan="2">培训支持</td>
                                    <td> 安调培训
                                    </td>
                                     <td>
                                      <input type="radio" value="1" name="installTraining"    > 已培训
                                        <input type="radio" value="2" name="installTraining"    > 未培训
                                     </td>
                                </tr>

                                <tr style="background:none">

                                    <td> 维修培训  </td>
                                     <td>
                                        <input type="radio" value="1" name="repairTraining"  > 已培训
                                        <input type="radio" value="2" name="repairTraining"   > 未培训

                                     </td>
                                </tr>
                                <tr style="background:none">
                                    <td>服务人员等级</td>
                                    <td> 服务人员等级  </td>
                                     <td>
                                            <select style="width: 100px;" name="servicePersonnelLevel">
                                                    <option value="0"  >请选择</option>
                                                    <option value="1"  >S级</option>
                                                    <option value="2"  >A级及以上</option>
                                                    <option value="3"  >B级及以上</option>
                                                    <option value="4" >C级及以上</option>
                                                    <option value="5"  >D级及以上</option>
                                            </select>
                                     </td>
                                </tr>


                            </tbody>
                        </table>
                    </div>
                </li>

                <c:if test="${spuType == '316' or spuType == '1008'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">安装政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td>产品是否可安装</td>
                                <td>
                                    <div class="customername pos_rel">
                                            <span>
                                                <c:if test="${skuGenerate.isInstallable == 1}">可安装</c:if>
                                                <c:if test="${skuGenerate.isInstallable == 0}">不可安装</c:if>
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                        <div class="pos_abs customernameshow">
                                            该字段表示商品本身是否具体安装特性，如口罩为“不可安装”,（若信息有误，请联系采购人员到SKU详情页修改。）
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <c:if test="${skuGenerate.isInstallable == 1}">
                                <tr>
                                    <td><font color="red">*</font>是否提供上门安装服务</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="0" name="installPolicyInstallType" onchange="installPolicyInstallTypeChange(this.value)"> 收费安装
                                                <input type="radio" value="1" name="installPolicyInstallType" onchange="installPolicyInstallTypeChange(this.value)"> 免费安装
                                                <input type="radio" value="2" name="installPolicyInstallType" onchange="installPolicyInstallTypeChange(this.value)"> 不提供安装
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                1.收费安装：指贝登提供安装服务，但需要收取安装费（另：偏远区域需要另外收取长途费）<br>
                                                2.免费安装：指贝登提供安装服务，且不收取安装费（另：偏远区域需要另外收取长途费）<br>
                                                3.不提供安装：指贝登不提供安装服务
                                            </div>
                                        </div>

                                    </td>
                                </tr>

                                <tr class="installPolicy" style="display: none">
                                    <td>安装区域</td>
                                    <td>
                                        <input type="text" name="installPolicyInstallArea" class="input-middle" placeholder="比如：偏远地区除外、仅江苏等"/>
                                    </td>
                                </tr>
                                <%--<tr class="installPolicy" style="display: none">
                                    <td>安装费</td>
                                    <td>
                                        <input type="text" name="installPolicyInstallFee" class="input-middle"/>
                                    </td>
                                </tr>--%>
                                <tr class="installPolicy" style="display: none">
                                    <td>是否需要装机资质</td>
                                    <td>
                                        <div class="customername pos_rel">
                                        <span>
                                            <input type="radio" value="1" name="installPolicyHaveInstallationQualification"> 是
                                            <input type="radio" value="0" name="installPolicyHaveInstallationQualification"> 否
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                        </span>
                                            <div class="pos_abs customernameshow">
                                                是否必须为供应商或厂家认证过的工程师才能装机
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="installPolicy" style="display: none">
                                    <td><font color="red">*</font>是否免费远程指导装机</td>
                                    <td>
                                        <div class="customername pos_rel">
                                        <span>
                                            <input type="radio" value="1" name="installPolicyFreeRemoteInstall"> 是
                                            <input type="radio" value="0" name="installPolicyFreeRemoteInstall"> 否
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                        </span>
                                            <div class="pos_abs customernameshow">
                                                可提供对产品的远程装机指导，包含电话指导，视频指导，视频资料提供等
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="installPolicy" style="display: none">
                                    <td>响应时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                    <span>
                                        <input type="text" name="installPolicyResponseTimeNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                        <select style="width: 100px;" name="installPolicyResponseTimeUnit">
                                            <option value="小时">小时</option>
                                            <option value="天">天</option>
                                            <option value="月">月</option>
                                            <option value="年">年</option>
                                        </select>
                                        <i class="iconbluesigh ml4 contorlIcon"></i>
                                        <span class="bt-small bt-bg-style bg-light-blue"
                                              onclick="setSupplyModelTime('installPolicyResponseTimeNum','installPolicyResponseTimeUnit',2,'小时');"
                                              style="margin-left: 10px;margin-top: 5px">2小时</span>
                                    </span>
                                            <div class="pos_abs customernameshow">
                                                XX时间内与客户联系
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="installPolicy" style="display: none">
                                    <td>上门时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                    <span>
                                        <input type="text" name="installPolicyVisitTimeNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                        <select style="width: 100px;" name="installPolicyVisitTimeUnit">
                                            <option value="小时">小时</option>
                                            <option value="天">天</option>
                                            <option value="月">月</option>
                                            <option value="年">年</option>
                                        </select>
                                        <i class="iconbluesigh ml4 contorlIcon"></i>
                                        <span class="bt-small bt-bg-style bg-light-blue"
                                              onclick="setSupplyModelTime('installPolicyVisitTimeNum','installPolicyVisitTimeUnit',72,'小时');"
                                              style="margin-left: 10px;margin-top: 5px">72小时</span>
                                    </span>
                                            <div class="pos_abs customernameshow">
                                                在客户需求上门的XX时间内上门服务。
                                                一旦与客户约定具体上门时间，则需要在约定的时间上门
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="installPolicy" style="display: none">
                                    <td>安装时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                    <span>
                                        <input type="text" name="installPolicyInstallTimeNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                        <select style="width: 100px;" name="installPolicyInstallTimeUnit">
                                            <option value="小时">小时</option>
                                            <option value="天">天</option>
                                            <option value="月">月</option>
                                            <option value="年">年</option>
                                        </select>
                                        <i class="iconbluesigh ml4 contorlIcon"></i>
                                        <span class="bt-small bt-bg-style bg-light-blue"
                                              onclick="setSupplyModelTime('installPolicyInstallTimeNum','installPolicyInstallTimeUnit',72,'小时');"
                                              style="margin-left: 10px;margin-top: 5px">72小时</span>
                                    </span>
                                            <div class="pos_abs customernameshow">
                                                通常是当天（24）小时内装机完毕，某些产品装机时间可能超24小时，如大型设备
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </c:if>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <c:if test="${spuType == '316' or spuType == '1008'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">技术指导</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td><font color="red">*</font>是否提供技术维修指导</td>
                                <td>
                                    <input type="radio" value="1" name="technicalDirectSupplyMaintain" checked onchange="technicalDirectSupplyMaintainChange(this.value)"> 提供
                                    <input type="radio" value="0" name="technicalDirectSupplyMaintain" onchange="technicalDirectSupplyMaintainChange(this.value)"> 不提供
                                </td>
                            </tr>
                            <tr class="technicalDirect">
                                <td>响应时效</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="technicalDirectResponseTimeNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                            <select style="width: 100px;" name="technicalDirectResponseTimeUnit">
                                                <option value="小时">小时</option>
                                                <option value="天">天</option>
                                                <option value="月">月</option>
                                                <option value="年">年</option>
                                            </select>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                            <span class="bt-small bt-bg-style bg-light-blue"
                                                  onclick="setSupplyModelTime('technicalDirectResponseTimeNum','technicalDirectResponseTimeUnit',2,'小时');"
                                                  style="margin-left: 10px;margin-top: 5px">2小时</span>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            XX时间内与客户联系
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="technicalDirect">
                                <td>技术指导时效</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="technicalDirectEffectTimeNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                            <select style="width: 100px;" name="technicalDirectEffectTimeUnit">
                                                <option value="小时">小时</option>
                                                <option value="天">天</option>
                                                <option value="月">月</option>
                                                <option value="年">年</option>
                                            </select>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                            <span class="bt-small bt-bg-style bg-light-blue"
                                                  onclick="setSupplyModelTime('technicalDirectEffectTimeNum','technicalDirectEffectTimeUnit',24,'小时');"
                                                  style="margin-left: 10px;margin-top: 5px">24小时</span>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            技术指导的时间需要在客户需求的XX时间内
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <c:if test="${spuType == '316' or spuType == '1008'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">保修政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr >
                                    <td><font color="red">*</font>是否保修</td>
                                    <td>
                                        <input type="radio" value="1" name="guaranteePolicyIsGuarantee" checked onchange="guaranteePolicyIsGuaranteeChange(this.value)"> 是
                                        <input type="radio" value="0" name="guaranteePolicyIsGuarantee" onchange="guaranteePolicyIsGuaranteeChange(this.value)"> 否
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td><font color="red">*</font>保修方式</td>
                                    <td>
                                        <input type="checkbox" value="1" name="guaranteePolicyGuaranteeType"> 上门维修
                                        <input type="checkbox" value="2" name="guaranteePolicyGuaranteeType"> 寄送修
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td><font color="red">*</font>主机保修期</td>
                                    <td>
                                        <input type="text" id="guaranteePolicyHostGuaranteePeriodNum" name="guaranteePolicyHostGuaranteePeriodNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                        <select style="width: 100px;" name="guaranteePolicyHostGuaranteePeriodUnit">
                                            <option value="小时">小时</option>
                                            <option value="天">天</option>
                                            <option value="月" selected>月</option>
                                            <option value="年">年</option>
                                        </select>
                                        <span class="bt-small bt-bg-style bg-light-blue"
                                              onclick="setSupplyModelTime('guaranteePolicyHostGuaranteePeriodNum','guaranteePolicyHostGuaranteePeriodUnit',12,'月');"
                                              style="margin-left: 10px;margin-top: 5px">12月</span>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>配件保修期</td>
                                    <td>
                                        <input type="text" name="guaranteePolicyPartsGuaranteePeriodNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                        <select style="width: 100px;" name="guaranteePolicyPartsGuaranteePeriodUnit">
                                            <option value="小时">小时</option>
                                            <option value="天">天</option>
                                            <option value="月" selected>月</option>
                                            <option value="年">年</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td><font color="red">*</font>周期计算方式</td>
                                    <td>
                                        <input type="radio" value="1" name="guaranteePolicyCycleCaltype"> 出厂时间
                                        <input type="radio" value="2" name="guaranteePolicyCycleCaltype"> 客户签收时间
                                        <input type="radio" value="3" name="guaranteePolicyCycleCaltype"> 发票时间
                                        <input type="radio" value="4" name="guaranteePolicyCycleCaltype"> 安装时间
                                        <input type="radio" value="5" name="guaranteePolicyCycleCaltype"> 贝登入库时间
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td><font color="red">*</font>保修区域</td>
                                    <td>
                                        <input type="radio" value="1" name="guaranteePolicyArea"> 全国
                                        <input type="radio" value="2" name="guaranteePolicyArea"> 部分区域
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>保修区域说明</td>
                                    <td><input type="text" name="guaranteePolicyAreaComment" class="input-middle" placeholder="比如：偏远地区除外、仅江苏等"/>
                                        <span class="bt-small bt-bg-style bg-light-blue pos_rel" onclick="setSupplyModelInfo('guaranteePolicyAreaComment','参照供应商政策偏远地区除外');" style="margin-left: 10px;margin-top: 5px">参照供应商政策偏远地区除外</span>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>响应时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="text" name="guaranteePolicyResponseTimeNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                                <select style="width: 100px;" name="guaranteePolicyResponseTimeUnit">
                                                    <option value="小时">小时</option>
                                                    <option value="天">天</option>
                                                    <option value="月">月</option>
                                                    <option value="年">年</option>
                                                </select>
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                                <span class="bt-small bt-bg-style bg-light-blue"
                                                      onclick="setSupplyModelTime('guaranteePolicyResponseTimeNum','guaranteePolicyResponseTimeUnit',2,'小时');"
                                                      style="margin-left: 10px;margin-top: 5px">2小时</span>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                XX时间内与客户联系
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>上门时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="text" name="guaranteePolicyVisitTimeNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                                <select style="width: 100px;" name="guaranteePolicyVisitTimeUnit">
                                                    <option value="小时">小时</option>
                                                    <option value="天">天</option>
                                                    <option value="月">月</option>
                                                    <option value="年">年</option>
                                                </select>
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                                <span class="bt-small bt-bg-style bg-light-blue"
                                                      onclick="setSupplyModelTime('guaranteePolicyVisitTimeNum','guaranteePolicyVisitTimeUnit',72,'小时');"
                                                      style="margin-left: 10px;margin-top: 5px">72小时</span>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                预约上门的时间需要在客户需求上门的XX时间内。
                                                一旦与客户约定具体上门时间，则需要在约定的时间上门
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>维修时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="text" name="guaranteePolicyRepaireTimeNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                                <select style="width: 100px;" name="guaranteePolicyRepaireTimeUnit">
                                                    <option value="小时">小时</option>
                                                    <option value="天">天</option>
                                                    <option value="月">月</option>
                                                    <option value="年">年</option>
                                                </select>
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                                <span class="bt-small bt-bg-style bg-light-blue"
                                                      onclick="setSupplyModelTime('guaranteePolicyRepaireTimeNum','guaranteePolicyRepaireTimeUnit',72,'小时');"
                                                      style="margin-left: 10px;margin-top: 5px">72小时</span>
                                                <span class="bt-small bt-bg-style bg-light-blue"
                                                      onclick="setSupplyModelTime('guaranteePolicyRepaireTimeNum','guaranteePolicyRepaireTimeUnit',168,'小时');"
                                                      style="margin-left: 10px;margin-top: 5px">168小时</span>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                通常是当天（24）小时内维修完毕，某些产品维修时间超24小时，如大型设备
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="guaranteePolicy">
                                    <td>保修备注</td>
                                    <td><input type="text" name="guaranteePolicyRepaireComment" class="input-middle" placeholder="需要特殊情况的说明"/></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>

                <c:if test="${spuType == '316' or spuType == '1008' or spuType == '317' or spuType == '318'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">退货政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td><font color="red">*</font>是否支持退货</td>
                                <td>
                                    <input type="radio" value="1" name="returnPolicySupportReturn" checked onchange="returnPolicySupportReturnChange(this.value)"> 支持退货
                                    <input type="radio" value="0" name="returnPolicySupportReturn" onchange="returnPolicySupportReturnChange(this.value)"> 不支持退货
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td><font color="red">*</font>退货条件</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" id="returnPolicyCondition" name="returnPolicyCondition" class="input-middle" value="质量问题"/>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            列出可换货情况，或不可换货的情况
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td>是否需要鉴定</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="radio" value="1" name="returnPolicyNeedIdentify" onchange="returnPolicyNeedIdentifyChange(this,'returnPolicyTr')" checked> 是
                                            <input type="radio" value="0" name="returnPolicyNeedIdentify" onchange="returnPolicyNeedIdentifyChange(this,'returnPolicyTr')"> 否
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            是否需要厂家人员鉴定（判断）产品有无故障，符不符合退换机条件
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="returnPolicy" id="returnPolicyTr">
                                <td>鉴定方式</td>
                                <td>
                                    <input type="checkbox" value="0" name="returnPolicyIdentifyType"> 电话鉴定
                                    <input type="checkbox" value="1" name="returnPolicyIdentifyType"> 上门鉴定
                                    <input type="checkbox" value="2" name="returnPolicyIdentifyType"> 返厂鉴定
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td>退货期限</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="returnPolicyReturnPeriodNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                            <select style="width: 100px;" name="returnPolicyReturnPeriodUnit">
                                                <option value="小时">小时</option>
                                                <option value="天" selected>天</option>
                                                <option value="月">月</option>
                                                <option value="年">年</option>
                                            </select>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                            <span class="bt-small bt-bg-style bg-light-blue"
                                                  onclick="setSupplyModelTime('returnPolicyReturnPeriodNum','returnPolicyReturnPeriodUnit',7,'天');"
                                                  style="margin-left: 10px;margin-top: 5px">7天</span>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            客户若需要退货，需要在XX时间内提交诉求，超过这个实现不支持退货。
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td>周期计算方式</td>
                                <td>
                                    <input type="radio" value="1" name="returnPolicyCycleCaltyp"> 出厂时间
                                    <input type="radio" value="2" name="returnPolicyCycleCaltyp"> 客户签收时间
                                    <input type="radio" value="3" name="returnPolicyCycleCaltyp"> 发票时间
                                    <input type="radio" value="4" name="returnPolicyCycleCaltyp"> 安装时间
                                    <input type="radio" value="5" name="returnPolicyCycleCaltyp"> 贝登入库时间
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td>包装要求</td>
                                <td><input type="text" name="returnPolicyPackagingRequirements" class="input-middle" placeholder="包装污损、破损时如何处理"/>
                                    <span class="bt-small bt-bg-style bg-light-blue" onclick="setSupplyModelInfo('returnPolicyPackagingRequirements','无破损无污损');" style="margin-left: 10px;margin-top: 5px">无破损无污损</span>
                                </td>
                            </tr>
                            <tr class="returnPolicy">
                                <td>退货备注</td>
                                <td><input type="text" name="returnPolicyReturnComments" class="input-middle" placeholder="需要特殊情况的说明"/></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <c:if test="${spuType == '316' or spuType == '1008' or spuType == '317' or spuType == '318'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">换货政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td><font color="red">*</font>是否支持换货</td>
                                <td>
                                    <input type="radio" value="1" name="exchangePolicySupportChange" checked onchange="exchangePolicySupportChangge(this.value)"> 支持换货
                                    <input type="radio" value="0" name="exchangePolicySupportChange" onchange="exchangePolicySupportChangge(this.value)"> 不支持换货
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td><font color="red">*</font>换货条件</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" id="exchangePolicyExchangeContition" name="exchangePolicyExchangeContition" class="input-middle" value="质量问题"/>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            列出可换货情况，或不可换货的情况
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td>是否需要鉴定</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="radio" value="1" name="exchangePolicyNeedIdentify" onchange="returnPolicyNeedIdentifyChange(this,'exchangePolicyTr')" checked> 是
                                            <input type="radio" value="0" name="exchangePolicyNeedIdentify" onchange="returnPolicyNeedIdentifyChange(this,'exchangePolicyTr')"> 否
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            是否需要厂家人员鉴定（判断）产品有无故障，符不符合退换机条件
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="exchangePolicy" id="exchangePolicyTr">
                                <td>鉴定方式</td>
                                <td>
                                    <input type="checkbox" value="0" name="exchangePolicyIdentifyType"> 电话鉴定
                                    <input type="checkbox" value="1" name="exchangePolicyIdentifyType"> 上门鉴定
                                    <input type="checkbox" value="2" name="exchangePolicyIdentifyType"> 返厂鉴定
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td>换货期限</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <input type="text" name="exchangePolicyExchangePeriodNum" class="input-middle" placeholder="请输入" onblur="checkValue(this)"/>
                                            <select style="width: 100px;" name="exchangePolicyExchangePeriodUnit">
                                                <option value="小时">小时</option>
                                                <option value="天" selected>天</option>
                                                <option value="月">月</option>
                                                <option value="年">年</option>
                                            </select>
                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                            <span class="bt-small bt-bg-style bg-light-blue"
                                                  onclick="setSupplyModelTime('exchangePolicyExchangePeriodNum','exchangePolicyExchangePeriodUnit',14,'天');"
                                                  style="margin-left: 10px;margin-top: 5px">14天</span>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            客户若需要换货，需要在XX时间内提交诉求，超过这个实现不支持换货
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td>周期计算方式</td>
                                <td>
                                    <input type="radio" value="1" name="exchangePolicyCycleCaltyp"> 出厂时间
                                    <input type="radio" value="2" name="exchangePolicyCycleCaltyp"> 客户签收时间
                                    <input type="radio" value="3" name="exchangePolicyCycleCaltyp"> 发票时间
                                    <input type="radio" value="4" name="exchangePolicyCycleCaltyp"> 安装时间
                                    <input type="radio" value="5" name="exchangePolicyCycleCaltyp"> 贝登入库时间
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td>包装要求</td>
                                <td><input type="text" name="exchangePolicyPackagingRequirements" class="input-middle" placeholder="包装污损、破损时如何处理"/>
                                    <span class="bt-small bt-bg-style bg-light-blue" onclick="setSupplyModelInfo('exchangePolicyPackagingRequirements','无破损无污损');" style="margin-left: 10px;margin-top: 5px">无破损无污损</span>
                                </td>
                            </tr>
                            <tr class="exchangePolicy">
                                <td>换货备注</td>
                                <td><input type="text" name="exchangePolicyExchangeComments" class="input-middle" placeholder="需要特殊情况的说明"/></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <c:if test="${spuType == '316'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">保外政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>是否提供有偿维修</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="parolePolicySupportRepair"> 是
                                                <input type="radio" value="0" name="parolePolicySupportRepair"> 否
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                超过保修期之后，供应商能否提供有偿的维修服务
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>是否提供翻新服务</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="parolePolicySupportRenovation"> 是
                                                <input type="radio" value="0" name="parolePolicySupportRenovation"> 否
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                产品因某些原因（破损、污损、故障）等，不能二次销售时，供应商能否对贝登提供翻新服务
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>是否提供纸箱</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="parolePolicySupplyBox"> 可提供
                                                <input type="radio" value="0" name="parolePolicySupplyBox"> 不可提供
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                产品因某些原因包装破损、污损等，不能二次销售时，供应商能否对贝登提供原厂包装箱
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>是否提供附件</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <input type="radio" value="1" name="parolePolicySupplyAttachment"> 可提供
                                                <input type="radio" value="0" name="parolePolicySupplyAttachment"> 不可提供
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                产品附件因某些原因丢失，损坏等，不能二次销售时，供应商能否对贝登提供响应的附件
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <c:if test="${spuType == '316' or spuType == '1008' or spuType == '317' or spuType == '318'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">超期处理政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td>可否提供备用机</td>
                                <td>
                                    <input type="radio" value="1" name="overduePolicySupplyBackup"> 是
                                    <input type="radio" value="0" name="overduePolicySupplyBackup"> 否
                                </td>
                            </tr>
                            <tr>
                                <td>超期处理详情</td>
                                <td><input type="text" name="overduePolicyDetail" class="input-middle" placeholder="请输入"/></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">服务联系人</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td>

                                    <span class="edit-user" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"${pageContext.request.contextPath}/trader/supplier/baseinfo.do?traderId=${afterSaleSupplyPolicyDto.traderId}","title":"基本信息"}' onclick="toServiceContract(this)">去添加</span>

                                    <%--<a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
					                    "link":"./price/basePriceMaintain/purchasePriceTrace.do?skuId=${skuPriceChangeApplyDto.skuId}","title":"去添加"}'>去添加</a>--%>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">相关附件</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="f_left insertli">
                                            <div class="mb8">

                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_1" style="display: none;" onchange="uploadFile(this,1);"/>
                                                    <input type="text" class="input-middle" id="name_1"
                                                           readonly="readonly"
                                                           placeholder="请上传文件" name="attashmentName"
                                                           onclick="file_1.click();">
                                                    <input type="hidden" id="uri_1" name="attashmentUri">
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10" onclick="return $('#file_1').click();">浏览</label>
                                                </div>

                                                <!-- 上传成功出现 -->
                                                <i class="iconsuccesss ml7 none" id="img_icon_1"></i>
                                                <a href="" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_1">查看</a>
                                                <span class="font-red cursor-pointer mt4 none" onclick="del(1)" id="img_del_1">删除</span>
                                                <div class="clear"></div>
                                            </div>

                                            <div class="bt-border-style bt-small border-blue f_left" id="conadd1">
                                                <span class="f_left" onclick="con_add(1);">继续添加</span>
                                            </div>
                                            <br>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="left">
                                        <div>
                                            <lable style="float:left">- 可以上传各类文件，包括图片、word、excel、pdf等</lable>
                                            <br>
                                            <lable style="float:left"> - 单个最大限制10M,最多添加10个附件</lable>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container" style="text-align:center">
                            <span class="bt-small bt-bg-style bg-light-blue" onclick="save();"  style="margin-left: 100px;margin-top: 5px">保存</span>
                        </div>
                    </div>
                </li>
            </ul>
        </form>
    </div>
</div>
<script type="text/javascript">


    function delAttachment(obj) {
        var uri = $(obj).parent().find("a").attr("href");
        if (uri == '') {
            $(obj).parent().parent().remove();
        } else {
            index = layer.confirm("您是否确认该操作？", {
                btn: ['确定', '取消'] //按钮
            }, function () {
                var length = $(obj).parent().parent().parent().find("input[type='file']").length;
                if (length == 1) {
                    $(obj).parent().find("i").hide();
                    $(obj).parent().find("a").hide();
                    $(obj).parent().find("span").hide();
                    $(obj).parent().parent().parent().find("input[type='text']").val('');
                } else {
                    $(obj).parent().parent().remove();
                }
                layer.close(index);
            }, function () {
            });
        }
    }

    function RndNum(n){
        var rnd="";
        for(var i=0;i<n;i++)
            rnd+=Math.floor(Math.random()*10);
        return rnd;
    }

    //点击继续添加按钮
    function con_add(id) {

        desc = "请上传文件";
        var rndNum = RndNum(8);
        var html = '<div >' +
            '<div class="pos_rel f_left mb8">' +
            '<input type="file" class=" uploadErp"  name="lwfile" id="lwfile_' + id + '_' + rndNum + '" onchange="uploadFile(this, ' + id + ')">' +
            '<input type="text" class="input-middle" id="name_' + id + '_' + rndNum + '" readonly="readonly" placeholder="' + desc + '" name="name_' + id + '" onclick="lwfile_' + id + '_' + rndNum + '.click();" value ="">' +
            '<input type="hidden" class="input-middle mr5" id="uri_' + id + '_' + rndNum + '" name="uri_' + id + '" value="">' +
            '<label class="bt-bg-style bt-middle bg-light-blue " type="file" style="margin:0 17px 0 14px;">浏览</label>' +
            '</div>' +
            '<div class="f_left ">' +
            '<i class="iconsuccesss mt5 none" id="img_icon_' + id + '"></i>' +
            '<a href="" target="_blank" class="font-blue cursor-pointer mr10  ml10 mt4 none" id="img_view_' + id + '" style="margin:0 8px 0 12px">查看</a>' +
            '<span class="font-red cursor-pointer mt4" onclick="delAttachment(this)" id="img_del_' + id + '">删除</span>' +
            '</div>' +
            '<div class="clear "></div></div>';
        $("#conadd" + id).before(html);
    }

    function del(num) {
        checkLogin();
        index = layer.confirm("您是否确认该操作？", {
            btn: ['确定', '取消'] //按钮
        }, function () {
            $("#img_icon_" + num).hide();
            $("#img_view_" + num).hide();
            $("#img_del_" + num).hide();
            $("#name_" + num).val("");
            $("#uri_" + num).val("");
            $("#file_" + num).val("");
            layer.close(index);
        }, function () {
        });
    }

    function uploadFile(obj, num) {
        var imgPath = $(obj).val();
        if (imgPath == '' || imgPath == undefined) {
            return false;
        }
        var oldName = imgPath.substr(imgPath.lastIndexOf('\\') + 1);
        var domain = $("#domain").val();
        //判断上传文件的后缀名
        var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
        // 转换为小写格式
        strExtension = strExtension.toLowerCase();
        if (strExtension != 'jpg' && strExtension != 'gif' && strExtension != 'png' && strExtension != 'doc' && strExtension != 'docx' && strExtension != 'bmp' && strExtension != 'pdf' && strExtension != 'jpeg') {
            layer.alert("文件格式不正确");
            return;
        }

        var fileSize = 0;
        var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
        if (isIE && !obj.files) {
            var filePath = obj.value;
            var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
            var file = fileSystem.GetFile(filePath);
            fileSize = file.Size;
        } else {
            fileSize = obj.files[0].size;
        }
        fileSize = Math.round(fileSize / 1024 * 100) / 100; //单位为KB
        if (fileSize > 5120 * 2) {
            layer.alert("上传附件不得超过10M");
            return false;
        }
        $(obj).parent().parent().find("i").attr("class", "iconloading mt5").show();
        $(obj).parent().parent().find("a").hide();
        $(obj).parent().parent().find("span").hide();
        var objCopy1 = $(obj).parent();
        var objCopy2 = $(obj).parent().parent();
        $.ajaxFileUpload({
            url: page_url + '/fileUpload/ajaxFileUpload.do', //用于文件上传的服务器端请求地址
            secureuri: false, //一般设置为false
            fileElementId: $(obj).attr("id"), //文件上传控件的id属性  <input type="file" id="file" name="file" /> 注意，这里一定要有name值   //$("form").serialize(),表单序列化。指把所有元素的ID，NAME 等全部发过去
            dataType: 'json',//返回值类型 一般设置为json
            //服务器成功响应处理函数
            success: function (data) {
                if (data.code == 0) {

                    objCopy1.find("input[type='text']").val(data.fileName);
                    objCopy1.find("input[type='hidden']").val(data.filePath);
                    $("#domain").val(data.httpUrl);
                    objCopy2.find("i").attr("class", "iconsuccesss ml7").show();
                    objCopy2.find("a").attr("href", 'http://' + data.httpUrl + data.filePath).show();
                    objCopy2.find("span").show();
                    $("#name_" + num).removeClass("errorbor");
                } else {
                    layer.alert(data.message);
                }
            },
            //服务器响应失败处理函数
            error: function (data, status, e) {

                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                } else {
                    layer.alert(data.responseText);
                }

            }
        });
    }

    function searchSupplier(){
        checkLogin();
        var searchTraderName = $("#searchTraderName").val()==undefined?"":$("#searchTraderName").val();

        if($("#searchTraderName").val()==''){
            $("#searchTraderName").parent("div").siblings("div").removeClass("none").html("查询条件不允许为空");
            $("#searchTraderName").addClass("errorbor");
            return false;
        }else{
            $("#searchTraderName").parent("div").siblings("div").addClass("none");
            $("#searchTraderName").removeClass("errorbor");
        }

        var searchUrl = page_url+"/aftersale/serviceStandard/getSupplierByName.do?supplierName="+encodeURI(searchTraderName)+"&callbackFuntion=recieveTraderInfo";
        $("#popSupplier").attr('layerParams','{"width":"800px","height":"500px","title":"搜索供应商","link":"'+searchUrl+'"}');
        $("#popSupplier").click();
    }

    function research(){
        checkLogin();
        $("#searchTraderName").val("");
        $("#searchTraderName").show();
        $("#name").addClass("none");
        $("#errorMes").removeClass("none");
        $("#research").addClass("none");
    }

    function recieveTraderInfo(traderId,traderSupplierName) {
        $("#traderId").val(traderId);
        $("#traderName").val(traderSupplierName);
    }

</script>
<%@ include file="../../common/footer.jsp"%>