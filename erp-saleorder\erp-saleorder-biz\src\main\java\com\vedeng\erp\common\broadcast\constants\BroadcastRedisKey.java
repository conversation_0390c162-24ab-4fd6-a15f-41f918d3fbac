package com.vedeng.erp.common.broadcast.constants;

/**
 * redis-key设置
 * @ClassName:  BroadcastRedisKey   
 * @author: <PERSON>.yang
 * @date:   2025年6月12日 上午10:13:41    
 * @Copyright:
 */
public class  BroadcastRedisKey {
	
	/**播报梯度：示例：uat:deptId:userId:amountStepNum:1*/
	public static final String AMOUNT_STEP_NUM = "{0}:{1}:{2}:amountStepNum";
	
	/**播报图片记录：【0:redis环境；】【1：专属类型：0=非专属，1=个人，2=团队，3=项目；】【2：个人ID或者团队ID或者0（专属类型为0时）】,【picId为固定值】*/
	public static final String PIC_CONFIG_RECORD = "{0}:{1}:{2}:picId";
	
	/**记录已经播报的图片选择，用于排行榜展示【0:redis环境；】【1：播报项目：1=日播报，2=月播报，3=AED出库，4=自有品牌出库金额；】【2：播报对象：1=个人；2=小组；3=部门】*/
	public static final String ALREADY_DISPLAY_PIC_URL = "{0}:{1}:{2}:picUrl";
	
	/**排行榜播报图片记录：【0:redis环境；】*/
	public static final String RANK_PIC_CONFIG_RECORD = "{0}:rank:picId";
	

}
