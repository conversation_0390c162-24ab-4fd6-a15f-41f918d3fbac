package com.vedeng.common.listener.process;

import com.google.common.eventbus.Subscribe;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.finance.dto.event.CheckGoodAptitudeSupplierEvent;
import com.vedeng.order.service.validator.dto.ValidaterResult;
import com.vedeng.order.service.validator.impl.QualifyAutoAudtioValidatorChain;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class CheckGoodAptitudeSupplierListener implements IObserver {

    @Resource
    private QualifyAutoAudtioValidatorChain qualifyAutoAudtioValidatorChain;

    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void checkGoodAptitudeSupplier(CheckGoodAptitudeSupplierEvent supplierEvent) {
        log.info("开始执行 aptitude checkGoodAptitude");
        List<Integer> traderIds = supplierEvent.getTraderIds();
        List<String> skuList = supplierEvent.getSkuNos();
        for (Integer traderId : traderIds) {
            for (String sku : skuList) {
                ValidaterResult validator = qualifyAutoAudtioValidatorChain.validator(traderId, sku);
                if (!validator.isValidatePass()) {
                    String auditNotPassReason = validator.getAuditFailMessage();
                    supplierEvent.setMessage(StringUtil.isBlank(auditNotPassReason) ? sku+"资质校验失败" : sku+auditNotPassReason);
                }
            }
        }
        log.info("结束执行 aptitude checkGoodAptitude");
    }
}
