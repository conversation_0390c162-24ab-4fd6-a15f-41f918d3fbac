package com.vedeng.temporal.workflow.step.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.*;
import com.vedeng.temporal.domain.result.PollingResult;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.polling.universal.workflow.UniversalPollingWorkflow;
import com.vedeng.temporal.enums.BusinessStepType;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.workflow.activity.InvoiceEntryActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 发票录入步骤 V2 - 新架构版本
 * 
 * 架构优化说明：
 * - 使用新的 InvoiceEntryActivity 替代直接的 Flow 调用
 * - 每个业务操作都是独立的 Activity 方法
 * - 通过 CompanyBusinessRequest.businessData 传递数据
 * - 支持数据串联：查询详情 → 创建录入 → 审核通过 → 状态确认
 * - 异常处理和重试由 Temporal 和 UniversalBusinessTemplate 统一管理
 * 
 * 业务流程：
 * 1. awaitSaleOrderQueryCompletion - 查询销售单号和采购单号并存储到扩展属性
 * 2. awaitInvoiceDetailQueryCompletion - 轮询等待发票详情准备就绪 (openInvoiceStatus=1)
 * 3. createInvoiceWithDetails - 基于预查询数据创建发票录入，获取发票ID
 * 4. approveInvoice - 审核发票，传递发票ID
 * 5. awaitInvoiceHrefReady - 等待发票InvoiceHref字段有值且不为空
 * 6. updateInvoiceHrefField - 调用invoiceEntryActivity更新InvoiceHref字段
 * 7. waitForCompletion - 等待发票处理完成（可选）
 * 
 * 功能迁移：
 * - 从 InvoiceEntryFlow 迁移核心业务逻辑
 * - 从 PaymentFlow 借鉴简化的流程控制
 * - 保持与 SalesOrderStepV2 的架构一致性
 * 
 * 错误处理：
 * - Activity层：技术异常自动重试（网络、超时等）
 * - Step层：业务异常处理（数据校验、业务规则等）
 * - 完整的状态追踪和日志记录
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (新架构版本)
 * @since 2025-01-21
 */
@Slf4j
public class InvoiceEntryStepV2 implements BusinessStep {
    
    private final InvoiceEntryActivity invoiceEntryActivity;
    
    public InvoiceEntryStepV2(InvoiceEntryActivity invoiceEntryActivity) {
        this.invoiceEntryActivity = invoiceEntryActivity;
    }
    
    @Override
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, CompanyExecutionContext context) {
        log.info("【倒序】开始执行发票录入步骤V2，业务ID: {}, 原始目标公司: {}", 
                request.getBusinessId(), request.getTargetCompanyCode());
        
        // 第一个公司跳过执行
        if (context.isLast()) {
            log.info("最后公司无需处理发票录入，跳过执行");
            return CompanyBusinessResponse.success("第一个公司无需处理，跳过执行", request.getBusinessId());
        }
        
        String currentCompany = context.getCurrentCompany();
        String nextCompany = context.getNextCompany();
        
        // 创建工作副本，避免修改原始request对象（Temporal重试兼容性）
        CompanyBusinessRequest workingRequest = buildWorkingRequest(request, currentCompany, nextCompany);
        
        log.info("执行发票录入，当前公司: {}, 目标公司: {}, 业务ID: {}", 
                currentCompany, nextCompany, request.getBusinessId());
        
            // 第1步：根据业务流转单查询销售单号和采购单号
            log.info("开始查询当前公司:{}销售单号,下家公司{}采购单, 业务ID: {}", currentCompany, nextCompany, request.getBusinessId());
            awaitSaleOrderQueryCompletion(currentCompany, workingRequest);
            awaitPurchaseOrderQueryCompletion(nextCompany, workingRequest);

            // 第2步：根据销售单号查询发票信息，判断销售单是否已经开票
            log.info("开始查询发票信息，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());
            Map<String, Object> stringObjectMap = awaitInvoiceDetailQueryCompletion(currentCompany, workingRequest);
            log.info("发票信息查询完成，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());
            
            // 将发票号存储到业务数据中，供后续步骤使用
            updateBusinessDataWithInvoiceNo(workingRequest, stringObjectMap);
            
            // 第3步：基于预查询数据创建发票录入
            CompanyBusinessResponse createResult = invoiceEntryActivity.createInvoiceWithDetails(workingRequest, stringObjectMap);
            log.info("发票录入结果：{}",JSON.toJSON(createResult));
            // 第4步：审核发票
            CompanyBusinessResponse approveResult = invoiceEntryActivity.approveInvoice(workingRequest, stringObjectMap);
            log.info("审核发票结果：{}",JSON.toJSON(approveResult));
            
            // 第5步：等待发票InvoiceHref字段有值且不为空
            log.info("开始检查发票InvoiceHref字段，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());
            awaitInvoiceHrefReady(currentCompany,workingRequest);
            log.info("发票InvoiceHref字段检查完成，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());
            
            // 第6步：调用invoiceEntryActivity更新InvoiceHref字段
            log.info("开始更新发票InvoiceHref字段， 业务ID: {}", request.getBusinessId());
            CompanyBusinessResponse updateResult = invoiceEntryActivity.updateInvoiceHref(workingRequest);
            log.info("更新发票InvoiceHref字段结果：{}", JSON.toJSON(updateResult));

            return approveResult;
    }
    
    @Override
    public String getStepName() {
        return "发票录入步骤V2";
    }

    @Override
    public BusinessStepType getStepType() {
        return BusinessStepType.INVOICE_ENTRY;
    }

    @Override
    public String getStepDescription() {
        return "执行发票录入完整流程：查询销售单号 → 等待发票状态 → 创建发票录入 → 审核通过";
    }

    
    // ========== 私有辅助方法 ==========
    
    /**
     * 构建工作请求副本，避免修改原始request对象
     * 这对Temporal重试兼容性至关重要
     */
    private CompanyBusinessRequest buildWorkingRequest(CompanyBusinessRequest originalRequest, 
                                                      String currentCompany, 
                                                      String nextCompany) {
        return CompanyBusinessRequest.builder()
                .businessId(originalRequest.getBusinessId())
                .businessType(originalRequest.getBusinessType())
                .sourceCompanyCode(currentCompany)
                .targetCompanyCode(nextCompany)
                .workflowExecutionId(originalRequest.getWorkflowExecutionId())
                .flowNodeId(originalRequest.getFlowNodeId())
                .businessData(originalRequest.getBusinessData())
                .extendedProperties(originalRequest.getExtendedProperties() != null ? 
                    new HashMap<>(originalRequest.getExtendedProperties()) : new HashMap<>())
                .build();
    }
    
    /**
     * 构建包含发票ID的请求
     */
    private CompanyBusinessRequest buildRequestWithInvoiceId(CompanyBusinessRequest originalRequest, String invoiceId) {
        // 解析原有的业务数据
        Map<String, Object> businessData = new HashMap<>();
        if (originalRequest.getBusinessData() != null) {
            try {
                businessData = JSON.parseObject(originalRequest.getBusinessData(), Map.class);
            } catch (Exception e) {
                log.warn("解析原有业务数据失败，使用空Map: {}", originalRequest.getBusinessData(), e);
            }
        }
        
        // 添加发票ID
        businessData.put("invoiceId", invoiceId);
        
        // 构建新的请求
        return CompanyBusinessRequest.builder()
                .businessId(originalRequest.getBusinessId())
                .businessType(originalRequest.getBusinessType())
                .sourceCompanyCode(originalRequest.getSourceCompanyCode())
                .targetCompanyCode(originalRequest.getTargetCompanyCode())
                .workflowExecutionId(originalRequest.getWorkflowExecutionId())
                .flowNodeId(originalRequest.getFlowNodeId())
                .businessData(JSON.toJSONString(businessData))
                .extendedProperties(originalRequest.getExtendedProperties())
                .build();
    }
    

    
    /**
     * 判断是否需要等待发票处理完成
     */
    private boolean shouldWaitForCompletion(CompanyBusinessRequest request) {
        // 发票录入通常需要等待审核完成和状态同步
        // 这里简化为默认不等待，可以根据实际需要调整
        // 可以根据业务类型或配置决定是否等待
        return false;
    }
    
    /**
     * 等待销售单号查询完成
     */
    private void awaitSaleOrderQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始轮询等待销售单号，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 构建查询参数
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("queryTypes", Arrays.asList("SALE_ORDER"));
            queryParameters.put("currentCompany", companyCode);
            
            // 构建统一轮询请求
            UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(request.getBusinessType())
                    .dataSourceType(DataSourceType.LOCAL_DATABASE)
                    .companyCode(companyCode)
                    .queryType("TEMPORAL_FLOW_ORDER_QUERY")
                    .queryParameters(queryParameters)
                    .completionCheckConfig("saleOrderNo:isNotBlank")
                    .build();
            
            // 执行统一轮询
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);
            
            // 轮询成功后，需要手动获取最终数据并设置到扩展属性
            if (result.isSuccess()) {
                Object saleOrderNo = result.getData().get("saleOrderNo");
                // 存储到扩展属性
                Map<String, Object> extendedProperties = request.getExtendedProperties();
                if (extendedProperties == null) {
                    extendedProperties = new HashMap<>();
                }
                extendedProperties.put("saleOrderNo", saleOrderNo);
                request.setExtendedProperties(extendedProperties);

                log.info("销售单号轮询完成，公司: {}, 业务ID: {}, 销售单号: {}",
                        companyCode, businessId, saleOrderNo);
            } else {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("销售单号轮询未完成", "POLLING_INCOMPLETE", context);
            }

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("销售单号轮询异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("销售单号轮询系统异常", "POLLING_ERROR", context);
        }
    }

    /**
     * 第2步：等待发票信息查询完成，判断销售单是否已经开票
     * 从 InvoiceEntryStep 迁移的逻辑，用于轮询等待发票详情准备就绪
     *
     * @return
     */
    private Map<String, Object> awaitInvoiceDetailQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始查询发票信息，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 从扩展属性中获取销售单号
            Map<String, Object> extendedProperties = request.getExtendedProperties();
            if (extendedProperties == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("扩展属性为空，无法获取销售单号", "MISSING_EXTENDED_PROPERTIES", context);
            }

            String saleOrderNo = (String) extendedProperties.get("saleOrderNo");
            if (saleOrderNo == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("未找到销售单号", "MISSING_SALE_ORDER_NO", context);
            }

            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("saleOrderNo", saleOrderNo);
            log.info("查询入参：{}",JSON.toJSON(apiParameters));

            // 构建统一轮询请求
            UniversalPollingRequest statusRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(companyCode)
                    .apiPath("/api/v1/receiptInvoice/queryInvoiceDetail.do")  // 发票详情查询API路径
                    .apiParameters(apiParameters)  // 使用自定义API参数
                    // 发票查询完成条件：查询成功
                    .completionCheckConfig("data.openInvoiceStatus:1")
                    .build();

            log.info("使用发票详情查询检查器：查询发票信息，销售单号: {}", saleOrderNo);

            // 执行统一轮询等待
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(statusRequest);

            // 验证最终状态
            if (!result.isSuccess()) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("发票信息查询轮询失败", "POLLING_INCOMPLETE", context);
            }
            Map<String, Object> data = result.getData();
            log.info("发票信息查询完成，公司: {}, 业务ID: {}, 结果: {}", companyCode, businessId, JSON.toJSON(data));
            
            return data;

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("查询发票信息异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("发票信息查询系统异常", "POLLING_ERROR", context);
        }
    }

    /**
     * 第1步：等待采购单号查询完成
     * 使用通用化枚举设计的轮询机制等待采购单号准备就绪
     */
    private void awaitPurchaseOrderQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始轮询等待采购单号，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 构建查询参数
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("queryTypes", Arrays.asList("BUY_ORDER"));
            queryParameters.put("currentCompany", companyCode);

            // 构建统一轮询请求
            UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.LOCAL_DATABASE)
                    .companyCode(companyCode)
                    .queryType("TEMPORAL_FLOW_ORDER_QUERY")
                    .queryParameters(queryParameters)
                    .completionCheckConfig("buyOrderNo:isNotBlank")
                    .build();

            // 执行统一轮询
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);

            // 轮询成功后，需要手动获取最终数据并设置到扩展属性
            if (result.isSuccess()) {
                // 由于轮询已完成，直接查询一次获取最终数据
                Object buyOrderNo = result.getData().get("buyOrderNo");

                // 存储到扩展属性
                Map<String, Object> extendedProperties = request.getExtendedProperties();
                if (extendedProperties == null) {
                    extendedProperties = new HashMap<>();
                }
                extendedProperties.put("buyOrderNo", buyOrderNo);
                request.setExtendedProperties(extendedProperties);

                log.info("采购单号轮询完成，公司: {}, 业务ID: {}, 采购单号: {}",
                        companyCode, businessId, buyOrderNo);
            } else {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("采购单号轮询未完成", "POLLING_INCOMPLETE", context);
            }

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("采购单号轮询异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("采购单号轮询系统异常", "POLLING_ERROR", context);
        }
    }

    /**
     * 将发票号存储到业务数据中，供后续步骤使用
     */
    private void updateBusinessDataWithInvoiceNo(CompanyBusinessRequest request, Map<String, Object> invoiceDetailData) {
        try {
            // 从发票详情数据中提取发票号
            Map<String, Object> data = (Map<String, Object>) invoiceDetailData.get("data");
            if (data != null) {
                String invoiceNo = (String) data.get("invoiceNo");
                if (invoiceNo != null) {
                    // 解析现有的业务数据
                    Map<String, Object> businessData = new HashMap<>();
                    if (request.getBusinessData() != null) {
                        try {
                            businessData = JSON.parseObject(request.getBusinessData(), Map.class);
                        } catch (Exception e) {
                            log.warn("解析现有业务数据失败，使用空Map: {}", request.getBusinessData(), e);
                        }
                    }
                    
                    // 添加发票号
                    businessData.put("invoiceNo", invoiceNo);
                    
                    // 更新业务数据
                    request.setBusinessData(JSON.toJSONString(businessData));
                    
                    log.info("已将发票号存储到业务数据中，业务ID: {}, 发票号: {}", request.getBusinessId(), invoiceNo);
                } else {
                    log.warn("从发票详情数据中未找到发票号，业务ID: {}", request.getBusinessId());
                }
            } else {
                log.warn("发票详情数据中data字段为空，业务ID: {}", request.getBusinessId());
            }
        } catch (Exception e) {
            log.error("更新业务数据中的发票号失败，业务ID: {}", request.getBusinessId(), e);
            // 这里不抛出异常，因为这不是关键流程
        }
    }

    /**
     * 第3步：等待发票InvoiceHref字段有值且不为空
     * 使用轮询机制检查t_Invoice表中的InvoiceHref字段
     */
    private void awaitInvoiceHrefReady(String currentCompany,CompanyBusinessRequest request) {
        log.info("开始轮询等待发票InvoiceHref字段，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 从业务数据中获取发票号
            Map<String, Object> businessData = new HashMap<>();
            if (request.getBusinessData() != null) {
                try {
                    businessData = JSON.parseObject(request.getBusinessData(), Map.class);
                } catch (Exception e) {
                    log.warn("解析业务数据失败: {}", request.getBusinessData(), e);
                }
            }
            
            String invoiceNo = (String) businessData.get("invoiceNo");
            if (invoiceNo == null) {
                String context = "Step=" + getStepName() + ", Company=" + currentCompany + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("业务数据中未找到发票号", "MISSING_INVOICE_NO", context);
            }

            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("invoiceNo", invoiceNo);

            // 构建统一轮询请求
            UniversalPollingRequest statusRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(currentCompany)
                    .apiPath("/api/v1/receiptInvoice/queryHref.do")  // 检查InvoiceHref字段的API路径
                    .apiParameters(apiParameters)
                    // InvoiceHref字段有值且不为空的完成条件
                    .completionCheckConfig("data.invoiceHref:isNotBlank")
                    .build();

            log.info("使用InvoiceHref检查器：检查发票InvoiceHref字段，发票号: {}", invoiceNo);

            // 执行统一轮询等待
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(statusRequest);

            // 验证最终状态
            if (!result.isSuccess()) {
                String context = "Step=" + getStepName() + ", Company=" + currentCompany + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("发票InvoiceHref字段轮询失败", "POLLING_INCOMPLETE", context);
            }

            log.info("发票InvoiceHref字段轮询完成，公司: {}, 业务ID: {}, 发票号: {}",
                    currentCompany, businessId, invoiceNo);

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("发票InvoiceHref字段轮询异常，公司: {}, 业务ID: {}",currentCompany, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + currentCompany + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("发票InvoiceHref字段轮询系统异常", "POLLING_ERROR", context);
        }
    }
}
