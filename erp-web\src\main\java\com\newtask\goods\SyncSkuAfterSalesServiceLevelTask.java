package com.newtask.goods;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.constant.AfterSaleServiceLevelEnum;
import com.vedeng.aftersales.dao.AfterSaleServiceStandardInfoMapper;
import com.vedeng.aftersales.service.AfterSaleServiceStandardService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.dao.SyncGoodsInfoMapper;
import com.vedeng.goods.domain.entity.CoreSku;
import com.vedeng.goods.domain.entity.SkuTerminalLevel;
import com.vedeng.goods.mapper.CoreSkuMapper;
import com.vedeng.goods.mapper.SkuTerminalLevelMapper;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSkuGenerateExample;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 同步售后服务等级
 */
@JobHandler(value = "syncSkuAfterSalesServiceLevelTask")
@Component
@Slf4j
public class SyncSkuAfterSalesServiceLevelTask extends AbstractJobHandler {

    @Autowired
    private CoreSkuGenerateMapper coreSkuGenerateMapper;
    @Autowired
    private AfterSaleServiceStandardService afterSaleServiceStandardService;

    @Autowired
    AfterSaleServiceStandardInfoMapper afterSaleServiceStandardInfoMapper;

    @Resource
    private SyncGoodsInfoMapper syncGoodsInfoMapper;
    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("同步售后服务等级start-----------");
        if(StringUtils.isNotBlank(s)){
            if(!s.startsWith("V")){
                XxlJobLogger.log("请输入V开头的SKU编号:{}", s);
                return FAIL;
            }
            XxlJobLogger.log("同步售后服务等级参数:{}", s);
            StringBuilder slog=new StringBuilder();
            Integer level= afterSaleServiceStandardService.calculateAfterSalesServiceLevelBySku(s,slog);
            XxlJobLogger.log("同步售后服务等级参数:{} ", slog.toString());
            CoreSkuGenerate coreSku = new CoreSkuGenerate();
            coreSku.setSkuId(Integer.parseInt(s.substring(1)));
            coreSku.setAfterSalesServiceLevel(level);
            //这里不更新sku的更新时间
            coreSkuGenerateMapper.updateByPrimaryKeySelective(coreSku);
            return SUCCESS;
        }

        //与索引同步数据源保持一致
        List<Integer> skuList =  syncGoodsInfoMapper.getValidSkuIds() ;
        XxlJobLogger.log("同步售后服务等级启用任务数:[{}}", skuList.size());
        log.info("同步售后服务等级启用任务数:[{}}", skuList.size());
        if(CollectionUtils.isNotEmpty(skuList)){
            skuList.forEach(skuId -> {
                try {
                    CoreSkuGenerate coreSku = new CoreSkuGenerate();
                    coreSku.setSkuId(skuId);
                    StringBuilder slog = new StringBuilder();
                    Integer level = afterSaleServiceStandardService.calculateAfterSalesServiceLevelBySku("V" + skuId, slog);
                    coreSku.setAfterSalesServiceLevel(level);
                    CoreSkuGenerate dbSku = coreSkuGenerateMapper.selectAfterServiceLevelByPrimaryKey(skuId);
                    // 有变动才更新
                    if (dbSku.getAfterSalesServiceLevel() == null || !dbSku.getAfterSalesServiceLevel().equals(level)) {
                        log.info("同步售后服务等级参数:{} ", slog.toString());
                        XxlJobLogger.log("同步售后服务等级参数:{}", slog.toString());
                        coreSkuGenerateMapper.updateByPrimaryKeySelective(coreSku);
                    }
                }catch (Exception e){
                    log.error("同步售后服务等级参数:V{} 异常:{}", skuId, e.getMessage(), e);
                    XxlJobLogger.log("同步售后服务等级参数:V{} 异常:{}", skuId, e.getMessage());
                }
            });
        }
//        List<Integer> skuListDis =  syncGoodsInfoMapper.getNotValidSkuIds() ;
//        XxlJobLogger.log("同步售后服务等级禁用总任务数:[{}}", skuListDis.size());
//        log.info("同步售后服务等级禁用总任务数:[{}}", skuList.size());
//        if(CollectionUtils.isNotEmpty(skuListDis)){
//            skuListDis.forEach(skuId -> {
//                CoreSkuGenerate coreSku = new CoreSkuGenerate();
//                coreSku.setSkuId(skuId);
//                // Integer level = afterSaleServiceStandardService.calculateAfterSalesServiceLevelBySku("V"+skuId,null);
//                coreSku.setAfterSalesServiceLevel(AfterSaleServiceLevelEnum.LEVEL_6.getLevel()); //禁用商品售后服务等级为6
//
//                CoreSkuGenerate dbSku= coreSkuGenerateMapper.selectAfterServiceLevelByPrimaryKey(skuId);
//                // 有变动才更新
//                if( dbSku.getAfterSalesServiceLevel() == null || !dbSku.getAfterSalesServiceLevel().equals(AfterSaleServiceLevelEnum.LEVEL_6.getLevel())){
//                    log.info("同步售后服务等级参数:V{} 禁用设置为 无需评级", skuId);
//                    XxlJobLogger.log("同步售后服务等级参数:V{} 禁用设置为 无需评级",skuId);
//                    coreSkuGenerateMapper.updateByPrimaryKeySelective(coreSku);
//                }
//            });
//        }
        return SUCCESS;
    }

}
