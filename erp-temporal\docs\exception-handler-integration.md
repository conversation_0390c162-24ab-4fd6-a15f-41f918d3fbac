# AbstractBusinessProcess 异常处理统一方案

## 概述

本文档描述了 AbstractBusinessProcess 类中统一异常通知机制的实现方案，解决了两种异常处理方式缺少统一通知的问题。

## 问题分析

### 原有异常处理方式

AbstractBusinessProcess 的 executeBusinessSteps 方法中存在两种异常处理方式：

1. **方式1 - 直接异常捕获**：
   - 位置：`executeStepWithEnhancedExceptionHandling()` 方法的 catch 块
   - 处理：直接转换为 `CompanyBusinessResponse.failure()`
   - 问题：缺少异常通知机制

2. **方式2 - stepResult 错误检查**：
   - 位置：`executeBusinessSteps()` 中检查 `stepResult.getSuccess()`
   - 处理：抛出 `BusinessProcessException.nonRetryable()`
   - 问题：缺少异常通知机制

## 解决方案

### 1. 增强 ExceptionHandler 类

新增两个方法来支持 AbstractBusinessProcess 的异常处理需求：

#### 1.1 handleBusinessExceptionWithNotificationOnly()

```java
/**
 * 处理业务异常（仅通知版本）
 * 专为 AbstractBusinessProcess 设计，只做异常分类和通知，不抛出异常
 * 始终返回 CompanyBusinessResponse.failure()，保持工作流状态为 Completed
 */
public CompanyBusinessResponse handleBusinessExceptionWithNotificationOnly(
    Exception e, String operationName, String businessId, String companyCode)
```

**特点**：
- 进行异常分类（技术异常 vs 业务异常）
- 发送统一的异常通知
- 始终返回失败响应，不抛出异常
- 保持 Temporal 工作流状态为 Completed

#### 1.2 sendNotificationForException()

```java
/**
 * 发送异常通知（独立方法）
 * 专为已经创建好的 BusinessProcessException 发送通知
 */
public void sendNotificationForException(
    BusinessProcessException exception, String operationName, String businessId)
```

**特点**：
- 专门处理已创建的 BusinessProcessException
- 独立的通知发送，不影响异常处理流程
- 异常安全，通知失败不影响主流程

### 2. 修改 AbstractBusinessProcess 类

#### 2.1 添加 ExceptionHandler 依赖

```java
// 异常处理器（通过构造函数注入）
protected final ExceptionHandler exceptionHandler;

protected AbstractBusinessProcess(
    CompanySequenceActivity companySequenceActivity,
    ExceptionHandler exceptionHandler) {
    // ...
}
```

#### 2.2 更新异常处理逻辑

**场景1 - 直接异常捕获**：
```java
} catch (Exception e) {
    // 使用 ExceptionHandler 处理异常（仅通知版本）
    return exceptionHandler.handleBusinessExceptionWithNotificationOnly(
        e, stepName, businessId, companyCode);
}
```

**场景2 - stepResult 错误检查**：
```java
if (!Boolean.TRUE.equals(stepResult.getSuccess())) {
    BusinessProcessException exception = BusinessProcessException.nonRetryable(...);
    
    // 发送异常通知
    exceptionHandler.sendNotificationForException(exception, step.getStepName(), request.getBusinessId());
    
    // 抛出异常（保持原有逻辑）
    throw exception;
}
```

### 3. 更新所有 Process 实现类

所有继承 AbstractBusinessProcess 的类都需要更新构造函数：

- PurchaseOrderProcess
- SalesOrderProcess  
- InventoryReceiptProcess
- InvoiceEntryProcess
- PaymentTransferProcess
- PeerListProcess

**更新示例**：
```java
public PurchaseOrderProcess(CompanySequenceActivity companySequenceActivity,
                            ExceptionHandler exceptionHandler,
                            PurchaseOrderActivity purchaseOrderActivity) {
    super(companySequenceActivity, exceptionHandler);
    this.purchaseOrderActivity = purchaseOrderActivity;
}
```

## 方案优势

### 1. 统一异常通知
- 两种异常处理方式都通过 ExceptionHandler 发送通知
- 统一的异常分类和处理逻辑
- 一致的通知格式和内容

### 2. 保持设计理念
- 维持 AbstractBusinessProcess 的核心设计：所有异常转换为失败响应
- 避免 Temporal 工作流进入 Failed 状态
- 保持业务逻辑的可控性

### 3. 最小侵入性
- 只需要添加一个构造函数参数
- 复用现有的 ExceptionHandler 逻辑
- 不改变现有的异常处理流程

### 4. 异常安全
- 通知失败不影响主业务流程
- 兜底策略确保系统稳定性
- 详细的日志记录便于问题诊断

## 使用示例

### Spring 配置示例

```java
@Configuration
public class TemporalProcessConfig {
    
    @Bean
    public PurchaseOrderProcess purchaseOrderProcess(
            CompanySequenceActivity companySequenceActivity,
            ExceptionHandler exceptionHandler,
            PurchaseOrderActivity purchaseOrderActivity) {
        return new PurchaseOrderProcess(
            companySequenceActivity, 
            exceptionHandler, 
            purchaseOrderActivity);
    }
}
```

### 异常通知效果

当业务步骤执行失败时，系统会：

1. **自动分类异常**：判断是技术异常还是业务异常
2. **发送统一通知**：包含详细的上下文信息
3. **记录结构化日志**：便于问题追踪和分析
4. **返回失败响应**：保持工作流状态一致

## 注意事项

1. **依赖注入**：确保 ExceptionHandler 已正确配置为 Spring Bean
2. **通知配置**：确认 TemporalNotificationService 的通知渠道配置
3. **性能考虑**：异常通知是异步的，不会影响主流程性能
4. **监控告警**：建议对异常通知进行监控和告警配置

## 总结

通过这个统一的异常处理方案，AbstractBusinessProcess 现在具备了：

- ✅ 统一的异常通知机制
- ✅ 完整的异常分类和处理
- ✅ 保持原有设计理念
- ✅ 最小的代码侵入性
- ✅ 异常安全的实现

这个方案为 ERP 系统的 Temporal 工作流提供了可靠的异常处理和通知能力，提升了系统的可观测性和运维效率。
