@import 'font/style.css';
html,
body {
  height: 100%;
  width: 100%;
  color: #333;
  font: 14px/1.5 'Microsoft YaHei', 'arial', 'sans-serif';
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  background-color: #f5f7fa;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
}
a {
  text-decoration: none;
  color: #333;
}
a:active,
a:visited {
  color: #333;
}
ul {
  list-style: none;
}
button,
input,
optgroup,
select,
textarea {
  border: 0;
  outline: 0;
  font: inherit;
  color: inherit;
}
img {
  border: 0;
  vertical-align: middle;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #333;
  /*输入框提示语的字体样式*/
  font-size: 14px;
}
input[type=search] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
  font-family: inherit;
  font-size: 100%;
}
input::-webkit-search-decoration,
input::-webkit-search-cancel-button {
  display: none;
}
.form-block {
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
}
.form-block:last-child {
  margin-bottom: 0;
}
.flex-box {
  display: flex;
  margin-bottom: 20px;
}
.flex-box > * {
  flex-shrink: 0;
}
.user-show {
  display: flex;
  align-items: center;
}
.user-show > .avatar {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}
.global__loading__wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
@keyframes loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.global__loading__wrap .icon-loading {
  font-size: 64px;
  color: #fff;
  animation: loading 2s linear infinite;
  z-index: 9999;
}
.global__loading__wrap .global__loading__p {
  font-size: 14px;
  color: #fff;
  margin-top: 10px;
}
.form-wrap .form-item {
  display: flex;
  margin-bottom: 20px;
}
.form-wrap .form-item:last-child {
  margin-bottom: 0;
}
.form-wrap .form-item .form-label {
  padding-top: 6px;
  margin-right: 10px;
  text-align: right;
  width: 240px;
  flex-shrink: 0;
  color: #999;
}
.form-wrap .form-item .form-label .must {
  color: #e64545;
}
.form-wrap .form-item .form-fields {
  flex: 1;
  word-break: break-all;
}
.form-wrap .form-item .form-fields .vd-ui-input-error {
  color: #E64545;
  margin-top: 5px;
  font-size: 0;
  white-space: nowrap;
}
.form-wrap .form-item .form-fields .vd-ui-input-error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
  vertical-align: -2px;
}
.form-wrap .form-item .form-fields .vd-ui-input-error .vd-ui-input-error--errmsg {
  font-size: 14px;
  margin: 0px;
  display: inline-block;
}
.form-wrap .form-item .form-tip {
  color: #999;
  margin-top: 5px;
}
.form-wrap .form-item.form-item-text .form-label {
  padding-top: 0;
}
.form-wrap .form-btn-wrap {
  padding-left: 270px;
}
.form-wrap.label-width-8 .form-item .form-label {
  width: 260px;
}
.form-wrap.label-width-8 .form-btn-wrap {
  padding-left: 370px;
}
.form-wrap.label-width-7 .form-item .form-label {
  width: 240px;
}
.form-wrap.label-width-7 .form-btn-wrap {
  padding-left: 350px;
}
.form-wrap.label-width-6 .form-item .form-label {
  width: 220px;
}
.form-wrap.label-width-6 .form-btn-wrap {
  padding-left: 330px;
}
.form-wrap.label-width-5 .form-item .form-label {
  width: 200px;
}
.form-wrap.label-width-5 .form-btn-wrap {
  padding-left: 310px;
}
.form-wrap.label-width-4 .form-item .form-label {
  width: 180px;
}
.form-wrap.label-width-4 .form-btn-wrap {
  padding-left: 290px;
}
.form-wrap.label-width-3 .form-item .form-label {
  width: 160px;
}
.form-wrap.label-width-3 .form-btn-wrap {
  padding-left: 270px;
}
.form-wrap.label-width-2 .form-item .form-label {
  width: 140px;
}
.form-wrap.label-width-2 .form-btn-wrap {
  padding-left: 250px;
}
.form-wrap.label-width-1 .form-item .form-label {
  width: 120px;
}
.form-wrap.label-width-1 .form-btn-wrap {
  padding-left: 230px;
}
.ui-col-1 {
  width: calc(100% * (1 / 12));
}
.ui-col-2 {
  width: calc(100% * (2 / 12));
}
.ui-col-3 {
  width: calc(100% * (3 / 12));
}
.ui-col-4 {
  width: calc(100% * (4 / 12));
}
.ui-col-5 {
  width: calc(100% * (5 / 12));
}
.ui-col-6 {
  width: calc(100% * (6 / 12));
}
.ui-col-7 {
  width: calc(100% * (7 / 12));
}
.ui-col-8 {
  width: calc(100% * (8 / 12));
}
.ui-col-9 {
  width: calc(100% * (9 / 12));
}
.ui-col-10 {
  width: calc(100% * (10 / 12));
}
.ui-col-11 {
  width: calc(100% * (11 / 12));
}
.ui-col-12 {
  width: calc(100% * (12 / 12));
}
.page-wrap .page-container {
  min-height: calc(100vh - 50px);
  min-width: 960px;
}
.page-wrap .page-container .page-main {
  position: relative;
}
.page-wrap .list-title {
  background: #fff;
  padding: 19px 0 18px 20px;
  font-size: 24px;
  font-weight: 700;
}
.page-wrap .list-top-option {
  position: absolute;
  top: 20px;
  right: 20px;
}
.card-block {
  max-width: 1200px;
  min-width: 1200px;
  margin: 0 auto;
  background: #fff;
  padding: 0 20px 40px;
  margin-bottom: 20px;
}
.card-block.detail-card {
  min-width: 960px;
}
.card-block:last-child {
  margin-bottom: 0;
}
.card-block.last {
  margin-bottom: 0;
}
.card-block .card-title {
  display: flex;
  align-items: center;
  padding: 20px 0;
  font-size: 20px;
  line-height: 30px;
  font-weight: 700;
  justify-content: space-between;
}
.card-block .card-title .title-options {
  display: flex;
  align-items: center;
  font-weight: normal;
  font-size: 14px;
  line-height: 1.5;
}
.card-block .card-title .title-options .option-item {
  color: #09f;
  cursor: pointer;
  display: flex;
  align-items: center;
}
.card-block .card-title .title-options .option-item .vd-ui_icon {
  margin-right: 5px;
  line-height: 1;
  font-size: 16px;
  margin-top: 1px;
}
.card-block .card-title .title-options .option-item:hover {
  color: #f60;
}
.detail-info-wrap .info-item {
  display: flex;
  margin-top: 15px;
}
.detail-info-wrap .info-item:first-child {
  margin-top: 0;
}
.detail-info-wrap .info-item .info-label {
  width: 240px;
  text-align: right;
  color: #999;
  margin-right: 10px;
}
.detail-info-wrap .info-item .info-content {
  flex: 1;
  min-width: 0;
  word-break: break-all;
}
.green {
  color: #13BF13;
}
.red {
  color: #e64545;
}
.orange {
  color: #f60;
}
.text-line-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.text-line-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.text-line-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.text-line-7 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 7;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.margin-b10 {
  margin-bottom: 10px;
}
.margin-b15 {
  margin-bottom: 15px;
}
.margin-b20 {
  margin-bottom: 20px;
}
.margin-t10 {
  margin-top: 20px;
}
.margin-t15 {
  margin-top: 15px;
}
.margin-t20 {
  margin-top: 20px;
}
.margin-l10 {
  margin-left: 10px;
}
.margin-l15 {
  margin-left: 15px;
}
.margin-l20 {
  margin-left: 20px;
}
.margin-r10 {
  margin-right: 10px;
}
.margin-r15 {
  margin-right: 15px;
}
.margin-r20 {
  margin-right: 20px;
}
.min-width250 {
  min-width: 250px;
}
.min-width300 {
  min-width: 300px;
}
.table-edit-wrap {
  display: flex;
  background: #F0F9FF;
  padding: 8px 10px;
  box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
  min-width: 262px;
}
.table-edit-wrap .edit-content {
  flex: 1;
  margin-right: 5px;
}
.table-edit-wrap .edit-content .vd-ui-date .vd-ui-date-editor {
  width: 100% !important;
}
.table-edit-wrap .edit-content .vd-ui-textarea {
  vertical-align: middle;
}
.table-edit-wrap .edit-content .vd-ui-select {
  width: 100%;
}
.table-edit-wrap .edit-content .vd-ui-input-error {
  color: #E64545;
  font-size: 0;
  padding: 10px;
  border: solid 1px #BABFC2;
  background: #fff;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  border-radius: 3px;
  position: absolute;
  min-width: 100%;
  white-space: nowrap;
}
.table-edit-wrap .edit-content .vd-ui-input-error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
  vertical-align: -2px;
}
.table-edit-wrap .edit-content .vd-ui-input-error .vd-ui-input-error--errmsg {
  font-size: 14px;
  margin: 0px;
  display: inline-block;
}
.table-edit-wrap .edit-option {
  display: flex;
  align-items: center;
}
.table-edit-wrap .edit-option .vd-ui-button {
  padding: 5px 9px;
  margin-right: 5px;
}
.table-edit-wrap .edit-option .vd-ui-button:last-child {
  margin-right: 0;
}
.table-edit-wrap .edit-option .vd-ui-button .vd-ui_icon {
  margin: 0;
}
.bubble-tip-wrap {
  position: absolute;
  background: #333;
  color: #fff;
  border-radius: 3px;
  padding: 7px 10px;
  font-size: 14px;
  white-space: nowrap;
}
.bubble-tip-wrap::before {
  content: "";
  width: 0;
  height: 0;
  border: 6px solid transparent;
  position: absolute;
}
.bubble-tip-wrap.top::before {
  border-top: 6px solid #333;
  bottom: -12px;
  left: calc(50% - 6px);
}
.form-top-tip {
  display: flex;
  padding: 10px 15px;
  background: #E0F3FF;
  margin: -20px -20px 20px -20px;
}
.form-top-tip .icon-info2 {
  color: #09f;
  font-size: 16px;
  line-height: 1;
  margin-top: 3px;
  margin-right: 10px;
}
.form-top-tip .tip-cnt {
  flex: 1;
}
.dlg-form-footer {
  padding-left: 150px;
  width: 100%;
  display: flex;
}
.error-nowrap .vd-ui-input-error {
  white-space: nowrap;
}
#page-container {
  opacity: 0;
}
#page-container.show {
  opacity: 1;
}
.list-sort-wrap {
  margin-top: -6px;
  margin-bottom: -6px;
}
.page-detail-container {
  padding-top: 73px;
}
.page-detail-container .page-detail-header {
  position: fixed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  top: 0;
  left: 0;
  height: 73px;
  background: #fff;
  border-bottom: solid 1px #E1E5E8;
  padding: 0 20px;
  z-index: 30;
}
.page-detail-container .page-detail-header .header-title {
  font-size: 24px;
  font-weight: 700;
}
.page-detail-container .page-detail-header .header-options {
  display: flex;
  align-items: center;
}
.page-detail-container .page-detail-header .header-options .vd-ui-button {
  margin-left: 10px;
}
.page-detail-container .page-detail-content {
  padding: 20px;
}
