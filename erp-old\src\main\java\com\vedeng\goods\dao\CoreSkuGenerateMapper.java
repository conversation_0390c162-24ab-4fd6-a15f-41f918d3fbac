package com.vedeng.goods.dao;

import com.pricecenter.dto.UserInfo;
import com.vedeng.docSync.model.pojo.generate.DocOfGoodsDo;
import com.vedeng.goods.command.SkuAddCommand;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSkuGenerateExample;
import com.vedeng.todolist.dto.PurchaseRankingDto;
import com.wms.model.po.WmsCoreSku;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface CoreSkuGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int countByExample(CoreSkuGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int deleteByExample(CoreSkuGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int deleteByPrimaryKey(Integer skuId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int insert(CoreSkuGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int insertSelective(CoreSkuGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    List<CoreSkuGenerate> selectByExample(CoreSkuGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    CoreSkuGenerate selectByPrimaryKey(Integer skuId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int updateByExampleSelective(@Param("record") CoreSkuGenerate record, @Param("example") CoreSkuGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int updateByExample(@Param("record") CoreSkuGenerate record, @Param("example") CoreSkuGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int updateByPrimaryKeySelective(CoreSkuGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int updateByPrimaryKey(CoreSkuGenerate record);

    /**
     * <b>Description:</b>根据spuId更新sku表PushStatus字段<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> ${date} ${time}
     */
    int updatePushStatusBySpuId(@Param(value = "spuId") Integer spuId,@Param(value = "status") int status);

    /**
     * <b>Description:</b>根据skuId更新推送状态<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> ${date} ${time}
     */
    int updatePushStatusBySkuId(@Param(value = "skuId") Integer skuId,@Param(value = "status")int status);

    int updatePushStatusBySkuNo(@Param(value = "skuNo") String skuNo,@Param(value = "status")int status);
    /**
     * <b>Description:</b>根据skuId查询推送状态<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> ${date} ${time}
     */
    int getPushStatusBySkuId(@Param(value = "skuId")Integer skuId);

    Integer getPushStatusBySkuNo(@Param(value = "skuNo")String skuNo);

    public UserInfo getAssignAndManageId(@Param(value = "skuId") Long skuId);

    CoreSkuGenerate selectBySkuNo(String skuNo);

    /**
     * @description: 查询skulist.
     * @jira: VDERP-2217 提供预计发货时间给前台.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/15 4:49 下午.
     * @author: Tomcat.Hui.
     * @param skuNoList: .
     * @return: java.util.List<com.vedeng.goods.model.CoreSkuGenerate>.
     * @throws: .
     */
    List<CoreSkuGenerate> getSkuListByNo(@Param("list") List<String> skuNoList);

    /**
     * @description: 批量更新sku填报预计可发货区间.
     * @jira: VDERP-2217 提供预计发货时间给前台.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/15 8:35 下午.
     * @author: Tomcat.Hui.
     * @param skuNoList: .
     * @return: java.lang.Integer.
     * @throws: .
     */
    Integer updateSkuDeliveryRange(@Param("list") List<CoreSkuGenerate> skuNoList);


    /**
     * 更新采购到货时间
     * @param skuNoList
     * @return
     */
    Integer updateSkuPurchaseTime(@Param("list") List<CoreSkuGenerate> skuNoList);

    /**
     * @description: 获取宽表sku发货区间.
     * @jira: VDERP-2217 提供预计发货时间给前台.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/22 10:47 上午.
     * @author: Tomcat.Hui.
     * @param skuNoList: .
     * @return: java.util.List<com.vedeng.goods.model.CoreSkuGenerate>.
     * @throws: .
     */
    List<CoreSkuGenerate> getWtSkuList(@Param("list") List<String> skuNoList);

    void updateSkuPriceVerifyStatusBySkuNo(@Param(value = "skuNo") String skuNo,@Param(value = "verifyStatus") int verifyStatus);

    void updateSkuBySkuNo(CoreSkuGenerate coreSkuGenerate);

    void updateSkuLatestYearAvgPrice(@Param("goodsId") Integer goodsId,@Param("price") BigDecimal price, @Param("userId")int userId);
    /**
     * <b>Description:</b>获取sku的上架状态<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/7/28
     */
    Integer getSkuOnSale(@Param("skuNo") String skuNo);

    /**
     * <b>Description:</b>根据skuId获取sku上架状态<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/7/28
     */
    Integer getSkuOnSaleBySkuId(@Param("skuId")Integer skuId);
    /**
     * <b>Description:</b>更新sku的上架状态<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/7/28
     */
    Integer updateSkuOnSale(@Param("skuNo") String skuNo,@Param("onSale")Integer onSale);

    /**
     * 获取商品档案下传WMS的数据
     * @Param: [skuId]
     * @Return: com.wms.model.po.WmsCoreSku
     * @Author: Rivan
     * @Date: 2020/7/28 14:17
     */
    WmsCoreSku getInputSkuToWmsBySkuId(Integer skuId) ;

    List<WmsCoreSku> getWmsCoreSkuList();

    List<WmsCoreSku> getSkuListBySkuNoStr(@Param("skuNoStr") String skuNoStr);

    void updateSkuValidatePeriod(@Param("skuId") Integer skuId);


    /**
     * 批量维护SKU报备信息
     *
     * @param skuIds
     * @param coreSkuGenerate
     * @return
     */
    Integer batchSaveSkuAuthorization(@Param("skuIds") List<Integer> skuIds, @Param("sku") CoreSkuGenerate coreSkuGenerate);

    /**
     * 获取SKU的信息同步状态
     *
     * @param skuId
     * @return
     */
    int getSynchronizationStatusBySkuId(Integer skuId);

    /**
     * 更新SKU的信息同步状态
     *
     * @param skuIds
     * @param status
     * @return
     */
    int updateSynchronizationStatusBySkuIds(@Param("skuIds") List<Integer> skuIds, @Param("status") Integer status);

    /**
     * 获取SKU推送信息
     *
     * @param skuId
     * @return
     */
    CoreSkuGenerate getPushInfoBySkuId(Integer skuId);

    /**
     * 获取Spu下审核通过的sku
     *
     * @param spuId
     * @return
     */
    List<SkuAddCommand> getValidedSkuInfoBySpuId(Integer spuId);

    List<String> getSkuNoListById(List<Integer> skuIdList);

    List<PurchaseRankingDto> getSkuNoAndOrderAssitIdMap(@Param("skuNoList") List<String> skuNoList);
    List<PurchaseRankingDto> getSkuNoAndOrderAssitIdMapModify(@Param("skuNoList") List<String> skuNoList);

    List<PurchaseRankingDto> getSkuNoAndOrderAssitIdMapModifyV2(@Param("skuNoList") List<String> skuNoList);

    DocOfGoodsDo selectDocOfGoodsBySkuId(@Param("skuId") Integer skuId);

    void updateLabelStausByNo(@Param("coreSkuGenerate") CoreSkuGenerate coreSkuGenerate);

    List<String> getAllSkuNo();

    Integer updateSkuIsDirect(CoreSkuGenerate a);
    CoreSkuGenerate selectAfterServiceLevelByPrimaryKey(Integer skuId);
}