# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Build Commands
```bash
# Build entire project
mvn clean install

# Build specific module
mvn clean install -pl erp-web

# Build module with dependencies
mvn clean install -pl erp-web -am

# Package as WAR files
mvn clean package
```

### Test Commands
```bash
# Run tests (note: tests are skipped by default)
mvn test -DskipTests=false

# Run tests for specific module
mvn test -pl erp-web -DskipTests=false

# Run integration tests
mvn verify -DskipTests=false
```

### Code Generation
```bash
# Generate MyBatis code
mvn mybatis-generator:generate
```

### Development Commands
```bash
# Run Spring Boot application (for erp-web-crm)
mvn spring-boot:run -pl erp-web-crm

# Check dependencies
mvn dependency:tree
```

## Project Architecture

This is a multi-module Maven Java ERP system with the following key characteristics:

### Technology Stack
- **Backend**: Spring 4.1.9.RELEASE (main), Spring Boot 2.1.5.RELEASE (mobile)
- **ORM**: MyBatis 3.3.1
- **Cache**: Redis (Spring Data Redis 1.6.6)
- **Database**: MySQL 5.1.47
- **Message Queue**: RabbitMQ
- **Utilities**: Hutool 5.7.15, MapStruct 1.4.2, PageHelper 5.3.0

### Module Structure

#### Core Business Modules
- **erp-system**: User management, regions, departments, dictionaries
- **erp-trader**: Customer/supplier management, business opportunities
- **erp-saleorder**: Sales orders, quotes, after-sales service
- **erp-buyorder**: Purchase orders, procurement after-sales
- **erp-goods**: Product information, inventory, categories
- **erp-wms**: Warehouse management, stock transfers
- **erp-finance**: Invoicing, payment terms, financial flows
- **erp-crm**: Customer relationship management
- **erp-oa**: Office automation workflows
- **erp-doc**: Document management
- **erp-kingdee**: Integration with Kingdee ERP system

#### Web Applications
- **erp-web**: Main web application with message queues and scheduled tasks
- **erp-web-mobile**: Mobile web app ("掌上小贝")
- **erp-web-crm**: CRM-specific web interface
- **erp-mobile**: Mobile API services

#### Infrastructure
- **erp-common**: Shared utilities (core, redis, mybatis, feign, etc.)
- **erp-infrastructure**: Third-party integrations (OSS, e-signature, SMS, express)
- **erp-old**: Legacy ERP code (maintenance only, no new features)

### Code Layer Structure

Each business module follows this pattern:

```
erp-xxx/
├── erp-xxx-api/           # External interfaces
│   ├── dto/               # Data transfer objects
│   ├── service/           # Interface definitions
│   └── remoteapi/         # Remote service interfaces
└── erp-xxx-biz/           # Business implementation
    ├── common/            # Utils, constants
    ├── config/            # Configuration
    ├── domain/            # Domain objects
    │   ├── entity/        # Database entities
    │   └── dto/           # Business objects
    ├── dao/               # Data access layer
    ├── manager/           # Third-party calls, transaction management
    ├── service/           # Business logic
    └── web/               # Web layer
        ├── api/           # Frontend APIs
        └── controller/    # Route controllers
```

## Development Guidelines

### Module Interaction
- **Never directly depend on other modules' implementation classes**
- **Use API interfaces** for cross-module communication
- **Leverage dependency inversion** principle through API modules

### Transaction Management
- Use `@Transactional` in **manager layer** for database operations
- Keep **service layer** focused on business logic
- Avoid long-running transactions in service methods

### Legacy Code
- **erp-old module**: Maintenance only, no new features
- New functionality should be implemented in corresponding new modules
- Use API interfaces to allow erp-old to call new module functionality

### Code Conventions
- Follow the comprehensive Java development standards outlined in README.md
- Use Lombok for boilerplate code reduction
- Entities end with `Entity`, DTOs with `DTO`, VOs with `VO`
- Maximum 3 parameters per method (encapsulate more in JavaBeans)
- Use meaningful method names with proper prefixes (get/list/count/save/update/remove)

### Testing
- Tests are configured to skip by default (`<skipTests>true</skipTests>`)
- Use `-DskipTests=false` to run tests explicitly
- Test files located in `src/test/java/com/test/`

### Database
- Required fields: `ID`, `ADD_TIME`, `MOD_TIME`
- Use wrapper types for primitives (Integer, Boolean, etc.)
- Enum fields must include all possible values in comments
- Follow database naming conventions with proper indexing

## Important Notes

- The project uses Git with branch-based development
- Commit messages should include Jira ticket numbers
- Code formatting and compilation checks are required before commits
- Maven repositories are configured for internal Vedeng nexus
- The system integrates with external services through erp-infrastructure