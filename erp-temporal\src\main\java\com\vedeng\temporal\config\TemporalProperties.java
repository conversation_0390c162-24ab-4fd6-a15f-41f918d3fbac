package com.vedeng.temporal.config;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * Temporal 统一配置属性类
 * 兼容 Spring 4.1.9，使用 @Value 注解统一管理所有配置，避免重复读取
 * 支持 Apollo 动态配置更新
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-11
 */
@Data
@Component
public class TemporalProperties {

    // ===== 服务器配置 =====
    @Value("${temporal.server.host:*************}")
    private String serverHost;
    
    @Value("${temporal.server.port:7233}")
    private int serverPort;
    
    // ===== 命名空间配置 =====
    @Value("${temporal.namespace:erp-namespace}")
    private String namespaceName;
    
    @Value("${temporal.namespace.description:ERP业务工作流命名空间}")
    private String namespaceDescription;
    
    @Value("${temporal.namespace.auto.create:true}")
    private boolean namespaceAutoCreate;
    
    @Value("${temporal.namespace.auto.update:true}")
    private boolean namespaceAutoUpdate;
    
    @Value("${temporal.namespace.retention.days:30}")
    private int namespaceRetentionDays;
    
    // ===== Worker 配置 =====
    @Value("${temporal.worker.threads:10}")
    private int workerThreads;
    
    @Value("${temporal.worker.max-concurrent-activity-executions:20}")
    private int workerMaxConcurrentActivityExecutions;
    
    @Value("${temporal.worker.max-concurrent-workflow-task-executions:10}")
    private int workerMaxConcurrentWorkflowTaskExecutions;
    
    // ===== 任务队列配置 =====
    @Value("${temporal.taskQueue.multiCompany:erp-multi-company-queue}")
    private String taskQueueMultiCompany;
    
    // =====   配置 =====
    @Value("${temporal.workflow.execution-timeout-hours:8}")
    private int workflowExecutionTimeoutHours;
    
    @Value("${temporal.workflow.task-timeout-minutes:10}")
    private int workflowTaskTimeoutMinutes;
    
    @Value("${temporal.workflow.id-prefix:multi-company-}")
    private String workflowIdPrefix;

    // ===== Activity 配置 =====
    @Value("${temporal.activity.start-to-close-timeout:30}")
    private int activityStartToCloseTimeoutMinutes;

    @Value("${temporal.activity.retry.maximum-attempts:3}")
    private int activityRetryMaximumAttempts;

    @Value("${temporal.activity.retry.initial-interval:10}")
    private int activityRetryInitialIntervalSeconds;

    @Value("${temporal.activity.retry.backoff-coefficient:2.0}")
    private double activityRetryBackoffCoefficient;
    
    // ===== 通知配置 =====
    @Value("${temporal.notification.robot.webhook:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fbe14b75-24cd-4690-9347-665c68769680}")
    private String notificationRobotWebhook;
    
    // ===== 轮询配置 =====
    @Value("${temporal.polling.enabled:true}")
    private boolean pollingEnabled;
    
    @Value("${temporal.polling.initial-interval-seconds:30}")
    private int pollingInitialIntervalSeconds;
    
    @Value("${temporal.polling.max-interval-seconds:300}")
    private int pollingMaxIntervalSeconds;
    
    @Value("${temporal.polling.backoff-coefficient:1.5}")
    private double pollingBackoffCoefficient;
    
    @Value("${temporal.polling.max-timeout-days:7}")
    private int pollingMaxTimeoutDays;
    
    @Value("${temporal.polling.heartbeat-interval-minutes:5}")
    private int pollingHeartbeatIntervalMinutes;
    
    @Value("${temporal.polling.max-retry-count:10}")
    private int pollingMaxRetryCount;
    
    @Value("${temporal.polling.verbose-logging:false}")
    private boolean pollingEnableVerboseLogging;

    // ===== 便捷访问方法 =====
    
    /**
     * 获取服务器地址和端口
     */
    public String getServerUrl() {
        return serverHost + ":" + serverPort;
    }
    
    /**
     * 服务器配置内部类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ServerConfig {
        private String host;
        private int port;
    }
    
    /**
     * 命名空间配置内部类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NamespaceConfig {
        private String name;
        private String description;
        private boolean autoCreate;
        private boolean autoUpdate;
        private int retentionDays;
    }
    
    /**
     * Worker配置内部类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WorkerConfig {
        private int threads;
        private int maxConcurrentActivityExecutions;
        private int maxConcurrentWorkflowTaskExecutions;
    }
    
    /**
     * 工作流配置内部类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WorkflowConfig {
        private int executionTimeoutHours;
        private int taskTimeoutMinutes;
        private String idPrefix;
        
        /**
         * 获取工作流执行超时时间
         */
        public Duration getExecutionTimeout() {
            return Duration.ofHours(executionTimeoutHours);
        }
        
        /**
         * 获取工作流任务超时时间
         */
        public Duration getTaskTimeout() {
            return Duration.ofMinutes(taskTimeoutMinutes);
        }
        
        /**
         * 获取工作流ID前缀
         */
        public String getIdPrefix() {
            return idPrefix;
        }
    }
    
    /**
     * 任务队列配置内部类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TaskQueueConfig {
        private String multiCompany;
    }

    /**
     * Activity 配置内部类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ActivityConfig {
        private int startToCloseTimeoutMinutes;
        private int retryMaximumAttempts;
        private int retryInitialIntervalSeconds;
        private double retryBackoffCoefficient;

        /**
         * 获取 Activity 执行超时时间
         */
        public Duration getStartToCloseTimeout() {
            return Duration.ofMinutes(startToCloseTimeoutMinutes);
        }

        /**
         * 获取 Activity 重试最大次数
         */
        public int getRetryMaximumAttempts() {
            return retryMaximumAttempts;
        }

        /**
         * 获取 Activity 重试初始间隔
         */
        public Duration getRetryInitialInterval() {
            return Duration.ofSeconds(retryInitialIntervalSeconds);
        }

        /**
         * 获取 Activity 重试最大间隔（使用 Temporal 默认值）
         */
        public Duration getRetryMaximumInterval() {
            // 使用 Temporal 默认的最大重试间隔（100倍初始间隔）
            return getRetryInitialInterval().multipliedBy(100);
        }

        /**
         * 获取 Activity 重试退避系数
         */
        public double getRetryBackoffCoefficient() {
            return retryBackoffCoefficient;
        }
    }


    
    /**
     * 通知配置内部类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NotificationConfig {
        private String robotWebhook;
    }
    
    /**
     * 轮询配置内部类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PollingConfig {
        private boolean enabled;
        private int initialIntervalSeconds;
        private int maxIntervalSeconds;
        private double backoffCoefficient;
        private int maxTimeoutDays;
        private int heartbeatIntervalMinutes;
        private int maxRetryCount;
        private boolean enableVerboseLogging;

        /**
         * 获取初始轮询间隔的 Duration 对象
         */
        public Duration getInitialIntervalDuration() {
            return Duration.ofSeconds(initialIntervalSeconds);
        }

        /**
         * 获取最大轮询间隔的 Duration 对象
         */
        public Duration getMaxIntervalDuration() {
            return Duration.ofSeconds(maxIntervalSeconds);
        }

        /**
         * 获取最大超时时间的 Duration 对象
         */
        public Duration getMaxTimeoutDuration() {
            return Duration.ofDays(maxTimeoutDays);
        }

        /**
         * 获取心跳间隔的 Duration 对象
         */
        public Duration getHeartbeatIntervalDuration() {
            return Duration.ofMinutes(heartbeatIntervalMinutes);
        }
    }
    
    // ===== 获取配置对象的方法 =====
    
    public ServerConfig getServer() {
        return new ServerConfig(serverHost, serverPort);
    }
    
    public NamespaceConfig getNamespace() {
        return new NamespaceConfig(namespaceName, namespaceDescription, 
                                 namespaceAutoCreate, namespaceAutoUpdate, namespaceRetentionDays);
    }
    
    public WorkerConfig getWorker() {
        return new WorkerConfig(workerThreads, workerMaxConcurrentActivityExecutions, 
                              workerMaxConcurrentWorkflowTaskExecutions);
    }
    
    public WorkflowConfig getWorkflow() {
        return new WorkflowConfig(workflowExecutionTimeoutHours, workflowTaskTimeoutMinutes, workflowIdPrefix);
    }
    
    public TaskQueueConfig getTaskQueue() {
        return new TaskQueueConfig(taskQueueMultiCompany);
    }

    public ActivityConfig getActivity() {
        return new ActivityConfig(activityStartToCloseTimeoutMinutes, activityRetryMaximumAttempts,
                                activityRetryInitialIntervalSeconds, activityRetryBackoffCoefficient);
    }
    
    public NotificationConfig getNotification() {
        return new NotificationConfig(notificationRobotWebhook);
    }
    
    public PollingConfig getPolling() {
        return new PollingConfig(pollingEnabled, pollingInitialIntervalSeconds, pollingMaxIntervalSeconds,
                               pollingBackoffCoefficient, pollingMaxTimeoutDays, pollingHeartbeatIntervalMinutes,
                               pollingMaxRetryCount, pollingEnableVerboseLogging);
    }
}
