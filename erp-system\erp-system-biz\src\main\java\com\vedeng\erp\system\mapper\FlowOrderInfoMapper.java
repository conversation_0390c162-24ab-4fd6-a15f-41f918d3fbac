package com.vedeng.erp.system.mapper;

import com.vedeng.erp.system.domain.entity.FlowOrderInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务流转单信息表Mapper
 */
public interface FlowOrderInfoMapper {
    /**
     * 根据主键删除
     *
     * @param flowOrderInfoId 主键
     * @return 删除条数
     */
    int deleteByPrimaryKey(Long flowOrderInfoId);

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 插入条数
     */
    int insert(FlowOrderInfoEntity record);

    /**
     * 选择性插入记录
     *
     * @param record 记录
     * @return 插入条数
     */
    int insertSelective(FlowOrderInfoEntity record);

    /**
     * 根据主键查询
     *
     * @param flowOrderInfoId 主键
     * @return 记录
     */
    FlowOrderInfoEntity selectByPrimaryKey(Long flowOrderInfoId);

    /**
     * 根据主键选择性更新
     *
     * @param record 记录
     * @return 更新条数
     */
    int updateByPrimaryKeySelective(FlowOrderInfoEntity record);

    /**
     * 根据主键更新
     *
     * @param record 记录
     * @return 更新条数
     */
    int updateByPrimaryKey(FlowOrderInfoEntity record);
    
    /**
     * 根据节点ID查询
     *
     * @param flowNodeId 节点ID
     * @return 记录列表
     */
    List<FlowOrderInfoEntity> findByFlowNodeId(@Param("flowNodeId") Long flowNodeId);

    List<FlowOrderInfoEntity> findByFlowOrderInfoNo(@Param("flowOrderInfoNo") String flowOrderInfoNo);


    /**
     * 根据节点ID和业务类型查询
     *
     * @param flowNodeId 节点ID
     * @param flowOrderInfoType 业务类型
     * @return 记录
     */
    FlowOrderInfoEntity findByFlowNodeIdAndType(@Param("flowNodeId") Long flowNodeId, @Param("flowOrderInfoType") Integer flowOrderInfoType);
}
