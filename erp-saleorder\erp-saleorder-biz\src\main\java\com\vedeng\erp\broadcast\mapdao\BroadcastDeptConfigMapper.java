package com.vedeng.erp.broadcast.mapdao;

import com.vedeng.erp.broadcast.domain.dto.BroadcastDeptConfigDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptConfigEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 部门播报前置配置Mapper
 */
public interface BroadcastDeptConfigMapper {

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 删除记录数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insert(BroadcastDeptConfigEntity record);

    /**
     * 选择性插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insertSelective(BroadcastDeptConfigEntity record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    BroadcastDeptConfigEntity selectByPrimaryKey(Integer id);

    /**
     * 选择性更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKeySelective(BroadcastDeptConfigEntity record);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKey(BroadcastDeptConfigEntity record);

    List<BroadcastDeptConfigDto> selectAll();

    /**
     * 逻辑删除所有部门配置记录
     *
     * @param userId 更新用户ID
     * @return 删除记录数
     */
    int deleteAllBroadcastDeptConfig(@Param("userId") Integer userId);

    /**
     * 批量插入部门配置记录
     *
     * @param list 配置记录列表
     * @return 插入记录数
     */
    int insertBatch(@Param("list") List<BroadcastDeptConfigEntity> list);
}
