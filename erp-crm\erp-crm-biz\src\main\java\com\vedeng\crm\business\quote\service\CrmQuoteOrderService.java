package com.vedeng.crm.business.quote.service;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.business.business.domain.entity.CrmBusinessChanceEntity;
import com.vedeng.crm.business.quote.domain.dto.CrmQuoteOrderDto;
import com.vedeng.crm.business.quote.domain.dto.CrmQuoteShareDto;
import com.vedeng.crm.business.quote.domain.dto.QuoteOrderGoodsExportVo;
import com.vedeng.crm.business.quote.domain.dto.QuoteValidRequestDto;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteOrderEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface CrmQuoteOrderService {

    CrmQuoteOrderDto selectOne(Integer quoteorderId);

    CrmQuoteOrderDto selectByBusinessChanceId(Integer businessChanceId);

    List<QuoteOrderGoodsExportVo> selectQuoteOrderGoodsExportVo(Integer quoteorderId,Boolean isNeeds);

    int updateQuoteOrderValid(QuoteValidRequestDto quoteValidRequestDto, CurrentUser currentUser);

    int updateQuoteOrder(CrmQuoteOrderDto crmQuoteOrderDto, CurrentUser currentUser);

    int getAuthorizationSum(Integer quoteorderId,Integer applyStatus);

    String selectBusinessChanceByQuoteorderId(Integer quoteorderId);

    /**
     * 根据报价单ID查询获取关联的商机信息
     * @param quoteorderId
     * @return
     */
    CrmBusinessChanceEntity selectBusinessChanceEntityByQuoteorderId(Integer quoteorderId);

    List<Map<String,Object>> checkSkuCheckStatusForQuoteShard(Integer quoteorderId);

    /**
     * 获取分享的信息
     * @param quoteorderId
     * @return
     */
    CrmQuoteShareDto getQuoteShardInfoById(Integer quoteorderId);

    /**
     * 获取
     * @param quoteorderId
     * @return
     */
    Integer getQuoteWithNoSkuInfoById(Integer quoteorderId);


    /**
     * 根据ID查询报价单信息
     * @param quoteorderId
     * @return
     */
    CrmQuoteOrderEntity selectByPrimaryKey(Integer quoteorderId);

    /**
     * 更新选择的用户信息
     * @param updateOrderDto
     * @param currentUser
     */
	void updateQuoteOrderForBuildUserIds(CrmQuoteOrderDto updateOrderDto, CurrentUser currentUser);

}

