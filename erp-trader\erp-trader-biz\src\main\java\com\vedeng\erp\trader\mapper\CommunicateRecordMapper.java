package com.vedeng.erp.trader.mapper;
import java.util.Collection;

import com.vedeng.erp.trader.dto.TagDto;
import org.apache.ibatis.annotations.Param;

import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecord;
import com.vedeng.erp.trader.dto.CommunicateTelRecordParams;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.domain.entity.CommunicateRecordEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 沟通记录(CommunicateRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-12 15:44:47
 */
@Repository("newCommunicateRecordMapper")
public interface CommunicateRecordMapper {

    /**
     * 查询指定行数据
     *
     * @param communicateRecordDto 查询条件
     * @return 对象列表
     */
    List<CommunicateRecordDto> findByAll(CommunicateRecordDto communicateRecordDto);

    /**
     * 新增数据
     *
     * @param tCommunicateRecord 实例对象
     * @return 影响行数
     */
    int insert(CommunicateRecordEntity tCommunicateRecord);

    /**
     * 新增数据
     *
     * @param communicateRecord 实例对象
     * @return 影响行数
     */
    int insertSelective(CommunicateRecordEntity communicateRecord);

    /**
     * 根据主机查信息
     *
     * @param communicateRecordDto
     * @return
     */
    CommunicateRecordEntity selectByPrimaryKey(CommunicateRecordDto communicateRecordDto);

    /**
     * 根据coid查询
     *
     * @param communicateRecordDto
     * @return
     */
    CommunicateRecordEntity selectByCoid(CommunicateRecordDto communicateRecordDto);

    /**
     * 根据关联业务id和业务类型查询沟通记录倒序获取最后一条沟通记录
     *
     * @param relatedId           关联业务id
     * @param communicateType 业务类型
     * @return List<CommunicateRecordEntity>
     */
    CommunicateRecordEntity findByRelatedIdAndCommunicateTypeOrderByCommunicateRecordIdDesc(@Param("relatedId") Integer relatedId,
                                                                                            @Param("communicateType") Integer communicateType);


    /**
     * 根据类型 时间 更新之前的处理   更新 IS_DONE 根据
     * NEXT_CONTACT_DATE
     * RELATED_ID
     * COMMUNICATE_TYPE
     *
     * @param communicateRecordEntity 对象
     * @return int
     */
    int updateIsDoneByNextContactDateAndRelatedIdAndCommunicateType(CommunicateRecordEntity communicateRecordEntity);

    /**
     * 更新
     *
     * @param communicateRecordEntity 对象
     * @return int
     */
    int updateByPrimaryKeySelective(CommunicateRecordEntity communicateRecordEntity);

    int updateByCoid(CommunicateRecordEntity communicateRecordEntity);

    Integer selectByRelatedIdAndCommunicateType(CommunicateRecordDto communicateRecordDto);

    List<TagDto> getCommunicateTags(@Param("communicateRecordId") Integer communicateRecordId) ;

    List<CommunicateRecordEntity> findByBegintimeBetween(@Param("minBegintime")Long minBegintime,@Param("maxBegintime")Long maxBegintime);


    CommunicateRecordEntity findByCommunicateRecordId(@Param("communicateRecordId")Integer communicateRecordId);

    List<CommunicateRecordEntity> findByAddTimeBetweenAndCreator(@Param("minAddTime")Long minAddTime,@Param("maxAddTime")Long maxAddTime,@Param("creator")Integer creator);

    List<CommunicateRecordEntity> findByCommunicateRecordIdIn(@Param("communicateRecordIdCollection")Collection<Integer> communicateRecordIdCollection);

    CommunicateRecordEntity getLastCommunicateRecord(@Param("relatedId") Integer relatedId, @Param("communicateType") Integer communicateType);

    /**
     * 查询通话记录（灵犀）
     * @param communicateTelRecord
     * @return
     */
	List<CommunicateTelRecord> getTelList(CommunicateTelRecordParams communicateTelRecord);
}

