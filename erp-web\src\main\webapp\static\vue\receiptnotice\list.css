.list-container {
  max-width: 1680px;
  min-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}
.list-container .list-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 20px;
  padding-bottom: 10px;
}
.list-container .list-top .title {
  font-size: 20px;
  font-weight: 700;
}
.list-container .list-top .list-top-options {
  display: flex;
  align-items: center;
}
.list-container .list-top .list-top-options .vd-ui-button {
  margin-left: 10px;
}
.list-container .list-filter {
  background: #fff;
  padding: 10px 20px 20px;
  margin: 0 auto 20px;
}
.list-container .list-filter .list-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.list-container .list-filter .list-row .form-item {
  margin-top: 10px;
}
.list-container .list-filter .list-row .form-item .form-fields {
  width: 200px;
}
.list-container .list-filter .list-row .form-item .form-fields .vd-ui-select {
  width: 100%;
}
.list-container .list-filter .list-row .filter-btns {
  display: flex;
  align-items: center;
  margin-top: 10px;
}
.list-container .list-wrap {
  padding: 10px 15px;
  background: #fff;
}
.list-container .list-wrap .list-pagination {
  margin-top: 20px;
  text-align: right;
}
