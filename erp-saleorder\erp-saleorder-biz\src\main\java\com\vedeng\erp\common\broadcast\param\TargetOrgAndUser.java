package com.vedeng.erp.common.broadcast.param;

import java.util.List;
import lombok.Data;

/**
 * <br>根据播报目标属性，获取播报目标所关联的组织ID列表，以及自定义的需要临时加入的用户ID和临时排除的用户ID
 * <br>小组名称、小组ID 与 部门名称、部门ID 可能为空
 * <br>小组名称、小组ID不为空时，orgIdList属于该小组
 * <br>部门名称、部门ID不为空时，orgIdList属于该部门
 * @ClassName:  TargetOrgAndUser   
 * @author: <PERSON>.yang
 * @date:   2025年6月6日 下午5:08:12    
 * @Copyright:
 */
@Data
public class TargetOrgAndUser {
	
	/**小组名称*/
	private String teamName;
	
	/**小组ID*/
	private Integer teamId;
	
	/**部门名称*/
	private String deptName;
	
	/**部门ID*/
	private Integer deptId;
	
	/**组织ID*/
	private List<Integer> orgIdList;
	
	/**自定义需要临时加入的用户ID*/
	private List<Integer> inUserIdList;
	
	/**自定义需要临时加入的用户所属的组织ID*/
	private List<Integer> inUserIdBelongOrgIdList;
	
	/**自定义需要临时排除的用户ID*/
	private List<Integer> outUserIdList;

}
