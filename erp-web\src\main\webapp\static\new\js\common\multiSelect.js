; void function () {

    var defaults = {
        el: '',
        options: [],
        placeholder: '请选择',
        value: [],
        size: ''
    };

    var getWrapHtml = function (config) {
        return `
            <div class="multi-select-wrap">
                <div class="multi-select-label J-multi-select-trigger ${config.size}">
                    <div class="multi-select-label-txt J-multi-select-label-wrap placeholder">${config.placeholder}</div>
                    <i class="vd-icon icon-down"></i>
                    <i class="vd-icon icon-error2 J-multi-clear"></i>
                </div>
                <div class="multi-select-drop J-multi-select-drop">
                    <div class="multi-select-list J-multi-select-list"></div>
                </div>  
            </div>
        `;
    }

    var MultiSelect = function (config) {
        this.config = $.extend({}, defaults, config);
        this.__init();
        return this;
    }

    MultiSelect.prototype = {
        constructor: 'MultiSelect',
        __init: function () {
            this.$wrap = $(this.config.el);
            this.value = this.config.value || [];

            this.__initTmpl();

            this.__bindEvent();

        },
        __initTmpl: function () {
            var _this = this;
            this.$wrap.append(getWrapHtml(this.config));
            $.each(this.config.options, function (i, item) {
                $('.J-multi-select-list', _this.$wrap).append(`<div class="multi-select-option J-multi-select-option" data-label="${item.label}" data-value="${item.value}">
                    <i class="vd-icon icon-checkbox1"></i>
                    <i class="vd-icon icon-checkbox2"></i>
                    <div class="multi-select-option-txt">${item.label}</div>
                </div>`)
            })
        },
        __bindEvent: function () {
            var _this = this;

            $('.J-multi-select-option', this.$wrap).click(function (e) {
                e.stopPropagation();

                var value = $(this).data('value');

                if ($(this).hasClass('checked')) {
                    $(this).removeClass('checked');

                    var index = _this.value.indexOf(value);

                    if (index !== -1) {
                        _this.value.splice(index, 1);
                    }
                } else {
                    $(this).addClass('checked');
                    _this.value.push(value);
                }

                _this.__triggerChange();
            })

            $(document).click(function() {
                $('.J-multi-select-trigger', _this.$wrap).removeClass('open');
                $('.J-multi-select-drop').slideUp(220);
            })

            $('.J-multi-select-trigger', this.$wrap).click(function(e) {
                e.stopPropagation();

                if($(this).hasClass('open')) {
                    $(this).removeClass('open');
                    $('.J-multi-select-drop', this.$wrap).slideUp(220);
                } else {
                    $(this).addClass('open');
                    $('.J-multi-select-drop', this.$wrap).slideDown(220);
                }
            })

            $('.J-multi-clear', this.$wrap).click(function() {
                _this.setValue([]);
            })
        },
        setValue(value) {
            this.value = value;
            this.__refreshOption();
            this.__triggerChange();
        },
        __triggerChange() {
            this.__refreshLabel();

            this.config.change && this.config.change(this.value);
        },
        __refreshOption() {
            var _this = this;
            $('.J-multi-select-option', this.$wrap).each(function() {
                var value = $(this).data('value');

                if(_this.value.indexOf(value) !== -1) {
                    $(this).addClass('checked');
                } else {
                    $(this).removeClass('checked');
                }
            })
        },
        __refreshLabel() {
            var _this = this;
            var $checked = $('.J-multi-select-option.checked', this.$wrap);

            var $label = $('.J-multi-select-label-wrap', this.$wrap);
            $label.empty();

            if ($checked.length) {
                $label.removeClass('placeholder');
                $('.J-multi-select-trigger', this.$wrap).addClass('on-select');
                $checked.each(function (i, item) {
                    var label = $(this).data('label');
                    var value = $(this).data('value');
                    $label.append(`<div class="select-label-item J-select-label-item">
                        <div class="select-label-item-txt">${label}</div>
                        <i class="vd-icon icon-delete J-select-label-delete" data-value="${value}"></i>
                    </div>`)
                })

                var top = 0;
                var num = 0;

                $('.J-select-label-item', this.$wrap).each(function(i) {
                    if(i === 0) {
                        top = $(this).offset().top;
                    }
                    
                    if($(this).offset().top !== top) {
                        num++;
                        $(this).addClass('need-hide');
                    }
                })

                $('.J-select-label-item.need-hide', this.$wrap).hide();

                if(num) {
                    $label.append(`
                        <div class="select-label-item item-num J-select-trigger-num">
                            <div class="select-label-item-txt">+${num}</div>
                        </div>
                    `)

                    var checkNumTop = function() {
                        if($('.J-select-trigger-num', this.$wrap).offset().top !== top) {
                            $('.J-select-label-item', this.$wrap).eq($('.J-select-label-item', this.$wrap).length - num - 1).hide();
                            num++;
                            $('.J-select-trigger-num .select-label-item-txt', this.$wrap).html('+' + num);

                            checkNumTop();
                        }
                    }

                    checkNumTop();
                }

                $('.J-select-label-delete', _this.$wrap).click(function(e) {
                    e.stopPropagation();
                    var value = $(this).data('value');
    
                    _this.value.splice(_this.value.indexOf(value), 1);
                    _this.__refreshOption();
                    _this.__triggerChange();
                })
            } else {
                $label.html(this.config.placeholder).addClass('placeholder');
                $('.J-multi-select-trigger', this.$wrap).removeClass('on-select');
            }
        }
    }

    window.MultiSelect = MultiSelect;


}.call(this);