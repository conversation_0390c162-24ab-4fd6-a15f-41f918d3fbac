package com.vedeng.aftersales.constant;

public enum AfterSaleServiceLevelEnum {
    LEVEL_1(1, "一星级"),
    LEVEL_2(2, "二星级"),
    LEVEL_3(3, "三星级"),
    LEVEL_4(4, "四星级"),
    LEVEL_5(5, "五星级")
    ,LEVEL_6(6, "无需评级")
    ,
    LEVEL_0(0, "待评级");

    private final int level;
    private final String description;

    AfterSaleServiceLevelEnum(int level, String description) {
        this.level = level;
        this.description = description;
    }

    public int getLevel() {
        return level;
    }

    public String getDescription() {
        return description;
    }

    public static AfterSaleServiceLevelEnum fromLevel(int level) {
        for (AfterSaleServiceLevelEnum serviceLevel : values()) {
            if (serviceLevel.getLevel() == level) {
                return serviceLevel;
            }
        }
        throw new IllegalArgumentException("Invalid service level: " + level);
    }
}
