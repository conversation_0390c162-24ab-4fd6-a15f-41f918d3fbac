package com.vedeng.erp.broadcast.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.util.Date;

/**
 * 播报用户列表DTO
 * 专门用于分页查询展示，字段扁平化设计，提高查询性能
 * 对应人员管理页面的表格数据展示
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BroadCastUserListDto {
    
    /**
     * 小组ID
     * 对应T_BROADCAST_R_AED_USER表的主键ID
     */
    private Integer id;
    
    /**
     * 用户名
     * ERP用户的登录用户名，用于页面显示
     */
    private String erpUsername;
    
    /**
     * 员工姓名
     * ERP用户的真实姓名，用于页面显示
     */
    private String erpRealName;
    
    /**
     * AED销售
     * AED系统中对应销售的用户名，用于页面显示
     */
    private String aedUsername;
    
    /**
     * AED销售姓名
     * AED系统中对应销售的真实姓名，用于页面显示
     */
    private String aedRealName;
    
    /**
     * 创建人
     * 创建此关联关系的用户姓名
     */
    private String creatorRealName;
    
    /**
     * 创建时间
     * 关联关系的创建时间，用于页面显示
     * 格式：yyyy-MM-dd HH:mm:ss
     * 对应数据库字段：ADD_TIME
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;
    
    // ========== 内部使用字段，不对外暴露 ==========
    
    /**
     * ERP用户ID
     * 内部使用，用于关联查询和操作
     */
    private Integer erpUserId;
    
    /**
     * AED用户ID
     * 内部使用，用于关联查询和操作
     */
    private Integer aedUserId;
    
    /**
     * 创建人ID
     * 内部使用，用于权限控制和审计
     */
    private Integer creator;
    
    /**
     * 是否删除
     * 0-正常，1-已删除
     */
    private Integer isDeleted;
} 