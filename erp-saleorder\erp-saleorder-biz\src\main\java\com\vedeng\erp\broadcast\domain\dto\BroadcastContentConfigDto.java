package com.vedeng.erp.broadcast.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * 播报内容配置DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BroadcastContentConfigDto extends BaseDto {
    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空", groups = {UpdateGroup.class})
    private Integer id;

    /**
     * 图片URL
     */
    @NotEmpty(message = "图片URL不能为空", groups = {AddGroup.class})
    private String picUrl;

    /**
     * 图片名称
     */
    @NotEmpty(message = "图片名称不能为空", groups = {AddGroup.class})
    private String picName;

    /**
     * 专属类型：1=个人，2=团队，3=项目
     */
    @NotNull(message = "专属类型不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private Integer exclusiveType;

    /**
     * 专属目标，逗号分隔：个人 用户ID; 部门 部门ID; 项目 1=月度AED TOP，2=月度自有品牌TOP
     */
    private String exclusiveTargetValues;

    /**
     * 用于搜索的文本，根据专属类型，专属目标保存相关文本
     */
    private String exclusiveTargetLabels;

    /**
     * 是否已删除（0=否，1=是）
     */
    private Integer isDeleted;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 更新人姓名
     */
    private String updaterName;
}
