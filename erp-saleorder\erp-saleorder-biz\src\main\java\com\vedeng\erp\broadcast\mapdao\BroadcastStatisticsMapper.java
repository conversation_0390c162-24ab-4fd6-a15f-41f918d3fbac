package com.vedeng.erp.broadcast.mapdao;

import com.vedeng.erp.common.broadcast.param.UserOrgInfo;
import com.vedeng.erp.common.broadcast.statistics.StatisticsDto;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * 播报项目统计 Mapper
 * @ClassName:  BroadcastStatisticsMapper   
 * @author: <PERSON>.yang
 * @date:   2025年6月9日 下午4:45:37    
 * @Copyright:
 */
public interface BroadcastStatisticsMapper {

	
	/**
     * 个人 == 到款查询（日、周、月到款等）
     * @param startTime 统计开始时间
     * @param endTime 统计结束时间
     * @param orgIdList 组织ID
     * @param excludeSaleIds 要排除的销售ID
     * @param excludeTraderIds 要排除的客户ID
     * @param inUserIdList 要统计进入的用户ID
     * @param outUserIdList 要排除的用户ID
     * @return
     */
	List<StatisticsDto> selectStatisticsAmountByPersonParams(@Param("startTime") long startTime, @Param("endTime") long endTime,@Param("orgIdList") List<Integer> orgIdList,
			@Param("excludeSaleIds") List<String> excludeSaleIds, @Param("excludeTraderIds") List<String> excludeTraderIds,@Param("inUserIdList") List<Integer> inUserIdList,
			@Param("outUserIdList") List<Integer> outUserIdList,@Param("inUserIdBelongOrgIdList")  List<Integer> inUserIdBelongOrgIdList);
	
	/**
     * 小组、部门 == 到款查询（日、周、月到款等）
     * @param startTime 统计开始时间
     * @param endTime 统计结束时间
     * @param orgIdList 组织ID
     * @param excludeSaleIds 要排除的销售ID
     * @param excludeTraderIds 要排除的客户ID
     * @param inUserIdList 要统计进入的用户ID
     * @param outUserIdList 要排除的用户ID
	 * @param list 
     * @return
     */
	StatisticsDto selectStatisticsAmountByTeamParams(@Param("startTime") long startTime, @Param("endTime") long endTime,@Param("orgIdList") List<Integer> orgIdList,
			@Param("excludeSaleIds") List<String> excludeSaleIds, @Param("excludeTraderIds") List<String> excludeTraderIds,@Param("inUserIdList") List<Integer> inUserIdList,
			@Param("outUserIdList") List<Integer> outUserIdList,@Param("inUserIdBelongOrgIdList")  List<Integer> inUserIdBelongOrgIdList);
	
	
	/**
	 * 月度AED出库量
	 * @param startTime
	 * @param endTime
	 * @param excludeTraderIds
	 * @param outUserIdList
	 * @param statSkuIds
	 * @param statBrandIds
	 * @param statCategoryIds
	 * @return
	 */
	List<StatisticsDto> selectStatisticsAedWarehouse(@Param("startTime") Date startTime, @Param("endTime") Date endTime,@Param("aedSkuIds") List<String> aedSkuIds,@Param("excludeSaleIds") List<String> excludeSaleIds,
			@Param("statSkuIds") List<String> statSkuIds,@Param("statBrandIds") List<String> statBrandIds,@Param("statCategoryIds") List<String> statCategoryIds,
			@Param("isUserDefine") Integer isUserDefine);
	
	
	/**
	 * 月度自由品牌出库金额
	 * @param startTime
	 * @param endTime
	 * @param excludeTraderIds
	 * @param outUserIdList
	 * @param statSkuIds
	 * @param statBrandIds
	 * @param statCategoryIds
	 * @return
	 */
	List<StatisticsDto> selectStatisticsAedAmount(@Param("startTime") Date startTime, @Param("endTime") Date endTime,@Param("excludeSaleIds") List<String> excludeSaleIds,
			@Param("statSkuIds") List<String> statSkuIds,@Param("statBrandIds") List<String> statBrandIds,@Param("statCategoryIds") List<String> statCategoryIds
			,@Param("isUserDefine") Integer isUserDefine);

	
    
}
