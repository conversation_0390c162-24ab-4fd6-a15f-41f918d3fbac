package com.vedeng.goods.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品信息运输类
 *
 * <AUTHOR>
 */
@Data
public class GoodsSearchBaseInfoDto {

    private Integer skuId;

    private String skuNo;

    private String skuName;

    private String goodsLevel;

    private String goodsPosition;

    private BigDecimal terminalPrice;

    private BigDecimal distributionPrice;

    private Integer availableStock;

    private Integer stock;

    private Long purchaseTime;

    private String unit;

    private String salerOfLastOrder;

    private String registerNumber;

    private Integer registerNumberId;

    private Integer firstEngageId;

    private String checkStatus;

    private String taxCategoryNo;

    private String classIficationAbbreviation;

    private String spuType;

    private String category;

    private String brandName;

    private Integer saleCountOfLastYear;

    private BigDecimal avgPriceOfLastYear;

    private String skuAssignments;

    private String occupyNum;

    /**
     * 在途库存
     */
    private String onWayNum;

    /**
     * 型号
     */
    private String model;

    /**
     * 规格
     */
    private String spec;

    /**
     * 直发货期范围
     */
    private String directDeliveryTime;

    /**
     * 普发货期范围
     */
    private String commonDeliveryTime;


    /**
     * 使用年限/单位：年
     */
    private Integer serviceLife;

    /**
     * DI_CODE 商品UDI-DI码 VDERP-17675 商品详情页：增加商品DI字段
     */
    private String diCode;
    /**
     * 售后服务级别 5五星级、4四星级、3三星级、2二星级、1一星级、0待评级
     */
    private Integer afterSalesServiceLevel;
}
