<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.CoreSkuGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CoreSkuGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <id column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SPU_ID" jdbcType="INTEGER" property="spuId" />
    <result column="CHECK_STATUS" jdbcType="TINYINT" property="checkStatus" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="SPEC" jdbcType="VARCHAR" property="spec" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
    <result column="MATERIAL_CODE" jdbcType="VARCHAR" property="materialCode" />
    <result column="SUPPLY_MODEL" jdbcType="VARCHAR" property="supplyModel" />
    <result column="IS_STOCKUP" jdbcType="VARCHAR" property="isStockup" />
    <result column="WIKI_HREF" jdbcType="VARCHAR" property="wikiHref" />
    <result column="TECHNICAL_PARAMETER" jdbcType="VARCHAR" property="technicalParameter" />
    <result column="PERFORMANCE_PARAMETER" jdbcType="VARCHAR" property="performanceParameter" />
    <result column="SPEC_PARAMETER" jdbcType="VARCHAR" property="specParameter" />
    <result column="BASE_UNIT_ID" jdbcType="INTEGER" property="baseUnitId" />
    <result column="MIN_ORDER" jdbcType="DECIMAL" property="minOrder" />
    <result column="GOODS_LENGTH" jdbcType="DECIMAL" property="goodsLength" />
    <result column="GOODS_WIDTH" jdbcType="DECIMAL" property="goodsWidth" />
    <result column="GOODS_HEIGHT" jdbcType="DECIMAL" property="goodsHeight" />
    <result column="PACKAGE_LENGTH" jdbcType="DECIMAL" property="packageLength" />
    <result column="PACKAGE_WIDTH" jdbcType="DECIMAL" property="packageWidth" />
    <result column="PACKAGE_HEIGHT" jdbcType="DECIMAL" property="packageHeight" />
    <result column="NET_WEIGHT" jdbcType="DECIMAL" property="netWeight" />
    <result column="GROSS_WEIGHT" jdbcType="DECIMAL" property="grossWeight" />
    <result column="UNIT_ID" jdbcType="INTEGER" property="unitId" />
    <result column="CHANGE_NUM" jdbcType="DECIMAL" property="changeNum" />
    <result column="PACKING_LIST" jdbcType="VARCHAR" property="packingList" />
    <result column="AFTER_SALE_CONTENT" jdbcType="VARCHAR" property="afterSaleContent" />
    <result column="QA_YEARS" jdbcType="VARCHAR" property="qaYears" />
    <!--存储条件标准化 since ERP_LV_2020_67-->
    <result column="STORAGE_CONDITION_ONE" jdbcType="TINYINT" property="storageConditionOne" />
    <result column="STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionOneLowerValue" />
    <result column="STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionOneUpperValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityLowerValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityUpperValue" />
    <result column="STORAGE_CONDITION_TWO" jdbcType="VARCHAR" property="storageConditionTwo" />

    <result column="EFFECTIVE_DAY_UNIT" jdbcType="TINYINT" property="effectiveDayUnit" />
    <result column="EFFECTIVE_DAYS" jdbcType="VARCHAR" property="effectiveDays" />
    <result column="QA_RULE" jdbcType="VARCHAR" property="qaRule" />
    <result column="QA_OUT_PRICE" jdbcType="DECIMAL" property="qaOutPrice" />
    <result column="QA_RESPONSE_TIME" jdbcType="DECIMAL" property="qaResponseTime" />
    <result column="HAS_BACKUP_MACHINE" jdbcType="VARCHAR" property="hasBackupMachine" />
    <result column="SUPPLIER_EXTEND_GUARANTEE_PRICE" jdbcType="DECIMAL" property="supplierExtendGuaranteePrice" />
    <result column="CORE_PARTS_PRICE_FID" jdbcType="INTEGER" property="corePartsPriceFid" />
    <result column="RETURN_GOODS_CONDITIONS" jdbcType="TINYINT" property="returnGoodsConditions" />
    <result column="FREIGHT_INTRODUCTIONS" jdbcType="VARCHAR" property="freightIntroductions" />
    <result column="EXCHANGE_GOODS_CONDITIONS" jdbcType="VARCHAR" property="exchangeGoodsConditions" />
    <result column="EXCHANGE_GOODS_METHOD" jdbcType="VARCHAR" property="exchangeGoodsMethod" />
    <result column="GOODS_COMMENTS" jdbcType="VARCHAR" property="goodsComments" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CHECK_TIME" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="CHECKER" jdbcType="INTEGER" property="checker" />
    <result column="OPERATE_INFO_ID" jdbcType="INTEGER" property="operateInfoId" />
    <result column="DELETE_REASON" jdbcType="VARCHAR" property="deleteReason" />
    <result column="LAST_CHECK_REASON" jdbcType="VARCHAR" property="lastCheckReason" />
    <result column="TAX_CATEGORY_NO" jdbcType="VARCHAR" property="taxCategoryNo" />
    <result column="JX_MARKET_PRICE" jdbcType="DECIMAL" property="jxMarketPrice" />
    <result column="JX_SALE_PRICE" jdbcType="DECIMAL" property="jxSalePrice" />
    <result column="JX_FLAG" jdbcType="INTEGER" property="jxFlag" />
    <result column="SOURCE" jdbcType="TINYINT" property="source" />
    <!-- add by Tomcat.Hui 2020/5/14 4:50 下午 .Desc: VDERP-2217 提供预计发货时间给前台. start -->
    <result column="DECLARE_DELIVERY_RANGE" jdbcType="VARCHAR" property="declareDeliveryRange" />
    <result column="DELIVERY_RANGE" jdbcType="VARCHAR" property="deliveryRange" />
    <!-- add by Tomcat.Hui 2020/5/14 4:50 下午 .Desc: VDERP-2217 提供预计发货时间给前台. end -->
    <result column="ON_SALE" jdbcType="TINYINT" property="onSale" />
    <result column="PUSH_STATUS" jdbcType="INTEGER" property="pushStatus" />
    <!--新增字段 -->
    <result column="GOODS_BARCODE" jdbcType="VARCHAR" property="goodsBarcode" />
    <result column="CURING_TYPE" jdbcType="TINYINT" property="curingType" />
    <result column="CURING_REASON" jdbcType="VARCHAR" property="curingReason" />
    <result column="REGULAR_MAINTAIN_TYPE" jdbcType="TINYINT" property="regularMaintainType" />
    <result column="REGULAR_MAINTAIN_REASON" jdbcType="VARCHAR" property="regularMaintainReason" />
    <result column="IS_NEED_TEST_REPROT" jdbcType="TINYINT" property="isNeedTestReprot" />
    <result column="IS_KIT" jdbcType="TINYINT" property="isKit" />
    <result column="KIT_DESC" jdbcType="VARCHAR" property="kitDesc" />
    <result column="IS_SAME_SN_CODE" jdbcType="TINYINT" property="isSameSnCode" />
    <result column="IS_FACTORY_SN_CODE" jdbcType="TINYINT" property="isFactorySnCode" />
    <result column="IS_MANAGE_VEDENG_CODE" jdbcType="TINYINT" property="isManageVedengCode" />
    <result column="IS_BAD_GOODS" jdbcType="TINYINT" property="isBadGoods" />
    <result column="IS_ENABLE_FACTORY_BATCHNUM" jdbcType="TINYINT" property="isEnableFactoryBatchnum" />
    <result column="IS_ENABLE_MULTISTAGE_PACKAGE" jdbcType="TINYINT" property="isEnableMultistagePackage" />
    <result column="INSTALL_TRAIN_TYPE" jdbcType="TINYINT" property="installTrainType" />
    <result column="LOGISTICS_DELIVERYTYPE" jdbcType="TINYINT" property="logisticsDeliveryType" />
    <result column="MID_PACKAGE_NUM" jdbcType="INTEGER" property="midPackageNum" />
    <result column="BOX_PACKAGE_NUM" jdbcType="INTEGER" property="boxPackageNum" />
    <result column="IS_ENABLE_VALIDITY_PERIOD" jdbcType="TINYINT" property="isEnableValidityPeriod" />
    <result column="NEAR_TERM_WARN_DAYS" jdbcType="INTEGER" property="nearTermWarnDays" />
    <result column="OVER_NEAR_TERM_WARN_DAYS" jdbcType="INTEGER" property="overNearTermWarnDays" />
    <result column="IS_NEED_REPORT" jdbcType="TINYINT" property="isNeedReport" />
    <result column="IS_AUTHORIZED" jdbcType="TINYINT" property="isAuthorized" />
    <result column="HISTORY_NAME" jdbcType="VARCHAR" property="historyName" />
    <result column="IS_NAME_CHANGE" jdbcType="TINYINT" property="isNameChange" />
    <result column="GOODS_LEVEL_NO" jdbcType="INTEGER" property="goodsLevelNo" />
    <result column="GOODS_POSITION_NO" jdbcType="INTEGER" property="goodsPositionNo" />
    <result column="IS_INSTALLABLE" jdbcType="INTEGER" property="isInstallable" />
    <result column="SYNCHRONIZATION_STATUS" jdbcType="TINYINT" property="synchronizationStatus" />
    <result column="DISABLED_REASON" jdbcType="VARCHAR" property="disabledReason" />
    <result column="PURCHASE_TIME" jdbcType="INTEGER" property="perchaseTime" />
    <result column="INSTITUTION_LEVEL_IDS" jdbcType="VARCHAR" property="institutionLevelIds" />
    <result column="IS_DIRECT" jdbcType="INTEGER" property="isDirect"/>
    <result column="TAX_CATEGORY_NO_RECORD" jdbcType="VARCHAR" property="taxCategoryNoRecord"/>
    <result column="SERVICE_LIFE" jdbcType="INTEGER" property="serviceLife"/>
    <result column="DI_CODE" jdbcType="VARCHAR" property="diCode"/>
    <result column="AFTER_SALES_SERVICE_LEVEL" jdbcType="INTEGER" property="afterSalesServiceLevel"/>
  </resultMap>

  <resultMap id="wmsInputSku" type="com.wms.model.po.WmsCoreSku" extends="BaseResultMap">
    <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="BRAND_NATURE" jdbcType="INTEGER" property="brandNature" />
    <result column="MANAGE_CATEGORY_LEVEL" jdbcType="INTEGER" property="manageCategoryLevel" />
    <result column="SPU_TYPE" jdbcType="INTEGER" property="spuType" />
    <result column="PRODUCT_COMPANY_CHINESE_NAME" jdbcType="VARCHAR" property="productCompanyChineseName" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    SKU_ID, SPU_ID, CHECK_STATUS, MODEL, SPEC, SKU_NO, SKU_NAME, SHOW_NAME, MATERIAL_CODE,
    SUPPLY_MODEL, IS_STOCKUP, WIKI_HREF, TECHNICAL_PARAMETER, PERFORMANCE_PARAMETER,
    SPEC_PARAMETER, BASE_UNIT_ID, MIN_ORDER, GOODS_LENGTH, GOODS_WIDTH, GOODS_HEIGHT,
    PACKAGE_LENGTH, PACKAGE_WIDTH, PACKAGE_HEIGHT, NET_WEIGHT, GROSS_WEIGHT, UNIT_ID,
    CHANGE_NUM, PACKING_LIST, AFTER_SALE_CONTENT, QA_YEARS,REGULAR_MAINTAIN_TYPE,REGULAR_MAINTAIN_REASON,
    IS_AVAILABLE_SALE, ORG_ID_LIST, PUSHED_ORG_ID_LIST,CONFIGURATION_LIST,IS_DIRECT,TAX_CATEGORY_NO_RECORD,SERVICE_LIFE,DI_CODE,
    <include refid="GoodsStorageConditionColumn"/>,
    EFFECTIVE_DAY_UNIT, EFFECTIVE_DAYS, QA_RULE, QA_OUT_PRICE, QA_RESPONSE_TIME, HAS_BACKUP_MACHINE,
    SUPPLIER_EXTEND_GUARANTEE_PRICE, CORE_PARTS_PRICE_FID, RETURN_GOODS_CONDITIONS, FREIGHT_INTRODUCTIONS,
    EXCHANGE_GOODS_CONDITIONS, EXCHANGE_GOODS_METHOD, GOODS_COMMENTS, `STATUS`, ADD_TIME,
    CREATOR, MOD_TIME, UPDATER, CHECK_TIME, CHECKER, OPERATE_INFO_ID, DELETE_REASON,
    LAST_CHECK_REASON, TAX_CATEGORY_NO, JX_MARKET_PRICE, JX_SALE_PRICE, JX_FLAG, `SOURCE`,DECLARE_DELIVERY_RANGE,
ON_SALE, PUSH_STATUS,HISTORY_NAME,IS_NAME_CHANGE,
    GOODS_BARCODE,CURING_TYPE,CURING_REASON,IS_NEED_TEST_REPROT,IS_KIT,
    KIT_DESC,IS_SAME_SN_CODE,IS_FACTORY_SN_CODE,IS_MANAGE_VEDENG_CODE,IS_BAD_GOODS,
    IS_ENABLE_FACTORY_BATCHNUM,IS_ENABLE_MULTISTAGE_PACKAGE,
    MID_PACKAGE_NUM,BOX_PACKAGE_NUM,NEAR_TERM_WARN_DAYS,OVER_NEAR_TERM_WARN_DAYS,INSTALL_TRAIN_TYPE,LOGISTICS_DELIVERYTYPE,IS_ENABLE_VALIDITY_PERIOD,
    IS_NEED_REPORT,IS_AUTHORIZED,GOODS_LEVEL_NO,GOODS_POSITION_NO,IS_INSTALLABLE,SYNCHRONIZATION_STATUS,DISABLED_REASON,PURCHASE_TIME,INSTITUTION_LEVEL_IDS
          ,AFTER_SALES_SERVICE_LEVEL
  </sql>
  <sql id="GoodsStorageConditionColumn">
    STORAGE_CONDITION_ONE, STORAGE_CONDITION_ONE_LOWER_VALUE, STORAGE_CONDITION_ONE_UPPER_VALUE,
    STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
    STORAGE_CONDITION_TWO
  </sql>

  <select id="selectByExample" parameterType="com.vedeng.goods.model.CoreSkuGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from V_CORE_SKU
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    select
    <include refid="Base_Column_List" />
    from V_CORE_SKU
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    delete from V_CORE_SKU
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.CoreSkuGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    delete from V_CORE_SKU
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.CoreSkuGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <selectKey keyProperty="skuId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_CORE_SKU (SPU_ID, CHECK_STATUS, MODEL,
    SPEC, SKU_NO, SKU_NAME,
    SHOW_NAME, MATERIAL_CODE, SUPPLY_MODEL,
    IS_STOCKUP, WIKI_HREF, TECHNICAL_PARAMETER,
    PERFORMANCE_PARAMETER, SPEC_PARAMETER, BASE_UNIT_ID,
    MIN_ORDER, GOODS_LENGTH, GOODS_WIDTH,
    GOODS_HEIGHT, PACKAGE_LENGTH, PACKAGE_WIDTH,
    PACKAGE_HEIGHT, NET_WEIGHT, GROSS_WEIGHT,
    UNIT_ID, CHANGE_NUM, PACKING_LIST,
    AFTER_SALE_CONTENT, QA_YEARS, STORAGE_CONDITION_ONE,
    STORAGE_CONDITION_ONE_LOWER_VALUE, STORAGE_CONDITION_ONE_UPPER_VALUE,
    STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
    STORAGE_CONDITION_TWO, EFFECTIVE_DAY_UNIT,
    EFFECTIVE_DAYS, QA_RULE, QA_OUT_PRICE,
    QA_RESPONSE_TIME, HAS_BACKUP_MACHINE, SUPPLIER_EXTEND_GUARANTEE_PRICE,
    CORE_PARTS_PRICE_FID, RETURN_GOODS_CONDITIONS,
    FREIGHT_INTRODUCTIONS, EXCHANGE_GOODS_CONDITIONS,
    EXCHANGE_GOODS_METHOD, GOODS_COMMENTS, `STATUS`,
    ADD_TIME, CREATOR, MOD_TIME,
    UPDATER, CHECK_TIME, CHECKER,
    OPERATE_INFO_ID, DELETE_REASON, LAST_CHECK_REASON,
    TAX_CATEGORY_NO, JX_MARKET_PRICE, JX_SALE_PRICE,
    JX_FLAG, `SOURCE`, PUSH_STATUS,
    DECLARE_DELIVERY_RANGE, PRICE_VERIFY_STATUS,
    AVGPRICE, LATEST_VALID_ORDER_USER, AVGPRICE_UPDATE_TIME,
    TERMINAL_PRICE, DISTRIBUTION_PRICE, COST_PRICE,
    AVAILABLE_STOCK_NUM, STOCK_NUM, ON_SALE,
    GOODS_BARCODE, CURING_TYPE, CURING_REASON,
    IS_NEED_TEST_REPROT, IS_KIT, KIT_DESC,
    IS_SAME_SN_CODE, IS_FACTORY_SN_CODE, IS_MANAGE_VEDENG_CODE,
    IS_BAD_GOODS, IS_ENABLE_FACTORY_BATCHNUM, IS_ENABLE_MULTISTAGE_PACKAGE,
    MID_PACKAGE_NUM, BOX_PACKAGE_NUM, IS_ENABLE_VALIDITY_PERIOD,
    NEAR_TERM_WARN_DAYS, OVER_NEAR_TERM_WARN_DAYS,
    INSTALL_TRAIN_TYPE, LOGISTICS_DELIVERYTYPE,
    IS_NEED_REPORT, IS_AUTHORIZED, HISTORY_NAME,
    IS_NAME_CHANGE, ONE_YEAR_SALE_NUM, REGULAR_MAINTAIN_TYPE,
    REGULAR_MAINTAIN_REASON, SYNCHRONIZATION_STATUS,
    IS_INSTALLABLE, GOODS_LEVEL_NO, GOODS_POSITION_NO,
    LAST_YEAR_RATIO_EIGHTY_SORT, THREE_MONTH_RATIO_EIGHTY_SORT,
    ONE_MONTH_RATIO_EIGHTY_SORT, ORG_ID_LIST, IS_AVAILABLE_SALE,
    PUSHED_ORG_ID_LIST, CONFIGURATION_LIST, ORDER_OCCUPY_NUM,
    SPU_TYPE, ACTION_LOCK_NUM, DISABLED_REASON,
    PURCHASE_TIME, PURCHASE_TIME_UPDATE_TIME,
    HAS_AFTER_SALE_SERVICE_LABEL, IS_SEVEN, INSTITUTION_LEVEL_IDS,TAX_CATEGORY_NO_RECORD,SERVICE_LIFE,DI_CODE
    )
    values (#{spuId,jdbcType=INTEGER}, #{checkStatus,jdbcType=INTEGER}, #{model,jdbcType=VARCHAR},
    #{spec,jdbcType=VARCHAR}, #{skuNo,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR},
    #{showName,jdbcType=VARCHAR}, #{materialCode,jdbcType=VARCHAR}, #{supplyModel,jdbcType=VARCHAR},
    #{isStockup,jdbcType=VARCHAR}, #{wikiHref,jdbcType=VARCHAR}, #{technicalParameter,jdbcType=VARCHAR},
    #{performanceParameter,jdbcType=VARCHAR}, #{specParameter,jdbcType=VARCHAR}, #{baseUnitId,jdbcType=INTEGER},
    #{minOrder,jdbcType=DECIMAL}, #{goodsLength,jdbcType=DECIMAL}, #{goodsWidth,jdbcType=DECIMAL},
    #{goodsHeight,jdbcType=DECIMAL}, #{packageLength,jdbcType=DECIMAL}, #{packageWidth,jdbcType=DECIMAL},
    #{packageHeight,jdbcType=DECIMAL}, #{netWeight,jdbcType=DECIMAL}, #{grossWeight,jdbcType=DECIMAL},
    #{unitId,jdbcType=INTEGER}, #{changeNum,jdbcType=DECIMAL}, #{packingList,jdbcType=VARCHAR},
    #{afterSaleContent,jdbcType=VARCHAR}, #{qaYears,jdbcType=VARCHAR}, #{storageConditionOne,jdbcType=INTEGER},
    #{storageConditionOneLowerValue,jdbcType=FLOAT}, #{storageConditionOneUpperValue,jdbcType=FLOAT},
    #{storageConditionHumidityLowerValue,jdbcType=FLOAT}, #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
    #{storageConditionTwo,jdbcType=VARCHAR}, #{effectiveDayUnit,jdbcType=INTEGER},
    #{effectiveDays,jdbcType=VARCHAR}, #{qaRule,jdbcType=VARCHAR}, #{qaOutPrice,jdbcType=DECIMAL},
    #{qaResponseTime,jdbcType=DECIMAL}, #{hasBackupMachine,jdbcType=VARCHAR}, #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
    #{corePartsPriceFid,jdbcType=INTEGER}, #{returnGoodsConditions,jdbcType=INTEGER},
    #{freightIntroductions,jdbcType=VARCHAR}, #{exchangeGoodsConditions,jdbcType=VARCHAR},
    #{exchangeGoodsMethod,jdbcType=VARCHAR}, #{goodsComments,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
    #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP},
    #{updater,jdbcType=INTEGER}, #{checkTime,jdbcType=TIMESTAMP}, #{checker,jdbcType=INTEGER},
    #{operateInfoId,jdbcType=INTEGER}, #{deleteReason,jdbcType=VARCHAR}, #{lastCheckReason,jdbcType=VARCHAR},
    #{taxCategoryNo,jdbcType=VARCHAR}, #{jxMarketPrice,jdbcType=DECIMAL}, #{jxSalePrice,jdbcType=DECIMAL},
    #{jxFlag,jdbcType=INTEGER}, #{source,jdbcType=TINYINT}, #{pushStatus,jdbcType=INTEGER},
    #{declareDeliveryRange,jdbcType=VARCHAR}, #{priceVerifyStatus,jdbcType=INTEGER},
    #{avgprice,jdbcType=DECIMAL}, #{latestValidOrderUser,jdbcType=INTEGER}, #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
    #{terminalPrice,jdbcType=DECIMAL}, #{distributionPrice,jdbcType=DECIMAL}, #{costPrice,jdbcType=DECIMAL},
    #{availableStockNum,jdbcType=INTEGER}, #{stockNum,jdbcType=INTEGER}, #{onSale,jdbcType=INTEGER},
    #{goodsBarcode,jdbcType=VARCHAR}, #{curingType,jdbcType=INTEGER}, #{curingReason,jdbcType=VARCHAR},
    #{isNeedTestReprot,jdbcType=INTEGER}, #{isKit,jdbcType=INTEGER}, #{kitDesc,jdbcType=VARCHAR},
    #{isSameSnCode,jdbcType=INTEGER}, #{isFactorySnCode,jdbcType=INTEGER}, #{isManageVedengCode,jdbcType=INTEGER},
    #{isBadGoods,jdbcType=INTEGER}, #{isEnableFactoryBatchnum,jdbcType=INTEGER}, #{isEnableMultistagePackage,jdbcType=INTEGER},
    #{midPackageNum,jdbcType=INTEGER}, #{boxPackageNum,jdbcType=INTEGER}, #{isEnableValidityPeriod,jdbcType=TINYINT},
    #{nearTermWarnDays,jdbcType=INTEGER}, #{overNearTermWarnDays,jdbcType=INTEGER},
    #{installTrainType,jdbcType=INTEGER}, #{logisticsDeliverytype,jdbcType=INTEGER},
    #{isNeedReport,jdbcType=INTEGER}, #{isAuthorized,jdbcType=INTEGER}, #{historyName,jdbcType=VARCHAR},
    #{isNameChange,jdbcType=TINYINT}, #{oneYearSaleNum,jdbcType=INTEGER}, #{regularMaintainType,jdbcType=INTEGER},
    #{regularMaintainReason,jdbcType=VARCHAR}, #{synchronizationStatus,jdbcType=INTEGER},
    #{isInstallable,jdbcType=INTEGER}, #{goodsLevelNo,jdbcType=INTEGER}, #{goodsPositionNo,jdbcType=INTEGER},
    #{lastYearRatioEightySort,jdbcType=INTEGER}, #{threeMonthRatioEightySort,jdbcType=INTEGER},
    #{oneMonthRatioEightySort,jdbcType=INTEGER}, #{orgIdList,jdbcType=VARCHAR}, #{isAvailableSale,jdbcType=INTEGER},
    #{pushedOrgIdList,jdbcType=VARCHAR}, #{configurationList,jdbcType=VARCHAR}, #{orderOccupyNum,jdbcType=INTEGER},
    #{spuType,jdbcType=INTEGER}, #{actionLockNum,jdbcType=INTEGER}, #{disabledReason,jdbcType=VARCHAR},
    #{purchaseTime,jdbcType=INTEGER}, #{purchaseTimeUpdateTime,jdbcType=TIMESTAMP},
    #{hasAfterSaleServiceLabel,jdbcType=INTEGER}, #{isSeven,jdbcType=INTEGER}, #{institutionLevelIds,jdbcType=VARCHAR}, #{taxCategoryNoRecord,jdbcType=VARCHAR}, #{serviceLife,jdbcType=INTEGER},#{diCode,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.CoreSkuGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <selectKey keyProperty="skuId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_CORE_SKU
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        SPU_ID,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="spec != null">
        SPEC,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="skuName != null">
        SKU_NAME,
      </if>
      <if test="showName != null">
        SHOW_NAME,
      </if>
      <if test="materialCode != null">
        MATERIAL_CODE,
      </if>
      <if test="supplyModel != null">
        SUPPLY_MODEL,
      </if>
      <if test="isStockup != null">
        IS_STOCKUP,
      </if>
      <if test="wikiHref != null">
        WIKI_HREF,
      </if>
      <if test="technicalParameter != null">
        TECHNICAL_PARAMETER,
      </if>
      <if test="performanceParameter != null">
        PERFORMANCE_PARAMETER,
      </if>
      <if test="specParameter != null">
        SPEC_PARAMETER,
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID,
      </if>
      <if test="minOrder != null">
        MIN_ORDER,
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH,
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH,
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT,
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH,
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH,
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT,
      </if>
      <if test="netWeight != null">
        NET_WEIGHT,
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT,
      </if>
      <if test="unitId != null">
        UNIT_ID,
      </if>
      <if test="changeNum != null">
        CHANGE_NUM,
      </if>
      <if test="packingList != null">
        PACKING_LIST,
      </if>
      <if test="afterSaleContent != null">
        AFTER_SALE_CONTENT,
      </if>
      <if test="qaYears != null">
        QA_YEARS,
      </if>
      <if test="storageConditionOne != null">
        STORAGE_CONDITION_ONE,
      </if>
      <if test="storageConditionOneLowerValue != null">
        STORAGE_CONDITION_ONE_LOWER_VALUE,
      </if>
      <if test="storageConditionOneUpperValue != null">
        STORAGE_CONDITION_ONE_UPPER_VALUE,
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
      </if>
      <if test="storageConditionTwo != null">
        STORAGE_CONDITION_TWO,
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT,
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS,
      </if>
      <if test="qaRule != null">
        QA_RULE,
      </if>
      <if test="qaOutPrice != null">
        QA_OUT_PRICE,
      </if>
      <if test="qaResponseTime != null">
        QA_RESPONSE_TIME,
      </if>
      <if test="hasBackupMachine != null">
        HAS_BACKUP_MACHINE,
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE,
      </if>
      <if test="corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID,
      </if>
      <if test="returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS,
      </if>
      <if test="freightIntroductions != null">
        FREIGHT_INTRODUCTIONS,
      </if>
      <if test="exchangeGoodsConditions != null">
        EXCHANGE_GOODS_CONDITIONS,
      </if>
      <if test="exchangeGoodsMethod != null">
        EXCHANGE_GOODS_METHOD,
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="checkTime != null">
        CHECK_TIME,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID,
      </if>
      <if test="deleteReason != null">
        DELETE_REASON,
      </if>
      <if test="lastCheckReason != null">
        LAST_CHECK_REASON,
      </if>
      <if test="taxCategoryNo != null">
        TAX_CATEGORY_NO,
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE,
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE,
      </if>
      <if test="jxFlag != null">
        JX_FLAG,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="pushStatus != null">
        PUSH_STATUS,
      </if>
      <if test="declareDeliveryRange != null">
        DECLARE_DELIVERY_RANGE,
      </if>
      <if test="priceVerifyStatus != null">
        PRICE_VERIFY_STATUS,
      </if>
      <if test="avgprice != null">
        AVGPRICE,
      </if>
      <if test="latestValidOrderUser != null">
        LATEST_VALID_ORDER_USER,
      </if>
      <if test="avgpriceUpdateTime != null">
        AVGPRICE_UPDATE_TIME,
      </if>
      <if test="terminalPrice != null">
        TERMINAL_PRICE,
      </if>
      <if test="distributionPrice != null">
        DISTRIBUTION_PRICE,
      </if>
      <if test="costPrice != null">
        COST_PRICE,
      </if>
      <if test="availableStockNum != null">
        AVAILABLE_STOCK_NUM,
      </if>
      <if test="stockNum != null">
        STOCK_NUM,
      </if>
      <if test="onSale != null">
        ON_SALE,
      </if>
      <if test="goodsBarcode != null">
        GOODS_BARCODE,
      </if>
      <if test="curingType != null">
        CURING_TYPE,
      </if>
      <if test="curingReason != null">
        CURING_REASON,
      </if>
      <if test="isNeedTestReprot != null">
        IS_NEED_TEST_REPROT,
      </if>
      <if test="isKit != null">
        IS_KIT,
      </if>
      <if test="kitDesc != null">
        KIT_DESC,
      </if>
      <if test="isSameSnCode != null">
        IS_SAME_SN_CODE,
      </if>
      <if test="isFactorySnCode != null">
        IS_FACTORY_SN_CODE,
      </if>
      <if test="isManageVedengCode != null">
        IS_MANAGE_VEDENG_CODE,
      </if>
      <if test="isBadGoods != null">
        IS_BAD_GOODS,
      </if>
      <if test="isEnableFactoryBatchnum != null">
        IS_ENABLE_FACTORY_BATCHNUM,
      </if>
      <if test="isEnableMultistagePackage != null">
        IS_ENABLE_MULTISTAGE_PACKAGE,
      </if>
      <if test="midPackageNum != null">
        MID_PACKAGE_NUM,
      </if>
      <if test="boxPackageNum != null">
        BOX_PACKAGE_NUM,
      </if>
      <if test="isEnableValidityPeriod != null">
        IS_ENABLE_VALIDITY_PERIOD,
      </if>
      <if test="nearTermWarnDays != null">
        NEAR_TERM_WARN_DAYS,
      </if>
      <if test="overNearTermWarnDays != null">
        OVER_NEAR_TERM_WARN_DAYS,
      </if>
      <if test="installTrainType != null">
        INSTALL_TRAIN_TYPE,
      </if>
      <if test="logisticsDeliverytype != null">
        LOGISTICS_DELIVERYTYPE,
      </if>
      <if test="isNeedReport != null">
        IS_NEED_REPORT,
      </if>
      <if test="isAuthorized != null">
        IS_AUTHORIZED,
      </if>
      <if test="historyName != null">
        HISTORY_NAME,
      </if>
      <if test="isNameChange != null">
        IS_NAME_CHANGE,
      </if>
      <if test="oneYearSaleNum != null">
        ONE_YEAR_SALE_NUM,
      </if>
      <if test="regularMaintainType != null">
        REGULAR_MAINTAIN_TYPE,
      </if>
      <if test="regularMaintainReason != null">
        REGULAR_MAINTAIN_REASON,
      </if>
      <if test="synchronizationStatus != null">
        SYNCHRONIZATION_STATUS,
      </if>
      <if test="isInstallable != null">
        IS_INSTALLABLE,
      </if>
      <if test="goodsLevelNo != null">
        GOODS_LEVEL_NO,
      </if>
      <if test="goodsPositionNo != null">
        GOODS_POSITION_NO,
      </if>
      <if test="lastYearRatioEightySort != null">
        LAST_YEAR_RATIO_EIGHTY_SORT,
      </if>
      <if test="threeMonthRatioEightySort != null">
        THREE_MONTH_RATIO_EIGHTY_SORT,
      </if>
      <if test="oneMonthRatioEightySort != null">
        ONE_MONTH_RATIO_EIGHTY_SORT,
      </if>
      <if test="orgIdList != null">
        ORG_ID_LIST,
      </if>
      <if test="isAvailableSale != null">
        IS_AVAILABLE_SALE,
      </if>
      <if test="pushedOrgIdList != null">
        PUSHED_ORG_ID_LIST,
      </if>
      <if test="configurationList != null">
        CONFIGURATION_LIST,
      </if>
      <if test="orderOccupyNum != null">
        ORDER_OCCUPY_NUM,
      </if>
      <if test="spuType != null">
        SPU_TYPE,
      </if>
      <if test="actionLockNum != null">
        ACTION_LOCK_NUM,
      </if>
      <if test="disabledReason != null">
        DISABLED_REASON,
      </if>
      <if test="purchaseTime != null">
        PURCHASE_TIME,
      </if>
      <if test="purchaseTimeUpdateTime != null">
        PURCHASE_TIME_UPDATE_TIME,
      </if>
      <if test="hasAfterSaleServiceLabel != null">
        HAS_AFTER_SALE_SERVICE_LABEL,
      </if>
      <if test="isSeven != null">
        IS_SEVEN,
      </if>
      <if test="institutionLevelIds != null">
        INSTITUTION_LEVEL_IDS,
      </if>
      <if test="isDirect !=null">
        IS_DIRECT,
      </if>
      <if test="serviceLife !=null">
        SERVICE_LIFE,
      </if>
      <if test="diCode !=null">
        DI_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        #{spuId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="supplyModel != null">
        #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="isStockup != null">
        #{isStockup,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null">
        #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameter != null">
        #{technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="performanceParameter != null">
        #{performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="specParameter != null">
        #{specParameter,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="minOrder != null">
        #{minOrder,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="grossWeight != null">
        #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        #{changeNum,jdbcType=DECIMAL},
      </if>
      <if test="packingList != null">
        #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContent != null">
        #{afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="qaYears != null">
        #{qaYears,jdbcType=VARCHAR},
      </if>
      <if test="storageConditionOne != null">
        #{storageConditionOne,jdbcType=INTEGER},
      </if>
      <if test="storageConditionOneLowerValue != null">
        #{storageConditionOneLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOneUpperValue != null">
        #{storageConditionOneUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTwo != null">
        #{storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDayUnit != null">
        #{effectiveDayUnit,jdbcType=INTEGER},
      </if>
      <if test="effectiveDays != null">
        #{effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="qaRule != null">
        #{qaRule,jdbcType=VARCHAR},
      </if>
      <if test="qaOutPrice != null">
        #{qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="qaResponseTime != null">
        #{qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="hasBackupMachine != null">
        #{hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="corePartsPriceFid != null">
        #{corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="returnGoodsConditions != null">
        #{returnGoodsConditions,jdbcType=INTEGER},
      </if>
      <if test="freightIntroductions != null">
        #{freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsConditions != null">
        #{exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsMethod != null">
        #{exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null">
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=INTEGER},
      </if>
      <if test="operateInfoId != null">
        #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null">
        #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null">
        #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null">
        #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="jxFlag != null">
        #{jxFlag,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="declareDeliveryRange != null">
        #{declareDeliveryRange,jdbcType=VARCHAR},
      </if>
      <if test="priceVerifyStatus != null">
        #{priceVerifyStatus,jdbcType=INTEGER},
      </if>
      <if test="avgprice != null">
        #{avgprice,jdbcType=DECIMAL},
      </if>
      <if test="latestValidOrderUser != null">
        #{latestValidOrderUser,jdbcType=INTEGER},
      </if>
      <if test="avgpriceUpdateTime != null">
        #{avgpriceUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="terminalPrice != null">
        #{terminalPrice,jdbcType=DECIMAL},
      </if>
      <if test="distributionPrice != null">
        #{distributionPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="availableStockNum != null">
        #{availableStockNum,jdbcType=INTEGER},
      </if>
      <if test="stockNum != null">
        #{stockNum,jdbcType=INTEGER},
      </if>
      <if test="onSale != null">
        #{onSale,jdbcType=INTEGER},
      </if>
      <if test="goodsBarcode != null">
        #{goodsBarcode,jdbcType=VARCHAR},
      </if>
      <if test="curingType != null">
        #{curingType,jdbcType=INTEGER},
      </if>
      <if test="curingReason != null">
        #{curingReason,jdbcType=VARCHAR},
      </if>
      <if test="isNeedTestReprot != null">
        #{isNeedTestReprot,jdbcType=INTEGER},
      </if>
      <if test="isKit != null">
        #{isKit,jdbcType=INTEGER},
      </if>
      <if test="kitDesc != null">
        #{kitDesc,jdbcType=VARCHAR},
      </if>
      <if test="isSameSnCode != null">
        #{isSameSnCode,jdbcType=INTEGER},
      </if>
      <if test="isFactorySnCode != null">
        #{isFactorySnCode,jdbcType=INTEGER},
      </if>
      <if test="isManageVedengCode != null">
        #{isManageVedengCode,jdbcType=INTEGER},
      </if>
      <if test="isBadGoods != null">
        #{isBadGoods,jdbcType=INTEGER},
      </if>
      <if test="isEnableFactoryBatchnum != null">
        #{isEnableFactoryBatchnum,jdbcType=INTEGER},
      </if>
      <if test="isEnableMultistagePackage != null">
        #{isEnableMultistagePackage,jdbcType=INTEGER},
      </if>
      <if test="midPackageNum != null">
        #{midPackageNum,jdbcType=INTEGER},
      </if>
      <if test="boxPackageNum != null">
        #{boxPackageNum,jdbcType=INTEGER},
      </if>
      <if test="isEnableValidityPeriod != null">
        #{isEnableValidityPeriod,jdbcType=TINYINT},
      </if>
      <if test="nearTermWarnDays != null">
        #{nearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="overNearTermWarnDays != null">
        #{overNearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test="installTrainType != null">
        #{installTrainType,jdbcType=INTEGER},
      </if>
      <if test="logisticsDeliverytype != null">
        #{logisticsDeliverytype,jdbcType=INTEGER},
      </if>
      <if test="isNeedReport != null">
        #{isNeedReport,jdbcType=INTEGER},
      </if>
      <if test="isAuthorized != null">
        #{isAuthorized,jdbcType=INTEGER},
      </if>
      <if test="historyName != null">
        #{historyName,jdbcType=VARCHAR},
      </if>
      <if test="isNameChange != null">
        #{isNameChange,jdbcType=TINYINT},
      </if>
      <if test="oneYearSaleNum != null">
        #{oneYearSaleNum,jdbcType=INTEGER},
      </if>
      <if test="regularMaintainType != null">
        #{regularMaintainType,jdbcType=INTEGER},
      </if>
      <if test="regularMaintainReason != null">
        #{regularMaintainReason,jdbcType=VARCHAR},
      </if>
      <if test="synchronizationStatus != null">
        #{synchronizationStatus,jdbcType=INTEGER},
      </if>
      <if test="isInstallable != null">
        #{isInstallable,jdbcType=INTEGER},
      </if>
      <if test="goodsLevelNo != null">
        #{goodsLevelNo,jdbcType=INTEGER},
      </if>
      <if test="goodsPositionNo != null">
        #{goodsPositionNo,jdbcType=INTEGER},
      </if>
      <if test="lastYearRatioEightySort != null">
        #{lastYearRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="threeMonthRatioEightySort != null">
        #{threeMonthRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="oneMonthRatioEightySort != null">
        #{oneMonthRatioEightySort,jdbcType=INTEGER},
      </if>
      <if test="orgIdList != null">
        #{orgIdList,jdbcType=VARCHAR},
      </if>
      <if test="isAvailableSale != null">
        #{isAvailableSale,jdbcType=INTEGER},
      </if>
      <if test="pushedOrgIdList != null">
        #{pushedOrgIdList,jdbcType=VARCHAR},
      </if>
      <if test="configurationList != null">
        #{configurationList,jdbcType=VARCHAR},
      </if>
      <if test="orderOccupyNum != null">
        #{orderOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="spuType != null">
        #{spuType,jdbcType=INTEGER},
      </if>
      <if test="actionLockNum != null">
        #{actionLockNum,jdbcType=INTEGER},
      </if>
      <if test="disabledReason != null">
        #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        #{purchaseTime,jdbcType=INTEGER},
      </if>
      <if test="purchaseTimeUpdateTime != null">
        #{purchaseTimeUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hasAfterSaleServiceLabel != null">
        #{hasAfterSaleServiceLabel,jdbcType=INTEGER},
      </if>
      <if test="isSeven != null">
        #{isSeven,jdbcType=INTEGER},
      </if>
      <if test="institutionLevelIds != null">
        #{institutionLevelIds,jdbcType=VARCHAR},
      </if>
      <if test="isDirect != null">
        #{isDirect,jdbcType=INTEGER},
      </if>
      <if test="serviceLife != null">
        #{serviceLife,jdbcType=INTEGER},
      </if>
      <if test="diCode != null">
        #{diCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.CoreSkuGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    select count(*) from V_CORE_SKU
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    update V_CORE_SKU
    <set>
      <if test="record.skuId != null">
        SKU_ID = #{record.skuId,jdbcType=INTEGER},
      </if>
      <if test="record.spuId != null">
        SPU_ID = #{record.spuId,jdbcType=INTEGER},
      </if>
      <if test="record.checkStatus != null">
        CHECK_STATUS = #{record.checkStatus,jdbcType=TINYINT},
      </if>
      <if test="record.model != null">
        MODEL = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.spec != null">
        SPEC = #{record.spec,jdbcType=VARCHAR},
      </if>
      <if test="record.skuNo != null">
        SKU_NO = #{record.skuNo,jdbcType=VARCHAR},
      </if>
      <if test="record.skuName != null">
        SKU_NAME = #{record.skuName,jdbcType=VARCHAR},
      </if>
      <if test="record.showName != null">
        SHOW_NAME = #{record.showName,jdbcType=VARCHAR},
      </if>
      <if test="record.materialCode != null">
        MATERIAL_CODE = #{record.materialCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplyModel != null">
        SUPPLY_MODEL = #{record.supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="record.isStockup != null">
        IS_STOCKUP = #{record.isStockup,jdbcType=VARCHAR},
      </if>
      <if test="record.wikiHref != null">
        WIKI_HREF = #{record.wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="record.technicalParameter != null">
        TECHNICAL_PARAMETER = #{record.technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="record.performanceParameter != null">
        PERFORMANCE_PARAMETER = #{record.performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="record.specParameter != null">
        SPEC_PARAMETER = #{record.specParameter,jdbcType=VARCHAR},
      </if>
      <if test="record.baseUnitId != null">
        BASE_UNIT_ID = #{record.baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="record.minOrder != null">
        MIN_ORDER = #{record.minOrder,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsLength != null">
        GOODS_LENGTH = #{record.goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsWidth != null">
        GOODS_WIDTH = #{record.goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsHeight != null">
        GOODS_HEIGHT = #{record.goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="record.packageLength != null">
        PACKAGE_LENGTH = #{record.packageLength,jdbcType=DECIMAL},
      </if>
      <if test="record.packageWidth != null">
        PACKAGE_WIDTH = #{record.packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="record.packageHeight != null">
        PACKAGE_HEIGHT = #{record.packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="record.netWeight != null">
        NET_WEIGHT = #{record.netWeight,jdbcType=DECIMAL},
      </if>
      <if test="record.grossWeight != null">
        GROSS_WEIGHT = #{record.grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="record.unitId != null">
        UNIT_ID = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.changeNum != null">
        CHANGE_NUM = #{record.changeNum,jdbcType=DECIMAL},
      </if>
      <if test="record.packingList != null">
        PACKING_LIST = #{record.packingList,jdbcType=VARCHAR},
      </if>
      <if test="record.afterSaleContent != null">
        AFTER_SALE_CONTENT = #{record.afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="record.qaYears != null">
        QA_YEARS = #{record.qaYears,jdbcType=VARCHAR},
      </if>
      <if test="record.storageConditionOne != null">
        STORAGE_CONDITION_ONE = #{record.storageConditionOne,jdbcType=TINYINT},
      </if>
      <if test="record.storageConditionTwo != null">
        STORAGE_CONDITION_TWO = #{record.storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="record.effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT = #{record.effectiveDayUnit,jdbcType=TINYINT},
      </if>
      <if test="record.effectiveDays != null">
        EFFECTIVE_DAYS = #{record.effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="record.qaRule != null">
        QA_RULE = #{record.qaRule,jdbcType=VARCHAR},
      </if>
      <if test="record.qaOutPrice != null">
        QA_OUT_PRICE = #{record.qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.qaResponseTime != null">
        QA_RESPONSE_TIME = #{record.qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="record.hasBackupMachine != null">
        HAS_BACKUP_MACHINE = #{record.hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE = #{record.supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID = #{record.corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="record.returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS = #{record.returnGoodsConditions,jdbcType=TINYINT},
      </if>
      <if test="record.freightIntroductions != null">
        FREIGHT_INTRODUCTIONS = #{record.freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="record.exchangeGoodsConditions != null">
        EXCHANGE_GOODS_CONDITIONS = #{record.exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="record.exchangeGoodsMethod != null">
        EXCHANGE_GOODS_METHOD = #{record.exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsComments != null">
        GOODS_COMMENTS = #{record.goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `STATUS` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.checkTime != null">
        CHECK_TIME = #{record.checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checker != null">
        CHECKER = #{record.checker,jdbcType=INTEGER},
      </if>
      <if test="record.operateInfoId != null">
        OPERATE_INFO_ID = #{record.operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="record.deleteReason != null">
        DELETE_REASON = #{record.deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="record.lastCheckReason != null">
        LAST_CHECK_REASON = #{record.lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="record.taxCategoryNo != null">
        TAX_CATEGORY_NO = #{record.taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.jxMarketPrice != null">
        JX_MARKET_PRICE = #{record.jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.jxSalePrice != null">
        JX_SALE_PRICE = #{record.jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.jxFlag != null">
        JX_FLAG = #{record.jxFlag,jdbcType=INTEGER},
      </if>
      <if test="record.source != null">
        `SOURCE` = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.serviceLife != null">
        SERVICE_LIFE = #{record.serviceLife,jdbcType=INTEGER},
      </if>
      <if test="record.diCode != null">
        DI_CODE = #{record.diCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    update V_CORE_SKU
    set SKU_ID = #{record.skuId,jdbcType=INTEGER},
      SPU_ID = #{record.spuId,jdbcType=INTEGER},
      CHECK_STATUS = #{record.checkStatus,jdbcType=TINYINT},
      MODEL = #{record.model,jdbcType=VARCHAR},
      SPEC = #{record.spec,jdbcType=VARCHAR},
      SKU_NO = #{record.skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{record.skuName,jdbcType=VARCHAR},
      SHOW_NAME = #{record.showName,jdbcType=VARCHAR},
      MATERIAL_CODE = #{record.materialCode,jdbcType=VARCHAR},
      SUPPLY_MODEL = #{record.supplyModel,jdbcType=VARCHAR},
      IS_STOCKUP = #{record.isStockup,jdbcType=VARCHAR},
      WIKI_HREF = #{record.wikiHref,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER = #{record.technicalParameter,jdbcType=VARCHAR},
      PERFORMANCE_PARAMETER = #{record.performanceParameter,jdbcType=VARCHAR},
      SPEC_PARAMETER = #{record.specParameter,jdbcType=VARCHAR},
      BASE_UNIT_ID = #{record.baseUnitId,jdbcType=INTEGER},
      MIN_ORDER = #{record.minOrder,jdbcType=DECIMAL},
      GOODS_LENGTH = #{record.goodsLength,jdbcType=DECIMAL},
      GOODS_WIDTH = #{record.goodsWidth,jdbcType=DECIMAL},
      GOODS_HEIGHT = #{record.goodsHeight,jdbcType=DECIMAL},
      PACKAGE_LENGTH = #{record.packageLength,jdbcType=DECIMAL},
      PACKAGE_WIDTH = #{record.packageWidth,jdbcType=DECIMAL},
      PACKAGE_HEIGHT = #{record.packageHeight,jdbcType=DECIMAL},
      NET_WEIGHT = #{record.netWeight,jdbcType=DECIMAL},
      GROSS_WEIGHT = #{record.grossWeight,jdbcType=DECIMAL},
      UNIT_ID = #{record.unitId,jdbcType=INTEGER},
      CHANGE_NUM = #{record.changeNum,jdbcType=DECIMAL},
      PACKING_LIST = #{record.packingList,jdbcType=VARCHAR},
      AFTER_SALE_CONTENT = #{record.afterSaleContent,jdbcType=VARCHAR},
      QA_YEARS = #{record.qaYears,jdbcType=VARCHAR},
      STORAGE_CONDITION_ONE = #{record.storageConditionOne,jdbcType=TINYINT},
      STORAGE_CONDITION_TWO = #{record.storageConditionTwo,jdbcType=VARCHAR},
      EFFECTIVE_DAY_UNIT = #{record.effectiveDayUnit,jdbcType=TINYINT},
      EFFECTIVE_DAYS = #{record.effectiveDays,jdbcType=VARCHAR},
      QA_RULE = #{record.qaRule,jdbcType=VARCHAR},
      QA_OUT_PRICE = #{record.qaOutPrice,jdbcType=DECIMAL},
      QA_RESPONSE_TIME = #{record.qaResponseTime,jdbcType=DECIMAL},
      HAS_BACKUP_MACHINE = #{record.hasBackupMachine,jdbcType=VARCHAR},
      SUPPLIER_EXTEND_GUARANTEE_PRICE = #{record.supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      CORE_PARTS_PRICE_FID = #{record.corePartsPriceFid,jdbcType=INTEGER},
      RETURN_GOODS_CONDITIONS = #{record.returnGoodsConditions,jdbcType=TINYINT},
      FREIGHT_INTRODUCTIONS = #{record.freightIntroductions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_CONDITIONS = #{record.exchangeGoodsConditions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_METHOD = #{record.exchangeGoodsMethod,jdbcType=VARCHAR},
      GOODS_COMMENTS = #{record.goodsComments,jdbcType=VARCHAR},
      `STATUS` = #{record.status,jdbcType=TINYINT},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      CHECK_TIME = #{record.checkTime,jdbcType=TIMESTAMP},
      CHECKER = #{record.checker,jdbcType=INTEGER},
      OPERATE_INFO_ID = #{record.operateInfoId,jdbcType=INTEGER},
      DELETE_REASON = #{record.deleteReason,jdbcType=VARCHAR},
      LAST_CHECK_REASON = #{record.lastCheckReason,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{record.taxCategoryNo,jdbcType=VARCHAR},
      JX_MARKET_PRICE = #{record.jxMarketPrice,jdbcType=DECIMAL},
      JX_SALE_PRICE = #{record.jxSalePrice,jdbcType=DECIMAL},
      JX_FLAG = #{record.jxFlag,jdbcType=INTEGER},
      `SOURCE` = #{record.source,jdbcType=TINYINT},
    SERVICE_LIFE = #{record.serviceLife,jdbcType=INTEGER},
    DI_CODE = #{record.diCode,jdbcType=VARCHAR},

    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.CoreSkuGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    update V_CORE_SKU
    <set>
      <if test="spuId != null">
        SPU_ID = #{spuId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        SHOW_NAME = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="supplyModel != null">
        SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="isStockup != null">
        IS_STOCKUP = #{isStockup,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null">
        WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameter != null">
        TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="performanceParameter != null">
        PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="specParameter != null">
        SPEC_PARAMETER = #{specParameter,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="minOrder != null">
        MIN_ORDER = #{minOrder,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        UNIT_ID = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        CHANGE_NUM = #{changeNum,jdbcType=DECIMAL},
      </if>
      <if test="packingList != null">
        PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContent != null">
        AFTER_SALE_CONTENT = #{afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="qaYears != null">
        QA_YEARS = #{qaYears,jdbcType=VARCHAR},
      </if>
      <if test="storageConditionOne != null">
        STORAGE_CONDITION_ONE = #{storageConditionOne,jdbcType=TINYINT},
      </if>
      <if test="storageConditionOne == 4">
        STORAGE_CONDITION_ONE_LOWER_VALUE = #{storageConditionOneLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOne == 4">
        STORAGE_CONDITION_ONE_UPPER_VALUE = #{storageConditionOneUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTwo != null">
        STORAGE_CONDITION_TWO = #{storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=TINYINT},
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS = #{effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="qaRule != null">
        QA_RULE = #{qaRule,jdbcType=VARCHAR},
      </if>
      <if test="qaOutPrice != null">
        QA_OUT_PRICE = #{qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="qaResponseTime != null">
        QA_RESPONSE_TIME = #{qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="hasBackupMachine != null">
        HAS_BACKUP_MACHINE = #{hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE = #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID = #{corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS = #{returnGoodsConditions,jdbcType=TINYINT},
      </if>
      <if test="freightIntroductions != null">
        FREIGHT_INTRODUCTIONS = #{freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsConditions != null">
        EXCHANGE_GOODS_CONDITIONS = #{exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsMethod != null">
        EXCHANGE_GOODS_METHOD = #{exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=INTEGER},
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null">
        DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null">
        LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null">
        TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="jxFlag != null">
        JX_FLAG = #{jxFlag,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>

      <if test=" goodsBarcode != null ">
        GOODS_BARCODE = #{goodsBarcode,jdbcType=VARCHAR},
      </if>
      <if test=" curingReason != null ">
        CURING_REASON = #{curingReason,jdbcType=VARCHAR},
      </if>
      <if test=" kitDesc != null ">
        KIT_DESC = #{kitDesc,jdbcType=VARCHAR},
      </if>
      <if test=" midPackageNum!=null ">
        MID_PACKAGE_NUM= #{midPackageNum,jdbcType=INTEGER},
      </if>
      <if test=" boxPackageNum!=null ">
        BOX_PACKAGE_NUM=#{boxPackageNum,jdbcType=INTEGER},
      </if>
      <if test=" nearTermWarnDays!=null ">
        NEAR_TERM_WARN_DAYS=#{nearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test=" overNearTermWarnDays!=null ">
        OVER_NEAR_TERM_WARN_DAYS=#{overNearTermWarnDays,jdbcType=INTEGER},
      </if>
      <if test=" curingType!=null ">
        CURING_TYPE=#{curingType,jdbcType=TINYINT},
      </if>
      <if test="regularMaintainType!=null ">
        REGULAR_MAINTAIN_TYPE=#{regularMaintainType,jdbcType=TINYINT},
      </if>
      <if test="regularMaintainReason!=null ">
        REGULAR_MAINTAIN_REASON=#{regularMaintainReason,jdbcType=VARCHAR},
      </if>
      <if test=" isNeedTestReprot!=null ">
        IS_NEED_TEST_REPROT=#{isNeedTestReprot,jdbcType=TINYINT},
      </if>
      <if test=" isKit!=null ">
        IS_KIT=#{isKit,jdbcType=TINYINT},
      </if>
      <if test=" isSameSnCode!=null ">
        IS_SAME_SN_CODE=#{isSameSnCode,jdbcType=TINYINT},
      </if>
      <if test=" isFactorySnCode!=null ">
        IS_FACTORY_SN_CODE=#{isFactorySnCode,jdbcType=TINYINT},
      </if>
      <if test=" isManageVedengCode!=null ">
        IS_MANAGE_VEDENG_CODE=#{isManageVedengCode,jdbcType=TINYINT},
      </if>
      <if test=" isBadGoods!=null ">
        IS_BAD_GOODS=#{isBadGoods,jdbcType=TINYINT},
      </if>

      <if test=" installTrainType!=null ">
        INSTALL_TRAIN_TYPE=#{installTrainType,jdbcType=TINYINT},
      </if>
      <if test=" logisticsDeliveryType!=null ">
        LOGISTICS_DELIVERYTYPE=#{logisticsDeliveryType,jdbcType=TINYINT},
      </if>
      <if test=" isEnableValidityPeriod!=null ">
        IS_ENABLE_VALIDITY_PERIOD=#{isEnableValidityPeriod,jdbcType=TINYINT},
      </if>
      <if test=" isEnableFactoryBatchnum!=null ">
        IS_ENABLE_FACTORY_BATCHNUM=#{isEnableFactoryBatchnum,jdbcType=TINYINT},
      </if>
      <if test=" isEnableMultistagePackage!=null ">
        IS_ENABLE_MULTISTAGE_PACKAGE=#{isEnableMultistagePackage,jdbcType=TINYINT},
      </if>
      <if test="isNeedReport!=null ">
        IS_NEED_REPORT=#{isNeedReport,jdbcType=TINYINT},
      </if>
      <if test="isAuthorized!=null ">
        IS_AUTHORIZED=#{isAuthorized,jdbcType=TINYINT},
      </if>
      <if test="historyName!=null ">
        HISTORY_NAME=#{historyName,jdbcType=VARCHAR},
      </if>
      <if test="isNameChange!=null ">
        IS_NAME_CHANGE=#{isNameChange,jdbcType=TINYINT},
      </if>
      <if test="goodsLevelNo!=null ">
        GOODS_LEVEL_NO=#{goodsLevelNo,jdbcType=INTEGER},
      </if>
      <if test="goodsPositionNo!=null ">
        GOODS_POSITION_NO=#{goodsPositionNo,jdbcType=INTEGER},
      </if>
      <if test="isInstallable!=null ">
        IS_INSTALLABLE=#{isInstallable,jdbcType=TINYINT},
      </if>
      <if test="synchronizationStatus != null ">
        SYNCHRONIZATION_STATUS=#{synchronizationStatus,jdbcType=TINYINT},
      </if>
      <if test="pushStatus != null">
        PUSH_STATUS=#{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="isAvailableSale != null">
        IS_AVAILABLE_SALE = #{isAvailableSale, jdbcType=INTEGER},
      </if>
      <if test="orgIdList != null">
        ORG_ID_LIST = #{orgIdList, jdbcType=VARCHAR},
      </if>
      <if test="configurationList != null">
        CONFIGURATION_LIST = #{configurationList,jdbcType=VARCHAR},
      </if>
      <if test="isDirect != null">
        IS_DIRECT = #{isDirect,jdbcType=INTEGER},
      </if>
      <if test="taxCategoryNoRecord != null">
        TAX_CATEGORY_NO_RECORD = #{taxCategoryNoRecord,jdbcType=VARCHAR},
      </if>
      <if test="serviceLife != null">
        SERVICE_LIFE = #{serviceLife,jdbcType=INTEGER},
      </if>
      <if test="diCode != null">
        DI_CODE = #{diCode,jdbcType=VARCHAR},
      </if>
      <if test="afterSalesServiceLevel != null">
        AFTER_SALES_SERVICE_LEVEL = #{afterSalesServiceLevel,jdbcType=INTEGER},
      </if>

    </set>
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </update>

  <update id="updatePushStatusBySpuId">
     update  V_CORE_SKU SET PUSH_STATUS=#{status} where SPU_ID=#{spuId}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.CoreSkuGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    update V_CORE_SKU
    set SPU_ID = #{spuId,jdbcType=INTEGER},
      CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      MODEL = #{model,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      SHOW_NAME = #{showName,jdbcType=VARCHAR},
      MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      IS_STOCKUP = #{isStockup,jdbcType=VARCHAR},
      WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=VARCHAR},
      PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=VARCHAR},
      SPEC_PARAMETER = #{specParameter,jdbcType=VARCHAR},
      BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      MIN_ORDER = #{minOrder,jdbcType=DECIMAL},
      GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      UNIT_ID = #{unitId,jdbcType=INTEGER},
      CHANGE_NUM = #{changeNum,jdbcType=DECIMAL},
      PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      AFTER_SALE_CONTENT = #{afterSaleContent,jdbcType=VARCHAR},
      QA_YEARS = #{qaYears,jdbcType=VARCHAR},
      STORAGE_CONDITION_ONE = #{storageConditionOne,jdbcType=TINYINT},
      STORAGE_CONDITION_TWO = #{storageConditionTwo,jdbcType=VARCHAR},
      EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=TINYINT},
      EFFECTIVE_DAYS = #{effectiveDays,jdbcType=VARCHAR},
      QA_RULE = #{qaRule,jdbcType=VARCHAR},
      QA_OUT_PRICE = #{qaOutPrice,jdbcType=DECIMAL},
      QA_RESPONSE_TIME = #{qaResponseTime,jdbcType=DECIMAL},
      HAS_BACKUP_MACHINE = #{hasBackupMachine,jdbcType=VARCHAR},
      SUPPLIER_EXTEND_GUARANTEE_PRICE = #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      CORE_PARTS_PRICE_FID = #{corePartsPriceFid,jdbcType=INTEGER},
      RETURN_GOODS_CONDITIONS = #{returnGoodsConditions,jdbcType=TINYINT},
      FREIGHT_INTRODUCTIONS = #{freightIntroductions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_CONDITIONS = #{exchangeGoodsConditions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_METHOD = #{exchangeGoodsMethod,jdbcType=VARCHAR},
      GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      CHECKER = #{checker,jdbcType=INTEGER},
      OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      JX_FLAG = #{jxFlag,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=TINYINT},
      PUSH_STATUS=#{pushStatus},
      SERVICE_LIFE = #{serviceLife,jdbcType=INTEGER},
      DI_CODE = #{diCode,jdbcType=VARCHAR}
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </update>

  <update id="updatePushStatusBySkuId">
    update V_CORE_SKU SET PUSH_STATUS=#{status} where SKU_ID=#{skuId}
  </update>

  <update id="updatePushStatusBySkuNo">
    update V_CORE_SKU SET PUSH_STATUS=#{status} where SKU_NO=#{skuNo}
  </update>

  <select id="getPushStatusBySkuId" resultType="java.lang.Integer">
    select IFNULL(PUSH_STATUS,0) FROM V_CORE_SKU WHERE SKU_ID=#{skuId}
  </select>

  <select id="getPushStatusBySkuNo" resultType="java.lang.Integer">
    select IFNULL(PUSH_STATUS,0) FROM V_CORE_SKU WHERE SKU_NO=#{skuNo}
  </select>

  <!-- add by Tomcat.Hui 2020/5/15 4:41 下午 .Desc: VDERP-2217 提供预计发货时间给前台 判断sku是否存在. start -->
  <select id="getSkuListByNo" resultType="com.vedeng.goods.model.CoreSkuGenerate">
    select SKU_NO,IS_NEED_REPORT FROM V_CORE_SKU WHERE SKU_NO IN
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <update id="updateSkuDeliveryRange" parameterType="map">
    UPDATE V_CORE_SKU
    SET DECLARE_DELIVERY_RANGE =
    <foreach collection="list" item="sku" index="index" open="CASE SKU_NO" close="END">
      WHEN #{sku.skuNo} THEN #{sku.declareDeliveryRange}
    </foreach>
    WHERE SKU_NO IN
    <foreach collection="list" item="sku" index="index" separator="," open="(" close=")">
      #{sku.skuNo}
    </foreach>
  </update>

  <update id="updateSkuPurchaseTime" parameterType="map">
    UPDATE V_CORE_SKU
    SET PURCHASE_TIME =
    <foreach collection="list" item="sku" index="index" open="CASE SKU_NO" close="END">
      WHEN #{sku.skuNo} THEN #{sku.purchaseTime}
    </foreach>
    , PURCHASE_TIME_UPDATE_TIME =
    <foreach collection="list" item="sku" index="index" open="CASE SKU_NO" close="END">
      WHEN #{sku.skuNo} THEN #{sku.purchaseTimeUpdateTime}
    </foreach>
    WHERE SKU_NO IN
    <foreach collection="list" item="sku" index="index" separator="," open="(" close=")">
      #{sku.skuNo}
    </foreach>
  </update>

  <select id="getWtSkuList" resultType="com.vedeng.goods.model.CoreSkuGenerate">
  SELECT
    T.SKU_NO as SKU_NO,
    T.DELIVERY_RANGE as DELIVERY_RANGE
  FROM
    ODS_SKU_INFO_ERP_WT T
  WHERE
    T.SKU_NO IN
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <!-- add by Tomcat.Hui 2020/5/15 4:41 下午 .Desc: VDERP-2217 提供预计发货时间给前台 判断sku是否存在. end -->


  <select id="getAssignAndManageId" resultType="com.pricecenter.dto.UserInfo">
        select
            P.ASSIGNMENT_ASSISTANT_ID as assistantId,
            P.ASSIGNMENT_MANAGER_ID AS managerId
        from V_CORE_SKU K LEFT JOIN V_CORE_SPU P ON K.SPU_ID=P.SPU_ID
        where K.SKU_ID=#{skuId}
    </select>


  <select id="selectBySkuNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    from V_CORE_SKU
    where SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </select>

    <update id="updateSkuPriceVerifyStatusBySkuNo">
    update V_CORE_SKU SET PRICE_VERIFY_STATUS = #{verifyStatus,jdbcType=INTEGER} where SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </update>


  <update id="updateSkuBySkuNo" parameterType="com.vedeng.goods.model.CoreSkuGenerate">
    update V_CORE_SKU
    <set>
      <if test="goodsLength != null">
        GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      </if>
    </set>
    where SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </update>


  <update id="updateSkuLatestYearAvgPrice">
    	UPDATE V_CORE_SKU T SET  T.AVGPRICE=#{price,jdbcType=DECIMAL} ,AVGPRICE_UPDATE_TIME=NOW(),AVGPRICE_UPDATE_TIME=NOW()
		,LATEST_VALID_ORDER_USER=#{userId,jdbcType=INTEGER}
		where SKU_ID= #{goodsId,jdbcType=INTEGER}
  </update>

  <update id="updateSkuOnSale">
    	UPDATE V_CORE_SKU  SET ON_SALE=#{onSale} WHERE SKU_NO=#{skuNo}
  </update>

  <select id="getSkuOnSale" resultType="java.lang.Integer">
     SELECT ON_SALE FROM V_CORE_SKU WHERE SKU_NO=#{skuNo}
  </select>

  <select id="getSkuOnSaleBySkuId" resultType="java.lang.Integer">
     SELECT ON_SALE FROM V_CORE_SKU WHERE SKU_ID=#{skuId}
  </select>
  <select id="getInputSkuToWmsBySkuId" resultMap="wmsInputSku">
SELECT
	sku.*,
	unit.UNIT_NAME,-- 单位名称
	brand.BRAND_NAME,-- 品牌名称
	brand.BRAND_NATURE,-- 国产品牌：1 进口品牌：2
	rn.MANAGE_CATEGORY_LEVEL,-- 管理类别
	spu.SPU_TYPE,-- 商品类型
	pc.PRODUCT_COMPANY_CHINESE_NAME, -- 生产企业
	rn.REGISTRATION_NUMBER -- 生产企业生产许可证号/备案凭证号
FROM
	V_CORE_SKU sku
	LEFT JOIN V_CORE_SPU spu ON sku.SPU_ID = spu.SPU_ID
	LEFT JOIN T_UNIT unit ON unit.UNIT_ID = sku.BASE_UNIT_ID
	LEFT JOIN T_BRAND brand ON brand.BRAND_ID = spu.BRAND_ID
	LEFT JOIN T_FIRST_ENGAGE fe ON fe.FIRST_ENGAGE_ID = spu.FIRST_ENGAGE_ID
	LEFT JOIN T_REGISTRATION_NUMBER rn ON rn.REGISTRATION_NUMBER_ID = fe.REGISTRATION_NUMBER_ID
	LEFT JOIN T_PRODUCT_COMPANY pc ON pc.PRODUCT_COMPANY_ID = rn.PRODUCT_COMPANY_ID
	WHERE sku.SKU_ID= #{skuId,jdbcType=INTEGER}
  </select>
  <select id="getWmsCoreSkuList" resultMap="wmsInputSku">
    SELECT
        sku.*,
        unit.UNIT_NAME,-- 单位名称
        brand.BRAND_NAME,-- 品牌名称
        brand.BRAND_NATURE,-- 国产品牌：1 进口品牌：2
        rn.MANAGE_CATEGORY_LEVEL,-- 管理类别
        spu.SPU_TYPE,-- 商品类型
        pc.PRODUCT_COMPANY_CHINESE_NAME, -- 生产企业
        rn.REGISTRATION_NUMBER -- 生产企业生产许可证号/备案凭证号
    FROM
        V_CORE_SKU sku
        LEFT JOIN V_CORE_SPU spu ON sku.SPU_ID = spu.SPU_ID
        LEFT JOIN T_UNIT unit ON unit.UNIT_ID = sku.BASE_UNIT_ID
        LEFT JOIN T_BRAND brand ON brand.BRAND_ID = spu.BRAND_ID
        LEFT JOIN T_FIRST_ENGAGE fe ON fe.FIRST_ENGAGE_ID = spu.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER rn ON rn.REGISTRATION_NUMBER_ID = fe.REGISTRATION_NUMBER_ID
        LEFT JOIN T_PRODUCT_COMPANY pc ON pc.PRODUCT_COMPANY_ID = rn.PRODUCT_COMPANY_ID
        -- where sku.CHECK_STATUS = 3
  </select>


  <select id="getSkuListBySkuNoStr" resultMap="wmsInputSku">
    SELECT
        sku.*,
        unit.UNIT_NAME,-- 单位名称
        brand.BRAND_NAME,-- 品牌名称
        brand.BRAND_NATURE,-- 国产品牌：1 进口品牌：2
        rn.MANAGE_CATEGORY_LEVEL,-- 管理类别
        spu.SPU_TYPE,-- 商品类型
        pc.PRODUCT_COMPANY_CHINESE_NAME, -- 生产企业
        rn.REGISTRATION_NUMBER -- 生产企业生产许可证号/备案凭证号
    FROM
        V_CORE_SKU sku
        LEFT JOIN V_CORE_SPU spu ON sku.SPU_ID = spu.SPU_ID
        LEFT JOIN T_UNIT unit ON unit.UNIT_ID = sku.BASE_UNIT_ID
        LEFT JOIN T_BRAND brand ON brand.BRAND_ID = spu.BRAND_ID
        LEFT JOIN T_FIRST_ENGAGE fe ON fe.FIRST_ENGAGE_ID = spu.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER rn ON rn.REGISTRATION_NUMBER_ID = fe.REGISTRATION_NUMBER_ID
        LEFT JOIN T_PRODUCT_COMPANY pc ON pc.PRODUCT_COMPANY_ID = rn.PRODUCT_COMPANY_ID
        where sku.SKU_NO in (${skuNoStr})
  </select>

  <select id="getSynchronizationStatusBySkuId" resultType="java.lang.Integer">
    SELECT
      IFNULL(SYNCHRONIZATION_STATUS,0)
    FROM
      V_CORE_SKU
    WHERE
      SKU_ID=#{skuId,jdbcType=INTEGER}
  </select>

  <select id="getPushInfoBySkuId" resultType="com.vedeng.goods.model.CoreSkuGenerate">
        SELECT
        SKU_ID,
        SKU_NO,
        PUSH_STATUS,
        SYNCHRONIZATION_STATUS
    FROM
        V_CORE_SKU
    WHERE
        SKU_ID = #{skuId,jdbcType=INTEGER}
  </select>

  <select id="getValidedSkuInfoBySpuId" resultType="com.vedeng.goods.command.SkuAddCommand">
    SELECT
        SKU_ID,
        SPU_ID
    FROM
        `V_CORE_SKU`
    WHERE
        CHECK_STATUS = 3
        AND GOODS_LEVEL_NO not in (4,5)
        AND SPU_ID = #{spuId,jdbcType=INTEGER}
  </select>
    <select id="getSkuNoListById" resultType="java.lang.String">
      SELECT SKU_NO FROM V_CORE_SKU WHERE SKU_ID IN
      <foreach collection="skuIdList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
    </select>

    <update id="updateSkuValidatePeriod" parameterType="java.lang.Integer">
        UPDATE V_CORE_SKU_SEARCH search
        SET
          VALIDITY_PERIOD_MIN = (
              SELECT MIN(EXPIRATION_DATE)
              FROM T_WAREHOUSE_GOODS_OPERATE_LOG
              WHERE  LOG_TYPE = 0
                     AND IS_USE = 0
                     AND GOODS_ID = #{skuId,jdbcType=INTEGER}
                     AND LOGICAL_WAREHOUSE_ID IN (1701,1708)
                     AND IS_ENABLE=1
                     AND NUM > 0
                     AND VEDENG_BATCH_NUMER != ''
          ),
          VALIDITY_PERIOD_MAX = (
              SELECT MAX(EXPIRATION_DATE)
              FROM T_WAREHOUSE_GOODS_OPERATE_LOG
              WHERE  LOG_TYPE = 0
                     AND IS_USE = 0
                     AND GOODS_ID = #{skuId,jdbcType=INTEGER}
                     AND LOGICAL_WAREHOUSE_ID IN (1701,1708)
                     AND IS_ENABLE=1
                     AND NUM > 0
                     AND VEDENG_BATCH_NUMER != ''
          )
        where SKU_ID = #{skuId,jdbcType=INTEGER}
  </update>

  <update id="batchSaveSkuAuthorization">
    UPDATE V_CORE_SKU
    SET
      IS_NEED_REPORT = #{sku.isNeedReport,jdbcType=TINYINT},
      IS_AUTHORIZED = #{sku.isAuthorized,jdbcType=TINYINT}
    WHERE
        SKU_ID IN
    <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
      #{skuId,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="updateSynchronizationStatusBySkuIds">
    UPDATE V_CORE_SKU
    SET SYNCHRONIZATION_STATUS = #{status,jdbcType=TINYINT}
    WHERE
        SKU_ID IN
    <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
      #{skuId,jdbcType=INTEGER}
    </foreach>
  </update>


    <select id="getSkuNoAndOrderAssitIdMap" resultType="com.vedeng.todolist.dto.PurchaseRankingDto">
    SELECT
      K.sku_no,
      (
        SELECT GROUP_CONCAT(ORDER_ASSSISTANT_USER_ID)
        FROM T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION
        WHERE PRODUCT_POSITOIN_USER_ID = P.ASSIGNMENT_MANAGER_ID AND POSITION_LEVEL=1 AND IS_DELETE = 0
      ) AS managerAsssistantIdStr,
      (
      SELECT GROUP_CONCAT(ORDER_ASSSISTANT_USER_ID)
      FROM T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION
      WHERE ORDER_ASSSISTANT_USER_ID = P.ASSIGNMENT_ASSISTANT_ID AND POSITION_LEVEL=2 AND IS_DELETE = 0
      ) AS assignmentAsssistantIdStr
    FROM V_CORE_SKU K
    LEFT JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    WHERE K.SKU_NO in
    <foreach collection="skuNoList" item="skuNo" index="index" open="(" close=")" separator=",">
      #{skuNo,jdbcType=VARCHAR}
    </foreach>
  </select>
    <select id="getSkuNoAndOrderAssitIdMapModify" resultType="com.vedeng.todolist.dto.PurchaseRankingDto">
        SELECT
        K.sku_no,
        (
        SELECT GROUP_CONCAT( distinct ORDER_ASSISTANT_USER_ID)
        FROM T_ORDER_ASSISTANT_RELATION
        WHERE PRODUCT_MANAGER_USER_ID = P.ASSIGNMENT_MANAGER_ID AND IS_DELETE = 0
        ) AS managerAsssistantIdStr,
        (
        SELECT GROUP_CONCAT( distinct ORDER_ASSISTANT_USER_ID)
        FROM T_ORDER_ASSISTANT_RELATION
        WHERE PRODUCT_ASSITANT_USER_ID = P.ASSIGNMENT_ASSISTANT_ID AND IS_DELETE = 0
        ) AS assignmentAsssistantIdStr
        FROM V_CORE_SKU K
        LEFT JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
        WHERE K.SKU_NO in
        <foreach collection="skuNoList" item="skuNo" index="index" open="(" close=")" separator=",">
            #{skuNo,jdbcType=VARCHAR}
        </foreach>
    </select>
  <select id="getSkuNoAndOrderAssitIdMapModifyV2" resultType="com.vedeng.todolist.dto.PurchaseRankingDto">
    SELECT
    K.sku_no,
    (
    SELECT GROUP_CONCAT(OAR.ORDER_ASSISTANT_USER_ID)
    FROM T_ORDER_ASSISTANT_RELATION OAR
    WHERE OAR.PRODUCT_MANAGER_USER_ID = P.ASSIGNMENT_MANAGER_ID AND OAR.PRODUCT_ASSITANT_USER_ID = P.ASSIGNMENT_ASSISTANT_ID AND IS_DELETE = 0
    ) AS managerAsssistantIdStr,
    (
    SELECT GROUP_CONCAT(OAR.ORDER_ASSISTANT_USER_ID)
    FROM T_ORDER_ASSISTANT_RELATION OAR
    WHERE OAR.PRODUCT_MANAGER_USER_ID = P.ASSIGNMENT_MANAGER_ID AND OAR.PRODUCT_ASSITANT_USER_ID = P.ASSIGNMENT_ASSISTANT_ID AND IS_DELETE = 0
    ) AS assignmentAsssistantIdStr
    FROM V_CORE_SKU K
    LEFT JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    WHERE K.SKU_NO in
    <foreach collection="skuNoList" item="skuNo" index="index" open="(" close=")" separator=",">
      #{skuNo,jdbcType=VARCHAR}
    </foreach>
  </select>
    <select id="selectDocOfGoodsBySkuId" resultType="com.vedeng.docSync.model.pojo.generate.DocOfGoodsDo">
      select
        *
      from
        T_DOC_OF_GOODS
      where
        IS_DELETE = 0
      and DISABLE = 0
      and SKU_ID = #{skuId,jdbcType=INTEGER}
      limit 1
    </select>

  <select id="getAllSkuNo" resultType="java.lang.String">
        SELECT SKU_NO
        FROM V_CORE_SKU
        WHERE SKU_NO IS NOT NULL
    </select>

  <update id="updateLabelStausByNo">
    update V_CORE_SKU set HAS_AFTER_SALE_SERVICE_LABEL = #{coreSkuGenerate.hasAfterSaleServiceLabel,jdbcType=INTEGER}
                      WHERE SKU_NO = #{coreSkuGenerate.skuNo,jdbcType=VARCHAR}
  </update>

  <update id="updateSkuIsDirect">
    update V_CORE_SKU set IS_DIRECT = #{isDirect,jdbcType=INTEGER} where SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </update>
  <select id="selectAfterServiceLevelByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    select
    SKU_ID,SKU_NO,SPU_ID,SHOW_NAME,AFTER_SALES_SERVICE_LEVEL
    from V_CORE_SKU
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </select>
</mapper>