package com.vedeng.erp.business.feign;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.R;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecordApiDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecordParams;
import com.vedeng.erp.trader.dto.FollowBindingTelParams;

import feign.Headers;
import feign.RequestLine;

import java.util.List;

import org.springframework.web.bind.annotation.RequestBody;

/**
 * 沟通记录服务-调用ERP接口
 */
@FeignApi(serverName = "erpServer")
public interface CommunicateRecordApiService {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /communicateRecord/page.do")
    R<PageInfo<CommunicateRecordDto>> page(@RequestBody PageParam<CommunicateRecordDto> recordDtoPageParam);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /communicateRecord/getOne.do")
    R<CommunicateRecordDto> getOne(@RequestBody CommunicateRecordDto communicateRecordDto);


    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /communicateRecord/add.do")
    R<?> add(@RequestBody CommunicateRecordDto communicateRecordDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /communicateRecord/update.do")
    R<?> update(@RequestBody CommunicateRecordDto communicateRecordDto); 
    
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /communicateRecord/getTelList.do")
    R<CommunicateTelRecordApiDto> getTelList(@RequestBody PageParam<CommunicateTelRecordParams> communicateTelRecordParams);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /communicateRecord/followBindingTel.do")
	R<List<Integer>> followBindingTel(FollowBindingTelParams followBindingTelParams);
    
}