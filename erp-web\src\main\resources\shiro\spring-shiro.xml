<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" 
	xmlns:mvc="http://www.springframework.org/schema/mvc" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:p="http://www.springframework.org/schema/p" 
	xmlns:context="http://www.springframework.org/schema/context" 
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans-3.2.xsd 
		http://www.springframework.org/schema/context 
		http://www.springframework.org/schema/context/spring-context-3.2.xsd 
		http://www.springframework.org/schema/mvc 
		http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd">

    <description><PERSON><PERSON>遗留安全配置</description>
     <!-- <PERSON><PERSON> Filter -->

    <bean id="legacyShiroFilter" class="com.vedeng.common.shiro.support.DefaultShiroFilterFactoryBean">
        <!-- 安全管理器 -->
        <property name="securityManager" ref="legacySecurityManager"/>
        <!-- 默认的登陆访问url -->
        <property name="loginUrl" value="/login.do"/>
        <property name="filterChainDefinitions">
            <value>
                /goodsInfo/searchForApi.do = anon
                /api/quoteToOrder/** = anon
                /api/broadCast/** = anon
                /communicateRecord/** = anon
                /api/terminal/** = anon
                /api/message/** = anon
                /api/goods/** = anon
                /api/tyc/** = anon
                /system/message/queryJCONoReadMsgNum.do = anon
                /system/message/getAllMessageNoread.do = anon
                /system/message/queryNoReadMsgNum.do = anon
                /api/userwork/** = anon
                /api/invoice/** = anon
                /order/saleorder/checkAptitudeForYxg.do = anon
                /orderstream/saleorder/contractReturnSaveForOtherErp.do = anon
                /order/buyorder/savebuyorderLogisticsSaveFromOtherErp.do = anon
                /orderstream/saleorder/receiveSaleorderFromOtherErp.do = anon
                /buyorder/bdorder/getBdSaleOrderListByBuyOrderNoForOtherErp.do = anon
                /static/** = anon
                /nopower.do = anon
                /login.do = anon
                /dologin.do = anon
                /checkpreload.html = anon
                /checkInnerWeb.html = anon
                /system/user/updateIsVedengState.do=anon
                /vgoods/operate/spuSend.do=anon
                /goods/static/vgoods/skuTip.do=anon
                /system/user/doJxAcountData.do=anon
                /trader/customer/getAccountSaler.do=anon
                /trader/customer/tyc/check.do=anon
                /trader/customer/saveMjxContactAdders.do=anon
                /checkSession.do = anon
                /code.do = anon
                /services/** = anon
                /tradermsg/sendMsg/sendTraderMsg.do = anon
                /tradermsg/sendMsg/sendWebaccountCertificateMsg.do = anon
                /userCenter/** = anon


                /ezadmin/index.html=anon
                /ezadmin/navs.html=anon
                /ezadmin/api/**=anon
                /ezadmin/clear.html=anon
                /ezadmin/welcome.html=anon

                /order/afterSalesCommon/saveFollowUpRecordForYXB.do = anon
                /warehouseOutIn/regenerateWarehouseInReport.do = anon
                /warehouseOutIn/regenerateWarehouseOutReport.do = anon
                /orderstream/saleorder/compenseOnlineConfirm.do=anon

                /system/call/pushVoiceWxMp3.do=anon

                /order/saleorder/saveBDAddSaleorder.do=anon
                /order/saleorder/saveYgyxBuyorderToErpSaleOrder.do=anon
                /order/saleorder/updateBDSaleStatus.do=anon
                /order/saleorder/searchOrderInfo.do=anon
                /order/afterSalesCommon/saveYxbRecord.do=anon
                /order/saleorder/updateOrderDeliveryStatus.do=anon
                /warehouse/warehouses/insertNewStock.do=anon
                /warehouse/warehouses/updateSaleorderOccupyNum.do=anon
                /order/hc/updateorderstatus.do = anon
                /warehouse/warehouses/getErrorStockGoodsList.do = anon
                /warehouse/warehouses/updateWarehouseLogIsUse.do = anon
                /warehouse/warehouses/getLogicalStockInfo.do = anon
                /warehouse/warehouses/initOrderOccupy.do= anon
                /warehouse/warehousesout/getPurchaseTime.do = anon
                /warehouse/warehousesout/getDeliverTime.do = anon
                /trader/customer/aptitude/status.do = anon
                /trader/customer/yxg/status/sync.do = anon
                /trader/customer/addYxgTrader.do = anon
                /trader/customer/yxgStartFinanceCheck.do = anon
                <!--/warehouse/warehousesout/viewAppliedItems.do = anon
                /warehouse/warehousesout/checkInvoiceParams.do = anon-->
                /aftersales/webaccount/certificate/update.do = anon
                /aftersales/webaccount/certificate/add.do = anon

                /warehouse/warehousesout/getInLibraryBarcode.do=anon
                /order/saleorder/getCouponOrderInfoByCouponId.do=anon
                /order/saleorder/getCouponOrderDetailByCouponcode.do=anon
                /order/bussinesschance/getBussinessChanceAndQuoteInfo.do=anon
                /warehouse/warehousesout/printOutOrder.do=anon
                /el/** = anon
                /wms/** = anon
                /aftersale/order/** = anon
                /pay/** = anon
                /api/saleOrder/getFlowAmount.do = anon
                /order/quote/printOrder.do=anon
                /order/quote/printAuthorizationElectronicSign.do=anon
                /businessChance/smartQuoteAdd.do=anon
                /user/getUserPermissionForSmartQuote.do=anon
                /coreSkuApi/getSkuInfoForSmartQuote.do=anon
                /traderCustomerBase/traderListForSmartQuote.do=anon

                <!--/el/** = anon-->
                <!-- /BDSaleorderTask/a.do=anon -->
                /order/saleorder/updateBDSaleStatus.do=anon
                /logistics/warehousein/saveExpress.do=anon
                <!--对外提供接口服务-->
                /mjx/** = anon
                /yxg/** = anon
                /goods/goods/getgoodslistextrainfo.do = anon
                /warehouse/warehousesout/updateWarehouseProblem.do=anon
                /producter/productering.do=anon
                /finance/invoice/selectInvoiceItems.do=anon
                /finance/invoice/viewInvoicedItems.do=anon
                /order/saleorder/queryOutBoundQuantity.do=anon
                /order/saleorder/ceshi.do=anon
                /aftersales/order/invoiceGoodList.do=anon
                /category/base/getCategoryListByParentId.do=anon
                /order/quote/canApply.do=anon
                /order/quote/next.do=anon
                /system/region/getregion.do=anon

                /goods/vgoods/uplodeSkuDeliveryRange.do=anon
                /goods/vgoods/saveUplodeSkuDeliveryRange.do=anon
                <!--/category/base/getFirstCategoryList.do=anon-->
                /category/base/getSecondCategoryList.do=anon
                /category/base/commitCategory.do=anon
                <!--/category/base/submitCategory.do=anon-->
                /category/base/choiceCategory.do=anon
                <!--二期用到的接口-->
                <!--/category/base/categoryMigretionIndex.do=anon
                /category/base/categoryMigrationExamine.do=anon-->
                /category/base/getAllLevelCategoryByIdList.do=anon

                /firstengage/baseinfo/doRegistrationImg.do=anon
                /firstengage/baseinfo/doRegistnumpdfImg.do=anon
                /firstengage/brand/brandNameForApi.do=anon
                /system/list/** = anon
                /order/quote/consult/** = anon

                /fileUpload/ajaxFileUploadAuthorization.do=anon
                /order/quote/authorizationPreview.do=anon
                /order/quote/authorizationExamine.do=anon

                /order/quote/authorizationExamine.do=anon
                /warehouse/warehouses/getWmsStock.do=anon

                /pay/** = anon
                /finance/invoice/hx_invoice/** = anon
                /order/saleorder/printOrder.do = anon
                /order/saleorder/editApplyValidSaleorder.do=anon
                /order/buyorder/editApplyValidBuyorder.do = anon
                /system/region/static/getAllRegion.do=anon
                /system/origanization/static/getAllOriganization.do=anon
                /system/userposit/static/getAllUser.do=anon
                /order/buyorder/indexPendingPurchaseExport.do = anon

                /order/aftersales/getEffectAfterSalePolicy.do = anon
                /buyorder/listGoodsOnWayInfo.do = anon
                /trader/supplier/addTraderSupplier.do = anon

                /goods/vgoods/viewSkuBySkuId.do=anon
                /goodsInfo/search.do=anon

                /goodsCategory/getAllCategory.do=anon
                /goodsCategory/getCategoryIdByFullPath.do=anon
                /goodsCategory/getFullPathNameById.do=anon

                /order/saleorder/printOrder.do=anon
                /orderstream/saleorder/contract_template/print.do=anon
                /order/newBuyorder/printBuyOrder.do=anon
                /buyorderExpense/printContract.do=anon
                /warehouse/warehousesout/expressInfoFile.do=anon
                /bankBill/api/alipayReceipt.do=anon
                /api/kingdee/paybill/cancel.do=anon
                /api/kingdee/invoiceVoucher.do=anon
                /api/kingdee/orderPayment.do=anon
                /api/bankBill/updateBankBillReceipt.do=anon
                /api/kingdee/pageQueryBuyOrder.do=anon
                /api/kingdee/queryPayBank.do=anon
                /trader/supplier/syncDocSupplierToFtp.do=anon
                /order/quote/queryForSaleMall.do = anon
                /sys/navs.do=anon
                /middleware/** = anon

                /flowOrder/contract/printBuyContract.do=anon
                /flowOrder/contract/printSaleContract.do=anon

                <!-- ERP API标准化模块 - 所有API接口免鉴权 -->
                /api/** = anon

                /logout.do = legacyLogout
                /** = authc <!--需要认证 -->
            </value>
        </property>
         <property name="filters">
			<map>
				<entry key="authc">
					<bean class="com.vedeng.common.shiro.AuthorityFilter"></bean>
				</entry>
                <entry key="legacyLogout" value-ref="legacyLogoutFilter" />
			</map>
		</property>
    </bean>

    <bean name="legacyLogoutFilter" class="com.vedeng.common.shiro.filter.LegacyLogoutFilter"/>

    <!-- shiro securityManager -->
    <bean id="legacySecurityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
        <!-- Single realm app. If you have multiple realms, use the 'realms' property
            instead. -->
        <!-- sessionManager -->
        <property name="sessionManager" ref="legacySessionManager" />
        <property name="realm" ref="shiroDbRealm" />
        <!-- cacheManager -->
        <property name="cacheManager" ref="cacheManager" />
        <!-- By default the servlet container sessions will be used. Uncomment
            this line to use shiro's native sessions (see the JavaDoc for more): -->
        <!-- <property name="sessionMode" value="native"/> -->
    </bean>


    <!-- sessionManager -->
    <bean id="legacySessionManager" class="org.apache.shiro.web.session.mgt.DefaultWebSessionManager">
        <property name="sessionDAO" ref="redisSessionDAO" />
        <property name="sessionIdCookie" ref="sessionIdCookie"/>
        <property name="sessionIdCookieEnabled" value="true"/>
        <!-- 去掉 JSESSIONID -->
        <property name="sessionIdUrlRewritingEnabled" value="false" />
        <property name="sessionValidationSchedulerEnabled" value="false"/>
    </bean>

    <!-- 指定本系统SESSIONID, 默认为: JSESSIONID 问题: 与SERVLET容器名冲突, 如JETTY, TOMCAT 等默认JSESSIONID,
       当跳出SHIRO SERVLET时如ERROR-PAGE容器会为JSESSIONID重新分配值导致登录会话丢失! -->
    <bean id="sessionIdCookie" class="org.apache.shiro.web.servlet.SimpleCookie">
        <constructor-arg name="name" value="shiroSessionId"/>
    </bean>

     <!-- 項目自定义的Realm -->
    <bean id="shiroDbRealm" class="com.vedeng.system.service.impl.ShiroDbRealm">
    	<property name="cachingEnabled" value="false"></property>
    </bean>

	<!-- 保证实现了Shiro内部lifecycle函数的bean执行 -->
	<bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor"/>
	<!-- AOP式方法级权限检查  -->
	<bean class="org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator" depends-on="lifecycleBeanPostProcessor">
		<property name="proxyTargetClass" value="true" />
        <property name="usePrefix" value="true"/>
	</bean>

    <bean id="ezFilter" class="com.vedeng.common.shiro.EzFilter"></bean>
</beans>