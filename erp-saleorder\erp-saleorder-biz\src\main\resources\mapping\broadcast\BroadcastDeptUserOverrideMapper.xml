<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.broadcast.mapdao.BroadcastDeptUserOverrideMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptUserOverrideEntity">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="ERP_USER_ID" jdbcType="INTEGER" property="erpUserId"/>
        <result column="BROADCAST_DEPT_ID" jdbcType="INTEGER" property="broadcastDeptId"/>
        <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, ERP_USER_ID, BROADCAST_DEPT_ID, IS_DELETED, ADD_TIME, MOD_TIME, CREATOR, UPDATER
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_DEPT_USER_OVERRIDE
        where ID = #{id,jdbcType=INTEGER}
        and IS_DELETED = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update T_BROADCAST_DEPT_USER_OVERRIDE
        set IS_DELETED = 1, MOD_TIME = now()
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptUserOverrideEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_DEPT_USER_OVERRIDE (ERP_USER_ID, BROADCAST_DEPT_ID,
        IS_DELETED, ADD_TIME, MOD_TIME,
        CREATOR, UPDATER)
        values (#{erpUserId,jdbcType=INTEGER}, #{broadcastDeptId,jdbcType=INTEGER},
        #{isDeleted,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptUserOverrideEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_DEPT_USER_OVERRIDE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="erpUserId != null">
                ERP_USER_ID,
            </if>
            <if test="broadcastDeptId != null">
                BROADCAST_DEPT_ID,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="erpUserId != null">
                #{erpUserId,jdbcType=INTEGER},
            </if>
            <if test="broadcastDeptId != null">
                #{broadcastDeptId,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptUserOverrideEntity">
        update T_BROADCAST_DEPT_USER_OVERRIDE
        <set>
            <if test="erpUserId != null">
                ERP_USER_ID = #{erpUserId,jdbcType=INTEGER},
            </if>
            <if test="broadcastDeptId != null">
                BROADCAST_DEPT_ID = #{broadcastDeptId,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptUserOverrideEntity">
        update T_BROADCAST_DEPT_USER_OVERRIDE
        set ERP_USER_ID = #{erpUserId,jdbcType=INTEGER},
        BROADCAST_DEPT_ID = #{broadcastDeptId,jdbcType=INTEGER},
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER}
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <select id="getAllBroadcastUserRelate"
            resultType="com.vedeng.erp.broadcast.domain.entity.BroadcastDeptUserOverrideEntity">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_DEPT_USER_OVERRIDE
        where   IS_DELETED = 0
    </select>

    <!-- 逻辑删除所有用户关系记录 -->
    <update id="deleteAllBroadcastUserRelate" parameterType="java.lang.Integer">
        update T_BROADCAST_DEPT_USER_OVERRIDE
        set IS_DELETED = 1, 
            MOD_TIME = now(),
            UPDATER = #{userId,jdbcType=INTEGER}
        where IS_DELETED = 0
    </update>

    <!-- 批量插入用户关系记录 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into T_BROADCAST_DEPT_USER_OVERRIDE 
        (ERP_USER_ID, BROADCAST_DEPT_ID, IS_DELETED, ADD_TIME,  CREATOR )
        values
        <foreach collection="list" item="item" separator=",">
        (#{item.erpUserId,jdbcType=INTEGER}, 
         #{item.broadcastDeptId,jdbcType=INTEGER},
         0,
         #{item.addTime,jdbcType=TIMESTAMP},

         #{item.creator,jdbcType=INTEGER}
        )
        </foreach>
    </insert>

</mapper>
