<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="myfn" uri="/WEB-INF/tld/myfn.tld" %>
<c:set var="title" value="采购详情" scope="application"/>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../../common/common.jsp" %>

<!DOCTYPE html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/order/style.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/orderstream/buyorder-detail.css">
    <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">

    <style>
        .block {
            height: 400px;
            background: #ccc;
            margin: 40px 0;
        }
    </style>

    <style>
        a, a:hover, a:focus {
            text-decoration: none;
            outline-style: none;
            color: #3384ef;
            cursor: pointer;
        }
    </style>

    <script>
        //json说明
        var data = [
            {
                label: 'XXX', //标签文字
                jump: '#XXX', //需要跳转的元素的ID
                tip: '', //文字下面的备注
                lock: '', //是否需要上面的锁和文字
                status: 3, //当前的状态，默认是0  0：未进行 1：已进行 2：正在进行 3：正在同步进行
                fail: 0 // 是否驳回，0：否 1：是
            }]
    </script>
    <script type="text/javascript">
        $(document).ready(function(){
            var now = getNow();
            $(".mask_div").remove();
            watermark({"watermark_txt":"${ sessionScope.curr_user.username}"+now});
        })
    </script>
</head>

<form action="" method="post" id="myform">
<span>
    <div class="t-line-wrap J-line-wrap" style="margin-left: 7%; margin-top: 2%; margin-bottom: 2%;"
         data-json=${statusNodeList}>
    </div>

		<div class="parts content1">
			<div class="title-container">
				<div class="table-title nobor">基本信息</div>
			</div>
			<table
                    class="table table-bordered table-striped table-condensed table-centered">
				<tbody>
					<tr>
						<td class="table-smaller">采购单号</td>
                        <td> <c:if test="${buyorderVo.isGift == 1}"><span style="color:#FF0000">赠品订单</span></c:if>${buyorderVo.buyorderNo}</td>
                        <td>创建人</td>
                        <td>${buyorderVo.createName}</td>
					</tr>
					<tr>
						<td>部门</td>
						<td>${buyorderVo.buyDepartmentName}</td>
						<td>创建时间</td>
						<td><date:date value="${buyorderVo.addTime}"/></td>
					</tr>
					<tr>
						<td>是否直发</td>
						<c:if test="${buyorderVo.deliveryDirect eq 0}">
                            <td>普发</td>
                        </c:if>
						<c:if test="${buyorderVo.deliveryDirect eq 1}">
                            <td class="warning-color1">直发</td>
                        </c:if>
						<td>生效日期</td>
						<td><date:date value="${buyorderVo.validTime}"/></td>
					</tr>
					<tr>
						<td>内部备注</td>
						<td>${buyorderVo.comments}</td>
						<td>补充条款</td>
						<td>${buyorderVo.additionalClause}</td>
					</tr>
					<tr>
						<td>订单状态</td>
						<td>
                        <c:choose>
                            <c:when test="${buyorderVo.status eq 0}">
                                <span>待确认</span>
                            </c:when>
                            <c:when test="${buyorderVo.status eq 1}">
                                <span>进行中</span>
                            </c:when>
                            <c:when test="${buyorderVo.status eq 2}">
                                <span>已完结</span>
                            </c:when>
                            <c:otherwise>
                                <span>已关闭</span>
                            </c:otherwise>
                        </c:choose>
                        </td>
						<td>锁定状态</td>
						<td>
                            <c:if test="${buyorderVo.lockedStatus eq 0}">未锁定</c:if>
                            <c:if test="${buyorderVo.lockedStatus eq 1}">已锁定（<span class="font-red">${lockedReason}</span>）</c:if>
                        </td>
					</tr>
					<tr>
						<td>审核状态</td>
						<td>
							<c:if test="${empty buyorderVo.verifyStatus }">待审核</c:if>
                            <c:if test="${buyorderVo.verifyStatus eq 0 }">审核中</c:if>
                            <c:if test="${buyorderVo.verifyStatus eq 1 }">审核通过</c:if>
                            <c:if test="${buyorderVo.verifyStatus eq 2 }">审核不通过</c:if>
						</td>
                        <td>生效状态</td>
						<td>
							<c:if test="${buyorderVo.validStatus eq 0}">未生效</c:if>
                            <c:if test="${buyorderVo.validStatus eq 1}">已生效</c:if>
						</td>
					</tr>
				</tbody>
			</table>
		</div>

    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">供应商信息</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">供应商名称</td>
                <td>
                    <div class="customername pos_rel">
                        <c:if test="${buyorderVo.isTraderRisk > 0}">
                            <img src="${pageContext.request.contextPath}/static/images/risk.png" width="28px"
                                 id="riskFlag_${buyorderVo.traderId}"
                                 onclick="checkRiskTrader('${buyorderVo.traderId}','1')">
                        </c:if>
                        <span class="font-blue addtitle"
                              tabTitle='{"num":"viewsupplier<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											"link":"./trader/supplier/baseinfo.do?traderId=${buyorderVo.traderId}","title":"供应商信息"}'>${buyorderVo.traderName}
                        </span>
                        <c:if test="${buyorderVo.certificateOverdue}">
                            <span id="showOverdue" style="color: red">(已过期)</span>
                        </c:if>
                    </div>
                </td>
                <td class="table-smaller">联系人</td>
                <td>${buyorderVo.traderContactName}</td>
            </tr>
            <tr>
                <td>电话</td>
                <td>${buyorderVo.traderContactTelephone}</td>
                <td>手机</td>
                <td>${buyorderVo.traderContactMobile}</td>
            </tr>
            <tr>
                <td>地址</td>
                <td>${buyorderVo.traderArea} &nbsp ${buyorderVo.traderAddress}</td>
                <td>供应商备注</td>
                <td>${buyorderVo.traderComments}</td>
            </tr>

            </tbody>
        </table>
    </div>

    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">产品信息</div>
        </div>
        <c:forEach var="bgv" items="${buyorderVo.buyorderGoodsVoList}" varStatus="num">
            <table class="table table-style7" style="table-layout:auto">
                <thead>
                <tr>
                    <th class="wid10">序号</th>
                    <th class="wid6">催货预警</th>
                    <th class="wid6">预警跟进</th>
                    <th class="wid6">订货号</th>
                    <th class="wid10">产品名称</th>
                    <th class="wid8">品牌</th>
                    <th class="wid8">规格/型号</th>
                    <th class="wid7">采购数量</th>
                    <th class="wid5">单位</th>
                    <c:if test="${buyorderVo.isGift ne 1}">
                    <th class="wid8">是否为赠品</th>
                    </c:if>
                    <c:if test="${buyorderVo.isGift eq 1}">
                    <th class="wid8">赠品参考价</th>
                    </c:if>
                    <th class="wid7">单价</th>
                    <th class="wid7">总额</th>
                    <c:if test="${buyorderVo.status ne 0}">
                        <th class="wid8">收货数量</th>
                        <th class="wid8">收票数量</th>
                    </c:if>
                    <th class="wid10">采购预计发货日</th>
                    <th class="wid10">采购预计到货日</th>
                    <th class="wid5">货期（天）</th>
                    <c:if test="${bgv.sku ne 'V127063'}">
                        <th class="wid8">是否有授权</th>
                    </c:if>
                    <th class="wid15 rebate_class">
                        <div>
                            返利信息
                        </div>
                    </th>
                    <th class="wid10">内部备注</th>
                    <th class="wid8">采购备注</th>
                    <th class="wid8">安装政策</th>
                    <th class="wid8">质保期</th>
                    <th class="wid8">售后政策</th>
                    <c:if test="${buyorderVo.traderId eq 613042}">
                    <th class="wid8">实际采购价</th>
                    </c:if>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <input type="hidden" name="buyorderGoodsId" value="${bgv.buyorderGoodsId}"/>
                    <td class="num">${num.count}</td>
                    <td>
                        <c:choose>
                            <c:when test="${bgv.earlyWarningTaskDto.taskStatus eq 1}">
                                    <img src="${pageContext.request.contextPath}/static/images/flash/warning_advent.png"
                                         width="28px"/>
                            </c:when>
                            <c:when test="${bgv.earlyWarningTaskDto.taskStatus gt 1}">
                                    <img src="${pageContext.request.contextPath}/static/images/flash/warning_overdue.png"
                                         width="28px"/>
                            </c:when>
                        </c:choose>
                    </td>
                    <td>
                         <c:choose>
                             <c:when test="${bgv.earlyWarningTaskDto.followUpNum gt 0}">
                                    <div class="btn-small bg-light-green bt-bg-style center"
                                         data-keyId="${bgv.earlyWarningTaskDto.earlyWarningTaskId}"
                                         onclick=showTaskInfo(this)>
                                        <div>预警跟进</div>
                                        <div>${bgv.earlyWarningTaskDto.followUpNum}</div>
                                    </div>
                             </c:when>
                             <c:when test="${bgv.earlyWarningTaskDto.followUpNum eq 0}">
                                    <div class="btn-small bg-light-red bt-bg-style center"
                                         data-keyId="${bgv.earlyWarningTaskDto.earlyWarningTaskId}"
                                         onclick=showTaskInfo(this)>
                                        <div>预警跟进</div>
                                        <div>${bgv.earlyWarningTaskDto.followUpNum}</div>
                                    </div>
                             </c:when>
                             <c:otherwise>
                             </c:otherwise>
                         </c:choose>
                        <div style="display:none;">
                                <!-- 弹框 -->
                                <div class="title-click nobor  pop-new-data" id="taskInfoDetail"></div>
                            </div>
                    </td>
                    <td>${bgv.sku}</td>
                    <td class="text-left">
                        <div class="customername pos_rel" style="display:inline;">
                            <c:if test="${bgv.isRisk > 0}">
                                <img src="${pageContext.request.contextPath}/static/images/risk.png" width="28px"
                                     id="riskFlag_${bgv.buyorderGoodsId}"
                                     onclick="checkSaleorderGoodsRisk('${bgv.sku}','${bgv.buyorderGoodsId}')">
                            </c:if>
                            <c:if test="${bgv.isGift == 1}"><span style="color:#FF0000">赠品</span></c:if>
                            <span class="font-blue cursor-pointer addtitle"
                                  tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                            "link":"./goods/goods/viewbaseinfo.do?goodsId=${bgv.goodsId}","title":"产品信息"}'>${bgv.goodsName}
                                            &nbsp;<i class="iconbluemouth contorlIcon"></i><br/></span>

                            <c:set var="skuNo" value="${bgv.sku}"></c:set>
                             <%@ include file="../../common/new_sku_common_tip.jsp" %>
                        </div>
                    </td>
                    <td>${bgv.brandName}</td>
                    <td>
                        ${bgv.spec}/${bgv.model}
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${bgv.afterSaleUpLimitNum > 0}">
                                <div class="customername pos_rel">
                                    <span alt="${bgv.goodsId}"> ${bgv.num - bgv.afterSaleUpLimitNum} <i
                                            class="iconredsigh ml4 contorlIcon"></i></span>
                                    <div class="pos_abs customernameshow">原值：${bgv.num}</div>
                                </div>
                            </c:when>
                            <c:otherwise>
                                ${bgv.num - bgv.afterSaleUpLimitNum}
                            </c:otherwise>
                        </c:choose>
                        <input type="hidden" name="buySum" alt="${bgv.buyorderGoodsId}"
                               value="${bgv.buyorderGoodsId}|${bgv.num}"/>
                    </td>
                    <td>${bgv.unitName}</td>
                    <c:if test="${buyorderVo.isGift ne 1}">
                    <td>
                        <c:choose>
                            <c:when test="${bgv.isGift eq 0}">
                                否
                            </c:when>
                            <c:when test="${bgv.isGift eq 1}">
                                是
                            </c:when>
                            <c:otherwise>
                                -
                            </c:otherwise>
                        </c:choose>
                    </td>
                    </c:if>
                    <c:if test="${buyorderVo.isGift eq 1}"><td>  <fmt:formatNumber type="number" value="${bgv.referPrice}" pattern="0.00"  maxFractionDigits="2"/></td></c:if>
                    <td class="price">
                        <c:choose>
                            <c:when test="${bgv.originalPurchasePrice != null}">
                                <div class="customername pos_rel">
                                            <span>
                                                <fmt:formatNumber type="number" value="${bgv.price}" pattern="0.00"
                                                                  maxFractionDigits="2"/>
                                                <i class="iconredsigh ml4 采购成本"></i>
                                            </span>
                                    <div class="pos_abs customernameshow">
                                        已核价采购成本：<fmt:formatNumber type="number" value="${bgv.originalPurchasePrice}"
                                                                  pattern="0.00" maxFractionDigits="2"/><br>
                                        本次优惠原因:${bgv.couponReason}
                                    </div>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <fmt:formatNumber type="number" value="${bgv.price}" pattern="0.00"
                                                  maxFractionDigits="2"/>
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <span class="oneAllMoney" alt="${bgv.buyorderGoodsId}"><fmt:formatNumber type="number"
                                                                                                 value="${bgv.price*(bgv.num - bgv.afterSaleUpLimitNum)}"
                                                                                                 pattern="0.00"
                                                                                                 maxFractionDigits="2"/></span>
                    </td>
                     <c:if test="${buyorderVo.status ne 0}">
                         <td class="warning-color1">${bgv.arrivalNum}</td>
                         <td>
                        <c:choose>
                            <c:when test="${bgv.invoiceNum >0}">
                                <div class="customername pos_rel ">
                                    <div>
                                            ${bgv.invoiceNum}<i class="iconbluesigh "></i>
                                    </div>
                                    <div class="pos_abs customernameshow " style="display: none; ">
                                        <c:forEach items="${bgv.invoiceList}" var="inv">
                                            发票号：${inv.invoiceNo }</br>
                                            票种：
                                            <c:if test="${inv.invoiceType eq 429}">17%增值税专用发票</c:if>
                                            <c:if test="${inv.invoiceType eq 430}">17%增值税普通发票</c:if>
                                            <c:if test="${inv.invoiceType eq 681}">16%增值税普通发票</c:if>
                                            <c:if test="${inv.invoiceType eq 682}">16%增值税专用发票</c:if>

                                            <c:if test="${inv.invoiceType eq 971}">13%增值税普通发票</c:if>
                                            <c:if test="${inv.invoiceType eq 972}">13%增值税专用发票</c:if>
                                            <c:if test="${inv.invoiceType eq 683}">6%增值税普通发票</c:if>
                                            <c:if test="${inv.invoiceType eq 684}">6%增值税专用发票</c:if>
                                            <c:if test="${inv.invoiceType eq 685}">3%增值税普通发票</c:if>
                                            <c:if test="${inv.invoiceType eq 686}">3%增值税专用发票</c:if>
                                            <c:if test="${inv.invoiceType eq 687}">0%增值税普通发票</c:if>
                                            <c:if test="${inv.invoiceType eq 688}">0%增值税专用发票</c:if>
                                            </br>
                                            红蓝字：
                                            <c:choose>
                                                <c:when test="${inv.colorType eq 1}">
                                                    <c:choose>
                                                        <c:when test="${inv.isEnable eq 0}">
                                                            <span style="color: red">红字作废</span>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <span style="color: red">红字有效</span>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </c:when>
                                                <c:otherwise>
                                                    <c:choose>
                                                        <c:when test="${inv.isEnable eq 0}">
                                                            <span style="color: red">蓝字作废</span>
                                                        </c:when>
                                                        <c:otherwise>
                                                            蓝字有效
                                                        </c:otherwise>
                                                    </c:choose>
                                                </c:otherwise>
                                            </c:choose></br>
                                            金额：${inv.amount }</br>
                                            开票数量：${inv.num}</br>
                                            日期：<date:date value="${inv.addTime}"/>
                                        </c:forEach>
                                    </div>
                                </div>
                            </c:when>
                            <c:otherwise>
                                0
                            </c:otherwise>
                        </c:choose>
                    </td>
                     </c:if>


                    <td><date:date value="${bgv.sendGoodsTime}" format="yyyy-MM-dd"/></td>
                    <td><date:date value="${bgv.receiveGoodsTime}" format="yyyy-MM-dd"/></td>
                    <td>${bgv.deliveryCycle}</td>
                    <c:if test="${bgv.sku ne 'V127063'}">
                        <td>${bgv.isHaveAuth eq 0 ? '否' : '是'}</td>
                    </c:if>
                    <td class="text-left">
                        <span style="display: block">返利总额:<span name="totalRebate" id="totalRebate${bgv.goodsId}"><fmt:formatNumber type="number" value="${bgv.rebateAmount}" pattern="0.00" maxFractionDigits="2"/></span></span>
                        <span style="display: block">返利单价:<span name="rebatePrice" id="rebatePrice${bgv.goodsId}"><fmt:formatNumber type="number" value="${bgv.rebatePrice}" pattern="0.00" maxFractionDigits="2"/></span></span>
                        <span style="display: block">返利后单价:<span name="afterRebatePrice" id="afterRebatePrice${bgv.goodsId}"><fmt:formatNumber type="number" value="${bgv.rebateAfterPrice}" pattern="0.00" maxFractionDigits="2"/></span></span>
                    </td>
                    <td>${bgv.goodsComments}</td>
                    <td>${bgv.insideComments}</td>
                    <td>${bgv.installPolicy}</td>
                    <td>${bgv.qualityPeriod}</td>
                    <td>
                        <c:if test="${bgv.supplyPolicyMaintained == 1}">
                                    <span class="title-click addtitle" style='float:none'
                                          style="float: right;margin-top: 5px"
                                          tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                    "link":"${pageContext.request.contextPath}/aftersale/serviceStandard/detail.do?skuNo=${bgv.sku}","title":"查看售后政策"}'>
                                                查看售后政策
                                            </span>
                        </c:if>
                        <c:if test="${bgv.supplyPolicyMaintained == 0}">
                                    <span class="title-click addtitle" style='float:none'
                                          style="float: right;margin-top: 5px"
                                          tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                    "link":"${pageContext.request.contextPath}/aftersale/serviceStandard/toAddSupplyAfterSalePolicy.do?skuNo=${bgv.sku}&traderId=${buyorderVo.traderId}&traderName=${buyorderVo.traderName}","title":"维护供应商售后政策"}'>
                                                去维护
                                            </span>
                        </c:if>
                    </td>
                    <c:if test="${buyorderVo.traderId eq 613042}">
                    <td><fmt:formatNumber type="number" value="${bgv.actualPurchasePrice}" pattern="0.00" maxFractionDigits="2"/></td>
                    </c:if>
                </tr>


                <c:if test="${bgv.geContractNo ne null and bgv.geContractNo ne '' }">
                    <tr>
                        <td colspan="18" class="table-container ">
                            <table class="table">
                                <thead>
                                <tr style="background-color: #F5F5F5">
                                    <th class="wid20">GE合同编号</th>
                                    <th class="wid20 ">GE销售订单编号</th>
                                    <th class="wid30 ">序列号</th>
                                    <th class="wid20 ">操作项</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>${bgv.geContractNo}</td>
                                    <td>${bgv.geSaleContractNo}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${bgv.masterSlaveLists eq null}">
                                                -
                                            </c:when>
                                            <c:otherwise>
                                                <c:forEach var="mslm" items="${bgv.masterSlaveLists}">
                                                    主机：${mslm.vBuyorderSncodeMaster.masterSncode}
                                                    探头：
                                                    <c:choose>
                                                        <c:when test="${empty mslm.vBuyorderSncodeSlaves}">
                                                            -
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:forEach var="msls"
                                                                       items="${mslm.vBuyorderSncodeSlaves}"
                                                                       varStatus="stat">
                                                                <c:if test="${msls.slaveSncode ne ''}">
                                                                    ${msls.slaveSncode}
                                                                    <c:if test="${!stat.last}">,</c:if>
                                                                </c:if>
                                                            </c:forEach>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <br>
                                                </c:forEach>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <c:if test="${empty buyorderVo.verifyStatus or buyorderVo.verifyStatus eq 2 }">
                                            <input type="button"
                                                   onclick="delGeInfoCollection(${bgv.buyorderGoodsId})"
                                                   value="删除">
                                            <input type="button"
                                                   onclick="addSncode('${bgv.sku}',${bgv.goodsId},${bgv.buyorderGoodsId})"
                                                   value="添加序列号">
                                            <input type="button"
                                                   onclick="updateGeInfoCollection(${bgv.buyorderGoodsId},'${bgv.geContractNo}','${bgv.geSaleContractNo}','${bgv.sku}')"
                                                   value="编辑">
                                        </c:if>
                                        <c:if test="${buyorderVo.verifyStatus eq 0 or buyorderVo.verifyStatus eq 1 }">
                                            <input type="button"
                                                   onclick="addSncode('${bgv.sku}',${bgv.goodsId},${bgv.buyorderGoodsId})"
                                                   value="添加序列号">
                                        </c:if>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </c:if>

                <c:choose>
                    <c:when test="${buyorderVo.status ne 0}">
                        <c:set var="col" value="24"></c:set>
                    </c:when>
                    <c:otherwise>
                        <c:set var="col" value="22"></c:set>
                    </c:otherwise>
                </c:choose>

                <tr>
                    <td colspan="${col}" class="table-container">
                        <table class="table">
                            <thead>
                            <tr>
                                <th class="wid8">关联单号</th>
                                <th class="wid10 ">申请人</th>
                                <th class="wid15 ">采购数量/需采数量</th>
                                <th class="wid11 ">销售价</th>
                                <th class="wid10">销售货期</th>
                                <th class="wid12">产品备注</th>
                                <th class="wid12">内部备注</th>
                                <th class="wid12 ">终端客户名称</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="saleorderGoods" items="${bgv.saleorderGoodsVoList}" varStatus="status">
                                <tr>
                                    <td>
                                        <c:if test="${saleorderGoods.orderType ne 2}">
                                          <a class="addtitle" href="javascript:void(0);"
                                             tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
                                                        "link":"/orderstream/saleorder/detail.do?saleOrderId=${saleorderGoods.saleorderId}"}'>${saleorderGoods.saleorderNo}</a>
                                        </c:if>

                                        <c:if test="${saleorderGoods.orderType eq 2}">
                                            <a class="addtitle" href="javascript:void(0);"
                                               tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
                                                    "link":"./order/saleorder/viewBhSaleorder.do?saleorderId=${saleorderGoods.saleorderId}"}'>${saleorderGoods.saleorderNo}</a>
                                        </c:if>


                                    </td>
                                    <td>${saleorderGoods.applicantName}</td>
                                    <td id="${num.count}_${saleorderGoods.saleorderId }_${status.index}">
                                        <c:if test="${buyorderVo.deliveryDirect eq 1}">
                                            /
                                        </c:if>
                                        <c:if test="${buyorderVo.deliveryDirect eq 0}">
                                            /
                                        </c:if>
                                    </td>
                                    <td><fmt:formatNumber type="number" value="${saleorderGoods.price}"
                                                          pattern="0.00"
                                                          maxFractionDigits="2"/></td>
                                    <td>${saleorderGoods.deliveryCycle}</td>
                                    <td>${saleorderGoods.goodsComments}</td>
                                    <td><span class="warning-color1">${saleorderGoods.insideComments}</span></td>
                                    <td>${saleorderGoods.terminalTraderName}</td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </td>
                </tr>

                </tbody>
            </table>
        </c:forEach>
        <div class="tablelastline-load" style="margin-top: -15px;">
            总件数<span class="warning-color1" id="zSum">${buyorderVo.buyorderSum}</span>

            <c:choose>
                <c:when test="${buyorderVo.paymentStatus ne 0}">
                    , 订单原金额<span class="warning-color1" id="zMoney"><fmt:formatNumber type="number" value="${buyorderVo.totalAmount}" pattern="0.00" maxFractionDigits="2"/></span>
                    , 订单实际金额<span class="warning-color1" id="zMoney2"><fmt:formatNumber type="number" value="${buyorderVo.realTotalAmount}" pattern="0.00" maxFractionDigits="2"/></span>
                    ，返利总额<span class="warning-color1" id="rebateTotal"><fmt:formatNumber type="number" value="${buyorderVo.usedTotalRebate}" pattern="0.00" maxFractionDigits="2"/></span>
                    ，应付金额<span class="warning-color1" id="rebateNeedPay"><fmt:formatNumber type="number" value="${buyorderVo.realTotalAmount - buyorderVo.usedTotalRebate}" pattern="0.00" maxFractionDigits="2"/></span>
                </c:when>
                <c:otherwise>
                    , 总金额<span class="warning-color1" id="zMoney"><fmt:formatNumber type="number" value="${buyorderVo.totalAmount}" pattern="0.00" maxFractionDigits="2"/></span>
                    ，返利总额<span class="warning-color1" id="rebateTotal"><fmt:formatNumber type="number" value="${buyorderVo.usedTotalRebate}" pattern="0.00" maxFractionDigits="2"/></span>
                    ，应付金额<span class="warning-color1" id="rebateNeedPay"><fmt:formatNumber type="number" value="${buyorderVo.buyorderAmount - buyorderVo.usedTotalRebate}" pattern="0.00" maxFractionDigits="2"/></span>
                </c:otherwise>
            </c:choose>
        </div>
    </div>

    <%---------------------------------------------虚拟商品---------------------------------------------------%>
     <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">虚拟商品</div>
        </div>
        <c:if test="${not empty buyorderVo.buyorderExpenseItemDtos}">
        <c:forEach var="item" items="${buyorderVo.buyorderExpenseItemDtos}" varStatus="status">
            <c:if test="${not empty item.buyOrderSaleOrderGoodsDetailDtos}">
        <table class="table table-style7" style="table-layout:auto">
            <thead>
                <tr>
                    <th class="wid10">序号</th>
                    <th class="wid6">订货号</th>
                    <th class="wid10">产品名称</th>
                    <th class="wid8">费用类别</th>
                    <th class="wid8">是否可库存管理</th>
                    <th class="wid8">采购数量</th>
                    <th class="wid8">单价</th>
                    <th class="wid8">总额</th>
                    <th class="wid10">采购备注</th>
                </tr>
            </thead>
            <tbody>


                    <input type="hidden" name="buyorderExpenseItemDtos[${status.index}].buyorderExpenseItemId" value="${item.buyorderExpenseItemId}"/>
                    <tr>
                        <td class="num">${status.count}</td>
                        <td>${item.buyorderExpenseItemDetailDto.sku}</td>
                        <td>${item.buyorderExpenseItemDetailDto.goodsName}</td>
                        <td>${item.buyorderExpenseItemDetailDto.expenseCategoryName}</td>
                        <td>
                            <c:choose>
                                <c:when test="${item.buyorderExpenseItemDetailDto.haveStockManage eq 0}">
                                    否
                                </c:when>
                                <c:when test="${item.buyorderExpenseItemDetailDto.haveStockManage eq 1}">
                                    是
                                </c:when>
                                <c:otherwise>-</c:otherwise>
                            </c:choose>
                        </td>
                        <td>${item.num}</td>
                        <td><fmt:formatNumber value="${item.buyorderExpenseItemDetailDto.price}" type="numer" pattern="0.00" maxFractionDigits="2"/></td>
                        <td><fmt:formatNumber value="${item.buyorderExpenseItemDetailDto.price * item.num}" type="numer" pattern="0.00" maxFractionDigits="2"/></td>
                        <td>${item.buyorderExpenseItemDetailDto.insideComments}</td>
                    </tr>
                    <%--                        虚拟商品关联销售订单--%>
                    <tr>
                        <c:if test="${not empty item.buyOrderSaleOrderGoodsDetailDtos}">
                            <td colspan="20" class="table-container">
                                <table class="table">
                                    <thead>
                                    <tr>
                                        <th class="wid10">关联单号</th>
                                        <th class="wid12">申请人</th>
                                        <th class="wid18">采购数量/剩余数量</th>
                                        <th class="wid12">销售价</th>
                                        <th class="wid12">销售货期</th>
                                        <th class="wid14">内部备注</th>
                                        <th class="wid14">商品备注</th>
                                        <th class="wid15">终端客户名称</th>
                                    </tr>
                                    </thead>
                                    <tbody>

                                    </tbody>
                                    <c:forEach var="list" items="${item.buyOrderSaleOrderGoodsDetailDtos}">
                                        <tr>
                                        <td>
                                        <c:if test="${list.orderType ne 2}">
                                              <a class="addtitle" href="javascript:void(0);"
                                                 tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
                                                            "link":"/orderstream/saleorder/detail.do?saleOrderId=${list.saleorderId}"}'>${list.saleorderNo}</a>
                                        </c:if>

                                        <c:if test="${list.orderType eq 2}">
                                                <a class="addtitle" href="javascript:void(0);"
                                                   tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
                                                        "link":"./order/saleorder/viewBhSaleorder.do?saleorderId=${list.saleorderId}"}'>${list.saleorderNo}</a>
                                        </c:if>
                                        </td>
                                        <td>${list.applicantName}</td>
                                        <td>${list.num}/${list.buyNum}</td>
                                        <td><fmt:formatNumber type="number" value="${list.price}"
                                                              pattern="0.00" maxFractionDigits="2"/></td>
                                        <td>${list.deliveryCycle}</td>
                                        <td><span class="warning-color1">${list.insideComments}</span></td>
                                        <td>${list.goodsComments}</td>
                                        <td>${list.terminalTraderName}</td>
                                        </tr>
                                    </c:forEach>
                                </table>
                            </td>
                        </c:if>
                    </tr>
            </tbody>
        </table>
            </c:if>
        </c:forEach>
        <table class="table" id="virtualGoodsAdd" style="table-layout:auto">
            <c:if test="${addVSkuFlag}">
            <thead>
                <tr>
                    <th width="5%">序号</th>
                    <th width="10%">订货号</th>
                    <th width="22%">产品名称</th>
                    <th width="10%">费用类别</th>
                    <th width="8%">是否可库存管理</th>
                    <th width="10%">采购数量</th>
                    <th width="10%">单价</th>
                    <th width="10%">总额</th>
                    <th width="15%">采购备注</th>
                </tr>
            </thead>
            <tbody>
                <c:forEach var="item" items="${buyorderVo.buyorderExpenseItemDtos}" varStatus="status">
                    <c:if test="${empty item.buyOrderSaleOrderGoodsDetailDtos}">
                        <tr>
                            <td>${status.count}</td>
                            <td>${item.buyorderExpenseItemDetailDto.sku}</td>
                            <td>${item.buyorderExpenseItemDetailDto.goodsName}</td>
                            <td>${item.buyorderExpenseItemDetailDto.expenseCategoryName}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.buyorderExpenseItemDetailDto.haveStockManage eq 0}">
                                        否
                                    </c:when>
                                    <c:when test="${item.buyorderExpenseItemDetailDto.haveStockManage eq 1}">
                                        是
                                    </c:when>
                                    <c:otherwise>-</c:otherwise>
                                </c:choose>
                            </td>
                            <td>${item.num}</td>
                            <td><fmt:formatNumber value="${item.buyorderExpenseItemDetailDto.price}" type="numer" pattern="0.00" maxFractionDigits="2"/></td>
                            <td><fmt:formatNumber value="${item.buyorderExpenseItemDetailDto.price * item.num}" type="numer" pattern="0.00" maxFractionDigits="2"/></td>
                            <td>${list.goodsComments}</td>
                        </tr>
                    </c:if>
                </c:forEach>
            </tbody>
            </c:if>
        </table>
        </c:if>
<%--         <c:if test="${empty buyorderVo.buyorderExpenseItemDtos}">--%>
<%--             <table class="table table-style7" style="table-layout:auto">--%>
<%--                 <thead>--%>
<%--                <tr>--%>
<%--                    <th class="wid10">序号</th>--%>
<%--                    <th class="wid6">订货号</th>--%>
<%--                    <th class="wid10">产品名称</th>--%>
<%--                    <th class="wid8">费用类别</th>--%>
<%--                    <th class="wid8">是否可库存管理</th>--%>
<%--                    <th class="wid8">采购数量</th>--%>
<%--                    <th class="wid8">单价</th>--%>
<%--                    <th class="wid8">总额</th>--%>
<%--                    <th class="wid10">采购备注</th>--%>
<%--                </tr>--%>
<%--            </thead>--%>
<%--                 <tbody>--%>
<%--                    <tr>--%>
<%--                        <td colspan='9'>暂无记录！</td>--%>
<%--                    </tr>--%>
<%--                 </tbody>--%>
<%--             </table>--%>
<%--         </c:if>--%>
         <div class="tablelastline">
            总件数<span class="warning-color1" id="eSum">${buyorderVo.ebuyorderSum}</span>
            ，总金额<span
             class="warning-color1" id="eMoney">${buyorderVo.ebuyorderAmount}</span>
        </div>
    </div>

    <%--------------------------------------------------------采购费用订单-------------------------------------------------------%>
    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">采购费用订单</div>
        </div>

        <table class="table table-style7" style="table-layout:auto">
            <thead>
                <tr>
                    <th class="wid10">采购费用单号</th>
                    <th class="wid6">创建人</th>
                    <th class="wid10">创建时间</th>
                    <th class="wid8">订单状态</th>
                    <th class="wid8">付款状态</th>
                    <th class="wid8">收票状态</th>
                    <th class="wid8">总额</th>
                    <th class="wid8">收票金额</th>
                    <th class="wid10">层级关系</th>
                </tr>
            </thead>
            <tbody>
                <c:if test="${not empty buyOrderExpenseDtoList}">
                    <c:forEach var="item" items="${buyOrderExpenseDtoList}" varStatus="status">
                        <tr>
                            <input type="hidden" name="buyorderExpenseId" value="${item.buyorderExpenseId}"/>
                            <td>
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"采购费用详情",
                                                            "link":"/buyorderExpense/details.do?buyorderExpenseId=${item.buyorderExpenseId}"}'>${item.buyorderExpenseNo}</a>
                            </td>
                            <td>${item.creatorName}</td>
                            <td><fmt:formatDate value="${item.addTime}" pattern="yyyy.MM.dd HH:mm:ss"/></td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.status eq 0}">
                                        待确认
                                    </c:when>
                                    <c:when test="${item.status eq 1}">
                                        进行中
                                    </c:when>
                                    <c:when test="${item.status eq 2}">
                                        已完结
                                    </c:when>
                                    <c:when test="${item.status eq 3}">
                                        已关闭
                                    </c:when>
                                    <c:otherwise>-</c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.paymentStatus eq 0}">
                                        待付款
                                    </c:when>
                                    <c:when test="${item.paymentStatus eq 1}">
                                        部分付款
                                    </c:when>
                                    <c:when test="${item.paymentStatus eq 2}">
                                        全部付款
                                    </c:when>
                                    <c:otherwise>-</c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.invoiceStatus eq 0}">
                                        待收票
                                    </c:when>
                                    <c:when test="${item.invoiceStatus eq 1}">
                                        部分收票
                                    </c:when>
                                    <c:when test="${item.invoiceStatus eq 2}">
                                        全部收票
                                    </c:when>
                                    <c:otherwise>-</c:otherwise>
                                </c:choose>
                            </td>
                            <td><fmt:formatNumber value="${item.buyorderExpenseDetailDto.totalAmount}" type="numer" pattern="0.00" maxFractionDigits="2"/></td>
                            <td><fmt:formatNumber value="${item.invoiceAmount}" type="numer" pattern="0.00" maxFractionDigits="2"/></td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.orderType eq 0}">
                                        生效前创建，已纳入合同
                                    </c:when>
                                    <c:when test="${item.orderType eq 1}">
                                        生效后创建，不纳入合同
                                    </c:when>
                                    <c:otherwise>-</c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>

                <c:if test="${empty buyOrderExpenseDtoList}">
                    <tr>
                        <td colspan='9'>暂无记录！</td>
                    </tr>
                </c:if>
            </tbody>
        </table>
    </div>


    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">付款计划</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th>计划</th>
                <th>计划内容</th>
                <th>支付金额</th>
                <th>备注</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${buyorderVo.paymentType eq 419}">
                <tr>
                    <td>第一期</td>
                    <td>预付款</td>
                    <td><fmt:formatNumber type="number" value="${buyorderVo.prepaidAmount}" pattern="0.00"
                                          maxFractionDigits="2"/></td>
                    <td>
                        <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                            <c:if test="${list.sysOptionDefinitionId eq buyorderVo.paymentType}">${list.title}</c:if>
                        </c:forEach>
                        <c:if test="${buyorderVo.bankAcceptance eq 1}">
                            <span>&nbsp;银行承兑汇票</span>
                        </c:if>
                    </td>
                </tr>
            </c:if>
            <c:if
                    test="${buyorderVo.paymentType ne 419 and buyorderVo.paymentType ne 424}">
                <tr>
                    <td>第一期</td>
                    <td>预付款</td>
                    <td><fmt:formatNumber type="number" value="${buyorderVo.prepaidAmount}" pattern="0.00"
                                          maxFractionDigits="2"/></td>
                    <td>
                        <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                            <c:if test="${list.sysOptionDefinitionId eq buyorderVo.paymentType}">${list.title}</c:if>
                        </c:forEach>
                    </td>
                </tr>
                <tr>
                    <td>第二期</td>
                    <td>帐期付款</td>
                    <td><fmt:formatNumber type="number" value="${buyorderVo.accountPeriodAmount}" pattern="0.00"
                                          maxFractionDigits="2"/></td>
                    <td>收货且收票后${buyorderVo.periodDay}天内支付</td>
                </tr>
            </c:if>
            <c:if test="${buyorderVo.paymentType eq 424}">
                <tr>
                    <td>第一期</td>
                    <td>预付款</td>
                    <td><fmt:formatNumber type="number" value="${buyorderVo.prepaidAmount}" pattern="0.00"
                                          maxFractionDigits="2"/></td>
                    <td>
                        <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                            <c:if test="${list.sysOptionDefinitionId eq quote.paymentType}">${list.title}</c:if>
                        </c:forEach>
                    </td>
                </tr>
                <tr>
                    <td>第二期</td>
                    <td>帐期付款</td>
                    <td><fmt:formatNumber type="number" value="${buyorderVo.accountPeriodAmount}" pattern="0.00"
                                          maxFractionDigits="2"/></td>
                    <td>收货且收票后${buyorderVo.periodDay}天内支付</td>
                </tr>
                <tr>
                    <td>第三期</td>
                    <td>尾款</td>
                    <td><fmt:formatNumber type="number" value="${buyorderVo.retainageAmount}" pattern="0.00"
                                          maxFractionDigits="2"/></td>
                    <td>到货后${buyorderVo.retainageAmountMonth}个月内支付</td>
                </tr>
            </c:if>
            <tr>
                <td>付款备注</td>
                <td colspan="3" class="text-left">
                    ${buyorderVo.paymentComments}
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">收货信息</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">指定物流公司</td>
                <td>
                    <div class="customername pos_rel">
                        <span class="">${buyorderVo.logisticsName}</span>
                    </div>
                </td>
                <td class="table-smaller">运费说明</td>
                <td>${buyorderVo.freightDes}</td>
            </tr>
            <c:if test="${buyorderVo.deliveryDirect eq 1}"></c:if>
            <tr>
                <td class="table-smaller">收货客户</td>
                <td>
                    <div class="customername pos_rel">
                        <span class="">${buyorderVo.takeTraderName}</span>
                    </div>
                </td>
                <td class="table-smaller">收货联系人</td>
                <td>${buyorderVo.takeTraderContactName}</td>
            </tr>
            <tr>
                <td class="table-smaller">电话</td>
                <td>
                    <div class="customername pos_rel">
                        <span class="">${buyorderVo.takeTraderContactTelephone}</span>
                    </div>
                </td>
                <td class="table-smaller">手机号</td>
                <td>${buyorderVo.takeTraderContactMobile}</td>
            </tr>
            <tr>
                <td class="table-smaller">地址</td>
                <td>
                    <div class="customername pos_rel">
                        <span class="">${buyorderVo.takeTraderArea}&nbsp ${buyorderVo.takeTraderAddress}</span>
                    </div>
                </td>
                <td>物流备注</td>
                <td>${buyorderVo.logisticsComments}</td>
            </tr>
            </tbody>
        </table>
    </div>
    <!-- /****************************************收票信息****************************************************/ -->
    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">收票信息</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">收票类型</td>
                <td>
                    <div class="customername pos_rel">
                        <span class="">${buyorderVo.invoiceTypeStr}</span>
                    </div>
                </td>
                <td>收票备注</td>
                <td>${buyorderVo.invoiceComments}</td>
            </tr>

            </tbody>
        </table>
    </div>


        <div class="tcenter" style="margin-top: 30px; margin-bottom: 15px">
        <input type="hidden" id="prepaidAmount" value="${buyorderVo.prepaidAmount }"/>
        <input type="hidden" id="retainageAmount" value="${buyorderVo.retainageAmount }"/>
        <input type="hidden" name="accountPeriodAmount" id="accountPeriodAmount"
               value="${buyorderVo.accountPeriodAmount }"/>
        <input type="hidden" name="paymentType" value="${buyorderVo.paymentType }"/>
        <input type="hidden" name="traderName" value="${buyorderVo.traderName }"/>
        <input type="hidden" name="traderId" value="${buyorderVo.traderId }"/>
        <input type="hidden" name="deliveryDirect" value="${buyorderVo.deliveryDirect}"/>
        <input type="hidden" name="buyorderNo" value="${buyorderVo.buyorderNo}"/>
        <input type="hidden" name="taskId" value="${taskInfo.id == null ?0: taskInfo.id}"/>

        <input type="hidden" name="uri" value="${uri}"/>
		<input type="hidden" name="buyorderId" id="buyorderId" value="${buyorderVo.buyorderId}"/>
		<input type="hidden" name="status" value="${buyorderVo.status}"/>
		<input type="hidden" name="validStatus" value="${buyorderVo.validStatus}">
		<input type="hidden" name="lockedStatus" value="${buyorderVo.lockedStatus}">
		<input type="hidden" name="userId" value="${buyorderVo.userId}">
		<input type="hidden" name="formToken" value="${formToken}"/>
        <input type="hidden" id="user_id" name="user_id" value="${curr_user.userId}">

   <c:if test="${contractUrl != null && contractUrl ne ''}">
       <a class="bg-light-green bt-bg-style bt-small" style="display: inline-block;height: 26px;width: 80px" href="${contractUrl}">下载合同</a>
   </c:if>


    <c:if test="${buyorderVo.status eq 1 || buyorderVo.status eq 2}">
        <c:if test="${buyorderVo.deliveryDirect eq 0}"><!-- 普发 -->
            <button type="button" class="bt-bg-style bt-small bg-light-blue mr10 addtitle"
                    tabTitle='{"num":"order_buyorder_${buyorderVo.buyorderId }","link":"./order/newBuyorder/printBuyOrder.do?buyorderId=${buyorderVo.buyorderId }&deliveryDirect=0&autoGenerate=false","title":"打印"}'>打印</button>
        </c:if>

        <c:if test="${buyorderVo.deliveryDirect eq 1}"><!-- 直发-->
            <button type="button" class="bt-bg-style bt-small bg-light-blue mr10 addtitle"
                    tabTitle='{"num":"order_buyorder_${buyorderVo.buyorderId }","link":"./order/newBuyorder/printBuyOrder.do?buyorderId=${buyorderVo.buyorderId }&deliveryDirect=1&autoGenerate=false","title":"打印"}'>打印</button>
        </c:if>

    </c:if>

        <%--创建者视角--%>
    <c:if test="${buyorderVo.userId eq curr_user.userId}">
        <%--待确认--%>
        <c:if test="${buyorderVo.status eq 0}">
            <%--区分直发/普发--%>
            <c:if test="${buyorderVo.deliveryDirect eq 0}"><!-- 普发 -->
                <button type="button" class="bt-bg-style bt-small bg-light-blue mr10 addtitle"
                        tabTitle='{"num":"order_buyorder_${buyorderVo.buyorderId }","link":"./order/newBuyorder/printBuyOrder.do?buyorderId=${buyorderVo.buyorderId }&deliveryDirect=0&autoGenerate=false","title":"打印预览"}'>打印预览</button>
            </c:if>

            <c:if test="${buyorderVo.deliveryDirect eq 1}"><!-- 直发-->
                <button type="button" class="bt-bg-style bt-small bg-light-blue mr10 addtitle"
                        tabTitle='{"num":"order_buyorder_${buyorderVo.buyorderId }","link":"./order/newBuyorder/printBuyOrder.do?buyorderId=${buyorderVo.buyorderId }&deliveryDirect=1&autoGenerate=false","title":"打印预览"}'>打印预览</button>
            </c:if>
        </c:if>

        <%--进行中|| 已完结--%>


        <%--待确认&待审核 || 待确认&审核不通过--%>
        <c:if test="${buyorderVo.status eq 0 && empty buyorderVo.verifyStatus && riskFlag eq 1 || (buyorderVo.status eq 0 && buyorderVo.verifyStatus eq 2)}">
            <button type="button" class="bt-bg-style bt-small bg-light-blue mr10"
                    onclick="newApplyValidBuyorder()">申请审核</button>
        </c:if>

        <%--待确认&待审核 || 待确认&审核不通过--%>
        <c:if test="${(buyorderVo.status eq 0 && buyorderVo.verifyStatus eq 2) || (buyorderVo.status eq 0 && empty buyorderVo.verifyStatus)}">
            <button type="button" class="bt-bg-style bt-small bg-light-blue mr10"
                    onclick="newEditBuyorder()">编辑订单</button>
        </c:if>

        <%--待确认&待审核 || 待确认&审核不通过 || 已生效&未付款&未发货&未收货&未收票&未锁定【规避付款审核中】且非已关闭--%>
        <c:if test="${((buyorderVo.status eq 0 && buyorderVo.verifyStatus eq 2) || (buyorderVo.status eq 0 && empty buyorderVo.verifyStatus) ||
        (buyorderVo.validStatus eq 1 && buyorderVo.paymentStatus eq 0 && buyorderVo.deliveryStatus eq 0 && buyorderVo.arrivalStatus eq 0
        && buyorderVo.invoiceStatus eq 0 && buyorderVo.lockedStatus eq 0 && buyorderVo.status ne 3)) &&
        ((null != buyorderVo.buyorderExpenseDto and buyorderVo.buyorderExpenseDto.lockedStatus ne 1 and  buyorderVo.buyorderExpenseDto.invoiceStatus eq 0 and buyorderVo.buyorderExpenseDto.paymentStatus eq 0
        and buyorderVo.buyorderExpenseDto.deliveryStatus eq 0 and buyorderVo.buyorderExpenseDto.arrivalStatus eq 0 )
        or buyorderVo.buyorderExpenseDto == null)}">
            <button type="button" class="bt-bg-style bt-small bg-light-blue mr10"
                    onclick="newCloseBuyorder(${buyorderVo.buyorderId})">关闭订单</button>
        </c:if>

        <%--已生效&未付款或部分付款&未锁定且非已关闭--%>
        <%--VDERP9628--%>
        <%--增加判断，直属采购费用单未锁定（售后锁定，付款申请锁定）--%>
        <c:if test="${buyorderVo.status ne 3 and isPayApplySh == 0 && buyorderVo.lockedStatus ne 1 and buyorderVo.validStatus eq 1 and ((null != buyorderVo.buyorderExpenseDto and buyorderVo.buyorderExpenseDto.lockedStatus ne 1) or buyorderVo.buyorderExpenseDto == null)}">
            <button type="button" class="bt-bg-style bt-small bg-light-blue mr10 addtitle"
                    tabTitle='{"num":"orderBuyorderApplyPayment${buyorderVo.buyorderId}","link":"./order/buyorder/applyPayment.do?buyorderId=${buyorderVo.buyorderId}","title":"申请付款"}'>申请付款</button>
        </c:if>

        <%--已生效【除已关闭、已完结外】&未锁定,即进行中&&未锁定--%>
        <c:if test="${buyorderVo.status eq 1 && buyorderVo.lockedStatus eq 0}">
            <button type="button" class="bt-bg-style bt-small bg-light-blue mr10 addtitle"
                    tabTitle='{"num":"modify_apply<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/order/newBuyorder/newApplyEditBuyOrder.do?buyorderId=${buyorderVo.buyorderId}","title":"申请修改"}'>申请修改</button>
        </c:if>

        <%--直发&已生效【非已完结、已关闭】&未锁定&未收货/部分收货--%>
        <c:if test="${buyorderVo.deliveryDirect eq 1 && buyorderVo.status eq 1 && buyorderVo.lockedStatus eq 0 && buyorderVo.arrivalStatus ne 2 && deliveryDirectConfirmArrival}">
            <button type="button" class="bt-bg-style bt-small bg-light-blue mr10 addtitle"
                    tabTitle='{"num":"buyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/order/buyorder/confirmArrivalInit.do?buyorderId=${buyorderVo.buyorderId}&isNew=1","title":"确认收货"}'>确认收货</button>
        </c:if>

    </c:if>

    <%--实际供应商信息--%>
    <c:if test="${buyorderVo.traderId eq 613042}">
        <button type="button" class="bt-bg-style bg-light-green bt-small  pop-new-data mr10" layerParams='{"width":"35%","height":"450px","title":"实际供应商信息","link":"/order/newBuyorder/actualSupplierInfo.do?buyorderId=${buyorderVo.buyorderId}"}'>
            实际供应商信息
        </button>
    </c:if>

    <%--付款申请审核---金蝶新增流转到供应链总监--%>
    <c:if test="${buyOrderPayApplyAuditFlag && expensePayApplyAuditFlag}">
            <c:choose>
                <c:when test="${taskInfoPay.assignee == curr_user.username or candidateUserMapPay['belong']}">
                    <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"${pageContext.request.contextPath}/order/buyorder/complement.do?taskId=${taskInfoPay.id}&expenseTaskId=${expenseTaskInfoPay.id}&pass=true&type=1"}'>付款审核通过</button>
                    <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"${pageContext.request.contextPath}/order/buyorder/complement.do?taskId=${taskInfoPay.id}&expenseTaskId=${expenseTaskInfoPay.id}&pass=false&type=1"}'>付款审核不通过</button>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核</button>
                </c:otherwise>
            </c:choose>
    </c:if>

     <c:if test="${buyOrderPayApplyAuditFlag && !expensePayApplyAuditFlag}">
         <c:choose>
             <c:when test="${taskInfoPay.assignee == curr_user.username or candidateUserMapPay['belong']}">
                 <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"${pageContext.request.contextPath}/order/buyorder/complement.do?taskId=${taskInfoPay.id}&pass=true&type=1"}'>付款审核通过</button>
                 <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"${pageContext.request.contextPath}/order/buyorder/complement.do?taskId=${taskInfoPay.id}&pass=false&type=1"}'>付款审核不通过</button>
             </c:when>
             <c:otherwise>
                 <button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核</button>
             </c:otherwise>
         </c:choose>
     </c:if>

     <c:if test="${!buyOrderPayApplyAuditFlag && expensePayApplyAuditFlag}">
         <c:choose>
             <c:when test="${expenseTaskInfoPay.assignee == curr_user.username or expenseCandidateUserMapPay['belong']}">
                 <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"${pageContext.request.contextPath}/order/buyorder/complement.do?expenseTaskId=${expenseTaskInfoPay.id}&pass=true&type=1"}'>付款审核通过</button>
                 <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"${pageContext.request.contextPath}/order/buyorder/complement.do?expenseTaskId=${expenseTaskInfoPay.id}&pass=false&type=1"}'>付款审核不通过</button>
             </c:when>
             <c:otherwise>
                 <button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核</button>
             </c:otherwise>
         </c:choose>
     </c:if>

    <c:if test="${buyorderVo.status eq 0 && (buyorderVo.userId eq curr_user.userId || prBoolean )}">
        <c:if test="${(null==taskInfo and null==taskInfo.getProcessInstanceId() )or (null!=taskInfo and taskInfo.assignee==null and empty candidateUserMap[taskInfo.id])}">
            <%--解除风控、以申请审核按钮 可见逻辑沿用原逻辑--%>
            <c:if test="${riskFlag eq 2}">
            <button type="button" class="bt-bg-style bg-light-blue bt-small mr10"
                    onclick="newApplyValidBuyorder()">解除风控</button>
            </c:if>
            <c:if test="${riskFlag eq 3}">
                <button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核</button>
            </c:if>
        </c:if>
    </c:if>

        <%--审核人视角--%>
    <c:if test="${(null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]}">
        <c:choose>
            <c:when test="${taskInfo.assignee == curr_user.username or candidateUserMap['belong']}">
                <button type="button" class="bt-bg-style bt-small bg-light-blue mr10 pop-new-data"
                        layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"/order/newBuyorder/complement.do?taskId=${taskInfo.id}&pass=true&type=3&buyorderId=${buyorderVo.buyorderId}"}'>审核通过</button>
                <button type="button" class="bt-bg-style bt-small bg-light-blue mr10 pop-new-data"
                        layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"/order/newBuyorder/complement.do?taskId=${taskInfo.id}&pass=false&type=3&buyorderId=${buyorderVo.buyorderId}"}'>审核不通过</button>
            </c:when>
        </c:choose>
    </c:if>
    </div>
    <div class="tcenter" style="margin-top: 10px; margin-bottom: 5px">
        <%--已生效&未付款或部分付款&未锁定且非已关闭--%>
        <%--VDERP9628--%>
        <%--增加判断，直属采购费用单未锁定（售后锁定，付款申请锁定）--%>
        <c:if test="${buyorderVo.status ne 3 and isPayApplySh == 0 && buyorderVo.lockedStatus ne 1 and buyorderVo.validStatus eq 1 and ((null != buyorderVo.buyorderExpenseDto and buyorderVo.buyorderExpenseDto.lockedStatus ne 1))}">
           <c:if test="${buyorderVo.buyorderExpenseDto.paymentStatus ne 2}">
               <span>直属费用单付款状态：${buyorderVo.buyorderExpenseDto.paymentStatus eq 0?'未付款':(buyorderVo.buyorderExpenseDto.paymentStatus eq 1?'部分付款':'全部付款') }</span>
           </c:if>
        </c:if>
    </div>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                沟通记录
            </div>
            <c:if test="${buyorderVo.status eq 0 || buyorderVo.status eq 1 && buyorderVo.lockedStatus ne 1 && buyorderVo.userId eq curr_user.userId }">
                <div class="title-click nobor  pop-new-data"
                     layerParams='{"width":"850px","height":"460px","title":"新增沟通记录","link":"./addCommunicateRecord.do?buyorderId=${buyorderVo.buyorderId}&&traderId=${buyorderVo.traderId }&flag=sx"}'>
                    新增
                </div>
            </c:if>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid10">沟通时间</th>
                <th class="">录音</th>
                <th class="">联系人</th>
                <th class="">联系方式</th>
                <th class="">沟通方式</th>
                <th class="wid30">沟通内容（AI分析整理）</th>
                <th class="">操作人</th>
                <th class="wid8">下次联系日期</th>
                <th class="wid15">下次沟通内容</th>
                <th class="">备注</th>
                <th class="wid10">创建时间</th>
                <th class="wid6">操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty communicateList}">
                <c:forEach items="${communicateList}" var="communicateRecord" varStatus="">
                    <tr>
                        <td><date:date value="${communicateRecord.begintime} "/>~<date:date
                                value="${communicateRecord.endtime}" format="HH:mm:ss"/></td>
                        <td><c:if
                                test="${not empty communicateRecord.coidUri }">${communicateRecord.communicateRecordId }</c:if></td>
                        <td>${communicateRecord.contactName}</td>
                        <td>${communicateRecord.phone}</td>
                        <td>${communicateRecord.communicateModeName}</td>
                        <td>
                            <ul class="communicatecontent ml0">
                                <c:if test="${not empty communicateRecord.tag }">
                                    <c:forEach items="${communicateRecord.tag }" var="tag">
                                        <li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
                                    </c:forEach>
                                </c:if>
                            </ul>
                        </td>
                        <td>${communicateRecord.user.username}</td>
                        <c:choose>
                            <c:when test="${communicateRecord.isDone == 0 }">
                                <td class="font-red">${communicateRecord.nextContactDate }</td>
                            </c:when>
                            <c:otherwise>
                                <td>${communicateRecord.nextContactDate }</td>
                            </c:otherwise>
                        </c:choose>
                        <td>${communicateRecord.nextContactContent}</td>
                        <td>${communicateRecord.comments}</td>
                        <td><date:date value="${communicateRecord.addTime} "/></td>
                        <td class="caozuo">
                            <c:if test="${buyorderVo.status ne 3 && buyorderVo.lockedStatus ne 1 && buyorderVo.userId eq curr_user.userId }">
			                        	<span class="border-blue pop-new-data"
                                              layerParams='{"width":"850px","height":"460px","title":"编辑沟通记录","link":"./editCommunicateRecord.do?communicateRecordId=${communicateRecord.communicateRecordId}&buyorderId=${buyorderVo.buyorderId}&&traderId=${buyorderVo.traderId }&flag=sx"}'>编辑</span>
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty communicateList}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='12'>暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <%--不等于(创建者&&(待确认 || 审核中 || (已关闭&待审核))) || (审核者 && (审核中 || 待审核)))--%>
    <c:if test="${!(((buyorderVo.userId eq curr_user.userId) && (buyorderVo.status eq 0 || buyorderVo.verifyStatus eq 0 || (buyorderVo.status eq 3 && empty buyorderVo.verifyStatus)))
     || (((null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]) && (buyorderVo.verifyStatus eq 0 || empty buyorderVo.verifyStatus)))}">

        <div class="parts">
            <div class="title-container ">
                <div class="table-title nobor">
                    付款申请
                </div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th>申请金额</th>
                    <th>申请时间</th>
                    <th>申请人</th>
                    <th>交易名称</th>
                    <th>交易方式</th>
                    <th>付款备注</th>
                    <th>审核状态</th>
                    <th>查看</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty payApplyList}">
                    <c:forEach items="${payApplyList}" var="list" varStatus="">
                        <tr>
                            <td><fmt:formatNumber type="number" value="${list.amount}" pattern="0.00"
                                                  maxFractionDigits="2"/></td>
                            <td><date:date value="${list.addTime}"/></td>
                            <td>${list.creatorName}</td>
                            <td>${list.traderName}</td>
                            <td>
                                <c:forEach var="modeList" items="${traderModeList}" varStatus="">
                                    <c:if test="${modeList.sysOptionDefinitionId eq list.traderMode}">${modeList.title}</c:if>
                                </c:forEach>
                            </td>
                            <td>${list.comments}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${list.validStatus eq 0}">
                                        待审核
                                    </c:when>
                                    <c:when test="${list.validStatus eq 1}">
                                        通过
                                    </c:when>
                                    <c:when test="${list.validStatus eq 2}">
                                        <span class="font-red">审核不通过</span>
                                    </c:when>
                                </c:choose>
                            </td>
                            <td>
                                <div class="caozuo">
                    <span class="caozuo-blue pop-new-data"
                          layerparams='{"width":"50%","height":"30%","title":"付款申请审核信息","link":"<%=basePath%>finance/after/paymentVerify.do?payApplyId=${list.payApplyId}"}'>查看</span>
                                </div>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty payApplyList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan='8'>暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                交易信息
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th>记账编号</th>
                <th>交易金额</th>
                <th>交易时间</th>
                <th>业务类型</th>
                <th>交易类型</th>
                <th>交易主体</th>
                <th>交易方式</th>
                <th>付款方</th>
                <th>收款方</th>
                <th>交易备注</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty buyorderVo.capitalBillList}">
                <c:forEach items="${buyorderVo.capitalBillList}" var="acb">
                    <tr>
                        <td>${acb.capitalBillNo}</td>
                        <td><fmt:formatNumber type="number" value="${acb.amount}" pattern="0.00"
                                              maxFractionDigits="2"/></td>
                        <td>
                            <c:if test="${acb.traderTime != 0}">
                                <date:date value="${acb.traderTime}"/>
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 525}">订单付款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 526}">订单收款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 531}">退款</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 532}">资金转移</c:if>
                            <c:if test="${acb.capitalBillDetail.bussinessType eq 533}">信用还款</c:if>
                        </td>
                        <td>
                            <c:if test="${acb.traderType eq 1}">收入</c:if>
                            <c:if test="${acb.traderType eq 2}">支出</c:if>
                            <c:if test="${acb.traderType eq 3}">转移</c:if>
                            <c:if test="${acb.traderType eq 4}">转入</c:if>
                            <c:if test="${acb.traderType eq 5}">转出</c:if>
                        </td>
                        <td>
                            <c:if test="${acb.traderSubject == 1}">
                                对公
                            </c:if>
                            <c:if test="${acb.traderSubject == 2}">
                                对私
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${acb.traderMode eq 520}">支付宝</c:if>
                            <c:if test="${acb.traderMode eq 521}">银行</c:if>
                            <c:if test="${acb.traderMode eq 522}">微信</c:if>
                            <c:if test="${acb.traderMode eq 523}">现金</c:if>
                            <c:if test="${acb.traderMode eq 527}">信用支付</c:if>
                            <c:if test="${acb.traderMode eq 528}">余额支付</c:if>
                            <c:if test="${acb.traderMode eq 529}">退还信用</c:if>
                            <c:if test="${acb.traderMode eq 530}">退还余额</c:if>
                            <c:if test="${acb.traderMode eq 10000}">返利</c:if>
                            <c:if test="${acb.traderMode eq 10001}">银行承兑汇票</c:if>
                        </td>
                        <td>${acb.payer}</td>
                        <td>${acb.payee}</td>
                        <td>${acb.comments}</td>
                        <td>
                            <c:if test="${(acb.traderType == 2 || acb.traderType == 5) && acb.bankBillId != 0}">
                                <div class="caozuo">
                                    <c:choose>
                                        <c:when test="${not empty acb.receiptUrl}">
                                            <span class="caozuo-blue addtitle" tabTitle='{"num":"credentials${acb.bankBillId}", "link":"${acb.receiptUrl}","title":"电子回执单"}'>预览</span>
                                            <span class="caozuo-blue" onclick="openFile('${acb.receiptUrl}')">回单</span>

                                        </c:when>
                                        <c:otherwise>
                                            <span class="caozuo-blue addtitle" tabTitle='{"num":"credentials${acb.bankBillId}", "link":"<%=basePath%>finance/capitalbill/credentials.do?bankBillId=${acb.bankBillId}","title":"电子回执单"}'>预览</span>
                                            <span class="caozuo-blue" onclick="openFile('${acb.receiptUrl}')">回单</span>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>
                <tr>
                    <td class="text-left" colspan="11">订单金额：<fmt:formatNumber type="number"
                                                                              value="${buyorderVo.totalAmount}"
                                                                              pattern="0.00"
                                                                              maxFractionDigits="2"/>
                        订单实际金额：<fmt:formatNumber type="number" value="${buyorderVo.realAmount}"
                                                 pattern="0.00"
                                                 maxFractionDigits="2"/>
                        已付款金额：<fmt:formatNumber type="number"
                                                value="${buyorderVo.paymentAmount+buyorderVo.periodAmount}"
                                                pattern="0.00" maxFractionDigits="2"/>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <span style="color:red">未付金额：<fmt:formatNumber type="number"
                                                                       value='${buyorderVo.realAmount - buyorderVo.paymentAmount - buyorderVo.periodAmount }'
                                                                       pattern="0.00"
                                                                       maxFractionDigits="2"/></span>
                        &nbsp;=&nbsp;
                        订单实际金额：<fmt:formatNumber type="number" value='${buyorderVo.realAmount}'
                                                 pattern="0.00"
                                                 maxFractionDigits="2"/>
                        &nbsp;-&nbsp;
                        客户实付金额：<fmt:formatNumber type="number"
                                                 value='${buyorderVo.paymentAmount +buyorderVo.periodAmount }'
                                                 pattern="0.00" maxFractionDigits="2"/>
                    </td>
                </tr>
            </c:if>
            <c:if test="${empty buyorderVo.capitalBillList}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='11'>暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                物流信息
                        
            </div>
            <c:if test="${buyorderVo.status ne 3 
                        && buyorderVo.lockedStatus ne 1 
                        && (buyorderVo.deliveryStatus ne 2 || (buyorderVo.deliveryStatus eq 2 && buyorderVo.allGoodsHaveExpressInfoFlag == 0)) 
                        && buyorderVo.userId eq curr_user.userId}">
                        
                        
                <div class="title-click nobor addtitle"
                     tabTitle='{"num":"addExpress","link":"./order/buyorder/addExpress.do?buyorderId=${buyorderVo.buyorderId}","title":" 新增快递"}'>
                    新增快递
                </div>
            </c:if>
        </div>
        <table class="table  table-style6" id="wulb">
            <thead>
            <tr>
                <th>快递单号</th>
                <th>快递公司</th>
                <th>发货时间</th>
                <th class="wid8">运费</th>
                <th>商品</th>
                <th>快递状态</th>
                <th class="wid15">备注</th>
                <c:if test="${buyorderVo.deliveryDirect == 1}">
                    <th class="wid8">是否可收货</th>
                </c:if>

                <th>操作</th>
            </tr>
            </thead>
        </table>
    </div>

    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">
                直发商品同行单数据
            </div>
            <c:if test="${buyorderVo.lockedStatus ne 1 && buyorderVo.deliveryStatus ne 0 && isShowDtBtn eq 1}">
                <div class="title-click nobor">
                    <span class="title-click addtitle" style="float: none" tabtitle='{"num":"editPeerListView<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                    "link":"/order/newBuyorderPeerList/editPeerListView2.do?buyorderId=${buyorderVo.buyorderId}" }'>模板导入新增信息</span>
                     <span class="title-click addtitle" style="float: none" tabtitle='{"num":"editPeerListView<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                    "link":"/order/newBuyorderPeerList/editPeerListView.do?buyorderId=${buyorderVo.buyorderId}" }'>新增信息</span>
                </div>
            </c:if>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="table-small">产品名称</th>
                <th class="table-small">订货号</th>
                <th class="table-small">型号/规格</th>
                <th class="table-small">单位</th>
                <th class="table-small">品牌(生产企业)</th>
                <th class="table-small">许可证号</th>
                <th class="table-small">注册证号</th>
                <th class="table-small">生产批号/序列号</th>
                <th class="table-small">收货数量</th>
                <th class="table-small">生产日期</th>
                <th class="table-small">失效日期</th>
                <th class="table-small">录入日期</th>
                <th class="table-small">操作人</th>
                <th class="table-small">操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty purchaseDeliveryDirectBatchInfoVoList}">
                <c:forEach items="${purchaseDeliveryDirectBatchInfoVoList}" var="list" varStatus="status">
                    <c:forEach items="${list.list}" var="detail" varStatus="num">
                        <tr>
                            <td>${detail.skuName}</td>
                            <td>${detail.sku}</td>
                            <td>${detail.model}</td>
                            <td>${detail.unit}</td>
                            <td>${detail.productCompany}</td>
                            <td>${detail.productionLicence}</td>
                            <td>${detail.registerNumber}</td>
                            <td>${detail.batchNumber}</td>
                            <td>${detail.arrivalCount}</td>
                            <td><fmt:formatDate value="${detail.manufactureDateTime}" pattern="yyyy-MM-dd"/></td>
                            <td><fmt:formatDate value="${detail.invalidDateTime}" pattern="yyyy-MM-dd"/></td>
                            <td><fmt:formatDate value="${detail.addTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                            <td>${detail.creatorName}</td>
                            <c:if test="${num.count == 1}">
                                <td rowspan="${fn:length(list.list)}">
                                    <div class="caozuo">
                                        <c:if test="${list.isUploadFile eq 1}">
                                            <span class="caozuo-blue pop-new-data"
                                                  layerparams='{"width":"50%","height":"70%","title":"查看同行单","link":"<%=basePath%>/order/newBuyorderPeerList/preViewAttachment.do?purchaseDeliveryDirectBatchInfoId=${detail.purchaseDeliveryDirectBatchInfoId}"}'>查看同行单</span>
                                        </c:if>
                                        <c:if test="${list.isUploadFile eq 0}">
                                            <span class="caozuo-grey">查看同行单</span>
                                        </c:if>
                                        <span class="caozuo-blue pop-new-data" style="margin: 0px"
                                              layerparams='{"width":"50%","height":"30%","title":"重新上传同行单","link":"<%=basePath%>/order/newBuyorderPeerList/preAddDeliveryDirectAttachment.do?purchaseDeliveryDirectBatchInfoId=${detail.purchaseDeliveryDirectBatchInfoId}"}'>重新上传同行单</span>
                                    </div>
                                </td>
                            </c:if>
                        </tr>
                    </c:forEach>
                </c:forEach>
            </c:if>
            <c:if test="${empty purchaseDeliveryDirectBatchInfoVoList}">
                <tr>
                    <td colspan="14">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                入库附件
            </div>
            <c:if test="${buyorderVo.validStatus eq 1}">
               <c:if test="${isShowWarehouseUpload eq 1}">
            <div class="title-click nobor  pop-new-data" layerparams='{"width":"520px","height":"300px","title":"入库附件","link":"./uploadInboundAttachments.do?buyorderId=${buyorderVo.buyorderId}"}' >
                   	 新增附件
            </div>
               </c:if>
            </c:if>
        </div>
        <table class="table  table-style6">
            <thead>
            <tr>
                <th class="wid6">序号</th>
                <c:if test="${isShowWarehouseUpload eq 1}">
                <th class="table-small">附件类型</th>
                </c:if>
                <th>附件名称</th>
                <th>操作人</th>
                <th>上传时间</th>
                <c:if test="${isShowWarehouseUpload eq 1}">
                <th class="table-smallest5"> 操作</th>
                </c:if>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty AttachmentList }">
                <c:forEach var="att" items="${AttachmentList}" varStatus="status">
                    <tr>
                        <td>${status.count}</td>
                        <%--如果att.attachmentFunction = 4330则附件类型显示采购普发同行单 如果是4331则显示质检报告  --%>
                        <c:if test="${isShowWarehouseUpload eq 1}">
                        <td><c:choose>
                            <c:when test="${att.attachmentFunction == 4330}">
                                同行单
                            </c:when>
                            <c:when test="${att.attachmentFunction == 4331}">
                                质检报告
                            </c:when>
                        </c:choose></td>
                        </c:if>
                        <td><a href="http://${att.domain}${att.uri}" target="_blank">${att.name}</a>
                        </td>
                        <td>${att.username}</td>
                        <td><date:date value="${att.addTime}"/></td>
                        <c:if test="${isShowWarehouseUpload eq 1}">
                        <td>
                            <div class="caozuo">

                              <span class="caozuo-red"
                              onclick="contractReturnDel(${att.attachmentId})">删除</span>

                            </div>
                        </td>
                        </c:if>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty AttachmentList }">
                <c:if test="${isShowWarehouseUpload eq 1}">
                <td colspan="6">暂无入库附件记录</td>
                </c:if>
                <c:if test="${isShowWarehouseUpload ne 1}">
                <td colspan="4">暂无入库附件记录</td>
                </c:if>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">发票列表</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <td>发票号</td>
                <td>票种</td>
                <td>红蓝字</td>
                <td>发票金额</td>
                <td>操作人</td>
                <td>操作时间</td>
                <td>审核状态</td>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty buyorderVo.invoiceList}">
                <c:set var="haveMoney" value="0"></c:set>
                <c:forEach items="${buyorderVo.invoiceList}" var="vr" varStatus="">
                    <c:set var="haveMoney" value="${haveMoney + vr.amount}"></c:set>
                    <tr>
                        <td>
                                ${vr.invoiceNo}

                            <c:if test="${not empty vr.invoiceRelationWarehouseLogDtoList}">
                                <span class="customername pos_rel">
                                        <i class="iconbluemouth contorlIcon"></i>
                                        <div class="pos_abs customernameshow mouthControlPos">
                                            入库单绑定关系：<br><br>
                                            <c:forEach items="${vr.invoiceRelationWarehouseLogDtoList}" var="relationItem"  varStatus="relationStatus">
                                                入库单号：${relationItem.outInNo}<br>
                                                <c:if test="${not empty relationItem.itemList}">
                                                    商品明细：
                                                    <c:forEach items="${relationItem.itemList}" var="item" varStatus="relationItemStatus">
                                                        ${item.sku}（订货号） ${item.totalNum}（数量）
                                                        <c:if test="${!relationItemStatus.last}">
                                                            、
                                                        </c:if>
                                                    </c:forEach>
                                                </c:if>
                                                <c:if test="${!relationStatus.last}">
                                                    <br><br>
                                                </c:if>
                                            </c:forEach>
                                        </div>
                                    </span>
                            </c:if>

                        </td>
                        <td>
                            <c:forEach var="invoiceList" items="${invoiceTypes}" varStatus="status">
                                <c:if test="${invoiceList.sysOptionDefinitionId eq vr.invoiceType}">${invoiceList.title}</c:if>
                            </c:forEach>
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${vr.colorComplementType eq 1}">
                                    蓝字冲销
                                </c:when>
                                <c:otherwise>
                                    <c:if test="${vr.colorType eq 1 and vr.isEnable eq 1}">红字有效</c:if>
                                    <c:if test="${vr.colorType eq 1 and vr.isEnable eq 0}">红字作废</c:if>
                                    <c:if test="${vr.colorType eq 2 and vr.isEnable eq 1}">蓝字有效</c:if>
                                    <c:if test="${vr.colorType eq 2 and vr.isEnable eq 0}">蓝字作废</c:if>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td><fmt:formatNumber type="number" value="${vr.amount }" pattern="0.00"
                                              maxFractionDigits="2"/></td>
                        <td>${vr.creatorName }</td>
                        <td><date:date value="${vr.addTime}"/></td>
                        <td>
                            <c:choose>
                                <c:when test="${vr.validStatus eq 0}">
                                    待审核
                                </c:when>
                                <c:when test="${vr.validStatus eq 1}">
                                    审核通过
                                </c:when>
                                <c:when test="${vr.validStatus eq 2}">
                                    <span class="font-red">审核不通过</span>
                                </c:when>
                            </c:choose>
                        </td>
                    </tr>
                </c:forEach>
                <tr>
                    <td colspan='7'>已开票总额：${haveMoney} &nbsp;&nbsp;
                        待开票总额：${buyorderVo.totalAmount-haveMoney - buyorderVo.usedTotalRebate}</td>
                </tr>
            </c:if>
            <c:if test="${empty buyorderVo.invoiceList}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='7'>暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container ">
            <div class="table-title nobor">
                订单修改申请
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th>订单修改申请单</th>
                <th>申请人</th>
                <th>审核状态</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach var="list" items="${buyorderModifyApplyList}" varStatus="num3">
                <tr>
                    <td>
                        <a class="addtitle" href="javascript:void(0);"
                           tabTitle='{"num":"viewbuyordermodifyapply${list.buyorderModifyApplyId}","link":"./order/newBuyorder/viewModifyApply.do?buyorderModifyApplyId=${list.buyorderModifyApplyId}","title":"采购单修改信息"}'>${list.buyorderModifyApplyNo}</a>
                    </td>
                    <td>${list.creatorName}</td>
                    <td>
                        <c:choose>
                            <c:when test="${list.verifyStatus eq 0}">
                                审核中
                            </c:when>
                            <c:when test="${list.verifyStatus eq 1}">
                                审核通过
                            </c:when>
                            <c:when test="${list.verifyStatus eq 2}">
                                审核未通过
                            </c:when>
                            <c:otherwise>
                                待审核
                            </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
            </c:forEach>
            <c:if test="${empty buyorderModifyApplyList}">
                <tr>
                    <td colspan="3">暂无订单修改申请。</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">售后列表</div>
            <c:if test="${addAfterSales eq 1 and buyorderVo.status != 0 and buyorderVo.status != 3 && buyorderVo.lockedStatus != 1 && buyorderVo.userId eq curr_user.userId}">
                <div class="title-click nobor addtitle"
                     tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
        "link":"./order/newBuyorder/addAfterSalesPage.do?flag=th&&buyorderId=${buyorder.buyorderId}","title":"新增售后"}'>
                    新增售后
                </div>
            </c:if>

        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid15">售后单号</th>
                <th class="wid15">售后类型</th>
                <th class="wid20">创建时间</th>
                <th class="wid10">创建人</th>
                <th class="wid10">订单状态</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${asList}" var="aftersales" varStatus="status">
                <tr>
                    <td>
                <span class="font-blue addtitle"
                      tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                            "link":"./order/newBuyorder/viewAfterSalesDetail.do?afterSalesId=${aftersales.afterSalesId}&traderType=2","title":"售后详情"}'>${aftersales.afterSalesNo}</span>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${aftersales.type eq 546}">采购退货</c:when>
                            <c:when test="${aftersales.type eq 547}">采购换货</c:when>
                            <c:when test="${aftersales.type eq 548}">采购退票</c:when>
                            <c:when test="${aftersales.type eq 549}">采购退款</c:when>
                        </c:choose>
                    </td>
                    <td><date:date value="${aftersales.addTime}"/></td>
                    <td>${aftersales.creatorName}</td>
                    <td>
                        <c:if test="${aftersales.atferSalesStatus eq 0}">待确认</c:if>
                        <c:if test="${aftersales.atferSalesStatus eq 1}">进行中</c:if>
                        <c:if test="${aftersales.atferSalesStatus eq 2}">已完结</c:if>
                        <c:if test="${aftersales.atferSalesStatus eq 3}">已关闭</c:if>
                    </td>
                </tr>
            </c:forEach>
            <c:if test="${empty asList}">
                <tr>
                    <td colspan="5">暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts content1" id="contractReturn">
        <div class="title-container">
            <div class="table-title nobor">
                合同回传
            </div>
            <div class=" title-click nobor pop-new-data "
                             layerParams='{"width":"80%","height":"60%","title":"审核记录","link":"/order/newBuyorder/purchaseContractInfo.do?buyorderId=${buyorderVo.buyorderId}"}'>
                 审核记录
            </div>
            <c:if test="${buyorderVo.validStatus eq 1 && buyorderVo.status ne 3 && buyorderVo.lockedStatus ne 1 && canUploadPurchaseContract}">
                <div class="title-click nobor pop-new-data "
                     layerParams='{"width":"50%","height":"50%","title":"合同回传","link":"/order/buyorder/contractReturnInit.do?buyorderId=${buyorderVo.buyorderId}&altType=0"}'>
                    上传非标准合同
                    <div class="tooltip">
                        <span class="tooltiptext" style="width: 280px">未使用贝登采购模板的合同即为非标准合同</span>
                    </div>
                </div>

                <div class=" title-click nobor pop-new-data "
                     layerParams='{"width":"50%","height":"50%","title":"合同回传","link":"/order/buyorder/contractReturnInit.do?buyorderId=${buyorderVo.buyorderId}&altType=1"}'>
                    上传标准合同
                    <div class="tooltip">
                        <span class="tooltiptext" style="width: 260px">使用贝登采购模板的合同即为标准合同</span>
                    </div>

                </div>
            </c:if>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="table-small">合同名称</th>
                <th class="table-small">合同类型</th>
                <th class="table-small">上传人</th>
                <th class="table-small">操作时间</th>
                <th class="table-small">节点处理人</th>
                <th class="table-small">状态</th>
                <th class="table-small">操作</th>

            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty voMap}">
                <c:forEach items="${voMap}" var="m">
                    <tr>
                    <td>
                        <c:forEach items="${m.value}" var="i">
                            <a href="http://${i.domain}${i.uri}" target="_blank">${i.name}</a></br>
                        </c:forEach>
                    </td>
                    <td>
                        ${m.value[0].alt}
                    </td>
                    <td>
                        ${m.value[0].username}
                    </td>
                    <td>
                        <date:date value ="${m.value[0].addTime}"/>
                    </td>
                    <td>
                        ${m.value[0].verifyName}
                    </td>
                    <td>
                        ${m.value[0].verifyStatusStr}
                    </td>
                    <td>
                        <c:if test="${m.value[0].verifyStatus == 1 || m.value[0].verifyStatus == 2}">
                            --
                        </c:if>
                        <c:if test="${m.value[0].verifyStatus == 0 && fn:contains(m.value[0].verifyName.toLowerCase(),curr_user.username.toLowerCase())}">
                            <button type="button" class="bt-bg-style bt-small bg-light-blue mr10" onclick="passContractVerify(${buyorderVo.buyorderId},${m.value[0].addTime})">通过</button>
                            <button type="button" class="bt-bg-style bt-small bg-light-blue mr10 pop-new-data"
                                    layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"/order/newBuyorder/purchaseContractComplement.do?taskId=${taskInfo.id}&pass=false&type=3&buyorderId=${buyorderVo.buyorderId}&addTime=${m.value[0].addTime}"}'>不通过</button>
                        </c:if>
                    </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty voMap}">
                <tr>
                    <td colspan="7">暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">入库记录</div>
            <c:if test="${buyorderVo.status eq 1 || buyorderVo.status eq 2}">
                <div class="title-click nobor "
                     onclick="javascrtpt:window.open('${ezBuyorderInstockUrl}?BUYORDER_NO=${buyorderVo.buyorderNo}')">
                    查看入库记录
                </div>
            </c:if>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            </thead>
            <tbody>
                <tr>
                    <td colspan="4">请点击右上角按钮查看！</td>
                </tr>
            </tbody>
        </table>
    </div>

    </c:if>


    <div class="parts">
            <div class="title-container ">
                <div class="table-title nobor">
                    审核记录
                </div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                    <tr>
                        <th>操作人</th>
                        <th>操作时间</th>
                        <th>操作事项</th>
                        <th>备注</th>
                    </tr>
                 </thead>
                 <tbody>

				 <c:if test="${buyorderVo.riskTime != null and buyorderVo.riskTime > 0}">
                     <jsp:useBean id="riskTime" class="java.util.Date"/>
                     <jsp:setProperty name="riskTime" property="time"
                                      value="${buyorderVo.riskTime}"/>
                 </c:if>
				 <c:if test="${(null == historicActivityInstance || empty historicActivityInstance) and buyorderVo.isRisk eq 1}">
					 <tr>
						 <td>质量官</td>
						 <td>
							 <c:if test="${buyorderVo.riskTime != null and buyorderVo.riskTime > 0}">
                                 <fmt:formatDate value="${riskTime}" pattern="yyyy-MM-dd HH:mm:ss"/>
                             </c:if>
						 </td>
						 <td>质量风控审核</td>
						 <td></td>
					 </tr>
                     <tr>
						 <td>质量官</td>
						 <td>
							 <c:if test="${buyorderVo.riskTime != null and buyorderVo.riskTime > 0}">
                                 <fmt:formatDate value="${riskTime}" pattern="yyyy-MM-dd HH:mm:ss"/>
                             </c:if>
						 </td>
						 <td>驳回</td>
						 <td>${buyorderVo.riskComments}</td>
					 </tr>
                 </c:if>

                      <c:if test="${not empty historicActivityInstance}">

                          <c:forEach var="hi" items="${historicActivityInstance}">
                              <c:if test="${hi.activityType == 'startEvent'}">
                                  <c:set var="examineTime" value="${hi.endTime}"></c:set>
                              </c:if>
                          </c:forEach>

                          <c:choose>
                              <c:when test="${buyorderVo.isRisk eq 0}">
								  <tr>
									  <td>质量官</td>
									  <td>
										  <fmt:formatDate value="${examineTime}" pattern="yyyy-MM-dd HH:mm:ss"/>
									  </td>
									  <td>质量风控审核</td>
									  <td></td>
								  </tr>
                              </c:when>
                              <c:when test="${buyorderVo.isRisk eq 1 || buyorderVo.isRisk eq 3}">
                                  <c:choose>
                                      <c:when test="${permoissionsFlag}">
										  <tr>
											  <td>质量官</td>
											  <td>
												  <c:if test="${buyorderVo.riskTime != null and buyorderVo.riskTime > 0}">
                                                      <fmt:formatDate value="${riskTime}"
                                                                      pattern="yyyy-MM-dd HH:mm:ss"/>
                                                  </c:if>
											  </td>
											  <td>质量风控审核</td>
											  <td></td>
										  </tr>
                                          </tr>
                                          <tr>
											  <td>质量官</td>
											  <td>
												  <c:if test="${buyorderVo.riskTime != null and buyorderVo.riskTime > 0}">
                                                      <fmt:formatDate value="${riskTime}"
                                                                      pattern="yyyy-MM-dd HH:mm:ss"/>
                                                  </c:if>
											  </td>
											  <td>驳回</td>
											  <td>${buyorderVo.riskComments}</td>
										  </tr>

                                          <tr>
											  <td>${startUser}</td>
											  <td>
												  <fmt:formatDate value="${examineTime}" pattern="yyyy-MM-dd HH:mm:ss"/>
											  </td>
											  <td>质量风控审核</td>
											  <td></td>
										  </tr>
                                          <tr>
											  <td>${startUser}</td>
											  <td>
												  <fmt:formatDate value="${examineTime}" pattern="yyyy-MM-dd HH:mm:ss"/>
											  </td>
											  <td>审核通过</td>
											  <td><span style="color: red">人工审核通过</span></td>
										  </tr>
                                      </c:when>

                                      <c:otherwise>
										  <tr>
											  <td>质量官</td>
											  <td>
												  <c:if test="${buyorderVo.riskTime != null and buyorderVo.riskTime > 0}">
                                                      <fmt:formatDate value="${riskTime}"
                                                                      pattern="yyyy-MM-dd HH:mm:ss"/>
                                                  </c:if>
											  </td>
											  <td>质量风控审核</td>
											  <td></td>
										  </tr>
                                          </tr>
                                          <tr>
											  <td>质量官</td>
											  <td>
												  <c:if test="${buyorderVo.riskTime != null and buyorderVo.riskTime > 0}">
                                                      <fmt:formatDate value="${riskTime}"
                                                                      pattern="yyyy-MM-dd HH:mm:ss"/>
                                                  </c:if>
											  </td>
											  <td>驳回</td>
											  <td>${buyorderVo.riskComments}</td>
										  </tr>
                                          <tr>
											  <td>质量官</td>
											  <td>
												  <fmt:formatDate value="${examineTime}" pattern="yyyy-MM-dd HH:mm:ss"/>
											  </td>
											  <td>质量风控审核</td>
											  <td></td>
										  </tr>
                                      </c:otherwise>
                                  </c:choose>
                              </c:when>
                          </c:choose>

                          <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                              <c:if test="${not empty  hi.activityName}">
                    <tr>
                    	<td>
                    	<c:choose>
                            <c:when test="${hi.activityType == 'startEvent'}">
                                ${startUser}
                            </c:when>
                            <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                            </c:when>
                            <c:otherwise>
                                <c:if test="${historicActivityInstance.size() == status.count}">
                                    ${verifyUsers}
                                </c:if>
                                <c:if test="${historicActivityInstance.size() != status.count}">
                                    <c:forEach items="${assigneeVos}" var="assigneeVo">
                                        <c:if test="${assigneeVo.assignee eq hi.assignee}">
                                            ${assigneeVo.realName}
                                        </c:if>
                                    </c:forEach>
                                    <%--${hi.assignee}  --%>
                                </c:if>
                            </c:otherwise>
                        </c:choose>


                    	</td>
                        <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                        <td>
                        <c:choose>
                            <c:when test="${hi.activityType == 'startEvent'}">
                                开始
                            </c:when>
                            <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                结束
                            </c:when>
                            <c:otherwise>
                                ${hi.activityName}
                            </c:otherwise>
                        </c:choose>
						</td>
                        <td class="font-red">${commentMap[hi.taskId]}</td>
                    </tr>
                              </c:if>
                          </c:forEach>
                      </c:if>
                    <c:if test="${empty historicActivityInstance and buyorderVo.isRisk ne 1}">
                        <!-- 查询无结果弹出 -->
                        <tr>
					       <td colspan='4'>暂无记录！</td>
					    </tr>
                    </c:if>
                </tbody>
            </table>

        	<div class="clear"></div>
        </div>

    <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">
                    	付款审核记录
                </div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                    <tr>
                        <th>操作人</th>
                        <th>操作时间</th>
                        <th>操作事项</th>
                        <th>备注</th>
                    </tr>
                 </thead>
                 <tbody>
                      <c:if test="${null!=historicActivityInstancePay}">
                          <c:forEach var="hi" items="${historicActivityInstancePay}" varStatus="status">
                              <c:if test="${not empty  hi.activityName}">
                    <tr>
                    	<td>
                    	<c:choose>
                            <c:when test="${hi.activityType == 'startEvent'}">
                                ${startUserPay}
                            </c:when>
                            <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                            </c:when>
                            <c:otherwise>
                                <c:if test="${historicActivityInstancePay.size() == status.count}">
                                    ${verifyUsersPay}
                                </c:if>
                                <c:if test="${historicActivityInstancePay.size() != status.count}">
                                    ${hi.assignee}
                                </c:if>
                            </c:otherwise>
                        </c:choose>


                    	</td>
                        <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                        <td>
                        <c:choose>
                            <c:when test="${hi.activityType == 'startEvent'}">
                                开始
                            </c:when>
                            <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                结束
                            </c:when>
                            <c:otherwise>
                                ${hi.activityName}
                            </c:otherwise>
                        </c:choose>
						</td>
                        <td class="font-red">${commentMapPay[hi.taskId]}</td>
                    </tr>
                              </c:if>
                          </c:forEach>
                      </c:if>
                </tbody>
            </table>
            <c:if test="${null==historicActivityInstancePay or empty historicActivityInstancePay}">
                <!-- 查询无结果弹出 -->
                <div class="noresult">暂无审核记录。</div>
            </c:if>
        	<div class="clear"></div>
        </div>



</span>
</form>


</span>
</form>
</html>

<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script>
    layui.use('element', function () {
        var $ = layui.jquery
            , element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块

        //触发事件
        var active = {
            tabChange: function () {
                //切换到指定Tab项
                element.tabChange('demo', '22'); //切换到：用户管理
            }
        };

        $('.site-demo-active').on('click', function () {
            var othis = $(this), type = othis.data('type');
            active[type] ? active[type].call(this, othis) : '';
        });

        //Hash地址的定位
        var layid = location.hash.replace(/^#test=/, '');
        element.tabChange('test', layid);

        element.on('tab(test)', function (elem) {
            location.hash = 'test=' + $(this).attr('lay-id');
        });

    });
</script>


<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/orderstream/index.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/orderstream/openfile.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/orderstream/buyorder/buyorder_detail.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/order/saleorder/saleorder_common.js?rnd=${resourceVersionKey}'></script>

