package com.vedeng.erp.broadcast.mapdao;

import com.vedeng.erp.broadcast.domain.entity.BroadcastTargetEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业绩目标Mapper
 */
public interface BroadcastTargetMapper {

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 删除记录数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insert(BroadcastTargetEntity record);

    /**
     * 选择性插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insertSelective(BroadcastTargetEntity record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    BroadcastTargetEntity selectByPrimaryKey(Integer id);

    /**
     * 选择性更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKeySelective(BroadcastTargetEntity record);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKey(BroadcastTargetEntity record);

    // ========== 自定义查询方法 ==========

    /**
     * 批量插入目标记录
     *
     * @param records 目标记录列表
     * @return 插入记录数
     */
    int batchInsert(@Param("records") List<BroadcastTargetEntity> records);

    /**
     * 根据年度和目标类型删除记录（覆盖式导入使用）
     *
     * @param targetYear 目标年度
     * @param targetType 目标类型
     * @param updater 更新人
     * @return 删除记录数
     */
    int deleteByYearAndType(@Param("targetYear") Integer targetYear,
                           @Param("targetType") Integer targetType,
                           @Param("updater") Integer updater);

    /**
     * 查询目标列表（带目标对象名称）
     *
     * @param targetYear 目标年度
     * @param targetType 目标类型（可选，null表示查询所有类型）
     * @param targetName 目标对象名称（可选，模糊查询）
     * @return 目标列表
     */
    List<BroadcastTargetEntity> selectTargetsWithName(@Param("targetYear") Integer targetYear,
                                                      @Param("targetType") Integer targetType,
                                                      @Param("targetName") String targetName);

    /**
     * 查询有数据的年度列表
     *
     * @return 年度列表（按降序排列）
     */
    List<Integer> selectAvailableYears();

    /**
     * 根据条件，获取目标列表
     * @param year 年
     * @param month 月
     * @param targetType 目标类型：1个人，2小组，3部门
     * @param teamIdList 小组ID列表
     * @param deptIdList 部门ID列表
     * @return
     */
	List<BroadcastTargetEntity> selectBroadcastTargeByParams(@Param("year") int year,
															 @Param("month") int month,
															 @Param("targetType")  int targetType, 
															 @Param("targetBuzIdList") List<Integer> targetBuzIdList);
}
