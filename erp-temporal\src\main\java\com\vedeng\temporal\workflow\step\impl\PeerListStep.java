package com.vedeng.temporal.workflow.step.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.dto.ExpressSignDto;
import com.vedeng.temporal.enums.BusinessStepType;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.polling.universal.workflow.UniversalPollingWorkflow;
import com.vedeng.temporal.workflow.activity.InventoryReceiptActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 同行单步骤 - 新架构版本
 * <p>
 * 业务流程：
 * 1. awaitPurchaseOrderQueryCompletion - 查询采购单号并存储到扩展属性
 * 2. awaitExpressQueryCompletion - 轮询等待快递状态准备就绪 (expressStatus=1)
 * 3. createExpressOnly - 查询快递信息并创建快递
 * 4. awaitStockQueryCompletion - 查询库存记录入参准备
 * 5. createPeerListWithStockData - 基于库存数据创建同行单
 * 6. waitForCompletion - 等待同行单处理完成（可选）
 * <p>
 * 错误处理：
 * - Activity层：技术异常自动重试（网络、超时等）
 * - Step层：业务异常处理（数据校验、业务规则等）
 * - 完整的状态追踪和日志记录
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (同行单架构版本)
 * @since 2025-08-06
 */
@Slf4j
public class PeerListStep implements BusinessStep {

    private final InventoryReceiptActivity inventoryReceiptActivity;

    public PeerListStep(InventoryReceiptActivity inventoryReceiptActivity) {
        this.inventoryReceiptActivity = inventoryReceiptActivity;
    }

    @Override
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, CompanyExecutionContext context) {
        // 固化关键参数，防止 Temporal 重试时参数变化
        final String currentCompany = context.getCurrentCompany();
        final String nextCompany = context.getNextCompany();
        final String originalSourceCompany = request.getSourceCompanyCode();
        final String originalTargetCompany = request.getTargetCompanyCode();
        final boolean isFirst = context.isFirst();
        final boolean isLast = context.isLast();
        
        log.info("开始执行同行单步骤，业务ID: {}, 源公司: {}, 原目标公司: {}",
                request.getBusinessId(), originalSourceCompany, originalTargetCompany);
        
        // 创建request副本，避免修改原始对象
        CompanyBusinessRequest safeRequest = request.toBuilder()
                .sourceCompanyCode(currentCompany)
                .targetCompanyCode(nextCompany)
                .build();
        // 最后一家公司跳过处理
        if (isLast) {
            log.info("执行最后一家公司：{}，无需处理同行单", currentCompany);
            return CompanyBusinessResponse.success("最后一家公司无需处理，跳过执行", safeRequest.getBusinessId());
        }

        // 第1步：查询当前公司的采购单号
        log.info("开始查询采购单号，公司: {}, 业务ID: {}", currentCompany, safeRequest.getBusinessId());
        Object buyOrderNo =  awaitPurchaseOrderQueryCompletion(currentCompany, safeRequest);
        Object nextBuyOrderNo = awaitPurchaseOrderQueryCompletion(nextCompany, safeRequest);

        // 创建新的ExtendedProperties副本，避免并发修改问题
        Map<String, Object> safeExtendedProperties = new HashMap<>();
        if (safeRequest.getExtendedProperties() != null) {
            safeExtendedProperties.putAll(safeRequest.getExtendedProperties());
        }
        safeExtendedProperties.put("buyOrderNo", buyOrderNo);
        safeExtendedProperties.put("nextBuyOrderNo", nextBuyOrderNo);
        safeExtendedProperties.put("isFirst", isFirst ? 1 : 0);
        
        // 更新safeRequest的ExtendedProperties
        safeRequest = safeRequest.toBuilder()
                .extendedProperties(safeExtendedProperties)
                .build();

        // 第4步：查询库存记录并创建同行单
        // 第4.1步：查询库存记录入参准备
        log.info("开始查询库存记录，公司: {}, 业务ID: {}", currentCompany, safeRequest.getBusinessId());
        Map<String, Object> stockResultData = awaitStockQueryCompletion(currentCompany, safeRequest);
        log.info("库存记录查询完成，公司: {}, 业务ID: {}", currentCompany, safeRequest.getBusinessId());

        // 第4.2步：创建同行单
        CompanyBusinessResponse peerListResult = inventoryReceiptActivity.createPeerListWithStockData(safeRequest, stockResultData);
        log.info("创建同行单结果：{}",JSON.toJSON(peerListResult));
        return peerListResult;
    }

    @Override
    public String getStepName() {
        return "同行单步骤";
    }

    @Override
    public BusinessStepType getStepType() {
        return BusinessStepType.PASSAGE_RECEIPT;
    }

    @Override
    public String getStepDescription() {
        return "执行同行单完整流程：查询采购单号 → 等待快递状态 → 创建快递 → 查询库存记录 → 创建同行单";
    }


    /**
     * 等待采购单号查询完成
     */
    private Object awaitPurchaseOrderQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始轮询等待采购单号，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 构建查询参数
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("queryTypes", Arrays.asList("BUY_ORDER"));
            queryParameters.put("currentCompany", companyCode);

            // 构建统一轮询请求 - 使用数据库轮询
            UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.LOCAL_DATABASE)
                    .companyCode(companyCode)
                    .queryType("TEMPORAL_FLOW_ORDER_QUERY")
                    .queryParameters(queryParameters)
                    .completionCheckConfig("buyOrderNo:isNotBlank")
                    .build();

            // 执行统一轮询（使用静态方法保持确定性）
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);

            // 轮询成功后，需要手动获取最终数据并设置到扩展属性
            if (result.isSuccess()) {
                Map<String, Object> data = result.getData();
                return data.get("buyOrderNo");
            } else {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("采购单号轮询未完成", "POLLING_INCOMPLETE", context);
            }

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("采购单号轮询异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("采购单号轮询系统异常", "POLLING_ERROR", context);
        }
    }

    /**
     * 第2步：等待快递信息查询完成，判断expressStatus是否为1
     * 从 InventoryReceiptStep 迁移的逻辑，用于轮询等待快递状态准备就绪
     *
     * @return
     */
    private Map<String, Object> awaitExpressQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始查询快递信息，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 从扩展属性中获取采购单号
            Map<String, Object> extendedProperties = request.getExtendedProperties();
            if (extendedProperties == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("扩展属性为空，无法获取采购单号", "MISSING_EXTENDED_PROPERTIES", context);
            }

            String buyOrderNo = (String) extendedProperties.get("buyOrderNo");
            if (buyOrderNo == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("未找到采购单号", "MISSING_BUY_ORDER_NO", context);
            }

            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("buyOrderNo", buyOrderNo);
            log.info("开始查询快递信息，公司: {}, 业务ID: {}, 入参: {}", companyCode, businessId, JSON.toJSON(apiParameters));

            // 构建统一轮询请求
            UniversalPollingRequest statusRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(companyCode)
                    .apiPath("/api/v1/express/query.do")  // 快递查询API路径
                    .apiParameters(apiParameters)  // 使用自定义API参数
                    // 快递查询完成条件：expressStatus为1表示可以创建
                    .completionCheckConfig("data.expressStatus:1")
                    .build();

            log.info("使用快递查询检查器：查询快递信息，采购单号: {}", buyOrderNo);

            // 执行轮询等待
            UniversalPollingResult<Map<String, Object>> finalResult = UniversalPollingWorkflow.universalPoll(statusRequest);
            log.info("轮询结果: {}", finalResult);
            // 验证最终状态
            if (!finalResult.isSuccess()) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("快递信息查询轮询失败: " + finalResult.getMessage(), "POLLING_INCOMPLETE", context);
            }

            log.info("快递信息查询完成，公司: {}, 业务ID: {}, 轮询结果: {}", companyCode, businessId, finalResult.isSuccess() ? "成功" : "失败");

            return finalResult.getData();
        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("查询快递信息异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("快递信息查询系统异常", "POLLING_ERROR", context);
        }
    }

    /**
     * 第4.1步：等待库存记录查询完成
     * 参考 awaitExpressQueryCompletion 的模式，查询库存记录并返回结果数据
     */
    private Map<String, Object> awaitStockQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始查询库存记录，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 从扩展属性中获取采购单号
            Map<String, Object> extendedProperties = request.getExtendedProperties();
            if (extendedProperties == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("扩展属性为空，无法获取采购单号", "MISSING_EXTENDED_PROPERTIES", context);
            }

            String buyOrderNo = (String) extendedProperties.get("buyOrderNo");
            if (buyOrderNo == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("未找到采购单号", "MISSING_BUY_ORDER_NO", context);
            }

            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("buyOrderNo", buyOrderNo);
            apiParameters.put("isFirst", extendedProperties.get("isFirst"));

            log.info("同行单步骤调用/api/v1/peerlist/queryStockRecords.do，入参：{}", apiParameters);
            // 构建统一轮询请求
            UniversalPollingRequest stockRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(companyCode)
                    .apiPath("/api/v1/peerlist/queryStockRecords.do")  // 库存查询API路径
                    .apiParameters(apiParameters)  // 使用自定义API参数
                    // 库存查询完成条件：有数据返回即可
                    .completionCheckConfig("data.peerStatus:1")
                    .build();

            log.info("使用库存查询检查器：查询库存记录，采购单号: {}, isFirst: {}", buyOrderNo, extendedProperties.get("isFirst"));

            // 执行轮询等待
            UniversalPollingResult<Map<String, Object>> finalResult = UniversalPollingWorkflow.universalPoll(stockRequest);
            log.info("同行单入参库存查询轮询结果: {}", finalResult);

            // 验证最终状态
            if (!finalResult.isSuccess()) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("库存记录查询轮询失败: " + finalResult.getMessage(), "POLLING_INCOMPLETE", context);
            }

            log.info("库存记录查询完成，公司: {}, 业务ID: {}, 轮询结果: {}", companyCode, businessId, finalResult.isSuccess() ? "成功" : "失败");

            return finalResult.getData();
        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("查询库存记录异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("库存记录查询系统异常", "POLLING_ERROR", context);
        }
    }


    /**
     * 异步执行快递签收流程
     * 优化：移除Workflow.await的使用，直接使用轮询机制
     */
    private void executeAsyncExpressReceipt(CompanyBusinessRequest request, String currentCompany, ExpressSignDto expressSignDto, String nextCompany) {
        try {
            log.info("开始异步快递签收流程，当前公司: {}, 下一个公司: {}, 物流号: {}",
                    currentCompany, nextCompany, expressSignDto.getLogisticsNo());
            
            // 1. 如果有上游公司，等待上游签收完成
            if (currentCompany != null) {
                log.info("等待上游公司 {} 签收完成，当前公司: {}, 入参: {}",
                        currentCompany, currentCompany, JSON.toJSON(expressSignDto));
                
                // 直接使用轮询机制，不使用Workflow.await避免死锁
                boolean signCompleted = awaitExpressSignComplete(currentCompany, request, expressSignDto);
                if (!signCompleted) {
                    log.warn("上游公司 {} 签收未完成，跳过当前异步任务", currentCompany);
                    return;
                }
            }

            // 2. 执行签收
            log.info("开始执行快递签收，目标公司: {}, 物流号: {}", nextCompany, expressSignDto.getLogisticsNo());
            inventoryReceiptActivity.executeExpressReceipt(nextCompany, expressSignDto, request);
            log.info("快递签收完成，目标公司: {}, 物流号: {}", nextCompany, expressSignDto.getLogisticsNo());

        } catch (BusinessProcessException e) {
            // 业务异常：记录日志并重新抛出，让Temporal处理重试
            log.error("异步快递签收业务异常，公司: {}, 业务ID: {}, 物流号: {}, 错误: {}",
                    currentCompany, request.getBusinessId(), expressSignDto.getLogisticsNo(), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            // 系统异常：包装为业务异常并抛出
            log.error("异步快递签收系统异常，公司: {}, 业务ID: {}, 物流号: {}",
                    currentCompany, request.getBusinessId(), expressSignDto.getLogisticsNo(), e);
            String context = "AsyncExpressReceipt, Company=" + currentCompany + ", BusinessId=" + request.getBusinessId();
            throw BusinessProcessException.retryable("异步快递签收系统异常", "ASYNC_EXPRESS_RECEIPT_ERROR", context);
        }
    }

    /**
     * 等待快递签收完成
     * 优化：改进错误处理逻辑，防止异常被忽略
     */
    private Boolean awaitExpressSignComplete(String currentCompany, CompanyBusinessRequest request, ExpressSignDto expressSignDto) {
        try {
            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("logisticsNo", expressSignDto.getLogisticsNo());
            apiParameters.put("buyOrderNo", expressSignDto.getBuyOrderNo());

            // 构建统一轮询请求
            UniversalPollingRequest statusRequest = UniversalPollingRequest.builder()
                    .businessId(request.getBusinessId())
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(currentCompany)
                    .apiPath("/api/v1/express/signCheck.do")  // 快递查询API路径
                    .apiParameters(apiParameters)  // 使用自定义API参数
                    .completionCheckConfig("data.arrivalStatus:2")
                    .build();

            log.info("使用快递查询检查器：查询快递信息，采购单号: {}, 物流号: {}", expressSignDto.getBuyOrderNo(), expressSignDto.getLogisticsNo());
            
            // 执行轮询等待
            UniversalPollingResult<Map<String, Object>> finalResult = UniversalPollingWorkflow.universalPoll(statusRequest);
            log.info("快递签收状态轮询结果: {}", finalResult.isSuccess() ? "成功" : "失败 - " + finalResult.getMessage());
            
            // 验证最终状态
            return finalResult.isSuccess();
            
        } catch (Exception e) {
            log.error("等待快递签收完成异常，公司: {}, 物流号: {}, 采购单号: {}",
                    currentCompany, expressSignDto.getLogisticsNo(), expressSignDto.getBuyOrderNo(), e);
            // 异常情况下返回false，让上层决定是否继续
            return false;
        }
    }
}


