<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>

    <%@ include file="../common/common.jsp" %>

        <link rel="stylesheet" href="/static/vue/receiptnotice/target.css">

        <div class="target-wrap" id="page-container">
            <div class="target-top">
                <div class="target-filter-list">
                    <div class="target-filter-item">
                        <div class="filter-label">年度：</div>
                        <div class="filter-cnt">
                            <el-select v-model="currentYear" placeholder="请选择" size="small" @change="getTargetList">
                                <el-option v-for="item in yearList" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                </div>
                <div class="target-options">
                    <el-upload
                        action="/broadcast/target/import.do"
                        accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" 
                        :show-file-list="false" 
                        :on-progress="handlerImportProgress"  
                        :on-success="handlerImportSuccess"
                        :on-error="handlerImportError"
                    >
                        <el-button type="primary" size="small">导入目标</el-button>
                    </el-upload>
                    <el-button size="small" @click="downloadTemplate">下载模板</el-button>
                </div>
            </div>
            <div class="target-content" v-if="yearList.length">
                <el-tabs v-model="tabActiveIndex">
                    <el-tab-pane label="个人" name="1">
                        <el-table :data="personList" border style="width: 100%" size="small" header-cell-class-name="gray" :highlight-current-row="true">
                            <el-table-column prop="targetName" label="姓名" width="120">
                            </el-table-column>
                            <template v-for="item in 12" :key="item">
                                <el-table-column :prop="'month' + item" :label="item + '月'">
                                </el-table-column>
                            </template>
                        </el-table>
                    </el-tab-pane>
                    <el-tab-pane label="小组" name="2">
                        <el-table :data="groupList" border style="width: 100%" size="small" header-cell-class-name="gray">
                            <el-table-column prop="targetName" label="三级组" width="130">
                            </el-table-column>
                            <template v-for="item in 12" :key="item">
                                <el-table-column :prop="'month' + item" :label="item + '月'">
                                </el-table-column>
                            </template>
                        </el-table>
                    </el-tab-pane>
                    <el-tab-pane label="部门" name="3">
                        <el-table :data="departmentList" border style="width: 100%" size="small" header-cell-class-name="gray">
                            <el-table-column prop="targetName" label="二级部" width="130">
                            </el-table-column>
                            <template v-for="item in 12" :key="item">
                                <el-table-column :prop="'month' + item" :label="item + '月'">
                                </el-table-column>
                            </template>
                        </el-table>
                    </el-tab-pane>
                </el-tabs>
            </div>
            <div class="target-empty" v-else>
                <span class="el-icon-info"></span>暂无目标数据，请先导入目标。
            </div>
        </div>

        <script src="${pageContext.request.contextPath}/static/js/receiptnotice/target.js"></script>