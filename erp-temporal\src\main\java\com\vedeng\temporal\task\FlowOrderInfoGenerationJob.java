package com.vedeng.temporal.task;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.temporal.service.FlowOrderInfoGenerationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * FlowOrderInfo状态更新定时任务
 * 专注于更新已存在的FlowOrderInfo记录的状态信息，不再负责记录创建
 * 记录创建已迁移到SalesOrderStep和PurchaseOrderStep中
 * 
 * 基于公司去调用采购接口 /api/v1/buyorder/query.do，更新采购单据状态信息
 * 也需要更新销售单据状态信息，接口是 /api/v1/saleorder/query.do
 *
 * 支持两种执行模式：
 * 1. 批量模式：参数为空、"batch"、"auto" 时，更新所有需要更新状态的FlowOrderInfo记录
 * 2. 单个模式：传入流转单编号(T_FLOW_ORDER.FLOW_ORDER_NO)，更新该流转单下所有节点的FlowOrderInfo记录状态
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (重构版本 - 专注状态更新)
 * @since 2025-01-29
 */
@Component
@JobHandler(value = "flowOrderInfoGenerationJob")
@Slf4j
public class FlowOrderInfoGenerationJob extends AbstractJobHandler {

    @Autowired
    private FlowOrderInfoGenerationService flowOrderInfoGenerationService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("================== FlowOrderInfo状态更新任务开始 ==================");
        log.info("FlowOrderInfo状态更新任务开始，参数: {}", param);

        try {
            // 判断执行模式
            if (isBatchMode(param)) {
                XxlJobLogger.log("启用批量模式，更新所有需要更新状态的FlowOrderInfo记录");
                log.info("启用批量模式，更新所有需要更新状态的FlowOrderInfo记录");
                return executeBatchMode();
            } else {
                XxlJobLogger.log("启用单个模式，更新指定流转单的所有FlowOrderInfo记录");
                log.info("启用单个模式，更新指定流转单的所有FlowOrderInfo记录");
                return executeSingleMode(param);
            }

        } catch (Exception e) {
            XxlJobLogger.log("FlowOrderInfo状态更新任务执行异常: {}", e.getMessage());
            log.error("FlowOrderInfo状态更新任务执行异常", e);
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("================== FlowOrderInfo状态更新任务结束 ==================");
            log.info("FlowOrderInfo状态更新任务结束");
        }
    }

    /**
     * 批量模式执行
     * 更新所有需要更新状态的FlowOrderInfo记录
     */
    private ReturnT<String> executeBatchMode() {
        try {
            XxlJobLogger.log("开始更新所有需要更新状态的FlowOrderInfo记录");
            log.info("开始更新所有需要更新状态的FlowOrderInfo记录");

            // 第一步：调用服务层更新所有FlowOrderInfo记录状态
            FlowOrderInfoGenerationService.ProcessResult updateResult = 
                flowOrderInfoGenerationService.updateAllErpFlowOrderInfoStatus();

            String updateMessage = String.format(
                "状态更新完成 - 总数: %d, 成功: %d, 失败: %d", 
                updateResult.getTotalCount(), 
                updateResult.getSuccessCount(), 
                updateResult.getFailureCount()
            );

            XxlJobLogger.log(updateMessage);
            log.info(updateMessage);

            // 第二步：批量检查并更新完成状态
            XxlJobLogger.log("开始批量检查并更新完成状态");
            log.info("开始批量检查并更新完成状态");

            FlowOrderInfoGenerationService.ProcessResult completionResult = 
                flowOrderInfoGenerationService.batchUpdateCompletedStatus();

            String completionMessage = String.format(
                "完成状态更新完成 - 总数: %d, 成功: %d, 失败: %d", 
                completionResult.getTotalCount(), 
                completionResult.getSuccessCount(), 
                completionResult.getFailureCount()
            );

            XxlJobLogger.log(completionMessage);
            log.info(completionMessage);

            // 汇总结果
            String finalMessage = String.format(
                "批量处理完成 - 状态更新: %d成功/%d失败, 完成状态更新: %d成功/%d失败", 
                updateResult.getSuccessCount(), updateResult.getFailureCount(),
                completionResult.getSuccessCount(), completionResult.getFailureCount()
            );

            XxlJobLogger.log(finalMessage);
            log.info(finalMessage);

            // 如果有失败的，返回警告，但不算作任务失败
            int totalFailures = updateResult.getFailureCount() + completionResult.getFailureCount();
            if (totalFailures > 0) {
                XxlJobLogger.log("存在状态更新失败的记录，请检查日志");
                return ReturnT.SUCCESS;
            }

            return ReturnT.SUCCESS;

        } catch (Exception e) {
            XxlJobLogger.log("批量模式执行异常: {}", e.getMessage());
            log.error("批量模式执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 单个模式执行
     * 更新指定流转单的所有FlowOrderInfo记录状态
     */
    private ReturnT<String> executeSingleMode(String param) {
        try {
            // 参数校验
            if (!StringUtils.hasText(param)) {
                XxlJobLogger.log("流转单编号参数为空");
                return new ReturnT<>(ReturnT.FAIL_CODE, "流转单编号参数为空");
            }

            String flowOrderNo = param.trim();
            XxlJobLogger.log("开始更新流转单的所有FlowOrderInfo状态，流转单编号: {}", flowOrderNo);
            log.info("开始更新流转单的所有FlowOrderInfo状态，流转单编号: {}", flowOrderNo);

            // 调用服务层更新指定流转单的所有FlowOrderInfo状态
            FlowOrderInfoGenerationService.ProcessResult result = 
                flowOrderInfoGenerationService.updateSingleFlowOrderInfoStatus(flowOrderNo);

            String resultMessage = String.format(
                "流转单FlowOrderInfo状态更新完成 - 流转单: %s, 总数: %d, 成功: %d, 失败: %d", 
                flowOrderNo, result.getTotalCount(), result.getSuccessCount(), result.getFailureCount()
            );

            XxlJobLogger.log(resultMessage);
            log.info(resultMessage);

            // 如果有失败的，返回警告，但不算作任务失败
            if (result.getFailureCount() > 0) {
                XxlJobLogger.log("存在FlowOrderInfo状态更新失败的记录，请检查日志");
                return ReturnT.SUCCESS;
            }

            return ReturnT.SUCCESS;

        } catch (Exception e) {
            XxlJobLogger.log("单个模式执行异常: {}", e.getMessage());
            log.error("单个模式执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 判断是否为批量模式
     *
     * @param param 任务参数
     * @return 是否为批量模式
     */
    private boolean isBatchMode(String param) {
        // 参数为空、"batch"、"auto" 时启用批量模式
        // 其他情况视为流转单编号，启用单个模式
        return !StringUtils.hasText(param) ||
               "batch".equalsIgnoreCase(param.trim()) ||
               "auto".equalsIgnoreCase(param.trim());
    }
}