package com.vedeng.api.standard.duplicate.generator;

import com.vedeng.api.standard.duplicate.constants.IdempotencyHeaders;
import com.vedeng.api.standard.duplicate.exception.IdempotencyException;
import com.vedeng.api.standard.core.ApiRequest;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 幂等性键生成器
 * 统一从请求头获取参数并生成幂等性键
 * 格式：{businessType}_{companyCode}_{flowOrderId}
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-15
 */
@Component
public class IdempotencyKeyGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(IdempotencyKeyGenerator.class);
    
    /**
     * 键分隔符（使用|避免与业务类型如PURCHASE_ORDER中的下划线冲突）
     */
    private static final String KEY_SEPARATOR = "|";
    
    /**
     * 生成幂等性键（向后兼容版本）
     * 只从请求头获取参数，格式：{businessType}|{companyCode}|{flowOrderId}
     * 
     * @param apiRequest API请求对象
     * @param businessType 业务类型
     * @return 生成的幂等性键
     * @throws IdempotencyException 如果生成失败
     */
    public String generateIdempotencyKey(ApiRequest apiRequest, String businessType) {
        return generateIdempotencyKey(apiRequest, businessType, null, null);
    }
    
    /**
     * 生成幂等性键（统一实现）
     * 格式：{businessType}|{companyCode}|{flowOrderId}|{businessField1}|{businessField2}|...
     * 
     * @param apiRequest API请求对象
     * @param businessType 业务类型
     * @param businessData 业务数据对象（可为null）
     * @param businessFieldNames 业务字段名称数组（可为null或空）
     * @return 生成的幂等性键
     * @throws IdempotencyException 如果生成失败
     */
    public String generateIdempotencyKey(ApiRequest apiRequest, String businessType, 
                                       Object businessData, String[] businessFieldNames) {
        try {
            // 1. 参数验证
            if (apiRequest == null) {
                throw new IllegalArgumentException("API请求不能为空");
            }
            if (StringUtils.isEmpty(businessType)) {
                throw new IllegalArgumentException("业务类型不能为空");
            }
            
            // 2. 从请求头提取基础参数
            String companyCode = extractFromHeader(apiRequest, IdempotencyHeaders.COMPANY_CODE, null);
            String flowOrderId = extractFromHeader(apiRequest, IdempotencyHeaders.FLOW_ORDER_ID, null);
            
            if (StringUtils.isEmpty(companyCode)) {
                throw new IllegalArgumentException("公司代码不能为空，请在请求头" + IdempotencyHeaders.COMPANY_CODE + "中提供");
            }
            if (StringUtils.isEmpty(flowOrderId)) {
                throw new IllegalArgumentException("流程订单ID不能为空，请在请求头" + IdempotencyHeaders.FLOW_ORDER_ID + "中提供");
            }
            
            // 3. 构建键组件列表
            java.util.List<String> keyComponents = new java.util.ArrayList<>();
            keyComponents.add(businessType);
            keyComponents.add(companyCode);
            keyComponents.add(flowOrderId);
            
            // 4. 从业务数据提取字段值（如果配置了业务数据和字段名）
            if (businessData != null && businessFieldNames != null && businessFieldNames.length > 0) {
                for (String fieldName : businessFieldNames) {
                    if (!StringUtils.isEmpty(fieldName)) {
                        String fieldValue = extractBusinessFieldValue(businessData, fieldName);
                        if (!StringUtils.isEmpty(fieldValue)) {
                            keyComponents.add(fieldValue);
                            logger.debug("添加业务字段到幂等性键: fieldName={}, fieldValue={}", fieldName, fieldValue);
                        } else {
                            logger.warn("业务字段值为空，跳过添加: fieldName={}, businessDataType={}", 
                                fieldName, businessData.getClass().getSimpleName());
                        }
                    }
                }
                logger.debug("从业务数据提取字段完成: fieldCount={}, extractedCount={}, businessDataType={}", 
                    businessFieldNames.length, keyComponents.size() - 3, businessData.getClass().getSimpleName());
            }
            
            // 5. 验证所有键组件字符合法性
            for (String component : keyComponents) {
                validateKeyComponent("keyComponent", component);
            }
            
            // 6. 构建幂等性键
            StringBuilder keyBuilder = new StringBuilder();
            for (int i = 0; i < keyComponents.size(); i++) {
                if (i > 0) {
                    keyBuilder.append(KEY_SEPARATOR);
                }
                keyBuilder.append(keyComponents.get(i));
            }
            
            String idempotencyKey = keyBuilder.toString();
            
            // 7. 处理键长度（用hash而非截断确保唯一性）
            if (idempotencyKey.length() > 120) {
                logger.warn("幂等性键长度超过120字符，使用hash后缀确保唯一性: length={}, originalKey={}", 
                    idempotencyKey.length(), idempotencyKey);
                // 保留前缀，后缀用hash确保唯一性
                String prefix = idempotencyKey.substring(0, 88);
                String hash = DigestUtils.md5Hex(idempotencyKey).substring(0, 32);
                idempotencyKey = prefix + "|" + hash;
            }
            
            // 8. 最终长度检查
            if (idempotencyKey.length() > 128) {
                logger.warn("生成的幂等性键长度仍超过128字符，将被截断: length={}, key={}", 
                    idempotencyKey.length(), idempotencyKey);
                idempotencyKey = idempotencyKey.substring(0, 128);
            }
            
            // 9. 统一的日志输出
            String fieldNamesStr = businessFieldNames != null && businessFieldNames.length > 0 ? String.join(",", businessFieldNames) : "none";
            logger.debug("生成幂等性键: key={}, businessType={}, companyCode={}, flowOrderId={}, businessFields=[{}]", 
                idempotencyKey, businessType, companyCode, flowOrderId, fieldNamesStr);
                
            return idempotencyKey;
            
        } catch (Exception e) {
            String fieldNamesStr = businessFieldNames != null && businessFieldNames.length > 0 ? String.join(",", businessFieldNames) : "none";
            logger.error("生成幂等性键失败: businessType={}, businessFields=[{}], requestId={}", 
                businessType, fieldNamesStr, apiRequest != null ? apiRequest.getRequestId() : "null", e);
            throw IdempotencyException.keyGenerationError("生成幂等性键失败", e);
        }
    }
    
    /**
     * 从业务数据对象中提取指定字段的值
     * 
     * @param businessData 业务数据对象
     * @param fieldName 字段名称
     * @return 字段值（转换为字符串），如果提取失败返回null
     */
    private String extractBusinessFieldValue(Object businessData, String fieldName) {
        if (businessData == null || StringUtils.isEmpty(fieldName)) {
            return null;
        }
        
        try {
            // 使用反射获取字段值
            Class<?> businessDataClass = businessData.getClass();
            
            // 首先尝试getter方法
            String getterMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            try {
                java.lang.reflect.Method getterMethod = businessDataClass.getMethod(getterMethodName);
                Object fieldValue = getterMethod.invoke(businessData);
                if (fieldValue != null) {
                    return fieldValue.toString();
                }
            } catch (Exception e) {
                logger.debug("使用getter方法获取字段值失败: method={}, error={}", getterMethodName, e.getMessage());
            }
            
            // 然后尝试直接字段访问
            try {
                java.lang.reflect.Field field = businessDataClass.getDeclaredField(fieldName);
                field.setAccessible(true);
                Object fieldValue = field.get(businessData);
                if (fieldValue != null) {
                    return fieldValue.toString();
                }
            } catch (Exception e) {
                logger.debug("使用字段直接访问获取值失败: field={}, error={}", fieldName, e.getMessage());
            }
            
            // 如果是Map类型，尝试按键获取
            if (businessData instanceof java.util.Map) {
                java.util.Map<?, ?> dataMap = (java.util.Map<?, ?>) businessData;
                Object fieldValue = dataMap.get(fieldName);
                if (fieldValue != null) {
                    return fieldValue.toString();
                }
            }
            
            logger.warn("无法从业务数据中提取字段值: fieldName={}, dataType={}", fieldName, businessDataClass.getSimpleName());
            return null;
            
        } catch (Exception e) {
            logger.error("提取业务字段值时发生异常: fieldName={}, dataType={}", 
                fieldName, businessData.getClass().getSimpleName(), e);
            return null;
        }
    }
    
    /**
     * 从请求头提取参数
     * 
     * @param apiRequest API请求
     * @param headerName 请求头名称
     * @param defaultValue 默认值
     * @return 参数值
     */
    private String extractFromHeader(ApiRequest apiRequest, String headerName, String defaultValue) {
        if (apiRequest.getHeaders() != null) {
            String headerValue = apiRequest.getHeaders().get(headerName);
            if (!StringUtils.isEmpty(headerValue)) {
                return headerValue.trim();
            }
        }
        return defaultValue;
    }
    
    /**
     * 验证键组件字符合法性
     * 
     * @param businessType 业务类型
     * @param companyCode 公司代码
     * @param flowOrderId 流程订单ID
     * @throws IllegalArgumentException 如果包含非法字符
     */
    private void validateKeyComponents(String businessType, String companyCode, String flowOrderId) {
        validateKeyComponent("businessType", businessType);
        validateKeyComponent("companyCode", companyCode);
        validateKeyComponent("flowOrderId", flowOrderId);
    }
    
    /**
     * 验证键组件字符合法性
     * 
     * @param componentName 组件名称
     * @param componentValue 组件值
     * @throws IllegalArgumentException 如果包含非法字符
     */
    private void validateKeyComponent(String componentName, String componentValue) {
        if (StringUtils.isEmpty(componentValue)) {
            throw new IllegalArgumentException(componentName + "不能为空");
        }
        
        if (componentValue.contains(KEY_SEPARATOR)) {
            throw new IllegalArgumentException(
                String.format("%s不能包含分隔符'%s': %s", componentName, KEY_SEPARATOR, componentValue));
        }
        
        // 检查其他可能有问题的字符
        if (componentValue.contains(" ") || componentValue.contains("\t") || 
            componentValue.contains("\n") || componentValue.contains("\r")) {
            throw new IllegalArgumentException(
                String.format("%s不能包含空白字符: %s", componentName, componentValue));
        }
    }
}
