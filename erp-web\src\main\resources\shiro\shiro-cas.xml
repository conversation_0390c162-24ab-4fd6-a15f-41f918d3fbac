<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">
    <description>Shiro单点登录安全配置</description>

    <bean id="casShiroFilter" class="com.vedeng.common.shiro.support.DefaultShiroFilterFactoryBean">
        <!-- 安全管理器 -->
        <property name="securityManager" ref="casSecurityManager"/>
        <!-- 默认的登陆访问url -->
        <property name="loginUrl" value="/login.do"/>
        <property name="successUrl" value="/index.do"/>
        <property name="unauthorizedUrl" value="/nopower.do" />
        <property name="filterChainDefinitions">
            <value>
                /system/message/queryJCONoReadMsgNum.do = anon
                /system/message/getAllMessageNoread.do = anon
                /system/message/queryNoReadMsgNum.do = anon
                /goodsInfo/searchForApi.do = anon
                /api/quoteToOrder/** = anon
                /api/broadCast/** = anon
                /communicateRecord/** = anon
                /api/terminal/** = anon
                /api/message/** = anon
                /api/goods/** = anon
                /api/tyc/** = anon
                /api/userwork/** = anon
                /api/invoice/** = anon
                /order/saleorder/checkAptitudeForYxg.do = anon
                /orderstream/saleorder/contractReturnSaveForOtherErp.do = anon
                /order/buyorder/savebuyorderLogisticsSaveFromOtherErp.do = anon
                /orderstream/saleorder/receiveSaleorderFromOtherErp.do = anon
                /buyorder/bdorder/getBdSaleOrderListByBuyOrderNoForOtherErp.do = anon
                /static/** = anon
                /nopower.do = anon
                /checkpreload.html = anon
                /checkInnerWeb.html = anon
                <!--/login.do = anon-->
                <!--/dologin.do = anon-->
                /system/user/updateIsVedengState.do=anon
                /vgoods/operate/spuSend.do=anon
                /goods/static/vgoods/skuTip.do=anon
                /system/user/doJxAcountData.do=anon
                /trader/customer/getAccountSaler.do=anon
                /trader/customer/tyc/check.do=anon
                /trader/customer/saveMjxContactAdders.do=anon
                /checkSession.do = anon
                /code.do = anon
                /service/model/** = anon
                /services/** = anon
                /tradermsg/sendMsg/sendTraderMsg.do = anon
                /tradermsg/sendMsg/sendWebaccountCertificateMsg.do = anon
                /userCenter/** = anon
                /warehouseOutIn/regenerateWarehouseInReport.do = anon
                /warehouseOutIn/regenerateWarehouseOutReport.do = anon

                /system/call/pushVoiceWxMp3.do=anon

                /order/saleorder/saveBDAddSaleorder.do=anon
                /order/saleorder/saveYgyxBuyorderToErpSaleOrder.do=anon
                /order/saleorder/updateBDSaleStatus.do=anon
                /order/saleorder/deleteBdOrder.do=anon
                /order/afterSalesCommon/saveYxbRecord.do=anon

                /order/saleorder/searchOrderInfo.do=anon
                /order/saleorder/updateOrderDeliveryStatus.do=anon
                /warehouse/warehouses/insertNewStock.do=anon
                /warehouse/warehouses/updateSaleorderOccupyNum.do=anon
                /order/hc/updateorderstatus.do = anon
                /warehouse/warehouses/getErrorStockGoodsList.do = anon
                /warehouse/warehouses/updateWarehouseLogIsUse.do = anon
                /warehouse/warehouses/getLogicalStockInfo.do = anon
                /warehouse/warehouses/initOrderOccupy.do= anon
                /trader/customer/aptitude/status.do = anon
                /trader/customer/yxg/status/sync.do = anon
                /trader/customer/addYxgTrader.do = anon
                /trader/customer/yxgStartFinanceCheck.do = anon
                <!--/warehouse/warehousesout/viewAppliedItems.do = anon
                /warehouse/warehousesout/checkInvoiceParams.do = anon-->
                /aftersales/webaccount/certificate/update.do = anon
                /aftersales/webaccount/certificate/add.do = anon

                /warehouse/warehousesout/getPurchaseTime.do = anon
                /warehouse/warehousesout/getDeliverTime.do = anon
                /warehouse/warehousesout/getInLibraryBarcode.do=anon
                /order/saleorder/getCouponOrderInfoByCouponId.do=anon
                /order/saleorder/getCouponOrderDetailByCouponcode.do=anon
                /order/bussinesschance/getBussinessChanceAndQuoteInfo.do=anon
                /warehouse/warehousesout/printOutOrder.do=anon
                /orderstream/saleorder/compenseOnlineConfirm.do=anon
                /el/** = anon
                /wms/** = anon
                /pay/** = anon
                /aftersale/order/** = anon
                /api/saleOrder/getFlowAmount.do = anon

                <!--/el/** = anon-->
                <!-- /BDSaleorderTask/a.do=anon -->
                /order/saleorder/updateBDSaleStatus.do=anon
                /logistics/warehousein/saveExpress.do=anon
                <!--对外提供接口服务-->
                /mjx/** = anon
                /yxg/** = anon
                /goods/goods/getgoodslistextrainfo.do = anon
                /warehouse/warehousesout/updateWarehouseProblem.do=anon
                /producter/productering.do=anon
                /finance/invoice/selectInvoiceItems.do=anon
                /finance/invoice/viewInvoicedItems.do=anon
                /order/saleorder/queryOutBoundQuantity.do=anon
                /order/saleorder/ceshi.do=anon
                /aftersales/order/invoiceGoodList.do=anon
                /category/base/getCategoryListByParentId.do=anon
                /order/quote/canApply.do=anon
                /order/quote/next.do=anon
                /system/region/getregion.do=anon
                /system/region/static/**=anon

                /goods/vgoods/uplodeSkuDeliveryRange.do=anon
                /goods/vgoods/saveUplodeSkuDeliveryRange.do=anon
                <!--/category/base/getFirstCategoryList.do=anon-->
                /category/base/getSecondCategoryList.do=anon
                /category/base/commitCategory.do=anon
                <!--/category/base/submitCategory.do=anon-->
                /category/base/choiceCategory.do=anon
                <!--二期用到的接口-->
                <!--/category/base/categoryMigretionIndex.do=anon
                /category/base/categoryMigrationExamine.do=anon-->
                /category/base/getAllLevelCategoryByIdList.do=anon

                /firstengage/baseinfo/doRegistrationImg.do=anon
                /firstengage/baseinfo/doRegistnumpdfImg.do=anon
                /firstengage/brand/brandNameForApi.do=anon
                /system/list/** = anon
                /order/quote/consult/** = anon

                /fileUpload/ajaxFileUploadAuthorization.do=anon
                /order/quote/authorizationPreview.do=anon
                /order/quote/authorizationExamine.do=anon

                /order/quote/authorizationExamine.do=anon
                /warehouse/warehouses/getWmsStock.do=anon
                /order/quote/printOrder.do=anon
                /order/quote/printAuthorizationElectronicSign.do=anon

                /businessChance/smartQuoteAdd.do=anon
                /goodsInfo/search.do=anon
                /user/getUserPermissionForSmartQuote.do=anon
                /coreSkuApi/getSkuInfoForSmartQuote.do=anon
                /traderCustomerBase/traderListForSmartQuote.do=anon

                /pay/** = anon
                /finance/invoice/hx_invoice/** = anon
                /order/saleorder/printOrder.do = anon
                /order/saleorder/editApplyValidSaleorder.do=anon
                /order/buyorder/editApplyValidBuyorder.do = anon

                /order/afterSalesCommon/saveFollowUpRecordForYXB.do = anon

                /goods/vgoods/viewSkuBySkuId.do=anon
                /order/saleorder/printOrder.do=anon
                /orderstream/saleorder/contract_template/print.do=anon
                /order/newBuyorder/printBuyOrder.do=anon
                /warehouse/warehousesout/expressInfoFile.do=anon

                /flowOrder/contract/printBuyContract.do=anon
                /flowOrder/contract/printSaleContract.do=anon

                /trader/supplier/syncDocSupplierToFtp.do=anon
                /system/role/static/savefeedback =anon
                /system/region/static/getAllRegion.do=anon
                /system/origanization/static/getAllOriganization.do=anon
                /system/userposit/static/getAllUser.do=anon
                /system/message/** = anon

                /order/aftersales/getEffectAfterSalePolicy.do = anon
                /buyorder/listGoodsOnWayInfo.do = anon
                /trader/supplier/addTraderSupplier.do = anon
                /bankBill/api/alipayReceipt.do=anon
                /api/kingdee/paybill/cancel.do=anon
                /api/kingdee/invoiceVoucher.do=anon
                /api/kingdee/orderPayment.do=anon
                /api/kingdee/pageQueryBuyOrder.do=anon
                /api/kingdee/queryPayBank.do=anon
                /buyorderExpense/printContract.do=anon
                /api/bankBill/updateBankBillReceipt.do=anon
                /logistics/filedeliveryNew/queryPrintUrlByPrintCode.do=anon
                /order/quote/queryForSaleMall.do = anon
                /ezadmin/index.html=anon
                /ezadmin/navs.html=anon
                /ezadmin/api/**=anon
                /ezadmin/clear.html=anon
                /ezadmin/welcome.html=anon
                /sys/navs.do=anon

                /goodsCategory/getAllCategory.do=anon
                /goodsCategory/getCategoryIdByFullPath.do=anon
                /goodsCategory/getFullPathNameById.do=anon

                <!-- ERP API标准化模块 - 所有API接口免鉴权 -->
                /api/** = anon

                /callback = casCallback
                /logout.do = casLogout
                /** = casAuth, customUser
            </value>
        </property>
        <property name="filters">
            <map>
                <entry key="casLogout" value-ref="casLogoutFilter"/>
                <entry key="casCallback" value-ref="casCallbackFilter"/>
                <entry key="casAuth" value-ref="casSecurityFilter"/>
                <entry key="customUser" value-ref="customUserFilter"/>
            </map>
        </property>
    </bean>

     <!--shiro securityManager -->
    <bean id="casSecurityManager" class="com.vedeng.common.shiro.cas.CustomWebSecurityManager">
        <property name="sessionManager" ref="messagingEventSessionManager"/>
        <property name="realm" ref="pac4jRealm"/>
        <property name="cacheManager" ref="cacheManager"/>
        <property name="subjectFactory" ref="pac4jSubjectFactory"/>
    </bean>


    <bean id="coreConfiguration" class="com.vedeng.common.shiro.cas.config.CoreConfiguration"/>

    <bean id="casClientConfiguration" class="com.vedeng.common.shiro.cas.config.CasClientConfiguration"/>

    <bean id="shiroWebConfiguration" class="com.vedeng.common.shiro.cas.config.ShiroWebConfiguration"/>

    <bean id="casClientProperties" class="com.vedeng.common.shiro.cas.properties.CasClientProperties"/>

</beans>