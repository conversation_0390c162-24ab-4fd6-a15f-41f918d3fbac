package com.vedeng.erp.system.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.domain.dto.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface FlowOrderService{

    /**
     * 判断某个采购订单是否在业务流转单中，同时要检查发起业务流转单的这个采购订单，也要检查
     * @param businessNo
     * @return
     */
    String getFlowOrderByBusinessForStandard(String businessNo);

    PageInfo<FlowOrderRespDto> page(PageParam<FlowOrderReqDto> flowOrderReqDto);

    FlowOrderDto init(String baseBusinessNo, Integer baseBusinessType,Integer pushDirection, String sourceErp);

    void add(FlowOrderDto flowOrderDto);

    void update(FlowOrderDto flowOrderDto);
    
    void checkGoodAptitude(FlowOrderDto flowOrderDto);

    void delete(HashSet<Long> ids);

    FlowOrderDto get(Long flowOrderId);

    void audit(Long flowOrderId);

    FlowOrderDto calculateNodeAmount(FlowOrderDto flowOrderDto);

    CornerNumDto cornerNum(PageParam<FlowOrderReqDto> flowOrderDto);

    List<FlowOrderBuySaleOrderDto> getBuySaleOrderInfo(Long flowOrderId,Integer baseBusinessType);

    /**
     * 上传流转单合同文件
     * @param file 合同文件
     * @param flowOrderId 流转单ID
     * @param nodeLevel 节点级别
     * @param flowOrderInfoType 流转单信息类型（0:采购，1:销售）
     * @return 合同文件URL
     */
    String uploadContract(HttpServletRequest request, MultipartFile file, Long flowOrderId, Integer nodeLevel, Integer flowOrderInfoType);
}
