package com.vedeng.erp.trader.web.api;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc;
import com.vedeng.infrastructure.tyc.domain.dto.TycResultDto;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.vedeng.common.core.base.ResultInfo;
import com.vedeng.infrastructure.tyc.service.TycSearchService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/17
 */
@ExceptionController
@RestController
@RequestMapping("/api/tyc")
public class TycApi {

    public static Logger logger = LoggerFactory.getLogger(TycApi.class);

    @Autowired
    private TycSearchService tycSearchService;

    /**
     * 查询ERP本地的天眼查信息
     * @param names   XX公司,XX有限公司,XX技术有限公司 多个公司名称以逗号分割
     * @return
     */
    @RequestMapping("/locallist")
    public ResultInfo<List<TraderInfoTyc>> locallist(String names) {
        List<TraderInfoTyc> listInfo = tycSearchService.searchLocalByNames(names);
        return ResultInfo.success(listInfo);
    }

    /**
     * 直接查询天眼查列表接口
     * @param name
     * @return
     */
    @RequestMapping("/list")
    public ResultInfo<PageInfo<TycResultDto>> list(String name) {
        PageInfo<TycResultDto> pageInfo = tycSearchService.searchByTerminalName(name);
        return ResultInfo.success(pageInfo);
    }


    /**
     * 优先查本地，本地无数据或数据超15天过期时，再实时查天眼查
     * @param name
     * @return
     */
    @RequestMapping("/detail")
    public ResultInfo<TraderInfoTyc> detail(String name) {
        logger.info("detail调用天眼查API，公司名称：{}", name);
        TraderInfoTyc infoTyc = tycSearchService.getTycInfo(name);
        return ResultInfo.success(infoTyc);
    }

}