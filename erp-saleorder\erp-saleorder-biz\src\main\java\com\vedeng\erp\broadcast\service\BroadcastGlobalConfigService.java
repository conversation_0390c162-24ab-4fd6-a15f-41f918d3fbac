package com.vedeng.erp.broadcast.service;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.broadcast.domain.dto.BroadcastGlobalConfigDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastRelationConfigDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastGlobalConfigEntity;

/**
 * 全局播报配置服务接口
 */
public interface BroadcastGlobalConfigService {
    /**
     * 获取全局播报配置
     *
     * @return 全局播报配置实体
     */
    BroadcastGlobalConfigDto getGlobalConfig();
    /**
     * 更新全局播报配置
     *
     * @param configEntity 配置实体
     * @return 更新后的配置实体
     */
    BroadcastGlobalConfigEntity updateGlobalConfig(BroadcastGlobalConfigDto configEntity, CurrentUser user);
}
