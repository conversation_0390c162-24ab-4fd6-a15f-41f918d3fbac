package com.vedeng.erp.common.broadcast.statistics;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 播报项目获取的统计结果
 * @ClassName:  StatisticsDto   
 * @author: <PERSON><PERSON>yang
 * @date:   2025年6月9日 下午4:48:44    
 * @Copyright:
 */
@Data
public class StatisticsDto {
	
	/**erp组织ID*/
	private Integer orgId;
	
	/**出库量和出库金额中，未限定组织orgId，故返回的组织ID为一个列表*/
	private List<Integer> orgIdList;
	
	/**erp用户ID*/
	private Integer userId;
	
	/**erp用户名称*/
	private String userName;
	
	/**统计的到款金额、统计的出库金额*/
	private BigDecimal totalAmount;
	
	/**达成率*/
	private Integer achievedNum;
	
	/**统计的总出库量*/
	private Integer totalNum;
	
	/**名次*/
	private Integer topNum;
	
	/**小组名称*/
	private String teamName;
	
	/**小组ID*/
	private Integer teamId;
	
	/**部门名称*/
	private String deptName;
	
	/**部门ID*/
	private Integer deptId;
	

}
