.setting-form-wrap {
  min-width: 1240px;
  max-width: 1600px;
  margin: 0 auto;
  padding: 20px;
  padding-bottom: 64px;
}
.setting-form-wrap .setting-form-block {
  padding: 20px;
  background: #fff;
  margin-bottom: 20px;
}
.setting-form-wrap .setting-form-block .setting-form-block-title {
  font-weight: 700;
  margin-bottom: 15px;
}
.setting-form-wrap .setting-form-block .form-item .vd-ui-select {
  width: 100%;
}
.setting-form-wrap .setting-form-block .form-item .form-label {
  width: 150px;
}
.setting-form-wrap .setting-form-block .top-broad-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}
.setting-form-wrap .setting-form-block .top-broad-item:first-child {
  padding-top: 6px;
}
.setting-form-wrap .setting-form-block .top-broad-item .vd-ui-input {
  width: 60px !important;
  text-align: center;
  margin: 0 10px;
  margin-top: -5px;
}
.setting-form-wrap .project-table .table-item {
  display: flex;
}
.setting-form-wrap .project-table .table-item .table-th {
  background: #f5f7fa;
  padding: 7px 10px;
  border: 1px solid #BABFC2;
  margin-top: -1px;
  display: flex;
  align-items: center;
}
.setting-form-wrap .project-table .table-item .table-td {
  flex: 1;
  padding: 7px 10px;
  border: 1px solid #BABFC2;
  margin-left: -1px;
  margin-top: -1px;
  text-align: center;
}
.setting-form-wrap .project-table .table-item .table-td:nth-child(2) {
  flex: 1.3;
}
.setting-form-wrap .project-table .table-item .table-td .vd-ui-input {
  margin: -3px 0;
}
.setting-form-wrap .project-table .table-item .table-td .vd-ui-input .vd-ui-input__inner {
  height: 28px;
}
.setting-form-wrap .target-table-wrap {
  overflow: auto;
}
.setting-form-wrap .target-table-wrap::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.setting-form-wrap .target-table-wrap::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.setting-form-wrap .target-table-wrap::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.setting-form-wrap .target-table-wrap::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.setting-form-wrap .target-table-wrap::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.setting-form-wrap .target-table {
  min-width: 1315px;
}
.setting-form-wrap .target-table .table-tr .table-th,
.setting-form-wrap .target-table .table-tr .table-td {
  text-align: center;
}
.setting-form-wrap .target-table .table-tr .table-th:nth-child(1),
.setting-form-wrap .target-table .table-tr .table-td:nth-child(1) {
  width: 150px;
}
.setting-form-wrap .target-table .table-tr .table-th:nth-child(2),
.setting-form-wrap .target-table .table-tr .table-td:nth-child(2) {
  width: 765px;
}
.setting-form-wrap .target-table .table-tr .table-th:nth-child(3),
.setting-form-wrap .target-table .table-tr .table-td:nth-child(3) {
  width: 210px;
}
.setting-form-wrap .target-table .table-tr .table-th:nth-child(4),
.setting-form-wrap .target-table .table-tr .table-td:nth-child(4) {
  min-width: 180px;
  flex: 1;
}
.setting-form-wrap .depart-base-table .table-th,
.setting-form-wrap .depart-base-table .table-td {
  text-align: center;
}
.setting-form-wrap .depart-base-table .table-th:nth-child(1),
.setting-form-wrap .depart-base-table .table-td:nth-child(1) {
  width: 120px;
}
.setting-form-wrap .depart-base-table .table-th:nth-child(2),
.setting-form-wrap .depart-base-table .table-td:nth-child(2) {
  flex: 1;
}
.setting-form-wrap .depart-base-table .table-th:nth-child(3),
.setting-form-wrap .depart-base-table .table-td:nth-child(3) {
  flex: 1;
}
.setting-form-wrap .depart-base-table .table-th:nth-child(4),
.setting-form-wrap .depart-base-table .table-td:nth-child(4) {
  flex: 1;
}
.setting-form-wrap .add-wrap {
  display: flex;
  margin-bottom: 10px;
}
.setting-form-wrap .add-wrap .add-btn {
  color: #09f;
  cursor: pointer;
  margin-right: 10px;
}
.setting-form-wrap .add-wrap .add-btn:hover {
  color: #f60;
}
.setting-form-wrap .add-wrap .add-btn-tip {
  color: #999;
}
.setting-form-wrap .depart-custom-table .table-th,
.setting-form-wrap .depart-custom-table .table-td {
  text-align: center;
}
.setting-form-wrap .depart-custom-table .table-th:nth-child(1),
.setting-form-wrap .depart-custom-table .table-td:nth-child(1) {
  width: 220px;
}
.setting-form-wrap .depart-custom-table .table-th:nth-child(2),
.setting-form-wrap .depart-custom-table .table-td:nth-child(2) {
  flex: 1;
}
.setting-form-wrap .depart-custom-table .table-th:nth-child(3),
.setting-form-wrap .depart-custom-table .table-td:nth-child(3) {
  flex: 1;
}
.setting-form-wrap .depart-custom-table .table-th:nth-child(4),
.setting-form-wrap .depart-custom-table .table-td:nth-child(4) {
  width: 100px;
}
.setting-form-wrap .depart-custom-table .table-th .link-btn,
.setting-form-wrap .depart-custom-table .table-td .link-btn {
  color: #09f;
  cursor: pointer;
}
.setting-form-wrap .depart-custom-table .table-th .link-btn.warn,
.setting-form-wrap .depart-custom-table .table-td .link-btn.warn {
  color: #e64545;
}
.setting-form-wrap .depart-custom-table .table-th .link-btn:hover,
.setting-form-wrap .depart-custom-table .table-td .link-btn:hover {
  color: #f60;
}
.setting-form-wrap .setting-form-tip {
  color: #999;
  margin-bottom: 10px;
}
.form-footer-wrap {
  text-align: center;
  background: #fff;
  padding: 15px;
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 5;
  border-top: 1px solid #BABFC2;
}
.form-table .table-tr {
  display: flex;
}
.form-table .table-tr .table-th,
.form-table .table-tr .table-td {
  margin-left: -1px;
  border: 1px solid #BABFC2;
  padding: 7px 10px;
}
.form-table .table-tr .table-th:first-child,
.form-table .table-tr .table-td:first-child {
  margin-left: 0;
}
.form-table .table-tr .table-th {
  background: #f5f7fa;
}
.form-table .table-tr .table-th .must {
  color: #e64545;
}
.form-table .table-tr .table-td {
  margin-top: -1px;
}
.form-table .table-tr .table-td > .vd-ui-input {
  margin: -2px 0;
}
.form-table .table-tr .table-td > .vd-ui-input .vd-ui-input__inner {
  height: 28px;
}
.form-table .table-tr .table-td.select-wrap {
  padding: 4px 10px;
}
.form-table .table-tr .table-td.select-wrap .vd-ui-select {
  width: 100%;
}
.form-table .table-tr .table-td.select-wrap .vd-ui-select .vd-ui-input__inner {
  height: 28px;
}
.form-table .table-tr .table-td.select-wrap .vd-ui-select .icon {
  top: 6px;
}
.form-table .table-empty {
  border: 1px solid #BABFC2;
  padding: 60px;
  text-align: center;
  margin-top: -1px;
  color: #999;
}
.form-error-wrap {
  color: #e64545;
  margin-top: 5px;
}
