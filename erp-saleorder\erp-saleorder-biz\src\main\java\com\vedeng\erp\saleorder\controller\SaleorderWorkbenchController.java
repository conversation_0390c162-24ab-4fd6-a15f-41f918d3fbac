package com.vedeng.erp.saleorder.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.erp.business.dto.NodeDto;
import com.vedeng.erp.saleorder.feign.OneDataSaleUserFeignApi;
import com.vedeng.erp.saleorder.model.dto.UnCollectedOrderInfoDto;
import com.vedeng.erp.saleorder.model.dto.WorkbenchDataDto;
import com.vedeng.erp.saleorder.model.query.WorkbenchDto;
import com.vedeng.erp.saleorder.service.SaleorderWorkbenchService;
import com.vedeng.erp.saleorder.vo.WorkbenchBusinessAnalysisVO;
import com.vedeng.erp.trader.service.BusinessSupportApiService;
import com.vedeng.flash.dto.ReviewQueryDto;
import com.vedeng.flash.dto.vo.ReviewAndMessageVO;
import com.vedeng.onedataapi.api.profit.req.SaleUserGrossProfitReqDto;
import com.vedeng.onedataapi.api.profit.res.GrossProfitData;
import com.vedeng.onedataapi.api.trader.res.TraderPageRes;
import com.vedeng.order.model.dto.SaleorderUserInfoDto;
import com.vedeng.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.solr.common.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping("orderstream/saleorder")
public class SaleorderWorkbenchController {

    @Autowired
    UserService userService;

    @Autowired
    @Qualifier("saleorderWorkbenchService")
    SaleorderWorkbenchService workbenchService;

    @Value("${sales.workbench.staff.url}")
    private String salesWorkbenchStaffUrl;
    @Value("${lxcrmUrl}")
    private String lxcrmUrl;
    
    @Autowired
    private BusinessSupportApiService businessSupportApiService;
    @Autowired
    private OneDataSaleUserFeignApi oneDataSaleUserFeignApi;
    @ResponseBody
    @RequestMapping(value = "/getWorkbenchBusinessAnalysis")
    @NoNeedAccessAuthorization
    public ResultInfo<WorkbenchBusinessAnalysisVO> getWorkbenchBusinessAnalysis(HttpServletRequest request,
                                                                                @RequestBody  WorkbenchDto  workbenchDto) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        if(user == null){
            return ResultInfo.error("未登录");
        }
        WorkbenchBusinessAnalysisVO result = workbenchService.getWorkbenchBusinessAnalysis(workbenchDto);
        return ResultInfo.success(result);
    }
    @ResponseBody
    @RequestMapping(value = "/getWorkbenchBusinessAnalysisMaoli")
    @NoNeedAccessAuthorization
    public ResultInfo<String> getWorkbenchBusinessAnalysisMaoli(HttpServletRequest request,
                                                                                @RequestBody  WorkbenchDto  workbenchDto) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        if(user == null){
            return ResultInfo.error("未登录");
        }
            SaleUserGrossProfitReqDto reqDto = new SaleUserGrossProfitReqDto();
            if(workbenchDto.getMonth()!=null&&workbenchDto.getMonth()>0){
                String monthStr = String.format("%02d", workbenchDto.getMonth());
                reqDto.setStartMonth(workbenchDto.getYear()+"-"+monthStr);
                reqDto.setEndMonth(workbenchDto.getYear()+"-"+monthStr);
            }else{
                //全年
                reqDto.setStartMonth(workbenchDto.getYear()+"-01" );
                reqDto.setEndMonth(workbenchDto.getYear()+"-12");
            }
            reqDto.setSaleUserIdList(workbenchDto.getUserIds());
            //把所有人的 金额加起来
        try {
            RestfulResult<List<GrossProfitData>> result = oneDataSaleUserFeignApi.batchSaleUserGrossProfitByDateRange(reqDto);
            if (result != null && result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {

                BigDecimal totalGrossProfitAmount=BigDecimal.ZERO;
                BigDecimal totalSaleAmount=BigDecimal.ZERO;
                for (int i = 0; i < result.getData().size(); i++) {
                    GrossProfitData item=result.getData().get(i);
                    if(org.apache.commons.lang3.StringUtils.isNotBlank(item.getGrossProfitAmount())
                            &&org.apache.commons.lang3.StringUtils.isNotBlank(item.getTotalSaleAmount())
                    ){
                        try {
                            totalGrossProfitAmount = totalGrossProfitAmount.add(new BigDecimal(item.getGrossProfitAmount()));
                            totalSaleAmount = totalSaleAmount.add(new BigDecimal(item.getTotalSaleAmount()));
                        }catch (Exception e){
                            log.warn("工作台计算毛利转换异常{} {} {}",JSON.toJSONString(workbenchDto), JSON.toJSONString(item));
                        }
                    }
                }
                if(totalSaleAmount.compareTo(BigDecimal.ZERO)>0){
                    BigDecimal grossProfitRate = totalGrossProfitAmount.divide(totalSaleAmount, 4, BigDecimal.ROUND_HALF_UP);
                    return ResultInfo.success(grossProfitRate.multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
                }
            }
        }catch (Exception e){
           log.error("工作台计算毛利异常：{}", JSON.toJSONString(workbenchDto),e);
        }
        return ResultInfo.success("0%");
    }

    @ResponseBody
    @RequestMapping(value = "/getCrmTaskGroupCount")
    @NoNeedAccessAuthorization
    public ResultInfo<List<com.vedeng.order.model.vo.TaskGroupVo>> getCrmTaskGroupCount(HttpServletRequest request,@RequestParam(name = "startDate") String startDate,@RequestParam(name = "endDate") String endDate) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        if(user == null){
            return ResultInfo.error("未登录");
        }
        List<com.vedeng.order.model.vo.TaskGroupVo> list = workbenchService.getCrmTaskGroupCount(user.getUserId(),startDate,endDate);

        return ResultInfo.success(list);
    }

    @ResponseBody
    @RequestMapping(value = "/getCrmTaskForOneDay")
    @NoNeedAccessAuthorization
    public ResultInfo<List<com.vedeng.order.model.vo.TaskDetailVo>> getCrmTaskForOneDay(HttpServletRequest request,@RequestParam(name = "dateStr") String dateStr) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        if(user == null){
            return ResultInfo.error("未登录");
        }
        List<com.vedeng.order.model.vo.TaskDetailVo> list = workbenchService.getCrmTaskForOneDay(user.getUserId(),dateStr);

        return ResultInfo.success(list);
    }
    /**
     * 销售工作台主页面
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/12/15 10:23.
     * @author: Randy.Xu.
     */
    @ResponseBody
    @RequestMapping(value = "/workbench")
    @NoNeedAccessAuthorization
    public ModelAndView saleorderWorkbenchPage(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView();
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        //不同视角员工和管理
        SaleorderUserInfoDto userInfoDto = null;
        try {
            userInfoDto = userService.getPositionAndSubUser(user);
        } catch (Exception e) {
           log.error("获取用户信息异常：e:{}",e);
        }
        WorkbenchDataDto workbenchDataDto = null;
        try {
            workbenchDataDto = workbenchService.getOverviewInfoDate(userInfoDto);
        } catch (Exception e) {
            log.warn("获取workbenchDataDto异常：e:{}",e);
        }

        mv.addObject("workbenchData",workbenchDataDto);
        mv.addObject("user",userInfoDto);

        Map ezAdminListUrlDtoMap = (Map)JSONObject.parse(salesWorkbenchStaffUrl);


        //待审核订单
        mv.addObject("saleNoVerifyOrder",ezAdminListUrlDtoMap.get("待审核订单"));

        //未收款/部分收款订单
        mv.addObject("saleNoPayment",ezAdminListUrlDtoMap.get("未收款"));

        //未开票订单
        mv.addObject("saleNoInvoice",ezAdminListUrlDtoMap.get("未开票订单"));

        //未回款订单
        mv.addObject("saleNoMoney",ezAdminListUrlDtoMap.get("未回款订单"));

        //审核中订单
        mv.addObject("saleVerifying",ezAdminListUrlDtoMap.get("审核中订单"));

        //未采购/部分采购订单
        mv.addObject("saleNoPurchase",ezAdminListUrlDtoMap.get("未采购"));

        //未发货/部分发货
        mv.addObject("saleNoSendGoods",ezAdminListUrlDtoMap.get("未发货"));

        //逾期交付
        mv.addObject("saleNoNoSend",ezAdminListUrlDtoMap.get("逾期交付"));

        //未完结售后单
        mv.addObject("saleNoCompletedAfterSale",ezAdminListUrlDtoMap.get("未完结售后单"));

        //未完结售后单
        mv.addObject("saleorderList",ezAdminListUrlDtoMap.get("销售订单列表"));

        // crm任务
        Integer crmTaskCount = workbenchService.getCrmTaskCount(user.getUserId());
        mv.addObject("crmTaskCount", crmTaskCount);
        mv.addObject("crmTaskUrl", lxcrmUrl + "/crm/task/profile/index?list=2");
        Integer crmTodoTaskCount = workbenchService.getCrmTodoTaskCount(user.getUserId());
        mv.addObject("crmTodoTaskCount", crmTodoTaskCount);
        mv.addObject("crmTodoTaskUrl", lxcrmUrl + "/crm/task/profile/index?list=1");

        List<NodeDto> supportList = businessSupportApiService.getSupportList();
        List<Integer> allIds = getAllIds(supportList);
        boolean contains = allIds.contains(user.getUserId());

        mv.addObject("businessSupportFlag", contains);

        mv.setViewName("orderstream/saleorder/workbench_staff");
        return mv;
    }

    public static List<Integer> getAllIds(List<NodeDto> nodeList) {
        List<Integer> ids = new ArrayList<>();
        if (Objects.nonNull(nodeList)) {
            for (NodeDto node : nodeList) {
                ids.add(node.getId());
                List<Integer> childIds = getAllIds(node.getChildren());
                ids.addAll(childIds);
            }
        }
        return ids;
    }


    /**
     * .销售工作台审批
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/12/15 10:22.
     * @author: Randy.Xu.
     */
    @ResponseBody
    @RequestMapping(value = "/reviewAndMessagePage")
    @NoNeedAccessAuthorization
    public ModelAndView reviewAndMessagePage(ReviewQueryDto reviewQueryDto, HttpServletRequest request, HttpSession session,
                                             @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                             @RequestParam(required = false) Integer pageSize){
        ModelAndView mav = new ModelAndView();
        String path = request.getRequestURL().toString();
        path = path + "?";

        if (reviewQueryDto.getSubmitEndTime() != null && !"".equals(reviewQueryDto.getSubmitEndTime())) {
            path = path + "submitEndTime=" + reviewQueryDto.getSubmitEndTime() + "&";
        }
        if (reviewQueryDto.getSubmitStartTime() != null && !"".equals(reviewQueryDto.getSubmitStartTime())) {
            path = path + "submitStartTime=" + reviewQueryDto.getSubmitStartTime() + "&";
        }
        if (reviewQueryDto.getTabStatus() != null) {
            path = path + "tabStatus=" + reviewQueryDto.getTabStatus() + "&";
        }
        path = path.substring(0,path.length()-1);
        User currentUser = (User) session.getAttribute(ErpConst.CURR_USER);

        mav.setViewName("orderstream/saleorder/review_page");
        List<ReviewAndMessageVO> reviewAndMessageVOList = workbenchService.getApprovalDataInfo(reviewQueryDto,currentUser);
        if (CollectionUtils.isEmpty(reviewAndMessageVOList)) {
            mav.addObject("review", reviewQueryDto);
            return mav;
        }
        reviewAndMessageVOList = reviewAndMessageVOList.stream().sorted(Comparator.comparing(ReviewAndMessageVO::getSubmitTimeSort, Comparator.nullsFirst(Long::compareTo)).reversed()).collect(Collectors.toList());
        // 分页对象
        Page page = Page.newBuilder(pageNo, pageSize, path);
        int dataTotal = reviewAndMessageVOList.size();
        page.setTotalRecord(dataTotal);
        page.setTotalPage((int) Math.ceil(((double) dataTotal) / page.getPageSize()));
        Integer start = (page.getPageNo() - 1) * page.getPageSize();
        reviewAndMessageVOList = reviewAndMessageVOList.subList(start, (start + page.getPageSize()) > dataTotal ? dataTotal : (start + page.getPageSize()));
        // 根据批量实例ID查找

        mav.addObject("reviewList", reviewAndMessageVOList);
        mav.addObject("review", reviewQueryDto);
        mav.addObject("page", page);
        return mav;
    }

    private List<Integer> getUserSubListIds(HttpServletRequest request){
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        return user.getSubUserIdList();
    }



    /**
     *当月毛利-从大数据取值
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getMaoli")
    @NoNeedAccessAuthorization
    public ResultInfo getMaoli(HttpServletRequest request){
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        if(user == null){
            return ResultInfo.error("未登录");
        }
        try{
            YearMonth currentYearMonth = YearMonth.now(); // 获取当前年月
            int year = currentYearMonth.getYear(); // 获取年份
            int month = currentYearMonth.getMonthValue(); // 获取月份
            String monthStr = String.format("%02d", month);//如果月份是单数，则在前面补0
            RestfulResult<GrossProfitData> result = oneDataSaleUserFeignApi.detailSaleUserMonthlyGrossProfit(user.getUserId(),year+"",monthStr);
            if(result.isSuccess()){
                return ResultInfo.success(result.getData());
            }
            return ResultInfo.error("查询销售毛利数据失败2");
        }catch (Exception e){
            return ResultInfo.error("查询销售毛利数据失败1");
        }
    }



    /**
     *当月毛利-从大数据取值
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getMaoliList")
    @NoNeedAccessAuthorization
    public ResultInfo<List<GrossProfitData>> getMaoliList(HttpServletRequest request){
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        if(user == null){
            return ResultInfo.error("未登录");
        }
        SaleorderUserInfoDto userInfoDto = null;
        try {
            userInfoDto = userService.getPositionAndSubUser(user);
        } catch (Exception e) {
            log.error("获取用户信息异常：e:{}",e);
        }
        try{
            List<Integer> userIdList = userInfoDto.getSubUserIdList();//取所有的下属，该方法至少返回自己这一个人
            if(CollectionUtils.size(userIdList)<=1){//没有下属也进入了此接口，不需要查大数据，直接返回，
                return ResultInfo.success(new ArrayList<>());
            }
            YearMonth currentYearMonth = YearMonth.now(); // 获取当前年月
            int year = currentYearMonth.getYear(); // 获取年份
            int month = currentYearMonth.getMonthValue(); // 获取月份
            String monthStr = String.format("%02d", month);//如果月份是单数，则在前面补0
            List<Integer> queryList = new ArrayList<>(userIdList);
            //Collections.copy(userIdList,queryList);
//            if(queryList.contains(user.getUserId())){
//                queryList.remove(user.getUserId());
//            }
            SaleUserGrossProfitReqDto reqDto = new SaleUserGrossProfitReqDto();
            reqDto.setGrossProfitMonth(year+"-"+monthStr);
            reqDto.setSaleUserIdList(queryList);
            RestfulResult<List<GrossProfitData>> result = oneDataSaleUserFeignApi.batchSaleUserMonthlyGrossProfit(reqDto);
            if(result.isSuccess()){
                List<GrossProfitData> resultList = result.getData();
                List<Integer> saleUserIds = resultList.stream()
                        .map(GrossProfitData::getSaleUserId)
                        .collect(Collectors.toList());
                queryList.removeAll(saleUserIds);//剩下的即为未查询到数据的对象，例如刚入职的人
                //resultList将会按照grossProfitRate从小到大排序，且所有grossProfitRate为空的GrossProfitData对象都会被放到列表的最后面
                Collections.sort(resultList, new Comparator<GrossProfitData>() {
                    @Override
                    public int compare(GrossProfitData o1, GrossProfitData o2) {
                        String rate1 = o1.getGrossProfitRate();
                        String rate2 = o2.getGrossProfitRate();
                        if (StringUtils.isEmpty(rate1)) {
                            return 1; // o1 is greater if its rate is null/empty
                        }
                        if (StringUtils.isEmpty(rate2)) {
                            return -1; // o2 is greater if its rate is null/empty
                        }
                        // Both rates are not null/empty, compare as Double
                        Double d1 = Double.valueOf(rate1.replace("%", ""));
                        Double d2 = Double.valueOf(rate2.replace("%", ""));
                        return d1.compareTo(d2);
                    }
                });
                if(CollectionUtils.isNotEmpty(queryList)){
                    for(Integer userIdTemp:queryList){
                        User userTemp = userService.getUserById(userIdTemp);
                        if(userTemp!= null){
                            GrossProfitData grossProfitData = new GrossProfitData();
                            grossProfitData.setSaleUserId(userIdTemp);
                            grossProfitData.setSaleUserName(userTemp.getUsername());
                            grossProfitData.setGrossProfitRate("");
                            resultList.add(grossProfitData);
                        }
                    }
                }


                return ResultInfo.success(resultList);
            }
            return ResultInfo.error("查询销售毛利数据失败2");
        }catch (Exception e){
            return ResultInfo.error("查询销售毛利数据失败1");
        }
    }


    /**
     *待办事项-待审核订单
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getNoVerifyOrderInfo")
    @NoNeedAccessAuthorization
    public ResultInfo getNoVerifyOrderInfo(HttpServletRequest request){
        return ResultInfo.success(workbenchService.getNoVerifyOrderInfo(getUserSubListIds(request)));
    }

    /**
     *待办事项-未处理商机
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getUnHandleBusinessChance")
    @NoNeedAccessAuthorization
    public ResultInfo getUnHandleBusinessChance(HttpServletRequest request){
        return ResultInfo.success(workbenchService.getUnHandleBusinessChance(getUserSubListIds(request)));
    }

    /**
     * 待办事项-未收款/部分收款订单
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getNoPaymentOrderInfo")
    @NoNeedAccessAuthorization
    public ResultInfo getNoPaymentOrderInfo(HttpServletRequest request){
        return ResultInfo.success(workbenchService.getNoPaymentOrderInfo(getUserSubListIds(request)));
    }

    /**
     * 待办事项-未开票订单
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getNoInvoiceOrderInfo")
    @NoNeedAccessAuthorization
    public ResultInfo getNoInvoiceOrderInfo(HttpServletRequest request){
        return ResultInfo.success(workbenchService.getNoInvoiceOrderInfo(getUserSubListIds(request)));
    }

    /**
     * 待办事项-待合同回传订单
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getNoContractOrderInfo")
    @NoNeedAccessAuthorization
    public ResultInfo getNoContractOrderInfo(HttpServletRequest request){
        return ResultInfo.success(workbenchService.getNoContractOrderInfo((User) request.getSession().getAttribute(ErpConst.CURR_USER)));
    }

    /**
     * 待跟踪事项-审核中订单
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getVerifyingOrderInfo")
    @NoNeedAccessAuthorization
    public ResultInfo getVerifyingOrderInfo(HttpServletRequest request){
        return ResultInfo.success(workbenchService.getVerifyingOrderInfo(getUserSubListIds(request)));
    }

    /**
     * 待跟踪事项-未采购/部分采购订单
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getNoPurchaseOrderInfo")
    @NoNeedAccessAuthorization
    public ResultInfo getNoPurchaseOrderInfo(HttpServletRequest request){
        return ResultInfo.success(workbenchService.getNoPurchaseOrderInfo(getUserSubListIds(request)));
    }

    /**
     * 待跟踪事项-未发货/部分发货订单
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getNoSendGoodsOrderInfo")
    @NoNeedAccessAuthorization
    public ResultInfo getNoSendGoodsOrderInfo(HttpServletRequest request){
        return ResultInfo.success(workbenchService.getNoSendGoodsOrderInfo(getUserSubListIds(request)));
    }

    /**
     * 待跟踪事项-未完结售后单
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getNoCompletedAfterSaleInfo")
    @NoNeedAccessAuthorization
    public ResultInfo getNoCompletedAfterSaleInfo(HttpServletRequest request){
        return ResultInfo.success(workbenchService.getNoCompletedAfterSaleInfo(getUserSubListIds(request)));
    }

    /**
     * 获取未回款订单数据
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/uncollectedOrders")
    @NoNeedAccessAuthorization
    public ResultInfo<UnCollectedOrderInfoDto> getUncollectedOrderInfo(HttpServletRequest request) {
        List<Integer> userIdList = getUserSubListIds(request);
        return ResultInfo.success(workbenchService.getUnCollectedOrderInfo(userIdList));
    }

    /**
     * 新业绩考核待办
     *
     * @param request HttpServletRequest
     * @return ResultInfo
     */
    @ResponseBody
    @RequestMapping(value = "/getPerformanceEvaluationInfo")
    @NoNeedAccessAuthorization
    public ResultInfo getPerformanceEvaluationInfo(HttpServletRequest request) {
        List<Integer> userIdList = getUserSubListIds(request);
        return ResultInfo.success(workbenchService.getPerformanceEvaluationInfo(userIdList));
    }

    /**
     * 销售工作台-商机支持
     */
    @ResponseBody
    @RequestMapping(value = "/workbench/businessSupport")
    @NoNeedAccessAuthorization
    public ModelAndView businessSupport(){
        return new ModelAndView("vue/view/businesschance/businessSupport");
    }

}
