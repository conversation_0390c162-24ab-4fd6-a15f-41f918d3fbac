<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.api.standard.duplicate.mapper.IdempotencyRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.vedeng.api.standard.duplicate.entity.IdempotencyRecord">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="IDEMPOTENCY_KEY" property="idempotencyKey" jdbcType="VARCHAR"/>
        <result column="FLOW_ORDER_ID" property="flowOrderId" jdbcType="VARCHAR"/>
        <result column="COMPANY_CODE" property="companyCode" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="BUSINESS_CONTENT_HASH" property="businessContentHash" jdbcType="VARCHAR"/>
        <result column="REQUEST_DATA" property="requestData" jdbcType="LONGVARCHAR"/>
        <result column="RESPONSE_DATA" property="responseData" jdbcType="LONGVARCHAR"/>
        <result column="STATUS" property="status" jdbcType="TINYINT"/>
        <result column="BUSINESS_DOCUMENT_ID" property="businessDocumentId" jdbcType="VARCHAR"/>
        <result column="BUSINESS_DOCUMENT_NO" property="businessDocumentNo" jdbcType="VARCHAR"/>
        <result column="EXTRA_INFO" property="extraInfo" jdbcType="LONGVARCHAR"/>
        <result column="IS_DELETED" property="isDeleted" jdbcType="TINYINT"/>
        <result column="CREATED_TIME" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_TIME" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, IDEMPOTENCY_KEY, FLOW_ORDER_ID, COMPANY_CODE, BUSINESS_TYPE, BUSINESS_CONTENT_HASH,
        REQUEST_DATA, RESPONSE_DATA, STATUS, BUSINESS_DOCUMENT_ID, BUSINESS_DOCUMENT_NO,
        EXTRA_INFO, IS_DELETED, CREATED_TIME, UPDATED_TIME
    </sql>

    <!-- 根据幂等性键查询记录 -->
    <select id="selectByIdempotencyKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_IDEMPOTENCY_RECORD
        WHERE IDEMPOTENCY_KEY = #{idempotencyKey}
        AND IS_DELETED = 0
    </select>

    <!-- 插入幂等性记录 -->
    <insert id="insert" parameterType="com.vedeng.api.standard.duplicate.entity.IdempotencyRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO T_IDEMPOTENCY_RECORD (
            IDEMPOTENCY_KEY, FLOW_ORDER_ID, COMPANY_CODE, BUSINESS_TYPE, BUSINESS_CONTENT_HASH,
            REQUEST_DATA, RESPONSE_DATA, STATUS, BUSINESS_DOCUMENT_ID, BUSINESS_DOCUMENT_NO,
            EXTRA_INFO, IS_DELETED, CREATED_TIME, UPDATED_TIME
        ) VALUES (
            #{idempotencyKey}, #{flowOrderId}, #{companyCode}, #{businessType}, #{businessContentHash},
            #{requestData}, #{responseData}, #{status}, #{businessDocumentId}, #{businessDocumentNo},
            #{extraInfo}, #{isDeleted}, #{createdTime}, #{updatedTime}
        )
    </insert>

    <!-- 根据幂等性键更新记录 -->
    <update id="updateByIdempotencyKey" parameterType="com.vedeng.api.standard.duplicate.entity.IdempotencyRecord">
        UPDATE T_IDEMPOTENCY_RECORD
        <set>
            <if test="status != null">STATUS = #{status},</if>
            <if test="responseData != null">RESPONSE_DATA = #{responseData},</if>
            <if test="businessDocumentId != null">BUSINESS_DOCUMENT_ID = #{businessDocumentId},</if>
            <if test="businessDocumentNo != null">BUSINESS_DOCUMENT_NO = #{businessDocumentNo},</if>
            <if test="extraInfo != null">EXTRA_INFO = #{extraInfo},</if>
            <if test="isDeleted != null">IS_DELETED = #{isDeleted},</if>
            UPDATED_TIME = NOW()
        </set>
        WHERE IDEMPOTENCY_KEY = #{idempotencyKey}
        AND IS_DELETED = 0
    </update>

    <!-- 根据主键删除记录 -->
    <delete id="deleteById">
        DELETE FROM T_IDEMPOTENCY_RECORD WHERE ID = #{id}
    </delete>

    <!-- 根据幂等性键删除记录 -->
    <delete id="deleteByIdempotencyKey">
        DELETE FROM T_IDEMPOTENCY_RECORD WHERE IDEMPOTENCY_KEY = #{idempotencyKey}
    </delete>
    
    <!-- 根据幂等性键逻辑删除记录 -->
    <update id="logicalDeleteByIdempotencyKey">
        UPDATE T_IDEMPOTENCY_RECORD 
        SET IS_DELETED = 1, UPDATED_TIME = NOW()
        WHERE IDEMPOTENCY_KEY = #{idempotencyKey}
        AND IS_DELETED = 0
    </update>

    <!-- 批量删除过期记录 -->
    <delete id="deleteExpiredRecords">
        DELETE FROM T_IDEMPOTENCY_RECORD
        WHERE CREATED_TIME &lt; #{beforeDate}
        LIMIT #{batchSize}
    </delete>

    <!-- 根据业务类型和时间删除记录 -->
    <delete id="deleteByBusinessTypeAndDate">
        DELETE FROM T_IDEMPOTENCY_RECORD 
        WHERE BUSINESS_TYPE = #{businessType} 
        AND CREATED_TIME &lt; #{beforeDate}
    </delete>

    <!-- 删除失败状态的记录 -->
    <delete id="deleteFailedRecords">
        DELETE FROM T_IDEMPOTENCY_RECORD 
        WHERE STATUS = 2 
        AND CREATED_TIME &lt; #{beforeDate}
    </delete>

    <!-- 查询长时间处理中的记录 -->
    <select id="selectLongProcessingRecords" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_IDEMPOTENCY_RECORD
        WHERE STATUS = 0 
        AND IS_DELETED = 0
        AND CREATED_TIME &lt; DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
        ORDER BY CREATED_TIME ASC
    </select>

    <!-- 根据流程订单ID和公司代码查询记录 -->
    <select id="selectByFlowOrderAndCompany" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_IDEMPOTENCY_RECORD
        WHERE FLOW_ORDER_ID = #{flowOrderId}
        AND COMPANY_CODE = #{companyCode}
        AND IS_DELETED = 0
        <if test="businessType != null">
            AND BUSINESS_TYPE = #{businessType}
        </if>
        ORDER BY CREATED_TIME DESC
    </select>

    <!-- 根据业务文档ID查询记录 -->
    <select id="selectByBusinessDocumentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_IDEMPOTENCY_RECORD
        WHERE BUSINESS_DOCUMENT_ID = #{businessDocumentId}
        AND BUSINESS_TYPE = #{businessType}
        AND IS_DELETED = 0
        ORDER BY CREATED_TIME DESC
        LIMIT 1
    </select>

    <!-- 统计指定时间范围内的记录数 -->
    <select id="countRecords" resultType="int">
        SELECT COUNT(*)
        FROM T_IDEMPOTENCY_RECORD
        WHERE CREATED_TIME BETWEEN #{startTime} AND #{endTime}
        AND IS_DELETED = 0
        <if test="businessType != null">
            AND BUSINESS_TYPE = #{businessType}
        </if>
        <if test="status != null">
            AND STATUS = #{status}
        </if>
    </select>

    <!-- 分页查询记录 -->
    <select id="selectRecordsWithPaging" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_IDEMPOTENCY_RECORD
        <where>
            IS_DELETED = 0
            <if test="businessType != null">
                AND BUSINESS_TYPE = #{businessType}
            </if>
            <if test="status != null">
                AND STATUS = #{status}
            </if>
            <if test="startTime != null">
                AND CREATED_TIME &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND CREATED_TIME &lt;= #{endTime}
            </if>
        </where>
        ORDER BY CREATED_TIME DESC
        LIMIT #{offset}, #{limit}
    </select>

</mapper>
