package com.vedeng.erp.system.service;

import com.vedeng.erp.system.dto.OrganizationDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/29 13:22
 **/
public interface OrganizationApiService {

    /**
     * 根据部门id查询部门信息
     *
     * @param orgId 部门id
     * @return OrganizationDto
     */
    OrganizationDto getOrganizationById(Integer orgId);

    /**
     * 根据当前部门id递归查询最上级部门id（查到所有一级部门）
     *
     * @param orgId 部门id
     * @return 最上级部门id
     */
    Integer getTopOrgIdByOrgId(Integer orgId);

    /**
     * 获取部门下所有部门id 包括自己
     * @param orgId 部门id
     * @return List<Integer>
     */
    List<Integer> getOrgAllSubId(Integer orgId);

    List<OrganizationDto> getOrgAllLeafOrgByParentId(Integer parentId);

}
