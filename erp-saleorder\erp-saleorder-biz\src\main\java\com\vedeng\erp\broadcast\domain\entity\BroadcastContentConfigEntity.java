package com.vedeng.erp.broadcast.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 播报内容配置表
 */
@Getter
@Setter
public class BroadcastContentConfigEntity extends BaseEntity {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 图片URL
     */
    private String picUrl;

    /**
     * 图片名称
     */
    private String picName;

    /**
     * 专属类型：1=个人，2=团队，3=项目
     */
    private Integer exclusiveType;

    /**
     * 专属目标，逗号分隔：个人 用户ID; 部门 部门ID; 项目 1=月度AED TOP，2=月度自有品牌TOP
     */
    private String exclusiveTargetValues;

    /**
     * 用于搜索的文本，根据专属类型，专属目标保存相关文本
     */
    private String exclusiveTargetLabels;

    /**
     * 是否已删除（0=否，1=是）
     */
    private Integer isDeleted;
}
