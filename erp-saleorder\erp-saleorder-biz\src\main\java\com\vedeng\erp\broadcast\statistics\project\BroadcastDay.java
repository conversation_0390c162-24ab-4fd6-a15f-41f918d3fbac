package com.vedeng.erp.broadcast.statistics.project;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayExample;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayWithBLOBs;
import com.vedeng.erp.broadcast.mapdao.BroadcastStatisticsMapper;
import com.vedeng.erp.common.broadcast.MessageSubjectEnum;
import com.vedeng.erp.common.broadcast.StatDateRangeEnum;
import com.vedeng.erp.common.broadcast.StatisticsTypeEnum;
import com.vedeng.erp.common.broadcast.config.BroadcastDeptConfigStatistics;
import com.vedeng.erp.common.broadcast.config.BroadcastGlobalConfigStatistics;
import com.vedeng.erp.common.broadcast.config.GlobalConfig;
import com.vedeng.erp.common.broadcast.constants.BroadcastRedisKey;
import com.vedeng.erp.common.broadcast.constants.MessageTemplate;
import com.vedeng.erp.common.broadcast.param.QwMessageParam;
import com.vedeng.erp.common.broadcast.param.QwMessageParam.Article;
import com.vedeng.erp.common.broadcast.param.QwMessageParam.News;
import com.vedeng.erp.common.broadcast.param.TargetOrgAndUser;
import com.vedeng.erp.common.broadcast.param.TimePeriod;
import com.vedeng.erp.common.broadcast.statistics.StatisticsDto;
import com.vedeng.erp.statistic.mapper.BroadcastStatisticIncomeDayMapper;

import cn.hutool.core.collection.CollectionUtil;


/**
 * 日到款播报项目
 * @ClassName:  BroadcastDay
 * @author: Neil.yang
 * @date:   2025年6月9日 上午9:30:25
 * @Copyright:
 */
@Component
public class BroadcastDay extends AbstractBroadcast {

	@Autowired
	private BroadcastStatisticsMapper broadcastStatisticsMapper;
	
	@Autowired
    private RedisUtils redisUtils;
	
	@Autowired
	private BroadcastStatisticIncomeDayMapper broadcastStatisticIncomeDayMapper;
	
	@Value("${redis_dbtype}")
	private String dbType;
	
	@Value("${userTopN:50}")
	private Integer userTopN;
	
	@Override
	public List<MessageSubjectEnum> getMessageSubjectList(GlobalConfig globalConfig) {
		//日到款播报到个人、小组、部门，发送企微播报只到个人
		List<MessageSubjectEnum> messageSubjectList = Arrays.asList(MessageSubjectEnum.SALES_SINGLE,MessageSubjectEnum.SALES_TEAM,MessageSubjectEnum.SALES_DEPT);
		return messageSubjectList;
	}

	@Override
	public int getLineNum() {
		return 6;
	}

	@Override
	public boolean isSaveDb() {
		return true;
	}
	
	@Override
	public List<BroadcastDeptConfigStatistics> getBroadcastDeptConfigByProject(List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList) {
		return broadcastDeptConfigStatisticsList.stream().filter(item -> item.getDayFlag() == 1).collect(Collectors.toList());
	}

	/**
     * 获取统计的类型
     * @return 统计类型列表
     */
    public  List<StatisticsTypeEnum> getStatisticsTypeList(){
    	List<StatisticsTypeEnum> statisticsTypeList = Arrays.asList(StatisticsTypeEnum.WAREHOUSE_INCOME);
    	return statisticsTypeList;
    }
    
    /**
     * 是否需要按挡位播报
     * @return 是否需要按挡位播报
     */
    public boolean getNeedPersonalLevelFilter() {
    	return true;
    }
	
    /**
     * 获取统计时间周期
     * @return 统计时间周期
     */
    public TimePeriod getStatisticsTime() {
    	Calendar calendar = Calendar.getInstance();
    	//calendar.add(Calendar.YEAR, -2);
    	calendar.set(Calendar.HOUR_OF_DAY, 0);
    	calendar.set(Calendar.MINUTE, 0);
    	calendar.set(Calendar.SECOND, 0);
    	calendar.set(Calendar.MILLISECOND, 0);
    	Date midnight = calendar.getTime();
    	return new TimePeriod(midnight,new Date());
    }
    
	@Override
	public List<QwMessageParam> execute(GlobalConfig globalConfig,Map<MessageSubjectEnum, List<TargetOrgAndUser>> targetOrgAndUserMap,Integer deptId,Integer amountStep,Integer isUserDefine,TimePeriod timePeriod,StatDateRangeEnum statDateRange,boolean isSendQwMessageFlag) {
		//日到款执行时间参数
		if(Objects.isNull(timePeriod)) {
			timePeriod = getStatisticsTime();
		}
		//播报的消息列表
		List<QwMessageParam> qwMessageParamList = new ArrayList<>();
		//日到款-个人
		List<TargetOrgAndUser> targetOrgAndUserPerson = targetOrgAndUserMap.get(MessageSubjectEnum.SALES_SINGLE);
		BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics = globalConfig.getBroadcastGlobalConfigStatistics();
		if(Objects.nonNull(targetOrgAndUserPerson)) {
			List<StatisticsDto> statisticsDtoListAll = new ArrayList<>();
			for (TargetOrgAndUser targetOrgAndUser : targetOrgAndUserPerson) {
				if(deptId==1) {
					List<StatisticsDto> statisticsDtoList = broadcastStatisticsMapper.selectStatisticsAmountByPersonParams(timePeriod.getStartTime().getTime(),timePeriod.getEndTime().getTime(),targetOrgAndUser.getOrgIdList(),broadcastGlobalConfigStatistics.getExcludeSaleIds(),broadcastGlobalConfigStatistics.getExcludeTraderIds(),targetOrgAndUser.getInUserIdList(),targetOrgAndUser.getOutUserIdList(),targetOrgAndUser.getInUserIdBelongOrgIdList());
					statisticsDtoListAll.addAll(statisticsDtoList);
				}else {
					List<StatisticsDto> statisticsDtoList = broadcastStatisticsMapper.selectStatisticsAmountByPersonParams(timePeriod.getStartTime().getTime(),timePeriod.getEndTime().getTime(),targetOrgAndUser.getOrgIdList(),broadcastGlobalConfigStatistics.getExcludeSaleIds(),broadcastGlobalConfigStatistics.getExcludeTraderIds(),null,null,null);
					statisticsDtoListAll.addAll(statisticsDtoList);
				}
			}
			if(showLog) {
				LOGGER.info("获取日播报个人信息排行信息：{}",JSON.toJSONString(statisticsDtoListAll));
			}
			if(!CollectionUtils.isEmpty(statisticsDtoListAll)) {
				//按照totalAmount降序排列
				statisticsDtoListAll = statisticsDtoListAll.stream().sorted(Comparator.comparing(StatisticsDto::getTotalAmount).reversed()).collect(Collectors.toList());
				//是否保存表信息(只排序后的结果，没有剔除梯度和播报数量)
				if(isSaveDb() && isUserDefine==0 && deptId == 1) {
					//存表
					try {
						if(statisticsDtoListAll.size()>userTopN) {
							statisticsDtoListAll = statisticsDtoListAll.subList(0, userTopN);
						}
						batchInert(statisticsDtoListAll,MessageSubjectEnum.SALES_SINGLE,timePeriod,globalConfig);
					}catch(Exception e) {
						LOGGER.error("日播报个人信息排行存表失败...参数：全局参数：{},数据：{}",JSON.toJSONString(globalConfig),JSON.toJSONString(statisticsDtoListAll),e);
					}
				}
				if(isSendQwMessageFlag) {
					//统计信息获取，组装企微信息，记录需要按挡位播报
					List<QwMessageParam> qwMessageParams = combineQwMessage(statisticsDtoListAll,globalConfig,amountStep,deptId);
					qwMessageParamList.addAll(qwMessageParams);
				}
			}
		}
		//日到款-小组（无需企微播报）
		List<TargetOrgAndUser> targetOrgAndUserTeam = targetOrgAndUserMap.get(MessageSubjectEnum.SALES_TEAM);
		if(Objects.nonNull(targetOrgAndUserTeam)) {
			List<StatisticsDto> statisticsDtoListAll = new ArrayList<>();
			for (TargetOrgAndUser targetOrgAndUser : targetOrgAndUserTeam) {
				StatisticsDto statisticsDto = broadcastStatisticsMapper.selectStatisticsAmountByTeamParams(timePeriod.getStartTime().getTime(),timePeriod.getEndTime().getTime(),targetOrgAndUser.getOrgIdList(),broadcastGlobalConfigStatistics.getExcludeSaleIds(),broadcastGlobalConfigStatistics.getExcludeTraderIds(),targetOrgAndUser.getInUserIdList(),targetOrgAndUser.getOutUserIdList(),targetOrgAndUser.getInUserIdBelongOrgIdList());
				if(Objects.nonNull(statisticsDto)) {
					statisticsDto.setTeamId(targetOrgAndUser.getTeamId());
					statisticsDto.setTeamName(targetOrgAndUser.getTeamName());
					statisticsDtoListAll.add(statisticsDto);
				}
				if(showLog) {
					LOGGER.info("获取日播报小组信息排行信息：{}",JSON.toJSONString(statisticsDto));
				}
			}
			if(CollectionUtil.isNotEmpty(statisticsDtoListAll)) {
				//是否保存表信息
				if(isSaveDb() && isUserDefine==0 && deptId == 1) {
					//存表
					try {
						batchInert(statisticsDtoListAll,MessageSubjectEnum.SALES_TEAM,timePeriod,globalConfig);
					}catch(Exception e) {
						LOGGER.error("日播报小组信息排行存表失败...参数：全局参数：{},数据：{}",JSON.toJSONString(globalConfig),JSON.toJSONString(statisticsDtoListAll),e);
					}
				}
			}
			
		}
		//日到款-部门（无需企微播报）
		List<TargetOrgAndUser> targetOrgAndUserDept = targetOrgAndUserMap.get(MessageSubjectEnum.SALES_DEPT);
		if(Objects.nonNull(targetOrgAndUserDept)) {
			List<StatisticsDto> statisticsDtoListAll = new ArrayList<>();
			for (TargetOrgAndUser targetOrgAndUser : targetOrgAndUserDept) {
				StatisticsDto statisticsDto = broadcastStatisticsMapper.selectStatisticsAmountByTeamParams(timePeriod.getStartTime().getTime(),timePeriod.getEndTime().getTime(),targetOrgAndUser.getOrgIdList(),broadcastGlobalConfigStatistics.getExcludeSaleIds(),broadcastGlobalConfigStatistics.getExcludeTraderIds(),targetOrgAndUser.getInUserIdList(),targetOrgAndUser.getOutUserIdList(),targetOrgAndUser.getInUserIdBelongOrgIdList());
				if(Objects.nonNull(statisticsDto)) {
					statisticsDto.setDeptId(targetOrgAndUser.getDeptId());
					statisticsDto.setDeptName(targetOrgAndUser.getDeptName());
					statisticsDtoListAll.add(statisticsDto);
				}
				if(showLog) {
					LOGGER.info("获取日播报部门信息排行信息：{}",JSON.toJSONString(statisticsDto));
				}
			}
			if(CollectionUtil.isNotEmpty(statisticsDtoListAll)) {
				//是否保存表信息
				if(isSaveDb() && isUserDefine==0 && deptId == 1) {
					//存表
					try {
						batchInert(statisticsDtoListAll,MessageSubjectEnum.SALES_DEPT,timePeriod,globalConfig);
					}catch(Exception e) {
						LOGGER.error("日播报部门信息排行存表失败...参数：全局参数：{},数据：{}",JSON.toJSONString(globalConfig),JSON.toJSONString(statisticsDtoListAll),e);
					}
				}
			}
		}

		return qwMessageParamList;
	}

	/**
	 * 日到款批量新增
	 * @param statisticsDtoListAll
	 * @param timePeriod 
	 * @param globalConfig 
	 * @param salesSingle 
	 */
	@Transactional
	private void batchInert(List<StatisticsDto> statisticsDtoListAll, MessageSubjectEnum messageSubjectEnum, TimePeriod timePeriod, GlobalConfig globalConfig) {
		//删除统计类型【1：个人；2：小组；3：部门】 和 统计日期【yyyy-MM-dd】 
		BroadcastStatisticIncomeDayExample example = new BroadcastStatisticIncomeDayExample();
		String statisticsTime = DateUtil.DateToString(timePeriod.getStartTime(), DateUtil.DATE_FORMAT);
		example.createCriteria().andIsDelEqualTo(0).andStatisticsTypeEqualTo(messageSubjectEnum.getSubject()).andStatisticsTimeEqualTo(statisticsTime);
		//历史数据先删除，在新增
		broadcastStatisticIncomeDayMapper.deleteByExample(example);
		
		List<BroadcastStatisticIncomeDayWithBLOBs> broadcastStatisticIncomeDayList = new ArrayList<>();
		Date now = new Date();
		for (StatisticsDto statisticsDto : statisticsDtoListAll) {
			BroadcastStatisticIncomeDayWithBLOBs broadcastStatisticIncomeDayWithBLOBs = new BroadcastStatisticIncomeDayWithBLOBs();
			broadcastStatisticIncomeDayWithBLOBs.setAmount(statisticsDto.getTotalAmount());
			broadcastStatisticIncomeDayWithBLOBs.setConditionText(JSON.toJSONString(globalConfig.getBroadcastGlobalConfigStatistics())+"=="+JSON.toJSONString(globalConfig.getBroadcastDeptConfigStatisticsList()));
			broadcastStatisticIncomeDayWithBLOBs.setCreateTime(now);
			broadcastStatisticIncomeDayWithBLOBs.setDeptId(statisticsDto.getDeptId());
			broadcastStatisticIncomeDayWithBLOBs.setDeptName(statisticsDto.getDeptName());
			broadcastStatisticIncomeDayWithBLOBs.setIsDel(0);
			broadcastStatisticIncomeDayWithBLOBs.setStatisticsTime(statisticsTime);
			broadcastStatisticIncomeDayWithBLOBs.setStatisticsType(messageSubjectEnum.getSubject());
			broadcastStatisticIncomeDayWithBLOBs.setTeamId(statisticsDto.getTeamId());
			broadcastStatisticIncomeDayWithBLOBs.setTeamName(statisticsDto.getTeamName());
			broadcastStatisticIncomeDayWithBLOBs.setUpdateTime(now);
			broadcastStatisticIncomeDayWithBLOBs.setUserId(statisticsDto.getUserId());
			broadcastStatisticIncomeDayWithBLOBs.setUserName(statisticsDto.getUserName());
			broadcastStatisticIncomeDayList.add(broadcastStatisticIncomeDayWithBLOBs);
		}
		if(!CollectionUtils.isEmpty(broadcastStatisticIncomeDayList)) {
			if(showLog) {
				LOGGER.info("日到款批量新增，入参：{}",JSON.toJSONString(broadcastStatisticIncomeDayList));
			}
			broadcastStatisticIncomeDayMapper.batchInsert(broadcastStatisticIncomeDayList);
		}
		
	}

	private List<QwMessageParam> combineQwMessage(List<StatisticsDto> statisticsDtoList,GlobalConfig globalConfig, Integer amountStep, Integer deptId) {
		List<QwMessageParam> qwMessageParamList = new ArrayList<>();
		BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics = globalConfig.getBroadcastGlobalConfigStatistics();
		//组装要发送的企微参数
		List<String> descriptionList = getDescription(statisticsDtoList,globalConfig,amountStep,deptId);
		for (String description : descriptionList) {
			QwMessageParam qwMessageParam = new QwMessageParam();
			qwMessageParam.setMsgtype("news");
			News news = new News();
			List<Article> articles = new ArrayList<>();
			Article article = new Article();
			article.setTitle("🎉"+broadcastGlobalConfigStatistics.getBroadcastTitleDay());
			// 如果最后有多余的 \n\n，去除它
		    if (description.length() > 2 && description.substring(description.length() - 2).equals("\n\n")) {
		    	description = description.subSequence(0,description.length() - 2).toString();
		    }
			article.setDescription(description);
			//跳转链接，本期固定
			article.setUrl(qwUrl);
			//获取图片逻辑（排序第一的个人）
			article.setPicurl(getPicUrl(statisticsDtoList.get(0).getUserId(),globalConfig,deptId));
			articles.add(article);
			news.setArticles(articles);
			qwMessageParam.setNews(news);
			qwMessageParamList.add(qwMessageParam);
		}
		return qwMessageParamList;
	}

	/**
	 * 拼接企微消息
	 * @param statisticsDtoList
	 * @param amountStep 
	 * @param deptId 
	 * @return
	 */
	private List<String> getDescription(List<StatisticsDto> statisticsDtoList,GlobalConfig globalConfig, Integer amountStep, Integer deptId) {
		List<StatisticsDto> statisticsDtoListChecked = new ArrayList<>();
		//日常到款金额梯度(先筛选出大于梯度金额的数据)
		statisticsDtoListChecked = statisticsDtoList.stream().filter(dto -> dto.getTotalAmount() != null &&  dto.getTotalAmount().compareTo(new BigDecimal(amountStep*10000)) >= 0).collect(Collectors.toList());
		if(showLog) {
			LOGGER.info("已排除掉不满足金额梯度的数据：{}",JSON.toJSONString(statisticsDtoListChecked));
		}
		int expireTime = Integer.parseInt(String.valueOf(DateUtil.getSecondsToNextDay()));
		//判断是否满足梯度
		//查询播报梯度（筛选出，梯度值大于原来的数据）
		List<StatisticsDto> statisticsDtoListStep = new ArrayList<>();
		for (StatisticsDto statisticsDto : statisticsDtoListChecked) {
			//用户ID
			Integer userId = statisticsDto.getUserId();
			//获取播报梯度 uat:deptId:userId:amountStepNum:1
			String amountStepNumKey = MessageFormat.format(BroadcastRedisKey.AMOUNT_STEP_NUM,dbType,deptId,userId);
			Integer amountStepNumValue = redisUtils.getInt(amountStepNumKey);
			LOGGER.info("播报梯度redis记录：key：{},value:{}",amountStepNumKey,amountStepNumValue);
			//无梯度值，说明未播报过，直接播报
			//本次触及梯度值
			Integer amountStepNum = (statisticsDto.getTotalAmount().divideToIntegralValue(new BigDecimal(amountStep*10000))).intValue();
			if(Objects.isNull(amountStepNumValue)) {
				statisticsDtoListStep.add(statisticsDto);
				//设置梯度
				redisUtils.set(amountStepNumKey,amountStepNum,expireTime);
			}else {
				LOGGER.info("梯度值：{},总额：{}",amountStepNum,statisticsDto.getTotalAmount());
				if(amountStepNumValue < amountStepNum) {
					statisticsDtoListStep.add(statisticsDto);
					//更新梯度值
					redisUtils.set(amountStepNumKey,amountStepNum,expireTime);
				}
			}
		}
		List<String> descriptionList = new ArrayList<>();
		
		//由于企微限制，每次不能超过512个字符，此处需要判断
		List<String> messageInfoList = new ArrayList<>();
		for (StatisticsDto statisticsDto : statisticsDtoListStep) {
			messageInfoList.add(processBatchMessage(Arrays.asList(statisticsDto)));
		}
	    Integer	batchSize = getLineNum();
	    while (batchSize > 0) {
	    	descriptionList.clear();
            boolean validBatch = true;

            for (int i = 0; i < messageInfoList.size(); i += batchSize) {
                List<String> batch = messageInfoList.subList(i, Math.min(i + batchSize, messageInfoList.size()));
                String combined = String.join("", batch);
                
                // 检查字节长度（UTF-8 编码）
                byte[] bytes = combined.getBytes(StandardCharsets.UTF_8);
                if (bytes.length >= 512) {
                    validBatch = false;
                    break;
                }
                descriptionList.add(combined);
            }

            if (validBatch) {
                return descriptionList; // 找到合适的批次大小
            }
            batchSize--; // 减小批次大小重试
        }
		return descriptionList;
	}
	

	private String processBatchMessage(List<StatisticsDto> statisticsDtoList) {
		StringBuilder stb = new StringBuilder();
		for (StatisticsDto statisticsDto : statisticsDtoList) {
			String userName = "未知用户";
			String teamName = statisticsDto.getTeamName();
			if(StringUtils.isNotEmpty(statisticsDto.getUserName())) {
				userName = statisticsDto.getUserName().split("\\.")[0];
			}
			String message = MessageFormat.format(MessageTemplate.DAY_PERSON_MESSAGE,teamName,userName,statisticsDto.getTotalAmount().toString());
			stb.append(message);
			stb.append("\n\n");
		}
		return stb.toString();
	}
	


	/**
	 * 获取展示的图片
	 * 1. 如排序第一的个人或团队，有专属图片，则随机选择专属图片（播报管理 - 专属目标），每张图片每日仅使用一次，如当日全部使用，则重置使用次数
	 * 2. 否则随机获取非专属目标的图片，每张图片每日仅使用一次，如当日全部使用，则重置使用次数
	 * @param userId
	 * @param globalConfig
	 * @param deptId 
	 * @return
	 */
	private String getPicUrl(Integer userId, GlobalConfig globalConfig, Integer deptId) {
		String picUrl = "http://#";
		Integer picId = null;
		List<BroadcastContentConfigEntity> broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList();
		//为空，返回空字符串
		if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
			return picUrl;
		}
		//是否有个人的专属图片
		boolean flag = broadcastContentConfigStatisticsList.stream().anyMatch(temp->(Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==1 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(userId))));
		LOGGER.info("用户：{}，是否有专属图片：{}",userId,flag);
		if(flag) {
			//筛选出图片列表,专属个人，并且在已使用记录中不存在的
			broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream()
					.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==1 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(userId)))
					.collect(Collectors.toList());
			if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
				return picUrl;
			}
			//REDIS获取已使用的图片列表
			String picConfigRecordKey = MessageFormat.format(BroadcastRedisKey.PIC_CONFIG_RECORD,dbType,1,userId);
			List<Integer> picConfigRecordValue = redisUtils.lgetAllInt(picConfigRecordKey);
			LOGGER.info("专属图片ID使用情况:{}",JSON.toJSONString(picConfigRecordValue));
			//REDIS为空，说明当日首次使用
			int expireTime = Integer.parseInt(String.valueOf(DateUtil.getSecondsToNextDay()));
			if(CollectionUtils.isEmpty(picConfigRecordValue)) {
				Collections.shuffle(broadcastContentConfigStatisticsList);
				picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
				picId = broadcastContentConfigStatisticsList.get(0).getId();
				redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
			}else {
				//筛选出图片列表,专属个人，并且在已使用记录中不存在的
				broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream().filter(temp-> !picConfigRecordValue.contains(temp.getId())).collect(Collectors.toList());
				//无筛选值，删除REDIS，随机取一个，重建REDIS
				if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
					//无筛选值，重新获取
					broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList().stream()
							.filter(temp-> Objects.nonNull(temp.getExclusiveType()) && temp.getExclusiveType()==1 && Objects.nonNull(temp.getExclusiveTargetValues()) && temp.getExclusiveTargetValues().equals(String.valueOf(userId)))
							.collect(Collectors.toList());;
					redisUtils.del(picConfigRecordKey);
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}else {
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}
			}
		}else {
			broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream().filter(temp-> temp.getExclusiveType()== 0).collect(Collectors.toList());
			if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
				return picUrl;
			}
			//REDIS获取已使用的图片列表
			String picConfigRecordKey = MessageFormat.format(BroadcastRedisKey.PIC_CONFIG_RECORD,dbType,0,0);
			List<Integer> picConfigRecordValue = redisUtils.lgetAllInt(picConfigRecordKey);
			LOGGER.info("非专属图片ID使用情况:{}",JSON.toJSONString(picConfigRecordValue));
			//REDIS为空，说明当日首次使用
			int expireTime = Integer.parseInt(String.valueOf(DateUtil.getSecondsToNextDay()));
			if(CollectionUtils.isEmpty(picConfigRecordValue)) {
				Collections.shuffle(broadcastContentConfigStatisticsList);
				picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
				picId = broadcastContentConfigStatisticsList.get(0).getId();
				redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
			}else {
				//筛选出图片列表,非专属，并且在已使用记录中不存在的
				broadcastContentConfigStatisticsList = broadcastContentConfigStatisticsList.stream().filter(temp->!picConfigRecordValue.contains(temp.getId()) ).collect(Collectors.toList());
				//无筛选值，删除REDIS，随机取一个，重建REDIS
				if(CollectionUtils.isEmpty(broadcastContentConfigStatisticsList)) {
					broadcastContentConfigStatisticsList = globalConfig.getBroadcastContentConfigStatisticsList().stream().filter(temp-> temp.getExclusiveType()== 0).collect(Collectors.toList());
					redisUtils.del(picConfigRecordKey);
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}else {
					Collections.shuffle(broadcastContentConfigStatisticsList);
					picUrl = broadcastContentConfigStatisticsList.get(0).getPicUrl();
					picId = broadcastContentConfigStatisticsList.get(0).getId();
					redisUtils.lpushSingleIntWithExpire(picConfigRecordKey,expireTime,picId);
				}
			}
		}
		if(deptId == 1) {
			String picConfigRecordKey = MessageFormat.format(BroadcastRedisKey.ALREADY_DISPLAY_PIC_URL,dbType,1,1);
			//不用设置过期时间，直接进行替换
			redisUtils.set(picConfigRecordKey, picUrl);
		}
		return picUrl;
	}

}
