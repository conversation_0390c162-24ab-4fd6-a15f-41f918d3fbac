package com.vedeng.erp.broadcast.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * T_BROADCAST_STATISTIC_INCOME_MONTH
 */
public class BroadcastStatisticIncomeMonth extends BroadcastStatisticIncomeMonthKey {
    /**
     * <pre>
     * 统计类型【1：个人；2：小组；3：部门】
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.STATISTICS_TYPE
     * </pre>
     * 
     */
    private Integer statisticsType;

    /**
     * <pre>
     * 统计月份【yyyy-MM】
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.STATISTICS_TIME
     * </pre>
     * 
     */
    private String statisticsTime;

    /**
     * <pre>
     * 用户ID（erp系统中的USER_ID）
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.USER_ID
     * </pre>
     * 
     */
    private Integer userId;

    /**
     * <pre>
     * 用户名称
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.USER_NAME
     * </pre>
     * 
     */
    private String userName;

    /**
     * <pre>
     * 小组ID
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.TEAM_ID
     * </pre>
     * 
     */
    private Integer teamId;

    /**
     * <pre>
     * 小组名
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.TEAM_NAME
     * </pre>
     * 
     */
    private String teamName;

    /**
     * <pre>
     * 部门ID
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.DEPT_ID
     * </pre>
     * 
     */
    private Integer deptId;

    /**
     * <pre>
     * 部门名
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.DEPT_NAME
     * </pre>
     * 
     */
    private String deptName;

    /**
     * <pre>
     * 到款金额
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.AMOUNT
     * </pre>
     * 
     */
    private BigDecimal amount;

    /**
     * <pre>
     * 完成率
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.ARCHIEVED_PRECENT
     * </pre>
     * 
     */
    private Integer archievedPrecent;

    /**
     * <pre>
     * 是否删除
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.IS_DEL
     * </pre>
     * 
     */
    private Integer isDel;

    /**
     * <pre>
     * 创建时间
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.CREATE_TIME
     * </pre>
     * 
     */
    private Date createTime;

    /**
     * <pre>
     * 修改时间
     * 表字段 : T_BROADCAST_STATISTIC_INCOME_MONTH.UPDATE_TIME
     * </pre>
     * 
     */
    private Date updateTime;

    /**
     * 统计类型【1：个人；2：小组；3：部门】
     * @return STATISTICS_TYPE 统计类型【1：个人；2：小组；3：部门】
     */
    public Integer getStatisticsType() {
        return statisticsType;
    }

    /**
     * 统计类型【1：个人；2：小组；3：部门】
     * @param statisticsType 统计类型【1：个人；2：小组；3：部门】
     */
    public void setStatisticsType(Integer statisticsType) {
        this.statisticsType = statisticsType;
    }

    /**
     * 统计月份【yyyy-MM】
     * @return STATISTICS_TIME 统计月份【yyyy-MM】
     */
    public String getStatisticsTime() {
        return statisticsTime;
    }

    /**
     * 统计月份【yyyy-MM】
     * @param statisticsTime 统计月份【yyyy-MM】
     */
    public void setStatisticsTime(String statisticsTime) {
        this.statisticsTime = statisticsTime == null ? null : statisticsTime.trim();
    }

    /**
     * 用户ID（erp系统中的USER_ID）
     * @return USER_ID 用户ID（erp系统中的USER_ID）
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * 用户ID（erp系统中的USER_ID）
     * @param userId 用户ID（erp系统中的USER_ID）
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 用户名称
     * @return USER_NAME 用户名称
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 用户名称
     * @param userName 用户名称
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    /**
     * 小组ID
     * @return TEAM_ID 小组ID
     */
    public Integer getTeamId() {
        return teamId;
    }

    /**
     * 小组ID
     * @param teamId 小组ID
     */
    public void setTeamId(Integer teamId) {
        this.teamId = teamId;
    }

    /**
     * 小组名
     * @return TEAM_NAME 小组名
     */
    public String getTeamName() {
        return teamName;
    }

    /**
     * 小组名
     * @param teamName 小组名
     */
    public void setTeamName(String teamName) {
        this.teamName = teamName == null ? null : teamName.trim();
    }

    /**
     * 部门ID
     * @return DEPT_ID 部门ID
     */
    public Integer getDeptId() {
        return deptId;
    }

    /**
     * 部门ID
     * @param deptId 部门ID
     */
    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    /**
     * 部门名
     * @return DEPT_NAME 部门名
     */
    public String getDeptName() {
        return deptName;
    }

    /**
     * 部门名
     * @param deptName 部门名
     */
    public void setDeptName(String deptName) {
        this.deptName = deptName == null ? null : deptName.trim();
    }

    /**
     * 到款金额
     * @return AMOUNT 到款金额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 到款金额
     * @param amount 到款金额
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 完成率
     * @return ARCHIEVED_PRECENT 完成率
     */
    public Integer getArchievedPrecent() {
        return archievedPrecent;
    }

    /**
     * 完成率
     * @param archievedPrecent 完成率
     */
    public void setArchievedPrecent(Integer archievedPrecent) {
        this.archievedPrecent = archievedPrecent;
    }

    /**
     * 是否删除
     * @return IS_DEL 是否删除
     */
    public Integer getIsDel() {
        return isDel;
    }

    /**
     * 是否删除
     * @param isDel 是否删除
     */
    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    /**
     * 创建时间
     * @return CREATE_TIME 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改时间
     * @return UPDATE_TIME 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}