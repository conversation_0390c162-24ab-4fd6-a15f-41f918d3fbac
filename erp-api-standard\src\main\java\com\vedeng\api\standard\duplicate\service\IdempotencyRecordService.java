package com.vedeng.api.standard.duplicate.service;

import com.alibaba.fastjson.JSON;
import com.vedeng.api.standard.duplicate.entity.IdempotencyRecord;
import com.vedeng.api.standard.duplicate.enums.IdempotencyStatus;
import com.vedeng.api.standard.duplicate.exception.IdempotencyException;
import com.vedeng.api.standard.duplicate.mapper.IdempotencyRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 幂等性记录服务类
 * 提供幂等性记录的CRUD操作和业务逻辑
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-15
 */
@Service
public class IdempotencyRecordService {
    
    private static final Logger logger = LoggerFactory.getLogger(IdempotencyRecordService.class);
    
    @Autowired
    private IdempotencyRecordMapper idempotencyRecordMapper;
    

    
    /**
     * 根据幂等性键查找记录
     * 
     * @param idempotencyKey 幂等性键
     * @return 幂等性记录，如果不存在返回null
     * @throws IdempotencyException 如果查询失败
     */
    public IdempotencyRecord findByKey(String idempotencyKey) {
        try {
            if (StringUtils.isEmpty(idempotencyKey)) {
                return null;
            }
            
            return idempotencyRecordMapper.selectByIdempotencyKey(idempotencyKey);
            
        } catch (Exception e) {
            logger.error("查询幂等性记录失败: key={}", idempotencyKey, e);
            throw IdempotencyException.recordQueryError(idempotencyKey, e);
        }
    }
    
    /**
     * 创建处理中记录
     * 
     * @param idempotencyKey 幂等性键
     * @param businessRequest 业务请求对象
     * @param context 上下文信息
     * @return 创建的幂等性记录
     * @throws IdempotencyException 如果创建失败
     */
    @Transactional
    public IdempotencyRecord createProcessingRecord(String idempotencyKey,
                                                   Object businessRequest,
                                                   Map<String, Object> context) {
        try {
            IdempotencyRecord record = new IdempotencyRecord();
            
            // 基础信息
            record.setIdempotencyKey(idempotencyKey);
            
            // 从幂等性键解析基础信息（格式：businessType|companyCode|flowOrderId）
            String[] keyParts = idempotencyKey.split("\\|");
            if (keyParts.length >= 3) {
                record.setBusinessType(keyParts[0]);
                record.setCompanyCode(keyParts[1]);
                record.setFlowOrderId(keyParts[2]);
            } else {
                // 如果解析失败，从 context 中获取
                record.setBusinessType((String) context.get("businessType"));
                record.setCompanyCode((String) context.get("companyCode"));
                record.setFlowOrderId((String) context.get("flowOrderId"));
            }
            
            // 请求数据
            if (businessRequest != null) {
                record.setRequestData(JSON.toJSONString(businessRequest));
            }
            
            // 状态信息
            record.setStatus(IdempotencyStatus.PROCESSING.getCode());
            
            // 逻辑删除状态（新记录默认未删除）
            record.setIsDeleted(0);
            
            // 时间信息
            Date now = new Date();
            record.setCreatedTime(now);
            record.setUpdatedTime(now);

            
            // 扩展信息
            if (context != null && !context.isEmpty()) {
                record.setExtraInfo(JSON.toJSONString(context));
            }
            
            idempotencyRecordMapper.insert(record);
            
            logger.info("创建幂等性处理记录: key={}, businessType={}, flowOrderId={}", 
                idempotencyKey, record.getBusinessType(), record.getFlowOrderId());
                
            return record;
            
        } catch (DuplicateKeyException e) {
            // 并发情况下可能出现重复插入，查询已存在记录
            logger.warn("幂等性记录已存在，查询现有记录: key={}", idempotencyKey);
            
            // 添加重试机制确保记录存在
            IdempotencyRecord existingRecord = null;
            for (int i = 0; i < 3; i++) {
                existingRecord = findByKey(idempotencyKey);
                if (existingRecord != null) {
                    break;
                }
                // 短暂等待后重试
                try { 
                    Thread.sleep(10 + i * 5); // 递增等待时间
                } catch (InterruptedException ie) { 
                    Thread.currentThread().interrupt();
                    break; 
                }
                logger.debug("重试查询幂等性记录: key={}, attempt={}", idempotencyKey, i + 1);
            }
            
            if (existingRecord == null) {
                logger.error("并发异常：记录创建冲突但查询不到记录: key={}", idempotencyKey);
                throw IdempotencyException.recordCreationError(idempotencyKey, 
                    new RuntimeException("记录创建冲突后查询失败", e));
            }
            
            logger.debug("成功获取已存在的幂等性记录: key={}, status={}", 
                idempotencyKey, existingRecord.getStatus());
            return existingRecord;
        } catch (Exception e) {
            logger.error("创建幂等性记录失败: key={}", idempotencyKey, e);
            throw IdempotencyException.recordCreationError(idempotencyKey, e);
        }
    }
    
    /**
     * 更新为成功状态
     * 
     * @param idempotencyKey 幂等性键
     * @param response 响应对象
     * @param documentId 业务文档ID
     * @param documentNo 业务文档编号
     * @throws IdempotencyException 如果更新失败
     */
    @Transactional
    public void updateToSuccess(String idempotencyKey, Object response, 
                               String documentId, String documentNo) {
        try {
            IdempotencyRecord updateRecord = new IdempotencyRecord();
            updateRecord.setIdempotencyKey(idempotencyKey);
            updateRecord.setStatus(IdempotencyStatus.SUCCESS.getCode());
            
            if (response != null) {
                updateRecord.setResponseData(JSON.toJSONString(response));
            }
            
            if (!StringUtils.isEmpty(documentId)) {
                updateRecord.setBusinessDocumentId(documentId);
            }
            
            if (!StringUtils.isEmpty(documentNo)) {
                updateRecord.setBusinessDocumentNo(documentNo);
            }
            
            int updated = idempotencyRecordMapper.updateByIdempotencyKey(updateRecord);
            
            if (updated > 0) {
                logger.info("更新幂等性记录为成功: key={}, documentId={}", 
                    idempotencyKey, documentId);
            } else {
                logger.warn("更新幂等性记录失败，记录不存在: key={}", idempotencyKey);
            }
            
        } catch (Exception e) {
            logger.error("更新幂等性记录为成功状态失败: key={}", idempotencyKey, e);
            throw IdempotencyException.recordUpdateError(idempotencyKey, IdempotencyStatus.SUCCESS, e);
        }
    }
    
    /**
     * 更新为失败状态
     * 
     * @param idempotencyKey 幂等性键
     * @param exception 异常信息
     * @throws IdempotencyException 如果更新失败
     */
    @Transactional
    public void updateToFailure(String idempotencyKey, Exception exception) {
        try {
            IdempotencyRecord updateRecord = new IdempotencyRecord();
            updateRecord.setIdempotencyKey(idempotencyKey);
            updateRecord.setStatus(IdempotencyStatus.FAILED.getCode());
            
            if (exception != null) {
                updateRecord.setResponseData(exception.getMessage());
            }
            
            int updated = idempotencyRecordMapper.updateByIdempotencyKey(updateRecord);
            
            if (updated > 0) {
                logger.info("更新幂等性记录为失败: key={}, error={}", 
                    idempotencyKey, exception != null ? exception.getMessage() : "未知错误");
            } else {
                logger.warn("更新幂等性记录失败，记录不存在: key={}", idempotencyKey);
            }
            
        } catch (Exception e) {
            logger.error("更新幂等性记录为失败状态失败: key={}", idempotencyKey, e);
            // 这里不抛出异常，避免影响主业务流程
        }
    }
    
    

    /**
     * 根据幂等性键删除记录
     *
     * @param idempotencyKey 幂等性键
     * @return 删除的记录数
     */
    @Transactional
    public int deleteByIdempotencyKey(String idempotencyKey) {
        try {
            if (StringUtils.isEmpty(idempotencyKey)) {
                logger.warn("幂等性键为空，跳过删除操作");
                return 0;
            }

            int deleted = idempotencyRecordMapper.deleteByIdempotencyKey(idempotencyKey);

            if (deleted > 0) {
                logger.info("删除幂等性记录: key={}, deletedCount={}", idempotencyKey, deleted);
            } else {
                logger.debug("未找到要删除的幂等性记录: key={}", idempotencyKey);
            }

            return deleted;

        } catch (Exception e) {
            logger.error("根据幂等性键删除记录失败: key={}", idempotencyKey, e);
            throw new IdempotencyException("删除幂等性记录失败: " + idempotencyKey, e);
        }
    }
    
    /**
     * 根据幂等性键逻辑删除记录
     * 用于失败记录的重试场景，保留历史数据用于问题排查
     *
     * @param idempotencyKey 幂等性键
     * @return 删除的记录数
     */
    @Transactional
    public int logicalDeleteByIdempotencyKey(String idempotencyKey) {
        try {
            if (StringUtils.isEmpty(idempotencyKey)) {
                logger.warn("幂等性键为空，跳过逻辑删除操作");
                return 0;
            }

            int deleted = idempotencyRecordMapper.logicalDeleteByIdempotencyKey(idempotencyKey);

            if (deleted > 0) {
                logger.info("逻辑删除幂等性记录: key={}, deletedCount={}", idempotencyKey, deleted);
            } else {
                logger.debug("未找到要逻辑删除的幂等性记录: key={}", idempotencyKey);
            }

            return deleted;

        } catch (Exception e) {
            logger.error("逻辑删除幂等性记录失败: key={}", idempotencyKey, e);
            throw new IdempotencyException("逻辑删除幂等性记录失败: " + idempotencyKey, e);
        }
    }

}
