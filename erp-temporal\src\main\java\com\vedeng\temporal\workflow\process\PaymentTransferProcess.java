package com.vedeng.temporal.workflow.process;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.enums.ExecutionMode;
import com.vedeng.temporal.workflow.activity.*;
import com.vedeng.temporal.workflow.step.BusinessStep;
import com.vedeng.temporal.workflow.step.impl.PaymentStepV2;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * 付款传递流程服务
 * 基于AbstractBusinessProcess重构，使用统一的执行框架
 *
 * 执行特点：
 * - 正序执行（A → B → C → D）
 * - 依赖关系：等待发票录入完成后再执行付款
 * - 包含付款单生成、资金转账步骤
 * - 使用AbstractBusinessProcess的统一框架
 *
 * <AUTHOR> 4.0 sonnet
 * @version 4.0 (AbstractBusinessProcess重构版)
 * @since 2025-01-11
 */
@Slf4j
public class PaymentTransferProcess extends AbstractBusinessProcess {

    // Activity依赖（通过构造函数注入）
    private final PaymentActivity paymentActivity;

    public PaymentTransferProcess(CompanySequenceActivity companySequenceActivity,
                                  PaymentActivity paymentActivity) {

        // 调用父类构造函数
        super(companySequenceActivity);

        this.paymentActivity = paymentActivity;
    }

    @Override
    protected ExecutionMode getExecutionMode() {
        return ExecutionMode.SEQUENTIAL;
    }

    @Override
    protected boolean isReverseOrder() {
        return false; // 正序执行
    }

    @Override
    protected List<BusinessStep> getBusinessSteps() {
        // 付款流程只有一个付款步骤
        return Arrays.asList(
            new PaymentStepV2(paymentActivity)
        );
    }

    @Override
    protected String getProcessName() {
        return "付款转账流程";
    }


    /**
     * 执行付款传递流程
     * 使用AbstractBusinessProcess统一框架
     *
     * @param request 业务请求
     * @param companySequence 公司执行序列
     * @return 执行结果
     */
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, List<String> companySequence) {
        return super.execute(request, companySequence);
    }
}
