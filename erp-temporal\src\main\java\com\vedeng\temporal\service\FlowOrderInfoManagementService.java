package com.vedeng.temporal.service;

import com.vedeng.temporal.domain.entity.FlowOrderInfoEntity;
import com.vedeng.temporal.mapper.TemporalFlowOrderInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * FlowOrderInfo管理服务
 * 负责FlowOrderInfo记录的创建、更新和管理
 * 
 * 设计理念：
 * - 在订单创建步骤中同时创建FlowOrderInfo记录，避免时序问题
 * - 提供统一的创建和更新接口，确保数据一致性
 * - 支持幂等性操作，避免重复创建记录
 * 
 * 核心功能：
 * - createFlowOrderInfoForSalesOrder: 为销售订单创建FlowOrderInfo记录
 * - createFlowOrderInfoForPurchaseOrder: 为采购订单创建FlowOrderInfo记录
 * - updateFlowOrderInfoStatus: 更新FlowOrderInfo状态
 * - checkExists: 检查记录是否已存在，支持幂等性
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-29
 */
@Service
@Slf4j
public class FlowOrderInfoManagementService {

    // 业务类型常量
    private static final int PURCHASE_TYPE = 0; // 采购
    private static final int SALES_TYPE = 1; // 销售

    @Autowired
    private TemporalFlowOrderInfoMapper temporalFlowOrderInfoMapper;

    /**
     * 为销售订单创建FlowOrderInfo记录
     *
     * @param flowNodeId 流程节点ID
     * @param salesOrderId 销售订单ID
     * @param companyCode 公司代码
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createFlowOrderInfoForSalesOrder(Long flowNodeId, String salesOrderId,
                                                   String companyCode) {
        log.info("开始为销售订单创建FlowOrderInfo记录: flowNodeId={}, salesOrderId={}, companyCode={}",
                flowNodeId, salesOrderId, companyCode);

        // 参数校验
        if (flowNodeId == null || !StringUtils.hasText(salesOrderId)) {
            log.error("参数校验失败: flowNodeId={}, salesOrderId={}", flowNodeId, salesOrderId);
            return false;
        }

        try {
            // 检查是否已存在（幂等性检查）
            if (checkFlowOrderInfoExists(flowNodeId, SALES_TYPE)) {
                log.info("销售订单FlowOrderInfo记录已存在，跳过创建: flowNodeId={}, salesOrderId={}",
                        flowNodeId, salesOrderId);
                return true;
            }

            // 构建FlowOrderInfo实体
            FlowOrderInfoEntity entity = createFlowOrderInfoEntity(flowNodeId, SALES_TYPE, salesOrderId);

            // 设置销售订单创建成功的初始状态
            entity.setOrderStatus(0);
            entity.setPaymentStatus(0); // 未付款
            entity.setStorageStatus(0); // 未入库
            entity.setInvoiceStatus(0); // 未收票

            // 插入记录
            int insertCount = temporalFlowOrderInfoMapper.insertSelective(entity);

            if (insertCount > 0) {
                log.info("销售订单FlowOrderInfo记录创建成功: flowNodeId={}, salesOrderId={}, insertId={}",
                        flowNodeId, salesOrderId, entity.getFlowOrderInfoId());
                return true;
            } else {
                log.warn("销售订单FlowOrderInfo记录插入失败: flowNodeId={}, salesOrderId={}",
                        flowNodeId, salesOrderId);
                return false;
            }

        } catch (Exception e) {
            log.error("创建销售订单FlowOrderInfo记录异常: flowNodeId={}, salesOrderId={}",
                    flowNodeId, salesOrderId, e);
            throw e;
        }
    }

    /**
     * 为采购订单创建FlowOrderInfo记录
     * 
     * @param flowNodeId 流程节点ID
     * @param purchaseOrderId 采购订单ID
     * @param companyCode 公司代码
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createFlowOrderInfoForPurchaseOrder(Long flowNodeId, String purchaseOrderId, 
                                                      String companyCode) {
        log.info("开始为采购订单创建FlowOrderInfo记录: flowNodeId={}, purchaseOrderId={}, companyCode={}", 
                flowNodeId, purchaseOrderId, companyCode);

        // 参数校验
        if (flowNodeId == null || !StringUtils.hasText(purchaseOrderId)) {
            log.error("参数校验失败: flowNodeId={}, purchaseOrderId={}", flowNodeId, purchaseOrderId);
            return false;
        }

        try {
            // 检查是否已存在（幂等性检查）
            if (checkFlowOrderInfoExists(flowNodeId, PURCHASE_TYPE)) {
                log.info("采购订单FlowOrderInfo记录已存在，跳过创建: flowNodeId={}, purchaseOrderId={}", 
                        flowNodeId, purchaseOrderId);
                return true;
            }

            // 构建FlowOrderInfo实体
            FlowOrderInfoEntity entity = createFlowOrderInfoEntity(
                    flowNodeId, PURCHASE_TYPE, purchaseOrderId);

            // 设置采购订单创建成功的初始状态
            entity.setOrderStatus(0);
            entity.setPaymentStatus(0); // 未付款
            entity.setStorageStatus(0); // 未入库
            entity.setInvoiceStatus(0); // 未收票

            // 插入记录
            int insertCount = temporalFlowOrderInfoMapper.insertSelective(entity);
            
            if (insertCount > 0) {
                log.info("采购订单FlowOrderInfo记录创建成功: flowNodeId={}, purchaseOrderId={}, insertId={}", 
                        flowNodeId, purchaseOrderId, entity.getFlowOrderInfoId());
                return true;
            } else {
                log.warn("采购订单FlowOrderInfo记录插入失败: flowNodeId={}, purchaseOrderId={}", 
                        flowNodeId, purchaseOrderId);
                return false;
            }

        } catch (Exception e) {
            log.error("创建采购订单FlowOrderInfo记录异常: flowNodeId={}, purchaseOrderId={}", 
                    flowNodeId, purchaseOrderId, e);
            throw e;
        }
    }


    /**
     * 检查FlowOrderInfo记录是否已存在
     * 
     * @param flowNodeId 流程节点ID
     * @param businessType 业务类型
     * @return 是否已存在
     */
    public boolean checkFlowOrderInfoExists(Long flowNodeId, Integer businessType) {
        try {
            List<FlowOrderInfoEntity> existingRecords = temporalFlowOrderInfoMapper
                    .selectListByFlowNodeIdAndType(flowNodeId, businessType);
            
            boolean exists = !CollectionUtils.isEmpty(existingRecords);
            log.debug("检查FlowOrderInfo记录存在性: flowNodeId={}, businessType={}, exists={}", 
                    flowNodeId, businessType, exists);
            
            return exists;

        } catch (Exception e) {
            log.error("检查FlowOrderInfo记录存在性异常: flowNodeId={}, businessType={}", 
                    flowNodeId, businessType, e);
            return false;
        }
    }

    /**
     * 创建FlowOrderInfo实体的通用方法
     * 
     * @param flowNodeId 流程节点ID
     * @param businessType 业务类型
     * @param orderNo 订单编号
     * @return FlowOrderInfo实体
     */
    private FlowOrderInfoEntity createFlowOrderInfoEntity(Long flowNodeId, Integer businessType, 
                                                         String orderNo) {
        FlowOrderInfoEntity entity = new FlowOrderInfoEntity();

        // 基本信息
        entity.setFlowOrderInfoType(businessType);
        entity.setFlowOrderInfoNo(orderNo);
        entity.setFlowNodeId(flowNodeId);

        // 系统字段
        entity.setIsDelete(0);
        entity.setCreator(1);
        entity.setCreatorName("admin");
        entity.setUpdater(1);
        entity.setUpdaterName("admin");
        entity.setAddTime(new Date());
        entity.setModTime(new Date());

        return entity;
    }

}