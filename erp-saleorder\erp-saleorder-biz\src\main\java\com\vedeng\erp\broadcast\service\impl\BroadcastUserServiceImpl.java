package com.vedeng.erp.broadcast.service.impl;

import java.util.Date;
import java.util.List;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserDetailDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserFormDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserListDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastUserQueryDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastRAedUserEntity;
import com.vedeng.erp.broadcast.mapdao.BroadcastRAedUserMapper;
import com.vedeng.erp.broadcast.service.BroadcastUserService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 播报用户管理服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Service
@Slf4j
public class BroadcastUserServiceImpl implements BroadcastUserService {

    @Autowired
    private BroadcastRAedUserMapper broadcastRAedUserMapper;

    /**
     * 分页查询播报用户列表
     * 
     * @param pageParam 分页查询参数，包含分页信息和查询条件
     * @return 分页播报用户列表，包含总数、页码等分页信息
     */
    @Override
    public PageInfo<BroadCastUserListDto> getBroadcastUserListPage(PageParam<BroadCastUserQueryDto> pageParam) {
        log.info("分页查询播报用户列表，查询参数：{}", pageParam);
        
        // 获取查询条件
        BroadCastUserQueryDto queryDto = pageParam.getParam();
        if (queryDto == null) {
            queryDto = new BroadCastUserQueryDto();
        }
        
        // 设置分页参数
        Integer pageNum = pageParam.getPageNum() != null ? pageParam.getPageNum() : 1;
        Integer pageSize = pageParam.getPageSize() != null ? pageParam.getPageSize() : 20;
        PageHelper.startPage(pageNum, pageSize);
        
        // 构建查询参数
        String username = StringUtils.hasText(queryDto.getUsername()) ? queryDto.getUsername().trim() : null;
        Integer aedUserId = queryDto.getAedUserId();
        
        // 调用Mapper查询
        List<BroadCastUserListDto> result = broadcastRAedUserMapper.selectBroadcastUserListPage(username, aedUserId);
        
        // 转换为PageInfo对象
        PageInfo<BroadCastUserListDto> pageInfo = new PageInfo<>(result);
        
        log.info("查询到播报用户列表数量：{}，总记录数：{}", result != null ? result.size() : 0, pageInfo.getTotal());
        return pageInfo;
    }

    /**
     * 获取全量AED销售列表
     * 
     * @param username 可选的用户名搜索参数，支持模糊查询
     * @return AED销售列表
     */
    @Override
    public List<BroadCastUserDetailDto.BroadCastAedUserDto> getAllAedUserList(String username) {
        log.info("获取全量AED销售列表，搜索参数：{}", username);
        
        // 设置分页参数，获取前1000条记录
        PageHelper.startPage(1, 1000);
        
        // 构建查询参数
        String searchUsername = StringUtils.hasText(username) ? username.trim() : null;
        
        List<BroadCastUserDetailDto.BroadCastAedUserDto> result = broadcastRAedUserMapper.selectAllAedUserList(searchUsername);
        
        log.info("查询到AED销售数量：{}", result != null ? result.size() : 0);
        return result;
    }
    
    /**
     * 保存播报用户关联关系
     * 新增播报用户与AED销售的关联关系
     * 
     * @param formDto 播报用户表单数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBroadcastUser(BroadCastUserFormDto formDto) {
        log.info("新增播报用户关联关系，参数：{}", formDto);
        
        if (formDto == null) {
            throw new ServiceException("表单数据不能为空");
        }
        
        if (formDto.getErpUserId() == null) {
            throw new ServiceException("ERP用户ID不能为空");
        }
        
        if (formDto.getAedUserId() == null) {
            throw new ServiceException("AED用户ID不能为空");
        }
        
        // 检查erpUserId是否已存在（忽略已删除的记录）
        BroadcastRAedUserEntity existEntity = broadcastRAedUserMapper.selectByErpUserId(formDto.getErpUserId());
        if (existEntity != null) {
            throw new ServiceException("该ERP用户已存在播报配置，请勿重复添加");
        }
        
        // 构建实体对象
        BroadcastRAedUserEntity entity = new BroadcastRAedUserEntity();
        entity.setErpUserId(formDto.getErpUserId());
        entity.setAedUserId(formDto.getAedUserId());
        entity.setIsDeleted(0);
        entity.setAddTime(new Date());
        entity.setModTime(new Date());
        
        // 设置创建人和更新人
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (currentUser != null) {
            entity.setCreator(currentUser.getId());
            entity.setUpdater(currentUser.getId());
        }
        
        // 调用Mapper插入
        int result = broadcastRAedUserMapper.insertSelective(entity);
        
        if (result <= 0) {
            throw new ServiceException("新增播报用户关联关系失败");
        }
        
        log.info("新增播报用户关联关系成功，生成ID：{}", entity.getId());
    }
    
    /**
     * 更新播报用户关联关系
     * 编辑已存在的播报用户与AED销售的关联关系
     * 
     * @param formDto 播报用户表单数据，必须包含ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBroadcastUser(BroadCastUserFormDto formDto) {
        log.info("更新播报用户关联关系，参数：{}", formDto);
        
        if (formDto == null) {
            throw new ServiceException("表单数据不能为空");
        }
        
        if (formDto.getId() == null) {
            throw new ServiceException("ID不能为空");
        }
        
        if (formDto.getErpUserId() == null) {
            throw new ServiceException("ERP用户ID不能为空");
        }
        
        if (formDto.getAedUserId() == null) {
            throw new ServiceException("AED用户ID不能为空");
        }
        
        // 检查记录是否存在
        BroadcastRAedUserEntity existEntity = broadcastRAedUserMapper.selectByPrimaryKey(formDto.getId());
        if (existEntity == null || existEntity.getIsDeleted() == 1) {
            throw new ServiceException("播报用户关联关系不存在");
        }
        
        // 检查erpUserId是否被修改（编辑时不允许修改erpUserId）
        if (!existEntity.getErpUserId().equals(formDto.getErpUserId())) {
            throw new ServiceException("编辑时不允许修改ERP用户，只能修改AED销售");
        }
        
        // 构建更新实体对象（只更新aedUserId和修改时间、更新人）
        BroadcastRAedUserEntity entity = new BroadcastRAedUserEntity();
        entity.setId(formDto.getId());
        entity.setAedUserId(formDto.getAedUserId());
        entity.setModTime(new Date());
        
        // 设置更新人
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (currentUser != null) {
            entity.setUpdater(currentUser.getId());
        }
        
        // 调用Mapper更新
        int result = broadcastRAedUserMapper.updateByPrimaryKeySelective(entity);
        
        if (result <= 0) {
            throw new ServiceException("更新播报用户关联关系失败");
        }
        
        log.info("更新播报用户关联关系成功，ID：{}", formDto.getId());
    }
    
    /**
     * 删除播报用户关联关系
     * 逻辑删除播报用户与AED销售的关联关系
     * 
     * @param id 播报用户关联关系ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBroadcastUser(Integer id) {
        log.info("删除播报用户关联关系，ID：{}", id);
        
        if (id == null) {
            throw new ServiceException("ID不能为空");
        }
        
        // 检查记录是否存在
        BroadcastRAedUserEntity existEntity = broadcastRAedUserMapper.selectByPrimaryKey(id);
        if (existEntity == null || existEntity.getIsDeleted() == 1) {
            throw new ServiceException("播报用户关联关系不存在");
        }
        
        // 调用Mapper进行逻辑删除
        int result = broadcastRAedUserMapper.deleteByPrimaryKey(id);
        
        if (result <= 0) {
            throw new ServiceException("删除播报用户关联关系失败");
        }
        
        log.info("删除播报用户关联关系成功，ID：{}", id);
    }
} 