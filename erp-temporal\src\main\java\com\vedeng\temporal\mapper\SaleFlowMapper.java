package com.vedeng.temporal.mapper;

import com.vedeng.temporal.domain.dto.SaleFlowDto;
import com.vedeng.temporal.dto.SaleOrderAddressDto;
import org.apache.ibatis.annotations.Param;

public interface SaleFlowMapper {
    SaleFlowDto selectSaleFlowWithSkuAndPrice(@Param("flowOrderId") Long flowOrderId, @Param("nodeLevel") Integer nodeLevel);

    SaleOrderAddressDto queryAddressByAddressId(@Param("addressId")Integer addressId);
} 