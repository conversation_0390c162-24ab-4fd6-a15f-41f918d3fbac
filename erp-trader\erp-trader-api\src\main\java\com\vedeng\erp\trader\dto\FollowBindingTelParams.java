package com.vedeng.erp.trader.dto;

import lombok.Data;

import java.util.List;

/**
 * 跟进记录绑定沟通
 * @ClassName:  FollowBindingTelParams   
 * @author: <PERSON>.yang
 * @date:   2025年7月4日 下午4:16:21    
 * @Copyright:
 */
@Data
public class FollowBindingTelParams {
	
    /**沟通记录主键id列表*/
    private List<Integer> communicateRecordIdList;

    /**沟通记录类型-字典值-(244商机，4109线索)*/
    private Integer communicateType;

    /**关联表的主键id（如：商机id，线索id）*/
    private Integer relatedId;


}