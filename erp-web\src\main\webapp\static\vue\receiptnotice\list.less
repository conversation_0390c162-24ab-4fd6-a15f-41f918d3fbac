.scrollbar() {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background: #D7DADE;
        width: 6px;
        height: 6px;
        border-radius: 3px;

        &:hover {
            background: #BABFC2;
        }

        &:active {
            background: #969B9E;
        }
    }
}


.list-container {
    max-width: 1680px;
    min-width: 1200px;
    margin: 0 auto;
    padding: 20px;

    .list-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #fff;
        padding: 20px;
        padding-bottom: 10px;

        .title {
            font-size: 20px;
            font-weight: 700;
        }

        .list-top-options {
            display: flex;
            align-items: center;

            .vd-ui-button {
                margin-left: 10px;
            }
        }
    }

    .list-filter {
        background: #fff;
        padding: 10px 20px 20px;
        margin: 0 auto 20px;

        .list-row {
            display: flex;
            flex-wrap: wrap;
            align-items: center;

            .form-item {
                margin-top: 10px;

                .form-fields {
                    width: 200px;

                    .vd-ui-select {
                        width: 100%;
                    }
                }
            }

            .filter-btns {
                display: flex;
                align-items: center;
                margin-top: 10px;
            }
        }
    }

    .list-wrap {
        padding: 10px 15px;
        background: #fff;

        .list-pagination {
            margin-top: 20px;
            text-align: right;
        }
    }
}