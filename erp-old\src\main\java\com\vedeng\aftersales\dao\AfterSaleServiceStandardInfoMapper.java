package com.vedeng.aftersales.dao;

import com.newtask.dto.SkuLabelDto;
import com.vedeng.aftersales.model.AfterSaleServiceStandardInfo;
import com.vedeng.aftersales.model.dto.AfterSaleServiceStandardInfoDto;
import com.vedeng.aftersales.model.vo.ApprovedSkuAfterSalesInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterSaleServiceStandardInfoMapper {
    /**
     * 硬删除数据会导致SKU售后服务等级计算异常
     * @param serviceStandardInfoId
     * @return
     */
    int deleteByPrimaryKey(Long serviceStandardInfoId);

    int insert(AfterSaleServiceStandardInfo record);

    int insertSelective(AfterSaleServiceStandardInfo record);

    AfterSaleServiceStandardInfo selectByPrimaryKey(Long serviceStandardInfoId);

    int updateByPrimaryKeySelective(AfterSaleServiceStandardInfo record);

    int updateByPrimaryKey(AfterSaleServiceStandardInfo record);

    AfterSaleServiceStandardInfoDto selectBySkuNo(String skuNo);

    int updateBySkuNo(@Param("skuLabelDto") SkuLabelDto skuLabelDto);

    int insertIntiServiceLabels(@Param("skuLabelDto") SkuLabelDto skuLabelDto);

    /**
     * 查询售后资料 1，2，3
     */
    List<ApprovedSkuAfterSalesInfoVO> selectAllApprovedSkuAfterSalesInfo(@Param("skuNo") String skuNo);
    /**
     * 查询需要计算等级的所有资料相关的sku
     */
    List<Integer> selectAllAfterServiceSkuId();
}