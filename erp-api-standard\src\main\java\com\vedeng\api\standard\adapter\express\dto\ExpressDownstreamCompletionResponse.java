package com.vedeng.api.standard.adapter.express.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class ExpressDownstreamCompletionResponse implements Serializable {
    
    /**
     * 采购单号
     */
    private String buyOrderNo;
    
    /**
     * 采购单商品数量
     */
    private Integer buyOrderGoodsCount;
    
    /**
     * 快递商品数量
     */
    private Integer expressGoodsCount;
    
    /**
     * 是否完成发货
     */
    private Boolean isCompleted;
    
    /**
     * 检查结果描述
     */
    private String resultDescription;
}
