package com.vedeng.temporal.context;

import com.vedeng.temporal.enums.ExecutionMode;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 公司上下文计算器
 * 负责计算多公司业务流程中每个公司的执行上下文信息
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-15
 */
@Slf4j
public class CompanyContextCalculator {
    

    /**
     * 计算单个公司的执行上下文
     * 
     * @param currentCompany 当前公司
     * @param originalSequence 原始公司序列
     * @param executionSequence 执行序列
     * @param currentIndex 当前索引
     * @param reverseOrder 是否逆序执行
     * @param executionMode 执行模式
     * @return 公司执行上下文
     */
    public CompanyExecutionContext calculateSingleContext(String currentCompany,
                                                         List<String> originalSequence,
                                                         List<String> executionSequence,
                                                         int currentIndex,
                                                         boolean reverseOrder,
                                                         ExecutionMode executionMode) {
        
        // 并行执行模式下，每个公司独立执行，但需要考虑逆序参数
        if (executionMode == ExecutionMode.PARALLEL) {
            return calculateParallelContext(currentCompany, originalSequence, reverseOrder);
        }
        
        // 串行执行模式下，按原有逻辑处理
        boolean isFirst = currentIndex == 0;
        boolean isLast = currentIndex == executionSequence.size() - 1;
        
        if (reverseOrder) {
            return calculateReverseOrderContext(currentCompany, 
                                              executionSequence, currentIndex, isFirst, isLast);
        } else {
            return calculateForwardOrderContext(currentCompany, 
                                              executionSequence, currentIndex, isFirst, isLast);
        }
    }
    
    /**
     * 准备执行序列
     */
    public List<String> prepareExecutionSequence(List<String> originalSequence, boolean reverseOrder) {
        if (reverseOrder) {
            List<String> reversedSequence = new ArrayList<>(originalSequence);
            Collections.reverse(reversedSequence);
            log.debug("逆序执行：原始顺序 {} -> 逆序后 {}", originalSequence, reversedSequence);
            return reversedSequence;
        } else {
            log.debug("正序执行：保持原始公司顺序 {}", originalSequence);
            return new ArrayList<>(originalSequence);
        }
    }
    
    /**
     * 计算正序执行的上下文
     */
    private CompanyExecutionContext calculateForwardOrderContext(String currentCompany,
                                                               List<String> executionSequence,
                                                               int currentIndex,
                                                               boolean isFirst,
                                                               boolean isLast) {
        
        String previousCompany = isFirst ? null : executionSequence.get(currentIndex - 1);
        String nextCompany = isLast ? null : executionSequence.get(currentIndex + 1);
        
        String description = isFirst ? "第一个公司" : String.format("前一公司[%s]", previousCompany);
        log.info("第{}个公司",currentIndex);
        log.info("正序执行上下文计算，当前公司: {}, 前一公司: {}, 下一公司: {}",
                currentCompany, previousCompany != null ? previousCompany : "无", 
                nextCompany != null ? nextCompany : "无");
        
        return CompanyExecutionContext.builder()
                .currentCompany(currentCompany)
                .previousCompany(previousCompany)
                .nextCompany(nextCompany)
                .isFirst(isFirst)
                .isLast(isLast)
                .contextDescription(description)
                .build();
    }
    
    /**
     * 计算逆序执行的上下文
     */
    private CompanyExecutionContext calculateReverseOrderContext(String currentCompany,
                                                               List<String> executionSequence,
                                                               int currentIndex,
                                                               boolean isFirst,
                                                               boolean isLast) {
        
        String previousCompany = isFirst ? null : executionSequence.get(currentIndex - 1);
        String nextCompany = isLast ? null : executionSequence.get(currentIndex + 1);
        
        String description = isFirst ? "最后一个公司" : String.format("前一公司[%s]", previousCompany);
        
        log.debug("逆序执行上下文计算，当前公司: {}, 前一公司: {}, 下一公司: {}", 
                currentCompany, previousCompany != null ? previousCompany : "无", 
                nextCompany != null ? nextCompany : "无");
        
        return CompanyExecutionContext.builder()
                .currentCompany(currentCompany)
                .previousCompany(previousCompany)
                .nextCompany(nextCompany)
                .isFirst(isFirst)
                .isLast(isLast)
                .contextDescription(description)
                .build();
    }
    
    /**
     * 计算并行执行的上下文
     * 
     * @param currentCompany 当前公司
     * @param originalSequence 原始公司序列
     * @param reverseOrder 是否逆序执行
     * @return 公司执行上下文
     */
    private CompanyExecutionContext calculateParallelContext(String currentCompany,
                                                           List<String> originalSequence,
                                                           boolean reverseOrder) {
        
        // 根据逆序标志选择计算序列
        List<String> calculationSequence = reverseOrder ? 
            prepareExecutionSequence(originalSequence, true) : originalSequence;
        
        int indexInSeq = calculationSequence.indexOf(currentCompany);
        boolean isFirst = indexInSeq == 0;
        boolean isLast = indexInSeq == calculationSequence.size() - 1;
        
        log.debug("并行执行上下文计算，当前公司: {}, 索引: {}, 首个: {}, 末个: {}, 逆序: {}", 
                currentCompany, indexInSeq, isFirst, isLast, reverseOrder);
                
        // 计算上下游公司关系（基于计算序列）
        String previousCompany = isFirst ? null : calculationSequence.get(indexInSeq - 1);
        String nextCompany = isLast ? null : calculationSequence.get(indexInSeq + 1);
        
        log.debug("并行执行上下文计算，当前公司: {}, 上游: {}, 下游: {}, 索引: {}, 序列: {}", 
                currentCompany, previousCompany, nextCompany, indexInSeq, calculationSequence);
        
        return CompanyExecutionContext.builder()
                .currentCompany(currentCompany)
                .previousCompany(previousCompany)
                .nextCompany(nextCompany)
                .isFirst(isFirst)
                .isLast(isLast)
                .contextDescription(reverseOrder ? "并行执行模式(逆序)" : "并行执行模式(正序)")
                .build();
    }
    

    
}
