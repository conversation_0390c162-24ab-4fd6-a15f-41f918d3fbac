package com.vedeng.erp.system.service;

import com.vedeng.erp.system.dto.BaseCompanyInfoDto;

import java.util.List;

/**
 * 获取公司-子公司其本信息
 */
public interface BaseCompanyInfoApiService {

    BaseCompanyInfoDto getCurrentCompanyInfo();

    BaseCompanyInfoDto selectBaseCompanyByCompanyName(String companyName);

    /**
     * 使用简称来查询，比如 YGYX VEDENG
     * @param shortName
     * @return
     */
    BaseCompanyInfoDto selectBaseCompanyByShortName(String shortName);


    List<BaseCompanyInfoDto> selectBaseCompanyByCompanyNames(List<String> companyNames);

    List<BaseCompanyInfoDto> findAll();

    int updateByPrimaryKeySelective(BaseCompanyInfoDto baseCompanyInfoDto);


}
