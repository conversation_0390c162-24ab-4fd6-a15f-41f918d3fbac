package com.vedeng.erp.buyorder.service.impl.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.buyorder.domain.entity.*;
import com.vedeng.erp.buyorder.dto.*;
import com.vedeng.erp.buyorder.mapper.*;
import com.vedeng.erp.buyorder.mapstruct.BuyOrderGoodsConvertor;
import com.vedeng.erp.buyorder.mapstruct.OrderPaymentDetailsConvertor;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorder.service.BuyorderInfoSyncService;
import com.vedeng.erp.buyorder.service.RBuyorderExpenseJSaleorderService;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.trader.dto.TraderAddressDto;
import com.vedeng.erp.trader.service.TraderAddressApiService;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.system.dao.AddressMapper;
import com.vedeng.system.model.Address;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购单
 * @date 2023/12/7 18:05
 */
@Service
@Slf4j
public class BuyorderApiServiceImpl implements BuyorderApiService {

    @Autowired
    BuyorderMapper buyorderMapper;
    @Autowired
    BuyorderGoodsMapper buyorderGoodsMapper;
    @Autowired
    BuyOrderGoodsConvertor buyOrderGoodsConvertor;
    @Autowired
    @Qualifier("buyorderExpressMapper")
    private ExpressMapper expressMapper;
    @Autowired
    @Qualifier("buyorderExpressDetailMapper")
    ExpressDetailMapper expressDetailMapper;
    @Resource
    private BuyorderService buyorderService;
    @Autowired
    private BuyorderInfoSyncService buyorderInfoSyncService;
    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;
    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;
    @Autowired
    private RBuyorderExpenseJSaleorderService rBuyorderExpenseJSaleorderService;
    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private BuyorderActualSupplierMapper buyorderActualSupplierMapper;
    @Autowired
    private OrderPaymentDetailsMapper orderPaymentDetailsMapper;
    @Autowired
    OrderPaymentDetailsConvertor orderPaymentDetailsConvertor;

    @Autowired
    private TraderAddressApiService traderAddressApiService;

    @Autowired
    private AddressMapper addressMapper;

    /**
     * 补充收货人地址信息信息
     * @param buyOrderApiDto
     */
    private void buildTakeTraderAddress(BuyOrderApiDto buyOrderApiDto) {
        if(buyOrderApiDto.getDeliveryDirect() !=null && buyOrderApiDto.getDeliveryDirect() <1){//为0时表示普发
            Address address = addressMapper.selectByPrimaryKey(Integer.parseInt(buyOrderApiDto.getTakeTraderAddressId()));
            Integer areaId = address.getAreaId();//收件地址的区Id
            buyOrderApiDto.setTakeTraderAreaId(areaId);
            buyOrderApiDto.setTakeTraderAddress(address.getAddress());//详细地址
        }else{
            //直发订单时，收货地址ID取联系地址表的数据
            String takeTraderAddressId = buyOrderApiDto.getTakeTraderAddressId();
            TraderAddressDto traderAddressDto = traderAddressApiService.findTraderAddressById(Integer.parseInt(takeTraderAddressId));
            Integer areaId = traderAddressDto.getAreaId();//收件地址的区Id
            buyOrderApiDto.setTakeTraderAreaId(areaId);
            buyOrderApiDto.setTakeTraderAddress(traderAddressDto.getAddress());//详细地址
        }
    }

    @Override
    public BuyOrderApiDto getBuyorderByBuyorderId(Integer buyorderId) {
        BuyOrderApiDto buyOrderApiDto = buyorderMapper.findByBuyorderId(buyorderId);
        if(buyOrderApiDto == null){
            return null;
        }
        // 查询采购单商品信息
        List<BuyorderGoods> buyorderGoods = buyorderGoodsMapper.findByBuyorderId(buyorderId);
        List<BuyorderGoodsApiDto> buyorderGoodsApiDtos = buyOrderGoodsConvertor.to(buyorderGoods);
        buyOrderApiDto.setBuyorderGoodsApiDtos(buyorderGoodsApiDtos);
        
        // 查询发票信息列表
        List<BuyOrderInvoiceDto> invoiceList = buyorderMapper.getInvoiceListByBuyorderId(buyorderId);
        buyOrderApiDto.setInvoiceList(invoiceList);
        buildTakeTraderAddress(buyOrderApiDto);
        return buyOrderApiDto;
    }

    @Override
    public BuyOrderInfoDto getBuyOrderInfo(Integer buyOrderId) {
        return buyorderMapper.getBuyOrderInfo(buyOrderId);
    }

    @Override
    public BuyOrderApiDto getBuyorderByBuyorderNo(String buyorderNo) {
        BuyOrderApiDto buyOrderApiDto = buyorderMapper.findByBuyorderNo(buyorderNo);
        if(buyOrderApiDto == null){
            return null;
        }
        // 查询采购单商品信息
        List<BuyorderGoods> buyorderGoods = buyorderGoodsMapper.findByBuyorderId(buyOrderApiDto.getBuyorderId());
        List<BuyorderGoodsApiDto> buyorderGoodsApiDtos = buyOrderGoodsConvertor.to(buyorderGoods);
        buyOrderApiDto.setBuyorderGoodsApiDtos(buyorderGoodsApiDtos);
        
        // 查询发票信息列表
        List<BuyOrderInvoiceDto> invoiceList = buyorderMapper.getInvoiceListByBuyorderId(buyOrderApiDto.getBuyorderId());
        buyOrderApiDto.setInvoiceList(invoiceList);
        buildTakeTraderAddress(buyOrderApiDto);
        return buyOrderApiDto;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deliveryDirectConfirmArrival(List<Integer> expressIdList) {
        log.info("直发采购快递确认收货,入参为:{}", expressIdList);
        if (CollUtil.isEmpty(expressIdList)) {
            log.info("直发采购快递确认收货,快递id集合为空");
            return;
        }
        List<ExpressDetail> expressDetailList = expressDetailMapper.getDeliveryDirectExpressInfo(expressIdList);
        log.info("直发采购快递确认收货,查询快递明细信息为:{}", JSON.toJSONString(expressDetailList));
        if (CollUtil.isEmpty(expressIdList)) {
            log.info("直发采购快递确认收货,未查询到满足条件的快递明细信息");
            return;
        }
        Map<Integer, List<ExpressDetail>> expressDetailMap = expressDetailList.stream().collect(Collectors.groupingBy(ExpressDetail::getBuyOrderId));
        log.info("直发采购快递确认收货,分组后的快递明细信息为:{}", JSON.toJSONString(expressDetailMap));
        for (Map.Entry<Integer, List<ExpressDetail>> entry : expressDetailMap.entrySet()) {
            Integer key = entry.getKey();
            List<ExpressDetail> value = entry.getValue();
            if (Objects.isNull(key) || CollUtil.isEmpty(value)) {
                log.info("直发采购快递确认收货,分组后的key或value为空,key:{},value:{}", key, JSON.toJSONString(value));
                continue;
            }
            String concatenatedIds = value.stream()
                    .map(e -> String.format("%s_%s_%s", e.getExpressId(), e.getRelatedId(), e.getNum()))
                    .collect(Collectors.joining(","));
            this.confirmArrivalBusinessHandle(key, concatenatedIds);
        }
    }

    @Override
    public boolean isDeliveryDirectBuyOrderExpress(Integer expressId) {
        log.info("判断当前快递单是否为直发采购未收货快递,expressId:{}", expressId);
        Express express = expressMapper.isDeliveryDirectBuyOrderExpress(expressId);
        log.info("判断当前快递单是否为直发采购未收货快递,查询结果为:{}", JSON.toJSONString(express));
        return Objects.nonNull(express);
    }

    @Override
    public List<Integer> getContractReviewStatus(Integer relatedId) {
        return buyorderMapper.getContractReviewStatus(relatedId);
    }

    @Override
    public List<String> getAuditBuyorderContract(Integer relatedId) {
        List<BuyorderContractAuditDto> contractAuditInfo = buyorderMapper.getContractAuditInfo(relatedId);
        if (CollUtil.isEmpty(contractAuditInfo)){
            log.info("未查询到合同审核信息");
            return null;
        }
        List<BuyorderContractAuditDto> filterPass = contractAuditInfo.stream().filter(e -> e.getStatus().equals(1)).collect(Collectors.toList());
        if (CollUtil.isEmpty(filterPass)){
            log.info("未查询到通过合同审核信息");
            return null;
        }
        List<Long> addTimeList = filterPass.stream().map(BuyorderContractAuditDto::getAddTime).collect(Collectors.toList());
        List<BuyorderContractAttachmentDto> attachmentInfo = buyorderMapper.getAttachmentInfo(relatedId, addTimeList);
        if (CollUtil.isEmpty(attachmentInfo)){
            log.info("未查询到合同附件信息");
            return null;
        }
        List<String> file = attachmentInfo.stream().filter(e -> StrUtil.isNotBlank(e.getUrl())).map(BuyorderContractAttachmentDto::getUrl).collect(Collectors.toList());
        return file;
    }

    @Override
    public PageInfo<BuyOrderKingDeeDto> pageList(PageParam<BuyOrderKingDeeRequestDto> pageParam) {
        log.info("调用分页入参：{}",JSON.toJSON(pageParam));
        return PageHelper.startPage(pageParam).doSelectPageInfo(() -> buyorderMapper.kingDeePageSelect(pageParam.getParam()));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveOrUpdateBuyorderActualSupplier(BuyorderActualSupplierDto dto) {
        log.info("保存或更新采购订单实际供应商,入参:{}", JSON.toJSONString(dto));
        if (Objects.isNull(dto) || Objects.isNull(dto.getBuyorderId())) {
            log.info("保存或更新采购订单实际供应商,入参为空");
            return;
        }
        Integer buyorderId = dto.getBuyorderId();
        BuyorderActualSupplier buyorderActualSupplier = buyorderActualSupplierMapper.findByBuyorderId(buyorderId);
        if (Objects.isNull(buyorderActualSupplier)) {
            BuyorderActualSupplier insertEntity = new BuyorderActualSupplier();
            BeanUtil.copyProperties(dto, insertEntity);
            log.info("保存或更新采购订单实际供应商,新增实体为:{}", JSON.toJSONString(insertEntity));
            buyorderActualSupplierMapper.insertSelective(insertEntity);
        } else {
            BuyorderActualSupplier updateEntity = new BuyorderActualSupplier();
            BeanUtil.copyProperties(dto, updateEntity);
            updateEntity.setBuyorderActualSupplierId(buyorderActualSupplier.getBuyorderActualSupplierId());
            log.info("保存或更新采购订单实际供应商,更新实体为:{}", JSON.toJSONString(updateEntity));
            buyorderActualSupplierMapper.updateByPrimaryKeySelective(updateEntity);
        }
    }

    @Override
    public BuyorderActualSupplierDto getActualSupplierByBuyorderId(Integer buyorderId) {
        BuyorderActualSupplierDto dto = new BuyorderActualSupplierDto();
        BuyorderActualSupplier actualSupplier = buyorderActualSupplierMapper.findByBuyorderId(buyorderId);
        if (Objects.nonNull(actualSupplier)) {
            BeanUtil.copyProperties(actualSupplier, dto);
        }
        return dto;
    }

    @Override
    public OrderPaymentDetailsDto queryOrderPaymentDetails(Integer orderId, Integer orderType, Integer paymentMethodType) {
        return orderPaymentDetailsConvertor.toDto(orderPaymentDetailsMapper.findByOrderIdAndOrderTypeAndPaymentMethodType(orderId, orderType, paymentMethodType));
    }

    @Override
    public void insertOrderPaymentDetails(Integer orderId, Integer orderType, Integer paymentMethodType) {
        OrderPaymentDetails orderPaymentDetails = new OrderPaymentDetails();
        orderPaymentDetails.setOrderId(orderId);
        orderPaymentDetails.setOrderType(orderType);
        orderPaymentDetails.setPaymentMethodType(paymentMethodType);
        orderPaymentDetailsMapper.insertSelective(orderPaymentDetails);
    }

    @Override
    public void deleteOrderPaymentDetails(Long tOrderPaymentDetailsId) {
        orderPaymentDetailsMapper.deleteByPrimaryKey(tOrderPaymentDetailsId);
    }

    @Override
    public void updateBuyorderGoodsPrices(Integer buyorderId, List<BuyorderGoodsApiDto> goodsList) {
        if (buyorderId == null || CollUtil.isEmpty(goodsList)) {
            log.warn("更新采购单商品价格参数为空");
            return;
        }

        for (BuyorderGoodsApiDto goods : goodsList) {
            BuyorderGoods updateGoods = new BuyorderGoods();
            updateGoods.setBuyorderGoodsId(goods.getBuyorderGoodsId());
            updateGoods.setPrice(goods.getPrice());
            buyorderGoodsMapper.updateByPrimaryKeySelective(updateGoods);
        }
        log.info("已更新采购单商品价格，采购单ID: {}，更新商品数: {}", buyorderId, goodsList.size());

        // 更新采购单金额
        this.updateBuyorderAmounts(buyorderId,goodsList);
    }

    public SyncExpressDto getExpressGoodsList(String buyOrderNo,List<String> notInExpressNoList) {
        log.info("获取物流信息，采购单编号: {},notInExpressNoList：{}", buyOrderNo, notInExpressNoList);
        List<ExpressGoodsDto> expressGoodsList = buyorderMapper.getExpressGoodsNotInList(buyOrderNo,notInExpressNoList);
        SyncExpressDto syncExpressDto = new SyncExpressDto();
        if (CollUtil.isEmpty(expressGoodsList)){
            syncExpressDto.setExpressStatus(0);
            syncExpressDto.setExpressGoodsList(new ArrayList<>());
            log.info("获取物流信息，快递为空，返回结果: {}", JSON.toJSONString(syncExpressDto));
            return syncExpressDto;
        }
        syncExpressDto.setExpressGoodsList(expressGoodsList);
        syncExpressDto.setExpressStatus(1);
        syncExpressDto.setBuyOrderId(expressGoodsList.get(0).getBuyOrderId());
        log.info("获取物流信息成功，返回结果: {}", JSON.toJSONString(syncExpressDto));
        return syncExpressDto;
    }

    /**
     * 更新采购订单的总金额和预付款金额
     * 根据采购订单下所有商品的价格和数量计算总金额
     *
     * @param buyorderId 采购订单ID
     */
    public void updateBuyorderAmounts(Integer buyorderId,List<BuyorderGoodsApiDto> goodsList) {
        log.info("开始更新采购单金额，采购单ID: {}", buyorderId);
        if (buyorderId == null) {
            log.warn("更新采购单金额参数为空");
            return;
        }

        // 查询采购单信息
        BuyOrderApiDto buyOrderInfo = buyorderMapper.findByBuyorderId(buyorderId);
        if (buyOrderInfo == null) {
            log.warn("未找到采购单信息，采购单ID: {}", buyorderId);
            return;
        }


        // 计算总金额：所有商品的价格乘以数量的总和
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (BuyorderGoodsApiDto goods : goodsList) {
            if (goods.getPrice() != null && goods.getNum() != null) {
                BigDecimal itemAmount = goods.getPrice().multiply(new BigDecimal(goods.getNum()));
                totalAmount = totalAmount.add(itemAmount);
            }
        }

        // 计算预付款金额，根据付款方式确定预付款比例
        BigDecimal prepaidAmount = calculatePrepaidAmount(buyOrderInfo.getPaymentType(), totalAmount);

        // 更新采购单金额
        Buyorder updateBuyorder = new Buyorder();
        updateBuyorder.setBuyorderId(buyorderId);
        updateBuyorder.setTotalAmount(totalAmount);
        updateBuyorder.setPrepaidAmount(prepaidAmount);

        // 更新数据库
        buyorderMapper.updateByPrimaryKeySelective(updateBuyorder);

        log.info("已更新采购单金额，采购单ID: {}，总金额: {}，预付款金额: {}", buyorderId, totalAmount, prepaidAmount);
    }

    /**
     * 根据付款方式计算预付款金额
     *
     * @param paymentType 付款方式
     * @param totalAmount 总金额
     * @return 预付款金额
     */
    private BigDecimal calculatePrepaidAmount(Integer paymentType, BigDecimal totalAmount) {
        if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal prepaidRate;
        // 根据付款方式确定预付款比例
        if (paymentType == null) {
            // 默认为预付100%
            prepaidRate = BigDecimal.ONE;
        } else {
            switch (paymentType) {
                case 419: // 预付100%
                case 424: // 自定义，等同于预付100%
                    prepaidRate = BigDecimal.ONE;
                    break;
                case 3175: // 预付90%
                    prepaidRate = new BigDecimal("0.90");
                    break;
                case 420: // 预付80%
                    prepaidRate = new BigDecimal("0.80");
                    break;
                case 421: // 预付50%
                    prepaidRate = new BigDecimal("0.50");
                    break;
                case 422: // 预付30%
                    prepaidRate = new BigDecimal("0.30");
                    break;
                case 3174: // 预付10%
                    prepaidRate = new BigDecimal("0.10");
                    break;
                case 423: // 预付0%
                    prepaidRate = BigDecimal.ZERO;
                    break;
                default:
                    prepaidRate = BigDecimal.ONE; // 默认为预付100%
            }
        }

        // 计算预付款金额并保留两位小数
        return totalAmount.multiply(prepaidRate).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void confirmArrivalBusinessHandle(Integer buyOrderId, String concatenatedIds) {
        log.info("直发采购快递确认收货业务处理,buyOrderId:{},concatenatedIds:{}", buyOrderId, concatenatedIds);
        boolean checkFlag = buyorderService.checkNumByExpress(buyOrderId, concatenatedIds);
        if (!checkFlag) {
            log.info("直发采购快递确认收货业务处理,产品的发货数少于收货数");
            throw new ServiceException("产品的发货数少于收货数");
        }
        com.vedeng.order.model.Buyorder buyorder = new com.vedeng.order.model.Buyorder();
        buyorder.setBuyorderId(buyOrderId);
        com.vedeng.order.model.Buyorder resultBuyOrder = buyorderService.confirmArrival(buyorder, null, concatenatedIds, "");
        log.info("直发采购快递确认收货业务处理,采购订单确认收货:{}", JSON.toJSONString(resultBuyOrder));
        buyorderService.confirmArrivalSaveBuyorderInvoiceStatus(buyOrderId);
        log.info("直发采购快递确认收货业务处理,同步计算并更新采购单发货状态,buyOrderId:{}", buyOrderId);
        buyorderInfoSyncService.syncDeliveryStatus(buyOrderId, ErpConstant.ONE);
        BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.queryAttributiveExpenseInfoByBuyorderId(buyOrderId);
        if (Objects.nonNull(buyorderExpenseDto)) {
            buyorderExpenseApiService.doArrivalStatus(buyorderExpenseDto.getBuyorderExpenseId());
            saleOrderGoodsApiService.dosaleDeliveryStatus(buyorderExpenseDto.getBuyorderExpenseId());
            saleOrderGoodsApiService.doConfirmArrival(buyorderExpenseDto.getBuyorderExpenseId());
            log.info("直发采购快递确认收货业务处理,直发采购单确认收货同步更新直属费用单的收货状态，buyorderExpenseDto：{}", JSON.toJSONString(buyorderExpenseDto));
            List<Integer> saleOrderIds = rBuyorderExpenseJSaleorderService.findSaleOrderIds(buyorderExpenseDto.getBuyorderExpenseId());
            log.info("直发采购快递确认收货业务处理,直属费用单关联的销售订单id为:{}", saleOrderIds);
            if (CollUtil.isNotEmpty(saleOrderIds)) {
                for (Integer saleOrderId : saleOrderIds) {
                    log.info("直发采购快递确认收货业务处理,开始检验更新直属费用单关联的销售单收发货状态,销售单Id：{}", saleOrderId);
                    saleOrderApiService.checkSaleorderDeliveryAndArrivalStatus(saleOrderId);
                }
            }
        }
    }
}
