<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>新商品流列表</title>
        <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/list.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/skuPush.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/dialogSearch.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
    <style>
        .tr-item a {
            color: #2E8AE6 !important;
            text-decoration: none;
        }

        .tr-item a:hover {
            color: #FF7733 !important;
        }
    </style>
</head>
<%--Anna.liu  编辑所有--%>
<%--Ethan.lin  编辑临时--%>
<%--Ted.dong  审核--%>

<body>
    <div class="erp-wrap">
        <div class="erp-title">
            <div class="erp-title-txt">商品管理 </div>
        </div>
        <div class="tab-nav J-list-tab" data-name="">
            <a class=" tab-item <c:if  test="${empty command.spuCheckStatus or command.spuCheckStatus==-1 }"> current </c:if>"
                href="/goods/vgoods/list.do?spuCheckStatus=-1" data-value="">全部<span class="J-allCount"></span></a>

            <a class="tab-item  <c:if test="${ command.spuCheckStatus ==5}"> current </c:if>"
               href="/goods/vgoods/list.do?spuCheckStatus=5" data-value="">待提交审核<span class="J-waitToPreCount"></span></a>

            <a class="tab-item  <c:if test="${command.spuCheckStatus == 1}"> current </c:if>"
                href="/goods/vgoods/list.do?spuCheckStatus=1" data-value="">审核中<span class="J-preCount"></span></a>

            <a class="tab-item  <c:if test="${ command.spuCheckStatus ==3}"> current </c:if>"
               href="/goods/vgoods/list.do?spuCheckStatus=3" data-value="">审核通过<span class="J-approveCount"></span></a>

            <a class="tab-item  <c:if test="${command.spuCheckStatus ==2}"> current </c:if>"
               href="/goods/vgoods/list.do?spuCheckStatus=2" data-value="">审核不通过<span class="J-rejectCount"></span></a>

            <a class="tab-item  <c:if test="${command.spuCheckStatus ==0}"> current </c:if>"
               href="/goods/vgoods/list.do?spuCheckStatus=0" data-value="">待完善<span class="J-newCount"></span></a>
        </div>
        <div class="erp-top-option">
            <div class="option-btn-wrap">
                <c:if test="${financeFlag == true}">
                    <a class="btn btn-blue btn-small" href="javascript:void(0)"  onclick="uploadTaxCategoryNo()">上传税收分类编码</a>
                </c:if>
                <a class="btn btn-blue btn-small" href="javascript:void(0)"  onclick="skuMove()">SKU迁移SPU</a>
                <input type="hidden" id = 'layerIndex'>
                <c:if test="${command.hasEditAuth}">
                    <a class="btn btn-blue btn-small" tabTitle='{"num":"addnewcorespu","link":"/goods/vgoods/addSpu.do","title":"新增SPU"}'>新增SPU</a>
                </c:if>
                <%--<c:if test="${command.hasEditTempAuth}">--%>
                    <%--<a class="btn btn-blue btn-small" tabTitle='{"num":"addnewtempspu","link":"/goods/vgoods/addTempSpu.do","title":"新增临时SPU", "random": "1"}'>新增SPU(临时)</a>--%>
                <%--</c:if>--%>
                <!-- a class="btn btn-blue btn-small" layerparams='{"width":"500px","height":"200px","title":"批量商品授权与定价","link":"./batchAuthorizationPricing.do"}'>批量商品授权与定价</a> -->
            </div>
        </div>
        <div class="erp-block base-form search-wrap J-search-wrap">
            <div class="search-list">
                <div class="search-item item-search-select">
                    <div class="item-label">
                        <select name="searchType" class="J-select J-search-select">
                            <option value="1" <c:if test="${command.searchType == 1}"> selected </c:if> data-place="请输入商品名称/订货号/SPU ID/物料编号/注册证号">关键词</option>
                            <option value="2" <c:if test="${command.searchType == 2}"> selected </c:if> data-place="请输入商品名称">商品名称</option>
                            <option value="3" <c:if test="${command.searchType == 3}"> selected </c:if> data-place="请输入订货号">订货号</option>
                            <option value="4" <c:if test="${command.searchType == 4}"> selected </c:if> data-place="请输入物料编号">物料编号</option>
                            <option value="5" <c:if test="${command.searchType == 5}"> selected </c:if> data-place="请输入注册证号">注册证号</option>
                            <option value="6" <c:if test="${command.searchType == 6}"> selected </c:if> data-place="请输入SPU ID">SPU ID</option>
                        </select>
                    </div>
                    <div class="item-fields">
                        <div class="search-input-wrap item-input">
                            <input type="text" name="searchValue" autocomplete="off" class="input-text J-search-word" value="${command.searchValue}">
                            <ul class="search-history-wrap J-search-history" style="display:none;"></ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="search-list J-search-more-cnt" style="display: none;">
                <div class="search-item">
                    <div class="item-label">商品分类：</div>
                    <div class="item-fields">
                        <input type="hidden" class="J-category-value" name="categoryId" value="${command.categoryId}">
                        <div class="select-lv-wrap J-category-wrap"></div>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">商品品牌：</div>
                    <div class="item-fields fields-suggest">
                        <input type="text" class="input-text J-suggest-input" data-url="/firstengage/brand/brandName.do" name="brandName" value="${command.brandName}">
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">商品等级：</div>
                    <div class="item-fields">
                        <select name="goodsLevelNo" class="J-select">
                            <option value="">全部</option>
                            <c:forEach items="${goodsLevelList}" var="goodsLevel">
                                <option value="${goodsLevel.id}" <c:if test="${command.goodsLevelNo eq goodsLevel.id}"> selected </c:if> >${goodsLevel.uniqueIdentifier}-${goodsLevel.levelName}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">商品档位：</div>
                    <div class="item-fields">
                        <select name="goodsPositionNo" class="J-select">
                            <option value="">全部</option>
                            <c:forEach items="${goodsPositionList}" var="goodsPosition">
                                <option value="${goodsPosition.id}" <c:if test="${command.goodsPositionNo eq goodsPosition.id}"> selected </c:if> >${goodsPosition.positionName}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <%--<div class="search-item">--%>
                    <%--<div class="item-label">商品等级：</div>--%>
                    <%--<div class="item-fields">--%>
                        <%--<select name="spuLevel" class="J-select">--%>
                            <%--<option value="-1">全部</option>--%>
                            <%--<option value="0" <c:if test="${command.spuLevel == 0}"> selected </c:if> >其他产品</option>--%>
                            <%--<option value="1" <c:if test="${command.spuLevel == 1}"> selected </c:if> >核心产品</option>--%>
                            <%--<option value="2" <c:if test="${command.spuLevel == 2}"> selected </c:if> >临时产品</option>--%>
                        <%--</select>--%>
                    <%--</div>--%>
                <%--</div>--%>
                <div class="search-item">
                    <div class="item-label">商品类型：</div>
                    <div class="item-fields">
                        <select name="spuType" class="J-select">
                            <option value="-1">全部</option>
                            <c:forEach var="spuType" items="${spuTypeList}" varStatus="status">
                                <option value="${spuType.sysOptionDefinitionId }" <c:if test="${command.spuType == spuType.sysOptionDefinitionId}"> selected </c:if>
                                    >
                                    ${spuType.title}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">管理类别：</div>
                    <div class="item-fields">
                        <select name="manageCategoryLevel" class="J-select">
                            <option value="-1">全部</option>
                            <option value="968" <c:if test="${command.manageCategoryLevel == 968}"> selected </c:if>>一类</option>
                            <option value="969" <c:if test="${command.manageCategoryLevel == 969}"> selected </c:if>>二类</option>
                            <option value="970" <c:if test="${command.manageCategoryLevel == 970}"> selected </c:if>>三类</option>
                        </select>
                    </div>
                </div>
                <div class="search-item item-lv-right">
                    <div class="item-label">新国标分类：</div>
                    <div class="item-fields">
                        <input type="hidden" name="newStandardCategoryId" class="J-stand-value" value="${command.newStandardCategoryId}">
                        <div class="select-lv-wrap J-stand-wrap"></div>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">厂商：</div>
                    <div class="item-fields fields-suggest">
                        <input type="text" class="input-text J-suggest-input" data-url="/goods/vgoods/productCompany.do" name="productCompanyName" value="${command.productCompanyName}">
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">归属（经理）：</div>
                    <div class="item-fields">
                        <select name="assignmentManagerId" class="J-select">
                            <option value="-1">全部</option>
                            <c:forEach var="user" items="${assUser}">
                                <option value="${user.userId}" <c:if test="${command.assignmentManagerId == user.userId}"> selected </c:if>>${user.username}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">归属（助理）：</div>
                    <div class="item-fields">
                        <select name="assignmentAssistantId" class="J-select">
                            <option value="-1">全部</option>
                            <c:forEach var="user" items="${assUser}">
                                <option value="${user.userId}" <c:if test="${command.assignmentAssistantId == user.userId}"> selected </c:if>>${user.username}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">科室：</div>
                    <div class="item-fields fields-suggest">
                        <input type="text" class="input-text J-suggest-input" data-url="/goods/vgoods/departmentsHospital.do" name="departmentName"  value="${command.departmentName}">
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">SPU审核状态：</div>
                    <div class="item-fields">
                        <select name="spuCheckStatus" class="J-select">
                            <option value="-1">全部</option>
                            <c:forEach var="checkStatus" items="${command.checkStatus}" varStatus="status">
                                <c:if test="${checkStatus.status != 4}">
                                <option value="${checkStatus.status}" <c:if test="${command.spuCheckStatus == checkStatus.status}"> selected </c:if> >${checkStatus.name}</option>
                                </c:if>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">SKU审核状态：</div>
                    <div class="item-fields">
                        <select name="skuCheckStatus" class="J-select">
                            <option value="-1">全部</option>
                            <c:forEach var="checkStatus" items="${command.checkStatus}" varStatus="status">
                                <c:if test="${checkStatus.status != 4}">
                                <option value="${checkStatus.status}" <c:if test="${command.skuCheckStatus == checkStatus.status}"> selected </c:if>>${checkStatus.name}</option>
                                </c:if>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">更新时间：</div>
                    <div class="item-fields">
                        <div class="item-fields J-date-range">
                            <div class="input-date item-input">
                                <input type="text" name="modTimeStart" class="input-text" placeholder="请选择日期" readonly value="${command.modTimeStart}">
                            </div>
                            <div class="search-item-gap">-</div>
                            <div class="input-date item-input">
                                <input type="text" name="modTimeEnd" class="input-text" placeholder="请选择日期" readonly value="${command.modTimeEnd}">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">运营信息：</div>
                    <div class="item-fields">
                        <select name="operateInfoFlag" class="J-select">
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.operateInfoFlag == 0}"> selected </c:if> >未添加</option>
                            <option value="1" <c:if test="${command.operateInfoFlag == 1 }"> selected </c:if>>已添加</option>

                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">是否备货：</div>
                    <div class="item-fields">
                        <select name="isStockup" class="J-select">
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.isStockup == 0}"> selected </c:if> >否</option>
                            <option value="1" <c:if test="${command.isStockup == 1}"> selected </c:if> >是</option>
                        </select>
                    </div>
                </div>

                <div class="search-item">
                    <div class="item-label">推送状态：</div>
                    <div class="item-fields">
                        <select name="synchronizationStatus" class="J-select">
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.synchronizationStatus == 0}"> selected </c:if> >未推送</option>
                            <option value="1" <c:if test="${command.synchronizationStatus == 1}"> selected </c:if> >已推送</option>
                            <option value="2" <c:if test="${command.synchronizationStatus == 2}"> selected </c:if> >需重推</option>
                        </select>
                    </div>
                </div>

                <div class="search-item">
                    <div class="item-label">推送平台：</div>
                    <div class="item-fields">
                        <select name="pushStatus" class="J-select">
                            <option value="-2">全部</option>
                            <option value="7" <c:if test="${command.pushStatus == -1}"> selected </c:if> >全部推送</option>
                            <option value="1" <c:if test="${command.pushStatus == 1}"> selected </c:if> >贝登推送</option>
                            <option value="2" <c:if test="${command.pushStatus == 2}"> selected </c:if> >医械购推送</option>
                            <!-- add by Tomcat.Hui 2020/3/11 10:50 上午 .Desc: VDERP-2140 erp-新商品流-推送状态筛选项无科研购推送. start -->
                            <option value="4" <c:if test="${command.pushStatus == 4}"> selected </c:if> >科研购推送</option>
                            <option value="8" <c:if test="${command.pushStatus == 8}"> selected </c:if> >集采推送</option>
                            <option value="32" <c:if test="${command.pushStatus == 32}"> selected </c:if> >科研特麦帮推送</option>
                            <!-- add by Tomcat.Hui 2020/3/11 10:50 上午 .Desc: VDERP-2140 erp-新商品流-推送状态筛选项无科研购推送. end -->
                            <option value="0" <c:if test="${command.pushStatus == 0}"> selected </c:if> >未推送</option>
                        </select>
                    </div>
                </div>

                <div class="search-item">
                    <div class="item-label">核价审核状态：</div>
                    <div class="item-fields">
                        <select name="priceVerifyStatus" class="J-select">
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.priceVerifyStatus == 0}"> selected </c:if> >待完善</option>
                            <option value="1" <c:if test="${command.priceVerifyStatus == 1}"> selected </c:if> >审核中</option>
                            <option value="2" <c:if test="${command.priceVerifyStatus == 2}"> selected </c:if> >审核通过</option>
                            <option value="3" <c:if test="${command.priceVerifyStatus == 3}"> selected </c:if> >审核不通过</option>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">养护类型：</div>
                    <div class="item-fields">
                        <select name="curingType" class="J-select">
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.curingType == 0}"> selected </c:if> >重点养护</option>
                            <option value="1" <c:if test="${command.curingType == 1}"> selected </c:if> >一般养护</option>
                            <option value="2" <c:if test="${command.curingType == 2}"> selected </c:if> >不养护</option>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">是否必须检测报告：</div>
                    <div class="item-fields">
                        <select name="isNeedTestReprot" class="J-select">
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.isNeedTestReprot == 0}"> selected </c:if> >否</option>
                            <option value="1" <c:if test="${command.isNeedTestReprot == 1}"> selected </c:if> >是</option>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">是否套件：</div>
                    <div class="item-fields">
                        <select name="isKit" class="J-select">
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.isKit == 0}"> selected </c:if> >否</option>
                            <option value="1" <c:if test="${command.isKit == 1}"> selected </c:if> >是</option>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">是否异形品：</div>
                    <div class="item-fields">
                        <select name="isBadGoods" class="J-select">
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.isBadGoods == 0}"> selected </c:if> >否</option>
                            <option value="1" <c:if test="${command.isBadGoods == 1}"> selected </c:if> >是</option>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">是否启用多级包装：</div>
                    <div class="item-fields">
                        <select name="isEnableMultistagePackage" class="J-select">
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.isEnableMultistagePackage == 0}"> selected </c:if> >否</option>
                            <option value="1" <c:if test="${command.isEnableMultistagePackage == 1}"> selected </c:if> >是</option>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">物料编码：</div>
                    <div class="item-fields fields-suggest">
                        <input type="text" class="input-text" name="materialCode" value="${command.materialCode}">
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">是否需要报备：</div>
                    <div class="item-fields">
                        <select name="isNeedReport" class="J-select">
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.isNeedReport == 0}"> selected </c:if> >否</option>
                            <option value="1" <c:if test="${command.isNeedReport == 1}"> selected </c:if> >是</option>
                        </select>
                    </div>
                </div>

                <div class="search-item">
                    <div class="item-label">SPU启用状态：</div>
                    <div class="item-fields">
                        <select name="spuDisableStatus" class="J-select">
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.spuDisableStatus == 0}"> selected </c:if> >禁用</option>
                            <option value="1" <c:if test="${command.spuDisableStatus == 1}"> selected </c:if> >启用</option>
                            <option value="2" <c:if test="${command.spuDisableStatus == 2}"> selected </c:if> >审核中</option>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">SKU启用状态：</div>
                    <div class="item-fields">
                        <select name="skuDisableStatus" class="J-select">
                            <option value="1" <c:if test="${command.skuDisableStatus == 1}"> selected </c:if> >启用</option>
                            <option value="-1">全部</option>
                            <option value="0" <c:if test="${command.skuDisableStatus == 0}"> selected </c:if> >禁用</option>
                            <option value="2" <c:if test="${command.skuDisableStatus == 2}"> selected </c:if> >审核中</option>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">售后服务等级：</div>
                    <div class="item-fields">
                        <select name="afterSalesServiceLevel" class="J-select">
                            <option value="">全部</option>
                            <option value="5" <c:if test="${command.afterSalesServiceLevel == 5}"> selected </c:if> >五星级</option>
                            <option value="4" <c:if test="${command.afterSalesServiceLevel == 4}"> selected </c:if> >四星级</option>
                            <option value="3" <c:if test="${command.afterSalesServiceLevel == 3}"> selected </c:if> >三星级</option>
                            <option value="2" <c:if test="${command.afterSalesServiceLevel == 2}"> selected </c:if> >二星级</option>
                            <option value="1" <c:if test="${command.afterSalesServiceLevel == 1}"> selected </c:if> >一星级</option>
                            <option value="0" <c:if test="${command.afterSalesServiceLevel == 0}"> selected </c:if> >待评级</option>
                            <option value="6" <c:if test="${command.afterSalesServiceLevel == 6}"> selected </c:if> >无需评级</option>
                        </select>
                    </div>
                </div>
            </div>

            <input name="goodsLevelFromTodoList" value="${command.goodsLevelFromTodoList}" type="hidden" />
            <input name="buzTypeFromTodoList" value="${command.buzTypeFromTodoList}" type="hidden" />
            <input name="subordinateList" value="${command.subordinateList}" type="hidden" />
            <div class="search-btns">
                <div class="btn btn-small btn-blue-bd J-search">搜索</div>
                <div class="btn btn-small J-reset">重置</div>
            </div>
            <div class="search-more J-search-show-toggle">
                <span class="J-more">更多筛选条件<i class="vd-icon icon-down"></i></span>
                <span class="J-less" style="display: none;">精选筛选条件<i class="vd-icon icon-up"></i></span>
            </div>
        </div>
        <form autocomplete="off" onsubmit="return false;">
            <div class="erp-block base-form erp-block-list">
                <div class="option-wrap J-fix-wrap">
                    <div class="option-fix-wrap cf J-fix-cnt">
                        <%--<button class="btn btn-small J-prod-stock btn-disabled">批量设置备货</button>--%>
                        <button id="batchSKU" class="btn btn-small1 J-remove-spu btn-disabled" disabled>批量迁移</button>
                        <button id="batchSaveAuthorization" class="btn btn-small" title="请先选择sku" disabled>维护报备信息</button>
                        <c:if test="${currentUser.isAdmin eq 1 || currentUser.isAdmin eq 2}">
                            <button class="btn btn-small" onclick="batchAddAuthorization()">批量导入报备信息</button>
                        </c:if>
                        <shiro:hasPermission name="/vgoods/operate/getAllOrgList.do">
                            <button class="btn btn-small J-multi-push-btn">批量推送区域商城</button>
                        </shiro:hasPermission>
                        <input id="selectiveSPUIds" type="hidden" name="spuIds" >
                        <%--                    <button class="btn btn-small  ">上传税收分类编码</button>--%>
    <%--                    <button class="btn btn-small">批量商品授权与定价</button>--%>
                        <div class="option-r"></div>
                    </div>
                </div>
                <div class="list-table">
                    <div class="table-th">
                        <div class="th">
                            <div class="input-checkbox">
                                <label class="input-wrap">
                                    <input type="checkbox" class="J-select-list-all">
                                    <span class="input-ctnr"></span>
                                </label>
                            </div>
                        </div>
                        <div class="th">商品信息</div>
                        <div class="th">商品类型</div>
                        <div class="th"></div>
                        <div class="th">注册证号</div>
                        <div class="th">审核状态</div>
                        <div class="th">启用状态</div>
                        <div class="th">更新时间</div>
                        <div class="th">运营信息</div>
                        <div class="th">推送平台</div>
                        <div class="th">上下架状态</div>
                        <div class="th"></div>
                        <div class="th"></div>
                        <div class="th"></div>
                        <div class="th"></div>
                        <div class="th">操作</div>
                    </div>
                    <c:if test="${not empty list}">
                    <c:forEach var="spuVO" items="${list}" varStatus="status">
                        <div class="table-tr">
                            <div class="tr-lv1 J-item-wrap J-list">
                                <div class="tr-list">
                                    <div class="tr-item">
                                        <div class="input-checkbox">
                                            <label class="input-wrap">
                                                <input type="checkbox" class="J-select-spu" value="${spuVO.spuId}">
                                                <span class="input-ctnr"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="tr-item item-icon-wrap">
                                        <div class="line-clamp2">
                                            <a tabTitle='{"num":"vgoodsview${spuVO.spuId}","link":"/goods/vgoods/viewSpu.do?spuId=${spuVO.spuId}","title":"查看SPU"}' href="#" class="">${spuVO.spuShowName}</a>

                                        </div>
                                        <span>${spuVO.spuId}</span>
                                        <c:if test="${spuVO.firstEngageStatus==2}">
                                            <div class="tip-wrap">
                                                <i class="vd-icon icon-info2">
                                                    <div class="tip arrow-left">
                                                        <div class="tip-con">
                                                            首营信息审核不通过，请及时修改，或通知提交人。
                                                        </div>
                                                        <span class="arrow arrow-out">
                                                            <span class="arrow arrow-in"></span>
                                                        </span>
                                                    </div>
                                                </i>
                                            </div>
                                        </c:if>
                                    </div>
                                    <div class="tr-item J-spu-type" data-type="${spuVO.spuType}" style="padding-left: 1.5%">
                                        <c:forEach var="spuType" items="${spuTypeList}" varStatus="status">
                                            <c:if test="${spuVO.spuType == spuType.sysOptionDefinitionId}"> ${spuType.title} </c:if>
                                        </c:forEach>
                                    </div>
                                    <div class="tr-item" style="padding-left: 1.5%;">
                                            归属：${spuVO.productMgrName}
                                        <c:if test="${not empty spuVO.productAssistantName}">
                                            &${spuVO.productAssistantName}
                                        </c:if>

                                    </div>
                                    <div class="tr-item" style="padding-left: 1.5%">
                                        <a href="javascript:void(0);" tabTitle='{"num":"firstengage${spuVO.firstEngageId}","link":"/firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${spuVO.firstEngageId}","title":"查看首营"}' >${spuVO.registrationNumber}</a>
                                    </div>
                                    <div class="tr-item" style="padding-left: 1.5%">
                                        <c:if test="${spuVO.checkStatus==2}">
                                            <span class="  status-red">审核不通过</span>
                                        </c:if>
                                        <c:if test="${spuVO.checkStatus==1}">
                                            <span class="  status-yellow">审核中</span>
                                        </c:if>
                                        <c:if test="${spuVO.checkStatus==0}">
                                            <span class="  status-yellow">待完善</span>
                                        </c:if>
                                        <c:if test="${spuVO.checkStatus==3}">
                                            <span class="  status-green">审核通过</span>
                                        </c:if>
                                        <c:if test="${spuVO.checkStatus==5}">
                                            <span class="  status-green">待提交审核</span>
                                        </c:if>
                                    </div>
                                    <div class="tr-item" style="padding-left: 1%">
                                        <c:choose>
                                            <c:when test="${spuVO.status==1}">
                                                <span>已启用</span>
                                            </c:when>
                                            <c:when test="${spuVO.status==0}">
                                                <span>已禁用</span>
                                                <img src="/static/images/questionMark.png" alt="图标展示异常"class="Tipps" tipps-text="<span style='color: black;'>${spuVO.disabledReason}</span>">
                                            </c:when>
                                            <c:otherwise>
                                                <span>审核中</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                    <div class="tr-item" style="padding-left: 1%"> ${spuVO.modTimeShow}</div>
                                    <div class="tr-item" style="padding-left: 1.5%">${spuVO.operateInfoIdShow}</div>
                                    <div class="tr-item"></div>
                                    <div class="tr-item"></div>
                                    <div class="tr-item"></div>
                                    <div class="tr-item"></div>
                                    <div class="tr-item"></div>
                                    <div class="tr-item">
                                        <c:if test="${spuVO.spuLevel!=2 && command.hasEditAuth }" >
                                            <div class="option-select-wrap J-option-select-wrap">
                                                <div class="option-select-btn" tabtitle={"num":"vgoodseditspu${spuVO.spuId}","link":"/goods/vgoods/addSpu.do?spuId=${spuVO.spuId}","title":"编辑SPU"}>编辑 </div> <div class="option-select-icon J-option-select-icon">
                                                    <i class="vd-icon icon-down"></i>
                                                </div>
                                                <div class="option-select-list">
                                                    <div class="option-select-item" tabtitle={"num":"vgoodsaddsku${spuVO.spuId}","link":"/goods/vgoods/addSku.do?spuId=${spuVO.spuId}","title":"新增SKU"}>新增SKU </div>
<%--                                                    <div class="option-select-item" tabtitle={"num":"changeTempToCoreSpu${spuVO.spuId}","link":"/vgoods/operate/openOperate.do?spuId=${spuVO.spuId}","title":"添加运营信息"}>添加运营信息</div>--%>
                                                    <div class="option-select-item J-remove-spu" data-spuid="${spuVO.spuId}">迁移</div>
                                                    <div class="option-select-item J-invalid-spu" data-spuid="${spuVO.spuId}">禁用</div>
                                                    <div class="option-select-item J-valid-spu" data-spuid="${spuVO.spuId}">启用</div>
                                                </div>
                                            </div>
                                        </c:if>
                                        <c:if test="${spuVO.spuLevel==2 && command.hasEditTempAuth }" >
                                            <div class="option-select-wrap J-option-select-wrap">
                                                <%--<div class="option-select-btn" tabtitle={"num":"vgoodseditspu${spuVO.spuId}","link":"/goods/vgoods/addTempSpu.do?spuId=${spuVO.spuId}","title":"编辑SPU"}>编辑 </div>--%>
                                                <div class="option-select-icon J-option-select-icon">
                                                <i class="vd-icon icon-down"></i>
                                                </div>
                                                <div class="option-select-list">
                                                    <div class="option-select-item J-sku-edit" data-spuid="${spuVO.spuId}" data-spuname="${spuVO.spuShowName}">新增SKU </div>
                                                    <div class="option-select-item J-remove-spu" data-spuid="${spuVO.spuId}">迁移</div>
                                                    <c:if test="${command.hasEditAuth}">
                                                        <div class="option-select-item" tabtitle={"num":"changeTempToCoreSpu${spuVO.spuId}","link":"/goods/vgoods/changeTempToCoreSpu.do?spuId=${spuVO.spuId}","title":"转为普通商品"}  >转为普通商品</div>
                                                    </c:if>
                                                        <div class="option-select-item J-invalid-spu" data-spuid="${spuVO.spuId}">禁用</div>
                                                        <div class="option-select-item J-valid-spu" data-spuid="${spuVO.spuId}">启用</div>
                                                </div>
                                            </div>
                                        </c:if>
                                        <c:if test="${spuVO.spuLevel==2 && command.hasEditAuth && !command.hasEditTempAuth }" >
                                            <div class="option-select-wrap J-option-select-wrap">
                                                <div class="option-select-btn" tabtitle={"num":"changeTempToCoreSpu${spuVO.spuId}","link":"/goods/vgoods/changeTempToCoreSpu.do?spuId=${spuVO.spuId}","title":"转为普通商品"}>转为普通商品 </div>
                                                <div class="option-select-item J-remove-spu" data-spuid="${spuVO.spuId}">迁移</div>
                                            </div>
                                        </c:if>
                                    </div>
                                </div>
                                <%--SKU信息--%>
                                <div class="tr-lv2 J-item-wrap">
                                    <c:forEach var="skuVO" items="${spuVO.coreSkuBaseVOList}" varStatus="status">
                                        <div class="tr-list J-sku-item">
                                            <div class="tr-item">
                                                <div class="input-checkbox">
                                                    <label class="input-wrap">
                                                        <input type="checkbox" class="J-select-sku" value="${skuVO.skuId}" hasEditAuth="${skuVO.hasEditAuth}">
                                                        <span class="input-ctnr"></span>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="tr-item">
                                                <div class="line-clamp2">
                                                    <a title="<c:if test="${skuVO.showName != null }">${skuVO.showName}</c:if><c:if test="${empty skuVO.showName}">${spuVO.spuShowName}</c:if>" tabtitle={"num":"vgoodsviewsku${skuVO.skuId}","link":"/goods/vgoods/viewSku.do?skuId=${skuVO.skuId}&spuId=${skuVO.spuId}&&pageType=0","title":"查看SKU"} href="#" class="">
                                                     <c:if test="${skuVO.showName != null }">${skuVO.showName}</c:if><c:if test="${empty skuVO.showName}">${spuVO.spuShowName}</c:if>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="tr-item">
                                                <span class="item-label">订货号：</span>
                                                <span class="item-value">${skuVO.skuNo}</span>
                                            </div>
                                            <div class="tr-item">
                                                <span class="item-label">商品等级：</span>
                                                <span class="item-value">${skuVO.goodsLevelName}</span>
                                            </div>
                                            <div class="tr-item">
                                                <span class="item-label">商品档位：</span>
                                                <span class="item-value">${skuVO.goodsPositionName}</span>
                                            </div>
                                            <div class="tr-item">
                                                <c:if test="${skuVO.checkStatus==0}">
                                                    <span class="  status-yellow">待完善</span>
                                                </c:if>
                                                <c:if test="${skuVO.checkStatus==2}">
                                                    <span class="  status-red">审核不通过</span>
                                                </c:if>

                                                <c:if test="${skuVO.checkStatus==1}">
                                                    <span class="  status-yellow">审核中</span>
                                                </c:if>
                                                <c:if test="${skuVO.checkStatus==3}">
                                                    <span class="  status-green">审核通过</span>
                                                </c:if>
                                                <c:if test="${skuVO.checkStatus==5}">
                                                    <span class="  status-green">待提交审核</span>
                                                </c:if>
                                            </div>
                                            <div class="tr-item">
                                                <c:choose>
                                                    <c:when test="${skuVO.status==1}">
                                                        <span>已启用</span>
                                                    </c:when>
                                                    <c:when test="${skuVO.status==0}">
                                                        <span>已禁用</span>
                                                        <img src="/static/images/questionMark.png" alt="图标展示异常"class="Tipps" tipps-text="<span style='color: black;'>${skuVO.disabledReason}</span>">
                                                    </c:when>
                                                    <c:otherwise>
                                                        <span>审核中</span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                            <div class="tr-item"><fmt:formatDate value="${skuVO.modTime}" pattern="yyyy-MM-dd HH:mm:ss" /></div>
                                            <div class="tr-item">${skuVO.operateInfoIdShow}</div>
                                            <div class="tr-item">
                                                <c:if test="${skuVO.synchronizationStatus eq 2}">
                                                    <img src="${pageContext.request.contextPath}/static/images/wanring.jpg" width="20px" height="20px" title="需重推"/>
                                                </c:if>
                                                ${skuVO.pushStatusStr}
                                            </div>
                                            <div class="tr-item">
                                                <span class="item-value">${skuVO.onSaleStr}</span>
                                            </div>
                                            <div class="tr-item"></div>
                                            <div class="tr-item"></div>
                                            <div class="tr-item"><!--没有权限就不展示按钮-->
                                                <c:if test="${spuVO.spuLevel!=2 && command.hasEditAuth }" >
                                                    <div class="option-select-wrap J-option-select-wrap">
                                                        <div class="option-select-btn" tabtitle={"num":"vgoodseditsku${skuVO.skuId}","link":"/goods/vgoods/addSku.do?skuId=${skuVO.skuId}&spuId=${spuVO.spuId}","title":"编辑SKU"}>编辑 </div>
                                                            <div class="option-select-icon J-option-select-icon">
                                                                <i class="vd-icon icon-down"></i>
                                                            </div>
                                                        <div class="option-select-list">
                                                        <div class="option-select-item" tabtitle={"num":"changeTempToCoreSku${skuVO.skuId}","link":"/vgoods/operate/viewOperate.do?skuId=${skuVO.skuId}","title":"编辑运营信息"}>编辑运营信息</div>
                                                            <%--E级别不给复制--%>
                                                            <c:if test="${skuVO.goodsLevelNo!=5}">
                                                                <div class="option-select-item" tabtitle={"num":"vgoodscopysku${skuVO.skuId}","link":"/goods/vgoods/copySku.do?skuId=${skuVO.skuId}&spuId=${spuVO.spuId}","title":"复制SKU"}>复制SKU</div>
                                                            </c:if>

                                                            <div class="option-select-item J-invalid-sku" data-skuid="${skuVO.skuId}">禁用</div>
                                                            <div class="option-select-item J-valid-sku" data-skuid="${skuVO.skuId}">启用</div>
                                                        </div>
                                                    </div>
                                                </c:if>

                                                <c:if test="${spuVO.spuLevel==2 && command.hasEditTempAuth }" >
                                                    <div class="option-select-wrap J-option-select-wrap">
                                                        <div class="option-select-btn J-sku-edit" data-spuid="${spuVO.spuId}" data-spuname="${spuVO.spuShowName}" data-skuid="${skuVO.skuId}">编辑</div>
                                                        <div class="option-select-icon J-option-select-icon">
                                                        <i class="vd-icon icon-down"></i>
                                                        </div>
                                                        <div class="option-select-list">
                                                            <div class="option-select-item J-invalid-sku" data-skuid="${skuVO.skuId}">禁用</div>
                                                            <div class="option-select-item J-valid-sku" data-skuid="${skuVO.skuId}">启用</div>
                                                        </div>
                                                    </div>
                                                </c:if>


                                            </div>
                                        </div>
                                    </c:forEach>

                                    <div class="list-pager-wrap cf J-page-wrap"  <c:if test="${spuVO.skuTotalSize < 6}">style="display: none;"</c:if> data-auth="${command.hasEditAuth}" data-tempauth="${command.hasEditTempAuth}" data-spuid="${spuVO.spuId}" data-spuname="${spuVO.spuShowName}" data-spuwiki="${spuVO.wikiHref}" data-lv="${spuVO.spuLevel}" data-total="${spuVO.skuTotalSize}">
                                        <div class="list-pager">
                                            <div class="pager-txt"><span class="J-page-txt">1</span>/<span class="J-page-txt-total"></span></div>
                                            <a class="pager-btn pager-l disabled J-page-prev">
                                                <i class="vd-icon icon-left"></i>
                                            </a>
                                            <a class="pager-btn pager-r J-page-next">
                                                <i class="vd-icon icon-right"></i>
                                            </a>
                                        </div>
                                    </div>

                                    <c:if test="${ empty spuVO.coreSkuBaseVOList}">
                                        <!--查不到sku数据-->

                                    </c:if>
                                </div>

                            </div>
                        </div>
                    </c:forEach>
                    </c:if>
                    <c:if test="${ empty list}">
                    <div class="table-tr no-data">
                        <div><i class="vd-icon icon-caution1"></i></div>
                        没有匹配的数据
                    </div>
                    </c:if>
                </div>


                <c:if test="${page.totalPage > 1}">
                    <tags:pageNew page="${page}" />
                </c:if>
            </div>
        </form>
    </div>
    <div class="push-loading-wrap J-push-loading-wrap">
        <div class="push-loading-cnt">
            <div class="push-loading-close J-push-loading-close"><i class="vd-icon icon-delete"></i></div>
            <div class="push-loading-txt J-push-loading-txt">推送中，请耐心等待。。。</div>
            <div class="push-loading-status J-push-loading-status"></div>
            <div class="push-loading-block">
                <div class="push-block-label">推送成功（<span class="J-push-finished-num">0</span>/<span class="J-push-total-num">322</span>）</div>
            </div>
            <div class="push-loading-block">
                <div class="push-block-label">推送失败（<span class="J-push-error-num">0</span>/<span class="J-push-total-num">232</span>）</div>
                <div class="push-error-list J-push-error-list">

                </div>
            </div>
        </div>
    </div>
    <script type="text/tmpl" class="J-sku-tmpl">
        <div class="edit-sku-wrap base-form form-span-6">
            <form class="J-sku-form" style="display: none;">
                <div class="form-item">
                    <div class="form-label">SPU：</div>    
                    <div class="form-fields form-label-txt">{{=spuName}}</div>
                </div>    
                <div class="form-item">
                    <div class="form-label">
                        <span class="must">*</span>
                        {{if(isHaocai){ }}
                            规格：
                        {{ }else{ }}
                            制造商型号：     
                        {{ } }}
                    </div>    
                    <div class="form-fields">
                        <div class="form-col col-10">
                            <input class="input-text J-cnt" name="content" type="text"> 
                        </div>
                    </div>    
                </div>  
            </form>  
            <div class="dlg-loading J-sku-loading">
                <img src="${pageContext.request.contextPath}/static/new/img/loading.gif" alt="">
            </div>
        </div>
    </script>
    <script type="text/tmpl" class="J-sku-list-tmpl">
        {{ $.each(list, function(i, item){ }}
            <div class="tr-list J-sku-item">
                <div class="tr-item">
                    <div class="input-checkbox">
                        <label class="input-wrap">
                            <input type="checkbox" class="J-select-sku" value="{{=item.skuId}}">
                            <span class="input-ctnr"></span>
                        </label>
                    </div>
                </div>
                <div class="tr-item">
                    <div class="line-clamp2">
                        <a tabtitle={"num":"vgoodsviewsku{{=item.skuId}}","link":"/goods/vgoods/viewSku.do?skuId={{=item.skuId}}&&pageType=0","title":"查看SKU"} href="javascript:void(0);" class="">{{=item.showName}}</a>
                    </div>
                </div>
                <div class="tr-item">
                    <span class="item-label">订货号：</span>
                    <span class="item-value">{{=item.skuNo}}</span>
                </div>
                <div class="tr-item">
                    <span class="item-label">商品等级：</span>
                    <span class="item-value">{{=item.goodsLevelName}}</span>
                </div>
                <div class="tr-item">
                    <span class="item-label">商品档位：</span>
                    <span class="item-value">{{=item.goodsPositionName}}</span>
                </div>
                <div class="tr-item">
                    {{ if(item.checkStatus==2){ }}
                        <span class="status-red">审核不通过</span>
                    {{ }else if(item.checkStatus==1){ }}
                        <span class="status-yellow">审核中</span>

                    {{ }else if(item.checkStatus==3){ }}
                    <span class="status-green">审核通过</span>
                    {{ } }}
                </div>
                <div class="tr-item">{{=item.modTimeShow}}</div>
                <div class="tr-item">
                    {{ if(spuWiki){ }}
                    <a href="{{=spuWiki}}" target="_blank">SPU Wiki</a><br>
                    {{ } }}

                    {{ if(item.wikiHref){ }}
                    <a href="{{=item.wikiHref}}" target="_blank">SKU Wiki</a>
                    {{ } }}
                </div>
                <div class="tr-item">{{=item.operateInfoIdShow}}</div>
                 <div class="tr-item">
                     {{ if(item.synchronizationStatus==2){ }}
                         <img src="${pageContext.request.contextPath}/static/images/wanring.jpg" width="20px" height="20px" title="需重推"/>
                    {{ } }}

                    {{ if(item.pushStatus==0){ }}
                       未推送
                    {{ }else if(item.pushStatus==1){ }}
                       贝登
                    {{ }else if(item.pushStatus==2){ }}
                        医械购
                    {{ }else if(item.pushStatus==3){ }}
                       贝登/医械购
                    {{ }else if(item.pushStatus==4){ }}
                       科研购
                    {{ }else if(item.pushStatus==5){ }}
                       贝登/科研购
                    {{ }else if(item.pushStatus==6){ }}
                       医械购/科研购
                    {{ }else if(item.pushStatus==7){ }}
                       贝登/医械购/科研购
                    {{ } }}
                 </div>
                <div class="tr-item">
                    {{ if(item.pushStatus>0){ }}
                            {{=item.onSaleStr}}
                    {{ } }}
                    {{ if(item.pushStatus==0){ }}
                        -
                    {{ } }}
                </div>
                <div class="tr-item">
                    <div class="option-select-wrap J-option-select-wrap">
                        {{ if(spulv != 2 && auth){ }}
                            <div class="option-select-btn" tabtitle={"num":"vgoodseditsku{{=item.skuId}}","link":"/goods/vgoods/addSku.do?skuId={{=item.skuId}}&spuId={{=item.spuId}}","title":"编辑SKU"}>编辑</div>
                            <div class="option-select-icon J-option-select-icon">
                                <i class="vd-icon icon-down"></i>
                            </div>
                            <div class="option-select-list">

                                <div class="option-select-item" tabtitle={"num":"vgoodscopysku{{=item.skuId}}","link":"/goods/vgoods/copySku.do?skuId={{=item.skuId}}&spuId={{=item.spuId}}","title":"复制SKU"}>复制SKU</div>
                                <div class="option-select-item J-invalid-sku" data-skuid="{{=item.skuId}}">禁用</div>
                                <div class="option-select-item J-valid-sku" data-skuid="{{=item.skuId}}">启用</div>
                            </div>
                        {{ } }}

                        {{ if(spulv == 2 && tempauth){ }}
                            <div class="option-select-btn J-sku-edit" data-spuid="{{=item.spuId}}" data-spuname="{{=spuName}}" data-skuid="{{=item.skuId}}">编辑</div>
                            <div class="option-select-icon J-option-select-icon">
                                <i class="vd-icon icon-down"></i>
                            </div>
                            <div class="option-select-list">
                                <div class="option-select-item J-invalid-sku" data-skuid="{{=item.skuId}}">禁用</div>
                                <div class="option-select-item J-valid-sku" data-skuid="{{=item.skuId}}">启用</div>
                            </div>
                        {{ } }}

                    </div>
                </div>
            </div>
        {{ }) }}
   </script>
    <script type="text/tmpl" class="J-dlg-tmpl">
        <div class="del-wrap">
            <div class="del-tip">
                <i class="vd-icon icon-caution2"></i><span class="J-dlg-tip"></span>
            </div>
            <form class="J-dlg-form">
                <div class="del-cnt base-form">
                    <textarea name="content" id="" cols="30" rows="10" class="input-textarea J-dlg-cnt" placeholder="必填：请填写删除原因，最少10个字，最多300个字"></textarea>
                </div>
            </form>
        </div>
    </script>
    <script type="text/tmpl" class="J-stock-tmpl">
        <div class="del-wrap">
            <form class="J-stock-form stock-form base-form form-span-5">
                <div class="form-item">
                    <div class="form-fields">
                         <div class="input-radio">
                            <label class="input-wrap">
                                <input type="radio" name="setGoods" value="1">
                                <span class="input-ctnr"></span>是
                            </label>
                            <label class="input-wrap">
                                <input type="radio" name="setGoods" value="0">
                                <span class="input-ctnr"></span>否
                            </label>
                        </div>
                        <div class="feedback-block" wrapfor="setGoods"></div>
                    </div>
                </div>
            </form>
        </div>
    </script>
    <%--spu迁移确认页面--%>
    <script type="text/tmpl" class="J-spu-preConfirm">
         <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/meinian/layui.css">
        <form id="spuConfirmForm">
        <input type="hidden" name="categoryId" id="targetCategoryId">
        <div class="div-spu">
        <div>
            <div class="div-spu" style="overflow:auto;">
                  <table class="layui-table" id ="spuPrepareTable">
                     <colgroup>
                        <col width="100">
                        <col width="150">
                        <col>
                      </colgroup>
                      <thead>
                        <tr>
                            <th>选择</th>
                            <th>SPU名称</th>
                            <th>SPU_ID</th>
                            <th>SKU数</th>
                            <th>原始归属分类名</th>
                            <th>原始归属分类ID</th>
                            <th>目标归属分类名</th>
                            <th>目标归属分类ID</th>
                            <th>增补属性数</th>
                        </tr>
                      </thead>
                      <tbody>
                      </tbody>
                  </table>
            </div>
            <div class="div-spu" >
                <div style="float: right;">
                     <label>合计：</label>&nbsp;&nbsp;<label>SPU：</label><span id="spuCount">0</span>&nbsp;&nbsp;<label>SKU：</label><span id="skuCount">0</span>
                </div>
            </div>
        </div>

        <div class="div-spu-text">
            <div style="margin: 10px;">
                 <label><strong><p>迁移原因：</p></strong></label>
            </div>
            <div>
                <textarea rows="5" class="spu-reason-textarea" style="width: 100%;resize: none;" name="reason"></textarea>
            </div>
         </div>
       </div>
       </form>
    </script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/tips/tipps.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/lv-select.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/modules/list.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/list.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/skuPush.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/dialogSearch.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>