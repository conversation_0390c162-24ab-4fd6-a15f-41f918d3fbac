package com.vedeng.erp.broadcast.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.broadcast.domain.dto.BatchUploadResultDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastContentConfigDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastContentConfigReqDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastContentConfigRespDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity;
import com.vedeng.erp.broadcast.mapdao.BroadcastContentConfigMapper;
import com.vedeng.erp.broadcast.mapstruct.BroadcastContentConfigConvertor;
import com.vedeng.erp.broadcast.service.BroadcastContentConfigService;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.oss.service.domain.FileInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 播报内容配置服务实现
 */
@Slf4j
@Service
public class BroadcastContentConfigServiceImpl implements BroadcastContentConfigService {

    @Autowired
    private BroadcastContentConfigMapper broadcastContentConfigMapper;

    @Autowired
    private BroadcastContentConfigConvertor broadcastContentConfigConvertor;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 支持的图片格式
     */
    private static final Set<String> SUPPORTED_IMAGE_FORMATS = new HashSet<>(Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "webp"
    ));

    /**
     * 文件大小限制（5MB）
     */
    @Value("${broadcast.upload.max-file-size:5242880}")
    private Long maxFileSize;

    @Value("${oss_http}")
    private String ossHttp;

    @Override
    public PageInfo<BroadcastContentConfigRespDto> page(PageParam<BroadcastContentConfigReqDto> pageParam) {
        BroadcastContentConfigReqDto reqDto = pageParam.getParam();
        if (ObjectUtil.isNull(reqDto)) {
            reqDto = new BroadcastContentConfigReqDto();
        }

        PageHelper.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        List<BroadcastContentConfigEntity> entities = broadcastContentConfigMapper.selectByCondition(
                reqDto.getPicName(),
                reqDto.getExclusiveType(),
                reqDto.getExclusiveTargetValues(),
                reqDto.getExclusiveTargetLabels(),
                reqDto.getCreatorList(),
                reqDto.getStartAddTime(),
                reqDto.getEndAddTime()
        );

        List<BroadcastContentConfigRespDto> respDtoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(entities)) {
            for (BroadcastContentConfigEntity entity : entities) {
                BroadcastContentConfigRespDto respDto = new BroadcastContentConfigRespDto();
                BeanUtils.copyProperties(entity, respDto);
                
                // 设置专属类型名称
                if (entity.getExclusiveType() != null) {
                    switch (entity.getExclusiveType()) {
                        case 1:
                            respDto.setExclusiveTypeName("个人");
                            break;
                        case 2:
                            respDto.setExclusiveTypeName("团队");
                            break;
                        case 3:
                            respDto.setExclusiveTypeName("项目");
                            break;
                        default:
                            respDto.setExclusiveTypeName("未知");
                            break;
                    }
                }



                respDtoList.add(respDto);
            }
        }

        PageInfo<BroadcastContentConfigEntity> pageInfo = new PageInfo<>(entities);
        PageInfo<BroadcastContentConfigRespDto> result = new PageInfo<>(respDtoList);
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(respDtoList);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void add(BroadcastContentConfigDto dto) {
        BroadcastContentConfigEntity entity = broadcastContentConfigConvertor.toEntity(dto);
        entity.setIsDeleted(0);
        int result = broadcastContentConfigMapper.insertSelective(entity);
        if (result <= 0) {
            throw new ServiceException("添加播报内容配置失败");
        }
        dto.setId(entity.getId());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(BroadcastContentConfigDto dto) {
        if (dto.getId() == null) {
            throw new ServiceException("ID不能为空");
        }

        BroadcastContentConfigEntity existEntity = broadcastContentConfigMapper.selectByPrimaryKey(dto.getId());
        if (existEntity == null || existEntity.getIsDeleted() == 1) {
            throw new ServiceException("播报内容配置不存在");
        }

        // 只更新允许编辑的字段：专属类型和专属目标
        BroadcastContentConfigEntity entity = new BroadcastContentConfigEntity();
        entity.setId(dto.getId());
        entity.setExclusiveType(dto.getExclusiveType());
        entity.setExclusiveTargetValues(dto.getExclusiveTargetValues());
        entity.setExclusiveTargetLabels(dto.getExclusiveTargetLabels());

        int result = broadcastContentConfigMapper.updateByPrimaryKeySelective(entity);
        if (result <= 0) {
            throw new ServiceException("修改播报内容配置失败");
        }
    }



    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void delete(HashSet<Integer> ids) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (CollUtil.isEmpty(ids)) {
            throw new ServiceException("删除ID不能为空");
        }

        List<Integer> idList = new ArrayList<>(ids);
        int result = broadcastContentConfigMapper.batchDelete(idList, currentUser.getId());
        if (result <= 0) {
            throw new ServiceException("删除播报内容配置失败");
        }
    }

    @Override
    public BroadcastContentConfigDto get(Integer id) {
        if (id == null) {
            throw new ServiceException("ID不能为空");
        }

        BroadcastContentConfigEntity entity = broadcastContentConfigMapper.selectByPrimaryKey(id);
        if (entity == null || entity.getIsDeleted() == 1) {
            throw new ServiceException("播报内容配置不存在");
        }

        return broadcastContentConfigConvertor.toDto(entity);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public BatchUploadResultDto batchUpload(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            throw new ServiceException("请选择要上传的文件");
        }

        BatchUploadResultDto result = BatchUploadResultDto.builder()
                .totalCount(files.length)
                .successCount(0)
                .failCount(0)
                .successFiles(new ArrayList<>())
                .failFiles(new ArrayList<>())
                .configIds(new ArrayList<>())
                .build();

        for (MultipartFile file : files) {
            try {
                // 验证文件
                validateFile(file);

                // 上传文件到OSS
                FileInfo fileInfo = ossUtilsService.upload2OssByOriginalName(request, file);
                if (fileInfo.getCode() != 0) {
                    addFailFile(result, file, "文件上传失败: " + fileInfo.getMessage());
                    continue;
                }

                // 创建播报内容配置记录（使用默认值）
                BroadcastContentConfigDto configDto = createConfigFromUpload(file, fileInfo);
                add(configDto);

                // 添加成功记录
                addSuccessFile(result, file, fileInfo, configDto.getId());

            } catch (Exception e) {
                log.error("批量上传文件失败: {}", file.getOriginalFilename(), e);
                addFailFile(result, file, e.getMessage());
            }
        }

        return result;
    }

    /**
     * 验证上传文件
     */
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new ServiceException("文件不能为空");
        }

        // 验证文件大小
        if (file.getSize() > maxFileSize) {
            throw new ServiceException("文件大小不能超过 " + (maxFileSize / 1024 / 1024) + "MB");
        }

        // 验证文件格式
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new ServiceException("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename).toLowerCase();
        if (!SUPPORTED_IMAGE_FORMATS.contains(extension)) {
            throw new ServiceException("不支持的文件格式，仅支持: " + String.join(", ", SUPPORTED_IMAGE_FORMATS));
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (StrUtil.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 根据上传文件创建配置DTO（使用默认值）
     */
    private BroadcastContentConfigDto createConfigFromUpload(MultipartFile file, FileInfo fileInfo) {
        return BroadcastContentConfigDto.builder()
                .picUrl(ossHttp + fileInfo.getOssUrl())
                .picName(file.getOriginalFilename())
                .exclusiveType(0)
                .exclusiveTargetValues(null) // 默认为空，后续可编辑
                .exclusiveTargetLabels(null) // 默认为空，后续可编辑
                .build();
    }

    /**
     * 添加成功文件记录
     */
    private void addSuccessFile(BatchUploadResultDto result, MultipartFile file, FileInfo fileInfo, Integer configId) {
        BatchUploadResultDto.UploadFileInfo fileInfoDto = BatchUploadResultDto.UploadFileInfo.builder()
                .originalFileName(file.getOriginalFilename())
                .fileSize(file.getSize())
                .fileUrl(fileInfo.getOssUrl())
                .configId(configId)
                .build();

        result.getSuccessFiles().add(fileInfoDto);
        result.getConfigIds().add(configId);
        result.setSuccessCount(result.getSuccessCount() + 1);
    }

    /**
     * 添加失败文件记录
     */
    private void addFailFile(BatchUploadResultDto result, MultipartFile file, String errorMessage) {
        BatchUploadResultDto.UploadFileInfo fileInfoDto = BatchUploadResultDto.UploadFileInfo.builder()
                .originalFileName(file.getOriginalFilename())
                .fileSize(file.getSize())
                .errorMessage(errorMessage)
                .build();

        result.getFailFiles().add(fileInfoDto);
        result.setFailCount(result.getFailCount() + 1);
    }
}
