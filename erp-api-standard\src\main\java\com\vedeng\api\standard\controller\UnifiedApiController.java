package com.vedeng.api.standard.controller;

import com.vedeng.api.standard.auth.UserLookupService;
import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.core.ApiResponse;
import com.vedeng.api.standard.core.BaseResponseCode;
import com.vedeng.api.standard.core.ServiceAdapter;
import com.vedeng.api.standard.core.exception.ApiStandardException;
import com.vedeng.api.standard.core.exception.AuthenticationException;
import com.vedeng.api.standard.core.exception.BusinessException;
import com.vedeng.api.standard.duplicate.exception.IdempotencyException;
import com.vedeng.api.standard.validation.exception.ValidationException;
import com.vedeng.api.standard.factory.ServiceAdapterFactory;
import com.vedeng.api.standard.processor.RequestProcessor;
import com.vedeng.api.standard.processor.ResponseProcessor;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.authorization.model.User;
import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.UUID;

/**
 * 统一API控制器
 * 提供标准化的API接口入口，支持所有业务模块
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
@Controller
@RequestMapping("/api")
@ResponseBody
public class UnifiedApiController {
    
    private static final Logger logger = LoggerFactory.getLogger(UnifiedApiController.class);
    
    @Autowired
    private ServiceAdapterFactory serviceAdapterFactory;

    @Autowired
    private RequestProcessor requestProcessor;

    @Autowired
    private ResponseProcessor responseProcessor;

    @Autowired
    private UserLookupService userLookupService;
    
    /**
     * 统一API入口 - POST请求
     * 
     * @param version API版本号
     * @param module 模块名称
     * @param action 操作名称
     * @param requestData 请求数据
     * @param httpRequest HTTP请求
     * @param httpResponse HTTP响应
     * @return 统一响应格式
     */
    @RequestMapping(value = "/{version}/{module}/{action}", method = RequestMethod.POST)
    public ApiResponse<Object> handlePost(@PathVariable String version,
                                         @PathVariable String module,
                                         @PathVariable String action,
                                         @RequestBody Map<String, Object> requestData,
                                         HttpServletRequest httpRequest,
                                         HttpServletResponse httpResponse) {
        
        return handleRequest(version, module, action, requestData, httpRequest, httpResponse);
    }
    
    /**
     * 统一API入口 - GET请求
     * 
     * @param version API版本号
     * @param module 模块名称
     * @param action 操作名称
     * @param httpRequest HTTP请求
     * @param httpResponse HTTP响应
     * @return 统一响应格式
     */
    @RequestMapping(value = "/{version}/{module}/{action}", method = RequestMethod.GET)
    public ApiResponse<Object> handleGet(@PathVariable String version,
                                        @PathVariable String module,
                                        @PathVariable String action,
                                        HttpServletRequest httpRequest,
                                        HttpServletResponse httpResponse) {
        
        // 从请求参数中提取数据
        Map<String, Object> requestData = requestProcessor.extractParametersFromRequest(httpRequest);
        
        return handleRequest(version, module, action, requestData, httpRequest, httpResponse);
    }
    
    /**
     * 处理API请求的核心方法
     */
    private ApiResponse<Object> handleRequest(String version, String module, String action,
                                             Map<String, Object> requestData,
                                             HttpServletRequest httpRequest,
                                             HttpServletResponse httpResponse) {
        
        String requestId = UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("API请求开始: requestId={}, version={}, module={}, action={}", 
                requestId, version, module, action);
            
            // 1. 构建统一请求对象
            ApiRequest apiRequest = buildApiRequest(version, module, action, requestData, httpRequest);
            apiRequest.setRequestId(requestId);
            
            // 2. 检查是否需要身份认证
            if (isAuthRequired(module, action)) {
                performSimplifiedAuthentication(apiRequest);
            } else {
                logger.debug("接口 {}/{} 无需身份认证，跳过认证步骤", module, action);
            }
            
            // 3. 预处理请求
            requestProcessor.preProcess(apiRequest);
            
            // 4. 获取服务适配器
            ServiceAdapter serviceAdapter = serviceAdapterFactory.getServiceAdapter(module);
            if (serviceAdapter == null) {
                throw new BusinessException(BaseResponseCode.API_NOT_FOUND, "模块不存在: " + module);
            }
            
            // 5. 执行业务逻辑
            Object result = serviceAdapter.execute(action, apiRequest);
            
            // 6. 后处理响应
            Object processedResult = responseProcessor.postProcess(apiRequest, result);
            
            // 7. 构建成功响应
            ApiResponse<Object> response = ApiResponse.success(processedResult);
            response.setRequestId(requestId);

            long executionTime = System.currentTimeMillis() - startTime;
            logger.info("API请求成功: requestId={}, executionTime={}ms", requestId, executionTime);

            return response;
            
        } catch (AuthenticationException e) {
            logger.warn("API认证失败: requestId={}, error={}", requestId, e.getMessage());
            ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage());
            response.setRequestId(requestId);
            return response;

        } catch (BusinessException e) {
            logger.warn("API业务异常: requestId={}, error={}", requestId, e.getMessage());
            ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage());
            response.setRequestId(requestId);
            return response;

        } catch (ApiStandardException e) {
            logger.warn("API标准异常: requestId={}, error={}", requestId, e.getMessage());
            ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage());
            response.setRequestId(requestId);
            return response;

        } catch (ValidationException e) {
            logger.warn("API验证异常: requestId={}, error={}", requestId, e.getMessage());
            ApiResponse<Object> response = ApiResponse.error(BaseResponseCode.VALIDATION_ERROR, e.getMessage());
            response.setRequestId(requestId);
            
            long executionTime = System.currentTimeMillis() - startTime;
            logger.info("API请求验证失败: requestId={}, executionTime={}ms", requestId, executionTime);
            return response;

        } catch (IdempotencyException e) {
            logger.warn("API幂等性异常: requestId={}, error={}", requestId, e.getMessage());
            ApiResponse<Object> response = ApiResponse.error(BaseResponseCode.IDEMPOTENCY_ERROR, e.getMessage());
            response.setRequestId(requestId);
            
            long executionTime = System.currentTimeMillis() - startTime;
            logger.info("API请求幂等性异常: requestId={}, executionTime={}ms", requestId, executionTime);
            return response;

        } catch (UnsupportedOperationException e) {
            logger.warn("API不支持的操作: requestId={}, error={}", requestId, e.getMessage());
            ApiResponse<Object> response = ApiResponse.error(BaseResponseCode.OPERATION_NOT_SUPPORTED, e.getMessage());
            response.setRequestId(requestId);
            
            long executionTime = System.currentTimeMillis() - startTime;
            logger.info("API请求操作不支持: requestId={}, executionTime={}ms", requestId, executionTime);
            return response;

        } catch (IllegalArgumentException e) {
            logger.warn("API参数异常: requestId={}, error={}", requestId, e.getMessage());
            ApiResponse<Object> response = ApiResponse.error(BaseResponseCode.PARAMETER_ERROR, e.getMessage());
            response.setRequestId(requestId);
            
            long executionTime = System.currentTimeMillis() - startTime;
            logger.info("API请求参数异常: requestId={}, executionTime={}ms", requestId, executionTime);
            return response;

        } catch (Exception e) {
            logger.error("API系统异常: requestId={}", requestId, e);
            ApiResponse<Object> response = ApiResponse.error(BaseResponseCode.SYSTEM_BUSY);
            response.setRequestId(requestId);
            return response;
        }
    }
    
    /**
     * 构建统一请求对象
     */
    private ApiRequest buildApiRequest(String version, String module, String action,
                                      Map<String, Object> requestData,
                                      HttpServletRequest httpRequest) {
        
        ApiRequest apiRequest = new ApiRequest(module, action, requestData);
        apiRequest.setApiVersion(version);
        
        // 设置客户端信息
        apiRequest.setClientIp(getClientIpAddress(httpRequest));
        apiRequest.setUserAgent(httpRequest.getHeader("User-Agent"));
        
        // 设置请求头
        apiRequest.setHeaders(requestProcessor.extractHeadersFromRequest(httpRequest));
        
        // 设置当前用户
        User currentUser = getCurrentUser(httpRequest);
        apiRequest.setCurrentUser(currentUser);
        
        return apiRequest;
    }
    
    /**
     * 判断指定的模块和操作是否需要身份认证
     *
     * @param module 模块名称
     * @param action 操作名称
     * @return true表示需要认证，false表示不需要认证
     */
    private boolean isAuthRequired(String module, String action) {
        try {
            ServiceAdapter serviceAdapter = serviceAdapterFactory.getServiceAdapter(module);
            if (serviceAdapter == null) {
                logger.debug("模块 {} 不存在，默认需要认证", module);
                return true; // 模块不存在，默认需要认证（安全优先）
            }

            String[] noAuthActions = serviceAdapter.getNoAuthActions();
            boolean authRequired = !Arrays.asList(noAuthActions).contains(action);

            logger.debug("模块 {} 操作 {} 认证检查: 免认证操作={}, 需要认证={}",
                module, action, Arrays.toString(noAuthActions), authRequired);

            return authRequired;

        } catch (Exception e) {
            logger.warn("检查认证需求时发生异常，默认需要认证: module={}, action={}", module, action, e);
            return true; // 异常情况下默认需要认证（安全优先）
        }
    }

    /**
     * 执行纯动态用户认证
     * 专注于动态真实用户注入的认证机制
     */
    private void performSimplifiedAuthentication(ApiRequest apiRequest) {
        try {
            logger.debug("开始执行动态用户认证，请求ID: {}", apiRequest.getRequestId());

            // 检查是否为动态用户注入调用
            if (isDynamicUserCall(apiRequest)) {
                // 动态用户注入调用：注入真实用户
                handleDynamicUserCall(apiRequest);
            } else {
                // 普通调用：验证Session用户
                handleUserCall(apiRequest);
            }

        } catch (AuthenticationException e) {
            // 重新抛出认证异常
            throw e;
        } catch (Exception e) {
            logger.error("身份认证过程中发生未预期异常", e);
            throw new AuthenticationException(BaseResponseCode.SYSTEM_BUSY.getCode(),
                "身份认证系统异常");
        }
    }

    /**
     * 判断是否为动态用户注入调用
     */
    private boolean isDynamicUserCall(ApiRequest apiRequest) {
        // 通过请求头判断是否为动态用户注入调用
        if (apiRequest.getHeaders() != null) {
            String systemSource = apiRequest.getHeaders().get("X-System-Source");
            if (systemSource == null) {
                systemSource = apiRequest.getHeaders().get("x-system-source");
            }

            boolean isDynamic = systemSource != null && !systemSource.trim().isEmpty();
            logger.debug("动态用户注入检查: systemSource={}, isDynamic={}", systemSource, isDynamic);
            return isDynamic;
        }

        return false;
    }

    /**
     * 处理动态用户注入调用
     */
    private void handleDynamicUserCall(ApiRequest apiRequest) {
        String systemSource = apiRequest.getHeaders().get("X-System-Source");
        if (systemSource == null) {
            systemSource = apiRequest.getHeaders().get("x-system-source");
        }

        // 获取动态指定的用户信息
        User dynamicUser = getDynamicUser(apiRequest);

        if (dynamicUser != null) {
            // 使用动态指定的真实用户
            apiRequest.setCurrentUser(dynamicUser);
            logger.info("动态用户注入成功，系统来源: {}, 用户: {} ({})",
                systemSource, dynamicUser.getUsername(), dynamicUser.getRealName());
        } else {
            // 没有指定用户，要求必须指定真实用户
            logger.error("动态用户注入调用必须指定真实用户，系统来源: {}", systemSource);
            throw AuthenticationException.notLogin("动态用户注入调用必须指定真实用户");
        }
    }

    /**
     * 获取动态指定的用户信息
     */
    private User getDynamicUser(ApiRequest apiRequest) {
        Map<String, String> headers = apiRequest.getHeaders();
        if (headers == null) {
            return null;
        }

        // 获取用户标识
        String userId = headers.get("X-System-User-Id");
        if (userId == null) {
            userId = headers.get("x-system-user-id");
        }

        String userName = headers.get("X-System-User-Name");
        if (userName == null) {
            userName = headers.get("x-system-user-name");
        }

        // 如果没有指定用户信息，返回null
        if (userId == null && userName == null) {
            logger.debug("未指定动态用户信息");
            return null;
        }

        // 查询真实用户信息
        User realUser = userLookupService.findUser(userId, userName);
        if (realUser == null) {
            logger.warn("未找到指定的用户: userId={}, userName={}", userId, userName);
            return null;
        }

        // 验证用户信息
        if (!userLookupService.isValidUser(realUser)) {
            logger.warn("指定的用户信息无效: userId={}, userName={}", userId, userName);
            return null;
        }

        // 补全用户信息
        realUser = userLookupService.completeUserInfo(realUser);

        logger.debug("成功获取动态用户: userId={}, userName={}, realName={}",
            realUser.getUserId(), realUser.getUsername(), realUser.getRealName());

        return realUser;
    }

    /**
     * 处理普通用户调用
     */
    private void handleUserCall(ApiRequest apiRequest) {
        User currentUser = apiRequest.getCurrentUser();

        if (currentUser == null) {
            logger.debug("Session中未找到用户信息");
            throw AuthenticationException.notLogin("用户未登录，请先登录");
        }

        // 验证用户信息的有效性
        if (currentUser.getUserId() == null || currentUser.getUserId() <= 0) {
            logger.debug("用户信息无效，用户ID: {}", currentUser.getUserId());
            throw AuthenticationException.userNotExist("用户信息无效");
        }

        logger.debug("用户认证成功，用户: {} (ID: {})",
            currentUser.getUsername(), currentUser.getUserId());
    }
    
    /**
     * 获取当前登录用户
     */
    private User getCurrentUser(HttpServletRequest request) {
        try {
            Object userObj = request.getSession().getAttribute(ErpConst.CURR_USER);
            if (userObj instanceof User) {
                return (User) userObj;
            }
        } catch (Exception e) {
            logger.debug("获取当前用户失败", e);
        }
        return null;
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
