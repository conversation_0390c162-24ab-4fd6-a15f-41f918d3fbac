package com.vedeng.erp.broadcast.statistics.project;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptRErpDeptEntity;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptUserOverrideEntity;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptRErpDeptMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptUserOverrideMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastStatisticsMapper;
import com.vedeng.erp.common.broadcast.param.UserDefineUser;
import com.vedeng.erp.common.broadcast.param.UserOrgInfo;
import com.vedeng.erp.statistic.mapper.BroadcastStatisticAedNumMapper;

/**
 * 播报辅助类
 * @ClassName:  BroadcastHelper   
 * @author: Neil.yang
 * @date:   2025年6月6日 下午5:19:02    
 * @Copyright:
 */
@Component
public class BroadcastHelper {
	
	//日志
	public static Logger LOGGER = LoggerFactory.getLogger(BroadcastHelper.class);
	
	@Autowired
	private BroadcastDeptRErpDeptMapper broadcastDeptRErpDeptMapper;
	
	@Autowired
	private BroadcastDeptUserOverrideMapper broadcastDeptUserOverrideMapper;
	
	@Autowired
	private BroadcastDeptMapper broadcastDeptMapper;
	
	@Autowired
	private BroadcastStatisticAedNumMapper broadcastStatisticAedNumMapper;
	
	@Value("${broad_show_log:true}")
    public boolean showLog;
	
	/**
	 * #计算个人数据，<br>
	 *	 #大群统计：无需计算自定义归属条件  ，大群是所有人业绩，只在最终企微播报时，如果播报的用户有自定义配置小组的情况下，替换掉所属小组名称即可<br>
	 *   #部门统计：需要判断自定义归属条件：<br>
	 *	 	#1：获取自定义销售配置中,用户现归属于现在的部门，且原归属部门也属于现在部门，只在最终企微播报时，如果播报的用户有自定义配置小组的情况下，替换掉所属小组名称即可<br>
	 *	 	#2：获取自定义销售配置中,用户现归属于现在的部门，但原归属部门不属于现在部门，需排除掉该用户 【AND USER_ID!=xxx】<br>
     * <br>
	 * #计算小组数据和部门数据，只在大群进行<br>
	 * 	#计算小组数据时<br>
	 *		#1：获取自定义销售配置中，用户归属于现在小组的数据，进行该用户和属于erp组织的添加,因为被添加用户可能存在多个erp组织ID,增加sql: 【OR (USER_ID=xxx and ORG_ID in(xxx,xxx)) 】<br>
     *    	#2：获取自定义销售配置中，用户不归属于现在小组的数据，无论用户原归属是否属于现在小组，直接进行该用户的排除 【AND USER_ID!=xxx】<br>
     *	#计算部门数据时<br>
	 *		#1：获取自定义销售配置中，用户归属于现在部门的数据，进行该用户和属于erp组织的添加,因为被添加用户可能存在多个erp组织ID,增加sql: 【OR (USER_ID=xxx and ORG_ID in(xxx,xxx)) 】<br>
     *    	#2：获取自定义销售配置中，用户不归属于现在部门的数据，无论用户原归属是否属于现在小组，直接进行该用户的排除 【AND USER_ID!=xxx】<br>
	 * @return
	 */
	public List<UserDefineUser> getExcludeUserId(){
		List<UserDefineUser> userDefineUserList = new ArrayList<>();
		List<BroadcastDeptUserOverrideEntity> broadcastDeptUserOverrideEntityList =  broadcastDeptUserOverrideMapper.getAllBroadcastUserRelate();
		for (BroadcastDeptUserOverrideEntity broadcastDeptUserOverrideEntity : broadcastDeptUserOverrideEntityList) {
			Integer userId = broadcastDeptUserOverrideEntity.getErpUserId();
			UserDefineUser userDefineUser = new UserDefineUser();
			userDefineUser.setUserId(userId);
			Integer deptId = broadcastDeptUserOverrideEntity.getBroadcastDeptId();
			BroadcastDeptEntity broadcastDeptEntityTeam = broadcastDeptMapper.selectByPrimaryKey(deptId);
			//用户现归属小组和部门
			userDefineUser.setTeamIdNow(broadcastDeptEntityTeam.getId());
			userDefineUser.setTeamNameNow(broadcastDeptEntityTeam.getDeptName());
			BroadcastDeptEntity broadcastDeptEntityDept = broadcastDeptMapper.selectByPrimaryKey(broadcastDeptEntityTeam.getParentId());
			userDefineUser.setDeptIdNow(broadcastDeptEntityDept.getId());
			userDefineUser.setDeptNameNow(broadcastDeptEntityDept.getDeptName());
			
			//用户原归属组织
			UserOrgInfo userOrgInfo =  broadcastStatisticAedNumMapper.getUserOrgIdInfo(userId);
			if(Objects.isNull(userOrgInfo)) {
				userDefineUserList.add(userDefineUser);
				LOGGER.info("用户：{}，没有查到所属的erp部门",userId);
				continue;
			}
			String orgIds = userOrgInfo.getOrgIds();
			if(StringUtils.isEmpty(orgIds)) {
				userDefineUserList.add(userDefineUser);
				LOGGER.info("用户：{}，没有查到所属的erp部门",userId);
				continue;
			}
			//查询orgIds现在所在的组和部门
			List<Integer> orgIdList = Arrays.stream(orgIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
			List<BroadcastDeptRErpDeptEntity> broadcastDeptRErpDeptEntityList = broadcastDeptRErpDeptMapper.selectByErpDeptIdList(orgIdList);
			//没有查到组织配置的关系，无法找到用户原归属部门和小组
			if(CollectionUtils.isEmpty(broadcastDeptRErpDeptEntityList)) {
				userDefineUserList.add(userDefineUser);
				LOGGER.info("用户：{}， 没有查到所属的erp部门：{}，在播报部门配置的关系，无法找到用户原归属部门和小组",userId,JSON.toJSONString(orgIdList));
				continue;
			}
			List<Integer> teamIdOldList = new ArrayList<>();
			List<String> teamNameOldList = new ArrayList<>();
			List<Integer> deptIdOldList = new ArrayList<>();
			List<String> deptNameOldList = new ArrayList<>();
			for (BroadcastDeptRErpDeptEntity broadcastDeptRErpDeptEntity : broadcastDeptRErpDeptEntityList) {
				Integer id = broadcastDeptRErpDeptEntity.getBroadcastDeptId();
				BroadcastDeptEntity broadcastDeptEntityTeamOld = broadcastDeptMapper.selectByPrimaryKey(id);
				//用户原归属小组和部门
				Integer teamIdOld = broadcastDeptEntityTeamOld.getId();
				teamIdOldList.add(teamIdOld);
				String teamNameOld = broadcastDeptEntityTeamOld.getDeptName();
				teamNameOldList.add(teamNameOld);
				BroadcastDeptEntity broadcastDeptEntityDeptOld = broadcastDeptMapper.selectByPrimaryKey(broadcastDeptEntityTeamOld.getParentId());
				Integer deptIdOld = broadcastDeptEntityDeptOld.getId();
				deptIdOldList.add(deptIdOld);
				String deptNameOld = broadcastDeptEntityDeptOld.getDeptName();
				deptNameOldList.add(deptNameOld);
			}
			userDefineUser.setTeamIdOldList(teamIdOldList);
			userDefineUser.setTeamNameOldList(teamNameOldList);
			userDefineUser.setDeptIdOldList(deptIdOldList);
			userDefineUser.setDeptNameOldList(deptNameOldList);
			userDefineUserList.add(userDefineUser);
		}
		if(showLog) {
			LOGGER.info("获取自定义归属数据：{}",JSON.toJSONString(userDefineUserList));
		}
		return userDefineUserList;
	}
	

	
}
