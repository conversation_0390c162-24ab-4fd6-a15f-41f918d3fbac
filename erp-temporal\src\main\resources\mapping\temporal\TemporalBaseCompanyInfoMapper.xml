<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.temporal.mapper.TemporalBaseCompanyInfoMapper">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.vedeng.temporal.domain.entity.BaseCompanyInfoEntity">
        <id column="ID" property="id" />
        <result column="COMPANY_NAME" property="companyName" />
        <result column="COMPANY_SHORT_NAME" property="companyShortName" />
        <result column="FRONT_END_SEQ" property="frontEndSeq" />
        <result column="KINGDEE_ACCOUNT_CODE" property="kingdeeAccountCode" />
        <result column="BUSINESS_LICENSE" property="businessLicense" />
        <result column="COMPANY_ADDRESS" property="companyAddress" />
        <result column="CONTACT_PHONE" property="contactPhone" />
        <result column="BANK_NAME" property="bankName" />
        <result column="BANK_ACCOUNT" property="bankAccount" />
        <result column="CONTRACT_ADDRESS" property="contractAddress" />
        <result column="CUSTOMER_TRADER_ID" property="customerTraderId" />
        <result column="SUPPLIER_TRADER_ID" property="supplierTraderId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_USER" property="createUser" />
        <result column="UPDATE_USER" property="updateUser" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="ERP_DOMAIN" property="erpDomain" />
        <result column="DETAIL_JSON" property="detailJson" />
    </resultMap>
    
    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        ID, COMPANY_NAME, COMPANY_SHORT_NAME, FRONT_END_SEQ, KINGDEE_ACCOUNT_CODE, BUSINESS_LICENSE,
        COMPANY_ADDRESS, CONTACT_PHONE, BANK_NAME, BANK_ACCOUNT, CONTRACT_ADDRESS, CUSTOMER_TRADER_ID,
        SUPPLIER_TRADER_ID, CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, IS_DELETED,
        ERP_DOMAIN, DETAIL_JSON
    </sql>
    
    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM T_BASE_COMPANY_INFO
        WHERE ID = #{id}
    </select>
    
    <!-- 根据公司简称查询 -->
    <select id="selectByShortName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM T_BASE_COMPANY_INFO
        WHERE COMPANY_SHORT_NAME = #{shortName}
        AND (IS_DELETED = 0 OR IS_DELETED IS NULL)
    </select>
    
    <!-- 根据公司名称查询 -->
    <select id="selectByCompanyName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM T_BASE_COMPANY_INFO
        WHERE COMPANY_NAME = #{companyName}
        AND (IS_DELETED = 0 OR IS_DELETED IS NULL)
    </select>
    
    <!-- 查询所有有效的公司信息 -->
    <select id="findAllActive" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM T_BASE_COMPANY_INFO
        WHERE (IS_DELETED = 0 OR IS_DELETED IS NULL)
        AND COMPANY_SHORT_NAME IS NOT NULL
        AND COMPANY_SHORT_NAME != ''
        AND ERP_DOMAIN IS NOT NULL
        AND ERP_DOMAIN != ''
        ORDER BY ID
    </select>
    
    <!-- 根据公司简称查询并获取真正的traderId -->
    <select id="selectTraderIdByShortName" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT ts.TRADER_ID 
        FROM T_BASE_COMPANY_INFO bci 
        INNER JOIN T_TRADER_SUPPLIER ts ON bci.SUPPLIER_TRADER_ID = ts.TRADER_SUPPLIER_ID
        WHERE bci.COMPANY_SHORT_NAME = #{shortName}
        AND (bci.IS_DELETED = 0 OR bci.IS_DELETED IS NULL)
        AND ts.IS_ENABLE = 1
    </select>
    
</mapper>
