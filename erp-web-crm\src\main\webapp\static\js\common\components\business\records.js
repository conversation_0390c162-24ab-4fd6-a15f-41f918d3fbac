// 跟进记录
Vue.component('follow-record-list', {
    template: `
        <div class="records-panel">
            <div class="panel-title">跟进记录</div>

            <div class="panel-wrap has-fixed-btn">
                <div class="followUpRecord-records" v-show="!pageLoading">
                    <template v-if="chanceFollowUpRecordList.length || leadsFollowUpRecordList.length || visitFollowUpRecordList.length">
                        <div v-if="chanceFollowUpRecordList.length">
                            <div class="subtitle">商机阶段</div>
                            <div class="list">
                                <div class="item" v-for="(item, index) in chanceFollowUpRecordList" :key="index">
                                    <div class="row">
                                        <div class="creator">
                                            <img :src="item.creatorPic" class="icon"/>
                                            <div class="name">{{ item.creatorName }}</div>
                                        </div>
                                        <div class="time">{{ item.beginTimeDate }}</div>
                                    </div>
                                    <div class="content-suffix text-line-7">{{ item.contentSuffix }}</div>
                                    <div class="detail">
                                        <div class="record-audio-wrap">
                                            <template v-if="item.aiWindowUrl">
                                                <div class="record-audio-label">录音ID：{{ item.communicateRecordId }}</div>
                                                <div class="record-audio-play" @click="playAudio(item)">播放</div>
                                            </template>
                                            <div class="record-audio-label" v-else-if="item.coid">未接通</div>
                                        </div>
                                        <a @click="look(item)">查看详情<i class="vd-ui_icon icon-right"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="visitFollowUpRecordList.length">
                            <div class="subtitle">拜访阶段</div>
                            <div class="list">
                                <div class="item" v-for="(item, index) in visitFollowUpRecordList" :key="index">
                                    <div class="row">
                                        <div class="creator">
                                            <img :src="item.creatorPic" class="icon"/>
                                            <div class="name">{{ item.creatorName }}</div>
                                        </div>
                                        <div class="time">{{ item.beginTimeDate }}</div>
                                    </div>
                                    <div class="content-suffix text-line-7">{{ item.contentSuffix }}</div>
                                    <div class="detail">
                                        <a @click="look(item)">查看详情<i class="vd-ui_icon icon-right"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="leadsFollowUpRecordList.length">
                            <div class="subtitle">线索阶段</div>
                            <div class="list">
                                <div class="item" v-for="(item, index) in leadsFollowUpRecordList" :key="index">
                                    <div class="row">
                                        <div class="creator">
                                            <img :src="item.creatorPic || ''" onerror="this.src='/static/image/img-error.png'" class="icon"/>
                                            <div class="name">{{ item.creatorName }}</div>
                                        </div>
                                        <div class="time">{{ item.beginTimeDate }}</div>
                                    </div>
                                    <div class="content-suffix text-line-7">{{ item.contentSuffix }}</div>
                                    <div class="detail">
                                        <div class="record-audio-wrap">
                                            <template v-if="item.aiWindowUrl">
                                                <div class="record-audio-label">录音ID：{{ item.communicateRecordId }}</div>
                                                <div class="record-audio-play" @click="playAudio(item)">播放</div>
                                            </template>
                                            <div class="record-audio-label" v-else-if="item.coid">未接通</div>
                                        </div>
                                        <a @click="look(item)">查看详情<i class="vd-ui_icon icon-right"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                    
                    <div class="panel-null-data" v-else>
                        <i class="vd-ui_icon icon-info1 icon"></i>
                        <p class="font">暂无跟进记录，请及时添加</p>
                    </div>
                </div>
            </div>

            <div class="panel-btn-wrap">
                <ui-button class="btn" type="primary" @click="addRecord">添加跟进记录</ui-button>
                <div class="panel-btn-link" @click="showSelectAudio">关联录音</div>
            </div>

            <follow-record-create-dialog
                ref="followUpRecordCreate"
                :communicate-type="communicateType"
                :related-id="relatedId"
                :success-fun="createSuccess"
            ></follow-record-create-dialog>

            <follow-record-review-dialog ref="followUpRecordReview"></follow-record-review-dialog>
            <ui-dialog
                :visible.sync="isShowAiDialog"
                :title="'贝壳助理语音识别，录音ID：' + itemShow.communicateRecordId"
                width="900px"
            >
               <iframe :src="itemShow.aiWindowUrl" class="ai-dialog-iframe" v-if="isShowAiDialog"></iframe>
            </ui-dialog>

            <ui-select-audio ref="selectAudio" @success="createSuccess"></ui-select-audio>
        </div>
    `,
    props: {
        // 244商机 4109线索
        communicateType: {
            type: Number,
        },
        // 关联表的主键id（如：商机id，线索id）
        relatedId: {
            type: [Number, String],
        },
        // 客户ID
        traderId: {
            type: [Number, String],
        },
        traderName: {
            type: String
        },
        // 归属销售id
        belongerId: {
            type: [Number, String],
        },
        // 归属销售名称
        belonger: {
            type: String,
        },
        belongerPic: {
            type: String
        },
        // 客户跳转 ---
        traderNameLink: String,
        traderNameInnerLink: String,
        tycFlag: String,

        // 联系方式 ---
        contact: String,
        traderContactId: {
            type: [Number, String],
        },
        phone: String,
        telePhone: String,
    },
    data() {
        return {
            userId: '',
            pageLoading: true,
            chanceFollowUpRecordList: [], // 商机阶段列表
            leadsFollowUpRecordList: [], // 线索阶段列表
            visitFollowUpRecordList: [], //拜访阶段列表
            isShowAiDialog: false,
            itemShow: {}
        }
    },
    commputed: {
    },
    mounted() {
        this.userId = USERINFO.userId;
        this.initData();
    },
    methods: {
        async initData() {
            this.pageLoading = true;
            // 跟进记录
            let { data } = await this.$axios.post('/crm/followUpRecord/profile/page', {
                communicateType: this.communicateType,
                relatedId: this.relatedId
            });
            if (data.success) {
                this.chanceFollowUpRecordList = data.data.chanceFollowUpRecordList || []; // 商机阶段跟进记录列表
                this.visitFollowUpRecordList = data.data.visitFollowUpRecordList || []; // 拜访阶段跟进记录列表
                this.leadsFollowUpRecordList = data.data.leadsFollowUpRecordList || []; // 线索阶段跟进记录列表
            }
            // else {
            //     this.$message.error(data.message);
            // }
            this.pageLoading = false;
        },
        createSuccess(data) {
            this.initData();
            this.$emit('addrecord', data)
        },
        addRecord() {
            if (GLOBAL.auth('C0110')) {
                this.$refs.followUpRecordCreate.open({
                    relatedId: this.relatedId,
                    traderId: this.traderId,
                    traderName: this.traderName,
                    belongerId: this.belongerId,
                    belonger: this.belonger,
                    belongerPic: this.belongerPic,

                    traderNameLink: this.traderNameLink,
                    traderNameInnerLink: this.traderNameInnerLink,
                    tycFlag: this.tycFlag,

                    contact: this.contact,
                    traderContactId: this.traderContactId,
                    phone: this.phone,
                    telephone: this.telePhone,
                });
            } else {
                GLOBAL.showNoAuth();
            }
        },
        look(item) {
            this.$refs.followUpRecordReview.open(item);
        },
        playAudio(item) {
            this.isShowAiDialog = true;
            this.itemShow = item;
        },
        showSelectAudio() {
            if (GLOBAL.auth('C0110')) {
                this.$refs.selectAudio.show({
                    phone: this.phone,
                    relatedId: this.relatedId,
                    communicateType: this.communicateType
                });
            }
        }
    }
})

// 跟进记录列表-弹层
Vue.component('follow-record-list-dialog', {
    template: `
        <div>
            <ui-dialog
                :visible.sync="isShow"
                title="跟进记录"
                width="720px"
                align="center"
            >
                <div class="add-record">
                    <ui-button class="btn" type="primary" @click="addRecord">添加跟进记录</ui-button>
                    <div class="audio-link-btn" @click="showSelectAudio">关联录音</div>
                </div>

                <div class="records-panel">
                    <div class="panel-wrap dialog-inner">
                        <div class="followUpRecord-records">
                            <template v-if="chanceFollowUpRecordList.length || leadsFollowUpRecordList.length || visitFollowUpRecordList.length">
                                <div v-if="chanceFollowUpRecordList.length">
                                    <div class="subtitle">商机阶段</div>
                                    <div class="list">
                                        <div class="item" v-for="(item, index) in chanceFollowUpRecordList" :key="index">
                                            <div class="row">
                                                <div class="creator">
                                                    <img :src="item.creatorPic" class="icon"/>
                                                    <div class="name">{{ item.creatorName }}</div>
                                                </div>
                                                <div class="time">{{ item.beginTimeDate }}</div>
                                            </div>
                                            <div class="content-suffix text-line-7">{{ item.contentSuffix }}</div>
                                            <div class="detail">
                                                <div class="record-audio-wrap">
                                                    <template v-if="item.aiWindowUrl">
                                                        <div class="record-audio-label">录音ID：{{ item.communicateRecordId }}</div>
                                                        <div class="record-audio-play" @click="playAudio(item)">播放</div>
                                                    </template>
                                                    <div class="record-audio-label" v-else-if="item.coid">未接通</div>
                                                </div>
                                                <a @click="look(item)">查看详情<i class="vd-ui_icon icon-right"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="visitFollowUpRecordList.length">
                                    <div class="subtitle">拜访阶段</div>
                                    <div class="list">
                                        <div class="item" v-for="(item, index) in visitFollowUpRecordList" :key="index">
                                            <div class="row">
                                                <div class="creator">
                                                    <img :src="item.creatorPic" class="icon"/>
                                                    <div class="name">{{ item.creatorName }}</div>
                                                </div>
                                                <div class="time">{{ item.beginTimeDate }}</div>
                                            </div>
                                            <div class="content-suffix text-line-7">{{ item.contentSuffix }}</div>
                                            <div class="detail">
                                                <a @click="look(item)">查看详情<i class="vd-ui_icon icon-right"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="leadsFollowUpRecordList.length">
                                    <div class="subtitle">线索阶段</div>
                                    <div class="list">
                                        <div class="item" v-for="(item, index) in leadsFollowUpRecordList" :key="index">
                                            <div class="row">
                                                <div class="creator">
                                                    <img :src="item.creatorPic || ''" onerror="this.src='/static/image/img-error.png'" class="icon"/>
                                                    <div class="name">{{ item.creatorName }}</div>
                                                </div>
                                                <div class="time">{{ item.beginTimeDate }}</div>
                                            </div>
                                            <div class="content-suffix text-line-7">{{ item.contentSuffix }}</div>
                                            <div class="detail">
                                                <div class="record-audio-wrap">
                                                    <template v-if="item.aiWindowUrl">
                                                        <div class="record-audio-label">录音ID：{{ item.communicateRecordId }}</div>
                                                        <div class="record-audio-play" @click="playAudio(item)">播放</div>
                                                    </template>
                                                    <div class="record-audio-label" v-else-if="item.coid">未接通</div>
                                                </div>
                                                <a @click="look(item)">查看详情<i class="vd-ui_icon icon-right"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>

                            <div class="panel-null-data" v-else>
                                <i class="vd-ui_icon icon-info1 icon"></i>
                                <p class="font">暂无跟进记录，请及时添加</p>
                            </div>
                        </div>
                    </div>
                </div>
            </ui-dialog>

            <follow-record-create-dialog
                ref="followUpRecordDialog"
                :communicate-type="communicateType"
                :success-fun="createSuccess"
            ></follow-record-create-dialog>

            <follow-record-review-dialog ref="followUpRecordReview"></follow-record-review-dialog>

            <ui-dialog
                :visible.sync="isShowAiDialog"
                :title="'贝壳助理语音识别，录音ID：' + itemShow.communicateRecordId"
                width="900px"
            >
                <iframe :src="itemShow.aiWindowUrl" class="ai-dialog-iframe" v-if="isShowAiDialog"></iframe>
            </ui-dialog>
            <ui-select-audio ref="selectAudio" @success="createSuccess"></ui-select-audio>
        </div>
    `,

    props: {
        communicateType: {
            type: Number, // 244商机 4109线索
        },
        refreshDetailList: Function
    },
    data() {
        return {
            userId: '', // 操作人id
            isShow: false,
            relatedId: '', // 关联表的主键id（如：商机id，线索id）
            chanceFollowUpRecordList: [], // 商机阶段列表
            visitFollowUpRecordList: [], //拜访阶段
            leadsFollowUpRecordList: [], // 线索阶段列表
            creatQuery: {}, // 创建记录-所用参数
            isShowAiDialog: false,
            itemShow: {},
        }
    },
    mounted() {
        this.userId = USERINFO.userId;
    },
    methods: {
        initData() {
            GLOBAL.showGlobalLoading();
            this.$axios.post('/crm/followUpRecord/profile/page', {
                communicateType: this.communicateType,
                relatedId: this.relatedId
            }).then(({ data }) => {
                if (data.success) {
                    this.chanceFollowUpRecordList = data.data.chanceFollowUpRecordList || []; // 商机阶段跟进记录列表
                    this.visitFollowUpRecordList = data.data.visitFollowUpRecordList || []; // 拜访阶段跟进记录列表
                    this.leadsFollowUpRecordList = data.data.leadsFollowUpRecordList || []; // 线索阶段跟进记录列表

                    this.isShow = true;
                } else {
                    this.$message.error(data.message);
                }
                GLOBAL.hideGlobalLoading();
            });
        },
        createSuccess(data) {
            this.initData();
            // 更新外层列表
            if (this.refreshDetailList) {
                this.refreshDetailList();
            }

            this.$emit('addrecord', data);
        },
        open(query) {
            console.log('dialog prop：', query);
            this.relatedId = query.relatedId || '';
            this.creatQuery = query;
            this.initData();
        },
        close() {
            this.isShow = false;
        },

        look(item) {
            this.$refs.followUpRecordReview.open(item);
        },
        addRecord() {
            if (GLOBAL.auth('C0110')) {
                this.$refs.followUpRecordDialog.open(this.creatQuery);
            } else {
                GLOBAL.showNoAuth();
            }
        },
        playAudio(item) {
            this.isShowAiDialog = true;
            this.itemShow = item;
        },
        showSelectAudio() {
            this.$refs.selectAudio.show({
                phone: this.creatQuery.phone || '',
                relatedId: this.relatedId,
                communicateType: this.creatQuery.communicateType
            });
        }
    }
})

// 查看-跟进记录
Vue.component('follow-record-review-dialog', {
    template: `
        <div class="follow-up-record-review-dialog">
            <ui-dialog
                :visible.sync="isShow"
                title="跟进记录"
                width="960px"
            >
                <div class="form-wrap label-width-3">
                    <template v-if="info.communicateType == 5503">
                        <ui-form-item label="客户名称" :text="true">{{ info.otherData.customerName || '-' }}</ui-form-item>
                        <ui-form-item label="拜访人" :text="true">
                            <div class="user-show">
                                <img class="avatar" :src="info.otherData.visitorPic || GLOBAL.defaultAvatar" onerror="this.src='/static/image/img-error.png'" />
                                <span class="name">{{ info.otherData.visitorName || '-' }}</span>
                            </div>
                        </ui-form-item>
                        <ui-form-item label="同行人" :text="true">{{ getUserName(info.otherData.tongxingUserList) }}</ui-form-item>
                        <ui-form-item label="跟进类型" :text="true">线下拜访</ui-form-item>
                        <ui-form-item label="联系人" :text="true">{{ info.otherData.recordContactName || '-' }}</ui-form-item>
                        <ui-form-item label="手机" :text="true">{{ info.otherData.recordContactMobile || '-' }}</ui-form-item>
                        <ui-form-item label="固话" :text="true">{{ info.otherData.recordContactTele || '-' }}</ui-form-item>
                    </template>
                    <template v-else> 
                        <ui-form-item label="客户名称" :text="true">{{ info.traderName || '-' }}</ui-form-item>
                        <ui-form-item label="归属销售" :text="true">
                            <div class="user-show">
                                <img class="avatar" :src="info.belongPic || GLOBAL.defaultAvatar" onerror="this.src='/static/image/img-error.png'" />
                                <span class="name">{{ info.belongUserName || '-' }}</span>
                            </div>
                        </ui-form-item>
                        <ui-form-item label="跟进类型" :text="true">{{ FTypes[info.followUpType] || '-' }}</ui-form-item>
                        <ui-form-item label="联系人" :text="true">{{ info.contact || '-' }}</ui-form-item>
                        <ui-form-item label="手机" :text="true">{{ info.contactMob || '-' }}</ui-form-item>
                        <ui-form-item label="固话" :text="true">{{ info.telephone || '-' }}</ui-form-item>
                    </template>
                    <ui-form-item label="跟进时间" :text="true">{{ info.beginTimeDate || '-' }}</ui-form-item>
                    <ui-form-item label="跟进内容" :text="true">{{ info.contentSuffix || '-' }}
                        <div class="ai-wrap" v-if="info.aiSummaryAnalysis || info.aiTodoAnalysis">
                            <div class="ai-title">以下为 AI 生成：
                                <div class="ai-play-btn" v-if="info.aiWindowUrl" @click="isShowAiDialog = true">
                                    <div class="ai-play-btn-icon"></div>
                                    <div class="ai-play-btn-txt">播放</div>
                                </div>
                            </div>
                            <div class="ai-content" v-html="info.aiSummaryAnalysis"></div>
                            <div class="ai-content" v-html="info.aiTodoAnalysis"></div>
                        </div>
                    </ui-form-item>
                </div>

                <div class="form-wrap border-top label-width-3" v-if="info.communicateType != 5503">
                    <ui-form-item label="待跟进时间" :text="true">{{ info.nextContactDate || '-' }}</ui-form-item>
                    <ui-form-item label="待跟进事项" :text="true">{{ info.nextContactContent || '-' }}</ui-form-item>
                </div>
            </ui-dialog>
            <ui-dialog
                :visible.sync="isShowAiDialog"
                :title="'贝壳助理语音识别，录音ID：' + info.communicateRecordId"
                width="900px"
            >
               <iframe :src="info.aiWindowUrl" class="ai-dialog-iframe" v-if="isShowAiDialog"></iframe>
            </ui-dialog>
        </div>
    `,
    data() {
        return {
            isShow: false,
            FTypes: {
                5901: '电话',
                5902: '企业微信',
                5903: '其他',
                5503: '线下拜访'
            },
            communicateRecordId: '', // 跟进记录id
            info: {}, // 跟进记录详情
            isShowAiDialog: false,
        }
    },
    created() {
    },
    mounted() {
    },
    watch: {
    },
    methods: {
        open(item) {
            this.initData(item);
            this.isShow = true;
        },
        initData(item) {
            this.communicateRecordId = item.communicateRecordId;
            this.$axios.get(`/crm/followUpRecord/profile/detail?communicateRecordId=${item.communicateRecordId}`).then(({ data }) => {
                if (data.success) {
                    this.info = data.data || {};
                    this.info.aiSummaryAnalysis = this.info.aiSummaryAnalysis && this.info.aiSummaryAnalysis.replace(/\n/g, '<div class="br" style="height: 7px;"></div>');
                    this.info.aiTodoAnalysis = this.info.aiTodoAnalysis && this.info.aiTodoAnalysis.replace(/\n/g, '<div class="br" style="height: 7px;"></div>');
                } else {
                    this.$message.warn(data.message || '网络异常')
                }
            })
        },
        close() {
            this.isShow = false;
        },
        getUserName(list) {
            let names = [];
            list.forEach(item => {
                names.push(item.userName);
            });

            return names.length ? names.join(',') : '-'
        }
    },
})

// 添加-跟进记录
Vue.component('follow-record-create-dialog', {
    template: `
        <div class="follow-up-record-created-dialog">
            <ui-dialog
                :visible.sync="isShow"
                title="添加跟进记录"
                width="960px"
                align="center"
            >
                <div class="form-wrap label-width-3" v-if="isShow">
                    <ui-form-item label="客户名称" :text="true">
                        <div class="ui-col-10">
                            <div class="content tyc-icon">
                                <template v-if="otherQuery.traderName">
                                    <span 
                                        class="company blue" 
                                        v-if="otherQuery.traderNameLink"
                                        @click="GLOBAL.link({name:'客户名称', url: otherQuery.traderNameInnerLink, link: otherQuery.traderNameLink, nohost: true})"
                                    >
                                        {{ otherQuery.traderName }}
                                        <!-- <span v-if="otherQuery.tycFlag == 'Y'" @click="openTyc" class="icon"></span> -->
                                    </span>
                                    <span v-else class="company">
                                        {{ otherQuery.traderName }}
                                        <!-- <span v-if="otherQuery.tycFlag == 'Y'" @click="openTyc" class="icon"></span> -->
                                    </span>
                                </template>
                                <template v-else>-</template>
                            </div>
                        </div>
                    </ui-form-item>
                    <ui-form-item label="归属销售" :text="true">
                        <div class="user-show" v-if="otherQuery.belonger">
                            <img class="avatar" :src="otherQuery.belongerPic || ''" onerror="this.src='/static/image/img-error.png'"/>
                            <span class="name">{{otherQuery.belonger}}</span>
                        </div>
                        <div v-else>-</div>
                    </ui-form-item>
                    <ui-form-item label="跟进类型" :must="true" :text="true">
                        <div class="ui-col-8">
                            <ui-radio-group
                                :list="FollowUpTypeS"
                                :value.sync="followUpType"
                                valid="createFollowUpRecord_followUpType"
                            ></ui-radio-group>
                        </div>
                    </ui-form-item>
                    <ui-form-item label="手机">
                        <ui-phone-related
                            width="230px"
                            placeholder="仅支持11位手机号"
                            v-model="contactMob"
                            :trader-id="otherQuery.traderId"
                            @blur="phone_Blur"
                            @change="handlerPhone"
                            :error-msg="contactMobError" 
                        ></ui-phone-related>
                    </ui-form-item>
                    <ui-form-item label="固话">
                        <ui-input 
                            type="number" width="230px" maxlength="20"
                            placeholder="需要输入区号最多支持20位"
                            v-model="telephone" @blur="contact_Blur"
                        ></ui-input>
                    </ui-form-item>
                    <ui-form-item label="联系人" :must="true">
                        <ui-input 
                            v-model="contact" 
                            width="230px" maxlength="20" 
                            @change="handlerContact"
                            valid="createFollowUpRecord_contact"
                        ></ui-input>
                    </ui-form-item>
                    <ui-form-item label="跟进时间" :must="true">
                        <ui-date-picker
                            type="datetime"
                            :append-to-body="true"
                            width="350px"
                            v-model="beginTimeDate"
                            placeholder=""
                            valid="createFollowUpRecord_beginTimeDate"
                        ></ui-date-picker>
                    </ui-form-item>
                    <ui-form-item label="跟进内容" :must="true">
                        <ui-input
                            width="823px"
                            type="textarea"
                            maxlength="200"
                            showWordLimit
                            placeholder=""
                            v-model="contentSuffix"
                            height="75px"
                            width="600px"
                            valid="createFollowUpRecord_contentSuffix"
                        ></ui-input>
                    </ui-form-item>
                </div>

                <div class="form-wrap border-top label-width-3"  v-if="isShow">
                    <ui-form-item label="待跟进时间" :must="true">
                        <ui-date-picker
                            type="date"
                            width="350px"
                            placeholder=""
                            v-model="nextContactDate"
                            :disabled="Boolean(noneNextDate)"
                            valid="createFollowUpRecord_nextContactDate"
                        ></ui-date-picker>
                        <ui-checkbox 
                            label="暂无待跟进时间"
                            class="nextData-margin" 
                            :checked.sync="noneNextDate" 
                            @change="handlerNext" 
                        ></ui-checkbox>
                    </ui-form-item>
                    <ui-form-item label="待跟进事项" :must="true">
                        <ui-input
                            type="textarea"
                            width="600px"
                            height="75px"
                            showWordLimit
                            maxlength="200"
                            placeholder=""
                            v-model="nextContactContent"
                            :disabled="Boolean(noneNextDate)"
                            valid="createFollowUpRecord_nextContactContent"
                        ></ui-input>
                    </ui-form-item>
                </div>
                <template slot="footer">
                    <div class="dlg-form-footer">
                        <ui-button @click="submit" type="primary">提交</ui-button>
                        <ui-button @click="close" class="close">取消</ui-button>
                    </div>
                </template>
            </ui-dialog>

            <!-- 天眼查详情 -->
            <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
        </div>
    `,
    props: {
        communicateType: {
            type: Number, // 244商机 4109线索
        },
        successFun: Function, // 成功回调
    },
    data() {
        return {
            isShow: false,
            canAjax: true,
            communicateRecordId: '', // 记录id - 仅编辑

            // query ↓↓↓
            otherQuery: {}, // 创建携带参数

            // form ↓↓↓
            FollowUpTypeS: [], // 跟进类型字典
            followUpType: '', // 跟进类型-字典值(电话 企微 其他)
            // 联系人
            contact: '',
            traderContactId: '',
            isFromPhone: false, // 联系人是否由手机带入的
            // 号码
            contactMob: '',
            contactMobError: '',
            telephone: '',
            beginTimeDate: '',   // 跟进时间
            contentSuffix: '',   // 跟进内容
            nextContactDate: '',    // 待跟进时间
            nextContactContent: '', // 待跟进事项
            noneNextDate: 0,     // 暂无待跟进时间
        }
    },
    created() {
        // 请求跟进类型字典
        this.$axios.post('/crm/sysOption/public/getByParentId?parentId=5900').then(({ data }) => {
            let res = data.data || [];
            this.FollowUpTypeS = res.map(item => {
                return {
                    label: item.title,
                    value: item.sysOptionDefinitionId
                }
            })
        })
    },
    mounted() {
    },
    watch: {
        isShow(newV) {
            if (!newV) {
                this.otherQuery = {};
                // 表单内容清空
                this.followUpType = '';
                this.contact = '';
                this.traderContactId = '';
                this.isFromPhone = false;
                this.contactMob = '';
                this.telephone = '';
                this.beginTimeDate = '';
                this.contentSuffix = '';
                this.nextContactDate = '';
                this.nextContactContent = '';
                this.noneNextDate = 0;
            }
        }
    },
    methods: {
        close() {
            this.isShow = false;
        },
        open(query) {
            console.log('create props：', query);
            this.otherQuery = query || {};
            this.handlerDefaultVal();
            this.isShow = true;

            const _this = this;
            this.$form && this.$form.rules({
                followUpType: {
                    required: '请选择跟进类型'
                },
                contact: {
                    required: '请输入联系人'
                },
                beginTimeDate: {
                    required: '请选择跟进时间'
                },
                contentSuffix: {
                    required: '请输入跟进内容'
                },
                nextContactDate: {
                    custom: {
                        valid(value) {
                            if (!_this.noneNextDate && !value.trim()) {
                                return false;
                            } else {
                                return true;
                            }
                        },
                        message: '请选择待跟进时间 或勾选暂无选项',
                    },
                },
                nextContactContent: {
                    custom: {
                        valid(value) {
                            if (!_this.noneNextDate && !value.trim()) {
                                return false;
                            } else {
                                return true;
                            }
                        },
                        message: '请输入待跟进事项',
                    },
                }
            }, 'createFollowUpRecord', this);
        },
        // 表单默认值
        handlerDefaultVal() {
            this.otherQuery.phone && (this.contactMob = this.otherQuery.phone || '')
            this.otherQuery.telephone && (this.telephone = this.otherQuery.telephone || '');
            // 联系人
            this.contact = this.otherQuery.contact || '';
            this.traderContactId = this.otherQuery.traderContactId || '';
            let currentTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.beginTimeDate = currentTime;
        },

        // 天眼查详情
        openTyc() {
            this.$refs.tycDetail.open(this.otherQuery.traderName);
        },

        // 手机
        handlerPhone(val) {
            console.log('handlerPhone:', this.contactMob, val);
            if (this.otherQuery.traderId) {
                if (this.isFromPhone) { // 当前是选择的
                    this.contact = val.traderContactName || '';
                    this.traderContactId = val.traderContactId || '';
                } else {
                    if (val.choosed) {
                        this.isFromPhone = true;
                        this.contact = val.traderContactName || '';
                        this.traderContactId = val.traderContactId || '';
                    }
                }
            }
            this.phone_Blur();
        },
        // 联系人
        handlerContact() {
            console.log('handlerContact:', this.contact);
            this.isFromPhone = false;
        },
        phone_Blur() {
            if (this.contactMob && this.contactMob.length != 11) {
                this.contactMobError = '请输入11位手机号码';
            } else if (!this.contactMob && !this.telephone) {
                this.contactMobError = '手机和固话请填写任意其一项';
            } else {
                this.contactMobError = '';
            }
        },
        contact_Blur() {
            if (!(this.contactMob || this.telephone)) {
                this.contactMobError = '手机和固话请填写任意其一项'
            } else {
                this.contactMobError = '';
            }
        },
        handlerNext() {
            if (this.noneNextDate) {
                this.nextContactDate = '';
                this.nextContactContent = '';
            }
            this.$form.validEl('createFollowUpRecord_nextContactDate');
            this.$form.validEl('createFollowUpRecord_nextContactContent');
        },

        checkForm() {
            let error = 0;
            if (!this.$form.validForm('createFollowUpRecord')) {
                error++;
                this.$forceUpdate();
            }
            if (!(this.contactMob || this.telephone)) {
                this.contactMobError = '手机和固话请填写任意其一项';
                error++;
            }
            if (error) return false;
            return true;
        },

        submit() {
            if (!this.checkForm()) return;

            this.canAjax = false;
            GLOBAL.showGlobalLoading();

            let reqData = {
                communicateType: this.communicateType, // 沟通记录类型-字典值- 244商机 4109线索
                relatedId: this.otherQuery.relatedId,             // 主键id（如：商机id，线索id）
                traderId: this.otherQuery.traderId,               // 客户ID - erp存档客户才有
                traderName: this.otherQuery.traderName,
                belongUserId: this.otherQuery.belongerId,     // -- 归属销售id
                belongUserName: this.otherQuery.belonger,  // -- 归属销售名称
                // ↓↓↓form↓↓↓
                followUpType: this.followUpType,   // -- 跟进类型-字典值(电话，企微，其他)
                contact: this.contact,             // -- 联系人名称
                traderContactId: this.traderContactId || '',               // 联系人ID - 选择联系人带入
                contactMob: this.contactMob,       // -- 手机
                telephone: this.telephone,         // -- 固话
                beginTimeDate: this.beginTimeDate, // -- 跟进时间
                contentSuffix: this.contentSuffix, // -- 跟进内容
                nextContactDate: this.nextContactDate, // -- 待跟进时间
                nextContactContent: this.nextContactContent.trim(), // -- 待跟进事项
                noneNextDate: this.noneNextDate ? 1 : 0,      // -- 暂无待跟进时间 (0否,1是)
            }

            this.$axios.post(`/crm/followUpRecord/profile/add`, reqData).then(({ data }) => {
                GLOBAL.hideGlobalLoading();
                if (data.success) {
                    this.$message.success('添加成功');
                    this.isShow = false;
                    this.close();

                    if (this.successFun) {
                        this.successFun(reqData);
                    }
                } else {
                    this.canAjax = true;
                    this.$message.warn(data.message || '网络异常')
                }
            })
        },
    },
})

Vue.component('ui-select-audio', {
    template: `<div class="vd-ui-select-audio">
        <ui-dialog
            :visible.sync="isShow"
            title="选择通话记录"
            titleTip="（仅能关联未绑定业务单据的座席通话或企微通话）"
            width="1200px"
            class="vd-ui-select-audio-dialog"
            :nostop="true"
        >
            <div class="ui-dlg-audio-wrap" v-if="isShow">
                <div class="ui-dlg-audio-search">
                    <div class="ui-dlg-audio-search-list" v-if="!resetting">
                        <div class="ui-dlg-audio-search-item">
                            <div class="ui-dlg-audio-search-label">号码/联系人：</div>
                            <div class="ui-dlg-audio-search-cnt">
                                <ui-input placeholder="请输入号码或联系人名称查询" v-model="keywords"></ui-input>
                            </div>
                        </div>
                        <div class="ui-dlg-audio-search-item">
                            <div class="ui-dlg-audio-search-label">通话方式：</div>
                            <div class="ui-dlg-audio-search-cnt">
                                <ui-select v-model="coidTypeList" multiple-type="fixed" clearable :data="audioTypeList"></ui-select>
                            </div>
                        </div>
                        <div class="ui-dlg-audio-search-item">
                            <div class="ui-dlg-audio-search-label">录音ID：</div>
                            <div class="ui-dlg-audio-search-cnt">
                                <ui-input v-model="communicateRecordId"></ui-input>
                            </div>
                        </div>
                        <div class="ui-dlg-audio-search-item">
                            <div class="ui-dlg-audio-search-label">通话时间：</div>
                            <div class="ui-dlg-audio-search-cnt">
                                <ui-date-range @change="handlerTimeChange"></ui-date-range>
                            </div>
                        </div>
                        <div class="ui-dlg-audio-search-item">
                            <div class="ui-dlg-audio-search-label">话务员：</div>
                            <div class="ui-dlg-audio-search-cnt">
                                <ui-select v-if="!isGettingUser" v-model="userIdList" :avatar="true" multiple-type="fixed" clearable :cansearch="true" :data="callUserList"></ui-select>
                            </div>
                        </div>
                    </div>
                    <div class="ui-dlg-audio-search-btns">
                        <ui-button type="primary" @click="search">搜索</ui-button>
                        <ui-button @click="reset">重置</ui-button>
                    </div>
                </div>
                <div class="ui-dlg-audio-list">
                    <ui-table
                        border
                        :width-border="true" 
                        :auto-scroll="false"
                        :headers="audioListHeaders" 
                        :list="list"
                        :canChoose="true"
                        :containerHeight="containHeight"
                        ref="list"
                        @selectchange="handlerSelect"
                        v-if="!isloading"
                    >
                        <template v-slot:time="{ row }">
                            <div class="ui-audio-time-type">
                                <div class="ui-audio-type">
                                    <template v-if="row.coidType == 2 || row.coidType == 3">
                                        <span class="ui-audio-type-icon out"></span>
                                        <div class="ui-audio-type-txt">呼出</div>
                                    </template>
                                    <template v-if="row.coidType == 1 || row.coidType == 4">
                                        <span class="ui-audio-type-icon in"></span>
                                        <div class="ui-audio-type-txt">呼入</div>
                                    </template>
                                    <div class="ui-audio-type-txt">
                                        <template v-if="row.coidType == 1 || row.coidType == 2">
                                            座席
                                        </template>
                                        <template v-if="row.coidType == 3 || row.coidType == 4">
                                            企微
                                        </template>
                                    </div>
                                </div>
                                <div class="ui-audio-time">{{ row.addTime }}</div>
                            </div>
                        </template>
                        <template v-slot:contact="{ row }">
                            <template v-if="row.coidType == 1 || row.coidType == 2">
                                <ui-user :avatar="row.avatarUrl" :name="row.phone"></ui-user>
                                <div class="ui-audio-customer-name">{{ row.contactName }}</div>
                            </template>
                            <template v-if="row.coidType == 3 || row.coidType == 4">
                                <ui-user :avatar="row.avatarUrl" :name="row.contactName ? row.contactName : row.contactNickName"></ui-user>
                                <div class="ui-audio-nickname" v-if="row.contactName && row.contactNickName">昵称：{{ row.contactNickName }}</div>
                            </template>
                        </template>
                        <template v-slot:belonger="{ row }">
                            <template v-if="row.traderName">
                                <div class="text-line-1" :title="row.traderName">{{ row.traderName }}</div>
                                <div class="ui-audio-belonger-name">归属：{{ row.saleName }}</div>
                            </template>
                            <template v-else>-</template>
                        </template>
                        <template v-slot:teluser="{ row }">
                            <ui-user :avatar="row.creatorAvatarUrl" :name="row.creatorName"></ui-user>
                        </template>
                        <template v-slot:option="{ row }">
                            <div class="option-wrap" v-if="row.aiWindowUrl">
                                <a class="table-edit" @click="playAudio(row)">播放</a>
                            </div>
                        </template>
                    </ui-table>
                </div>
                <div class="ui-dlg-audio-footer">
                    <div class="ui-dlg-audio-footer-page">
                        <ui-pagination
                            :total="total"
                            :pageSize="pageSize"
                            :currentPage="pageNo"
                            @change="handlerPageChange" 
                        ></ui-pagination>
                    </div>
                    <div class="ui-dlg-audio-footer-btns">
                        <ui-button type="primary" @click="confirm">确定</ui-button>
                        <ui-button @click="isShow = false;">取消</ui-button>
                    </div>
                </div>
            </div>
        </ui-dialog>
        <ui-dialog
            :visible.sync="isShowAiDialog"
            :title="'贝壳助理语音识别，录音ID：' + aiItem.communicateRecordId"
            width="900px"
        >
            <iframe :src="aiItem.aiWindowUrl" class="ai-dialog-iframe" v-if="isShowAiDialog"></iframe>
        </ui-dialog>
    </div>`,
    props: {

    },
    data() {
        return {
            isShow: false,
            audioTypeList: [{
                label: '座席-呼出',
                value: 2,
            }, {
                label: '座席-呼入',
                value: 1,
            }, {
                label: '企微-呼出',
                value: 3,
            }, {
                label: '企微-呼入',
                value: 4,
            }],
            audioListHeaders: [
                {
                    key: "communicateRecordId",
                    label: "录音ID",
                    width: "80px",
                },
                {
                    key: "time",
                    label: "通话时间/方式",
                    width: "150px",
                },
                {
                    key: "contact",
                    label: "联系人",
                    width: "240px",
                },
                {
                    key: "phoneArea",
                    label: "号码归属地",
                    width: "100px",
                },
                {
                    key: "coidLength",
                    label: "通话时长(秒)",
                    width: "90px",
                    align: 'right'
                },
                {
                    key: "belonger",
                    label: "客户名称/归属销售",
                    width: "260px",
                },
                {
                    key: "telUser",
                    label: "话务员",
                    width: "120px"
                },
                {
                    key: "option",
                    label: "操作",
                    width: "56px",
                },
            ],
            list: [],
            total: 0,
            pageSize: 20,
            pageNo: 1,
            containHeight: 'calc(100vh - 342px)',
            isloading: false,
            selectedData: [],
            callUserList: [], //话务员列表
            //搜索参数
            keywords: '',
            coidTypeList: [],
            communicateRecordId: '',
            createTimeStart: '',
            createTimeEnd: '',
            userIdList: [],
            //end
            isShowAiDialog: false,
            aiItem: {},
            isGettingUser: true,
            resetting: false,
            communicateType: '', //244商机，4109线索
            relatedId: '', //商机id，线索id
            cansubmit: true
        }
    },
    computed: {

    },
    created() {

    },
    methods: {
        getCalluserList() {
            this.isGettingUser = true;
            this.$axios.post('/crm/followUpRecord/profile/getUacSubUserInfoByCurrent').then(({ data }) => {
                if (data.success) {
                    let userList = [];
                    if (data.data && data.data.length) {
                        data.data.forEach(item => {
                            userList.push({
                                label: item.username,
                                value: item.userId,
                                avatar: item.aliasHeadPicture
                            })
                        })
                    }

                    this.callUserList = userList;
                }

                this.isGettingUser = false;
            })
        },
        getList() {
            GLOBAL.showGlobalLoading();
            this.$axios.post('/crm/followUpRecord/profile/getTelList', {
                pageNum: this.pageNo,
                pageSize: this.pageSize,
                param: {
                    keywords: this.keywords,
                    coidTypeList: this.coidTypeList,
                    communicateRecordId: this.communicateRecordId,
                    createTimeStart: this.createTimeStart,
                    createTimeEnd: this.createTimeEnd,
                    userIdList: this.userIdList
                }
            }).then(({ data }) => {
                GLOBAL.hideGlobalLoading();
                if (data.success) {
                    let resData = data.data || {};
                    this.list = resData.communicateTelRecordList || [];
                    this.total = resData.total || 0;
                    this.isloading = true;

                    setTimeout(() => {
                        this.isloading = false;
                    }, 100)
                }
            })
        },
        search() {
            this.pageNo = 1;
            this.getList();
        },
        clearData() {
            this.keywords = '';
            this.coidTypeList = [];
            this.communicateRecordId = '';
            this.createTimeStart = '';
            this.createTimeEnd = '';
            this.userIdList = [];
        },
        reset() {
            this.clearData();
            this.resetting = true;

            setTimeout(() => {
                this.resetting = false;
            })

            this.search();
        },
        show(params) {
            this.clearData();
            this.isShow = true;
            if (params.phone) {
                this.keywords = params.phone;
            }

            this.communicateType = params.communicateType;
            this.relatedId = params.relatedId;

            this.getCalluserList();
            this.getList();
        },
        handlerTimeChange(time) {
            this.createTimeStart = time[0] || '';
            this.createTimeEnd = time[1] || '';
        },
        handlerPageChange(num) {
            this.pageNo = num;
            this.getList();
        },
        playAudio(item) {
            this.aiItem = item;
            this.isShowAiDialog = true;
        },
        handlerSelect(data) {
            this.selectedData = data;
        },
        confirm() {
            if (!this.selectedData.length) {
                this.$message.warn('请选择通话记录');
            } else {
                let communicateRecordIdList = [];
                this.selectedData.forEach(item => {
                    communicateRecordIdList.push(item.communicateRecordId);
                })

                GLOBAL.showGlobalLoading();

                this.$axios.post('/crm/followUpRecord/profile/followBindingTel', {
                    communicateRecordIdList,
                    communicateType: this.communicateType,
                    relatedId: this.relatedId
                }).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        if(data.data && data.data.length) {
                            this.$message.success(`录音id：${data.data.join('、')}已绑定业务单据无法再次绑定该商机。`)
                        } else {
                            this.$message.success('关联成功');
                        }
                        this.$emit('success')
                        this.isShow = false;
                    } else {
                        this.$message.error(data.message || '关联失败')
                    }
                })
            }
        }
    }
})