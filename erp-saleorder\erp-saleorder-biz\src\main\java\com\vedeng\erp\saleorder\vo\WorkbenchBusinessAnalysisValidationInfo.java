package com.vedeng.erp.saleorder.vo;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 验证阶段信息
 */
@Data
public class WorkbenchBusinessAnalysisValidationInfo {
    /** 数量 */
    private Integer num;

    /** 转化率 */
    private BigDecimal conversionRateNumber;
    private String conversionRate;

    /** 转化率均值 */
    private BigDecimal conversionRateAvgNumber;
    private String conversionRateAvg;


} 