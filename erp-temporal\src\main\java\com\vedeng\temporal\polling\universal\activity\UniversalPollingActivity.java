package com.vedeng.temporal.polling.universal.activity;

import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.Map;

/**
 * 统一轮询Activity接口
 * 
 * 定义了统一轮询组件的Activity接口，支持远程API和本地数据库两种数据源的轮询。
 * 这是一个快速执行的Activity，只负责单次查询，不包含轮询逻辑。
 * 
 * 设计原则：
 * - Activity只执行单次查询，快速返回结果
 * - 轮询逻辑由Workflow层控制
 * - 支持多种数据源类型
 * - 提供完整的错误处理和监控信息
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-24
 */
@ActivityInterface
public interface UniversalPollingActivity {
    
    /**
     * 执行单次查询
     * 
     * 根据请求中的数据源类型，选择相应的查询执行器执行单次查询操作。
     * 这个方法应该快速执行完成（通常在30秒内），不包含任何轮询逻辑。
     * 
     * @param request 统一轮询请求，包含查询配置和条件检查器
     * @return 查询结果，包含成功/失败状态和数据
     * @throws RuntimeException 如果查询执行失败
     */
    @ActivityMethod(name = "executeQueryPolling")
    UniversalPollingResult<Map<String, Object>> executeQuery(UniversalPollingRequest request);
}