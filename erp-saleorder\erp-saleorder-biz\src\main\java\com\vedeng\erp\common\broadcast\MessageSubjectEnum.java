package com.vedeng.erp.common.broadcast;

/**
 * 消息发送主体
 * @ClassName:  MessageSubjectEnum   
 * @author: <PERSON><PERSON>yang
 * @date:   2025年6月6日 下午4:57:58    
 * @Copyright:
 */
public enum MessageSubjectEnum {

	SALES_SINGLE(1,"个人"),
	SALES_TEAM(2,"小组"),
	SALES_DEPT(3,"部门");

	private MessageSubjectEnum(Integer subject, String subjectName) {
		this.subject = subject;
		this.subjectName = subjectName;
	}
	
	private Integer subject;

    private String subjectName;

	public Integer getSubject() {
		return subject;
	}

	public String getSubjectName() {
		return subjectName;
	}
}
