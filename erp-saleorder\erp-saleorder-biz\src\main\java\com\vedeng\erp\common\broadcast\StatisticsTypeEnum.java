package com.vedeng.erp.common.broadcast;

/**
 * 统计类型枚举
 * @ClassName:  StatisticsTypeEnum   
 * @author: <PERSON><PERSON>ya<PERSON>
 * @date:   2025年6月6日 下午4:05:33    
 * @Copyright:
 */
public enum StatisticsTypeEnum {
	
	WAREHOUSE_INCOME(1,"到款"),
	WAREHOUSE_SALES_NUM(2,"出库量"),
	WAREHOUSE_SALES_VD_AMOUNT(3,"出库金额");

	private StatisticsTypeEnum(Integer type, String typeName) {
		this.type = type;
		this.typeName = typeName;
	}

	private Integer type;

    private String typeName;

	public Integer getType() {
		return type;
	}

	public String getTypeName() {
		return typeName;
	}

}
