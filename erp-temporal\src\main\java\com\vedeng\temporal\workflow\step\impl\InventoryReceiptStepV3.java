package com.vedeng.temporal.workflow.step.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.dto.ExpressSignDto;
import com.vedeng.temporal.enums.BusinessStepType;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.mapper.TemporalFlowOrderMapper;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.polling.universal.workflow.UniversalPollingWorkflow;
import com.vedeng.temporal.workflow.activity.InventoryReceiptActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 入库单步骤 V3 - 快递增量同步版本
 * <p>
 * V3 版本核心改进：
 * - 实现上游快递逐个创建时的实时下游同步
 * - 正确的完成判断逻辑：下游已创建快递数量 = 预期数量
 * - Temporal 框架兼容的增量监控机制
 * - 保留并优化 V2 版本的签收逻辑
 * <p>
 * 业务流程：
 * 1. 获取预期需要创建的快递数量
 * 2. 持续监控上游快递新增情况
 * 3. 发现新快递时立即创建对应的下游快递
 * 4. 跟踪下游快递创建进度，直到所有快递创建完成
 * 5. 继续执行库存查询和同行单创建流程
 * <p>
 * 与 V2 版本对比：
 * - V2：等待所有上游快递准备就绪后批量创建
 * - V3：上游每创建一个快递，立即创建对应下游快递
 * <p>
 * Temporal 兼容性：
 * - 所有外部 API 调用通过 Activity 执行
 * - Workflow 状态正确持久化，支持重放
 * - 使用确定性的状态变更逻辑
 *
 * <AUTHOR> 4.0 sonnet
 * @version 3.0 (快递增量同步版本)
 * @since 2025-01-21
 */
@Slf4j
public class InventoryReceiptStepV3 implements BusinessStep {

    private final InventoryReceiptActivity inventoryReceiptActivity;

    public InventoryReceiptStepV3(InventoryReceiptActivity inventoryReceiptActivity) {
        this.inventoryReceiptActivity = inventoryReceiptActivity;
    }

    @Override
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, CompanyExecutionContext context) {
        // 固化关键参数，防止 Temporal 重试时参数变化
        final String currentCompany = context.getCurrentCompany();
        final String nextCompany = context.getNextCompany();
        final String originalSourceCompany = request.getSourceCompanyCode();
        final String originalTargetCompany = request.getTargetCompanyCode();
        final boolean isFirst = context.isFirst();
        final boolean isLast = context.isLast();
        
        log.info("开始执行入库单步骤V3（增量同步版本），业务ID: {}, 源公司: {}, 原目标公司: {}, 当前公司: {}, 下一个公司: {}",
                request.getBusinessId(), originalSourceCompany, originalTargetCompany, currentCompany, nextCompany);
        
        // 创建request副本，避免修改原始对象
        CompanyBusinessRequest safeRequest = request.toBuilder()
                .sourceCompanyCode(currentCompany)
                .targetCompanyCode(nextCompany)
                .build();

        // 最后一家公司跳过处理
        if (isLast) {
            log.info("执行最后一家公司：{}，无需处理入库单", currentCompany);
            return CompanyBusinessResponse.success("最后一家公司无需处理，跳过执行", safeRequest.getBusinessId());
        }

        // 第1步：查询当前公司的采购单号
        log.info("开始查询采购单号，公司: {}, 业务ID: {}", currentCompany, safeRequest.getBusinessId());
        Object buyOrderNo = awaitPurchaseOrderQueryCompletion(currentCompany, safeRequest);
        Object nextBuyOrderNo = awaitPurchaseOrderQueryCompletion(nextCompany, safeRequest);

        // 创建新的ExtendedProperties副本，避免并发修改问题
        Map<String, Object> safeExtendedProperties = new HashMap<>();
        if (safeRequest.getExtendedProperties() != null) {
            safeExtendedProperties.putAll(safeRequest.getExtendedProperties());
        }
        safeExtendedProperties.put("buyOrderNo", buyOrderNo);
        safeExtendedProperties.put("nextBuyOrderNo", nextBuyOrderNo);
        safeExtendedProperties.put("isFirst", isFirst ? 1 : 0);
        
        // 更新safeRequest的ExtendedProperties
        safeRequest = safeRequest.toBuilder()
                .extendedProperties(safeExtendedProperties)
                .build();
        
        log.info("快递查询阶段：公司：{}查询单号结果：{}",currentCompany,JSON.toJSON(safeRequest.getExtendedProperties()));

        // 第2-3步：增量监控上游快递并创建下游快递（V3核心改进）
        log.info("开始增量监控上游快递创建并同步下游快递");
        this.monitorAndCreateExpressIncrement(currentCompany, safeRequest);
        return CompanyBusinessResponse.success("快递单处理完成", null);
    }

    /**
     * V3 核心方法：增量监控上游快递并创建下游快递（基于下游完成状态判断版）
     * <p>
     * 重构后的核心逻辑：
     * 1. 检查下游完成状态（下游快递商品数 = 下游采购单商品数）
     * 2. 如果未完成，检查上游是否有新快递可处理
     * 3. 为新快递创建对应的下游快递
     * 4. 等待后重复检查，直到下游数据匹配完成
     * <p>
     * Temporal 兼容性保证：
     * - 使用 Workflow 状态跟踪处理进度
     * - 所有外部调用通过 Activity 执行
     * - 支持可靠的重放和故障恢复
     *
     * @param companyCode 当前公司代码
     * @param request     业务请求
     * @return 快递创建结果
     */
    private void monitorAndCreateExpressIncrement(String companyCode, CompanyBusinessRequest request) {
        // Workflow 状态：跟踪快递处理情况（会被 Temporal 持久化）
        boolean allExpressCompleted = false;

        try {
            log.info("开始增量快递同步，基于下游商品数量完成判断");
            // 持续处理直到下游完成
            while (!allExpressCompleted) {
                // 1. 检查下游完成状态（核心改进）
                CompanyBusinessResponse statusResponse = inventoryReceiptActivity.checkDownstreamCompletionStatus(request);
                DownstreamStatusInfo statusInfo = parseDownstreamStatusInfo(statusResponse);
                
                log.debug("下游状态检查 - 快递商品数: {}, 采购单商品数: {}, 完成状态: {}", 
                    statusInfo.getDownstreamExpressGoodsCount(), 
                    statusInfo.getDownstreamPurchaseGoodsCount(), 
                    statusInfo.isCompleted());

                // 2. 判断是否完成（基于下游数据）
                allExpressCompleted = statusInfo.isCompleted();

                if (!allExpressCompleted) {
                    
                    // 3. 获取下游全部快递
                    CompanyBusinessResponse downstreamResponse = inventoryReceiptActivity.getDownstreamAllExpress(request);
                    List<String> downstreamExpressList = parseDownstreamExpressList(downstreamResponse);
                    request.getExtendedProperties().put("notInExpressNoList", downstreamExpressList);


                    // 基于request创建一个safeRequest
                    CompanyBusinessRequest revertRequest = request.toBuilder()
                            .sourceCompanyCode(request.getTargetCompanyCode())
                            .targetCompanyCode(request.getSourceCompanyCode())
                            .extendedProperties(request.getExtendedProperties())
                            .build();
                    log.info("开始查询上游快递：{}",JSON.toJSON(revertRequest));
                    
                    // 第4步：查询上游新增快递
                    CompanyBusinessResponse upstreamResponse = inventoryReceiptActivity.getUpstreamAllExpress(revertRequest);
                    Map<String, Object> resultData = parseUpstreamExpressList(upstreamResponse);
                    Integer expressStatus = (Integer) resultData.get("expressStatus");
                    if (expressStatus == 1){
                        
                        // 第5步：下游创建快递
                        CompanyBusinessResponse expressCreateResult = inventoryReceiptActivity.createExpressOnly(request, resultData);
                        if (expressCreateResult.getSuccess()) { 
                            log.info("快递创建成功，业务ID: {}, 快递ID: {}", request.getBusinessId(), 
                                expressCreateResult.getGeneratedDocumentId());   
                        }
                    }

                    // 6. 等待后继续监控
                    if (!allExpressCompleted) {
                        log.debug("等待30秒后继续监控");
                        Workflow.sleep(Duration.ofSeconds(30));
                    }
                }
            }

            log.info("下游快递商品数量与采购单商品数量匹配，增量同步完成");
        } catch (Exception e) {
            log.error("增量快递同步异常，公司: {}, 业务ID: {}", companyCode, request.getBusinessId(), e);
        }
    }

    private Map<String, Object> awaitExpressQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始查询快递信息，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 从扩展属性中获取采购单号
            Map<String, Object> extendedProperties = request.getExtendedProperties();
            if (extendedProperties == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("扩展属性为空，无法获取采购单号", "MISSING_EXTENDED_PROPERTIES", context);
            }

            String buyOrderNo = (String) extendedProperties.get("buyOrderNo");
            if (buyOrderNo == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("未找到采购单号", "MISSING_BUY_ORDER_NO", context);
            }

            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("buyOrderNo", buyOrderNo);
            log.info("开始查询快递信息，公司: {}, 业务ID: {}, 入参: {}", companyCode, businessId, JSON.toJSON(apiParameters));

            // 构建统一轮询请求
            UniversalPollingRequest statusRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(companyCode)
                    .apiPath("/api/v1/express/query.do")  // 快递查询API路径
                    .apiParameters(apiParameters)  // 使用自定义API参数
                    // 快递查询完成条件：expressStatus为1表示可以创建
                    .completionCheckConfig("data.expressStatus:1")
                    .build();

            log.info("使用快递查询检查器：查询快递信息，采购单号: {}", buyOrderNo);

            // 执行轮询等待
            UniversalPollingResult<Map<String, Object>> finalResult = UniversalPollingWorkflow.universalPoll(statusRequest);
            log.info("轮询结果: {}", finalResult);
            // 验证最终状态
            if (!finalResult.isSuccess()) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("快递信息查询轮询失败: " + finalResult.getMessage(), "POLLING_INCOMPLETE", context);
            }

            log.info("快递信息查询完成，公司: {}, 业务ID: {}, 轮询结果: {}", companyCode, businessId, finalResult.isSuccess() ? "成功" : "失败");

            return finalResult.getData();
        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("查询快递信息异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("快递信息查询系统异常", "POLLING_ERROR", context);
        }
    }

    /**
     * 创建单个下游快递并返回快递ID
     * <p>
     * 处理流程：
     * 1. 等待上游快递状态就绪
     * 2. 创建对应的下游快递
     * 3. 启动签收监控（复用V2逻辑）
     * 4. 返回下游快递ID用于进度跟踪
     *
     * @param upstreamExpress 上游快递信息
     * @param request         业务请求
     * @return 下游快递ID，创建失败返回null
     */
    private String createDownstreamExpressAndReturnId(ExpressSignDto upstreamExpress, CompanyBusinessRequest request) {
        try {
            log.info("开始处理上游快递: {}", upstreamExpress.getLogisticsNo());
            
            // 1. 等待上游快递状态就绪（可创建）
            boolean ready = inventoryReceiptActivity.waitForExpressReady(request, upstreamExpress);
            
            if (ready) {
                // 2. 创建下游快递
                CompanyBusinessResponse createResult = inventoryReceiptActivity.createSingleExpress(
                    request, upstreamExpress);
                
                if (createResult.getSuccess()) {
                    // 3. 提取下游快递ID
                    String downstreamExpressId = extractDownstreamExpressId(createResult);
                    
                    // 4. 启动签收监控（复用V2逻辑，异步执行）
                    String nextCompany = (String) request.getExtendedProperties().get("nextCompany");
                    if (nextCompany != null) {
                        // 设置快递信息用于签收监控
                        upstreamExpress.setBuyOrderNo(
                            request.getExtendedProperties().get("buyOrderNo").toString());
                        upstreamExpress.setNextBuyOrderNo(
                            request.getExtendedProperties().get("nextBuyOrderNo").toString());
                        
                        // 异步启动签收监控（复用V2逻辑）
                        Async.procedure(() -> executeAsyncExpressReceiptV2(
                            request, request.getSourceCompanyCode(), upstreamExpress, nextCompany));
                    }
                    
                    log.info("下游快递创建成功 - 上游: {}, 下游ID: {}", 
                        upstreamExpress.getLogisticsNo(), downstreamExpressId);
                    
                    return downstreamExpressId;
                }
            }
            
            log.warn("下游快递创建失败 - 上游: {}, 原因: 快递未就绪或创建API失败", 
                upstreamExpress.getLogisticsNo());
            return null;
            
        } catch (Exception e) {
            log.error("创建下游快递异常 - 上游: {}", upstreamExpress.getLogisticsNo(), e);
            return null;
        }
    }

    /**
     * 从创建结果中提取下游快递ID
     * 解析API响应，获取创建的快递标识符
     *
     * @param createResult 快递创建API响应
     * @return 下游快递ID
     */
    private String extractDownstreamExpressId(CompanyBusinessResponse createResult) {
        try {
            if (createResult.getResultData() instanceof Map) {
                Map<String, Object> data = (Map<String, Object>) createResult.getResultData();
                
                // 尝试多种可能的字段名
                if (data.containsKey("expressId")) {
                    return data.get("expressId").toString();
                }
                if (data.containsKey("id")) {
                    return data.get("id").toString();
                }
                if (data.containsKey("logisticsNo")) {
                    return data.get("logisticsNo").toString();
                }
                
                // 处理嵌套数据结构
                if (data.containsKey("data") && data.get("data") instanceof Map) {
                    Map<String, Object> nestedData = (Map<String, Object>) data.get("data");
                    if (nestedData.containsKey("expressId")) {
                        return nestedData.get("expressId").toString();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("提取下游快递ID异常", e);
        }
        
        // 如果无法提取ID，使用时间戳作为替代标识
        return "express_" + System.currentTimeMillis();
    }

    // ========== 保留并优化的V2签收逻辑 ==========

    /**
     * 异步执行快递签收流程（基于V2逻辑，略有优化）
     * 复用V2版本的签收逻辑，但移除了等待逻辑的阻塞问题
     */
    private void executeAsyncExpressReceiptV2(CompanyBusinessRequest request, String currentCompany, 
                                              ExpressSignDto expressSignDto, String nextCompany) {
        try {
            log.info("开始异步快递签收流程（V2逻辑），当前公司: {}, 下一个公司: {}, 物流号: {}",
                    currentCompany, nextCompany, expressSignDto.getLogisticsNo());
            
            // 1. 如果有上游公司，等待上游签收完成
            if (currentCompany != null) {
                log.info("等待上游公司 {} 签收完成，物流号: {}", currentCompany, expressSignDto.getLogisticsNo());
                
                // 使用轮询机制等待签收完成
                boolean signCompleted = awaitExpressSignCompleteV2(currentCompany, request, expressSignDto);
                if (!signCompleted) {
                    log.warn("上游公司 {} 签收未完成，跳过当前异步任务", currentCompany);
                    return;
                }
            }

            // 2. 执行签收
            log.info("开始执行快递签收，目标公司: {}, 物流号: {}", nextCompany, expressSignDto.getLogisticsNo());
            inventoryReceiptActivity.executeExpressReceipt(nextCompany, expressSignDto, request);
            log.info("快递签收完成，目标公司: {}, 物流号: {}", nextCompany, expressSignDto.getLogisticsNo());

        } catch (BusinessProcessException e) {
            log.error("异步快递签收业务异常，公司: {}, 业务ID: {}, 物流号: {}, 错误: {}",
                    currentCompany, request.getBusinessId(), expressSignDto.getLogisticsNo(), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("异步快递签收系统异常，公司: {}, 业务ID: {}, 物流号: {}",
                    currentCompany, request.getBusinessId(), expressSignDto.getLogisticsNo(), e);
            String context = "AsyncExpressReceipt, Company=" + currentCompany + ", BusinessId=" + request.getBusinessId();
            throw BusinessProcessException.retryable("异步快递签收系统异常", "ASYNC_EXPRESS_RECEIPT_ERROR", context);
        }
    }

    /**
     * 等待快递签收完成（基于V2逻辑）
     */
    private Boolean awaitExpressSignCompleteV2(String currentCompany, CompanyBusinessRequest request, ExpressSignDto expressSignDto) {
        try {
            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("logisticsNo", expressSignDto.getLogisticsNo());
            apiParameters.put("buyOrderNo", expressSignDto.getBuyOrderNo());

            // 构建统一轮询请求
            UniversalPollingRequest statusRequest = UniversalPollingRequest.builder()
                    .businessId(request.getBusinessId() + "_sign_" + expressSignDto.getLogisticsNo())
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(currentCompany)
                    .apiPath("/api/v1/express/signCheck.do")
                    .apiParameters(apiParameters)
                    .completionCheckConfig("data.arrivalStatus:2")
                    .build();

            log.info("V2签收检查：查询快递签收状态，采购单号: {}, 物流号: {}", 
                expressSignDto.getBuyOrderNo(), expressSignDto.getLogisticsNo());
            
            // 执行轮询等待
            UniversalPollingResult<Map<String, Object>> finalResult = UniversalPollingWorkflow.universalPoll(statusRequest);
            log.info("V2签收检查结果: {}", finalResult.isSuccess() ? "成功" : "失败 - " + finalResult.getMessage());
            
            return finalResult.isSuccess();
            
        } catch (Exception e) {
            log.error("V2签收检查异常，公司: {}, 物流号: {}, 采购单号: {}",
                    currentCompany, expressSignDto.getLogisticsNo(), expressSignDto.getBuyOrderNo(), e);
            return false;
        }
    }

    // ========== 复用V2版本的辅助方法 ==========

    /**
     * 等待采购单号查询完成（复用V2逻辑）
     */
    private Object awaitPurchaseOrderQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始轮询等待采购单号，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 构建查询参数
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("queryTypes", Arrays.asList("BUY_ORDER"));
            queryParameters.put("currentCompany", companyCode);

            // 构建统一轮询请求 - 使用数据库轮询
            UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.LOCAL_DATABASE)
                    .companyCode(companyCode)
                    .queryType("TEMPORAL_FLOW_ORDER_QUERY")
                    .queryParameters(queryParameters)
                    .completionCheckConfig("buyOrderNo:isNotBlank")
                    .build();

            // 执行统一轮询
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);

            if (result.isSuccess()) {
                Map<String, Object> data = result.getData();
                return data.get("buyOrderNo");
            } else {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("采购单号轮询未完成", "POLLING_INCOMPLETE", context);
            }

        } catch (BusinessProcessException e) {
            throw e;
        } catch (Exception e) {
            log.error("采购单号轮询异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("采购单号轮询系统异常", "POLLING_ERROR", context);
        }
    }

    /**
     * 等待库存记录查询完成（复用V2逻辑）
     */
    private Map<String, Object> awaitStockQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始查询库存记录，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 从扩展属性中获取采购单号
            Map<String, Object> extendedProperties = request.getExtendedProperties();
            if (extendedProperties == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("扩展属性为空，无法获取采购单号", "MISSING_EXTENDED_PROPERTIES", context);
            }

            String buyOrderNo = (String) extendedProperties.get("buyOrderNo");
            if (buyOrderNo == null) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.nonRetryable("未找到采购单号", "MISSING_BUY_ORDER_NO", context);
            }

            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("buyOrderNo", buyOrderNo);
            apiParameters.put("isFirst", extendedProperties.get("isFirst"));

            log.info("StepV3调用/api/v1/peerlist/queryStockRecords.do，入参：{}", apiParameters);
            
            // 构建统一轮询请求
            UniversalPollingRequest stockRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(companyCode)
                    .apiPath("/api/v1/peerlist/queryStockRecords.do")
                    .apiParameters(apiParameters)
                    .completionCheckConfig("data.peerStatus:1")
                    .build();

            log.info("V3库存查询：查询库存记录，采购单号: {}, isFirst: {}", buyOrderNo, extendedProperties.get("isFirst"));

            // 执行轮询等待
            UniversalPollingResult<Map<String, Object>> finalResult = UniversalPollingWorkflow.universalPoll(stockRequest);
            log.info("V3库存查询结果: {}", finalResult);

            // 验证最终状态
            if (!finalResult.isSuccess()) {
                String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
                throw BusinessProcessException.retryable("库存记录查询轮询失败: " + finalResult.getMessage(), "POLLING_INCOMPLETE", context);
            }

            log.info("库存记录查询完成，公司: {}, 业务ID: {}, 轮询结果: {}", companyCode, businessId, finalResult.isSuccess() ? "成功" : "失败");

            return finalResult.getData();
        } catch (BusinessProcessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询库存记录异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            String context = "Step=" + getStepName() + ", Company=" + companyCode + ", BusinessId=" + businessId;
            throw BusinessProcessException.retryable("库存记录查询系统异常", "POLLING_ERROR", context);
        }
    }

    @Override
    public String getStepName() {
        return "入库单步骤V3（快递增量同步版本）";
    }

    @Override
    public BusinessStepType getStepType() {
        return BusinessStepType.INVENTORY_RECEIPT;
    }

    @Override
    public String getStepDescription() {
        return "V3版本：实现上游快递逐个创建时的实时下游同步，正确跟踪下游快递创建进度，保留V2签收逻辑";
    }

    // ========== 快递对比算法 ==========

    /**
     * 对比上游下游快递，找出需要新增的快递
     * 
     * @param upstreamList 上游全部快递列表
     * @param downstreamList 下游全部快递列表
     * @return 快递对比结果
     */
    private ExpressComparisonInfo findNewExpress(List<ExpressSignDto> upstreamList, 
                                                List<ExpressSignDto> downstreamList) {
        // 提取下游已有的快递单号
        Set<String> downstreamLogisticsNos = downstreamList.stream()
            .map(ExpressSignDto::getLogisticsNo)
            .filter(logisticsNo -> logisticsNo != null && !logisticsNo.trim().isEmpty())
            .collect(Collectors.toSet());
        
        // 过滤出上游有但下游没有的快递
        List<ExpressSignDto> newExpressList = upstreamList.stream()
            .filter(express -> express.getLogisticsNo() != null 
                && !express.getLogisticsNo().trim().isEmpty()
                && !downstreamLogisticsNos.contains(express.getLogisticsNo()))
            .collect(Collectors.toList());
        
        ExpressComparisonInfo comparisonInfo = ExpressComparisonInfo.builder()
            .upstreamExpressList(upstreamList)
            .downstreamExpressList(downstreamList)
            .newExpressList(newExpressList)
            .upstreamCount(upstreamList.size())
            .downstreamCount(downstreamList.size())
            .newExpressCount(newExpressList.size())
            .build();
        
        log.info("快递对比结果 - 上游总数: {}, 下游总数: {}, 需新增: {}", 
            comparisonInfo.getUpstreamCount(), 
            comparisonInfo.getDownstreamCount(), 
            comparisonInfo.getNewExpressCount());
        
        if (log.isDebugEnabled()) {
            log.debug("下游已有快递单号: {}", downstreamLogisticsNos);
            log.debug("需要新增的快递单号: {}", 
                newExpressList.stream().map(ExpressSignDto::getLogisticsNo).collect(Collectors.toList()));
        }
        
        return comparisonInfo;
    }

    // ========== 解析方法 ==========

    /**
     * 解析下游状态信息
     */
    private DownstreamStatusInfo parseDownstreamStatusInfo(CompanyBusinessResponse response) {
        try {
            if (response.getSuccess() && response.getResultData() instanceof Map) {
                Map<String, Object> data = (Map<String, Object>) response.getResultData();
                Map<String, Object> statusData = (Map<String, Object>) data.get("data");
                
                if (statusData != null) {
                    Integer downstreamExpressGoodsCount = (Integer) statusData.get("expressGoodsCount");
                    Integer downstreamPurchaseGoodsCount = (Integer) statusData.get("buyOrderGoodsCount");
                    Boolean isCompleted = (Boolean) statusData.get("isCompleted");
                    
                    // 如果API没有返回isCompleted，根据数量关系判断
                    if (isCompleted == null && downstreamExpressGoodsCount != null && downstreamPurchaseGoodsCount != null) {
                        isCompleted = downstreamExpressGoodsCount >= downstreamPurchaseGoodsCount;
                    }
                    
                    return DownstreamStatusInfo.builder()
                        .downstreamExpressGoodsCount(downstreamExpressGoodsCount != null ? downstreamExpressGoodsCount : 0)
                        .downstreamPurchaseGoodsCount(downstreamPurchaseGoodsCount != null ? downstreamPurchaseGoodsCount : 0)
                        .isCompleted(isCompleted != null ? isCompleted : false)
                        .build();
                }
            }
        } catch (Exception e) {
            log.warn("解析下游状态信息异常", e);
        }
        
        // 默认返回未完成状态
        return DownstreamStatusInfo.builder()
            .downstreamExpressGoodsCount(0)
            .downstreamPurchaseGoodsCount(0)
            .isCompleted(false)
            .build();
    }

    /**
     * 解析上游快递列表
     */
    private Map<String, Object> parseUpstreamExpressList(CompanyBusinessResponse response) {
        try {
            if (response.getSuccess() && response.getResultData() instanceof Map) {
                Map<String, Object> data = (Map<String, Object>) response.getResultData();
                return data;
            }
        } catch (Exception e) {
            log.warn("解析上游快递列表异常", e);
        }
        return new HashMap<>();
    }

    /**
     * 解析下游快递列表
     */
    private List<String> parseDownstreamExpressList(CompanyBusinessResponse response) {
        try {
            if (response.getSuccess() && response.getResultData() instanceof Map) {
                Map<String, Object> data = (Map<String, Object>) response.getResultData();
                Map<String, Object> listData = (Map<String, Object>) data.get("data");
                
                if (listData != null) {
                    Object expressListObj = listData.get("expresslist");
                    if (expressListObj instanceof Collection) {
                        List<String> expressList = BeanUtil.copyToList((Collection<?>) expressListObj, String.class);
                        return expressList;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析下游快递列表异常", e);
        }
        
        log.info("下游快递列表为空，返回空列表");
        return new ArrayList<>();
    }

    /**
     * 解析新增快递列表信息（简化版）
     */
    private ExpressListInfo parseNewExpressListInfo(CompanyBusinessResponse response) {
        try {
            if (response.getSuccess() && response.getResultData() instanceof Map) {
                Map<String, Object> data = (Map<String, Object>) response.getResultData();
                Map<String, Object> listData = (Map<String, Object>) data.get("data");
                
                if (listData != null) {
                    // 解析新增快递列表
                    List<ExpressSignDto> newExpressList = new ArrayList<>();
                    Object newExpressListObj = listData.get("newExpressList");
                    if (newExpressListObj instanceof Collection) {
                        newExpressList = BeanUtil.copyToList((Collection<?>) newExpressListObj, ExpressSignDto.class);
                    }
                    
                    return ExpressListInfo.builder()
                        .newExpressList(newExpressList)
                        .build();
                }
            }
        } catch (Exception e) {
            log.warn("解析新增快递列表信息异常", e);
        }
        
        // 默认返回空列表
        return ExpressListInfo.builder()
            .newExpressList(new ArrayList<>())
            .build();
    }

    /**
     * 解析快递列表信息（修正版，已废弃）
     * @deprecated 使用 parseNewExpressListInfo 替代
     */
    @Deprecated
    private ExpressListInfo parseExpressListInfo(CompanyBusinessResponse response) {
        try {
            if (response.getSuccess() && response.getResultData() instanceof Map) {
                Map<String, Object> data = (Map<String, Object>) response.getResultData();
                Map<String, Object> listData = (Map<String, Object>) data.get("data");
                
                if (listData != null) {
                    // 解析新增快递列表
                    List<ExpressSignDto> newExpressList = new ArrayList<>();
                    Object newExpressListObj = listData.get("newExpressList");
                    if (newExpressListObj instanceof Collection) {
                        newExpressList = BeanUtil.copyToList((Collection<?>) newExpressListObj, ExpressSignDto.class);
                    }
                    
                    // 解析所有快递列表
                    List<ExpressSignDto> allExpressList = new ArrayList<>();
                    Object allExpressListObj = listData.get("allExpressList");
                    if (allExpressListObj instanceof Collection) {
                        allExpressList = BeanUtil.copyToList((Collection<?>) allExpressListObj, ExpressSignDto.class);
                    }
                    
                    Integer currentExpressCount = (Integer) listData.get("currentExpressCount");
                    Integer shipmentCount = (Integer) listData.get("shipmentCount");
                    Boolean isCompleted = (Boolean) listData.get("isCompleted");
                    
                    // 如果API没有返回isCompleted，根据数量关系判断
                    if (isCompleted == null && currentExpressCount != null && shipmentCount != null) {
                        isCompleted = currentExpressCount >= shipmentCount;
                    }
                    
                    return ExpressListInfo.builder()
                        .newExpressList(newExpressList)
                        .allExpressList(allExpressList)
                        .currentExpressCount(currentExpressCount != null ? currentExpressCount : 0)
                        .shipmentCount(shipmentCount != null ? shipmentCount : 0)
                        .isCompleted(isCompleted != null ? isCompleted : false)
                        .build();
                }
            }
        } catch (Exception e) {
            log.warn("解析快递列表信息异常", e);
        }
        
        // 默认返回空结果
        return ExpressListInfo.builder()
            .newExpressList(new ArrayList<>())
            .allExpressList(new ArrayList<>())
            .currentExpressCount(0)
            .shipmentCount(0)
            .isCompleted(false)
            .build();
    }

    // ========== 内部数据结构 ==========

    /**
     * 快递创建结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExpressCreationResult {
        private List<String> createdExpressIds;
        private List<String> processedUpstreamIds;
        private int createdExpressCount;
        private boolean success;
        private String errorMessage;
    }

    /**
     * 下游状态信息（V3新增）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DownstreamStatusInfo {
        private int downstreamExpressGoodsCount;    // 下游快递商品数
        private int downstreamPurchaseGoodsCount;   // 下游采购单商品数
        private boolean isCompleted;                // 是否完成（接口直接返回）
    }

    /**
     * 快递对比信息（V3新增）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExpressComparisonInfo {
        private List<ExpressSignDto> upstreamExpressList;    // 上游全部快递
        private List<ExpressSignDto> downstreamExpressList;  // 下游全部快递
        private List<ExpressSignDto> newExpressList;         // 需要新增的快递
        private int upstreamCount;                           // 上游快递数量
        private int downstreamCount;                         // 下游快递数量
        private int newExpressCount;                         // 新增快递数量
    }

    /**
     * 快递列表信息（简化版）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExpressListInfo {
        private List<ExpressSignDto> newExpressList;     // 新增快递列表
        
        /**
         * @deprecated 已简化，不再使用这些字段
         */
        @Deprecated
        private List<ExpressSignDto> allExpressList;     // 所有快递列表
        @Deprecated
        private int currentExpressCount;                 // 当前快递数量
        @Deprecated
        private int shipmentCount;                       // 发货数量
        @Deprecated
        private boolean isCompleted;                     // 是否完成 (快递数量 >= 发货数量)
    }
}
