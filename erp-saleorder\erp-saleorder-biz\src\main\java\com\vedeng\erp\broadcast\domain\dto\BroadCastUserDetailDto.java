package com.vedeng.erp.broadcast.domain.dto;

import lombok.*;
import java.util.Date;

/**
 * 播报用户详情DTO
 * 用于新增、编辑、详情查看等需要完整信息的场景
 * 包含T_BROADCAST_R_AED_USER表的所有字段及关联用户的完整信息
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BroadCastUserDetailDto {
    
    // ========== T_BROADCAST_R_AED_USER表字段 ==========
    
    /**
     * 主键ID
     * 自增主键，唯一标识一条关联记录
     */
    private Integer id;
    
    /**
     * ERP用户ID
     * 关联ERP系统中的用户ID
     */
    private Integer erpUserId;
    
    /**
     * AED用户ID
     * 关联AED系统中的销售用户ID
     */
    private Integer aedUserId;
    
    /**
     * 是否删除
     * 0-正常，1-已删除，软删除标记
     */
    private Integer isDeleted;
    
    /**
     * 创建时间
     * 记录的创建时间戳
     */
    private Date addTime;
    
    /**
     * 修改时间
     * 记录的最后修改时间戳
     */
    private Date modTime;
    
    /**
     * 创建人ID
     * 创建此关联关系的操作员ID
     */
    private Integer creator;
    
    /**
     * 更新人ID
     * 最后更新此关联关系的操作员ID
     */
    private Integer updater;
    
    // ========== 关联用户信息对象 ==========
    
    /**
     * ERP用户信息
     * 包含ERP用户的基本信息（ID、用户名、姓名）
     */
    private BroadCastErpUserDto erpUser;
    
    /**
     * AED用户信息
     * 包含AED销售的基本信息（ID、用户名、姓名）
     */
    private BroadCastAedUserDto aedUser;
    
    // ========== 内部静态类：防止外部直接使用 ==========
    
    /**
     * 播报ERP用户DTO
     * 内部静态类，防止外部包直接使用，保护业务边界
     * 包含ERP用户的基本信息
     */
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BroadCastErpUserDto {
        
        /**
         * ERP用户ID
         * ERP系统中的用户唯一标识
         */
        private Integer erpUserId;
        
        /**
         * 用户名
         * ERP用户的登录用户名
         */
        private String username;
        
        /**
         * 真实姓名
         * ERP用户的真实姓名
         */
        private String realName;
        
        /**
         * 获取显示名称
         * 格式：用户名(真实姓名)
         * 
         * @return 格式化的显示名称
         */
        public String getDisplayName() {
            if (username != null && realName != null) {
                return username + "(" + realName + ")";
            }
            return username != null ? username : realName;
        }
    }
    
    /**
     * 播报AED用户DTO
     * 内部静态类，防止外部包直接使用，保护业务边界
     * 包含AED销售的基本信息，也可用于AED销售选择场景
     */
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BroadCastAedUserDto {
        
        /**
         * AED用户ID
         * AED系统中的销售用户唯一标识
         */
        private Integer aedUserId;
        
        /**
         * 用户名
         * AED销售的登录用户名
         */
        private String username;
        
        /**
         * 真实姓名
         * AED销售的真实姓名
         */
        private String realName;
        
        /**
         * 获取显示名称
         * 格式：用户名(真实姓名)
         * 
         * @return 格式化的显示名称
         */
        public String getDisplayName() {
            if (username != null && realName != null) {
                return username + "(" + realName + ")";
            }
            return username != null ? username : realName;
        }
    }
} 