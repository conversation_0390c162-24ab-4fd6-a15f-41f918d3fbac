package com.vedeng.erp.broadcast.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptRErpDeptEntity;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptRErpDeptMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastStatisticsMapper;
import com.vedeng.erp.broadcast.service.BroadcastTarget;
import com.vedeng.erp.broadcast.statistics.project.BroadcastHelper;
import com.vedeng.erp.common.broadcast.MessageSubjectEnum;
import com.vedeng.erp.common.broadcast.bo.BroadcastDeptRErpDeptBo;
import com.vedeng.erp.common.broadcast.config.BroadcastDeptConfigStatistics;
import com.vedeng.erp.common.broadcast.config.GlobalConfig;
import com.vedeng.erp.common.broadcast.param.TargetOrgAndUser;
import com.vedeng.erp.common.broadcast.param.UserDefineUser;
import com.vedeng.erp.common.broadcast.param.UserOrgInfo;

/**
 * 播报目标实现类
 * @ClassName:  BroadcastTargetImpl   
 * @author: Neil.yang
 * @date:   2025年6月10日 下午3:35:10    
 * @Copyright:
 */
@Service
public class BroadcastTargetImpl  extends BroadcastHelper implements BroadcastTarget{
	
	//日志
	public static Logger LOGGER = LoggerFactory.getLogger(BroadcastTargetImpl.class);
	
	@Autowired
	private BroadcastDeptRErpDeptMapper broadcastDeptRErpDeptMapper;
	
	@Autowired
	private BroadcastStatisticsMapper broadcastStatisticsMapper;


	/**
	 * #计算个人数据，<br>
	 *	 #大群统计：无需计算自定义归属条件  ，大群是所有人业绩，只在最终企微播报时，如果播报的用户有自定义配置小组的情况下，替换掉所属小组名称即可<br>
	 *   #部门统计：需要判断自定义归属条件：<br>
	 *	 	#1：获取自定义销售配置中,用户现归属于现在的部门，且原归属部门也属于现在部门，只在最终企微播报时，如果播报的用户有自定义配置小组的情况下，替换掉所属小组名称即可<br>
	 *	 	#2：获取自定义销售配置中,用户现归属于现在的部门，但原归属部门不属于现在部门，需排除掉该用户 【AND USER_ID!=xxx】<br>
	 *		#3：获取自定义销售配置中,用户现归不属于现在的部门，但原归属部门属于现在部门，需添加用户 【AND USER_ID!=xxx】
	 */
	@Override
	public List<TargetOrgAndUser> getOrgIdBySingle(Integer deptId) {
		List<TargetOrgAndUser> targetOrgAndUserList = new ArrayList<>();
		List<BroadcastDeptRErpDeptBo> broadcastDeptRErpDeptBoList = new ArrayList<>();
		//播报大群获取所有组织ID
		if(deptId==1) {
			broadcastDeptRErpDeptBoList = broadcastDeptRErpDeptMapper.selectAllByDeptId(null);
		}
		//非播报大群获取大区下的所有组织ID
		else {
			broadcastDeptRErpDeptBoList = broadcastDeptRErpDeptMapper.selectAllByDeptId(deptId);
		}
		if(CollectionUtils.isEmpty(broadcastDeptRErpDeptBoList)) {
			LOGGER.info("dept:{}：未查询到配置小组和部门信息",deptId);
			return null;
		}
		List<UserDefineUser> userDefineUserList =  getExcludeUserId();
		for (BroadcastDeptRErpDeptBo broadcastDeptRErpDeptBo : broadcastDeptRErpDeptBoList) {
			TargetOrgAndUser targetOrgAndUser = new TargetOrgAndUser();
			targetOrgAndUser.setTeamId(broadcastDeptRErpDeptBo.getTeamId());
			targetOrgAndUser.setTeamName(broadcastDeptRErpDeptBo.getTeamName());
			List<Integer> orgIdList = Arrays.asList(broadcastDeptRErpDeptBo.getOrgIds().split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
			targetOrgAndUser.setOrgIdList(orgIdList);
			List<Integer> inUserIdList = new ArrayList<>();
			List<Integer> outUserIdList = new ArrayList<>(); 
			if(!CollectionUtils.isEmpty(userDefineUserList) && deptId !=1 ) {
				//获取当前统计的组织所有小组ID
				LOGGER.info("大区ID:{}",deptId);
				//#1：获取自定义销售配置中,用户现归属于现在的部门，且原归属部门也属于现在部门，只在最终企微播报时，如果播报的用户有自定义配置小组的情况下，替换掉所属小组名称即可<br>
				//#2：获取自定义销售配置中,用户现归属于现在的部门，但原归属部门不属于现在部门，需添加用户 【AND USER_ID==xxx】
				//#3：获取自定义销售配置中,用户现归不属于现在的部门，但原归属部门属于现在部门，需添加用户 【AND USER_ID!=xxx】
				for (UserDefineUser userDefineUser : userDefineUserList) {
					Integer deptIdNow = userDefineUser.getDeptIdNow();
					Integer teamIdNow = userDefineUser.getTeamIdNow();
					List<Integer> deptIdOldList = userDefineUser.getDeptIdOldList();
					if(deptId.equals(deptIdNow) && teamIdNow.equals(broadcastDeptRErpDeptBo.getTeamId())  && (CollectionUtils.isEmpty(deptIdOldList) || !deptIdOldList.contains(deptId))) {
						List<Integer> userIdBelongOrgIdList = getOrgIdByUserId(Arrays.asList(userDefineUser.getUserId()));
						if(!CollectionUtils.isEmpty(userIdBelongOrgIdList)) {
							targetOrgAndUser.setInUserIdBelongOrgIdList(userIdBelongOrgIdList);
							inUserIdList.add(userDefineUser.getUserId());
						}
					}
					if(!deptId.equals(deptIdNow) && !CollectionUtils.isEmpty(deptIdOldList) && deptIdOldList.contains(deptId)) {
						outUserIdList.add(userDefineUser.getUserId());
					}
				}
			}
			targetOrgAndUser.setInUserIdList(inUserIdList);
			targetOrgAndUser.setOutUserIdList(outUserIdList);
			targetOrgAndUserList.add(targetOrgAndUser);
		}
		return targetOrgAndUserList;
	}
	
	
	/**
	 * #计算小组数据和部门数据，只在大群进行<br>
	 * 	#计算小组数据时<br>
	 *		#1：获取自定义销售配置中，用户归属于现在小组的数据,增加sql: 【OR (USER_ID=xxx ) 】<br>
     *    	#2：获取自定义销售配置中，用户不归属于现在小组的数据，无论用户原归属是否属于现在小组，直接进行该用户的排除 【AND USER_ID!=xxx】<br>
	 * @return
	 */
	@Override
	public List<TargetOrgAndUser> getOrgIdByTeam(Integer deptId) {
		List<TargetOrgAndUser> targetOrgAndUserList = new ArrayList<>();
		List<BroadcastDeptRErpDeptBo> broadcastDeptRErpDeptBoList = new ArrayList<>();
		//播报大群获取所有组织ID
		if(deptId==1) {
			broadcastDeptRErpDeptBoList = broadcastDeptRErpDeptMapper.selectAllByDeptId(null);
		}
		//非播报大群获取大区下的所有组织ID
		else {
			broadcastDeptRErpDeptBoList = broadcastDeptRErpDeptMapper.selectAllByDeptId(deptId);
		}
		if(CollectionUtils.isEmpty(broadcastDeptRErpDeptBoList)) {
			LOGGER.info("dept:{}：未查询到配置小组和部门信息",deptId);
			return null;
		}
		List<UserDefineUser> userDefineUserList =  getExcludeUserId();
		for (BroadcastDeptRErpDeptBo broadcastDeptRErpDeptBo : broadcastDeptRErpDeptBoList) {
			TargetOrgAndUser targetOrgAndUser = new TargetOrgAndUser();
			targetOrgAndUser.setTeamId(broadcastDeptRErpDeptBo.getTeamId());
			targetOrgAndUser.setTeamName(broadcastDeptRErpDeptBo.getTeamName());
			List<Integer> orgIdList = Arrays.asList(broadcastDeptRErpDeptBo.getOrgIds().split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
			targetOrgAndUser.setOrgIdList(orgIdList);
			List<Integer> inUserIdList = new ArrayList<>();
			List<Integer> outUserIdList = new ArrayList<>(); 
			if(!CollectionUtils.isEmpty(userDefineUserList)) {
				//#1：获取自定义销售配置中，用户归属于现在小组的数据,增加sql: 【OR (USER_ID=xxx ) 】
			    //#2：获取自定义销售配置中，用户不归属于现在小组的数据，无论用户原归属是否属于现在小组，直接进行该用户的排除 【AND USER_ID!=xxx】
				for (UserDefineUser userDefineUser : userDefineUserList) {
					//获取现在归属小组
					Integer teamIdNow = userDefineUser.getTeamIdNow();
					//现归属小组与该小组相同，添加
					if(teamIdNow == broadcastDeptRErpDeptBo.getTeamId()) {
						inUserIdList.add(userDefineUser.getUserId());
					}
					//现归属小组与该小组不同，排除
					else{
						outUserIdList.add(userDefineUser.getUserId());
					}
				}
			}
			List<Integer> userIdBelongOrgIdList = getOrgIdByUserId(inUserIdList);
			if(!CollectionUtils.isEmpty(inUserIdList) && !CollectionUtils.isEmpty(userIdBelongOrgIdList)) {
				targetOrgAndUser.setInUserIdBelongOrgIdList(userIdBelongOrgIdList);
				targetOrgAndUser.setInUserIdList(inUserIdList);
			}else {
				targetOrgAndUser.setInUserIdList(inUserIdList);
			}
			targetOrgAndUser.setOutUserIdList(outUserIdList);
			targetOrgAndUserList.add(targetOrgAndUser);
		}
		return targetOrgAndUserList;
	}
	
	/**
	 * #计算小组数据和部门数据，只在大群进行<br>
     *	#计算部门数据时<br>
	 *		#1：获取自定义销售配置中，用户归属于现在部门的数据,增加sql: 【OR (USER_ID=xxx ) 】<br>
     *    	#2：获取自定义销售配置中，用户不归属于现在部门的数据，无论用户原归属是否属于现在小组，直接进行该用户的排除 【AND USER_ID!=xxx】<br>
	 * @return
	 */
	@Override
	public List<TargetOrgAndUser> getOrgIdByDept(Integer deptId) {
		List<TargetOrgAndUser> targetOrgAndUserList = new ArrayList<>();
		List<BroadcastDeptRErpDeptBo> broadcastDeptRErpDeptBoList = new ArrayList<>();
		//大群使用
		if(deptId == 1) {
			broadcastDeptRErpDeptBoList = broadcastDeptRErpDeptMapper.selectDeptByDeptId(null);
		}else {
			broadcastDeptRErpDeptBoList = broadcastDeptRErpDeptMapper.selectDeptByDeptId(deptId);
		}
		if(CollectionUtils.isEmpty(broadcastDeptRErpDeptBoList)) {
			LOGGER.info("dept:{}：未查询到配置小组和部门信息",deptId);
			return null;
		}
		List<UserDefineUser> userDefineUserList =  getExcludeUserId();
		for (BroadcastDeptRErpDeptBo broadcastDeptRErpDeptBo : broadcastDeptRErpDeptBoList) {
			TargetOrgAndUser targetOrgAndUser = new TargetOrgAndUser();
			targetOrgAndUser.setDeptId(broadcastDeptRErpDeptBo.getDeptId());
			targetOrgAndUser.setDeptName(broadcastDeptRErpDeptBo.getDeptName());
			List<Integer> orgIdList = Arrays.asList(broadcastDeptRErpDeptBo.getOrgIds().split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
			targetOrgAndUser.setOrgIdList(orgIdList);
			List<Integer> inUserIdList = new ArrayList<>();
			List<Integer> outUserIdList = new ArrayList<>(); 
			if(!CollectionUtils.isEmpty(userDefineUserList)) {
				//#1：获取自定义销售配置中，用户归属于现在部门的数据,增加sql: 【OR (USER_ID=xxx ) 】
			    //#2：获取自定义销售配置中，用户不归属于现在部门的数据,直接进行该用户的排除 【AND USER_ID!=xxx】
				for (UserDefineUser userDefineUser : userDefineUserList) {
					//获取现在归属大区
					Integer deptIdNow = userDefineUser.getDeptIdNow();
					//现归属大区与该大区相同，添加
					if(deptIdNow == broadcastDeptRErpDeptBo.getDeptId()) {
						inUserIdList.add(userDefineUser.getUserId());
					}
					else{
						outUserIdList.add(userDefineUser.getUserId());
					}
				}
			}
			List<Integer> userIdBelongOrgIdList = getOrgIdByUserId(inUserIdList);
			if(!CollectionUtils.isEmpty(inUserIdList) && !CollectionUtils.isEmpty(userIdBelongOrgIdList)) {
				targetOrgAndUser.setInUserIdBelongOrgIdList(userIdBelongOrgIdList);
				targetOrgAndUser.setInUserIdList(inUserIdList);
			}else {
				targetOrgAndUser.setInUserIdList(inUserIdList);
			}
			targetOrgAndUser.setOutUserIdList(outUserIdList);
			targetOrgAndUserList.add(targetOrgAndUser);
		}
		return targetOrgAndUserList;
	}

	

	@Override
	public List<MessageSubjectEnum> getMessageSubjectList(Integer deptId) {
		List<MessageSubjectEnum> messageSubjectList = new ArrayList<>();
		//播报大群
		if(deptId==1) {
			//播报小群发送个人消息
			messageSubjectList = Arrays.asList(MessageSubjectEnum.SALES_SINGLE,MessageSubjectEnum.SALES_TEAM,MessageSubjectEnum.SALES_DEPT);
		}else {
			//播报小群发送个人消息
			messageSubjectList = Arrays.asList(MessageSubjectEnum.SALES_SINGLE);
		}
		
		return messageSubjectList;
	}

	@Override
	public String getWebHook(GlobalConfig configs,Integer deptId) {
		String webhook = "";
		List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList =  configs.getBroadcastDeptConfigStatisticsList();
		for (BroadcastDeptConfigStatistics broadcastDeptConfigStatistics : broadcastDeptConfigStatisticsList) {
			if(broadcastDeptConfigStatistics.getBroadcastDeptId() == deptId) {
				webhook = broadcastDeptConfigStatistics.getWebhook();
			}
		}
		return webhook;
	}
	
	@Override
	public Integer getAmountStep(GlobalConfig configs,Integer deptId) {
		Integer amountStep = null;
		List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList =  configs.getBroadcastDeptConfigStatisticsList();
		for (BroadcastDeptConfigStatistics broadcastDeptConfigStatistics : broadcastDeptConfigStatisticsList) {
			if(broadcastDeptConfigStatistics.getBroadcastDeptId() == deptId) {
				amountStep = broadcastDeptConfigStatistics.getAmountStep();
			}
		}
		return amountStep;
	}
	
	/**
	 * 直接返回已配置相应小组的ERP组织ID，作为用户归属的组织ID，不能取用户当前的所属组织，因为用户可能会更换组织ID
	 * 例如流水表历史中的orgId=1 ，计算用户流水时  使用 userId = 1 and orgId=1
	 * 但当前用户更换了orgId=3，计算用户流水时  使用 userId = 1 and orgId=3，导致在流水表中查询不到该数据
	 * 又由于用户所属组织可能挂在不同orgId下，例如有用户流水挂在orgId=1 和 orgId=2 上，但是统计数据的时候
	 * 只会统计orgId=1且在部门管理中已配置到某个小组下的数据，这时如果 使用 userId = 1 and orgId in（1，2）就会使统计范围扩大
	 * @param userIds
	 * @return
	 */
	private List<Integer> getOrgIdByUserId(List<Integer> userIds) {
		List<Integer> orgIdListReturn = new ArrayList<>();
		if(CollectionUtils.isEmpty(userIds)) {
			return orgIdListReturn;
		}
		//获取目前配置的所有orgId
		List<BroadcastDeptRErpDeptEntity> broadcastDeptRErpDeptEntityList = broadcastDeptRErpDeptMapper.getAllBroadcastDeptRelate();
		//如果用户所在的orgIdStrs存在于配置的组织中，则进行返回
		if(CollectionUtils.isEmpty(broadcastDeptRErpDeptEntityList)) {
			return orgIdListReturn;
		}
		orgIdListReturn = broadcastDeptRErpDeptEntityList.stream().map(temp->temp.getErpDeptId()).collect(Collectors.toList());
		return orgIdListReturn;
	}
	

}
