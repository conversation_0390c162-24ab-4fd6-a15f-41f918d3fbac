package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.model.dto.BusinessChanceREQ;
import com.vedeng.erp.saleorder.model.dto.UnCollectedOrderInfoDto;
import com.vedeng.erp.saleorder.model.dto.ext.BusinessChanceStageCountDto;
import com.vedeng.erp.saleorder.model.query.WorkbenchDto;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.dao
 * @Date 2021/12/6 13:22
 */
public interface SaleorderWorkbenchMapper {
    /**
     * 待办事项-待审核订单
     * @param workbenchDto
     * @return
     */
    Integer getNoVerifyOrderInfo(WorkbenchDto workbenchDto);


    /**
     * 待办事项-获取未处理商机数量
     * @param businessChanceREQ
     * @return
     */
    Integer getUnHandleNum(BusinessChanceREQ businessChanceREQ);


    /**
     * 待办事项-获取待再次沟通商机数量
     * @param businessChanceREQ
     * @return
     */
    Integer getWaitCommunicateNum(BusinessChanceREQ businessChanceREQ);

    /**
     * 待办事项-未收款/部分收款订单
     * @param workbenchDto
     * @return
     */
    Integer getNoPaymentOrderInfo(WorkbenchDto workbenchDto);

    /**
     *待办事项-未收款/部分收款订单金额
     * @param workbenchDto
     * @return
     */
    BigDecimal getNoPaymentMoneyInfo(WorkbenchDto workbenchDto);

    /**
     * 待办事项-未开票订单
     * @param workbenchDto
     * @return
     */
    Map getNoInvoiceOrderInfo(WorkbenchDto workbenchDto);

    /**
     * 待跟踪事项-审核中订单
     * @param workbenchDto
     * @return
     */
    Integer getVerifyingOrderInfo(WorkbenchDto workbenchDto);

    /**
     * 待跟踪事项-未采购/部分采购订单
     * @param workbenchDto
     * @return
     */
    Integer getNoPurchaseOrderInfo(WorkbenchDto workbenchDto);

    /**
     * 待跟踪事项-未发货/部分发货订单
     * @param workbenchDto
     * @return
     */
    Integer getNoSendGoodsOrderInfo(WorkbenchDto workbenchDto);

    /**
     * 待跟踪事项-未完结售后单
     * @param workbenchDto
     * @return
     */
    Integer getNoCompletedAfterSaleInfo(WorkbenchDto workbenchDto);

    /**
     * 获取未归还账期总额、未逾期账期总额、逾期0-60天账期总额、逾期大于60天总额
     * @param userIdList
     * @return
     */
    UnCollectedOrderInfoDto getUnReturnedInfo(@Param("userIdList")List<Integer> userIdList);

    /**
     * 获取未发货/部分发货订单逾期交付订单数
     * @param workbenchDto
     * @return
     */
    Integer getNoNoSendOrderInfo(WorkbenchDto workbenchDto);

    /**
     * 新业绩考核待办-未开票订单数量
     *
     * @param subUserIdList 所有下属userId
     * @return 订单数量
     */
    Integer getNoInvoiceOrderNum(@Param("subUserIdList") List<Integer> subUserIdList);

    /**
     * 新业绩考核待办-合同未审核订单数量
     *
     * @param subUserIdList 所有下属userId
     * @return 订单数量
     */
    Integer getContractNotReviewedOrderNum(@Param("subUserIdList") List<Integer> subUserIdList);

    /**
     * 新业绩考核待办-确认单未审核订单数量
     *
     * @param subUserIdList 所有下属userId
     * @return 订单数量
     */
    Integer getConfirmationNotApprovedOrderNum(@Param("subUserIdList") List<Integer> subUserIdList);

    /**
     * 获取到款金额-退款金额
     * @param userIdList 用户ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 到款金额
     */
    BigDecimal getRealReceiveAmount(@Param("userIdList") List<Integer> userIdList, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 获取到款目标
     * @param userIdList 用户ID列表
     * @param year 年份
     * @param month 月份
     * @return 到款目标
     */
    BigDecimal getPaymentTarget(@Param("userIdList") List<Integer> userIdList, @Param("year") Integer year, @Param("month") Integer month);

    /**
     * 获取自有品牌销售占比
     * @param userIdList 用户ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 自有品牌销售占比数据
     */
    List<Map<String, Object>> getOwnBrandSalesRatio(@Param("userIdList") List<Integer> userIdList, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 获取预计成单商机金额
     * @param userIdList 用户ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 预计成单商机金额
     */
    BigDecimal getExpectedOrderOpportunityAmount(@Param("userIdList") List<Integer> userIdList, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 获取商机满足率相关数据
     * @param userIdList 用户ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 商机满足率相关数据
     */
    BigDecimal getOpportunitySatisfactionData(@Param("userIdList") List<Integer> userIdList, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 获取线索数
     * @param userIdList 用户ID列表
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 线索数
     */
    Integer getClueNum(@Param("userIdList") List<Integer> userIdList, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取无关联线索商机数
     * @param userIdList 用户ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 无关联线索商机数
     */
    Integer getUnrelatedClueOpportunityNum(@Param("userIdList") List<Integer> userIdList, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 获取商机数
     * @param userIdList 用户ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 商机数
     */
    Integer getOpportunityNum(@Param("userIdList") List<Integer> userIdList, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 获取各阶段商机数量
     * @param userIdList 用户ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 各阶段商机数量
     */
    List<BusinessChanceStageCountDto> getOpportunityStageCount(@Param("userIdList") List<Integer> userIdList, @Param("startTime") Long startTime, @Param("endTime") Long endTime);
 }