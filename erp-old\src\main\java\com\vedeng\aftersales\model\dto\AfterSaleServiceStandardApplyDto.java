package com.vedeng.aftersales.model.dto;

import com.vedeng.aftersales.model.AfterSaleServiceStandardApply;

public class AfterSaleServiceStandardApplyDto extends AfterSaleServiceStandardApply {

    /**
     * 名称
     */
    private String showName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 规格
     */
    private String spec;

    /**
     * 产品是否可安装
     */
    private Integer isInstallable;

    /**
     * 供应商是否维护
     */
    private String supplyMatainment;

    /**
     * 产品经理名称
     */
    private String managerName;

    /**
     * 产品助理名称
     */
    private String assitName;

    /**
     * 省市区json格式值
     */
    private String provinceCityJsonvalue;

    /**
     * 是否含售后标签
     */
    private String hasAfterSaleServiceLabel;

    private Integer afterSalesServiceLevel;

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getIsInstallable() {
        return isInstallable;
    }

    public void setIsInstallable(Integer isInstallable) {
        this.isInstallable = isInstallable;
    }

    public String getSupplyMatainment() {
        return supplyMatainment;
    }

    public void setSupplyMatainment(String supplyMatainment) {
        this.supplyMatainment = supplyMatainment;
    }

    public String getAssitName() {
        return assitName;
    }

    public void setAssitName(String assitName) {
        this.assitName = assitName;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getProvinceCityJsonvalue() {
        return provinceCityJsonvalue;
    }

    public void setProvinceCityJsonvalue(String provinceCityJsonvalue) {
        this.provinceCityJsonvalue = provinceCityJsonvalue;
    }

    public String getHasAfterSaleServiceLabel() {
        return hasAfterSaleServiceLabel;
    }

    public void setHasAfterSaleServiceLabel(String hasAfterSaleServiceLabel) {
        this.hasAfterSaleServiceLabel = hasAfterSaleServiceLabel;
    }

    public Integer getAfterSalesServiceLevel() {
        return afterSalesServiceLevel;
    }

    public void setAfterSalesServiceLevel(Integer afterSalesServiceLevel) {
        this.afterSalesServiceLevel = afterSalesServiceLevel;
    }
}
