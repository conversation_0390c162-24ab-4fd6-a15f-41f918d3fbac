package com.vedeng.erp.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity;
import com.vedeng.erp.system.dto.BaseCompanyInfoDetailDto;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.mapstruct.BaseCompanyInfoConvertor;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.erp.system.service.BaseCompanyInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class BaseCompanyInfoApiServiceImpl implements BaseCompanyInfoApiService {

    @Value("${erp_short_name}")
    private String erpShortName;

    @Autowired
    private BaseCompanyInfoConvertor baseCompanyInfoConvertor;

    @Autowired
    private BaseCompanyInfoService baseCompanyInfoService;


    @Override
    public BaseCompanyInfoDto getCurrentCompanyInfo(){
        return selectBaseCompanyByShortName(erpShortName);
    }



    @Override
    public BaseCompanyInfoDto selectBaseCompanyByCompanyName(String companyName) {
        BaseCompanyInfoEntity baseCompanyInfoEntity =  baseCompanyInfoService.selectByCompanyName(companyName);
        if(baseCompanyInfoEntity==null){
            return null;
        }
        //使用baseCompanyInfoConvertor 转换成dto
        BaseCompanyInfoDto baseCompanyInfoDto =  baseCompanyInfoConvertor.toDto(baseCompanyInfoEntity);
        initBaseCompanyInfoDtoDetail(baseCompanyInfoDto);
        return baseCompanyInfoDto;
    }

    @Override
    public BaseCompanyInfoDto selectBaseCompanyByShortName(String shortName) {
        BaseCompanyInfoEntity baseCompanyInfoEntity =  baseCompanyInfoService.selectByShortName(shortName);
        if(baseCompanyInfoEntity==null){
            return null;
        }
        //使用baseCompanyInfoConvertor 转换成dto
        BaseCompanyInfoDto baseCompanyInfoDto =  baseCompanyInfoConvertor.toDto(baseCompanyInfoEntity);
        initBaseCompanyInfoDtoDetail(baseCompanyInfoDto);
        return baseCompanyInfoDto;
    }

    @Override
    public List<BaseCompanyInfoDto> selectBaseCompanyByCompanyNames(List<String> companyNames) {
        List<BaseCompanyInfoEntity> baseCompanyInfoEntities = baseCompanyInfoService.selectByCompanyNames(companyNames);
        if(baseCompanyInfoEntities==null){
            return Collections.emptyList();
        }
        List<BaseCompanyInfoDto> baseCompanyInfoDtos =  baseCompanyInfoConvertor.toDto(baseCompanyInfoEntities);
        for(BaseCompanyInfoDto baseCompanyInfoDto : baseCompanyInfoDtos){
            initBaseCompanyInfoDtoDetail(baseCompanyInfoDto);
        }
        return baseCompanyInfoDtos;
    }

    @Override
    public List<BaseCompanyInfoDto> findAll() {
        List<BaseCompanyInfoEntity> baseCompanyInfoEntities = baseCompanyInfoService.findAll();
        List<BaseCompanyInfoDto> baseCompanyInfoDtos =  baseCompanyInfoConvertor.toDto(baseCompanyInfoEntities);
        for(BaseCompanyInfoDto baseCompanyInfoDto : baseCompanyInfoDtos){
            initBaseCompanyInfoDtoDetail(baseCompanyInfoDto);
        }
        return baseCompanyInfoDtos;
    }

    @Override
    public int updateByPrimaryKeySelective(BaseCompanyInfoDto baseCompanyInfoDto) {
        BaseCompanyInfoEntity entity = baseCompanyInfoConvertor.toEntity(baseCompanyInfoDto);
        return baseCompanyInfoService.updateByPrimaryKeySelective(entity);
    }

    private void initBaseCompanyInfoDtoDetail(BaseCompanyInfoDto baseCompanyInfoDto){
        String detailJson  = baseCompanyInfoDto.getDetailJson();
        if(StringUtils.isBlank(detailJson)){
            return;
        }
        try{
            BaseCompanyInfoDetailDto detailDto = JSONObject.parseObject(detailJson, BaseCompanyInfoDetailDto.class);
            baseCompanyInfoDto.setBaseCompanyInfoDetailDto(detailDto);
        } catch (Exception e) {
            log.warn("解析公司详细信息失败:{}",detailJson);
        }
    }

}