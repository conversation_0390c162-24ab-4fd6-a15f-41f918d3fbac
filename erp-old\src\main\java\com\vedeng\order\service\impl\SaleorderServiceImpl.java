package com.vedeng.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.newtask.data.saleorder.SaleorderCurrentOrgAndUserIdSync;
import com.rabbitmq.MarketMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.activiti.constants.ProcessConstants;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.dao.AfterSalesDetailMapper;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.dao.RInvoiceJInvoiceMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.RInvoiceJInvoice;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.model.vo.RegionVo;
import com.vedeng.common.constant.*;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.exception.BusinessException;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.http.HttpClientUtils4Stock;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.model.ReqVo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.model.ResultInfo4Stock;
import com.vedeng.common.page.Page;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.*;
import com.vedeng.coupon.service.dto.CouponInfo;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodSettlementTypeEnum;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.erp.aftersale.dto.AfterSalesDetailApiDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.confirmrecord.dto.ConfirmRecordDto;
import com.vedeng.erp.confirmrecord.enums.ConfirmRecordBusinessTypeEnum;
import com.vedeng.erp.confirmrecord.service.ConfirmRecordApiService;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.saleorder.api.OrderInfoSyncService;
import com.vedeng.erp.saleorder.api.QuoteInfoService;
import com.vedeng.erp.saleorder.api.SaleorderInfoQueryApiService;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.dto.*;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.erp.saleorder.service.OrderGoodsLowerPriceApiService;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.service.SaleOrderDataApiService;
import com.vedeng.erp.system.SyncDataTypeEnum;
import com.vedeng.erp.system.api.SyncDataErpApiService;
import com.vedeng.erp.system.api.SyncOutInRelateApiService;
import com.vedeng.erp.system.dto.*;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.erp.system.service.VerifiesInfoApiService;
import com.vedeng.erp.trader.dto.SpecialSalesDto;
import com.vedeng.erp.trader.service.SpecialSalesApiService;
import com.vedeng.erp.wms.service.OrderPeerApiService;
import com.vedeng.finance.dao.CapitalBillDetailMapper;
import com.vedeng.finance.dao.InvoiceApplyMapper;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.model.SimpleMedicalCategory;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.flash.dto.SpecialDeliveryInfoDto;
import com.vedeng.flash.model.EarlyWarningTask;
import com.vedeng.goods.dao.*;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.model.GoodsSettlementPrice;
import com.vedeng.goods.service.GoodsChannelPriceService;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.infrastructure.sms.service.SmsService;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.dao.OutboundBatchesRecodeMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.model.OutboundBatchesRecode;
import com.vedeng.logistics.model.SaleorderWarehouseComments;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.market.api.dto.response.mycoupon.MyCouponSkuSubDTO;
import com.vedeng.market.api.dto.response.mycoupon.MyCouponSkuSubResponse;
import com.vedeng.order.chain.RiskHandlerKeyConstant;
import com.vedeng.order.chain.model.SaleorderRiskModelVo;
import com.vedeng.order.dao.*;
import com.vedeng.order.enums.PaymentTypeEnum;
import com.vedeng.order.model.*;
import com.vedeng.order.model.coupon.CouponOrderData;
import com.vedeng.order.model.coupon.CouponOrderDetailData;
import com.vedeng.order.model.dto.*;
import com.vedeng.order.model.query.LabelQuery;
import com.vedeng.order.model.vo.*;
import com.vedeng.order.service.*;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import com.vedeng.passport.api.wechat.dto.req.ReqTemplateVariable;
import com.vedeng.passport.api.wechat.dto.template.TemplateVar;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.dao.TraderAddressGenerateMapper;
import com.vedeng.system.dao.VerifiesInfoMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.TraderAddressGenerate;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.service.TodoListService;
import com.vedeng.trader.dao.*;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.vo.TraderAddressVo;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderFinanceVo;
import com.vedeng.trader.service.TraderCustomerService;
import com.vedeng.common.core.utils.PdfUtil;
import com.wms.constant.CancelReasonConstant;
import com.wms.service.CancelTypeService;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.thymeleaf.util.MapUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.vedeng.common.constant.OrderGoodsAptitudeConstants.KEY_AUTOCHECK_APTITUDE;

@Service("saleorderService")
@Slf4j
public class SaleorderServiceImpl extends BaseServiceimpl implements SaleorderService  {

	private static final int MAX_RETRY_COUNT = 3;
	Logger logger= LoggerFactory.getLogger(SaleorderServiceImpl.class);

	@Resource
	private OutboundBatchesRecodeMapper outboundBatchesRecodeMapper;

	@Autowired
	@Qualifier("communicateRecordMapper")
	private CommunicateRecordMapper communicateRecordMapper;
	@Autowired
	private RedisUtils redisUtils;
	@Autowired
	@Qualifier("userMapper")
	private UserMapper userMapper;

	@Autowired
	private SaleorderCouponMapper saleorderCouponMapper;

	@Autowired
	@Qualifier("goodsSettlementPriceMapper")
	private GoodsSettlementPriceMapper goodsSettlementPriceMapper;

	@Autowired
	@Qualifier("goodsChannelPriceService")
	private GoodsChannelPriceService goodsChannelPriceService;

	@Autowired
	@Qualifier("traderCustomerService")
	private TraderCustomerService traderCustomerService;// 客户-交易者

	@Autowired
	@Qualifier("regionService")
	private RegionService regionService;// 自动注入regionService

	@Autowired
	@Qualifier("rCategoryJUserMapper")
	private RCategoryJUserMapper rCategoryJUserMapper;

	@Autowired
	@Qualifier("quoteService")
	private QuoteService quoteService;

	@Autowired
	@Qualifier("userService")
	private UserService userService;

	@Autowired
	@Qualifier("saleorderMapper")
	private SaleorderMapper saleorderMapper;

	@Autowired
	private BuyorderGoodsMapper buyorderGoodsMapper;

	@Autowired
	private BuyorderMapper buyorderMapper;

	@Lazy
	@Autowired
	@Qualifier("goodsService")
	private GoodsService goodsService;

	@Autowired
	@Qualifier("traderMapper")
	private TraderMapper traderMapper;

	@Autowired
	@Qualifier("expressMapper")
	ExpressMapper expressMapper;

	@Autowired
	@Qualifier("quoteorderMapper")
	private QuoteorderMapper  quoteorderMapper;

	@Autowired
	@Qualifier("quoteorderGoodsMapper")
	private QuoteorderGoodsMapper  quoteorderGoodsMapper;

	@Autowired
	@Qualifier("verifiesRecordService")
	private VerifiesRecordService verifiesRecordService;

	@Autowired
	@Qualifier("actionProcdefService")
	protected ActionProcdefService actionProcdefService;

	@Autowired // 自动装载
	protected ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

	@Autowired
	private WebAccountMapper webAccountMapper;

	@Autowired
	private InvoiceService invoiceService;

	@Autowired
	private OrderPeerApiService orderPeerApiService;

	@Autowired
	private SaleorderGoodsMapper saleorderGoodsMapper;

	@Autowired
	private WarehouseStockService warehouseStockService;

	@Autowired
	private InvoiceMapper invoiceMapper;

	@Lazy
	@Autowired
	ExpressService expressService;

	@Autowired
	private TraderCertificateMapper traderCertificateMapper;

	@Autowired
	private TraderMedicalCategoryMapper traderMedicalCategoryMapper;

	@Autowired
	private TraderCustomerMapper traderCustomerMapper;

	@Autowired
	private QuoteorderConsultReplyMapper quoteorderConsultReplyMapper;

	@Autowired
	private SaleorderInfoQueryApiService saleorderInfoQueryApiService;

	@Autowired
	private VerifiesInfoApiService verifiesInfoApiService;

	@Autowired
	private FirstEngageMapper firstEngageMapper;
	@Value("${vx_service}")
	protected String vxService;
	@Value("${orderPayMiddle}")
	protected String orderPayMiddle;//待支付订单消息提醒
	@Value("${wx.saleorder.valid.tempid}")
	protected String orderValidMiddle;//待支付订单消息提醒

	@Value("${oss_http}")
	private String ossHttp;
	/**
	 * OSS地址
	 */
	@Value("${oss_url}")
	private String ossUrl;
	/**
	 * oss文档路径
	 */
	@Value("${oss_file_path}")
	private String ossFilePath;

	@Value("${ht_remove_blank_page}")
	private Boolean isRemoveBlankPage;

	@Value("${mjx_page}")
	protected String mjxPage;

	@Autowired
	private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Autowired
	private CoreSpuGenerateMapper coreSpuGenerateMapper;

	@Autowired
	private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseServiceImpl;

	@Value("${erp.domain}")
	private String erpDomain;

	@Value("${html2Pdf.domain}")
	private String html2PdfDomain;

	@Autowired
	private MarketMsgProducer msgProducer;

	@Autowired
	private OssUtilsService ossUtilsService;

	@Autowired
	private AttachmentMapper attachmentMapper;

	@Autowired
	private RiskCheckService riskCheckService;

	@Autowired
	@Qualifier("afterSalesGoodsMapper")
	private AfterSalesGoodsMapper afterSalesGoodsMapper;

	@Autowired
	private CapitalBillDetailMapper capitalBillDetailMapper;

	@Autowired
	private RemarkComponentMapper remarkComponentMapper;

	@Autowired
	private RemarkComponentServiceImpl remarkComponentService;

	@Autowired
	private OrderReviewProcessService orderReviewProcessService;

	@Autowired
	private EarlyWarningTaskMapper earlyWarningTaskMapper;

	@Autowired
	private OrderAccountPeriodService orderAccountPeriodService;
	@Resource
	private GoodsMapper goodsMapper;
	@Autowired
	private AfterSalesMapper afterSalesMapper;
	@Autowired
	private AfterSalesDetailMapper afterSalesDetailMapper;
	@Autowired
	private TraderContactMapper traderContactMapper;
	@Autowired
	private RInvoiceJInvoiceMapper rInvoiceJInvoiceMapper;
	@Autowired
	private TraderFinanceMapper traderFinanceMapper;

	@Autowired
	private TraderAddressMapper traderAddressMapper;

	@Autowired
	private TraderContactGenerateMapper traderContactGenerateMapper;

	@Resource
	private OrderCommonService orderCommonService;

	@Resource
	private TraderAddressGenerateMapper traderAddressGenerateMapper;

	@Value("${confirm_order_url}")
	private String confirmOrderUrl;
	@Resource
	private SaleorderModifyApplyMapper saleorderModifyApplyMapper;

	@Autowired
	private OrderInfoSyncService orderInfoSyncService;

	@Autowired
	private CancelTypeService cancelTypeService;


	@Autowired
	private ConfirmRecordApiService confirmRecordApiService;

	@Autowired
	private RTraderJUserMapper rTraderJUserMapper;

	@Value("#{'${notSendMessageTranderIds}'.split(',')}")
	private List<Integer> notSendMessageTranderIds;

	@Resource
	private SaleorderModifyApplyGoodsMapper saleorderModifyApplyGoodsMapper;

	@Autowired
	private SmsService smsService;


	@Autowired
	private BdOrderAutoAuditService bdOrderAutoAuditService;
	@Resource
	private SaleorderSyncService saleorderSyncService;
	@Resource
	private SaleorderDataSyncService saleorderDataSyncService;

	@Resource
	private BussinessChanceMapper bussinessChanceMapper;

	@Resource
	private QuoteInfoService quoteInfoService;

	@Resource
	private SaleContractAutoAuditService saleContractAutoAuditService;

	@Resource
	private OrderGoodsLowerPriceApiService orderGoodsLowerPriceApiService;

	@Autowired
	private OrganizationMapper organizationMapper;

	@Autowired
	private InvoiceApplyMapper invoiceApplyMapper;

	/**
	 * 销售订单审核时,账期订单白名单客户
	 */
	@Value("${saleorder_verify_white_list}")
	private String saleorderVerifyWhiteList;

	/**
	 * 销售订单审核时,需要排除的销售总监
	 */
	@Value("${saleorder_verify_exclude_userId}")
	private String saleorderVerifyExcludeUserId;


	private static Integer CONFIRMATION_FORM_AUDIT_PASS = 2;

	private static Integer CONFIRMATION_FORM_AUDIT_REFUSE = 3;

	@Autowired
	SpecialSalesApiService specialSalesApiService;

	@Autowired
	BussinessChanceService bussinessChanceService;
	@Autowired
	TodoListService todoListService;
	@Autowired
	HcSaleorderService hcSaleorderService;
	@Autowired
	VedengSoapService vedengSoapService;
	@Autowired
	private AfterSalesApiService afterSalesApiService;
	@Autowired
	private OrderTerminalApiService orderTerminalApiService;

	/**
	 * 销售订单非账期类型
	 */
	public static final List<Integer> NO_PAYMENT_PERIOD_TYPE = Collections.singletonList(
			OrderConstant.PREPAY_100_PERCENT
	);

	@Override
	public ResultInfo checkGoodAptitude(Integer saleorderId) {
		String str = "";
		try {
			Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderId);
			if (saleorder == null || saleorder.getTraderId() == null) {
				return new ResultInfo(-1, OrderGoodsAptitudeEnum.ORDER_IS_NULL.getMessage(), OrderGoodsAptitudeEnum.ORDER_IS_NULL.getMessageType());
			}
			TraderCustomer traderCustomer = traderCustomerMapper.getBaseCustomerByTraderId(saleorder.getTraderId());
			if (traderCustomer == null || traderCustomer.getCustomerNature() == null || traderCustomer.getCustomerNature() == 0) {
				return new ResultInfo(-1, OrderGoodsAptitudeEnum.CUSTOMER_IS_NULL.getMessage(),OrderGoodsAptitudeEnum.CUSTOMER_IS_NULL.getMessageType());
			}
			List<TraderCertificate> certificates = traderCertificateMapper.getCertificateListByTraderId(saleorder.getTraderId(),ErpConst.ONE);
			//新自动审核规则，终端和分销都需要审核营业执照
			int businessCertificateVaild = checkCertificateVaild(certificates, SysOptionConstant.ID_25);
			if (businessCertificateVaild == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
				return new ResultInfo(-1, OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_IS_NULL.getMessage(), OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_IS_NULL.getMessageType());
			} else if (businessCertificateVaild == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
				return new ResultInfo(-1, OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_DATE_OUT.getMessage(), OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_DATE_OUT.getMessageType());
			}
			if (SysOptionConstant.ID_465.equals(traderCustomer.getCustomerNature())) {
				//处理分销
				int firstCertificateValid = checkMedicalVaild(certificates);//一类的医疗器械需要判断营业执照是否包含医疗器械
				int secondCertificateValid = checkCertificateVaild(certificates, SysOptionConstant.ID_28);
				int thirdCertificateValid = checkCertificateVaild(certificates, SysOptionConstant.ID_29);

				TraderMedicalCategory queryCategory = new TraderMedicalCategory();
				queryCategory.setTraderId(saleorder.getTraderId());
				queryCategory.setTraderType(1);
				List<TraderMedicalCategory> medicalCategories = traderMedicalCategoryMapper.getMedicalCategoryList(queryCategory);
				HashMap<Integer, HashSet<Integer>> categoryMap = getSetFromMedicalCategory(medicalCategories);
				List<SaleorderGoods> goods = saleorderGoodsMapper.getGoodsSkuByOrderId(saleorderId);
				if (!CollectionUtils.isEmpty(goods)) {
					for (SaleorderGoods g : goods) {
						if (g == null || StringUtil.isEmpty(g.getSku())) {
							continue;
						}
						SimpleMedicalCategory category = firstEngageMapper.getSimpleMedicalCategory(g.getSku());
						if (category == null) {
							return new ResultInfo(-1, OrderGoodsAptitudeEnum.CATEGORY_IS_NULL.getMessage(),OrderGoodsAptitudeEnum.CATEGORY_IS_NULL.getMessageType());
						}
						//
						if(category.getOldMedicalCategory()!=null&&category.getOldMedicalCategory()==0){
							category.setOldMedicalCategory(null);
						}
						//新的业务逻辑：根据sku是否有注册证信息来判断sku是否为医疗器械
						if (category.getRegistrationNumberId() != null && category.getRegistrationNumberId() > 0) {
							//是医疗器械
							if(SysOptionConstant.ID_968.equals(category.getManageCategory())){
								//一类判断营业执照是否包括医疗器械
								if(firstCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_WITHOUT_MED){
									return new ResultInfo(-1,OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_WITHOUT_MED.getMessage(),OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_WITHOUT_MED.getMessageType());
								}
							}else if (SysOptionConstant.ID_969.equals(category.getManageCategory())) {
								//二类
								if (secondCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
									return new ResultInfo(-1, OrderGoodsAptitudeEnum.SECOND_CERTIFICATE_IS_NULL.getMessage(), OrderGoodsAptitudeEnum.SECOND_CERTIFICATE_IS_NULL.getMessageType());
								} else if (secondCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
									return new ResultInfo(-1, OrderGoodsAptitudeEnum.SECOND_CERTIFICATE_DATE_OUT.getMessage(),OrderGoodsAptitudeEnum.SECOND_CERTIFICATE_DATE_OUT.getMessageType());
								}
								ResultInfo categoryResult = checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_SECOND_OLD_MEDICAL_CATEGORY),
										category.getOldMedicalCategory(), SysOptionConstant.ID_969);
								if (categoryResult.getCode() == -1) {
									categoryResult = checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_SECOND_NEW_MEDICAL_CATEGORY),
											category.getNewMedicalCategory(), SysOptionConstant.ID_969);
									if (categoryResult.getCode() == -1) {
										return categoryResult;
									}
								}

							} else if (SysOptionConstant.ID_970.equals(category.getManageCategory())) {
								//三类
								if (thirdCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
									return new ResultInfo(-1, OrderGoodsAptitudeEnum.THIRD_CERTIFICATE_IS_NULL.getMessage(), OrderGoodsAptitudeEnum.THIRD_CERTIFICATE_IS_NULL.getMessageType());
								} else if (thirdCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
									return new ResultInfo(-1, OrderGoodsAptitudeEnum.THIRD_CERTIFICATE_DATE_OUT.getMessage(), OrderGoodsAptitudeEnum.THIRD_CERTIFICATE_DATE_OUT.getMessageType());
								}
								ResultInfo categoryResult = checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_THIRD_OLD_MEDICAL_CATEGORY)
										, category.getOldMedicalCategory(), SysOptionConstant.ID_970);
								if (categoryResult.getCode() == -1) {
									categoryResult = checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_THIRD_NEW_MEDICAL_CATEGORY)
											, category.getNewMedicalCategory(), SysOptionConstant.ID_970);
									if (categoryResult.getCode() == -1) {
										return categoryResult;
									}
								}

							}
						}
					}
				}
			} else if (SysOptionConstant.ID_466.equals(traderCustomer.getCustomerNature())) {
				//处理终端
				int operateCertificateValid = checkCertificateVaild(certificates, SysOptionConstant.ID_438);//医疗机构执业许可证
				int chnMedicalCertificateValid = checkCertificateVaild(certificates, SysOptionConstant.CHINESE_MEDICAL_CLINIC);//中医诊所备案证
				int animalCertificateValid = checkCertificateVaild(certificates, SysOptionConstant.ANIMAL_CLINIC);//动物诊疗许可证
				int firstCertificateValid = checkMedicalVaild(certificates);
				logger.info("operateCertificateValid{},firstCertificateValid{}",operateCertificateValid,firstCertificateValid);
				List<SaleorderGoods> goods = saleorderGoodsMapper.getGoodsSkuByOrderId(saleorderId);
				if (!CollectionUtils.isEmpty(goods)) {
					boolean checkStatus = false;
					String checkStr = "";
					for (SaleorderGoods g : goods) {
						if (g == null || StringUtil.isEmpty(g.getSku())) {
							continue;
						}
						SimpleMedicalCategory category = firstEngageMapper.getSimpleMedicalCategory(g.getSku());
						if (category == null) {
							return new ResultInfo(-1, OrderGoodsAptitudeEnum.CATEGORY_IS_NULL.getMessage(), OrderGoodsAptitudeEnum.CATEGORY_IS_NULL.getMessageType());
						}
						//新的业务逻辑：根据sku是否有注册证信息来判断sku是否为医疗器械
						if (category.getRegistrationNumberId() != null && category.getRegistrationNumberId() > 0) {
							//是医疗器械
							if(SysOptionConstant.ID_968.equals(category.getManageCategory())){
								//一类判断营业执照是否包括医疗器械
								if(firstCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_WITHOUT_MED){
									return new ResultInfo(-1,OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_WITHOUT_MED.getMessage(),OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_WITHOUT_MED.getMessageType());
								}
							}else if (SysOptionConstant.ID_969.equals(category.getManageCategory()) ||
									SysOptionConstant.ID_970.equals(category.getManageCategory())) {
								//二类三类
//								//获取所有二类三类涉及的编码
//								HashMap<Integer, HashSet<Integer>> categoryMap = getSetByChnMedical();
								//不存在存在旧国标6827且不存在新国标20的情况
								boolean b = judgeSku(category);
								if(b){
									StringBuffer validMsg = new StringBuffer("");
									List<String> messageTypeList = new ArrayList<>();
									//医疗机构执业许可证和动物诊疗许可证都不存在的情况->不通过
									if(operateCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD
											&& animalCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
										if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
											if(validMsg == null || validMsg.length() == 0){
												validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_IS_NULL.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_IS_NULL.getMessageType());
											}
										} else if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_DATE_OUT.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_DATE_OUT.getMessageType());
											}
										}
										if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_IS_NULL.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_IS_NULL.getMessageType());
											}
										} else if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_DATE_OUT.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_DATE_OUT.getMessageType());
											}
										}
										return new ResultInfo(-1,validMsg.toString(),String.join(",",messageTypeList));
									}else {
										if(operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
											checkStatus = true;
											if(checkStr.equals("")){
												checkStr = "客户有医疗机构执业许可证";
											}
										}else if(animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
											checkStatus = true;
											if(checkStr.equals("")){
												checkStr = "客户有动物诊疗许可证";
											}
										}
									}
								}else {
									//医疗机构执业许可证和动物诊疗许可证，中医诊所备案证都不存在的情况->不通过
									if(operateCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD
											&& animalCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD
											&& chnMedicalCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
										StringBuffer validMsg = new StringBuffer("");
										List<String> messageTypeList = new ArrayList<>();
										if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_IS_NULL.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_IS_NULL.getMessageType());
											}
										} else if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_DATE_OUT.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_DATE_OUT.getMessageType());
											}
										}
										if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_IS_NULL.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_IS_NULL.getMessageType());
											}
										} else if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_DATE_OUT.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_DATE_OUT.getMessageType());
											}
										}
										if (chnMedicalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.CHNMEDICAL_CERTIFICATE_IS_NULL.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.CHNMEDICAL_CERTIFICATE_IS_NULL.getMessageType());
											}
										} else if (chnMedicalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.CHNMEDICAL_CERTIFICATE_DATE_OUT.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.CHNMEDICAL_CERTIFICATE_DATE_OUT.getMessageType());
											}
										}
										return new ResultInfo(-1,validMsg.toString(),String.join(",",messageTypeList));
									}else {
										if(operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
											checkStatus = true;
											if(checkStr.equals("")){
												checkStr = "客户有医疗机构执业许可证";
											}
										}else if(chnMedicalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
											checkStatus = true;
											if(checkStr.equals("")){
												checkStr = "客户有中医诊所备案证";
											}
										}else if(animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
											checkStatus = true;
											if(checkStr.equals("")){
												checkStr = "客户有动物诊疗许可证";
											}
										}
									}
								}
							}
						}
					}
					if(checkStatus){
						return new ResultInfo(1,checkStr);
					}
				}

			}
			return new ResultInfo(0, "操作成功");
		}catch (Exception ex){
			logger.error("订单自动审核出现异常",ex);
			return new ResultInfo(-1,"自动审核失败");
		}
	}

	@Override
	public ResultInfo checkGoodAptitude(Integer traderId, List<String> skuList) {
		String str = "";
		try {
			TraderCustomer traderCustomer = traderCustomerMapper.getBaseCustomerByTraderId(traderId);
			if (traderCustomer == null || traderCustomer.getCustomerNature() == null || traderCustomer.getCustomerNature() == 0) {
				return new ResultInfo(-1, OrderGoodsAptitudeEnum.CUSTOMER_IS_NULL.getMessage(),OrderGoodsAptitudeEnum.CUSTOMER_IS_NULL.getMessageType());
			}
			List<TraderCertificate> certificates = traderCertificateMapper.getCertificateListByTraderId(traderId,ErpConst.ONE);
			//新自动审核规则，终端和分销都需要审核营业执照
			int businessCertificateVaild = checkCertificateVaild(certificates, SysOptionConstant.ID_25);
			if (businessCertificateVaild == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
				return new ResultInfo(-1, OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_IS_NULL.getMessage(), OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_IS_NULL.getMessageType());
			} else if (businessCertificateVaild == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
				return new ResultInfo(-1, OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_DATE_OUT.getMessage(), OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_DATE_OUT.getMessageType());
			}
			if (SysOptionConstant.ID_465.equals(traderCustomer.getCustomerNature())) {
				//处理分销
				int firstCertificateValid = checkMedicalVaild(certificates);//一类的医疗器械需要判断营业执照是否包含医疗器械
				int secondCertificateValid = checkCertificateVaild(certificates, SysOptionConstant.ID_28);
				int thirdCertificateValid = checkCertificateVaild(certificates, SysOptionConstant.ID_29);

				TraderMedicalCategory queryCategory = new TraderMedicalCategory();
				queryCategory.setTraderId(traderId);
				queryCategory.setTraderType(1);
				List<TraderMedicalCategory> medicalCategories = traderMedicalCategoryMapper.getMedicalCategoryList(queryCategory);
				HashMap<Integer, HashSet<Integer>> categoryMap = getSetFromMedicalCategory(medicalCategories);
				if (!CollectionUtils.isEmpty(skuList)) {
					for (String skuNo : skuList) {
						SimpleMedicalCategory category = firstEngageMapper.getSimpleMedicalCategory(skuNo);
						if (category == null) {
							return new ResultInfo(-1, skuNo+ OrderGoodsAptitudeEnum.CATEGORY_IS_NULL.getMessage(),OrderGoodsAptitudeEnum.CATEGORY_IS_NULL.getMessageType());
						}
						//
						if(category.getOldMedicalCategory()!=null&&category.getOldMedicalCategory()==0){
							category.setOldMedicalCategory(null);
						}
						//新的业务逻辑：根据sku是否有注册证信息来判断sku是否为医疗器械
						if (category.getRegistrationNumberId() != null && category.getRegistrationNumberId() > 0) {
							//是医疗器械
							if(SysOptionConstant.ID_968.equals(category.getManageCategory())){
								//一类判断营业执照是否包括医疗器械
								if(firstCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_WITHOUT_MED){
									return new ResultInfo(-1,skuNo+ OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_WITHOUT_MED.getMessage(),OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_WITHOUT_MED.getMessageType());
								}
							}else if (SysOptionConstant.ID_969.equals(category.getManageCategory())) {
								//二类
								if (secondCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
									return new ResultInfo(-1, skuNo+ OrderGoodsAptitudeEnum.SECOND_CERTIFICATE_IS_NULL.getMessage(), OrderGoodsAptitudeEnum.SECOND_CERTIFICATE_IS_NULL.getMessageType());
								} else if (secondCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
									return new ResultInfo(-1, skuNo+ OrderGoodsAptitudeEnum.SECOND_CERTIFICATE_DATE_OUT.getMessage(),OrderGoodsAptitudeEnum.SECOND_CERTIFICATE_DATE_OUT.getMessageType());
								}
								ResultInfo categoryResult = checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_SECOND_OLD_MEDICAL_CATEGORY),
										category.getOldMedicalCategory(), SysOptionConstant.ID_969);
								if (categoryResult.getCode() == -1) {
									categoryResult = checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_SECOND_NEW_MEDICAL_CATEGORY),
											category.getNewMedicalCategory(), SysOptionConstant.ID_969);
									if (categoryResult.getCode() == -1) {
										categoryResult.setMessage(skuNo+ categoryResult.getMessage());
										return categoryResult;
									}
								}

							} else if (SysOptionConstant.ID_970.equals(category.getManageCategory())) {
								//三类
								if (thirdCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
									return new ResultInfo(-1, skuNo+ OrderGoodsAptitudeEnum.THIRD_CERTIFICATE_IS_NULL.getMessage(), OrderGoodsAptitudeEnum.THIRD_CERTIFICATE_IS_NULL.getMessageType());
								} else if (thirdCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
									return new ResultInfo(-1, skuNo+ OrderGoodsAptitudeEnum.THIRD_CERTIFICATE_DATE_OUT.getMessage(), OrderGoodsAptitudeEnum.THIRD_CERTIFICATE_DATE_OUT.getMessageType());
								}
								ResultInfo categoryResult = checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_THIRD_OLD_MEDICAL_CATEGORY)
										, category.getOldMedicalCategory(), SysOptionConstant.ID_970);
								if (categoryResult.getCode() == -1) {
									categoryResult = checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_THIRD_NEW_MEDICAL_CATEGORY)
											, category.getNewMedicalCategory(), SysOptionConstant.ID_970);
									if (categoryResult.getCode() == -1) {
										categoryResult.setMessage(skuNo+ categoryResult.getMessage());
										return categoryResult;
									}
								}

							}
						}
					}
				}
			} else if (SysOptionConstant.ID_466.equals(traderCustomer.getCustomerNature())) {
				//处理终端
				int operateCertificateValid = checkCertificateVaild(certificates, SysOptionConstant.ID_438);//医疗机构执业许可证
				int chnMedicalCertificateValid = checkCertificateVaild(certificates, SysOptionConstant.CHINESE_MEDICAL_CLINIC);//中医诊所备案证
				int animalCertificateValid = checkCertificateVaild(certificates, SysOptionConstant.ANIMAL_CLINIC);//动物诊疗许可证
				int firstCertificateValid = checkMedicalVaild(certificates);
				logger.info("operateCertificateValid{},firstCertificateValid{}",operateCertificateValid,firstCertificateValid);
				if (!CollectionUtils.isEmpty(skuList)) {
					boolean checkStatus = false;
					String checkStr = "";
					for (String skuNo : skuList) {
						SimpleMedicalCategory category = firstEngageMapper.getSimpleMedicalCategory(skuNo);
						if (category == null) {
							return new ResultInfo(-1, skuNo+ OrderGoodsAptitudeEnum.CATEGORY_IS_NULL.getMessage(), OrderGoodsAptitudeEnum.CATEGORY_IS_NULL.getMessageType());
						}
						//新的业务逻辑：根据sku是否有注册证信息来判断sku是否为医疗器械
						if (category.getRegistrationNumberId() != null && category.getRegistrationNumberId() > 0) {
							//是医疗器械
							if(SysOptionConstant.ID_968.equals(category.getManageCategory())){
								//一类判断营业执照是否包括医疗器械
								if(firstCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_WITHOUT_MED){
									return new ResultInfo(-1,skuNo+  OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_WITHOUT_MED.getMessage(),OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_WITHOUT_MED.getMessageType());
								}
							}else if (SysOptionConstant.ID_969.equals(category.getManageCategory()) ||
									SysOptionConstant.ID_970.equals(category.getManageCategory())) {
								//二类三类
//								//获取所有二类三类涉及的编码
//								HashMap<Integer, HashSet<Integer>> categoryMap = getSetByChnMedical();
								//不存在存在旧国标6827且不存在新国标20的情况
								boolean b = judgeSku(category);
								if(b){
									StringBuffer validMsg = new StringBuffer("");
									List<String> messageTypeList = new ArrayList<>();
									//医疗机构执业许可证和动物诊疗许可证都不存在的情况->不通过
									if(operateCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD
											&& animalCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
										if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
											if(validMsg == null || validMsg.length() == 0){
												validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_IS_NULL.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_IS_NULL.getMessageType());
											}
										} else if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_DATE_OUT.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_DATE_OUT.getMessageType());
											}
										}
										if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_IS_NULL.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_IS_NULL.getMessageType());
											}
										} else if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_DATE_OUT.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_DATE_OUT.getMessageType());
											}
										}
										return new ResultInfo(-1, skuNo+ validMsg.toString(),String.join(",",messageTypeList));
									}else {
										if(operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
											checkStatus = true;
											if(checkStr.equals("")){
												checkStr = "客户有医疗机构执业许可证";
											}
										}else if(animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
											checkStatus = true;
											if(checkStr.equals("")){
												checkStr = "客户有动物诊疗许可证";
											}
										}
									}
								}else {
									//医疗机构执业许可证和动物诊疗许可证，中医诊所备案证都不存在的情况->不通过
									if(operateCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD
											&& animalCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD
											&& chnMedicalCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
										StringBuffer validMsg = new StringBuffer("");
										List<String> messageTypeList = new ArrayList<>();
										if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_IS_NULL.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_IS_NULL.getMessageType());
											}
										} else if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_DATE_OUT.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_DATE_OUT.getMessageType());
											}
										}
										if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_IS_NULL.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_IS_NULL.getMessageType());
											}
										} else if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_DATE_OUT.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_DATE_OUT.getMessageType());
											}
										}
										if (chnMedicalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.CHNMEDICAL_CERTIFICATE_IS_NULL.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.CHNMEDICAL_CERTIFICATE_IS_NULL.getMessageType());
											}
										} else if (chnMedicalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
											if(validMsg == null || validMsg.length() == 0) {
												validMsg.append(OrderGoodsAptitudeEnum.CHNMEDICAL_CERTIFICATE_DATE_OUT.getMessage());
												messageTypeList.add(OrderGoodsAptitudeEnum.CHNMEDICAL_CERTIFICATE_DATE_OUT.getMessageType());
											}
										}
										return new ResultInfo(-1, skuNo+ validMsg.toString(),String.join(",",messageTypeList));
									}else {
										if(operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
											checkStatus = true;
											if(checkStr.equals("")){
												checkStr = "客户有医疗机构执业许可证";
											}
										}else if(chnMedicalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
											checkStatus = true;
											if(checkStr.equals("")){
												checkStr = "客户有中医诊所备案证";
											}
										}else if(animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD){
											checkStatus = true;
											if(checkStr.equals("")){
												checkStr = "客户有动物诊疗许可证";
											}
										}
									}
								}
							}
						}
					}
					if(checkStatus){
						return new ResultInfo(1,checkStr);
					}
				}

			}
			return new ResultInfo(0, "操作成功");
		}catch (Exception ex){
			logger.error("订单自动审核出现异常",ex);
			return new ResultInfo(-1,"自动审核失败");
		}
	}
	/**
	 * <b>Description:</b><br>
	 * 方法注释
	 *
	 * @param category
	 * @return void
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/5/18 10:19
	 */
	boolean judgeSku(SimpleMedicalCategory category){
		if(category.getNewMedicalCategory() != null && OrderGoodsAptitudeConstants.LEVEL_CHN_MEDICAL_NEW.equals(category.getNewMedicalCategory())){
			return false;
		}else if(category.getOldMedicalCategory() != null && OrderGoodsAptitudeConstants.LEVEL_CHN_MEDICAL_OLD.equals(category.getOldMedicalCategory())){
			return false;
		}else {
			return true;
		}
	}
	/**
	 * <b>Description:</b>检查医疗资质是否符合条件<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/17
	 */
	public ResultInfo checkMedicalCategory(HashSet categorySet, Integer categoryId, Integer type) {
		if (categorySet == null || categoryId == null) {
			logger.info("checkMedicalCategory null  {}  {} {}",categorySet,categoryId,type);
			if (SysOptionConstant.ID_969.equals(type)) {
				return new ResultInfo(-1, OrderGoodsAptitudeEnum.SECOND_MEDICAL_IS_INVALIED.getMessage());
			} else if (SysOptionConstant.ID_970.equals(type)) {
				return new ResultInfo(-1, OrderGoodsAptitudeEnum.THIRD_MEDICAL_IS_INVALIED.getMessage());
			}
		}
		if (!categorySet.contains(categoryId)) {
			logger.info("checkMedicalCategory  {}  {} {}",categorySet,categoryId,type);
			if (SysOptionConstant.ID_969.equals(type)) {
				return new ResultInfo(-1, OrderGoodsAptitudeEnum.SECOND_MEDICAL_IS_INVALIED.getMessage());
			} else if (SysOptionConstant.ID_970.equals(type)) {
				return new ResultInfo(-1, OrderGoodsAptitudeEnum.THIRD_MEDICAL_IS_INVALIED.getMessage());
			}
		}
		return new ResultInfo(0, "验证有效");
	}

	/**
	 * <b>Description:</b>从列表中获取医疗资质分类的集合<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/17
	 */
	public HashMap<Integer,HashSet<Integer>> getSetFromMedicalCategory(List<TraderMedicalCategory> categoryVoList) {
		HashMap<Integer,HashSet<Integer>> resultMap=new HashMap<>();
		if (CollectionUtils.isEmpty(categoryVoList)) {
			return resultMap;
		}
		for (TraderMedicalCategory c : categoryVoList) {
			if (c == null || c.getMedicalCategoryId() == null||c.getMedicalCategoryLevel()==null) {
				continue;
			}
			if(OrderGoodsAptitudeConstants.LEVEL_SECOND_NEW_MEDICAL_CATEGORY.equals(c.getMedicalCategoryLevel())) {
				putMedicalCategory2Map(resultMap,c,OrderGoodsAptitudeConstants.LEVEL_SECOND_NEW_MEDICAL_CATEGORY);
			}else if(OrderGoodsAptitudeConstants.LEVEL_SECOND_OLD_MEDICAL_CATEGORY.equals(c.getMedicalCategoryLevel())){
				putMedicalCategory2Map(resultMap,c,OrderGoodsAptitudeConstants.LEVEL_SECOND_OLD_MEDICAL_CATEGORY);
			}else if(OrderGoodsAptitudeConstants.LEVEL_THIRD_NEW_MEDICAL_CATEGORY.equals(c.getMedicalCategoryLevel())){
				putMedicalCategory2Map(resultMap,c,OrderGoodsAptitudeConstants.LEVEL_THIRD_NEW_MEDICAL_CATEGORY);
			}else if(OrderGoodsAptitudeConstants.LEVEL_THIRD_OLD_MEDICAL_CATEGORY.equals(c.getMedicalCategoryLevel())){
				putMedicalCategory2Map(resultMap,c,OrderGoodsAptitudeConstants.LEVEL_THIRD_OLD_MEDICAL_CATEGORY);
			}
		}
		return resultMap;
	}
	/**
	 * <b>Description:</b><br>
	 * 获取新国标20，旧国标6827资质分类
	 *
	 * @param
	 * @return java.util.HashMap<java.lang.Integer,java.util.HashSet<java.lang.Integer>>
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/4/19 14:23
	 */
//	public HashMap<Integer,HashSet<Integer>>  getSetByChnMedical(){
//		HashMap<Integer,HashSet<Integer>> resultMap=new HashMap<>();
//		//新国标
//		List<TraderMedicalCategory> traderMedicalCategories = standardCategoryMapper.getCategoryByChnMedical(SysOptionConstant.NEW_CHN_MED_NO);
//		traderMedicalCategories.forEach(item->putMedicalCategory2Map(resultMap,item,OrderGoodsAptitudeConstants.LEVEL_CHN_MEDICAL_NEW));
//		traderMedicalCategories = sysOptionDefinitionMapper.getCategoryByChnMedical(SysOptionConstant.OLD_CHN_MED_NO);
//		traderMedicalCategories.forEach(item->putMedicalCategory2Map(resultMap,item,OrderGoodsAptitudeConstants.LEVEL_CHN_MEDICAL_OLD));
//		return resultMap;
//	}

	/**
	 * <b>Description:</b>把分类结果装入字典中<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/19
	 */
	public void putMedicalCategory2Map(HashMap<Integer,HashSet<Integer>> resultMap,TraderMedicalCategory c,Integer type){
		if(!resultMap.containsKey(type)){
			HashSet<Integer> hashSet=new HashSet<>();
			resultMap.put(type,hashSet);
		}
		resultMap.get(type).add(c.getMedicalCategoryId());
	}
	/**
	 * <b>Description:</b>检查资质证书是否在有效期内，1代表缺失，2代表过期，3代表验证通过<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/17
	 */
	public int checkCertificateVaild(List<TraderCertificate> certificates, Integer type) {

		if (CollectionUtils.isEmpty(certificates)) {
			return OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL;
		}
		for (TraderCertificate certificate : certificates) {
			if (certificate == null || StringUtil.isEmpty(certificate.getUri()) || certificate.getSysOptionDefinitionId() == null) {
				continue;
			}
			if (type.equals(certificate.getSysOptionDefinitionId())) {
				if (certificate.getEndtime() == null || certificate.getEndtime() >= System.currentTimeMillis() || certificate.getEndtime().equals(0L)) {
					return OrderGoodsAptitudeConstants.CERTIFICATE_VAILD;
				} else {
					return OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT;
				}
			}
		}
		return OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL;
	}

	/**
	 * <b>Description:</b>一类医疗器械的情况，检查营业执照是否包含医疗器械<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:Thor</b>
	 * <br><b>Date:</b> 2020/3/17
	 */
	public int checkMedicalVaild(List<TraderCertificate> certificates) {
		for (TraderCertificate certificate : certificates) {
			if (certificate == null || StringUtil.isEmpty(certificate.getUri()) || certificate.getSysOptionDefinitionId() == null) {
				continue;
			}
			//一类医疗器械的情况
			if(certificate.getSysOptionDefinitionId().equals(SysOptionConstant.ID_25)){
				if(certificate.getIsMedical() == null || !SysOptionConstant.CONTAIN_MEDICAL.equals(certificate.getIsMedical())){
					return OrderGoodsAptitudeConstants.CERTIFICATE_WITHOUT_MED;
				}else if(SysOptionConstant.CONTAIN_MEDICAL.equals(certificate.getIsMedical())){
					return OrderGoodsAptitudeConstants.CERTIFICATE_VAILD;
				}
			}
		}
		return OrderGoodsAptitudeConstants.CERTIFICATE_WITHOUT_MED;
	}
	@Override
	public Map<String, Object> getSaleorderListPage(HttpServletRequest request, Saleorder saleorder, Page page,Integer i) {
		if(i==1) {
			saleorder.setOnlineFlag(true);
		}else {
			saleorder.setOnlineFlag(false);
		}
		Map<String, Object> map = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<Map<String, Object>>> TypeRef = new TypeReference<ResultInfo<Map<String, Object>>>() {
			};
			String url = httpUrl + "order/saleorder/getsaleorderlistpage.htm";

			logger.info("请求DB的地址是:"+url);

			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef,
					page);
			if (result != null && result.getCode() == 0) {
				Map<String, Object> result_map = (Map<String, Object>) result.getData();
				if (result_map != null && result_map.size() > 0) {
					map = new HashMap<String, Object>();
					net.sf.json.JSONArray json = null;
					String openInvoiceApplyStr = result_map.get("saleorderList").toString();
					json = net.sf.json.JSONArray.fromObject(openInvoiceApplyStr);

					List<Saleorder> saleorderList = (List<Saleorder>) json.toCollection(json, Saleorder.class);
					for (Saleorder saleorder1 : saleorderList) {
						riskCheckService.setSaleorderIsRiskInfo(saleorder1);
					}
					map.put("saleorderList", saleorderList);

					saleorder = (Saleorder) JSONObject.toBean(JSONObject.fromObject(result_map.get("saleorder")),
							Saleorder.class);
					// 订单总金额数
					map.put("total_amount", result_map.get("total_amount").toString());

					map.put("saleorder", saleorder);

					page = result.getPage();
					map.put("page", page);
				}
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return map;
	}

	@Override
	public List<CommunicateRecord> getCommunicateRecord(List<Integer> list, String communicateType) {
		return communicateRecordMapper.getCommunicateRecord(list, communicateType);
	}

	@Override
	public Long getCustomerIdByOrderId(Integer saleOrderId) {
		return saleorderMapper.getCustomerIdByOrderId(saleOrderId);
	}

	@Override
	public Saleorder getBaseSaleorderInfo(Saleorder saleorder) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Saleorder>> TypeRef = new TypeReference<ResultInfo<Saleorder>>() {
		};
		String url = httpUrl + "order/saleorder/getbasesaleorderinfo.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			saleorder = (Saleorder) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return saleorder;
	}
	//TODO
	@Override
	public List<SaleorderGoods> getSaleorderGoodsById(Saleorder saleorder) {
		List<SaleorderGoods> list = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<SaleorderGoods>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoods>>>() {
		};
		String url = httpUrl + "order/saleorder/getsaleordergoodsbyid.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			list = (List<SaleorderGoods>) result.getData();
			logger.info("根据销售单查询商品db查询结果：getSaleorderGoodsById:{}",JSON.toJSONString(list));
			if (null != list && list.size() > 0) {
				// 手填总成本
				BigDecimal totalReferenceCostPrice = new BigDecimal(0);
				for (SaleorderGoods g : list) {
					if (null == g)
						continue;
					g.setGoodsUserNm(rCategoryJUserMapper.getUserByCategoryNm(g.getGoods().getCategoryId(),
							g.getGoods().getCompanyId()));
					g.getGoods().setUserList(rCategoryJUserMapper.getUserByCategory(g.getGoods().getCategoryId(),
							g.getGoods().getCompanyId()));
					// modify by Franlin at 2018-08-13 for[修改最小原则] begin
					if (null != saleorder.getReqType() && 1 == saleorder.getReqType()
							&& null != g.getReferenceCostPrice() && null != g.geteNum()) {
						BigDecimal mllNum = new BigDecimal(g.getNum()).subtract(new BigDecimal(g.getAfterReturnNum()));
						totalReferenceCostPrice = totalReferenceCostPrice
								.add(g.getReferenceCostPrice().multiply(mllNum));
					}
					// modify by Franlin at 2018-08-13 for[修改最小原则] end
					//判断是否为改低价商品
					isLowerSaleorderGoods(saleorder.getSaleorderId(),g);
				}
				//设置风控信息
				riskCheckService.setSaleorderIsRiskInfo(saleorder,list);
				saleorder.setFiveTotalAmount(totalReferenceCostPrice);
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}

	private void isLowerSaleorderGoods(Integer salorderId,SaleorderGoods g) {
		BigDecimal checkPrice = orderGoodsLowerPriceApiService.getCheckPrice(salorderId,g.getSaleorderGoodsId());
		if (ObjectUtil.isNotNull(checkPrice)){
			g.setIsLowerGoods(ErpConst.ONE);
			g.setCheckPrice(checkPrice);
		}
	}


	public void setMaoLiBuyPrice(Saleorder saleorder,List<SaleorderGoods> saleorderGoodsList){
		if(CollectionUtils.isEmpty(saleorderGoodsList)){
			return;
		}
		List<SaleOrderGoodsMaoLiBuyPriceDto> priceMaoLiList = saleOrderDataApiService.queryListForSaleOrderGoodsMaoli(saleorder.getSaleorderId());
		Map<String, SaleOrderGoodsMaoLiBuyPriceDto> priceMaoLiMap = priceMaoLiList.stream()
				.collect(Collectors.toMap(
						SaleOrderGoodsMaoLiBuyPriceDto::getSkuNo, // key
						item -> item, // value
						(existing, replacement) -> existing
				));
		saleorderGoodsList.forEach(saleorderGoods -> {
			SaleOrderGoodsMaoLiBuyPriceDto priceDto = priceMaoLiMap.get(saleorderGoods.getSku());
			if (priceDto != null && priceDto.getBuyPrice()!=null && priceDto.getBuyPrice().compareTo(new BigDecimal(0.00))>0) {
				saleorderGoods.setMaoLiBuyPrice(priceDto.getBuyPrice());
				saleorderGoods.setMaoLiBuyPriceDesc(priceDto.getBuyPriceDesc());
			}
		});
	}

	public void setSaleOrderGoodsImg(List<SaleorderGoods> saleorderGoodsList){
		if(CollectionUtils.isEmpty(saleorderGoodsList)){
			return;
		}
		List<String> skuNoList = saleorderGoodsList.stream().map(SaleorderGoods::getSku).collect(Collectors.toList());
		List<SaleOrderGoodsImageDto> imageList = saleOrderDataApiService.queryListForSaleOrderGoodsImage(skuNoList);
		Map<String, SaleOrderGoodsImageDto> imageMap = imageList.stream()
				.collect(Collectors.toMap(
						SaleOrderGoodsImageDto::getSkuNo, // key
						item -> item // value
				));
		saleorderGoodsList.forEach(saleorderGoods -> {
			SaleOrderGoodsImageDto imageDto = imageMap.get(saleorderGoods.getSku());
			if (imageDto != null) {
				saleorderGoods.setImgUrl(imageDto.getImgUrl());
			}
		});
	}

	/*新商品流*/
	@Override
	public List<SaleorderGoods> getSaleorderGoodsByIdOther(Saleorder saleorder) {
		List<SaleorderGoods> list = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<SaleorderGoods>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoods>>>() {
		};
		String url = httpUrl + "order/saleorder/getsaleordergoodsbyidOther.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			list = (List<SaleorderGoods>) result.getData();
			if (null != list && list.size() > 0) {
				// 手填总成本
				BigDecimal totalReferenceCostPrice = new BigDecimal(0);
				for (SaleorderGoods g : list) {
					if (null == g)
						continue;
					g.setGoodsUserNm(rCategoryJUserMapper.getUserByCategoryNm(g.getGoods().getCategoryId(),
							g.getGoods().getCompanyId()));
					g.getGoods().setUserList(rCategoryJUserMapper.getUserByCategory(g.getGoods().getCategoryId(),
							g.getGoods().getCompanyId()));
					// modify by Franlin at 2018-08-13 for[修改最小原则] begin
					if (null != saleorder.getReqType() && 1 == saleorder.getReqType()
							&& null != g.getReferenceCostPrice() && null != g.geteNum()) {
						BigDecimal mllNum = new BigDecimal(g.getNum()).subtract(new BigDecimal(g.getAfterReturnNum()));
						totalReferenceCostPrice = totalReferenceCostPrice
								.add(g.getReferenceCostPrice().multiply(mllNum));
					}
					// modify by Franlin at 2018-08-13 for[修改最小原则] end
				}
				saleorder.setFiveTotalAmount(totalReferenceCostPrice);
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}

    @Override
    public List<SaleorderGoods> getSaleorderGoodsInfo(Saleorder saleorder) {
        List<SaleorderGoods> list = null;
        // 定义反序列化 数据格式
        saleorder.setBussinessType(2);
        saleorder.setExtraType("warehouse");
        final TypeReference<ResultInfo<List<SaleorderGoods>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoods>>>() {
        };
        final TypeReference<ResultInfo4Stock<Map<String,Integer>>> typeReference = new TypeReference<ResultInfo4Stock<Map<String,Integer>>>() {
        };
        String url = null;
        try {
            url = httpUrl + "order/saleorder/getsaleordergoodsinfo.htm";
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        try {

            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
            list = (List<SaleorderGoods>) result.getData();
            /**
             * 如果是活动商品 获取商品的锁定库存信息
             */
			Optional skuNo = list.stream()
								.filter(item -> item.getIsActionGoods() > 0)
								.map(SaleorderGoods::getSku)
								.reduce((s1,s2) -> s1 + "," + s2);
			if (skuNo.isPresent()) {
				String queryUrl = stockUrl + "/promotion/action/lockedCount/sku?skuNo=" + skuNo.get();
				ResultInfo4Stock<?> resultInfo = (ResultInfo4Stock<?>) HttpClientUtils4Stock.get(queryUrl, null, clientId, clientKey, typeReference);
				if (resultInfo != null) {
					Map<String, Integer> actionLockCountMap = (Map<String, Integer>) resultInfo.getData();
					list.forEach(
							item -> item.setActionLockCount(actionLockCountMap.get(item.getSku()))
					);
				}else{
				    list.forEach(item -> item.setActionLockCount(0));
                }
			}

        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return list;
    }


	@Override
	public Saleorder saveEditSaleorderInfo(Saleorder saleorder, HttpServletRequest request, HttpSession session) {
		User user = null;

		if(session == null || session.getAttribute(ErpConst.CURR_USER) == null){
			user = userMapper.getByUsername("njadmin",1);
		}else{
			user = (User) session.getAttribute(ErpConst.CURR_USER);
		}

		Long time = DateUtil.sysTimeMillis();

		saleorder.setModTime(time);
		saleorder.setUpdater(user.getUserId());
		if(!StringUtils.isEmpty(saleorder.getDeliveryDelayTimeStr())){
			saleorder.setDeliveryDelayTime(DateUtil.convertLong(saleorder.getDeliveryDelayTimeStr(),DateUtil.DATE_FORMAT));
		}
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {
		};
		try {
			ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(
					httpUrl + "order/saleorder/saveeditsaleorderinfo.htm", saleorder, clientId, clientKey, TypeRef2);
			Saleorder res = (Saleorder) result2.getData();
			if(result2 != null && result2.getCode().equals(0)&&res!=null){
				if("2".equals(res.getOptType())){
					//代表是审核通过或者生效
					orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null, OrderDataUpdateConstant.SALE_ORDER_VAILD);
				}else{
					orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null, OrderDataUpdateConstant.SALE_ORDER_EDIT);
				}

			}
			return res;
		} catch (IOException e) {
			return null;
		}
	}

    @Override
    public Saleorder saveEditSaleorderInfo(Saleorder saleorder, User user) {

        Long time = DateUtil.sysTimeMillis();
        saleorder.setModTime(time);
        saleorder.setUpdater(user.getUserId());

        final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(
                    httpUrl + "order/saleorder/saveeditsaleorderinfo.htm", saleorder, clientId, clientKey, TypeRef2);
            Saleorder res = (Saleorder) result2.getData();
            if(result2 != null && result2.getCode().equals(0)&&res!=null){
                if("2".equals(res.getOptType())){//代表是审核通过或者生效
                    orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null, OrderDataUpdateConstant.SALE_ORDER_VAILD);
                }else{
                    orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null, OrderDataUpdateConstant.SALE_ORDER_EDIT);
                }
            }
            return res;
        } catch (IOException e) {
            return null;
        }
    }

	@Override
	public Saleorder saveEditSaleorderInfo(Saleorder saleorder) {
		Long time = DateUtil.sysTimeMillis();
		saleorder.setModTime(time);
		saleorder.setUpdater(0);
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {
		};
		try {
			ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(
					httpUrl + "order/saleorder/saveeditsaleorderinfo.htm", saleorder, clientId, clientKey, TypeRef2);
			Saleorder res = (Saleorder) result2.getData();
			if(result2 != null && result2.getCode().equals(0)){
				orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_EDIT);
			}
			return res;
		} catch (IOException e) {
			return null;
		}
	}

	@Override
	public ResultInfo<?> saveSaleorderGoods(SaleorderGoods saleorderGoods) {
		ResultInfo<?> result = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
		};
		String url = httpUrl + "order/saleorder/savesaleordergoods.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderGoods, clientId, clientKey, TypeRef);
			if(result != null && result.getCode().equals(0)){

				orderCommonService.updateSaleOrderDataUpdateTime(null,saleorderGoods.getSaleorderGoodsId(),OrderDataUpdateConstant.SALE_ORDER_GOODS_EDIT);
				Saleorder saleorder = new Saleorder();
				saleorder.setSaleorderId(saleorderGoods.getSaleorderId());
				User user = new User();
				logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(saleorder,user);
			}
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	/**
	 * 保存备注组件关联关系
	 * @param labelDataDto 订单产品信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResultInfo<?> saveInsideComments(RemarkComponentDto labelDataDto) {
		ResultInfo<?> resultInfo = new ResultInfo<>(0, "操作成功");
		if (labelDataDto == null) {
			return new ResultInfo<>(0, "没有可保存的内部备注");
		}
		LabelQuery labelQuery = labelDataDto.getLabelQuery();
		List<LabelQueryDto> queryDtoList = labelDataDto.getData();
		if (CollectionUtils.isEmpty(queryDtoList) && labelQuery == null) {
			return new ResultInfo<>(0, "没有可保存的内部备注");
		}
		// 根据格式 编写默认值方法
		if (CollectionUtils.isEmpty(queryDtoList) && labelQuery != null) {
			if(labelQuery.getRelationId() != null && StringUtil.isNotEmpty(labelQuery.getSkuNo()) && labelQuery.getScene() != null){
				// 默认值处理 预先判断是否已经存在关联关系
				List<RemarkComponent> remarkComponentList = remarkComponentMapper.selectComponentRelationList(labelQuery);
				// 默认值类型 1采购要求, 2供应商要求, 5 专项发货
				ArrayList<Integer> typeList = remarkComponentMapper.selectType();

				typeList.forEach(typeItem->{
					long countOfTypeOne = remarkComponentList.stream()
							.filter(item -> typeItem.equals(item.getType()) && !Integer.valueOf(0).equals(item.getParentId()))
							.count();
					if(countOfTypeOne == 0L){
						ComponentRelation componentRelation = ComponentRelation
								.builder()
								.scene(labelQuery.getScene())
								.relationId(labelQuery.getRelationId())
								.skuNo(labelQuery.getSkuNo())
								.skuName(labelQuery.getSkuName())
								.time(0L)
								.build();
						if(Integer.valueOf(1).equals(typeItem)){
							componentRelation.setComponentId(2);
							remarkComponentMapper.saveComponentRelation(componentRelation);
							if(RemarkComponentSceneEnum.SALE_ORDER.getCode().equals(labelQuery.getScene())){
								saleorderMapper.updateSaleorderRemarkComponent(componentRelation);
							}
						}
						if(Integer.valueOf(2).equals(typeItem)){
							componentRelation.setComponentId(5);
							remarkComponentMapper.saveComponentRelation(componentRelation);
						}
						if(Integer.valueOf(5).equals(typeItem)){
							componentRelation.setComponentId(17);
							remarkComponentMapper.saveComponentRelation(componentRelation);
						}
					}
				});
			}else{
				return new ResultInfo<>(0, "没有可保存的内部备注");
			}
			return resultInfo;
		}
		for (LabelQueryDto queryDto : queryDtoList) {
			List<LabelQueryDto> childList = queryDto.getChild();
			if (CollectionUtils.isEmpty(childList)) {
				continue;
			}
			for (LabelQueryDto child : childList) {
				if (!child.getSelected()) {
					continue;
				}
				List<SkuVo> skuVoList = child.getSkuList();
				if (CollectionUtils.isEmpty(skuVoList)) {
					continue;
				}
				for (SkuVo sku : skuVoList) {
					if (!sku.getChoose()) {
						continue;
					}
					ComponentRelation componentRelation = new ComponentRelation();
					componentRelation.setScene(labelQuery.getScene());
					componentRelation.setComponentId(child.getId());
					componentRelation.setRelationId(labelQuery.getRelationId());
					componentRelation.setSkuNo(sku.getSkuNo());
					componentRelation.setSkuName(sku.getSkuName());
					componentRelation.setTime(DateUtil.convertLong(child.getDate(), "yyyy-MM-dd"));
					componentRelation.setReason(child.getReason());
					remarkComponentMapper.saveComponentRelation(componentRelation);
					if (child.getParentId() == 1 && labelQuery.getScene() != RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode()) {
						// 更新订单产品明细表的采购要求
						saleorderMapper.updateSaleorderRemarkComponent(componentRelation);
					}
					// 维护销售单SKU中专向发货标记字段 (专向发货 - 是)
					if(child.getId() == 16 && labelQuery.getScene() != RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode()){
						saleorderMapper.updateSaleOrderFlagRemarkComponent(componentRelation);
					}
				}
			}
		}
		return resultInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResultInfo<?> updateInsideComments(RemarkComponentDto labelDataDto) {
		ResultInfo<?> resultInfo = new ResultInfo<>(0, "操作成功");
		if (labelDataDto == null) {
			return new ResultInfo<>(0, "没有可保存的内部备注");
		}
		LabelQuery labelQuery = labelDataDto.getLabelQuery();
		List<LabelQueryDto> queryDtoList = labelDataDto.getData();
		if (CollectionUtils.isEmpty(queryDtoList) && labelQuery == null) {
			return new ResultInfo<>(0, "没有可保存的内部备注");
		}
		// 根据格式 编写默认值方法
		if (CollectionUtils.isEmpty(queryDtoList) && labelQuery != null) {
			if(labelQuery.getRelationId() != null && StringUtil.isNotEmpty(labelQuery.getSkuNo()) && labelQuery.getScene() != null){
				// 默认值处理 预先判断是否已经存在关联关系
				List<RemarkComponent> remarkComponentList = remarkComponentMapper.selectComponentRelationList(labelQuery);
				// 默认值类型 1采购要求, 2供应商要求, 5 专项发货
//				ArrayList<Integer> typeList = new ArrayList<>(); typeList.add(1);typeList.add(2);typeList.add(5);
				ArrayList<Integer> typeList = remarkComponentMapper.selectType();
				typeList.stream().forEach(typeItem->{
					long countOfTypeOne = remarkComponentList.stream()
							.filter(item -> typeItem.equals(item.getType()) && !Integer.valueOf(0).equals(item.getParentId()))
							.count();
					if(countOfTypeOne == 0L){
						ComponentRelation componentRelation = ComponentRelation
								.builder()
								.scene(labelQuery.getScene())
								.relationId(labelQuery.getRelationId())
								.skuNo(labelQuery.getSkuNo())
								.skuName(labelQuery.getSkuName())
								.time(0L)
								.build();
						if(Integer.valueOf(1).equals(typeItem)){
							componentRelation.setComponentId(2);
							remarkComponentMapper.saveComponentRelation(componentRelation);
							if(RemarkComponentSceneEnum.SALE_ORDER.getCode().equals(labelQuery.getScene())){
								saleorderMapper.updateSaleorderRemarkComponent(componentRelation);
							}
						}
						if(Integer.valueOf(2).equals(typeItem)){
							componentRelation.setComponentId(5);
							remarkComponentMapper.saveComponentRelation(componentRelation);
						}
						if(Integer.valueOf(5).equals(typeItem)){
							componentRelation.setComponentId(17);
							remarkComponentMapper.saveComponentRelation(componentRelation);
						}
					}
				});
			}else{
				return new ResultInfo<>(0, "没有可保存的内部备注");
			}
			return resultInfo;
		}
		for (LabelQueryDto queryDto : queryDtoList) {
			List<LabelQueryDto> childList = queryDto.getChild();
			if (CollectionUtils.isEmpty(childList)) {
				continue;
			}
			for (LabelQueryDto child : childList) {
				List<SkuVo> skuVoList = child.getSkuList();
				if (CollectionUtils.isEmpty(skuVoList)) {
					continue;
				}
				for (SkuVo sku : skuVoList) {
					// 判断原来是否配置
					LabelQuery param = new LabelQuery();
					param.setScene(labelQuery.getScene());
					param.setComponentId(child.getId());
					param.setSkuNo(sku.getSkuNo());
					param.setRelationId(labelQuery.getRelationId());
					ComponentRelation componentRelationIsExist = remarkComponentMapper.getComponentRelation(param);
					if (!sku.getChoose()) {
						// 未选中，已配置，删除
						if (componentRelationIsExist != null) {
							remarkComponentMapper.deleteComponentRelation(param);
							// 置空
							componentRelationIsExist.setComponentId(null);
							if (child.getParentId() == 1 && labelQuery.getScene() != RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode()) {
								// 更新订单产品明细表的采购要求
								saleorderMapper.updateSaleorderRemarkComponent(componentRelationIsExist);
							}
						}
						continue;
					}
					// 选中未配置，新增
					if (componentRelationIsExist == null) {
						ComponentRelation componentRelation = new ComponentRelation();
						componentRelation.setScene(labelQuery.getScene());
						componentRelation.setComponentId(child.getId());
						componentRelation.setRelationId(labelQuery.getRelationId());
						componentRelation.setSkuNo(sku.getSkuNo());
						componentRelation.setSkuName(sku.getSkuName());
						componentRelation.setTime(DateUtil.convertLong(child.getDate(), "yyyy-MM-dd"));
						componentRelation.setReason(child.getReason());
						remarkComponentMapper.saveComponentRelation(componentRelation);
						if (child.getParentId() == 1 && labelQuery.getScene() != RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode()) {
							// 更新订单产品明细表的采购要求
							saleorderMapper.updateSaleorderRemarkComponent(componentRelation);
						}
						// 判断订单&SKU是否满足修改条件
						Saleorder saleOrder = saleorderMapper.selectBySaleOrderId(labelQuery.getRelationId());
						Integer buyOrderGoodsNum = saleorderGoodsMapper.selectSaleOrderGoodsByIdAndSku(labelQuery.getRelationId(),sku.getSkuNo());
						// 维护销售单SKU中专向发货标记字段 (专向发货 - 是)
						if(child.getId() == 16  && labelQuery.getScene() != RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode()){
							saleorderMapper.updateSaleOrderFlagRemarkComponent(componentRelation);
						}
						if(child.getId() == 17  && labelQuery.getScene() != RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode()){
							saleorderMapper.updateSaleOrderFlagRemarkComponent0(componentRelation);
						}
						if((child.getId() == 16 || child.getId() == 17)  && labelQuery.getScene() != RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode()){
							// 订单处于未审核,订单支持编辑,sku的专向发货标签编辑不做限制
							// 订单处于进行中,申请修改时,修改sku专向发货标签,调取原订单取消接口
							if (Integer.valueOf(1).equals(saleOrder.getStatus())) {
								if(!NumberUtil.isZeroOrNull(buyOrderGoodsNum)){
									throw new RuntimeException("货号"+sku.getSkuNo()+"已采购不支持修改专向发货标签");
								}

								//出库撤消接口返回代码值定义：
								if (!cancelTypeService.cancelOutSaleOutMethod(saleOrder.getSaleorderNo(), CancelReasonConstant.SALE_DING_ORDER)) {
									throw new RuntimeException("WMS系统正在作业，请稍后操作！");
								}
							}
						}
					} else {
						// 选中已配置，更新
						componentRelationIsExist.setTime(DateUtil.convertLong(child.getDate(), "yyyy-MM-dd"));
						componentRelationIsExist.setReason(child.getReason());
						remarkComponentMapper.updateComponentRelation(componentRelationIsExist);
						if (child.getParentId() == 1 && labelQuery.getScene() != RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode()) {
							// 更新订单产品明细表的采购要求
							//获取之前订单详情的历史采购要求
							SaleorderGoodsVo saleorderGoodsVo =  saleorderMapper.getRemarkComponetInfo(componentRelationIsExist);
							if(Saleorder.COMPONENTID_BUY_NOW.equals(saleorderGoodsVo.getComponentId()) && Saleorder.COMPONENTID_BUY_LATER.equals(componentRelationIsExist.getComponentId())){
								//立即采购调整为暂缓采购时
								componentRelationIsExist.setWarnLevel(null);
								componentRelationIsExist.setAging(AgingTypeEnum.NORMAL.getCode());
								saleorderMapper.updateWarnStatus(componentRelationIsExist);
							}
							if(Saleorder.COMPONENTID_BUY_LATER.equals(saleorderGoodsVo.getComponentId()) && Saleorder.COMPONENTID_BUY_NOW.equals(componentRelationIsExist.getComponentId())){
								//暂缓采购调整为立即采购时
								long nowTime = DateUtil.gainNowDate();
								Long agingTime = saleorderGoodsVo.getAgingTime();
								long differ = (nowTime - agingTime)/(1000*60*60);
								if(differ < 8){
									componentRelationIsExist.setWarnLevel(null);
									componentRelationIsExist.setAging(AgingTypeEnum.NORMAL.getCode());
								}else if(differ < 12){
									componentRelationIsExist.setWarnLevel(WarnLevelEnum.THREE_L.getCode());
									componentRelationIsExist.setAging(AgingTypeEnum.APPROACH.getCode());
								}else {
									componentRelationIsExist.setWarnLevel(WarnLevelEnum.TWO_L.getCode());
									componentRelationIsExist.setAging(AgingTypeEnum.OVERDUE.getCode());
									if(differ > 24){
										componentRelationIsExist.setWarnLevel(WarnLevelEnum.ONE_L.getCode());
									}
								}
							}
							saleorderMapper.updateSaleorderRemarkComponent(componentRelationIsExist);
						}
					}
				}
			}
		}
		//订单流二期，此处修改内部备注后需再走一次下发wms逻辑
		try {
			Saleorder saleorder = new Saleorder();
			saleorder.setSaleorderId(labelQuery.getRelationId());
			User user = new User();
			logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(saleorder,user);
		} catch (Exception e) {
			log.error("【updateInsideComments】处理异常",e);
		}
		return resultInfo;
	}

	@Override
	public ResultInfo<?> deleteInsideComment(LabelQuery labelQuery) {
		ResultInfo<?> resultInfo = new ResultInfo<>(0, "操作成功");
		remarkComponentMapper.deleteComponentRelation(labelQuery);
		return resultInfo;
	}

	@Override
	public LabelQuery dealWidthInsideComments(String saleorderGoodsIdArr, Integer saleOrderId, Integer scene) {
		LabelQuery labelQuery = new LabelQuery();
		String[] detailIdArr = saleorderGoodsIdArr.split(",");
		List<SkuVo> skuList = new ArrayList<>();
		List<String> skuNoList = new ArrayList<>();
		if (detailIdArr.length == 0) {
			return labelQuery;
		}
		for (String saleOrderGoodsId:detailIdArr) {
			SaleorderGoods saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsInfoById(Integer.parseInt(saleOrderGoodsId));
			if (saleorderGoods != null) {
				SkuVo skuVo = new SkuVo();
				skuVo.setSkuNo(saleorderGoods.getSku());
				skuVo.setSkuId(saleorderGoods.getGoodsId());
				skuVo.setSkuName(saleorderGoods.getGoodsName());
				skuList.add(skuVo);
				skuNoList.add(saleorderGoods.getSku());
			}
		}
		labelQuery.setSkuList(skuList);
		LabelQuery _labelQuery = new LabelQuery();
		_labelQuery.setScene(scene);
		_labelQuery.setRelationId(saleOrderId);
		_labelQuery.setSkuNoList(skuNoList);
		String componentHtml = remarkComponentService.getComponentHtml(_labelQuery);
		labelQuery.setComponentHtml(componentHtml);
		return labelQuery;
	}

	@Override
	public ResultInfo<?> updateBuyWarnStatus(Saleorder saleorder) {
		List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleorder.getSaleorderId());
		if(CollectionUtils.isNotEmpty(saleorderGoodsList)){
			for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
				SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
				saleorderGoodsVo.setSaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
				saleorderGoodsVo.setWarnLevel(null);
				saleorderGoodsVo.setAging(0);
				saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
			}
		}
		return new ResultInfo<>(0,"操作成功");
	}

    @Override
	public SaleorderGoods getSaleorderGoodsInfoById(Integer saleorderGoodsId) {
		SaleorderGoods saleorderGoods = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<SaleorderGoods>> TypeRef = new TypeReference<ResultInfo<SaleorderGoods>>() {
		};
		String url = httpUrl + "order/saleorder/getsaleordergoodsinfobyidnew.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderGoodsId, clientId, clientKey,
					TypeRef);
			saleorderGoods = (SaleorderGoods) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return saleorderGoods;
	}

	@Override
	public ResultInfo<?> delSaleorderGoodsById(SaleorderGoods saleorderGoods) {
		ResultInfo<?> result = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
		};
		String url = httpUrl + "order/saleorder/delsaleordergoodsbyid.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderGoods, clientId, clientKey, TypeRef);
			if(result != null && result.getCode().equals(0)){
				orderCommonService.updateSaleOrderDataUpdateTime(null,saleorderGoods.getSaleorderGoodsId(),OrderDataUpdateConstant.SALE_ORDER_GOODS_EDIT);
				//更新订单相关报价单的咨询状态
				Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderGoods.getSaleorderId());
				if (saleorder != null && saleorder.getQuoteorderId() != null && saleorder.getQuoteorderId() > 0){
					SaleorderGoods goodsOfDeleted = saleorderGoodsMapper.selectByPrimaryKey(saleorderGoods.getSaleorderGoodsId());
					quoteorderConsultReplyMapper.updateAllConsultStatusByQuoteorderIdAndSkuList(saleorder.getQuoteorderId(), Collections.singletonList(goodsOfDeleted.getSku()),0);
					quoteService.updateConsultStatusOfQuoteorder(saleorder.getQuoteorderId(),1);
				}
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public ResultInfo<?> closeSaleorder(Saleorder saleorder) {
		ResultInfo<?> result = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
		};
		String url = httpUrl + "order/saleorder/closesaleorder.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			if(result != null && result.getCode().equals(0)){
			    //更新订单updatatime
                orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_CLOSE);
                //更新订单关联报价单的咨询状态
				Saleorder closedSaleorder = saleorderMapper.getSaleOrderById(saleorder.getSaleorderId());
				if (closedSaleorder != null && closedSaleorder.getQuoteorderId() != null && closedSaleorder.getQuoteorderId() > 0){
					List<String> skuList = saleorderGoodsMapper.getSaleorderGoodsSku(saleorder.getSaleorderId());
					if (skuList.size() > 0){
						quoteorderConsultReplyMapper.updateAllConsultStatusByQuoteorderIdAndSkuList(closedSaleorder.getQuoteorderId(),skuList,0);
						quoteService.updateConsultStatusOfQuoteorder(closedSaleorder.getQuoteorderId(),1);
					}
				}
            }
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public ResultInfo<?> editSaleorderHaveCommunicate(Saleorder saleorder) {
		ResultInfo<?> result = null;

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
		};
		String url = httpUrl + "order/saleorder/editsaleorderhavecommunicate.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			if(result.getCode().equals(-1)){//修改沟通记录时如果没有修改订单，接口也会返回错误码，此时改为操作正确
				return new ResultInfo(0, "操作成功");
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public Saleorder quoteorderToSaleorder(Integer quoteorderId, User user) {
		Map<String, Object> map = new HashMap<>();
		// 获取session
		ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = ra.getRequest();
		//VDERP-13616【ERP】客户下单流程优化 - 报价单---------------
		Quoteorder quoteorderInfo = updateReferenceCostPrice(quoteorderId, request);

		Saleorder saleorderPar = new Saleorder();
		saleorderPar.setQuoteorderId(quoteorderId);

        // 归属销售
        User belongUser = new User();
        if (quoteorderInfo.getTraderId() != null) {
            belongUser = userService.getUserInfoByTraderId(quoteorderInfo.getTraderId(), 1);// 1客户，2供应商
            if (belongUser != null && belongUser.getUserId() != null) {
                saleorderPar.setUserId(belongUser.getUserId());
            }
            if (belongUser != null && belongUser.getOrgId() != null) {
                saleorderPar.setOrgId(belongUser.getOrgId());
            }
            if (belongUser != null && belongUser.getOrgName() != null) {
                saleorderPar.setOrgName(belongUser.getOrgName());
            }
        }
        map.put("saleorder", saleorderPar);
        map.put("user", user);

        Saleorder saleorder = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Saleorder>> TypeRef = new TypeReference<ResultInfo<Saleorder>>() {
        };
        String url = httpUrl + "order/saleorder/quoteordertosaleorder.htm";
        try {
			ResultInfo<Saleorder> result = (ResultInfo<Saleorder>) HttpClientUtils.post(url, map, clientId, clientKey,
					TypeRef);
			saleorder = (Saleorder) result.getData();
			if (saleorder != null) {
				if (quoteorderInfo.getTraderId() != null) {
					saleorder.setTraderId(quoteorderInfo.getTraderId());
				}

				//VDERP-2263   订单售后采购改动通知
				orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(), null, OrderDataUpdateConstant.SALE_ORDER_GENERATE);
				saleorder.setQuoteorderId(quoteorderInfo.getQuoteorderId());
				saleorder.setQuoteorderNo(quoteorderInfo.getQuoteorderNo());
				riskCheckService.riskCheckQuoteorder(saleorder);
				Saleorder _saleorder = new Saleorder();
				_saleorder.setSaleorderId(saleorder.getSaleorderId());
				List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsById(_saleorder);
				// 查询报价单的内部备注标签
				// 然后根据订单的ID
				LabelQuery labelQuery = new LabelQuery();
				labelQuery.setRelationId(quoteorderId);
				labelQuery.setScene(RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode());
				List<ComponentRelation> componentRelations = remarkComponentMapper.getComponentRelationListByRelationId(labelQuery);
				if (CollectionUtils.isNotEmpty(componentRelations)) {
					for (ComponentRelation componentRelation : componentRelations) {
						componentRelation.setScene(RemarkComponentSceneEnum.SALE_ORDER.getCode());
						componentRelation.setRelationId(saleorder.getSaleorderId());
						remarkComponentMapper.saveComponentRelation(componentRelation);
					}
				}

				for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
					//更新产品快照信息
					saleorderGoodsMapper.updateSaleorderGoodsSnapshotInfo(saleorderGoods);
					for (ComponentRelation componentRelation : componentRelations) {
						if (null != componentRelation && !StringUtils.isEmpty(componentRelation.getType()) && !StringUtils.isEmpty(componentRelation.getSkuNo())
								&& componentRelation.getType() == 1 && componentRelation.getSkuNo().equals(saleorderGoods.getSku())) {
							// 更新订单产品明细表的采购要求
							saleorderMapper.updateSaleorderRemarkComponent(componentRelation);
						}
						if (componentRelation.getType() == 5 && !StringUtils.isEmpty(componentRelation.getSkuNo()) && componentRelation.getSkuNo().equals(saleorderGoods.getSku())) {
							// 更新订单产品明细表的专向发货字段
							if (componentRelation.getComponentId() !=null && componentRelation.getComponentId() == 16) {
								saleorderMapper.updateSaleOrderFlagRemarkComponent(componentRelation);
							}
							if (componentRelation.getComponentId() !=null && componentRelation.getComponentId() == 17) {
								saleorderMapper.updateSaleOrderFlagRemarkComponent0(componentRelation);
							}
						}
					}
				}

				// 报价原有内部备注为空
				for(int i = 0;i < saleorderGoodsList.size();i++){
					SaleorderGoods item = saleorderGoodsList.get(i);
					LabelQuery labelQueryTemp = new LabelQuery();
					labelQueryTemp.setScene(Integer.valueOf(1));
					labelQueryTemp.setRelationId(quoteorderId);
					labelQueryTemp.setSkuNo(item.getSku());
					List<RemarkComponent> remarkComponentList = remarkComponentMapper.selectComponentRelationList(labelQueryTemp);

					if(remarkComponentList.size() != 0){
						continue;
					}

					ComponentRelation componentRelation = ComponentRelation
							.builder()
							.scene(Integer.valueOf(0))
							.relationId(item.getSaleorderId())
							.skuNo(item.getSku())
							.skuName(item.getGoodsName())
							.time(0L)
							.build();
					saleorderGoodsMapper.insertInnerInsideDefault(componentRelation);

					componentRelation.setComponentId(2);
					saleorderMapper.updateSaleorderRemarkComponent(componentRelation);
				}
				//如果是'特麦帮'报价，转订单时往T_SPECIAL_SALES新增记录
				List<SpecialSalesDto> specialSales = specialSalesApiService.findByRelateIdInAndRelateTypeAndIsDelete(Collections.singletonList(saleorder.getQuoteorderId()), SpecialSalesEnum.QUOTEORDER.getCode(),ErpConst.ZERO);
				if (CollUtil.isNotEmpty(specialSales)){
					SpecialSalesDto saleDto = new SpecialSalesDto();
					saleDto.setRelateId(saleorder.getSaleorderId());
					saleDto.setRelateNo(saleorder.getSaleorderNo());
					saleDto.setRelateType(SpecialSalesEnum.SALEORDER.getCode());
					saleDto.setIsDelete(ErpConst.ZERO);
					specialSalesApiService.insertSpecialSales(saleDto);
				}
			}
		} catch (NullPointerException ex){
			logger.error(Contant.ERROR_MSG, ex);
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return saleorder;
    }

	private Quoteorder updateReferenceCostPrice(Integer quoteorderId, HttpServletRequest request) {
		Quoteorder quoteorderInfo = quoteService.getQuoteInfoByKey(quoteorderId);
		if (quoteorderInfo != null) {
			HttpSession session = null;
			if(request != null){
				session = request.getSession();
			}
			Map<String, Object> quoteGoodsmap = quoteService.getQuoteGoodsByQuoteId(quoteorderId,
					quoteorderInfo.getCompanyId(), session, 2, quoteorderInfo.getTraderId());
			List<QuoteorderGoods> quoteGoodsList = (List<QuoteorderGoods>) quoteGoodsmap.get("quoteGoodsList");
			// 根据客户ID查询客户信息
			TraderCustomerVo customer = traderCustomerService.getTraderCustomerInfo(quoteorderInfo.getTraderId());
			// 产品核算价
			quoteGoodsList = goodsChannelPriceService.getQuoteChannelPriceList(quoteorderInfo.getSalesAreaId(),
					quoteorderInfo.getCustomerNature(), customer.getOwnership(), quoteGoodsList);
			for (QuoteorderGoods qg : quoteGoodsList) {
				// 如果参考报价为0，核价的成本>0
				if (qg.getReferenceCostPrice() != null && qg.getReferenceCostPrice().compareTo(BigDecimal.ZERO) == 0
						&& qg.getCostPrice() != null && qg.getCostPrice().compareTo(BigDecimal.ZERO) == 1) {
					qg.setReferenceCostPrice(qg.getCostPrice());
				}
			}
			quoteorderInfo.setQuoteorderGoods(quoteGoodsList);
			// 将参考报价修改为核价成本的值
			quoteService.editQuoteOrderGoods(quoteorderInfo);
		}
		return quoteorderInfo;
	}

	@Override
    public List<Integer> getCommunicateRecordByDate(Long beginDate, Long endDate, String communicateType) {
        return communicateRecordMapper.getCommunicateRecordByDate(beginDate, endDate, communicateType);
    }

	@Override
	public ResultInfo<?> saveSaleorderAttachment(Attachment attachment) {
		ResultInfo<?> result = ResultInfo.error();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Attachment>> TypeRef = new TypeReference<ResultInfo<Attachment>>() {
		};
		String url = httpUrl + "order/saleorder/savesaleorderattachment.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, attachment, clientId, clientKey, TypeRef);
			//同步订单表回传字段状态
			if(result.getCode().equals(0) && attachment.getAttachmentFunction() != null){
				updateContractStatus(attachment, OrderConstant.ORDER_CONTRACT_ISRETURN, "upload", OrderConstant.ORDER_DELIVERYORDER_ISRETURN);
			}
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	private void updateContractStatus(Attachment attachment, Integer orderContractIsreturn, String upload, Integer orderDeliveryorderIsreturn) {
		if(OrderConstant.ORDER_CONTRACT.equals(attachment.getAttachmentFunction())){//合同
			saleorderMapper.uptContactStatus(orderContractIsreturn, attachment.getRelatedId());
			saleorderDataSyncService.syncContractStatusBySaleorderId(attachment.getRelatedId(), upload);
		}else if(OrderConstant.ORDER_DELIVERY.equals(attachment.getAttachmentFunction())){//送货单
			saleorderMapper.uptDeliveryStatus(orderDeliveryorderIsreturn, attachment.getRelatedId());
		}
	}


	@Override
	public ResultInfo<?> delSaleorderAttachmentNew(Attachment attachment) {
		String message=JsonUtils.convertObjectToJsonStr(attachment);
		try {
			Attachment attachmentInfo = attachmentMapper.getAttachment(attachment);
			Saleorder saleorderInfo = null;
			if(attachmentInfo.getAttachmentFunction() == 492 || attachmentInfo.getAttachmentFunction() == 493 || attachmentInfo.getAttachmentFunction() == 586){
				saleorderInfo = saleorderMapper.getBaseSaleorderInfo(attachmentInfo.getRelatedId());
			}
//			if (null != saleorderInfo && saleorderInfo.getStatus() == 3) {
//				return ResultInfo.error("操作失败，订单已被关闭");
//			}

			// 查主键 为了方便更新T_VERIFIES_INFO表里的数据
			Attachment saleorderId = attachmentMapper.findSaleorderId(attachment);

			 attachmentMapper.updateByAttachment(attachment);
			if(saleorderId!=null){

				// 将T_VERIFIES_INFO表的数据删除
				log.info("订单合同回传删除，删除T_VERIFIES_INFO表的数据，saleorderId:{}", saleorderId);
				attachmentMapper.delVerifiesInfo(saleorderId);
			}
			//同步订单表回传字段状态
			if( attachmentInfo.getAttachmentFunction() != null){
				updateContractStatus(attachmentInfo, OrderConstant.ORDER_CONTRACT_NORETURN, "deleteNew", OrderConstant.ORDER_DELIVERYORDER_NORETURN);
			}
			return ResultInfo.success();


		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG+"{}",JsonUtils.convertObjectToJsonStr(attachment), e);
		}
		return ResultInfo.error();
	}


	@Override
    public ResultInfo<?> delSaleorderAttachment(Attachment attachment) {
		logger.info("删除订单附件：{}",JsonUtils.convertObjectToJsonStr(attachment));
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        String url = httpUrl + "order/saleorder/delsaleorderattachment.htm";
		Attachment attachmentInfo = attachmentMapper.getAttachment(attachment);
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, attachment, clientId, clientKey, TypeRef);
			//同步订单表回传字段状态
			if(result.getCode().equals(0) && attachmentInfo.getAttachmentFunction() != null){
				updateContractStatus(attachmentInfo, OrderConstant.ORDER_CONTRACT_NORETURN, "delete", OrderConstant.ORDER_DELIVERYORDER_NORETURN);
			}else{
				logger.error("删除订单附件失败：{} {}",JsonUtils.convertObjectToJsonStr(attachmentInfo),result);
			}
        } catch (Exception e) {
			logger.error("删除订单附件失败：{}",JsonUtils.convertObjectToJsonStr(attachmentInfo),e);
			throw new RuntimeException(e);
        }
        return result;
    }

    @Override
    public List<Attachment> getSaleorderAttachmentList(Integer saleorderId) {
        List<Attachment> list = null;
        try {

			list = attachmentMapper.getSaleorderAttachmentById(saleorderId);

            // 操作人信息补充
            if (list.size() > 0) {
                List<Integer> userIds = new ArrayList<>();
                for (Attachment b : list) {
                    if (b.getCreator() > 0) {
                        userIds.add(b.getCreator());
                    }
                }

                if (userIds.size() > 0) {
                    List<User> userList = userMapper.getUserByUserIds(userIds);

                    for (Attachment b : list) {
                        for (User u : userList) {
                            if (u.getUserId().equals(b.getCreator())) {
                                b.setUsername(u.getUsername());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return list;
    }

    @Override
    public int getSaleorderCommunicateRecordCount(CommunicateRecord cr) {
        return communicateRecordMapper.getSaleorderCommunicateRecordCount(cr);
    }

	@Override
	public ResultInfo<?> validSaleorder(Saleorder saleorder) {
		ResultInfo<?> result = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
		};
		String url = httpUrl + "order/saleorder/validsaleorder.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			throw new RuntimeException(e);
		}
		return result;
	}


    @Override
	public ResultInfo<?> isValidSaleOrder(Saleorder saleOrderInfo) {


		// 生效前判断订单中产品报价或者货期是否为空
		if (saleorderGoodsMapper.vailSaleorderPriceDeliveryCycle(saleOrderInfo.getSaleorderId()) > 0) {
			return new ResultInfo(-1, "订单中产品报价或者货期为空时，订单不允许生效");
		}
		// 生效前判断订单中未删除产品数量是否为0
		if (saleorderGoodsMapper.vailSaleorderNotDelNum(saleOrderInfo.getSaleorderId()) == 0) {
			return new ResultInfo(-1, "订单中无产品，订单不允许生效");
		}
		if (saleorderGoodsMapper.getUncheckedSkuCountOfSaleorder(saleOrderInfo.getSaleorderId()) > 0) {
			return new ResultInfo(-1, "订单中存在未审核通过的产品");
		}

		return new ResultInfo(0, "校验成功");
	}

    @Override
    public ResultInfo<?> noValidSaleorder(Saleorder saleorder) {
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        String url = httpUrl + "order/saleorder/novalidsaleorder.htm";
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
			throw new RuntimeException(e);
        }
        return result;
    }


    @Override
    public ResultInfo<?> preQuoteorderToSaleorder(Integer quoteorderId) {
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        String url = httpUrl + "order/saleorder/prequoteordertosaleorder.htm";
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, quoteorderId, clientId, clientKey, TypeRef);
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
			throw new RuntimeException(e);
        }
        return result;
    }

    @Override
    public Saleorder saveAddBhSaleorder(Saleorder saleorder, HttpServletRequest request, HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();
        saleorder.setCompanyId(user.getCompanyId());
        saleorder.setOrgId(user.getOrgId());
        saleorder.setUserId(user.getUserId());
        saleorder.setAddTime(time);
        saleorder.setCreator(user.getUserId());
        saleorder.setModTime(time);
        saleorder.setUpdater(user.getUserId());

//        // 定义反序列化 数据格式
//        final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {
//        };
        try {
//            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils
//                    .post(httpUrl + "order/saleorder/saveaddbhsaleorder.htm", saleorder, clientId, clientKey, TypeRef2);
//            Saleorder res = (Saleorder) result2.getData();
			Saleorder res =	saveAddBhSaleorderDb(saleorder);
            if(res != null){
                orderCommonService.updateSaleOrderDataUpdateTime(res.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_GENERATE);
            }
            return res;
        } catch (Exception e) {
			throw new RuntimeException(e);
        }
    }
	@Resource
	private OrderNoDict orderNoDict;
	private Saleorder saveAddBhSaleorderDb(Saleorder saleorder) {
		saleorder.setOrderType(2);
		Integer i = saleorderMapper.insertSelective(saleorder);
		if (i == 1) {
			Integer saleorderId = saleorder.getSaleorderId();

			// 生成备货单号
			Saleorder saleorderExtra = new Saleorder();
			saleorderExtra.setSaleorderId(saleorderId);
			saleorderExtra.setSaleorderNo(orderNoDict.getOrderNum(saleorderId, 4));
			saleorderMapper.updateByPrimaryKeySelective(saleorderExtra);
			return saleorderExtra;
		} else {
			return null;
		}
	}

	@Override
    public ResultInfo<?> saveEditBhSaleorder(Saleorder saleorder) {
        ResultInfo<?> result = new ResultInfo(-1, "操作失败");
        // 定义反序列化 数据格式
//        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
//        };
//        String url = httpUrl + "order/saleorder/saveeditbhsaleorder.htm";
        try {
//            result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			saleorderMapper.updateByPrimaryKeySelective(saleorder);
//            if(result != null && result.getCode().equals(0)){
                orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_EDIT);
//            }
			result =  new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
			throw new RuntimeException(e);
        }
        return result;
    }

    @Override
    public Saleorder getSaleorderBySaleorderNo(Saleorder saleorder) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Saleorder>> TypeRef = new TypeReference<ResultInfo<Saleorder>>() {
        };
        String url = httpUrl + "order/saleorder/getsaleorderbysaleorderno.htm";
        try {
            ResultInfo result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
            if (result.getCode().equals(0)) {
                Saleorder res = (Saleorder) result.getData();
                return res;
            } else {
				logger.error("getSaleorderBySaleorderNo{}",JsonUtils.convertObjectToJsonStr(saleorder));
                return null;
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
			throw new RuntimeException(e);
        }
    }

	@Override
	public Saleorder getBySaleOrderNo(String saleOrderNo) {
		if (saleOrderNo == null || saleOrderNo.length() == 0) {
			return null;
		}
		return saleorderMapper.getSaleorderByOrderNo(saleOrderNo);
	}

	@Override
	public Integer getSaleordergetVerifyStatus(Integer saleorderId){
		return saleorderMapper.getSaleordergetVerifyStatus(saleorderId);
	}

	@Override
    public Map<String, Object> getSaleorderGoodsListPage(HttpServletRequest request, Saleorder saleorder, Page page) {
        Map<String, Object> map = new HashMap<>();
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<Saleorder>>> TypeRef = new TypeReference<ResultInfo<List<Saleorder>>>() {
            };
            String url = httpUrl + "order/saleorder/getsaleordergoodslistpage.htm";

            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef,
                    page);
            List<Saleorder> saleorderList = (List<Saleorder>) result.getData();
            page = result.getPage();
            map.put("saleorderList", saleorderList);
            map.put("page", page);
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return map;
    }

    @Override
    public ResultInfo<?> batchSaveBhSaleorderGoods(List<SaleorderGoods> list, Saleorder saleorder) throws Exception {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        String url = httpUrl + "order/saleorder/batchsavebhsaleordergoods.htm";
        Map<String, Object> map = new HashMap<>();
        map.put("saleGoodsList", list);
        map.put("saleorder", saleorder);
        ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, map, clientId, clientKey, TypeRef);
        if (result != null) {
            return result;
        } else {
            return new ResultInfo<>();
        }
    }

    @Override
    public Saleorder getSaleorderFlag(Saleorder saleorder) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils
                    .post(httpUrl + "order/saleorder/getsaleorderflag.htm", saleorder, clientId, clientKey, TypeRef2);
            Saleorder res = (Saleorder) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public List<SaleorderGoodsVo> getSdList(Goods goods) {
        List<SaleorderGoodsVo> list = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<SaleorderGoodsVo>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoodsVo>>>() {
        };
        String url = httpUrl + "order/saleorder/getSdList.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goods, clientId, clientKey, TypeRef);
            list = (List<SaleorderGoodsVo>) result.getData();
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return list;
    }

    /**
     * <b>Description:</b><br>
     * 根据销售订单的id获取销售订单产品的列表
     *
     * @param saleorder
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年10月10日 下午1:00:18
     */
    @Override
    public SaleorderVo getSaleorderGoodsVoList(Saleorder saleorder) {
        try {
			SaleorderVo sv = saleorderMapper.getSaleorderVoBySaleorderId(saleorder.getSaleorderId());
			// 当前销售订单是否含有待确认或进行中的退换货退款的售后订单
			if (saleorder.geteFlag() == null || "".equals(saleorder.geteFlag()) || "0".equals(saleorder.geteFlag())) {
				sv.setThk(0);

				AfterSales afterSales1 = new AfterSales();
				afterSales1.setOrderId(saleorder.getSaleorderId());
				List<AfterSalesVo> afterSalesVoList = afterSalesMapper.getReturnBackMoneyByOrderId(afterSales1);
				// 如果没有，则解锁销售订单
				if (afterSalesVoList.size() == 0 || afterSalesVoList == null) {
					sv.setHasReturnMoney(0);
				} else {
					sv.setHasReturnMoney(1);
				}

				// 有待处理退款
				if (null != afterSalesVoList && afterSalesVoList.size() > 0) {
					sv.setThk(1);
					if (AfterSalesProcessEnum.AFTERSALES_TH.getType().equals(saleorder.getFlag()) ||
							AfterSalesProcessEnum.AFTERSALES_HH.getType().equals(saleorder.getFlag())
							|| AfterSalesProcessEnum.AFTERSALES_TK.getType().equals(saleorder.getFlag())) {
						saleorder.setFlag("at");
						sv.setFlag("at");
					} else {
						sv.setFlag(saleorder.getFlag());
					}
				} else {
					List<SaleorderGoodsVo> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
					if (null != sgvList && sgvList.size() > 0) {// 去掉已经锁定的产品
						Integer totalcount = sgvList.size();
						for (int i = 0; i < sgvList.size(); i++) {
							if (sgvList.get(i).getLockedStatus() == 1) {
								sgvList.remove(i);
								i--;
							}
						}
						if (sgvList.size() < totalcount) {
							sv.setIsLocked(1);// 有锁定的产品
						} else {
							sv.setIsLocked(0);// 没有锁定的产品
						}
					}

					if (sgvList == null || sgvList.size() <= 0) {// 没有未锁定的产品
						sv.setThk(1);
						if ("th".equals(saleorder.getFlag()) || "hh".equals(saleorder.getFlag())
								|| "tk".equals(saleorder.getFlag())) {
							saleorder.setFlag("at");
							sv.setFlag("at");
						} else {
							sv.setFlag(saleorder.getFlag());
						}

					} else {
						sv.setFlag(saleorder.getFlag());
					}
				}
			}

			if ("at".equals(sv.getFlag())){
				sv.setFlag("atn");
				saleorder.setFlag("atn");
			}

			if ("th".equals(saleorder.getFlag()) || "hh".equals(saleorder.getFlag()) || "at".equals(saleorder.getFlag())
					|| "aty".equals(saleorder.getFlag())
					|| "atn".equals(saleorder.getFlag())
					|| "wx".equals(saleorder.getFlag())) {


				List<SaleorderGoodsVo> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);

				if ("aty".equals(saleorder.getFlag())){
					sgvList = sgvList.stream().filter(item->ErpConst.ONE.equals(item.getHaveInstallation())).collect(Collectors.toList());
				}

				if ("atn".equals(saleorder.getFlag())){
					sgvList = sgvList.stream().filter(item->ErpConst.ZERO.equals(item.getHaveInstallation())).collect(Collectors.toList());
				}

				if (CollectionUtils.isNotEmpty(sgvList)) {
					// 批量查询售后退货数量
					List<SaleorderGoodsVo> saleAfterList = afterSalesGoodsMapper.batchSaleorderAftersaleReturnGoods(sgvList);
					for (SaleorderGoodsVo sgv : sgvList) {
						for (SaleorderGoodsVo sa : saleAfterList) {
							if (sgv.getSaleorderGoodsId().equals(sa.getSaleorderGoodsId())) {
								sgv.setAfterReturnNum(sa.getSaleAfterNum());
								break;
							}
						}
					}

					for (SaleorderGoodsVo sgv : sgvList) {
						// 计算商品的售后数量上限
						if ("at".equals(saleorder.getFlag()) || "wx".equals(saleorder.getFlag()) || "aty".equals(saleorder.getFlag())
								|| "atn".equals(saleorder.getFlag())) {
							if (sgv.getNum().equals(sgv.getDeliveryNum())) {// 全部已发货
								sgv.setAfterSaleUpLimitNum(
										sgv.getNum() - (sgv.getAfterReturnNum() == null ? 0 : sgv.getAfterReturnNum()));
							} else if (sgv.getNum() > sgv.getDeliveryNum() && sgv.getDeliveryNum() > 0) {// 部分发货
								Integer sale = sgv.getNum() - (sgv.getAfterReturnNum() == null ? 0 : sgv.getAfterReturnNum());
								if (sale >= sgv.getDeliveryNum()) {// 减去退货后数量大于已发货
									sgv.setAfterSaleUpLimitNum(sgv.getDeliveryNum());
								} else {
									sgv.setAfterSaleUpLimitNum(sale);
								}
							} else {
								sgv.setAfterSaleUpLimitNum(0);
							}
						}

						if ("th".equals(saleorder.getFlag())) {
							sgv.setAfterSaleUpLimitNum(
									sgv.getNum() - (sgv.getAfterReturnNum() == null ? 0 : sgv.getAfterReturnNum()));
							Integer arrivalNum = saleorderGoodsMapper.getArrivalNum(sgv.getSaleorderGoodsId());
							sgv.setReceiveNum(arrivalNum);
							sgv.setInvoiceNum(saleorderMapper.getHaveInvoiceNums(sgv.getSaleorderGoodsId()));
						}
						if ("hh".equals(saleorder.getFlag())){
							int realNum = sgv.getNum() - (sgv.getAfterReturnNum() == null ? 0 : sgv.getAfterReturnNum());
							sgv.setAfterSaleUpLimitNum(Math.min(realNum, sgv.getDeliveryNum()));
							Integer arrivalNum = saleorderGoodsMapper.getArrivalNum(sgv.getSaleorderGoodsId());
							sgv.setReceiveNum(arrivalNum);
							sgv.setInvoiceNum(saleorderMapper.getHaveInvoiceNums(sgv.getSaleorderGoodsId()));
						}
						// 销售订单中产品为直发且为已发货已收货状态，新增售后退货/换货，已发货数量和已收货数量为num
						if (("hh".equals(saleorder.getFlag()) || "th".equals(saleorder.getFlag()))
								&& sgv.getDeliveryDirect() == 1 && sgv.getDeliveryStatus() == 2
								&& sgv.getArrivalStatus() == 2) {
							sgv.setDeliveryNum(sgv.getNum());
							sgv.setReceiveNum(sgv.getNum());
						}
					}
					sv.setSgvList(sgvList);
				}

				if ("at".equals(saleorder.getFlag()) || "wx".equals(saleorder.getFlag()) || "aty".equals(saleorder.getFlag())
						|| "atn".equals(saleorder.getFlag())) {
					if(sv.getTakeTraderAddressId() != 0) {
						TraderAddress ta = traderAddressMapper.selectByPrimaryKey(sv.getTakeTraderAddressId());
						sv.setAreaId(ta.getAreaId());
					}
				}

				TraderContact traderContact = new TraderContact();
				traderContact.setTraderId(sv.getTraderId());
				traderContact.setTraderType(1);
				traderContact.setIsEnable(1);
				List<TraderContact> tcList = traderContactMapper.getTraderContact(traderContact);
				sv.setTcList(tcList);
			}
			if ("hh".equals(saleorder.getFlag())) {// 换货
				TraderAddress traderAddress = new TraderAddress();
				traderAddress.setTraderId(sv.getTraderId());
				traderAddress.setTraderType(1);
				traderAddress.setIsEnable(1);
				List<TraderAddress> list = traderAddressMapper.getTraderAddressListByModel(traderAddress);
				sv.setTavList(list);
			} else if (("at".equals(saleorder.getFlag()) || "wx".equals(saleorder.getFlag()) || "aty".equals(saleorder.getFlag())
					|| "atn".equals(saleorder.getFlag()))
					&& sv.getTakeTraderAddressId() != 0) {// 安调维修
				TraderAddress traderAddress = traderAddressMapper.selectByPrimaryKey(sv.getTakeTraderAddressId());
				sv.setAreaId(traderAddress.getAreaId());
			} else if ("tp".equals(saleorder.getFlag()) || "qp".equals(saleorder.getFlag())) {// 退票
				// todo 丢票、退票校验发票金额&数量
				List<SaleorderGoodsVo> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
				// 查询所有销售单SKU
				Invoice invoice = new Invoice();
				invoice.setCompanyId(sv.getCompanyId());
				invoice.setSgvList(sgvList);
				List<Integer> typeList = new ArrayList<>();
				typeList.add(504);
				typeList.add(505);
				invoice.setTypeList(typeList);
				// 查询销售单SKU关联的 销售发票 & 售后发票
				List<Invoice> list = invoiceMapper.getInvoiceListByParam(invoice);
				if (list != null && list.size() > 0) {
					Iterator<Invoice> iterator = list.iterator();
					while (iterator.hasNext()) {
						Invoice asi = (Invoice) iterator.next();
						// 查看发票 关联表（销售-售后） -- 发生售后退票 红票
						List<RInvoiceJInvoice> rInvoiceJInvoiceList = rInvoiceJInvoiceMapper
								.getRInvoiceJInvoiceList(asi.getInvoiceId());
						if (rInvoiceJInvoiceList != null && rInvoiceJInvoiceList.size() > 0) {
							// 查询销售发票 和 售后发票差值 （可退票金额） 大于0才可以退票
							BigDecimal amount = rInvoiceJInvoiceMapper.getAfterInvoiceTotalAmount(asi.getInvoiceId());
							if (amount.compareTo(new BigDecimal(0)) <= 0) {
								iterator.remove();
							} else {
								asi.setAmount(amount);
							}
						}
					}
				}
				sv.setInvoiceList(list);
				TraderContact traderContact = new TraderContact();
				traderContact.setTraderId(sv.getTraderId());
				traderContact.setTraderType(1);
				traderContact.setIsEnable(1);
				List<TraderContact> tcList = traderContactMapper.getTraderContact(traderContact);
				sv.setTcList(tcList);

				Trader trader = traderMapper.selectByPrimaryKey(sv.getTraderId());
				sv.setPayee(trader.getTraderName());
				TraderFinanceVo traderFinance = new TraderFinanceVo();
				traderFinance.setTraderId(sv.getTraderId());
				traderFinance.setTraderType(1);
				traderFinance = traderFinanceMapper.getTraderCustomerFinance(traderFinance);
				if (traderFinance != null) {
					sv.setBank(traderFinance.getBank());
					sv.setBankAccount(traderFinance.getBankAccount());
					sv.setBankCode(traderFinance.getBankCode());
					sv.setPaid(saleorderMapper.getSaleorderPaymentAmount(sv.getSaleorderId()));
				}
			} else if ("jz".equals(saleorder.getFlag())) {
				List<SaleorderGoodsVo> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
				TraderContact traderContact = new TraderContact();
				traderContact.setTraderId(sv.getTraderId());
				traderContact.setTraderType(1);
				traderContact.setIsEnable(1);
				List<TraderContact> tcList = traderContactMapper.getTraderContact(traderContact);
				sv.setTcList(tcList);
				sv.setSgvList(sgvList);
			} else if ("tk".equals(saleorder.getFlag())) {
				TraderContact traderContact = new TraderContact();
				traderContact.setTraderId(sv.getTraderId());
				traderContact.setTraderType(1);
				traderContact.setIsEnable(1);
				List<TraderContact> tcList = traderContactMapper.getTraderContact(traderContact);
				sv.setTcList(tcList);
				Trader trader = traderMapper.selectByPrimaryKey(sv.getTraderId());
				sv.setPayee(trader.getTraderName());
				TraderFinanceVo traderFinance = new TraderFinanceVo();
				traderFinance.setTraderId(sv.getTraderId());
				traderFinance.setTraderType(1);
				traderFinance = traderFinanceMapper.getTraderCustomerFinance(traderFinance);
				if (traderFinance != null) {
					sv.setBank(traderFinance.getBank());
					sv.setBankAccount(traderFinance.getBankAccount());
					sv.setBankCode(traderFinance.getBankCode());
				}

				sv.setPaid(saleorderMapper.getPaymentAndPeriodAmount(sv.getSaleorderId()));
			} else if ("qt".equals(saleorder.getFlag())) {
				List<SaleorderGoodsVo> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
				TraderContact traderContact = new TraderContact();
				traderContact.setTraderId(sv.getTraderId());
				traderContact.setTraderType(1);
				traderContact.setIsEnable(1);
				List<TraderContact> tcList = traderContactMapper.getTraderContact(traderContact);
				sv.setTcList(tcList);
				sv.setSgvList(sgvList);
			}
            if ("hh".equals(saleorder.getFlag())) {
                List<TraderAddress> list = sv.getTavList();
                if (list != null && list.size() > 0) {
                    List<TraderAddressVo> tavList = new ArrayList<>();
                    TraderAddressVo tav = null;
                    for (TraderAddress ta : list) {
                        tav = new TraderAddressVo();
                        tav.setTraderAddress(ta);
                        tav.setArea(getAddressByAreaId(ta.getAreaId()));
                        tavList.add(tav);
                    }
                    sv.setTraderAddressVoList(tavList);
                }
            }
            return sv;
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }

	}

    @Override
    public List<SaleorderGoodsWarrantyVo> getSaleorderGoodsWarrantys(Integer saleorderId) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<SaleorderGoodsWarrantyVo>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoodsWarrantyVo>>>() {
            };
            String url = httpUrl + "order/saleorder/getsaleordergoodswarrantys.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderId, clientId, clientKey, TypeRef);
            List<SaleorderGoodsWarrantyVo> sgw = (List<SaleorderGoodsWarrantyVo>) result.getData();
            if (sgw != null && sgw.size() > 0) {
                List<Integer> userIds = new ArrayList<>();
                for (SaleorderGoodsWarrantyVo s : sgw) {
                    if (s.getCreator() > 0) {
                        userIds.add(s.getCreator());
                    }

				}

                if (userIds.size() > 0) {
                    List<User> userList = userMapper.getUserByUserIds(userIds);
                    for (SaleorderGoodsWarrantyVo s : sgw) {
                        for (User u : userList) {
                            if (u.getUserId().equals(s.getCreator())) {
                                s.setCreateName(u.getUsername());
                            }
                        }
                    }
                }
            }
            return sgw;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }


	@Override
	public BigDecimal getSaleorderPriceInfo(Integer saleorderId){
		return saleorderMapper.getSaleorderPriceInfo(saleorderId);
	}


	@Override
    public Map<String, BigDecimal> getSaleorderDataInfo(Integer saleorderId) {
        Map<String, BigDecimal> resultMap = new HashMap<>();
        Map<String, BigDecimal> map = new HashMap<>();
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Map<String, BigDecimal>>> TypeRef = new TypeReference<ResultInfo<Map<String, BigDecimal>>>() {
            };
            String url = httpUrl + "order/saleorder/getsaleorderdatainfo.htm";

            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderId, clientId, clientKey, TypeRef);
            resultMap = (Map<String, BigDecimal>) result.getData();
            if (null != resultMap) {
				BigDecimal awardAmount = getSaleorderPriceInfo(saleorderId);
				map.put("awardAmount",awardAmount==null?new BigDecimal(0.00):awardAmount);
                map.put("paymentAmount", resultMap.get("paymentAmount"));// 订单已收款金额(不含账期)
                map.put("periodAmount", resultMap.get("periodAmount"));// 订单账期金额
                map.put("realAmount", resultMap.get("realAmount"));// 订单实际金额
                map.put("lackAccountPeriodAmount", resultMap.get("lackAccountPeriodAmount"));// 订单实际金额
                map.put("refundBalanceAmount", resultMap.get("refundBalanceAmount"));// 订单实际金额
                map.put("receivedAmount", resultMap.get("receivedAmount"));// 订单已收款金额
            } else {
				map.put("awardAmount",new BigDecimal(0.00));
                map.put("paymentAmount", new BigDecimal(0.00));
                map.put("periodAmount", new BigDecimal(0.00));
                map.put("realAmount", new BigDecimal(0.00));
                map.put("lackAccountPeriodAmount", new BigDecimal(0.00));
                map.put("refundBalanceAmount", new BigDecimal(0.00));
                map.put("receivedAmount", new BigDecimal(0.00));
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }

    @Override
    public List<SaleorderGoodsWarrantyVo> getAllSaleorderGoodsWarrantys(Saleorder saleorder) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<SaleorderGoodsWarrantyVo>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoodsWarrantyVo>>>() {
            };
            String url = httpUrl + "order/saleorder/getallsaleordergoodswarrantys.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
            List<SaleorderGoodsWarrantyVo> sgw = (List<SaleorderGoodsWarrantyVo>) result.getData();
            if (sgw != null && sgw.size() > 0) {
                List<Integer> userIds = new ArrayList<>();
                for (SaleorderGoodsWarrantyVo s : sgw) {
                    if (null != s.getCreator() && s.getCreator() > 0) {
                        userIds.add(s.getCreator());
                    }

                }

                if (userIds.size() > 0) {
                    List<User> userList = userMapper.getUserByUserIds(userIds);
                    for (SaleorderGoodsWarrantyVo s : sgw) {
                        for (User u : userList) {
                            if (u.getUserId().equals(s.getCreator())) {
                                s.setCreateName(u.getUsername());
                            }
                        }
                    }
                }
            }
            return sgw;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    public SaleorderGoodsWarrantyVo getSaleorderGoodsInfoForWarranty(SaleorderGoodsWarrantyVo goodsWarrantyVo) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<SaleorderGoodsWarrantyVo>> TypeRef = new TypeReference<ResultInfo<SaleorderGoodsWarrantyVo>>() {
            };
            String url = httpUrl + "order/saleorder/getsaleordergoodsinfoforwarranty.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goodsWarrantyVo, clientId, clientKey,
                    TypeRef);
            SaleorderGoodsWarrantyVo sgw = (SaleorderGoodsWarrantyVo) result.getData();
            return sgw;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    public SaleorderGoodsWarrantyVo saveAddGoodsWarranty(HttpServletRequest request, HttpSession session,
                                                         SaleorderGoodsWarrantyVo goodsWarrantyVo) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();
        goodsWarrantyVo.setCreator(user.getUserId());
        goodsWarrantyVo.setAddTime(time);
        goodsWarrantyVo.setModTime(time);
        goodsWarrantyVo.setUpdater(user.getUserId());

        if (Integer.parseInt(request.getParameter("zone")) > 0) {
            goodsWarrantyVo.setAreaId(Integer.parseInt(request.getParameter("zone")));
        } else {
            goodsWarrantyVo.setAreaId(Integer.parseInt(request.getParameter("city")));
        }

        // 接口调用
        String url = httpUrl + "order/saleorder/saveaddgoodswarranty.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<SaleorderGoodsWarrantyVo>> TypeRef2 = new TypeReference<ResultInfo<SaleorderGoodsWarrantyVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, goodsWarrantyVo, clientId, clientKey,
                    TypeRef2);
            SaleorderGoodsWarrantyVo res = (SaleorderGoodsWarrantyVo) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public SaleorderGoodsWarrantyVo getGoodsWarrantyInfo(SaleorderGoodsWarrantyVo goodsWarrantyVo) {
        // 接口调用
        String url = httpUrl + "order/saleorder/getgoodswarrantyinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<SaleorderGoodsWarrantyVo>> TypeRef2 = new TypeReference<ResultInfo<SaleorderGoodsWarrantyVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, goodsWarrantyVo, clientId, clientKey,
                    TypeRef2);
            SaleorderGoodsWarrantyVo res = (SaleorderGoodsWarrantyVo) result2.getData();
            if (null != res && res.getCreator() != null && res.getCreator() > 0) {
                res.setCreateName(getUserNameByUserId(res.getCreator()));
            }
            if (null != res && res.getAreaId() != null && res.getAreaId() > 0) {
                String area = (String) regionService.getRegion(res.getAreaId(), 2);
                res.setArea(area);
            }
            return res;
        } catch (IOException e) {
            return null;
        }
    }
    /*新商品流*/
	@Override
	public SaleorderGoodsWarrantyVo getGoodsWarrantyInfoNew(SaleorderGoodsWarrantyVo goodsWarrantyVo) {
		// 接口调用
		String url = httpUrl + "order/saleorder/getgoodswarrantyinfoNew.htm";

		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<SaleorderGoodsWarrantyVo>> TypeRef2 = new TypeReference<ResultInfo<SaleorderGoodsWarrantyVo>>() {
		};
		try {
			ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, goodsWarrantyVo, clientId, clientKey,
					TypeRef2);
			SaleorderGoodsWarrantyVo res = (SaleorderGoodsWarrantyVo) result2.getData();
			if (null != res && res.getCreator() != null && res.getCreator() > 0) {
				res.setCreateName(getUserNameByUserId(res.getCreator()));
			}
			if (null != res && res.getAreaId() != null && res.getAreaId() > 0) {
				String area = (String) regionService.getRegion(res.getAreaId(), 2);
				res.setArea(area);
			}
			return res;
		} catch (IOException e) {
			return null;
		}
	}

    @Override
    public SaleorderGoodsWarrantyVo saveEditGoodsWarranty(HttpServletRequest request, HttpSession session,
                                                          SaleorderGoodsWarrantyVo goodsWarrantyVo) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();
        goodsWarrantyVo.setModTime(time);
        goodsWarrantyVo.setUpdater(user.getUserId());

        if (Integer.parseInt(request.getParameter("zone")) > 0) {
            goodsWarrantyVo.setAreaId(Integer.parseInt(request.getParameter("zone")));
        } else {
            goodsWarrantyVo.setAreaId(Integer.parseInt(request.getParameter("city")));
        }

        // 接口调用
        String url = httpUrl + "order/saleorder/saveeditgoodswarranty.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<SaleorderGoodsWarrantyVo>> TypeRef2 = new TypeReference<ResultInfo<SaleorderGoodsWarrantyVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, goodsWarrantyVo, clientId, clientKey,
                    TypeRef2);
            SaleorderGoodsWarrantyVo res = (SaleorderGoodsWarrantyVo) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public boolean vailSaleorderGoodsRepeat(SaleorderGoods saleorderGoods) {
		if(saleorderGoods == null || saleorderGoods.getSaleorderId() == null){
			return false;
		}
		return saleorderGoodsMapper.vailSaleorderGoods(saleorderGoods) > 0 ? true : false;
    }

	@Override
	public SaleorderVo getPrintOrderInfo(Saleorder saleorder) {
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<SaleorderVo>> TypeRef = new TypeReference<ResultInfo<SaleorderVo>>() {
			};
			String url = httpUrl + "order/saleorder/getprintorderinfo.htm";
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			SaleorderVo sgw = (SaleorderVo) result.getData();
			return sgw;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
	}

    /**
     * <b>Description:</b><br>
     * 根据产品ID获取结算价格
     *
     * @return
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年12月18日 上午10:49:02
     */
    @Override
    public BigDecimal getSaleorderGoodsSettlementPrice(Integer goodsId, Integer companyId) {
        return goodsSettlementPriceMapper.getSaleorderGoodsSettlementPrice(goodsId, companyId);
    }

    @Override
    public Saleorder saveAddSaleorderInfo(Saleorder saleorder, HttpServletRequest request, HttpSession session) {
        // 归属销售
        if (saleorder.getTraderId() != null) {
			User belongUser = userService.getUserInfoByTraderId(saleorder.getTraderId(), 1);// 1客户，2供应商
            if (belongUser != null && belongUser.getUserId() != null) {
                saleorder.setUserId(belongUser.getUserId());
            }
            if (belongUser != null && belongUser.getOrgId() != null) {
                saleorder.setOrgId(belongUser.getOrgId());
            }
            if (belongUser != null && belongUser.getOrgName() != null) {
                saleorder.setOrgName(belongUser.getOrgName());
            }
        }

		// 确认订单
		//if(!OrderConstant.ORDER_TYPE_JCF.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_JCO.equals(saleorder.getOrderType())
		//		&& !OrderConstant.ORDER_TYPE_BD.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_HC.equals(saleorder.getOrderType())
		//		&& !OrderConstant.ORDER_TYPE_DH.equals(saleorder.getOrderType())){
		//	saleorder.setConfirmTime(new Date());
		//	saleorder.setConfirmStatus(1);
		//}
		if(checkOrderType(saleorder.getOrderType())){
			saleorder.setConfirmTime(new Date());
			saleorder.setConfirmStatus(1);
		}


        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(
                    httpUrl + "order/saleorder/saveaddsaleorderinfo.htm", saleorder, clientId, clientKey, TypeRef2);
            Saleorder res = (Saleorder) result2.getData();
            if(res != null){
				//VDERP-2263   订单售后采购改动通知
            	orderCommonService.updateSaleOrderDataUpdateTime(res.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_GENERATE);
			}
            return res;
        } catch (IOException e) {
            return null;
        }
    }

	public boolean checkOrderType(Integer orderType){
		List<Integer> typeList = new ArrayList<>();
		typeList.add(OrderConstant.ORDER_TYPE_JCF);
		typeList.add(OrderConstant.ORDER_TYPE_JCO);
		typeList.add(OrderConstant.ORDER_TYPE_BD);
		typeList.add(OrderConstant.ORDER_TYPE_HC);
		typeList.add(OrderConstant.ORDER_TYPE_DH);
		if(typeList.contains(orderType)){
			return true;
		}
		return false;
	}

    @Override
    public Saleorder confirmArrival(Saleorder saleorder, HttpServletRequest request, HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();
        List<SaleorderGoods> saleorderGoodsList = new ArrayList<>();

        if (null != request.getParameter("id_str")) {
            String[] categoryAttributeIds2 = request.getParameter("id_str").split("_");
            for (String attIds2 : categoryAttributeIds2) {
                if (attIds2 == "" || null == request.getParameter("arrivalStatus_" + attIds2))
                    continue;
                SaleorderGoods saleorderGoods = new SaleorderGoods();

                saleorderGoods.setSaleorderGoodsId(Integer.parseInt(attIds2));
                saleorderGoods.setArrivalStatus(Integer.parseInt(request.getParameter("arrivalStatus_" + attIds2)));
                saleorderGoods.setArrivalUserId(user.getUserId());
                saleorderGoods.setArrivalTime(time);

                saleorderGoodsList.add(saleorderGoods);
            }

        }
        saleorder.setGoodsList(saleorderGoodsList);

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + "order/saleorder/confirmarrival.htm",
                    saleorder, clientId, clientKey, TypeRef2);
            Saleorder res = (Saleorder) result2.getData();
            if(result2 != null && result2.getCode().equals(0)){
                orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_EXPRESS_END);
            }
            return res;
        } catch (IOException e) {
            return null;
        }

    }

	@Override
	public SaleorderModifyApply convertModifyApply(SaleorderModifyApply saleorderModifyApply, HttpServletRequest request, HttpSession session) {
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();
		saleorderModifyApply.setCompanyId(user.getCompanyId());
		saleorderModifyApply.setAddTime(time);
		saleorderModifyApply.setCreator(user.getUserId());

		List<SaleorderModifyApplyGoods> saleorderGoodsList = new ArrayList<>();

		if (null != request.getParameter("id_str")) {
			String[] categoryAttributeIds2 = request.getParameter("id_str").split("_");
			for (String attIds2 : categoryAttributeIds2) {
				if ("".equals(attIds2) || null == request.getParameter("deliveryDirect_" + attIds2)) {
					continue;
				}
				SaleorderModifyApplyGoods saleorderGoods = new SaleorderModifyApplyGoods();
				saleorderGoods.setSaleorderGoodsId(Integer.parseInt(attIds2));
				saleorderGoods.setDeliveryDirect(Integer.parseInt(request.getParameter("deliveryDirect_" + attIds2)));
				saleorderGoods.setDeliveryDirectComments(request.getParameter("deliveryDirectComments_" + attIds2));
				saleorderGoods.setGoodsComments(request.getParameter("goodsComments_" + attIds2));
				saleorderGoods.setOldDeliveryDirect(Integer.parseInt(request.getParameter("oldDeliveryDirect_" + attIds2)));
				saleorderGoods.setOldDeliveryDirectComments(request.getParameter("oldDeliveryDirectComments_" + attIds2));
				saleorderGoods.setOldGoodsComments(request.getParameter("oldGoodsComments_" + attIds2));
				saleorderGoodsList.add(saleorderGoods);
			}

		}
		saleorderModifyApply.setGoodsList(saleorderGoodsList);
		return saleorderModifyApply;
	}

	@Override
	public void saleorderGoodsDeliveryDirectModifyCheck(List<SaleorderGoods> saleorderGoods) throws Exception {
		if (saleorderGoods.size() == 0) {
			return;
		}
		Map<Integer, Integer> saleorderGoodDeliveryDirectMap = saleorderGoods.stream().collect(Collectors.toMap(SaleorderGoods::getSaleorderGoodsId
				,SaleorderGoods::getDeliveryDirect));
		List<SaleorderGoods> associatedSaleorderGoodsList =
				saleorderGoodsMapper.getSaleorderGoodsAssociatedValidBuyorder(new ArrayList<>(saleorderGoodDeliveryDirectMap.keySet()));
		for (SaleorderGoods item : associatedSaleorderGoodsList){
			if (!item.getDeliveryDirect().equals(saleorderGoodDeliveryDirectMap.get(item.getSaleorderGoodsId()))){
				throw new ShowErrorMsgException("商品：" + item.getSku() + "已关联采购单，无法申请修改销售单发货方式");
			}
		}
	}


	@Override
    public SaleorderModifyApply modifyApplySave(SaleorderModifyApply saleorderModifyApply, HttpServletRequest request,
                                                HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();
        saleorderModifyApply.setCompanyId(user.getCompanyId());
        saleorderModifyApply.setAddTime(time);
        saleorderModifyApply.setCreator(user.getUserId());
		saleorderModifyApply.setDeliveryDelayTime(DateUtil.convertLong(saleorderModifyApply.getDeliveryDelayTimeStr(),DateUtil.DATE_FORMAT));

		if (saleorderModifyApply.getTakeTraderContactId() != null && saleorderModifyApply.getTakeTraderContactId() > 0  && StringUtils.isEmpty(saleorderModifyApply.getTakeTraderContactMobile())){
			TraderContactGenerate traderContactGenerate = traderContactGenerateMapper.selectByPrimaryKey(saleorderModifyApply.getTakeTraderContactId());
			if(!Objects.isNull(traderContactGenerate)){
				saleorderModifyApply.setTakeTraderContactMobile(traderContactGenerate.getMobile());
				saleorderModifyApply.setTakeTraderContactTelephone(traderContactGenerate.getTelephone());
				saleorderModifyApply.setTakeTraderContactName(traderContactGenerate.getName());
			}
		}

		if (saleorderModifyApply.getTakeTraderAddressId() != null && saleorderModifyApply.getTakeTraderAddressId()>0 && StringUtils.isEmpty(saleorderModifyApply.getTakeTraderArea())){
			TraderAddressGenerate traderAddressGenerate = traderAddressGenerateMapper.selectByPrimaryKey(saleorderModifyApply.getTakeTraderAddressId());
			if (!Objects.isNull(traderAddressGenerate)){
				if (traderAddressGenerate.getAreaId()!=null && traderAddressGenerate.getAreaId()>0){
					saleorderModifyApply.setTakeTraderArea(getAddressByAreaId(traderAddressGenerate.getAreaId()));
				}
				saleorderModifyApply.setTakeTraderAddress(traderAddressGenerate.getAddress());
			}

		}

        List<SaleorderModifyApplyGoods> saleorderGoodsList = new ArrayList<>();

        if (null != request.getParameter("id_str")) {
            String[] categoryAttributeIds2 = request.getParameter("id_str").split("_");
            for (String attIds2 : categoryAttributeIds2) {
                if ("".equals(attIds2) || null == request.getParameter("deliveryDirect_" + attIds2)) {
					continue;
				}
                SaleorderModifyApplyGoods saleorderGoods = new SaleorderModifyApplyGoods();

                saleorderGoods.setSaleorderGoodsId(Integer.parseInt(attIds2));
				//VDERP-13661 异步操作拦截发货方式的修改
				SaleorderGoods curSaleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(Integer.parseInt(attIds2));
				if(!ErpConst.ZERO.equals(curSaleorderGoods.getDeliveryStatus())
						&& !request.getParameter("oldDeliveryDirect_" + attIds2).equals(request.getParameter("deliveryDirect_" + attIds2))){
					//商品已发货，且修改了发货方式
					request.setAttribute("forceModify",ErpConst.ONE);
					return null;
				}

                saleorderGoods.setDeliveryDirect(Integer.parseInt(request.getParameter("deliveryDirect_" + attIds2)));
                saleorderGoods.setDeliveryDirectComments(request.getParameter("deliveryDirectComments_" + attIds2));
                saleorderGoods.setGoodsComments(request.getParameter("goodsComments_" + attIds2));
                saleorderGoods
                        .setOldDeliveryDirect(Integer.parseInt(request.getParameter("oldDeliveryDirect_" + attIds2)));
                saleorderGoods
                        .setOldDeliveryDirectComments(request.getParameter("oldDeliveryDirectComments_" + attIds2));
                saleorderGoods.setOldGoodsComments(request.getParameter("oldGoodsComments_" + attIds2));
				saleorderGoods.setInsideComments(request.getParameter("insideComments_" + attIds2));
				saleorderGoods.setOldInsideComments(request.getParameter("oldInsideComments_" + attIds2));
                saleorderGoodsList.add(saleorderGoods);
            }

        }
        saleorderModifyApply.setGoodsList(saleorderGoodsList);
		//VDERP-9344 【FAQ3244】【功能】【erp】非票货同行订单，出库后，无法修改票据信息
		//兼容新老订单修改
		if(saleorderModifyApply.getLogisticsId() == null){
			saleorderModifyApply.setLogisticsId(0);
		}
		Saleorder saleorder = new Saleorder();
		saleorder.setSaleorderId(saleorderModifyApply.getSaleorderId());
		List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsById(saleorder);
		saleorder = saleorderMapper.getSaleOrderById(saleorderModifyApply.getSaleorderId());
		//是否修改了发货信息和直发状态
		boolean isSendWmsflag = isSendWMS(saleorderModifyApply,saleorder,saleorderGoods);
		boolean isModifyOrderOutput = isModifyOrderOutput(saleorderModifyApply,saleorder);
		if(isSendWmsflag){
			try {

				boolean flag = cancelTypeService.cancelOutSaleOutMethod(saleorder.getSaleorderNo(), CancelReasonConstant.SALE_EDIT_ORDER);
				if(!flag){
					request.setAttribute("cancel","fail");
					return null;
				}else {
					saleorderModifyApply.setIsWmsCancel(1);
				}
				logger.info("ERP取消出库单至WMS的 单号:{}, 响应:{}",saleorder.getSaleorderNo(),flag);
			} catch (Exception e) {
				logger.error("ERP取消出库单至WMS的请求接口报错", e);
			}
		} else if(isModifyOrderOutput){
			try {
				if(!logicalSaleorderChooseServiceImpl.modifyOrderOutput(saleorder.getSaleorderId(),saleorderModifyApply,user)){
					request.setAttribute("cancel","fail");
					logger.info("ERP修改销售单信息失败，WMS拒绝修改{}",saleorder.getSaleorderNo());
					return null;
				}
				logger.info("ERP修改销售单信息成功");
			} catch (Exception e) {
				logger.error("ERP取消出库单至WMS的请求接口报错{}", saleorder.getSaleorderNo(), e);
			}
		}
		// 定义反序列化 数据格式
        final TypeReference<ResultInfo<SaleorderModifyApply>> TypeRef2 = new TypeReference<ResultInfo<SaleorderModifyApply>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.postTimeOut5Min(
                    httpUrl + "order/saleorder/modifyapplysave.htm", saleorderModifyApply, clientId, clientKey,
                    TypeRef2);
			return (SaleorderModifyApply) result2.getData();
        } catch (Exception e) {
			logger.error("ERP取消出库单至WMS的请求接口报错{}", saleorder.getSaleorderNo(), e);
            return null;
        }
    }

	@Override
	public boolean isSendWMS(SaleorderModifyApply saleorderModifyApply, Saleorder saleorder, List<SaleorderGoods> saleorderGoods) {
    	logger.info("isSendWMS 单号:{},newInfo:{},oldInfo:{}",saleorder.getSaleorderNo(),JSON.toJSONString(saleorderModifyApply),JSON.toJSONString(saleorderGoods));
		//如果修改发货信息或者商品直发状态则为true
		// 需要将订单修改中的收货联系人，收货地址的校验从ID改为文本校验
		if (saleorder.getTakeTraderAddress() != null &&
				!saleorder.getTakeTraderAddress().equals(saleorderModifyApply.getTakeTraderAddress())) {
			return true;
		}
		if (saleorder.getTakeTraderArea() != null
				&& !saleorder.getTakeTraderArea().equals(saleorderModifyApply.getTakeTraderArea())) {
			return true;
		}
		if (saleorder.getTakeTraderContactName() != null
				&& !saleorder.getTakeTraderContactName().equals(saleorderModifyApply.getTakeTraderContactName())) {
			return true;
		}
		if (saleorder.getTakeTraderContactMobile() != null
				&& !saleorder.getTakeTraderContactMobile().equals(saleorderModifyApply.getTakeTraderContactMobile())) {
			return true;
		}
		if (!saleorder.getOrderType().equals(OrderConstant.ORDER_TYPE_HC)) {
			if (saleorder.getTakeTraderAddressId() != null
					&& !saleorder.getTakeTraderAddressId().equals(saleorderModifyApply.getTakeTraderAddressId())) {
				return true;
			}
			if (saleorder.getTakeTraderContactId() != null
					&& !saleorder.getTakeTraderContactId().equals(saleorderModifyApply.getTakeTraderContactId())) {
				return true;
			}
			if (saleorder.getIsPrintout() != null
					&& !saleorder.getIsPrintout().equals(saleorderModifyApply.getIsPrintout())) {
				return true;
			}
		}
		if (saleorder.getLogisticsComments() == null && saleorderModifyApply.getLogisticsComments() != null){
			//原物流备注为null，修改后不为null
			return true;
		}
		if (saleorder.getLogisticsComments() != null
				&& !saleorder.getLogisticsComments().equals(saleorderModifyApply.getLogisticsComments())) {
			// 是否修改了物流备注
			return true;
		}

		Map<Integer,SaleorderModifyApplyGoods> modifyApplyGoodsMap = saleorderModifyApply.getGoodsList()
				.stream()
				.collect(Collectors.toMap(SaleorderModifyApplyGoods::getSaleorderGoodsId,item->item));
		for (SaleorderGoods sg : saleorderGoods) {
			SaleorderModifyApplyGoods saleorderModifyApplyGoods = modifyApplyGoodsMap.get(sg.getSaleorderGoodsId());
			if (saleorderModifyApplyGoods != null
					&& !sg.getDeliveryDirect().equals(saleorderModifyApplyGoods.getDeliveryDirect())) {
				return true;
			}

		}
		return false;
	}
	@Override
	public boolean isModifyOrderOutput(SaleorderModifyApply saleorderModifyApply, Saleorder saleorder){
		if(saleorder.getDeliveryType() != null
				&& !saleorder.getDeliveryType().equals(saleorderModifyApply.getDeliveryType())){
			return true;
		}
		if(saleorder.getDeliveryClaim() != null
				&& !saleorder.getDeliveryClaim().equals(saleorderModifyApply.getDeliveryClaim())){
			return true;
		}
		if(saleorder.getDeliveryDelayTime() != null
				&& !saleorder.getDeliveryDelayTime().equals(saleorderModifyApply.getDeliveryDelayTime())){
			return true;
		}
		if (saleorder.getLogisticsId() != null && !saleorder.getLogisticsId().equals(saleorderModifyApply.getLogisticsId())){
			return true;
		}
		return false;
	}

	/**
     * <b>Description:</b><br>
     * 保存申请提前采购
     *
     * @param saleorder
     * @param user
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2018年1月17日 上午9:56:21
     */
    @Override
    public ResultInfo<?> saveApplyPurchase(Saleorder saleorder, User user) {
        saleorder.setHaveAdvancePurchase(1);// 有提前采购
        saleorder.setAdvancePurchaseStatus(2);// 审核通过
        saleorder.setAdvancePurchaseTime(DateUtil.sysTimeMillis());
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_APPLY_PURCHASE,
                    saleorder, clientId, clientKey, TypeRef);
            if (result == null || result.getCode() == -1) {
                return new ResultInfo<>(-1, "操作失败");
            }
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public Map<String, Object> getSaleorderModifyApplyListPage(HttpServletRequest request,
                                                               Saleorder saleorderModifyApply, Page page) {
        Map<String, Object> map = null;
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Map<String, Object>>> TypeRef = new TypeReference<ResultInfo<Map<String, Object>>>() {
            };
            String url = httpUrl + "order/saleorder/getsaleordermodifyapplylistpage.htm";

            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderModifyApply, clientId, clientKey,
                    TypeRef, page);
            if (result != null && result.getCode() == 0) {
                Map<String, Object> result_map = (Map<String, Object>) result.getData();
                if (result_map != null && result_map.size() > 0) {
                    map = new HashMap<String, Object>();

                    net.sf.json.JSONArray json = null;
                    String openInvoiceApplyStr = result_map.get("saleorderList").toString();
                    json = net.sf.json.JSONArray.fromObject(openInvoiceApplyStr);

                    List<Saleorder> saleorderList = (List<Saleorder>) json.toCollection(json, Saleorder.class);
                    map.put("saleorderList", saleorderList);

                    saleorderModifyApply = (Saleorder) JSONObject
                            .toBean(JSONObject.fromObject(result_map.get("saleorder")), Saleorder.class);
                    map.put("saleorder", saleorderModifyApply);

                    page = result.getPage();
                    map.put("page", page);
                }
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }


    @Override
    public SaleorderModifyApply getSaleorderModifyApplyInfo(SaleorderModifyApply saleorderModifyApply) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<SaleorderModifyApply>> TypeRef = new TypeReference<ResultInfo<SaleorderModifyApply>>() {
        };
        String url = httpUrl + "order/saleorder/getsaleordermodifyapplyinfo.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderModifyApply, clientId, clientKey,
                    TypeRef);
            saleorderModifyApply = (SaleorderModifyApply) result.getData();
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
        return saleorderModifyApply;
    }

    @Override
    public List<SaleorderModifyApplyGoods> getSaleorderModifyApplyGoodsById(SaleorderModifyApply saleorderModifyApply) {
        List<SaleorderModifyApplyGoods> list = null;
        // 定义反序列化 数据格式
//        final TypeReference<ResultInfo<List<SaleorderModifyApplyGoods>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderModifyApplyGoods>>>() {
//        };
//        String url = httpUrl + "order/saleorder/getsaleordermodifyapplygoodsbyid.htm";
//        try {
//            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderModifyApply, clientId, clientKey,
//                    TypeRef);
//            list = (List<SaleorderModifyApplyGoods>) result.getData();
//        } catch (IOException e) {
//            logger.error(Contant.ERROR_MSG, e);
//        }
		//AROP-13292 改为直接erp调用
		list = saleorderModifyApplyGoodsMapper.getSaleorderModifyApplyGoodsById(saleorderModifyApply);
        return list;
    }

    @Override
    public ResultInfo<?> saveSaleorderModifyApplyToSaleorder(SaleorderModifyApply saleorderModifyApply) {
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        String url = httpUrl + "order/saleorder/savesaleordermodifyapplytosaleorder.htm";
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderModifyApply, clientId, clientKey, TypeRef);
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return result;
    }

    @Override
    public List<SaleorderModifyApply> getSaleorderModifyApplyList(SaleorderModifyApply saleorderModifyApply) {
        List<SaleorderModifyApply> list = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<SaleorderModifyApply>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderModifyApply>>>() {
        };
        String url = httpUrl + "order/saleorder/getsaleordermodifyapplylist.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderModifyApply, clientId, clientKey,
                    TypeRef);
            list = (List<SaleorderModifyApply>) result.getData();
            if (list != null && list.size() > 0) {
                List<Integer> userIds = new ArrayList<>();
                for (SaleorderModifyApply s : list) {
                    if (null != s.getCreator() && s.getCreator() > 0) {
                        userIds.add(s.getCreator());
                    }

                }

                if (userIds.size() > 0) {
                    List<User> userList = userMapper.getUserByUserIds(userIds);
                    for (SaleorderModifyApply s : list) {
                        for (User u : userList) {
                            if (u.getUserId().equals(s.getCreator())) {
                                s.setCreateName(u.getUsername());
                            }
                        }
                    }
                }
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return list;
    }


    @Override
    public ResultInfo<?> getSaleorderGoodsExtraInfo(Saleorder saleorder) {
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        String url = httpUrl + "order/saleorder/getsaleordergoodsextrainfo.htm";
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return result;
    }

    @Override
    public SaleorderVo getSaleorderForSync(Integer saleorderId) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<SaleorderVo>> TypeRef = new TypeReference<ResultInfo<SaleorderVo>>() {
        };
        String url = httpUrl + "order/saleorder/getsaleorderforsync.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderId, clientId, clientKey, TypeRef);
            SaleorderVo saleorder = (SaleorderVo) result.getData();
            return saleorder;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    public SaleorderVo getMessageInfoForSync(Integer orderId) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<SaleorderVo>> TypeRef = new TypeReference<ResultInfo<SaleorderVo>>() {
        };
        String url = httpUrl + "order/saleorder/getmessageinfoforsync.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, orderId, clientId, clientKey, TypeRef);
            SaleorderVo saleorder = (SaleorderVo) result.getData();
            return saleorder;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    public ResultInfo<?> saveBatchAddSaleGoods(Saleorder saleorder) {
		logger.info("saveBatchAddSaleGoods orderInfo:{}", JSON.toJSONString(saleorder));
		try {
			if (ErpConst.ONE.equals(saleorderMapper.getBaseSaleorderInfo(saleorder.getSaleorderId()).getOrderType()) &&
					CollectionUtils.isNotEmpty(saleorder.getGoodsList())) {
				saleorder.getGoodsList().forEach(item -> {
					item.setRealPrice(item.getPrice());
					BigDecimal priceToUse = item.getPrice() == null ? BigDecimal.ZERO : item.getPrice();
					item.setMaxSkuRefundAmount(priceToUse.multiply(new BigDecimal(item.getNum())));
				});
			}
		}catch(Exception e){
			logger.error(Contant.ERROR_MSG, e);
		}
		logger.info("请求DB批量保存销售商品信息 order:{}", JSON.toJSONString(saleorder));
		// 定义反序列化 数据格式
        final TypeReference<ResultInfo<SaleorderVo>> TypeRef = new TypeReference<ResultInfo<SaleorderVo>>() {
        };
        String url = httpUrl + "order/saleorder/savebatchaddsalegoods.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
	@Deprecated
    public List<SaleorderGoods> getSaleorderGoodNoOutList(Integer saleorderId) {
        List<SaleorderGoods> list = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<SaleorderGoods>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoods>>>() {
        };
        String url = httpUrl + "order/saleorder/getsaleordergoodnooutlist.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderId, clientId, clientKey, TypeRef);
            list = (List<SaleorderGoods>) result.getData();
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return list;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	public ResultInfo<?> saveBatchReferenceCostPrice(Saleorder saleorder) {
    	//从db迁移过来
			saleorderGoodsMapper.updateReferenceCostPriceBatch(saleorder.getGoodsList());
			return new ResultInfo<>();
	}

    @Override
    public Map<Integer, Object> getSaleorderGoodsSettlementPriceByGoodsIds(List<Integer> goodsIds, Integer companyId) {
        // 查询产品ID和产品结算价的集合
        Map<Integer, Object> map = new HashMap<>();
        List<GoodsSettlementPrice> gspList = goodsSettlementPriceMapper
                .getSaleorderGoodsSettlementPriceByGoodsIds(goodsIds, companyId);
        for (GoodsSettlementPrice gsp : gspList) {
            map.put(gsp.getGoodsId(), gsp.getSettlementPrice());
        }
        return map;
    }


	@Override
	public Saleorder getBaseSaleorderInfoNew(Saleorder saleorder) {
		// TODO Auto-generated method stub
		final TypeReference<ResultInfo<Saleorder>> TypeRef = new TypeReference<ResultInfo<Saleorder>>() {
		};
		String url = httpUrl + "order/saleorder/getbasesaleorderinfonew.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			saleorder = (Saleorder) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return saleorder;
	}


	@SuppressWarnings("unchecked")
	@Override
	public List<SaleorderGoods> getSaleorderGoodsByIdNew(Saleorder saleorder) {
		// TODO Auto-generated method stub
		final TypeReference<ResultInfo<List<SaleorderGoods>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoods>>>() {
		};
		String url = httpUrl + "order/saleorder/getsaleordergoodsbyidnew.htm";
		List<SaleorderGoods> saleorderGoodsList = null;
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			saleorderGoodsList = (List<SaleorderGoods>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return saleorderGoodsList;
	}

    @Override
    public ResultInfo<?> updateSaleGoodsSave(SaleorderGoods saleorderGoods) {
        final TypeReference<ResultInfo<SaleorderVo>> TypeRef = new TypeReference<ResultInfo<SaleorderVo>>() {
        };
        String url = httpUrl + "order/saleorder/updatesalegoodssave.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderGoods, clientId, clientKey,
                    TypeRef);
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderId(saleorderGoods.getSaleorderId());
            User user = new User();
			logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(saleorder,user);
            return result;
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            return new ResultInfo<>();
        }
    }

    @Override
    public ResultInfo<?> updateSaleGoodsByAllSpecialGoods(Integer saleorderId) {
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        String url = httpUrl + "order/saleorder/updatesalegoodsbyallspecialgoods.htm";
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderId, clientId, clientKey, TypeRef);
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return result;
    }


    @Override
	public Saleorder getSaleOrderInfo(Saleorder saleorder) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Saleorder>> TypeRef = new TypeReference<ResultInfo<Saleorder>>() {
		};
		String url = httpUrl + "order/saleorder/getsaleorderinfo.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			if (result != null && result.getCode() == 0) {
				saleorder = (Saleorder) result.getData();
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return saleorder;
	}


	@Override
	public Saleorder getSaleOrderById(Integer saleOrderId) {
		return saleorderMapper.getSaleOrderById(saleOrderId);
	}

	@Override
	public List<SaleorderGoods> getSaleOrderGoodsList(Saleorder sale) {
		final TypeReference<ResultInfo<List<SaleorderGoods>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoods>>>() {
		};
		String url = httpUrl + "order/saleorder/getsaleordergoodslist.htm";
		List<SaleorderGoods> saleorderGoodsList = null;
		try {

			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, sale, clientId, clientKey, TypeRef);
			saleorderGoodsList = (List<SaleorderGoods>) result.getData();
			//设置风控信息
			riskCheckService.setSaleorderIsRiskInfo(sale,saleorderGoodsList);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return saleorderGoodsList;
	}



	/*
     * (non-Javadoc)
     *
     * @see com.vedeng.order.service.SaleorderService#getSaleorderListByInvoiceIds(
     * java.util.List)
     */
    @Override
    public List<Saleorder> getSaleorderListByInvoiceIds(List<Integer> invoiceIdList) {
        String url = restDbUrl + "rest/order/hc/v1/search/getSaleOrderList";
        final TypeReference<ResultInfo<List<SaleorderGoods>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderGoods>>>() {
        };
        ReqVo<List<Integer>> reqVo = new ReqVo<List<Integer>>();
        reqVo.setReq(invoiceIdList);
        List<Saleorder> saleorderList = null;
        ResultInfo<?> result = (ResultInfo<?>) HttpRestClientUtil.post(url, TypeRef, reqVo);
        if (result.getCode() == 0) {
            saleorderList = (List<Saleorder>) result.getData();
        }
        return saleorderList;
    }


	@Override
	public List<SaleorderWarehouseComments> getListComments(Saleorder saleorder) {
		List<SaleorderWarehouseComments> list = null;
		final TypeReference<ResultInfo<List<SaleorderWarehouseComments>>> TypeRef = new TypeReference<ResultInfo<List<SaleorderWarehouseComments>>>() {
		};
		String url = httpUrl + "order/saleorder/getlistcomments.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			list = (List<SaleorderWarehouseComments>) result.getData();
			if (list != null && list.size() > 0) {
				for (SaleorderWarehouseComments sc : list) {
					sc.setOpterName(getUserNameByUserId(sc.getCreator()));
				}
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}


	@Override
	public ResultInfo<?> updateComments(SaleorderWarehouseComments saleorderWarehouseComments) {
		String url = httpUrl + "order/saleorder/updateComments.htm";
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderWarehouseComments, clientId,
					clientKey, TypeRef);
			// 接口返回条码生成的记录
			if (result.getCode() == 0) {
				return new ResultInfo(0, "操作成功");
			} else {
				return new ResultInfo();
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo();
		}
	}

	@Override
	public SaleorderWarehouseComments getSaleorderWarehouseComments(
			SaleorderWarehouseComments saleorderWarehouseComments) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<SaleorderWarehouseComments>> TypeRef = new TypeReference<ResultInfo<SaleorderWarehouseComments>>() {
		};
		String url = httpUrl + "order/saleorder/getsaleorderwarehousecomments.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorderWarehouseComments, clientId,
					clientKey, TypeRef);
			if (result != null && result.getCode() == 0) {
				saleorderWarehouseComments = (SaleorderWarehouseComments) result.getData();
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return saleorderWarehouseComments;
	}

    /**
     * <b>Description:</b><br>
     * 订单状态的同步
     *
     * @param :
     * @return :
     * @Note <b>Author:</b> Bert <br>
     * <b>Date:</b> 2019/2/1 11:46
     */
    @Override
    public void synchronousOrderStatus(User curr_user, Saleorder saleorder) {
//        User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        // 得到一个销售单
        Saleorder sv = getBaseSaleorderInfo(saleorder);
        SaleorderVo saleorderVo = new SaleorderVo();
        // 解决空指针问题
        if (!StringUtils.isEmpty(sv)) {
            // 普发的(0是普发,1是直发)
            if (sv.getDeliveryDirect().equals(ErpConst.ZERO)) {
                if (!sv.getStatus().equals(ErpConst.THREE)) {
                    // 这边正对于部分发货(对应的部分收货和未收货的状态值)
                    if ((sv.getDeliveryStatus().equals(ErpConst.ONE) && (sv.getArrivalStatus().equals(ErpConst.ZERO)
                            || sv.getArrivalStatus().equals(ErpConst.ONE)))
                            || (sv.getDeliveryStatus().equals(ErpConst.TWO)
                            && !sv.getArrivalStatus().equals(ErpConst.TWO))) {
                        // 计算数据收货状态值(计算公式)
                        // 获取订单产品信息,以及所有产品的手填产品成本总和totalAmount
                        Saleorder sale = new Saleorder();
                        // 订单的ID
                        sale.setSaleorderId(sv.getSaleorderId());
                        // 交易者的ID
                        sale.setTraderId(saleorder.getTraderId());
                        sale.setCompanyId(curr_user.getCompanyId());
                        sale.setReqType(1);
                        // 获取订单产品信息,以及所有产品的手填产品成本总和totalAmount
                        List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsById(sale);
                        // 销售订单退货数量
                        Optional<List<SaleorderGoods>> saleorderGoods = Optional.ofNullable(saleorderGoodsList);
                        boolean[] isSendStatus = {true};
                        if (saleorderGoods.isPresent() && !saleorderGoods.get().isEmpty()) {
                            saleorderGoods.get().stream().forEach(x -> {
                                // 判断收货的(已发货减去实际退货入库的)
                                // 该产品已发数量 – 该产品退回数量（售后入库数量）
                                Integer temp = x.getDeliveryNum() - x.getWarehouseReturnNum();
                                // 当产品退货后的数量（该产品数量-该产品退货数量）
                                saleorderVo.setCompanyId(saleorder.getCompanyId());
                                saleorderVo.setSaleorderGoodId(x.getSaleorderGoodsId());
                                saleorderVo.setSaleorderNo(sv.getSaleorderNo());
                                Integer goodsAfterReturnNum = getGoodsAfterReturnNum(saleorderVo);
                                Integer temp1 = x.getNum() - goodsAfterReturnNum;

                                if (!temp.equals(temp1)) {
                                    isSendStatus[0] = false;
                                }
                            });
                        }
                        // 如果满足条件就进行更新操作
                        if (isSendStatus[0]) {
                            // 更新收款状态
                            Saleorder updateSaleorderStatus = new Saleorder();
                            // updateSaleorderStatus.setSaleorderNo(saleorder.getSaleorderNo());
                            updateSaleorderStatus.setDeliveryStatus(ErpConst.TWO);
							updateSaleorderStatus.setModTime(System.currentTimeMillis());
                            if (sv.getArrivalStatus().equals(ErpConst.ONE)) {
                                updateSaleorderStatus.setArrivalStatus(ErpConst.TWO);
                            }
                            updateSaleorderStatus.setSaleorderNo(sv.getSaleorderNo());
                            updateSalderStatusDelivery(updateSaleorderStatus);
                        }
                    }
                }
            }
            // 直发的目前不用考虑
            /*
             * else {
             *
             * }
             */
        }

        try{
        	logger.info("售后单完结后更新收发货发票状态开始======》"+ JSON.toJSONString(saleorder));
//			updateState(saleorder);
//			InvoiceState(saleorder.getSaleorderId(),curr_user.getUserId());
//订单流二期改为调用公共计算票货的方法
			orderInfoSyncService.syncPaymentStatusOfSaleOrder(saleorder.getSaleorderId());
			orderInfoSyncService.syncDeliveryAndArrivalDetailOfSaleOrder(saleorder.getSaleorderId());
			orderInfoSyncService.syncInvoiceDetailOfSaleOrder(saleorder.getSaleorderId());
			logger.info("售后单完结后更新收发货发票状态结束======》"+ JSON.toJSONString(saleorder));
		}catch (Exception e){
        	logger.error("售后完结更新收发货开票状态失败",e);
		}



    }


    /**
     * <b>Description:</b><br>
     *
     * @param :a
     * @return :a
     * @Note <b>Author:</b> Bert <br>
     * <b>Date:</b> 2019/2/14 15:11
     */
    @Override
    public String updateSalderStatusDelivery(Saleorder saleorder) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<SaleorderWarehouseComments>> TypeRef = new TypeReference<ResultInfo<SaleorderWarehouseComments>>() {
        };
        // 数据更新的仓库地址
        String url = httpUrl + "order/saleorder/updateSalderStatusDelivery.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                return ErpConst.SEND_DATA_SUCCESS;
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
        return ErpConst.SEND_DATA_FAIL;
    }

	/**
	 * <b>Description:</b><br>
	 *
	 * @param :a
	 * @return :a
	 * @Note <b>Author:</b> Bert <br>
	 *       <b>Date:</b> 2019/2/15 11:21
	 */
	@Override
	public Integer getGoodsAfterReturnNum(SaleorderVo saleorder) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Integer>> TypeRef = new TypeReference<ResultInfo<Integer>>() {
		};
		// 数据更新的仓库地址
		String url = httpUrl + "order/saleorder/seachAfterSaleNum.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
			if (result != null && result.getCode() == 0) {
				return new Integer(result.getData().toString());
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		// 默认返回0
		return 0;
	}

    @Override
    public List<Integer> getSaleOrderIdListByParam(Map<String, Object> paraMap) {

        return saleorderMapper.getSaleOrderIdListByParam(paraMap);
    }

    @Override
    public List<Saleorder> getOrderListInfoById(Map<String, Object> paraMap) {

        return saleorderMapper.getOrderListInfoById(paraMap);
    }

    @Override
    public Map<String, Object> getContractReturnOrderListPage(SaleorderContract saleOrderContract, Page page,
                                                              String searchType) {

        Map<String, Object> paraMap = new HashMap<>();

        paraMap.put("saleOrderContract", saleOrderContract);
        paraMap.put("page", page);

        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<SaleorderContract> list = null;
        Integer listCount = 0;
        if ("1".equals(searchType)) {
            list = saleorderMapper.getContractReturnOrderListPage(paraMap);
            //listCount = saleorderMapper.getContractReturnOrderListCount(paraMap);
        } else {
            list = saleorderMapper.getContractReturnOrderNoqualityListPage(paraMap);
            //listCount = saleorderMapper.getContractReturnOrderNoqualityListCount(paraMap);
        }

        //page.setTotalRecord(listCount);

        resultMap.put("page", page);
        resultMap.put("list", list);

        return resultMap;
    }

    @Override
    public ResultInfo saveOrderRatioEdit(Integer orderId, Integer invoiceType) {
        int i = saleorderMapper.saveOrderRatioEdit(orderId, invoiceType);
        if (i == 1) {
            return new ResultInfo(0, "操作成功");
        } else {
            return new ResultInfo();
        }
    }

    @Override
    public List<Saleorder> getHcOrderList(Saleorder saleorder) {
        return saleorderMapper.getHcOrderList(saleorder);
    }

    @Override
    public Saleorder saveBDAddSaleorder(OrderData orderData) {
        Long time = DateUtil.sysTimeMillis();
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderNo(orderData.getOrderNo()); //订单号
        saleorder.setOrderType(1);  //订单类型
        saleorder.setTakeTraderName(orderData.getUsername());//收货公司
        saleorder.setTakeTraderContactName(orderData.getDeliveryUserName());//收货联系人名称
        saleorder.setTakeTraderArea(orderData.getDeliveryUserArea());//收货地区
        saleorder.setTakeTraderAddress(orderData.getDeliveryUserAddress());//收货地址
        saleorder.setTakeTraderContactMobile(orderData.getDeliveryUserPhone());//收货人手机
        saleorder.setTakeTraderContactTelephone(orderData.getDeliveryUserTel());//收货人电话
        saleorder.setBdtraderComments(orderData.getRemakes());//客户备注
		saleorder.setHaveAccountPeriod(0);//账期支付
        saleorder.setTotalAmount(orderData.getTotalCouponedAmount());//订单总价

		//疫情秒杀活动
		if(orderData.getActivityId()!=null){
			saleorder.setDiscountTypeId(1);
			saleorder.setActionId(orderData.getActivityId());
		}

        saleorder.setPaymentMode(1);//支付方式
        saleorder.setPaymentType(419);//付款方式
        saleorder.setTraderName(orderData.getUsername());//如果没有关联erp客户就显示用户名称为注册时的企业名称
        saleorder.setStatus(orderData.getOrderStatus());//订单状态
        saleorder.setQuoteorderNo(orderData.getOrderNo());//报价单号
        saleorder.setSource(orderData.getOrderSrc());//订单来源BD
        saleorder.setValidStatus(0);//生效状态
        saleorder.setValidTime(0L);//生效时间
        saleorder.setAddTime(time);
        saleorder.setParentId(0);
        saleorder.setCompanyId(1);//南京公司
        saleorder.setPurchaseStatus(0);//采购状态0未采购（默认）
        saleorder.setLockedStatus(0);//锁定状态0未锁定
        saleorder.setInvoiceStatus(0);//开票状态0未开票
        saleorder.setInvoiceTime(0L);//开票时间
        saleorder.setPaymentStatus(0);//付款状态 0未付款
        saleorder.setPaymentTime(0L);//付款时间
        saleorder.setDeliveryStatus(0);//发货状态0未发货
        saleorder.setDeliveryTime(0L);//发货时间
        saleorder.setArrivalTime(0L);//客户收货时间
        saleorder.setServiceStatus(0);//售后状态 0无
        saleorder.setArrivalStatus(0);//客户收货状态0未收货
        saleorder.setHaveAdvancePurchase(0);//有提前采购 0
        saleorder.setAdvancePurchaseStatus(0);//0无提前采购需求
        saleorder.setAdvancePurchaseTime(0L);//提前采购申请时间
        saleorder.setIsUrgent(0);//是否加急0否1是
        BigDecimal a = new BigDecimal(0);
        saleorder.setUrgentAmount(a);//加急费用
        saleorder.setHaveCommunicate(0);//有无沟通记录
        saleorder.setSyncStatus(0);//0未同步
        saleorder.setIsSendInvoice(1);//是否寄送发票
		// add by Tomcat.Hui 2020/2/4 3:35 下午 .Desc: VDERP-1889 线上BD订单默认发票类型修改. start
		//线上订单（BD订单）生成后，默认发票类型改为【13%增值税专用发票（寄送）】
		saleorder.setInvoiceType(972);
		// add by Tomcat.Hui 2020/2/4 3:35 下午 .Desc: VDERP-1889 线上BD订单默认发票类型修改. end
		saleorder.setInvoiceMethod(2); //开票方式1手动纸质开票、2自动纸质开票、3自动电子发票
		saleorder.setIsDelayInvoice(0);//是否延迟开票0否1是
        saleorder.setPrepaidAmount(orderData.getJxSalePrice());//预付金额
        User user = userMapper.getByUsername("jx.vedeng", 1);
        saleorder.setCreator(user.getUserId());
        if (orderData.getDeliveryLevel3Id() != null) {
            saleorder.setTakeTraderAreaId(Integer.valueOf(orderData.getDeliveryLevel3Id()));//地区id
        }
        saleorder.setCreateMobile(orderData.getPhone());
		saleorder.setIsCoupons(orderData.getIsCoupons());
		saleorder.setCouponInfo(orderData.getCouponInfo());

		//VDERP-13616 【ERP】客户下单流程优化 - 报价单 ----------------
		boolean shareOrderFlag = false;
		if(orderData.getQuoteorderId() != null && orderData.getQuoteorderId() > 0){
			saleorder.setQuoteorderId(orderData.getQuoteorderId());
			shareOrderFlag = true;
		}
//		saleorder.setQuoteorderId((orderData.getQuoteorderId()== null || orderData.getQuoteorderId() == 0) ? orderData.getQuoteorderId() : null);
		//VDERP-13616 【ERP】客户下单流程优化 - 报价单 ----------------

		//VDERP-7306, 新增默认为2 产品开票
		//Integer billPeriodSettlementType = orderData.getBillPeriodSettlementType()==null || orderData.getBillPeriodSettlementType() ==0 ?2:orderData.getBillPeriodSettlementType();
		saleorder.setBillPeriodSettlementType(CustomerBillPeriodSettlementTypeEnum.ORDER_INVOICE.getCode());
        // BD订单归属销售
        User belongUser = new User();
        if (orderData.getPhone() != null) {
            belongUser = userService.getBDUserInfoByMobile(orderData.getPhone());
            if (belongUser != null && belongUser.getUserId() != null) {
                saleorder.setUserId(belongUser.getUserId());
            }

            if (belongUser != null && belongUser.getOrgId() != null) {
                saleorder.setOrgId(belongUser.getOrgId());
            }
            if (belongUser != null && belongUser.getOrgName() != null) {
                saleorder.setOrgName(belongUser.getOrgName());
            }
            if (belongUser != null && belongUser.getTraderId() != null && belongUser.getTraderId() != 0) {
				//判断交易者下有无地址
				Integer deliveryLevel1Id = 0;
				if (orderData.getDeliveryLevel1Id() != null && !orderData.getDeliveryLevel1Id().equals("")) {
					deliveryLevel1Id = Integer.valueOf(orderData.getDeliveryLevel1Id());
				}
				Integer deliveryLevel2Id = 0;
				if (orderData.getDeliveryLevel2Id() != null && !orderData.getDeliveryLevel2Id().equals("")) {
					deliveryLevel2Id = Integer.valueOf(orderData.getDeliveryLevel2Id());
				}
				Integer deliveryLevel3Id = 0;
				if (orderData.getDeliveryLevel3Id() != null && !orderData.getDeliveryLevel3Id().equals("")) {
					deliveryLevel3Id = Integer.valueOf(orderData.getDeliveryLevel3Id());
				}
                Integer traderAddressId = vailTraderAddress(belongUser.getTraderId(), deliveryLevel1Id, deliveryLevel2Id, deliveryLevel3Id, orderData.getDeliveryUserAddress());
                saleorder.setTakeTraderAddressId(traderAddressId);
                saleorder.setTakeTraderAreaId(deliveryLevel3Id);
                //判断交易者联系人是否存在并保存
                saleorder.setTraderId(belongUser.getTraderId());
                Integer traderContactId = vailTraderContact(belongUser.getTraderId(), orderData.getDeliveryUserName(), orderData.getDeliveryUserTel(), orderData.getDeliveryUserPhone());
                saleorder.setTakeTraderContactId(traderContactId);
                //判断保存注册用户信息是否在客户联系人中,如果没有则添加
//				vailTraderContact(belongUser.getTraderId(), orderData.getDeliveryUserName(), "", orderData.getPhone());
				//获取交易者信息
                Trader trader = traderMapper.getTraderInfoByTraderIdAndMobile(belongUser.getTraderId(),orderData.getPhone());

                if (trader != null) {
                    saleorder.setTraderName(trader.getTraderName());
                    saleorder.setCustomerType(trader.getCustomerType());
                    saleorder.setCustomerNature(trader.getCustomerNature());
                    if (trader.getCustomerNature().equals(466)) {//终端
                        saleorder.setTerminalTraderType(trader.getCustomerType());
                        saleorder.setTerminalTraderName(trader.getTraderName());
                        saleorder.setTerminalTraderId(trader.getTraderId());
                        //获取交易者经营区域
//						String areaIds = traderMapper.getTraderBussinessAreaByTraderId(belongUser.getTraderId());
                        String areaIds = trader.getAreaIds();
                        if (org.apache.commons.lang.StringUtils.isNotBlank(areaIds)) {
                            //查询地区
                            String traderBussinessArea = regionService.getRegionNameStringByMinRegionIds(areaIds);
                            String[] areaid = areaIds.split(",");
                            Integer areaid2 = Integer.valueOf(areaid[areaid.length - 1]);
                            saleorder.setSalesAreaId(areaid2);
                            saleorder.setSalesArea(traderBussinessArea);
                        }
                    }
                    //Integer traderContactId = vailTraderContact(trader.getTraderId(),orderData.getDeliveryUserName(),orderData.getDeliveryUserPhone(),orderData.getDeliveryUserTel());
                    saleorder.setTraderContactId(trader.getTraderContactId());
                    if (trader.getTraderContactName() != null && !trader.getTraderContactName().isEmpty()) {
                        saleorder.setTraderContactName(trader.getTraderContactName());
                    }
                    if (trader.getTraderContactMobile() != null && !trader.getTraderContactMobile().isEmpty()) {
                        saleorder.setTraderContactMobile(trader.getTraderContactMobile());
                    }
                    saleorder.setTraderContactTelephone(trader.getTraderContactTelephone());
                    saleorder.setTraderAreaId(trader.getAreaId());
                    saleorder.setTraderAddressId(trader.getTraderAddressId());
                    saleorder.setTraderAddress(trader.getTraderAddress());
                    saleorder.setCustomerLevelStr(trader.getCustomerLevelStr());
                    //查询地区
                    String area = regionService.getRegionNameStringByMinRegionIds(trader.getAreaIds());
                    saleorder.setTraderArea(area);
                }
            }
        }
        //保存订单商品
        List<OrderGoodsData> goodsList = orderData.getGoodsList();
        List<SaleorderGoods> salegoodsList = new ArrayList<SaleorderGoods>();
        Boolean shunFengFlag = false;
        Boolean deliveryFlag = false;
        Boolean paymentFlag = false;

        for (OrderGoodsData orderGoodsData : goodsList) {
            SaleorderGoods saleorderGoods = new SaleorderGoods();
            saleorderGoods.setSku(orderGoodsData.getSkuNo());
            if (belongUser != null && belongUser.getUserId() != null) {
                saleorderGoods.setCreator(belongUser.getUserId());
                saleorderGoods.setUpdater(belongUser.getUserId());
            }
            saleorderGoods.setAddTime(DateUtil.sysTimeMillis());
            saleorderGoods.setNum(orderGoodsData.getProductNum());
            saleorderGoods.setModTime(DateUtil.sysTimeMillis());
            saleorderGoods.setGoodsId(goodsService.getGoodsIdBySku(orderGoodsData.getSkuNo()));
			//
			Goods goods = goodsService.getSkuInfoBySku(orderGoodsData.getSkuNo());
			if(goods !=null){
				saleorderGoods.setBrandId(goods.getBrandId());
			}

            saleorderGoods.setDeliveryDirect(0);


			// 现货现价直购
			if (ErpConstant.ONE.equals(orderGoodsData.getIsDirectPurchase())){
				saleorderGoods.setPrice(orderGoodsData.getDirectPurchasePrice());
				saleorderGoods.setIsDirectPurchase(ErpConstant.ONE);
			}

            saleorderGoods.setPrice(orderGoodsData.getIsCoupons() == 1 ?
									orderGoodsData.getSkuAmount().divide(BigDecimal.valueOf(orderGoodsData.getProductNum()),2, BigDecimal.ROUND_HALF_UP) :
									orderGoodsData.getJxSalePrice());

			//疫情秒杀活动
			if(Objects.equals(orderGoodsData.getLimitFlag(), "Y")){
				saleorderGoods.setPrice(orderGoodsData.getLimitPrice());
				saleorderGoods.setIsActionGoods(1);
			}

			// 赠品标识
			if(Objects.equals(orderGoodsData.getGiftFlag(), "Y")){
				saleorderGoods.setIsGift(ErpConstant.ONE);
			}


			// 是否含安调
			saleorderGoods.setHaveInstallation(orderGoodsData.getHaveInstallation());

            saleorderGoods.setJxSalePrice(orderGoodsData.getJxSalePrice());

            //添加优惠券相关数据
			saleorderGoods.setIsCoupons(orderGoodsData.getIsCoupons());
			saleorderGoods.setMaxSkuRefundAmount(orderGoodsData.getSkuAmount());
			saleorderGoods.setRealPrice(orderGoodsData.getJxSalePrice());

            //使用优惠券
            if(orderGoodsData.getIsCoupons() == 1){

                BigDecimal originalTotalAmout = saleorderGoods.getRealPrice().multiply(BigDecimal.valueOf(saleorderGoods.getNum()));

                String goodsComment = "已使用满减券，原价"+originalTotalAmout+ "，" +
                        "优惠了"+(originalTotalAmout.subtract(saleorderGoods.getMaxSkuRefundAmount()))+"元；"
                        +"有效期截止到" + DateUtil.convertString(Long.valueOf(orderData.getCouponInfo().getEffevtiveEndTime()),"yyyy-MM-dd HH:mm:ss");

				logger.info("不再进行该提示的保存：{}",goodsComment);
//                saleorderGoods.setGoodsComments(goodsComment);
            }

            //增加服务标签标签和货期承诺天数
			if(!MapUtils.isEmpty(orderData.getLogisticDaysMap())){
				saleorderGoods.setPerfermenceDeliveryTime(orderData.getLogisticDaysMap().get(orderGoodsData.getSkuNo()));
			}else{
				saleorderGoods.setPerfermenceDeliveryTime(-1);
			}


			if(CollectionUtils.isNotEmpty(orderGoodsData.getTagList())){
				String labelCodes = orderGoodsData.getTagList().stream().map(SkuServiceTag::getTagCode).collect(Collectors.joining(";"));
				String labelNames = orderGoodsData.getTagList().stream().map(SkuServiceTag::getTagName).collect(Collectors.joining(","));
				saleorderGoods.setLabelCodes(labelCodes);
				saleorderGoods.setLabelNames(labelNames);
                Optional<SkuServiceTag> shunFunLabel = orderGoodsData.getTagList().stream().filter(e -> "支持顺丰".equals(e.getTagName())).findAny();
                if(shunFunLabel.isPresent()){
                    shunFengFlag = true;
                }
                Optional<SkuServiceTag> deliveryLabel = orderGoodsData.getTagList().stream().filter(e -> "送货上门".equals(e.getTagName())).findAny();
                if(deliveryLabel.isPresent()){
                    deliveryFlag = true;
                }
                Optional<SkuServiceTag> paymentLabel = orderGoodsData.getTagList().stream().filter(e -> "逾期赔付".equals(e.getTagName())).findAny();
                if(paymentLabel.isPresent()){
                    paymentFlag = true;
                }
            }

            salegoodsList.add(saleorderGoods);
        }
        if(shunFengFlag || deliveryFlag ||paymentFlag ) {
            StringBuffer labelComments = new StringBuffer();
            labelComments.append("BD");
            if(shunFengFlag){
                labelComments.append("支持顺丰、");
            }
            if(deliveryFlag){
                labelComments.append("送货上门、");
            }
            if(paymentFlag){
                labelComments.append("逾期赔付、");
            }
            String logisticsComments = labelComments.substring(0,labelComments.length()-1);
			logisticsComments = logisticsComments + "（系统生成，请勿修改）";
//            saleorder.setLogisticsComments(logisticsComments);
        }
        saleorder.setGoodsList(salegoodsList);

        //VDERP-6918支持前台切换BD订单收票模块信息数据源start
		Integer invoiceTraderContactId = vailTraderContact(belongUser.getTraderId(), orderData.getInvoiceTraderContactName(), orderData.getInvoiceTraderContactTelephone(), orderData.getInvoiceTraderContactMobile());
		saleorder.setInvoiceTraderContactId(invoiceTraderContactId);
		//收票联系人地址
		saleorder.setInvoiceTraderContactName(orderData.getInvoiceTraderContactName());
		//收票联系人电话
		saleorder.setInvoiceTraderContactMobile(orderData.getInvoiceTraderContactMobile());
		//收票联系人手机
		saleorder.setInvoiceTraderContactTelephone(orderData.getInvoiceTraderContactTelephone());
		//收票地址
		saleorder.setInvoiceTraderAddress(orderData.getInvoiceTraderAddress());
		//收票区域
		saleorder.setInvoiceTraderArea(orderData.getInvoiceTraderArea());
		//是否寄送发票
		saleorder.setIsSendInvoice(orderData.getIsSendInvoice());
		// 兼容历史数据
		saleorder.setInvoiceMethod(orderData.getInvoiceMethod());
		if(orderData.getIsSendInvoice() == 0) {
			//不寄送发票，默认为电子开票
			saleorder.setInvoiceTraderAddressId(0);
		}else if(orderData.getIsSendInvoice() == 1){
			//寄送发票默认为 数电发票
			Integer invoiceTraderDeliveryLevel1Id = 0;
			if (orderData.getInvoiceTraderDeliveryLevel1Id() != null && !orderData.getInvoiceTraderDeliveryLevel1Id().equals("")) {
				invoiceTraderDeliveryLevel1Id = Integer.valueOf(orderData.getInvoiceTraderDeliveryLevel1Id());
			}
			Integer invoiceTraderDeliveryLevel2Id = 0;
			if (orderData.getInvoiceTraderDeliveryLevel2Id() != null && !orderData.getInvoiceTraderDeliveryLevel2Id().equals("")) {
				invoiceTraderDeliveryLevel2Id = Integer.valueOf(orderData.getInvoiceTraderDeliveryLevel2Id());
			}
			Integer invoiceTraderDeliveryLevel3Id = 0;
			if (orderData.getInvoiceTraderDeliveryLevel3Id() != null && !orderData.getInvoiceTraderDeliveryLevel3Id().equals("")) {
				invoiceTraderDeliveryLevel3Id = Integer.valueOf(orderData.getInvoiceTraderDeliveryLevel3Id());
			}
			Integer invoiceAddressId = vailTraderAddress(belongUser.getTraderId(),
					invoiceTraderDeliveryLevel1Id, invoiceTraderDeliveryLevel2Id, invoiceTraderDeliveryLevel3Id, orderData.getInvoiceTraderAddress());
			saleorder.setInvoiceTraderAddressId(invoiceAddressId);
		}
		//开票类型
		saleorder.setInvoiceType(orderData.getInvoiceType());

		//新订单流标记状态
		saleorder.setIsNew(orderInfoSyncService.setSaleorderIsNewByOrg(saleorder.getSaleorderNo(), saleorder.getTraderId()));
		logger.info("请求DB保存BD订单信息 saleorder:{}", JSON.toJSONString(saleorder));

		//设置订单确认时间 确认状态
		saleorder.setConfirmTime(new Date());
		saleorder.setConfirmStatus(ErpConst.STATUS_STATE.SOME);
		//VDERP-6918支持前台切换BD订单收票模块信息数据源end

		//前台订单 设置推送状态为1
		saleorder.setSendToPc(1);

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(
                    httpUrl + "order/saleorder/saveBDAddSaleorder.htm", saleorder, clientId, clientKey, TypeRef2);
            Saleorder res = (Saleorder) result2.getData();
            if(res != null){
				//VDERP-2263   订单售后采购改动通知
				orderCommonService.updateSaleOrderDataUpdateTime(res.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_GENERATE);
			}
            if (belongUser != null && belongUser.getUserId() != null && belongUser.getUserId() != 0) {
                //判读端erp客户是否禁用
                quoteService.getIsDisabled(res.getCreateMobile(), belongUser.getUserId(), res.getSaleorderNo(), res.getSaleorderId());
            } else {
                //发送消息推送
                quoteService.sendAllocation(orderData.getPhone(), null);
            }


			//VDERP-10864 【ERP】【贝登】线上订单的自动审核功能 start
			//设置默认为不打印出库单
			Saleorder saleorderUpt = new Saleorder();
			saleorderUpt.setSaleorderId(res.getSaleorderId());
			saleorderUpt.setIsPrintout(ErpConst.ZERO);
			if(saleorder.getSalesArea() == null || "".equals(saleorder.getSalesArea())
					|| saleorder.getSalesAreaId() == null || ErpConst.ZERO.equals(saleorder.getSalesAreaId())) {
				saleorderUpt.setSalesArea(saleorder.getTakeTraderArea());
				RegionVo region = regionService.getRegionByArea(saleorder.getTakeTraderArea());
				logger.info("前台推送订单收货地址{},查询到的区域信息{},",saleorder.getTakeTraderArea(),JSON.toJSONString(region));
				if (region != null) {
					saleorderUpt.setSalesAreaId(region.getZoneId() == null?(region.getCity() == null ? region.getProvince() : region.getCity()):region.getZoneId());
				} else {
					saleorderUpt.setSalesAreaId(ErpConst.ZERO);
				}
			}
			ResultInfo resultInfo = verifyBdIsAutoAudit(res);
			logger.info("校验是否自动审核返回信息{}，",JSON.toJSONString(resultInfo));
			if(ErpConst.ZERO.equals(resultInfo.getCode())) {
				//校验风控信息
				saleorderUpt.setTraderId(res.getTraderId());
				SaleorderRiskModelVo saleorderRiskModelVo = riskCheckService.getUrlRiskAndCheckSaleorder(saleorderUpt);
				if (saleorderRiskModelVo.getIsRisk() && !shareOrderFlag && !ErpConstant.ONE.equals(orderData.getIsRemoteOrder())) {
					bdOrderAutoAuditService.bdOrderAutoAudit(saleorderUpt);
				}
				saleorderUpt.setAutoAudit(ErpConst.ONE);
				if (shareOrderFlag || ErpConstant.ONE.equals(orderData.getIsRemoteOrder())) {
					saleorderUpt.setAutoAudit(ErpConst.ZERO);
				}
				if(RiskHandlerKeyConstant.NO_RISK.equals(saleorderUpt.getIsRisk())){
					saleorderUpt.setIsRisk(ErpConst.ZERO);
				}
				logger.info("更新销售单信息{},",JSON.toJSONString(saleorderUpt));
				saleorderMapper.updateByPrimaryKeySelective(saleorderUpt);
			}

			if(orderData.getAwardAmount() != null && orderData.getAwardAmount().compareTo(BigDecimal.ZERO)>0){
				SaleOrderPriceDto saleOrderPriceDto = new SaleOrderPriceDto();
				saleOrderPriceDto.setSaleOrderId(res.getSaleorderId());
				saleOrderPriceDto.setAlterType(1);
				saleOrderPriceDto.setAlterPrice(orderData.getAwardAmount());
				saleorderMapper.saveSaleOrderPrice(saleOrderPriceDto);
			}

			String LogisticsComments = "";
			//VDERP-10864 【ERP】【贝登】线上订单的自动审核功能 end
			// 现货现价直购逻辑
			if (StrUtil.isNotBlank(orderData.getDesignatedLogistics()) || Objects.nonNull(orderData.getDelayDelivery()) ||
					Objects.nonNull(orderData.getPrivacyDelivery()) || Objects.nonNull(orderData.getIsRemoteOrder())) {
				logger.info("现货现价直购订单,订单号:{},指定物流:{},暂缓发货:{},隐私发货:{},是否偏远订单:{}", res.getSaleorderNo(), orderData.getDesignatedLogistics(), orderData.getDelayDelivery(), orderData.getPrivacyDelivery(), orderData.getIsRemoteOrder());
				Saleorder directPurchaseUpdate = new Saleorder();
				directPurchaseUpdate.setSaleorderId(res.getSaleorderId());
				directPurchaseUpdate.setLogisticsComments(orderData.getDesignatedLogistics() + " " + (ErpConstant.THREE.equals(orderData.getPrivacyDelivery()) ? "打印不带贝登LOGO出库单" : ""));
				directPurchaseUpdate.setDeliveryClaim(orderData.getDelayDelivery());
				if (ErpConstant.ONE.equals(orderData.getDelayDelivery())) {
					DateTime ninetyDaysLater = cn.hutool.core.date.DateUtil.offsetDay(cn.hutool.core.date.DateUtil.date(), 90);
					directPurchaseUpdate.setDeliveryDelayTime(ninetyDaysLater.getTime());
				}
				directPurchaseUpdate.setIsPrintout(ErpConstant.THREE.equals(orderData.getPrivacyDelivery()) ? ErpConstant.TWO : orderData.getPrivacyDelivery());
				if (ErpConstant.ONE.equals(orderData.getIsRemoteOrder())) {
					directPurchaseUpdate.setAutoAudit(ErpConstant.ZERO);
				}
				LogisticsComments = directPurchaseUpdate.getLogisticsComments();
				saleorderMapper.updateByPrimaryKeySelective(directPurchaseUpdate);

				if (ErpConstant.ONE.equals(orderData.getIsRemoteOrder())) {
					logger.info("偏远单给客户归属销售发送ERP站内消息:{}", res.getSaleorderNo());
					Map<String, String> map = new HashMap<>();
					map.put("saleorderNo", res.getSaleorderNo());
					String url = "./orderstream/saleorder/detail.do?saleOrderId=" + res.getSaleorderId();
					User imputedSale = userService.getUserByTraderId(res.getTraderId(), ErpConstant.ONE);
					if (Objects.isNull(imputedSale)) {
						logger.info("偏远单给客户归属销售发送ERP站内消息,未找到归属销售,traderId:{}", res.getTraderId());
					} else {
						MessageUtil.sendMessage(320, Collections.singletonList(imputedSale.getUserId()), map, url);
					}
				}
			}

			if (Objects.nonNull(orderData.getTestReportMethod()) && orderData.getTestReportMethod() > 0){
				Saleorder updateCommon = new Saleorder();
				updateCommon.setSaleorderId(res.getSaleorderId());
				updateCommon.setLogisticsComments(LogisticsComments + (StrUtil.isNotBlank(LogisticsComments) ? "；" : "") +getTestReportComment(orderData.getTestReportMethod()));
				log.info("前台客户检测报告选项：更新物流备注：{}",JSON.toJSONString(updateCommon));
				saleorderMapper.updateByPrimaryKeySelective(updateCommon);
			}
            return res;
        } catch (IOException e) {
            logger.error("saveBDAddSaleorder", e);
            return null;
        }
    }


    @Override
	public Saleorder saveBDAddSaleorderForSync(OrderData orderData) throws Exception {
    	Saleorder s = new Saleorder();
		s.setSaleorderNo(orderData.getOrderNo());
		Saleorder saleorderExists = getSaleorderBySaleorderNo(s);
		if(saleorderExists!=null) {
			logger.info("订单号已存在！");
			return null;
		}
        Long time = DateUtil.sysTimeMillis();
        Saleorder saleorder = new Saleorder();
        saleorder.setOrderType(1);  //订单类型
        saleorder.setTakeTraderName(orderData.getUsername());//收货公司
        saleorder.setTakeTraderContactName(orderData.getDeliveryUserName());//收货联系人名称
        saleorder.setTakeTraderArea(orderData.getDeliveryUserArea());//收货地区
        saleorder.setTakeTraderAddress(orderData.getDeliveryUserAddress());//收货地址
        saleorder.setTakeTraderContactMobile(orderData.getDeliveryUserPhone());//收货人手机
        saleorder.setTakeTraderContactTelephone(orderData.getDeliveryUserTel());//收货人电话
        saleorder.setBdtraderComments(orderData.getRemakes());//客户备注
		saleorder.setHaveAccountPeriod(0);//账期支付
        saleorder.setTotalAmount(orderData.getTotalCouponedAmount());//订单总价
        saleorder.setIsSendInvoice(orderData.getIsSendInvoice());//是否寄送发票
        saleorder.setInvoiceType(orderData.getInvoiceType());//线上订单（BD订单）生成后，默认发票类型改为【13%增值税专用发票（寄送）】
		String createName = "njadmin";
        saleorder.setPaymentMode(0);//支付方式
        saleorder.setInvoiceMethod(orderData.getInvoiceMethod()); //开票方式1手动纸质开票、2自动纸质开票、3自动电子发票
		saleorder.setIsDelayInvoice(0);//是否延迟开票0否1是
		saleorder.setPaymentType(419);//付款方式 先货后款，预付0% -- 11. 功能调整：通过采购单生成的对方销售订单的付款计划调整为"先款后货，预付100%"
		saleorder.setTraderName(orderData.getUsername());//如果没有关联erp客户就显示用户名称为注册时的企业名称
        saleorder.setStatus(0);//订单状态
        saleorder.setQuoteorderNo(orderData.getOrderNo());//报价单号
        saleorder.setValidStatus(0);//生效状态
        saleorder.setValidTime(0L);//生效时间
        saleorder.setAddTime(time);
        saleorder.setParentId(0);
        saleorder.setCompanyId(1);//南京公司
        saleorder.setPurchaseStatus(0);//采购状态0未采购（默认）
        saleorder.setLockedStatus(0);//锁定状态0未锁定
        saleorder.setInvoiceStatus(0);//开票状态0未开票
        saleorder.setInvoiceTime(0L);//开票时间
        saleorder.setPaymentStatus(0);//付款状态 0未付款
        saleorder.setPaymentTime(0L);//付款时间
        saleorder.setDeliveryStatus(0);//发货状态0未发货
        saleorder.setDeliveryTime(0L);//发货时间
        saleorder.setArrivalTime(0L);//客户收货时间
        saleorder.setServiceStatus(0);//售后状态 0无
        saleorder.setArrivalStatus(0);//客户收货状态0未收货
        saleorder.setHaveAdvancePurchase(0);//有提前采购 0
        saleorder.setAdvancePurchaseStatus(0);//0无提前采购需求
        saleorder.setAdvancePurchaseTime(0L);//提前采购申请时间
        saleorder.setIsUrgent(0);//是否加急0否1是
        BigDecimal a = new BigDecimal(0);
        saleorder.setUrgentAmount(a);//加急费用
        saleorder.setHaveCommunicate(0);//有无沟通记录
        saleorder.setSyncStatus(0);//0未同步
        saleorder.setPrepaidAmount(orderData.getTotalCouponedAmount());//预付金额
        User user = userMapper.getByUsername(createName, 1);
        saleorder.setCreator(user.getUserId());
        if (orderData.getDeliveryLevel3Id() != null) {
            saleorder.setTakeTraderAreaId(Integer.valueOf(orderData.getDeliveryLevel3Id()));//地区id
        }
        saleorder.setCreateMobile(orderData.getPhone());
		saleorder.setIsCoupons(orderData.getIsCoupons());
		saleorder.setCouponInfo(orderData.getCouponInfo());
		saleorder.setDeliveryType(orderData.getDeliveryType());
		saleorder.setDeliveryClaim(orderData.getDelayDelivery());
		saleorder.setIsPrintout(orderData.getIsPrintout());
		saleorder.setLogisticsComments(orderData.getLogisticsComments());
		saleorder.setAdditionalClause(orderData.getAdditionalClause());
		saleorder.setComments(orderData.getComments());
		saleorder.setBillPeriodSettlementType(CustomerBillPeriodSettlementTypeEnum.ORDER_INVOICE.getCode());
        //BD订单归属销售
        User belongUser = new User();
        if (orderData.getTraderId() != null) {
            belongUser = userService.getBDUserInfoByTraderId(orderData.getTraderId());
            if (belongUser != null && belongUser.getUserId() != null) {
                saleorder.setUserId(belongUser.getUserId());
            }
            if (belongUser != null && belongUser.getOrgId() != null) {
                saleorder.setOrgId(belongUser.getOrgId());
            }
            if (belongUser != null && belongUser.getOrgName() != null) {
                saleorder.setOrgName(belongUser.getOrgName());
            }
            if (belongUser != null && belongUser.getTraderId() != null && belongUser.getTraderId() != 0) {
				//判断交易者下有无地址
				Integer deliveryLevel1Id = 0;
				if (orderData.getDeliveryLevel1Id() != null && !orderData.getDeliveryLevel1Id().equals("")) {
					deliveryLevel1Id = Integer.valueOf(orderData.getDeliveryLevel1Id());
				}
				Integer deliveryLevel2Id = 0;
				if (orderData.getDeliveryLevel2Id() != null && !orderData.getDeliveryLevel2Id().equals("")) {
					deliveryLevel2Id = Integer.valueOf(orderData.getDeliveryLevel2Id());
				}
				Integer deliveryLevel3Id = 0;
				if (orderData.getDeliveryLevel3Id() != null && !orderData.getDeliveryLevel3Id().equals("")) {
					deliveryLevel3Id = Integer.valueOf(orderData.getDeliveryLevel3Id());
				}
                Integer traderAddressId = vailTraderAddress(belongUser.getTraderId(), deliveryLevel1Id, deliveryLevel2Id, deliveryLevel3Id, orderData.getDeliveryUserAddress());
                saleorder.setTakeTraderAddressId(traderAddressId);
                saleorder.setTakeTraderAreaId(deliveryLevel3Id);
                //判断交易者联系人是否存在并保存
                saleorder.setTraderId(belongUser.getTraderId());
                Integer traderContactId = vailTraderContact(belongUser.getTraderId(), orderData.getDeliveryUserName(), orderData.getDeliveryUserTel(), orderData.getDeliveryUserPhone());
                saleorder.setTakeTraderContactId(traderContactId);
                //判断保存注册用户信息是否在客户联系人中,如果没有则添加
				Trader trader = traderMapper.getTraderInfoByDeal(belongUser.getTraderId(),orderData.getPhone(),orderData.getDeliveryUserAddress(),orderData.getDeliveryUserName());
                if (trader != null) {
                    saleorder.setTraderName(trader.getTraderName());
                    saleorder.setCustomerType(trader.getCustomerType());
                    saleorder.setCustomerNature(trader.getCustomerNature());
                    if (trader.getCustomerNature().equals(466)) {//终端
                        saleorder.setTerminalTraderType(trader.getCustomerType());
                        saleorder.setTerminalTraderName(trader.getTraderName());
                        saleorder.setTerminalTraderId(trader.getTraderId());
                        //获取交易者经营区域
                        String areaIds = trader.getAreaIds();
                        if (org.apache.commons.lang.StringUtils.isNotBlank(areaIds)) {
                            //查询地区
                            String traderBussinessArea = regionService.getRegionNameStringByMinRegionIds(areaIds);
                            String[] areaid = areaIds.split(",");
                            Integer areaid2 = Integer.valueOf(areaid[areaid.length - 1]);
                            saleorder.setSalesAreaId(areaid2);
                            saleorder.setSalesArea(traderBussinessArea);
                        }
                    }
                    saleorder.setTraderContactId(trader.getTraderContactId());
                    if (trader.getTraderContactName() != null && !trader.getTraderContactName().isEmpty()) {
                        saleorder.setTraderContactName(trader.getTraderContactName());
                    }
                    if (trader.getTraderContactMobile() != null && !trader.getTraderContactMobile().isEmpty()) {
                        saleorder.setTraderContactMobile(trader.getTraderContactMobile());
                    }
                    saleorder.setTraderContactTelephone(trader.getTraderContactTelephone());
                    saleorder.setTraderAreaId(trader.getAreaId());
                    saleorder.setTraderAddressId(trader.getTraderAddressId());
                    saleorder.setTraderAddress(trader.getTraderAddress());
                    saleorder.setCustomerLevelStr(trader.getCustomerLevelStr());
                    //查询地区
                    String area = regionService.getRegionNameStringByMinRegionIds(trader.getAreaIds());
                    saleorder.setTraderArea(area);
                }
            }
        }
        //保存订单商品
        List<OrderGoodsData> goodsList = orderData.getGoodsList();
        List<SaleorderGoods> salegoodsList = new ArrayList<SaleorderGoods>();
        for (OrderGoodsData orderGoodsData : goodsList) {
            SaleorderGoods saleorderGoods = new SaleorderGoods();
            saleorderGoods.setSku(orderGoodsData.getSkuNo());
            if (belongUser != null && belongUser.getUserId() != null) {
                saleorderGoods.setCreator(belongUser.getUserId());
                saleorderGoods.setUpdater(belongUser.getUserId());
            }
            saleorderGoods.setAddTime(DateUtil.sysTimeMillis());
            saleorderGoods.setNum(orderGoodsData.getProductNum());
            saleorderGoods.setModTime(DateUtil.sysTimeMillis());
            saleorderGoods.setGoodsId(goodsService.getGoodsIdBySku(orderGoodsData.getSkuNo()));
			saleorderGoods.setDeliveryDirect(1);
			saleorderGoods.setDeliveryDirectComments(orderGoodsData.getDelilveryDirectComments());
			saleorderGoods.setDeliveryCycle(orderGoodsData.getDeliveryCycle());
			saleorderGoods.setGoodsComments(orderGoodsData.getGoodsComments());
			saleorderGoods.setInsideComments(orderGoodsData.getInsideComments());
            saleorderGoods.setPrice(orderGoodsData.getJxSalePrice());
			// 是否含安调
			saleorderGoods.setHaveInstallation(orderGoodsData.getHaveInstallation());
            saleorderGoods.setJxSalePrice(orderGoodsData.getJxSalePrice());
            //添加优惠券相关数据
			saleorderGoods.setIsCoupons(orderGoodsData.getIsCoupons());
			saleorderGoods.setMaxSkuRefundAmount(orderGoodsData.getSkuAmount());
			saleorderGoods.setRealPrice(orderGoodsData.getJxSalePrice());
            salegoodsList.add(saleorderGoods);
        }
        saleorder.setGoodsList(salegoodsList);
        //VDERP-6918支持前台切换BD订单收票模块信息数据源start
		Integer invoiceTraderContactId = vailTraderContact(belongUser.getTraderId(), orderData.getInvoiceTraderContactName(), orderData.getInvoiceTraderContactTelephone(), orderData.getInvoiceTraderContactMobile());
		saleorder.setInvoiceTraderContactId(invoiceTraderContactId);
		//收票联系人地址
		saleorder.setInvoiceTraderContactName(orderData.getInvoiceTraderContactName());
		//收票联系人电话
		saleorder.setInvoiceTraderContactMobile(orderData.getInvoiceTraderContactMobile());
		//收票联系人手机
		saleorder.setInvoiceTraderContactTelephone(orderData.getInvoiceTraderContactTelephone());
		//收票地址
		saleorder.setInvoiceTraderAddress(orderData.getInvoiceTraderAddress());
		//收票区域
		saleorder.setInvoiceTraderArea(orderData.getInvoiceTraderArea());
		//是否寄送发票
		saleorder.setIsSendInvoice(orderData.getIsSendInvoice());
		// 兼容历史数据
		saleorder.setInvoiceMethod(orderData.getInvoiceMethod());
		//开票类型
		saleorder.setInvoiceType(orderData.getInvoiceType());
		//新订单流标记状态
		saleorder.setIsNew(orderInfoSyncService.setSaleorderIsNewByOrg(saleorder.getSaleorderNo(), saleorder.getTraderId()));
		logger.info("请求DB保存BD订单信息 saleorder:{}", JSON.toJSONString(saleorder));
		//前台订单 设置推送状态为1
		saleorder.setSendToPc(1);
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {};
        ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + "order/saleorder/saveBDAddSaleorder.htm", saleorder, clientId, clientKey, TypeRef2);
        Saleorder res = (Saleorder) result2.getData();
        if(Objects.isNull(res)) {
        	throw new Exception("dbCenter保存销售单信息失败！");
        }
        try {
        	if(res != null){
    			//VDERP-2263   订单售后采购改动通知
    			orderCommonService.updateSaleOrderDataUpdateTime(res.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_GENERATE);
    		}
            if (belongUser != null && belongUser.getUserId() != null && belongUser.getUserId() != 0) {
                //判读端erp客户是否禁用
                quoteService.getIsDisabled(res.getCreateMobile(), belongUser.getUserId(), res.getSaleorderNo(), res.getSaleorderId());
            } else {
                //发送消息推送
                quoteService.sendAllocation(orderData.getPhone(), null);
            }
    		Saleorder saleorderUpt = new Saleorder();
    		saleorderUpt.setSaleorderId(res.getSaleorderId());
    		saleorderUpt.setTerminalTraderName("");
    		saleorderUpt.setSalesArea("");
    		saleorderUpt.setSalesAreaId(ErpConst.ZERO);
    		saleorderUpt.setOriginBuyOrderNo(orderData.getOriginBuyOrderNo());
    		saleorderUpt.setPaymentType(saleorder.getPaymentType());
    		saleorderUpt.setIsPrintout(2);
    		saleorderUpt.setSaleorderNo(orderNoDict.getOrderNum(res.getSaleorderId(), 3));
//    		saleorderUpt.setPeriodDay(0);
//    		saleorderUpt.setPrepaidAmount(BigDecimal.ZERO);
//    		saleorderUpt.setAccountPeriodAmount(orderData.getTotalCouponedAmount());
    		saleorderMapper.updateByPrimaryKeySelective(saleorderUpt);
    		//更新商品明细普发直发属性
    		List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(res.getSaleorderId());
//    		if(CollectionUtils.isNotEmpty(saleorderGoodsList)){
//    			for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
//    					SaleorderGoods saleorderGoodsUpdate = new SaleorderGoods();
//    					Integer saleorderGoodsId = saleorderGoods.getSaleorderGoodsId();
//    					saleorderGoodsUpdate.setSaleorderGoodsId(saleorderGoodsId);
//    					saleorderGoodsUpdate.setDeliveryDirect(0);
//    					saleorderGoodsMapper.updateByPrimaryKeySelective(saleorderGoodsUpdate);
//    				}
//    		}
			//需要返回销售订单号
			res.setSaleorderNo(saleorderUpt.getSaleorderNo());
    		//同步订单，更新终端和订单信息
    		OrderTerminalDto orderTerminalDto = new OrderTerminalDto();
    		orderTerminalDto.setBusinessId(res.getSaleorderId());
    		orderTerminalDto.setBusinessNo(saleorderUpt.getSaleorderNo());
    		orderTerminalDto.setAddTime(new Date());
    		orderTerminalDto.setCreator(1);
    		orderTerminalDto.setCreatorName("admin");
    		orderTerminalDto.setHosModel("0");
    		orderTerminalDto.setTerminalTraderNature(5605);
    		orderTerminalDto.setNatureTypeName("非公集团");
    		orderTerminalApiService.save(orderTerminalDto);
        }catch(Exception e) {
        	//异常捕获，不进行回滚，因为dbcenter已经存储过，回滚也无济于事，按照成功进行处理，修复bug按照医购优选定时任务ThirdRequestRetryTask要求进行执行
        	logger.error("更新订单信息不全，需要修复bug按照定时任务要求进行重新执行,订单号：{}",saleorder.getSaleorderNo());
        }
        return res;
	}


	private String getTestReportComment(Integer testReportMethod){
		if (Objects.equals(testReportMethod, 1)){
			return "不需要检测报告";
		}
		if (Objects.equals(testReportMethod, 2)){
			return  "需要不含贝登章检测报告";
		}
		if (Objects.equals(testReportMethod, 3)){
			return  "需要含贝登章检测报告";
		}
		return "";
	}

	@Override
	public Saleorder saveJDAddSaleorder(OrderData orderData,User belongUser) throws Exception {
		Long time = DateUtil.sysTimeMillis();
		Saleorder saleorder = new Saleorder();
//		User belongUser = new User();
//		belongUser = userService.getBDUserInfoByMobile(orderData.getPhone());
		//默认赋值
		try {
			getsalderByConsatant(time, saleorder);
		} catch (Exception e) {
			logger.error("saveBDAddSaleorder", e);
			throw new Exception("默认赋值失败");
		}


		try {
			//根据京东单子赋值
			getsaleOrderByorderDate(orderData, saleorder,belongUser);
		} catch (Exception e) {
			logger.error("saveBDAddSaleorder", e);
			throw new Exception("根据京东表格赋值失败");
		}


		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {
		};
		try {
			//保存订单
			Saleorder res = startSaveSaleorder(orderData, saleorder, belongUser, TypeRef2);

			//VDERP-10864 【ERP】【贝登】线上订单的自动审核功能 start
			examine(saleorder, res);
			//VDERP-10864 【ERP】【贝登】线上订单的自动审核功能 end
			return res;
		} catch (Exception e) {
			logger.error("saveBDAddSaleorder", e);
			throw new Exception("保存订单失败" + e.getMessage());
		}
	}

	private Saleorder startSaveSaleorder(OrderData orderData, Saleorder saleorder, User belongUser, TypeReference<ResultInfo<Saleorder>> TypeRef2) throws Exception {
		ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(
				httpUrl + "order/saleorder/saveBDAddSaleorder.htm", saleorder, clientId, clientKey, TypeRef2);
		if (result2.getCode().equals(1)){
			logger.error("订单保存失败");
			throw new Exception("订单保存失败");
		}
		Saleorder res = (Saleorder) result2.getData();

		if(res != null){
			//VDERP-2263   订单售后采购改动通知
			orderCommonService.updateSaleOrderDataUpdateTime(res.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_GENERATE);
		}
		if (belongUser != null && belongUser.getUserId() != null && belongUser.getUserId() != 0) {
			//判读端erp客户是否禁用
			quoteService.getIsDisabled(res.getCreateMobile(), belongUser.getUserId(), res.getSaleorderNo(), res.getSaleorderId());
		} else {
			//发送消息推送
			quoteService.sendAllocation(orderData.getPhone(), null);
		}
		return res;
	}

	private void examine(Saleorder saleorder, Saleorder res) {
		//设置默认为不打印出库单
		Saleorder saleorderUpt = new Saleorder();
		saleorderUpt.setSaleorderId(res.getSaleorderId());
		saleorderUpt.setIsPrintout(ErpConst.ZERO);
		if(saleorder.getSalesArea() == null || "".equals(saleorder.getSalesArea())
				|| saleorder.getSalesAreaId() == null || ErpConst.ZERO.equals(saleorder.getSalesAreaId())) {
			saleorderUpt.setSalesArea(saleorder.getTakeTraderArea());
			RegionVo region = regionService.getRegionByArea(saleorder.getTakeTraderArea());
			logger.info("前台推送订单收货地址{},查询到的区域信息{},", saleorder.getTakeTraderArea(),JSON.toJSONString(region));
			if (region != null) {
				saleorderUpt.setSalesAreaId(region.getZoneId() == null?(region.getCity() == null ? region.getProvince() : region.getCity()):region.getZoneId());
			} else {
				saleorderUpt.setSalesAreaId(ErpConst.ZERO);
			}
		}
		ResultInfo resultInfo = verifyBdIsAutoAudit(res);
		logger.info("校验是否自动审核返回信息{}，",JSON.toJSONString(resultInfo));
		if(ErpConst.ZERO.equals(resultInfo.getCode())) {
			//校验风控信息
			saleorderUpt.setTraderId(res.getTraderId());
			SaleorderRiskModelVo saleorderRiskModelVo = riskCheckService.getUrlRiskAndCheckSaleorder(saleorderUpt);
			if (saleorderRiskModelVo.getIsRisk()) {
				bdOrderAutoAuditService.bdOrderAutoAudit(saleorderUpt);
			}
			logger.info("更新销售单信息{},",JSON.toJSONString(saleorderUpt));
			saleorderUpt.setAutoAudit(ErpConst.ONE);
			if(RiskHandlerKeyConstant.NO_RISK.equals(saleorderUpt.getIsRisk())){
				saleorderUpt.setIsRisk(ErpConst.ZERO);
			}
			saleorderMapper.updateByPrimaryKeySelective(saleorderUpt);
		}
	}

	private void getsalderByConsatant(Long time, Saleorder saleorder) {
		saleorder.setHaveAccountPeriod(1);//账期支付
		saleorder.setOrderType(1);  //订单类型
		saleorder.setPaymentMode(1);//支付方式
		saleorder.setPaymentType(423);//付款方式  先货后款，预付0%
		saleorder.setValidStatus(0);//生效状态
		saleorder.setValidTime(0L);//生效时间
		saleorder.setAddTime(time);//创建时间
		saleorder.setParentId(0); //父级订单为0

		//创建人Njadmin
		saleorder.setCreator(2);


		saleorder.setCustomerType(427);//临床医疗
		saleorder.setDeliveryClaim(0);//立即发货
		saleorder.setDeliveryType(482);//分批发货
		saleorder.setFreightDescription(470);//运费说明
		saleorder.setLogisticsComments("京东订单，仅可发京东/德邦/跨越/达达");
		saleorder.setWheatherJdOrder(1);
		saleorder.setCompanyId(1);//南京公司
		saleorder.setPurchaseStatus(0);//采购状态0未采购（默认）
		saleorder.setLockedStatus(0);//锁定状态0未锁定
		saleorder.setInvoiceStatus(0);//开票状态0未开票
		saleorder.setInvoiceTime(0L);//开票时间
		saleorder.setPaymentStatus(0);//付款状态 0未付款
		saleorder.setPaymentTime(0L);//付款时间
		saleorder.setDeliveryStatus(0);//发货状态0未发货
		saleorder.setDeliveryTime(0L);//发货时间
		saleorder.setArrivalTime(0L);//客户收货时间
		saleorder.setServiceStatus(0);//售后状态 0无
		saleorder.setArrivalStatus(0);//客户收货状态0未收货
		saleorder.setHaveAdvancePurchase(0);//有提前采购 0
		saleorder.setAdvancePurchaseStatus(0);//0无提前采购需求
		saleorder.setAdvancePurchaseTime(0L);//提前采购申请时间
		saleorder.setIsUrgent(0);//是否加急0否1是
		BigDecimal a = new BigDecimal(0);
		saleorder.setUrgentAmount(a);//加急费用
		saleorder.setHaveCommunicate(0);//有无沟通记录
		saleorder.setSyncStatus(0);//0未同步
		saleorder.setIsSendInvoice(1);//是否寄送发票
		// add by Tomcat.Hui 2020/2/4 3:35 下午 .Desc: VDERP-1889 线上BD订单默认发票类型修改. start
		//默认发票类型改为【13%增值税专用发票（寄送）】
		saleorder.setInvoiceType(972);
		// add by Tomcat.Hui 2020/2/4 3:35 下午 .Desc: VDERP-1889 线上BD订单默认发票类型修改. end
		saleorder.setInvoiceMethod(2); //开票方式1手动纸质开票、2自动纸质开票、3自动电子发票
		saleorder.setIsDelayInvoice(1);//是否延迟开票0否1是
		saleorder.setBillPeriodSettlementType(CustomerBillPeriodSettlementTypeEnum.ORDER_INVOICE.getCode());
		User user = userMapper.getByUsername("jx.vedeng", 1);
		saleorder.setCreator(user.getUserId());
		//前台订单 设置推送状态为1
		saleorder.setSendToPc(1);
	}

	private void getsaleOrderByorderDate(OrderData orderData, Saleorder saleorder,User belongUser) {
		saleorder.setTakeTraderName(orderData.getUsername());//收货公司
		saleorder.setTakeTraderContactName(orderData.getDeliveryUserName());//收货联系人名称
		saleorder.setSalesAreaId(Integer.valueOf(orderData.getInvoiceTraderDeliveryLevel3Id()));
		saleorder.setSalesArea(orderData.getDeliveryUserArea());
		saleorder.setTakeTraderArea(orderData.getDeliveryUserArea());//收货地区
		saleorder.setTakeTraderAddress(orderData.getDeliveryUserAddress());//收货地址
		saleorder.setTakeTraderContactMobile(orderData.getDeliveryUserPhone());//收货人手机
		saleorder.setTakeTraderContactTelephone(orderData.getDeliveryUserTel());//收货人电话
		//saleorder.setBdtraderComments(orderData.getRemakes());//客户备注-京东订单导入时，此备注为内部备注
		saleorder.setSaleorderNo(orderData.getOrderNo()); //订单号
		saleorder.setTotalAmount(orderData.getTotalCouponedAmount());//订单总价
		saleorder.setTraderName(orderData.getUsername());//如果没有关联erp客户就显示用户名称为注册时的企业名称
		saleorder.setStatus(orderData.getOrderStatus());//订单状态
		saleorder.setQuoteorderNo(orderData.getOrderNo());//报价单号
		saleorder.setSource(orderData.getOrderSrc());//订单来源BD
		saleorder.setPeriodDay(60);
		if (orderData.getDeliveryLevel3Id() != null) {
			saleorder.setTakeTraderAreaId(Integer.valueOf(orderData.getDeliveryLevel3Id()));//地区id
		}
		saleorder.setCreateMobile(orderData.getPhone());
		saleorder.setIsCoupons(orderData.getIsCoupons());
		saleorder.setCouponInfo(orderData.getCouponInfo());
		saleorder.setPrepaidAmount(new BigDecimal(0));//预付金额
		saleorder.setAccountPeriodAmount(orderData.getJxSalePrice());
		saleorder.setIsNew(1);
		// BD订单归属销售

		if (orderData.getPhone() != null) {
			if (belongUser != null && belongUser.getUserId() != null) {
				saleorder.setUserId(belongUser.getUserId());
			}

			if (belongUser != null && belongUser.getOrgId() != null) {
				saleorder.setOrgId(belongUser.getOrgId());
			}
			if (belongUser != null && belongUser.getOrgName() != null) {
				saleorder.setOrgName(belongUser.getOrgName());
			}
			if (belongUser != null && belongUser.getTraderId() != null && belongUser.getTraderId() != 0) {
				//判断交易者下有无地址
				Integer deliveryLevel1Id = 0;
				if (orderData.getDeliveryLevel1Id() != null && !orderData.getDeliveryLevel1Id().equals("")) {
					deliveryLevel1Id = Integer.valueOf(orderData.getDeliveryLevel1Id());
				}
				Integer deliveryLevel2Id = 0;
				if (orderData.getDeliveryLevel2Id() != null && !orderData.getDeliveryLevel2Id().equals("")) {
					deliveryLevel2Id = Integer.valueOf(orderData.getDeliveryLevel2Id());
				}
				Integer deliveryLevel3Id = 0;
				if (orderData.getDeliveryLevel3Id() != null && !orderData.getDeliveryLevel3Id().equals("")) {
					deliveryLevel3Id = Integer.valueOf(orderData.getDeliveryLevel3Id());
				}
				Integer traderAddressId = vailTraderAddress(belongUser.getTraderId(), deliveryLevel1Id, deliveryLevel2Id, deliveryLevel3Id, orderData.getDeliveryUserAddress());
				saleorder.setTakeTraderAddressId(traderAddressId);
				saleorder.setTakeTraderAreaId(deliveryLevel3Id);
				//判断交易者联系人是否存在并保存
				saleorder.setTraderId(belongUser.getTraderId());
				Integer traderContactId = vailTraderContact(belongUser.getTraderId(), orderData.getDeliveryUserName(), orderData.getDeliveryUserTel(), orderData.getDeliveryUserPhone());
				saleorder.setTakeTraderContactId(traderContactId);
				//判断保存注册用户信息是否在客户联系人中,如果没有则添加
//				vailTraderContact(belongUser.getTraderId(), orderData.getDeliveryUserName(), "", orderData.getPhone());
				//获取交易者信息
				Trader trader = traderMapper.getJDTraderInfoByTraderIdAndMobile(belongUser.getTraderId(), orderData.getPhone());
				if (trader != null) {
					saleorder.setTraderName(trader.getTraderName());
					saleorder.setCustomerType(trader.getCustomerType());
					saleorder.setCustomerNature(trader.getCustomerNature());
//					if (trader.getCustomerNature().equals(466)) {//终端
//						saleorder.setTerminalTraderType(trader.getCustomerType());
//						saleorder.setTerminalTraderName(trader.getTraderName());
//						saleorder.setTerminalTraderId(trader.getTraderId());
//						String areaIds = trader.getAreaIds();
//						if (org.apache.commons.lang.StringUtils.isNotBlank(areaIds)) {
//							//查询地区
//							String traderBussinessArea = regionService.getRegionNameStringByMinRegionIds(areaIds);
//							String[] areaid = areaIds.split(",");
//							Integer areaid2 = Integer.valueOf(areaid[areaid.length - 1]);
//							saleorder.setSalesAreaId(areaid2);
//							saleorder.setSalesArea(traderBussinessArea);
//						}
//					}

					saleorder.setTerminalTraderId(0);
					saleorder.setTerminalTraderName(orderData.getTerminalTraderName());
					saleorder.setTerminalTraderType(0);
					saleorder.setSalesAreaId(saleorder.getTakeTraderAreaId());
					saleorder.setSalesArea(orderData.getDeliveryUserArea());


					//Integer traderContactId = vailTraderContact(trader.getTraderId(),orderData.getDeliveryUserName(),orderData.getDeliveryUserPhone(),orderData.getDeliveryUserTel());
					saleorder.setTraderContactId(trader.getTraderContactId());
					if (trader.getTraderContactName() != null && !trader.getTraderContactName().isEmpty()) {
						saleorder.setTraderContactName(trader.getTraderContactName());
					}
					if (trader.getTraderContactMobile() != null && !trader.getTraderContactMobile().isEmpty()) {
						saleorder.setTraderContactMobile(trader.getTraderContactMobile());
					}
					saleorder.setTraderContactTelephone(trader.getTraderContactTelephone());
					saleorder.setTraderAreaId(trader.getAreaId());
					saleorder.setTraderAddressId(trader.getTraderAddressId());
					saleorder.setTraderAddress(trader.getTraderAddress());
					saleorder.setCustomerLevelStr(trader.getCustomerLevelStr());
					//查询地区
					String area = regionService.getRegionNameStringByMinRegionIds(trader.getAreaIds());
					saleorder.setTraderArea(area);
				}
			}
		}


		//保存订单商品
		List<OrderGoodsData> goodsList = orderData.getGoodsList();
		List<SaleorderGoods> salegoodsList = new ArrayList<SaleorderGoods>();
		for (OrderGoodsData orderGoodsData : goodsList) {
			SaleorderGoods saleorderGoods = new SaleorderGoods();
			saleorderGoods.setDeliveryCycle(orderGoodsData.getDeliveryCycle());//货期
			saleorderGoods.setGoodsComments("京东订单号："+orderGoodsData.getJdSaleorderNo());
			CoreSkuGenerate coreSku = coreSkuGenerateMapper.selectBySkuNo(orderGoodsData.getSkuNo());
			if (coreSku != null && coreSku.getSpuId() != null) {
				CoreSpuGenerate coreSpu = coreSpuGenerateMapper.selectByPrimaryKey(coreSku.getSpuId());
				if (coreSpu != null && Constants.ONE.equals(coreSpu.getMedicalInstrumentCatalogIncluded())) {
					saleorderGoods.setInsideComments("安调费用已收取");
				}
			}
			saleorderGoods.setSku(orderGoodsData.getSkuNo());
			if (belongUser != null && belongUser.getUserId() != null) {
				saleorderGoods.setCreator(belongUser.getUserId());
				saleorderGoods.setUpdater(belongUser.getUserId());
			}
			saleorderGoods.setAddTime(DateUtil.sysTimeMillis());
			saleorderGoods.setNum(orderGoodsData.getProductNum());
			saleorderGoods.setModTime(DateUtil.sysTimeMillis());
			saleorderGoods.setGoodsId(goodsService.getGoodsIdBySku(orderGoodsData.getSkuNo()));
			saleorderGoods.setDeliveryDirect(coreSku.getIsDirect());//是否直发 VDERP-16215 修改为取SKU表中的直发状态
			saleorderGoods.setPrice(orderGoodsData.getIsCoupons() == 1 ?
					orderGoodsData.getSkuAmount().divide(BigDecimal.valueOf(orderGoodsData.getProductNum()),2, BigDecimal.ROUND_HALF_UP) :
					orderGoodsData.getJxSalePrice());

			saleorderGoods.setJxSalePrice(orderGoodsData.getJxSalePrice());

			//添加优惠券相关数据
			saleorderGoods.setIsCoupons(orderGoodsData.getIsCoupons());
			saleorderGoods.setMaxSkuRefundAmount(orderGoodsData.getSkuAmount());
			saleorderGoods.setRealPrice(orderGoodsData.getJxSalePrice());

			//使用优惠券
			if(orderGoodsData.getIsCoupons() == 1){

				BigDecimal originalTotalAmout = saleorderGoods.getRealPrice().multiply(BigDecimal.valueOf(saleorderGoods.getNum()));

				String goodsComment = "已使用满减券，原价"+originalTotalAmout+ "，" +
						"优惠了"+(originalTotalAmout.subtract(saleorderGoods.getMaxSkuRefundAmount()))+"元；"
						+"有效期截止到" + DateUtil.convertString(Long.valueOf(orderData.getCouponInfo().getEffevtiveEndTime()),"yyyy-MM-dd HH:mm:ss");

				saleorderGoods.setGoodsComments(goodsComment);
			}

			//增加服务标签标签和货期承诺天数
			if(!MapUtils.isEmpty(orderData.getLogisticDaysMap())){
				saleorderGoods.setPerfermenceDeliveryTime(orderData.getLogisticDaysMap().get(orderGoodsData.getSkuNo()));
			}else{
				saleorderGoods.setPerfermenceDeliveryTime(-1);
			}


			if(CollectionUtils.isNotEmpty(orderGoodsData.getTagList())){
				String labelCodes = orderGoodsData.getTagList().stream().map(SkuServiceTag::getTagCode).collect(Collectors.joining(";"));
				String labelNames = orderGoodsData.getTagList().stream().map(SkuServiceTag::getTagName).collect(Collectors.joining(","));
				saleorderGoods.setLabelCodes(labelCodes);
				saleorderGoods.setLabelNames(labelNames);
				Optional<SkuServiceTag> shunFunLabel = orderGoodsData.getTagList().stream().filter(e -> "支持顺丰".equals(e.getTagName())).findAny();
			}
			saleorderGoods.setInsideComments(orderData.getRemakes());//VDERP-17005 应急订单导入SKU增加备注字段

			salegoodsList.add(saleorderGoods);
		}

		saleorder.setGoodsList(salegoodsList);
		//VDERP-6918支持前台切换BD订单收票模块信息数据源start
		Integer invoiceTraderContactId = vailTraderContact(belongUser.getTraderId(), orderData.getInvoiceTraderContactName(), orderData.getInvoiceTraderContactTelephone(), orderData.getInvoiceTraderContactMobile());
		saleorder.setInvoiceTraderContactId(invoiceTraderContactId);
		//收票联系人地址
		saleorder.setInvoiceTraderContactName(orderData.getInvoiceTraderContactName());
		//收票联系人电话
		saleorder.setInvoiceTraderContactMobile(orderData.getInvoiceTraderContactMobile());
		//收票联系人手机
		saleorder.setInvoiceTraderContactTelephone(orderData.getInvoiceTraderContactTelephone());
		//收票地址
		saleorder.setInvoiceTraderAddress(orderData.getInvoiceTraderAddress());
		//收票区域
		saleorder.setInvoiceTraderArea(orderData.getInvoiceTraderArea());
		//是否寄送发票
		saleorder.setIsSendInvoice(orderData.getIsSendInvoice());
		//收票公司ID
		saleorder.setInvoiceTraderId(orderData.getTraderId());
		//收票公司名称
		saleorder.setInvoiceTraderName(orderData.getUsername());
		//延迟开票
		saleorder.setIsDelayInvoice(Constants.ONE);
		//寄送发票默认数电发票
		saleorder.setInvoiceMethod(4);
		Integer invoiceTraderDeliveryLevel1Id = 0;
		if (orderData.getInvoiceTraderDeliveryLevel1Id() != null && !orderData.getInvoiceTraderDeliveryLevel1Id().equals("")) {
			invoiceTraderDeliveryLevel1Id = Integer.valueOf(orderData.getInvoiceTraderDeliveryLevel1Id());
		}
		Integer invoiceTraderDeliveryLevel2Id = 0;
		if (orderData.getInvoiceTraderDeliveryLevel2Id() != null && !orderData.getInvoiceTraderDeliveryLevel2Id().equals("")) {
			invoiceTraderDeliveryLevel2Id = Integer.valueOf(orderData.getInvoiceTraderDeliveryLevel2Id());
		}
		Integer invoiceTraderDeliveryLevel3Id = 0;
		if (orderData.getInvoiceTraderDeliveryLevel3Id() != null && !orderData.getInvoiceTraderDeliveryLevel3Id().equals("")) {
			invoiceTraderDeliveryLevel3Id = Integer.valueOf(orderData.getInvoiceTraderDeliveryLevel3Id());
		}
		Integer invoiceAddressId = vailTraderAddress(belongUser.getTraderId(),
				invoiceTraderDeliveryLevel1Id, invoiceTraderDeliveryLevel2Id, invoiceTraderDeliveryLevel3Id, orderData.getInvoiceTraderAddress());
		saleorder.setInvoiceTraderAddressId(invoiceAddressId);
		//开票类型
		saleorder.setInvoiceType(orderData.getInvoiceType());
		//新订单流标记状态
		saleorder.setIsNew(orderInfoSyncService.setSaleorderIsNewByOrg(saleorder.getSaleorderNo(), saleorder.getTraderId()));
		logger.info("请求DB保存BD订单信息 saleorder:{}", JSON.toJSONString(saleorder));
		//设置订单确认时间 确认状态
		saleorder.setConfirmTime(new Date());
		saleorder.setConfirmStatus(ErpConst.STATUS_STATE.SOME);
		//VDERP-6918支持前台切换BD订单收票模块信息数据源end
		saleorder.setTakeTraderId(orderData.getTraderId());
		saleorder.setInvoiceTraderId(orderData.getTraderId());
		saleorder.setInvoiceTraderName(orderData.getUsername());
	}

	@Override
	public ResultInfo verifyBdIsAutoAudit(Saleorder saleorder) {
		logger.info("自动审核判断，订单信息{},",JSON.toJSONString(saleorder));
		//判断首单逻辑
		List<Integer> saleorderIds = traderMapper.getSaleorderRealAmountByTraderId(saleorder.getTraderId());
		logger.info("前台推送订单，查询客户首单信息{},",JSON.toJSONString(saleorderIds));
		if(CollectionUtils.isNotEmpty(saleorderIds)){
			//不是首单--走自动审核
			List<String> skuLists = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleorder.getSaleorderId()).stream().map(SaleorderGoods :: getSku).distinct().collect(Collectors.toList());
			List<CoreSkuGenerate> coreSkuGenerates = coreSkuGenerateMapper.getSkuListByNo(skuLists);
			logger.info("前台推送订单，商品详情信息{},",JSON.toJSONString(coreSkuGenerates));
			if(CollectionUtils.isNotEmpty(coreSkuGenerates)){
				List<CoreSkuGenerate> noNeedReportSku = coreSkuGenerates.stream().filter(item -> ErpConst.ZERO.equals(item.getIsNeedReport())).collect(Collectors.toList());
				logger.info("前台推送订单，无需报备的sku信息{},",JSON.toJSONString(noNeedReportSku));
				if(noNeedReportSku.size() == skuLists.size()){
					//不存在需要报备的sku信息--走自动审核
					return new ResultInfo(0,"满足自动审核");
				}else {
					return new ResultInfo(-1,"存在非无需报备的商品信息");
				}
			}else {
				return new ResultInfo(-1,"没有商品信息，无法自动审核");
			}
		}else {
			return new ResultInfo(-1,"非首单，不自动审核");
		}
	}

    @Override
    public Integer updateBDSaleStatus(OrderData orderData) {
		logger.info("updateBDSaleStatus orderNo:{},orderStatus:{}",orderData.getOrderNo(),orderData.getOrderStatus());
        if ( 3 == orderData.getOrderStatus()) {
			Saleorder saleorder = new Saleorder();
			saleorder.setSaleorderNo(orderData.getOrderNo());
            saleorder.setStatus(3);//订单状态--关闭
			saleorder.setModTime(System.currentTimeMillis());
            saleorder.setCloseComments(StringUtils.isEmpty(orderData.getCloseComments())?"客户前台关闭，且未填写原因":orderData.getCloseComments());//订单关闭原因
            Saleorder saleorderInfo = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
			dealOrderClosePushReviewTask(saleorderInfo);
            saleorder.setSaleorderId(saleorderInfo.getSaleorderId());

			try {
				logger.info("BD订单前台关闭处理账期业务信息 saleOrder:{}", JSON.toJSONString(saleorderInfo));
				orderAccountPeriodService.dealCloseOrderCustomerBillPeriodOfOrder(saleorderInfo);
			} catch (CustomerBillPeriodException e) {
				logger.error("dealCloseOrderCustomerBillPeriodOfBdOrder message:{}", e.getMessage());
				return null;
			} catch (Exception e){
				logger.error("dealCloseOrderCustomerBillPeriodOfBdOrder error", e);
			}

			saleorder.setSaleorderId(saleorderInfo.getSaleorderId());
            int i = saleorderMapper.updateByPrimaryKeySelective(saleorder);

			SaleorderCoupon couponDb = this.saleorderCouponMapper.selectBySaleOrderId(saleorderInfo.getSaleorderId());

			//优惠券归还
			if(couponDb != null) {

				Map<String, String> couponReturnReqMap = new HashMap<>();
				couponReturnReqMap.put("couponCode", couponDb.getCouponCode());
				couponReturnReqMap.put("traderId", saleorderInfo.getTraderId() + Strings.EMPTY);

				logger.info("归还优惠券消息 start=======" + JSON.toJSONString(couponReturnReqMap));
				msgProducer.sendMsg(RabbitConfig.MARKET_RETURNCOUPON_EXCHANGE, RabbitConfig.MARKET_RETURNCOUPON_ROUTINGKEY, JSON.toJSONString(couponReturnReqMap));
				logger.info("归还优惠券消息 end=======");

				//处理优惠券的信息
				clearCoupon(saleorderInfo.getSaleorderId());
			}

            //更新库存服务数据
            int i1 = warehouseStockService.updateOccupyStockService(saleorder, 0);
            orderCommonService.updateSaleOrderDataUpdateTime(saleorderInfo.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_CLOSE);
            return i;
        } else {
            return null;
        }
    }

	/**
	 * 处理订单关闭推送审核信息
	 * @param saleOrder
	 * @return
	 */
	private boolean dealOrderClosePushReviewTask(Saleorder saleOrder){
		if (saleOrder == null || saleOrder.getSaleorderId() == null){
			return false;
		}
		logger.info("处理订单关闭推送时审核信息 saleOrder:{}", JSON.toJSONString(saleOrder));
		orderReviewProcessService.dealSaleOrderReviewProcess(saleOrder.getSaleorderId(),  1);
		return true;
	}

    /* (non-Javadoc)
     * @see com.vedeng.order.service.SaleorderService#updateBDChangeErp(com.vedeng.trader.model.WebAccount, java.lang.Integer, java.lang.Integer)
     * BD订单更改erp
     */
    @Override
    public Integer updateBDChangeErp(WebAccount erpAccount, Integer traderId, Integer saleorderId) {
        logger.info("updateBDChangeErp----------------------------start");
        int i = 0;
        int j = 0;
        try {
            //BD订单更改erp客户和归属销售
            WebAccount webAccountInfo = webAccountMapper.getWebAccountInfo(erpAccount.getErpAccountId());
            List<Saleorder> saleorderList = saleorderMapper.getSaleOrderlistByStatusMobile(webAccountInfo.getMobile());
            User belongUser = userMapper.getBDUserInfoById(erpAccount.getErpAccountId());
            //获取交易者客户信息
            Trader traderName = traderMapper.getTraderNameByTraderContactId(erpAccount.getTraderContactId());
            //获取交易者公司信息
            Trader traderCompany = traderMapper.getTraderCompanyByTraderId(traderId, erpAccount.getTraderAddressId());
            //获取交易者经营区域
            String areaIds = traderMapper.getTraderBussinessAreaByTraderId(traderId);
            //保存到报价表
            Quoteorder record = new Quoteorder();
            if (saleorderList != null) {
//				logger.info("saleorderList+++"+saleorderList.toString());
                //有traderId获得此用户所有订单批量修改
                for (Saleorder saleorder : saleorderList) {
                    Saleorder s = getPojo(belongUser, traderName, saleorder, traderCompany, areaIds, traderId);
                    s.setUserId(erpAccount.getUserId());
                    s.setTraderId(traderId);
                    i += saleorderMapper.updateByPrimaryKeySelective(s);
                    //更新报价单信息
                    record.setQuoteorderId(s.getQuoteorderId());
                    record = quoteorderMapper.getQuoteInfoByQuoteordeId(record);
					if(record == null){
						continue;
					}
                    record = getrecordPOJO(record, s, belongUser, traderId, traderCompany, traderName, areaIds);
                    record.setTraderId(traderId);
                    j += quoteorderMapper.updateQuote(record);
                }
                if (i < saleorderList.size() || j < saleorderList.size()) {
                    i = 0;
                    j = 0;
                }
            }

            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {
            };
            try {
                UserData userData = new UserData();
                userData.setAccountId(webAccountInfo.getWebAccountId());

                if (traderCompany != null && traderCompany.getTraderName() != null) {


                    userData.setCompanyName(traderCompany.getTraderName());
					userData.setSsoAccountId(webAccountInfo.getSsoAccountId());
                    String json = JsonUtils.translateToJson(userData);
//					logger.info(json);
//					String url = "http://wxtest.vedeng.com/user/updateCompanyName";
                    String url = mjxUrl + "/user/updateCompanyName";
                    JSONObject result2 = NewHttpClientUtils.httpPost(url, JSONObject.fromObject(userData));
                    logger.info(result2.toString());
                }
            } catch (Exception e) {
                logger.error("updateBDChangeErp", e);
            }
            logger.info("updateBDChangeErp----------------------------END");
            return j + i;
//			}
        } catch (Exception e) {
            logger.error("updateBDChangeErp:操作异常：", e);
            logger.error(Contant.ERROR_MSG, e);
        }
        logger.info("updateBDChangeErp----------------------------END");
        return i + j;
    }

    private Quoteorder getrecordPOJO(Quoteorder s, Saleorder saleorder2, User belongUser, Integer traderId, Trader traderCompany, Trader traderName, String areaIds) {
        logger.info("getrecordPOJO--------------------------start----------");
        try {
            if (belongUser != null && belongUser.getOrgId() != null) {
                s.setOrgId(belongUser.getOrgId());
            }
            if (belongUser != null && belongUser.getUserId() != null) {
                s.setUserId(belongUser.getUserId());
            }
            s.setTraderId(traderId);
            if (traderCompany != null && traderCompany.getTraderName() != null) {
                s.setTraderName(traderCompany.getTraderName());
            }
            if (traderName != null && traderName.getTraderContactId() != null) {
                s.setTraderContactId(traderName.getTraderContactId());
            }
            if (traderName != null && traderName.getTraderContactName() != null) {
                s.setTraderContactName(traderName.getTraderContactName());
            }
            if (traderName != null && traderName.getTraderContactMobile() != null) {
                s.setMobile(traderName.getTraderContactMobile());
            }
            if (traderName != null && traderName.getTraderContactTelephone() != null) {
                s.setTelephone(traderName.getTraderContactTelephone());
            }
            if (traderCompany != null && traderCompany.getTraderAddressId() != null) {
                s.setTraderAddressId(traderCompany.getTraderAddressId());
            }
            if (traderCompany != null && traderCompany.getTraderAddress() != null) {
                s.setAddress(traderCompany.getTraderAddress());
            }
            if (traderCompany != null && traderCompany.getCustomerType() != null) {
                s.setCustomerType(traderCompany.getCustomerType());

            }
            if (traderCompany != null && traderCompany.getCustomerNature() != null) {
                s.setCustomerNature(traderCompany.getCustomerNature());
                if (traderCompany.getCustomerNature().equals(466)) {//终端
                    s.setTerminalTraderId(traderId);
                    s.setTerminalTraderName(traderCompany.getTraderName());
                    s.setTerminalTraderType(traderCompany.getCustomerType());
                    if (traderCompany.getAreaIds() != null) {
                        //查询经营地区
                        String traderBussinessArea = regionService.getRegionNameStringByMinRegionIds(traderCompany.getAreaIds());
                        String[] areaid = traderCompany.getAreaIds().split(",");
                        Integer areaid2 = Integer.valueOf(areaid[areaid.length - 1]);
                        s.setSalesAreaId(areaid2);
                        s.setSalesArea(traderBussinessArea);
                    }
                }
                if (traderCompany.getCustomerNature().equals(465)) {
                    s.setTerminalTraderId(0);
                    s.setTerminalTraderName("");
                    s.setTerminalTraderType(0);
                    s.setSalesAreaId(0);
                    s.setSalesArea("");
                }
            }
            //查询地区
            if (traderCompany != null && traderCompany.getAreaIds() != null) {
                String area = regionService.getRegionNameStringByMinRegionIds(traderCompany.getAreaIds());
                if (area != null) {
                    s.setArea(area);
                }
            }
            if (areaIds != null) {
                //查询经营地区
                String traderBussinessArea = regionService.getRegionNameStringByMinRegionIds(areaIds);
                String[] areaid = areaIds.split(",");
                Integer areaid2 = Integer.valueOf(areaid[areaid.length - 1]);
                s.setSalesAreaId(areaid2);
                s.setSalesArea(traderBussinessArea);
            }
            s.setModTime(System.currentTimeMillis());

        } catch (Exception e) {
            logger.error("getrecordPOJO:操作异常：", e);
            logger.error(Contant.ERROR_MSG, e);
        }
        logger.info("getrecordPOJO--------------------------END----------");
        return s;
    }

    private Saleorder getPojo(User belongUser, Trader traderName, Saleorder saleorder, Trader traderCompany, String areaIds, Integer traderId) {
        logger.info("getPojo--------------------------start----------");
        try {
            if (belongUser != null && belongUser.getUserId() != null) {
                saleorder.setUserId(belongUser.getUserId());
            }
            if (belongUser != null && belongUser.getOrgId() != null) {
                saleorder.setOrgId(belongUser.getOrgId());
            }
            if (belongUser != null && belongUser.getOrgName() != null) {
                saleorder.setOrgName(belongUser.getOrgName());
            }
            saleorder.setTraderId(traderId);
            saleorder.setTakeTraderContactId(traderId);
            saleorder.setTakeTraderId(traderId);

            if (traderCompany != null && traderCompany.getTraderName() != null) {
                saleorder.setTraderName(traderCompany.getTraderName());
            }
            if (traderCompany != null && traderCompany.getCustomerType() != null) {
                saleorder.setCustomerType(traderCompany.getCustomerType());

            }
            if (traderCompany != null && traderCompany.getCustomerNature() != null) {
                saleorder.setCustomerNature(traderCompany.getCustomerNature());
                if (traderCompany.getCustomerNature().equals(466)) {//终端
                    saleorder.setTerminalTraderId(traderId);
                    saleorder.setTerminalTraderName(traderCompany.getTraderName());
                    saleorder.setTerminalTraderType(traderCompany.getCustomerType());
                    if (traderCompany.getAreaIds() != null) {
                        //查询经营地区
                        String traderBussinessArea = regionService.getRegionNameStringByMinRegionIds(traderCompany.getAreaIds());
                        String[] areaid = traderCompany.getAreaIds().split(",");
                        Integer areaid2 = Integer.valueOf(areaid[areaid.length - 1]);
                        saleorder.setSalesAreaId(areaid2);
                        saleorder.setSalesArea(traderBussinessArea);
                    }
                }
                if (traderCompany.getCustomerNature().equals(465)) {
                    saleorder.setTerminalTraderId(0);
                    saleorder.setTerminalTraderName("");
                    saleorder.setTerminalTraderType(0);
                    saleorder.setSalesAreaId(0);
                    saleorder.setSalesArea("");
                }
            }
            if (traderName != null && traderName.getTraderContactId() != null) {
                saleorder.setTraderContactId(traderName.getTraderContactId());
            }
            if (traderName != null && traderName.getTraderContactName() != null) {
                saleorder.setTraderContactName(traderName.getTraderContactName());
            }
            if (traderName != null && traderName.getTraderContactMobile() != null) {
                saleorder.setTraderContactMobile(traderName.getTraderContactMobile());
            }
            if (traderName != null && traderName.getTraderContactTelephone() != null) {
                saleorder.setTraderContactTelephone(traderName.getTraderContactTelephone());
            }
            if (traderCompany != null && traderCompany.getAreaId() != null) {
                saleorder.setTraderAreaId(traderCompany.getAreaId());
            }
            if (traderCompany != null && traderCompany.getTraderAddressId() != null) {
                saleorder.setTraderAddressId(traderCompany.getTraderAddressId());
            }
            if (traderCompany != null && traderCompany.getTraderAddress() != null) {
                saleorder.setTraderAddress(traderCompany.getTraderAddress());
            }
            saleorder.setInvoiceTraderId(traderId);
            if (traderCompany != null && traderCompany.getTraderName() != null) {
                saleorder.setInvoiceTraderName(traderCompany.getTraderName());
                saleorder.setTakeTraderName(traderCompany.getTraderName());
            }
            if (traderName != null && traderName.getTraderContactId() != null) {
                saleorder.setInvoiceTraderContactId(traderName.getTraderContactId());
            }
            if (traderName != null && traderName.getTraderContactName() != null) {
                saleorder.setInvoiceTraderContactName(traderName.getTraderContactName());
            }
            if (traderName != null && traderName.getTraderContactMobile() != null) {
                saleorder.setInvoiceTraderContactMobile(traderName.getTraderContactMobile());
            }
            if (traderName != null && traderName.getTraderContactTelephone() != null) {
                saleorder.setInvoiceTraderContactTelephone(traderName.getTraderContactTelephone());
            }
            if (traderCompany != null && traderCompany.getTraderAddressId() != null) {
                saleorder.setInvoiceTraderAddressId(traderCompany.getTraderAddressId());
            }
            if (traderCompany != null && traderCompany.getTraderName() != null) {
                saleorder.setTakeTraderName(traderCompany.getTraderName());
            }
            //查询地区
            if (null != traderCompany && traderCompany.getAreaIds() != null) {
                String area = regionService.getRegionNameStringByMinRegionIds(traderCompany.getAreaIds());
                if (area != null) {
                    saleorder.setTraderArea(area);
                }
            }
            logger.info("getPojo-------------------------------END");
        } catch (Exception e) {
			log.error("【getPojo】处理异常",e);
            logger.error("getPojo:操作异常：", e);
        }
        return saleorder;
    }



    @Override
    public Saleorder getsaleorderbySaleorderId(Integer saleorderId) {
        return saleorderMapper.getSaleOrderById(saleorderId);
    }
	@Override
	public OrderData queryOrderNoForMjx(String saleorderNo) throws Exception {
		Saleorder saleorderTemp = saleorderMapper.getSaleorderByOrderNo(saleorderNo);
		if(saleorderTemp == null){
			throw new Exception("订单不存在:"+saleorderNo);
		}

		Integer saleorderId = saleorderTemp.getSaleorderId();
		Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderId);
		//修改订单状态
		long time = System.currentTimeMillis();
		// changed by Tomcat.Hui 2020/3/3 7:45 下午 .Desc: VDERP-2067 删除用户确认流程 .
		//在前置的流程里,会使用verifiesInfoMapper更新主表T_SALEORDER的STATUS为1,这里只需去掉更新为4即可
		//saleorder.setStatus(4);
		saleorder.setModTime(time);
		saleorder.setBdMobileTime(time);
		OrderData orderData = new OrderData();
		orderData.setOrderNo(saleorder.getSaleorderNo());

		// changed by Tomcat.Hui 2020/3/9 10:38 上午 .Desc:  VDERP-2057 BD订单流程优化-ERP部分
		// 因为自定义首付款（0%）审核完成默认付账期变为部分付款 这里需要传订单状态给mjx .
		if (saleorder.getPaymentStatus() == 2) {
			orderData.setOrderStatus(3);
		} else if (saleorder.getPaymentStatus() == 1) {
			orderData.setOrderStatus(7);
		} else if (saleorder.getPaymentStatus() == 0) {
			orderData.setOrderStatus(2);
		}
		if(saleorder.getOrderType()==0){
			saleorder.setCreateMobile(saleorder.getTraderContactMobile());
		}
		if (saleorder.getCreateMobile() == null || saleorder.getCreateMobile().isEmpty()) {
			ResultInfo r = new ResultInfo(-1, "创建人手机号为空");
			String result = r.toString();
			throw new Exception(r.getMessage());
		} else {
			WebAccount web = webAccountMapper.getWenAccountInfoByMobile(saleorder.getCreateMobile());
//            orderData.setAccountId(web.getWebAccountId());//待定  =用户ID
//			orderData.setSsoAccountId(web.getSsoAccountId());

			if(web!=null){
				orderData.setAccountId(web.getWebAccountId());
				orderData.setSsoAccountId(web.getSsoAccountId());
			}
		}
		orderData.setDeliveryUserName(saleorder.getTakeTraderContactName());
		orderData.setDeliveryUserArea(saleorder.getTakeTraderArea());
		orderData.setDeliveryUserAddress(saleorder.getTakeTraderAddress());
		orderData.setDeliveryUserPhone(saleorder.getTakeTraderContactMobile());
		orderData.setDeliveryUserTel(saleorder.getTakeTraderContactTelephone());
		orderData.setInvoiceTraderContactId(saleorder.getInvoiceTraderContactId());
		orderData.setInvoiceTraderContactName(saleorder.getInvoiceTraderContactName());
		orderData.setInvoiceTraderContactMobile(saleorder.getInvoiceTraderContactMobile());
		orderData.setInvoiceTraderContactTelephone(saleorder.getInvoiceTraderContactTelephone());
		orderData.setInvoiceTraderArea(saleorder.getInvoiceTraderArea());
		orderData.setInvoiceTraderAddress(saleorder.getInvoiceTraderAddress());
		orderData.setInvoiceUserPhone(saleorder.getInvoiceTraderContactTelephone());
		orderData.setAdditionalClause(saleorder.getAdditionalClause());//附加条款
		orderData.setIsSendInvoice(saleorder.getIsSendInvoice());//是否寄送发票
		orderData.setInvoiceType(saleorder.getInvoiceType());
		orderData.setCompanyName(saleorder.getTraderName());
		orderData.setCompanyId(saleorder.getTraderId());
		//  TraderAddress traderAddress = traderAddressMapper.getAddressInfoById(saleorder.getTakeTraderAddressId(), 1);
		orderData.setDeliveryAddressId(saleorder.getTakeTraderAddressId());

//		orderData.setDeliveryAreaIds(traderAddress.getAreaIds());
		List<SaleorderGoods> goodsList = goodsService.getGoodsPriceList(saleorderId);
		List<OrderGoodsData> goodsList2 = new ArrayList<OrderGoodsData>();
		BigDecimal m = null;
		Integer num = 0;

		for (SaleorderGoods s : goodsList) {
			OrderGoodsData orderGoodsData = new OrderGoodsData();
			orderGoodsData.setSkuNo(s.getSku());

			//VS单子
			if(saleorder.getOrderType() == 0){
				orderGoodsData.setCouponPrice(s.getPrice());
				orderGoodsData.setJxSalePrice(s.getPrice());
				//BD单子
			}else if(saleorder.getOrderType() == 1){
				orderGoodsData.setCouponPrice(s.getPrice());
				orderGoodsData.setJxSalePrice(s.getRealPrice());
			}

			m = new BigDecimal(s.getNum());
			m = m.multiply(s.getRealPrice());
			orderGoodsData.setMarketMomey(m);
			orderGoodsData.setIsCoupons(s.getIsCoupons());
			orderGoodsData.setProductNum(s.getNum());
			orderGoodsData.setStoreRemarks(s.getGoodsComments());
			num += s.getNum();
			orderGoodsData.setGiftFlag(s.getIsGift() != null && Constants.ONE.equals(s.getIsGift()) ? "Y" : "N");
			orderGoodsData.setHaveInstallation(s.getHaveInstallation());
			goodsList2.add(orderGoodsData);
		}
		orderData.setGoodsList(goodsList2);

		orderData.setOrderType(1);
		if(saleorder.getOrderType()==0){
			orderData.setOrderType(0);
		}
		// changed by Tomcat.Hui 2020/3/6 9:10 上午 .Desc: VDERP-2057 BD订单流程优化-ERP部分 取消原有待确认状态 改为mjx查询order服务获取 .
		//orderData.setOrderStatus(4);//待用户确认
		orderData.setProductNum(num);
		orderData.setTotalCouponedAmount(saleorder.getTotalAmount());

		if(saleorder.getValidTime() != null){
			long validDate = HolidayUtil.getWorkDate(saleorder.getValidTime(),5);
			orderData.setCancelDate(DateUtil.convertString(validDate, DateUtil.TIME_FORMAT));
		}

		BigDecimal totalAmout = BigDecimal.ZERO;
		for(SaleorderGoods saleorderGood : goodsList){
			//VS单子
			if(saleorder.getOrderType() == 0){
				totalAmout = totalAmout.add(saleorderGood.getPrice().multiply(new BigDecimal(saleorderGood.getNum())));

				//BD单子
			}else if(saleorder.getOrderType() == 1){
				totalAmout = totalAmout.add(saleorderGood.getRealPrice().multiply(new BigDecimal(saleorderGood.getNum())));
			}

		}
		orderData.setJxSalePrice(totalAmout);

		//添加订单的优惠券信息
		SaleorderCoupon saleorderCoupon = this.saleorderCouponMapper.selectBySaleOrderId(saleorderId);
		orderData.setIsCoupons(saleorderCoupon == null ? 0 : 1);
		orderData.setCouponInfo(saleorderCoupon);
		return orderData;
	}


	/* (non-Javadoc)
     * @see com.vedeng.order.service.SaleorderService#updateVedengJX(java.lang.Integer)
     * BD订单审核完成推送数据
     */
    @Override
    public JSONObject updateVedengJX(Integer saleorderId) throws Exception {
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderId);
        //修改订单状态
        long time = System.currentTimeMillis();
        // changed by Tomcat.Hui 2020/3/3 7:45 下午 .Desc: VDERP-2067 删除用户确认流程 .
		//在前置的流程里,会使用verifiesInfoMapper更新主表T_SALEORDER的STATUS为1,这里只需去掉更新为4即可
        //saleorder.setStatus(4);
        saleorder.setModTime(time);
        saleorder.setBdMobileTime(time);
        OrderData orderData = new OrderData();
        orderData.setOrderNo(saleorder.getSaleorderNo());

		// changed by Tomcat.Hui 2020/3/9 10:38 上午 .Desc:  VDERP-2057 BD订单流程优化-ERP部分
		// 因为自定义首付款（0%）审核完成默认付账期变为部分付款 这里需要传订单状态给mjx .
		if (saleorder.getPaymentStatus() == 2) {
			orderData.setOrderStatus(3);
		} else if (saleorder.getPaymentStatus() == 1) {
			orderData.setOrderStatus(7);
		} else if (saleorder.getPaymentStatus() == 0) {
			orderData.setOrderStatus(2);
		}
		if(saleorder.getOrderType()==0){
			saleorder.setCreateMobile(saleorder.getTraderContactMobile());
		}
        if (saleorder.getCreateMobile() == null || saleorder.getCreateMobile().isEmpty()) {
            ResultInfo r = new ResultInfo(-1, "创建人手机号为空");
            String result = r.toString();
            return JSONObject.fromObject(result);
        } else {
            WebAccount web = webAccountMapper.getWenAccountInfoByMobile(saleorder.getCreateMobile());
//            orderData.setAccountId(web.getWebAccountId());//待定  =用户ID
//			orderData.setSsoAccountId(web.getSsoAccountId());

			if(web!=null){
				orderData.setAccountId(web.getWebAccountId());
				orderData.setSsoAccountId(web.getSsoAccountId());
			}
        }
        orderData.setDeliveryUserName(saleorder.getTakeTraderContactName());
        orderData.setDeliveryUserArea(saleorder.getTakeTraderArea());
        orderData.setDeliveryUserAddress(saleorder.getTakeTraderAddress());
        orderData.setDeliveryUserPhone(saleorder.getTakeTraderContactMobile());
        orderData.setDeliveryUserTel(saleorder.getTakeTraderContactTelephone());
        orderData.setInvoiceTraderContactId(saleorder.getInvoiceTraderContactId());
        orderData.setInvoiceTraderContactName(saleorder.getInvoiceTraderContactName());
        orderData.setInvoiceTraderContactMobile(saleorder.getInvoiceTraderContactMobile());
        orderData.setInvoiceTraderContactTelephone(saleorder.getInvoiceTraderContactTelephone());
        orderData.setInvoiceTraderArea(saleorder.getInvoiceTraderArea());
        orderData.setInvoiceTraderAddress(saleorder.getInvoiceTraderAddress());
        orderData.setInvoiceUserPhone(saleorder.getInvoiceTraderContactTelephone());
        orderData.setAdditionalClause(saleorder.getAdditionalClause());//附加条款
        orderData.setIsSendInvoice(saleorder.getIsSendInvoice());//是否寄送发票
        orderData.setInvoiceType(saleorder.getInvoiceType());
        orderData.setCompanyName(saleorder.getTraderName());
        orderData.setCompanyId(saleorder.getTraderId());
      //  TraderAddress traderAddress = traderAddressMapper.getAddressInfoById(saleorder.getTakeTraderAddressId(), 1);
        orderData.setDeliveryAddressId(saleorder.getTakeTraderAddressId());

//		orderData.setDeliveryAreaIds(traderAddress.getAreaIds());
        List<SaleorderGoods> goodsList = goodsService.getGoodsPriceList(saleorderId);
        List<OrderGoodsData> goodsList2 = new ArrayList<OrderGoodsData>();
        BigDecimal m = null;
        Integer num = 0;

        for (SaleorderGoods s : goodsList) {
            OrderGoodsData orderGoodsData = new OrderGoodsData();
            orderGoodsData.setSkuNo(s.getSku());

            //VS单子
            if(saleorder.getOrderType() == 0){
                orderGoodsData.setCouponPrice(s.getPrice());
                orderGoodsData.setJxSalePrice(s.getPrice());
            //BD单子
            }else if(saleorder.getOrderType() == 1){
                orderGoodsData.setCouponPrice(s.getPrice());
                orderGoodsData.setJxSalePrice(s.getRealPrice());
            }

            m = new BigDecimal(s.getNum());
            m = m.multiply(s.getRealPrice());
            orderGoodsData.setMarketMomey(m);
            orderGoodsData.setIsCoupons(s.getIsCoupons());
            orderGoodsData.setProductNum(s.getNum());
            orderGoodsData.setStoreRemarks(s.getGoodsComments());
            num += s.getNum();
			orderGoodsData.setGiftFlag(s.getIsGift() != null && Constants.ONE.equals(s.getIsGift()) ? "Y" : "N");
			orderGoodsData.setHaveInstallation(s.getHaveInstallation());
            goodsList2.add(orderGoodsData);
        }
        orderData.setGoodsList(goodsList2);

        orderData.setOrderType(1);
		if(saleorder.getOrderType()==0){
			orderData.setOrderType(0);
		}
		// changed by Tomcat.Hui 2020/3/6 9:10 上午 .Desc: VDERP-2057 BD订单流程优化-ERP部分 取消原有待确认状态 改为mjx查询order服务获取 .
        //orderData.setOrderStatus(4);//待用户确认
        orderData.setProductNum(num);
        orderData.setTotalCouponedAmount(saleorder.getTotalAmount());

        if(saleorder.getValidTime() != null){
			long validDate = HolidayUtil.getWorkDate(saleorder.getValidTime(),5);
			orderData.setCancelDate(DateUtil.convertString(validDate, DateUtil.TIME_FORMAT));
		}

		BigDecimal totalAmout = BigDecimal.ZERO;
		for(SaleorderGoods saleorderGood : goodsList){
            //VS单子
            if(saleorder.getOrderType() == 0){
                totalAmout = totalAmout.add(saleorderGood.getPrice().multiply(new BigDecimal(saleorderGood.getNum())));

            //BD单子
            }else if(saleorder.getOrderType() == 1){
                totalAmout = totalAmout.add(saleorderGood.getRealPrice().multiply(new BigDecimal(saleorderGood.getNum())));
            }

		}
        orderData.setJxSalePrice(totalAmout);

		//添加订单的优惠券信息
		SaleorderCoupon saleorderCoupon = this.saleorderCouponMapper.selectBySaleOrderId(saleorderId);
		orderData.setIsCoupons(saleorderCoupon == null ? 0 : 1);
		orderData.setCouponInfo(saleorderCoupon);

        try {
            String url =  mjxUrl+"/order/verifyOrder";
            String json = JsonUtils.translateToJson(orderData);
            logger.info("updateVedengJX"+json);
            JSONObject result2 = NewHttpClientUtils.httpPost(url, JSONObject.fromObject(orderData));
            int i = saleorderMapper.updateByPrimaryKeySelective(saleorder);
            return result2;
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            logger.info("updateVedengJXErro+++" + e);
            return null;
        }

    }

    /* (non-Javadoc)
     * @see com.vedeng.order.service.SaleorderService#returnBDStatus(java.lang.Integer)
     * BD订单撤回
     */
    @Override
    public ResultInfo returnBDStatus(Integer saleorderId) {
        ResultInfo resultInfo = new ResultInfo();
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleorderId);
        saleorder.setValidStatus(0);
        saleorder.setStatus(0);
        saleorder.setModTime(System.currentTimeMillis());
        saleorder.setBdMobileTime(0L);
        Saleorder saleOrderById = saleorderMapper.getSaleOrderById(saleorderId);
        //修改订单状态
        int i = saleorderMapper.updateByPrimaryKeySelective(saleorder);
        //修改审核状态
        int j = verifiesRecordService.updateBDStatus(saleorderId);
        //删除流程
        Map<String, Object> historic = actionProcdefService.getHistoric(processEngine,
                "saleorderVerify_" + saleorderId);
        HistoryService historyService = processEngine.getHistoryService();
        RuntimeService runtimeService = processEngine.getRuntimeService();
        String key = "saleorderVerify_" + saleorderId;

        // add by Tomcat.Hui 2019/11/22 13:57 .Desc: VDERP-1448 BD订单在后台点击撤回订单，状态已更新，但无相应的操作按钮. start
        List<HistoricProcessInstance> hpis = historyService.createHistoricProcessInstanceQuery().processInstanceBusinessKey(key).list();
        if (!CollectionUtils.isEmpty(hpis)) {
            for (HistoricProcessInstance hpi : hpis) {
                if (hpi != null) {
                    String processInstanceId = hpi.getId(); //流程实例ID
                    ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();// 使用流程实例ID查询
                    if (pi == null) {
                        //该流程实例已经完成了
                        historyService.deleteHistoricProcessInstance(processInstanceId);
                    } else {
                        //该流程实例未结束的
                        runtimeService.deleteProcessInstance(processInstanceId, "撤回订单");
                        historyService.deleteHistoricProcessInstance(processInstanceId);//(顺序不能换)
                    }
                }
            }
        }
        // add by Tomcat.Hui 2019/11/22 13:57 .Desc: VDERP-1448 BD订单在后台点击撤回订单，状态已更新，但无相应的操作按钮. end
		//重构
		saleorderSyncService.syncSaleorderStatus2Mjx(saleorder.getSaleorderId()
				, PCOrderStatusEnum.INIT, SaleorderSyncEnum.BD_RETURN);

        if (i > 0 && j > 0) {
            resultInfo.setCode(0);
            resultInfo.setMessage("成功");
        } else {
            resultInfo.setCode(1);
            resultInfo.setMessage("失败");
        }
        return resultInfo;
    }

    /* (non-Javadoc)
     * @see com.vedeng.order.service.SaleorderService#ChangeEditSaleOrder(com.vedeng.order.model.Saleorder)
     * BD订单申请修改
     */
    @Override
    public JSONObject ChangeEditSaleOrder(Saleorder saleorder) {
        OrderData orderData = new OrderData();
        orderData.setOrderNo(saleorder.getSaleorderNo());
		if(saleorder.getOrderType()==0){
			saleorder.setCreateMobile(saleorder.getTraderContactMobile());
		}
        WebAccount web = webAccountMapper.getWenAccountInfoByMobile(saleorder.getCreateMobile());
//        orderData.setAccountId(web.getWebAccountId());//待定  =用户ID
//		orderData.setSsoAccountId(web.getSsoAccountId());

		if(web!=null){
			orderData.setAccountId(web.getWebAccountId());
			orderData.setSsoAccountId(web.getSsoAccountId());
		}else{
			logger.error("ChangeEditSaleOrder  账号不存在，不推送"+saleorder.getCreateMobile());
		}
        orderData.setDeliveryUserName(saleorder.getTakeTraderContactName());
        orderData.setDeliveryUserArea(saleorder.getTakeTraderArea());
        orderData.setDeliveryUserAddress(saleorder.getTakeTraderAddress());
        orderData.setDeliveryUserPhone(saleorder.getTakeTraderContactMobile());
        orderData.setDeliveryUserTel(saleorder.getTakeTraderContactTelephone());
        orderData.setInvoiceTraderContactId(saleorder.getInvoiceTraderContactId());
        orderData.setInvoiceTraderContactName(saleorder.getInvoiceTraderContactName());
        orderData.setInvoiceTraderContactMobile(saleorder.getInvoiceTraderContactMobile());
        orderData.setInvoiceTraderContactTelephone(saleorder.getInvoiceTraderContactTelephone());
        orderData.setInvoiceTraderArea(saleorder.getInvoiceTraderArea());
        orderData.setInvoiceTraderAddress(saleorder.getInvoiceTraderAddress());
        orderData.setInvoiceUserPhone(saleorder.getInvoiceTraderContactTelephone());
        orderData.setAdditionalClause(saleorder.getAdditionalClause());
        orderData.setIsSendInvoice(saleorder.getIsSendInvoice());//是否寄送发票
        orderData.setCompanyName(saleorder.getTraderName());
        orderData.setInvoiceType(saleorder.getInvoiceType());
        orderData.setCompanyId(saleorder.getTraderId());
       // TraderAddress traderAddress = traderAddressMapper.getAddressInfoById(saleorder.getTakeTraderAddressId(), 1);
        orderData.setDeliveryAddressId(saleorder.getTakeTraderAddressId());
//		orderData.setDeliveryAreaIds(traderAddress.getAreaIds());
        List<SaleorderGoods> goodsList = getSaleorderGoodsById(saleorder);
        List<OrderGoodsData> goodsList2 = null;
        for (SaleorderGoods s : goodsList) {
            goodsList2 = new ArrayList<OrderGoodsData>();
            OrderGoodsData orderGoodsData = new OrderGoodsData();
            orderGoodsData.setSkuNo(s.getSku());
            orderGoodsData.setStoreRemarks(s.getGoodsComments());
            goodsList2.add(orderGoodsData);
        }
        orderData.setGoodsList(goodsList2);
        JSONObject result2 = new JSONObject();
        try {
            String url = mjxUrl + "/order/updateErpOrder";
            String json = JsonUtils.translateToJson(orderData);
            logger.info(json);
            CloseableHttpClient httpClient = HttpClientBuilder.create().build();
            result2 = NewHttpClientUtils.httpPost(url, JSONObject.fromObject(orderData));
            logger.info(result2.toString());
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            logger.info("ChangeEditSaleOrderErro+++" + e);
        }
        return result2;
    }

    @Override
    public List<User> getUserByCategory(Integer categoryId, Integer companyId) {
        return rCategoryJUserMapper.getUserByCategory(categoryId, companyId);
    }

    @Override
    public List<Saleorder> selectSaleorderNo(Saleorder saleorder) {
        return saleorderMapper.selectSaleorderNo(saleorder);
    }

    /**
     * 功能描述: 验证客户地址是否存在
     *
     * @param:
     * @return:
     * @auther: duke.li
     * @date: 2019/8/13 17:08
     */
    public Integer vailTraderAddress(Integer traderId, Integer provinceId, Integer cityId, Integer areaId, String address) {
        try {
        	if(provinceId == 0 && cityId == 0 && areaId == 0){
        		//省市区都为0的情况不新增
        		return 0;
			}
            // 验证地址是否存在
            TraderAddress traderAddress = traderAddressMapper.getAddressInfoByAddress(traderId, CommonConstants.TRADER_TYPE_1, provinceId + "," + cityId + "," + areaId, areaId, address);
            if (traderAddress == null || traderAddress.getTraderAddressId() == null) {
                traderAddress = new TraderAddress();
                traderAddress.setTraderId(traderId);
                traderAddress.setTraderType(CommonConstants.TRADER_TYPE_1);
                traderAddress.setIsEnable(CommonConstants.ENABLE);
                traderAddress.setAreaId(areaId);
                traderAddress.setAreaIds(provinceId + "," + cityId + "," + areaId);
                traderAddress.setAddress(address);
                traderAddress.setCreator(548);//精选用户
                traderAddress.setAddTime(DateUtil.gainNowDate());
                traderAddress.setUpdater(548);
                traderAddress.setModTime(DateUtil.gainNowDate());
                traderAddressMapper.insertSelective(traderAddress);
            }
            return traderAddress.getTraderAddressId();
        } catch (Exception e) {
            logger.error("验证客户地址是否存在vailTraderAddress发生异常", e);
        }
        return null;
    }

    /**
     * 功能描述: 验证客户联系人是否存在
     *
     * @param: [traderId, name, telephone, mobile]
     * @return: java.lang.Integer
     * @auther: duke.li
     * @date: 2019/8/13 17:36
     */
    public Integer vailTraderContact(Integer traderId, String name, String telephone, String mobile) {
        try {
            TraderContactGenerate contactInfo = traderContactGenerateMapper.getContactInfo(traderId, CommonConstants.TRADER_TYPE_1, name, telephone, mobile);
            if (contactInfo == null || contactInfo.getTraderContactId() == null) {
                contactInfo = new TraderContactGenerate();
                contactInfo.setTraderId(traderId);
                contactInfo.setTraderType(CommonConstants.TRADER_TYPE_1);
                contactInfo.setIsEnable(CommonConstants.ENABLE);
                contactInfo.setName(name);
                contactInfo.setTelephone(telephone);
                contactInfo.setMobile(mobile);
                contactInfo.setSex(2);
                contactInfo.setIsOnJob(1);
                contactInfo.setCreator(548);//精选用户
                contactInfo.setAddTime(DateUtil.gainNowDate());
                contactInfo.setUpdater(548);
                contactInfo.setModTime(DateUtil.gainNowDate());
                traderContactGenerateMapper.insertSelective(contactInfo);
            }
            return contactInfo.getTraderContactId();
        } catch (Exception e) {
            logger.error("验证客户联系人是否存在vailTraderContact发生异常", e);
        }
        return null;
    }

	@Override
	public ResultInfo<?> closeBDSaleorder(Saleorder saleorder) {
		try {
			saleorder = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
			//重构
			saleorderSyncService.syncSaleorderStatus2Mjx(saleorder.getSaleorderId()
					, PCOrderStatusEnum.CANCEL, SaleorderSyncEnum.CANCEL_BD_HAND);
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return ResultInfo.error("关闭BD订单异常");
		}
		return ResultInfo.success();
	}

    @Override
    public Integer isExistQuoteorderId(Integer quoteorderId) {
        return saleorderMapper.isExistQuoteorderId(quoteorderId);
    }


    /**
     * @param saleorderId
     * @description: getLackAccountPeriodAmount.
     * @notes: add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053  .
     * @author: Tomcat.Hui.
     * @date: 2019/9/5 11:42.
     * @return: java.math.BigDecimal.
     * @throws: .
     */
    @Override
    public BigDecimal getLackAccountPeriodAmount(Integer saleorderId) {
        BigDecimal lackAccountPeriodAmount = saleorderMapper.getSaleorderLackAccountPeriodAmount(saleorderId);
        return lackAccountPeriodAmount;
    }

    /**
     * @param saleorderId
     * @description: getPeriodAmount.
     * @notes: add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053  .
     * @author: Tomcat.Hui.
     * @date: 2019/9/5 14:41.
     * @return: java.math.BigDecimal.
     * @throws: .
     */
    @Override
    public BigDecimal getPeriodAmount(Integer saleorderId) {
        return saleorderMapper.getPeriodAmount(saleorderId);
    }

    /* (non-Javadoc)
     * @see com.vedeng.order.service.SaleorderService#getSaleOrderInfoBySaleorderNo(com.vedeng.order.model.Saleorder)
     * 订单状态，付款状态，发货状态，收货状态，审核状态，生效状态，开票状态
     * ，支付方式（0线上、1线下），支付类型:(1支付宝、2微信、3银行) 接口
     */
    @Override
    public List<Saleorder> getSaleOrderInfoBySaleorderNo(ArrayList<String> orderNoList,Integer type) {
        logger.info("getSaleOrderInfoBySaleorderNo:开始");
        List<Saleorder> saleorderList = new ArrayList<>();
        saleorderList = saleorderMapper.getSaleorderBySaleorderNoList(orderNoList);
        //将订单中非直发商品的发货状态排除后,重新判断订单发货状态
		if(type!=1) {
			for (Saleorder saleorder : saleorderList) {
				List<SaleorderGoods> orderGoods = saleorderGoodsMapper.getSaleorderGoodsById(saleorder);
				Integer i = 0;//全部发货数量
				Integer j = 0;//部分发货数量
				Integer k = 0;//订单普发商品数
				Integer l = 0;//直发商品数
				Integer a = 0;//商品总数
				for (SaleorderGoods goods : orderGoods) {//排除直发商品,判断订单发货状态
					a = a + 1;
					if (goods.getDeliveryDirect().equals(ErpConst.ZERO)) {//普发商品
						k = k + 1;
						if (goods.getSku().equals("V127063")) {//运费
							goods.setDeliveryStatus(ErpConst.TWO);
						}
						if (goods.getDeliveryStatus().equals(ErpConst.TWO)) {//全部发货
							i = i + 1;
						} else if (goods.getDeliveryStatus().equals(ErpConst.ONE)) {//部分发货
							j = j + 1;
						}
					} else if (goods.getDeliveryDirect().equals(ErpConst.ONE)) {
						if (goods.getNum() != 0) {
							goods.setBuyorderStatus(2);//数量不为0的直发商品采购状态为全部采购
						}
						l = l + 1;
					}
				}
				saleorder.setGoodsList(orderGoods);
				//订单发货状态
				if (i.equals(k) || a.equals(l)) {
					saleorder.setDeliveryStatus(ErpConst.TWO);//全部发货
				} else if (j > 0) {
					saleorder.setDeliveryStatus(ErpConst.ONE);//部分发货
				} else if (i.equals(ErpConst.ZERO) && j.equals(ErpConst.ZERO)) {
					saleorder.setDeliveryStatus(ErpConst.ZERO);//未发货
				}
				logger.info("订单号为:" + saleorder.getSaleorderNo() + "全部发货数量:" + i + "," + "部分发货数量:" + j + "," + "订单普发商品数:" + k + "," + "直发商品数:" + l + "," + "商品总数:" + a);
			}
		}
        logger.info("getSaleOrderInfoBySaleorderNo:结束");
        return saleorderList;
    }

    @Override
    public Integer updateOrderDeliveryStatus(Saleorder saleorder) {
        logger.info("HC订单修改订单收货状态开始 orderNo:{}",saleorder.getSaleorderNo());
        Integer i = 0;
        Long time = DateUtil.gainNowDate();
        saleorder.setModTime(time);
        if (StringUtil.isNotEmpty(saleorder.getSaleorderNo())) {
			//全部收货
            if (saleorder.getArrivalStatus() != null && saleorder.getArrivalStatus().equals(ErpConst.TWO)) {
                saleorder.setWebTakeDeliveryTime(time);
				//全部发货
                saleorder.setDeliveryStatus(ErpConst.TWO);
                i = saleorderMapper.updateDeliveryStatusBySaleorderNo(saleorder);
            }
            Saleorder saleorder2 = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
            List<SaleorderGoods> orderGoods = saleorderGoodsMapper.getSaleorderGoodsById(saleorder2);
            for (SaleorderGoods goods : orderGoods) {
                goods.setDeliveryStatus(ErpConst.TWO);//全部发货
                goods.setArrivalStatus(ErpConst.TWO);//全部收货
                goods.setModTime(time);
                int j = saleorderGoodsMapper.updateByPrimaryKey(goods);
            }
            if (saleorder2.getDeliveryDirect() == 1) { // 直发
				syncArrivalNumForDirect(saleorder,orderGoods);
			}
        }
        logger.info("HC订单修改订单收货状态结束");
        return i;
    }

	// 同步直发订单的收货数量
	private void syncArrivalNumForDirect(Saleorder saleorder, List<SaleorderGoods> orderGoods) {
		if (null == saleorder && orderGoods.size()<=0) return ;
		long logTime = System.currentTimeMillis();

		List<Integer> saleOrderGoodsIdList = orderGoods.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
		List<SaleorderGoodsVo> bgvList = new ArrayList<>(saleOrderGoodsIdList.size());
		for (Integer i : saleOrderGoodsIdList) {
			SaleorderGoodsVo goodsVo = new SaleorderGoodsVo();
			goodsVo.setSaleorderGoodsId(i);
			bgvList.add(goodsVo);
		}

		// 更加销售商品号获取对应的售后退货数量
		List<SaleorderGoodsVo> saleorderGoodsAfterSales = afterSalesGoodsMapper.batchSaleorderAftersaleReturnGoods(bgvList);
		Map<Integer, Integer> afterNumMap =  new HashMap<>(saleorderGoodsAfterSales.size());;
		if (saleorderGoodsAfterSales.size() > 0) {
			saleorderGoodsAfterSales.forEach(item -> {
				if ( null != item.getSaleorderGoodsId() &&  item.getSaleAfterNum()>0 ){
					afterNumMap.put(item.getSaleorderGoodsId(),item.getSaleAfterNum());
				}

			});
		}

		for (SaleorderGoods goods : orderGoods) {
			if (goods.getDeliveryNum() < goods.getNum()) {
				goods.setModTime(logTime);
				if (afterNumMap.get(goods.getSaleorderGoodsId()) != null) {
					// 有售后退货
					int afterNum = afterNumMap.get(goods.getSaleorderGoodsId());
					int total = goods.getNum() - afterNum;
					goods.setDeliveryNum(total > goods.getDeliveryNum() ? total : goods.getDeliveryNum());
				} else {
					goods.setDeliveryNum(goods.getNum());
				}
				int j = saleorderGoodsMapper.updateByPrimaryKey(goods);
			}

		}

		// 同步采购销售数据
		List<BuyorderGoods> list = buyorderGoodsMapper.getListBySaleOrderGoodIds(saleOrderGoodsIdList);
		if ( list.size()>0 ){
			Set<Integer> buyOrderIds = list.stream().map(BuyorderGoods::getBuyorderId).collect(Collectors.toSet());
			for (BuyorderGoods goods : list){
				if ( goods.getArrivalNum() < goods.getNum()){
					BuyorderGoods updateGoods = new BuyorderGoods();
					updateGoods.setBuyorderGoodsId(goods.getBuyorderGoodsId());
					int total = goods.getNum() - goods.getAfterReturnNum();
					updateGoods.setArrivalNum(goods.getNum() > goods.getArrivalNum() ? total : goods.getArrivalNum());
					updateGoods.setArrivalStatus(2);
					buyorderGoodsMapper.updateByPrimaryKeySelective(updateGoods);
				}
			}
			for (Integer buyOrderId : buyOrderIds){
				Buyorder dbBuyOrder = buyorderMapper.selectByPrimaryKey(buyOrderId);
				List<BuyorderGoodsVo> buyorderGoodsList = buyorderGoodsMapper.getBuyorderGoodsListByBuyorderId(buyOrderId);
				if (dbBuyOrder.getDeliveryDirect() == 1 && (dbBuyOrder.getArrivalStatus() != 2 || dbBuyOrder.getDeliveryStatus() != 2)) {
					int buyorderArrivalStatus = 1;
					int arrivalStatus0Num = 0;
					int arrivalStatus2Num = 0;
					Set<String> specialGoods=new HashSet<>();

					try {
						specialGoods= goodsMapper.getSpecialGoodsList()
								.stream().map(Goods::getSku).collect(Collectors.toSet());
					}catch(Exception e){specialGoods=new HashSet<>();}

					for (BuyorderGoodsVo sg : buyorderGoodsList) {
						//特殊商品
						if(specialGoods.contains(sg.getSku())){
							continue;
						}
						if (ErpConst.ZERO.equals(sg.getArrivalStatus())) {
							arrivalStatus0Num++;
						}
						if (ErpConst.TWO.equals(sg.getArrivalStatus())) {
							arrivalStatus2Num++;
						}
					}
					if (arrivalStatus0Num == buyorderGoodsList.size()) {
						buyorderArrivalStatus = 0;
					}
					if (arrivalStatus2Num == buyorderGoodsList.size()) {
						buyorderArrivalStatus = 2;
					}
					Buyorder buyorderExtra = new Buyorder();
					buyorderExtra.setBuyorderId(buyOrderId);
					buyorderExtra.setArrivalStatus(buyorderArrivalStatus);
					buyorderExtra.setArrivalTime(DateUtil.sysTimeMillis());
					if (ErpConst.TWO.equals(buyorderArrivalStatus)) {
						buyorderExtra.setDeliveryStatus(2);
						buyorderExtra.setDeliveryTime(buyorderExtra.getArrivalTime());
					}
					if (ErpConst.ONE.equals(buyorderArrivalStatus) && ErpConst.ZERO.equals(dbBuyOrder.getDeliveryStatus())){
						buyorderExtra.setDeliveryStatus(1);
						buyorderExtra.setDeliveryTime(buyorderExtra.getArrivalTime());
					}
					buyorderMapper.updateByPrimaryKeySelective(buyorderExtra);
				}
			}

		}

    }

    @Override
    public List<SaleorderGoodsVo> getNewSdList(Goods goods) {
        goods.setGoodsType(1);
        List<SaleorderGoodsVo> list = saleorderGoodsMapper.getNewSdList(goods);
        return sortSaleOrderList(list);
    }


    private List<SaleorderGoodsVo> sortSaleOrderList(List<SaleorderGoodsVo> list) {
        List<SaleorderGoodsVo> list2 = new ArrayList<>();
        // 付款
        List<SaleorderGoodsVo> list3 = new ArrayList<>();
        // 创建
        List<SaleorderGoodsVo> list4 = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (SaleorderGoodsVo s : list) {
                if (s.getPaymentStatus() != null && s.getPaymentStatus() == 2) {
                    list2.add(s);
                } else {
					if (!Objects.isNull(s.getPaymentTime()) && s.getPaymentTime() != 0) {
						list3.add(s);
					} else {
						list4.add(s);
					}
                }
            }
            Collections.sort(list2, new Comparator<SaleorderGoodsVo>() {
                @Override
                public int compare(SaleorderGoodsVo o1, SaleorderGoodsVo o2) {
					Long paymentTime1 = o1.getPaymentTime();
					if(paymentTime1 == null){
						paymentTime1 = 0L;
					}
					Long paymentTime2 = o2.getPaymentTime();
					if(paymentTime2 == null){
						paymentTime2 = 0L;
					}
					Long time = paymentTime1 - paymentTime2;
                    if (time > 0) {
                        return 1;
                    } else if (time < 0) {
                        return -1;
                    }
                    return 0;
                }
            });
			Collections.sort(list3, new Comparator<SaleorderGoodsVo>() {
				@Override
				public int compare(SaleorderGoodsVo o1, SaleorderGoodsVo o2) {
					Long paymentTime1 = o1.getPaymentTime();
					if(paymentTime1 == null){
						paymentTime1 = 0L;
					}
					Long paymentTime2 = o2.getPaymentTime();
					if(paymentTime2 == null){
						paymentTime2 = 0L;
					}
					Long time = paymentTime1 - paymentTime2;
					if (time > 0) {
						return 1;
					} else if (time < 0) {
						return -1;
					}
					return 0;
				}
			});
            Collections.sort(list4, new Comparator<SaleorderGoodsVo>() {
                @Override
                public int compare(SaleorderGoodsVo o1, SaleorderGoodsVo o2) {
					Long addTime1 = o1.getAddTime();
					if(addTime1 == null){
						addTime1 = 0L;
					}
					Long addTime2 = o2.getAddTime();
					if(addTime2 == null){
						addTime2 = 0L;
					}
					Long time = addTime1 - addTime2;
                    if (time > 0) {
                        return 1;
                    } else if (time < 0) {
                        return -1;
                    }
                    return 0;
                }
            });
            list.clear();
            list.addAll(list2);
            list.addAll(list3);
            list.addAll(list4);
        }
        return list;
    }

    @Override
    public List<SaleorderGoods> getSaleorderGoods(Saleorder saleorder) {
        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsById(saleorder);
        return saleorderGoods;
    }


//    @Override
//    public Saleorder getWebAccountId(Integer saleorderId) {
//        return saleorderMapper.getWebAccountId(saleorderId);
//    }
	@Override
	public List<Integer> getSaleOrderGoodsIdListByUserId(Integer proUserId) {
		return saleorderGoodsMapper.getSaleOrderGoodsIdListByUserId(proUserId);
	}

    @Override
    public List<SaleorderGoods> getSaleGoodsNoOutNumList(Integer saleorderId) {
        List<SaleorderGoods> list = saleorderGoodsMapper.getSaleGoodsNoOutNumList(saleorderId);
        return list;
    }


    @Override
    public Saleorder updateisOpenInvoice(Saleorder saleorder, List<SaleorderGoods> saleorderGoodsList) {
        try {
            Map<Integer, Map<String, Object>> invoiceNums = invoiceService.getInvoiceNums(saleorder);
            // changed by Tomcat.Hui 2019/12/2 21:13 .Desc: VDERP-1325 分批开票 APPLY_NUM改为申请中的数量. start
            BigDecimal applyNum = BigDecimal.ZERO;//有效的已申请数量
            Integer num = 0;//商品总数

            // add by Tomcat.Hui 2020/2/6 6:40 下午 .Desc: VDERP-1882 以订单商品总金额判断 而非订单商品总数量. start
			Map<Integer,BigDecimal> invoiceOccupiedAmount = invoiceService.getInvoiceOccupiedAmount(saleorder);
			BigDecimal alreadyTotalAmount = BigDecimal.ZERO;
			BigDecimal realAmount = BigDecimal.ZERO;
			for (SaleorderGoods g : saleorderGoodsList) {
				BigDecimal goodsAmount = BigDecimal.ZERO;

				if (saleorder.getOrderType().equals(5)) {
					goodsAmount = g.getMaxSkuRefundAmount().subtract(g.getAfterReturnAmount());
				} else {
					Integer goodsNum = g.getNum() - g.getAfterReturnNum();
					goodsAmount = g.getPrice().multiply(new BigDecimal(goodsNum));
				}

				BigDecimal alreadyAmount = invoiceOccupiedAmount.containsKey(g.getSaleorderGoodsId()) ? invoiceOccupiedAmount.get(g.getSaleorderGoodsId()) : BigDecimal.ZERO;
				alreadyTotalAmount = alreadyTotalAmount.add(alreadyAmount);
				realAmount = realAmount.add(goodsAmount);
				//logger.info("订单ID：" + saleorder.getSaleorderNo() + " 订单商品: " + g.getSku() + " 实际金额：" + goodsAmount + " 已开票金额：" + alreadyAmount);
			}

            // add by Tomcat.Hui 2020/2/6 6:40 下午 .Desc: VDERP-1882 以订单商品总金额判断 而非订单商品总数量. end

			// 原有规则:1允许开票2存在开票申请待审核-不允许;;;;:3允许提前开票，4存在提前开票待审核-不允许::::5全部开票----0全部售后
            //现在 当为2和4 符合所有有效的开票数量小于订单商品数也可以
			logger.info("开票按钮：{}{}",saleorder.getSaleorderNo(),  saleorder.getIsOpenInvoice());
            if (alreadyTotalAmount.compareTo(realAmount) < 0) {
                if (saleorder.getIsOpenInvoice() != null) {
                    if (saleorder.getIsOpenInvoice().equals(2)) {
                        saleorder.setIsOpenInvoice(1);
                    } else if (saleorder.getIsOpenInvoice().equals(4)) {
                        saleorder.setIsOpenInvoice(3);
                    }
                }
            }
			logger.info("开票按钮：{}{}",saleorder.getSaleorderNo(),  saleorder.getIsOpenInvoice());

			// changed by Tomcat.Hui 2019/12/2 21:13 .Desc: VDERP-1325 分批开票 APPLY_NUM改为申请中的数量. end

        } catch (Exception e) {
            logger.error("计算是否允许开票出现异常 error", e);
        }
        return saleorder;
    }

    @Override
    public ResultInfo updateOrderStatusByOrderNo(Saleorder saleorder) {
        int i = saleorderMapper.updateOrderStatusByOrderNo(saleorder);
        if (i == 1) {
            Saleorder s1 = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
            //调用库存服务
            //关闭订单
            s1.setOperateType(StockOperateTypeConst.COLES_ORDER);
            warehouseStockService.updateOccupyStockService(s1, 0);
			logicalSaleorderChooseServiceImpl.closeOrdersynchronizeStockData(s1);
            logger.info("updateOrderStatusByOrderNo关闭成功,订单号:{}",saleorder.getSaleorderNo());
            return new ResultInfo(0, "操作成功");
        } else {
            return new ResultInfo();
        }
    }

	@Override
	public Integer queryOutBoundQuantity(SaleorderVo saleorderVo) {
    	saleorderVo.setType(1);
		List<SaleorderGoods> saleorderGoodslist = saleorderGoodsMapper.getActionGoodsInfo(saleorderVo);
		if(!CollectionUtils.isEmpty(saleorderGoodslist)) {
			Integer deliveryNum = 0;
			for (SaleorderGoods saleorderGoods : saleorderGoodslist) {
				deliveryNum += saleorderGoods.getDeliveryNum();
			}
			return deliveryNum;
		}
		return 0;
	}

	@Override
	public Saleorder getsaleorderId(String saleorderNo) {
		return saleorderMapper.getSaleOrderId(saleorderNo);
	}
    @Override
	public void updateOrderdeliveryDirect(Saleorder s) {
        List<SaleorderGoods> saleorderGoodslist = saleorderGoodsMapper.getSaleorderGoodsById(s);
        for (SaleorderGoods saleorderGoods : saleorderGoodslist) {
            if (saleorderGoods.getDeliveryDirect().equals(1)) {
                s.setDeliveryDirect(1);
                break;
            } else {
                s.setDeliveryDirect(0);
            }
        }
        saleorderMapper.updateDeliveryStatusBySaleorderNo(s);

    }


	@Override
	public Spu getSpu(String sku) {
		return saleorderGoodsMapper.getSku(sku);
	}

	@Override
	public CoreSpuGenerate getSpuBase(Integer skuId) {
		return saleorderGoodsMapper.getSpuBase(skuId);
	}

	@Override
	public List<SaleorderGoods> statisticalOrderGoods(SaleorderGoods saleorderGoods) {

		return saleorderGoodsMapper.statisticalOrderGoods(saleorderGoods);
	}

	@Override
	public BigDecimal transactionPrice(String sku) {
		return saleorderGoodsMapper.transactionPrice(sku);
	}

	@Override
	public List<Integer> getSaleOrderGoodsIdListByUserIds(List<User> userList) {
		return saleorderGoodsMapper.getSaleOrderGoodsIdListByUserIds(userList);
	}
	//第一次物流改变评论
	@Override
	public int updateLogisticsComments(Integer saleorderId, String s) {
		return saleorderMapper.updateLogisticsComments(saleorderId,s);
	}

	@Override
	public Saleorder getWebAccountId(Integer saleorderId) {
		return saleorderMapper.getWebAccountId(saleorderId);
	}



     @Override
	public Integer getContractReturnOrderCount(SaleorderContract saleOrderContract,
											   String searchType) {

		Map<String, Object> paraMap = new HashMap<>();

		paraMap.put("saleOrderContract", saleOrderContract);


		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<SaleorderContract> list = null;
		Integer listCount = 0;
		if ("1".equals(searchType)) {

			listCount = saleorderMapper.getContractReturnOrderListCount(paraMap);
		} else {

			listCount = saleorderMapper.getContractReturnOrderNoqualityListCount(paraMap);
		}

		return listCount;
	}


	@Override
	public BigDecimal getContractReturnOrderCount(SaleorderContract saleOrderContract) {
		Map<String, Object> paraMap = new HashMap<>();
		paraMap.put("saleOrderContract", saleOrderContract);
		return saleorderMapper.getContractReturnOrderListRealityMoney(paraMap);
	}

	/*校验锁的状态*/
	@Override
	public void updateLockedStatus(Integer saleorderId) {
		saleorderMapper.updateLockedStatus(saleorderId);
	}

	/**
	* @Description: 更新收发货和发票状态
	* @Param:
	* @return:
	* @Author: addis
	* @Date: 2020/2/5
	*/

	public void updateState(Saleorder saleorder){
// 获取订单产品信息
		List<SaleorderGoods> goodsList =
				saleorderGoodsMapper.getSaleorderGoodsById(saleorder);
		List<ExpressDetail> expresseList = expressService.getSEGoodsNum(goodsList.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList()));

		if (null != goodsList) {
			// 销售商品只要有一个确认收货，当前订单中的特殊商品就自动改成已发货，已收货
			boolean arrStatus = false;
			Integer arrivalUserId = 0;
			List<String> virtureSku = Arrays.asList("V127063", "V251526", "V256675", "V253620", "V251462");
			for (SaleorderGoods sgs : goodsList) {
//				if(sgs.getGoodsId() == 127063 || sgs.getGoodsId() == 251526 || sgs.getGoodsId() == 256675
//						|| sgs.getGoodsId() == 253620 || sgs.getGoodsId() == 251462 || sgs.getGoodsId() == 140633){
//					continue;
//				}
				//第n+1处快递签收过滤虚拟商品
				if((sgs.getIsVirtureSku() == null || sgs.getIsVirtureSku() == 0) && !virtureSku.contains(sgs.getSku())){
					continue;
				}
				SaleorderGoods newGoods = new SaleorderGoods();
				Integer agvNum=sgs.getNum()-sgs.getAfterReturnNum(); //商品数量-售后已完成得数量
				if(expresseList!=null&&expresseList.size()>0){
					for(ExpressDetail expressDetail:expresseList){
						if(sgs.getSaleorderGoodsId().equals(expressDetail.getSaleOrderGoodsId())){
							logger.info("完结后发货数量"+expressDetail.getSendNum()+"=============商品数量"+agvNum);
							if(expressDetail.getSendNum().equals(0)){
								newGoods.setDeliveryStatus(0);//未发货
							}else if(0<expressDetail.getSendNum()&&expressDetail.getSendNum()<agvNum){
								newGoods.setDeliveryStatus(1);//部分发货
							}else{
								newGoods.setDeliveryStatus(2);//全部发货
							}
							/*Express express = new Express();
							express.setOrderGoodsId(sgs.getSaleorderGoodsId());
							Express e = expressMapper.getSEGoodsNum(express);*/
							if (expressDetail.getArriveNum() == 0) {
								// 未签收
								newGoods.setArrivalStatus(0);
							} else if (0<expressDetail.getArriveNum()&&expressDetail.getArriveNum()<agvNum) {
								newGoods.setArrivalStatus(1);// 部分签收
							} else {
								// 全部签收
								newGoods.setArrivalStatus(2);

							}
							if (expressDetail.getSendNum()>0&&( newGoods.getDeliveryStatus()==1||newGoods.getDeliveryStatus()==2)) {
								arrStatus = true;
							}
							arrivalUserId = sgs.getArrivalUserId();

							newGoods.setSaleorderGoodsId(sgs.getSaleorderGoodsId());
							newGoods.setArrivalUserId(sgs.getArrivalUserId());
							newGoods.setArrivalTime(sgs.getArrivalTime());

							newGoods.setDeliveryTime(sgs.getArrivalTime());

							saleorderGoodsMapper.updateByPrimaryKeySelective(newGoods);
						}
					}
				}

			}
			if (arrStatus && saleorder.getSaleorderId() > 0) {
				// 查询销售单下的所有特殊商品
				SaleorderGoods sgs = new SaleorderGoods();
				sgs.setSaleorderId(saleorder.getSaleorderId());
				sgs.setArrivalStatus(2);
				sgs.setArrivalUserId(arrivalUserId);
				sgs.setArrivalTime(DateUtil.sysTimeMillis());
				sgs.setDeliveryStatus(2);
				sgs.setDeliveryTime(DateUtil.sysTimeMillis());
				saleorderGoodsMapper.updateTsSaleorderGoodsByParam(sgs);

			}else{
				// 查询销售单下的所有特殊商品
				SaleorderGoods sgs = new SaleorderGoods();
				sgs.setSaleorderId(saleorder.getSaleorderId());
				sgs.setArrivalStatus(0);
				sgs.setArrivalUserId(arrivalUserId);
				sgs.setArrivalTime(DateUtil.sysTimeMillis());
				sgs.setDeliveryStatus(0);
				sgs.setDeliveryTime(DateUtil.sysTimeMillis());
				saleorderGoodsMapper.updateTsSaleorderGoodsByParam(sgs);
			}

		}

		List<SaleorderGoods> sgList = saleorderGoodsMapper.getSaleorderGoodsNoSH(saleorder);
		Integer saleorderArrivalStatus = 1;
		Integer saleorderDeliveryStatus = 1;
		Integer arrivalStatus0Num = 0;// 未到货数量
		Integer arrivalStatus2Num = 0;// 全部到货数量
		Integer deliveryStatus0Num = 0;// 未发货数量
		Integer deliverytatus2Num = 0;// 全发货数量

		Integer size = sgList.size();
		for (SaleorderGoods sg : sgList) {
			// 判断直发商品的收货状态(直发商品的收货状态 == 普发商品的发货状态)
			if (sg.getDeliveryDirect() == 1
					|| (sg.getGoodsId() == 127063 || sg.getGoodsId() == 251526 || sg.getGoodsId() == 256675
					|| sg.getGoodsId() == 253620 || sg.getGoodsId() == 251462 || sg.getGoodsId() == 140633)) {

				//未收货
				if (sg.getArrivalStatus() == 0) {
					arrivalStatus0Num++;
					deliveryStatus0Num++;
				}

				//已收货
				if (sg.getArrivalStatus() == 2) {
					arrivalStatus2Num++;
					deliverytatus2Num++;
				}
			}
			// 判断普发商品收发货状态
			else if (sg.getGoodsId() != 127063 && sg.getGoodsId() != 251526 && sg.getGoodsId() != 256675
					&& sg.getGoodsId() != 253620 && sg.getGoodsId() != 251462 && sg.getGoodsId() != 140633) {
				/******************** 判断收货状态 **********************/
				Express express = new Express();
				express.setOrderGoodsId(sg.getSaleorderGoodsId());
				Express e = expressMapper.getSEGoodsNum(express);
				if (e.getAllnum() == 0) {
					size--;
					continue;
					// arrivalStatus2Num++;
				} else {
					if (e.getFnum() == 0) {
						// 未签收
						arrivalStatus0Num++;
					} else if (e.getAllnum() > e.getFnum()) {
						// 部分签收
					} else {
						// 全部签收
						arrivalStatus2Num++;
					}
				}
				/********************** 判断发货状态 **************************/
				if (sg.getDeliveryStatus() == 0) {
					deliveryStatus0Num++;
				}
				if (sg.getDeliveryStatus() == 2) {
					deliverytatus2Num++;
				}
			}

		}
		if (arrivalStatus0Num.intValue() == size) {
			saleorderArrivalStatus = 0;
		}
		if(size!=0){
			if (arrivalStatus2Num.intValue() == size) {
				saleorderArrivalStatus = 2;
			}
		}

		if (deliveryStatus0Num.intValue() == size) {
			saleorderDeliveryStatus = 0;
		}
		if(size!=0){
			if (deliverytatus2Num.intValue() == size) {
				saleorderDeliveryStatus = 2;
			}
		}


		Saleorder saleorderExtra = new Saleorder();
		saleorderExtra.setSaleorderId(saleorder.getSaleorderId());
		saleorderExtra.setArrivalStatus(saleorderArrivalStatus);
		saleorderExtra.setArrivalTime(DateUtil.sysTimeMillis());
		saleorderExtra.setDeliveryStatus(saleorderDeliveryStatus);
		saleorderExtra.setDeliveryTime(DateUtil.sysTimeMillis());
		saleorderMapper.updateByPrimaryKeySelective(saleorderExtra);
	}



	/**
	* @Description: 更新发票状态
	* @Param:
	* @return:
	* @Author: addis
	* @Date: 2020/2/5
	*/

	public void InvoiceState(Integer SaleorderId,Integer userId){
		Invoice invoice=new Invoice();
		invoice.setRelatedId(SaleorderId);
		invoice.setUpdater(userId);
		invoice.setModTime(DateUtil.gainNowDate());
		// 查询已开票金额
		BigDecimal openInvoiceAmount =
				invoiceMapper.getSaleOpenInvoiceAmount(invoice.getRelatedId());
		// 修改销售订单发票状态
		if(openInvoiceAmount==null){
			invoice.setAmountCount(new BigDecimal(0));
		}else{
			invoice.setAmountCount(openInvoiceAmount);
		}

		//logger.info("订单号: " + saleorderParam.getSaleorderNo() + " 开始判断是否全部开票,已开票金额: " + openInvoiceAmount );

		// add by Tomcat.Hui 2019/12/16 13:40 .Desc: VDERP-1686 开票申请是否开完票，改为根据开票金额与开票申请的金额进行比较来判断. start
		//加入订单类型,SQL里有用到
		Saleorder order = saleorderMapper.getSaleOrderById(invoice.getRelatedId());
		invoice.setOrderType(order.getOrderType());
		// add by Tomcat.Hui 2019/12/16 13:40 .Desc: VDERP-1686 开票申请是否开完票，改为根据开票金额与开票申请的金额进行比较来判断. end
		invoiceMapper.updateSaleInvoiceStatus(invoice);// 多条sql返回结果不确定
	}


	@Override
	public Map<String, Object> getFlowerPrintOutListPage(HttpServletRequest request, Saleorder saleorder, Page page) {
		String[] split = printOutTrader.split(",");
		List<String> traderNameList = new ArrayList<>();
		for (String traderName : split) {
			if(!StringUtils.isEmpty(traderName))
			traderNameList.add(traderName);
		}
		//获取宝石花客户id
    	List<Integer> traderIdList = traderMapper.getFlowerTraderId(traderNameList);
		saleorder.setTraderIdList(traderIdList);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("page", page);
		map.put("saleorder", saleorder);
		List<Saleorder> list = saleorderMapper.getFlowerPrintOutListPage(map);
		map.put("saleorderList",list);
		return map;
	}

	/**
	 * @Description: 待支付订单发送消息
	 * @Param: [saleorders, sTempMap]
	 * @return: void
	 * @Author: addis
	 * @Date: 2019/9/25
	 */
	@Override
	public void waitSendOrderFor(Saleorder saleorders, Map sTempMap, User user) {
		//logger.info("微信消息公众号待支付订单发送消息begin .......{}", JSON.toJSONString(saleorders));
		if (null == saleorders) {
			return;
		}
		Integer orderType = saleorders.getOrderType();
		// 电子签章：除集采线下订单 所有线上订单 (JCF、JCO、BD、HC、DH 剔除) 自动发短信给采购人员
		if (!OrderConstant.ORDER_TYPE_JCF.equals(orderType) && !OrderConstant.ORDER_TYPE_JCO.equals(orderType)
				&& !OrderConstant.ORDER_TYPE_BD.equals(orderType) && !OrderConstant.ORDER_TYPE_HC.equals(orderType)
				&& !OrderConstant.ORDER_TYPE_DH.equals(orderType)) {
			try {
				//如果没有确认 发短信确认
				if (saleorders.getConfirmStatus() == null || saleorders.getConfirmStatus() == 0) {
					// VDERP-9217 订单发货短信&线下转线上短信屏蔽指定公司
					boolean contains = false;
					if (!CollectionUtils.isEmpty(notSendMessageTranderIds)) {
						logger.info("traderIds:{}",JSON.toJSONString(notSendMessageTranderIds));
						notSendMessageTranderIds = notSendMessageTranderIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
						contains = notSendMessageTranderIds.contains(saleorders.getTraderId());
					}
					// TODO: 2022/10/14 VDERP-12290【审计】订单确认、签回单功能修改（隐藏和取消）
					//if (!contains) {
					//	boolean sendTplSms = sendMes(saleorders);
					//	if (sendTplSms) {
					//		insertConFirmRecord(saleorders.getSaleorderNo(), ConfirmRecordSendTypeEnum.SYSTEM_SEND.getCode(), user);
					//	}
					//}
					// VDERP-9217 订单发货短信&线下转线上短信屏蔽指定公司
				}
			} catch (Exception e) {
				logger.error("订单生效短信发送失败", e);
			}
		}

		//必须是BD JX DH VS 订单
		if (OrderConstant.ORDER_TYPE_BD.equals(orderType)) {
			sendWx(saleorders, orderType, sTempMap);
			logger.info("贝登微信发送订单签收模板消息 | end .......");
		}
	}

	@Autowired
	private SaleOrderDataApiService  saleOrderDataApiService;

	@Override
	public void refreshSaleOrderBuyPrice(Integer saleOrderId) {
		List<SaleorderGoods> goodsList =  saleorderMapper.getSaleorderGoodsById(saleOrderId);
		if(CollectionUtils.isEmpty(goodsList)){
			log.warn("未获取到订单明细，不插入待刷新的采购价");
		}
		// 使用Stream API进行转换
		List<SaleOrderGoodsMaoLiBuyPriceDto> initList = goodsList.stream()
				.map(goods -> new SaleOrderGoodsMaoLiBuyPriceDto(goods.getSaleorderId(), goods.getSku()))
				.collect(Collectors.toList());
		// 输出结果
		saleOrderDataApiService.initSaleOrderGoodsMaoli(initList);
	}

	//发短信
	public boolean sendMes(Saleorder saleorder){
		boolean sendTplSms=false;
		String tplId=""; //模板Id
		Trader trader = traderMapper.selectByPrimaryKey(saleorder.getTraderId());

		//如果不是集采 和集团业务部 发送短信
		if(trader!=null && trader.getBelongPlatform()!=null && trader.getBelongPlatform()!=4 && trader.getBelongPlatform()!=6){
			//贝登医疗
			if(BelongPlatformEnum.BD.getBelong().intValue() == trader.getBelongPlatform().intValue() ){
				tplId="JSM40187-0073";  //贝登医疗短信模板Id
			}else{
				tplId="JSM40187-0074";//科研购，医械购,其他短信模板Id
			}

			String content = "@1@=" + (String) saleorder.getTraderName() + ",@2@=" + saleorder.getSaleorderNo() + ",@3@="+confirmOrderUrl + "e/o/" + saleorder.getSaleorderNo();
			//模板要修改
			sendTplSms =  smsService.sendTplSms(saleorder.getTraderContactMobile(), tplId, content);
			if (sendTplSms) {
				logger.info("订单生效短信通知成功" + saleorder.getSaleorderNo() + "\t" + saleorder.getTraderContactMobile());
			}
		}


		return sendTplSms;
	}

	//订单发送短信通知成功时，插入T_CONFIRM_RECORD表
	public void insertConFirmRecord(String saleorderNo,Integer sendType,User user){
		ConfirmRecordDto confirmRecord = new ConfirmRecordDto();
		confirmRecord.setBusinessNo(saleorderNo);
		confirmRecord.setBusinessType(ConfirmRecordBusinessTypeEnum.ORDER_CONFIRM.getCode());//订单确认
		confirmRecord.setCreator(user.getUserId());
		confirmRecord.setAddTime(new Date());
		confirmRecord.setUpdater(user.getUserId());
		confirmRecord.setModTime(new Date());
		confirmRecord.setSendType(sendType);
		confirmRecord.setSendTime(new Date());
		confirmRecord.setSendMethodType("0");
		confirmRecord.setConfirmStatus(ErpConst.ZERO);
		confirmRecordApiService.insertConFirmRecord(confirmRecord);

	}

	public void sendWx(Saleorder saleorders,Integer orderType,Map sTempMap){
			// 贝登消息推送
			ReqTemplateVariable reqTemp = new ReqTemplateVariable();
			if(OrderConstant.ORDER_TYPE_SALE.equals(orderType)){
				saleorders.setCreateMobile(saleorders.getTraderContactMobile());
			}
			logger.info("waitSendOrderFor | traderConMobile:{}", saleorders.getCreateMobile());

			// 订单客户联系人
			reqTemp.setMobile(saleorders.getCreateMobile());
			//reqTemp.setMobile("***********");
			// 模板消息数字字典Id
			reqTemp.setTemplateId(orderPayMiddle);
			reqTemp.setJumpUrl(mjxPage+"?orderNo="+saleorders.getSaleorderNo());


			TemplateVar first = new TemplateVar();
			String firstStr = getConfigStringByDefault("尊敬的客户，您的订单已生效，等待支付中：", SysOptionConstant.WECHAT_TEMOLATE_ORDER_PAY_MIDDLE);
			logger.info("获取数据配置 | firstStr：{} ", firstStr);
			first.setValue(firstStr + "\r\n");

			TemplateVar keyword1 = new TemplateVar();
			TemplateVar keyword2 = new TemplateVar();
			TemplateVar keyword3 = new TemplateVar();
			TemplateVar keyword4 = new TemplateVar();
			TemplateVar keyword5 = new TemplateVar();

			TemplateVar remark = new TemplateVar();
			String remarkStr = getConfigStringByDefault("感谢您对贝登的支持与信任，如有疑问请联系：4006-999-569", SysOptionConstant.WECHAT_TEMPLATE_BEDENG_REMARK);
			remark.setValue(remarkStr);

			if (null != sTempMap) {
				String saleorderAllNum =String.valueOf(sTempMap.get("saleorderAllNum"));
				// 商品详情
				keyword1.setValue((String) sTempMap.get("saleorderFirstGoodsName") + "等 " + saleorderAllNum + "个商品");
				// 订单金额
				keyword2.setValue(String.valueOf(sTempMap.get("totalAmount")) + "元");
				// 订单编号
				keyword3.setValue(saleorders.getSaleorderNo());

				keyword4.setValue(String.valueOf(sTempMap.get("validTime")));
				keyword5.setValue("请尽快支付"+ "\r\n");

				reqTemp.setFirst(first);
				reqTemp.setKeyword1(keyword1);
				reqTemp.setKeyword2(keyword2);
				reqTemp.setKeyword3(keyword3);
				reqTemp.setKeyword4(keyword4);
				reqTemp.setKeyword5(keyword5);
				reqTemp.setRemark(remark);
				// 发送 微信服务 消息模板 订单签收

				sendTemplateMsg(vxService+"/wx/wxchat/send", reqTemp);
				//	sendTemplateMsg("http://172.16.3.123:8280/wx/wxchat/send",reqTemp);
			}
			logger.info("贝登微信发送订单签收模板消息 | end .......");
		}


	@Override
	public OrderData waitMjxSendOrderFor(Saleorder saleorders,int orderStatus) {
		OrderData orderData = new OrderData();
		if (saleorders.getOrderType()==1) {
			logger.info("mjx订单完成状态推送开始");
			orderData.setOrderNo(saleorders.getSaleorderNo());
			orderData.setOrderStatus(orderStatus);

			WebAccount web = webAccountMapper.getWenAccountInfoByMobile(saleorders.getCreateMobile());
			if(null!=web&&web.getWebAccountId()!=null){
				orderData.setAccountId(web.getWebAccountId());
				orderData.setSsoAccountId(web.getSsoAccountId());
			}
		}
		return orderData;
	}
	/**
	 *   根据手机 获取6月16日 0 点以后的订单
	 * @param mobile
	 * @return
	 */
	@Override
	public List<Saleorder> selectLatestSaleOrderByMobile(String mobile) {
		return saleorderMapper.selectLatestSaleOrderByMobile(mobile);
	}

	@Override
	public void updateBDLogisticsStatus(Saleorder saleOrder) {
		Saleorder sv = saleorderMapper.getSaleOrderById(saleOrder.getSaleorderId());
		String key = ErpConst.BDEXPRESSKEY + saleOrder.getSaleorderId();
		boolean lock = redisUtils.tryGetDistributedLock(key, UUID.randomUUID().toString(), 5000);
		logger.info("updateBDLogisticsStatus saleorderId:{} ,locck:{}",sv.getSaleorderId(),lock);
		 try {
		if ((sv.virtualBdOrder()|| sv.getOrderType().equals(1)) && sv.getArrivalStatus().equals(2) ) {
			logger.info("updateBDLogisticsStatus saleorderId:{} 开始发送 ",sv.getSaleorderId());
			saleorderSyncService.syncSaleorderStatus2Mjx(saleOrder.getSaleorderId()
					, PCOrderStatusEnum.FINISH, SaleorderSyncEnum.UPDATE_BDLOGISTICS_STATUS);
		}
		 } catch (Exception e) {
			 logger.error("updateBDLogisticsStatus saleorderId:{} ChangeEditSaleOrder  error  ",sv.getSaleorderId(),e);
		 }finally {
			 String value = redisUtils.get(key);
			 redisUtils.releaseDistributedLock(key, value);
		 }
	}

	@Override
	public void updateMaxSkuRefundAmount(Saleorder saleorder) {
		List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsById(saleorder);
		for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
			SaleorderGoods sg = new SaleorderGoods();
			sg.setSaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
			sg.setMaxSkuRefundAmount(saleorderGoods.getPrice().multiply(new BigDecimal(saleorderGoods.getNum())));
			log.info("更新maxSkuRefundAmount:{}",JSON.toJSON(sg));
			saleorderGoodsMapper.updateByPrimaryKeySelective(sg);
		}
	}

	@Override
	public void updateRealPrice(Saleorder updateSaleorder) {
		List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsById(updateSaleorder);
		for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
			SaleorderGoods sg=new SaleorderGoods();
			sg.setSaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
			sg.setRealPrice(saleorderGoods.getPrice());
			saleorderGoodsMapper.updateByPrimaryKeySelective(sg);
		}
	}


	@Override
	public ResultInfo<Object> checkTraderFirstOrder(Integer traderId) {
		ResultInfo<Object> resultInfo = new ResultInfo<>();
		Trader trader = traderMapper.getTraderByTraderId(traderId);
		if(trader == null){
			resultInfo.setMessage("查询此id:"+traderId+"无结果");
			return resultInfo;
		}
		//获取实付金额大于0的有效状态订单
		List<Saleorder> list = saleorderMapper.getSaleorderRealAmountByTraderId(traderId);
		if(CollectionUtils.isEmpty(list)){
			resultInfo.setCode(0);
			resultInfo.setMessage("此客户为首单");
			return resultInfo;
		}
		resultInfo.setMessage("此客户不为首单");
		return resultInfo;
	}

	@Override
	public ResultInfo<Object> getCouponOrderInfo(Integer couponId) {
		ResultInfo<Object> resultInfo = new ResultInfo<>();
		logger.info("getCouponOrderInfo id:{}",couponId);
		//统计数据
		CouponOrderData couponOrderData = saleorderMapper.getCouponOrderDataByCouponId(couponId);
		//明细数据
//		List<CouponOrderDetailData> list = saleorderMapper.getCouponOrderDataDetailListByCouponId(couponId);
		if(couponOrderData == null){
			resultInfo.setCode(0);
			resultInfo.setMessage("查询无结果");
			return resultInfo;
		}
//		couponOrderData.setCouponOrderDetailDataList(list);
		logger.info("getCouponOrderInfo id:{},info:{}",couponId,couponOrderData.toString());
		resultInfo.setData(couponOrderData);
		resultInfo.setMessage("查询成功");
		resultInfo.setCode(0);
		return resultInfo;
	}

	@Override
	public ResultInfo<Object> getCouponOrderDetailInfo(List<String> couponCodeList) {
		ResultInfo<Object> resultInfo = new ResultInfo<>();
		List<CouponOrderDetailData> list = saleorderMapper.getCouponOrderDataDetailListBycouponCodeList(couponCodeList);
		resultInfo.setCode(0);
		resultInfo.setMessage("查询成功");
		resultInfo.setData(list);
		return resultInfo;
	}

	/**
	 * 处理优惠券信息
	 * @param saleOrderId
	 * @param couponInfo
	 * @param myCouponSkuSubResponse
	 */
	@Override
	@Transactional
	public void dealWithOrderGoodsCouponPrice(Integer saleOrderId,
											  CouponInfo couponInfo,
                                              MyCouponSkuSubResponse myCouponSkuSubResponse) {

		if(CollectionUtils.isEmpty(myCouponSkuSubResponse.getSkuCouponList())){
			return;
		}

        //商品中所有的skuNos
        List<SaleorderGoods> saleorderGoodsList = this.saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleOrderId);
        Map<String,SaleorderGoods> skuNoAndGoodsMap = saleorderGoodsList.stream()
                .collect(Collectors.toMap(k -> k.getSku(),v -> v, (k,v)-> v));

		//更新优惠券信息，如果没有就新增，
		for(MyCouponSkuSubDTO couponSku : myCouponSkuSubResponse.getSkuCouponList()){

            SaleorderGoods saleorderGoodsDb = skuNoAndGoodsMap.get(couponSku.getSkuNo());

			SaleorderGoods saleorderGoods = new SaleorderGoods();
			saleorderGoods.setSaleorderGoodsId(saleorderGoodsDb.getSaleorderGoodsId());

			//计算优惠后的单价
			BigDecimal couponPrice = null;

			if(couponSku.getIsUseCoupon() == 1){
				couponPrice = couponSku.getCouponTotalAmount().divide(BigDecimal.valueOf(couponSku.getSkuCounts()),2, BigDecimal.ROUND_HALF_UP);
			}else{
				couponPrice = saleorderGoodsDb.getRealPrice();
			}
			saleorderGoods.setPrice(couponPrice);
			saleorderGoods.setModTime(System.currentTimeMillis());

            //原始的总金额
            BigDecimal originalTotalAmout = saleorderGoodsDb.getRealPrice().multiply(BigDecimal.valueOf(saleorderGoodsDb.getNum()));

			//是否使用优惠券
			if(couponSku.getIsUseCoupon() == 1){
				String goodsComment = "已使用满减券，原价"+originalTotalAmout+ "，" +
						"优惠了"+(originalTotalAmout.subtract(couponSku.getCouponTotalAmount()))+"元；"
						+"有效期截止到" + couponInfo.getEffevtiveEndTime();

				saleorderGoods.setGoodsComments(goodsComment);
				saleorderGoods.setMaxSkuRefundAmount(couponSku.getCouponTotalAmount());
			}else{
				saleorderGoods.setGoodsComments("");
				saleorderGoods.setMaxSkuRefundAmount(originalTotalAmout);
			}

			this.saleorderGoodsMapper.updateByPrimaryKeySelective(saleorderGoods);
		}

		BigDecimal totalCouponAmout = BigDecimal.ZERO;

		List<SaleorderGoods> goodLists = this.saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleOrderId);
		for(SaleorderGoods good : goodLists){
			totalCouponAmout = totalCouponAmout.add(good.getMaxSkuRefundAmount());
		}

		//更新sku的总金额
		Saleorder saleorder = new Saleorder();
		saleorder.setSaleorderId(saleOrderId);
		saleorder.setTotalAmount(totalCouponAmout);
		//此处增加,isCoupons =1 ,已使用优惠券
		/*saleorder.setIsCoupons(ErpConst.ONE);*/
		saleorderMapper.updateByPrimaryKeySelective(saleorder);
	}

	private void setCouponInfo(SaleorderCoupon saleorderCoupon,CouponInfo couponInfo){
		saleorderCoupon.setCouponId(Integer.valueOf(couponInfo.getCouponId()));
		saleorderCoupon.setCouponCode(couponInfo.getCouponCode());
		saleorderCoupon.setDenomination(couponInfo.getDenomination());
		saleorderCoupon.setUseThreshold(couponInfo.getUseThreshold());
		saleorderCoupon.setLimitTypeStr(couponInfo.getLimitTypeStr());
		saleorderCoupon.setEffevtiveStartTime(couponInfo.getEffevtiveStartTime());
		saleorderCoupon.setEffevtiveEndTime(couponInfo.getEffevtiveEndTime());
	}

	/**
	 * 清空订单的优惠券信息
	 * @param saleOrderId
	 */
	@Override
	@Transactional
	public void clearCoupon(Integer saleOrderId) {

		//清空优惠券的信息
		logger.info("删除优惠券信息---->saleOrderId:" + saleOrderId);
		this.saleorderCouponMapper.deleteBySaleOrderId(saleOrderId);

		List<SaleorderGoods> saleorderGoodsList = this.saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleOrderId);
		if(CollectionUtils.isEmpty(saleorderGoodsList)){
			return;
		}

		BigDecimal totalAmout = BigDecimal.ZERO;

		for(SaleorderGoods saleorderGood : saleorderGoodsList){

			totalAmout = totalAmout.add(saleorderGood.getRealPrice().multiply(new BigDecimal(saleorderGood.getNum()))) ;

			saleorderGood.setGoodsComments("");
			saleorderGood.setPrice(saleorderGood.getRealPrice());
			saleorderGood.setMaxSkuRefundAmount(saleorderGood.getRealPrice().multiply(BigDecimal.valueOf(saleorderGood.getNum())));
			saleorderGoodsMapper.updateByPrimaryKeySelective(saleorderGood);

		}

        //更新订单的总金额
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleOrderId);
        saleorder.setTotalAmount(totalAmout);
        saleorderMapper.updateByPrimaryKeySelective(saleorder);
	}

	@Override
	public List<Saleorder> getBDSaleOrderAndHasCoupon() {
		return saleorderMapper.getBDSaleOrderAndHasCoupon();
	}

	@Override
	public void updateByPrimaryKeySelective(Saleorder updateSaleOrder) {
		saleorderMapper.updateByPrimaryKeySelective(updateSaleOrder);
	}

    @Override
    @Transactional
    public void dealWithCouponInfo(Integer saleOrderId, CouponInfo couponInfo) {

        SaleorderCoupon saleorderCoupon = this.saleorderCouponMapper.selectBySaleOrderId(saleOrderId);

        //没有就新增
        if(saleorderCoupon == null){
            saleorderCoupon = new SaleorderCoupon();
            saleorderCoupon.setSaleorderId(saleOrderId);
            setCouponInfo(saleorderCoupon,couponInfo);
            saleorderCouponMapper.insertSelective(saleorderCoupon);
        //有就修改一个订单就一张优惠券
        }else{
            SaleorderCoupon updateSaleorderCoupon = new SaleorderCoupon();
            updateSaleorderCoupon.setSaleorderCouponId(saleorderCoupon.getSaleorderCouponId());
            setCouponInfo(updateSaleorderCoupon,couponInfo);
            saleorderCouponMapper.updateByPrimaryKeySelective(updateSaleorderCoupon);
        }
    }


	@Override
	public CoreSkuGenerate getSkuBySkuNo(String skuNo) {
		return StringUtils.isEmpty(skuNo)?null:coreSkuGenerateMapper.selectBySkuNo(skuNo);
	}

	@Override
	public CoreSpuGenerate getSpuBySpuId(Integer spuId) {
		return StringUtils.isEmpty(spuId)?null:coreSpuGenerateMapper.selectByPrimaryKey(spuId);
	}

	@Override
	public ResultInfo<?> cancelWMSOrder(AfterSalesVo afterSalesInfo) {
		try{
			Integer type = afterSalesInfo.getType();
			//取消订单参数对象
			if(StockOperateTypeConst.AFTERORDER_CHANGE_FINSH.equals(type)){
				if(!cancelTypeService.cancelExchangSaleorderMethod(afterSalesInfo.getAfterSalesNo(),"售后关闭")){
					return new ResultInfo<>(0,"物流正在进行货物操作，请等待作业完成后操作，如紧急请直接联系物流部。");
				}
			}else if(StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(type)){
				if(!cancelTypeService.cancelInputSaleReturnMethod(afterSalesInfo.getAfterSalesNo(),"售后关闭")){
					return new ResultInfo<>(0,"物流正在进行货物操作，请等待作业完成后操作，如紧急请直接联系物流部。");
				}
			}
//			cancelPoDto.setErpCancelReason("售后关闭");
//			logger.info("ERP取消售后单至WMS的请求: cancelPoDto:{}" + JSON.toJSONString(cancelPoDto));
			//撤销入库单 TODO Holiis ok

			/*WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.CANCEL_PO);
			WmsResponse wmsResponse = wmsInterface.request(cancelPoDto);
			logger.info("ERP取消售后单至WMS的响应:" + JSON.toJSONString(wmsResponse));
			if(!WmsCommonUtil.wmsInputOrderCanCacel(wmsResponse)){
				return new ResultInfo<>(0,wmsResponse.getReturnDesc());
			}*/

		}catch (Exception e){
			logger.error("poc error",e);
		}
		return new ResultInfo(1,"允许关闭");
	}

	@Override
	public ResultInfo<?> putOrderPricetoHC(Map<String, Object> map) {
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
		};
		ResultInfo<?> resultInfo = new ResultInfo<>();
		try {
			HashMap<String, Object> dataMap = new HashMap<>();
			// 封装订单信息
			Saleorder saleOrder = (Saleorder) map.get("saleOrder");
			List<SaleorderGoods> saleorderGoods =  getSaleorderGoods(saleOrder);

			BigDecimal totalMoney = new BigDecimal(0);
			BigDecimal realTotalMoney = new BigDecimal(0);
			BigDecimal deliverMoney = new BigDecimal(0);

			ArrayList<Map<String,Object>> goodsList = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(saleorderGoods)){
				for (SaleorderGoods saleorderGood : saleorderGoods) {
					HashMap<String, Object> goodsMap = new HashMap<>();
					goodsMap.put("skuId",saleorderGood.getGoodsId());
					goodsMap.put("skuName",saleorderGood.getGoodsName());
					goodsMap.put("skuPrice",saleorderGood.getPrice());
					goodsMap.put("couponAmount",saleorderGood.getPrice());
					//订单商品实际金额
					goodsMap.put("skuAmount",saleorderGood.getMaxSkuRefundAmount());
					/**
					 * 1.运费商品不推送；
					 * 2.推送运费价格；
					 */
					if ("V127063".equals(saleorderGood.getSku())){
						deliverMoney = saleorderGood.getPrice();
					} else {
						goodsList.add(goodsMap);
					}
					realTotalMoney = realTotalMoney.add(saleorderGood.getMaxSkuRefundAmount());
				}
			}
			totalMoney = realTotalMoney.subtract(deliverMoney);
			/**
			 * 订单号
			 */
			dataMap.put("orderNo",saleOrder.getSaleorderNo());
			//订单商品的总金额（不包含运费）
			dataMap.put("totalMoney",totalMoney);
			//订单的实际金额
			dataMap.put("realTotalMoney",realTotalMoney);
			//运费价格
			dataMap.put("deliverMoney",deliverMoney);
			//商品列表
			dataMap.put("orderSkuList",goodsList);

			String url = apiUrl + "/order/updateOrderSkuPrice" ;
			//String url = "http://172.17.1.2:8080/order/updateOrderSkuPrice" ;
			// map转json对象
			JSONObject jsonObject = JSONObject.fromObject(dataMap);
			Map<String, String> header = new HashMap<String, String>();
			header.put("version", "v1");
			logger.info("putOrderPricetoHC:" + dataMap.toString());
			resultInfo = (ResultInfo<?>) HttpClientUtils.put(url, jsonObject.toString(), header, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return resultInfo;
	}

	/**
	 * 根据发票ID获取票货同行订单信息
	 *
	 * @param invoiceIds
	 * @return
	 */
	@Override
	public List<Saleorder> getHcSaleorderInfoByInvoiceIds(List<Integer> invoiceIds) {
		return saleorderMapper.getHcSaleorderInfoByInvoiceIds(invoiceIds);
	}

	@Override
	public void deleteBdOrderByNo(Saleorder saleorder) {
		saleorderMapper.updateIsDeleteByOrderNo(saleorder);
	}


	@Override
	public List<SimpleBdOrderChooseRes> getBDOrderForLinkByPage(SimpleBdOrderChooseRes param, Page page) {
		Map<String,Object> map=new HashMap<>();
		map.put("order",param);
		map.put("page",page);
		List<SimpleBdOrderChooseRes> list=saleorderMapper.getBdOrderListPage(map);
		list.stream().forEach(order->{
			order.setGoodsList(saleorderGoodsMapper.getSimpleGoodsList(order.getSaleorderId()));
		});
		return list;
	}

	@Override
	public String getCommentsOfAutoCheck(Integer saleorderId) {
		Integer customerNature = traderCustomerMapper.getCustomerNatureBySaleorderId(saleorderId);
		if (customerNature == null) {
			return null;
		}
		//获取订单里sku为医疗器械的首营管理类别
		//968：一类，969：二类，970：三类
		List<Integer> managerCategoryList = saleorderMapper.getManageCategoryOfSkuBySaleorderId(saleorderId);
		if (managerCategoryList == null || managerCategoryList.size() == 0) {
			return null;
		}
		return this.getCommentsOfAutoCheckFromManageCategoryList(customerNature, managerCategoryList);
	}

	@Override
	public String getCommentsOfAutoCheckFromManageCategoryList(Integer customerNature, List<Integer> managerCategoryList) {
		if (customerNature == 466) {
			if (managerCategoryList.size() == 1) {
				if (managerCategoryList.get(0) == 968) {
					return "订单中产品为一类医疗器械，在客户营业执照经营范围内";
				} else {
					return "客户有医疗机构执业许可证";
				}
			} else {
				return "客户有医疗机构执业许可证";
			}
		}
		if (managerCategoryList.size() == 1) {
			if (managerCategoryList.get(0) == 968) {
				return "订单中产品为一类医疗器械，在客户营业执照经营范围内";
			}
			if (managerCategoryList.get(0) == 969) {
				return "订单中产品在客户二类医疗器械经营备案凭证范围内";
			}
			if (managerCategoryList.get(0) == 970) {
				return "订单中产品在客户医疗器械经营许可证范围内";
			}
		}
		if (managerCategoryList.size() == 2) {
			if (managerCategoryList.contains(968) && managerCategoryList.contains(969)) {
				return "订单中产品在客户二类医疗器械经营备案凭证范围内";
			}
			if (managerCategoryList.contains(970) && managerCategoryList.contains(969)) {
				return "订单中产品在客户二类医疗器械经营备案凭证、医疗器械经营许可证范围内";
			}
			if (managerCategoryList.contains(968) && managerCategoryList.contains(970)) {
				return "订单中产品在客户医疗器械经营许可证范围内";
			}
		}
		if (managerCategoryList.size() == 3) {
			return "订单中产品在客户二类医疗器械经营备案凭证、医疗器械经营许可证（三类）范围内";
		}
		return null;
	}

    @Override
    public void generateContractTemplateAndSave(Integer saleorderId) {
		CompletableFuture.runAsync(() -> {
			Saleorder saleorder = saleorderMapper.getSaleorderBySaleorderId(saleorderId);
			if (saleorder == null) {
				return;
			}
			//查看订单是否有回传附件
			List<Attachment> attachments  = attachmentMapper.getAttachmentInfoByRelatedIdAndFunctionId(saleorderId,492);
			if (CollectionUtils.isNotEmpty(attachments)){
				logger.info("订单：{}已经有合同回传附件，不再更新订单合同模板",saleorder.getSaleorderNo());
				return;
			}
			String contractTemplateUrl = erpDomain + "/order/saleorder/printOrder.do?saleorderId=" + saleorderId;
			String html2PdfUrl = html2PdfDomain + "/api/render";
			UrlToPdfParam urlToPdfParam = new UrlToPdfParam();
			urlToPdfParam.setUrl(contractTemplateUrl);
			UrlToPdfParam.Pdf pdf = new UrlToPdfParam.Pdf();
			UrlToPdfParam.Pdf.PdfMargin margin = new UrlToPdfParam.Pdf.PdfMargin("1cm","1cm","1cm","1cm");
			pdf.setMargin(margin);
			urlToPdfParam.setPdf(pdf);
			String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl,"pdf",saleorder.getSaleorderNo() + saleorder.getSaleorderNo(),urlToPdfParam);
			logger.info("原自动生成订单：{}模板合同{}",saleorder.getSaleorderNo(),ossUrl);
			if (isRemoveBlankPage) {
				AtomicInteger retryCount = new AtomicInteger(0);
				ossUrl = removeBlankPdfPagesAndSaveFile2Oss(ossUrl, retryCount);
			}
			if (org.apache.commons.lang3.StringUtils.isBlank(ossUrl)) {
				logger.error("自动生成订单模板合同失败，订单ID：{}",saleorderId);
				return;
			}
			logger.info("自动生成订单：{}模板合同{}",saleorder.getSaleorderNo(),ossUrl);
			saleorderMapper.updateContractUrlOfSaleorder(saleorderId,ossUrl);
		});

    }

	/**
	 * 去除合同空白页
	 * @param ssoUrl
	 * @param retryCount
	 */
	private String removeBlankPdfPagesAndSaveFile2Oss(String ssoUrl, AtomicInteger retryCount) {
		if (org.springframework.util.StringUtils.isEmpty(ssoUrl)) {
			logger.error("返回的空白合同无网络地址！");
			throw new ServiceException("返回的空白合同无网络地址！");
		}
		HttpURLConnection urlConnection = null;
		InputStream inputStream = null;
		String filePath = "";
		try {
			URL url = new URL(ssoUrl);
			urlConnection = (HttpURLConnection) url.openConnection();
			urlConnection.setConnectTimeout(15000);
			urlConnection.setReadTimeout(60000);
			urlConnection.connect();
			inputStream = urlConnection.getInputStream();

			// 去除空白页
			ByteArrayOutputStream fileOutputStream = new ByteArrayOutputStream();
			PdfUtil.removeBlankPdfPages(inputStream,fileOutputStream);

			ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(fileOutputStream.toByteArray());

			String fileName = cn.hutool.core.lang.UUID.randomUUID().toString();
			filePath = ossUtilsService.upload2OssForInputStream("pdf", fileName,ossHttp+ossUrl+ossFilePath, byteArrayInputStream);
			logger.info("消费-》oss保存文件地址：{}", filePath);
			fileOutputStream.close();
			byteArrayInputStream.close();
			return filePath;
		} catch (Exception e) {
			logger.error("电子签章: 空白合同{}，去除空白页异常：{}",ssoUrl, e);
			if (retryCount.get() < MAX_RETRY_COUNT) {
				logger.warn("电子签章: 重试次数{},空白合同{}", retryCount.get(), ssoUrl);
				retryCount.incrementAndGet();
				return this.removeBlankPdfPagesAndSaveFile2Oss(ssoUrl, retryCount);
			} else {
				logger.error("电子签章:空白合同去除空白页:{}重试超过3次,异常:{}",ssoUrl, e);
				throw new ServiceException("电子签章:空白合同去除空白页重试超过3次");
			}
		} finally {
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (urlConnection != null) {
					urlConnection.disconnect();
				}
			} catch (Exception e) {
				logger.error("关闭连接失败", e);
				throw new ServiceException(e.getMessage());
			}
		}
	}

	@Override
	public Integer getSaleorderIdByOrderModifyId(Integer saleorderModifyId) {
		return saleorderMapper.getSaleorderIdByModifyApplyId(saleorderModifyId);
	}

	@Override
	public Map<String, Object> checkAutoCheckOfSaleorderByVerifyLog(List<HistoricActivityInstance> historicActivityInstances, Map<String, Object> commentMap, Integer saleorderId) {
		if (historicActivityInstances == null || historicActivityInstances.size() == 0) {
			return commentMap;
		}
		for (int i = 0; i < historicActivityInstances.size(); i++) {
			if (KEY_AUTOCHECK_APTITUDE.equals(historicActivityInstances.get(i).getActivityName()) && historicActivityInstances.size() > i+1
				&& "审核完成".equals(historicActivityInstances.get(i+1).getActivityName())){
				commentMap.put(historicActivityInstances.get(i).getTaskId(),getCommentsOfAutoCheck(saleorderId));
				return commentMap;
			}
		}
		return commentMap;
	}

	@Override
	public SaleorderBillDTO getBillInfoOfSaleorder(String saleorderNo) {
		Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(saleorderNo);
		if (saleorder == null) {
			return null;
		}
		SaleorderBillDTO billDTO = new SaleorderBillDTO();
		billDTO.setOrderNo(saleorderNo);
		billDTO.setArriveTime(saleorder.getArrivalTime());

		CapitalBill capitalBill = new CapitalBill();
		CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
		capitalBillDetail.setOrderType(ErpConst.ONE);// 销售订单类型
		capitalBillDetail.setOrderNo(saleorder.getSaleorderNo());
		capitalBillDetail.setRelatedId(saleorder.getSaleorderId());
		capitalBill.setCapitalBillDetail(capitalBillDetail);
		List<CapitalBill> capitalBills = capitalBillDetailMapper.getCapitalBillList(capitalBill);
		if (capitalBills.size() > 0) {
			billDTO.setPayTime(capitalBills.get(0).getAddTime());
			billDTO.setPayAmount(capitalBills.stream().map(CapitalBill::getAmount).reduce(BigDecimal::add).get());
		}
		capitalBill.setOperationType("finance_sale_detail");
		List<CapitalBill> afterReturnBills = capitalBillDetailMapper.getAfterReturnCapitalBillList(capitalBill);
		if (afterReturnBills.size() > 0) {
			billDTO.setRefundTime(afterReturnBills.get(0).getAddTime());
			billDTO.setRefundAmount(afterReturnBills.stream().map(CapitalBill::getAmount).reduce(BigDecimal::add).get());
		}
		return billDTO;
	}

	@Override
	public CapitalBillDetail checkAfterCapitalBillAmountOfHcOrder(Integer capitalBillDetailId) {
		return capitalBillDetailMapper.checkAfterCapitalBillAmountOfHcOrder(capitalBillDetailId);
	}

	@Override
	public SaleorderBillDTO getPayAmountInfoWhenOrderArrived(String orderNo, Long timestamp) {
		Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(orderNo);
		if (saleorder == null) {
			return null;
		}
		SaleorderBillDTO billDTO = new SaleorderBillDTO();
		billDTO.setOrderNo(orderNo);
		billDTO.setArriveTime(timestamp);
		BigDecimal payAmount = new BigDecimal("0");
		CapitalBill capitalBill = new CapitalBill();
		CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
		capitalBillDetail.setOrderType(ErpConst.ONE);// 销售订单类型
		capitalBillDetail.setOrderNo(saleorder.getSaleorderNo());
		capitalBillDetail.setRelatedId(saleorder.getSaleorderId());
		capitalBill.setCapitalBillDetail(capitalBillDetail);
		capitalBill.setAddTime(timestamp);
		List<CapitalBill> capitalBills = capitalBillDetailMapper.getCapitalBillList(capitalBill);
		if (capitalBills.size() > 0) {
			payAmount = capitalBills.stream().map(CapitalBill::getAmount).reduce(BigDecimal::add).get();
			billDTO.setPayTime(capitalBills.get(capitalBills.size() -1).getAddTime());
		}
		capitalBill.setOperationType("finance_sale_detail");
		List<CapitalBill> afterReturnBills = capitalBillDetailMapper.getAfterReturnCapitalBillList(capitalBill);
		if (afterReturnBills.size() > 0) {
			payAmount = payAmount.add(afterReturnBills.stream().map(CapitalBill::getAmount).reduce(BigDecimal::add).get());
			billDTO.setPayTime(afterReturnBills.get(afterReturnBills.size()-1).getAddTime());
		}
		billDTO.setPayAmount(payAmount);
		return billDTO;
	}

	@Override
	public Map<String, Object> getOfflineDirectSalesOrderListPage(Saleorder saleorder, Page page) {
		Map<String, Object> map = new HashMap<>();
		map.put("saleorder", saleorder);

		// 如果使用账期未还条件查询；1、先查询出全部-账期未还-的销售订单
		if (saleorder.getAccountPeriod() != null && saleorder.getAccountPeriod().intValue() >= 0) {
			List<Integer> saleOrderIdList = saleorderMapper.getLackAccountPeriodOrderSaleOrderId();
			saleorder.setKeyIds(saleOrderIdList);
		}
		BigDecimal totalAmount = null;
		if (saleorder != null && saleorder.getIsSearchCount() == 1) {
			totalAmount = saleorderMapper.getSaleorderListSum(map);
			saleorder.setAllTotalAmount(totalAmount);
			map.put("saleorder", saleorder);
		}
		/**
		 * 合同回传和送货单回传查询条件
		 */
		dealDeliveryOrderReturn(saleorder, map);

        /**
		 * 添加产品归属人条件搜索
		 */
		if(saleorder.getProductBelongUserId() != null && saleorder.getProductBelongUserId() !=  -1) {
			List<Integer> saleOrderIds = saleorderMapper.getSaleorderIdListBelongtoProductRole(saleorder.getProductBelongUserId());
			map.put("productSaleIds",saleOrderIds);
		}

		/******************************************* 完 ***************************************************/
		Map<String, Object> saleorderINfo = null;
		saleorderINfo = saleorderMapper.getSaleorderListCountAndTotalCount(map);

		map.put("page", page);
		map.put("total_amount", new BigDecimal(0));
		page.setTotalRecord(Integer.valueOf(saleorderINfo.get("total_count").toString()));

		if(null != saleorderINfo){
			map.put("total_amount", new BigDecimal(saleorderINfo.get("total_amount").toString()));
			page.setTotalRecord(Integer.valueOf(saleorderINfo.get("total_count").toString()));
		}
		List<Saleorder> saleorderList = saleorderMapper.getOfflineDirectSalesOrderListPage(map);
		if (!(org.apache.commons.lang3.StringUtils.isNotBlank(saleorder.getOptType()) &&
				"orderIndex".equals(saleorder.getOptType()) &&
				CollectionUtils.isNotEmpty(saleorderList))) {
			saleorderList.forEach(saleorderInfo ->{
				saleorderInfo.setAccountPayable(saleorderMapper.getPeriodAmount(saleorderInfo.getSaleorderId()));
				saleorderInfo.setPaymentAmount(saleorderMapper.getPaymentAmount(saleorderInfo.getSaleorderId()));
			});
		}
		map.put("saleorderList", saleorderList);
		return map;
	}

    private void dealDeliveryOrderReturn(Saleorder saleorder, Map<String, Object> map) {
        // 最终订单ID集合
        List<Integer> saleorderIdsList = new ArrayList<Integer>();
        // 合同回传的ID集合
        List<Integer> saleorderContractReturnList = null;
        // 送货单回传的ID集合
        List<Integer> saleorderDeliveryReturnList = null;
        if (saleorder.getIsContractReturn() != null || saleorder.getIsDeliveryOrderReturn() != null) {
            // 两个回传只要有一个不是全部条件的都进来
            if (saleorder.getIsContractReturn() != -1 || saleorder.getIsDeliveryOrderReturn() != -1) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("saleorder", saleorder);
                // 合同已回传+送货单已回传
                if (saleorder.getIsContractReturn() == 1 && saleorder.getIsDeliveryOrderReturn() == 1) {
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);

                    saleorderIdsList.addAll(saleorderContractReturnList);
                    // 求交集，形成合同已回传+送货单已回传的订单Id集合
                    saleorderIdsList.retainAll(saleorderDeliveryReturnList);
                    // 合同未回传+送货单已回传
                } else if (saleorder.getIsContractReturn() == 0 && saleorder.getIsDeliveryOrderReturn() == 1) {
                    // 所有订单Id集合
                    tempMap.put("type", 0);
                    saleorderIdsList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 将所有订单Id中排除合同已回传的订单Id集合
                    saleorderIdsList.removeAll(saleorderContractReturnList);
                    // 求交集，形成合同未回传+送货单已回传的订单Id集合
                    saleorderIdsList.retainAll(saleorderDeliveryReturnList);
                    // 合同已回传+送货单未回传
                } else if (saleorder.getIsContractReturn() == 1 && saleorder.getIsDeliveryOrderReturn() == 0) {
                    // 所有订单Id集合
                    tempMap.put("type", 0);
                    saleorderIdsList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 将所有订单Id中排除送货单已回传的订单Id集合
                    saleorderIdsList.removeAll(saleorderDeliveryReturnList);
                    // 求交集，形成合同已回传+送货单未回传的订单Id集合
                    saleorderIdsList.retainAll(saleorderContractReturnList);
                    // 合同未回传+送货单未回传
                } else if (saleorder.getIsContractReturn() == 0 && saleorder.getIsDeliveryOrderReturn() == 0) {
                    // 所有订单Id集合
                    tempMap.put("type", 0);
                    saleorderIdsList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 复制列表
                    List<Integer> saleorderIdsListOther = new ArrayList<Integer>();
                    saleorderIdsListOther.addAll(saleorderIdsList);
                    // tempMap.put("type", 0);
                    // List<Integer>
                    // saleorderIdsList1=saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 将所有订单Id中排除合同已回传的订单Id集合
                    saleorderIdsList.removeAll(saleorderContractReturnList);
                    // 将所有订单Id中排除送货单已回传的订单Id集合
                    saleorderIdsListOther.removeAll(saleorderDeliveryReturnList);
                    // 求交集，形成合同未回传+送货单未回传的订单Id集合
                    saleorderIdsList.retainAll(saleorderIdsListOther);
                    // 合同回传全部+送货单已回传
                } else if (saleorder.getIsContractReturn() == -1 && saleorder.getIsDeliveryOrderReturn() == 1) {
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    saleorderIdsList.addAll(saleorderDeliveryReturnList);
                    // 合同回传全部+送货单未回传
                } else if (saleorder.getIsContractReturn() == -1 && saleorder.getIsDeliveryOrderReturn() == 0) {
                    // 所有订单Id集合
                    tempMap.put("type", 0);
                    saleorderIdsList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 送货单已回传的订单Id集合
                    tempMap.put("type", 2);
                    saleorderDeliveryReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 将所有订单Id中排除送货单已回传的订单Id集合
                    saleorderIdsList.removeAll(saleorderDeliveryReturnList);
                    // 合同已回传+送货单全部
                } else if (saleorder.getIsContractReturn() == 1 && saleorder.getIsDeliveryOrderReturn() == -1) {
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    saleorderIdsList.addAll(saleorderContractReturnList);
                    // 合同未回传+送货单全部
                } else if (saleorder.getIsContractReturn() == 0 && saleorder.getIsDeliveryOrderReturn() == -1) {
                    // 所有订单Id集合
                    tempMap.put("type", 0);
                    saleorderIdsList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 合同已回传的订单Id集合
                    tempMap.put("type", 1);
                    saleorderContractReturnList = saleorderMapper.getSaleorderReturnOrNotList(tempMap);
                    // 将所有订单Id中排除合同已回传的订单Id集合
                    saleorderIdsList.removeAll(saleorderContractReturnList);
                }

            }
            map.put("saleorderIdsList", saleorderIdsList);
        }
    }

    @Override
	public List<SaleorderGoods> getSaleorderGoodsBySaleorderId(Integer saleorderId) {
		if (saleorderId == null || saleorderId == 0){
			return null;
		}
		return saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleorderId);
	}
	@Override
	public void clearBhOrderAuditInfo(Integer saleOrderId) {
		saleorderMapper.clearBhOrderAuditInfo(saleOrderId);
	}


	@Override
	public List<Saleorder> getHCSaleOrderAndHasCoupon() {
		return saleorderMapper.getHCSaleOrderAndHasCoupon();
	}

	@Override
	public void updateByJCOrder(Saleorder saleorder) {
		saleorderMapper.updateByJCOrder(saleorder);
	}

	@Override
	public ResultInfo<?> updateUnlockSaleOrderWarning(Integer saleorderId) {
		try{
			long nowTime = DateUtil.gainNowDate();
			List<SaleorderGoods> saleorderGoodsList = saleorderMapper.getSaleorderGoodsById(saleorderId);
			if(CollectionUtils.isNotEmpty(saleorderGoodsList)){
				for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
					// 时效监控开始时间不为空，才去更新，不然不需要重置
					if (saleorderGoods.getAgingTime() != null && saleorderGoods.getAgingTime() > 0) {
						SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
						saleorderGoodsVo.setSaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
						saleorderGoodsVo.setWarnLevel(null);
						saleorderGoodsVo.setAging(0);
						saleorderGoodsVo.setAgingTime(nowTime);
						saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
					}
				}
			}
		}catch (Exception e){
			logger.error("解锁时,更新销售单预警状态失败，销售单id:{},错误：{}",saleorderId,e);
			return new ResultInfo<>(-1,"解锁更新销售单预警失败");
		}
		return new ResultInfo<>(0,"解锁更新销售单预警成功");
	}

	@Override
	public ResultInfo<?> updateLockSaleorderWarning(Integer saleorderId) {
		try{
			List<SaleorderGoods> saleorderGoodsLists = saleorderMapper.getSaleorderGoodsById(saleorderId);
			if(CollectionUtils.isNotEmpty(saleorderGoodsLists)){
				for (SaleorderGoods sg : saleorderGoodsLists) {
					SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
					saleorderGoodsVo.setSaleorderGoodsId(sg.getSaleorderGoodsId());
					saleorderGoodsVo.setWarnLevel(null);
					saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
				}
			}
		}catch(Exception e){
			logger.error("锁定销售单时，清除预警失败，销售单ID：{},错误原因：{}",saleorderId,e);
			return new ResultInfo<>(-1,"锁定更新销售单预警失败");
		}
		return new ResultInfo<>(0,"锁定更新销售单预警成功");
	}

	@Override
	public List<Integer> getOrderApplyIdsBySaleOrderId(Integer orderId) {
		if (orderId == null){
			return null;
		}
		return saleorderMapper.getOrderApplyIdsBySaleOrderId(orderId);
	}

	@Override
	public List<SpecialDeliveryInfoDto> getSpecialDeliveryInfo(Saleorder saleorder) {
		List<SpecialDeliveryInfoDto> specialDeliveryInfoDtoList = new ArrayList<>();
		Saleorder saleOrderById = saleorderMapper.getSaleOrderById(saleorder.getSaleorderId());
		List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleorder.getSaleorderId());
		if (CollectionUtils.isNotEmpty(saleorderGoodsList)) {
			for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
				SpecialDeliveryInfoDto specialDeliveryInfoDto = new SpecialDeliveryInfoDto();
				specialDeliveryInfoDto.setSaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
				List<BuyorderGoodsVo> buyorderGoodsList= buyorderGoodsMapper.getBuyorderNosBySaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
				if(CollectionUtils.isNotEmpty(buyorderGoodsList)) {
					List<Integer> buyorderGoodsIdList = buyorderGoodsList.stream().map(e -> e.getBuyorderGoodsId()).collect(Collectors.toList());
					//催货预警
					List<EarlyWarningTask> existEarlyWarningGoodsTaskList = earlyWarningTaskMapper.getExistEarlyWarningGoodsTaskListByBuyorderGoodsIdList(buyorderGoodsIdList);
					if(CollectionUtils.isNotEmpty(existEarlyWarningGoodsTaskList)){
						EarlyWarningTask earlyWarningTask = existEarlyWarningGoodsTaskList.stream().max((e, o) -> DateUtil.convertLong(e.getFollowUpTime(), null) > DateUtil.convertLong(o.getFollowUpTime(), null) ? 1 : -1).orElse(null);
						if(earlyWarningTask != null){
							specialDeliveryInfoDto.setIsExpediting(1);
							specialDeliveryInfoDto.setFollowUpNum(earlyWarningTask.getFollowUpNum());
							specialDeliveryInfoDto.setFollowUpComment(earlyWarningTask.getFollowUpComment());
							specialDeliveryInfoDto.setTaskStatusStr(earlyWarningTask.getTaskStatus() == 1?"临期":"逾期");
						}
					}
				}
				//专向发货
				if (saleorderGoods.getSpecialDelivery() != null && saleorderGoods.getSpecialDelivery() == 1) {
					specialDeliveryInfoDto.setIsSpecialDeliveryStr("是");
					//绑定的采购单号
					if(CollectionUtils.isNotEmpty(buyorderGoodsList)){
                        Set<String> buyorderNoList = buyorderGoodsList.stream().map(e -> e.getBuyorderNo()).collect(Collectors.toSet());
                        specialDeliveryInfoDto.setBuyorderNoStr(String.join("/", buyorderNoList));

					}
				} else {
					specialDeliveryInfoDto.setIsSpecialDeliveryStr("否");
				}
				//发货要求
                specialDeliveryInfoDto.setDeliveryClaim(saleOrderById.getDeliveryClaim());
				if (saleOrderById.getDeliveryClaim() != null && saleOrderById.getDeliveryClaim() == 1) {
					specialDeliveryInfoDto.setDeliveryClaimStr("等通知发货");
					if(saleOrderById.getDeliveryDelayTime() != null) {
                        String timeStr = DateUtil.convertString(saleOrderById.getDeliveryDelayTime(), DateUtil.DATE_FORMAT);
                        specialDeliveryInfoDto.setDeliveryDelayTimeStr(timeStr);
                    }
				} else if (saleOrderById.getDeliveryClaim() != null && saleOrderById.getDeliveryClaim() == 0) {
					specialDeliveryInfoDto.setDeliveryClaimStr("立即发货");
				}
				specialDeliveryInfoDtoList.add(specialDeliveryInfoDto);
			}
		}
		return specialDeliveryInfoDtoList;
	}


    @Override
    public Integer getClosedAndOtherOrder(Integer saleorderId){
        return saleorderGoodsMapper.getClosedAndOtherOrder(saleorderId);
    }



	@Override
	public String getIsSameAddressNoRe(Saleorder saleorder) {

			//不满足票货同行原因ID
			String reasonNo = "";

			//校验是否满足票货同行条件
			if (saleorder.getIsSendInvoice() == 1 && saleorder.getIsSameAddress() != null && saleorder.getIsSameAddress() == 1) {

				//发票是否寄送
				if (saleorder.getIsSendInvoice() != 1) {
					reasonNo = reasonNo + "1";
				}
				//开票方式
				if (saleorder.getInvoiceMethod() != 3) {
					reasonNo = reasonNo + "2";
				}
				if (!saleorder.getTakeTraderId().equals(saleorder.getInvoiceTraderId()) || !saleorder.getTakeTraderAddressId().equals(saleorder.getInvoiceTraderAddressId())) {
					reasonNo = reasonNo + "5";
				}
                List<SaleorderGoods> saleorderGoods= getSaleorderGoodsBySaleorderId(saleorder.getSaleorderId());
                if (CollectionUtils.isNotEmpty(saleorderGoods)){
                    //订单中全部商品的发货方式为“普发”
                    for (int i = 0; i < saleorderGoods.size(); i++) {
                        if (saleorderGoods.get(i).getDeliveryDirect()== 1){
                            reasonNo = reasonNo + "3";
                            break;
                        }
                    }
                }
                //订单不存在非“已关闭”状态的“销售订单退货”或“销售订单退票”的售后单
                int closedAndOtherOrder = getClosedAndOtherOrder(saleorder.getSaleorderId());

                if (closedAndOtherOrder > 0){
                    reasonNo = reasonNo + "4";
                }
			}

			return reasonNo;
		}


	@Override
	public boolean isPartialPayment(Integer paymentType) {
		return Objects.nonNull(paymentType) &&
				paymentType > PaymentTypeEnum.PAY_BEFORE.getType() &&
				paymentType <= PaymentTypeEnum.PAY_OTHER.getType();
	}


	@Override
	public void updateSaleorderGoodsSnapshotInfo(int saleorderId) {
		List<SaleorderGoods> saleorderGoodsList = saleorderMapper.getSaleorderGoodsById(saleorderId);
		if(CollectionUtils.isNotEmpty(saleorderGoodsList)){
			saleorderGoodsList.forEach(saleorderGoods->{
				saleorderGoodsMapper.updateSaleorderGoodsSnapshotInfo(saleorderGoods);
			});
		}

		logger.info("订单生效后更新销售订单快照信息成功！订单ID: " + saleorderId + "时间： " + DateUtil.getNowDate(DateUtil.TIME_FORMAT) );
	}
	@Override
	public List<Integer> getAffectOrderBySkuId(Long skuId){
		return saleorderMapper.getPriceChangeAffectSaleorderBySkuId(skuId);
	}

    @Override
    public SaleorderVo getOrderInfo4PrintContractTemplate(Saleorder saleorderParam) {

        SaleorderVo saleorderVoResult = saleorderMapper.selectOrderInfo4PrintContractTemplate(saleorderParam);
        return saleorderVoResult;
    }

	@Override
	public ResultInfo<?> preQuoteToSaleOrder(Integer quoteOrderId) {

		// 获取报价主信息
		Quoteorder quote = quoteorderMapper.selectQuoteInfoById(quoteOrderId);
		// 获取客户信息
		TraderCustomerVo traderCustomerVo = traderCustomerMapper.getCustomerInfo(quote.getTraderId());

		if (traderCustomerVo.getIsEnable() == 0) {
			// return new ResultInfo(-1,"客户被禁用后，报价单无法转化成订单", 1);
			ResultInfo resultInfo = new ResultInfo(-1, "客户被禁用后，报价单无法转化成订单");
			resultInfo.setStatus(1);
			return resultInfo;
		}

		// 获取报价主信息
		List<QuoteorderGoods> quoteGoodsList = quoteorderGoodsMapper.selectQuoteGoodsById(quoteOrderId, null);
		for (QuoteorderGoods quoteorderGoods : quoteGoodsList) {

			// 报价单中含有未审核产品，无法转化为订单，请等待产品审核完成！
			if (quoteorderGoods.getIsTemp() == 1 && quoteorderGoods.getGoodsId() != null && quoteorderGoods.getGoodsId() != 0) {

				// 报价单中临时产品无法被转化到订单中，是否转化为订单？转化后因价格变化会导致付款计划变化，请重新设置。
				ResultInfo resultInfo = new ResultInfo(-1, "报价单中临时产品无法被转化到订单中，是否转化为订单？转化后因价格变化会导致付款计划变化，请重新设置。");
				resultInfo.setStatus(2);
				return resultInfo;
			}
		}
		return new ResultInfo(0, "操作成功");
	}

	@Override
	public void saveEditOrderBeforeOperateSku(HttpServletRequest request, Saleorder saleorder) {

		User currentLoginUser = (User) request.getSession().getAttribute(Consts.SESSION_USER);
		if (currentLoginUser == null) {
			currentLoginUser = userMapper.getByUsername("njadmin", 1);
		}
		saleorder.setModTime(DateUtil.sysTimeMillis());
		saleorder.setUpdater(currentLoginUser.getUserId());
		if(!StringUtils.isEmpty(saleorder.getDeliveryDelayTimeStr())){
			saleorder.setDeliveryDelayTime(DateUtil.convertLong(saleorder.getDeliveryDelayTimeStr(),DateUtil.DATE_FORMAT));
		}
		saleorderMapper.updateByPrimaryKeySelective(saleorder);
	}

	@Override
	public void updateInsideCommentsBySaleorderIdAndSku(Integer saleOrderId, String skuNo, String remark) {
		saleorderGoodsMapper.updateInsideCommentsBySaleorderIdAndSku(saleOrderId,skuNo,remark);
	}

	@Override
	public List<SaleorderGoods> getSaleOrderGoodsListByOrderIdList(List<Integer> saleOrderIdList) {
		List<SaleorderGoods> saleOrderGoodsList = saleorderMapper.getSaleOrderGoodsListByOrderIdList(saleOrderIdList);

		// 使用Map来存储SKU和SaleorderGoods的关系，如果有相同的SKU，只保留saleOrderId较大的
		Map<String, SaleorderGoods> skuToSaleOrderMap = new HashMap<>();
		for (SaleorderGoods saleorderGoods : saleOrderGoodsList) {
			String sku = saleorderGoods.getSku();
			if (!skuToSaleOrderMap.containsKey(sku)) {
				// 如果Map中不存在相同的SKU，将当前的saleOrder放入Map中
				skuToSaleOrderMap.put(sku, saleorderGoods);
				continue;
			}
               // 如果Map中已经存在相同的SKU，只保留saleOrderId较大的
			if (saleorderGoods.getSaleorderId() > skuToSaleOrderMap.get(sku).getSaleorderId()) {
				skuToSaleOrderMap.put(sku, saleorderGoods);
			}
		}
		// 最后，从Map中获取所有的SaleorderGoods
		List<SaleorderGoods> result = new ArrayList<>(skuToSaleOrderMap.values());

		Set<Integer> specialGoods= goodsMapper.getSpecialGoodsList()
				.stream().map(Goods::getGoodsId).collect(Collectors.toSet());
		List<SaleorderGoods> goodsList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(result)){
			for (SaleorderGoods saleorderGoods : result) {
				//特殊商品
				if (specialGoods.contains(saleorderGoods.getGoodsId())) {
					continue;
				}
				saleorderGoods.setNum(0);
				goodsList.add(saleorderGoods);
			}
		}
		return goodsList;
	}

	@Override
	public Saleorder getSaleOrderByOrderNoAndTraderId(String saleOrderNo,Integer traderId) {
		Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(saleOrderNo);
		if (Objects.isNull(saleorder)) {
			throw new IllegalArgumentException("请输入正确的订单号");
		}
		if (saleorder.getTraderId() != null && !saleorder.getTraderId().equals(traderId)){
			throw new IllegalArgumentException("不同客户的订单不能同时引用");
		}
		return saleorder;
	}

	@Override
	public Boolean getPendingOrderBySaleorder(Saleorder saleorder) {
		List<Saleorder> saleorderList = saleorderGoodsMapper.getPendingOrderBySaleorderIdAndValidTime(saleorder);

		return CollectionUtils.isNotEmpty(saleorderList);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateConfirmTimeAndStatus(Saleorder saleOrderInfo) {
		Saleorder saleOrder = saleorderMapper.getSaleorderByOrderNo(saleOrderInfo.getSaleorderNo());
		if(saleOrder.getConfirmStatus()==null || saleOrder.getConfirmStatus()==0){
			saleOrderInfo.setSaleorderId(saleOrder.getSaleorderId());
			saleorderMapper.updateConfirmTimeAndStatus(saleOrderInfo);
			//更新确认记录
			ConfirmRecordDto confirmRecordDto = new ConfirmRecordDto();
			confirmRecordDto.setModTime(new Date());
			confirmRecordDto.setBusinessNo(saleOrderInfo.getSaleorderNo());
			confirmRecordDto.setConfirmTime(new Date());
			confirmRecordDto.setConfirmStatus(ErpConst.ONE);
			confirmRecordApiService.updateConfirmTimeAndStatus(confirmRecordDto);
		}
	}

	@Override
	public SaleorderModifyApply getSaleorderModifyApplyInfoByRelatedIdAndOrderId(SaleorderModifyApply saleorderModifyApply) {
		return saleorderModifyApplyMapper.getSaleorderModifyApply(saleorderModifyApply.getSaleorderId(),saleorderModifyApply.getRelatedId());
	}

	@Override
	public SaleorderModifyApply getSaleorderModifyApplyBySaleOrderId(Integer saleOrderId, Integer status) {
		return saleorderModifyApplyMapper.getSaleorderModifyApplyBySaleOrderId(saleOrderId,status);
	}

    @Override
    public void updateSaleorderGoodsConfirmNumber(Integer customerId, String traderContactMobile) {
        saleorderGoodsMapper.updateSaleorderGoodsConfirmNumber(customerId, traderContactMobile);
    }

	@Override
	public List<SaleorderVo> getConfirmNumberByTraderIdAndTraderContactMobile(Integer customerId, String traderContactMobile) {
		return saleorderGoodsMapper.getConfirmNumberByTraderIdAndTraderContactMobile(customerId, traderContactMobile);
	}

	@Override
	public boolean isShowSignature(User user, Integer saleorderId) {
		//查根据traderId T_R_TRADER_J_USER表里的userId
		Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderId);
		RTraderJUser rTraderJUser = rTraderJUserMapper.getUserByTraderId(saleorder.getTraderId());
		if( !StringUtils.isEmpty(saleorder) && saleorder.getTraderId()!=null && rTraderJUser.getUserId().equals( user.getUserId()) ){
			return true;
		}
		return false;
	}

	@Override
	public Map<Integer, Integer> getTraderOrderCountByRecentDaysMap(List<Integer> customerIds, Integer validOrderDays) {
		logger.info("getTraderOrderCountByRecentDaysMap start customerIds:{},validOrderDays:{}", JSON.toJSONString(customerIds), validOrderDays);
		if (CollectionUtils.isEmpty(customerIds)){
			return new HashMap<>(8);
		}
		validOrderDays = Math.max(validOrderDays - 9, 0);
		List<CustomerCountDto> traderOrderCountByRecentDaysList = saleorderMapper
				.getTraderOrderCountByRecentDaysList(customerIds, validOrderDays);
		if (CollectionUtils.isEmpty(traderOrderCountByRecentDaysList)){
			logger.info("配置时间内暂无生效订单记录 validOrderDays:{}", validOrderDays);
			return customerIds.stream().collect(Collectors.toMap(i -> i, i -> 0));
		}
		Map<Integer, Integer> resultMap = traderOrderCountByRecentDaysList.stream()
				.collect(Collectors.toMap(CustomerCountDto :: getTraderCustomerId,CustomerCountDto::getMyCount));
		customerIds.forEach(customerId ->  resultMap.put(customerId, resultMap.getOrDefault(customerId, 0)));
		return resultMap;
	}

	@Override
	public BigDecimal getRealTotalAmountBySaleorderIdList(List<Integer> saleorderIdList) {
		return Lists.partition(saleorderIdList,1000).stream()
				.map(list -> saleorderMapper.getRealTotalAmountBySaleorderIdList(list))
				.reduce(BigDecimal.ZERO, BigDecimal::add);
	}

	@Override
	public List<SaleorderGoods> querySaleorderGoodsForOut(Saleorder saleorder) {
		List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.querySaleorderGoodsForOut(saleorder);
		//查询关联采购单
		for(SaleorderGoods saleorderGoods : saleorderGoodsList){
			List<Buyorder> buyorderList = buyorderMapper.getBuyorderListBySaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
			if (buyorderList == null || buyorderList.size() > 0) {
				List buyNewList = buyorderList.stream().collect(Collectors.collectingAndThen(
						Collectors.toCollection(()->new TreeSet<>(Comparator.comparing(Buyorder::getBuyorderNo))),ArrayList::new));//list去重
				saleorderGoods.setBuyorderList(buyNewList);
			} else {
				List<Buyorder> buyorderList2=buyorderMapper.getBuyorderOutListBySaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
				if(buyorderList2!=null&&buyorderList2.size()>0){
					List buyNewList = buyorderList2.stream().collect(Collectors.collectingAndThen(
							Collectors.toCollection(()->new TreeSet<>(Comparator.comparing(Buyorder::getBuyorderNo))),ArrayList::new));//list去重
					saleorderGoods.setBuyorderList(buyNewList);
				}
			}
		}
		return saleorderGoodsList;
	}

	@Override
	public List<SimpleBdOrderChooseRes> getQuoteOrderForLinkByPage(SimpleBdOrderChooseRes param, Page page) {
		Map<String, Object> map = new HashMap<>();
		map.put("order", param);
		map.put("page", page);
		List<SimpleBdOrderChooseRes> list = saleorderMapper.getQuoteOrderListPage(map);
		list.forEach(order -> order.setGoodsList(saleorderGoodsMapper.getSimpleGoodsList(order.getSaleorderId())));
		return list;
	}

	@Override
	public List<ExpressOnlineReceiptVo> getExpressOnlineReceiptListByOrderId(Integer orderId) {

		List<ExpressOnlineReceiptVo> expressOnlineReceiptList = expressMapper.getExpressOnlineReceiptListByOrderId(orderId);
		if (CollectionUtils.isEmpty(expressOnlineReceiptList)){
			return null;
		}
		expressOnlineReceiptList.forEach(item -> item.setMobile(DesensitizedUtil.mobilePhone(item.getMobile())));
		return expressOnlineReceiptList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void checkConfirmationDetail(HttpServletRequest request,ConfirmationReplyMsgDto replyMsg) throws BusinessException {
		//审核通过的批次 审核流状态改为通过 修改批次审核状态1
		List<ApproveMsgDto> approveList = replyMsg.getApproveList();
		if (approveList.size()>0){
			approveList.forEach(a -> {
				OutboundBatchesRecode outboundBatchesRecode = outboundBatchesRecodeMapper.getById(a.getId());
				if (outboundBatchesRecode!=null && outboundBatchesRecode.getUploadStatus()==1&& outboundBatchesRecode.getAuditStatus()==1&& outboundBatchesRecode.getOnlineConfirm()==1){
					throw new BusinessException("客户已经在线签收，不可操作");
				}
			});
			approveList.forEach(a -> {
				logger.info("开始处理确认单审核通过的审核流，批次id:{}",a.getId());
				this.confirmationCheck(request,Boolean.TRUE,a.getId(),null);
			});
			outboundBatchesRecodeMapper.approveAuditStatusById(approveList,System.currentTimeMillis());
		}
		//审核驳回的批次 审核流状态改为驳回 修改批次审核状态0，上传状态0
		List<RefuseMsgDto> refuseList = replyMsg.getRefuseList();
		if (refuseList.size()>0){
			refuseList.forEach(r -> {
				OutboundBatchesRecode outboundBatchesRecode = outboundBatchesRecodeMapper.getById(r.getId());
				if (outboundBatchesRecode!=null && outboundBatchesRecode.getUploadStatus()==1&& outboundBatchesRecode.getAuditStatus()==1&& outboundBatchesRecode.getOnlineConfirm()==1){
					throw new BusinessException("客户已经在线签收，不可操作");
				}
			});

			refuseList.forEach(r -> {
				if (StringUtils.isEmpty(r.getRefuseReason())){
					r.setRefuseReason("");
				}
				logger.info("开始处理确认单审核驳回的审核流，批次id:{}",r.getId());
				this.confirmationCheck(request,Boolean.FALSE,r.getId(),r.getRefuseReason());
				outboundBatchesRecodeMapper.refuseAuditStatusById(r,System.currentTimeMillis());
			});
		}
		//判断订单下是否所有批次都审核通过，通过则修改saleorder订单确认单审核状态为审核通过，否则改为审核不通过
		Integer saleOrderId = replyMsg.getSaleOrderId();
		Express express = new Express();
		express.setSaleorderId(saleOrderId);
		List<Express> expressList = expressMapper.getExpressInfoConfirmation(express);

		List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(saleOrderId);
		List<Express> buyExpressList = new ArrayList<>();
		if (saleorderGoods.size()>0){
			List<Integer> listSale = saleorderGoods.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
			express.setRelatedIds(listSale);
			express.setSaleorderId(saleOrderId);
			buyExpressList = expressMapper.getBuyExpressList(express);
		}
		expressList.addAll(buyExpressList);
		List<String> batchNos = expressList.stream().map(Express::getBatchNo).distinct().collect(Collectors.toList());
		List<OutboundBatchesRecode> allByBatchNoList = new ArrayList<>();
		if (batchNos.size()>0){
			allByBatchNoList = outboundBatchesRecodeMapper.findAllByBatchNoList(batchNos);
		}

		boolean allApprove = allByBatchNoList.stream().allMatch(o -> 1 == o.getAuditStatus());
		if (allApprove){
			saleorderMapper.updateConfirmationFormAuditById(saleOrderId,CONFIRMATION_FORM_AUDIT_PASS);
		}else {
			saleorderMapper.updateConfirmationFormAuditById(saleOrderId,CONFIRMATION_FORM_AUDIT_REFUSE);
		}
	}



	private ResultInfo confirmationCheck(HttpServletRequest request,boolean passBoolean,Integer batchId,String refuseReason){
		// 2.审核通过或驳回
		Boolean pass = passBoolean;
		String comment = pass ? "审核通过" : "审核不通过";
		User user = getSessionUser(request);
		Map<String, Object> variables = new HashMap<>();
		variables.put("pass", pass);
		variables.put("updater", user.getUserId());
		variables.put("autoCheckAptitude", false);
		variables.put("rejectReason", refuseReason);
		variables.put("processDefinitionKey", "confirmationOrderVerify");
		// 审批操作
		try {
			String businessKey = "confirmationOrderVerify_" + batchId;
			Task taskInfo = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
			if (Objects.isNull(taskInfo)) {
				return new ResultInfo<>(-1, "流程此节点已操作完成，请确认");
			}
			String taskId = taskInfo.getId();
			int statusInfo = 0;
			if (Boolean.FALSE.equals(pass)) {
				// 如果审核不通过
				statusInfo = 2;
				// 回写数据的表在db中
				variables.put("db", 2);
				verifiesRecordService.saveVerifiesInfo(taskId, statusInfo);
			}
			if (org.apache.commons.lang3.StringUtils.isNotBlank(refuseReason)){
				comment = refuseReason;
			}
			ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
			// 如果未结束添加审核对应主表的审核状态
			if (complementStatus != null) {
				// 审核节点操作失败
				if (complementStatus.getCode().equals(-1)) {
					return complementStatus;
				}
				if (!"endEvent".equals(complementStatus.getData())) {
					verifiesRecordService.saveVerifiesInfo(taskId, statusInfo);
				}
			}
		} catch (Exception e) {
			logger.error("确认单批次通过或驳回异常，complementTaskParallel:", e);
			return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
		}
		return ResultInfo.error();
	}


	@Override
	public void updateShareQuoteInfo(Integer quoteorderId, Saleorder saleorder) {
		updateReferenceCostPrice(quoteorderId, null);

		// 修改报价跟单状态为"已成单"
		Quoteorder quote = new Quoteorder();
		quote.setQuoteorderId(quoteorderId);
		quote.setFollowOrderStatus(1);
		quote.setFollowOrderTime(DateUtil.sysTimeMillis());
		quoteorderMapper.updateQuote(quote);
		BussinessChance bussinessChance = quoteorderMapper.getBussinessChanceByQuoteOrderId(quoteorderId);
		if(bussinessChance != null){
			// 修改商机跟单状态为"已订单"
			BussinessChance updateChance = new BussinessChance();
			updateChance.setBussinessChanceId(bussinessChance.getBussinessChanceId());
			updateChance.setStatus(3);//
			updateChance.setStage(5);// Aadi 灵犀CRM项目，报价单前台商城下单即为已订单，非已订单-商机阶段
			updateChance.setWinningOrderTime(saleorder.getAddTime()==null?(new Date()):new Date(saleorder.getAddTime()));//更新赢单时间
			bussinessChanceMapper.updateByPrimaryKeySelective(updateChance);
		}

		quoteInfoService.saveQuSaleHist(quoteorderId,saleorder.getSaleorderId());
	}

	@Override
	public boolean isExistQuSaleHist(Integer saleorderId) {
		return quoteInfoService.isExistQuSaleHist(saleorderId);
	}

    /**
	 *
     * @param fileName CK2407271000011.pdf
     * @return
     */
	@Override
	public ResultInfo checkJdBatchUpload(String fileName){

		//CK2407271000011.pdf截止.前面一部分
		String jdOrderNo= fileName.split("\\.")[0];
		SaleorderInfoDto saleorderInfoDto = saleorderInfoQueryApiService.queryInfoByJdOrderNo(jdOrderNo);
		//判断当前saleOrderInfoDto是否为空
		if(saleorderInfoDto == null){
			return ResultInfo.error("该京东订单号未匹配到订单");
		}
		//发货状态
		if(!ErpConst.TWO.equals(saleorderInfoDto.getDeliveryStatus())){
			return ResultInfo.error("该订单未全部发货");
        }
		//确认单审核状态
		if(!ErpConst.ZERO.equals(saleorderInfoDto.getConfirmationFormAudit())){
			return ResultInfo.error("该订单确认单已存在上传文件");
		}
		//确认单文件
//		List<Attachment> concatAttachmentList = attachmentMapper.getSaleorderAttachmentByIdAndType(saleorderInfoDto.getSaleorderId(),CommonConstants.ATTACHMENT_TYPE_980);
//		if(CollectionUtils.isNotEmpty(concatAttachmentList)){
//			return ResultInfo.error("该订单确认单已存在上传文件");
//		}

		//是否有已上传的合同
		List<Attachment> saleorderAttachmentList = attachmentMapper.getSaleorderAttachmentByIdAndType(saleorderInfoDto.getSaleorderId(),492);
		if(CollectionUtils.isNotEmpty(saleorderAttachmentList)){
			return ResultInfo.error("该订单合同已存在上传文件");
		}
		return ResultInfo.success(saleorderInfoDto);
	}



	@Transactional(rollbackFor = Exception.class)
	@Override
	public ResultInfo contractReturnSaveForOtherErp( List<Attachment> attachmentList, Integer saleorderId){
		long nowL = DateUtil.sysTimeMillis();
		for (Attachment attachment : attachmentList) {
			Attachment insertAttachment= new Attachment();
			insertAttachment.setRelatedId(saleorderId);
			insertAttachment.setAttachmentFunction(SysOptionConstant.ID_492);
			insertAttachment.setDomain(attachment.getDomain());
			insertAttachment.setUri(attachment.getFilePath());
			insertAttachment.setName(attachment.getFileName());
			insertAttachment.setOssResourceId(attachment.getOssResourceId());
			String suffix = attachment.getPrefix();
			insertAttachment.setSuffix(suffix);
			insertAttachment.setAttachmentType(SysOptionConstant.ID_461);
			insertAttachment.setCreator(1);
			insertAttachment.setAddTime(nowL);
			attachmentMapper.insertSelective(insertAttachment);
		}
		Attachment insertAttachment= new Attachment();
		insertAttachment.setRelatedId(saleorderId);
		insertAttachment.setAttachmentFunction(SysOptionConstant.ID_492);

		VerifiesInfoDto verifiesInfoDto = new VerifiesInfoDto();
		verifiesInfoDto.setRelateTable("T_SALEORDER");
		verifiesInfoDto.setVerifiesType(868);
		verifiesInfoDto.setRelateTableKey(saleorderId);
		verifiesInfoDto.setStatus(ErpConst.ONE);//审核通过
		verifiesInfoDto.setAddTime(System.currentTimeMillis());
		verifiesInfoDto.setModTime(System.currentTimeMillis());
		verifiesInfoDto.setVerifyUsername("admin");
		verifiesInfoDto.setApplyerId(0);
		verifiesInfoDto.setVerifyerId(0);
		verifiesInfoApiService.saveVerifiesInfo(verifiesInfoDto);//插入一条自动审核通过的记录
		updateContractStatus(insertAttachment, OrderConstant.ORDER_CONTRACT_ISRETURN, "upload", OrderConstant.ORDER_DELIVERYORDER_ISRETURN);

//		User user;
//		if(creat > 0){
//			user = userService.getUserById(creat);
//		}else {
//			user = userMapper.getUserByName("njadmin");
//		}
//		Saleorder baseSaleorderInfo = saleorderMapper.getBaseSaleorderInfo(saleorderId);
//		//开启流程实例
//		saleContractAutoAuditService.startProcessInstance(baseSaleorderInfo,user);
		return ResultInfo.success();
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public ResultInfo contractReturnSave(Integer creat, List<Attachment> attachmentList, Integer saleorderId) {
		long nowL = DateUtil.sysTimeMillis();

		for (Attachment attachment : attachmentList) {
			Attachment insertAttachment= new Attachment();
			insertAttachment.setRelatedId(saleorderId);
			insertAttachment.setAttachmentFunction(SysOptionConstant.ID_492);
			insertAttachment.setDomain(attachment.getDomain());
			insertAttachment.setUri(attachment.getFilePath());
			insertAttachment.setName(attachment.getFileName());
			insertAttachment.setOssResourceId(attachment.getOssResourceId());
			String suffix = attachment.getPrefix();
			insertAttachment.setSuffix(suffix);
			insertAttachment.setAttachmentType(SysOptionConstant.ID_461);
			insertAttachment.setCreator(creat);
			insertAttachment.setAddTime(nowL);
			attachmentMapper.insertSelective(insertAttachment);
		}
		Attachment insertAttachment= new Attachment();
		insertAttachment.setRelatedId(saleorderId);
		insertAttachment.setAttachmentFunction(SysOptionConstant.ID_492);
		updateContractStatus(insertAttachment, OrderConstant.ORDER_CONTRACT_ISRETURN, "upload", OrderConstant.ORDER_DELIVERYORDER_ISRETURN);

		User user;
		if(creat > 0){
			user = userService.getUserById(creat);
		}else {
			user = userMapper.getUserByName("njadmin");
		}
		Saleorder baseSaleorderInfo = saleorderMapper.getBaseSaleorderInfo(saleorderId);
		//开启流程实例
		saleContractAutoAuditService.startProcessInstance(baseSaleorderInfo,user);
		return ResultInfo.success();
	}

	@Override
	public void updateContractReas(String comment, Boolean pass, String businessKey, Task taskInfo) {
		if(pass){
			return;
		}
		if("contractReturnVerify".equals(businessKey.split("_")[0])&& "质量部质量专员".equals(taskInfo.getName())) {
			Integer orderId = Integer.valueOf(businessKey.split("_")[1]);
			Saleorder saleorder = saleorderMapper.getSaleOrderById(orderId);
			if(saleorder != null){
				saleorderMapper.updateContractReas(orderId, StrUtil.sub(comment,0,255));
			}

		}
	}

	@Override
	public List<Integer> needSalesDirectorVerifyUserIds(Integer saleorderId) {

		List<Integer> result = new ArrayList<>();

		try {

			SaleorderAttributionInfoDto saleOrderAttributionInfo = saleorderMapper.getSaleOrderAttributionInfo(saleorderId);

			if (Objects.isNull(saleOrderAttributionInfo) || !saleOrderAttributionInfo.checkData()) {
				log.info("销售订单无需销售总监审核-订单归属信息不完善,saleorderId:{}", saleorderId);
				return result;
			}


			if (NO_PAYMENT_PERIOD_TYPE.contains(saleOrderAttributionInfo.getPaymentType())) {
				log.info("销售订单无需销售总监审核-非账期订单,saleorderId:{}", saleorderId);
				return result;
			}

			List<Integer> traderWhiteList = JSON.parseArray(saleorderVerifyWhiteList, Integer.class);

			Integer traderId = saleOrderAttributionInfo.getTraderId();

			if (traderWhiteList.contains(traderId)) {
				log.info("销售订单无需销售总监审核-客户在白名单中,saleorderId:{},traderId:{}", saleorderId, traderId);
				return result;
			}

			Integer salesDeptId = saleOrderAttributionInfo.getSalesDeptId();

			// 查询订单归属一级部门
			Integer belongPrimaryOrgId = queryBelongPrimaryOrgId(salesDeptId);

			if (Objects.isNull(belongPrimaryOrgId)) {
				log.info("销售订单无需销售总监审核-未查询到订单归属一级部门,saleorderId:{},salesDeptId:{}", saleorderId, salesDeptId);
				return result;
			}

			List<Integer> salesDirectorIdList = userMapper.findSalesDirectorByOrgId(belongPrimaryOrgId);
			if (CollectionUtils.isEmpty(salesDirectorIdList)) {
				log.info("销售订单无需销售总监审核-未查询到部门下的销售总监,saleorderId:{},belongPrimaryOrgId:{}", saleorderId, belongPrimaryOrgId);
				return result;
			}

			List<Integer> excludeUserIdList = JSON.parseArray(saleorderVerifyExcludeUserId, Integer.class);

			salesDirectorIdList.stream().filter(userId -> Objects.nonNull(userId) && !excludeUserIdList.contains(userId)).forEach(result::add);

			log.info("销售订单是否需要销售总监审核结果为:saleorderId:{},result:{}", saleorderId, JSON.toJSONString(result));

			return result;

		} catch (Exception e) {

			log.error("销售订单是否需要销售总监审核异常,saleorderId:{}", saleorderId, e);

			return result;
		}
	}

	private Integer queryBelongPrimaryOrgId(Integer orgId) {
		Organization organization = organizationMapper.selectByPrimaryKey(orgId);
		if (Objects.nonNull(organization) && Objects.nonNull(organization.getLevel()) && organization.getLevel() > 0) {
			if (organization.getLevel() < ErpConst.FOUR) {
				return organization.getOrgId();
			} else {
				return queryBelongPrimaryOrgId(organization.getParentId());
			}
		} else {
			return null;
		}
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResultInfo updateInvoiceProperty(OrderRatioVo orderRatioVo) {
		//更新invoiceApply
		InvoiceApplyDto invoiceApplyDto = new InvoiceApplyDto();
		invoiceApplyDto.setInvoiceApplyId(orderRatioVo.getInvoiceApplyId());
		if (ErpConst.THREE.equals(orderRatioVo.getInvoiceProperty())){
			invoiceApplyDto.setIsAuto(ErpConst.FOUR);
		}else if (ErpConst.TWO.equals(orderRatioVo.getInvoiceProperty())){
			invoiceApplyDto.setIsAuto(ErpConst.THREE);
		}else if (null != orderRatioVo.getInvoiceProperty()) {
			invoiceApplyDto.setIsAuto(ErpConst.TWO);
		}
		invoiceApplyDto.setInvoiceProperty(orderRatioVo.getInvoiceProperty());
		invoiceApplyMapper.updateInvoiceProperty(invoiceApplyDto);

		return ResultInfo.success();
	}


	@Override
	public ResultInfo<?> closeOrder(Saleorder saleorder){
			CurrentUser user = CurrentUser.getCurrentUser();
			if (user != null) {
				saleorder.setUpdater(user.getId());
				saleorder.setModTime(DateUtil.sysTimeMillis());
			}
			ResultInfo<?> resultInfo ;
			resultInfo = this.closeSaleorder(saleorder);
			Saleorder order = this.getsaleorderbySaleorderId(saleorder.getSaleorderId());
			try{
				//VDERP-5010 关闭订单后联动关闭报价、商机
				if( resultInfo.getCode().equals(0)
//						&& (order.getOrderType() == 0 || order.getOrderType() == 1)
						&& order.getQuoteorderId() != null ){
					Quoteorder quoteorder = quoteService.relateCloseQuote(order.getQuoteorderId(),"SYS_AUTO_CLOSE_TYPE_2");
					logger.info("联动关闭报价单：" + JSON.toJSONString(quoteorder));
					if (quoteorder != null && quoteorder.getBussinessChanceId() != null){
						BussinessChance bussinessChance = bussinessChanceService.relateCloseBussChance(quoteorder.getBussinessChanceId(),"SYS_AUTO_CLOSE_TYPE_2",3);
						logger.info("联动关闭商机：" + JSON.toJSONString(bussinessChance));
					}
				}
			}catch (Exception e){
				logger.info("订单关闭后联动关闭报价、商机失败："+ e.getMessage());
			}

			if((order.virtualBdOrder() || order.getOrderType()==1) && resultInfo.getCode().equals(0)) {
				resultInfo = this.closeBDSaleorder(order);

				//如果使用了优惠券，则归还优惠券
				SaleorderCoupon saleorderCoupon = saleorderCouponMapper.selectBySaleOrderId(saleorder.getSaleorderId());

				if(saleorderCoupon != null){

					Saleorder saleorderDB = this.saleorderMapper.getSaleOrderById(saleorder.getSaleorderId());

					Map<String,String> couponReturnReqMap = new HashMap<>();
					couponReturnReqMap.put("couponCode",saleorderCoupon.getCouponCode());
					couponReturnReqMap.put("traderId",saleorderDB.getTraderId() + Strings.EMPTY);

					logger.info("归还优惠券消息 start=======" + JSON.toJSONString(couponReturnReqMap));
					msgProducer.sendMsg(RabbitConfig.MARKET_RETURNCOUPON_EXCHANGE, RabbitConfig.MARKET_RETURNCOUPON_ROUTINGKEY, JSON.toJSONString(couponReturnReqMap));
					logger.info("归还优惠券消息 end=======");

					//清空订单优惠券
					this.clearCoupon(saleorder.getSaleorderId());
				}

			}
			if(order.getOrderType()==5 && resultInfo.getCode().equals(0)){
				//如果使用了优惠券，则归还优惠券
				SaleorderCoupon saleorderCoupon = saleorderCouponMapper.selectBySaleOrderId(saleorder.getSaleorderId());

				if(saleorderCoupon != null){

					Saleorder saleorderDB = this.saleorderMapper.getSaleOrderById(saleorder.getSaleorderId());

					Map<String,String> couponReturnReqMap = new HashMap<>();
					couponReturnReqMap.put("couponCode",saleorderCoupon.getCouponCode());
					couponReturnReqMap.put("traderId",saleorderDB.getTraderId() + Strings.EMPTY);

					logger.info("归还优惠券消息 start=======" + JSON.toJSONString(couponReturnReqMap));
					msgProducer.sendMsg(RabbitConfig.MARKET_RETURNCOUPON_EXCHANGE, RabbitConfig.MARKET_RETURNCOUPON_ROUTINGKEY, JSON.toJSONString(couponReturnReqMap));
					logger.info("归还优惠券消息 end=======");

					//清空订单优惠券
					this.clearCoupon(saleorder.getSaleorderId());
				}
			}


			if (resultInfo != null && resultInfo.getCode().equals(0) && user.getCompanyId().equals(1)) {
				vedengSoapService.orderSync(saleorder.getSaleorderId());
				Integer saleorderId = saleorder.getSaleorderId();

				// 如果订单是耗材商城的订单，需要同步订单状态到耗材商城
				Saleorder saleorder2 = new Saleorder();
				saleorder2.setSaleorderId(saleorderId);
				saleorder2 = this.getSaleOrderInfo(saleorder2);
				if (saleorder2.getOrderType() == 5) {
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("orderNo", saleorder2.getSaleorderNo());
					map.put("orderStatus", 3);
					// 同步订单状态到耗材商城
					hcSaleorderService.putOrderStatustoHC(map);
					// 如果该订单已存在付款,且订单未生效，调用退款接口在线退款（三期再做）
//						if ((saleorder.getPaymentStatus() == 1 || saleorder.getPaymentStatus() == 2)
//								&& saleorder.getValidStatus() == 0) {
//
//						}
				}

			}

			//更新库存服务占用数量
			saleorder.setOperateType(StockOperateTypeConst.COLES_ORDER);
			int i = warehouseStockService.updateOccupyStockService(saleorder, 0);
			logicalSaleorderChooseServiceImpl.closeOrdersynchronizeStockData(saleorder);

			//更新采购预警信息
			this.updateBuyWarnStatus(saleorder);

			Saleorder saleorderDB = this.saleorderMapper.getSaleOrderById(saleorder.getSaleorderId());

			//删除这个销售单对应的 风控商品的代办
			todoListService.deleteByBuzTypeAndBuzExtra(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(),saleorderDB.getSaleorderNo());

			orderReviewProcessService.dealSaleOrderReviewProcess(saleorder.getSaleorderId(), user != null && user.getId() != null ? user.getId() : 1);
			//关闭订单处理订单的账期业务
			try {
				orderAccountPeriodService.dealCloseOrderCustomerBillPeriodOfOrder(saleorder);
			} catch (CustomerBillPeriodException e) {
				logger.error("dealCustomerBillPeriodUseDetailOfOrder message:{}", e.getMessage());
				return ResultInfo.error(e.getMessage());
			} catch (Exception e){
				logger.error("dealCustomerBillPeriodUseDetailOfOrder error", e);
			}

			return resultInfo;
		}


	private void updateSaleorderInvoiceContactInfo(Integer orderId, Integer traderContactId, Integer traderContactAddressId, Integer sendInvoice, Integer invoiceType, Integer isAuto, Integer type) {
		if (SysOptionConstant.ID_504.equals(type)) {
			AfterSalesDetailApiDto afterSalesDetailApiDto = afterSalesApiService.getAfterSalesDetailByAfterSalesId(orderId);
			AfterSalesDetail updateAfterSalesDetail = new AfterSalesDetail();
			updateAfterSalesDetail.setAfterSalesDetailId(afterSalesDetailApiDto.getAfterSalesDetailId());
			updateAfterSalesDetail.setInvoiceType(invoiceType);
			updateAfterSalesDetail.setIsSendInvoice(sendInvoice);
			if(ErpConst.ONE.equals(sendInvoice) && ObjectUtil.isNotNull(traderContactId) && ObjectUtil.isNotNull(traderContactAddressId)){
				updateAfterSalesDetail.setInvoiceTraderContactId(traderContactId);
				TraderContact traderContact = traderContactMapper.selectByPrimaryKey(traderContactId);
				updateAfterSalesDetail.setInvoiceTraderContactName(traderContact.getName());
				updateAfterSalesDetail.setInvoiceTraderContactMobile(traderContact.getMobile());
				updateAfterSalesDetail.setInvoiceTraderContactTelephone(traderContact.getTelephone());

				updateAfterSalesDetail.setInvoiceTraderAddressId(traderContactAddressId);
				TraderAddress traderAddress = traderAddressMapper.selectByPrimaryKey(traderContactAddressId);
				updateAfterSalesDetail.setInvoiceTraderArea(getAddressByAreaId(traderAddress.getAreaId()));
				updateAfterSalesDetail.setInvoiceTraderAddress(traderAddress.getAddress());
			}
			log.info("开票申请修改开票方式，afterSalesDetail：{}", JSONUtil.toJsonStr(updateAfterSalesDetail));
			afterSalesDetailMapper.updateByPrimaryKeySelective(updateAfterSalesDetail);
		} else if (SysOptionConstant.ID_505.equals(type)) {
			Saleorder updateSaleorder = new Saleorder();
			updateSaleorder.setInvoiceType(invoiceType);
			updateSaleorder.setSaleorderId(orderId);
			updateSaleorder.setIsSendInvoice(sendInvoice);
			updateSaleorder.setInvoiceMethod(isAuto);
			if(ErpConst.ONE.equals(sendInvoice) && ObjectUtil.isNotNull(traderContactId) && ObjectUtil.isNotNull(traderContactAddressId)){
				updateSaleorder.setInvoiceTraderContactId(traderContactId);
				TraderContact traderContact = traderContactMapper.selectByPrimaryKey(traderContactId);
				updateSaleorder.setInvoiceTraderContactName(traderContact.getName());
				updateSaleorder.setInvoiceTraderContactMobile(traderContact.getMobile());
				updateSaleorder.setInvoiceTraderContactTelephone(traderContact.getTelephone());
				updateSaleorder.setInvoiceTraderAddressId(traderContactAddressId);
				TraderAddress traderAddress = traderAddressMapper.selectByPrimaryKey(traderContactAddressId);
				updateSaleorder.setInvoiceTraderArea(getAddressByAreaId(traderAddress.getAreaId()));
				updateSaleorder.setInvoiceTraderAddress(traderAddress.getAddress());
				updateSaleorder.setInvoiceTraderAreaId(traderAddress.getAreaId());
			}
			log.info("开票申请修改开票方式，updateSaleorder：{}", JSONUtil.toJsonStr(updateSaleorder));
			saleorderMapper.updateByPrimaryKeySelective(updateSaleorder);
		}
	}


	@Autowired
	private BaseCompanyInfoApiService baseCompanyInfoApiService;

	@Autowired
	private SyncOutInRelateApiService syncOutInRelateApiService;


	@Autowired
	private SyncDataErpApiService syncDataErpApiService;
	/**
	 * 建立外部单号和内部销售单号的统一关系
	 * @param originBuyOrderNo
	 * @param saleorderNo
	 */
	private void addOutInRelateOrder(String originBuyOrderNo,String saleorderNo,BaseCompanyInfoDto baseCompanyInfoDto){
		//检查来源ERP的信息
		SyncOutInRelateDto record = new SyncOutInRelateDto();
		record.setBusinessType(SyncDataTypeEnum.BUYORDER_TO_SALEORDER.getDataType());
		record.setOutBusinessNo(originBuyOrderNo);
		record.setInBusinessNo(saleorderNo);
		record.setSourceErp(baseCompanyInfoDto.getCompanyShortName());
		record.setRequestContent("{}");
		record.setProcessStatus(ErpConst.TWO);
		record.setCreateTime(new Date());
		record.setUpdateTime(new Date());
		record.setCreateUser("system");
		record.setUpdateUser("system");
		record.setIsDeleted(false);
		syncOutInRelateApiService.insert(record);
	}

	private SyncOutInRelateDto checkOrderExist(String originBuyOrderNo){
		List<SyncOutInRelateDto> syncOutInRelateDtoList = syncOutInRelateApiService.selectByOutBusinessNo(originBuyOrderNo);
		if(CollectionUtils.isNotEmpty(syncOutInRelateDtoList)){
			return syncOutInRelateDtoList.get(0);
		}
		return null;

	}

	@Autowired
	SaleorderCurrentOrgAndUserIdSync saleorderCurrentOrgAndUserIdSync;
	@Override
	public Saleorder saveBDAddSaleorderForSyncHttp(OrderData orderData) throws Exception {
		Saleorder s = new Saleorder();
		String originBuyOrderNo = orderData.getOriginBuyOrderNo();
		//根据来源ERP的采购单，在当前已存在的表里检查是否已经处理过了（防止异常情况，幂等性检查）
		SyncOutInRelateDto syncOutInRelateDto = checkOrderExist(orderData.getOriginBuyOrderNo());
		if(syncOutInRelateDto!=null) {
			logger.info("订单号已存在！",syncOutInRelateDto.getInBusinessNo());
			s.setSaleorderNo(syncOutInRelateDto.getOutBusinessNo());
			return s;
		}
		BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByCompanyName(orderData.getCompanyName());
		if(baseCompanyInfoDto == null){
			logger.warn("来源ERP的主体信息缺失，请检查{}",orderData.getCompanyName());
			return null;
		}
		Long time = DateUtil.sysTimeMillis();
		Saleorder saleorder = new Saleorder();
		saleorder.setOrderType(1);  //订单类型
		saleorder.setTakeTraderName(orderData.getUsername());//收货公司
		saleorder.setTakeTraderContactName(orderData.getDeliveryUserName());//收货联系人名称
		saleorder.setTakeTraderArea(orderData.getDeliveryUserArea());//收货地区
		saleorder.setTakeTraderAddress(orderData.getDeliveryUserAddress());//收货地址
		saleorder.setTakeTraderContactMobile(orderData.getDeliveryUserPhone());//收货人手机
		saleorder.setTakeTraderContactTelephone(orderData.getDeliveryUserTel());//收货人电话
		saleorder.setBdtraderComments(orderData.getRemakes());//客户备注
		saleorder.setHaveAccountPeriod(0);//账期支付
		saleorder.setTotalAmount(orderData.getTotalCouponedAmount());//订单总价
		saleorder.setIsSendInvoice(orderData.getIsSendInvoice());//是否寄送发票
		saleorder.setInvoiceType(orderData.getInvoiceType());//线上订单（BD订单）生成后，默认发票类型改为【13%增值税专用发票（寄送）】
		String createName = "njadmin";
		saleorder.setPaymentMode(0);//支付方式
		saleorder.setInvoiceMethod(orderData.getInvoiceMethod()); //开票方式1手动纸质开票、2自动纸质开票、3自动电子发票
		saleorder.setIsDelayInvoice(0);//是否延迟开票0否1是
		saleorder.setPaymentType(419);//付款方式 先货后款，预付0% -- 11. 功能调整：通过采购单生成的对方销售订单的付款计划调整为"先款后货，预付100%"
		saleorder.setTraderName(orderData.getUsername());//如果没有关联erp客户就显示用户名称为注册时的企业名称
		saleorder.setStatus(0);//订单状态
		saleorder.setQuoteorderNo(orderData.getOrderNo());//报价单号
		saleorder.setValidStatus(0);//生效状态
		saleorder.setValidTime(0L);//生效时间
		saleorder.setAddTime(time);
		saleorder.setParentId(0);
		saleorder.setCompanyId(1);//南京公司
		saleorder.setPurchaseStatus(0);//采购状态0未采购（默认）
		saleorder.setLockedStatus(0);//锁定状态0未锁定
		saleorder.setInvoiceStatus(0);//开票状态0未开票
		saleorder.setInvoiceTime(0L);//开票时间
		saleorder.setPaymentStatus(0);//付款状态 0未付款
		saleorder.setPaymentTime(0L);//付款时间
		saleorder.setDeliveryStatus(0);//发货状态0未发货
		saleorder.setDeliveryTime(0L);//发货时间
		saleorder.setArrivalTime(0L);//客户收货时间
		saleorder.setServiceStatus(0);//售后状态 0无
		saleorder.setArrivalStatus(0);//客户收货状态0未收货
		saleorder.setHaveAdvancePurchase(0);//有提前采购 0
		saleorder.setAdvancePurchaseStatus(0);//0无提前采购需求
		saleorder.setAdvancePurchaseTime(0L);//提前采购申请时间
		saleorder.setIsUrgent(0);//是否加急0否1是
		BigDecimal a = new BigDecimal(0);
		saleorder.setUrgentAmount(a);//加急费用
		saleorder.setHaveCommunicate(0);//有无沟通记录
		saleorder.setSyncStatus(0);//0未同步
		saleorder.setPrepaidAmount(orderData.getTotalCouponedAmount());//预付金额
		User user = userMapper.getByUsername(createName, 1);
		saleorder.setCreator(user.getUserId());
		if (orderData.getDeliveryLevel3Id() != null) {
			saleorder.setTakeTraderAreaId(Integer.valueOf(orderData.getDeliveryLevel3Id()));//地区id
		}
		saleorder.setCreateMobile(orderData.getPhone());
		saleorder.setIsCoupons(orderData.getIsCoupons());
		saleorder.setCouponInfo(orderData.getCouponInfo());
		saleorder.setDeliveryType(orderData.getDeliveryType());
		saleorder.setDeliveryClaim(orderData.getDelayDelivery());
		saleorder.setIsPrintout(orderData.getIsPrintout());
		saleorder.setLogisticsComments(orderData.getLogisticsComments());
		saleorder.setAdditionalClause(orderData.getAdditionalClause());
		saleorder.setComments(orderData.getComments());
		saleorder.setBillPeriodSettlementType(CustomerBillPeriodSettlementTypeEnum.ORDER_INVOICE.getCode());
		//BD订单归属销售
		User belongUser = new User();
		if (orderData.getTraderId() != null) {
			belongUser = userService.getBDUserInfoByTraderId(orderData.getTraderId());
			if (belongUser != null && belongUser.getUserId() != null) {
				saleorder.setUserId(belongUser.getUserId());
			}
			if (belongUser != null && belongUser.getOrgId() != null) {
				saleorder.setOrgId(belongUser.getOrgId());
			}
			if (belongUser != null && belongUser.getOrgName() != null) {
				saleorder.setOrgName(belongUser.getOrgName());
			}
			if (belongUser != null && belongUser.getTraderId() != null && belongUser.getTraderId() != 0) {
				//判断交易者下有无地址
				Integer deliveryLevel1Id = 0;
				if (orderData.getDeliveryLevel1Id() != null && !orderData.getDeliveryLevel1Id().equals("")) {
					deliveryLevel1Id = Integer.valueOf(orderData.getDeliveryLevel1Id());
				}
				Integer deliveryLevel2Id = 0;
				if (orderData.getDeliveryLevel2Id() != null && !orderData.getDeliveryLevel2Id().equals("")) {
					deliveryLevel2Id = Integer.valueOf(orderData.getDeliveryLevel2Id());
				}
				Integer deliveryLevel3Id = 0;
				if (orderData.getDeliveryLevel3Id() != null && !orderData.getDeliveryLevel3Id().equals("")) {
					deliveryLevel3Id = Integer.valueOf(orderData.getDeliveryLevel3Id());
				}
				Integer traderAddressId = vailTraderAddress(belongUser.getTraderId(), deliveryLevel1Id, deliveryLevel2Id, deliveryLevel3Id, orderData.getDeliveryUserAddress());
				saleorder.setTakeTraderAddressId(traderAddressId);
				saleorder.setTakeTraderAreaId(deliveryLevel3Id);
				//判断交易者联系人是否存在并保存
				saleorder.setTraderId(belongUser.getTraderId());
				Integer traderContactId = vailTraderContact(belongUser.getTraderId(), orderData.getDeliveryUserName(), orderData.getDeliveryUserTel(), orderData.getDeliveryUserPhone());
				saleorder.setTakeTraderContactId(traderContactId);
				//判断保存注册用户信息是否在客户联系人中,如果没有则添加
				Trader trader = traderMapper.getTraderInfoByDeal(belongUser.getTraderId(),orderData.getPhone(),orderData.getDeliveryUserAddress(),orderData.getDeliveryUserName());
				if (trader != null) {
					saleorder.setTraderName(trader.getTraderName());
					saleorder.setCustomerType(trader.getCustomerType());
					saleorder.setCustomerNature(trader.getCustomerNature());
					if (trader.getCustomerNature().equals(466)) {//终端
						saleorder.setTerminalTraderType(trader.getCustomerType());
						saleorder.setTerminalTraderName(trader.getTraderName());
						saleorder.setTerminalTraderId(trader.getTraderId());
						//获取交易者经营区域
						String areaIds = trader.getAreaIds();
						if (org.apache.commons.lang.StringUtils.isNotBlank(areaIds)) {
							//查询地区
							String traderBussinessArea = regionService.getRegionNameStringByMinRegionIds(areaIds);
							String[] areaid = areaIds.split(",");
							Integer areaid2 = Integer.valueOf(areaid[areaid.length - 1]);
							saleorder.setSalesAreaId(areaid2);
							saleorder.setSalesArea(traderBussinessArea);
						}
					}
					saleorder.setTraderContactId(trader.getTraderContactId());
					if (trader.getTraderContactName() != null && !trader.getTraderContactName().isEmpty()) {
						saleorder.setTraderContactName(trader.getTraderContactName());
					}
					if (trader.getTraderContactMobile() != null && !trader.getTraderContactMobile().isEmpty()) {
						saleorder.setTraderContactMobile(trader.getTraderContactMobile());
					}
					saleorder.setTraderContactTelephone(trader.getTraderContactTelephone());
					saleorder.setTraderAreaId(trader.getAreaId());
					saleorder.setTraderAddressId(trader.getTraderAddressId());
					saleorder.setTraderAddress(trader.getTraderAddress());
					saleorder.setCustomerLevelStr(trader.getCustomerLevelStr());
					//查询地区
					String area = regionService.getRegionNameStringByMinRegionIds(trader.getAreaIds());
					saleorder.setTraderArea(area);
				}
			}
		}
		//保存订单商品
		List<OrderGoodsData> goodsList = orderData.getGoodsList();
		List<SaleorderGoods> salegoodsList = new ArrayList<SaleorderGoods>();
		for (OrderGoodsData orderGoodsData : goodsList) {
			SaleorderGoods saleorderGoods = new SaleorderGoods();
			saleorderGoods.setSku(orderGoodsData.getSkuNo());
			if (belongUser != null && belongUser.getUserId() != null) {
				saleorderGoods.setCreator(belongUser.getUserId());
				saleorderGoods.setUpdater(belongUser.getUserId());
			}
			saleorderGoods.setAddTime(DateUtil.sysTimeMillis());
			saleorderGoods.setNum(orderGoodsData.getProductNum());
			saleorderGoods.setModTime(DateUtil.sysTimeMillis());
			saleorderGoods.setGoodsId(goodsService.getGoodsIdBySku(orderGoodsData.getSkuNo()));
			saleorderGoods.setDeliveryDirect(0);
			saleorderGoods.setDeliveryDirectComments(orderGoodsData.getDelilveryDirectComments());
			saleorderGoods.setDeliveryCycle(orderGoodsData.getDeliveryCycle());
			saleorderGoods.setGoodsComments(orderGoodsData.getGoodsComments());
			saleorderGoods.setInsideComments(orderGoodsData.getInsideComments());
			saleorderGoods.setPrice(orderGoodsData.getJxSalePrice());
			// 是否含安调
			saleorderGoods.setHaveInstallation(orderGoodsData.getHaveInstallation());
			saleorderGoods.setJxSalePrice(orderGoodsData.getJxSalePrice());
			//添加优惠券相关数据
			saleorderGoods.setIsCoupons(orderGoodsData.getIsCoupons());
			saleorderGoods.setMaxSkuRefundAmount(orderGoodsData.getSkuAmount());
			saleorderGoods.setRealPrice(orderGoodsData.getJxSalePrice());
			salegoodsList.add(saleorderGoods);
		}
		saleorder.setGoodsList(salegoodsList);
		//VDERP-6918支持前台切换BD订单收票模块信息数据源start
		Integer invoiceTraderContactId = vailTraderContact(belongUser.getTraderId(), orderData.getInvoiceTraderContactName(), orderData.getInvoiceTraderContactTelephone(), orderData.getInvoiceTraderContactMobile());
		saleorder.setInvoiceTraderContactId(invoiceTraderContactId);
		//收票联系人地址
		saleorder.setInvoiceTraderContactName(orderData.getInvoiceTraderContactName());
		//收票联系人电话
		saleorder.setInvoiceTraderContactMobile(orderData.getInvoiceTraderContactMobile());
		//收票联系人手机
		saleorder.setInvoiceTraderContactTelephone(orderData.getInvoiceTraderContactTelephone());
		//收票地址
		saleorder.setInvoiceTraderAddress(orderData.getInvoiceTraderAddress());
		//收票区域
		saleorder.setInvoiceTraderArea(orderData.getInvoiceTraderArea());
		//是否寄送发票
		saleorder.setIsSendInvoice(orderData.getIsSendInvoice());
		// 兼容历史数据
		saleorder.setInvoiceMethod(orderData.getInvoiceMethod());
		//开票类型
		saleorder.setInvoiceType(orderData.getInvoiceType());
		//新订单流标记状态
		saleorder.setIsNew(orderInfoSyncService.setSaleorderIsNewByOrg(saleorder.getSaleorderNo(), saleorder.getTraderId()));
		logger.info("请求DB保存BD订单信息 saleorder:{}", JSON.toJSONString(saleorder));
		//前台订单 设置推送状态为1
		saleorder.setSendToPc(1);
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {};
		ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + "order/saleorder/saveBDAddSaleorder.htm", saleorder, clientId, clientKey, TypeRef2);
		Saleorder res = (Saleorder) result2.getData();
		if(Objects.isNull(res)) {
			throw new Exception("dbCenter保存销售单信息失败！");
		}
		try {
			if(res != null){
				//VDERP-2263   订单售后采购改动通知
				orderCommonService.updateSaleOrderDataUpdateTime(res.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_GENERATE);
			}
			if (belongUser != null && belongUser.getUserId() != null && belongUser.getUserId() != 0) {
				//判读端erp客户是否禁用
				quoteService.getIsDisabled(res.getCreateMobile(), belongUser.getUserId(), res.getSaleorderNo(), res.getSaleorderId());
			} else {
				//发送消息推送
				quoteService.sendAllocation(orderData.getPhone(), null);
			}
			Saleorder saleorderUpt = new Saleorder();
			saleorderUpt.setSaleorderId(res.getSaleorderId());
			saleorderUpt.setTerminalTraderName("");
			saleorderUpt.setSalesArea("");
			saleorderUpt.setSalesAreaId(ErpConst.ZERO);
			saleorderUpt.setOriginBuyOrderNo(orderData.getOriginBuyOrderNo());
			saleorderUpt.setPaymentType(saleorder.getPaymentType());
			saleorderUpt.setIsPrintout(0);
			saleorderUpt.setSaleorderNo(orderNoDict.getOrderNum(res.getSaleorderId(), 3));
//    		saleorderUpt.setPeriodDay(0);
//    		saleorderUpt.setPrepaidAmount(BigDecimal.ZERO);
//    		saleorderUpt.setAccountPeriodAmount(orderData.getTotalCouponedAmount());
			saleorderMapper.updateByPrimaryKeySelective(saleorderUpt);
			//更新商品明细普发直发属性
//			List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(res.getSaleorderId());
//    		if(CollectionUtils.isNotEmpty(saleorderGoodsList)){
//    			for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
//    					SaleorderGoods saleorderGoodsUpdate = new SaleorderGoods();
//    					Integer saleorderGoodsId = saleorderGoods.getSaleorderGoodsId();
//    					saleorderGoodsUpdate.setSaleorderGoodsId(saleorderGoodsId);
//    					saleorderGoodsUpdate.setDeliveryDirect(0);
//    					saleorderGoodsMapper.updateByPrimaryKeySelective(saleorderGoodsUpdate);
//    				}
//    		}

			List<Integer> saleOrderIdList = new ArrayList<>();
			saleOrderIdList.add(res.getSaleorderId());
			saleorderCurrentOrgAndUserIdSync.loadBizData(saleOrderIdList);
			//需要返回销售订单号
			res.setSaleorderNo(saleorderUpt.getSaleorderNo());
			//同步订单，更新终端和订单信息
//			OrderTerminalDto orderTerminalDto = new OrderTerminalDto();
//			orderTerminalDto.setBusinessId(res.getSaleorderId());
//			orderTerminalDto.setBusinessNo(saleorderUpt.getSaleorderNo());
//			orderTerminalDto.setAddTime(new Date());
//			orderTerminalDto.setCreator(1);
//			orderTerminalDto.setCreatorName("admin");
//			orderTerminalDto.setHosModel("0");
//			orderTerminalDto.setTerminalTraderNature(5605);
//			orderTerminalDto.setNatureTypeName("非公集团");
//			orderTerminalApiService.save(orderTerminalDto);
			addOutInRelateOrder(originBuyOrderNo,saleorderUpt.getSaleorderNo(),baseCompanyInfoDto);
			addOrderLogisticsSyncTask(orderData,saleorderUpt.getSaleorderNo());
		}catch(Exception e) {
			log.error("同步订单信息时，出现异常",e);
			//异常捕获，不进行回滚，因为dbcenter已经存储过，回滚也无济于事，按照成功进行处理，修复bug按照医购优选定时任务ThirdRequestRetryTask要求进行执行
			logger.error("更新订单信息不全，需要修复bug按照定时任务要求进行重新执行,订单号：{}",saleorder.getSaleorderNo());
		}
		return res;
	}


	@Override
	public Saleorder saveBdSaleOrderForStandard(OrderData orderData) {
//		Saleorder s = new Saleorder();
		//需要增加一个防重复检查的逻辑
//		String originBuyOrderNo = orderData.getOriginBuyOrderNo();
//		//根据来源ERP的采购单，在当前已存在的表里检查是否已经处理过了（防止异常情况，幂等性检查）
//		SyncOutInRelateDto syncOutInRelateDto = checkOrderExist(orderData.getOriginBuyOrderNo());
//		if(syncOutInRelateDto!=null) {
//			logger.info("订单号已存在！",syncOutInRelateDto.getInBusinessNo());
//			s.setSaleorderNo(syncOutInRelateDto.getOutBusinessNo());
//			return s;
//		}
		try {
			BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByCompanyName(orderData.getCompanyName());
			if(baseCompanyInfoDto == null){
				logger.warn("来源ERP的主体信息缺失，请检查{}",orderData.getCompanyName());
				throw  new RuntimeException("来源ERP的主体信息缺失");

			}

			BaseCompanyInfoDetailDto baseCompanyInfoDetailDto =  baseCompanyInfoDto.getBaseCompanyInfoDetailDto();
			String traderContactNameSync = baseCompanyInfoDetailDto.getTraderContactNameSync();
			String invoiceTraderContactMobileSync = baseCompanyInfoDetailDto.getInvoiceTraderContactMobileSync();



			// 取默认联系地址
//			Integer traderId = orderData.getTraderId();
//			TraderAddress traderAddress = traderAddressMapper.getTraderDefaultAdressInfo(traderId);
//			if(traderAddress == null){
//				logger.warn("来源ERP中的销售订单客户联系地址无默认&启用{}",traderId);
//				throw  new RuntimeException("来源ERP中的销售订单客户联系地址无默认&启用");
//			}

			//取默认联系人
//			TraderContact traderContact = new TraderContact();
//			traderContact.setTraderId(traderId);
//			traderContact.setTraderType(1);
//			traderContact.setIsDefault(1);
//			traderContact.setIsEnable(1);
//			List<TraderContact>  traderContacts = traderContactMapper.getTraderContact( traderContact);
//			if(CollectionUtils.isEmpty(traderContacts)){
//				logger.warn("来源ERP中的销售订单客户联系人无默认&启用{}",traderId);
//				throw  new RuntimeException("来源ERP中的销售订单客户联系人无默认&启用");
//			}


			Long time = DateUtil.sysTimeMillis();
			Saleorder saleorder = new Saleorder();
			saleorder.setOrderType(1);  //订单类型
			saleorder.setTakeTraderName(baseCompanyInfoDto.getCompanyName());//收货公司
			saleorder.setTakeTraderContactName(orderData.getDeliveryUserName());//收货联系人名称
			saleorder.setTakeTraderArea(orderData.getDeliveryUserArea());//收货地区
			saleorder.setTakeTraderAddress(orderData.getDeliveryUserAddress());//收货地址
			saleorder.setTakeTraderContactMobile(orderData.getDeliveryUserPhone());//收货人手机
			saleorder.setTakeTraderContactTelephone(orderData.getDeliveryUserTel());//收货人电话
			saleorder.setBdtraderComments(orderData.getRemakes());//客户备注
			saleorder.setHaveAccountPeriod(0);//账期支付
			saleorder.setTotalAmount(orderData.getTotalCouponedAmount());//订单总价
			saleorder.setIsSendInvoice(orderData.getIsSendInvoice());//是否寄送发票
			saleorder.setInvoiceType(orderData.getInvoiceType());//线上订单（BD订单）生成后，默认发票类型改为【13%增值税专用发票（寄送）】
			String createName = orderData.getUsername();
			saleorder.setPaymentMode(0);//支付方式
			saleorder.setInvoiceMethod(orderData.getInvoiceMethod()); //开票方式1手动纸质开票、2自动纸质开票、3自动电子发票
			saleorder.setIsDelayInvoice(0);//是否延迟开票0否1是
			saleorder.setPaymentType(419);//付款方式 先货后款，预付0% -- 11. 功能调整：通过采购单生成的对方销售订单的付款计划调整为"先款后货，预付100%"
			saleorder.setTraderName(baseCompanyInfoDto.getCompanyName());//如果没有关联erp客户就显示用户名称为注册时的企业名称
			saleorder.setStatus(0);//订单状态
			saleorder.setQuoteorderNo(orderData.getOrderNo());//报价单号
			saleorder.setValidStatus(0);//生效状态
			saleorder.setValidTime(0L);//生效时间
			saleorder.setAddTime(time);
			saleorder.setParentId(0);
			saleorder.setCompanyId(1);//南京公司
			saleorder.setPurchaseStatus(0);//采购状态0未采购（默认）
			saleorder.setLockedStatus(0);//锁定状态0未锁定
			saleorder.setInvoiceStatus(0);//开票状态0未开票
			saleorder.setInvoiceTime(0L);//开票时间
			saleorder.setPaymentStatus(0);//付款状态 0未付款
			saleorder.setPaymentTime(0L);//付款时间
			saleorder.setDeliveryStatus(0);//发货状态0未发货
			saleorder.setDeliveryTime(0L);//发货时间
			saleorder.setArrivalTime(0L);//客户收货时间
			saleorder.setServiceStatus(0);//售后状态 0无
			saleorder.setArrivalStatus(0);//客户收货状态0未收货
			saleorder.setHaveAdvancePurchase(0);//有提前采购 0
			saleorder.setAdvancePurchaseStatus(0);//0无提前采购需求
			saleorder.setAdvancePurchaseTime(0L);//提前采购申请时间
			saleorder.setIsUrgent(0);//是否加急0否1是
			BigDecimal a = new BigDecimal(0);
			saleorder.setUrgentAmount(a);//加急费用
			saleorder.setHaveCommunicate(0);//有无沟通记录
			saleorder.setSyncStatus(0);//0未同步
			saleorder.setPrepaidAmount(orderData.getTotalCouponedAmount());//预付金额
			User user = userMapper.getByUsername(createName, 1);
			saleorder.setCreator(user.getUserId());
			if (orderData.getDeliveryLevel3Id() != null) {
				saleorder.setTakeTraderAreaId(Integer.valueOf(orderData.getDeliveryLevel3Id()));//地区id
				saleorder.setTakeTraderArea(getAddressByAreaId(saleorder.getTakeTraderAreaId()));
			}
			saleorder.setCreateMobile(orderData.getPhone());
			saleorder.setIsCoupons(orderData.getIsCoupons());
			saleorder.setCouponInfo(orderData.getCouponInfo());
			saleorder.setDeliveryType(orderData.getDeliveryType());
			saleorder.setDeliveryClaim(orderData.getDelayDelivery());
			saleorder.setIsPrintout(orderData.getIsPrintout());
			saleorder.setLogisticsComments(orderData.getLogisticsComments());
			saleorder.setAdditionalClause(orderData.getAdditionalClause());
			saleorder.setComments(orderData.getComments());
			saleorder.setBillPeriodSettlementType(CustomerBillPeriodSettlementTypeEnum.ORDER_INVOICE.getCode());
			//BD订单归属销售
			User belongUser = new User();
			if (orderData.getTraderId() != null) {
				belongUser = userService.getBDUserInfoByTraderId(orderData.getTraderId());
				if (belongUser != null && belongUser.getUserId() != null) {
					saleorder.setUserId(belongUser.getUserId());
				}
				if (belongUser != null && belongUser.getOrgId() != null) {
					saleorder.setOrgId(belongUser.getOrgId());
				}
				if (belongUser != null && belongUser.getOrgName() != null) {
					saleorder.setOrgName(belongUser.getOrgName());
				}
				if (belongUser != null && belongUser.getTraderId() != null && belongUser.getTraderId() != 0) {
					//判断交易者下有无地址
					Integer deliveryLevel1Id = 0;
					if (orderData.getDeliveryLevel1Id() != null && !orderData.getDeliveryLevel1Id().equals("")) {
						deliveryLevel1Id = Integer.valueOf(orderData.getDeliveryLevel1Id());
					}
					Integer deliveryLevel2Id = 0;
					if (orderData.getDeliveryLevel2Id() != null && !orderData.getDeliveryLevel2Id().equals("")) {
						deliveryLevel2Id = Integer.valueOf(orderData.getDeliveryLevel2Id());
					}
					Integer deliveryLevel3Id = 0;
					if (orderData.getDeliveryLevel3Id() != null && !orderData.getDeliveryLevel3Id().equals("")) {
						deliveryLevel3Id = Integer.valueOf(orderData.getDeliveryLevel3Id());
					}
					Integer traderAddressId = vailTraderAddress(belongUser.getTraderId(), deliveryLevel1Id, deliveryLevel2Id, deliveryLevel3Id, orderData.getDeliveryUserAddress());
					saleorder.setTakeTraderAddressId(traderAddressId);
					saleorder.setTakeTraderAreaId(deliveryLevel3Id);
					//判断交易者联系人是否存在并保存
					saleorder.setTraderId(belongUser.getTraderId());
					Integer traderContactId = vailTraderContact(belongUser.getTraderId(), orderData.getDeliveryUserName(), orderData.getDeliveryUserTel(), orderData.getDeliveryUserPhone());
					saleorder.setTakeTraderContactId(traderContactId);
					//判断保存注册用户信息是否在客户联系人中,如果没有则添加
					Trader trader =  traderMapper.getTraderInfoByDeal(belongUser.getTraderId(),orderData.getDeliveryUserPhone(),orderData.getDeliveryUserAddress(),orderData.getDeliveryUserName());
					if (trader != null) {
						saleorder.setTraderName(trader.getTraderName());
						saleorder.setCustomerType(trader.getCustomerType());
						saleorder.setCustomerNature(trader.getCustomerNature());
						saleorder.setCustomerLevelStr(trader.getCustomerLevelStr());
						if (trader.getCustomerNature().equals(466)) {//终端
							saleorder.setTerminalTraderType(trader.getCustomerType());
							saleorder.setTerminalTraderName(trader.getTraderName());
							saleorder.setTerminalTraderId(trader.getTraderId());
							//获取交易者经营区域
							String areaIds = trader.getAreaIds();
							if (org.apache.commons.lang.StringUtils.isNotBlank(areaIds)) {
								//查询地区
								String traderBussinessArea = regionService.getRegionNameStringByMinRegionIds(areaIds);
								String[] areaid = areaIds.split(",");
								Integer areaid2 = Integer.valueOf(areaid[areaid.length - 1]);
								saleorder.setSalesAreaId(areaid2);
								saleorder.setSalesArea(traderBussinessArea);
							}
						}
					}
				}
			}
			//保存订单商品
			List<OrderGoodsData> goodsList = orderData.getGoodsList();
			List<SaleorderGoods> salegoodsList = new ArrayList<SaleorderGoods>();
			for (OrderGoodsData orderGoodsData : goodsList) {
				SaleorderGoods saleorderGoods = new SaleorderGoods();
				saleorderGoods.setSku(orderGoodsData.getSkuNo());
				if (belongUser != null && belongUser.getUserId() != null) {
					saleorderGoods.setCreator(belongUser.getUserId());
					saleorderGoods.setUpdater(belongUser.getUserId());
				}
				saleorderGoods.setAddTime(DateUtil.sysTimeMillis());
				saleorderGoods.setNum(orderGoodsData.getProductNum());
				saleorderGoods.setModTime(DateUtil.sysTimeMillis());
				saleorderGoods.setGoodsId(goodsService.getGoodsIdBySku(orderGoodsData.getSkuNo()));
				saleorderGoods.setDeliveryDirect(1);
				saleorderGoods.setDeliveryDirectComments(orderGoodsData.getDelilveryDirectComments());
				saleorderGoods.setDeliveryCycle(orderGoodsData.getDeliveryCycle());
				saleorderGoods.setGoodsComments(orderGoodsData.getGoodsComments());
				saleorderGoods.setInsideComments(orderGoodsData.getInsideComments());
				saleorderGoods.setPrice(orderGoodsData.getJxSalePrice());
				// 是否含安调
				saleorderGoods.setHaveInstallation(orderGoodsData.getHaveInstallation());
				saleorderGoods.setJxSalePrice(orderGoodsData.getJxSalePrice());
				//添加优惠券相关数据
				saleorderGoods.setIsCoupons(orderGoodsData.getIsCoupons());
				saleorderGoods.setMaxSkuRefundAmount(orderGoodsData.getSkuAmount());
				saleorderGoods.setRealPrice(orderGoodsData.getJxSalePrice());
				salegoodsList.add(saleorderGoods);
			}

			//VDERP-6918支持前台切换BD订单收票模块信息数据源start
			Integer invoiceTraderContactId = vailTraderContact(orderData.getTraderId(),
					traderContactNameSync,
					null,
					invoiceTraderContactMobileSync);

			//客户联系人和地址：
			Integer traderAddressId = vailTraderAddress(belongUser.getTraderId(),
					Integer.parseInt(baseCompanyInfoDetailDto.getDiliveryProvinceIdSync().split(",")[0]),
					Integer.parseInt(baseCompanyInfoDetailDto.getDiliveryCityIdSync().split(",")[0]),
					Integer.parseInt(baseCompanyInfoDetailDto.getDiliveryAreaIdSync().split(",")[0]),
					baseCompanyInfoDetailDto.getDiliveryAddressSync());

			saleorder.setTraderContactId(invoiceTraderContactId);
			saleorder.setTraderContactName(traderContactNameSync);
			saleorder.setTraderContactMobile(invoiceTraderContactMobileSync);
			saleorder.setTraderContactTelephone(null);
			saleorder.setTraderAreaId(Integer.parseInt(baseCompanyInfoDetailDto.getDiliveryAreaIdSync().split(",")[0]));
			saleorder.setTraderAddressId(traderAddressId);
			saleorder.setTraderAddress(baseCompanyInfoDetailDto.getDiliveryAddressSync());

			//查询地区
			//String area = regionService.getRegionNameStringByMinRegionIds(trader.getAreaIds());
			saleorder.setTraderArea(baseCompanyInfoDetailDto.getDiliveryProvinceIdSync().split(",")[1]
					+" "+baseCompanyInfoDetailDto.getDiliveryCityIdSync().split(",")[1]
					+" "+baseCompanyInfoDetailDto.getDiliveryAreaIdSync().split(",")[1]);

			saleorder.setGoodsList(salegoodsList);
			saleorder.setInvoiceTraderContactId(invoiceTraderContactId);
			//收票联系人地址
			saleorder.setInvoiceTraderContactName(traderContactNameSync);
			//收票联系人电话
			saleorder.setInvoiceTraderContactMobile(invoiceTraderContactMobileSync);
			//收票联系人手机
			saleorder.setInvoiceTraderContactTelephone(null);
			//收票地址
			saleorder.setInvoiceTraderAddress(baseCompanyInfoDetailDto.getDiliveryAddressSync());
			saleorder.setInvoiceTraderAddressId(traderAddressId);
			//收票区域
			saleorder.setInvoiceTraderArea(baseCompanyInfoDetailDto.getDiliveryProvinceIdSync().split(",")[1]
				+" "+baseCompanyInfoDetailDto.getDiliveryCityIdSync().split(",")[1]
				+" "+baseCompanyInfoDetailDto.getDiliveryAreaIdSync().split(",")[1]
			);
			//是否寄送发票
			saleorder.setIsSendInvoice(orderData.getIsSendInvoice());
			// 兼容历史数据
			saleorder.setInvoiceMethod(orderData.getInvoiceMethod());
			//开票类型
			saleorder.setInvoiceType(orderData.getInvoiceType());
			//新订单流标记状态
			saleorder.setIsNew(orderInfoSyncService.setSaleorderIsNewByOrg(saleorder.getSaleorderNo(), saleorder.getTraderId()));

			logger.info("请求DB保存BD订单信息 saleorder:{}", JSON.toJSONString(saleorder));
			//前台订单 设置推送状态为1
			saleorder.setSendToPc(1);
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<Saleorder>> TypeRef2 = new TypeReference<ResultInfo<Saleorder>>() {};
			ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + "order/saleorder/saveBDAddSaleorder.htm", saleorder, clientId, clientKey, TypeRef2);
			Saleorder res = (Saleorder) result2.getData();
			if(Objects.isNull(res)) {
				//throw new Exception("dbCenter保存销售单信息失败！");
			}

			Saleorder saleorderUpt = new Saleorder();
			saleorderUpt.setSaleorderId(res.getSaleorderId());
			saleorderUpt.setTerminalTraderName("");
			saleorderUpt.setSalesArea("");
			saleorderUpt.setSalesAreaId(ErpConst.ZERO);
			saleorderUpt.setOriginBuyOrderNo(orderData.getOriginBuyOrderNo());
			saleorderUpt.setPaymentType(saleorder.getPaymentType());
			saleorderUpt.setIsPrintout(2);
			saleorderUpt.setSaleorderNo(orderNoDict.getOrderNum(res.getSaleorderId(), 3));
			saleorderUpt.setIsDelayInvoice(orderData.getIsDelayInvoice());//是否暂缓开票
//    		saleorderUpt.setPeriodDay(0);
//    		saleorderUpt.setPrepaidAmount(BigDecimal.ZERO);
//    		saleorderUpt.setAccountPeriodAmount(orderData.getTotalCouponedAmount());
			saleorderMapper.updateByPrimaryKeySelective(saleorderUpt);

			List<Integer> saleOrderIdList = new ArrayList<>();
			saleOrderIdList.add(res.getSaleorderId());
			List<Map<String, Object>> saleorderList  = saleorderCurrentOrgAndUserIdSync.loadBizData(saleOrderIdList);
			if(CollectionUtils.isNotEmpty(saleorderList)){
				saleorderCurrentOrgAndUserIdSync.updateData(saleorderList);
			}
			if(res != null){
				//需要返回销售订单号
				res.setSaleorderNo(saleorderUpt.getSaleorderNo());
				//VDERP-2263   订单售后采购改动通知
				orderCommonService.updateSaleOrderDataUpdateTime(res.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_GENERATE);
			}
			if (belongUser != null && belongUser.getUserId() != null && belongUser.getUserId() != 0) {
				//判读端erp客户是否禁用
				quoteService.getIsDisabled(res.getCreateMobile(), belongUser.getUserId(), res.getSaleorderNo(), res.getSaleorderId());
			} else {
				//发送消息推送
				quoteService.sendAllocation(orderData.getPhone(), null);
			}

			//更新商品明细普发直发属性
//			List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(res.getSaleorderId());
//    		if(CollectionUtils.isNotEmpty(saleorderGoodsList)){
//    			for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
//    					SaleorderGoods saleorderGoodsUpdate = new SaleorderGoods();
//    					Integer saleorderGoodsId = saleorderGoods.getSaleorderGoodsId();
//    					saleorderGoodsUpdate.setSaleorderGoodsId(saleorderGoodsId);
//    					saleorderGoodsUpdate.setDeliveryDirect(0);
//    					saleorderGoodsMapper.updateByPrimaryKeySelective(saleorderGoodsUpdate);
//    				}
//    		}



			//同步订单，更新终端和订单信息
//			OrderTerminalDto orderTerminalDto = new OrderTerminalDto();
//			orderTerminalDto.setBusinessId(res.getSaleorderId());
//			orderTerminalDto.setBusinessNo(saleorderUpt.getSaleorderNo());
//			orderTerminalDto.setAddTime(new Date());
//			orderTerminalDto.setCreator(1);
//			orderTerminalDto.setCreatorName("admin");
//			orderTerminalDto.setHosModel("0");
//			orderTerminalDto.setTerminalTraderNature(5605);
//			orderTerminalDto.setNatureTypeName("非公集团");
//			orderTerminalApiService.save(orderTerminalDto);
			return res;
			//TODO 增加一个外部与内部单号的记录
			//addOutInRelateOrder(originBuyOrderNo,saleorderUpt.getSaleorderNo(),baseCompanyInfoDto);
			//addOrderLogisticsSyncTask(orderData,saleorderUpt.getSaleorderNo());
		}catch(Exception e) {
			log.error("同步订单信息时，出现异常",e);
			return null;
		}

	}


	private void addOrderLogisticsSyncTask(OrderData orderData,String saleorderNo){
		boolean deliveryDirect = orderData.getDeliveryDirect()!=null && orderData.getDeliveryDirect().equals("Y");//Y或N表示采购订单是否是直发
		if(deliveryDirect){
			//
			String companyName = orderData.getCompanyName(); //"companyName": "南京医购优选供应链管理有限公司",
			BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByCompanyName(companyName);
			if(baseCompanyInfoDto == null){
				log.info("采购订单合同物流信息同步时，{}不符合同步到子公司的条件",companyName);
				return;
			}
			log.info("采购订单合同物流信息同步时，{}符合同步到子公司的条件",companyName);
			com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
			jsonObject.put("buyorderNo",orderData.getOriginBuyOrderNo());
			String jsonContent = jsonObject.toJSONString();
			String dataType = SyncDataTypeEnum.SALEORDER_LOGISTICS_TO_BUYORDER.getDataType();
			//需要同步给子公司
			SyncDataErpDto syncDataErpDto = new SyncDataErpDto();
			syncDataErpDto.setBusinessNo(saleorderNo);
			syncDataErpDto.setTargetErp(baseCompanyInfoDto.getCompanyShortName());
			syncDataErpDto.setBusinessType(dataType);
			syncDataErpDto.setRequestContent(jsonContent);
			syncDataErpDto.setProcessStatus(ErpConst.ZERO);
			syncDataErpApiService.insert(syncDataErpDto);
		}else{
			log.info("是否直普发时为普发，不需要创建物流信息同步任务:{}",orderData.getOriginBuyOrderNo());
		}
	}


	public String getTaskIdBySaleOrderId(Integer saleOrderId){
		//订单审核记录
		Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
				"saleorderVerify_" + saleOrderId);
		Task taskInfo = (Task) historicInfo.get("taskInfo");
		if(taskInfo == null){
			return "";
		}
		return taskInfo.getId();
	}
}
