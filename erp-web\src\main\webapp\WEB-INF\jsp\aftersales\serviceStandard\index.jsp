<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="贝登售后标准" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css ">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css ">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/lvSelect.css ">
<style>
    .select-lv .select-list-wrap li {
        float: none;
    }

    .select-lv .select-list {
        border: 0;
    }


    .select-lv .vd-icon {
        background: none !important;
    }
</style>
<div class="main-container">
    <div class="list-pages-search">
        <form method="post" id="search" action="<%=basePath%>aftersale/serviceStandard/index.do">
            <input class="hidden-reset" name="buzTypeFromTodoList" value="${queryDto.buzTypeFromTodoList}" type="hidden" />
            <input class="hidden-reset" name="goodsLevelFromTodoList" value="${queryDto.goodsLevelFromTodoList}" type="hidden" />
            <input class="hidden-reset" name="subordinateList" value="${queryDto.subordinateList}" type="hidden" />
            <ul>
                <li>
                    <label class="infor_name">关键词</label>
                    <input type="text" class="input-middle" placeholder="请输入订货号/商品名称/品牌/规格型号" name="keyWord" id="keyWord" value="${queryDto.keyWord}" style="width:220px"/>
                </li>
                <li>
                    <label class="infor_name">产品是否可安装</label>
                    <select class="input-middle f_left" name="productInstall">
                        <option value="" >全部</option>
                        <option value="0" <c:if test="${queryDto.productInstall eq '0' }">selected="selected"</c:if>>不可安装</option>
                        <option value="1" <c:if test="${queryDto.productInstall eq '1' }">selected="selected"</c:if>>可安装</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">供应商售后政策</label>
                    <select class="input-middle f_left" name="supplyMatainment">
                        <option value="">全部</option>
                        <option value="0" <c:if test="${queryDto.supplyMatainment == '0' }">selected="selected"</c:if>>未维护</option>
                        <option value="1" <c:if test="${queryDto.supplyMatainment == '1'}">selected="selected"</c:if>>已维护</option>
                    </select>
                </li>


                <li>
                    <label class="infor_name">贝登售后标准</label>
                    <select class="input-middle f_left" name="status">
                        <option value="">全部</option>
                        <option value="0" <c:if test="${queryDto.status == '0' }">selected="selected"</c:if>>待审核</option>
                        <option value="1" <c:if test="${queryDto.status == '1' }">selected="selected"</c:if>>审核中</option>
                        <option value="2" <c:if test="${queryDto.status == '2' }">selected="selected"</c:if>>审核通过</option>
                        <option value="3" <c:if test="${queryDto.status == '3' }">selected="selected"</c:if>>审核不通过</option>
                    </select>
                </li>

                <li>
                    <label class="infor_name" style="width: 178px">贝登是否提供安装服务</label>
                    <select class="input-middle f_left" name="installTrainType">
                        <option value="">全部</option>
                        <option value="0" <c:if test="${queryDto.installTrainType == '0' }">selected="selected"</c:if>>收费安装</option>
                        <option value="1" <c:if test="${queryDto.installTrainType == '1' }">selected="selected"</c:if>>免费安装</option>
                        <option value="2" <c:if test="${queryDto.installTrainType == '2' }">selected="selected"</c:if>>不提供安装</option>
                        <option value="3" <c:if test="${queryDto.installTrainType == '3' }">selected="selected"</c:if>>无</option>
                    </select>
                </li>


            </ul>
            <ul>
             <li>
                    <label class="infor_name" >售前标签</label>
                    <select class="input-middle f_left" name="hasAfterSaleServiceLabel">
                        <option value="">全部</option>
                        <option value="1" <c:if test="${queryDto.hasAfterSaleServiceLabel == '1' }">selected="selected"</c:if>>含</option>
                        <option value="0" <c:if test="${queryDto.hasAfterSaleServiceLabel == '0' }">selected="selected"</c:if>>不含</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">待审核人</label>
                    <select class="input-middle f_left" name="checkPersonId">
                        <option value="">全部</option>
                        <c:forEach var="checkPerson" items="${afterSaleDirectorList}">
                            <option value="${checkPerson.userId}" <c:if test="${queryDto.auditPerson == checkPerson.userId}">selected="selected"</c:if> >${checkPerson.username}</option>
                        </c:forEach>
                    </select>
                </li>

                <li>
                    <label class="infor_name">产品归属</label>
                    <select class="input-middle f_left" name="productManagerName">
                        <option value="" >全部</option>
                        <c:forEach var="manage" items="${managerUserList}">
                            <option value="${manage.username}" <c:if test="${queryDto.productManagerName == manage.username}">selected="selected"</c:if> >${manage.username}</option>
                        </c:forEach>
                    </select>
                </li>

                <li style="display: flex">
                    <label class="infor_name">商品分类</label>
                    <div class="search-list J-search-more-cnt">
                        <div class="search-item">
                            <div class="item-fields">
                                <input type="hidden" class="J-category-value" id="categoryId" name="categoryId" value="${queryDto.categoryId}">
                                <div class="select-lv-wrap J-category-wrap"></div>
                            </div>
                        </div>
                    </div>
                </li>
                 <li>
                    <label class="infor_name">售后服务等级</label>
                    <select class="input-middle f_left" name="afterSalesServiceLevel">
                        <option value="" >全部</option>
                        <option value="5" <c:if test="${queryDto.afterSalesServiceLevel eq '5' }">selected="selected"</c:if>>五星级</option>
                        <option value="4" <c:if test="${queryDto.afterSalesServiceLevel eq '4' }">selected="selected"</c:if>>四星级</option>
                        <option value="3" <c:if test="${queryDto.afterSalesServiceLevel eq '3' }">selected="selected"</c:if>>三星级</option>
                        <option value="2" <c:if test="${queryDto.afterSalesServiceLevel eq '2' }">selected="selected"</c:if>>二星级</option>
                         <option value="1" <c:if test="${queryDto.afterSalesServiceLevel eq '1' }">selected="selected"</c:if>>一星级</option>
                         <option value="0" <c:if test="${queryDto.afterSalesServiceLevel eq '0' }">selected="selected"</c:if>>待评级</option>
                        <option value="6" <c:if test="${queryDto.afterSalesServiceLevel eq '6' }">selected="selected"</c:if>>无需评级</option>
                    </select>

                </li>

            </ul>
            <div class="tcenter">

                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">查询</span>

                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>

            </div>
        </form>
    </div>
    <div class="normal-list-page">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid3">序号</th>

                <th class="wid5">订货号</th>
                <th class="wid8">商品名称</th>
                <th class="wid6">品牌</th>
                <th class="wid6">规格/型号</th>
                <th class="wid6">售后服务等级</th>

                <th class="wid6">产品是否可安装</th>

                <th class="wid6">供应商售后政策</th>
                <th class="wid6">贝登售后标准</th>
                <th class="wid7">贝登是否提供安装服务</th>
                <th class="wid6">安装费</th>
                <th class="wid6">售前标签</th>
                <th class="wid6">产品归属</th>

                <th class="wid6">操作</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach var="serviceStandardApplyDto" items="${serviceStandardApplyDtoList}"  varStatus="num">
                <tr>
                    <td>${num.count}</td>

                    <td>${serviceStandardApplyDto.skuNo}</td>
                    <td>
                        <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${serviceStandardApplyDto.skuNo}","link":"./goods/goods/viewbaseinfo.do?goodsId=${fn:substringAfter(serviceStandardApplyDto.skuNo,'V')}","title":"产品信息"}'>${serviceStandardApplyDto.showName}</a>
                    </td>
                    <td>${serviceStandardApplyDto.brandName}</td>
                    <td>
                        ${serviceStandardApplyDto.spec}
                    </td>
                    <td>
                     <c:choose>
                        <c:when test="${serviceStandardApplyDto.afterSalesServiceLevel == '5'}">
                            五星级
                        </c:when>
                        <c:when test="${serviceStandardApplyDto.afterSalesServiceLevel == '4'}">
                            四星级
                        </c:when>
                        <c:when test="${serviceStandardApplyDto.afterSalesServiceLevel == '3'}">
                            三星级
                        </c:when>
                        <c:when test="${serviceStandardApplyDto.afterSalesServiceLevel == '2'}">
                            二星级
                        </c:when>
                        <c:when test="${serviceStandardApplyDto.afterSalesServiceLevel == '1'}">
                            一星级
                        </c:when>
                        <c:when test="${serviceStandardApplyDto.afterSalesServiceLevel == '0'}">
                            待评级
                        </c:when>
                        <c:when test="${serviceStandardApplyDto.afterSalesServiceLevel == '6'}">
                            无需评级
                        </c:when>
                        <c:otherwise>
                            -
                        </c:otherwise>
                    </c:choose>
                    </td>

                    <td>
                        <c:choose>
                            <c:when test="${serviceStandardApplyDto.isInstallable == '0'}">
                                不可安装
                            </c:when>
                            <c:when test="${serviceStandardApplyDto.isInstallable == '1'}">
                                可安装
                            </c:when>
                            <c:otherwise>
                                -
                            </c:otherwise>
                        </c:choose>
                    </td>

                    <td>
                        <c:choose>
                            <c:when test="${serviceStandardApplyDto.supplyAfterSaleIsMaintain == '0'}">
                                未维护
                            </c:when>
                            <c:when test="${serviceStandardApplyDto.supplyAfterSaleIsMaintain == '1'}">
                                已维护
                            </c:when>
                            <c:otherwise>
                                -
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>

                        <c:choose>
                            <c:when test="${serviceStandardApplyDto.afterSaleStandardStatus == '0'}">
                                待完善
                            </c:when>
                            <c:when test="${serviceStandardApplyDto.afterSaleStandardStatus == '1'}">
                                审核中
                            </c:when>
                            <c:when test="${serviceStandardApplyDto.afterSaleStandardStatus == '2'}">
                                审核通过
                            </c:when>
                            <c:when test="${serviceStandardApplyDto.afterSaleStandardStatus == '3'}">
                                审核不通过
                            </c:when>
                            <c:otherwise>
                                -
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:if test="${serviceStandardApplyDto.isInstallable == '0'}">
                            -
                        </c:if>
                        <c:if test="${serviceStandardApplyDto.isInstallable == '1'}">
                            <c:choose>
                                <c:when test="${serviceStandardApplyDto.installPolicyInstallType == '0'}">
                                    收费安装
                                </c:when>
                                <c:when test="${serviceStandardApplyDto.installPolicyInstallType == '1'}">
                                    免费安装
                                </c:when>
                                <c:when test="${serviceStandardApplyDto.installPolicyInstallType == '2'}">
                                    不提供安装
                                </c:when>
                                <c:otherwise>
                                    -
                                </c:otherwise>
                            </c:choose>
                        </c:if>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${serviceStandardApplyDto.isInstallable == '0' || serviceStandardApplyDto.installPolicyInstallType == '1' || serviceStandardApplyDto.installPolicyInstallType == '2'}">
                                -
                            </c:when>
                            <c:otherwise>
                                ${serviceStandardApplyDto.installPolicyInstallFee}
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${serviceStandardApplyDto.hasAfterSaleServiceLabel == 1}">含</c:when>
                            <c:otherwise>不含</c:otherwise>
                        </c:choose>
                    </td>
                    <td>${serviceStandardApplyDto.managerName}&${serviceStandardApplyDto.assitName}</td>
                    <td>
                        <span class="edit-user" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./aftersale/serviceStandard/detail.do?serviceStandardApplyId=${serviceStandardApplyDto.serviceStandardApplyId}&skuNo=${serviceStandardApplyDto.skuNo}","title":"详情"}'
                              onclick="toDetail('${serviceStandardApplyDto.serviceStandardApplyId}',this)">详情</span>

                    </td>
                </tr>
            </c:forEach>
            <c:if test="${empty serviceStandardApplyDtoList}">
                <tr>
                    <td colspan='12'>查询无结果！请尝试使用其它搜索条件。</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>
    <tags:page page="${page}" />
</div>

<script type="text/javascript">

    function toDetail(serviceStandardApplyId,span) {

        openTab(span);

        /*$.ajax({
            async:true,
            url:page_url+'/aftersale/serviceStandard/toDetailValidator.do',
            data:{"serviceStandardApplyId":serviceStandardApplyId},
            type:"POST",
            dataType : "json",
            success:function(data){
                if(data.code==0){
                    openTab(span);
                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })*/
    }

    function openTab(span){
        var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
        var newPageId;
        var tabTitle = $(span).attr('tabTitle');
        if (typeof(tabTitle) == 'undefined') {
            alert('参数错误');
        } else {
            tabTitle = $.parseJSON(tabTitle);
        }
        var id = tabTitle.num;
        // var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
        var name = tabTitle.title;
        var uri = tabTitle.link;
        var closable = 1;
        var item = { 'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false };
        self.parent.closableTab.addTab(item);
        self.parent.closableTab.resizeMove();
        $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    }

    function reset(){
        window.location.href = "${pageContext.request.contextPath}/aftersale/serviceStandard/index.do";
    }

    $(function () {
        new LvSelect({
            el: '.J-category-wrap',
            input: '.J-category-value',
            async: true,
            url: page_url + '/category/base/getCategoryList.do',
            parseData: function (res) {
                var categoryData = [];

                $.each(res.listData, function (i, lv1) {
                    var lv1item = {
                        label: lv1.baseCategoryName,
                        value: lv1.baseCategoryId,
                        child: []
                    };

                    if (lv1.secondCategoryList) {
                        $.each(lv1.secondCategoryList, function (ii, lv2) {
                            var lv2item = {
                                label: lv2.baseCategoryName,
                                value: lv2.baseCategoryId,
                                child: []
                            };

                            if (lv2.thirdCategoryList) {
                                $.each(lv2.thirdCategoryList, function (iii, lv3) {
                                    lv2item.child.push({
                                        label: lv3.baseCategoryName,
                                        value: lv3.baseCategoryId
                                    })
                                })
                            }

                            if (lv2item.child.length) {
                                lv1item.child.push(lv2item);
                            }
                        })
                    }

                    if (lv1item.child.length) {
                        categoryData.push(lv1item);
                    }
                })

                return categoryData;
            }
        })
    });

</script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/lv-select.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/modules/list.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/dialogSearch.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
