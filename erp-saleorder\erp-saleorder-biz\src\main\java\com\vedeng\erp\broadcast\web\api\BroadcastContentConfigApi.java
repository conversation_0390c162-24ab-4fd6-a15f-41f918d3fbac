package com.vedeng.erp.broadcast.web.api;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.broadcast.domain.dto.BatchUploadResultDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastContentConfigDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastContentConfigReqDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastContentConfigRespDto;
import com.vedeng.erp.broadcast.service.BroadcastContentConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 播报内容配置API控制器
 */
@ExceptionController
@RestController
@RequestMapping("/broadcastContentConfig")
@Slf4j
public class BroadcastContentConfigApi {

    @Autowired
    private BroadcastContentConfigService broadcastContentConfigService;

    /**
     * 分页查询播报内容配置
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<PageInfo<BroadcastContentConfigRespDto>> page(@RequestBody PageParam<BroadcastContentConfigReqDto> pageParam) {
        return R.success(broadcastContentConfigService.page(pageParam));
    }

    /**
     * 修改播报内容配置
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<Void> update(@RequestBody BroadcastContentConfigDto dto) {
        ValidatorUtils.validate(dto, UpdateGroup.class);
        broadcastContentConfigService.update(dto);
        return R.success();
    }



    /**
     * 删除播报内容配置
     */
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Void> delete(@RequestParam Integer id) {
        broadcastContentConfigService.delete(CollUtil.newHashSet(id));
        return R.success();
    }

    /**
     * 批量删除播报内容配置
     */
    @RequestMapping(value = "/batchDelete", method = RequestMethod.POST)
    public R<Void> batchDelete(@RequestBody java.util.List<Integer> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            broadcastContentConfigService.delete(CollUtil.newHashSet(ids));
        }
        return R.success();
    }

    /**
     * 获取播报内容配置详情
     */
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    @ExcludeAuthorization
    public R<BroadcastContentConfigDto> get(@RequestParam Integer id) {
        return R.success(broadcastContentConfigService.get(id));
    }

    /**
     * 批量上传图片并创建播报内容配置
     */
    @RequestMapping(value = "/batchUpload", method = RequestMethod.POST)
    public R<BatchUploadResultDto> batchUpload(@RequestParam("files") MultipartFile[] files) {
        BatchUploadResultDto result = broadcastContentConfigService.batchUpload(files);
        return R.success(result);
    }
}
