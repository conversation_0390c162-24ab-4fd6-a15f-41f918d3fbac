package com.vedeng.goodsSearch.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品信息运输类
 *
 * <AUTHOR>
 */
@Data
public class GoodsSearchInfoResDto {

    private Integer skuId;

    private String skuNo;

    private String skuName;

    private String goodsLevel;

    private String goodsPosition;

    private BigDecimal terminalPrice;

    private BigDecimal distributionPrice;

    private BigDecimal electronicCommercePrice;

    private BigDecimal researchTerminalPrice;

    private Integer availableStock;

    private Integer stock;

    private Long purchaseTime;

    private String unit;

    private String salerOfLastOrder;

    private String registerNumber;

    private Integer registerNumberId;

    private String checkStatus;

    private String taxCategoryNo;

    private String classIficationAbbreviation;

    private String spuType;

    private String category;

    private String brandName;

    private Integer saleCountOfLastYear;

    private BigDecimal avgPriceOfLastYear;

    private String skuAssignments;

    private String occupyNum;

    private Integer firstEngageId;

    /**
     * 在途库存
     */
    private String onWayNum;

    /**
     * 型号
     */
    private String model;

    /**
     * 规格
     */
    private String spec;

    /**
     * 商品标签："销量之王","精选"
     */
    private List<String> labelList;

    /**
     * 直发货期范围
     */
    private String directDeliveryTime;

    /**
     * 普发货期范围
     */
    private String commonDeliveryTime;

    /**
     * 使用年限/单位：年
     */
    private Integer serviceLife;

    /**
     * DI_CODE 商品UDI-DI码 VDERP-17675 商品详情页：增加商品DI字段
     */
    private String diCode;
    
    /**销售价是否含运费 ,0 未维护 1 含运费 2 不含运费*/
    private Integer saleContainsFee;
    
    /**成本价是否含运费 ,0 未维护 1 含运费 2 不含运费*/
    private Integer purchseContainsFee;
    /**
     * 售后服务级别 5五星级、4四星级、3三星级、2二星级、1一星级、0待评级
     */
    private Integer afterSalesServiceLevel;
}
