package com.vedeng.erp.broadcast.web.api;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.erp.broadcast.domain.dto.*;
import com.vedeng.erp.broadcast.service.BroadCastDeptUserRelateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 播报部门管理API控制器
 * 提供部门管理相关的RESTful接口
 */
@RestController
@RequestMapping("/broadcast/relate")
@Slf4j
public class BroadCastDeptUserRelateApiController {

    @Resource
    BroadCastDeptUserRelateService broadCastDeptUserRelateService;
   /**
     *  初始化
     * @return BroadcastGlobalConfigDto
     */
    @RequestMapping("/init")
    @ResponseBody
    public R<BroadcastRelationConfigDto> init() {
    	log.info("播报部门管理,初始化");
        BroadcastRelationConfigDto dto=broadCastDeptUserRelateService.init();
        return R.success(dto);
    }
    /**
     * 保存
     * @return BroadcastGlobalConfigDto
     */
    @RequestMapping("/saveOrUpdate")
    @ResponseBody
    public R<BroadcastGlobalConfigDto> save(HttpServletRequest request, @RequestBody BroadcastRelationConfigDto dto) {
        CurrentUser user = CurrentUser.getCurrentUser();
        if (user == null) {
            return R.error(CommonConstants.FAIL_CODE, "登陆信息过期");
        }
        try {
            broadCastDeptUserRelateService.save(dto, user);
            return R.success();
        }catch (Exception e){
            log.error("",e);
            return R.error(CommonConstants.FAIL_CODE, "保存失败");
        }
    }
}
