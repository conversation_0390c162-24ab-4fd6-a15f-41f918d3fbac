package com.vedeng.common.shiro;


import com.alibaba.fastjson.JSONObject;
import com.ezadmin.ConfigUtils;
import com.ezadmin.common.constants.SessionConstants;
import com.ezadmin.common.utils.Utils;
import com.ezadmin.web.EzAdminFilter;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.shiro.cas.CasClientHelper;
import com.vedeng.common.shiro.constant.SecurityConstants;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.system.service.UserService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.UrlPathHelper;
import top.ezadmin.EzClientBootstrap;


import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.io.IOException;
import java.util.*;

public class EzFilter  extends OncePerRequestFilter {

    private static final Logger LOGGE = LoggerFactory.getLogger(EzAdminFilter.class);


    @Value("${ezadmin.appName:}")
    private String appName;
    @Value("${ezadmin.cacheFlag:true}")
    private boolean cacheFlag;
    @Value("${ezadmin.uploadUrl:}")
    private String uploadUrl ;
    @Value("${ezadmin.logType:0000-10000}")
    private String logType ;
    @Value("${ezadmin.regionUrl:}")
    private String regionUrl ;
    @Value("${ezadmin.categoryUrl:}")
    private String categoryUrl ;
    @Value("${ezadmin.orgUrl:}")
    private String orgUrl ;

    @Value("${ezadmin.systemName:}")
    private String systemName="System";
    @Value("${ezadmin.logoUrl:}")
    private String logoUrl="/static/logo.png";
    @Value("${ezadmin.navUrl:}")
    private String navUrl="/navs.html";

    @Value("${ezadmin.appendJs:}")
    private String appendJs;
    @Value("${ezadmin.searchUrl:}")
    private String searchUrl;
    @Value("${ezadmin.indexUrl:/ezadmin/welcome.html}")
    private String indexUrl;
    @Value("${ezadmin.signoutUrl:/logout}")
    private String signoutUrl;
    @Value("${ezadmin.messageUrl:}")
    private String messageUrl;
    @Value("${ezadmin.chatUrl:}")
    private String chatUrl;

    @Value("${ezadmin.datasourceBeanNames:dataSource}")
    private String datasourceBeanNames;

    @Value("${ezadmin.listResourceLocation}")
    private String listResourceLocation;
    @Value("${ezadmin.formResourceLocation}")
    private String formResourceLocation;
    @Value("${ezadmin.pluginsFormResourceLocation}")
    private String pluginsFormResourceLocation;
    @Value("${ezadmin.pluginsListResourceLocation}")
    private String pluginsListResourceLocation;
    @Value("${ezadmin.adminStyle}")
    private String adminStyle;
    private String listResourceLocation2="classpath*:/topezadmin/config/layui/list/**/*.html";
    private String formResourceLocation2="classpath*:/topezadmin/config/layui/form/**/*.html";
    private String pluginsFormResourceLocation2="classpath*:/topezadmin/config/layui/plugins/form/**/*.html";
    private String pluginsListResourceLocation2="classpath*:/topezadmin/config/layui/plugins/list/**/*.html";
    private String pluginsDetailResourceLocation2="classpath*:/topezadmin/config/layui/plugins/detail/**/*.html";


    private com.ezadmin.EzBootstrap ezBootstrap;
    @Autowired
    SpringContextHolder contextHolder;
    @Autowired
    UserService userService;


    public void initFilterBean() throws ServletException {
        ezBootstrap= com.ezadmin.EzBootstrap.instance();
        ezBootstrap.setAppName(appName);
        ezBootstrap.setSqlCache(cacheFlag);
        ezBootstrap.setUploadUrl(uploadUrl);
        ezBootstrap.setLogType(logType);
        ezBootstrap.setRegionUrl(regionUrl);
        ezBootstrap.setCategoryUrl(categoryUrl);
        ezBootstrap.setOrgUrl(orgUrl);
        ezBootstrap.setSystemName(systemName);
        ezBootstrap.setNavUrl(navUrl);
        ezBootstrap.setLogoUrl(logoUrl);
        ezBootstrap.setSearchUrl(searchUrl);
        ezBootstrap.setAppendJs(appendJs);
        ezBootstrap.setIndexUrl(indexUrl);
        ezBootstrap.setSignoutUrl(signoutUrl);
        ezBootstrap.setMessageUrl(messageUrl);
        ezBootstrap.setChatUrl(chatUrl);

        ezBootstrap.setAdminStyle( adminStyle);

        String beanNames[]= org.apache.commons.lang.StringUtils.split(datasourceBeanNames ,",");
        for (int i = 0; i < beanNames.length; i++) {
            DataSource dataSource=(DataSource)contextHolder.getBean(beanNames[i]);
            if(dataSource==null){
                throw new IllegalArgumentException(" can not find datasource with bean name :"+beanNames[i]);
            }
            ezBootstrap.addBizDataSource(beanNames[i],dataSource);
            ezBootstrap.addOriginDataSource(dataSource);
        }

        try {
            ezBootstrap.setListConfigResources(ConfigUtils.loadFiles(listResourceLocation));
            ezBootstrap.setFormConfigResources(ConfigUtils.loadFiles(formResourceLocation));
            ezBootstrap.setPluginsFormConfigResources(ConfigUtils.loadFiles(pluginsFormResourceLocation));
            ezBootstrap.setPluginsListConfigResources(ConfigUtils.loadFiles(pluginsListResourceLocation));

            ezBootstrap.init( );
            initClient();
        } catch (Exception throwables) {
            throw new ServletException(throwables);
        }




    }
    private void initClient(){
        EzClientBootstrap ezBootstrap = EzClientBootstrap.instance();
       // String configJson= ezClientProperties.getConfigJson();
        Map<String, Object> configMap=new HashMap<>();// JSONUtils.parseObjectMap(configJson);
        ezBootstrap.setConfig(configMap);
        ezBootstrap.setAppName(appName);
        ezBootstrap.setSqlCache(cacheFlag);
        ezBootstrap.setLogType(logType);
        //关闭高级搜索
        ezBootstrap.setCustomSearchOpen(false);

        //布局 默认container
        ezBootstrap.setLayout("fluid");
        ezBootstrap.setUploadUrl("/vgoods/operate/fileUploadEz.do");

        String beanNames[]= org.apache.commons.lang.StringUtils.split(datasourceBeanNames ,",");
        for (int i = 0; i < beanNames.length; i++) {
            DataSource dataSource = (DataSource) contextHolder.getBean(beanNames[i]);
            if (dataSource == null) {
                logger.error(" can not find datasource with bean name :" + beanNames[i]);
                throw new IllegalArgumentException(" can not find datasource with bean name :" + beanNames[i]);
            }
            ezBootstrap.addBizDataSource(beanNames[i], dataSource);
        }

        DataSource dataSourceSystem = (DataSource) contextHolder.getBean("dataSource");
        ezBootstrap.addBizDataSource("dataSource",dataSourceSystem);

        try {
            ezBootstrap.setListConfigResources(top.ezadmin.ConfigUtils.loadFiles(listResourceLocation2));
            ezBootstrap.setPluginsListConfigResources(top.ezadmin.ConfigUtils.loadFiles( pluginsListResourceLocation2));
            ezBootstrap.setFormConfigResources(top.ezadmin.ConfigUtils.loadFiles( formResourceLocation2));
            ezBootstrap.setPluginsFormConfigResources(top.ezadmin.ConfigUtils.loadFiles( pluginsFormResourceLocation2));
            ezBootstrap.setPluginsDetailConfigResources(top.ezadmin.ConfigUtils.loadFiles( pluginsDetailResourceLocation2));
            ezBootstrap.init();
        } catch (Exception throwables) {
            logger.error("   初始化异常", throwables);
            throw new RuntimeException(throwables);
        }
    }

    private static String SESSION_USER_BY_USER_ORG_IDS="SESSION_USER_BY_USER_ORG_IDS";
    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        long start=System.currentTimeMillis();
        try {
            User user = (User) httpServletRequest.getSession().getAttribute(Consts.SESSION_USER);
            if(user!=null) {
                httpServletRequest.getSession().setAttribute(SessionConstants.EZ_SESSION_USER_NAME_KEY, user.getUsername());
                httpServletRequest.getSession().setAttribute(SessionConstants.EZ_SESSION_USER_ID_KEY, user.getUserId());
                httpServletRequest.setAttribute(SessionConstants.EZ_SESSION_USER_NAME_KEY, user.getUsername());
                httpServletRequest.setAttribute(SessionConstants.EZ_SESSION_USER_ID_KEY,   user.getUserId());
                httpServletRequest.getSession().setAttribute(SessionConstants.EZ_SESSION_USER_ID_KEY, user.getUserId());
//                List<Integer> positionType = new ArrayList<>();
//                positionType.add(SysOptionConstant.ID_310);
//                List<User> userList = userService.getMyUserList(user, positionType, false);
                if(httpServletRequest.getSession().getAttribute(SESSION_USER_BY_USER_ORG_IDS)==null){
                    List<User> extendsUsers=userService.getUserByUsersOrgIds(user.getUserId());
                    httpServletRequest.getSession().setAttribute(SESSION_USER_BY_USER_ORG_IDS,
                            extendsUsers==null?new ArrayList<User>():extendsUsers);
                }
                Map<String, String> session = new HashMap<>();
                if(CollectionUtils.isNotEmpty(user.getSubUserIdList())) {
                    LOGGE.info("doFilterInternal with ez:{},{}",new Object[]{user.getUserId(),user.getSubUserIdList()});
                    session.put(SessionConstants.EZ_SESSION_MY_USER_KEY, StringUtils.join(user.getSubUserIdList(), ","));
                    httpServletRequest.getSession().setAttribute(SessionConstants.EZ_SESSION_MY_USER_KEY,StringUtils.join(user.getSubUserIdList(), ","));
                }
                else{
                    LOGGE.info("doFilterInternal with ez:{},{}",new Object[]{user.getUserId(),user.getPositType()});
                    //某种特殊情况下，user.getSubUserIdList() 是空的  针对销售 做个兜底处理，其他职位，产品反馈后续会出需求。
                    if(null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
                        session.put(SessionConstants.EZ_SESSION_MY_USER_KEY, user.getUserId() + "");
                    }
                }
                putSubUserForEzSelect(session,user,(List<User> )httpServletRequest.getSession().getAttribute(SESSION_USER_BY_USER_ORG_IDS));
                //设置用户可以查看的部门ID
                session.put("EZ_SESSION_ORG_IDS_LIST_KEY",user.getOrgIdsList());

                LOGGE.info("setAttribute ezsessionParamKey:"+user.getUserId()+":{}", JSONObject.toJSONString(session));
                httpServletRequest.getSession().setAttribute(SessionConstants.EZ_SESSION_PARAM_KEY, session);


            }else{
                LOGGE.info("notAssignPosition ");
                response.sendRedirect("/notAssignPosition.do");
                return;
            }
            if (CasClientHelper.enableSingleSignOn() && user != null && !CasClientHelper.isAdminUser(user.getIsAdmin())) {
                if (user.getPositions() == null || user.getPositions().isEmpty()) {
                    response.sendRedirect("/notAssignPosition.do");
                    return  ;
                } else if (user.getPositions().size() > 1) {
                    response.sendRedirect(SecurityConstants.SELECT_ORG_URL);
                    return  ;
                }
            }
            UrlPathHelper helper = new UrlPathHelper();
            String realUrl = helper.getRequestUri(httpServletRequest);
            try {
                Map<String, String> currentSessionMap = (Map<String, String>) httpServletRequest.getSession().getAttribute(SessionConstants.EZ_SESSION_PARAM_KEY);
                if(MapUtils.isEmpty(currentSessionMap)||StringUtils.isBlank(currentSessionMap.get(SessionConstants.EZ_SESSION_MY_USER_KEY))){
                    LOGGE.warn("EzFilter session empty {} {}",user.getUserId() ,JSONObject.toJSONString(currentSessionMap));
                }
            }catch (Exception e){
                LOGGE.error("", e);
            }
            if(StringUtils.startsWith(realUrl,"/topezadmin")){
                EzClientBootstrap.instance().doFilter(httpServletRequest,response,filterChain);
            }else{
                ezBootstrap.doFilter(httpServletRequest, response);
            }

        } catch (Exception e) {
            LOGGE.error("", e);
            response.getWriter().println(e.getMessage());
        } finally {
            //System.out.println("消耗时间：：：："+(System.currentTimeMillis()-start));
            Utils.clearLog();
        }
    }

    private void putSubUserForEzSelect(Map<String, String> session, User user,List<User> orgUserList) {
        List<Map<String, String>> userList = new ArrayList<>();
        Set<String> userIdSet=new HashSet<>();
        try {
            if(CollectionUtils.isNotEmpty(user.getUsers())){
                for (int i = 0; i < user.getUsers().size(); i++) {
                    Map<String, String> u = new HashMap<>();
                    if(userIdSet.contains(user.getUsers().get(i).getUserId() + "")){
                        continue;
                    }
                    userIdSet.add(user.getUsers().get(i).getUserId() + "");
                    u.put("K",user.getUsers().get(i).getUserId() + "");
                    u.put("V", StringUtils.lowerCase(user.getUsers().get(i).getUsername() + ""));
                    userList.add(u);
                }
            }
            if(CollectionUtils.isNotEmpty(orgUserList)){
                for (int i = 0; i < orgUserList.size(); i++) {
                    Map<String, String> u = new HashMap<>();
                    if(userIdSet.contains(orgUserList.get(i).getUserId() + "")){
                        continue;
                    }
                    userIdSet.add(orgUserList.get(i).getUserId() + "");
                    u.put("K",orgUserList.get(i).getUserId() + "");
                    u.put("V",StringUtils.lowerCase(orgUserList.get(i).getUsername() + ""));
                    userList.add(u);
                }
            }
            session.put("EZ_SESSION_MY_USER_MAP_KEY", JsonUtils.convertConllectionToJsonStr(userList));
        }catch (Exception e){
            LOGGE.error("", e);
        }
    }
    public String getOrgUrl() {
        return orgUrl;
    }

    public void setOrgUrl(String orgUrl) {
        this.orgUrl = orgUrl;
    }



}
