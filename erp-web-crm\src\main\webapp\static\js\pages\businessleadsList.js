void function () {
    //获取来源
    let pageFrom = GLOBAL.getQuery('from');

    new Vue({
        el: '#page-container',
        data: {
            tableHeaders: [
                {
                    key: "leadsNo",
                    label: "线索编号",
                    width: "150px",
                    lock: true
                }, 
                {
                    key: "clueTypeName",
                    label: "来源",
                    width: "95px",
                },
                {
                    key: "followstatus",
                    label: "线索状态",
                    width: "75px"
                },
                {
                    key: "goodsInfo",
                    label: "产品信息",
                    width: "360px"
                },
                {
                    key: "content",
                    label: "商品分类",
                    width: "220px"
                },
                {
                    key: "traderName",
                    label: "客户名称",
                    width: "240px",
                    copy: !(pageFrom === 'chance')
                },
                {
                    key: "contact",
                    label: "联系人",
                    width: "80px",
                },
                {
                    key: "phone",
                    label: "手机",
                    width: "130px",
                    tel: true,
                    copy: !(pageFrom === 'chance')
                },
                {
                    key: "telephone",
                    label: "固话",
                    width: "135px",
                    tel: true
                },
                {
                    key: "addTime",
                    label: "创建时间",
                    width: "150px"
                },
                {
                    key: "assignTime",
                    label: "分配时间",
                    width: "160px"
                },
                {
                    key: "belonger",
                    label: "归属销售",
                    width: "140px",
                    avatar: 'belongerPic'
                },
                {
                    key: "record",
                    label: "跟进记录",
                    width: "220px"
                },
                {
                    key: "sourceName",
                    label: "渠道类型",
                    width: "85px",
                },
                {
                    key: "communicationName",
                    label: "渠道名称",
                    width: "130px",
                },
                {
                    key: "address",
                    label: "地区",
                    width: "160px",
                },
                {
                    key: "inquiryName",
                    label: "询价行为",
                    width: "70px",
                },
                {
                    key: "entrancesName",
                    label: "咨询入口",
                    width: "110px",
                },
                {
                    key: "functionsName",
                    label: "功能",
                    width: "90px",
                },
                {
                    key: "creatorName",
                    label: "创建人",
                    width: "140px",
                    avatar: 'creatorPic',
                },
                {
                    key: "option",
                    label: "操作",
                    width: "90px",
                },
            ],
            chanceTypeList: [],
            defaultTab: [{
                label: '全部线索',
                id: 'all'
            }],
            searchParams: {
                leadsNo: '',
                followStatusList: [],
                belongerIdList: [],
                belongerMultiItems: [],
                shareUserIdList: [],
                shareUserMultiItems: [],
                contact: '',
                contactMerge: '',
                traderName: '',
                filterAddTime: [],
                startAddTime: '',
                endAddTime: '',
                filterAssignTime: [],
                startAssignTime: '',
                endAssignTime: '',
                filterNextCommunicationDate: [],
                nextCommunicationDateStart: '',
                nextCommunicationDateEnd: '',
                filterLastCommunicationTime: [],
                startLastCommunicationTime: '',
                endLastCommunicationTime: '',
                clueTypeList: [],
                sourceIdList: [], //线索渠道(渠道类型)-一级ID
                communicationIdList: [], //线索渠道-二级ID
                sourceSelectedIds: {},
                content: '',
                goodsInfo: '',
                inquiryList: [],
                entrancesList: [],
                functionsList: [],
                creatorUserIdList: [],
                creatorUserMultiItems: [],
                //终端区域
                provinceIdList: [],
                cityIdList: [],
                countyIdList: [],
                areaSelectedIds: {},
                //销售区域
                salerAreaSelectedIds: {},
                provinceIds: [],
                cityIds: [],
                //end
                //分类
                selectedCategoryIdList: {},
                categoryIdList: [],
                //end
                //销售部门
                organizationIdList: [], //所有勾选部门id,后台筛选用
                organizationIdValues: [] //所有勾选最下级部门id，组件的model值
            },
            leadsStatusList: [],
            sourceList: [{
                label: '总机',
                value: 391
            }, {
                label: '销售',
                value: 392
            }, {
                label: '自有商城',
                value: 394
            }, {
                label: '拜访',
                value: 6012
            }],
            clueTypeRemoteInfo: {
                url: '/crm/sysOption/public/getByParentCode?parentCode=clueType',
                parseLabel: 'title',
                parseValue: 'sysOptionDefinitionId'
            },
            belongerListRemoteInfo: {
                url: '/crm/businessLeads/profile/findAllBelongUser',
                paramsType: 'url',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            shareUserListRemoteInfo: {
                url: '/crm/user/profile/getAllUserInfo',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            creatorUserIdListRemoteInfo: {
                url: '/crm/businessLeads/profile/findAllCreateUser',
                paramsType: 'url',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            isShowBelongerDialog: false, //是否展示分配人弹层 
            editId: [],
            belonger: '',
            // 分配线索
            allUserRemoteInfo: {
                url: '/crm/user/profile/getAllUserInfo',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            // 设置协作人
            allShardUserRemoteInfo: {
                url: '/crm/businessLeads/profile/queryShardUserForBusiness',
                paramsType: 'url',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            isShowShareDialog: false, //协作人列表弹层
            isShowShareAddDialog: false, //协作人设置弹层
            isShareLoading: true, //协作人弹层加载
            shareListHeaders: [
                {
                    key: "saleUserName",
                    label: "归属销售",
                    width: "220px"
                },
                {
                    key: "addTime",
                    label: "添加时间",
                    width: "220px"
                },
                {
                    key: "option",
                    label: "操作",
                    width: "120px"
                },
            ],
            sharelist: [],
            shareId: '',
            shareLeadsno: '',
            shareUserId: '',
            shareUserName: '',
            addressValue: [],
            addressData: [],
            addressLv2Data: [],
            sourceData: [], //渠道数据
            isFromChance: false, //是否来自商机切换
            allCategoryList: [],
            departmentData: []
        },
        created() {
            if(pageFrom === 'chance') {
                this.isFromChance = true;
            }

            //默认最近90天
            this.initAddTime();

            //获取地址数据
            this.getAddressData();

            //获取渠道数据
            this.getSourceData();

            //状态枚举数据
            this.getStatusList();

            //获取分类数据
            this.getCategoryList();

            //获取部门数据
            this.getDepartmentList();
        },
        methods: {
            triggerTabChange(index) {
                window.parent.triggerTabChange(index);
            },
            initAddTime(){
                let endTime = moment(new Date()).format('YYYY-MM-DD');
                let startTime = moment(new Date().getTime() - 90*24*60*60*1000).format('YYYY-MM-DD');

                this.searchParams.endAddTime = endTime + ' 23:59:59';
                this.searchParams.startAddTime = startTime + ' 00:00:00';
                this.searchParams.filterAddTime = [startTime, endTime];
            },
            getStatusList() {
                let list = [];
                for (let item in GLOBAL.BUSINESSLEADS_STATUS) {
                    list.push({
                        label: GLOBAL.BUSINESSLEADS_STATUS[item],
                        value: item + ''
                    })
                }

                this.leadsStatusList = list;
            },
            getAddressData() {
                this.$axios.post('/crm/common/profile/getRegionAll').then(({ data }) => {
                    if (data.success) {
                        this.addressData = data.data;

                        let lv2Data = [];
                        this.addressData.forEach(item => {
                            let itemData = JSON.parse(JSON.stringify(item));
                            if(itemData.children && itemData.children.length) {
                                itemData.children.forEach(lv2 => {
                                    delete lv2.children;
                                })
                            }

                            lv2Data.push(itemData)
                        })

                        this.addressLv2Data = lv2Data;
                    }
                })
            },
            parseCategoryData(list) {
                let categorys = [];

                list.forEach(item => {
                    let cateItem = {
                        label: item.baseCategoryName,
                        value: item.baseCategoryId,
                        children: []
                    }

                    if(item.children && item.children.length) {
                        cateItem.children = this.parseCategoryData(item.children)
                    }

                    categorys.push(cateItem);
                }) 

                return categorys;
            },
            getCategoryList() {
                this.$axios.get(`/crm/category/public/getAllCategory`).then(({ data }) => {
                    if(data.success) {
                        this.allCategoryList = this.parseCategoryData(data.data || []);
                    }
                })
            },
            getSourceData() {
                this.$axios.post('/crm/businessLeads/profile/getCascaderChannelOptions').then(({ data }) => {
                    if (data.success) {
                        this.sourceData = data.data;
                    }
                })
            },
            //获取组织架构数据
            getDepartmentList() {
                this.$axios.get(`/crm/user/profile/getFullDepartmentTree`).then(({ data }) => {
                    if(data.success) {
                        let allDep = data.data.childOrganization[0].childOrganization;

                        this.departmentData = this.parseDepartmentList(allDep);
                    }
                })
            },
            parseDepartmentList(list) {
                let parseList =  [];
                list.forEach(item => {
                    let itemData = {
                        label: item.organizationName,
                        value: item.organizationId,
                        children: [],
                        checked: false,
                        hasChecked: false
                    }

                    if(item.childOrganization && item.childOrganization.length) {
                        itemData.children = this.parseDepartmentList(item.childOrganization);
                    }

                    parseList.push(itemData);
                })

                return parseList;
            },
            handlerDepartmentChange(data) {
                this.searchParams.organizationIdList = data.allSelectedIds;
            },
            //日期范围选择后塞值
            handlerFilterDateChange(key, value) {
                let startKey = 'start' + key;
                let endKey = 'end' + key;

                if (key === 'nextCommunicationDate') {
                    startKey = key + 'Start';
                    endKey = key + 'End';
                }

                this.searchParams[startKey] = value[0] ? value[0] + ' 00:00:00' : '';
                this.searchParams[endKey] = value[1] ? value[1] + ' 23:59:59' : '';
            },
            remoteSelectChange(key, data) {
                this.searchParams[key] = data.list || [];
            },
            showBelongerDialog() {
                this.isShowBelongerDialog = true;
                this.belonger = '';

                this.$form.rules({
                    belonger: {
                        required: '请选择新归属人'
                    },
                }, 'belongerForm', this)
            },
            assignLeads() {
                let ids = this.$refs.listContainer.getSelectedData('id');

                if (!ids || !ids.length) {
                    this.$message.warn('请勾选具体线索');
                } else {
                    this.editId = ids;
                    this.showBelongerDialog();
                }
            },
            assignLeadsItem(data) {
                this.editId = [data.id];
                this.showBelongerDialog();
            },
            setBelonger() {
                if (this.$form.validForm('belongerForm')) {
                    GLOBAL.showGlobalLoading();
                    this.$axios.post('/crm/businessLeads/profile/assign', {
                        ids: this.editId,
                        userId: this.belonger
                    }).then(({ data }) => {
                        this.isShowBelongerDialog = false;
                        GLOBAL.hideGlobalLoading();
                        if (data.success) {
                            this.$message.success('分配成功');
                            this.$refs.listContainer.refresh();
                        } else {
                            this.$message.warn(data.message || '网络异常')
                        }
                    })
                }
            },
            showShareList(data) {
                this.isShowShareDialog = true;
                this.shareId = data.id;
                this.shareLeadsno = data.leadsNo;
                this.sharelist = [];
                this.getShareList();
            },
            handlerShareSelect(data) {
                this.shareUserName = data.selected.label;
            },
            getShareList() {
                this.isShareLoading = true;
                this.$axios.post(`/crm/businessLeads/profile/queryShardList?businessId=${this.shareId}&businessType=5`).then(({ data }) => {
                    this.isShareLoading = false;
                    if (data.success) {
                        this.sharelist = data.data.list || [];
                    } else {
                        this.$message.warn(data.message || '网络异常')
                    }
                })
            },
            showShareAddDialog() {
                this.isShowShareAddDialog = true;
                this.shareUserId = '';
                this.shareUserName = '';
                this.allShardUserRemoteInfo.url = `/crm/businessLeads/profile/queryShardUserForBusiness?businessId=${this.shareId}&businessType=5`;
                this.$form.rules({
                    shareUserId: {
                        required: '请选择协作人'
                    },
                }, 'shareDlgForm', this)
            },
            addShareUser() {
                if (this.$form.validForm('shareDlgForm')) {
                    GLOBAL.showGlobalLoading();
                    this.$axios.post('/crm/businessLeads/profile/shareBusiness', {
                        businessId: this.shareId,
                        businessNo: this.shareLeadsno,
                        saleUserId: this.shareUserId,
                        saleUserName: this.shareUserName,
                        businessType: 5
                    }).then(({ data }) => {
                        this.isShowShareAddDialog = false;
                        GLOBAL.hideGlobalLoading();
                        if (data.success) {
                            this.$message.success('添加成功');
                            this.getShareList();
                        } else {
                            this.$message.warn(data.message || '网络异常')
                        }
                    })
                }
            },
            removeShareUser(data) {
                let _this = this;
                this.$popup.warn({
                    message: '是否移除协作人？',
                    buttons: [{
                        txt: '是',
                        btnClass: 'confirm',
                        callback() {
                            GLOBAL.showGlobalLoading();
                            _this.$axios.post('/crm/businessLeads/profile/cancelShare?id=' + data.id).then(({ data }) => {
                                GLOBAL.hideGlobalLoading();
                                if (data.success) {
                                    _this.$message.success("移除成功");
                                    _this.getShareList();
                                } else {
                                    _this.$message.warn(data.message || '网络异常')
                                }
                            })
                        }
                    },
                    {
                        txt: '取消',
                    }]
                })
            },
            handleFilterAddressChange(val) {
                this.searchParams.areaSelectedIds = val || {};
                this.searchParams.provinceIdList = val.level1 || [];
                this.searchParams.cityIdList = val.level2 || [];
                this.searchParams.countyIdList = val.level3 || [];
            },
            handleFilterSalerAddressChange(val) {
                this.searchParams.salerAreaSelectedIds = val || {};
                this.searchParams.provinceIds = val.level1 || [];
                this.searchParams.cityIds = val.level2 || [];
            },
            parseFilterCategory(ids, list) {
                let resIds = [];
                if(!list) {
                    list = this.allCategoryList;
                }
                list.forEach(item => {
                    if(ids.indexOf(item.value) !== -1) {
                        if(item.children && item.children.length) {
                            let cIds = [];
                            item.children.forEach(item1 => {
                                cIds.push(item1.value);
                            })
                            resIds = resIds.concat(this.parseFilterCategory(cIds, item.children));
                        } else {
                            resIds.push(item.value);
                        }
                    } else {
                        if(item.children && item.children.length) {
                            item.children.forEach(item1 => {
                                if(ids.indexOf(item1.value) !== -1) {
                                    if(item1.children && item1.children.length) {
                                        let cIds = [];
                                        item1.children.forEach(item2 => {
                                            cIds.push(item2.value);
                                        })
                                        resIds = resIds.concat(this.parseFilterCategory(cIds, item1.children));
                                    } else {
                                        resIds.push(item.value);
                                    }
                                }
                            })
                        }
                    }
                })

                return resIds;
            },
            handleFilterCategoryChange(value) {
                this.searchParams.categoryIdList = this.parseFilterCategory(value.level1).concat(this.parseFilterCategory(value.level2).concat(value.level3));
                this.searchParams.selectedCategoryIdList = value || {};

                console.log(this.searchParams.categoryIdList);
            },
            handleFilterSourceChange(val) {
                this.searchParams.sourceSelectedIds = val || {};
                this.searchParams.sourceIdList = val.level1 || [];
                this.searchParams.communicationIdList = val.level2 || [];
            },
            handlerCallNumber(data) {
                GLOBAL.callNumber({
                    phone: data.phone || '',
                    traderId: data.data.traderId || 0,
                    traderType: 1,
                    callType: 7,
                    orderId: data.data.id || '',
                    traderContactId: data.data.traderContactId || 0
                })
            },
            showRecordDialog(row) {
                this.$refs.followRecordListDialog.open({
                    relatedId: row.id,
                    traderId: row.traderId,
                    traderName: row.traderName,
                    belongerId: row.belongerId,
                    belonger: row.belonger,
                    belongerPic: row.belongerPic,
                    traderNameLink: row.traderNameLink,
                    traderNameInnerLink: row.traderNameInnerLink,
                    tycFlag: row.tycFlag,
                    contact: row.contact,
                    traderContactId: row.traderContactId,
                    phone: row.phone,
                    telePhone: row.telephone,
                    communicateType: 4109,
                });
            },
            gotoLeadsAdd() {
                if(GLOBAL.auth('C0101')) {
                    GLOBAL.link({name:'新建线索', url: '/crm/businessLeads/profile/add', multi: true})
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            gotoChanceAdd() {
                if (GLOBAL.auth('C0201')) {
                    GLOBAL.link({ name: '新建商机/线索', url: '/crm/businessChance/profile/add', multi: true })
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            handlerRecordAdd (data) {
                console.log(data)
                if(data && data.noneNextDate === 1) {
                    let _this = this;

                    this.$popup.warn({
                        message: '无下次跟进时间，是否需要关闭该条线索？',
                        buttons: [{
                            txt: '关闭线索',
                            btnClass: 'delete',
                            callback() {
                                GLOBAL.link({name:'查看线索', url: '/crm/businessLeads/profile/detail?id=' + data.relatedId + '&isdelete=1'})
                            }
                        },
                        {
                            txt: '无需关闭',
                        }]
                    })
                }
            },
            // 天眼查详情
            openTyc (traderName) {
                this.$refs.tycDetail.open(traderName);
            },
            multiAddPartner() {
                let selectedArr = this.$refs.listContainer.getSelectedData();

                if (!selectedArr || !selectedArr.length) {
                    this.$message.warn('请勾选具体线索');
                } else {
                    let bizs = [];

                    selectedArr.forEach(item => {
                        bizs.push({
                            businessType: 5, // 1.商机 2.报价 3.订单 4.售后 5.线索
                            businessId: item.id,
                            businessNo: item.leadsNo
                        })
                    })

                    this.$refs.partnerCreateDialog.open({
                        multi: true,
                        list: bizs
                    });
                }
            },
            handlerAddPartner() {
                this.$refs.listContainer.refresh();
            }
        }
    })
}.call(this);