package com.vedeng.erp.trader.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.dto.D3ResultDto;
import com.vedeng.erp.trader.domain.dto.*;
import com.vedeng.erp.trader.domain.entity.TraderCustomerCategoryEntity;
import com.vedeng.erp.trader.domain.entity.WebAccountEntity;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.dto.TraderCustomerTerminal;
import com.vedeng.erp.trader.dto.TraderForSmartQuoteDto;
import com.vedeng.erp.trader.dto.TraderUserDto;
import com.vedeng.onedataapi.api.archived.res.TraderArchivedResDto;
import com.vedeng.onedataapi.api.usertrace.res.UserBehaviorResDto;
import com.vedeng.erp.trader.dto.TraderCustomerDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface TraderCustomerBaseService {

    /**
     * 获取客户关联客户信息
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2022/2/16 15:41.
     * @author: Randy.Xu.
     * @param traderQueryDto
     * @return: java.util.List<com.vedeng.erp.trader.domain.dto.TraderCustomerInfoDto>.
     * @throws:  .
     */
    List<TraderCustomerInfoDto> getTraderRelationInfo(TraderCustomerRelationQueryDto traderQueryDto);


    /**
     * A关联客户B，建立客户关联关系
     * @param traderCustomerId 客户A
     * @param associatedTraderCustomerId 客户B
     * @return 关联结果
     */
    String associateTraderCustomer(Integer traderCustomerId, Integer associatedTraderCustomerId);

    /**
     * 判断客户是否处于锁定并且未解锁成功的状态
     * @param traderCustomerIdList 客户集合
     * @return 是否处于锁定并且未解锁成功状态，true：是，false：否
     */
    Boolean checkTraderCustomerIsPrivatizedAndNotUnlocked(List<Integer> traderCustomerIdList);

    /**
     * 取消关联客户
     * @param traderCustomerId 待取消关联的客户ID
     * @return 取消关联结果
     */
    String cancelAssociateTraderCustomer(Integer traderCustomerId);

    /**
     * 查询客户基本信息
     * @param traderId 客户id
     * @return TraderCustomerInfoVo TraderCustomer 基本信息
     */
    TraderCustomerInfoVo getTraderCustomerInfoVo(Integer traderId);

    /**
     * 查询客户和客户归属销售信息
     * @param traderId 客户id
     * @return TraderCustomerInfoVo TraderCustomer 基本信息
     */
    TraderCustomerInfoVo getTraderCustomerInfo(Integer traderId);

    /**
     * 客户分类
     * @param traderCustomerCategoryId id
     * @return List<TraderCustomerCategoryEntity> 集合
     */
    List<TraderCustomerCategoryEntity> getCustomerCategory(Integer traderCustomerCategoryId);

    /**
     * 根据 姓名模糊查询
     * @param limit 限制返条数
     * @param name 客户名
     * @return List<TraderCustomerInfoVo>
     */
    List<TraderCustomerInfoVo> queryTraderCustomerInfoByName(Integer limit, String name);

    List<TraderCustomerInfoVo> queryTraderCustomerInfoByNameForCrm(Integer limit, String name);

    List<TraderCustomerInfoVo> queryTraderCustomerInfoByNameAndUserIdForCrm(Integer limit, String name,Integer userId,boolean belong);

    /**
     * 查询分享给自己的客户，但需要去除掉 归属自己的
     * @param limit
     * @param name
     * @param userId
     * @param belong
     * @return
     */
    List<TraderCustomerInfoVo> queryTraderCustomerInfoByNameAndUserIdForCrmShared(Integer limit, String name,Integer userId,List<Integer> notIncludeTraderIds,boolean share);

    /**
     * 获取归属销售id
     * @param traderId 客户id
     * @return 销售id
     */
    Integer getTraderCustomerUserIdByTraderId(Integer traderId);

    /**
     * 获取客户360信息
     *
     * @param traderId 客户id
     * @return TraderCustomerPortraitDto
     */
    TraderCustomerPortraitDto getTraderCustomerPortrait(Integer traderId);


    /**
     * 调用大数据接口查询
     *
     * @param name 查询参数
     * @param traderId -1 默认不排除 1 排除已经选择的
     * @param pageSize 每页大小
     * @param pageNum 页码
     * @return List<TraderCustomerTerminal>
     */
    PageInfo<TraderCustomerTerminal> getTerminalDetail(String name,Integer traderId, Integer pageSize,Integer pageNum);

    /**
     * 查询并组装关联客户信息（用于展示关系图）
     *
     * @param traderId traderId
     * @return Map<String,List<Map<String,Object>>>
     */
    Map<String, List<Map<String, Object>>> handleRelatedCustomerInfo(Integer traderId);

    /**
     * 获取用户分类和用户行为信息
     *
     * @param traderId traderId
     * @return Map<String,Object>
     */
    Map<String, Object> getCustomerBehaviorTrace(Integer traderId);




    /**
     * 根据手机号码获取行为轨迹
     *
     * @param mobile
     * @return
     */
    UserBehaviorResDto getBehaviorByMobile(String mobile, Integer pageNo, Integer pageSize);

    /**
     * 根据traderId查询注册用户集合
     *
     * @param traderId traderId
     * @return List<WebAccount>
     */
    List<WebAccountEntity> getWebAccountListByTraderId(Integer traderId);



    TraderCustomerActionDto getTraderCustomerActionInfo(Integer traderId);

    /**
     * 获取客户行为基本信息
     *
     * @param
     * @return TraderCustomerPortraitDto
     */
    TraderCustomerActionDto getTraderCustomerAction( Integer traderId);

    /**
     * 根据TraderCutomerId获取客户信息
     * @param traderCustomerId
     * @return
     */
    TraderCustomerInfoDto getTraderCutomerInfoById(Integer traderCustomerId);

    /**
     * 分页查询大数据经销链路信息
     *
     * @param pageParam 分页查询参数
     * @return PageInfo<DistributionLinkDto>
     */
    PageInfo<DistributionLinkDto> searchDistributionLinkPage(PageParam<DistributionLinkDto> pageParam);

    /**
     * 查询终端
     * @param pageParam
     * @param userId
     * @return
     */
    PageInfo<TerminalResponseDto> searchTerminalPage(PageParam<TerminalRequestDto> pageParam,Integer userId);

    /**
     * 查询终端对应的经销商
     * @param pageParam
     * @param userId
     * @return
     */
    PageInfo<TerminalDistributionLinkResponseDto> searchTerminaCooperationDistributionLinkPage(PageParam<TerminalDistributionLinkRequestDto> pageParam,Integer userId);

    /**
     * 查询大数据经销链路并组装成d3.js的数据格式
     *
     * @param pageParam 分页查询参数
     * @return D3ResultDto
     */
    D3ResultDto searchDistributionLinkD3(PageParam<DistributionLinkDto> pageParam);

    /**
     * CPM计算标签
     *
     * @param traderId traderId
     * @return TraderPerfectTagDto
     */
    TraderPerfectTagDto getTraderCpmTag(Integer traderId);

    /**
     * 查询天眼查
     * @param name
     * @param traderId
     * @param pageSize
     * @param pageNum
     * @return
     */
    PageInfo<TraderCustomerTerminal> getTycTerminalInfo(String name, Integer traderId, Integer pageSize, Integer pageNum);

    /***
     * 分页查询客户信息（同新增订单页面逻辑）
     * @param pageParam PageParam<TraderCustomerDto>
     * @return PageInfo<TraderCustomerDto>
     */
    PageInfo<TraderCustomerDto> getTraderCustomerPage(PageParam<TraderCustomerDto> pageParam);

    R<PageInfo<TraderForSmartQuoteDto>> getTraderListForSmartQuote(String traderName, Integer userId, PageParam<TraderForSmartQuoteDto> pageParam);

    /**
     * 根据traderId查询归属销售信息
     *
     * @param traderId traderId
     * @return TraderUserDto
     */
    TraderUserDto getTraderUserByTraderId(Integer traderId);

    /**
     * 查询所属地区
     * @param userId
     * @return
     */
    Object[] terminalAreaSearchList(Integer userId);

    /**
     * 获取终端360信息
     *
     * @param searchName 终端名称
     * @return TraderCustomerPortraitDto
     */
    TraderCustomerTerminalPortraitDto getTraderCustomerTerminalPortrait(String searchName);

    /**
     * 终端360详情查询大数据经销链路并组装成d3.js的数据格式
     *
     * @param pageParam 分页查询参数
     * @return D3ResultDto
     */
    D3ResultDto searchTerminalDistributionLinkD3(PageParam<DistributionLinkDto> pageParam);

    /**
     * 分页查询终端360大数据经销链路信息
     *
     * @param pageParam 分页查询参数
     * @return PageInfo<DistributionLinkDto>
     */
    PageInfo<DistributionLinkDto> searchTerminalDistributionLinkPage(PageParam<DistributionLinkDto> pageParam);

    /**
     * 查询终端批量派发拜访计划信息
     * @param terminalResponseDtoList 终端信息
     * @return DistributionVisitPlanDto
     */
    DistributionVisitPlanDto getDistributeVisitPlanList(List<TerminalResponseDto> terminalResponseDtoList,Integer userId);

    /**
     * 提交拜访计划
     * @param submitVisitPlanDto
     */
    void submitVisitPlan(SubmitVisitPlanDto submitVisitPlanDto,Integer userId);

    /**
     * 根据traderIds查询客户信息
     * @param traderIds
     * @return
     */
    List<TraderCustomerDto> getTraderCustomerListByTraderIds(List<Integer> traderIds);

    /**
     * 根据客户ID获取用户档案信息
     * @param traderId 客户ID
     * @param archiveCursor 游标
     * @return
     */
    Map<String, Object> getCustomerArchiveTrace(Integer traderId,String archiveCursor, List<Long> archiveIds);


    /**
     * 根据客户ID、档案ID列表查询客户档案
     * @param traderId
     * @param archiveIdList
     * @param archiveCursor 
     * @param pageNo
     * @param pageSize
     * @return
     */
	TraderArchivedResDto getCustomerArchiveTraceByArchiveIds(Integer traderId,String startTime,String endTime, List<Long> archiveIdList, String archiveCursor, Integer pageNo,
			Integer pageSize);

    /**
     * 根据客户ID获取客户等级对应的分数
     * @param traderId
     * @return
     */
    Integer getTraderScoreByTraderId(Integer traderId);

    List<TraderCustomerInfoVo> getTraderCustomerByTraderName(String keyword, int startForPage, int limitC);

    long countTraderCustomerByTraderName(String keyword);

    List<TraderCustomerInfoVo> queryTraderCustomerInfoByNameAndSubUserId(int limit, String name, List<Integer> allSubUserIdsList, List<Integer> notIncludeTraderIds);

    List<TraderCustomerInfoVo> getTraderCustomerByTraderNameDefault(String name, int i, int i1);
}
