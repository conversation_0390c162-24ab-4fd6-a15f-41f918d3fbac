package com.vedeng.trader.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.newtask.model.YXGTraderAptitude;
import com.newtask.service.TraderGroupService;
import com.pricecenter.dto.PriceInfoUploadValidatorDto;
import com.pricecenter.dto.ValidatorResult;
import com.pricecenter.service.validator.CustomerNameValidator;
import com.pricecenter.service.validator.ValidatorChain;
import com.pricecenter.service.validator.ValidatorChainBuild;
import com.rabbitmq.MsgProducer;
import com.rabbitmq.OpMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.Role;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.model.vo.RoleVo;
import com.vedeng.authorization.service.AuthService;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.*;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ReqVo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.HttpSendUtil;
import com.vedeng.common.trace.annotation.EventTrackingAnnotation;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.*;
import com.vedeng.common.validator.FormToken;
import com.vedeng.coupon.service.BaseCouponService;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodApplyCheckStatusEnum;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodTypeEnum;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodCreditHistoryDto;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodCreditHistoryQueryDto;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodDetailsDto;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodSummaryInfoDto;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.customerbillperiod.model.CustomerBillPeriod;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodApply;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodApplyService;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodService;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.business.dto.PublicCustomerRecordDto;
import com.vedeng.erp.finance.dto.CheckInvoiceApplyResponseDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.service.KingDeeCustomerApiService;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.trader.api.CommunicateVoiceTaskApi;
import com.vedeng.erp.trader.constant.AiConstant;
import com.vedeng.erp.trader.dto.*;
import com.vedeng.erp.trader.service.*;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.TraderAccountPeriodApply;
import com.vedeng.goods.service.UnitService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.market.api.dto.request.QueryCouponCenterRequest;
import com.vedeng.market.api.dto.response.mycoupon.MyCouponDto;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.qualityReport.service.TmpTraderValidtimeExtService;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.Tag;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.system.model.vo.AccountSalerToGo;
import com.vedeng.system.service.*;
import com.vedeng.todolist.service.impl.ZlbCheckTraderCustomerCertificate;
import com.vedeng.trader.dao.RTraderJUserMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.dto.CustomerBillPeriodCreditHistoryVo;
import com.vedeng.trader.group.model.TraderGroup;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.dto.*;
import com.vedeng.trader.model.model.vo.CustomerBillPeriodApplyVo;
import com.vedeng.trader.model.vo.*;
import com.vedeng.trader.service.CustomerAccountPeriodProcessService;
import com.vedeng.trader.service.TraderCustomerService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskInfo;
import org.apache.commons.collections.MapUtils;
import org.apache.poi.ss.usermodel.*;
import org.olap4j.impl.ArrayMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <b>Description:</b><br>
 * 客户+经销商
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.trader.controller <br>
 * <b>ClassName:</b> TraderController <br>
 * <b>Date:</b> 2017年5月10日 上午9:38:28
 */

/**
 * <b>Description:</b><br>
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.trader.controller
 * <br><b>ClassName:</b> TraderCustomerController
 * <br><b>Date:</b> 2017年6月5日 下午5:42:35
 */
@SuppressWarnings("all")
@Controller
@RequestMapping("/trader/customer")
public class TraderCustomerController extends BaseController {
    Logger logger = LoggerFactory.getLogger(TraderCustomerController.class);
    @Autowired
    @Qualifier("regionService")
    private RegionService regionService;// 自动注入regionService

    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired // 自动装载
    @Qualifier("historyService")
    private HistoryService historyService;

    @Autowired
    private HttpSendUtil httpSendUtil;

    //	@Autowired
//	@Qualifier("traderCustomerService")
    @Resource
    private TraderCustomerService traderCustomerService;
    
    @Resource
    private PublicCustomerRecordApiService publicCustomerRecordApiService;

    @Autowired
    @Qualifier("sysOptionDefinitionService")
    private SysOptionDefinitionService sysOptionDefinitionService;
    @Resource
    private UserService userService;
    @Resource
    private PositService positService;
    @Autowired
    @Qualifier("tagService")
    private TagService tagService;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;

    @Resource
    private DictionaryService dictionaryService;

    @Autowired
    @Qualifier("ftpUtilService")
    private FtpUtilService ftpUtilService;


    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("saleorderService")
    private SaleorderService saleorderService;

    @Autowired
    @Qualifier("vedengSoapService")
    private VedengSoapService vedengSoapService;

    @Autowired
    @Qualifier("orgService")
    protected OrgService orgService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private VgoodsService vGoodsService;
    @Resource
    WebAccountMapper webAccountMapper;

    @Autowired
    private BaseCouponService baseCouponService;

    @Autowired
    private TraderGroupService traderGroupService;

    @Autowired
    private CustomerNameValidator customerNameValidator;

    // add by Randy.Xu 2021/1/7 17:41 .Desc: VDERP-4927 质量报表添加客户最近一次通过审核的时间. begin
    @Autowired
    private TmpTraderValidtimeExtService tmpTraderValidtimeExtService;
    // add by Randy.Xu 2021/1/7 17:41 .Desc: VDERP-4927 质量报表添加客户最近一次通过审核的时间. end

    @Autowired
    private TraderWorkAreaApiService traderWorkAreaApiService;


    @Autowired
    private RiskCheckService riskCheckService;

    @Autowired
    private ZlbCheckTraderCustomerCertificate zlbCheckTraderCustomerCertificate;

    @Value("${new_trader_certificate_check_startTime}")
    private Long newTraderCertificateCheckStartTime;

    @Value("${trader_certificate_check_startTime}")
    private Long traderCertificateCheckStartTime;

    @Value("${jd.upload.username}")
    private String jdUploadUsername;

//    @Value("${jd.upload.tradeId}")
//    private String jdUploadTradeId;

    @Autowired
    private TraderMapper traderMapper;

    @Autowired
    private AuthService authService;

    @Autowired
    private CustomerBillPeriodApplyService customerBillPeriodApplyService;
    @Autowired
    private UnitService unitService;
    @Autowired
    private MsgProducer msgProducer;

    @Resource
    private CustomerAccountPeriodProcessService customerAccountPeriodProcessService;

    @Autowired
    @Qualifier("rTraderJUserMapper")
    private RTraderJUserMapper rTraderJUserMapper;

    @Value("${jcServiceDomain}")
    private String jcServiceDomain;

    @Value("${jcWeb}")
    private String jcWeb;

    @Autowired
    private CustomerBillPeriodService customerBillPeriodService;

    @Autowired
    private TraderCustomerApiService traderCustomerApiService;

    @Autowired
    private KingDeeCustomerApiService kingDeeCustomerApiService;

    @Autowired
    private TraderCustomerMarketingApiService traderCustomerMarketingApiService;

    @Autowired
    private TraderCustomerMarketingNodeApiService traderCustomerMarketingNodeApiService;

    @Autowired
    private TraderCustomerMarketingPrincipalApiService traderCustomerMarketingPrincipalApiService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private TraderCustomerTerminalApiService traderCustomerTerminalApiService;

    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;

    @Value("${traderCustomerLevelGrade}")
    private String traderCustomerLevelGradeList;

    @Value("${traderCustomerLevelGradeOriginal}")
    private String traderCustomerLevelGradeOriginal;

    @Autowired
    private CommunicateVoiceTaskApi communicateVoiceTaskApi;

    @RequestMapping(value = "/changeTraderOwnerPage")
    public ModelAndView changeTraderOwnerPage() {
        return new ModelAndView("trader/customer/batch_change_trader_owner");
    }

    /**
     * <b>Description:</b>分配客户实现模板导入批量划拨客户的功能<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2019/12/20
     */
    @ResponseBody
    @RequestMapping(value = "/batchChangeTraderOwner")
    public ResultInfo<?> batchChangeTraderOwner(HttpServletRequest request,
                                                @RequestParam("lwfile") MultipartFile lwfile) throws IOException {
        FileInputStream fileInputStream = null;
        try {
            User currentUser = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            String path = request.getSession().getServletContext().getRealPath("/upload/saleorder");
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);

            if (fileInfo.getCode() == 0) {// 上传成功

                List<RTraderJUser> traderJUsers = new ArrayList<>();
                fileInputStream = new FileInputStream(new File(fileInfo.getFilePath()));
                // 获取excel路径
                Workbook workbook = WorkbookFactory.create(fileInputStream);
                // 获取第一面sheet
                Sheet sheet = workbook.getSheetAt(0);
                // 起始行
                int startRowNum = sheet.getFirstRowNum() + 1;// 第一行标题
                int endRowNum = sheet.getLastRowNum();// 结束行

                int startCellNum = 0;// 默认从第一列开始（防止第一列为空）
                // int endCellNum = sheet.getRow(0).getLastCellNum() - 1;//列数
                List<Trader> list = new ArrayList<>();// 保存Excel中读取的数据

                Map<Integer, List> traderIdMap = new HashMap<>();
                for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {
                    // 循环行数
                    Row row = sheet.getRow(rowNum);
                    Cell cellOne = row.getCell(0);
                    Cell cellTwo = row.getCell(1);
                    int r = rowNum + 1;
                    if (cellOne == null || cellOne.equals("")) {

                        return new ResultInfo<>(-1, String.format(TraderConstants.ROW_TRADER_NAME_IS_NULL, r));
                    }
                    if (cellTwo == null || cellTwo.equals("")) {
                        return new ResultInfo<>(-1, String.format(TraderConstants.ROW_USER_NAME_IS_NULL, r));
                    }

                    String traderName = cellOne.getStringCellValue();
                    String userName = cellTwo.getStringCellValue();
                    Trader trader = new Trader();
                    trader.setTraderName(traderName);
                    trader.setTraderType(1);
                    trader = traderCustomerService.getTraderByTraderName(trader, 1);

                    if (trader == null || trader.getTraderId() == null) {
                        return new ResultInfo<>(-1, String.format(TraderConstants.ROW_TRADER_IS_NOT_EXIST, r));
                    }

                    User user = userService.getUserByNameAndPositionType(userName, TraderConstants.SALESPERSON_POSITION_TYPE);

                    if (user == null || user.getUserId() == null) {
                        return new ResultInfo<>(-1, String.format(TraderConstants.ROW_USER_IS_NOT_EXIST, r));
                    }
                    if (trader.getSource() != null && trader.getSource() == 1) {
                        if (traderIdMap.containsKey(user.getUserId())) {
                            traderIdMap.get(user.getUserId()).add(trader.getTraderId());
                        } else {
                            List<Integer> traderIds = new ArrayList<>();
                            traderIds.add(trader.getTraderId());
                            traderIdMap.put(user.getUserId(), traderIds);
                        }
                    }
                    RTraderJUser traderJUser = new RTraderJUser();
                    traderJUser.setCompanyId(1);
                    traderJUser.setTraderId(trader.getTraderId());
                    traderJUser.setUserId(user.getUserId());
                    traderJUser.setSource(trader.getSource());
                    traderJUser.setTraderType(1);
                    traderJUser.setUserName(user.getUsername());
                    traderJUsers.add(traderJUser);

                }

                for (RTraderJUser t : traderJUsers) {
                    traderCustomerService.changeTraderOwner(t,currentUser);
                    if (t.getSource() != null && t.getSource() == 1 && traderIdMap.containsKey(t.getUserId())) {
                        Map<String, Object> map = Maps.newHashMap();
                        map.put("salerId", t.getUserId());
                        map.put("salerName", t.getUserName());
                        map.put("traderIdList", traderIdMap.get(t.getUserId()));
                        ResultInfo resultInfo = traderCustomerService.putTraderSaleUserIdtoHC(map);
                        traderIdMap.remove(t.getUserId());
                    }
                }
            }
        } catch (Exception ex) {

            logger.error("批量导入失败", ex);
            return new ResultInfo<>(-1, "操作失败");
        } finally {
            if (fileInputStream != null) {
                fileInputStream.close();
                fileInputStream = null;
            }

        }
        return new ResultInfo<>(0, "操作成功");
    }

    @ResponseBody
    @RequestMapping(value = "/importChangeTraderOwner")
    @NoNeedAccessAuthorization
    public ResultInfo<?> importChangeTraderOwner(HttpServletRequest request,
                                                @RequestParam("file") MultipartFile lwfile) throws IOException {
        log.info("批量划拨导入");
        FileInputStream fileInputStream = null;
        try {
            User currentUser = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            String path = request.getSession().getServletContext().getRealPath("/upload/saleorder");
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);

            if (fileInfo.getCode() == 0) {// 上传成功

                List<RTraderJUser> traderJUsers = new ArrayList<>();
                fileInputStream = new FileInputStream(new File(fileInfo.getFilePath()));
                // 获取excel路径
                Workbook workbook = WorkbookFactory.create(fileInputStream);
                // 获取第一面sheet
                Sheet sheet = workbook.getSheetAt(0);
                // 起始行
                int startRowNum = sheet.getFirstRowNum() + 1;// 第一行标题
                int endRowNum = sheet.getLastRowNum();// 结束行

                int startCellNum = 0;// 默认从第一列开始（防止第一列为空）
                // int endCellNum = sheet.getRow(0).getLastCellNum() - 1;//列数
                List<Trader> list = new ArrayList<>();// 保存Excel中读取的数据

                Map<Integer, List> traderIdMap = new HashMap<>();
                for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {
                    // 循环行数
                    Row row = sheet.getRow(rowNum);
                    Cell cellOne = row.getCell(0);
                    Cell cellTwo = row.getCell(1);
                    int r = rowNum + 1;
                    if (cellOne == null || cellOne.equals("")) {

                        return new ResultInfo<>(-1, String.format(TraderConstants.ROW_TRADER_NAME_IS_NULL, r));
                    }
                    if (cellTwo == null || cellTwo.equals("")) {
                        return new ResultInfo<>(-1, String.format(TraderConstants.ROW_USER_NAME_IS_NULL, r));
                    }

                    String traderName = cellOne.getStringCellValue();
                    String userName = cellTwo.getStringCellValue();
                    Trader trader = new Trader();
                    trader.setTraderName(traderName);
                    trader.setTraderType(1);
                    trader = traderCustomerService.getTraderByTraderName(trader, 1);

                    if (trader == null || trader.getTraderId() == null) {
                        return new ResultInfo<>(-1, String.format(TraderConstants.ROW_TRADER_IS_NOT_EXIST, r));
                    }

                    User user = userService.getUserByNameAndPositionType(userName, TraderConstants.SALESPERSON_POSITION_TYPE);

                    if (user == null || user.getUserId() == null) {
                        return new ResultInfo<>(-1, String.format(TraderConstants.ROW_USER_IS_NOT_EXIST, r));
                    }
                    if (trader.getSource() != null && trader.getSource() == 1) {
                        if (traderIdMap.containsKey(user.getUserId())) {
                            traderIdMap.get(user.getUserId()).add(trader.getTraderId());
                        } else {
                            List<Integer> traderIds = new ArrayList<>();
                            traderIds.add(trader.getTraderId());
                            traderIdMap.put(user.getUserId(), traderIds);
                        }
                    }
                    List<AccountSalerToGo> accountSaler = traderCustomerService.getAccountSaler(Collections.singletonList(trader.getTraderId()));
                    
                    RTraderJUser traderJUser = new RTraderJUser();
                    traderJUser.setCompanyId(1);
                    traderJUser.setTraderId(trader.getTraderId());
                    traderJUser.setTraderCustomerId(trader.getTraderCustomerId());
                    traderJUser.setTraderName(trader.getTraderName());
                    traderJUser.setUserId(user.getUserId());
                    traderJUser.setSource(trader.getSource());
                    traderJUser.setTraderType(1);
                    traderJUser.setUserName(user.getUsername());
                    traderJUser.setBelongSaleUser(CollUtil.isNotEmpty(accountSaler) ? accountSaler.get(0).getUserName() : null);
                    traderJUsers.add(traderJUser);
                    if (traderJUsers.size() > 10000){
                        return new ResultInfo<>(-1, "批量导入仅限10000行数据");
                    }
                }

                List<RTraderJUser> bei = traderJUsers.stream().filter(e -> Objects.equals(e.getBelongSaleUser(),"Bei")).collect(Collectors.toList());
                List<RTraderJUser> notBei = traderJUsers.stream().filter(e -> !Objects.equals(e.getBelongSaleUser(),"Bei")).collect(Collectors.toList());
                
                checkNotPublicCustomer(notBei);

                List<PublicAssignDto> publicCustomerRecordIds = checkPublicCustomer(bei);
                
                for (RTraderJUser t : notBei) {
                    traderCustomerService.changeTraderOwner(t,currentUser);
                    if (t.getSource() != null && t.getSource() == 1 && traderIdMap.containsKey(t.getUserId())) {
                        Map<String, Object> map = Maps.newHashMap();
                        map.put("salerId", t.getUserId());
                        map.put("salerName", t.getUserName());
                        map.put("traderIdList", traderIdMap.get(t.getUserId()));
                        ResultInfo resultInfo = traderCustomerService.putTraderSaleUserIdtoHC(map);
                        traderIdMap.remove(t.getUserId());
                    }
                }

                if (CollUtil.isNotEmpty(publicCustomerRecordIds)){
                    Map<Integer, PublicAssignDto> traderMap = publicCustomerRecordIds.stream().collect(Collectors.toMap(PublicAssignDto::getTraderId, Function.identity(), (v1, v2) -> v1));
                    for (RTraderJUser rTraderJUser : bei) {
                        log.info("批量划拨公海客户,traderId：{},前归属销售：{}",rTraderJUser.getTraderId(),rTraderJUser.getUserId());
                        PublicAssignDto publicAssignDto = traderMap.get(rTraderJUser.getTraderId());
                        if (Objects.nonNull(publicAssignDto) 
                                && Objects.nonNull(publicAssignDto.getPublicCustomerRecordId()) 
                                && Objects.nonNull(rTraderJUser.getUserId())){
                            publicCustomerRecordApiService.updateOrgUserId(publicAssignDto.getPublicCustomerRecordId(),rTraderJUser.getUserId());
                            traderCustomerService.async(rTraderJUser,publicAssignDto,currentUser);
                        }
                    }
                }
            }
        } catch (ServiceException ex) {
            return new ResultInfo<>(-1, ex.getMessage());
        } catch (Exception ex) {
            logger.error("批量导入失败", ex);
            return new ResultInfo<>(-1, "操作失败");
        } finally {
            if (fileInputStream != null) {
                fileInputStream.close();
                fileInputStream = null;
            }

        }
        return new ResultInfo<>(0, "操作成功");
    }

    /**
     * 公海
     * @param bei
     * @return
     */
    private List<PublicAssignDto> checkPublicCustomer(List<RTraderJUser> bei){
        log.info("校验公海客户开始");
        if (CollUtil.isEmpty(bei)){
            log.info("无公海客户，校验结束");
            return null;
        }
        List<Integer> traderIds = bei.stream().map(RTraderJUser::getTraderId).collect(Collectors.toList());
        if (CollUtil.isEmpty(traderIds)){
            log.info("无公海客户，校验结束");
            return null;
        }
        List<PublicCustomerRecordDto> topOne = publicCustomerRecordApiService.getTopOne(traderIds);
        if (CollUtil.isEmpty(topOne)){
            log.info("无公海客户，校验结束");
            return null;
        }
        Map<Integer, PublicCustomerRecordDto> collect = topOne.stream().collect(Collectors.toMap(PublicCustomerRecordDto::getTraderCustomerId, Function.identity(),(key1,key2)->key1));
        List<PublicAssignDto> dto = new ArrayList<>();
        for (RTraderJUser rTraderJUser : bei) {
            PublicCustomerRecordDto topRecord = collect.get(rTraderJUser.getTraderCustomerId());
            // 公海客户
            if (Objects.isNull(topRecord) || (!Objects.equals(topRecord.getIsPrivatized(),0) && !Objects.equals(topRecord.getIsUnlock(),0))){
                throw new ServiceException(rTraderJUser.getTraderName() + "客户无公海记录，请确认");
            }
            PublicAssignDto publicAssignDto = new PublicAssignDto();
            publicAssignDto.setPublicCustomerRecordId(topRecord.getPublicCustomerRecordId());
            publicAssignDto.setUserId(rTraderJUser.getUserId());
            publicAssignDto.setTraderId(rTraderJUser.getTraderId());
            publicAssignDto.setOriginUserId(topRecord.getOriginUserId());
            dto.add(publicAssignDto);
        }
        log.info("校验公海客户结束:{}", JSONUtil.toJsonStr(dto));
        return dto;

    }

    private void checkNotPublicCustomer(List<RTraderJUser> notBei){
        log.info("校验非公海客户");
        // 非公海客户
        if (CollUtil.isEmpty(notBei)){
            log.info("无非公海客户，校验结束");
            return;
        }
        List<Integer> traderIds = notBei.stream().map(RTraderJUser::getTraderId).collect(Collectors.toList());
        if (CollUtil.isEmpty(traderIds)){
            log.info("无非公海客户，校验结束");
            return;
        }
        List<PublicCustomerRecordDto> topOne = publicCustomerRecordApiService.getTopOne(traderIds);
        if (CollUtil.isEmpty(topOne)){
            log.info("无公海记录，校验结束");
            return;
        }
        Map<Integer, PublicCustomerRecordDto> collect = topOne.stream().collect(Collectors.toMap(PublicCustomerRecordDto::getTraderCustomerId, Function.identity(),(key1,key2)->key1));
        for (RTraderJUser rTraderJUser : notBei) {
            PublicCustomerRecordDto topRecord = collect.get(rTraderJUser.getTraderCustomerId());
            // 公海客户
            if (Objects.nonNull(topRecord) && Objects.equals(topRecord.getIsPrivatized(),1) && Objects.equals(topRecord.getIsUnlock(),0)){
                log.info("{}客户处于公海锁定状态，校验结束",rTraderJUser.getTraderName());
                throw new ServiceException(rTraderJUser.getTraderName() + "正处于公海锁定状态，需等完全解锁后方可划拨");
            }
        }
        
    }

    /**
     * <b>Description:</b><br>
     * 客户+经销商列表
     *
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月10日 上午9:40:51
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "/index")
    public ModelAndView index(HttpServletRequest request, TraderCustomerVo traderCustomerVo,
                              @RequestParam(required = false, defaultValue = "1") Integer pageType,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize) {
        // 客户列表页面
        return searchTraderListPage(request, traderCustomerVo, pageType, pageNo, pageSize);
    }

    /**
     * <b>Description: 耗材商城的客户列表</b><br>
     *
     * @param request
     * @param traderCustomerVo
     * @param pageType         [页面类型] 默认1--erp的客户列表页面/2--耗材商城的客户列表页面
     * @param pageNo
     * @param pageSize
     * @return <b>Author: Franlin.wu</b>
     * <br><b>Date: 2018年11月21日 下午6:53:47 </b>
     */
    @RequestMapping(value = "/hcTraderListPage")
    public ModelAndView hcTraderListPage(HttpServletRequest request, TraderCustomerVo traderCustomerVo,
                                         @RequestParam(required = false, defaultValue = "0") Integer pageType,
                                         @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                         @RequestParam(required = false) Integer pageSize) {
        // 客户来源： 0 erp / 默认1耗材
        traderCustomerVo.setSource(ErpConst.ONE);
        return searchTraderListPage(request, traderCustomerVo, pageType, pageNo, pageSize);
    }


    /**
     * <b>Description:</b>判断该客户财务信息是否允许开票<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2019/12/11
     */
    @ResponseBody
    @RequestMapping(value = "/isTraderAllowInvoice", method = RequestMethod.POST)
    public ResultInfo isTraderAllowInvoice(Saleorder order) {
        InvoiceApplyDto invoiceApplyDto = new InvoiceApplyDto();
        invoiceApplyDto.setTraderId(order.getTraderId());
        invoiceApplyDto.setTraderName(order.getTraderName());
        invoiceApplyDto.setInvoiceType(order.getInvoiceType());
        invoiceApplyDto.setRelatedId(order.getSaleorderId());
        try{
            CheckInvoiceApplyResponseDto checkInvoiceApplyResponseDto = invoiceApplyApiService.checkInvoiceApply(invoiceApplyDto);
            return new ResultInfo(0, "校验完成",checkInvoiceApplyResponseDto);
        } catch (ServiceException e) {
            // 业务异常提醒
            return new ResultInfo(-1, e.getMessage());
        }catch (Exception e) {
            log.error("校验失败", e);
            return new ResultInfo(-1, "校验失败");
        }
    }

    /**
     * 售后开票申请前置校验
     * @param afterSalesId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/isTraderAllowInvoiceAfter", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public ResultInfo isTraderAllowInvoiceAfter(AfterSalesDto afterSalesDto) {
        try {
            CheckInvoiceApplyResponseDto checkInvoiceApplyResponseDto = invoiceApplyApiService.checkInvoiceApplyAfter(afterSalesDto.getAfterSalesId());
            return new ResultInfo(0, "校验完成", checkInvoiceApplyResponseDto);
        } catch (ServiceException e) {
            // 业务异常提醒
            return new ResultInfo(-1, e.getMessage());
        }catch (Exception e) {
            log.error("校验失败", e);
            return new ResultInfo(-1, "校验失败");
        }
    }

    /**
     * 规则校验
     *
     */
    @ResponseBody
    @RequestMapping(value = "/ruleCheck", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public ResultInfo ruleCheck(Saleorder order) {
        try{
            invoiceApplyApiService.ruleCheck(order.getSaleorderId());
            return new ResultInfo(0, "校验完成");
        }catch (ServiceException e){
            return new ResultInfo(-1, "校验失败");
        }
    }

    /**
     * <b>Description: 客户列表页面</b><br>
     *
     * @param request
     * @param traderCustomerVo
     * @param pageType         [页面类型] 默认1--erp的客户列表页面/2--耗材商城的客户列表页面
     * @param pageNo
     * @param pageSize
     * @return <b>Author: Franlin.wu</b>
     * <br><b>Date: 2018年11月21日 下午6:39:59 </b>
     */
    private ModelAndView searchTraderListPage(HttpServletRequest request, TraderCustomerVo traderCustomerVo,
                                              Integer pageType, Integer pageNo, Integer pageSize) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        Page page = getPageTag(request, pageNo, pageSize);
        // 查询所有职位类型为310的员工
        List<Integer> positionType = new ArrayList<>();
        positionType.add(SysOptionConstant.ID_310);//销售
        List<User> userList = userService.getMyUserList(user, positionType, false);
        List<User> userListByOrgId = userService.getMyUserListByUserOrgsList(user,positionType,false);
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(userListByOrgId)){
            Set<Integer> userIds = new HashSet<>();
            for (User u : userList) {
                userIds.add(u.getUserId());
            }
            // 遍历 userListByOrgId 并添加那些 userId 不在 userIds Set 中的 User 对象
            for (User newUser : userListByOrgId) {
                if (!userIds.contains(newUser.getUserId())) {
                    userList.add(newUser);
                    userIds.add(newUser.getUserId()); // 更新 userIds Set
                }
            }
            // 此时 userList 包含了原始用户以及新添加的，没有重复 userId 的用户

            // 使用自定义比较器进行排序，不区分大小写
            Collections.sort(userList, new Comparator<User>() {
                @Override
                public int compare(User u1, User u2) {
                    return u1.getUsername().compareToIgnoreCase(u2.getUsername());
                }
            });
        }



        if (userList == null || userList.size() == 0) {
            userList = new ArrayList<User>();
            userList.add(new User());
        }
        List<Integer> userIdList = new LinkedList<>();



        ModelAndView mv = new ModelAndView();
        // 将品牌搜索参数返回
        String selectTraderCustomerBrandList = request.getParameter("selectTraderCustomerBrandList");
        if(StrUtil.isNotBlank(selectTraderCustomerBrandList)){
            mv.addObject("selectTraderCustomerBrandList", selectTraderCustomerBrandList);
        }

        // 代理品牌 回显
        String selectAgencyBrandList = request.getParameter("selectAgencyBrandList");
        if(StrUtil.isNotBlank(selectAgencyBrandList)){
            mv.addObject("selectAgencyBrandList", selectAgencyBrandList);
        }

        String saleList = request.getParameter("saleList");
        if(StrUtil.isNotBlank(saleList)){
            mv.addObject("saleList", saleList);
        }

        String search = request.getParameter("search");
        // modify by frinlin.wu for[耗材商城客户列表] to add[!ErpConst.ZERO.equals(pageType)] at 2018-11-22 begin
        if ((search == null || "".equals(search)) && !ErpConst.ZERO.equals(pageType)) {
            // modify by frinlin.wu for[耗材商城客户列表] !ErpConst.ZERO.equals(pageType) at 2018-11-22 end
            for (User us : userList) {
                userIdList.add(us.getUserId());
            }
        } else {
            if (ObjectUtils.notEmpty(traderCustomerVo.getHomePurchasing())) {
                userIdList.add(traderCustomerVo.getHomePurchasing());
            }

            if (ObjectUtils.notEmpty(traderCustomerVo.getZone())) {
                traderCustomerVo.setAreaId(traderCustomerVo.getZone());
                List<Region> list = regionService.getRegionByParentId(traderCustomerVo.getCity());
                mv.addObject("zoneList", list);
                List<Region> cys = regionService.getRegionByParentId(traderCustomerVo.getProvince());
                mv.addObject("cityList", cys);
            } else if (ObjectUtils.notEmpty(traderCustomerVo.getProvince()) && ObjectUtils.notEmpty(traderCustomerVo.getCity()) && ObjectUtils.isEmpty(traderCustomerVo.getZone())) {
                traderCustomerVo.setAreaId(traderCustomerVo.getCity());
                List<Region> list = regionService.getRegionByParentId(traderCustomerVo.getCity());
                mv.addObject("zoneList", list);
                List<Region> cys = regionService.getRegionByParentId(traderCustomerVo.getProvince());
                mv.addObject("cityList", cys);
            } else if (ObjectUtils.notEmpty(traderCustomerVo.getProvince()) && ObjectUtils.isEmpty(traderCustomerVo.getCity()) && ObjectUtils.isEmpty(traderCustomerVo.getZone())) {
                traderCustomerVo.setAreaId(traderCustomerVo.getProvince());
                List<Region> cys = regionService.getRegionByParentId(traderCustomerVo.getProvince());
                mv.addObject("cityList", cys);
            } else {
                traderCustomerVo.setAreaId(null);
            }
            if (ObjectUtils.notEmpty(traderCustomerVo.getQueryStartTime())) {
                traderCustomerVo.setStartTime(DateUtil.convertLong(traderCustomerVo.getQueryStartTime(), "yyyy-MM-dd"));
            }
            if (ObjectUtils.notEmpty(traderCustomerVo.getQueryEndTime())) {
                traderCustomerVo.setEndTime(DateUtil.convertLong(traderCustomerVo.getQueryEndTime() + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));
            }
        }


        if (userIdList == null || userIdList.size() == 0) {
            //userIdList.add(-1);//防止关联不到用户全表查询
            userIdList = null;
        }
        traderCustomerVo.setUserIdList(userIdList);
        //VDERP-2465  客户中心-ERP客户列表改动
        //客户分群信息
        List<Integer> traderGroupIdList = new ArrayList<>();
        List<TraderGroup> traderGroupList = traderGroupService.getTraderGroupInfo(traderGroupIdList);
        mv.addObject("traderGroupList",traderGroupList);

        // 客户分页列表信息
        Map<String, Object> map = this.traderCustomerService.getTraderCustomerVoPage(traderCustomerVo, page, userList);
        List<TraderCustomerVo> list = null;
        if (map != null) {
            list = (List<TraderCustomerVo>) map.get("list");
            page = (Page) map.get("page");
        }
        setCouponFlag(list);
        //客户分群信息
        list = traderGroupService.setTraderGroupname(list,traderGroupList);
        mv.addObject("list", list);

        // 省级地区
        List<Region> provinceList = regionService.getRegionByParentId(1);
        //客户等级
        List<SysOptionDefinition> customerLevers = getSysOptionDefinitionList(SysOptionConstant.ID_11);

        mv.addObject("customerLevers", customerLevers);
        //销售评级
        List<SysOptionDefinition> salersLevers = getSysOptionDefinitionList(SysOptionConstant.ID_12);

        mv.addObject("salersLevers", salersLevers);
        //战略合作伙伴
        List<SysOptionDefinition> partners = getSysOptionDefinitionList(SysOptionConstant.ID_303);

        mv.addObject("partners", partners);
        mv.addObject("userList", userList);
        mv.addObject("provinceList", provinceList);

        mv.addObject("traderCustomerVo", traderCustomerVo);
        mv.addObject("page", page);
        if (StrUtil.isNotEmpty(traderCustomerLevelGradeList)) {
            List<String> split = StrUtil.split(traderCustomerLevelGradeList, ",");
            mv.addObject("traderCustomerLevelGradeListStr", traderCustomerLevelGradeList);
            mv.addObject("traderCustomerLevelGradeList", split);
        }
        if (StrUtil.isNotEmpty(traderCustomerLevelGradeOriginal)) {
            List<String> split = StrUtil.split(traderCustomerLevelGradeOriginal, ",");
            mv.addObject("traderCustomerLevelGradeOriginalStr", traderCustomerLevelGradeOriginal);
            mv.addObject("traderCustomerLevelGradeOriginal", split);
        }

        // modify by franlin.wu for[耗材商城的客户同步，新增客户来源类型] begin
        // 1 erp客户列表
        if (ErpConst.ONE.equals(pageType)) {
            //客户分群信息
            List<Integer> traderIds = list.stream().map(item -> item.getTraderId()).collect(Collectors.toList());
            Map<Integer,RTraderGroupJTrader> traderGroupMap = getTraderGroup(traderIds);
            mv.addObject("traderGroupMap", traderGroupMap);
            mv.setViewName("trader/customer/index");
        }
        // 其他页面传的是初始化0/2 耗材商城客户列表
        else {
            mv.setViewName("trader/customer/hc/hc_trader_list");
        }
        // modify by franlin.wu for[耗材商城的客户同步，新增客户来源类型] end
        return mv;
    }

    /**
     * 查询客户分类类型树形
     *
     * @return R
     */
    @ResponseBody
    @RequestMapping(value = "/traderCustomerTypeTree",method = RequestMethod.GET)
    @NoNeedAccessAuthorization
    public R<List<TraderCustomerCategoryDto>> traderCustomerTypeTree(HttpServletRequest request) {
        List<TraderCustomerCategoryDto> traderCustomerCategoryDtos = traderCustomerService.getTraderCustomerTypeTree();
        return R.success(traderCustomerCategoryDtos);
    }

    /**
     * 功能描述: 限制改价名单
     */
    @ResponseBody
    @RequestMapping(value = "/limitPrice")
    public ModelAndView limitPrice(HttpServletRequest request, @RequestParam(required = false, value = "traderName") String traderName) {
        ModelAndView mv = new ModelAndView();
        // 获取发票信息
        List<TraderCustomerVo> traderCustomerList = traderCustomerService.getLimitPriceTraderName(traderName);
        mv.addObject("traderCustomerList", traderCustomerList);
        mv.addObject("traderName", EmptyUtils.isNotBlank(traderName) ? traderName : "");
        mv.setViewName("trader/customer/limit_price_traderName");
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "/batchLimitPriceInit")
    public ModelAndView batchLimitPriceInit(HttpServletRequest request) {
        return new ModelAndView("trader/customer/batchLimitPrice");
    }

    /*
     * 功能描述: 批量限制改价导入
     * 优化代码
     */
    @ResponseBody
    @RequestMapping("batchListPriceSave")
    public ResultInfo<?> batchListPriceSave(HttpServletRequest request, @RequestParam("lwfile") MultipartFile lwfile) {


        ResultInfo<?> resultInfo = new ResultInfo<>();

        try {

            List<TraderCustomerVo> traderList = dealwithFile(request, lwfile);

            // 保存更新
            traderCustomerService.batchUpdateListPrice(traderList);

            resultInfo.setCode(0);
            resultInfo.setMessage("批量导入成功");

        } catch (Exception e) {
            logger.error("batchUpload limitPrice file error", e);
            resultInfo.setMessage(e.getMessage());
        }
        return resultInfo;
    }


    /**
     * 处理文件
     *
     * @param request
     * @return
     * @throws Exception
     */
    private List<TraderCustomerVo> dealwithFile(HttpServletRequest request, MultipartFile lwfile) throws Exception {

        List<Row> rows = getRows(request, lwfile);
        if (CollectionUtils.isEmpty(rows)) {
            throw new Exception("上传文件不能为空");
        }

        PriceInfoUploadValidatorDto priceInfoUploadValidatorDto = new PriceInfoUploadValidatorDto();
        priceInfoUploadValidatorDto.setUser((User) request.getSession().getAttribute(ErpConst.CURR_USER));
        priceInfoUploadValidatorDto.setRows(rows);

        ValidatorChain validatorChain = ValidatorChainBuild.newBuild()
                .setCustomerNameValidator(this.customerNameValidator)
                .create();

        ValidatorResult validatorResult = validatorChain.validator(priceInfoUploadValidatorDto);

        //检验不通过返回结果
        if (!validatorResult.getResult()) {
            throw new Exception(validatorResult.getMessage());
        }

        return processRow(rows, priceInfoUploadValidatorDto);
    }

    /**
     * 获取行结果
     *
     * @param rows
     * @return
     */
    private List<TraderCustomerVo> processRow(List<Row> rows, PriceInfoUploadValidatorDto priceInfoUploadValidatorDto) {

        List<TraderCustomerVo> traders = new ArrayList<>();

        Map<String, Long> traderNameAndIdMap = priceInfoUploadValidatorDto.getTraderNameAndIdMap();

        rows.stream().forEach(row -> {

            TraderCustomerVo trader = new TraderCustomerVo();
            traders.add(trader);

            trader.setTraderName(row.getCell(0).getStringCellValue());
            trader.setTraderId(traderNameAndIdMap.get(trader.getTraderName()).intValue());
        });

        return traders;
    }


    /**
     * 解析行
     *
     * @param request
     * @param lwfile
     * @return
     * @throws Exception
     */
    private List<Row> getRows(HttpServletRequest request, MultipartFile lwfile) throws Exception {

        // 临时文件存放地址
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        // 临时文件存放地址
        String path = request.getSession().getServletContext().getRealPath("/upload/trader");
        FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);

        if (fileInfo.getCode() != 0) {
            throw new Exception("文件上传失败");
        }

        if (!"xls".equals(fileInfo.getPrefix()) && !"xlsx".equals(fileInfo.getPrefix())) {
            throw new Exception("文件格式不正确，请上传excel格式文件");
        }
        List<Row> rows;
        FileInputStream fileInputStream = null;
        Workbook workbook = null;
        try {
            // 获取excel路径
            fileInputStream = new FileInputStream(new File(fileInfo.getFilePath()));
            workbook = WorkbookFactory.create(fileInputStream);
            // 获取第一面sheet
            Sheet sheet = workbook.getSheetAt(0);
            // 起始行
            int startRowNum = sheet.getFirstRowNum() + 1;// 第一行标题
            int endRowNum = sheet.getLastRowNum();// 结束行

            rows = new ArrayList<>();
            for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {
                rows.add(sheet.getRow(rowNum));
            }
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (Exception e) {
                    logger.error("【getRows】处理异常",e);
                }
            }

            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    logger.error("【getRows】处理异常",e);
                }
            }
        }
        return rows;
    }

    @ResponseBody
    @RequestMapping(value = "/delPriceLimitTrader")
    public ResultInfo<?> delPriceLimitTrader(HttpServletRequest request, TraderCustomerVo traderCustomerVo,
                                             @RequestParam("traderCustomerIdArr") String traderCustomerIdArr) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        if (null != user) {
            traderCustomerVo.setCompanyId(user.getCompanyId());
            traderCustomerVo.setUpdater(user.getUserId());
            traderCustomerVo.setModTime(DateUtil.gainNowDate());
        }
        List<Integer> traderCustomerIdList = JSON.parseArray(traderCustomerIdArr, Integer.class);
        traderCustomerVo.setTraderList(traderCustomerIdList);
        return traderCustomerService.delPriceLimitTrader(traderCustomerVo);
    }

    private void setCouponFlag(List<TraderCustomerVo> list) {
        try {
            if(CollectionUtils.isEmpty(list)){return;}
            for (TraderCustomerVo traderCustomerVo : list) {
                QueryCouponCenterRequest queryCouponCenterRequest = new QueryCouponCenterRequest();
                queryCouponCenterRequest.setPlatformId(traderCustomerVo.getBelongPlatform());
                queryCouponCenterRequest.setTraderId(traderCustomerVo.getTraderId());
                MyCouponDto myCouponDto = baseCouponService.getCouponByTraderId(queryCouponCenterRequest);
                if(myCouponDto == null){
                    continue;
                }
                if(!CollectionUtils.isEmpty(myCouponDto.getAvailableCouponsList())
                        ||  !CollectionUtils.isEmpty(myCouponDto.getExpiredCouponsList())
                        ||  !CollectionUtils.isEmpty(myCouponDto.getUsedCouponsList())){
                    traderCustomerVo.setCouponFlag(1);
                }
            }
        }catch (Exception e){
            logger.error("setCouponFlag error",e);
        }
    }

    /**
     * <b>Description:</b><br> 判断当前是否有搜索条件
     *
     * @param traderCustomerVo
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年8月10日 下午4:34:06
     */
    private boolean isNUllTraderCustomerVo(TraderCustomerVo traderCustomerVo) {
        if (ObjectUtils.isEmpty(traderCustomerVo.getName()) && ObjectUtils.isEmpty(traderCustomerVo.getHomePurchasing())
                && ObjectUtils.isEmpty(traderCustomerVo.getCustomerType()) && ObjectUtils.isEmpty(traderCustomerVo.getCustomerProperty())
                && ObjectUtils.isEmpty(traderCustomerVo.getCooperate()) && ObjectUtils.isEmpty(traderCustomerVo.getQuote())
                && ObjectUtils.isEmpty(traderCustomerVo.getSearchMsg()) && ObjectUtils.isEmpty(traderCustomerVo.getCustomerLevel())
                && ObjectUtils.isEmpty(traderCustomerVo.getUserEvaluate()) && ObjectUtils.isEmpty(traderCustomerVo.getPartnerId())
                && ObjectUtils.isEmpty(traderCustomerVo.getContactWay()) && ObjectUtils.isEmpty(traderCustomerVo.getCustomerStatus())
                && ObjectUtils.isEmpty(traderCustomerVo.getWxStatus()) && ObjectUtils.isEmpty(traderCustomerVo.getQueryEndTime())
                && ObjectUtils.isEmpty(traderCustomerVo.getQueryStartTime()) && ObjectUtils.isEmpty(traderCustomerVo.getProvince())
                && ObjectUtils.isEmpty(traderCustomerVo.getCity()) && ObjectUtils.isEmpty(traderCustomerVo.getZone())
        ) {
            return true;
        }
        return false;
    }


    /**
     * <b>Description:</b><br>
     * 置顶
     *
     * @param id
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:34:37
     */
    @SuppressWarnings("rawtypes")
    @ResponseBody
    @RequestMapping(value = "/isStick")
    @SystemControllerLog(operationType = "edit", desc = "置顶/取消置顶客户")
    public ResultInfo isStick(TraderCustomer traderCustomer, HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        ResultInfo ri = traderCustomerService.isStick(traderCustomer, user);
        return ri;
    }

    /**
     * <b>Description:</b><br>
     * 初始化禁用页面
     *
     * @param request
     * @param id
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月22日 下午2:09:38
     */
    @ResponseBody
    @RequestMapping(value = "/initDisabledPage")
    public ModelAndView initDisabledPage(HttpServletRequest request, TraderCustomer traderCustomer) {
        ModelAndView mav = new ModelAndView("trader/customer/forbid");
        mav.addObject("traderCustomer", traderCustomer);
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 禁用客户
     *
     * @param id
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:34:37
     */
    @SuppressWarnings("rawtypes")
    @ResponseBody
    @RequestMapping(value = "/isDisabledCustomer")
    @SystemControllerLog(operationType = "edit", desc = "禁用/启用客户")
    public ResultInfo isDisabledCustomer(TraderCustomer traderCustomer, HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        ResultInfo ri = traderCustomerService.isDisabled(user, traderCustomer);
        if (ri == null) {
            ri = new ResultInfo<>();
            ri.setCode(-1);
            ri.setMessage("操作失败");
        }
        ri.setData(traderCustomer.getTraderId() + "," + traderCustomer.getTraderCustomerId());
        return ri;
    }

    /**
     * <b>Description:</b><br>
     * 禁用客户联系人
     *
     * @param id
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:34:37
     */
    @SuppressWarnings("rawtypes")
    @ResponseBody
    @RequestMapping(value = "/isDisabledContact")
    @SystemControllerLog(operationType = "edit", desc = "启用/禁用客户联系人")
    public ResultInfo isDisabledContact(TraderContact traderContact, HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        ResultInfo ri = traderCustomerService.isDisabledContact(traderContact, user);
        if (ri == null) {
            ri = new ResultInfo<>();
            ri.setCode(-1);
            ri.setMessage("操作失败");
        }
        return ri;
    }

    /**
     * <b>Description:</b><br>
     * 设置客户默认联系人
     *
     * @param id
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:34:37
     */
    @SuppressWarnings("all")
    @ResponseBody
    @RequestMapping(value = "/saveCustomerDefaultContact")
    @SystemControllerLog(operationType = "edit", desc = "设置客户默认联系人")
    public ResultInfo saveCustomerDefaultContact(TraderContact traderContact, HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ResultInfo ri = traderCustomerService.isDefaultContact(traderContact, user);
        if (ri != null && ri.getCode() == 0) {
            ri.setData(traderContact.getTraderId() + "," + request.getParameter("traderCustomerId"));
        }
        return ri;
    }

    /**
     * <b>Description:</b><br>
     * 禁用/启用客户地址
     *
     * @param id
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:34:37
     */
    @SuppressWarnings("rawtypes")
    @ResponseBody
    @RequestMapping(value = "/isDisabledAddress")
    @SystemControllerLog(operationType = "edit", desc = "禁用/启用客户地址")
    public ResultInfo isDisabledAddress(TraderAddress traderAddress, HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        ResultInfo ri = traderCustomerService.isDisabledAddress(traderAddress, user);
        return ri;
    }

    /**
     * <b>Description:</b><br>
     * 设置客户默认地址
     *
     * @param id
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月17日 上午10:34:37
     */
    @SuppressWarnings("rawtypes")
    @ResponseBody
    @RequestMapping(value = "/isDefaultAddress")
    @SystemControllerLog(operationType = "edit", desc = "设置客户默认地址")
    public ResultInfo isDefaultAddress(TraderAddress traderAddress, HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ResultInfo ri = traderCustomerService.isDefaultAddress(traderAddress, user);
        if (ri == null) {
            ri = new ResultInfo<>();
            ri.setCode(-1);
            ri.setMessage("操作失败");
        }
        if (traderAddress.getTraderType() == 1) {
            ri.setData(traderAddress.getTraderId() + "," + request.getParameter("traderCustomerId"));
        } else {
            ri.setData(traderAddress.getTraderId() + "," + request.getParameter("traderSupplierId"));
        }
        return ri;
    }

    /**
     * <b>Description:</b><br>
     * 新增客户
     *
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月10日 上午10:21:26
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/add")
    public ModelAndView add(String traderName,Integer optType,Integer isAiAssistant,Integer aiCommunicateRecordId) {
        ModelAndView mv = new ModelAndView();
         try {
             TraderInfoTyc tycInfo = new TraderInfoTyc();
             if (traderName != null && !"".equals(traderName)) {
                 logger.info("add调用天眼查API，公司名称：{}", traderName);
                 tycInfo = traderCustomerService.getTycInfo(2, traderName);
                 mv.addObject("tycInfo", tycInfo);
                 mv.addObject("traderName", traderName);
             }
             User user = (User) ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest()
                     .getSession().getAttribute(ErpConst.CURR_USER);
             Integer belongPlatformOfUser = userService.getBelongPlatformOfUser(user.getUserId(), user.getCompanyId());
             if (!ErpConstant.THREE.equals(belongPlatformOfUser)) {
                 belongPlatformOfUser = ErpConstant.ONE;
             }
             mv.addObject("belongPlatformOfUser", belongPlatformOfUser);
             // 地区
             List<Region> provinceList = regionService.getRegionByParentId(1);

             //查询非公集团的客户
             List<Trader> noPublicGroupList = traderGroupService.queryNoPublicGroupList();
             mv.addObject("provinceList", provinceList);
             mv.addObject("optType",optType);
             mv.addObject("noPublicGroupList",noPublicGroupList);
             mv.addObject("noPublicGroupList",noPublicGroupList);

             // ai助手
             mv.addObject("isAiAssistant",isAiAssistant);
             mv.addObject("aiCommunicateRecordId",aiCommunicateRecordId);

             mv.setViewName("trader/customer/add_customer");
            } catch (Exception e) {
            logger.error("新增客户 error",e);
         }
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 保存新增客户信息
     *
     * @param traderCustomer
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月15日 上午11:07:28
     */
    @ResponseBody
    @RequestMapping(value = "/saveadd")
    @SystemControllerLog(operationType = "add", desc = "保存新增客户信息")
    @EventTrackingAnnotation(eventTrackingEnum = EventTrackingEnum.BASE_INFO_NEW_CUSTOMER)
    public ModelAndView saveAdd(Trader trader, HttpServletRequest request, HttpSession session) {
        ModelAndView mv = new ModelAndView();
        TraderCustomer traderCustomer;
        try {
            trader.setTraderName(trader.getTraderName().trim());
            traderCustomer = traderCustomerService.saveCustomer(trader, request, session);
            if (null != traderCustomer) {
                mv.addObject("url", "./baseinfo.do?traderId=" + traderCustomer.getTraderId() + "&traderCustomerId="
                        + traderCustomer.getTraderCustomerId());
                return success(mv);
            } else {
                return fail(mv);
            }
        } catch (Exception e) {
            logger.error("trader customer saveadd:", e);
            return fail(mv);
        }
    }
    /**
     * <b>Description:</b><br>
     * 保存新增客户信息
     *(和saveadd方法只有回参不同)
     * @param traderCustomer
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月15日 上午11:07:28
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveadd2")
    @SystemControllerLog(operationType = "add", desc = "保存新增客户信息")
    @EventTrackingAnnotation(eventTrackingEnum = EventTrackingEnum.BASE_INFO_NEW_CUSTOMER)
    public ResultInfo saveAdd2(Trader trader, HttpServletRequest request, HttpSession session) {
        ResultInfo rs = new ResultInfo();
        TraderCustomer traderCustomer;
        try {
            trader.setTraderName(trader.getTraderName().trim());
            traderCustomer = traderCustomerService.saveCustomer(trader, request, session);
            rs.setCode(0);
            rs.setData(traderCustomer);
            rs.setMessage("保存成功");
            return rs;
        } catch (Exception e) {
            logger.error("trader customer saveadd2:", e);
        }
        return rs;
    }
    /**
     * <b>Description:</b><br>
     * 客户详情
     *
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月10日 上午11:04:33
     */
    @ResponseBody
    @RequestMapping(value = "/view")
    public ModelAndView view(HttpServletRequest request, TraderCustomer traderCustomer) {
        User user = getSessionUser(request);
        if(user==null){
            return new ModelAndView("redirect:/login.do");
        }
        if (StringUtils.isEmpty(traderCustomer) || null == traderCustomer.getTraderCustomerId()
                || traderCustomer.getTraderCustomerId() <= 0) {
            return pageNotFound(request);
        }
        ModelAndView mv = new ModelAndView();
        mv.addObject("traderCustomer", traderCustomer);
        mv.setViewName("trader/customer/view_customer");
        return mv;
    }

    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/authForJc")
    public RestfulResult authForJc(HttpServletRequest request) {
//去前台获取sign签名
        String url = jcServiceDomain + ErpConst.JC_NOLOGIN_GENERATESIGN_URL;
        com.alibaba.fastjson.TypeReference<RestfulResult<String>> typeReference =
                new com.alibaba.fastjson.TypeReference<RestfulResult<String>>(){};
        String sign ="";
        try{
            RestfulResult<?> result = HttpRestClientUtil.restPost(url,typeReference,null,null);
            if(result == null){
                logger.warn("获取集采登录签名失败");
                return RestfulResult.fail("-1","获取集采登录签名失败");
            }
            return result;
        }catch (Exception e){
            logger.error("获取集采登录签名失败",e);
        }
        return RestfulResult.fail("-1","获取集采登录签名失败");

    }


    /**
     * <b>Description:</b><br>
     * 基本信息
     *
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月10日 上午11:44:59
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/baseinfo")
    public ModelAndView baseinfo(TraderCustomer traderCustomer, HttpServletRequest request, String traderName) {
        User curr_user = getSessionUser(request);
        if(curr_user==null){
            return new ModelAndView("redirect:/login.do");
        }
        if ((null == traderCustomer.getTraderCustomerId() && null == traderCustomer.getTraderId())
                || ((null != traderCustomer.getTraderCustomerId() && traderCustomer.getTraderCustomerId() == 0)
                || (null != traderCustomer.getTraderId() && traderCustomer.getTraderId() == 0))) {
            return pageNotFound(request);
        }
        ModelAndView mv = new ModelAndView();


        // add by Randy.Xu 2021/3/26 17:37 .Desc: . begin
        // VDERP-5839 数据越权处理 只可以看到自己及下属的客户信息。
        if(authService.checkUserIsSale(curr_user)) {
            if (null == traderCustomer.getTraderId()) {
                Integer traderId = traderCustomerService.getTraderIdByTraderCustomId(traderCustomer.getTraderCustomerId());
                traderCustomer.setTraderId(traderId);
            }
            List<User> userList = authService.getUserListByorderId(traderCustomer.getTraderId(), authService.TRADER_CUSTOMER_TYPE);
            Boolean checkFlag = authService.existOrNot(curr_user, userList);
            if (checkFlag) {
                logger.info("销售越权操作:接口[trader/customer/baseinfo],行为[查看非自己及下属客户的信息],操作人{}",curr_user.getUsername());
            }
        }
        // add by Randy.Xu 2021/3/26 17:37 .Desc: . end

        mv.addObject("curr_user", curr_user);
        TraderCustomerVo traderBaseInfo = traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
        if (traderBaseInfo.getTraderCustomerId() == null&&traderBaseInfo.getTrader() == null) {
            mv.setViewName("trader/customer/view_baseinfo_warn");
            return mv;
        }
        TraderCustomerDto traderCustomerDto = traderCustomerApiService.getTraderCustomerInfoByTraderId(traderCustomer.getTraderId());
        String s = traderCustomerApiService.setShowInvalidReason(traderCustomerDto.getInvalidReason(), traderCustomerDto.getOtherReason());
        traderBaseInfo.setShowInvalidReason(s);

        boolean isDealer = Objects.nonNull(traderBaseInfo.getTraderCustomerCategoryId()) && traderBaseInfo.getTraderCustomerCategoryId().toString().equals("5");
        if (isDealer) {
            TraderDealerFrontDto.Principal principal = traderCustomerMarketingPrincipalApiService.stealthFrontData(traderBaseInfo.getTraderCustomerId());
            if (Objects.nonNull(principal)) {
                traderBaseInfo.getTrader().setEffectiveness(principal.getEffectiveness());
            } else {
                traderBaseInfo.getTrader().setEffectiveness(1);
            }
        }

        //审核人
        if (null != traderBaseInfo.getVerifyUsername()) {
            List<String> verifyUsernameList = Arrays.asList(traderBaseInfo.getVerifyUsername().split(","));
            traderBaseInfo.setVerifyUsernameList(verifyUsernameList);
        }
        // 地区
        if (null != traderBaseInfo.getTrader().getAreaId() && traderBaseInfo.getTrader().getAreaId() > 0) {
            String region = (String) regionService.getRegion(traderBaseInfo.getTrader().getAreaId(), 2);
            mv.addObject("region", region);
        }

        //仓库地址
        if (null != traderBaseInfo.getTrader().getWarehouseAreaId() && traderBaseInfo.getTrader().getWarehouseAreaId() > 0) {
            String warehouseRegion = (String) regionService.getRegion(traderBaseInfo.getTrader().getWarehouseAreaId(), 2);
            mv.addObject("warehouseRegion", warehouseRegion);
        }

        // 经营地区
        if (null != traderBaseInfo.getTraderCustomerBussinessAreas()) {
            for (TraderCustomerBussinessArea bussinessArea : traderBaseInfo.getTraderCustomerBussinessAreas()) {
                String region = (String) regionService.getRegion(bussinessArea.getAreaId(), 2);
                bussinessArea.setAreaStr(region);
            }
        }

        //是否分销
        List<TraderCustomerCategory> customerCategories = traderBaseInfo.getCustomerCategories();

        Boolean show_fenxiao = false;
        if (customerCategories != null && customerCategories.size() > 0) {
            for (TraderCustomerCategory c : customerCategories) {
                if (c.getTraderCustomerCategoryId() == 3
                        || c.getTraderCustomerCategoryId() == 5) {
                    show_fenxiao = true;
                }
            }
        }

        //当客户性质是分销时，展示中标信息
        if (traderBaseInfo.getCustomerNature() == 465){
            mv.addObject("bidingData",traderCustomerService.getBidingDataOfTraderCustomer(traderBaseInfo.getTraderCustomerId()));
        }
        TraderInfoTyc tycInfo = new TraderInfoTyc();
        if (traderName != null && !"".equals(traderName)) {
            logger.info("baseinfo调用天眼查API，公司名称：{}", traderName);
            tycInfo = traderCustomerService.getTycInfo(2, traderName);
            mv.addObject("tycInfo", tycInfo);
            mv.addObject("traderName", traderName);
        }
        TraderWorkareaDto data = traderWorkAreaApiService.getData(traderCustomer.getTraderId());
        mv.addObject("workArea", data);
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "traderCustomerVerify_" + traderBaseInfo.getTraderCustomerId());
        mv.addObject("taskInfo", historicInfo.get("taskInfo"));
        mv.addObject("startUser", historicInfo.get("startUser"));
        mv.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态
        mv.addObject("endStatus", historicInfo.get("endStatus"));
        mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        mv.addObject("commentMap", historicInfo.get("commentMap"));

        //客户名称审核记录
        Map<String, Object> historicInfoName = actionProcdefService.getHistoric(processEngine, "editTraderCustomerName_" + traderBaseInfo.getTraderId());
        mv.addObject("taskInfoName", historicInfoName.get("taskInfo"));
        mv.addObject("startUserName", historicInfoName.get("startUser"));
        mv.addObject("candidateUserMapName", historicInfoName.get("candidateUserMap"));
        // 最后审核状态
        mv.addObject("endStatusName", historicInfoName.get("endStatus"));
        mv.addObject("historicActivityInstanceName", historicInfoName.get("historicActivityInstance"));
        mv.addObject("commentMapName", historicInfoName.get("commentMap"));

        mv.addObject("traderCustomer", traderBaseInfo);
        mv.addObject("show_fenxiao", show_fenxiao);
        mv.addObject("method", "baseinfo");


//        mv.addObject("sign",sign);
        mv.addObject("jcWeb",jcWeb);

        //VDERP-5598 客户详情页展示子公司列表
        List<Trader> childrenTrader = traderMapper.getChildrenTrader(traderCustomer.getTraderId());
        mv.addObject("childrenTrader",childrenTrader);
        if (Objects.nonNull(traderBaseInfo.getTrader().getParentId())&&traderBaseInfo.getTrader().getParentId() > 0){
            Optional.ofNullable(traderMapper.getTraderByTraderId(traderBaseInfo.getTrader().getParentId()))
                    .ifPresent(item -> mv.addObject("parentTrader",item));
        }
        Task taskInfo = (Task) historicInfo.get("taskInfo");
        Task taskInfoName = (Task) historicInfoName.get("taskInfo");
        //当前审核人
        String verifyUsers = null;
        if (null != taskInfo) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfo);
            verifyUsers = (String) taskInfoVariables.get("verifyUsers");
        }
        mv.addObject("verifyUsers", verifyUsers);

        //当前审核人
        String verifyUsersName = null;
        Trader traderInfo = null;
        if (null != taskInfoName) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfoName);
            verifyUsersName = (String) taskInfoVariables.get("verifyUsers");
            traderInfo = (Trader) taskInfoVariables.get("trader");
        }
        //客户分群信息
        Map<Integer, RTraderGroupJTrader> traderGroupMap = getTraderGroup(Arrays.asList(traderBaseInfo.getTraderId()));
        mv.addObject("traderGroupMap", traderGroupMap);
        mv.addObject("traderInfo", traderInfo);
        mv.addObject("verifyUsersName", verifyUsersName);

        // 客户营销-终端属性
        List<TraderCustomerMarketingTerminalDto> traderCustomerMarketing = traderCustomerMarketingApiService.getTraderCustomerMarketing(traderCustomer.getTraderCustomerId());
        // 临床医疗 终端 客户营销属性
        if(traderBaseInfo.getCustomerType() == 427 && traderBaseInfo.getCustomerNature() == 466){
            mv.addObject("traderCustomerMarketing", CollUtil.getFirst(traderCustomerMarketing));
        }
        // 经销商-经营终端大类
        if (traderBaseInfo.getCustomerType() == 427 && traderBaseInfo.getCustomerNature() == 465) {
            mv.addObject("customerMarketingList", traderCustomerMarketing);
            TraderCustomerMarketingPrincipalDto marketingPrincipal = traderCustomerMarketingPrincipalApiService.getByTraderId(traderCustomer.getTraderId());
            mv.addObject("marketingPrincipal", marketingPrincipal);

            List<TraderCustomerTerminalDto> traderCustomerTerminalDtos = traderCustomerTerminalApiService.queryByTraderId(traderCustomer.getTraderId());
            mv.addObject("traderCustomerTerminalList", traderCustomerTerminalDtos);

        }

        mv.setViewName("trader/customer/view_baseinfo");
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 客户申请审核
     *
     * @param request
     * @param afterSales
     * @return
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年10月24日 下午18:42:13
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/editApplyValidCustomer")
    public ResultInfo<?> editApplyValidCustomer(HttpServletRequest request, TraderCustomer traderCustomer, String taskId, HttpSession session) {
        try {
            Map<String, Object> variableMap = new HashMap<String, Object>();
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            // 查询当前订单的一些状态
            TraderCustomerVo traderBaseInfo = traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
            //开始生成流程(如果没有taskId表示新流程需要生成)
            if (taskId.equals("0")) {
                variableMap.put("traderCustomerVo", traderBaseInfo);
                variableMap.put("currentAssinee", user.getUsername());
                variableMap.put("processDefinitionKey", "traderCustomerVerify");
                variableMap.put("businessKey", "traderCustomerVerify_" + traderBaseInfo.getTraderCustomerId());
                variableMap.put("relateTableKey", traderBaseInfo.getTraderCustomerId());
                variableMap.put("relateTable", "T_TRADER_CUSTOMER");
                actionProcdefService.createProcessInstance(request, "traderCustomerVerify", "traderCustomerVerify_" + traderBaseInfo.getTraderCustomerId(), variableMap);
            }
            //默认申请人通过
            //根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "traderCustomerVerify_" + traderBaseInfo.getTraderCustomerId());
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<String, Object>();
                //默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "", user.getUsername(), variables);
                //如果未结束添加审核对应主表的审核状态
                if (complementStatus != null && complementStatus.getData() != null && !complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                }
            }
            // add by Randy.Xu 2021/1/7 17:36 .Desc: VDERP-4927 质量报表添加供应商最近一次通过审核的时间. begin
                tmpTraderValidtimeExtService.updateTraderCustomerValidTIme(traderCustomer.getTraderCustomerId());
            // add by Randy.Xu 2021/1/7 17:36 .Desc: VDERP-4927 质量报表添加供应商最近一次通过审核的时间. end

            try {
                riskCheckService.checkTraderTodo(traderCustomer.getTraderId());
            } catch (Exception e) {
                logger.error("checkTraderTodo with editApplyValidCustomer error", e);
            }

            //更新贝登会员
            traderCustomerService.updateVedengMember();
            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("editApplyValidCustomer:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    /**
     * <b>Description:</b><br>
     * 管理信息
     *
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月10日 上午11:45:33
     */
    @ResponseBody
    @RequestMapping(value = "/manageinfo")
    public ModelAndView manageinfo(HttpServletRequest request, TraderCustomer traderCustomer, HttpSession session) {
        User curr_user = getSessionUser(request);
        if(curr_user==null){
            return new ModelAndView("redirect:/login.do");
        }
        if ((null == traderCustomer.getTraderCustomerId() && null == traderCustomer.getTraderId())
                || ((null != traderCustomer.getTraderCustomerId() && traderCustomer.getTraderCustomerId() == 0)
                || (null != traderCustomer.getTraderId() && traderCustomer.getTraderId() == 0))) {
            return pageNotFound(request);
        }
        ModelAndView mv = new ModelAndView();
        TraderCustomerVo traderManageInfo = traderCustomerService.getTraderCustomerManageInfo(traderCustomer, session);

//		// 审核记录
//		VerifiesRecord verifiesRecord = new VerifiesRecord();
//		verifiesRecord.setVerifiesType(SysOptionConstant.ID_135);
//		verifiesRecord.setRelatedId(traderCustomer.getTraderCustomerId());
//		List<VerifiesRecord> verifiesList = verifiesRecordService.getVerifiesRecord(verifiesRecord);

        mv.addObject("traderCustomer", traderManageInfo);
//		mv.addObject("verifiesList", verifiesList);
        mv.addObject("method", "manageinfo");
        mv.setViewName("trader/customer/view_manageinfo");
        return mv;
    }

    /**
     * 订单覆盖品类、品牌、区域
     * <b>Description:</b><br>
     *
     * @param request
     * @param traderOrderGoods
     * @return
     * @Note <b>Author:</b> Bill
     * <br><b>Date:</b> 2018年7月6日 下午2:19:16
     */
    @ResponseBody
    @RequestMapping(value = "/ordercoverinfo")
    public TraderCustomerVo getOrderCoverInfo(HttpServletRequest request, TraderOrderGoods traderOrderGoods) {

        return new TraderCustomerVo();
    }


    // 递归函数,拼接node的label属性
    public void printLabels(TraderCustomerMarketingNodeDto.Node node) {
        if(CollectionUtil.isEmpty(node.getChildren())){
            return;
        }
        for (TraderCustomerMarketingNodeDto.Node child : node.getChildren()) {
            child.setLabel(node.getLabel()+"-"+child.getLabel());
            printLabels(child);
        }
    }

    /**
     * 获取经营终端大类&类型下拉框数据
     */
    @ResponseBody
    @RequestMapping(value = "/getBusinessTerminal")
    @NoNeedAccessAuthorization
    public ResultInfo getBusinessTerminal(HttpServletRequest request, TraderCustomer traderCustomer) {
        ResultInfo resultInfo = new ResultInfo<>();
        ArrayList<TraderCustomerMarketingNodeDto.Node> nodes = new ArrayList<>();
        try {
            List<TraderCustomerMarketingNodeDto> traderCustomerMarketList = traderCustomerMarketingNodeApiService.getTraderCustomerMarketList(6, false);
            traderCustomerMarketList.forEach(x->{
                TraderCustomerMarketingNodeDto.Node threeCustomerType = x.getThreeCustomerType();
                threeCustomerType.setChildren(x.getInstitutionType());
                nodes.add(threeCustomerType);
            });
            nodes.forEach(x->{
                printLabels(x);
            });
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setData(nodes);
        } catch (Exception e) {
            logger.error("getBusinessTerminal:", e);
            resultInfo.setCode(-1);
            resultInfo.setMessage("操作失败");
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 编辑基本信息
     *
     * @param traderCustomer
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月17日 上午9:54:47
     */
    @ResponseBody
    @RequestMapping(value = "/editbaseinfo")
    public ModelAndView editBaseInfo(TraderCustomer traderCustomer,Integer isAiAssistant,Integer aiCommunicateRecordId) {
        ModelAndView mv = new ModelAndView();

        TraderCustomer traderBaseInfo = traderCustomerService.getTraderCustomerEditBaseInfo(traderCustomer);
        String parentTraderName="";
        if (Objects.nonNull(traderBaseInfo.getTrader().getParentId()) && traderBaseInfo.getTrader().getParentId() > 0) {
            //查询归属总公司名称
            parentTraderName = traderCustomerService.queryParentTraderName(traderBaseInfo.getTrader().getParentId());
        }
        mv.addObject("parentTraderName",parentTraderName);

        // 无效原因
        TraderCustomerDto traderCustomerDto = traderCustomerApiService.getTraderCustomerInfoByTraderId(traderBaseInfo.getTraderId());
        traderBaseInfo.setInvalidReason(traderCustomerDto.getInvalidReason());
        traderBaseInfo.setOtherReason(traderCustomerDto.getOtherReason());
        
        //查询非公集团的客户
        List<Trader> noPublicGroupList = traderGroupService.queryNoPublicGroupList();
        mv.addObject("noPublicGroupList",noPublicGroupList);
        // 地区
        List<Region> provinceList = regionService.getRegionByParentId(1);
        if (null != traderBaseInfo.getTrader().getAreaId() && traderBaseInfo.getTrader().getAreaId() > 0
                && null != traderBaseInfo.getTrader().getAreaIds() && traderBaseInfo.getTrader().getAreaIds() != "") {

            Integer areaId = traderBaseInfo.getTrader().getAreaId();
            List<Region> regionList = (List<Region>) regionService.getRegion(areaId, 1);

            if (!regionList.isEmpty()) {
                for (Region r : regionList) {
                    switch (r.getRegionType()) {
                        case 1:
                            List<Region> cityList = regionService.getRegionByParentId(r.getRegionId());
                            mv.addObject("provinceRegion", r);
                            mv.addObject("cityList", cityList);
                            break;
                        case 2:
                            List<Region> zoneList = regionService.getRegionByParentId(r.getRegionId());
                            mv.addObject("cityRegion", r);
                            mv.addObject("zoneList", zoneList);
                            break;
                        case 3:
                            mv.addObject("zoneRegion", r);
                            break;
                        default:
                            mv.addObject("countryRegion", r);
                            break;
                    }
                }
            }
        }

        TraderWorkareaDto data = traderWorkAreaApiService.getData(traderBaseInfo.getTraderId());
        mv.addObject("workProvinceList", provinceList);
        if (Objects.nonNull(data.getAreaId()) && data.getAreaId() > 0) {
            Region regionByRegionId = regionService.getRegionByRegionId(data.getAreaId());
            List<Region> zone = regionService.getRegionByParentId(regionByRegionId.getParentId());
            mv.addObject("workZoneList", zone);
            mv.addObject("workZone", regionByRegionId.getRegionId());

            Region city = regionService.getRegionByRegionId(regionByRegionId.getParentId());
            List<Region> cityList = regionService.getRegionByParentId(city.getParentId());
            mv.addObject("workCityList", cityList);
            mv.addObject("workCity", city.getRegionId());
            mv.addObject("workProvince", city.getParentId());
        }

        dealwithWarehouseAdress(provinceList,traderBaseInfo,mv);

        List<Integer> scopeList = new ArrayList<Integer>();
        scopeList.add(SysOptionConstant.SCOP_1016);// 员工人数
        scopeList.add(SysOptionConstant.SCOP_1017);// 年销售额
        scopeList.add(SysOptionConstant.SCOP_1018);// 销售模式
        scopeList.add(SysOptionConstant.SCOP_1013);// 所有制
        scopeList.add(SysOptionConstant.SCOP_1014);// 医学类型
        scopeList.add(SysOptionConstant.SCOP_1015);// 医院等级

        Map<Integer, List<SysOptionDefinition>> optionList = sysOptionDefinitionService.getOptionByScopeList(scopeList);

        // 客户类型
        List<TraderCustomerCategory> customerCategories = traderBaseInfo.getCustomerCategories();
        Boolean show_fenxiao = false;
        TraderCustomerAttributeCategory traderCustomerAttributeCategory = new TraderCustomerAttributeCategory();
        if (customerCategories != null && !customerCategories.isEmpty()) {
            for (TraderCustomerCategory cate : customerCategories) {
                if (cate.getTraderCustomerCategoryId().equals(3)) {
                    show_fenxiao = true;
                }
                traderCustomerAttributeCategory.setTraderCustomerCategoryId(cate.getTraderCustomerCategoryId());
            }
        }
        Map<Integer, List<TraderCustomerCategory>> customerCategoriesMap = traderBaseInfo.getCustomerCategoriesMap();

        TreeMap<Integer, List<TraderCustomerCategory>> newMap = new TreeMap<>();
        AtomicBoolean oldTerminal = new AtomicBoolean(false);
        if (customerCategoriesMap.keySet().size() > 0) {
            for (Integer id : customerCategoriesMap.keySet()) {
                List<TraderCustomerCategory> traderCustomerCategories = customerCategoriesMap.get(id);
                if (CollUtil.isNotEmpty(traderCustomerCategories)&& "6".equals(traderCustomerCategories.get(0).getParentId().toString())) {
                    oldTerminal.set(true);
                    break;
                }
                newMap.put(id, traderCustomerCategories);
            }
        }
        traderBaseInfo.setCustomerCategoriesMap(newMap);
        // 客户属性
        List<TraderCustomerAttributeCategory> attributes = traderCustomerService
                .getTraderCustomerAttributeByCategoryId(traderCustomerAttributeCategory);

        Collections.reverse(attributes);
        // 属性子集处理
        List<TraderCustomerAttributeCategory> attributeCategories = new ArrayList<>();
        for (TraderCustomerAttributeCategory attr : attributes) {
            attributeCategories.add(attr);
            List<TraderCustomerAttributeCategory> childAttribute = traderCustomerService
                    .getTraderCustomerChildAttribute(attr);
            if (null != childAttribute) {
                for (TraderCustomerAttributeCategory childAttr : childAttribute) {
                    attributeCategories.add(childAttr);
                }
            }
        }

        // 经营地区
        List<TraderCustomerBussinessArea> customerBussinessAreas = traderBaseInfo.getTraderCustomerBussinessAreas();

        Map<Integer, Map<String, String>> bussinessMap = new HashMap<>();
        if (customerBussinessAreas.size() > 0) {
            for (TraderCustomerBussinessArea bussinessArea : customerBussinessAreas) {
                Map<String, String> regionMap = new HashMap<>();
                String regionStr = (String) regionService.getRegion(bussinessArea.getAreaId(), 2);
                regionMap.put("areaIds", bussinessArea.getAreaIds());
                regionMap.put("areaStr", regionStr);

                bussinessMap.put(bussinessArea.getAreaId(), regionMap);
            }

        }

        mv.addObject("traderCustomer", traderBaseInfo);

        mv.addObject("provinceList", provinceList);

        mv.addObject("employees", optionList.get(SysOptionConstant.SCOP_1016));
        mv.addObject("annualSales", optionList.get(SysOptionConstant.SCOP_1017));
        mv.addObject("salesModel", optionList.get(SysOptionConstant.SCOP_1018));

        mv.addObject("ownership", optionList.get(SysOptionConstant.SCOP_1013));
        mv.addObject("medicalType", optionList.get(SysOptionConstant.SCOP_1014));
        mv.addObject("hospitalLevel", optionList.get(SysOptionConstant.SCOP_1015));

        mv.addObject("show_fenxiao", show_fenxiao);
        boolean isTerminal = Objects.nonNull(traderBaseInfo.getTraderCustomerCategoryId()) && traderBaseInfo.getTraderCustomerCategoryId().toString().equals("6") ||oldTerminal.get();
        boolean isDealer = Objects.nonNull(traderBaseInfo.getTraderCustomerCategoryId()) && traderBaseInfo.getTraderCustomerCategoryId().toString().equals("5");
        mv.addObject("attributes", isTerminal||isDealer?Collections.emptyList():attributeCategories);
        mv.addObject("isTerminal", isTerminal);
        mv.addObject("isDealer", isDealer);
        if (isTerminal) {
            List<TraderCustomerMarketingTerminalDto> traderCustomerMarketing = traderCustomerMarketingApiService.getTraderCustomerMarketing(traderBaseInfo.getTraderCustomerId());
            List<TraderCustomerMarketingNodeDto> traderCustomerMarketList = traderCustomerMarketingNodeApiService.getTraderCustomerMarketList(6, false);
            if (CollUtil.isNotEmpty(traderCustomerMarketing)) {
                TraderCustomerMarketingTerminalDto traderCustomerMarketingTerminalDto = traderCustomerMarketing.get(0);
                mv.addObject("traderCustomerMarketing", traderCustomerMarketingTerminalDto);

                if (ObjectUtil.isNotEmpty(traderCustomerMarketingTerminalDto.getTraderCustomerMarketingType())) {

                    if (CollUtil.isNotEmpty(traderCustomerMarketList)) {
                        Optional<TraderCustomerMarketingNodeDto> first = traderCustomerMarketList.stream()
                                .filter(x -> Objects.nonNull(x.getThreeCustomerType()) && Integer.valueOf(x.getThreeCustomerType().getLabel()).equals(traderCustomerMarketingTerminalDto.getTraderCustomerMarketingType()))
                                .findFirst();
                        if (first.isPresent()) {
                            TraderCustomerMarketingNodeDto traderCustomerMarketingNodeDto = first.get();
                            List<TraderCustomerMarketingNodeDto.Node> institutionType = traderCustomerMarketingNodeDto.getInstitutionType();
                            List<TraderCustomerMarketingNodeDto.Node> institutionLevel = traderCustomerMarketingNodeDto.getInstitutionLevel();
                            mv.addObject("institutionType", institutionType);
                            mv.addObject("institutionLevel", institutionLevel);
                            if (StringUtil.isNotEmpty(traderCustomerMarketingTerminalDto.getInstitutionType())&&CollUtil.isNotEmpty(institutionType)) {

                                Optional<TraderCustomerMarketingNodeDto.Node> first1 = institutionType.stream().filter(x -> x.getLabel().equals(traderCustomerMarketingTerminalDto.getInstitutionType())).findFirst();
                                if (first1.isPresent()) {
                                    List<TraderCustomerMarketingNodeDto.Node> children = first1.get().getChildren();
                                    mv.addObject("institutionTypeChild", children);
                                }

                            }
                        }

                    }
                }
            }


            if (CollUtil.isNotEmpty(traderCustomerMarketList)) {
                List<TraderCustomerMarketingNodeDto.Node> collect = traderCustomerMarketList.stream()
                        .filter(x->Objects.nonNull(x.getThreeCustomerType()))
                        .map(x -> x.getThreeCustomerType()).collect(Collectors.toList());
                mv.addObject("threeCustomerType", collect);
            }
        }
        if (isDealer) {
            TraderDealerFrontDto.Principal principal = traderCustomerMarketingPrincipalApiService.stealthFrontData(traderBaseInfo.getTraderCustomerId());
            if (Objects.nonNull(principal)) {
                traderBaseInfo.getTrader().setEffectiveness(principal.getEffectiveness());
            } else {
                traderBaseInfo.getTrader().setEffectiveness(1);
            }

        }


        mv.addObject("bussinessMap", bussinessMap);
        mv.addObject("method", "baseinfo");

        try {
            mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(traderBaseInfo)));
        } catch (Exception e) {
            logger.error("trader customer editbaseinfo:", e);
        }

        // ai助手
        mv.addObject("isAiAssistant",isAiAssistant);
        mv.addObject("aiCommunicateRecordId",aiCommunicateRecordId);

        mv.setViewName("trader/customer/edit_baseinfo");
        return mv;
    }

    private void dealwithWarehouseAdress(List<Region> provinceList,TraderCustomer traderBaseInfo,ModelAndView mv) {

        Integer warehouseAreaId = traderBaseInfo.getTrader().getWarehouseAreaId();
        String warehouseAreaIds = traderBaseInfo.getTrader().getWarehouseAreaIds();

        if(warehouseAreaId == null || warehouseAreaId <= 0){
            return;
        }

        if (StringUtils.isEmpty(warehouseAreaIds)) {
            return;
        }

        List<Region> regionList = (List<Region>) regionService.getRegion(warehouseAreaId, 1);

        if (CollectionUtils.isEmpty(regionList)) {
            return;
        }

        regionList.stream().forEach(
                region->{
                    switch (region.getRegionType()) {
                        case 1:
                            List<Region> cityList = regionService.getRegionByParentId(region.getRegionId());
                            mv.addObject("warehouseProvinceRegion", region);
                            mv.addObject("warehouseCityList", cityList);
                            break;
                        case 2:
                            List<Region> zoneList = regionService.getRegionByParentId(region.getRegionId());
                            mv.addObject("warehouseCityRegion", region);
                            mv.addObject("warehouseZoneList", zoneList);
                            break;
                        case 3:
                            mv.addObject("warehouseZoneRegion", region);
                            break;
                        default:
                            mv.addObject("warehouseCountryRegion", region);
                            break;
                    }
                }
        );
    }

    /**
     * <b>Description:</b><br>
     * 保存编辑客户
     *
     * @param trader
     * @param request
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月18日 上午8:42:50
     */
    @ResponseBody
    @RequestMapping(value = "/saveeditbaseinfo")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑客户信息")
    public ModelAndView saveEditBaseInfo(Trader trader, HttpServletRequest request, HttpSession session, String beforeParams, Integer isCheckAptitudeStatus) {
        ModelAndView mv = new ModelAndView();
        TraderCustomer customer;
        if (trader == null || trader.getTraderCustomer() == null || trader.getTraderCustomer().getTraderCustomerId() == null) {
            fail(mv);
        }

        try {
            if (isCheckAptitudeStatus != null && isCheckAptitudeStatus == 1) {
                ResultInfo resultInfo = getAptitudeStatus(trader.getTraderCustomer().getTraderCustomerId());
                Trader msgTrader=traderCustomerService.getTraderByCustomerId(trader.getTraderCustomer().getTraderCustomerId());
                Integer status = (Integer) resultInfo.getData();
                if (TraderConstants.APTITUDE_IN_CHECK.equals(status)) {
                    Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "customerAptitude_" + trader.getTraderCustomer().getTraderCustomerId());
                    TaskInfo taskInfo = (TaskInfo) historicInfo.get("taskInfo");
                    if(taskInfo!=null) {
                        ResultInfo resultUpdateTable = verifiesRecordService.saveVerifiesInfoForTrader(taskInfo.getId(), 3);
                        ResultInfo resultDeleteInstance = actionProcdefService.deleteProcessInstance(taskInfo.getId());
                        sendMsgIfAptitudeStatusChange(msgTrader.getTraderId(), -1, msgTrader.getBelongPlatform());
                    }
                } else if (TraderConstants.APTITUDE_PASSED.equals(status)) {
                    verifiesRecordService.deleteVerifiesInfoByRelateKey(trader.getTraderCustomer().getTraderCustomerId()
                            , TraderConstants.APTITUDE_CHECK_TABLE_STR);
                    sendMsgIfAptitudeStatusChange(msgTrader.getTraderId(),-1,msgTrader.getBelongPlatform());
                }
            }
            if (StringUtil.isNotBlank(trader.getTraderName())) {
                trader.setTraderName(trader.getTraderName().trim());
            }
            trader.setTraderName(trader.getTraderName().trim());
            Page page = getPageTag(request, 1, 10);
            Map<String, Object> map;
            // 搜索该客户已生效订单
            if (!trader.getTraderName().equals(trader.getTraderNameBefore())) {

                boolean checkByTraderNameHaveSaleOrder = saleOrderApiService.checkByTraderNameHaveSaleOrder(trader.getTraderNameBefore().trim());
                if (checkByTraderNameHaveSaleOrder) {
                    //如果有已经生效的订单并且名字有所变更
                    try {
                        Map<String, Object> variableMap = new HashMap<String, Object>();
                        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
                        //开始生成流程(如果没有taskId表示新流程需要生成)
                        variableMap.put("trader", trader);
                        variableMap.put("currentAssinee", user.getUsername());
                        variableMap.put("processDefinitionKey", "editTraderCustomerName");
                        variableMap.put("businessKey", "editTraderCustomerName_" + trader.getTraderId());
                        variableMap.put("relateTableKey", trader.getTraderId());
                        variableMap.put("relateTable", "T_TRADER");
                        actionProcdefService.createProcessInstance(request, "editTraderCustomerName", "editTraderCustomerName_" + trader.getTraderId(), variableMap);
                        //默认申请人通过
                        //根据BusinessKey获取生成的审核实例
                        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "editTraderCustomerName_" + trader.getTraderId());
                        if (historicInfo.get("endStatus") != "审核完成") {
                            Task taskInfo = (Task) historicInfo.get("taskInfo");
                            String taskId = taskInfo.getId();
                            Authentication.setAuthenticatedUserId(user.getUsername());
                            Map<String, Object> variables = new HashMap<String, Object>();
                            //默认审批通过
                            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "", user.getUsername(), variables);
                            //如果未结束添加审核对应主表的审核状态
                            if (!complementStatus.getData().equals("endEvent")) {
                                verifiesRecordService.saveVerifiesInfo(taskId, 0);
                            }
                        }
                        //return new ResultInfo(0, "操作成功");
                    } catch (Exception e) {
                        //return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
                    }
                    if (null != trader.getTraderNameBefore()) {
                        trader.setTraderName(trader.getTraderNameBefore().trim());
                    }
                }
            }
            customer = traderCustomerService.saveEditBaseInfo(trader, request, session);
            if (null != trader.getTraderCustomer().getTraderCustomerId()) {
                actionProcdefService.updateVerifyInfo("T_TRADER_CUSTOMER", trader.getTraderCustomer().getTraderCustomerId(), 3);
            }
            if (null != customer) {
                //检验风控
                riskCheckService.checkTraderTodo(trader.getTraderId());

                mv.addObject("url", "./baseinfo.do?traderCustomerId=" + customer.getTraderCustomerId());
                return success(mv);
            } else {
                return fail(mv);
            }
        } catch (Exception e) {
            logger.error("trader customer saveeditbaseinfo:", e);
            return fail(mv);
        }
    }

    /**
     * <b>Description:</b><br>
     * 编辑管理信息
     *
     * @param traderCustomer
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月17日 上午9:54:47
     */
    @ResponseBody
    @RequestMapping(value = "/editmanageinfo")
    public ModelAndView editManageInfo(TraderCustomer traderCustomer, HttpServletRequest request, HttpSession session) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        // 管理信息
        TraderCustomer traderManageInfo = traderCustomerService.getTraderCustomerManageInfo(traderCustomer, session);

        List<Integer> scopeList = new ArrayList<Integer>();
        scopeList.add(SysOptionConstant.SCOP_1009);// 客户来源
        scopeList.add(SysOptionConstant.SCOP_1010);// 首次询价方式
        scopeList.add(SysOptionConstant.SCOP_1012);// 销售评级
        scopeList.add(SysOptionConstant.SCOP_1033);// 战略合作伙伴

        Map<Integer, List<SysOptionDefinition>> optionList = sysOptionDefinitionService.getOptionByScopeList(scopeList);
        // 客户标签
        Tag tag = new Tag();
        tag.setTagType(SysOptionConstant.ID_30);
        tag.setIsRecommend(ErpConst.ONE);
        tag.setCompanyId(user.getCompanyId());

        Integer pageNo = 1;
        Integer pageSize = 10;

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

        mv.addObject("traderCustomer", traderManageInfo);
        mv.addObject("customerFrom", optionList.get(SysOptionConstant.SCOP_1009));
        mv.addObject("firstEnquiryType", optionList.get(SysOptionConstant.SCOP_1010));
        mv.addObject("userEvaluate", optionList.get(SysOptionConstant.SCOP_1012));
        mv.addObject("zlhz", optionList.get(SysOptionConstant.SCOP_1033));

        mv.addObject("tagList", (List<Tag>) tagMap.get("list"));
        mv.addObject("page", (Page) tagMap.get("page"));

        mv.addObject("method", "manageinfo");
        try {
            mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(traderManageInfo)));
        } catch (Exception e) {
            logger.error("trader customer editmanageinfo:", e);
        }
        mv.setViewName("trader/customer/edit_manageinfo");
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 保存编辑管理信息
     *
     * @param traderCustomer
     * @param request
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月17日 上午11:21:08
     */
    @ResponseBody
    @RequestMapping(value = "/saveeditmanageinfo")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑客户管理信息")
    public ModelAndView saveEditManageInfo(TraderCustomer traderCustomer, HttpServletRequest request,
                                           HttpSession session, String beforeParams) {
        ModelAndView mv = new ModelAndView();
        TraderCustomer customer;
        try {
            customer = traderCustomerService.saveEditManageInfo(traderCustomer, request, session);
            if (null != customer) {
                mv.addObject("url", "./manageinfo.do?traderCustomerId=" + customer.getTraderCustomerId());
                return success(mv);
            } else {
                return fail(mv);
            }
        } catch (Exception e) {
            logger.error("trader customer saveeditmanageinfo:", e);
            return fail(mv);
        }
    }

    /**
     * <b>Description:</b><br>
     * 客户分类
     *
     * @param request
     * @param traderCustomerCategory
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月11日 下午2:39:06
     */
    @ResponseBody
    @RequestMapping(value = "/gettradercustomercategory")
    public ResultInfo<TraderCustomerCategory> getTraderCustomerCategory(HttpServletRequest request,
                                                                        TraderCustomerCategory traderCustomerCategory) {
        ResultInfo<TraderCustomerCategory> resultInfo = new ResultInfo<TraderCustomerCategory>();
        List<TraderCustomerCategory> categoryList = traderCustomerService
                .getTraderCustomerCategoryByParentId(traderCustomerCategory);

        if (categoryList != null) {
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setListData(categoryList);
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 客户属性
     *
     * @param request
     * @param traderCustomerCategory
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月11日 下午2:44:35
     */
    @ResponseBody
    @RequestMapping(value = "/gettradercustomerattribute")
    public ResultInfo<TraderCustomerAttributeCategory> getTraderCustomerAttribute(HttpServletRequest request,
                                                                                  TraderCustomerAttributeCategory traderCustomerAttributeCategory) {
        ResultInfo<TraderCustomerAttributeCategory> resultInfo = new ResultInfo<TraderCustomerAttributeCategory>();
        List<TraderCustomerAttributeCategory> categoryList = traderCustomerService
                .getTraderCustomerAttributeByCategoryId(traderCustomerAttributeCategory);

        if (categoryList != null) {
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setListData(categoryList);
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 获取子集属性
     *
     * @param request
     * @param traderCustomerAttributeCategory
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月11日 下午5:21:12
     */
    @ResponseBody
    @RequestMapping(value = "/gettradercustomerchildattribute")
    public ResultInfo<TraderCustomerAttributeCategory> getTraderCustomerChildAttribute(HttpServletRequest request,
                                                                                       TraderCustomerAttributeCategory traderCustomerAttributeCategory) {
        ResultInfo<TraderCustomerAttributeCategory> resultInfo = new ResultInfo<TraderCustomerAttributeCategory>();
        List<TraderCustomerAttributeCategory> categoryList = traderCustomerService
                .getTraderCustomerChildAttribute(traderCustomerAttributeCategory);

        if (categoryList != null) {
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setListData(categoryList);
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 获取客户字典库属性（员工人数\年销售额\客户来源\首次询价方式\战略合作伙伴）
     *
     * @param request
     * @return
     * @throws IOException
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月12日 下午2:06:27
     */
    @ResponseBody
    @RequestMapping(value = "/gettradercustomeroption")
    @NoNeedAccessAuthorization
    public ResultInfo<?> getTraderCustomerOption(HttpServletRequest request) throws IOException {
        ResultInfo<?> resultInfo = new ResultInfo<>();
        List<Integer> scopeList = new ArrayList<Integer>();
        scopeList.add(SysOptionConstant.SCOP_1016);// 员工人数
        scopeList.add(SysOptionConstant.SCOP_1017);// 年销售额
        scopeList.add(SysOptionConstant.SCOP_1018);// 销售模式
        scopeList.add(SysOptionConstant.SCOP_1009);// 客户来源
        scopeList.add(SysOptionConstant.SCOP_1010);// 首次询价方式
        scopeList.add(SysOptionConstant.SCOP_1033);// 战略合作伙伴

        Map<Integer, List<SysOptionDefinition>> optionList = sysOptionDefinitionService.getOptionByScopeList(scopeList);

        if (optionList != null) {
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setParam(JsonUtils.translateToJson(optionList));
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 查询交易者
     *
     * @param request
     * @param trader
     * @return
     * @throws IOException
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月22日 上午10:29:31
     */
    @ResponseBody
    @RequestMapping(value = "/gettraderbytradername")
    public ResultInfo<Trader> getTraderByTraderName(HttpServletRequest request, Trader trader, HttpSession session)
            throws IOException {
        ResultInfo<Trader> resultInfo = new ResultInfo<>();
        Trader traderInfo = traderCustomerService.getTraderByTraderName(trader, session);
        if (null != traderInfo) {
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setData(traderInfo);
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 沟通记录
     *
     * @param request
     * @param communicateRecord
     * @param pageNo
     * @param pageSize
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月23日 上午9:12:06
     */
    @ResponseBody
    @RequestMapping(value = "/communicaterecord")
    public ModelAndView communicateRecord(HttpServletRequest request, CommunicateRecord communicateRecord,
                                          TraderCustomer traderCustomer, @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false) Integer pageSize, HttpSession session) {
        ModelAndView mv = new ModelAndView();
        Page page = getPageTag(request, pageNo, pageSize);
        communicateRecord.setTraderType(ErpConst.ONE);//客户类型
        if(communicateRecord.getTraderId()!=null&&communicateRecord.getTraderId()>0){
            List<CommunicateRecord> communicateRecordList = traderCustomerService.getCommunicateRecordListPageNew(communicateRecord, page);
            mv.addObject("communicateRecordList", communicateRecordList);
            TraderCustomer customerInfoByTraderCustomer = traderCustomerService.getCustomerInfoByTraderCustomer(traderCustomer);
            traderCustomer.setBelongPlatform(traderMapper.getTraderByTraderId(traderCustomer.getTraderId()).getBelongPlatform());
            mv.addObject("traderCustomer",traderCustomer);
            mv.addObject("customerInfoByTraderCustomer", customerInfoByTraderCustomer);
        }else{
            logger.error("traderid isnull "+request.getHeader("REFERER"));
        }
        mv.addObject("page", page);
        mv.addObject("method", "communicaterecord");
        mv.setViewName("trader/customer/communicate_record");
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 新增沟通
     *
     * @param traderCustomer
     * @param request
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月23日 上午11:35:17
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addcommunicate")
    public ModelAndView addCommunicate(TraderCustomer traderCustomer, HttpServletRequest request) {
        String pop = request.getParameter("pop");

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();

        TraderContact traderContact = new TraderContact();
        // 联系人
        traderContact.setTraderId(traderCustomer.getTraderId());
        traderContact.setIsEnable(ErpConst.ONE);
        traderContact.setTraderType(ErpConst.ONE);
        List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);

        List<Integer> scopeList = new ArrayList<Integer>();
        scopeList.add(SysOptionConstant.SCOP_1024);// 沟通目的
        scopeList.add(SysOptionConstant.SCOP_1023);// 沟通方式

        Map<Integer, List<SysOptionDefinition>> optionList = sysOptionDefinitionService.getOptionByScopeList(scopeList);

        // 客户标签
        Tag tag = new Tag();
        tag.setTagType(SysOptionConstant.ID_32);
        tag.setIsRecommend(ErpConst.ONE);
        tag.setCompanyId(user.getCompanyId());

        Integer pageNo = 1;
        Integer pageSize = 10;

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

        traderCustomer.setBelongPlatform(traderMapper.getTraderByTraderId(traderCustomer.getTraderId()).getBelongPlatform());

        mv.addObject("traderCustomer", traderCustomer);
        mv.addObject("traderBaseInfo", getTraderBaseInfoByTraderId(traderCustomer.getTraderId(), ErpConst.ONE));

        mv.addObject("contactList", contactList);

        mv.addObject("communicateGoal", optionList.get(SysOptionConstant.SCOP_1024));
        mv.addObject("communicateMode", optionList.get(SysOptionConstant.SCOP_1023));

        mv.addObject("tagList", (List<Tag>) tagMap.get("list"));
        mv.addObject("page", (Page) tagMap.get("page"));

        CommunicateRecord communicate = new CommunicateRecord();
        communicate.setBegintime(DateUtil.sysTimeMillis());
        communicate.setEndtime(DateUtil.sysTimeMillis() + 2 * 60 * 1000);
        mv.addObject("communicateRecord", communicate);

        mv.addObject("method", "communicaterecord");
        mv.setViewName("trader/customer/add_communicate");
        mv.addObject("pop",pop);
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 新增沟通
     *
     * @param communicateRecord
     * @param request
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月24日 下午2:36:53
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveaddcommunicate")
    @SystemControllerLog(operationType = "add", desc = "保存新增客户沟通")
    public ModelAndView saveAddCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request,
                                           HttpSession session) {
        String pop = request.getParameter("pop");
        ModelAndView mv = new ModelAndView();
        Boolean record;
        try {
            communicateRecord.setCommunicateType(SysOptionConstant.ID_242);
            communicateRecord.setRelatedId(communicateRecord.getTraderCustomerId());
            record = traderCustomerService.saveAddCommunicate(communicateRecord, request, session);
            if (record ) {
                if(org.apache.commons.lang3.StringUtils.equals("Y",pop)){//如果是弹框添加沟通记录，则不跳转，只提示成功
                    mv.addObject("refresh","");//refresh为空表示跳转到成功页后，不自动跳转刷新
                    mv.addObject("reloadParent","Y");
                }else{
                    mv.addObject("url", "./communicaterecord.do?traderId=" + communicateRecord.getTraderId()
                            + "&traderCustomerId=" + communicateRecord.getTraderCustomerId());
                }

                return success(mv);
            } else {
                return fail(mv);
            }
        } catch (Exception e) {
            logger.error("saveaddcommunicate:", e);
            return fail(mv);
        }
    }

    /**
     * <b>Description:</b><br>
     * 获取当前客户的联系人和地址
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 上午9:08:41
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "/getContactsAddress")
    public ModelAndView getContactsAddress(HttpServletRequest request, TraderCustomer traderCustomer) {
        ModelAndView mav = new ModelAndView("trader/customer/view_contactsaddress");
        TraderContactVo traderContactVo = new TraderContactVo();
        traderContactVo.setTraderId(traderCustomer.getTraderId());
        traderContactVo.setTraderType(ErpConst.ONE);
        traderCustomer.setBelongPlatform(traderMapper.getTraderByTraderId(traderCustomer.getTraderId()).getBelongPlatform());
        //List<TraderContactVo> list = traderCustomerService.getTraderContactList(traderCustomer.getTraderId());
        //List<TraderAddressVo> taList = traderCustomerService.getTraderAddressList(traderCustomer.getTraderId());
        TraderCustomer customerInfoByTraderCustomer = traderCustomerService.getCustomerInfoByTraderCustomer(traderCustomer);
        //List<MjxAccountAddress> mjxlist = traderCustomerService.getMjxAccountAddressList(traderContactVo);
        mav.addObject("customerInfoByTraderCustomer", customerInfoByTraderCustomer);
        //mav.addObject("mjxlist", mjxlist);
        //mav.addObject("contactList", list);
        //mav.addObject("addressList", taList);
        mav.addObject("traderCustomer", traderCustomer);
        mav.addObject("traderId", traderCustomer.getTraderId());
        //mav.addObject("method", "contactsaddress");
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 初始化新增联系人页面
     *
     * @param request
     * @param traderId
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 下午3:11:47
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/toAddContactPage")
    public ModelAndView toAddContactPage(TraderCustomer traderCustomer,Integer pageType) {
        ModelAndView mav = new ModelAndView("trader/customer/add_contact");
        if(traderCustomer.getTraderId()== null || traderCustomer.getTraderId().equals(0)){
            mav.addObject("message","请先完善客户信息");
            return fail(mav);
        }
        TraderCustomerDto byTraderId = traderCustomerApiService.getTraderCustomerInfoByTraderId(traderCustomer.getTraderId());
        traderCustomer.setCustomerNature(byTraderId.getCustomerNature());
        mav.addObject("pageType",pageType);
        mav.addObject("traderCustomer", traderCustomer);
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 初始化新增地址页面
     *
     * @param request
     * @param traderId
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 下午3:11:47
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/toAddAddressPage")
    public ModelAndView toAddAddressPage(HttpServletRequest request, Integer traderId) {
        ModelAndView mav = new ModelAndView("trader/customer/add_address");
        mav.addObject("traderId", traderId);
        // 省级地区
        List<Region> provinceList = regionService.getRegionByParentId(1);

        mav.addObject("provinceList", provinceList);

        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 初始化编辑地址页面
     *
     * @param request
     * @param traderId
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 下午3:11:47
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/toEditAddressPage")
    public ModelAndView toEditAddressPage(HttpServletRequest request, TraderAddress traderAddress)
            throws IOException {
        ModelAndView mav = new ModelAndView("trader/customer/add_address");
        traderAddress = traderCustomerService.getTraderAddress(traderAddress);
        User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        mav.addObject("currentUser", curr_user);
        mav.addObject("traderAddress", traderAddress);
        if (ObjectUtils.notEmpty(traderAddress.getAreaIds())) {
            if (traderAddress.getAreaIds().split(",").length == 3) {
                List<Region> list = regionService
                        .getRegionByParentId(Integer.valueOf(traderAddress.getAreaIds().split(",")[1]));
                mav.addObject("zoneList", list);
                List<Region> cys = regionService
                        .getRegionByParentId(Integer.valueOf(traderAddress.getAreaIds().split(",")[0]));
                mav.addObject("cityList", cys);
                mav.addObject("zone", Integer.valueOf(traderAddress.getAreaIds().split(",")[2]));
                mav.addObject("city", Integer.valueOf(traderAddress.getAreaIds().split(",")[1]));
            } else if (traderAddress.getAreaIds().split(",").length == 2) {
                List<Region> list = regionService
                        .getRegionByParentId(Integer.valueOf(traderAddress.getAreaIds().split(",")[0]));
                mav.addObject("cityList", list);
                mav.addObject("city", Integer.valueOf(traderAddress.getAreaIds().split(",")[1]));
            }
            mav.addObject("province", Integer.valueOf(traderAddress.getAreaIds().split(",")[0]));
        }

        // 省级地区
        List<Region> provinceList = regionService.getRegionByParentId(1);
        mav.addObject("provinceList", provinceList);

        mav.addObject("traderId", traderAddress.getTraderId());
        mav.addObject("method", "contactsaddress");
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(traderAddress)));
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 保存地址
     *
     * @param session
     * @param traderAddress
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月27日 上午11:09:54
     */
    //@FormToken(remove=true)
    @ResponseBody
    @RequestMapping(value = "/saveAddress")
    @SystemControllerLog(operationType = "add", desc = "保存新增客户地址")
    public ResultInfo saveAddress(HttpSession session, TraderAddress traderAddress, Integer province, Integer city,
                                  Integer zone, String beforeParams) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        if (zone != 0) {
            traderAddress.setAreaId(zone);
            traderAddress.setAreaIds(province + "," + city + "," + zone);
        } else if (zone == 0 && city != 0) {
            traderAddress.setAreaId(city);
            traderAddress.setAreaIds(province + "," + city);
        }
        ResultInfo res = traderCustomerService.saveTraderAddress(traderAddress, user);
        return res;
    }

    /**
     * <b>Description:</b><br>
     * 新增保存联系人
     *
     * @param request
     * @param traderContact
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 下午3:13:43
     */
    //@FormToken(remove=true)
    @SuppressWarnings("rawtypes")
    @ResponseBody
    @RequestMapping(value = "/addSaveContact")
    @SystemControllerLog(operationType = "add", desc = "新增保存客户联系人")
    public ResultInfo addSaveContact(HttpSession session, TraderCustomer traderCustomer, TraderContact traderContact) throws IOException {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Integer traderContactId = traderCustomerService.saveTraderContact(traderContact, user);
        if (traderContactId > 0) {
            traderCustomerService.saveTraderBusinessCard(traderContact.getBusinessCards(),user.getUserId(),traderContactId);
            return new ResultInfo(0, "操作成功", traderCustomer.getTraderCustomerId() + "," + traderCustomer.getTraderId() + "," + traderContactId
                    +","+traderContact.getName()+","+traderContact.getMobile()+","+traderContact.getTelephone());
        } else if (traderContactId == -1) {
            return new ResultInfo(-1, "该客户已存在相同联系人");
        } else {
            return new ResultInfo(1, "操作失败");
        }
    }

    /**
     * <b>Description:</b><br>
     * 编辑保存联系人
     *
     * @param request
     * @param traderContact
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 下午3:13:43
     */
    @SuppressWarnings("rawtypes")
    @ResponseBody
    @RequestMapping(value = "/editSaveContact")
    @SystemControllerLog(operationType = "edit", desc = "编辑保存客户联系人")
    public ModelAndView editSaveContact(HttpServletRequest request, @RequestParam(required = false, value = "xg") String[] sg,
                                        TraderContact traderContact, TraderCustomer traderCustomer, String beforeParams) throws IOException {
        ModelAndView mav = new ModelAndView();
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        if(null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
            List<User> userListByorderId = authService.getUserListByorderId(traderCustomer.getTraderId(), authService.TRADER_CUSTOMER_TYPE);
            if (authService.existOrNot(user, userListByorderId)) {
                logger.info("销售越权操作:接口[trader/customer/editSaveContact],行为[修改非自己及下属的客户联系人],操作人{}",user.getUsername());
            }
        }
        if (null != traderContact.getTraderContactId()) {
            //String[] sg = request.getParameterValues("xg");
            StringBuffer sb = new StringBuffer();
            if (null != sg && sg.length > 0) {
                for (String str : sg) {
                    sb.append(str).append(",");
                }
                traderContact.setCharacter(sb.toString());
            }
        }
        Integer traderContactId = traderCustomerService.saveTraderContact(traderContact, user);
        if (traderContactId > 0) {
            traderCustomerService.saveTraderBusinessCard(traderContact.getBusinessCards(),user.getUserId(),traderContact.getTraderContactId());
//            mav.addObject("url", "./getContactsAddress.do?traderId=" + traderContact.getTraderId() + "&&traderCustomerId=" +
//                    traderCustomer.getTraderCustomerId());
            mav.addObject("url", "/trader/customer/toEditContactPage.do?traderContactId="+traderContact.getTraderContactId()+"&traderId="+traderContact.getTraderId()+"&traderCustomerId="+traderCustomer.getTraderCustomerId());
            return success(mav);
        } else if (traderContactId == -1) {
            List<ObjectError> allErrors = new ArrayList<>();
            allErrors.add(new ObjectError("1", "该客户已存在相同的联系人"));
            request.setAttribute("allErrors", allErrors);
            return toEditContactPage(request, traderContact.getTraderContactId(), traderCustomer);
        } else {
            return fail(mav);
        }

    }

    /**
     * <b>Description:</b><br> 联系人详情页
     *
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月5日 下午2:40:05
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "/getContactInfo")
    public ModelAndView getContactInfo(TraderContact traderContact, TraderCustomer traderCustomer) {
        ModelAndView mav = new ModelAndView("trader/customer/view_contacts");
        Map<String, Object> map = traderCustomerService.viewTraderContact(traderContact);
        if (map.containsKey("contact")) {
            JSONObject json = JSONObject.fromObject(map.get("contact"));
            TraderContactVo tcv = (TraderContactVo) JSONObject.toBean(json, TraderContactVo.class);
            //从redis查询性格名称集合
            if (tcv != null && tcv.getCharacter() != null && !"".equals(tcv.getCharacter())) {
                String[] chars = tcv.getCharacter().split(",");
                StringBuffer sb = new StringBuffer();
                for (String c : chars) {
                    SysOptionDefinition sod = getSysOptionDefinition(Integer.valueOf(c));
                    sb = sb.append(sod.getTitle()).append("、");

                }
                tcv.setCharacterName(sb.toString());
            }

            List<Map<String, Object>> businessCardList = traderCustomerService.getTraderBusinessCardById(tcv.getTraderContactId());
            mav.addObject("api_http", api_http);
            mav.addObject("businessCardList", businessCardList);
            mav.addObject("traderContact", tcv);
        }
        if (map.containsKey("experience")) {
            List<TraderContactExperienceVo> tceList = (List<TraderContactExperienceVo>) map.get("experience");
            mav.addObject("experienceList", tceList);
        }
        mav.addObject("method", "contactsaddress");
        mav.addObject("traderCustomer", traderCustomer);
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 初始化编辑联系人页面
     *
     * @param request
     * @param traderId
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月23日 下午3:11:47
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "/toEditContactPage")
    public ModelAndView toEditContactPage(HttpServletRequest request, Integer traderContactId, TraderCustomer traderCustomer) throws IOException {
        ModelAndView mav = new ModelAndView("trader/customer/edit_contact");
        TraderContact tc = traderCustomerService.getTraderContactById(traderContactId);
        mav.addObject("traderContact", tc);
        List<SysOptionDefinition> xgList = getSysOptionDefinitionList(SysOptionConstant.ID_4);//性格
        List<SysOptionDefinition> xlList = getSysOptionDefinitionList(SysOptionConstant.ID_3);//学历
        //获取名片信息
        List<Map<String, Object>> traderCertificatesMaps = traderCustomerService.getTraderBusinessCardById(traderContactId);
        mav.addObject("traderCertificatesMaps", new org.json.JSONArray(traderCertificatesMaps).toString());
        mav.addObject("xgList", xgList);
        mav.addObject("xlList", xlList);
        mav.addObject("method", "contactsaddress");
        TraderCustomerDto byTraderId = traderCustomerApiService.getTraderCustomerInfoByTraderId(traderCustomer.getTraderId());
        traderCustomer.setCustomerNature(byTraderId.getCustomerNature());
        mav.addObject("traderCustomer", traderCustomer);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(tc)));
        if(Objects.nonNull(tc)){
            mav.addObject("isVedengMember", traderCustomerService.getIsVedengMemberByTraderMobile(tc.getMobile()));
        }
        return mav;
    }

    /**
     * <b>Description:</b><br> 跳转新增客户联系人的行业背景页面
     *
     * @param traderContact
     * @param traderContactExperience
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月6日 下午3:22:54
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addContactExperience")
    public ModelAndView addContactExperience(TraderContact traderContact, TraderCustomer traderCustomer) {
        ModelAndView mav = new ModelAndView("trader/customer/add_contactexperience");
        // 地区
        List<Region> provinceList = regionService.getRegionByParentId(1);
        mav.addObject("provinceList", provinceList);
        mav.addObject("traderContact", traderContact);
        mav.addObject("traderCustomer", traderCustomer);
        return mav;
    }

    /**
     * <b>Description:</b><br> 保存新增客户联系人的行业背景
     *
     * @param traderContactExperience
     * @param traderCustomer
     * @param request
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月10日 上午8:42:46
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveAddContactExperience")
    @SystemControllerLog(operationType = "add", desc = "保存新增客户联系人的行业背景")
    public ResultInfo saveAddContactExperience(TraderContactExperience traderContactExperience, TraderCustomer traderCustomer, String start, String end,
                                               @RequestParam(required = false, value = "bussinessAreaId") String[] bussinessAreaIds,
                                               @RequestParam(required = false, value = "bussinessAreaIds") String[] bussinessAreaIdsStr,
                                               @RequestParam(required = false, value = "bussinessBrandId") String[] bussinessBrandIds,
                                               HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        List<TraderContactExperienceBussinessArea> bussinessAreas = null;
        List<TraderContactExperienceBussinessBrand> bussinessBrands = null;
        // 客户经营区域
        if (null != bussinessAreaIds) {
            //String[] bussinessAreaIds = request.getParameterValues("bussinessAreaId");
            //String[] bussinessAreaIdsStr = request.getParameterValues("bussinessAreaIds");
            bussinessAreas = new ArrayList<>();
            for (Integer i = 0; i < bussinessAreaIds.length; i++) {
                TraderContactExperienceBussinessArea traderCustomerBussinessArea = new TraderContactExperienceBussinessArea();
                traderCustomerBussinessArea.setAreaId(Integer.parseInt(bussinessAreaIds[i]));
                traderCustomerBussinessArea.setAreaIds(bussinessAreaIdsStr[i]);
                bussinessAreas.add(traderCustomerBussinessArea);
            }
        }

        // 客户经营品牌
        if (null != bussinessBrandIds) {
            //String[] bussinessBrandIds = request.getParameterValues("bussinessBrandId");
            bussinessBrands = new ArrayList<>();
            for (String brandId : bussinessBrandIds) {
                TraderContactExperienceBussinessBrand traderCustomerBussinessBrand = new TraderContactExperienceBussinessBrand();
                traderCustomerBussinessBrand.setBrandId(Integer.parseInt(brandId));
                bussinessBrands.add(traderCustomerBussinessBrand);
            }
        }
        //String start=request.getParameter("start");
        //String end = request.getParameter("end");
        if (start != null && !"".equals(start)) {
            traderContactExperience.setBegintime(DateUtil.convertLong(start, "yyyy-MM"));
        }
        if (end != null && !"".equals(end)) {
            traderContactExperience.setEndtime(DateUtil.convertLong(end, "yyyy-MM"));
        }
        if (bussinessAreas == null && bussinessBrands == null && "".equals(traderContactExperience.getCompany())
                && "".equals(traderContactExperience.getPosition()) && traderContactExperience.getBegintime() == null && traderContactExperience.getEndtime() == null) {
            return new ResultInfo(2, "请至少填写/选择一项数据");
        }
        ResultInfo re = traderCustomerService.saveAddContactExperience(traderContactExperience, user, bussinessAreas, bussinessBrands);
        if (re.getCode() == 0) {
            return new ResultInfo(0, "操作成功", traderCustomer.getTraderCustomerId() + "," + traderCustomer.getTraderId() + "," + traderContactExperience.getTraderContactId());
        } else {
            return new ResultInfo(1, "操作失败");
        }
    }

    /**
     * <b>Description:</b><br> 保存编辑客户联系人的行业背景
     *
     * @param traderContactExperience
     * @param traderCustomer
     * @param request
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月10日 上午8:42:46
     */
    @ResponseBody
    @RequestMapping(value = "/saveEditContactExperience")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑客户联系人的行业背景")
    public ResultInfo saveEditContactExperience(TraderContactExperience traderContactExperience, TraderCustomer traderCustomer, String start, String end, String beforeParams,
                                                @RequestParam(required = false, value = "bussinessAreaId") String[] bussinessAreaIds,
                                                @RequestParam(required = false, value = "bussinessAreaIds") String[] bussinessAreaIdsStr,
                                                @RequestParam(required = false, value = "bussinessBrandId") String[] bussinessBrandIds, HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        List<TraderContactExperienceBussinessArea> bussinessAreas = null;
        List<TraderContactExperienceBussinessBrand> bussinessBrands = null;
        // 客户经营区域
        if (null != bussinessAreaIds) {
            //String[] bussinessAreaIds = request.getParameterValues("bussinessAreaId");
            //String[] bussinessAreaIdsStr = request.getParameterValues("bussinessAreaIds");
            bussinessAreas = new ArrayList<>();
            for (Integer i = 0; i < bussinessAreaIds.length; i++) {
                TraderContactExperienceBussinessArea traderCustomerBussinessArea = new TraderContactExperienceBussinessArea();
                traderCustomerBussinessArea.setAreaId(Integer.parseInt(bussinessAreaIds[i]));
                traderCustomerBussinessArea.setAreaIds(bussinessAreaIdsStr[i]);
                bussinessAreas.add(traderCustomerBussinessArea);
            }
        }

        // 客户经营品牌
        if (null != bussinessBrandIds) {
            //String[] bussinessBrandIds = request.getParameterValues("bussinessBrandId");
            bussinessBrands = new ArrayList<>();
            for (String brandId : bussinessBrandIds) {
                TraderContactExperienceBussinessBrand traderCustomerBussinessBrand = new TraderContactExperienceBussinessBrand();
                traderCustomerBussinessBrand.setBrandId(Integer.parseInt(brandId));
                bussinessBrands.add(traderCustomerBussinessBrand);
            }
        }
        //String start=request.getParameter("start");
        //String end = request.getParameter("end");
        if (start != null && !"".equals(start)) {
            traderContactExperience.setBegintime(DateUtil.convertLong(start, "yyyy-MM"));
        } else {
            traderContactExperience.setBegintime(Long.valueOf(0));
        }
        if (end != null && !"".equals(end)) {
            traderContactExperience.setEndtime(DateUtil.convertLong(end, "yyyy-MM"));
        } else {
            traderContactExperience.setEndtime(Long.valueOf(0));
        }
        if (bussinessAreas == null && bussinessBrands == null && "".equals(traderContactExperience.getCompany())
                && "".equals(traderContactExperience.getPosition()) && traderContactExperience.getBegintime() == null && traderContactExperience.getEndtime() == null) {
            return new ResultInfo(2, "请至少填写/选择一项数据");
        }
        ResultInfo re = traderCustomerService.saveAddContactExperience(traderContactExperience, user, bussinessAreas, bussinessBrands);
        if (re.getCode() == 0) {
            return new ResultInfo(0, "操作成功", traderCustomer.getTraderCustomerId() + "," + traderCustomer.getTraderId() + "," + traderContactExperience.getTraderContactId());
        } else {
            return new ResultInfo(1, "操作失败");
        }
    }

    /**
     * <b>Description:</b><br> 跳转编辑客户联系人的行业背景页面
     *
     * @param traderContact
     * @param traderContactExperience
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月6日 下午3:22:54
     */
    @ResponseBody
    @RequestMapping(value = "/editContactExperience")
    public ModelAndView editContactExperience(TraderContact traderContact, TraderCustomer traderCustomer, TraderContactExperience traderContactExperience) throws IOException {
        ModelAndView mav = new ModelAndView("trader/customer/edit_contactexperience");
        Map<String, Object> map = traderCustomerService.getTraderContactExperience(traderContactExperience);
        if (map.containsKey("traderContactExperience")) {
            JSONObject json = JSONObject.fromObject(map.get("traderContactExperience"));
            traderContactExperience = (TraderContactExperience) JSONObject.toBean(json, TraderContactExperience.class);
            mav.addObject("traderContactExperience", traderContactExperience);
        }
        //经营品牌
        if (map.containsKey("tcebbList")) {
            JSONArray jsonArray = JSONArray.fromObject(map.get("tcebbList"));
            List<TraderContactExperienceBussinessBrandVo> tcebbList =
                    (List<TraderContactExperienceBussinessBrandVo>) JSONArray.toCollection(jsonArray, TraderContactExperienceBussinessBrandVo.class);
            mav.addObject("tcebbList", tcebbList);
        }
        // 经营地区
        if (map.containsKey("tcebaList")) {
            JSONArray jsonArray = JSONArray.fromObject(map.get("tcebaList"));
            List<TraderContactExperienceBussinessAreaVo> tcebaList =
                    (List<TraderContactExperienceBussinessAreaVo>) JSONArray.toCollection(jsonArray, TraderContactExperienceBussinessAreaVo.class);
            mav.addObject("tcebaList", tcebaList);
        }
        // 地区
        List<Region> provinceList = regionService.getRegionByParentId(1);
        mav.addObject("provinceList", provinceList);
        mav.addObject("traderContact", traderContact);
        mav.addObject("traderCustomer", traderCustomer);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(map)));
        return mav;
    }

    /**
     * <b>Description:</b><br> 删除联系人背景
     *
     * @param traderContactExperience
     * @param traderCustomer
     * @param request
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月10日 下午2:58:15
     */

    @ResponseBody
    @RequestMapping(value = "/delContactExperience")
    @SystemControllerLog(operationType = "del", desc = "删除联系人行业背景")
    public ResultInfo delContactExperience(TraderContactExperience traderContactExperience, TraderCustomer traderCustomer, HttpServletRequest request) {
        ResultInfo re = traderCustomerService.delContactExperience(traderContactExperience);
        if (re != null && re.getCode() == 0) {
            return new ResultInfo(0, "操作成功", traderCustomer.getTraderCustomerId() + "," + traderCustomer.getTraderId() + "," + traderContactExperience.getTraderContactId());
        } else {
            return new ResultInfo(1, "操作失败");
        }

    }

    /**
     * <b>Description:</b><br>
     * 编辑沟通记录
     *
     * @param communicateRecord
     * @param request
     * @param session
     * @return
     * @throws IOException
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月24日 下午1:31:13
     */
    @ResponseBody
    @RequestMapping(value = "/editcommunicate")
    public ModelAndView editCommunicate(CommunicateRecord communicateRecord, TraderCustomer traderCustomer,
                                        HttpServletRequest request, HttpSession session) throws IOException {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        CommunicateRecord communicate = traderCustomerService.getCommunicate(communicateRecord);
        communicate.setTraderCustomerId(communicateRecord.getTraderCustomerId());
        communicate.setTraderId(communicateRecord.getTraderId());

        TraderContact traderContact = new TraderContact();
        // 联系人
        traderContact.setTraderId(communicateRecord.getTraderId());
        traderContact.setIsEnable(ErpConst.ONE);
        traderContact.setTraderType(ErpConst.ONE);
        List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);

        List<Integer> scopeList = new ArrayList<Integer>();
        scopeList.add(SysOptionConstant.SCOP_1024);// 沟通目的
        scopeList.add(SysOptionConstant.SCOP_1023);// 沟通方式

        Map<Integer, List<SysOptionDefinition>> optionList = sysOptionDefinitionService.getOptionByScopeList(scopeList);

        // 客户标签
        Tag tag = new Tag();
        tag.setTagType(SysOptionConstant.ID_32);
        tag.setIsRecommend(ErpConst.ONE);
        tag.setCompanyId(user.getCompanyId());

        Integer pageNo = 1;
        Integer pageSize = 10;

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

        mv.addObject("communicateRecord", communicate);

        mv.addObject("contactList", contactList);

        mv.addObject("traderBaseInfo", getTraderBaseInfoByTraderId(traderCustomer.getTraderId(), ErpConst.ONE));

        mv.addObject("communicateGoal", optionList.get(SysOptionConstant.SCOP_1024));
        mv.addObject("communicateMode", optionList.get(SysOptionConstant.SCOP_1023));

        mv.addObject("tagList", (List<Tag>) tagMap.get("list"));
        mv.addObject("page", (Page) tagMap.get("page"));
        mv.addObject("method", "communicaterecord");
        mv.addObject("traderCustomer", traderCustomer);
        mv.setViewName("trader/customer/edit_communicate");
        mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(communicate)));

        if(org.apache.commons.lang.StringUtils.isNotBlank(communicate.getCoidUri())){
            String voiceStatusGptSuccess = "9";//Gpt解析成功
            CommunicateVoiceTaskDto taskDto = communicateVoiceTaskApi.selectByCommunicateRecordIdAndSence(communicate.getCommunicateRecordId(),
                    AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode());

            mv.addObject("communicateTypeName",AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getName());

            if(taskDto != null && voiceStatusGptSuccess.equals(taskDto.getVoiceStatus()) ){
                List<VoiceFieldResultDto> voiceFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
                        communicate.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode(),
                        AiConstant.CODE_GROUP_SUMMARY);
                mv.addObject("voiceFieldList", voiceFieldList);

                List<VoiceFieldResultDto> voiceToDoFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
                        communicate.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode(),
                        AiConstant.CODE_GROUP_TODOTASK);
                mv.addObject("voiceToDoFieldList", voiceToDoFieldList);
            }else{
                String voiceStatusIgnore = "-1";//忽略的状态
                if(taskDto == null){
                    //如果解析任务不存在，即历史数据或忽略
                }else if(voiceStatusIgnore.equals(taskDto.getVoiceStatus())){
                    //忽略，则不做任务数据展示
                } else{  //非以上情况，即如果解析任务存在，但是在解析中，则展示AI解析中...
                    List<VoiceFieldResultDto> voiceFieldList = new ArrayList<>();
                    VoiceFieldResultDto tipVoiceField = new VoiceFieldResultDto();
                    tipVoiceField.setFieldName("提示");
                    tipVoiceField.setFieldResult("AI解析中...");
                    voiceFieldList.add(tipVoiceField);
                    mv.addObject("voiceFieldList", voiceFieldList);

                    List<VoiceFieldResultDto> voiceToDoFieldList = new ArrayList<>();
                    VoiceFieldResultDto tipVoiceToDoField = new VoiceFieldResultDto();
                    tipVoiceToDoField.setFieldName("提示");
                    tipVoiceToDoField.setFieldResult("AI解析中...");
                    voiceToDoFieldList.add(tipVoiceToDoField);
                    mv.addObject("voiceToDoFieldList", voiceToDoFieldList);
                }
            }
        }

        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 保存编辑沟通
     *
     * @param communicateRecord
     * @param request
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年5月24日 下午2:37:47
     */
    @ResponseBody
    @RequestMapping(value = "/saveeditcommunicate")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑沟通记录")
    public ModelAndView saveEditCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request, String beforeParams,
                                            HttpSession session) {
        ModelAndView mv = new ModelAndView();
        Boolean record;
        try {
            record = traderCustomerService.saveEditCommunicate(communicateRecord, request, session);
            if (record) {
                mv.addObject("url", "./communicaterecord.do?traderId=" + communicateRecord.getTraderId()
                        + "&traderCustomerId=" + communicateRecord.getTraderCustomerId());
                return success(mv);
            } else {
                return fail(mv);
            }
        } catch (Exception e) {
            logger.error("saveeditcommunicate:", e);
            return fail(mv);
        }
    }

    /**
     * <b>Description:</b><br>
     * 跳转到转移联系人页面
     *
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月25日 下午5:34:13
     */
    @ResponseBody
    @RequestMapping(value = "/toTransferContactPage")
    public ModelAndView toTransferContactPage(TraderContact traderContact, HttpServletRequest request)
            throws IOException {
        ModelAndView mav = new ModelAndView("trader/customer/transfer_contact");
        // get 方式提交转码
        String name = URLDecoder.decode(request.getParameter("name"), "UTF-8");
        String department = URLDecoder.decode(request.getParameter("department"), "UTF-8");
        String position = URLDecoder.decode(request.getParameter("position"), "UTF-8");
        traderContact.setName(name);
        traderContact.setDepartment(department);
        traderContact.setPosition(position);
        mav.addObject("traderContact", traderContact);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(traderContact)));
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 转移联系人页面搜索客户
     *
     * @param name
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月26日 下午1:23:07
     */
    @ResponseBody
    @RequestMapping(value = "/getCustomersByName")
    public ModelAndView getCustomersByName(TraderContact traderContact, String customerName, HttpServletRequest request,
                                           @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                           @RequestParam(required = false, defaultValue = "10") Integer pageSize) throws IOException {
        ModelAndView mav = new ModelAndView("trader/customer/transfer_contact");
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Page page = getPageTag(request, pageNo, 10);
        // 查询所有职位类型为310的员工
        List<Integer> positionType = new ArrayList<>();
        positionType.add(SysOptionConstant.ID_310);//销售
        List<User> userList = userService.getMyUserList(user, positionType, false);
        //List<Integer> traderList = userService.getTraderIdListByUserList(userList,ErpConst.ONE+"");
        TraderCustomerVo tcv = new TraderCustomerVo();
        tcv.setCompanyId(user.getCompanyId());
        tcv.setName(customerName);
        //tcv.setTraderList(traderList);
        Map<String, Object> map = traderCustomerService.getTraderCustomerVoPage(tcv, page, userList);
        List<TraderCustomerVo> list = (List<TraderCustomerVo>) map.get("list");
        page = (Page) map.get("page");
        mav.addObject("list", list);
        mav.addObject("page", page);
        mav.addObject("customerName", customerName);
        mav.addObject("traderContact", traderContact);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(traderContact)));
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 转移联系人
     *
     * @param traderContact
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月26日 下午5:38:55
     */
    @ResponseBody
    @RequestMapping(value = "/transferContact")
    @SystemControllerLog(operationType = "edit", desc = "转移联系人")
    public ResultInfo transferContact(TraderContact traderContact, HttpSession session, String beforeParams) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        ResultInfo res = traderCustomerService.transferContact(traderContact, user);
        return res;
    }


    /**
     * <b>Description:</b><br> 开始审核资质
     *
     * @param traderCustomer 客户信息
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:2019/9/5</b>
     */
    @Transactional
    public ResultInfo startCheckAptitude(HttpServletRequest request, TraderCustomerVo traderCustomer, String taskId) {
        try {
            Map<String, Object> variableMap = new HashMap<String, Object>();
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            // 查询当前订单的一些状态
            TraderCustomerVo traderBaseInfo = traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
            //开始生成流程(如果没有taskId表示新流程需要生成)
            if (StringUtil.isBlank(taskId) || taskId.equals("0")) {
//                variableMap.put("traderCustomerVo", traderBaseInfo);
                variableMap.put("currentAssinee", user.getUsername());
                variableMap.put("processDefinitionKey", "customerAptitudeVerify");
                variableMap.put("businessKey", "customerAptitude_" + traderBaseInfo.getTraderCustomerId());
                variableMap.put("relateTableKey", traderBaseInfo.getTraderCustomerId());
                variableMap.put("relateTable", "T_CUSTOMER_APTITUDE");
                actionProcdefService.createProcessInstance(request, "customerAptitudeVerify", "customerAptitude_" + traderBaseInfo.getTraderCustomerId(), variableMap);
            }
            //默认申请人通过
            //根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "customerAptitude_" + traderBaseInfo.getTraderCustomerId());
            if (StringUtil.isBlank(taskId) && historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                String newtaskId = taskInfo.getId();
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, newtaskId, "", user.getUsername(), variableMap);
                //如果未结束添加审核对应主表的审核状态
//                if (!complementStatus.getData().equals("endEvent")) {
//                    verifiesRecordService.saveVerifiesInfoForTrader(newtaskId, 0);
//                }
                if (traderBaseInfo.getTrader() != null
                        && TraderConstants.TRADER_BELONG_BEIDENG.equals(traderBaseInfo.getTrader().getBelongPlatform())) {
                    //客户属于贝登
//                    verifiesRecordService.saveVerifiesInfoForTrader(newtaskId, TraderConstants.CUSTOMER_FIRST_CHECK);
                    verifiesRecordService.saveVerifiesInfoForTraderAddApplyOrVerify(newtaskId, TraderConstants.CUSTOMER_FIRST_CHECK,user, ErpConst.ONE);
                    sendMessageWhenStartCheckAptitude(TraderConstants.BEDENG_CUSTOMER_OPERATE, traderBaseInfo);
                    sendMsgIfAptitudeStatusChange(traderBaseInfo.getTraderId(), TraderConstants.CUSTOMER_FIRST_CHECK, traderBaseInfo.getBelongPlatform());
                } else {
                    //客户不属于贝登
                    TaskService taskService = processEngine.getTaskService();
                    List<String> candidate = new ArrayList<>();

                    Map<String, Object> variables = new HashMap<String, Object>();
                    variables.put("pass", true);
                    Task autoTaskInfo = taskService.createTaskQuery().processInstanceBusinessKey("customerAptitude_" + traderBaseInfo.getTraderCustomerId()).singleResult();
                    if (autoTaskInfo == null) {
                        return new ResultInfo<>(-1, "流程此节点已操作完成，请确认");
                    }
                    candidate.add(TraderConstants.CUSTOMER_FIRST_CHECK_DEFAULT_CANDIDATE);
                    actionProcdefService.addCandidate(autoTaskInfo.getId(), candidate);
                    String comment = TraderConstants.FIRST_CHECK_AUTO_PASS_COMMENT;
                    complementStatus = actionProcdefService.complementTask(request, autoTaskInfo.getId(), comment,
                            "njadmin", variables);
//                    verifiesRecordService.saveVerifiesInfoForTrader(newtaskId, TraderConstants.CUSTOMER_SEAL_CHECK);
                    verifiesRecordService.saveVerifiesInfoForTraderAddApplyOrVerify(newtaskId, TraderConstants.CUSTOMER_SEAL_CHECK,user, ErpConst.ONE);
                    //公章自动审核
                    sealCheck(request, traderBaseInfo);
                    sendMsgIfAptitudeStatusChange(traderBaseInfo.getTraderId(), 0, traderBaseInfo.getBelongPlatform());
                }
            } else if (StringUtil.isNotBlank(taskId) && historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                if (taskInfo != null && TraderConstants.CUSTOMER_FIRST_CHECK_TITLE.equals(taskInfo.getName())) {
                    verifiesRecordService.saveVerifiesInfoForTrader(taskId, TraderConstants.CUSTOMER_FIRST_CHECK);
                } else if (taskInfo != null && TraderConstants.CUSTOMER_SEAL_CHECK_TITLE.equals(taskInfo.getName())) {
                    verifiesRecordService.saveVerifiesInfoForTrader(taskId, TraderConstants.CUSTOMER_SEAL_CHECK);
                } else {
                    verifiesRecordService.saveVerifiesInfoForTrader(taskId, TraderConstants.CUSTOMER_SECOND_CHECK);
                }
            }




            //更新贝登会员
//            traderCustomerService.updateVedengMember();
            return new ResultInfo(0, "操作成功");
        } catch (NullPointerException ex){
            logger.warn("editApplyValidCustomer:", ex);
            return new ResultInfo(-1, "任务完成操作失败：" + ex.getMessage());
        } catch (Exception e) {
            logger.error("editApplyValidCustomer:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }

    private void sealCheck(HttpServletRequest request,TraderCustomerVo traderBaseInfo) {
        //客户不属于贝登
        TaskService taskService = processEngine.getTaskService();
        List<String> candidate = new ArrayList<>();

        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("pass", true);
        Task autoTaskInfo = taskService.createTaskQuery().processInstanceBusinessKey("customerAptitude_" + traderBaseInfo.getTraderCustomerId()).singleResult();
        candidate.add(TraderConstants.CUSTOMER_FIRST_CHECK_DEFAULT_CANDIDATE);
        actionProcdefService.addCandidate(autoTaskInfo.getId(), candidate);
        String comment = TraderConstants.FIRST_CHECK_AUTO_PASS_COMMENT;
        ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, autoTaskInfo.getId(), comment,
                "njadmin", variables);
        verifiesRecordService.saveVerifiesInfoForTrader(autoTaskInfo.getId(), TraderConstants.CUSTOMER_SECOND_CHECK);
        sendMsgIfAptitudeStatusChange(traderBaseInfo.getTraderId(),0,traderBaseInfo.getBelongPlatform());
        sendMessageWhenStartCheckAptitude(TraderConstants.QUALITY_DEPART_ASSISTANT, traderBaseInfo);

    }

    private void sendMessageWhenStartCheckAptitude(String roleName, TraderCustomerVo traderBaseInfo) {
        List<Integer> userIds = new ArrayList<>();
        Page page = new Page(1, 2);
        RoleVo role = new RoleVo();
        role.setRoleName(roleName);
        List<Role> roles = roleService.queryListPage(role, page);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(roles)) {
            for (Role r : roles) {
                List<User> users = userService.getUserByRoleId(r.getRoleId());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(users)) {
                    for (User u : users) {
                        userIds.add(u.getUserId());
                    }
                }
            }
        }
        Map<String, String> map = new HashMap<>();
        map.put("traderName", traderBaseInfo.getName());
        String url = String.format(ErpConst.GET_APTITUDE_URL,traderBaseInfo.getTraderId(),traderBaseInfo.getTraderCustomerId()) ;

        WebAccount webAccount = new WebAccount();
        webAccount.setTraderId(traderBaseInfo.getTraderId());
        List<WebAccount> webAccountList = webAccountMapper.getWebAccountListByParam(webAccount);
        if (webAccountList != null && webAccountList.size() > 0) {
            MessageUtil.sendMessage(104, userIds, map, url);
        } else {
            MessageUtil.sendMessage(97, userIds, map, url);
        }

    }

    /**
     * <b>Description:</b><br> 完成审核
     *
     * @param traderCustomerVo 客户信息
     * @param passed           是否通过
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:2019/9/5</b>
     */
    @ResponseBody
    @RequestMapping(value = "/completeCheckAptitude")
    public ResultInfo completeAptitude(HttpServletRequest request, TraderCustomerVo traderCustomer, String comment, String taskId, boolean pass) {


        try {
            Map<String, Object> variableMap = new HashMap<String, Object>();
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            TraderCustomerVo traderBaseInfo = traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
            String businessKey="customerAptitude_" + traderBaseInfo.getTraderCustomerId();
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,businessKey);
            TaskInfo taskInfo = (TaskInfo) historicInfo.get("taskInfo");
            variableMap.put("pass", pass);
            variableMap.put("updater", user.getUserId());
            if (pass) {


                if (taskInfo != null && TraderConstants.CUSTOMER_SEAL_CHECK_TITLE.equals(taskInfo.getName())) {
//                    verifiesRecordService.saveVerifiesInfoForTrader(taskId, TraderConstants.CUSTOMER_SECOND_CHECK);
                    verifiesRecordService.saveVerifiesInfoForTraderAddApplyOrVerify(taskId, TraderConstants.CUSTOMER_SECOND_CHECK,user,ErpConst.ONE);
                    sendMessageWhenStartCheckAptitude(TraderConstants.QUALITY_DEPART_ASSISTANT, traderBaseInfo);
                    sendMsgIfAptitudeStatusChange(traderBaseInfo.getTraderId(),TraderConstants.CUSTOMER_SECOND_CHECK,traderBaseInfo.getBelongPlatform());
                    traderCustomerService.sendMessage2Mjx(traderBaseInfo);
                } else if (taskInfo != null && TraderConstants.CUSTOMER_FIRST_CHECK_TITLE.equals(taskInfo.getName())){
                    if (isNewActivitiInstance(traderBaseInfo.getTraderCustomerId(),traderCertificateCheckStartTime)){
//                        verifiesRecordService.saveVerifiesInfoForTrader(taskId, TraderConstants.CUSTOMER_SEAL_CHECK);
                        verifiesRecordService.saveVerifiesInfoForTraderAddApplyOrVerify(taskId, TraderConstants.CUSTOMER_SEAL_CHECK,user,ErpConst.ONE);
//                        //同步个人名片到联系人中
//                        traderCustomerService.synBusinessCars(traderBaseInfo.getTraderId());
                        sendMessageWhenStartCheckAptitude(TraderConstants.BEDENG_CUSTOMER_OPERATE, traderBaseInfo);
                        sendMsgIfAptitudeStatusChange(traderBaseInfo.getTraderId(),TraderConstants.CUSTOMER_SEAL_CHECK,traderBaseInfo.getBelongPlatform());
                        traderCustomerService.sendMessage2Mjx(traderBaseInfo);
                        //初审通过给贝登前台发送消息
                        sendMsg(traderBaseInfo.getTraderId(),1,traderBaseInfo.getBelongPlatform());
                    }else {
//                        verifiesRecordService.saveVerifiesInfoForTrader(taskId, TraderConstants.CUSTOMER_SECOND_CHECK);
                        verifiesRecordService.saveVerifiesInfoForTraderAddApplyOrVerify(taskId, TraderConstants.CUSTOMER_SECOND_CHECK,user,ErpConst.ONE);
                        if (isNewActivitiInstance(traderBaseInfo.getTraderCustomerId(),newTraderCertificateCheckStartTime)){
                            sendMessageWhenStartCheckAptitude(TraderConstants.QUALITY_DEPART_ASSISTANT, traderBaseInfo);
                        } else {
                            sendMessageWhenStartCheckAptitude(TraderConstants.VEDENG_YUNYING_ROLE, traderBaseInfo);
                        }
//                        //同步个人名片到联系人中
//                        traderCustomerService.synBusinessCars(traderBaseInfo.getTraderId());
                        sendMsgIfAptitudeStatusChange(traderBaseInfo.getTraderId(),TraderConstants.CUSTOMER_SECOND_CHECK,traderBaseInfo.getBelongPlatform());
                        traderCustomerService.sendMessage2Mjx(traderBaseInfo);
                        //初审通过给贝登前台发送消息
                        sendMsg(traderBaseInfo.getTraderId(),1,traderBaseInfo.getBelongPlatform());
                    }
                } else {
//                    verifiesRecordService.saveVerifiesInfoForTrader(taskId, 1);
                    verifiesRecordService.saveVerifiesInfoForTraderAddApplyOrVerify(taskId, 1,user,ErpConst.ONE);
                    Map<String, Object> variables = actionProcdefService.getVariablesMap(businessKey);
                    if (variables != null
                            && variables.get("currentAssinee") != null
                            && StringUtil.isNotBlank((String) variables.get("currentAssinee"))) {
                        User user1 = userService.getByUsername((String) variables.get("currentAssinee"), 1);
                        if (user1 != null && user1.getUserId() != null) {
                            sendMessageWhenCompleteCheck(user1.getUserId(), 110, traderBaseInfo);
                        }
                    }
                    sendMsgIfAptitudeStatusChange(traderBaseInfo.getTraderId(),1,traderBaseInfo.getBelongPlatform());
                }
            } else {
//                User user1 = traderCustomerService.getPersonalUser(traderBaseInfo.getTraderId());
                Map<String, Object> variables = actionProcdefService.getVariablesMap(businessKey);
                if (variables != null
                        && variables.get("currentAssinee") != null
                        && StringUtil.isNotBlank((String) variables.get("currentAssinee"))) {
                    User user1 = userService.getByUsername((String) variables.get("currentAssinee"), 1);
                    if (user1 != null && user1.getUserId() != null) {
                        sendMessageWhenCompleteCheck(user1.getUserId(), 98, traderBaseInfo);
                    }
                }
                sendMsgIfAptitudeStatusChange(traderBaseInfo.getTraderId(),2,traderBaseInfo.getBelongPlatform());
//                verifiesRecordService.saveVerifiesInfoForTrader(taskId, 2);
                verifiesRecordService.saveVerifiesInfoForTraderAddApplyOrVerify(taskId, 2,user,ErpConst.ONE);
            }
            if (historicInfo.get("endStatus") != "审核完成") {
                taskId = taskInfo.getId();
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variableMap);
            }
            //校验释放风控
            riskCheckService.checkTraderTodo(traderBaseInfo.getTraderId());

            return new ResultInfo(0, "操作成功");
        } catch (Exception ex) {
            return new ResultInfo(-1, "操作失败");
        }
    }

    /**
     * 判断是否为新的客户资质审核流程实例
     * @param traderCustomerId 客户id
     * @return 结果
     */
    private Boolean isNewActivitiInstance(Integer traderCustomerId,Long checkStartTime){
        List<HistoricProcessInstance> historicProcessInstance = new ArrayList<>();
        HistoryService historyService = processEngine.getHistoryService();
        historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceBusinessKey("customerAptitude_" + traderCustomerId).orderByProcessInstanceStartTime().asc().list();
        if (historicProcessInstance == null || historicProcessInstance.size() == 0){
            return true;
        } else {
            //获取最新的审核流程实例
            HistoricProcessInstance hi = historicProcessInstance.get(historicProcessInstance.size() - 1);
            List<HistoricActivityInstance> hia = historyService.createHistoricActivityInstanceQuery()
                    .processInstanceId(hi.getId()).orderByHistoricActivityInstanceStartTime().asc().list();
            if (hia.get(0).getEndTime().getTime() < checkStartTime){
                return false;
            } else {
                return true;
            }
        }
    }

    /**
     * 初审通过给贝登前台发送消息
     * @param traderId
     * @param status
     * @param belongPlatform
     */
    private void sendMsg(Integer traderId,Integer status,Integer belongPlatform){
        com.alibaba.fastjson.JSONObject json=new com.alibaba.fastjson.JSONObject();
        json.put("traderId",traderId);
        json.put("status",1);
        json.put("belongPlatform",belongPlatform);
        msgProducer.sendMsg(RabbitConfig.CUSTOMER_LINK_ACCOUNT_EXCHANGE,null,json.toJSONString());
    }

    @Autowired
    private OpMsgProducer opMsgProducer;
    private void sendMsgIfAptitudeStatusChange(Integer traderId,Integer status,Integer belongPlatform){
        com.alibaba.fastjson.JSONObject json=new com.alibaba.fastjson.JSONObject();
        json.put("traderId",traderId);
        json.put("status",status);
        json.put("belongPlatform",belongPlatform);
        Timer time=new Timer();
        time.schedule(new TimerTask() {
            @Override
            public void run() {
                opMsgProducer.sendMsg(RabbitConfig.TRADER_APTITUDE_STATUS_EXCHANGE,null,json.toJSONString());
            }
        },5000);

    }

    private void sendMessageWhenCompleteCheck(Integer userId, Integer messageTemplateId, TraderCustomerVo traderBaseInfo) {
        List<Integer> userIds = new ArrayList<>();
        userIds.add(userId);
        Map<String, String> map = new HashMap<>();
        map.put("traderName", traderBaseInfo.getName());
        String url = String.format(ErpConst.GET_APTITUDE_URL,traderBaseInfo.getTraderId(),traderBaseInfo.getTraderCustomerId()) ;
        MessageUtil.sendMessage(messageTemplateId, userIds, map, url);
    }

    /**
     * <b>Description:</b><br>
     * 获取财务与资质信息
     *
     * @param traderId
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月31日 上午10:08:19
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "/getFinanceAndAptitude")
    public ModelAndView getFinanceAndAptitude(HttpServletRequest request, TraderCustomerVo traderCustomer) {
        ModelAndView mav = new ModelAndView("trader/customer/view_financeAndAptitude");
        User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        mav.addObject("currentUser", curr_user);
        TraderCustomerVo traderBaseInfo = traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
        if (traderCustomer.getCustomerNature() == null && traderBaseInfo != null) {
            traderCustomer.setCustomerNature(traderBaseInfo.getCustomerNature());
        }
        if(traderCustomer.getTraderCustomerId()==null){
            traderCustomer.setTraderCustomerId(traderBaseInfo.getTraderCustomerId());
        }
        if(traderCustomer.getTraderId()==null){
            logger.error("getFinanceAndAptitude::traderId{}",traderCustomer.getTraderId());
        }
        mav.addObject("traderCustomer", traderBaseInfo);
        //Integer customerProperty = getCustomerCategory(traderCustomer.getTraderCustomerId());
        //mav.addObject("customerProperty", customerProperty);
        //traderCustomer.setCustomerProperty(customerProperty);
        TraderCertificateVo tc = new TraderCertificateVo();
        tc.setTraderId(traderCustomer.getTraderId());
        tc.setTraderType(ErpConst.ONE);
        if (traderCustomer.getCustomerNature() != null && traderCustomer.getCustomerNature() == 465) {
            tc.setCustomerType(2);
        } else {
            tc.setCustomerType(1);
        }
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "customerAptitude_" + traderCustomer.getTraderCustomerId());
        mav.addObject("taskInfo", historicInfo.get("taskInfo"));
        mav.addObject("startUser",historicInfo.get("startUser") != null ?
                getRealNameByUserName(historicInfo.get("startUser").toString()) : null);
        mav.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态
        mav.addObject("endStatus", historicInfo.get("endStatus"));

        List<HistoricActivityInstance> historic = setAssignRealNames(mav, historicInfo);
        mav.addObject("historicActivityInstance", historic);

        mav.addObject("commentMap", historicInfo.get("commentMap"));
        String verifyUsers = null;
        String verifyUsersFinance = null;
        if (null != historicInfo.get("taskInfo")) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap((Task) historicInfo.get("taskInfo"));
            verifyUsers = (String) taskInfoVariables.get("verifyUsers");
        }
        mav.addObject("verifyUsers", getVerifyUserRealNames(verifyUsers));
        Map<String, Object> map = traderCustomerService.getFinanceAndAptitudeByTraderId(tc, "all");


        CustomerAptitudeComment comment = traderCustomerService.getCustomerAptitudeCommentByTraderId(traderCustomer.getTraderId());
        if (comment != null && StringUtil.isNotBlank(comment.getComment())) {
            mav.addObject("comment", comment.getComment());
        }
        List<TraderCertificateVo> bussinessList = null;
        // 营业执照信息
        if (map.containsKey("business")) {
            bussinessList = (List<TraderCertificateVo>) map.get("business");
            mav.addObject("bussinessList", bussinessList);
        }
        // 税务登记信息
        TraderCertificateVo tax = null;
        if (map.containsKey("tax")) {
            JSONObject json = JSONObject.fromObject(map.get("tax"));
            tax = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
            mav.addObject("tax", tax);
        }
        // 组织机构信息
        TraderCertificateVo orga = null;
        if (map.containsKey("orga")) {
            JSONObject json = JSONObject.fromObject(map.get("orga"));
            orga = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
            mav.addObject("orga", orga);
        }
        // add by fralin.wu for[耗材商城的客户管理--代付款证明] at 2018-11-22 begin
        if (map.containsKey("pofPayList")) {
            mav.addObject("pofPayList", map.get("pofPayList"));
        }
        if (map.containsKey("trader")) {
            JSONObject json = JSONObject.fromObject(map.get("trader"));
            mav.addObject("trader", JSONObject.toBean(json, Trader.class));
        }
        // add by fralin.wu for[耗材商城的客户管理--代付款证明] at 2018-11-22 end

        if (traderCustomer.getCustomerNature() == 465) {
            // 二类医疗资质
            List<TraderCertificateVo> twoMedicalList = null;
            if (map.containsKey("twoMedical")) {
//				JSONObject json=JSONObject.fromObject(map.get("twoMedical"));
                twoMedicalList = (List<TraderCertificateVo>) map.get("twoMedical");
                mav.addObject("twoMedicalList", twoMedicalList);
            }
            // 三类医疗资质
            List<TraderCertificateVo> threeMedicalList = null;
            if (map.containsKey("threeMedical")) {
                try {
//				 JSONObject json=JSONObject.fromObject(map.get("threeMedical"));
                    threeMedicalList = (List<TraderCertificateVo>) map.get("threeMedical");
                    mav.addObject("threeMedical", threeMedicalList);
                } catch (Exception e) {
                    logger.error("threeMedical", e);
                }
            }
            List<TraderMedicalCategoryVo> two = null;
            if (map.containsKey("two")) {
                JSONArray jsonArray = JSONArray.fromObject(map.get("two"));
                two = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
                mav.addObject("two", two);
            }
            List<TraderMedicalCategoryVo> three = null;
            if (map.containsKey("three")) {
                JSONArray jsonArray = JSONArray.fromObject(map.get("three"));
                three = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
                mav.addObject("three", three);
            }
            List<TraderMedicalCategoryVo> newTwo = null;
            if (map.containsKey("newTwo")) {
                JSONArray jsonArray = JSONArray.fromObject(map.get("newTwo"));
                newTwo = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
                mav.addObject("newTwo", newTwo);
            }
            List<TraderMedicalCategoryVo> newThree = null;
            if (map.containsKey("newThree")) {
                JSONArray jsonArray = JSONArray.fromObject(map.get("newThree"));
                three = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
                mav.addObject("newThree", three);
            }
        } else if (traderCustomer.getCustomerNature() == 466) {
            // 医疗机构执业许可证
            List<TraderCertificateVo> practiceList = null;
            if (map.containsKey("practice")) {
                // JSONObject json=JSONObject.fromObject(map.get("practice"));
                practiceList = (List<TraderCertificateVo>) map.get("practice");
                mav.addObject("practiceList", practiceList);
            }
            // 中医诊所备案证
            TraderCertificateVo clinic = null;
            if (map.containsKey("clinic")) {
                JSONObject json = JSONObject.fromObject(map.get("clinic"));
                clinic = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
                mav.addObject("clinic", clinic);
            }
            // 动物诊疗许可证
            TraderCertificateVo animal = null;
            if (map.containsKey("animal")) {
                JSONObject json = JSONObject.fromObject(map.get("animal"));
                animal = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
                mav.addObject("animal", animal);
            }
            // 其他
            List<TraderCertificateVo> otherList = null;
            if (map.containsKey("others")) {
                otherList = (List<TraderCertificateVo>) map.get("others");
                mav.addObject("others", otherList);
            }
        }
        // 财务信息
        TraderFinanceVo tf = null;
        if (map.containsKey("finance")) {
            JSONObject json = JSONObject.fromObject(map.get("finance"));
            tf = (TraderFinanceVo) JSONObject.toBean(json, TraderFinanceVo.class);
            /*if (tf != null && ObjectUtils.notEmpty(tf.getAverageTaxpayerUri())) {
                tf.setAverageTaxpayerDomain(domain);
            }*/
            if (tf != null && tf.getTraderFinanceId() != null) {
                Map<String, Object> historicInfoFinance = actionProcdefService.getHistoric(processEngine, "TraderFinanceCheck_" + tf.getTraderFinanceId());
                mav.addObject("financeTaskInfo", historicInfoFinance.get("taskInfo"));
                mav.addObject("financeStartUser", historicInfoFinance.get("startUser"));
                mav.addObject("financeCandidateUserMap", historicInfoFinance.get("candidateUserMap"));
                // 最后审核状态
                mav.addObject("financeEndStatus", historicInfoFinance.get("endStatus"));
                mav.addObject("financeHistoricActivityInstance", historicInfoFinance.get("historicActivityInstance"));
                mav.addObject("financeCommentMap", historicInfoFinance.get("commentMap"));
                if (null != historicInfoFinance.get("taskInfo")) {
                    Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap((Task) historicInfoFinance.get("taskInfo"));
                    verifyUsersFinance = (String) taskInfoVariables.get("verifyUsers");
                }
                mav.addObject("verifyUsersFinance", verifyUsersFinance);
            }
            mav.addObject("finance", tf);
        }

        //获取账期 客户的各个类型账期的简要信息
        Integer companyId = ErpConst.NJ_COMPANY_ID;




        Integer traderCustomerId = traderCustomer.getTraderCustomerId();
        List<CustomerBillPeriodSummaryInfoDto> billInfoList = new ArrayList<>();
        if (null!=traderCustomerId){
            billInfoList = customerBillPeriodService.getCustomerBillPeriodSummaryInfo(companyId,Long.valueOf(traderCustomerId));
        }

        if (!CollectionUtils.isEmpty(billInfoList)){
            //正式账期
            List<CustomerBillPeriodSummaryInfoDto> billInfoListFormal = billInfoList.stream().filter(s->Objects.nonNull(s)).filter(t->CustomerBillPeriodTypeEnum.OFFICIAL.getCode().equals(t.getBillPeriodType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(billInfoListFormal)){
                mav.addObject("billInfoListFormal", billInfoListFormal.get(0));
            }
            //临时账期
            List<CustomerBillPeriodSummaryInfoDto> billInfoListTemporary = billInfoList.stream().filter(s->Objects.nonNull(s)).filter(t->CustomerBillPeriodTypeEnum.TEMPORARY.getCode().equals(t.getBillPeriodType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(billInfoListTemporary)){
                mav.addObject("billInfoListTemporary", billInfoListTemporary.get(0));
            }
            //订单账期
            List<CustomerBillPeriodSummaryInfoDto> billInfoListOrder = billInfoList.stream().filter(s->Objects.nonNull(s)).filter(t->CustomerBillPeriodTypeEnum.ORDER.getCode().equals(t.getBillPeriodType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(billInfoListOrder)){
                mav.addObject("billInfoListOrder", billInfoListOrder.get(0));
            }
        }


        //帐期列表
        if (map.containsKey("billList")) {
            JSONArray jsonArray = JSONArray.fromObject(map.get("billList"));
            List<TraderAccountPeriodApply> billList = (List<TraderAccountPeriodApply>) JSONArray.toCollection(jsonArray, TraderAccountPeriodApply.class);
            mav.addObject("billList", billList);
        }

        mav.addObject("isBelongBdOperator",isBelongBdOperator(curr_user));
        User user = traderCustomerService.getPersonalUser(traderCustomer.getTraderId());
        if (isTraderBelongToUser(user, curr_user)) {
            mav.addObject("isbelong", true);
            mav.addObject("isFinanceBelong", true);
        } else if (traderBaseInfo.getSource() != null && traderBaseInfo.getSource() == 1 && curr_user.getUsername().equals("haocai.vedeng")) {
            mav.addObject("isFinanceBelong", true);
        } else {
            mav.addObject("isbelong", false);
            mav.addObject("isFinanceBelong", false);
        }

        mav.addObject("isCustomeroperation",isCustomerOperation(curr_user,traderBaseInfo));



        TraderCustomerVo customerInfoByTraderCustomer = traderCustomerService.getCustomerInfoByTraderCustomer(traderCustomer);
        mav.addObject("customerInfoByTraderCustomer", customerInfoByTraderCustomer);

        mav.addObject("method", "financeandaptitude");
        return mav;
    }

    private boolean isCustomerOperation(User curr_user, TraderCustomerVo traderBaseInfo) {

        List<Integer> userIds=roleService.getUserIdByRoleName("贝登商城客户运营",1);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(userIds) || curr_user==null ||traderBaseInfo==null ||traderBaseInfo.getBelongPlatform()==null){
            return false;
        }
        if (userIds.contains(curr_user.getUserId()) && traderBaseInfo.getBelongPlatform()==1){
            return true;
        }else {
            return false;
        }
    }


    /**
     * <b>Description:</b>判断当前用户角色是否是贝登商城运营人员<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/6/28
     */
    private boolean isBelongBdOperator(User currUser){
        List<Integer> userIds = new ArrayList<>();
        Page page = new Page(1, 2);
        RoleVo role = new RoleVo();
        role.setRoleName(TraderConstants.BEDENG_OPERATE_ROLE_NAME);
        List<Role> roles = roleService.queryListPage(role, page);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(roles)) {
            for (Role r : roles) {
                List<User> users = userService.getUserByRoleId(r.getRoleId());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(users)) {
                    for (User u : users) {
                        if(currUser.getUserId().equals(u.getUserId())){
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
    private boolean isTraderBelongToUser(User user, User currentUser) {
        //查询该销售的领导
        if (user == null) {
            return false;
        }
        List<User> leaders = userService.getLeadersByParentId(user.getParentId(), SysOptionConstant.ID_310);

        if (CollectionUtils.isEmpty(leaders)) {
            leaders = new ArrayList<>();
        }
        leaders.add(user);
        for (User u : leaders) {
            if (u != null
                    && u.getUserId() != null
                    && u.getUserId().equals(currentUser.getUserId())) {
                return true;
            }
        }
        return false;
    }

    /**
     * <b>Description:</b><br> 获取交易者的账期分页信息
     *
     * @param request
     * @param traderCustomer
     * @param pageNo
     * @param pageSize
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年11月10日 下午4:09:21
     */
    @ResponseBody
    @RequestMapping(value = "/getAmountBillPage")
    @NoNeedAccessAuthorization
    public ModelAndView getAmountBillPage(HttpServletRequest request, TraderVo traderVo,
                                          @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false) Integer pageSize) {
        ModelAndView mav = new ModelAndView("trader/customer/amount_bill_page");
        Page page = getPageTag(request, pageNo, 10);
        Map<String, Object> map = traderCustomerService.getAmountBillListPage(traderVo, page);
        //帐期记录
        if (map.containsKey("list")) {
            List<TraderAmountBillVo> billList = (List<TraderAmountBillVo>) map.get("list");
            mav.addObject("billList", billList);
        }
        if (map.containsKey("page")) {
            page = (Page) map.get("page");
            mav.addObject("page", page);
        }
        mav.addObject("trader", traderVo);
        return mav;
    }

    /**
     * 获取交易者的账期分页信息
     * @param request
     * @param traderVo
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getAmountBillPageApply")
    @NoNeedAccessAuthorization
    public ModelAndView getAmountBillPageApply(HttpServletRequest request, TraderVo traderVo,
                                          @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false) Integer pageSize) {
        ModelAndView mav = new ModelAndView("trader/customer/amount_bill_page_apply");
        Page page = getPageTag(request, pageNo, 10);
        //历史申请
        List<CustomerBillPeriodApply> customerBillPeriodApplyList = customerBillPeriodApplyService.getCustomerBillPeriodApplyListByPage(traderVo.getTraderCustomerId(), page);
        //申请人
        if (!CollectionUtils.isEmpty(customerBillPeriodApplyList)){
            List<Integer> creator = customerBillPeriodApplyList.stream().map(CustomerBillPeriodApply::getCreator).distinct().collect(Collectors.toList());
            List<User> creatorName = traderCustomerService.getCreatorNameByCreator(creator);
            mav.addObject("creatorName",creatorName);
        }

        mav.addObject("customerBillPeriodApplyList",customerBillPeriodApplyList);
        mav.addObject("page",page);
        mav.addObject("traderCustomer",traderVo);
        return mav;
    }

    /**
     * <b>Description:</b><br> 获取交易者的交易流水分页信息
     *
     * @param request
     * @param traderCustomer
     * @param pageNo
     * @param pageSize
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年11月10日 下午4:09:21
     */
    @ResponseBody
    @RequestMapping(value = "/getCapitalBillPage")
    public ModelAndView getCapitaltBillPage(HttpServletRequest request, TraderVo traderVo,
                                            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                            @RequestParam(required = false) Integer pageSize) {
        ModelAndView mav = new ModelAndView("trader/customer/capital_bill_page");
        Page page = getPageTag(request, pageNo, 10);
        Map<String, Object> map = traderCustomerService.getCapitalBillListPage(traderVo, page);
        //资金流水
        if (map.containsKey("list")) {
            JSONArray jsonArray = JSONArray.fromObject(map.get("list"));
            List<CapitalBill> capitalBillList = (List<CapitalBill>) JSONArray.toCollection(jsonArray, CapitalBill.class);
            mav.addObject("capitalBill", capitalBillList);
        }
        if (map.containsKey("page")) {
            page = (Page) map.get("page");
            mav.addObject("page", page);
        }
        mav.addObject("trader", traderVo);
        return mav;
    }


    /**
     * <b>Description:</b><br>
     * 初始化编辑资质页面
     *
     * @param traderId
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月31日 下午3:51:00
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "/editAptitude")
    @FormToken(save = true)
    public ModelAndView editAptitude(HttpServletRequest request, TraderCustomerVo traderCustomer) throws IOException {
        ModelAndView mav = new ModelAndView("trader/customer/edit_aptitude");

        TraderCertificateVo traderCertificate = new TraderCertificateVo();
        traderCertificate.setTraderId(traderCustomer.getTraderId());
        traderCertificate.setTraderType(ErpConst.ONE);
        TraderCustomerVo traderBaseInfo = traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
        if(traderBaseInfo!=null&&traderBaseInfo.getTrader()!=null){
            mav.addObject("belongPlatform",traderBaseInfo.getTrader().getBelongPlatform());
        }else{
            mav.addObject("belongPlatform",5);
        }
        mav.addObject("traderCustomer", traderBaseInfo);
        User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        mav.addObject("currentUser", curr_user);
        //Integer customerProperty=getCustomerCategory(traderCustomer.getTraderCustomerId());
        //mav.addObject("customerProperty", customerProperty);
        //traderCustomer.setCustomerProperty(customerProperty);
        CustomerAptitudeComment comment = traderCustomerService.getCustomerAptitudeCommentByTraderId(traderCustomer.getTraderId());
        if (comment != null && StringUtil.isNotBlank(comment.getComment())) {
            mav.addObject("comment", comment.getComment());
        }
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "customerAptitude_" + traderCustomer.getTraderCustomerId());
        mav.addObject("taskInfo", historicInfo.get("taskInfo"));
        mav.addObject("startUser", historicInfo.get("startUser"));
        mav.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态
        mav.addObject("endStatus", historicInfo.get("endStatus"));
        mav.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        mav.addObject("commentMap", historicInfo.get("commentMap"));
        Page page = Page.newBuilder(null, null, null);
        if (traderCustomer.getCustomerNature() == 465) {
            traderCertificate.setCustomerType(2);
        } else {
            traderCertificate.setCustomerType(1);
        }
        if(traderCustomer.getTraderId()==null){
            logger.error("editAptitude::traderId{}",traderCustomer.getTraderId());
        }
        Map<String, Object> map = traderCustomerService.getFinanceAndAptitudeByTraderId(traderCertificate, "zz");
        StringBuffer sb = new StringBuffer();
        TraderCertificateVo tc = null;
        // 营业执照信息
        List<TraderCertificateVo> bussinessList = null;
        if (map.containsKey("business")) {
            bussinessList = (List<TraderCertificateVo>) map.get("business");
            mav.addObject("bussinessList", bussinessList);
            sb = sb.append(JsonUtils.translateToJson(bussinessList));
        }
        // 税务登记信息
        if (map.containsKey("tax")) {
            JSONObject json = JSONObject.fromObject(map.get("tax"));
            tc = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
            mav.addObject("tax", tc);
            sb = sb.append(JsonUtils.translateToJson(tc));
        }
        // 组织机构信息
        if (map.containsKey("orga")) {
            JSONObject json = JSONObject.fromObject(map.get("orga"));
            tc = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
            mav.addObject("orga", tc);
            sb = sb.append(JsonUtils.translateToJson(tc));
        }
        if (traderCustomer.getCustomerNature() == 465) {
            // 二类医疗资质
            List<TraderCertificateVo> twoMedicalList = null;
            if (map.containsKey("twoMedical")) {
//				JSONObject json=JSONObject.fromObject(map.get("twoMedical"));
                twoMedicalList = (List<TraderCertificateVo>) map.get("twoMedical");
                mav.addObject("twoMedicalList", twoMedicalList);
                if (MapUtils.isNotEmpty(map)) {
//                    List<LinkedHashMap> twoListQ = (List<LinkedHashMap>) map.get("twoMedical");
                    if (!CollectionUtils.isEmpty(twoMedicalList)) {
                        mav.addObject("qualification", twoMedicalList.get(0).getMedicalQualification());
                    }
                }

                sb = sb.append(JsonUtils.translateToJson(twoMedicalList));
            }
            // 三类医疗资质
            List<TraderCertificateVo> threeMedical = null;
            if (map.containsKey("threeMedical")) {
//				JSONObject json=JSONObject.fromObject(map.get("threeMedical"));
                threeMedical = (List<TraderCertificateVo>) map.get("threeMedical");
                mav.addObject("threeMedical", threeMedical);
                sb = sb.append(JsonUtils.translateToJson(threeMedical));
            }
            List<TraderMedicalCategoryVo> two = null;
            if (map.containsKey("two")) {
                JSONArray jsonArray = JSONArray.fromObject(map.get("two"));
                two = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
                mav.addObject("two", two);
                sb = sb.append(JsonUtils.translateToJson(two));
            }
            List<TraderMedicalCategoryVo> three = null;
            if (map.containsKey("three")) {
                JSONArray jsonArray = JSONArray.fromObject(map.get("three"));
                three = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
                mav.addObject("three", three);
                sb = sb.append(JsonUtils.translateToJson(three));
            }
            List<TraderMedicalCategoryVo> newTwo = null;
            if (map.containsKey("newTwo")) {
                JSONArray jsonArray1 = JSONArray.fromObject(map.get("newTwo"));
                newTwo = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray1, TraderMedicalCategoryVo.class);
                mav.addObject("newTwo", newTwo);
                sb = sb.append(JsonUtils.translateToJson(three));
            }
            List<TraderMedicalCategoryVo> newThree = null;
            if (map.containsKey("newThree")) {
                JSONArray jsonArray = JSONArray.fromObject(map.get("newThree"));
                newThree = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
                mav.addObject("newThree", newThree);
                sb = sb.append(JsonUtils.translateToJson(three));
            }
        } else if (traderCustomer.getCustomerNature() == 466) {
            // 医疗机构执业许可证
            List<TraderCertificateVo> practiceList = null;
            if (map.containsKey("practice")) {
                practiceList = (List<TraderCertificateVo>) map.get("practice");
                mav.addObject("practiceList", practiceList);
                sb = sb.append(JsonUtils.translateToJson(practiceList));
            }
            //中医诊所备案证
            if (map.containsKey("clinic")) {
                JSONObject json =  JSONObject.fromObject(map.get("clinic"));
                tc = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
                mav.addObject("clinic", tc);
                sb = sb.append(JsonUtils.translateToJson(tc));
            }
            //动物诊疗许可证
            if (map.containsKey("animal")) {
                JSONObject json =  JSONObject.fromObject(map.get("animal"));
                tc = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
                mav.addObject("animal", tc);
                sb = sb.append(JsonUtils.translateToJson(tc));
            }
            //其他
            List<TraderCertificateVo> others = null;
            if (map.containsKey("others")) {
                others = (List<TraderCertificateVo>) map.get("others");
                mav.addObject("others", others);
                sb = sb.append(JsonUtils.translateToJson(others));
            }
        }
        User user = traderCustomerService.getPersonalUser(traderCustomer.getTraderId());
        if (isTraderBelongToUser(user, curr_user)) {
            mav.addObject("isbelong", true);
        } else {
            mav.addObject("isbelong", false);
        }

        mav.addObject("isCustomeroperation",isCustomerOperation(curr_user,traderBaseInfo));
        // 医疗类别
        List<SysOptionDefinition> medicalTypes = getSysOptionDefinitionList(SysOptionConstant.ID_20);
        List<SysOptionDefinition> newMedicalTypes = getSysOptionDefinitionList(SysOptionConstant.ID_1024);
        mav.addObject("medicalTypes", medicalTypes);
        mav.addObject("newMedicalTypes", newMedicalTypes);
        // 医疗类别级别
        List<SysOptionDefinition> medicalTypLevels = getSysOptionDefinitionList(SysOptionConstant.ID_21);

        mav.addObject("medicalTypLevels", medicalTypLevels);
        mav.addObject("domain", ossUrl);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(sb.toString())));
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 获取医疗类别
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月2日 下午5:41:14
     */
    @ResponseBody
    @RequestMapping(value = "/getMedicalTypeByAjax")
    public ResultInfo getMedicalTypeByAjax() {
        // 医疗类别
        List<SysOptionDefinition> medicalTypes = getSysOptionDefinitionList(SysOptionConstant.ID_20);

        // 医疗类别级别
        List<SysOptionDefinition> medicalTypLevels = getSysOptionDefinitionList(SysOptionConstant.ID_21);
        ResultInfo res = new ResultInfo<>(1, "操作成功");
        res.setData(medicalTypes);
        res.setListData(medicalTypLevels);
        return res;
    }

    /**
     * <b>Description:</b><br>
     * 保存客户资质
     *
     * @return
     * @throws CloneNotSupportedException
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月5日 下午1:08:16
     */
    @ResponseBody
    @RequestMapping(value = "saveAptitude")
    @SystemControllerLog(operationType = "edit", desc = "保存客户资质")
    @FormToken(remove = true)
//    @Transactional
    public ModelAndView saveAptitude(HttpServletRequest request, TraderVo traderVo, TraderCustomerVo traderCustomer, String beforeParams, String taskId) throws CloneNotSupportedException {
        List<TraderCertificateVo> list = new ArrayList<>();
        String traderId = request.getParameter("traderId");
        String threeInOne = request.getParameter("threeInOne");
        String medicalQualification = request.getParameter("medicalQualification");
        String comment = request.getParameter("comment");
        CustomerAptitudeComment addComment = new CustomerAptitudeComment();
        addComment.setComment(comment);
        addComment.setTraderCustomerId(Integer.valueOf(traderId));
        traderCustomerService.addCustomerAptitudeComment(addComment);
        //Integer customerProperty=getCustomerCategory(Integer.valueOf(request.getParameter("traderCustomerId")));
        if (threeInOne != null && Integer.valueOf(threeInOne) == 1) {// 三证合一
            //营业执照
            TraderCertificateVo bus = saveBussiness(request);
            if (bus != null) {
                bus.setThreeInOne(1);
                list.add(bus);
                //税务登记
                TraderCertificateVo tax = (TraderCertificateVo) bus.clone();
                tax.setThreeInOne(1);
                tax.setSysOptionDefinitionId(SysOptionConstant.ID_26);
                tax.setUris(null);
                list.add(tax);
                //组织机构证
                TraderCertificateVo org = (TraderCertificateVo) bus.clone();
                org.setSysOptionDefinitionId(SysOptionConstant.ID_27);
                org.setThreeInOne(1);
                org.setUris(null);
                list.add(org);
            }

        } else {
            TraderCertificateVo bus = saveBussiness(request);
            if (bus != null) {
                bus.setThreeInOne(0);
                list.add(bus);
            }
            TraderCertificateVo tax = saveTax(request);
            if (tax != null) {
                tax.setThreeInOne(0);
                list.add(tax);
            }
            TraderCertificateVo org = saveOrg(request);
            if (org != null) {
                org.setThreeInOne(0);
                list.add(org);
            }

        }
        List<TraderMedicalCategory> twomcList = null;
        List<TraderMedicalCategory> threemcList = null;
        List<TraderMedicalCategory> newTwomcList = null;
        List<TraderMedicalCategory> newThreemcList = null;
        if (traderCustomer.getCustomerNature() == 465) {
            if (medicalQualification != null && Integer.valueOf(medicalQualification) == 1) {// 医疗资质合一
                TraderCertificateVo two = saveTwoMedical(request);
                if (two != null) {
                    two.setMedicalQualification(1);
                    list.add(two);
                    TraderCertificateVo three = (TraderCertificateVo) two.clone();
                    three.setSysOptionDefinitionId(SysOptionConstant.ID_29);
                    three.setMedicalQualification(1);
                    list.add(three);
                    //根据资质类别的数量获取资质类别所属的分类
                    twomcList = saveMedicalAptitudes(request);
                    threemcList = saveThreeMedicalAptitudes(request);
                    newTwomcList = saveNewTwoMedicalAptitude(request);
                    newThreemcList = saveNewThreeMedicalAptitude(request);
                }

            } else {
                TraderCertificateVo two = saveTwoMedical(request);
                twomcList = saveMedicalAptitudes(request);
                newTwomcList = saveNewTwoMedicalAptitude(request);
                if (two != null) {
                    two.setMedicalQualification(0);
                    list.add(two);
                    //根据资质类别的数量获取资质类别所属的分类

                }
                TraderCertificateVo three = saveThreeMedical(request);
                threemcList = saveThreeMedicalAptitudes(request);
                newThreemcList = saveNewThreeMedicalAptitude(request);
                if (three != null) {
                    three.setMedicalQualification(0);
                    list.add(three);
                    //根据资质类别的数量获取资质类别所属的分类

                }
            }

        } else if (traderCustomer.getCustomerNature() == 466) {
            TraderCertificateVo practice = savePractice(request);
            if (practice != null) {
                list.add(practice);
            }

            //中医诊所备案证
            TraderCertificateVo clinic = saveCustomerAptitude(request,"clinicStartTime","clinicEndTime","clinicName","clinicUri","clinicSn",SysOptionConstant.CHINESE_MEDICAL_CLINIC);
            if (clinic != null) {
                list.add(clinic);
            }
            //动物诊疗许可证
            TraderCertificateVo animal = saveCustomerAptitude(request,"animalStartTime","animalEndTime","animalName","animalUri","animalSn",SysOptionConstant.ANIMAL_CLINIC);
            if (animal != null) {
                list.add(animal);
            }
            //其他
            saveCustomerAptitudes(request,list,"otherStartTime","otherEndTime","otherName","otherUri","otherSn",SysOptionConstant.OTHER);

            String isProfit = request.getParameter("isProfit");
            traderCustomerService.updateCustomerIsProfit(Integer.valueOf(traderId), Integer.valueOf(isProfit));
        }
        TraderCertificateVo product = saveProduct(request);
        if (product != null) {
            list.add(product);
        }
//		if(list.size()==0&&tcmcList==null){
//			ResultInfo res =new ResultInfo(2, "请至少填写/选择一项数据");
//			return res;
//		}
        ResultInfo res = traderCustomerService.saveMedicalAptitude(traderVo, list, twomcList, threemcList, newTwomcList, newThreemcList);
        String customerAptitude = request.getParameter("customerAptitude");
        if (res != null && res.getCode() == 0 && (StringUtil.isBlank(customerAptitude) || Integer.valueOf(customerAptitude).intValue() != 0)) {
            startCheckAptitude(request, traderCustomer, taskId);
        }
        ModelAndView mav = new ModelAndView();
        if (res != null && res.getCode() == 0) {
            //更新贝登会员
            traderCustomerService.updateVedengMember();
            mav.addObject("refresh", "true_false_true");//是否关闭当前页，是否刷新当前页，是否刷新上层页面----三个参数为必传项
            mav.addObject("url", "./getFinanceAndAptitude.do?traderId=" + traderCustomer.getTraderId() + "&traderCustomerId=" + traderCustomer.getTraderCustomerId());
            return success(mav);
        } else {
            return fail(new ModelAndView());
        }
    }

    /**
     * <b>Description:</b><br> 获取二类医疗资质新国标
     *
     * @param request 请求参数
     * @return
     * @Note <b>Author:</b>
     * <br><b>Date:</b>
     */
    private List<TraderMedicalCategory> saveNewTwoMedicalAptitude(HttpServletRequest request) {
        String medicalTypes[] = request.getParameterValues("newTwoMedicalType");
        String traderId = request.getParameter("traderId");
        String traderType = request.getParameter("traderType");
        TraderMedicalCategory tcac = null;
        if (medicalTypes == null)
            return null;
        if (medicalTypes.length > 0) {
            List<TraderMedicalCategory> list = new ArrayList<>();
            for (int i = 0; i < medicalTypes.length; i++) {
                if (ObjectUtils.notEmpty(medicalTypes[i])) {
                    tcac = new TraderMedicalCategory();
                    tcac.setTraderId(Integer.valueOf(traderId));
                    tcac.setTraderType(Integer.valueOf(traderType));

                    tcac.setMedicalCategoryId(Integer.valueOf(medicalTypes[i]));

                    tcac.setMedicalCategoryLevel(SysOptionConstant.NEW_TWO_MEDICAL_CATEGORY);

                    list.add(tcac);
                }
            }
            return list;
        } else {
            return null;
        }
    }

    /**
     * <b>Description:</b><br> 获取三类医疗资质新国标
     *
     * @param request 请求参数
     * @return
     * @Note <b>Author:</b>
     * <br><b>Date:</b>
     */
    private List<TraderMedicalCategory> saveNewThreeMedicalAptitude(HttpServletRequest request) {
        String medicalTypes[] = request.getParameterValues("newThreeMedicalType");
        String traderId = request.getParameter("traderId");
        String traderType = request.getParameter("traderType");
        TraderMedicalCategory tcac = null;
        if (medicalTypes == null)
            return null;
        if (medicalTypes.length > 0) {
            List<TraderMedicalCategory> list = new ArrayList<>();
            for (int i = 0; i < medicalTypes.length; i++) {
                if (ObjectUtils.notEmpty(medicalTypes[i])) {
                    tcac = new TraderMedicalCategory();
                    tcac.setTraderId(Integer.valueOf(traderId));
                    tcac.setTraderType(Integer.valueOf(traderType));

                    tcac.setMedicalCategoryId(Integer.valueOf(medicalTypes[i]));

                    tcac.setMedicalCategoryLevel(SysOptionConstant.NEW_THREE_MEDICAL_CATEGORY);

                    list.add(tcac);
                }
            }
            return list;
        } else {
            return null;
        }
    }


    /**
     * <b>Description:</b><br>
     * 获取二类医疗资质信息
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月5日 下午4:10:55
     */
    private List<TraderMedicalCategory> saveMedicalAptitudes(HttpServletRequest request) {
        String medicalTypes[] = request.getParameterValues("twoMedicalType");
        String traderId = request.getParameter("traderId");
        String traderType = request.getParameter("traderType");
        TraderMedicalCategory tcac = null;
        if (medicalTypes == null)
            return null;
        if (medicalTypes.length > 0) {
            List<TraderMedicalCategory> list = new ArrayList<>();
            for (int i = 0; i < medicalTypes.length; i++) {
                if (ObjectUtils.notEmpty(medicalTypes[i])) {
                    tcac = new TraderMedicalCategory();
                    tcac.setTraderId(Integer.valueOf(traderId));
                    tcac.setTraderType(Integer.valueOf(traderType));

                    tcac.setMedicalCategoryId(Integer.valueOf(medicalTypes[i]));

                    tcac.setMedicalCategoryLevel(239);

                    list.add(tcac);
                }
            }
            return list;
        } else {
            return null;
        }

    }

    /**
     * <b>Description:</b><br>
     * 获取三类医疗资质信息
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月5日 下午4:10:55
     */
    private List<TraderMedicalCategory> saveThreeMedicalAptitudes(HttpServletRequest request) {
        String medicalTypes[] = request.getParameterValues("threeMedicalType");
        String traderId = request.getParameter("traderId");
        String traderType = request.getParameter("traderType");
        TraderMedicalCategory tcac = null;
        if (medicalTypes == null)
            return null;
        if (medicalTypes.length > 0) {
            List<TraderMedicalCategory> list = new ArrayList<>();
            for (int i = 0; i < medicalTypes.length; i++) {
                if (ObjectUtils.notEmpty(medicalTypes[i])) {
                    tcac = new TraderMedicalCategory();
                    tcac.setTraderId(Integer.valueOf(traderId));
                    tcac.setTraderType(Integer.valueOf(traderType));
                    tcac.setMedicalCategoryId(Integer.valueOf(medicalTypes[i]));
                    tcac.setMedicalCategoryLevel(240);
                    list.add(tcac);
                }
            }
            return list;
        } else {
            return null;
        }

    }

    /**
     * <b>Description:</b><br>
     * 保存营业执照
     *
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月5日 下午1:54:45
     */
    private TraderCertificateVo saveBussiness(HttpServletRequest request) {
        String busStartTime = request.getParameter("busStartTime");
        if (ObjectUtils.isEmpty(busStartTime)) {
            return null;
        }
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        //String busTraderCertificateId = request.getParameter("busTraderCertificateId");
        String traderId = request.getParameter("traderId");
        String[] otherPicName = request.getParameterValues("name_1");
        // 营业执照
        TraderCertificateVo bus = new TraderCertificateVo();

        String busName = request.getParameter("busName");
        if (ObjectUtils.notEmpty(busName)) {
            String busUri = request.getParameter("busUri");
            bus.setName(busName);
            bus.setUri(busUri);
        }
        if (otherPicName != null && otherPicName.length > 0) {
            String[] uris = request.getParameterValues("uri_1");
            bus.setUris(uris);
            bus.setNames(otherPicName);
        }
        bus.setCreator(user.getUserId());
        bus.setAddTime(System.currentTimeMillis());
        bus.setUpdater(user.getUserId());
        bus.setModTime(System.currentTimeMillis());

        bus.setTraderId(Integer.valueOf(traderId));
        bus.setTraderType(ErpConst.ONE);
        bus.setSysOptionDefinitionId(SysOptionConstant.ID_25);// 营业执照
        String isMedical = request.getParameter("isMedical");
        if (ObjectUtils.notEmpty(isMedical)) {
            bus.setIsMedical(1);
        } else {
            bus.setIsMedical(0);
        }

        if (ObjectUtils.notEmpty(busStartTime)) {
            bus.setBegintime(DateUtil.StringToDate(busStartTime).getTime());
        }
        String busEndTime = request.getParameter("busEndTime");
        if (ObjectUtils.notEmpty(busEndTime)) {
            bus.setEndtime(DateUtil.convertLong(busEndTime + " 23:59:59", null));
        }
        return bus;
    }

    /**
     * <b>Description:</b><br>
     * 保存税务登记证
     *
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月5日 下午1:54:45
     */
    private TraderCertificateVo saveTax(HttpServletRequest request) {
        String taxStartTime = request.getParameter("taxStartTime");
        if (ObjectUtils.isEmpty(taxStartTime)) {
            return null;
        }
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
//		String taxTraderCertificateId = request.getParameter("taxTraderCertificateId");
        String traderId = request.getParameter("traderId");
        String taxName = request.getParameter("taxName");

        String taxEndTime = request.getParameter("taxEndTime");
        // 税务登记证
        TraderCertificateVo tax = new TraderCertificateVo();
        tax.setCreator(user.getUserId());
        tax.setAddTime(System.currentTimeMillis());
        tax.setUpdater(user.getUserId());
        tax.setModTime(System.currentTimeMillis());

        tax.setTraderId(Integer.valueOf(traderId));
        tax.setTraderType(ErpConst.ONE);
        tax.setSysOptionDefinitionId(SysOptionConstant.ID_26);// 税务登记证
        tax.setIsMedical(0);
        if (ObjectUtils.notEmpty(taxName)) {
            String taxUri = request.getParameter("taxUri");
            tax.setUri(taxUri);
            tax.setName(taxName);
        }
        if (ObjectUtils.notEmpty(taxStartTime)) {
            tax.setBegintime(DateUtil.StringToDate(taxStartTime).getTime());
        }
        if (ObjectUtils.notEmpty(taxEndTime)) {
            tax.setEndtime(DateUtil.convertLong(taxEndTime + " 23:59:59", null));
        }
        return tax;
    }

    /**
     * <b>Description:</b><br>
     * 保存组织机构代码证
     *
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月5日 下午1:54:45
     */
    private TraderCertificateVo saveOrg(HttpServletRequest request) {
        String orgaStartTime = request.getParameter("orgaStartTime");
        if (ObjectUtils.isEmpty(orgaStartTime)) {
            return null;
        }
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
//		String orgaTraderCertificateId = request.getParameter("orgaTraderCertificateId");
        String traderId = request.getParameter("traderId");

        String orgName = request.getParameter("orgName");
        String orgaEndTime = request.getParameter("orgaEndTime");
        // 组织机构代码证
        TraderCertificateVo org = new TraderCertificateVo();
        org.setCreator(user.getUserId());
        org.setAddTime(System.currentTimeMillis());
        org.setUpdater(user.getUserId());
        org.setModTime(System.currentTimeMillis());

        org.setTraderId(Integer.valueOf(traderId));
        org.setTraderType(ErpConst.ONE);
        org.setSysOptionDefinitionId(SysOptionConstant.ID_27);// 组织机构代码证
        org.setIsMedical(0);
        if (ObjectUtils.notEmpty(orgName)) {
            String orgUri = request.getParameter("orgaUri");
            org.setUri(orgUri);
            org.setName(orgName);
        }
        if (ObjectUtils.notEmpty(orgaStartTime)) {
            org.setBegintime(DateUtil.StringToDate(orgaStartTime).getTime());
        }
        if (ObjectUtils.notEmpty(orgaEndTime)) {
            org.setEndtime(DateUtil.convertLong(orgaEndTime + " 23:59:59", null));
        }
        return org;
    }

    /**
     * <b>Description:</b><br>
     * 保存二类医疗资质
     *
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月5日 下午1:54:45
     */
    private TraderCertificateVo saveTwoMedical(HttpServletRequest request) {
        String twoStartTime = request.getParameter("twoStartTime");
        String[] otherPicName = request.getParameterValues("name_4");
        if (ObjectUtils.isEmpty(twoStartTime)) {
            return null;
        }
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
//		String twoTraderCertificateId = request.getParameter("twoTraderCertificateId");
        String traderId = request.getParameter("traderId");
        String twoName = request.getParameter("twoName");

        String twoEndTime = request.getParameter("twoEndTime");
        String twoSn = request.getParameter("twoSn");
        // 二类医疗资质
        TraderCertificateVo two = new TraderCertificateVo();
        two.setCreator(user.getUserId());
        two.setAddTime(System.currentTimeMillis());
        two.setUpdater(user.getUserId());
        two.setModTime(System.currentTimeMillis());

        two.setSn(twoSn);
        two.setTraderId(Integer.valueOf(traderId));
        two.setTraderType(ErpConst.ONE);
        two.setSysOptionDefinitionId(SysOptionConstant.ID_28);
        two.setIsMedical(0);
        if (ObjectUtils.notEmpty(twoName)) {
            String twoUri = request.getParameter("twoUri");
            two.setUri(twoUri);
            two.setName(twoName);
        }
        if (otherPicName != null && otherPicName.length > 0) {
            String[] uris = request.getParameterValues("uri_4");
            two.setUris(uris);
            two.setNames(otherPicName);
        }
        if (ObjectUtils.notEmpty(twoStartTime)) {
            two.setBegintime(DateUtil.StringToDate(twoStartTime).getTime());
        }
        if (ObjectUtils.notEmpty(twoEndTime)) {
            two.setEndtime(DateUtil.convertLong(twoEndTime + " 23:59:59", null));
        }
        return two;
    }

    /**
     * <b>Description:</b><br>
     * 保存三类医疗资质
     *
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月5日 下午1:54:45
     */
    private TraderCertificateVo saveThreeMedical(HttpServletRequest request) {
        String threeStartTime = request.getParameter("threeStartTime");
        String[] otherPicName = request.getParameterValues("name_5");
        if (ObjectUtils.isEmpty(threeStartTime)) {
            return null;
        }
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
//		String threeTraderCertificateId = request.getParameter("threeTraderCertificateId");
        String traderId = request.getParameter("traderId");

        String threeName = request.getParameter("threeName");

        String threeEndTime = request.getParameter("threeEndTime");
        String threeSn = request.getParameter("threeSn");
        // 三类医疗资质
        TraderCertificateVo three = new TraderCertificateVo();

        three.setCreator(user.getUserId());
        three.setAddTime(System.currentTimeMillis());
        three.setUpdater(user.getUserId());
        three.setModTime(System.currentTimeMillis());

        three.setSn(threeSn);
        three.setTraderId(Integer.valueOf(traderId));
        three.setTraderType(ErpConst.ONE);
        three.setSysOptionDefinitionId(SysOptionConstant.ID_29);
        three.setIsMedical(0);
        if (ObjectUtils.notEmpty(threeName)) {
            String threeUri = request.getParameter("threeUri");
            three.setUri(threeUri);
            three.setName(threeName);
        }

        if (otherPicName != null && otherPicName.length > 0) {
            String[] uris = request.getParameterValues("uri_5");
            three.setUris(uris);
            three.setNames(otherPicName);
        }
        if (ObjectUtils.notEmpty(threeStartTime)) {
            three.setBegintime(DateUtil.StringToDate(threeStartTime).getTime());
        }
        if (ObjectUtils.notEmpty(threeEndTime)) {
            three.setEndtime(DateUtil.convertLong(threeEndTime + " 23:59:59", null));
        }
        return three;
    }

    /**
     * <b>Description:</b><br>
     * 保存医疗机构执业许可证
     *
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月5日 下午1:54:45
     */
    private TraderCertificateVo savePractice(HttpServletRequest request) {
        String practiceName = request.getParameter("practiceName");
        String[] otherPicName = request.getParameterValues("name_4");//多张图片的URI
        if (ObjectUtils.isEmpty(practiceName)) {
            return null;
        }
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
//		String practiceTraderCertificateId = request.getParameter("practiceTraderCertificateId");
        String traderId = request.getParameter("traderId");

        String practiceTime = request.getParameter("practiceStartTime");
        String practiceEndTime = request.getParameter("practiceEndTime");
        String practiceSn = request.getParameter("practiceSn");
        // 医疗机构执业许可证
        TraderCertificateVo practice = new TraderCertificateVo();
        practice.setCreator(user.getUserId());
        practice.setAddTime(System.currentTimeMillis());
        practice.setUpdater(user.getUserId());
        practice.setModTime(System.currentTimeMillis());

        practice.setSn(practiceSn);
        practice.setTraderId(Integer.valueOf(traderId));
        practice.setTraderType(ErpConst.ONE);
        practice.setSysOptionDefinitionId(SysOptionConstant.ID_438);
        if (ObjectUtils.notEmpty(practiceTime)) {
            String practiceUri = request.getParameter("practiceUri");
            practice.setUri(practiceUri);
            practice.setName(practiceName);
        }
        //判断是否有多张图片
        if (otherPicName != null && otherPicName.length > 0) {
            String[] uris = request.getParameterValues("uri_4");
            practice.setUris(uris);
            practice.setNames(otherPicName);
        }
        if (ObjectUtils.notEmpty(practiceTime)) {
            practice.setBegintime(DateUtil.StringToDate(practiceTime).getTime());
        }
        if (ObjectUtils.notEmpty(practiceEndTime)) {
            practice.setEndtime(DateUtil.convertLong(practiceEndTime + " 23:59:59", null));
        }
        return practice;
    }

    private TraderCertificateVo saveCustomerAptitude(HttpServletRequest request,String startTime,String endTime,String name,String uri,String sn,Integer sysOptionDefinitionId) {
        String aptitudeName = request.getParameter(name);
        String[] otherPicName = request.getParameterValues("name_4");
        String aptitudeStartTime = request.getParameter(startTime);
        if (ObjectUtils.isEmpty(aptitudeName)) {
            return null;
        }
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
//		String taxTraderCertificateId = request.getParameter("taxTraderCertificateId");
        String traderId = request.getParameter("traderId");

        String aptitudeEndTime = request.getParameter(endTime);
        String aptitudeSn = request.getParameter(sn);
        TraderCertificateVo aptitude = new TraderCertificateVo();
        aptitude.setCreator(user.getUserId());
        aptitude.setAddTime(System.currentTimeMillis());
        aptitude.setUpdater(user.getUserId());
        aptitude.setModTime(System.currentTimeMillis());
        aptitude.setSn(aptitudeSn);
        aptitude.setTraderId(Integer.valueOf(traderId));
        aptitude.setTraderType(ErpConst.ONE);
        aptitude.setSysOptionDefinitionId(sysOptionDefinitionId);
        aptitude.setIsMedical(0);
        if (ObjectUtils.notEmpty(aptitudeName)) {
            String aptitudeUri = request.getParameter(uri);
            aptitude.setUri(aptitudeUri);
            aptitude.setName(aptitudeName);
        }
        if (ObjectUtils.notEmpty(aptitudeStartTime)) {
            aptitude.setBegintime(DateUtil.StringToDate(aptitudeStartTime).getTime());
        }
        if (ObjectUtils.notEmpty(aptitudeEndTime)) {
            aptitude.setEndtime(DateUtil.convertLong(aptitudeEndTime + " 23:59:59", null));
        }
        return aptitude;
    }

    private void saveCustomerAptitudes(HttpServletRequest request,List<TraderCertificateVo> list,String startTime,String endTime,String name,String uri,String sn,Integer sysOptionDefinitionId) {
        String aptitudeName = request.getParameter(name);
        String[] otherPicName = request.getParameterValues("name_8");
        String aptitudeStartTime = request.getParameter(startTime);
        if (ObjectUtils.isEmpty(aptitudeName) && otherPicName == null) {
            return;
        }
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
//		String taxTraderCertificateId = request.getParameter("taxTraderCertificateId");
        String traderId = request.getParameter("traderId");
        String aptitudeSn = request.getParameter(sn);
        String aptitudeEndTime = request.getParameter(endTime);
        TraderCertificateVo aptitude = new TraderCertificateVo();
        aptitude.setCreator(user.getUserId());
        aptitude.setAddTime(System.currentTimeMillis());
        aptitude.setUpdater(user.getUserId());
        aptitude.setModTime(System.currentTimeMillis());
        aptitude.setSn(aptitudeSn);
        aptitude.setTraderId(Integer.valueOf(traderId));
        aptitude.setTraderType(ErpConst.ONE);
        aptitude.setSysOptionDefinitionId(sysOptionDefinitionId);
        aptitude.setIsMedical(0);
        if (ObjectUtils.notEmpty(aptitudeName)) {
            String aptitudeUri = request.getParameter(uri);
            aptitude.setUri(aptitudeUri);
            aptitude.setName(aptitudeName);
        }

        if (ObjectUtils.notEmpty(aptitudeStartTime)) {
            aptitude.setBegintime(DateUtil.StringToDate(aptitudeStartTime).getTime());
        }
        if (ObjectUtils.notEmpty(aptitudeEndTime)) {
            aptitude.setEndtime(DateUtil.convertLong(aptitudeEndTime + " 23:59:59", null));
        }
        list.add(aptitude);
        //判断是否有多张图片
        if (otherPicName != null && otherPicName.length > 0) {
            String[] uris = request.getParameterValues("uri_8");

            //VDERP-6709【质量管理】终端客户资质信息增加字段中其他字段的逻辑修改
            String[] startTimes = request.getParameterValues("otherStartTimes");
            String[] endTimes = request.getParameterValues("otherEndTimes");
            String[] otherSns = request.getParameterValues("otherSns");
            for(int i = 0; i < uris.length; i++){
                TraderCertificateVo certificateVo = new TraderCertificateVo();
                BeanUtils.copyProperties(aptitude,certificateVo);
                certificateVo.setUri(uris[i]);
                certificateVo.setName(otherPicName[i]);
                if (startTimes[i] != null && startTimes[i] != ""){
                    certificateVo.setBegintime(DateUtil.StringToDate(startTimes[i]).getTime());
                }else {
                    certificateVo.setBegintime(null);
                }
                if (endTimes[i] != null && endTimes[i] != ""){
                    certificateVo.setEndtime(DateUtil.convertLong(endTimes[i] + " 23:59:59", null));
                }else {
                    certificateVo.setEndtime(null);
                }
                certificateVo.setSn(otherSns[i]);
                list.add(certificateVo);
            }

        }
    }

    /**
     * <b>Description:</b><br>
     * 保存医疗器械生产许可证
     *
     * @param request
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月5日 下午1:54:45
     */
    private TraderCertificateVo saveProduct(HttpServletRequest request) {
        String productTime = request.getParameter("productStartTime");
        if (ObjectUtils.isEmpty(productTime)) {
            return null;
        }
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
//		String productTraderCertificateId = request.getParameter("productTraderCertificateId");
        String traderId = request.getParameter("traderId");

        String productName = request.getParameter("productName");
        String productEndTime = request.getParameter("productEndTime");
        String productSn = request.getParameter("productSn");
        // 医疗器械生产许可证
        TraderCertificateVo product = new TraderCertificateVo();
        product.setCreator(user.getUserId());
        product.setAddTime(System.currentTimeMillis());
        product.setUpdater(user.getUserId());
        product.setModTime(System.currentTimeMillis());

        product.setSn(productSn);
        product.setTraderId(Integer.valueOf(traderId));
        product.setTraderType(ErpConst.ONE);
        product.setSysOptionDefinitionId(SysOptionConstant.ID_439);
        if (ObjectUtils.notEmpty(productName)) {
            String productUri = request.getParameter("productUri");
            product.setUri(productUri);
            product.setName(productName);
        }
        if (ObjectUtils.notEmpty(productTime)) {
            product.setBegintime(DateUtil.StringToDate(productTime).getTime());
        }
        if (ObjectUtils.notEmpty(productEndTime)) {
            product.setEndtime(DateUtil.convertLong(productEndTime + " 23:59:59", null));
        }
        return product;
    }


    /**
     * <b>Description:</b><br> 分配客户
     *
     * @param request
     * @param pageNo
     * @param pageSize
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年6月5日 下午5:53:47
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/assign")
    public ModelAndView assign(HttpServletRequest request,
                               @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                               @RequestParam(required = false) Integer pageSize,
                               HttpSession session) {
        User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        // 查询所有职位类型为310的员工
        List<User> userList = userService.getActiveUserByPositType(SysOptionConstant.ID_310, session_user.getCompanyId());

        //地区
        List<Region> provinceList = regionService.getRegionByParentId(1);

        String type = request.getParameter("type");
        String traderName = request.getParameter("traderName");
        String from_user = request.getParameter("from_user");
        String provinceId = request.getParameter("province");
        String cityId = request.getParameter("city");
        String zoneId = request.getParameter("zone");

        if (type != null) {
            if (type.equals("1")) {//单个分配查询
                from_user = "0";

                //查询集合
                Page page = getPageTag(request, pageNo, pageSize);
                RTraderJUser rTraderJUser = new RTraderJUser();
                rTraderJUser.setTraderName(traderName);
                rTraderJUser.setCompanyId(session_user.getCompanyId());

                Map<String, Object> map = traderCustomerService.getUserTraderByTraderNameListPage(rTraderJUser, page);

                mv.addObject("traderList", (List<RTraderJUser>) map.get("list"));
                mv.addObject("page", (Page) map.get("page"));
            }
            if (type.equals("2")) {//批量分配查询
                traderName = "";

                User user = userService.getUserById(Integer.parseInt(from_user));
                RTraderJUser rTraderJUser = new RTraderJUser();
                rTraderJUser.setUserId(Integer.parseInt(from_user));
                rTraderJUser.setCompanyId(session_user.getCompanyId());
                if (!zoneId.equals("0")) {
                    rTraderJUser.setAreaId(Integer.parseInt(zoneId));
                } else {
                    //String areaIds = "";
                    if (!provinceId.equals("0")) {
                        //areaIds += provinceId+",";
                        rTraderJUser.setAreaId(Integer.parseInt(provinceId));
                    }
                    if (!cityId.equals("0")) {
                        //areaIds += ","+cityId;
                        rTraderJUser.setAreaId(Integer.parseInt(cityId));
                    }

                    //rTraderJUser.setAreaIds(areaIds);
                }

                Integer userSupplierNum = traderCustomerService.getUserCustomerNum(rTraderJUser, Integer.parseInt(from_user)).size();

                if (!provinceId.equals("0")) {
                    List<Region> cityList = regionService.getRegionByParentId(Integer.parseInt(provinceId));
                    mv.addObject("cityList", cityList);
                }
                if (!cityId.equals("0")) {
                    List<Region> zoneList = regionService.getRegionByParentId(Integer.parseInt(cityId));
                    mv.addObject("zoneList", zoneList);
                }
                mv.addObject("user", user);
                mv.addObject("userSupplierNum", userSupplierNum);
            }
        }
        //获取销售部门
        List<Organization> orgList = orgService.getAllOrganizationListByType(SysOptionConstant.ID_310,
                session_user.getCompanyId());
        mv.addObject("orgList", orgList);
        mv.addObject("userList", userList);
        mv.addObject("provinceList", provinceList);

        mv.addObject("type", type);
        mv.addObject("traderName", traderName);
        mv.addObject("from_user", from_user);
        mv.addObject("provinceId", provinceId);
        mv.addObject("cityId", cityId);
        mv.addObject("zoneId", zoneId);
        mv.setViewName("trader/customer/assign");
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "/queryAssignInfo")
    @NoNeedAccessAuthorization
    public ResultInfo<List<AssignCustomerInfo>> getAssignList(@RequestParam Integer traderId){
        AssignCustomerInfo assignCustomerInfo = traderCustomerService.getUserTraderByTraderNameList(traderId);
        return ResultInfo.success(Collections.singletonList(assignCustomerInfo));
    }

    @ResponseBody
    @RequestMapping(value = "/queryUserInfo")
    @NoNeedAccessAuthorization
    public ResultInfo<List<AssignUserInfo>> queryUserInfo(@RequestBody AssignUserInfoReqDto reqDto){
        AssignUserInfo assignUserInfo = traderCustomerService.getAssignUserInfo(reqDto);
        return ResultInfo.success(Collections.singletonList(assignUserInfo));
    }


    /**
     * <b>Description:</b><br> 保存分配客户
     *
     * @param type
     * @param traderId
     * @param single_to_user
     * @param from_user
     * @param batch_to_user
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年6月6日 下午2:17:09
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveassign")
    @SystemControllerLog(operationType = "edit", desc = "保存分配客户")
    public ResultInfo<?> saveAssign(Integer type, Integer traderId, Integer single_to_user,
                                    Integer from_user, Integer batch_to_user,
                                    Integer provinceId, Integer cityId, Integer zoneId,
                                    HttpServletRequest request,HttpSession session) {
        try {
            User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
            ResultInfo<?> resultInfo = new ResultInfo<>();

            Boolean succ = false;
            //推送客户Id列表
            List<Integer> traderIdList = Lists.newArrayList();
            Integer salerId = 0;
            String salerName = "";
            if (type.equals(1)) {
                //VDERP-10066 关联客户分配销售，视情况取消关联
                try {
                    RTraderJUser ru = rTraderJUserMapper.getUserByTraderId(traderId);
                    if(!single_to_user.equals(ru.getUserId())){
                        traderCustomerService.clearRelationBetweenTraderCustomer(traderId);
                    }
                } catch (Exception e) {
                    log.info("分配单个客户时，取消客户关联关系失败");
                }
                succ = traderCustomerService.assignSingleCustomer(traderId, single_to_user, session_user.getCompanyId(),session_user,0);
                //如果客户在公海列表，在分配客户时，则将该客户置为私有（IS_PRIVATIZED：3）
                traderCustomerService.setPublicCustomerPrivatizedByAssign(traderId);

                traderIdList.add(traderId);
                salerId = single_to_user;
                try {
                    salerName = userService.getUserNameByUserId(salerId);
                } catch (Exception e) {
                    logger.error("TraderCustomerController--saveassign--查询用户名异常", e);
                }
            }

            if (type.equals(2)) {
                String areaIds = "";
                Integer areaId = 0;
                if (zoneId > 0) {
                    areaId = zoneId;
                } else {
                    if (provinceId > 0) {
                        areaIds += provinceId;
                        areaId = provinceId;
                    }
                    if (cityId > 0) {
                        areaIds += "," + cityId + ",";
                        areaId = cityId;
                    }
                }
                List<Integer> assignedTraderIdList = traderCustomerService.assignBatchCustomer(session_user, from_user, batch_to_user,
                        areaId);

                if (assignedTraderIdList == null) {
                    succ = false;
                } else {
                    succ = true;
                }

                //如果客户在公海列表，在分配客户时，则将该客户置为私有（IS_PRIVATIZED：3）
                if (assignedTraderIdList.size() > 0) {
                    traderMapper.setPublicCustomerPrivatizedByAssign(assignedTraderIdList);
                }

                //根据销售ID查询该销售下的所有所属客户
                RTraderJUser rTraderJUser = new RTraderJUser();
                rTraderJUser.setUserId(from_user);
                rTraderJUser.setCompanyId(session_user.getCompanyId());
                if (!zoneId.equals("0")) {
                    rTraderJUser.setAreaId(zoneId);
                } else {
                    //String areaIds = "";
                    if (!provinceId.equals("0")) {
                        //areaIds += provinceId+",";
                        rTraderJUser.setAreaId(provinceId);
                    }
                    if (!cityId.equals("0")) {
                        //areaIds += ","+cityId;
                        rTraderJUser.setAreaId(cityId);
                    }

                    //rTraderJUser.setAreaIds(areaIds);
                }
                List<RTraderJUser> traderList = traderCustomerService.getUserCustomerNum(rTraderJUser, from_user);
                salerId = batch_to_user;
                try {
                    salerName = userService.getUserNameByUserId(salerId);
                } catch (Exception e) {
                    logger.error("TraderCustomerController--saveassign--查询用户名异常", e);
                }
                if (!CollectionUtils.isEmpty(traderList)) {
                    for (RTraderJUser rTraderJUserTemp : traderList) {
                        traderIdList.add(rTraderJUserTemp.getTraderId());
                    }
                }
            }
            if (succ) {
                resultInfo.setCode(0);
                resultInfo.setMessage("操作成功，相关数据处理约需等待30分钟。");
                //客户分配推送到医械购
                Map<String, Object> map = Maps.newHashMap();
                map.put("salerId", salerId);
                map.put("salerName", salerName);
                map.put("traderIdList", traderIdList);
                traderCustomerService.putTraderSaleUserIdtoHC(map);
            }
            return resultInfo;
        } catch (Exception e) {
            logger.error("保存分配客户信息异常：", e);
            return new ResultInfo<>(CommonConstants.FAIL_CODE, CommonConstants.FAIL_MSG);
        }
    }

    /**
     * <b>Description:</b>保存财务信息<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2019/12/4
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveYxgTraderFinance")
    public ResultInfo saveYxgTraderFinance(HttpServletRequest request, TraderFinance traderFinance) {
        logger.info("保存医械购客户财务信息：{}",JSON.toJSONString(traderFinance));
        ResultInfo result = saveTraderFinance(request, traderFinance);
        if (result.getCode() == 0 && StringUtil.isBlank(traderFinance.getTaxNum())) {
            if (!(StringUtil.isBlank(traderFinance.getPreTaxNum())
                    && traderFinance.getCheckStatus() != null
                    && traderFinance.getCheckStatus() == 1)) {
                startFinanceCheck(request, traderFinance, null);
            }
        } else if (result.getCode() == 0) {
            traderCustomerService.syncTraderFinanceToPlatformOfYxg(traderFinance);
        }
        ResultInfo res = new ResultInfo(0, "操作成功");
        res.setData(request.getParameter("traderId") + "," + request.getParameter("traderCustomerId"));
        return res;
    }

    private ResultInfo saveTraderFinance(HttpServletRequest request, TraderFinance traderFinance) {

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ResultInfo res = traderCustomerService.saveCustomerFinance(traderFinance, user);
        return res;
    }

    /**
     * <b>Description:</b>完成财务信息审核接口<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2019/12/4
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/finishYxgFinanceCheck")
    @Transactional
    public ResultInfo finishFinanceCheck(HttpServletRequest request, TraderFinance traderFinance, String comment, String taskId, boolean pass) throws Exception {
        ResultInfo resultInfo = completeFinanceCheck(request, traderFinance, comment, taskId, pass);
        if (pass && resultInfo.getCode() == 0) {
            saveTraderFinance(request, traderFinance);
            ResultInfo res = traderCustomerService.syncTraderFinanceToPlatformOfYxg(traderFinance);
//            if(res.getCode()==-1){
//                throw new Exception("更新到医械购平台失败");
//            }
        }
        resultInfo.setData(request.getParameter("traderId") + "," + request.getParameter("traderCustomerId"));
        return resultInfo;
    }

    /**
     * <b>Description:</b>完成医械购财务信息审核操作数据库的接口<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2019/12/4
     */
    @Transactional
    public ResultInfo completeFinanceCheck(HttpServletRequest request, TraderFinance traderFinance, String comment, String taskId, boolean pass) {
        try {
            Map<String, Object> variableMap = new HashMap<String, Object>();
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            TraderCustomerVo traderBaseInfo = traderCustomerService.getTraderCustomerInfo(traderFinance.getTraderId());
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "TraderFinanceCheck_" + traderFinance.getTraderFinanceId());

            variableMap.put("pass", pass);
            variableMap.put("updater", user.getUserId());
            if (pass) {
                verifiesRecordService.saveVerifiesInfoForTrader(taskId, 1);
            } else {
//                User user1 = traderCustomerService.getPersonalUser(traderBaseInfo.getTraderId());
//                List<Integer> userIds = new ArrayList<>();
//                if (user1 != null && user1.getUserId() != null) {
//                    userIds.add(user1.getUserId());
//                    Map<String, String> map = new HashMap<>();
//                    map.put("traderName", traderBaseInfo.getName());
//                    String url = ErpConst.GET_FINANCE_AND_APTITUDE_URL + traderBaseInfo.getTraderId();
//                    MessageUtil.sendMessage(99, userIds, map, url);
//                }
                verifiesRecordService.saveVerifiesInfo(taskId, 2);
            }
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                taskId = taskInfo.getId();
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variableMap);


            }
            return new ResultInfo(0, "操作成功");
        } catch (Exception ex) {
            throw ex;
        }
    }
    @ResponseBody
    @RequestMapping(value = "/yxgStartFinanceCheck",method = RequestMethod.POST)
    public ResultInfo yxgStartFinanceCheck(@RequestBody TraderFinanceVo finance){
        logger.info("yxgStartFinanceCheck request param:", JSON.toJSONString(finance));
        finance.setTraderType(1);
        TraderFinanceVo financeVo=traderCustomerService.getTraderFinanceByTraderId(finance);
        if(financeVo==null||ErpConst.ZERO.equals(financeVo.getCheckStatus())){
            return ResultInfo.success();
        }
        startFinanceCheck(null,financeVo,null);
        return ResultInfo.success();
    }

    @Transactional
    public ResultInfo startFinanceCheck(HttpServletRequest request, TraderFinance finance, String taskId) {
        try {
            Map<String, Object> variableMap = new HashMap<String, Object>();
            User user;
            if(request!=null) {
                user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            }else{
                user=new User();
                user.setUserId(2);
                user.setUsername("njadmin");
            }
            TraderFinanceVo traderFinanceVo = new TraderFinanceVo();
            traderFinanceVo.setTraderId(finance.getTraderId());
            traderFinanceVo.setTraderType(1);
            TraderFinanceVo traderFinance = traderCustomerService.getTraderFinanceByTraderId(traderFinanceVo);
            //开始生成流程(如果没有taskId表示新流程需要生成)
            if (StringUtil.isBlank(taskId) || taskId.equals("0")) {
//                variableMap.put("traderCustomerVo", traderBaseInfo);
                variableMap.put("currentAssinee", user.getUsername());
                variableMap.put("processDefinitionKey", "customerAptitudeVerify");
                variableMap.put("businessKey", "TraderFinanceCheck_" + traderFinance.getTraderFinanceId());
                variableMap.put("relateTableKey", traderFinance.getTraderFinanceId());
                variableMap.put("relateTable", "T_TRADER_FINANCE");
                actionProcdefService.createProcessInstance(request, "TraderFinanceCheck", "TraderFinanceCheck_" + traderFinance.getTraderFinanceId(), variableMap);
            }
            //默认申请人通过
            //根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "TraderFinanceCheck_" + traderFinance.getTraderFinanceId());
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                taskId = taskInfo.getId();
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "", user.getUsername(), variableMap);
                //如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                }

            }
            List<Integer> userIds = new ArrayList<>();
            Page page = new Page(1, 20);
            RoleVo role = new RoleVo();
            role.setRoleName("财务专员");
            List<Role> roles = roleService.queryListPage(role, page);
            TraderCustomerVo traderCustomer = traderCustomerService.getTraderCustomerInfo(traderFinance.getTraderId());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(roles)) {
                for (Role r : roles) {
                    if (r.getCompanyId() != 1) {
                        continue;
                    }
                    List<User> users = userService.getUserByRoleId(r.getRoleId());
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(users)) {
                        for (User u : users) {
                            userIds.add(u.getUserId());
                        }
                    }
                }
            }
            Map<String, String> map = new HashMap<>();
            map.put("traderName", traderCustomer.getTraderName());
            String url = ErpConst.GET_FINANCE_AND_APTITUDE_URL + traderCustomer.getTraderId();
            if(request!=null) {
                MessageUtil.sendMessage(99, userIds, map, url);
            }else{
                String[] str = {"njadmin", "2"};
                MessageUtil.sendMessage(99, userIds, map, url,str);
            }
            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("editApplyValidCustomer:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }

    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/yxgTraderFinanceComplement")
    public ModelAndView yxgTraderFinanceComplement(HttpServletRequest request, TraderCustomer customer, TraderFinance finance, String taskId, boolean pass) {
        ModelAndView mv = new ModelAndView("trader/customer/yxg_finance_check_complement");
        mv.addObject("pass", pass);
        mv.addObject("taskId", taskId);
        mv.addObject("traderCustomer", customer);
        mv.addObject("finance", finance);
        return mv;
    }

    /**
     * <b>Description:</b>医械购客户专用改税号接口<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2019/12/3
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/editYxgFinancePage")
    public ModelAndView editYXGTraderFinance(HttpServletRequest request, TraderCustomer traderCustomer, Integer customerProperty) {
        ModelAndView mav = new ModelAndView("trader/customer/edit_yxg_trader_finance");
        User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        mav.addObject("traderCustomer", traderCustomer);
        TraderFinanceVo tf = new TraderFinanceVo();
        tf.setTraderId(traderCustomer.getTraderId());
        tf.setTraderType(ErpConst.ONE);

        TraderFinanceVo traderFinance = traderCustomerService.getTraderFinanceByTraderId(tf);
        if (traderFinance != null) {
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "TraderFinanceCheck_" + traderFinance.getTraderFinanceId());
            mav.addObject("financeTaskInfo", historicInfo.get("taskInfo"));
            mav.addObject("financeStartUser", historicInfo.get("startUser"));
            mav.addObject("financeCandidateUserMap", historicInfo.get("candidateUserMap"));
            // 最后审核状态
            mav.addObject("financeEndStatus", historicInfo.get("endStatus"));
            mav.addObject("financeHistoricActivityInstance", historicInfo.get("historicActivityInstance"));
            mav.addObject("financeCommentMap", historicInfo.get("commentMap"));
            mav.addObject("traderFinance", traderFinance);
            mav.addObject("customerProperty", customerProperty);
            mav.addObject("domain", domain);
        }
        User user = traderCustomerService.getPersonalUser(traderCustomer.getTraderId());
        if (user != null && user.getUserId() != null && curr_user.getUserId().intValue() == Integer.valueOf(user.getUserId()).intValue()) {
            mav.addObject("isbelong", true);
        } else if (curr_user.getUsername().equals("haocai.vedeng")) {
            mav.addObject("isbelong", true);
        } else {
            mav.addObject("isbelong", false);
        }
        try {
            mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(traderFinance)));
        } catch (Exception ex) {
        }
        return mav;
    }

    /**
     * <b>Description:</b><br> 客户的财务信息
     *
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年6月7日 下午3:07:00
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/toEditFinancePage")
    public ModelAndView toEditFinancePage(TraderCustomer traderCustomer, Integer customerProperty) throws IOException {
        ModelAndView mav = new ModelAndView("trader/customer/edit_finance");
        mav.addObject("traderCustomer", traderCustomer);
        TraderFinanceVo tf = new TraderFinanceVo();
        tf.setTraderId(traderCustomer.getTraderId());
        tf.setTraderType(ErpConst.ONE);

        TraderFinanceVo traderFinance = traderCustomerService.getTraderFinanceByTraderId(tf);

        mav.addObject("traderFinance", traderFinance);
        if (traderFinance != null) {
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "TraderFinanceCheck_" + traderFinance.getTraderFinanceId());
            mav.addObject("financeTaskInfo", historicInfo.get("taskInfo"));
        }
        mav.addObject("customerProperty", customerProperty);
        mav.addObject("domain", domain);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(traderFinance)));
        return mav;
    }

    /**
     * <b>Description:</b><br> 保存客户财务信息
     *
     * @param session
     * @param traderCustomer
     * @param traderFinance
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年6月7日 下午3:58:17
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveCustomerFinance")
    @SystemControllerLog(operationType = "add", desc = "保存客户财务信息")
    public ResultInfo saveCustomerFinance(HttpServletRequest request, HttpSession session, TraderFinance traderFinance, String beforeParams) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        //String upload_file_tmp=request.getParameter("upload_file_tmp");
        if (traderFinance == null) {
            return new ResultInfo(-1, "财务信息不得为空");
        }
        ResultInfo res = traderCustomerService.saveCustomerFinance(traderFinance, user);
        res.setData(request.getParameter("traderId") + "," + request.getParameter("traderCustomerId"));
        //保存客户财务信息后调用同步网站接口
        if (StringUtil.isBlank(traderFinance.getTaxNum())) {
            if (!(StringUtil.isBlank(traderFinance.getPreTaxNum())
                    && traderFinance.getCheckStatus() != null
                    && traderFinance.getCheckStatus() == 1)
            ) {
                startFinanceCheck(request, traderFinance, null);
            }


        }
        vedengSoapService.financeInfoSync(traderFinance.getTraderId());

        // 客户财务信息提交后推送至金蝶（这边无法判断是否更新了开户银行及联行号，因此只要更新了，就推送一遍）
        Integer traderCustomerId = Integer.valueOf(request.getParameter("traderCustomerId"));
        KingDeeCustomerDto kingDeeCustomerInfo = traderCustomerApiService.getKingDeeCustomerInfo(traderCustomerId);
        kingDeeCustomerApiService.register(kingDeeCustomerInfo);
        log.info("客户财务信息编辑提交推送客户信息至金蝶，客户id：{}", traderCustomerId);

        return res;
    }


    /**
     * <b>Description:</b><br>
     * @param traderCustomerId
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年6月21日 下午1:57:50
     */
	/*@ResponseBody
	@RequestMapping(value="/accountperiodapplycheck")
	public ResultInfo accountPeriodApplyCheck(Integer traderCustomerId){
		ResultInfo resultInfo = new ResultInfo<>();
		if(traderCustomerId <= 0){
			return resultInfo;
		}
		TraderAccountPeriodApply lastAccountPeriodApply = traderCustomerService.getTraderCustomerLastAccountPeriodApply(traderCustomerId);

		if(null == lastAccountPeriodApply
				&& lastAccountPeriodApply.getStatus() != 0){
			resultInfo.setCode(0);
			resultInfo.setMessage("操作成功");
		}else{
			resultInfo.setMessage("请等待当前帐期审核完成");
		}
		return resultInfo;
	}*/

    /**
     * <b>Description:</b><br> 客户账期申请
     *
     * @param traderCustomer
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年6月21日 上午10:43:56
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/accountperiodapply")
    public ModelAndView accountPeriodApply(TraderCustomer traderCustomer) {
        TraderAccountPeriodApply lastAccountPeriodApply = traderCustomerService.getTraderCustomerLastAccountPeriodApply(traderCustomer.getTraderId());

        ModelAndView mav = new ModelAndView("trader/customer/apply_accountperiod");
        if (null != lastAccountPeriodApply
                && lastAccountPeriodApply.getStatus() == 0) {
            mav.addObject("message", "当前有账期未审核，请等待当前帐期审核完成！");
        } else {
            TraderCustomer traderCustomerForAccountPeriod = traderCustomerService.getTraderCustomerForAccountPeriod(traderCustomer);
            mav.addObject("traderCustomer", traderCustomerForAccountPeriod);
        }
        return mav;
    }

    /**
     * <b>Description:</b><br> 保存账期申请
     *
     * @param request
     * @param session
     * @param traderAccountPeriodApply
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年6月21日 上午11:47:02
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveaccountperiodapply")
    @SystemControllerLog(operationType = "add", desc = "保存客户账期申请")
    public ResultInfo saveAccountPeriodApply(HttpServletRequest request, HttpSession session, TraderAccountPeriodApply traderAccountPeriodApply) {
        traderAccountPeriodApply.setTraderType(ErpConst.ONE);
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ResultInfo resultInfo = traderCustomerService.saveAccountPeriodApply(traderAccountPeriodApply, session);
        //如果操作成功触发审核操作
        if (resultInfo.getCode() == 0) {
            Integer periodApplyId = (Integer) resultInfo.getData();
            TraderAccountPeriodApply tapaInfo = traderCustomerService.getAccountPeriodDaysApplyInfo(periodApplyId);
            try {
                Map<String, Object> variableMap = new HashMap<String, Object>();
                // 查询当前账期创建者

                variableMap.put("currentAssinee", user.getUsername());
                variableMap.put("processDefinitionKey", "customerPeriodVerify");
                variableMap.put("businessKey", "customerPeriodVerify_" + periodApplyId);
                variableMap.put("relateTableKey", periodApplyId);
                variableMap.put("relateTable", "T_TRADER_ACCOUNT_PERIOD_APPLY");
                variableMap.put("orgId", user.getOrgId());
                variableMap.put("traderAccountPeriodApply", tapaInfo);
                variableMap.put("accountPeriodApply", tapaInfo.getAccountPeriodApply());
                variableMap.put("accountPeriodDaysApply", tapaInfo.getAccountPeriodDaysApply());
                variableMap.put("traderId", tapaInfo.getTraderId());
                variableMap.put("traderType", tapaInfo.getTraderType());
                variableMap.put("traderAccountPeriodApplyId", tapaInfo.getTraderAccountPeriodApplyId());
                actionProcdefService.createProcessInstance(request, "customerPeriodVerify", "customerPeriodVerify_" + periodApplyId, variableMap);
                //默认申请人通过
                //根据BusinessKey获取生成的审核实例
                Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "customerPeriodVerify_" + periodApplyId);
                if (historicInfo.get("endStatus") != "审核完成") {
                    Task taskInfo = (Task) historicInfo.get("taskInfo");
                    String taskId = taskInfo.getId();
                    Authentication.setAuthenticatedUserId(user.getUsername());
                    Map<String, Object> variables = new HashMap<String, Object>();
                    variables.put("tableName", "T_TRADER_ACCOUNT_PERIOD_APPLY");
                    variables.put("id", "TRADER_ACCOUNT_PERIOD_APPLY_ID");
                    variables.put("idValue", periodApplyId);
                    variables.put("key", "STATUS");
                    variables.put("value", 1);
                    //回写的表在dbcenter中
                    variables.put("db", 2);
                    //默认审批通过
                    ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "", user.getUsername(), variables);
                    //如果未结束添加审核对应主表的审核状态
                    if (!complementStatus.getData().equals("endEvent")) {
                        verifiesRecordService.saveVerifiesInfo(taskId, 0);
                    }
                }
                // return new ResultInfo(0, "操作成功");
            } catch (Exception e) {
                logger.error("saveaccountperiodapply:", e);
                return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
            }
        }
        resultInfo.setData(request.getParameter("traderId") + "," + request.getParameter("traderCustomerId"));
        return resultInfo;
    }

    /**
     * <b>Description:</b><br> 查询客户属于分销还是终端
     *
     * @param traderCustomerId
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年6月26日 下午4:23:40
     */
    private Integer getCustomerCategory(Integer traderCustomerId) {
        return traderCustomerService.getCustomerCategory(traderCustomerId);
    }

    /**
     * <b>Description:</b><br> 查询终端客户列表
     *
     * @param request
     * @param traderCustomerVo
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年6月27日 下午3:13:04
     */
    @RequestMapping(value = "/getTerminalList")
    public ModelAndView getTerminalList(HttpServletRequest request, TraderCustomerVo traderCustomerVo,
                                        @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                        @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        ModelAndView mv = new ModelAndView();

        try {
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            traderCustomerVo.setCompanyId(user.getCompanyId());

            String linename = request.getParameter("searchTraderName");
            //java : 字符解码
            traderCustomerVo.setSearchTraderName(java.net.URLDecoder.decode(java.net.URLDecoder.decode(linename, "UTF-8"), "UTF-8"));

            if (traderCustomerVo != null && org.apache.commons.lang.StringUtils.isNotBlank(traderCustomerVo.getSearchTraderName())) {
                Page page = getPageTag(request, pageNo, pageSize);

                Map<String, Object> map = traderCustomerService.getTerminalPageList(traderCustomerVo, page);

                mv.addObject("page", (Page) map.get("page"));
                mv.addObject("terminalList", (List<TraderCustomerVo>) map.get("terminalList"));

            }
            mv.addObject("traderCustomerVo", traderCustomerVo);

            //终端类型
            List<SysOptionDefinition> terminalTypeList = getSysOptionDefinitionList(425);
//			if(JedisUtils.exists(ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + 425)){
//				String json_result = JedisUtils.get(ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + 425);
//				JSONArray jsonArray = JSONArray.fromObject(json_result);
//				terminalTypeList = (List<SysOptionDefinition>) JSONArray.toCollection(jsonArray, SysOptionDefinition.class);
//			}
            mv.addObject("terminalTypeList", terminalTypeList);

            // 省级地区
            List<Region> provinceList = regionService.getRegionByParentId(1);
            mv.addObject("provinceList", provinceList);

            mv.setViewName("trader/customer/list_terminal");
        } catch (Exception e) {
            logger.error("getTerminalList:", e);
        }
        mv.addObject("traderCustomerVo", traderCustomerVo);

        //终端类型
        List<SysOptionDefinition> terminalTypeList = getSysOptionDefinitionList(425);
//		if(JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + 425)){
//			String json_result = JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + 425);
//			JSONArray jsonArray = JSONArray.fromObject(json_result);
//			terminalTypeList = (List<SysOptionDefinition>) JSONArray.toCollection(jsonArray, SysOptionDefinition.class);
//		}
        mv.addObject("terminalTypeList", terminalTypeList);

        // 省级地区
        List<Region> provinceList = regionService.getRegionByParentId(1);
        mv.addObject("provinceList", provinceList);
        mv.setViewName("trader/customer/list_terminal");
        return mv;
    }

    /**
     * <b>Description:</b><br> 搜索客户列表（弹框简单版）
     *
     * @param request
     * @param traderCustomerVo
     * @param pageNo
     * @param pageSize
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年7月7日 下午5:35:54
     */
    @ResponseBody
    @RequestMapping(value = "/searchCustomerList")
    public ModelAndView searchCustomerList(HttpServletRequest request, TraderCustomerVo traderCustomerVo,
                                           @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                           @RequestParam(required = false, defaultValue = "10") Integer pageSize, @RequestParam(required = false) Integer lendOut,
                                           @RequestParam(required = false) Integer traderType,
                                           @RequestParam(required = false) String orderType,
                                           @RequestParam(value="callbackFuntion",required=false) String callbackFuntion) {
        ModelAndView mv = new ModelAndView();
        User user = getSessionUser(request);
        String linename = request.getParameter("searchTraderName");

        mv.addObject("lendOut", lendOut);
        mv.addObject("callbackFuntion", callbackFuntion);
        mv.addObject("traderType", traderType);

        try {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(linename)) {
                //java : 字符解码
                traderCustomerVo.setSearchTraderName((java.net.URLDecoder.decode(java.net.URLDecoder.decode(linename, "UTF-8"), "UTF-8")).trim());
                traderCustomerVo.setCompanyId(user.getCompanyId());
                if (traderCustomerVo.getBelongPlatform() != null && BelongPlatformEnum.JC.getBelong().equals(traderCustomerVo.getBelongPlatform())) {
                    traderCustomerVo.setUserId(user.getUserId());
                }

                if (traderCustomerVo != null && org.apache.commons.lang.StringUtils.isNotBlank(traderCustomerVo.getSearchTraderName())) {
                    Page page = getPageTag(request, pageNo, pageSize);
                    Map<String, Object> map;
                    if (StringUtil.isNotBlank(orderType) && orderType.equals("zxf")) { //直销订单只查询终端用户
                        map = traderCustomerService.getTerminalPageList(traderCustomerVo, page);
                        map.put("searchCustomerList", map.get("terminalList"));
                    } else if (ErpConst.TWO.equals(lendOut)) {
                        // VDERP-15907 【审计支持】样品申请单优化 客户搜索条件变更为 同新增销售订单逻辑
                        traderCustomerVo.setIsEnable(ErpConst.ONE);
                        List<Integer> userIds = userService.getAllSubordinateByUserId(user.getUserId());
                        traderCustomerVo.setUserIdList(userIds);
                        List<TraderCustomerVo> searchCustomerList = traderCustomerService.searchTraderCustomerListPage(traderCustomerVo, page);
                        map = new HashMap<>();
                        map.put("searchCustomerList", searchCustomerList);
                        map.put("page", page);
                    } else {
                        map = traderCustomerService.searchCustomerPageList(traderCustomerVo, page);
                    }
                    mv.addObject("page", (Page) map.get("page"));
                    //外借商品出库搜索
                    if (lendOut != null && (ErpConst.ONE.equals(lendOut) || ErpConst.TWO.equals(lendOut))) {
                        List<TraderCustomerVo> searchCustomerList = (List<TraderCustomerVo>) map.get("searchCustomerList");
                        List<TraderVo> traderList = new ArrayList<TraderVo>();
                        for (TraderCustomerVo t : searchCustomerList) {
                            TraderVo trader = new TraderVo();
                            trader.setTraderId(t.getTraderId());
                            trader.setTraderName(t.getTraderName());
                            trader.setOwner(t.getPersonal());
                            trader.setAddress(t.getAddress());
                            trader.setAddTime(t.getAddTime());
                            trader.setTraderType(1);
                            traderList.add(trader);
                        }
                        mv.addObject("traderList", traderList);
                    } else {
                        mv.addObject("searchCustomerList", (List<TraderCustomerVo>) map.get("searchCustomerList"));

                    }
                }
                mv.addObject("traderCustomerVo", traderCustomerVo);

                // 省级地区
                List<Region> provinceList = regionService.getRegionByParentId(1);
                mv.addObject("provinceList", provinceList);

                String indexId = request.getParameter("indexId");

                mv.addObject("indexId", indexId);
            }
        } catch (Exception e) {
            logger.error("searchCustomerList 客户名称:" + linename + ",错误:", e);
        }
        if (StringUtil.isNotBlank(orderType) && orderType.equals("9")){
            mv.addObject("orderType", "9");
        }

        if (traderCustomerVo.getBelongPlatform() != null && BelongPlatformEnum.JC.getBelong().equals(traderCustomerVo.getBelongPlatform())) {
            mv.setViewName("/order/jc/search_customer_list");
        } else {
            mv.setViewName("trader/customer/search_customer_list");
        }
        return mv;
    }

    /**
     * <b>Description:</b><br> 批量新增客户
     *
     * @param request
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年11月28日 下午1:07:21
     */
    @ResponseBody
    @RequestMapping(value = "/uplodebatchcustomer")
    public ModelAndView uplodeBatchCustomer(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("trader/customer/uplode_batch_customer");
        return mv;
    }

    /**
     * <b>Description:</b><br> 保存批量新增客户
     *
     * @param request
     * @param session
     * @param lwfile
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年11月28日 下午1:16:54
     */
    @ResponseBody
    @RequestMapping("saveuplodebatchcustomer")
    @SystemControllerLog(operationType = "import", desc = "保存批量新增客户")
    public ResultInfo<?> saveUplodeBatchCustomer(HttpServletRequest request, HttpSession session,
                                                 @RequestParam("lwfile") MultipartFile lwfile) {
        ResultInfo<?> resultInfo = new ResultInfo<>();

        FileInputStream fileInputStream = null;
        Workbook workbook = null;
        try {
            User user = (User) session.getAttribute(Consts.SESSION_USER);
            //临时文件存放地址
            String path = request.getSession().getServletContext().getRealPath("/upload/trader");
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);
            if (fileInfo.getCode() == 0) {
                List<Trader> list = new ArrayList<>();
                // 获取excel路径
                fileInputStream = new FileInputStream(new File(fileInfo.getFilePath()));
                workbook = WorkbookFactory.create(fileInputStream);
                // 获取第一面sheet
                Sheet sheet = workbook.getSheetAt(0);
                // 起始行
                int startRowNum = sheet.getFirstRowNum() + 1;
                int endRowNum = sheet.getLastRowNum();// 结束行
                // 防止表格数据第一行为空导致该行的开始列号错误，因此采用表头的第一列作为所有数据的开始列,表头的最后一列作为所有数据的结束列
                Row titleRow = sheet.getRow(sheet.getFirstRowNum());

                for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {// 循环行数
                    Row row = sheet.getRow(rowNum);
                    int startCellNum = titleRow.getFirstCellNum();// 起始列
                    int endCellNum = titleRow.getLastCellNum() - 1;// 结束列
                    // 获取excel的值
                    Trader trader = new Trader();
                    trader.setIsEnable(ErpConst.ONE);

                    TraderCustomer traderCustomer = new TraderCustomer();
                    traderCustomer.setIsEnable(ErpConst.ONE);
                    if (user != null) {
                        trader.setCompanyId(user.getCompanyId());
                        trader.setAddTime(DateUtil.gainNowDate());
                        trader.setCreator(user.getUserId());
                        trader.setModTime(DateUtil.gainNowDate());
                        trader.setUpdater(user.getUserId());

                        traderCustomer.setAddTime(DateUtil.gainNowDate());
                        traderCustomer.setCreator(user.getUserId());
                        traderCustomer.setModTime(DateUtil.gainNowDate());
                        traderCustomer.setUpdater(user.getUserId());
                        trader.setBelongPlatform(userService.getBelongPlatformOfUser(user.getUserId(),user.getCompanyId()));
                    }
                    Integer areaId = 0;
                    String areaIds = "";

                    // 判断客户列表为空 默认首列
                    Cell tempCell = row.getCell(0);
                    if (tempCell == null || tempCell.getCellType() ==  CellType.BLANK) {
                        resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (0 + 1) + "列客户名称不允许为空！");
                        throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (0 + 1) + "列客户名称不允许为空！");
                    }

                    for (int cellNum = startCellNum; cellNum <= 6; cellNum++) {// 循环列数（下表从0开始）--注意---此处的6是根据表格的列数来确定的，表格列数修改此处要跟着修改
                        Cell cell = row.getCell(cellNum);

                        String traderNameRegex = "^[a-zA-Z0-9\\u4e00-\\u9fa5\\.\\(\\)\\,\\（\\）\\s]{2,128}$";
                        if (cellNum == 0) {//客户名称
                            //若excel中无内容，而且没有空格，cell为空--默认3，空白
                            if (cell == null) {
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户名称不允许为空！");
                                throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户名称不允许为空！");
                            } else if (cell.getStringCellValue().toString().length() < 2 || cell.getStringCellValue().toString().length() > 128) {
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户名称长度为2-128个字符长度！");
                                throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户名称长度为2-128个字符长度！");
//                            } else if (!RegexUtil.match(traderNameRegex, cell.getStringCellValue().toString())) {
//                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户名称不允许使用特殊字符！");
//                                throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户名称不允许使用特殊字符！");
                            } else {
                                trader.setTraderName(cell.getStringCellValue().toString());
                            }
                        }
                        if (cellNum == 1) {//省
                            if (cell == null) {
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列省不能为空！");
                                throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列省不能为空！");
                            } else {
                                Region region = new Region();
                                region.setRegionName(cell.getStringCellValue().toString());
                                region.setParentId(100000);
                                Region regionInfo = regionService.getRegion(region);
                                if (null == regionInfo) {
                                    resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列省不存在！");
                                    throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列省不存在！");
                                } else {
                                    areaId = regionInfo.getRegionId();
                                    areaIds = regionInfo.getRegionId().toString();
                                }
                            }
                        }
                        if (cellNum == 2) {//市
                            if (cell == null) {
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列市不能为空！");
                                throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列市不能为空！");
                            } else {
                                Region region = new Region();
                                region.setRegionName(cell.getStringCellValue().toString());
                                region.setParentId(areaId);
                                Region regionInfo = regionService.getRegion(region);
                                if (null == regionInfo) {
                                    resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列市不存在！");
                                    throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列市不存在！");
                                } else {
                                    areaId = regionInfo.getRegionId();
                                    areaIds += "," + regionInfo.getRegionId().toString();
                                }
                            }
                        }
                        if (cellNum == 3) {//区
                            if(cell == null){
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列区/县不能为空！");
                                throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列区/县不能为空！");
                            }
                            if (cell != null) {
                                Region region = new Region();
                                region.setRegionName(cell.getStringCellValue().toString());
                                region.setParentId(areaId);
                                Region regionInfo = regionService.getRegion(region);
                                if (null == regionInfo) {
                                    resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列区不存在！");
                                    throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列区不存在！");
                                } else {
                                    areaId = regionInfo.getRegionId();
                                    areaIds += "," + regionInfo.getRegionId().toString();
                                }
                            }
                        }

                        if (cellNum == 4) {//客户类型
                            if (cell == null) {
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户类型不允许为空！");
                                throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户类型不允许为空！");
                            } else {
                                if (!cell.getStringCellValue().toString().equals("临床医疗")
                                        && !cell.getStringCellValue().toString().equals("科研医疗")) {
                                    resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户类型错误！");
                                    throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户类型错误！");
                                } else {
                                    Integer customerType = 0;
                                    String customerTypeString = cell.getStringCellValue().toString();
                                    switch (customerTypeString) {
                                        case "临床医疗":
                                            customerType = 427;
                                            break;
                                        case "科研医疗":
                                            customerType = 426;
                                            break;
                                    }
                                    traderCustomer.setCustomerType(customerType);
                                }
                            }
                        }
                        if (cellNum == 5) {//客户性质
                            if (cell == null || cell.getCellType() != CellType.STRING) {
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户性质不允许为空！");
                                throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户性质不允许为空！");
                            } else {
                                if (!cell.getStringCellValue().toString().equals("分销")
                                        && !cell.getStringCellValue().toString().equals("终端")) {
                                    resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户性质错误！");
                                    throw new Exception("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列客户性质错误！");
                                } else {
                                    Integer customerNature = 0;
                                    String customerNatureString = cell.getStringCellValue().toString();
                                    switch (customerNatureString) {
                                        case "分销":
                                            customerNature = 465;
                                            break;
                                        case "终端":
                                            customerNature = 466;
                                            break;
                                    }
                                    traderCustomer.setCustomerNature(customerNature);
                                }
                            }
                        }
                    }

                    Integer traderCustomerCategoryId = 0;
                    if (traderCustomer.getCustomerType().equals(426)) {
                        switch (traderCustomer.getCustomerNature()) {
                            case 465://科研分销
                                traderCustomerCategoryId = 3;
                                break;
                            case 466://科研终端
                                traderCustomerCategoryId = 4;
                                break;
                        }
                    }
                    if (traderCustomer.getCustomerType().equals(427)) {
                        switch (traderCustomer.getCustomerNature()) {
                            case 465://临床分销
                                traderCustomerCategoryId = 5;
                                break;

                            case 466://临床终端
                                traderCustomerCategoryId = 6;
                                break;
                        }
                    }

                    traderCustomer.setTraderCustomerCategoryId(traderCustomerCategoryId);
                    trader.setAreaId(areaId);
                    trader.setAreaIds(areaIds);
                    trader.setTraderType(1);
                    trader.setTraderCustomer(traderCustomer);
                    list.add(trader);
                }

                //保存更新
                resultInfo = traderCustomerService.saveUplodeBatchCustomer(list,user);
            }
        } catch (Exception e) {
            logger.warn("saveuplodebatchcustomer:", e);
            return resultInfo;
        }finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (Exception e) {
                    logger.error("【saveUplodeBatchCustomer】处理异常",e);
                }
            }

            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    logger.error("【saveUplodeBatchCustomer】处理异常",e);
                }
            }
        }
        return resultInfo;
    }


    /**
     * <b>Description:</b><br>
     * 客户审核弹层页面
     *
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年11月10日 下午1:39:42
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/complement")
    public ModelAndView complement(HttpSession session, Integer traderCustomerId, String taskId, Boolean pass, Integer type) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("taskId", taskId);
        mv.addObject("pass", pass);
        mv.addObject("traderCustomerId", traderCustomerId);
        mv.addObject("type", type);
        mv.setViewName("trader/customer/complement");
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 客户审核操作
     *
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年11月10日 下午1:39:42
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/complementTask")
    @SystemControllerLog(operationType = "edit", desc = "客户审核操作")
    public ResultInfo<?> complementTask(HttpServletRequest request, Integer traderCustomerId, String taskId, String comment, Boolean pass,
                                        HttpSession session) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("pass", pass);
        variables.put("updater", user.getUserId());
        try {
            //如果审核没结束添加审核对应主表的审核状态
            Integer status = 0;
            if (pass) {
                //如果审核通过
                status = 0;
            } else {
                //如果审核不通过
                status = 2;
                verifiesRecordService.saveVerifiesInfo(taskId, status);
            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
            //如果未结束添加审核对应主表的审核状态
            if (complementStatus != null && complementStatus.getData() != null && !complementStatus.getData().equals("endEvent")) {
                verifiesRecordService.saveVerifiesInfo(taskId, status);
            }
            if(pass) {
                // add by Randy.Xu 2021/1/7 17:36 .Desc: VDERP-4927 质量报表添加客户最近一次通过审核的时间. begin
                tmpTraderValidtimeExtService.updateTraderCustomerValidTIme(traderCustomerId);
                // add by Randy.Xu 2021/1/7 17:36 .Desc: VDERP-4927 质量报表添加客户最近一次通过审核的时间. end
            }
            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("complementTask:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    /**
     * <b>Description:</b><br> 模糊查询客户列表
     *
     * @param request
     * @param session
     * @param traderCustomer
     * @return
     * @Note <b>Author:</b> scott
     * <br><b>Date:</b> 2018年1月11日 上午9:08:35
     */
    @ResponseBody
    @RequestMapping(value = "/queryEyeCheck")
    public ModelAndView queryEyeCheck(HttpServletRequest request, HttpSession session, TraderCustomerVo traderCustomerVo, Integer type, String traderName) {
        ModelAndView mv = new ModelAndView();
        ResultInfo<List<TraderCustomerVo>> resultInfo = new ResultInfo<List<TraderCustomerVo>>();
        List<TraderCustomerVo> list = new ArrayList<TraderCustomerVo>();

        logger.info("queryEyeCheck调用天眼查API，公司名称：{}", traderName);
        String result = httpSendUtil.queryDetails(type, traderName.replaceAll("\\s+",""));
        JSONObject jsonObject = JSONObject.fromObject(result);
        String code = jsonObject.getString("error_code");
        if (!"0".equals(code)) {
            mv.addObject("code", code);
            mv.setViewName("trader/customer/list_traderCustomers");
        } else {
            JSONObject json = JSONObject.fromObject(jsonObject.get("result"));
            JSONArray jsonArray = JSONArray.fromObject(json.get("items"));
            for (int i = 0; i < jsonArray.size(); i++) {
                TraderCustomerVo tv = new TraderCustomerVo();
                tv.setTraderName(jsonArray.getJSONObject(i).getString("name").replaceAll("<em>", "").replaceAll("</em>", ""));
                list.add(tv);
            }
            mv.addObject("TraderCustomerVoList", list);
            mv.setViewName("trader/customer/list_traderCustomers");
        }
        return mv;
    }

    @ResponseBody
    @RequestMapping("tyc/check")
    public ResultInfo<TraderInfoTyc> getTraderInfoTyc(String traderName){
        logger.info("getTraderInfoTyc调用天眼查API，公司名称：{}", traderName);
        TraderInfoTyc tycInfo = traderCustomerService.getTycInfo(2, traderName);
        return new ResultInfo<>(0,"操作成功",tycInfo);
    }

    /**
     * <b>Description:</b><br> 天眼查客户的详情
     *
     * @param request
     * @param session
     * @param traderCustomerVo
     * @param traderName
     * @return
     * @Note <b>Author:</b> scott
     * <br><b>Date:</b> 2018年1月12日 上午9:51:00
     */
    @ResponseBody
    @RequestMapping(value = "/eyeCheckInfo")
    public ResultInfo eyeCheckInfo(HttpServletRequest request, HttpSession session, TraderCustomerVo traderCustomerVo, String traderName) {
        logger.info("eyeCheckInfo调用天眼查API，公司名称：{}", traderName);
        //返回天眼查详情
        TraderInfoTyc tycInfo = traderCustomerService.getTycInfo(2, traderName);
        ResultInfo res = null;
        if (tycInfo == null) {
            res = new ResultInfo<>(-1, "接口错误");
        } else {
            if (tycInfo.getCodeType() == 2) {
                res = new ResultInfo<>(2, "没有查询到" + traderName + "的信息");
            } else if (tycInfo.getCodeType() == 3) {
                res = new ResultInfo<>(3, "余额不足");
            } else {
                res = new ResultInfo<>(1, "操作成功");
            }
            res.setData(traderName);
        }
        return res;
    }

    /**
     * <b>Description:</b><br> 客户重置为待审核
     *
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2018年1月23日 上午11:49:04
     */
    @ResponseBody
    @RequestMapping(value = "/restverify")
    @SystemControllerLog(operationType = "edit", desc = "客户重置为待审核")
    public ResultInfo restVerify(HttpServletRequest request, HttpSession session, TraderCustomer traderCustomer) {
        if (null == traderCustomer || traderCustomer.getTraderCustomerId() == null) {
            return new ResultInfo<>();
        }

        ResultInfo resultInfo = traderCustomerService.restVerify(traderCustomer);
        return resultInfo;
    }

    /**
     * <b>Description:</b><br> 交易记录
     *
     * @param request
     * @param buyorderGoodsVo
     * @param traderSupplierVo
     * @param pageNo
     * @param pageSize
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2018年1月25日 下午2:45:14
     */
    @ResponseBody
    @RequestMapping(value = "/businesslist")
    public ModelAndView businessList(HttpServletRequest request, SaleorderGoodsVo saleorderGoodsVo, TraderCustomerVo traderCustomerVo,
                                     @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                     @RequestParam(required = false) Integer pageSize) {
        ModelAndView mv = new ModelAndView();
        User curr_user = getSessionUser(request);
        if(curr_user==null){
            return new ModelAndView("redirect:/login.do");
        }
        String username = curr_user.getUsername();

        if (saleorderGoodsVo.getTraderId() == null) {
            logger.error("saleorderGoodsVo.getTraderId()==null",request.getHeader("REFERER"));
            return pageNotFound(request);
        }
//        List<String> usernames = Arrays.asList(jdUploadUsername.split(","));
//        List<String> tradeIds = Arrays.asList(jdUploadTradeId.split(","));
//        if (tradeIds.contains(saleorderGoodsVo.getTraderId().toString())&&usernames.contains(username)){
//            mv.addObject("uploadEnable",true);
//        }
        traderCustomerVo.setBelongPlatform(traderMapper.getTraderByTraderId(traderCustomerVo.getTraderId()).getBelongPlatform());
        mv.setViewName("trader/customer/bussiness_list");
        mv.addObject("method", "saleorder");
        mv.addObject("saleorderGoodsVo", saleorderGoodsVo);
        mv.addObject("traderCustomer", traderCustomerVo);
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "/getAccountSaler", method = RequestMethod.POST)
    public ResultInfo getAccountSaler(HttpServletRequest request, @RequestBody ReqVo rv) {
        List<Integer> traderIdList = (List<Integer>) rv.getReq();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(traderIdList)) {
            List<AccountSalerToGo> accountSalerToGos = traderCustomerService.getAccountSaler(traderIdList);
            return new ResultInfo(0, "返回数据成功", JSON.toJSON(accountSalerToGos));
        }
        return new ResultInfo(-1, "获取失败");
    }

    /**
     * @param @param traderMjxContactAdderss
     * @return ResultInfo    返回类型
     * @throws
     * @Title: saveMjxContactAdders
     * @Description: TODO(同步MJX地址)
     * <AUTHOR>
     * @date 2019年8月20日
     */
    @ResponseBody
    @RequestMapping(value = "/saveMjxContactAdders")
    public ResultInfo saveMjxContactAdders(@RequestBody() TraderMjxContactAdderss traderMjxContactAdderss) {
        ResultInfo resultInfo = new ResultInfo<>();
        if (StringUtils.isEmpty(traderMjxContactAdderss.getPhone())) {
            resultInfo.setCode(-1);
            resultInfo.setMessage("注册手机号为空");
            return resultInfo;
        }
        resultInfo = traderCustomerService.saveMjxContactAdders(traderMjxContactAdderss);
        logger.info("saveMjxContactAdders:" + resultInfo);
        return resultInfo;
    }

//    /**
//     * <b>Description:</b>初始化客户资质审核流程<br>
//     * @param request 请求信息
//     * @return
//     * @Note
//     * <b>Author:calvin</b>
//     * <br><b>Date:</b> ${date} ${time}
//     */
//    @ResponseBody
//    @RequestMapping(value = "/initCustomerAptitudeCheck")
//    public ResultInfo initCustomerAptitudeCheck(HttpServletRequest request){
//        Page page=getPageTag(request,1,30);
//        return traderCustomerService.initCustomerAptitudeCheck(request,page);
//    }

    @ResponseBody
    @RequestMapping(value = "/recallChangeAptitude")
    public ResultInfo recallChangeAptitude(String taskId, Integer traderCustomerId) {
        if (traderCustomerId == null || StringUtil.isBlank(taskId)) {
            return new ResultInfo(-1, "客户标识或审核流程标识为空");
        }
        ResultInfo resultInfo = getAptitudeStatus(traderCustomerId);
        if (resultInfo != null || resultInfo.getData() != null) {
            Integer status = (Integer) resultInfo.getData();
            if (status != null && status != 0 && status != 4 && status != 5) {
                return new ResultInfo(-1, "客户资质审核状态已发生改变，请刷新页面");
            }
        }

        Trader trader=traderCustomerService.getTraderByCustomerId(traderCustomerId);
        ResultInfo resultUpdateTable = verifiesRecordService.saveVerifiesInfoForTrader(taskId, 3);
        ResultInfo resultDeleteInstance = actionProcdefService.deleteProcessInstance(taskId);
        if (resultUpdateTable.getCode() == 0 && resultDeleteInstance.getCode() == 0) {
            sendMsgIfAptitudeStatusChange(trader.getTraderId(),-1,trader.getBelongPlatform());
            //删除质量部客户资质待审核待办事项
            zlbCheckTraderCustomerCertificate.delete(traderCustomerId);
            return resultDeleteInstance;
        }
        return new ResultInfo(-1, "操作失败");
    }


    @ResponseBody
    @RequestMapping(value = "/getAptitudeStatus")
    public ResultInfo getAptitudeStatus(Integer traderCustomerId) {
        VerifiesInfo verifiesInfo = traderCustomerService.getCustomerAptitudeVerifiesInfo(traderCustomerId);
        Integer aptitudeStatus = -3;
        if (verifiesInfo == null || verifiesInfo.getStatus() == null) {
            aptitudeStatus = -3;
        } else {
            aptitudeStatus = verifiesInfo.getStatus();
        }
        ResultInfo<Integer> resultInfo = new ResultInfo<>(0, "操作成功");
        resultInfo.setData(aptitudeStatus);
        return resultInfo;
    }


    /**
     * <b>Description:</b>判断提交前后资质状态是否相同<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/6/28
     */
    @RequestMapping(value = "/isAptitudeStatusSame")
    @ResponseBody
    public ResultInfo isAptitudeStatusSame(Integer traderCustomerId, Integer aptitudeStatus) {
        if(traderCustomerId==null||traderCustomerId==0){
            return new ResultInfo(-1, "找不到客户ID"+traderCustomerId);
        }
        VerifiesInfo verifiesInfo = traderCustomerService.getCustomerAptitudeVerifiesInfo(traderCustomerId);
        if (verifiesInfo == null || verifiesInfo.getStatus() == null) {
            return new ResultInfo(-1, "资质审核状态异常，无法审核");
        }
        if (!verifiesInfo.getStatus().equals(aptitudeStatus)) {
            return new ResultInfo(-1, "资质状态发生变化，请刷新页面重新操作");
        }
        return new ResultInfo(0, "操作成功");
    }

    @ResponseBody
    @RequestMapping(value = "/isCanCheckAptitude")
    public ResultInfo canCheckAptitude(Integer traderCustomerId) {
        if(traderCustomerId==null||traderCustomerId==0){
            return new ResultInfo(-1, "找不到客户ID"+traderCustomerId);
        }
        VerifiesInfo verifiesInfo = traderCustomerService.getCustomerAptitudeVerifiesInfo(traderCustomerId);
        if (verifiesInfo == null || verifiesInfo.getStatus() == null) {
            return new ResultInfo(-1, "资质审核状态异常，无法审核");
        }
        if (verifiesInfo.getStatus() == 3) {
            return new ResultInfo(-1, "销售已更新资质信息，请刷新页面重新操作");
        }
        if (verifiesInfo.getStatus() == 0||verifiesInfo.getStatus()==4 ||verifiesInfo.getStatus()==5) {
            return new ResultInfo(0, "操作成功");
        }
        return new ResultInfo(-1, "资质审核状态异常，无法审核");
    }

    /**
     * <b>Description:</b>同步医械购平台客户资质审核结果<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/3/19
     */
    @ResponseBody
    @RequestMapping(value = "/aptitude/status", method = RequestMethod.PUT)
    public ResultInfo syncYxgAptitudeStatus(@RequestBody() YXGTraderAptitude yxgTraderAptitude) {
        logger.info("同步医械购客户资质状态：{}",yxgTraderAptitude.toString());
        if(yxgTraderAptitude==null||yxgTraderAptitude.getTraderId()==null
                ||yxgTraderAptitude.getStatus()==null){
            return new ResultInfo(-1,"操作失败，客户标识或审核状态为空");
        }
        Integer status=yxgTraderAptitude.getStatus()==3?-1:yxgTraderAptitude.getStatus();
        sendMsgIfAptitudeStatusChange(yxgTraderAptitude.getTraderId(),status,2);
        return traderCustomerService.syncYxgTraderStatus(yxgTraderAptitude);
    }

    /**
     * <b>Description:</b>同步医械购审核状态(代付款证明)到erp<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/10/22
     */
    @ResponseBody
    @RequestMapping(value = "/yxg/status/sync",method = RequestMethod.POST)
    public ResultInfo syncYxgTraderStatus(@RequestBody YXGTraderAptitude yxgTraderAptitude){
        return traderCustomerService.syncYxgCustomerStatus(yxgTraderAptitude);
    }
    /**
     * @description: 搜索客户列表
     * @return:
     * @author: Strange
     * @date:  2020/6/5
     **/
    @ResponseBody
    @RequestMapping(value = "/searchCustomer")
    public ResultInfo searchCustomer(HttpServletRequest request, TraderCustomerVo traderCustomerVo,
                                           @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                           @RequestParam(required = false, defaultValue = "15") Integer pageSize,
                                           @RequestParam(required = false) Integer traderType) {
        ResultInfo resultInfo = new ResultInfo();
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        try {
            List<TraderCustomerVo> searchCustomerList = new ArrayList<>();
            String linename = request.getParameter("searchTraderName");
            if(linename != null) {
                //java : 字符解码
                traderCustomerVo.setSearchTraderName((java.net.URLDecoder.decode(java.net.URLDecoder.decode(linename, "UTF-8"), "UTF-8")).trim());
            }
            traderCustomerVo.setCompanyId(1);
            if (traderCustomerVo != null && (org.apache.commons.lang.StringUtils.isNotBlank(traderCustomerVo.getSearchTraderName()))|| traderCustomerVo.getTraderId()!= null) {
                Page page = getPageTag(request, pageNo, pageSize);
                Map<String, Object> map = traderCustomerService.searchNewCustomerPageList(traderCustomerVo, page);
                searchCustomerList = (List<TraderCustomerVo>) map.get("searchCustomerList");
                //设置是否是当前销售归属客户
                traderCustomerService.setIsBelong(searchCustomerList,user);
                //客户联系人
                if(!CollectionUtils.isEmpty(searchCustomerList)) {
                    for (TraderCustomerVo customerVo : searchCustomerList) {
                        StringBuffer sb = new StringBuffer();
                        //获取客户账期余额
                        TraderCustomerVo traderCustomerInfo = traderCustomerService.getTraderCustomerInfo(customerVo.getTraderId());
                        List<TraderCustomerCategory> customerCategories = traderCustomerInfo.getCustomerCategories();
                        if(!CollectionUtils.isEmpty(customerCategories)){
                            for (TraderCustomerCategory customerCategory : customerCategories) {
                               sb = sb.append(customerCategory.getCustomerCategoryName()).append(" ");
                            }
                        }else{
                            sb = sb.append(traderCustomerInfo.getCustomerTypeStr()).append(" ").append(traderCustomerInfo.getCustomerNatureStr());
                        }
                        customerVo.setCustomerTypeStr(sb.toString());
                        customerVo.setAccountPeriodLeft(traderCustomerInfo.getAccountPeriodLeft());
                        TraderContact traderContact = new TraderContact();
                        traderContact.setTraderId(customerVo.getTraderId());
                        traderContact.setIsEnable(1);
                        List<TraderContact> traderContactList = traderCustomerService.getTraderContact(traderContact);
                        if (!CollectionUtils.isEmpty(traderContactList)){
                            traderContactList.stream().forEach(traderContacter -> {
                                if (traderContacter.getIsDefault() != null && traderContacter.getIsDefault() == 1){
                                    customerVo.setTraderContactName(traderContacter.getName());
                                }
                            });
                        }
                        customerVo.setTraderContactList(traderContactList);
                    }
                }
                resultInfo.setData(searchCustomerList);
            }else{
                resultInfo.setData(searchCustomerList);
            }
            resultInfo.setMessage("查询成功");
            resultInfo.setCode(0);
        } catch (Exception e) {
            logger.error("searchCustomerList:", e);
        }
        return resultInfo;
    }

    @ResponseBody
    @RequestMapping(value = "addYxgTrader",method = RequestMethod.POST)
    public ResultInfo addYxgTraderByName(@RequestBody() TraderAccountInfo traderInfoVo){
        if (traderInfoVo == null || traderInfoVo.getSsoAccountId() == null
                || org.apache.commons.lang3.StringUtils.isBlank(traderInfoVo.getTraderName())) {
            return new ResultInfo(-1, "必要参数不得为空");
        }
        return traderCustomerService.addYxgTraderByName(traderInfoVo);
    }


    /**
     * 根据客户编号获取客户信息（含帐期信息）
     *
     * @param traderId
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, value = "/getCustomerSettlementInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @NoNeedAccessAuthorization
    public ResultInfo getCustomerInfo(Integer traderId) {
        if (traderId == null || traderId <= 0) {
            return ResultInfo.error("客户编号为空");
        }
        TraderCustomerVo customerInfo = traderCustomerService.getCustomerInfo(traderId);
        if (customerInfo == null) {
            return ResultInfo.error("查询客户信息失败");
        }
        return ResultInfo.success(customerInfo);
    }

    @ResponseBody
    @RequestMapping(value = "/isAddressStick")
    public ResultInfo isAddresstStick(TraderAddress traderAddress, HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        traderAddress.setModTime(System.currentTimeMillis());
        traderAddress.setUpdater(user.getUserId());
        Integer ri = traderCustomerService.isAddresstStick(traderAddress);
        if (ri > 0){
            return ResultInfo.success();
        }
        return ResultInfo.error();
    }

    @ResponseBody
    @RequestMapping(value = "/isContactStick")
    public ResultInfo isContactStick(TraderContact traderContact, HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        traderContact.setModTime(System.currentTimeMillis());
        traderContact.setUpdater(user.getUserId());
        Integer ri = traderCustomerService.isContactStick(traderContact);
        if (ri > 0){
            return ResultInfo.success();
        }
        return ResultInfo.error();
    }

    /**
     * <b>Description:</b><br>
     * 正式账期申请
     *
     * @param traderCustomer, session
     * @return org.springframework.web.servlet.ModelAndView
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/7/27 14:51
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/accountFormalapply")
    @NoNeedAccessAuthorization
    public ModelAndView accountFormalapply(TraderCustomer traderCustomer,HttpSession session) {
        ModelAndView mv = new ModelAndView();
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        List<CustomerBillPeriodApply> applyListInChecking = customerBillPeriodApplyService.getCustomerBillPeriodApplyList(1,
                Long.valueOf(traderCustomer.getTraderCustomerId())).stream()
                .filter(item -> item.getCheckStatus() == 0)
                .collect(Collectors.toList());
        if(applyListInChecking.size() > 0){
            //存在有未审核完成的账期，不可继续申请
            mv.addObject("message","需待现有未审核完成的账期审核结束后，才可进行新的申请/修改。");
            return fail(mv);
        }
        mv.setViewName("trader/customer/apply_formal_accountperiod");
        //调用接口查询各个类型账期的简要信息
        List<CustomerBillPeriodSummaryInfoDto> customerBillPeriodSummaryInfoDtos = customerBillPeriodService.getCustomerBillPeriodSummaryInfo(1,
                Long.valueOf(traderCustomer.getTraderCustomerId()));
        if(customerBillPeriodSummaryInfoDtos != null){
            for(CustomerBillPeriodSummaryInfoDto customerBillPeriodSummaryInfo : customerBillPeriodSummaryInfoDtos){
                if(customerBillPeriodSummaryInfo.getBillPeriodType() == 1){
                    //正式账期的简要信息返回前台展示
                    mv.addObject("customerBillPeriodSummaryInfoDto",customerBillPeriodSummaryInfo);
                }
            }
        }else {
            mv.addObject("customerBillPeriodSummaryInfoDto",null);
        }
        //调用接口查询是否存在生效的正式账期
        List<CustomerBillPeriodDetailsDto> customerBillPeriods = customerBillPeriodService.getCustomerBillPeriodDetailsByType(1,Long.valueOf(traderCustomer.getTraderCustomerId()),1);
        if(!CollectionUtils.isEmpty(customerBillPeriods)){
            mv.addObject("customerBillPeriod", customerBillPeriods.get(0));
        }else {
            mv.addObject("customerBillPeriod", null);
        }
        mv.addObject("nowTime",DateUtil.getNowDayMillisecond());
        mv.addObject("traderCustomer",traderCustomer);
        return mv;
    }


    /**
     * 修改申请账期页面
     *
     * @param applyId
     * @param customerId
     * @param session
     * @return
     */
    @NoNeedAccessAuthorization
    @RequestMapping(method = RequestMethod.GET, value = "/modifyAccountPeriodApply")
    public ModelAndView modifyAccountPeroid(Long applyId, Integer customerId ,Integer billPeriodType) {
        TraderCustomer traderCustomerQuery = traderCustomerService.selectByPrimaryKey(customerId);

        CustomerBillPeriodTypeEnum typeEnum = CustomerBillPeriodTypeEnum.getTypeEnumByCode(billPeriodType);

        final String viewToUse;
        if(CustomerBillPeriodTypeEnum.OFFICIAL == typeEnum) {
            viewToUse="trader/customer/apply_formal_verify_change";
        } else if (CustomerBillPeriodTypeEnum.ORDER == typeEnum) {
            viewToUse="trader/customer/apply_order_verify_change";
        }else  if(CustomerBillPeriodTypeEnum.TEMPORARY == typeEnum){
            viewToUse="trader/customer/apply_temp_verify_change";
        }else {
            throw new IllegalStateException();
        }

        ModelAndView mv = new ModelAndView(viewToUse);
        //调用接口查询账期申请信息
        CustomerBillPeriodApply customerBillPeriodApply = customerBillPeriodApplyService.selectByPrimaryKey(applyId);

        if(CustomerBillPeriodTypeEnum.OFFICIAL == typeEnum) {
            //正式账期
            //调用接口查询各个类型账期的简要信息
            List<CustomerBillPeriodSummaryInfoDto> customerBillPeriodSummaryInfoDtos = customerBillPeriodService.getCustomerBillPeriodSummaryInfo(1,
                    Long.valueOf(customerId));
            if(customerBillPeriodSummaryInfoDtos != null){
                for(CustomerBillPeriodSummaryInfoDto customerBillPeriodSummaryInfo : customerBillPeriodSummaryInfoDtos){
                    if(customerBillPeriodSummaryInfo.getBillPeriodType() == 1){
                        //正式账期的简要信息返回前台展示
                        mv.addObject("customerBillPeriodSummaryInfoDto",customerBillPeriodSummaryInfo);
                    }
                }
            }else {
                mv.addObject("customerBillPeriodSummaryInfoDto",null);
            }
        }else if(CustomerBillPeriodTypeEnum.TEMPORARY == typeEnum){
            //临时账期
            CustomerBillPeriod customerBillPeriod = customerBillPeriodService.selectByprimaryKey(customerBillPeriodApply.getBillPeriodId());
            mv.addObject("customerBillPeriodSummaryInfoDto",customerBillPeriod);
        }else if(CustomerBillPeriodTypeEnum.ORDER == typeEnum){
            //订单账期
            if(customerBillPeriodApply.getRelatedOrderId() != null && customerBillPeriodApply.getRelatedOrderId() != 0){
                Saleorder saleorder = saleorderService.getSaleOrderById(customerBillPeriodApply.getRelatedOrderId());
                mv.addObject("saleorderNo",saleorder.getSaleorderNo());
            }
            CustomerBillPeriod customerBillPeriod = customerBillPeriodService.selectByprimaryKey(customerBillPeriodApply.getBillPeriodId());
            mv.addObject("customerBillPeriodSummaryInfoDto",customerBillPeriod);
        }
        mv.addObject("customerBillPeriod", customerBillPeriodApply);
        mv.addObject("nowTime",DateUtil.getNowDayMillisecond());
        mv.addObject("traderCustomer", traderCustomerQuery);
        return mv;
    }


    /**
     * VDERP-7303
     * 客户信用记录
     * @param traderCustomer
     * @return
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/customerCreditHistory")
    public ModelAndView customerCreditHistory(TraderCustomer traderCustomer) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("trader/customer/customerCreditHistory");
        //调用接口查询各个类型账期的简要信息
        Integer companyId = ErpConst.NJ_COMPANY_ID;
        Integer customerId = traderCustomer.getTraderCustomerId();
        Integer traderId = traderCustomer.getTraderId();
        if (null == customerId || null == traderId){
            return null;
        }

        mv.addObject("traderId",traderId);
        List<CustomerBillPeriodSummaryInfoDto> customerBillPeriodSummaryInfoDtos =
                customerBillPeriodService.getCustomerBillPeriodSummaryInfo(companyId,Long.valueOf(customerId));

        if (!CollectionUtils.isEmpty(customerBillPeriodSummaryInfoDtos)){
            //正式账期
            List<CustomerBillPeriodSummaryInfoDto> billInfoListFormal = customerBillPeriodSummaryInfoDtos.stream().filter(s->Objects.nonNull(s)).filter(t->CustomerBillPeriodTypeEnum.OFFICIAL.getCode().equals(t.getBillPeriodType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(billInfoListFormal)){
                mv.addObject("billInfoListFormal", billInfoListFormal.get(0));
            }
            //临时账期
            List<CustomerBillPeriodSummaryInfoDto> billInfoListTemporary = customerBillPeriodSummaryInfoDtos.stream().filter(s->Objects.nonNull(s)).filter(t->CustomerBillPeriodTypeEnum.TEMPORARY.getCode().equals(t.getBillPeriodType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(billInfoListTemporary)){
                mv.addObject("billInfoListTemporary", billInfoListTemporary.get(0));
            }
            //订单账期
            List<CustomerBillPeriodSummaryInfoDto> billInfoListOrder = customerBillPeriodSummaryInfoDtos.stream().filter(s->Objects.nonNull(s)).filter(t->CustomerBillPeriodTypeEnum.ORDER.getCode().equals(t.getBillPeriodType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(billInfoListOrder)){
                mv.addObject("billInfoListOrder", billInfoListOrder.get(0));
            }
        }

        TraderCertificateVo traderFinance = new TraderCertificateVo();
        traderFinance.setTraderId(traderId);
        traderFinance.setTraderType(ErpConst.ONE);
        traderFinance.setCreator(ErpConst.ZERO);
        try {
            Map<String, Object> map = traderCustomerService.getNewFinanceAndAptitude(traderFinance);
            if (map.containsKey("finance")) {
                JSONObject json = JSONObject.fromObject(map.get("finance"));
                TraderFinanceVo tf = (TraderFinanceVo) JSONObject.toBean(json, TraderFinanceVo.class);
                mv.addObject("finance", tf);
            }
        } catch (Exception ex){
            logger.error("TraderCustomerController->customerCreditHistory traderCustomer: {}",traderCustomer);
        }

        return mv;
    }

    /**
     * VDERP-7304
     * 申请人/部门信用记录页面开发
     * @param traderCustomer
     * @return
     */
//    @FormToken(save = true)
//    @ResponseBody
//    @RequestMapping(value = "/applicantDepartment")
//    public ModelAndView applicantDepartment(TraderCustomer traderCustomer,HttpServletRequest request,
//                                            @RequestParam(required = false, value="startTime") String startTime,
//                                            @RequestParam(required = false, value="endTime") String endTime) {
//        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
//        ModelAndView mv = new ModelAndView();
//        mv.setViewName("trader/customer/applicantDepartment");
//        mv.addObject("startTime", startTime);mv.addObject("endTime", endTime);
//
//        if (null==traderCustomer){
//            return mv;
//        }
//        //通过 traderCustomerId 查询归属销售
//        if (ObjectUtils.isEmpty(traderCustomer.getTraderId())){
//            logger.warn("TraderCustomerController-> applicantDepartment,traderId= {}", traderCustomer.getTraderId());
//            return mv;
//        }
//
//        User userApply = orgService.getTraderUserAndOrgByTraderId(traderCustomer.getTraderId(), ErpConst.ONE);// 1客户，2供应商
//        if (null == userApply){
//            logger.warn("TraderCustomerController-> applicantDepartment,traderId= {}", traderCustomer.getTraderId());
//            return mv;
//        }
//        //当前申请人
//        String saleName = userApply.getUsername()==null?"":userApply.getUsername();
//        mv.addObject("saleName", saleName);
//        //申请人部门名
//        //String orgName = userApply.getOrgName()==null?"":userApply.getOrgName();
//
//        //归属部门,查询一级/二级/三级，否则当前部门
//        Integer orgId = userApply.getOrgId();
//        Integer saleId = userApply.getUserId();
//        Integer orgFId = user.getOrgId();
//
//        if (ObjectUtils.notEmpty(saleId)){
//            //申请人信息 列表
//            CustomerBillPeriodCreditHistoryQueryDto queryDto = new CustomerBillPeriodCreditHistoryQueryDto();
//            queryDto.setCompanyId(ErpConst.NJ_COMPANY_ID);
//            queryDto.setBillPeriodAppliers(Collections.singletonList(saleId));
//            if(StringUtil.isNotBlank(startTime)){
//                queryDto.setStartTime(DateUtil.convertLong(startTime + " 00:00:00",""));
//            }
//            if(StringUtil.isNotBlank(endTime)){
//                queryDto.setEndTime(DateUtil.convertLong(endTime + " 23:59:59",""));
//            }
//
//            List<CustomerBillPeriodCreditHistoryDto> customerBillList = customerBillPeriodService.getCustomerBillPeriodCreditHistoryByCreators(queryDto);
//            if (!CollectionUtils.isEmpty(customerBillList)) {
//                //申请人 合计
//                CustomerBillPeriodCreditHistoryDto applyCount = addTotal(customerBillList);
//                mv.addObject("appCount", applyCount);
//                List<BigDecimal> appCountReal = new ArrayList<>();
//                List<BigDecimal> appCountHistoryReal = new ArrayList<>();
//
//                //正式账期
//                List<CustomerBillPeriodCreditHistoryDto> applyFormal = customerBillList.stream().filter(s -> null != s && ObjectUtils.notEmpty(s.getBillPeriodType()) && CustomerBillPeriodTypeEnum.OFFICIAL.getCode().equals(s.getBillPeriodType())).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(applyFormal)) {
//                    mv.addObject("applyFormal", applyFormal.get(ErpConst.ZERO));
//                    //获取正式账期订单实际总金额
//                    BigDecimal formalReal = applyFormal.stream().filter(s -> null != s.getOrderIdList())
//                            .map(s -> traderCustomerService.getRealTotalAmount(s.getOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    mv.addObject("applyFormalReal", formalReal);
//                    appCountReal.add(formalReal);
//                    //获取正式账期历史账期订单总金额
//                    BigDecimal formalHistoryReal = applyFormal.stream().filter(s -> null != s.getHistoryOrderIdList())
//                            .map(s -> traderCustomerService.getRealTotalAmount(s.getHistoryOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    mv.addObject("applyFormalHistoryReal", formalHistoryReal);
//                    if(formalHistoryReal.compareTo(BigDecimal.ZERO) == 0){
//                        mv.addObject("applyFormalHistoryReal", null);
//                    }
//                    appCountHistoryReal.add(formalHistoryReal);
//                }
//
//                //临时账期
//                List<CustomerBillPeriodCreditHistoryDto> applyTemporary = customerBillList.stream().filter(s -> null != s && ObjectUtils.notEmpty(s.getBillPeriodType()) && CustomerBillPeriodTypeEnum.TEMPORARY.getCode().equals(s.getBillPeriodType())).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(applyTemporary)) {
//                    mv.addObject("applyTemporary", applyTemporary.get(ErpConst.ZERO));
//                    //获取临时账期订单实际总金额
//                    BigDecimal formalReal = applyTemporary.stream().filter(s -> null != s.getOrderIdList())
//                            .map(s -> traderCustomerService.getRealTotalAmount(s.getOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    mv.addObject("applyTemporaryReal", formalReal);
//                    appCountReal.add(formalReal);
//                    //获取临时账期历史账期订单总金额
//                    BigDecimal formalHistoryReal = applyTemporary.stream().filter(s -> null != s.getHistoryOrderIdList())
//                            .map(s -> traderCustomerService.getRealTotalAmount(s.getHistoryOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    mv.addObject("applyTemporaryHistoryReal", formalHistoryReal);
//                    if(formalHistoryReal.compareTo(BigDecimal.ZERO) == 0){
//                        mv.addObject("applyTemporaryHistoryReal", null);
//                    }
//                    appCountHistoryReal.add(formalHistoryReal);
//                }
//
//                //订单账期
//                List<CustomerBillPeriodCreditHistoryDto> applyOrder = customerBillList.stream().filter(s -> null != s && ObjectUtils.notEmpty(s.getBillPeriodType()) && CustomerBillPeriodTypeEnum.ORDER.getCode().equals(s.getBillPeriodType())).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(applyOrder)) {
//                    mv.addObject("applyOrder", applyOrder.get(ErpConst.ZERO));
//                    //获取订单账期订单实际总金额
//                    BigDecimal formalReal = applyOrder.stream().filter(s -> null != s.getOrderIdList())
//                            .map(s -> traderCustomerService.getRealTotalAmount(s.getOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    mv.addObject("applyOrderReal", formalReal);
//                    appCountReal.add(formalReal);
//                    //获取订单账期历史账期订单总金额
//                    BigDecimal formalHistoryReal = applyOrder.stream().filter(s -> null != s.getHistoryOrderIdList())
//                            .map(s -> traderCustomerService.getRealTotalAmount(s.getHistoryOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    mv.addObject("applyOrderHistoryReal", formalHistoryReal);
//                    if(formalHistoryReal.compareTo(BigDecimal.ZERO) == 0){
//                        mv.addObject("applyOrderHistoryReal", null);
//                    }
//                    appCountHistoryReal.add(formalHistoryReal);
//                }
//                mv.addObject("appCountReal", appCountReal.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
//                mv.addObject("appCountHistoryReal", appCountHistoryReal.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
//                if (appCountHistoryReal.stream().reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO) == 0){
//                    mv.addObject("appCountHistoryReal", null);
//                }
//            }
//
//            //orgName = orgService.getOrgName(orgId);
//            try{
//                traderCustomer.setIsEnable(ErpConst.ZERO);
//                //判断是否销售
//                orgService.getCurrentIsSale(orgFId,traderCustomer);
//                if(null!=traderCustomer.getIsEnable() && ErpConst.ONE.equals(traderCustomer.getIsEnable())){
//                    //销售不展示
//                    mv.addObject("isEnable",ErpConst.ONE);
//                }else{
//                     if (ErpConst.ONE.equals(user.getIsAdmin())){
//                         //增加njadmin的全展示
//                         traderCustomer.setIsEnable(ErpConst.TWO);
//                     }
//                    //循环递归部门，查询userInfo
//                    List<Organization> organizationList = new ArrayList<>();
//                    List<Map<Integer,List<Integer>>> billPeriodAppliers = orgService.getCurrentLevel(orgId,orgFId,traderCustomer,organizationList);
//                    //页面list
//                    //List<List<CustomerBillPeriodCreditHistoryPageDto>> resultLists = new ArrayList<>();
//                    List<CustomerBillPeriodCreditHistoryPageDto> resultList = new ArrayList<>();
//
//                    if (!CollectionUtils.isEmpty(billPeriodAppliers)) {
//
//                        billPeriodAppliers.stream().forEach(t->{
//                            CustomerBillPeriodCreditHistoryPageDto result = new CustomerBillPeriodCreditHistoryPageDto();
//                            t.forEach( (k, v)  -> {
//                                organizationList.stream().filter(Objects::nonNull).forEach(s->{
//                                    if ( k.equals(s.getOrgId())){
//                                        result.setOrgName(s.getOrgName());
//                                        return;
//                                    }
//                                });
//                                queryDto.setBillPeriodAppliers(v);
//                            });
//
//                            List<CustomerBillPeriodCreditHistoryDto> customerBillLists = new ArrayList<>();
//                            //调查询接口
////                            if (!CollectionUtils.isEmpty(queryDto.getBillPeriodAppliers())){
////                                customerBillLists = customerBillPeriodService.getCustomerBillPeriodCreditHistoryByCreators(queryDto);
////                            }
//
//                            if (!CollectionUtils.isEmpty(queryDto.getBillPeriodAppliers())){
//                                customerBillLists = customerBillPeriodService.getCustomerBillPeriodCreditHistoryByCreators(queryDto);
//                            }
//
//                            if (!CollectionUtils.isEmpty(customerBillLists)) {
//
//                                CustomerBillPeriodCreditHistoryDto applyCounts = addTotal(customerBillLists);
//                                //合计
//                                result.setTotal(applyCounts);
//                                //mv.addObject("appCounts", applyCounts);
//
//                                List<BigDecimal> appCountsReal = new ArrayList<>();
//                                List<BigDecimal> appCountsHistoryReal = new ArrayList<>();
//                                //正式账期
//                                List<CustomerBillPeriodCreditHistoryDto> applysFormal = customerBillLists.stream().filter(s -> null != s && ObjectUtils.notEmpty(s.getBillPeriodType()) && CustomerBillPeriodTypeEnum.OFFICIAL.getCode().equals(s.getBillPeriodType())).collect(Collectors.toList());
//                                if (!CollectionUtils.isEmpty(applysFormal)) {
//                                    //mv.addObject("applyFormals", applysFormal.get(ErpConst.ZERO));
//                                    //正式
//                                    result.setFormal(applysFormal.get(ErpConst.ZERO));
//                                    //获取正式账期订单实际总金额
//                                    BigDecimal formalReal = applysFormal.stream().filter(s -> null != s.getOrderIdList())
//                                            .map(s -> traderCustomerService.getRealTotalAmount(s.getOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                                    //mv.addObject("applyFormalsReal", formalReal);
//                                    //正式-订单金额
//                                    result.setApplyFormalsReal(formalReal);
//                                    appCountsReal.add(formalReal);
//                                    //获取正式账期历史账期订单总金额
//                                    BigDecimal formalHistoryReal = applysFormal.stream().filter(s -> null != s.getHistoryOrderIdList())
//                                            .map(s -> traderCustomerService.getRealTotalAmount(s.getHistoryOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                                    //mv.addObject("applyFormalsHistoryReal", formalHistoryReal);
//                                    result.setApplyFormalsHistoryReal(formalHistoryReal);
//                                    if(formalHistoryReal.compareTo(BigDecimal.ZERO) == 0){
//                                        result.setApplyFormalsHistoryReal(null);
//                                    }
//                                    appCountsHistoryReal.add(formalHistoryReal);
//                                }
//
//                                //临时账期
//                                List<CustomerBillPeriodCreditHistoryDto> applysTemporary = customerBillLists.stream().filter(s -> null != s && ObjectUtils.notEmpty(s.getBillPeriodType()) && CustomerBillPeriodTypeEnum.TEMPORARY.getCode().equals(s.getBillPeriodType())).collect(Collectors.toList());
//                                if (!CollectionUtils.isEmpty(applysTemporary)) {
//                                    //mv.addObject("applyTemporarys", applysTemporary.get(ErpConst.ZERO));
//                                    //临时
//                                    result.setTemporarys(applysTemporary.get(ErpConst.ZERO));
//                                    //获取临时账期订单实际总金额
//                                    BigDecimal formalReal = applysTemporary.stream().filter(s -> null != s.getOrderIdList())
//                                            .map(s -> traderCustomerService.getRealTotalAmount(s.getOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                                    //mv.addObject("applyTemporarysReal", formalReal);
//                                    result.setApplyTemporarysReal(formalReal);
//                                    appCountsReal.add(formalReal);
//                                    //获取临时账期历史账期订单总金额
//                                    BigDecimal formalHistoryReal = applysTemporary.stream().filter(s -> null != s.getHistoryOrderIdList())
//                                            .map(s -> traderCustomerService.getRealTotalAmount(s.getHistoryOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                                    ///mv.addObject("applyTemporarysHistoryReal", formalHistoryReal);
//                                    result.setApplyTemporarysHistoryReal(formalHistoryReal);
//                                    if(formalHistoryReal.compareTo(BigDecimal.ZERO) == 0){
//                                        result.setApplyTemporarysHistoryReal(null);
//                                    }
//                                    appCountsHistoryReal.add(formalHistoryReal);
//                                }
//
//                                //订单账期
//                                List<CustomerBillPeriodCreditHistoryDto> applysOrder = customerBillLists.stream().filter(s -> null != s && ObjectUtils.notEmpty(s.getBillPeriodType()) && CustomerBillPeriodTypeEnum.ORDER.getCode().equals(s.getBillPeriodType())).collect(Collectors.toList());
//                                if (!CollectionUtils.isEmpty(applysOrder)) {
//                                    //mv.addObject("applyOrders", applysOrder.get(ErpConst.ZERO));
//                                    //订单
//                                    result.setOrder(applysOrder.get(ErpConst.ZERO));
//                                    //获取订单账期订单实际总金额
//                                    BigDecimal formalReal = applysOrder.stream().filter(s -> null != s.getOrderIdList())
//                                            .map(s -> traderCustomerService.getRealTotalAmount(s.getOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                                    //mv.addObject("applyOrdersReal", formalReal);
//                                    result.setApplyOrdersReal(formalReal);
//                                    appCountsReal.add(formalReal);
//                                    //获取订单账期历史账期订单总金额
//                                    BigDecimal formalHistoryReal = applysOrder.stream().filter(s -> null != s.getHistoryOrderIdList())
//                                            .map(s -> traderCustomerService.getRealTotalAmount(s.getHistoryOrderIdList())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                                    //mv.addObject("applyOrdersHistoryReal", formalHistoryReal);
//                                    result.setApplyOrdersHistoryReal(formalHistoryReal);
//                                    if(formalHistoryReal.compareTo(BigDecimal.ZERO) == 0){
//                                        result.setApplyOrdersHistoryReal(null);
//                                    }
//                                    appCountsHistoryReal.add(formalHistoryReal);
//                                }
//
//                                //mv.addObject("appCountsReal", appCountsReal.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
//                                //mv.addObject("appCountsHistoryReal", appCountsHistoryReal.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
//                                result.setAppCountsReal(appCountsReal.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
//                                result.setAppCountsHistoryReal(appCountsHistoryReal.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
//                                if (appCountsHistoryReal.stream().reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO)==0){
//                                    result.setAppCountsHistoryReal(null);
//                                }
//                            }
//                            resultList.add(result);
//                        });
//                        mv.addObject("resultList", resultList);
//                    }
//                }
//            } catch (Exception ex){
//                logger.warn("TraderCustomerController-> applicantDepartment,get position fail，apply orgId= {},login orgId={}", orgId,orgFId);
//                return mv;
//            }
//
//        }else{
//            logger.warn("TraderCustomerController-> applicantDepartment,traderId= {},apply userId is null", traderCustomer.getTraderId());
//        }
//
//        //mv.addObject("orgName", orgName);
//
//        return mv;
//    }



    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/applicantDepartment")
    @NoNeedAccessAuthorization
    public ModelAndView applicantDepartment1(TraderCustomer traderCustomer,HttpServletRequest request,
                                            @RequestParam(required = false, value="startTime") String startTime,
                                            @RequestParam(required = false, value="endTime") String endTime) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();
        mv.setViewName("trader/customer/applicantDepartment1");
        mv.addObject("startTime", startTime);
        mv.addObject("endTime", endTime);

        if (null == traderCustomer) {
            return mv;
        }
        //通过 traderCustomerId 查询归属销售
        if (ObjectUtils.isEmpty(traderCustomer.getTraderId())) {
            logger.warn("TraderCustomerController-> applicantDepartment,traderId= {}", traderCustomer.getTraderId());
            return mv;
        }

        User userApply = orgService.getTraderUserAndOrgByTraderId(traderCustomer.getTraderId(), ErpConst.ONE);// 1客户，2供应商
        if (null == userApply) {
            logger.warn("TraderCustomerController-> applicantDepartment,traderId= {}", traderCustomer.getTraderId());
            return mv;
        }
        //当前申请人
        String saleName = userApply.getUsername() == null ? "" : userApply.getUsername();
        mv.addObject("saleName", saleName);

        //归属部门,查询一级/二级/三级，否则当前部门
        Integer orgId = userApply.getOrgId();
        Integer saleId = userApply.getUserId();
        Integer orgFId = user.getOrgId();

        if (ObjectUtils.isEmpty(saleId)){
            return mv;
        }

        CustomerBillPeriodCreditHistoryQueryDto queryDto = new CustomerBillPeriodCreditHistoryQueryDto();
        queryDto.setCompanyId(ErpConst.NJ_COMPANY_ID);
        queryDto.setBillPeriodAppliers(Collections.singletonList(saleId));
        if (StringUtil.isNotBlank(startTime)) {
            queryDto.setStartTime(DateUtil.convertLong(startTime + " 00:00:00", ""));
        }
        if (StringUtil.isNotBlank(endTime)) {
            queryDto.setEndTime(DateUtil.convertLong(endTime + " 23:59:59", ""));
        }

        if (ObjectUtils.notEmpty(saleId)) {
            List<CustomerBillPeriodCreditHistoryVo> saleHistory = getCustomerBillPeriodCreditHistory(queryDto);
            mv.addObject("saleHistory",saleHistory);
            mv.addObject("totalHistory",getTotalCustomerBillPeriodCreditHistory1(saleHistory));
        }


        //查询部门账期使用情况
        traderCustomer.setIsEnable(ErpConst.ZERO);
        //判断是否销售
        orgService.getCurrentIsSale(orgFId,traderCustomer);
        if(null!=traderCustomer.getIsEnable() && ErpConst.ONE.equals(traderCustomer.getIsEnable())){
            return mv;
        }

        if (ErpConst.ONE.equals(user.getIsAdmin())){
            //增加njadmin的全展示
            traderCustomer.setIsEnable(ErpConst.TWO);
        }

        List<Organization> organizationList = new ArrayList<>();
        List<Map<Integer,List<Integer>>> billPeriodAppliers = orgService.getCurrentLevel(orgId,orgFId,traderCustomer,organizationList);
        Map<Integer, String> organizationMap = organizationList.stream().collect(Collectors.toMap(Organization::getOrgId,Organization::getOrgName));
        Map<String, List<CustomerBillPeriodCreditHistoryVo>> departHistory = billPeriodAppliers.parallelStream()
                .map(mapItem -> {
                    Map<String, List<CustomerBillPeriodCreditHistoryVo>> departHistoryMap = new ArrayMap<>();
                    String departName = null;
                    CustomerBillPeriodCreditHistoryQueryDto queryItem = new CustomerBillPeriodCreditHistoryQueryDto();
                    queryItem.setCompanyId(ErpConst.NJ_COMPANY_ID);
                    queryItem.setStartTime(queryDto.getStartTime());
                    queryItem.setEndTime(queryDto.getEndTime());
                    Integer departId = mapItem.keySet().stream().findFirst().get();
                    queryItem.setBillPeriodAppliers(mapItem.get(departId));
                    departHistoryMap.put(organizationMap.get(departId),getCustomerBillPeriodCreditHistory(queryItem));
                    return departHistoryMap;
                })
                .reduce(new HashMap<>(),(a,b) -> {
                    a.putAll(b);
                    return a;
                });

        Map<String, Map<String, List<CustomerBillPeriodCreditHistoryVo>>> departHistoryAndTotal = new HashMap<>();
        departHistory.keySet().forEach(key -> {
            Map<String, List<CustomerBillPeriodCreditHistoryVo>> departHistoryItem = new HashMap<>();
            departHistoryItem.put("departHistory",departHistory.get(key));
            departHistoryItem.put("departTotalHistory",Collections.singletonList(getTotalCustomerBillPeriodCreditHistory1(departHistory.get(key))));
            departHistoryAndTotal.put(key,departHistoryItem);
        });
        mv.addObject("departHistoryAndTotal",departHistoryAndTotal);
        return mv;
    }


    private List<CustomerBillPeriodCreditHistoryVo> getCustomerBillPeriodCreditHistory(CustomerBillPeriodCreditHistoryQueryDto queryDto){
        List<CustomerBillPeriodCreditHistoryVo> customerBillPeriodCreditHistoryVoList = customerBillPeriodService.getCustomerBillPeriodCreditHistoryByCreators(queryDto).stream()
                .map(item -> {
                    CustomerBillPeriodCreditHistoryVo vo = new CustomerBillPeriodCreditHistoryVo();
                    BeanUtils.copyProperties(item,vo);
                    List<Integer> orderIdList = item.getOrderIdList().stream().map(Long::intValue).collect(Collectors.toList());
                    vo.setOrderListAmount(saleorderService.getRealTotalAmountBySaleorderIdList(orderIdList));
                    List<Integer> historyOrderIdList = item.getHistoryOrderIdList().stream().map(Long::intValue).collect(Collectors.toList());
                    vo.setHistoryOrderListAmount(saleorderService.getRealTotalAmountBySaleorderIdList(historyOrderIdList));
                    return vo;
                })
                .collect(Collectors.toList());
        List<Integer> billPeriodTypeList = CollectionUtil.newArrayList(1,2,3);
        billPeriodTypeList.removeAll(customerBillPeriodCreditHistoryVoList.stream().map(CustomerBillPeriodCreditHistoryDto::getBillPeriodType).collect(Collectors.toList()));
        if (billPeriodTypeList.size() > 0) {
            //补充没有使用的账期类型
            List<CustomerBillPeriodCreditHistoryVo> unUsedCustomerBillPeriodCreditHistory = billPeriodTypeList.stream()
                            .map(billPeriodType -> {
                                CustomerBillPeriodCreditHistoryVo item = new CustomerBillPeriodCreditHistoryVo();
                                item.setBillPeriodType(billPeriodType);
                                return item;
                            })
                            .collect(Collectors.toList());
            customerBillPeriodCreditHistoryVoList.addAll(unUsedCustomerBillPeriodCreditHistory);
        }
        customerBillPeriodCreditHistoryVoList = customerBillPeriodCreditHistoryVoList.stream()
                .sorted((a,b) -> {
                    return Integer.compare(a.getBillPeriodType(),b.getBillPeriodType());
                })
                .collect(Collectors.toList());
        return customerBillPeriodCreditHistoryVoList;
    }


    private CustomerBillPeriodCreditHistoryVo getTotalCustomerBillPeriodCreditHistory(List<CustomerBillPeriodCreditHistoryVo> historyVos){
        CustomerBillPeriodCreditHistoryVo totalHistory = new CustomerBillPeriodCreditHistoryVo();
        List<Map<String, Object>> fieldTotalValueList = historyVos.stream()
                .map(historyVo -> {
                    Field[] fields = ReflectUtil.getFields(CustomerBillPeriodCreditHistoryVo.class);
                    HashMap<String, Field> fieldMap = MapUtil.newHashMap(fields.length, true);
                    for (Field field : fields) {
                        if (field.isSynthetic()) {
                            continue;
                        }
                        fieldMap.put(field.getName(), field);
                    }
                    return fieldMap.keySet().stream()
                            .map(key -> {
                                String[] fieldTypeNameArray = fieldMap.get(key).getType().getTypeName().split(".");
                                String fieldType = fieldTypeNameArray[fieldTypeNameArray.length -1];
                                if (!"Integer".equals(fieldType) || !"BigDecimal".equals(fieldType)){
                                    return null;
                                }
                                Map<String, Object> fieldValueMap = new HashMap<>(1);
                                Object value = ReflectUtil.getFieldValue(historyVo,fieldMap.get(key));
                                if (value == null) {
                                    return null;
                                }
                                fieldValueMap.put(key,value);
                                return fieldValueMap;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        Map<String, Object> fieldTotalValueMap = new HashMap<>();
        return totalHistory;
    }


    private CustomerBillPeriodCreditHistoryVo getTotalCustomerBillPeriodCreditHistory1(List<CustomerBillPeriodCreditHistoryVo> customerBillList){
        CustomerBillPeriodCreditHistoryVo totalHistory = new CustomerBillPeriodCreditHistoryVo();
        totalHistory.setApplyAmount(customerBillList.stream().filter(s->null!=s && null != s.getApplyAmount()).map(CustomerBillPeriodCreditHistoryDto::getApplyAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        totalHistory.setAvailableAmount(customerBillList.stream().filter(s->null!=s && null != s.getAvailableAmount()).map(CustomerBillPeriodCreditHistoryDto::getAvailableAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalHistory.setUnreturnedAmount(customerBillList.stream().filter(s->null!=s && null != s.getUnreturnedAmount()).map(CustomerBillPeriodCreditHistoryDto::getUnreturnedAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalHistory.setOverdueAmount(customerBillList.stream().filter(s->null!=s && null != s.getOverdueAmount()).map(CustomerBillPeriodCreditHistoryDto::getOverdueAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalHistory.setOrderCountOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getOrderCountOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getOrderCountOfOverdue).reduce(0, Integer::sum));
        totalHistory.setDaysOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getDaysOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getDaysOfOverdue).reduce(0, Integer::sum));
        totalHistory.setHistoryCountOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getHistoryCountOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getHistoryCountOfOverdue).reduce(0, Integer::sum));
        totalHistory.setHistoryUsedCount(customerBillList.stream().filter(s->null!=s && null != s.getHistoryUsedCount()).map(CustomerBillPeriodCreditHistoryDto::getHistoryUsedCount).reduce(0, Integer::sum));
        totalHistory.setHistoryAmountOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getHistoryAmountOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getHistoryAmountOfOverdue).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalHistory.setHistoryOrderCountOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getHistoryOrderCountOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getHistoryOrderCountOfOverdue).reduce(0, Integer::sum));
        totalHistory.setHistoryDaysOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getHistoryDaysOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getHistoryDaysOfOverdue).reduce(0, Integer::sum));
        totalHistory.setOrderListAmount(customerBillList.stream().filter(s->null!=s && null != s.getOrderListAmount()).map(CustomerBillPeriodCreditHistoryVo::getOrderListAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        totalHistory.setHistoryOrderListAmount(customerBillList.stream().filter(s->null!=s && null != s.getHistoryOrderListAmount()).map(CustomerBillPeriodCreditHistoryVo::getHistoryOrderListAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        return totalHistory;
    }


    private CustomerBillPeriodCreditHistoryDto addTotal(List<CustomerBillPeriodCreditHistoryDto> customerBillList){
        CustomerBillPeriodCreditHistoryDto list = new CustomerBillPeriodCreditHistoryDto();
            list.setApplyAmount(customerBillList.stream().filter(s->null!=s && null != s.getApplyAmount()).map(CustomerBillPeriodCreditHistoryDto::getApplyAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
            list.setAvailableAmount(customerBillList.stream().filter(s->null!=s && null != s.getAvailableAmount()).map(CustomerBillPeriodCreditHistoryDto::getAvailableAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            list.setUnreturnedAmount(customerBillList.stream().filter(s->null!=s && null != s.getUnreturnedAmount()).map(CustomerBillPeriodCreditHistoryDto::getUnreturnedAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            list.setOverdueAmount(customerBillList.stream().filter(s->null!=s && null != s.getOverdueAmount()).map(CustomerBillPeriodCreditHistoryDto::getOverdueAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            list.setOrderCountOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getOrderCountOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getOrderCountOfOverdue).reduce(0, Integer::sum));
            list.setDaysOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getDaysOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getDaysOfOverdue).reduce(0, Integer::sum));
            list.setAvgOverdueDaysByOrder(customerBillList.stream().filter(s->null!=s && null != s.getAvgOverdueDaysByOrder()).map(CustomerBillPeriodCreditHistoryDto::getAvgOverdueDaysByOrder).reduce(BigDecimal.ZERO, BigDecimal::add));
            list.setHistoryCountOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getHistoryCountOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getHistoryCountOfOverdue).reduce(0, Integer::sum));
            list.setHistoryUsedCount(customerBillList.stream().filter(s->null!=s && null != s.getHistoryUsedCount()).map(CustomerBillPeriodCreditHistoryDto::getHistoryUsedCount).reduce(0, Integer::sum));
            list.setHistoryPercentOverdueByUsedCount(customerBillList.stream().filter(s->null!=s && null != s.getHistoryPercentOverdueByUsedCount()).map(CustomerBillPeriodCreditHistoryDto::getHistoryPercentOverdueByUsedCount).reduce(BigDecimal.ZERO, BigDecimal::add));
            list.setHistoryAmountOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getHistoryAmountOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getHistoryAmountOfOverdue).reduce(BigDecimal.ZERO, BigDecimal::add));
            list.setHistoryOrderCountOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getHistoryOrderCountOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getHistoryOrderCountOfOverdue).reduce(0, Integer::sum));
            list.setHistoryDaysOfOverdue(customerBillList.stream().filter(s->null!=s && null != s.getHistoryDaysOfOverdue()).map(CustomerBillPeriodCreditHistoryDto::getHistoryDaysOfOverdue).reduce(0, Integer::sum));
            list.setHistoryAvgDaysOfOverdueByOrder(customerBillList.stream().filter(s->null!=s && null != s.getHistoryAvgDaysOfOverdueByOrder()).map(CustomerBillPeriodCreditHistoryDto::getHistoryAvgDaysOfOverdueByOrder).reduce(BigDecimal.ZERO, BigDecimal::add));
        return list;
    }

    /**
     * <b>Description:</b><br>
     * 保存账期申请
     *
     * @param request, session, customerBillPeriodApplyDto,isClose 页面关闭调用，清除redis信息
     * @return com.vedeng.common.model.ResultInfo
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/7/28 13:00
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/saveAccountPeriodApplyNew")
    @SystemControllerLog(operationType = "add", desc = "保存客户账期申请")
    @NoNeedAccessAuthorization
    public ResultInfo saveAccountPeriodApplyNew(HttpServletRequest request, HttpSession session, CustomerBillPeriodApplyVo customerBillPeriodApplyVo) {
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        try {
            if(customerBillPeriodApplyVo.getApplyAmount() == null){
                customerBillPeriodApplyVo.setApplyAmount(new BigDecimal(0));
            }
            customerAccountPeriodProcessService.saveOrUpdateAccountPeriodApply(user, customerBillPeriodApplyVo);
            if(CommonConstants.ON.equals(customerBillPeriodApplyVo.getIsVerifyChange())){
                CustomerBillPeriodApply customerBillPeriodApply = customerBillPeriodApplyService.selectByPrimaryKey(customerBillPeriodApplyVo.getBillPeriodApplyId());
                if(customerBillPeriodApply.getBeforeApplyAmount() == null){
                    customerBillPeriodApply.setBeforeApplyAmount(new BigDecimal(0));
                }
                if(customerBillPeriodApply.getBeforeSettlementPeriod() == null){
                    customerBillPeriodApply.setBeforeSettlementPeriod(0);
                }
                //审批流页面修改，备注传递
                resultInfo.setData(customerBillPeriodApplyVo.getBillPeriodApplyId()+","+customerBillPeriodApplyVo.getCustomerId()+","
                        +"额度由"+customerBillPeriodApply.getBeforeApplyAmount()+"调增/减改为"+customerBillPeriodApply.getApplyAmount()
                        +"；周期由"+customerBillPeriodApply.getBeforeSettlementPeriod()+"天改为"+customerBillPeriodApply.getSettlementPeriod()+"天");
            }else {
                resultInfo.setData(customerBillPeriodApplyVo.getTraderId()+","+customerBillPeriodApplyVo.getCustomerId());
            }
        } catch (CustomerBillPeriodException e) {
            logger.warn("新增账期申请失败{}",e.getMessage());
            resultInfo = new ResultInfo(-1,"操作失败: "+e.getMessage());
        } catch (Exception e){
            logger.error("新增账期申请失败{}",e.getMessage(),e);
            resultInfo = new ResultInfo(-1,"操作失败" +e.getMessage());
        }

        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 临时/订单账期申请
     *
     * @param traderCustomer, session,billPeriodType账期类型
     * @return org.springframework.web.servlet.ModelAndView
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/7/27 14:51
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/accountOtherapply")
    @NoNeedAccessAuthorization
    public ModelAndView accountOtherapply(TraderCustomer traderCustomer,HttpSession session,@RequestParam(required = false)Integer billPeriodType) {
        ModelAndView mv = new ModelAndView();
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        List<CustomerBillPeriodApply> applyListInChecking = customerBillPeriodApplyService.getCustomerBillPeriodApplyList(1,
                Long.valueOf(traderCustomer.getTraderCustomerId())).stream()
                .filter(item -> CustomerBillPeriodApplyCheckStatusEnum.IN_CHECK.getCode().equals(item.getCheckStatus()))
                .collect(Collectors.toList());
        if(applyListInChecking.size() > 0){
            //存在有未审核完成的账期，不可继续申请
            mv.addObject("message","需待现有未审核完成的账期审核结束后，才可进行新的申请/修改。");
            return fail(mv);
        }
        //调用接口查询各个类型账期的简要信息
        List<CustomerBillPeriodSummaryInfoDto> customerBillPeriodSummaryInfoDtos = customerBillPeriodService.getCustomerBillPeriodSummaryInfo(1,
                Long.valueOf(traderCustomer.getTraderCustomerId()));
        List<CustomerBillPeriodSummaryInfoDto> outputInfos = new ArrayList<>();
        if(billPeriodType == CustomerBillPeriodTypeEnum.TEMPORARY.getCode()){
            //临时账期
            mv.setViewName("trader/customer/apply_temp_accountperiod");

            if(!CollectionUtils.isEmpty(customerBillPeriodSummaryInfoDtos)){
                for(CustomerBillPeriodSummaryInfoDto customerBillPeriodSummaryInfo : customerBillPeriodSummaryInfoDtos){
                    if(customerBillPeriodSummaryInfo.getBillPeriodType() == 2){
                        //临时账期的简要信息返回前台展示
                        outputInfos.add(customerBillPeriodSummaryInfo);
                    }
                }
            }
            //调用接口查询是否存在生效的临时账期
            List<CustomerBillPeriodDetailsDto> customerBillPeriods = customerBillPeriodService.getCustomerBillPeriodDetailsByType(1,Long.valueOf(traderCustomer.getTraderCustomerId()),2);
            mv.addObject("customerBillPeriods",CollectionUtils.isEmpty(customerBillPeriods) ? null : customerBillPeriods);
        }else if(billPeriodType == CustomerBillPeriodTypeEnum.ORDER.getCode()){
            //订单账期
            mv.setViewName("trader/customer/apply_order_accountperiod");
        }

        if(!CollectionUtils.isEmpty(outputInfos)){
            mv.addObject("customerBillPeriodSummaryInfoDtos",outputInfos);
        }else {
            mv.addObject("customerBillPeriodSummaryInfoDtos",null);
        }
        mv.addObject("nowTime",DateUtil.getNowDayMillisecond());
        mv.addObject("traderCustomer",traderCustomer);
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 根据订单查询对应订单账期
     *
     * @param
     * @return com.vedeng.common.model.ResultInfo
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/8/3 14:36
     */
    @ResponseBody
    @RequestMapping(value = "/queryOrderAccountPeriod")
    public ResultInfo queryOrderAccountPeriod(HttpServletRequest request, HttpSession session,String saleorderNo,Long traderCustomerId){
        ResultInfo resultInfo = new ResultInfo(0,"查询成功");
        Saleorder saleorder = saleorderService.getBySaleOrderNo(saleorderNo);
        if(saleorder == null){
            return new ResultInfo(-1,"未查到该订单");
        }
        //过滤出对应订单的记录
        List<CustomerBillPeriodDetailsDto> orderBillPeriod = customerBillPeriodService.
                getCustomerBillPeriodDetailsByType(1,traderCustomerId,CustomerBillPeriodTypeEnum.ORDER.getCode()).stream()
                .filter(item -> saleorder.getSaleorderId().equals(item.getRelatedOrderId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderBillPeriod)){
            return new ResultInfo(-1,"该订单未查到账期");
        }
        //过滤出对应订单的申请记录
        List<CustomerBillPeriodApply> customerBillPeriodApplyList = customerBillPeriodApplyService.
                getCustomerBillPeriodApplyList(1,traderCustomerId).stream()
                .filter(item -> CustomerBillPeriodTypeEnum.ORDER.getCode().equals(item.getBillPeriodType()))
                .filter(item -> saleorder.getSaleorderId().equals(item.getRelatedOrderId()))
                .collect(Collectors.toList());
        CustomerBillPeriodApplyVo customerBillPeriodApplyVo = new CustomerBillPeriodApplyVo();
        customerBillPeriodApplyVo.setSaleorderNo(saleorderNo);
        customerBillPeriodApplyVo.setRelatedOrderId(saleorder.getSaleorderId());
        customerBillPeriodApplyVo.setApplyAmount(orderBillPeriod.get(0).getAvailableAmount());
        customerBillPeriodApplyVo.setTotalAmount(orderBillPeriod.get(0).getApplyAmount());
        customerBillPeriodApplyVo.setUnReturnAmount(orderBillPeriod.get(0).getUnreturnedAmount());
        customerBillPeriodApplyVo.setApplyReason(customerBillPeriodApplyList.get(0).getApplyReason());
        customerBillPeriodApplyVo.setSettlementPeriod(customerBillPeriodApplyList.get(0).getSettlementPeriod());
        customerBillPeriodApplyVo.setExpectedMargin(customerBillPeriodApplyList.get(0).getExpectedMargin());
        customerBillPeriodApplyVo.setBillPeriodId(orderBillPeriod.get(0).getBillPeriodId());
        resultInfo.setParam(customerBillPeriodApplyVo);
        return resultInfo;
    }

    @ResponseBody
    @RequestMapping(value = "/searchBusinessList")
    @NoNeedAccessAuthorization
    public ModelAndView searchBusinessList(HttpServletRequest request, SaleorderGoodsVo saleorderGoodsVo, TraderCustomerVo traderCustomerVo,
                                     @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                     @RequestParam(required = false) Integer pageSize) {
        ModelAndView mv = new ModelAndView();
        User curr_user = getSessionUser(request);
        if(curr_user==null){
            return new ModelAndView("redirect:/login.do");
        }
        if (saleorderGoodsVo.getTraderId() == null) {
            return pageNotFound(request);
        }

        // 时间处理
        if (null != saleorderGoodsVo.getStarttime() && saleorderGoodsVo.getStarttime() != "") {
            saleorderGoodsVo.setStarttimeLong(DateUtil.convertLong(saleorderGoodsVo.getStarttime(), "yyyy-MM-dd"));
        }
        if (null != saleorderGoodsVo.getEndtime() && saleorderGoodsVo.getEndtime() != "") {
            saleorderGoodsVo.setEndtimeLong(DateUtil.convertLong(saleorderGoodsVo.getEndtime() + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));
        }

        Page page = getPageTag(request, pageNo, pageSize);

        List<SaleorderGoodsVo> list = null;
        Map<String, Object> map = traderCustomerService.getBusinessListPage(saleorderGoodsVo, page);

        list = (List<SaleorderGoodsVo>) map.get("list");

        Map<String, Map<String, Object>> newSkuInfos = new HashMap<>();

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if (!CollectionUtils.isEmpty(list)) {
            List<Integer> skuIds = new ArrayList<>();
            list.stream().forEach(saleGood -> {
                skuIds.add(saleGood.getGoodsId());
            });
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        mv.setViewName("trader/customer/searchBusinessList");
        mv.addObject("method", "saleorder");
        mv.addObject("page", (Page) map.get("page"));
        mv.addObject("saleorderGoodsVo", saleorderGoodsVo);
        mv.addObject("businessList", list);
        mv.addObject("traderCustomer", traderCustomerVo);
        return mv;
    }


    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/viewBusinessCards")
    @NoNeedAccessAuthorization
    public ModelAndView viewBusinessCards(HttpServletRequest request,Integer traderContactId) {
        ModelAndView mv = new ModelAndView("trader/customer/viewBusinessCard");
        //获取名片信息
        List<Map<String, Object>> traderCertificatesMaps = traderCustomerService.getTraderBusinessCardById(traderContactId);
        mv.addObject("traderCertificatesMaps", new org.json.JSONArray(traderCertificatesMaps).toString());
        mv.addObject("traderContactId", traderContactId);
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "/saveBusinessCards")
    @SystemControllerLog(operationType = "add", desc = "保存个人名片")
    @NoNeedAccessAuthorization
    public ResultInfo saveBusinessCards(HttpSession session, TraderCertificate traderCertificate) throws IOException {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);

        try {
            ResultInfo resultInfo = traderCustomerService.saveBusinessCards(traderCertificate, user);
            return resultInfo;
        }catch (IllegalArgumentException e){
            return ResultInfo.error(e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/updateBusinessCards")
    @SystemControllerLog(operationType = "add", desc = "更新个人名片")
    @NoNeedAccessAuthorization
    public ResultInfo updateBusinessCards(HttpSession session, TraderCertificate traderCertificate) throws IOException {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        try {
            return traderCustomerService.updateBusinessCards(traderCertificate, user);
        }catch (IllegalArgumentException e){
            return ResultInfo.error(e.getMessage());
        }
    }
}








