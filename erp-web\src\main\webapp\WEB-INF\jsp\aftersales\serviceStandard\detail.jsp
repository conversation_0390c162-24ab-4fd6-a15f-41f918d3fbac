<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="title" value="详情" scope="application" />
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/dialogSelectAndEdit.css?rnd=${resourceVersionKey}">
<script type="text/javascript">

    function openTab(span){
        var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
        var newPageId;
        var tabTitle = $(span).attr('tabTitle');
        if (typeof(tabTitle) == 'undefined') {
            alert('参数错误');
        } else {
            tabTitle = $.parseJSON(tabTitle);
        }
        var id = tabTitle.num;
        // var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
        var name = tabTitle.title;
        var uri = tabTitle.link;
        var closable = 1;
        var item = { 'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false };
        self.parent.closableTab.addTab(item);
        self.parent.closableTab.resizeMove();
        $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    }

    /**
     * 删除供应商售后政策
     * @param supplyPolicyId
     */
    function deleteSupplyPolicy(supplyPolicyId){

        layer.confirm("删除后数据不可恢复，确定继续操作么？", {
            btn: ['确定','取消'] //按钮
        }, function(){
            $.ajax({
                type: "POST",
                url: "./deleteSupplyPolicy.do",
                data: {'supplyPolicyId':supplyPolicyId},
                dataType:'json',
                success: function(data){
                    window.location.reload();
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                    }
                }
            });
        }, function(){
        });

    }

</script>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/price/basePriceMaintain/detail.do">
        <div style="    position: fixed;top: 0; z-index: 1000; width: 100%;">
                    <div class="parts">
                        <div class="title-container">

<%--                            订货号:${afterSaleServiceStandardApply.skuNo} 商品名称:${skuGenerate.showName}--%>
                            订货号: ${skuNo}  商品名称:${skuGenerate.showName}

                            <span class="bt-small bg-light-blue bt-bg-style addtitle" style="float: right;margin-top: 3px" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
						"link":"./aftersale/serviceStandard/toAddSupplyAfterSalePolicy.do?skuNo=${skuNo}","title":"维护供应商售后政策"}'>新增供应商售后政策</span>

                            <span class="bt-small bg-light-blue bt-bg-style addtitle" style="float: right;margin-top: 3px" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
						"link":"./aftersale/serviceStandard/toBdAfterSalePolicyApply.do?skuNo=${skuNo}","title":"维护贝登标准"}'>维护贝登标准</span>

						  <c:if test="${null!=historicInfo.taskInfo and (taskInfo.assignee == curr_user.username or historicInfo.candidateUserMap['belong']) }">
						                                          <button style="float: right;margin-top: 3px" type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&serviceStandardApplyId=${skuPriceChangeApplyDto.serviceStandardApplyId}&pass=false"}'>审核不通过</button>

                                        <button style="float: right;margin-top: 3px" type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&serviceStandardApplyId=${afterSaleServiceStandardApply.serviceStandardApplyId}&pass=true"}'>审核通过</button>
                                    </c:if>
                        </div>
                    </div>
                </div>
            <ul class="payplan" style="padding-top:30px">


                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">基础售后内容</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>类型</td>
                                    <td>贝登售后标准</td>
                                    <c:if test="${afterSaleServiceStandardApply.supplyAfterSaleIsMaintain == 1}">
                                        <td colspan="${fn:length(afterSaleSupplyPolicyList)}">供应商售后政策</td>
                                    </c:if>
                                </tr>
                                <tr>
                                    <td>供应商名称</td>
                                    <td>/</td>

                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            ${afterSaleSupplyPolicy.traderName}&nbsp;
                                            <div>
<%--                                                <span class="title-click pop-new-data" style='float:none' layerParams='{"width":"900px","height":"600px","title":"复制供应商售后政策","link":"${pageContext.request.contextPath}/aftersale/serviceStandard/toCopySupplyAfterSalePolicy.do?supplyPolicyId=${afterSaleSupplyPolicy.supplyPolicyId}"}'>--%>
<%--                                                     复制--%>
<%--                                                </span>--%>
                                                    <div class="title-click pop-new-data" style="float: none" layerParams='{"width":"1000px","height":"850px","title":"参考复制其他产品售后政策","link":"./referenceSupplyAfterSalePolicy.do?skuNo=${skuNo}&policyType=2&formerSupplyPolicyId=${afterSaleSupplyPolicy.supplyPolicyId}"}'>
                                                        复制
                                                    </div>
                                                <span class="title-click addtitle" style='float:none' style="float: right;margin-top: 5px" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
						"link":"./aftersale/serviceStandard/toModifySupplyPolicyPage.do?supplyPolicyId=${afterSaleSupplyPolicy.supplyPolicyId}","title":"维护供应商售后政策"}'>
                                                    编辑
                                                </span>
                                                <span class="title-click" style='float:none' onclick="deleteSupplyPolicy(${afterSaleSupplyPolicy.supplyPolicyId})">删除</span>
                                            </div>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>售后服务商</td>
                                    <td>/</td>

                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${afterSaleSupplyPolicy.serviceProviderType == '1'}">
                                                    原厂服务
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.serviceProviderType == '2'}">
                                                    该供应商提供服务
                                                </c:when>
                                                <c:otherwise>
                                                    -
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">售后服务等级
                            <c:choose>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '5'}">
                                    ★★★★★
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '4'}">
                                    ★★★★☆
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '3'}">
                                    ★★★☆☆
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '2'}">
                                    ★★☆☆☆
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '1'}">
                                    ★☆☆☆☆
                                </c:when>
                                <c:when test="${skuGenerate.afterSalesServiceLevel == '6'}">
                                    无需评级
                                </c:when>
                                <c:otherwise>
                                    待评级
                                </c:otherwise>
                            </c:choose>
                            </div>
                        </div>
                         <c:if test="${skuGenerate.afterSalesServiceLevel != '6'}">
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
 <tr>    <td  style="padding:0"  > </td>
                                    <td style="padding:0"  > </td>
                                     <td   style="padding:0"> </td>
                                      <td  style="padding:0" > </td>
                                       <td  style="padding:0" > </td>
                                        <td style="padding:0"  > </td>

                                  </tr>
                                  <tr>
                                    <td rowspan="2">时效性</td>
                                    <td >响应</td>
                                    <td>技术指导</td>
                                    <td>维修</td>
                                    <td></td>
                                    <td> </td>
                                  </tr>
                                    <tr>
                                    <td  > <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null}">
                                                ${afterSaleServiceLevelDto.installPolicyResponseTimeShow}
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>

                                            </td>
                                    <td><c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null}">
                                                ${afterSaleServiceLevelDto.technicalDirectorEffectTimeShow}
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose> </td>
                                    <td><c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null}">
                                                ${afterSaleServiceLevelDto.guaranteePolicyRepaireTimeShow}
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose> </td>
                                    <td></td>
                                    <td> </td>
                                  </tr>
                                    <tr>
                                    <td rowspan="2">安调资料</td>
                                    <td  >说明书</td>
                                    <td  >操作流程</td>
                                    <td  >安调视频/安装指南</td>
                                    <td  >产品PPT</td>
                                    <td  >装箱清单</td>
                                  </tr>
                                    <tr>

                                    <td  >
                                     <c:choose>
                                            <c:when test="${docInstructionManual}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td  > <c:choose>
                                            <c:when test="${docOperationFlowCard}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                    <td  ><c:choose>
                                            <c:when test="${docSecurityAdjustmentVideo || docGuide  }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                    <td  ><c:choose>
                                            <c:when test="${docProductPPT }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                    <td  ><c:choose>
                                            <c:when test="${docPackingList }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                  </tr>

                                   <tr>
                                    <td rowspan="2"><div class="customername pos_rel">
                                    <span>技术支持
                                    <i class="iconbluesigh ml4 contorlIcon"></i></span>
                                    <div class="pos_abs customernameshow">指资料库》商品售后资料管理》技术指导》厂家/供应商技术对接人及联系方式或者厂家/供应商售后负责人及联系方式有具体值</div>
                                    </div></td>
                                    <td  >技术支持联系方式</td>
                                       <td   > </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                    <td   ><c:choose>
                                            <c:when test="${docTechnicalContact or docAfterSalesManager }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose></td>
                                     <td   > </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>

                                  </tr>
                                   <tr>
                                    <td rowspan="2">维修资料</td>
                                    <td  >维修手册</td>
                                       <td   > 维修案例/FAQ</td>
                                      <td   >维修视频 </td>
                                       <td   >维修配附件 </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                     <td   >
<c:choose>
                                            <c:when test="${ docManual }">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                     </td>
                                    <td   ><c:choose>
                                            <c:when test="${docCaseStudy}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                     </td>
                                      <td   ><c:choose>
                                            <c:when test="${docRepairVideo}">
                                                √
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                      </td>
                                       <td   >
                                       <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null and  afterSaleServiceLevelDto.repairPartsAccessory == 1}">
                                                √
                                            </c:when>
                                             <c:when test="${afterSaleServiceLevelDto!=null and  afterSaleServiceLevelDto.repairPartsAccessory == 2}">
                                                 X
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                   </td>
                                        <td   > </td>

                                  </tr>

                                        <tr>
                                    <td rowspan="2">培训支持</td>
                                    <td  >安调培训</td>
                                       <td   > 维修培训</td>
                                      <td   >  </td>
                                       <td   > </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                    <td   >

                                     <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null and  afterSaleServiceLevelDto.installTraining == 1}">
                                                √
                                            </c:when>
                                             <c:when test="${afterSaleServiceLevelDto!=null and  afterSaleServiceLevelDto.installTraining == 2}">
                                                 X
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                     <td   >

                                      <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null and  afterSaleServiceLevelDto.repairTraining == 1}">
                                                √
                                            </c:when>
                                             <c:when test="${afterSaleServiceLevelDto!=null and  afterSaleServiceLevelDto.repairTraining == 2}">
                                                X
                                            </c:when>
                                             <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>

                                     </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>

                                  </tr>

                                       <tr>
                                    <td rowspan="2">服务人员等级</td>
                                    <td  >服务人员等级</td>
                                       <td   > </td>
                                      <td   >  </td>
                                       <td   > </td>
                                        <td   > </td>
                                  </tr>
                                    <tr>
                                    <td   >
                                  <c:choose>
                                            <c:when test="${afterSaleServiceLevelDto!=null and  afterSaleServiceLevelDto.servicePersonnelLevel == '5'}">
                                                D级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null and  afterSaleServiceLevelDto.servicePersonnelLevel == '4'}">
                                                C级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null and  afterSaleServiceLevelDto.servicePersonnelLevel == '3'}">
                                                B级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null and  afterSaleServiceLevelDto.servicePersonnelLevel == '2'}">
                                                A级及以上
                                            </c:when>
                                            <c:when test="${afterSaleServiceLevelDto!=null and  afterSaleServiceLevelDto.servicePersonnelLevel == '1'}">
                                                S级
                                            </c:when>
                                            <c:otherwise>
                                                -
                                            </c:otherwise>
                                        </c:choose>
                                        </td>
                                     <td   > </td>
                                      <td   > </td>
                                       <td   > </td>
                                        <td   > </td>

                                  </tr>
                            </tbody>
                        </table>
                        </c:if>
                    </div>
                </li>

                <c:if test="${spuType == '316' or spuType == '1008'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">安装政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>产品是否可安装</td>
                                    <td>
                                        <c:if test="${skuGenerate.isInstallable == 1}">可安装</c:if>
                                        <c:if test="${skuGenerate.isInstallable == 0}">不可安装</c:if>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:if test="${skuGenerate.isInstallable == 1}">可安装</c:if>
                                            <c:if test="${skuGenerate.isInstallable == 0}">不可安装</c:if>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <c:if test="${skuGenerate.isInstallable == 1}">
                                    <tr>
                                        <td>是否提供上门安装服务</td>
                                        <td>
                                            <div class="customername pos_rel">
                                                <span>
                                                    <c:choose>
                                                        <c:when test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '0'}">
                                                            收费安装
                                                        </c:when>
                                                        <c:when test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '1'}">
                                                            免费安装
                                                        </c:when>
                                                        <c:when test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '2'}">
                                                            不提供安装
                                                        </c:when>
                                                        <c:otherwise>
                                                            空
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType != afterSaleServiceStandardApply.installPolicyInstallType}">
                                                        <i class="iconbluesigh ml4 contorlIcon"></i>
                                                    </c:if>
                                                </span>
                                                <div class="pos_abs customernameshow">
                                                    需审批新值:
                                                    <c:choose>
                                                        <c:when test="${afterSaleServiceStandardApply.installPolicyInstallType == '0'}">
                                                            收费安装
                                                        </c:when>
                                                        <c:when test="${afterSaleServiceStandardApply.installPolicyInstallType == '1'}">
                                                            免费安装
                                                        </c:when>
                                                        <c:when test="${afterSaleServiceStandardApply.installPolicyInstallType == '2'}">
                                                            不提供安装
                                                        </c:when>
                                                        <c:otherwise>
                                                            /
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </td>
                                        <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                            <td>
                                                <c:choose>
                                                    <c:when test="${afterSaleSupplyPolicy.installPolicyInstallType == '0'}">
                                                        收费安装
                                                    </c:when>
                                                    <c:when test="${afterSaleSupplyPolicy.installPolicyInstallType == '1'}">
                                                        免费安装
                                                    </c:when>
                                                    <c:when test="${afterSaleSupplyPolicy.installPolicyInstallType == '2'}">
                                                        不提供安装
                                                    </c:when>
                                                    <c:otherwise>
                                                        /
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                        </c:forEach>
                                    </tr>
                                    <c:if test="${afterSaleServiceStandardInfoDto.installPolicyInstallType == '0'}">
                                        <tr>
                                            <td>安装费</td>
                                            <td>
                                                <div class="customername pos_rel">
                                                    <span>
                                                        ${afterSaleServiceStandardInfoDto.installPolicyInstallFee}
                                                        <c:if test="${ afterSaleServiceStandardApply.installPolicyInstallFee != afterSaleServiceStandardInfoDto.installPolicyInstallFee}">
                                                            <i class="iconbluesigh ml4 contorlIcon"></i>
                                                        </c:if>
                                                    </span>
                                                    <div class="pos_abs customernameshow">
                                                        需审批新值:${afterSaleServiceStandardApply.installPolicyInstallFee == null ? "/" : afterSaleServiceStandardApply.installPolicyInstallFee}
                                                    </div>
                                                </div>
                                            </td>
                                            <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                                <td>
                                                     /
                                                </td>
                                            </c:forEach>
                                        </tr>
                                    </c:if>
                                    <tr>
                                        <td>安装区域</td>
                                        <td>
                                            <div class="title-click J-area-select" style='float:none;margin:-4px 0 0 10px;'>
                                                查看
                                            </div>
                                            <input type="hidden" class="J-area-value" value='${applyInstallArea.provinceCityJsonvalue}'>
                                        </td>
                                        <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                            <td>
                                                    ${afterSaleSupplyPolicy.installPolicyInstallArea}
                                            </td>
                                        </c:forEach>
                                    </tr>
                                    <tr>
                                        <td>安装区域备注</td>
                                        <td>
                                            <div class="customername pos_rel">
                                            <span>
                                                ${afterSaleServiceStandardInfoDto.installPolicyInstallAreaComment == ''?'-':afterSaleServiceStandardInfoDto.installPolicyInstallAreaComment}
                                                <c:if test="${ afterSaleServiceStandardApply.installPolicyInstallAreaComment != afterSaleServiceStandardInfoDto.installPolicyInstallAreaComment}">
                                                     <i class="iconbluesigh ml4 contorlIcon"></i>
                                                 </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:${afterSaleServiceStandardApply.installPolicyInstallAreaComment == ''?'-':afterSaleServiceStandardApply.installPolicyInstallAreaComment}
                                            </div>
                                            </div>
                                        </td>
                                        <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                            <td>
                                                /
                                            </td>
                                        </c:forEach>
                                    </tr>
                                    <tr>
                                        <td>是否需要装机资质</td>
                                        <td>

                                            <div class="customername pos_rel">
                                                <span>
                                                    <c:choose>
                                                        <c:when test="${afterSaleServiceStandardInfoDto.installPolicyHaveInstallationQualification == '1'}">
                                                            是
                                                        </c:when>
                                                        <c:when test="${afterSaleServiceStandardInfoDto.installPolicyHaveInstallationQualification == '0'}">
                                                            否
                                                        </c:when>
                                                        <c:otherwise>
                                                            空
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <c:if test="${ afterSaleServiceStandardApply.installPolicyHaveInstallationQualification != afterSaleServiceStandardInfoDto.installPolicyHaveInstallationQualification}">
                                                        <i class="iconbluesigh ml4 contorlIcon"></i>
                                                    </c:if>
                                                </span>
                                                <div class="pos_abs customernameshow">
                                                    需审批新值:
                                                    <c:choose>
                                                        <c:when test="${afterSaleServiceStandardApply.installPolicyHaveInstallationQualification == '1'}">
                                                            是
                                                        </c:when>
                                                        <c:when test="${afterSaleServiceStandardApply.installPolicyHaveInstallationQualification == '0'}">
                                                            否
                                                        </c:when>
                                                        <c:otherwise>
                                                            /
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>


                                        </td>
                                        <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                            <td>
                                                <c:choose>
                                                    <c:when test="${afterSaleSupplyPolicy.installPolicyHaveInstallationQualification == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleSupplyPolicy.installPolicyHaveInstallationQualification == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        /
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                        </c:forEach>
                                    </tr>
                                    <tr>
                                        <td>是否免费远程指导装机</td>
                                        <td>

                                            <div class="customername pos_rel">
                                                <span>
                                                    <c:choose>
                                                        <c:when test="${afterSaleServiceStandardInfoDto.installPolicyFreeRemoteInstall == '1'}">
                                                            是
                                                        </c:when>
                                                        <c:when test="${afterSaleServiceStandardInfoDto.installPolicyFreeRemoteInstall == '0'}">
                                                            否
                                                        </c:when>
                                                        <c:otherwise>
                                                            空
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <c:if test="${ afterSaleServiceStandardApply.installPolicyFreeRemoteInstall != afterSaleServiceStandardInfoDto.installPolicyFreeRemoteInstall}">
                                                        <i class="iconbluesigh ml4 contorlIcon"></i>
                                                    </c:if>
                                                </span>
                                                <div class="pos_abs customernameshow">
                                                    需审批新值:
                                                    <c:choose>
                                                        <c:when test="${afterSaleServiceStandardApply.installPolicyFreeRemoteInstall == '1'}">
                                                            是
                                                        </c:when>
                                                        <c:when test="${afterSaleServiceStandardApply.installPolicyFreeRemoteInstall == '0'}">
                                                            否
                                                        </c:when>
                                                        <c:otherwise>
                                                            /
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>

                                        </td>
                                        <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                            <td>
                                                <c:choose>
                                                    <c:when test="${afterSaleSupplyPolicy.installPolicyFreeRemoteInstall == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleSupplyPolicy.installPolicyFreeRemoteInstall == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        /
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                        </c:forEach>
                                    </tr>
                                    <tr>
                                        <td>响应时效</td>
                                        <td>

                                            <div class="customername pos_rel">
                                                <span>
                                                    <c:choose>
                                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.installPolicyResponseTime, ',') == ''
                                                        || fn:contains(afterSaleServiceStandardInfoDto.installPolicyResponseTime,'null')}">
                                                            空
                                                        </c:when>
                                                        <c:otherwise>
                                                            ${fn:replace(afterSaleServiceStandardInfoDto.installPolicyResponseTime, ",", "")}
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <c:if test="${ afterSaleServiceStandardApply.installPolicyResponseTime != afterSaleServiceStandardInfoDto.installPolicyResponseTime}">
                                                        <i class="iconbluesigh ml4 contorlIcon"></i>
                                                    </c:if>
                                                </span>
                                                <div class="pos_abs customernameshow">
                                                    需审批新值:
                                                    <c:choose>
                                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.installPolicyResponseTime, ',') == ''
                                                        || fn:contains(afterSaleServiceStandardApply.installPolicyResponseTime,'null')}">
                                                            /
                                                        </c:when>
                                                        <c:otherwise>
                                                            ${fn:replace(afterSaleServiceStandardApply.installPolicyResponseTime, ",", "")}
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>


                                        </td>
                                        <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                            <td>
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.installPolicyResponseTime, ',') == ''
                                                    || fn:contains(afterSaleSupplyPolicy.installPolicyResponseTime,'null')}">
                                                        /
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleSupplyPolicy.installPolicyResponseTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                        </c:forEach>
                                    </tr>
                                    <tr>
                                        <td>上门时效</td>
                                        <td>
                                            <div class="customername pos_rel">
                                                <span>
                                                    <c:choose>
                                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.installPolicyVisitTime, ',') == ''
                                                        || fn:contains(afterSaleServiceStandardInfoDto.installPolicyVisitTime,'null')}">
                                                            空
                                                        </c:when>
                                                        <c:otherwise>
                                                            ${fn:replace(afterSaleServiceStandardInfoDto.installPolicyVisitTime, ",", "")}
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <c:if test="${ afterSaleServiceStandardApply.installPolicyVisitTime != afterSaleServiceStandardInfoDto.installPolicyVisitTime}">
                                                        <i class="iconbluesigh ml4 contorlIcon"></i>
                                                    </c:if>
                                                </span>
                                                <div class="pos_abs customernameshow">
                                                    需审批新值:
                                                    <c:choose>
                                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.installPolicyVisitTime, ',') == ''
                                                        || fn:contains(afterSaleServiceStandardApply.installPolicyVisitTime,'null')}">
                                                            /
                                                        </c:when>
                                                        <c:otherwise>
                                                            ${fn:replace(afterSaleServiceStandardApply.installPolicyVisitTime, ",", "")}
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </td>
                                        <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                            <td>
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.installPolicyVisitTime, ',') == ''
                                                    || fn:contains(afterSaleSupplyPolicy.installPolicyVisitTime,'null')}">
                                                        /
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleSupplyPolicy.installPolicyVisitTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                        </c:forEach>
                                    </tr>
                                    <tr>
                                        <td>安装时效</td>
                                        <td>
                                            <div class="customername pos_rel">
                                                <span>
                                                    <c:choose>
                                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.installPolicyInstallTime, ',') == ''
                                                        || fn:contains(afterSaleServiceStandardInfoDto.installPolicyInstallTime,'null')}">
                                                            空
                                                        </c:when>
                                                        <c:otherwise>
                                                            ${fn:replace(afterSaleServiceStandardInfoDto.installPolicyInstallTime, ",", "")}
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <c:if test="${ afterSaleServiceStandardApply.installPolicyInstallTime != afterSaleServiceStandardInfoDto.installPolicyInstallTime}">
                                                        <i class="iconbluesigh ml4 contorlIcon"></i>
                                                    </c:if>
                                                </span>
                                                <div class="pos_abs customernameshow">
                                                    需审批新值:
                                                    <c:choose>
                                                        <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.installPolicyInstallTime, ',') == ''
                                                        || fn:contains(afterSaleServiceStandardApply.installPolicyInstallTime,'null')}">
                                                            /
                                                        </c:when>
                                                        <c:otherwise>
                                                            ${fn:replace(afterSaleServiceStandardApply.installPolicyInstallTime, ",", "")}
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </td>
                                        <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                            <td>
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.installPolicyInstallTime, ',') == ''
                                                    || fn:contains(afterSaleSupplyPolicy.installPolicyInstallTime,'null')}">
                                                        /
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleSupplyPolicy.installPolicyInstallTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                        </c:forEach>
                                    </tr>
                                </c:if>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>

                <c:if test="${spuType == '316' or spuType == '1008'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">技术指导</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>是否提供技术维修指导</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.technicalDirectSupplyMaintain == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.technicalDirectSupplyMaintain == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        空
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.technicalDirectSupplyMaintain != afterSaleServiceStandardInfoDto.technicalDirectSupplyMaintain}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardApply.technicalDirectSupplyMaintain == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.technicalDirectSupplyMaintain == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        /
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${afterSaleSupplyPolicy.technicalDirectSupplyMaintain == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.technicalDirectSupplyMaintain == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>响应时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.technicalDirectResponseTime, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardInfoDto.technicalDirectResponseTime,'null')}">
                                                        空
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardInfoDto.technicalDirectResponseTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.technicalDirectResponseTime != afterSaleServiceStandardInfoDto.technicalDirectResponseTime}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.technicalDirectResponseTime, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardApply.technicalDirectResponseTime,'null')}">
                                                        /
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardApply.technicalDirectResponseTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.technicalDirectResponseTime, ',') == ''
                                                || fn:contains(afterSaleSupplyPolicy.technicalDirectResponseTime,'null')}">
                                                    /
                                                </c:when>
                                                <c:otherwise>
                                                    ${fn:replace(afterSaleSupplyPolicy.technicalDirectResponseTime, ",", "")}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>技术指导时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.technicalDirectEffectTime, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardInfoDto.technicalDirectEffectTime,'null')}">
                                                        空
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardInfoDto.technicalDirectEffectTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.technicalDirectEffectTime != afterSaleServiceStandardInfoDto.technicalDirectEffectTime}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.technicalDirectEffectTime, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardApply.technicalDirectEffectTime,'null')}">
                                                        /
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardApply.technicalDirectEffectTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.technicalDirectEffectTime, ',') == ''
                                                || fn:contains(afterSaleSupplyPolicy.technicalDirectEffectTime,'null')}">
                                                    /
                                                </c:when>
                                                <c:otherwise>
                                                    ${fn:replace(afterSaleSupplyPolicy.technicalDirectEffectTime, ",", "")}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <c:if test="${spuType == '316' or spuType == '1008'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">保修政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>是否保修</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                            <c:choose>
                                                <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyIsGuarantee == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyIsGuarantee == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.guaranteePolicyIsGuarantee != afterSaleServiceStandardInfoDto.guaranteePolicyIsGuarantee}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardApply.guaranteePolicyIsGuarantee == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.guaranteePolicyIsGuarantee == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        空
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${afterSaleSupplyPolicy.guaranteePolicyIsGuarantee == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.guaranteePolicyIsGuarantee == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>保修方式</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.guaranteePolicyGuaranteeType,'1')}">
                                                    上门维修
                                                </c:if>
                                                <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.guaranteePolicyGuaranteeType,'2')}">
                                                    寄送修
                                                </c:if>
                                                <c:if test="${ afterSaleServiceStandardApply.guaranteePolicyGuaranteeType != afterSaleServiceStandardInfoDto.guaranteePolicyGuaranteeType}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:if test="${fn:contains(afterSaleServiceStandardApply.guaranteePolicyGuaranteeType,'1')}">
                                                    上门维修
                                                </c:if>
                                                <c:if test="${fn:contains(afterSaleServiceStandardApply.guaranteePolicyGuaranteeType,'2')}">
                                                    寄送修
                                                </c:if>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                                <c:if test="${fn:contains(afterSaleSupplyPolicy.guaranteePolicyGuaranteeType,'1')}">
                                                    上门维修
                                                </c:if>
                                                <c:if test="${fn:contains(afterSaleSupplyPolicy.guaranteePolicyGuaranteeType,'2')}">
                                                    寄送修
                                                </c:if>

                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>主机保修期</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.guaranteePolicyHostGuaranteePeriod, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardInfoDto.guaranteePolicyHostGuaranteePeriod,'null')}">
                                                        空
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardInfoDto.guaranteePolicyHostGuaranteePeriod, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.guaranteePolicyHostGuaranteePeriod != afterSaleServiceStandardInfoDto.guaranteePolicyHostGuaranteePeriod}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.guaranteePolicyHostGuaranteePeriod, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardApply.guaranteePolicyHostGuaranteePeriod,'null')}">
                                                        /
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardApply.guaranteePolicyHostGuaranteePeriod, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.guaranteePolicyHostGuaranteePeriod, ',') == ''
                                                || fn:contains(afterSaleSupplyPolicy.guaranteePolicyHostGuaranteePeriod,'null')}">
                                                    /
                                                </c:when>
                                                <c:otherwise>
                                                    ${fn:replace(afterSaleSupplyPolicy.guaranteePolicyHostGuaranteePeriod, ",", "")}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>配件保修期</td>
                                    <td>

                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.guaranteePolicyPartsGuaranteePeriod, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardInfoDto.guaranteePolicyPartsGuaranteePeriod,'null')}">
                                                        空
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardInfoDto.guaranteePolicyPartsGuaranteePeriod, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.guaranteePolicyPartsGuaranteePeriod != afterSaleServiceStandardInfoDto.guaranteePolicyPartsGuaranteePeriod}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.guaranteePolicyPartsGuaranteePeriod, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardApply.guaranteePolicyPartsGuaranteePeriod,'null')}">
                                                        /
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardApply.guaranteePolicyPartsGuaranteePeriod, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.guaranteePolicyPartsGuaranteePeriod, ',') == ''
                                                || fn:contains(afterSaleSupplyPolicy.guaranteePolicyPartsGuaranteePeriod,'null')}">
                                                    /
                                                </c:when>
                                                <c:otherwise>
                                                    ${fn:replace(afterSaleSupplyPolicy.guaranteePolicyPartsGuaranteePeriod, ",", "")}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>客户签收时间</td>
                                    <td>

                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyCycleCaltype == '1'}">
                                                        出厂时间
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyCycleCaltype == '2'}">
                                                        客户签收时间
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyCycleCaltype == '3'}">
                                                        发票时间
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyCycleCaltype == '4'}">
                                                        安装时间
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyCycleCaltype == '5'}">
                                                        贝登入库时间
                                                    </c:when>
                                                    <c:otherwise>
                                                        空
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.guaranteePolicyCycleCaltype != afterSaleServiceStandardInfoDto.guaranteePolicyCycleCaltype}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardApply.guaranteePolicyCycleCaltype == '1'}">
                                                        出厂时间
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.guaranteePolicyCycleCaltype == '2'}">
                                                        客户签收时间
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.guaranteePolicyCycleCaltype == '3'}">
                                                        发票时间
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.guaranteePolicyCycleCaltype == '4'}">
                                                        安装时间
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.guaranteePolicyCycleCaltype == '5'}">
                                                        贝登入库时间
                                                    </c:when>
                                                    <c:otherwise>
                                                        /
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>

                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${afterSaleSupplyPolicy.guaranteePolicyCycleCaltype == '1'}">
                                                    出厂时间
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.guaranteePolicyCycleCaltype == '2'}">
                                                    客户签收时间
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.guaranteePolicyCycleCaltype == '3'}">
                                                    发票时间
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.guaranteePolicyCycleCaltype == '4'}">
                                                    安装时间
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.guaranteePolicyCycleCaltype == '5'}">
                                                    贝登入库时间
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>保修区域</td>
                                    <td>

                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyArea == '1'}">
                                                        全国
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.guaranteePolicyArea == '2'}">
                                                        部分区域
                                                    </c:when>
                                                    <c:otherwise>
                                                        空
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.guaranteePolicyArea != afterSaleServiceStandardInfoDto.guaranteePolicyArea}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardApply.guaranteePolicyArea == '1'}">
                                                        全国
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.guaranteePolicyArea == '2'}">
                                                        部分区域
                                                    </c:when>
                                                    <c:otherwise>
                                                        /
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>

                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${afterSaleSupplyPolicy.guaranteePolicyArea == '1'}">
                                                    全国
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.guaranteePolicyArea == '2'}">
                                                    部分区域
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>保修区域说明</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                ${afterSaleServiceStandardInfoDto.guaranteePolicyAreaComment}
                                                <c:if test="${ afterSaleServiceStandardApply.guaranteePolicyAreaComment != afterSaleServiceStandardInfoDto.guaranteePolicyAreaComment}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:${afterSaleServiceStandardApply.guaranteePolicyAreaComment}
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                             ${afterSaleSupplyPolicy.guaranteePolicyAreaComment}
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>响应时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.guaranteePolicyResponseTime, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardInfoDto.guaranteePolicyResponseTime,'null')}">
                                                        空
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardInfoDto.guaranteePolicyResponseTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.guaranteePolicyResponseTime != afterSaleServiceStandardInfoDto.guaranteePolicyResponseTime}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.guaranteePolicyResponseTime, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardApply.guaranteePolicyResponseTime,'null')}">
                                                        /
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardApply.guaranteePolicyResponseTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.guaranteePolicyResponseTime, ',') == ''
                                                || fn:contains(afterSaleSupplyPolicy.guaranteePolicyResponseTime,'null')}">
                                                    /
                                                </c:when>
                                                <c:otherwise>
                                                    ${fn:replace(afterSaleSupplyPolicy.guaranteePolicyResponseTime, ",", "")}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>上门时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.guaranteePolicyVisitTime, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardInfoDto.guaranteePolicyVisitTime,'null')}">
                                                        空
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardInfoDto.guaranteePolicyVisitTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.guaranteePolicyVisitTime != afterSaleServiceStandardInfoDto.guaranteePolicyVisitTime}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.guaranteePolicyVisitTime, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardApply.guaranteePolicyVisitTime,'null')}">
                                                        /
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardApply.guaranteePolicyVisitTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.guaranteePolicyVisitTime, ',') == ''
                                                || fn:contains(afterSaleSupplyPolicy.guaranteePolicyVisitTime,'null')}">
                                                    /
                                                </c:when>
                                                <c:otherwise>
                                                    ${fn:replace(afterSaleSupplyPolicy.guaranteePolicyVisitTime, ",", "")}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>维修时效</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.guaranteePolicyRepaireTime, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardInfoDto.guaranteePolicyRepaireTime,'null')}">
                                                        空
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardInfoDto.guaranteePolicyRepaireTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.guaranteePolicyRepaireTime != afterSaleServiceStandardInfoDto.guaranteePolicyRepaireTime}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.guaranteePolicyRepaireTime, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardApply.guaranteePolicyRepaireTime,'null')}">
                                                        /
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardApply.guaranteePolicyRepaireTime, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.guaranteePolicyRepaireTime, ',') == ''
                                                || fn:contains(afterSaleSupplyPolicy.guaranteePolicyRepaireTime,'null')}">
                                                    /
                                                </c:when>
                                                <c:otherwise>
                                                    ${fn:replace(afterSaleSupplyPolicy.guaranteePolicyRepaireTime, ",", "")}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>保修备注</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                ${afterSaleServiceStandardInfoDto.guaranteePolicyRepaireComment}
                                                <c:if test="${ afterSaleServiceStandardApply.guaranteePolicyRepaireComment != afterSaleServiceStandardInfoDto.guaranteePolicyRepaireComment}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:${afterSaleServiceStandardApply.guaranteePolicyRepaireComment}
                                            </div>
                                         </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                                ${afterSaleSupplyPolicy.guaranteePolicyRepaireComment}
                                        </td>
                                    </c:forEach>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>

                <c:if test="${spuType == '316' or spuType == '1008' or spuType == '317' or spuType == '318'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">退货政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td>是否支持退货</td>
                                <td>

                                    <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.returnPolicySupportReturn == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.returnPolicySupportReturn == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        空
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.returnPolicySupportReturn != afterSaleServiceStandardInfoDto.returnPolicySupportReturn}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:
                                            <c:choose>
                                                <c:when test="${afterSaleServiceStandardApply.returnPolicySupportReturn == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.returnPolicySupportReturn == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                        <c:choose>
                                            <c:when test="${afterSaleSupplyPolicy.returnPolicySupportReturn == '1'}">
                                                是
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.returnPolicySupportReturn == '0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                /
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>退货条件</td>
                                <td>

                                    <div class="customername pos_rel">
                                        <span>
                                            ${afterSaleServiceStandardInfoDto.returnPolicyCondition}
                                            <c:if test="${ afterSaleServiceStandardApply.returnPolicyCondition != afterSaleServiceStandardInfoDto.returnPolicyCondition}">
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </c:if>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:${afterSaleServiceStandardApply.returnPolicyCondition}
                                        </div>
                                    </div>

                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                            ${afterSaleSupplyPolicy.returnPolicyCondition}
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>是否需要鉴定</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <c:choose>
                                                <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyNeedIdentify == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyNeedIdentify == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    空
                                                </c:otherwise>
                                            </c:choose>
                                            <c:if test="${ afterSaleServiceStandardApply.returnPolicyNeedIdentify != afterSaleServiceStandardInfoDto.returnPolicyNeedIdentify}">
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </c:if>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:
                                            <c:choose>
                                                <c:when test="${afterSaleServiceStandardApply.returnPolicyNeedIdentify == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.returnPolicyNeedIdentify == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                        <c:choose>
                                            <c:when test="${afterSaleSupplyPolicy.returnPolicyNeedIdentify == '1'}">
                                                是
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.returnPolicyNeedIdentify == '0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                /
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>鉴定方式</td>
                                <td>
                                    <div class="customername pos_rel">
                                            <span>
                                                <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.returnPolicyIdentifyType,'0')}">
                                                    电话鉴定
                                                </c:if>
                                                <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.returnPolicyIdentifyType,'1')}">
                                                    上门鉴定
                                                </c:if>
                                                <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.returnPolicyIdentifyType,'2')}">
                                                    返厂鉴定
                                                </c:if>
                                                <c:if test="${ afterSaleServiceStandardApply.returnPolicyIdentifyType != afterSaleServiceStandardInfoDto.returnPolicyIdentifyType}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:
                                            <c:if test="${fn:contains(afterSaleServiceStandardApply.returnPolicyIdentifyType,'0')}">
                                                电话鉴定
                                            </c:if>
                                            <c:if test="${fn:contains(afterSaleServiceStandardApply.returnPolicyIdentifyType,'1')}">
                                                上门鉴定
                                            </c:if>
                                            <c:if test="${fn:contains(afterSaleServiceStandardApply.returnPolicyIdentifyType,'2')}">
                                                返厂鉴定
                                            </c:if>
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                        <c:if test="${fn:contains(afterSaleSupplyPolicy.returnPolicyIdentifyType,'0')}">
                                            电话鉴定
                                        </c:if>
                                        <c:if test="${fn:contains(afterSaleSupplyPolicy.returnPolicyIdentifyType,'1')}">
                                            上门鉴定
                                        </c:if>
                                        <c:if test="${fn:contains(afterSaleSupplyPolicy.returnPolicyIdentifyType,'2')}">
                                            返厂鉴定
                                        </c:if>
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>退货期限</td>
                                <td>
                                    <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.returnPolicyReturnPeriod, ',') == ''
                                                    || fn:contains(afterSaleServiceStandardInfoDto.returnPolicyReturnPeriod,'null')}">
                                                        空
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${fn:replace(afterSaleServiceStandardInfoDto.returnPolicyReturnPeriod, ",", "")}
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.returnPolicyReturnPeriod != afterSaleServiceStandardInfoDto.returnPolicyReturnPeriod}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                        <div class="pos_abs customernameshow">
                                            原值:
                                            <c:choose>
                                                <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.returnPolicyReturnPeriod, ',') == ''
                                                || fn:contains(afterSaleServiceStandardApply.returnPolicyReturnPeriod,'null')}">
                                                    /
                                                </c:when>
                                                <c:otherwise>
                                                    ${fn:replace(afterSaleServiceStandardApply.returnPolicyReturnPeriod, ",", "")}
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                        <c:choose>
                                            <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.returnPolicyReturnPeriod, ',') == ''
                                            || fn:contains(afterSaleSupplyPolicy.returnPolicyReturnPeriod,'null')}">
                                                /
                                            </c:when>
                                            <c:otherwise>
                                                ${fn:replace(afterSaleSupplyPolicy.returnPolicyReturnPeriod, ",", "")}
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>周期计算方式</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <c:choose>
                                                <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '1'}">
                                                    出厂时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '2'}">
                                                    客户签收时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '3'}">
                                                    发票时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '4'}">
                                                    安装时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp == '5'}">
                                                    贝登入库时间
                                                </c:when>
                                                <c:otherwise>
                                                    空
                                                </c:otherwise>
                                            </c:choose>
                                            <c:if test="${ afterSaleServiceStandardApply.returnPolicyCycleCaltyp != afterSaleServiceStandardInfoDto.returnPolicyCycleCaltyp}">
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </c:if>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:
                                            <c:choose>
                                                <c:when test="${afterSaleServiceStandardApply.returnPolicyCycleCaltyp == '1'}">
                                                    出厂时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.returnPolicyCycleCaltyp == '2'}">
                                                    客户签收时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.returnPolicyCycleCaltyp == '3'}">
                                                    发票时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.returnPolicyCycleCaltyp == '4'}">
                                                    安装时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.returnPolicyCycleCaltyp == '5'}">
                                                    贝登入库时间
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                        <c:choose>
                                            <c:when test="${afterSaleSupplyPolicy.returnPolicyCycleCaltyp == '1'}">
                                                出厂时间
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.returnPolicyCycleCaltyp == '2'}">
                                                客户签收时间
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.returnPolicyCycleCaltyp == '3'}">
                                                发票时间
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.returnPolicyCycleCaltyp == '4'}">
                                                安装时间
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.returnPolicyCycleCaltyp == '5'}">
                                                贝登入库时间
                                            </c:when>
                                            <c:otherwise>
                                                /
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>包装要求</td>
                                <td>
                                    <div class="customername pos_rel">
                                            <span>
                                                ${afterSaleServiceStandardInfoDto.returnPolicyPackagingRequirements}
                                                <c:if test="${ afterSaleServiceStandardApply.returnPolicyPackagingRequirements != afterSaleServiceStandardInfoDto.returnPolicyPackagingRequirements}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:${afterSaleServiceStandardApply.returnPolicyPackagingRequirements}
                                        </div>
                                    </div>

                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                            ${afterSaleSupplyPolicy.returnPolicyPackagingRequirements}
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>退货备注</td>
                                <td>

                                            <div class="customername pos_rel">
                                            <span>
                                                    ${afterSaleServiceStandardInfoDto.returnPolicyReturnComments}
                                                <c:if test="${ afterSaleServiceStandardApply.returnPolicyReturnComments != afterSaleServiceStandardInfoDto.returnPolicyReturnComments}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                                <div class="pos_abs customernameshow">
                                                    需审批新值:${afterSaleServiceStandardApply.returnPolicyReturnComments}
                                                </div>
                                            </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                            ${afterSaleSupplyPolicy.returnPolicyReturnComments}
                                    </td>
                                </c:forEach>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>

                <c:if test="${spuType == '316' or spuType == '1008' or spuType == '317' or spuType == '318'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">换货政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td>是否支持换货</td>
                                <td>
                                    <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicySupportChange == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicySupportChange == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        空
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.exchangePolicySupportChange != afterSaleServiceStandardInfoDto.exchangePolicySupportChange}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:
                                            <c:choose>
                                                <c:when test="${afterSaleServiceStandardApply.exchangePolicySupportChange == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.exchangePolicySupportChange == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                        <c:choose>
                                            <c:when test="${afterSaleSupplyPolicy.exchangePolicySupportChange == '1'}">
                                                是
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.exchangePolicySupportChange == '0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                /
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>换货条件</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            ${afterSaleServiceStandardInfoDto.exchangePolicyExchangeContition}
                                            <c:if test="${ afterSaleServiceStandardApply.exchangePolicyExchangeContition != afterSaleServiceStandardInfoDto.exchangePolicyExchangeContition}">
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </c:if>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:${afterSaleServiceStandardApply.exchangePolicyExchangeContition}
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                            ${afterSaleSupplyPolicy.exchangePolicyExchangeContition}
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>是否需要鉴定</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <c:choose>
                                                <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicyNeedIdentify == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicyNeedIdentify == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    空
                                                </c:otherwise>
                                            </c:choose>
                                            <c:if test="${ afterSaleServiceStandardApply.exchangePolicyNeedIdentify != afterSaleServiceStandardInfoDto.exchangePolicyNeedIdentify}">
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </c:if>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:
                                            <c:choose>
                                                <c:when test="${afterSaleServiceStandardApply.exchangePolicyNeedIdentify == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.exchangePolicyNeedIdentify == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                        <c:choose>
                                            <c:when test="${afterSaleSupplyPolicy.exchangePolicyNeedIdentify == '1'}">
                                                是
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.exchangePolicyNeedIdentify == '0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                /
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>鉴定方式</td>
                                <td>
                                    <div class="customername pos_rel">
                                            <span>
                                                <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.exchangePolicyIdentifyType,'0')}">
                                                    电话鉴定
                                                </c:if>
                                                <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.exchangePolicyIdentifyType,'1')}">
                                                    上门鉴定
                                                </c:if>
                                                <c:if test="${fn:contains(afterSaleServiceStandardInfoDto.exchangePolicyIdentifyType,'2')}">
                                                    返厂鉴定
                                                </c:if>
                                                <c:if test="${ afterSaleServiceStandardApply.exchangePolicyIdentifyType != afterSaleServiceStandardInfoDto.exchangePolicyIdentifyType}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:
                                            <c:if test="${fn:contains(afterSaleServiceStandardApply.exchangePolicyIdentifyType,'0')}">
                                                电话鉴定
                                            </c:if>
                                            <c:if test="${fn:contains(afterSaleServiceStandardApply.exchangePolicyIdentifyType,'1')}">
                                                上门鉴定
                                            </c:if>
                                            <c:if test="${fn:contains(afterSaleServiceStandardApply.exchangePolicyIdentifyType,'2')}">
                                                返厂鉴定
                                            </c:if>
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                        <span>
                                            <c:if test="${fn:contains(afterSaleSupplyPolicy.exchangePolicyIdentifyType,'0')}">
                                                电话鉴定
                                            </c:if>
                                            <c:if test="${fn:contains(afterSaleSupplyPolicy.exchangePolicyIdentifyType,'1')}">
                                                上门鉴定
                                            </c:if>
                                            <c:if test="${fn:contains(afterSaleSupplyPolicy.exchangePolicyIdentifyType,'2')}">
                                                返厂鉴定
                                            </c:if>
                                        </span>
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>换货期限</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <c:choose>
                                                <c:when test="${fn:substringBefore(afterSaleServiceStandardInfoDto.exchangePolicyExchangePeriod, ',') == ''
                                                || fn:contains(afterSaleServiceStandardInfoDto.exchangePolicyExchangePeriod,'null')}">
                                                    空
                                                </c:when>
                                                <c:otherwise>
                                                    ${fn:replace(afterSaleServiceStandardInfoDto.exchangePolicyExchangePeriod, ",", "")}
                                                </c:otherwise>
                                            </c:choose>
                                            <c:if test="${ afterSaleServiceStandardApply.exchangePolicyExchangePeriod != afterSaleServiceStandardInfoDto.exchangePolicyExchangePeriod}">
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </c:if>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:
                                            <c:choose>
                                                <c:when test="${fn:substringBefore(afterSaleServiceStandardApply.exchangePolicyExchangePeriod, ',') == ''
                                                || fn:contains(afterSaleServiceStandardApply.exchangePolicyExchangePeriod,'null')}">
                                                    /
                                                </c:when>
                                                <c:otherwise>
                                                    ${fn:replace(afterSaleServiceStandardApply.exchangePolicyExchangePeriod, ",", "")}
                                                </c:otherwise>
                                            </c:choose>
                                        </div>

                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                        <c:choose>
                                            <c:when test="${fn:substringBefore(afterSaleSupplyPolicy.exchangePolicyExchangePeriod, ',') == ''
                                            || fn:contains(afterSaleSupplyPolicy.exchangePolicyExchangePeriod,'null')}">
                                                /
                                            </c:when>
                                            <c:otherwise>
                                                ${fn:replace(afterSaleSupplyPolicy.exchangePolicyExchangePeriod, ",", "")}
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>周期计算方式</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            <c:choose>
                                                <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicyCycleCaltyp == '1'}">
                                                    出厂时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicyCycleCaltyp == '2'}">
                                                    客户签收时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicyCycleCaltyp == '3'}">
                                                    发票时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicyCycleCaltyp == '4'}">
                                                    安装时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardInfoDto.exchangePolicyCycleCaltyp == '5'}">
                                                    贝登入库时间
                                                </c:when>
                                                <c:otherwise>
                                                    空
                                                </c:otherwise>
                                            </c:choose>
                                            <c:if test="${ afterSaleServiceStandardApply.exchangePolicyCycleCaltyp != afterSaleServiceStandardInfoDto.exchangePolicyCycleCaltyp}">
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </c:if>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:
                                            <c:choose>
                                                <c:when test="${afterSaleServiceStandardApply.exchangePolicyCycleCaltyp == '1'}">
                                                    出厂时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.exchangePolicyCycleCaltyp == '2'}">
                                                    客户签收时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.exchangePolicyCycleCaltyp == '3'}">
                                                    发票时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.exchangePolicyCycleCaltyp == '4'}">
                                                    安装时间
                                                </c:when>
                                                <c:when test="${afterSaleServiceStandardApply.exchangePolicyCycleCaltyp == '5'}">
                                                    贝登入库时间
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                        <c:choose>
                                            <c:when test="${afterSaleSupplyPolicy.exchangePolicyCycleCaltyp == '1'}">
                                                出厂时间
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.exchangePolicyCycleCaltyp == '2'}">
                                                客户签收时间
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.exchangePolicyCycleCaltyp == '3'}">
                                                发票时间
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.exchangePolicyCycleCaltyp == '4'}">
                                                安装时间
                                            </c:when>
                                            <c:when test="${afterSaleSupplyPolicy.exchangePolicyCycleCaltyp == '5'}">
                                                贝登入库时间
                                            </c:when>
                                            <c:otherwise>
                                                /
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>包装要求</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            ${afterSaleServiceStandardInfoDto.exchangePolicyPackagingRequirements}
                                            <c:if test="${ afterSaleServiceStandardApply.exchangePolicyPackagingRequirements != afterSaleServiceStandardInfoDto.exchangePolicyPackagingRequirements}">
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </c:if>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:${afterSaleServiceStandardApply.exchangePolicyPackagingRequirements}
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                            ${afterSaleSupplyPolicy.exchangePolicyPackagingRequirements}
                                    </td>
                                </c:forEach>
                            </tr>
                            <tr>
                                <td>换货备注</td>
                                <td>
                                    <div class="customername pos_rel">
                                        <span>
                                            ${afterSaleServiceStandardInfoDto.exchangePolicyExchangeComments}
                                            <c:if test="${ afterSaleServiceStandardApply.exchangePolicyExchangeComments != afterSaleServiceStandardInfoDto.exchangePolicyExchangeComments}">
                                                <i class="iconbluesigh ml4 contorlIcon"></i>
                                            </c:if>
                                        </span>
                                        <div class="pos_abs customernameshow">
                                            需审批新值:${afterSaleServiceStandardApply.exchangePolicyExchangeComments}
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                    <td>
                                            ${afterSaleSupplyPolicy.exchangePolicyExchangeComments}
                                    </td>
                                </c:forEach>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>

                <c:if test="${spuType == '316'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">保外政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>是否提供有偿维修</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupportRepair == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupportRepair == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        空
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.parolePolicySupportRepair != afterSaleServiceStandardInfoDto.parolePolicySupportRepair}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardApply.parolePolicySupportRepair == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.parolePolicySupportRepair == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        /
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${afterSaleSupplyPolicy.parolePolicySupportRepair == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.parolePolicySupportRepair == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>是否提供翻新服务</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupportRenovation == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupportRenovation == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        空
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.parolePolicySupportRenovation != afterSaleServiceStandardInfoDto.parolePolicySupportRenovation}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardApply.parolePolicySupportRenovation == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.parolePolicySupportRenovation == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        /
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${afterSaleSupplyPolicy.parolePolicySupportRenovation == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.parolePolicySupportRenovation == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>是否提供纸箱</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupplyBox == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupplyBox == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        空
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.parolePolicySupplyBox != afterSaleServiceStandardInfoDto.parolePolicySupplyBox}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardApply.parolePolicySupplyBox == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.parolePolicySupplyBox == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        /
                                                    </c:otherwise>
                                                </c:choose>

                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${afterSaleSupplyPolicy.parolePolicySupplyBox == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.parolePolicySupplyBox == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>是否提供附件</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupplyAttachment == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.parolePolicySupplyAttachment == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        空
                                                    </c:otherwise>
                                                </c:choose>

                                                <c:if test="${ afterSaleServiceStandardApply.parolePolicySupplyAttachment != afterSaleServiceStandardInfoDto.parolePolicySupplyAttachment}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardApply.parolePolicySupplyAttachment == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.parolePolicySupplyAttachment == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        /
                                                    </c:otherwise>
                                                </c:choose>

                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${afterSaleSupplyPolicy.parolePolicySupplyAttachment == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.parolePolicySupplyAttachment == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>

                <c:if test="${spuType == '316' or spuType == '1008' or spuType == '317' or spuType == '318'}">
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">超期处理政策</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>可否提供备用机</td>
                                    <td>

                                        <div class="customername pos_rel">
                                            <span>
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.overduePolicySupplyBackup == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardInfoDto.overduePolicySupplyBackup == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        空
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:if test="${ afterSaleServiceStandardApply.overduePolicySupplyBackup != afterSaleServiceStandardInfoDto.overduePolicySupplyBackup}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:
                                                <c:choose>
                                                    <c:when test="${afterSaleServiceStandardApply.overduePolicySupplyBackup == '1'}">
                                                        是
                                                    </c:when>
                                                    <c:when test="${afterSaleServiceStandardApply.overduePolicySupplyBackup == '0'}">
                                                        否
                                                    </c:when>
                                                    <c:otherwise>
                                                        /
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${afterSaleSupplyPolicy.overduePolicySupplyBackup == '1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${afterSaleSupplyPolicy.overduePolicySupplyBackup == '0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    /
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td>超期处理详情</td>
                                    <td>
                                        <div class="customername pos_rel">
                                            <span>
                                                ${afterSaleServiceStandardInfoDto.overduePolicyDetail}
                                                <c:if test="${ afterSaleServiceStandardApply.overduePolicyDetail != afterSaleServiceStandardInfoDto.overduePolicyDetail}">
                                                    <i class="iconbluesigh ml4 contorlIcon"></i>
                                                </c:if>
                                            </span>
                                            <div class="pos_abs customernameshow">
                                                需审批新值:${afterSaleServiceStandardApply.overduePolicyDetail}
                                            </div>
                                        </div>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                                ${afterSaleSupplyPolicy.overduePolicyDetail}
                                        </td>
                                    </c:forEach>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                </c:if>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">服务联系人</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>联系人</td>
                                    <td>/</td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <div class="title-click pop-new-data" style='float:none;margin:-4px 0 0 10px;' layerParams='{"width":"600px","height":"500px","title":"服务联系人","link":"${pageContext.request.contextPath}/aftersale/serviceStandard/queryServiceContactList.do?traderId=${afterSaleSupplyPolicy.traderId}"}'>
                                                点击查看
                                            </div>
                                        </td>
                                    </c:forEach>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">相关附件</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                                <tr>
                                    <td>附件</td>
                                    <td>
                                        <c:forEach var="atttashment" items="${applyAttashmentList}">
                                            <%--<img src="http://${atttashment.domain}${atttashment.uri}" style="width: 60px;height: 60px"/>--%>
                                            <a href="http://${atttashment.domain}${atttashment.uri}" target="_blank">${atttashment.fileName}</a><br/>
                                        </c:forEach>
                                    </td>
                                    <c:forEach var="afterSaleSupplyPolicy" items="${afterSaleSupplyPolicyList}">
                                        <td>
                                            <c:forEach var="atttashment" items="${afterSaleSupplyPolicy.afterSaleSupplyPolicyAttachmentList}">
                                                <a href="http://${atttashment.domain}${atttashment.uri}" target="_blank">${atttashment.fileName}</a><br/>
                                            </c:forEach>
                                        </td>
                                    </c:forEach>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">售前服务标签</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td style=" text-align: left">
                                    <div class="" pane="" style="margin-left: 2%">
                                        <div style="margin-left: 3%">
                                            <div>
                                                <c:forEach var="label" items="${labels}" >
                                                    <div style="display: inline-block; width: 18%" >
                                                    <label class="input-wrap">
                                                        <div class="customername pos_rel"><input type="checkbox" name="serviceLabels"  <c:if test="${label.checked == 1}"> checked </c:if> value="${label.afterSaleServiceLabelId}" disabled>${label.labelName}<span><i class="iconbluesigh ml4 contorlIcon"></i></span><div class="pos_abs customernameshow">
                                                            <c:choose>
                                                                <c:when test="${label.backUp == '1'}">
                                                                    需审批新值:是
                                                                </c:when>
                                                                <c:when test="${label.backUp == '0'}">
                                                                    需审批新值:否
                                                                </c:when>
                                                                <c:otherwise>
                                                                    ${label.labelDescription}
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div></div>
                                                    </label>
                                                    </div>
                                                </c:forEach>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                <%--<li>
                    <div class="parts">
                        <div class="title-container" style="text-align:center">
                            <span class="bt-small bt-bg-style bg-light-blue" onclick="auditPass();"     style="margin-left: 100px;margin-top: 5px">审核通过</span>
                            <span class="bt-small bt-bg-style bg-light-blue" onclick="auditNotPass();"  style="margin-left: 20px;margin-top: 5px">审核不通过</span>
                        </div>
                    </div>
                </li>--%>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">审核记录</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">操作人</th>
                                <th style="width:80px">操作时间</th>
                                <th style="width:80px">操作事项</th>
                                <th style="width:80px">备注</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                                <c:if test="${not empty  hi.activityName}">
                                    <tr>
                                        <td>
                                            <c:choose>
                                                <c:when test="${hi.activityType == 'startEvent'}">
                                                    ${startUser}
                                                </c:when>
                                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                </c:when>
                                                <c:otherwise>
                                                    <c:if test="${historicActivityInstance.size() == status.count}">
                                                        ${verifyUsers}
                                                    </c:if>
                                                    <c:if test="${historicActivityInstance.size() != status.count}">
                                                        ${hi.assignee}
                                                    </c:if>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${hi.activityType == 'startEvent'}">
                                                    开始
                                                </c:when>
                                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                    结束
                                                </c:when>
                                                <c:otherwise>
                                                    ${hi.activityName}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td class="font-red">${commentMap[hi.taskId]}</td>
                                    </tr>
                                </c:if>
                            </c:forEach>
                            <!-- 查询无结果弹出 -->

                            <c:if test="${empty historicActivityInstance}">
                                <!-- 查询无结果弹出 -->
                                <tr>
                                    <td colspan="4">暂无审核记录。</td>
                                </tr>
                            </c:if>

                            <tr>
                                <td colspan="4">
                                    <c:if test="${null!=historicInfo.taskInfo and (taskInfo.assignee == curr_user.username or historicInfo.candidateUserMap['belong']) }">
                                        <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&serviceStandardApplyId=${afterSaleServiceStandardApply.serviceStandardApplyId}&pass=true"}'>审核通过</button>
                                        <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&serviceStandardApplyId=${skuPriceChangeApplyDto.serviceStandardApplyId}&pass=false"}'>审核不通过</button>
                                    </c:if>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

            </ul>
        </form>
    </div>
</div>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/dialogSelectAndEdit.js?rnd=${resourceVersionKey}"></script>
<script>
    $(function () {
        new SelectEdit({
            button: '.J-area-select',
            url: page_url + '/system/region/getregion.do?isBring=1',
            input: '.J-area-value',
            onlyShow: true
        });
    })
</script>
<%@ include file="../../common/footer.jsp"%>