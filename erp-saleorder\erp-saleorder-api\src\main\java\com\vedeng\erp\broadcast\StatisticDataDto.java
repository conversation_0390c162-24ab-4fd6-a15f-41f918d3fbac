package com.vedeng.erp.broadcast;

import java.util.List;
import lombok.Data;

/**
 * 到款播报统计数据
 * @ClassName:  StatisticDataDto   
 * @author: <PERSON>.yang
 * @date:   2025年7月18日 上午10:46:22    
 * @Copyright:
 */
@Data
public class StatisticDataDto {
	
	/**日到款*/
	private List<StatisticsData> incomeDayDataList;
	
	/**月到款*/
	private List<StatisticsData> incomeMonthDataList;
	
	/**AED出库量*/
	private List<StatisticsData> aedNumDataList;
	
	/**自有品牌商品出库金额*/
	private List<StatisticsData> vdAmountDataList;
	
	@Data
	public static class StatisticsData{
		/**类型【1：个人；2：小组；3：部门】*/
		private Integer type;
		
		/**榜单分类图片*/
		private String picUrl;
		
		/**对应的数据*/
		private List<StatisticsLineData> statisticsLineDataList;
	}
	
	@Data
	public static class StatisticsLineData{

	    /**用户ID（erp系统中的USER_ID）*/
	    private Integer userId;

	    /**用户名*/
	    private String userName;
	    
	    /**用户头像*/
	    private String userAliasHeadPic;

	    /**小组ID*/
	    private Integer teamId;

	    /**小组名*/
	    private String teamName;

	    /**部门ID*/
	    private Integer deptId;

	    /**部门名*/
	    private String deptName;

	    /**到款金额*/
	    private String amount;
	    
	    /**达成率*/
        private Integer archievedPrecent;

        /**出库量*/
        private Integer warehouseNum;
        
        /**出库金额*/
        private String warehouseAmount;
		
	}

}
