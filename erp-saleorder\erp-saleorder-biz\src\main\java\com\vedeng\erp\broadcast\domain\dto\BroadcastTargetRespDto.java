package com.vedeng.erp.broadcast.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 业绩目标查询响应DTO
 */
@Data
public class BroadcastTargetRespDto {

    /**
     * 目标对象名称
     */
    private String targetName;

    /**
     * 目标业务ID
     */
    private Integer targetBuzId;

    /**
     * 年度
     */
    private Integer targetYear;

    /**
     * 目标类型
     * 1-个人，2-小组，3-部门
     */
    private Integer targetType;

    /**
     * 1月目标
     */
    private BigDecimal month1;

    /**
     * 2月目标
     */
    private BigDecimal month2;

    /**
     * 3月目标
     */
    private BigDecimal month3;

    /**
     * 4月目标
     */
    private BigDecimal month4;

    /**
     * 5月目标
     */
    private BigDecimal month5;

    /**
     * 6月目标
     */
    private BigDecimal month6;

    /**
     * 7月目标
     */
    private BigDecimal month7;

    /**
     * 8月目标
     */
    private BigDecimal month8;

    /**
     * 9月目标
     */
    private BigDecimal month9;

    /**
     * 10月目标
     */
    private BigDecimal month10;

    /**
     * 11月目标
     */
    private BigDecimal month11;

    /**
     * 12月目标
     */
    private BigDecimal month12;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 获取指定月份的目标金额
     */
    public BigDecimal getMonthTarget(int month) {
        switch (month) {
            case 1: return month1;
            case 2: return month2;
            case 3: return month3;
            case 4: return month4;
            case 5: return month5;
            case 6: return month6;
            case 7: return month7;
            case 8: return month8;
            case 9: return month9;
            case 10: return month10;
            case 11: return month11;
            case 12: return month12;
            default: return null;
        }
    }

    /**
     * 设置指定月份的目标金额
     */
    public void setMonthTarget(int month, BigDecimal amount) {
        switch (month) {
            case 1: month1 = amount; break;
            case 2: month2 = amount; break;
            case 3: month3 = amount; break;
            case 4: month4 = amount; break;
            case 5: month5 = amount; break;
            case 6: month6 = amount; break;
            case 7: month7 = amount; break;
            case 8: month8 = amount; break;
            case 9: month9 = amount; break;
            case 10: month10 = amount; break;
            case 11: month11 = amount; break;
            case 12: month12 = amount; break;
        }
    }
}
