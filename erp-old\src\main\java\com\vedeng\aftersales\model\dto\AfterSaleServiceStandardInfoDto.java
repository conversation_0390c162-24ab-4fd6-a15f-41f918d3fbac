package com.vedeng.aftersales.model.dto;

import com.vedeng.aftersales.model.AfterSaleServiceStandardInfo;
import com.vedeng.aftersales.model.AfterSaleServiceStandardInfoInstallArea;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class AfterSaleServiceStandardInfoDto extends AfterSaleServiceStandardInfo {

    private AfterSaleServiceStandardInfoInstallArea installArea;

    public AfterSaleServiceStandardInfoInstallArea getInstallArea() {
        return installArea;
    }

    public void setInstallArea(AfterSaleServiceStandardInfoInstallArea installArea) {
        this.installArea = installArea;
    }


    /**
     * 安装响应时效
     */
    private String installPolicyResponseTimeShow;
    /**
     * 技术指导响应时效
     */
    private String technicalDirectorEffectTimeShow;
    /**
     * 维修时效
     */
    private String guaranteePolicyRepaireTimeShow;


    private Integer spuType;



    public String getInstallPolicyResponseTimeShow() {
        return transTimeToString(getInstallPolicyResponseTime());
    }
    public String getTechnicalDirectorEffectTimeShow() {
        return transTimeToString(getTechnicalDirectEffectTime());
    }
    public String getGuaranteePolicyRepaireTimeShow() {
        return transTimeToString(getGuaranteePolicyRepaireTime());
    }
    /**
     * 转换数据库中的异常数据
     * @param dbtime
     * @return
     */
    private String transTimeToString(String dbtime){
        if (dbtime == null || dbtime.isEmpty()) {
            return "-";
        }
        if(StringUtils.contains(dbtime, "null")) {
            return "-";
        }
        String[] d=dbtime.split(",");
        if(d.length!=2||d.length<0||StringUtils.isBlank(d[0])) {
            return "-";
        }
        StringBuilder result=new StringBuilder();
        result.append("≤");
        result.append(d[0]);
        //先保留，原需求是 展示为字母
        if(d[1].equals("小时")){
            result.append("小时");
        }
        else if(d[1].equals("天")){
            result.append("天");
        }
        else if(d[1].equals("月")){
            result.append("月");
        }
        else if(d[1].equals("年")){
            result.append("年");
        }
        else{
            return "-";
        }
        return result.toString();
    }

    public Integer getSpuType() {
        return spuType;
    }

    public void setSpuType(Integer spuType) {
        this.spuType = spuType;
    }
}