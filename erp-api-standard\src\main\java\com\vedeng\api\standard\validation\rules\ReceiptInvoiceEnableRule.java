package com.vedeng.api.standard.validation.rules;

import com.google.common.collect.Lists;
import com.vedeng.api.standard.adapter.receiptInvoice.dto.ReceiptInvoiceRequest;
import com.vedeng.api.standard.validation.ValidationContextKeys;
import com.vedeng.api.standard.validation.ValidationResult;
import com.vedeng.api.standard.validation.ValidationRule;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.dto.BuyOrderInfoDto;
import com.vedeng.erp.buyorder.dto.InvoiceSaveSearchDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.dto.InvoiceGoodsDto;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ReceiptInvoiceEnableRule implements ValidationRule<ReceiptInvoiceRequest> {
    
    @Autowired
    private InvoiceApiService invoiceApiService;
    
    @Autowired
    private InvoiceService invoiceService;
    
    @Autowired
    private BuyorderApiService buyorderApiService;

    @Override
    public ValidationResult validate(ReceiptInvoiceRequest request, Map<String, Object> context) {
        log.info("开始验证开票是否可用: invoiceNo={}", request.getInvoiceNo());
        BuyOrderApiDto buyOrderApiDto = buyorderApiService.getBuyorderByBuyorderNo(request.getBuyOrderNo());
        // 判断是否已生成
        InvoiceDto invoiceDto = invoiceApiService.queryReceiptInvoiceRecord(request.getBuyOrderNo(), request.getInvoiceNo());
        if (Objects.nonNull(invoiceDto)){
            return ValidationResult.failure(getRuleName(), request.getInvoiceNo()+"重复录票");
        }
        BuyOrderInfoDto buyOrderInfo = buyorderApiService.getBuyOrderInfo(buyOrderApiDto.getBuyorderId());
        // 判断是否可录票
        // 1. 采购单生效状态 = 已生效
        if ( !Objects.equals(buyOrderInfo.getValidStatus(),1)){
            log.info("采购单生效状态：{}",buyOrderInfo.getValidStatus());
            return ValidationResult.failure(getRuleName(), request.getInvoiceNo()+"采购单未生效");
        }
        //2. 采购单单据状态 = 进行中
        if (!Objects.equals(buyOrderInfo.getStatus(),1)){
            log.info("采购单单据状态：{}",buyOrderInfo.getStatus());
            return ValidationResult.failure(getRuleName(), request.getInvoiceNo()+"采购单未进行中");
        }
        //3. 采购单锁定状态 = 未锁定
        if (Objects.equals(buyOrderInfo.getLockedStatus(),1)){
            log.info("采购单锁定状态：{}",buyOrderInfo.getLockedStatus());
            return ValidationResult.failure(getRuleName(), request.getInvoiceNo()+"采购单已锁定");
        }
        //4. 采购单录票状态 = 未录票，部分录票
        if (!Objects.equals(buyOrderInfo.getInvoiceStatus(),0) && !Objects.equals(buyOrderInfo.getInvoiceStatus(),1)){
            log.info("采购单录票状态：{}",buyOrderInfo.getInvoiceStatus());
            return ValidationResult.failure(getRuleName(), request.getInvoiceNo()+"采购单非未录票、部分录票");
        }
        //5. 采购单收货状态 = 部分收货，全部收货
        if (!Objects.equals(buyOrderInfo.getArrivalStatus(),1) && !Objects.equals(buyOrderInfo.getArrivalStatus(),2)){
            log.info("采购单收货状态：{}",buyOrderInfo.getArrivalStatus());
            return ValidationResult.failure(getRuleName(), request.getInvoiceNo()+"采购单非部分收货、全部收货");
        }
        
        // 判断录票条件
        InvoiceSaveSearchDto invoiceSaveSearchDto = new InvoiceSaveSearchDto();
        invoiceSaveSearchDto.setOrderNo(buyOrderInfo.getBuyorderNo());
        List<BuyorderGoodsVo> buyOrderInvoiceInfo = invoiceService.getBuyOrderInvoiceInfo(invoiceSaveSearchDto);
        Map<String, BuyorderGoodsVo> goodsVoMap = buyOrderInvoiceInfo.stream().collect(Collectors.toMap(BuyorderGoodsVo::getSku, Function.identity(), (old, newOne) -> old));
        // 可录票数量
        List<InvoiceGoodsDto> invoiceGoods = request.getInvoiceGoods();
        List<Integer> goodsIdList = Lists.newArrayList();
        List<BigDecimal> numList = Lists.newArrayList();
        List<BigDecimal> priceList = Lists.newArrayList();
        List<BigDecimal> totalAmount = Lists.newArrayList();
        for (InvoiceGoodsDto invoiceGoodsDto : invoiceGoods) {
            BuyorderGoodsVo buyorderGoodsVo = goodsVoMap.get(invoiceGoodsDto.getSku());
            if (Objects.isNull(buyorderGoodsVo)){
                return ValidationResult.failure(getRuleName(), invoiceGoodsDto.getSku()+"无此商品");
            }
            // 已入库
            Integer arrivalNum = buyorderGoodsVo.getArrivalNum();
            // 已录票
            BigDecimal invoicedNum = buyorderGoodsVo.getInvoicedNum();
            // 可录票
            BigDecimal canInvoiceNum = new BigDecimal(arrivalNum).subtract(invoicedNum);
            if (canInvoiceNum.compareTo(invoiceGoodsDto.getNum()) < 0){
                return ValidationResult.failure(getRuleName(), invoiceGoodsDto.getSku()+"可录票数量不足");
            }
            goodsIdList.add(buyorderGoodsVo.getBuyorderGoodsId());
            numList.add(canInvoiceNum);
            priceList.add(buyorderGoodsVo.getPrice());
            totalAmount.add(canInvoiceNum.multiply(buyorderGoodsVo.getPrice()));
        }

        InvoiceGoodsDto invoiceGoodsDto = invoiceGoods.get(0);
        Integer invoiceType = invoiceGoodsDto.getInvoiceType();
        
        BigDecimal ratio = invoiceGoodsDto.getRatio();
        BigDecimal amount = invoiceGoodsDto.getAmount();

        //  设置参数
        Invoice invoice = new Invoice();
        invoice.setInvoiceCode("0000000000");
        invoice.setInvoiceProperty(3);
        invoice.setInvoiceNo(request.getInvoiceNo());
        invoice.setInvoiceType(invoiceType);
        invoice.setColorType(2);
        invoice.setIsEnable(1);

        invoice.setRatio(ratio);
        // 求和 reduce
        invoice.setAmount(amount);
        
        // size个503，逗号分割
        String goodsTypeArr = StringUtils.join(Collections.nCopies(goodsIdList.size(), "503"), ",");
        String relateIdArr = StringUtils.join(Collections.nCopies(goodsIdList.size(), buyOrderApiDto.getBuyorderId()), ",");
        
        // 将参数直接存储到验证上下文中，以便后续使用
        context.put(ValidationContextKeys.INVOICE, invoice);
        context.put(ValidationContextKeys.SAVE_INVOICE_TYPE, 1);
        context.put(ValidationContextKeys.RELATED_ID_ARR, relateIdArr);
        context.put(ValidationContextKeys.DETAIL_GOODS_ID_ARR, goodsIdList.toString().substring(1, goodsIdList.toString().length()-1));
        context.put(ValidationContextKeys.INVOICE_NUM_ARR, numList.toString().substring(1, numList.toString().length()-1));
        context.put(ValidationContextKeys.INVOICE_PRICE_ARR, priceList.toString().substring(1, priceList.toString().length()-1));
        context.put(ValidationContextKeys.GOODS_TYPE_ARR, goodsTypeArr);
        context.put(ValidationContextKeys.INVOICE_TOTAL_AMOUNT_ARR, totalAmount.toString().substring(1, totalAmount.toString().length()-1));
        return ValidationResult.success();
    }

    @Override
    public String getRuleName() {
        return "ReceiptInvoiceEnableRule";
    }
}
