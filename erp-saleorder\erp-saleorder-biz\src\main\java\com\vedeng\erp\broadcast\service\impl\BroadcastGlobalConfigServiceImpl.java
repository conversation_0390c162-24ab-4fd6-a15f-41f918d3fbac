package com.vedeng.erp.broadcast.service.impl;

import com.alibaba.fastjson.JSON;
import com.common.dto.SelectDto;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.util.NumberUtil;
import com.vedeng.erp.broadcast.domain.dto.BroadcastDeptConfigDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastGlobalConfigDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptConfigEntity;
import com.vedeng.erp.broadcast.domain.entity.BroadcastGlobalConfigEntity;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptConfigMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastGlobalConfigMapper;
import com.vedeng.erp.broadcast.service.BroadcastGlobalConfigService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;

import com.vedeng.erp.trader.dto.TraderCustomerDto;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.goods.dto.BaseCategoryDto;
import com.vedeng.goods.dto.BrandFrontDto;
import com.vedeng.goods.service.BaseCategoryApiService;
import com.vedeng.goods.service.BrandApiService;
import com.vedeng.goods.service.GoodsApiService;
import com.vedeng.goods.vo.CoreSkuVo;
import io.swagger.models.auth.In;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Arrays;

@Service
public class BroadcastGlobalConfigServiceImpl implements BroadcastGlobalConfigService {

    @Value("${broadcast.job.time:{\"monthJobTime\":\"每月20号后 17:20\",\"zyJobTime\":\"每日 17:40\",\"customJobTime\":\"每日 17:50\",\"aedJobTime\":\"每日 17:30\",\"weekJobTime\":\"每月1-19号 17:20\",\"dayJobTime\":\"每日10:00 - 24:00 间 每1小时\"}}")
    private String brodcastJobTime;
    @Resource
    BroadcastGlobalConfigMapper broadcastGlobalConfigMapper;
    @Resource
    BroadcastDeptConfigMapper broadcastDeptConfigMapper;

    @Resource
    UserApiService userApiService;
    @Resource
    TraderCustomerApiService traderCustomerApiService;
    @Resource
    BaseCategoryApiService baseCategoryApiService;
    @Resource
    BrandApiService brandApiService;
    @Resource
    GoodsApiService goodsApiService;
    /**
     * 获取全局播报配置
     *
     * @return 全局播报配置实体
     */
    @Override
    public BroadcastGlobalConfigDto getGlobalConfig() {
        BroadcastGlobalConfigEntity entity= broadcastGlobalConfigMapper.selectGlobalConfig();
        BroadcastGlobalConfigDto dto=new BroadcastGlobalConfigDto();
        //jobtime
        Map<String,String> jobTimeMap = JSON.parseObject(brodcastJobTime, Map.class);
        dto.setJobTimeStrMap(jobTimeMap);
        if(entity==null){
            return dto;
        }
        //部门配置
        List<BroadcastDeptConfigDto> deptConfigList=broadcastDeptConfigMapper.selectAll();
        dto.setDeptConfigList(deptConfigList);
        //过滤销售
        dto.setExcludeSaleIdList(convertToUserSelectDtoList(entity.getExcludeSaleIds()));
        //过滤AED商品ID
        dto.setAedSkuIdList(convertToSkuSelectDtoList(entity.getAedSkuIds()));
        //过滤客户ID
        dto.setExcludeTraderIdList(convertToTraderSelectDtoList(entity.getExcludeTraderIds()));
        //个人榜单
        dto.setTopnUser(entity.getTopnUser()==null?ErpConst.FIVE:entity.getTopnUser());
        //团队、部门榜单
        dto.setTopnDept(entity.getTopnDept()==null?ErpConst.FIVE:entity.getTopnDept());
        //日常到款播报标题
        dto.setBroadcastTitleDay(entity.getBroadcastTitleDay());
        //周播报标题
        dto.setBroadcastTitleWeek(entity.getBroadcastTitleWeek());
        //月播报标题
        dto.setBroadcastTitleMonth(entity.getBroadcastTitleMonth());
        //AED播报标题
        dto.setBroadcastTitleAed(entity.getBroadcastTitleAed());
        dto.setBroadcastTitleZy(entity.getBroadcastTitleZy());
        //自定义播报标题
        dto.setBroadcastTitleCustom(entity.getBroadcastTitleCustom());
        //统计类型
        dto.setStatType(entity.getStatType()==null?ErpConst.TWO:entity.getStatType());
        //统计日期范围  
        dto.setStatDateRange(entity.getStatDateRange()==null?ErpConst.ONE:entity.getStatDateRange());
        //统计目标
        dto.setStatTarget(entity.getStatTarget()==null?ErpConst.ONE+"":entity.getStatTarget());
        //统计SKU
        dto.setStatSkuIdList(convertToSkuSelectDtoList(entity.getStatSkuIds()));
        //统计品牌
        dto.setStatBrandIdList(convertToBrandSelectDtoList(entity.getStatBrandIds()));
        //统计分类
        dto.setStatCategoryIdList(convertToCategorySelectDtoList(entity.getStatCategoryIds()));
        return dto;
    }



    /**
     * 更新全局播报配置
     *
     * @param configEntity 配置实体
     * @return 更新后的配置实体
     */
    @Override
    @Transactional
    public BroadcastGlobalConfigEntity updateGlobalConfig(BroadcastGlobalConfigDto configEntity, CurrentUser user) {
        //先禁用，再新增
        BroadcastGlobalConfigEntity entity= broadcastGlobalConfigMapper.selectGlobalConfig();
        if (entity != null) {
            BroadcastGlobalConfigEntity deleteEntity = new BroadcastGlobalConfigEntity();
            deleteEntity.setId(entity.getId());
            deleteEntity.setIsDeleted(ErpConst.DELETE_STATE.IS_DELETE);
            deleteEntity.setModTime(new Date());
            deleteEntity.setUpdater(user.getId());
            broadcastGlobalConfigMapper.updateByPrimaryKeySelective(deleteEntity);
        }
        
        // 使用configEntity的数据初始化configEntityToInsert
        BroadcastGlobalConfigEntity configEntityToInsert = new BroadcastGlobalConfigEntity();
        configEntityToInsert.setId(null); // 新增时ID应为null
        configEntityToInsert.setAddTime(new Date());
        configEntityToInsert.setModTime(new Date());
        configEntityToInsert.setCreator(user.getId());
        configEntityToInsert.setUpdater(user.getId());
        configEntityToInsert.setIsDeleted(ErpConst.DELETE_STATE.IS_NOT_DELETE); // 新增时未删除
        
        // 从configEntity复制数据到configEntityToInsert
        configEntityToInsert.setExcludeSaleIds( configEntity.getExcludeSaleIds() );
        configEntityToInsert.setAedSkuIds( configEntity.getAedSkuIds() );
        configEntityToInsert.setExcludeTraderIds( configEntity.getExcludeTraderIds());
        configEntityToInsert.setTopnUser(configEntity.getTopnUser());
        configEntityToInsert.setTopnDept(configEntity.getTopnDept());
        configEntityToInsert.setBroadcastTitleDay(configEntity.getBroadcastTitleDay());
        configEntityToInsert.setBroadcastTitleWeek(configEntity.getBroadcastTitleWeek());
        configEntityToInsert.setBroadcastTitleMonth(configEntity.getBroadcastTitleMonth());
        configEntityToInsert.setBroadcastTitleAed(configEntity.getBroadcastTitleAed());
        configEntityToInsert.setBroadcastTitleZy(configEntity.getBroadcastTitleZy());
        configEntityToInsert.setBroadcastTitleCustom(configEntity.getBroadcastTitleCustom());
        configEntityToInsert.setStatType(configEntity.getStatType()==null?ErpConst.TWO:configEntity.getStatType());
        configEntityToInsert.setStatDateRange(configEntity.getStatDateRange()==null?ErpConst.ONE:configEntity.getStatDateRange());
        configEntityToInsert.setStatTarget(StringUtils.isBlank(configEntity.getStatTarget())?ErpConst.ONE+"":configEntity.getStatTarget());
        configEntityToInsert.setStatSkuIds(configEntity.getStatSkuIds());
        configEntityToInsert.setStatBrandIds( (configEntity.getStatBrandIds()));
        configEntityToInsert.setStatCategoryIds( (configEntity.getStatCategoryIds()));

//        //统计类型
//        dto.setStatType(entity.getStatType()==null?ErpConst.TWO:entity.getStatType());
//        //统计日期范围
//        dto.setStatDateRange(entity.getStatDateRange()==null?ErpConst.ONE:entity.getStatDateRange());
//        //统计目标
//        dto.setStatTarget(entity.getStatTarget()==null?ErpConst.ONE+"":entity.getStatTarget());

        // 插入全局配置
        broadcastGlobalConfigMapper.insert(configEntityToInsert);
        
        // 处理部门配置列表：先删除再新增
        if (CollectionUtils.isNotEmpty(configEntity.getDeptConfigList())) {
            // 1. 先删除所有现有的部门配置
            broadcastDeptConfigMapper.deleteAllBroadcastDeptConfig(user.getId());
            
            // 2. 将BroadcastDeptConfigDto转换为BroadcastDeptConfigEntity
            List<BroadcastDeptConfigEntity> deptConfigEntities = configEntity.getDeptConfigList().stream()
                .map(dto -> convertDtoToEntity(dto, user.getId()))
                .collect(Collectors.toList());
            
            // 3. 批量插入新的部门配置
            if (!deptConfigEntities.isEmpty()) {
                broadcastDeptConfigMapper.insertBatch(deptConfigEntities);
            }
        }

        return configEntityToInsert;
    }
    
    /**
     * 将BroadcastDeptConfigDto转换为BroadcastDeptConfigEntity
     *
     * @param dto DTO对象
     * @param userId 用户ID
     * @return Entity对象
     */
    private BroadcastDeptConfigEntity convertDtoToEntity(BroadcastDeptConfigDto dto, Integer userId) {
        BroadcastDeptConfigEntity entity = new BroadcastDeptConfigEntity();
        entity.setId(null); // 新增时ID为null
        entity.setBroadcastDeptId(dto.getBroadcastDeptId());
        entity.setDayFlag(dto.getDayFlag());
        entity.setWeekFlag(dto.getWeekFlag());
        entity.setMonthFlag(dto.getMonthFlag());
        entity.setAedFlag(dto.getAedFlag());
        entity.setZyFlag(dto.getZyFlag());
        entity.setCustomFlag(dto.getCustomFlag());
        entity.setAmountStep(dto.getAmountStep());
        entity.setWebhook(dto.getWebhook());
        entity.setIsDeleted(ErpConst.DELETE_STATE.IS_NOT_DELETE);
        entity.setAddTime(new Date());
        entity.setModTime(new Date());
        entity.setCreator(userId);
        entity.setUpdater(userId);
        return entity;
    }
    
    /**
     * 将SelectDto列表转换为逗号分隔的字符串
     *
     * @param selectDtoList SelectDto列表
     * @return 逗号分隔的字符串
     */
    private String listToString(List<SelectDto> selectDtoList) {
        if (CollectionUtils.isEmpty(selectDtoList)) {
            return null;
        }
        return selectDtoList.stream()
            .map(SelectDto::getValue)
            .filter(value -> value != null && StringUtils.isNotBlank(value.toString()))
            .map(Object::toString)
            .collect(Collectors.joining(","));
    }

    private List<SelectDto> convertToUserSelectDtoList(String ids) {
        List<Integer> userIdList = strToList(ids);
        if (userIdList.isEmpty()) {
            return Collections.emptyList();
        }
        //id
        List<UserDto> userList = userApiService.getUserInfoByUserIds(userIdList);
        return userList.stream().map(user->new SelectDto(user.getUserId()+"",user.getUsername())).collect(Collectors.toList());
    }
    private List<SelectDto> convertToTraderSelectDtoList(String ids) {
        List<Integer> userIdList = strToList(ids);
        if (userIdList.isEmpty()) {
            return Collections.emptyList();
        }
        List<TraderCustomerDto> traderList = traderCustomerApiService.getTraderCustomerListByTraderIds(userIdList);
        return traderList.stream().map(trader->new SelectDto(trader.getTraderId()+"",trader.getTraderName())).collect(Collectors.toList());
    }

    private List<SelectDto> convertToCategorySelectDtoList(String ids) {
        List<Integer> userIdList = strToList(ids);
        if (userIdList.isEmpty()) {
            return Collections.emptyList();
        }
        //id
        List<BaseCategoryDto> userList = baseCategoryApiService.getCategoryDtoByList(userIdList);
        return userList.stream().map(user->new SelectDto(user.getBaseCategoryId()+"",user.getBaseCategoryName())).collect(Collectors.toList());
    }

    private List<SelectDto> convertToBrandSelectDtoList(String ids) {
        List<Integer> userIdList = strToList(ids);
        if (userIdList.isEmpty()) {
            return Collections.emptyList();
        }
        //id
        List<BrandFrontDto> userList = brandApiService.getByBrandIdList(userIdList);
        return userList.stream().map(user->new SelectDto(user.getBrandId()+"",user.getBrandName())).collect(Collectors.toList());
    }

    private List<SelectDto> convertToSkuSelectDtoList(String ids) {
        List<Integer> userIdList = strToList(ids);
        if (userIdList.isEmpty()) {
            return Collections.emptyList();
        }
        //id
        List<String>  skuNoList = userIdList.stream().map(item->{
            return "V"+item;
        }).collect(Collectors.toList());
        List<CoreSkuVo> userList = goodsApiService.getGoodsInfoBySkuNos(skuNoList);
        return userList.stream().map(user->new SelectDto(user.getSkuId(),user.getSkuNo(),user.getSkuNo()+" "+user.getShowName())).collect(Collectors.toList());
    }
    private List<Integer> strToList(String ids) {
        if (StringUtils.isBlank(ids)||StringUtils.isBlank(ids.trim())) {
            return Collections.emptyList();
        }
        String[] idArray = StringUtils.split(ids.trim(),",");
        // 将 String[] 转换为 List<Integer>
        return Arrays.stream(idArray).filter(StringUtils::isNotBlank)
                .map(NumberUtils::toInt)
                .collect(Collectors.toList());
    }
}
