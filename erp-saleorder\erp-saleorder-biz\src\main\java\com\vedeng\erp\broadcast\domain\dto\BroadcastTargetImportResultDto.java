package com.vedeng.erp.broadcast.domain.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 业绩目标导入结果DTO
 */
@Data
@Builder
public class BroadcastTargetImportResultDto {

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 成功记录详情
     */
    private List<ImportRecordDetail> successRecords;

    /**
     * 失败记录详情
     */
    private List<ImportRecordDetail> failRecords;

    /**
     * 导入记录详情
     */
    @Data
    @Builder
    public static class ImportRecordDetail {
        /**
         * 行号
         */
        private Integer rowNum;

        /**
         * Sheet名称
         */
        private String sheetName;

        /**
         * 目标对象名称
         */
        private String targetName;

        /**
         * 年度
         */
        private Integer targetYear;

        /**
         * 目标类型
         */
        private Integer targetType;

        /**
         * 错误信息（失败记录使用）
         */
        private String errorMessage;

        /**
         * 导入的月度数据数量（成功记录使用）
         */
        private Integer monthDataCount;
    }
}
