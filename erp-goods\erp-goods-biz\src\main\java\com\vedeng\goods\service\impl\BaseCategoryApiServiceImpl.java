package com.vedeng.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.dto.TaxcodeClassificationDto;
import com.vedeng.erp.finance.service.TaxcodeClassificationApiService;
import com.vedeng.goods.domain.entity.BaseCategory;
import com.vedeng.goods.dto.BaseCategoryDto;
import com.vedeng.goods.dto.CategoryFrontDto;
import com.vedeng.goods.dto.CategoryQueryDto;
import com.vedeng.goods.dto.CategoryResultDto;
import com.vedeng.goods.mapper.BaseCategoryMapper;
import com.vedeng.goods.mapper.CoreSpuMapper;
import com.vedeng.goods.service.BaseCategoryApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.thymeleaf.expression.Ids;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 对外提供商品分类服务
 * @date 2022/7/11 16:59
 **/
@Service
@Slf4j
public class BaseCategoryApiServiceImpl implements BaseCategoryApiService {

    @Resource
    private BaseCategoryMapper tokeBaseCategoryMapper;
    @Autowired
    private CoreSpuMapper coreSpuMapper;
    @Autowired(required = false)
    private TaxcodeClassificationApiService taxcodeClassificationApiService;

    @Override
    public List<BaseCategoryDto> findLevelCategory(Integer isDelete, Integer level) {
        List<BaseCategoryDto> result = new ArrayList<>();
        List<BaseCategory> levelCategory = tokeBaseCategoryMapper.findLevelCategory(isDelete, level);
        if (!CollectionUtils.isEmpty(levelCategory)) {
            levelCategory.forEach(c -> {
                BaseCategoryDto data = new BaseCategoryDto();
                BeanUtils.copyProperties(c, data);
                result.add(data);
            });
        }
        return result;
    }

    @Override
    public BaseCategoryDto selectByPrimaryKey(Integer baseCategoryId) {
        BaseCategory baseCategory = tokeBaseCategoryMapper.selectByPrimaryKey(baseCategoryId);
        if (baseCategory == null) {
            return null;
        }
        BaseCategoryDto data = new BaseCategoryDto();
        BeanUtils.copyProperties(baseCategory, data);
        if (StrUtil.isNotBlank(data.getTaxClassificationCode())) {
            TaxcodeClassificationDto taxcodeClassificationDto = taxcodeClassificationApiService.findByCode(data.getTaxClassificationCode());
            if (Objects.nonNull(taxcodeClassificationDto)) {
                data.setTaxCodeSimpleName(taxcodeClassificationDto.getClassificationAbbreviation());
            }
        }
        return data;
    }

    @Override
    public List<CategoryFrontDto> getAllCategory() {

        List<CategoryFrontDto> categoryFrontDtoOne = tokeBaseCategoryMapper.selectByBaseCategoryLevel(1);

        if (CollUtil.isEmpty(categoryFrontDtoOne)) {
            return Collections.emptyList();
        }
        List<CategoryFrontDto> categoryFrontDtoTwo = tokeBaseCategoryMapper.selectByBaseCategoryLevel(2);
        if (CollUtil.isEmpty(categoryFrontDtoTwo)) {
            return categoryFrontDtoOne;
        }
        Map<Integer, List<CategoryFrontDto>> categoryFrontDtoTwo2Map = categoryFrontDtoTwo.stream().collect(Collectors.groupingBy(CategoryFrontDto::getParentId));
        List<CategoryFrontDto> categoryFrontDtoThree = tokeBaseCategoryMapper.selectByBaseCategoryLevel(3);
        Map<Integer, List<CategoryFrontDto>> categoryFrontDtoThree2Map = new HashMap<>();
        if (CollUtil.isNotEmpty(categoryFrontDtoThree)) {
            categoryFrontDtoThree2Map = categoryFrontDtoThree.stream().collect(Collectors.groupingBy(CategoryFrontDto::getParentId));
        }
        // 排除内部没有的子节点的数据
        Map<Integer, List<CategoryFrontDto>> finalCategoryFrontDtoThree2Map = categoryFrontDtoThree2Map;
        List<CategoryFrontDto> oneNodes = categoryFrontDtoOne.stream().map(x -> {
            List<CategoryFrontDto> categoryFrontDtos = categoryFrontDtoTwo2Map.get(x.getBaseCategoryId());
            if (CollUtil.isNotEmpty(categoryFrontDtos)) {
                List<CategoryFrontDto> treeNodes = categoryFrontDtos.stream().map(a -> {
                    List<CategoryFrontDto> three = finalCategoryFrontDtoThree2Map.get(a.getBaseCategoryId());
                    if (CollUtil.isEmpty(three)) {
                        return null;
                    }
                    a.setChildren(three);
                    return a;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                x.setChildren(treeNodes);
            }
            if (CollUtil.isEmpty(x.getChildren())) {
                return null;
            }
            return x;

        }).filter(Objects::nonNull).collect(Collectors.toList());

        return oneNodes;
    }

    @Override
    public List<List<Integer>> bindParentIdByChild(List<Integer> child) {

        if (CollUtil.isEmpty(child)) {
            return Collections.emptyList();
        }

        List<List<Integer>> result = new ArrayList<>();

        List<BaseCategory> byBaseCategoryIds = tokeBaseCategoryMapper.findByBaseCategoryIds(child);
        List<BaseCategory> byParentBaseCategoryIds = tokeBaseCategoryMapper.findParentByBaseCategoryIds(child);
        Map<Integer, List<BaseCategory>> integerListMap = byBaseCategoryIds.stream().collect(Collectors.groupingBy(BaseCategory::getBaseCategoryId));
        Map<Integer, List<BaseCategory>> integerParentListMap = byParentBaseCategoryIds.stream().collect(Collectors.groupingBy(BaseCategory::getBaseCategoryId));
        child.stream().forEach(x -> {
            List<BaseCategory> baseCategories = integerListMap.get(x);
            if (CollUtil.isNotEmpty(baseCategories)) {
                List<BaseCategory> data = integerParentListMap.get(baseCategories.get(0).getParentId());

                if (CollUtil.isNotEmpty(data)) {
                    BaseCategory baseCategory = data.get(0);

                    ArrayList<Integer> integers = CollUtil.toList(baseCategory.getParentId(), baseCategory.getBaseCategoryId(), baseCategories.get(0).getBaseCategoryId());
                    result.add(integers);
                }

            }
        });
        log.info("bindParentIdByChild,result:{}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public String getCategoryNameByList(List<Integer> baseCategoryIds) {

        List<BaseCategory> byBaseCategoryIds = tokeBaseCategoryMapper.findByBaseCategoryIds(baseCategoryIds);
        if (CollUtil.isEmpty(byBaseCategoryIds)) {
            return "";
        }
        return byBaseCategoryIds.stream().map(BaseCategory::getBaseCategoryName).filter(StrUtil::isNotEmpty).collect(Collectors.joining(StrUtil.COMMA));
    }

    @Override
    public List<BaseCategoryDto> getCategoryDtoByList(List<Integer> baseCategoryIds) {
        List<BaseCategory> byBaseCategoryIds = tokeBaseCategoryMapper.findByBaseCategoryIds(baseCategoryIds);
        if (CollUtil.isEmpty(byBaseCategoryIds)) {
            return Collections.emptyList();
        }
        return byBaseCategoryIds.stream().map(item -> {
            BaseCategoryDto baseCategoryDto = new BaseCategoryDto();
            baseCategoryDto.setBaseCategoryId(item.getBaseCategoryId());
            baseCategoryDto.setBaseCategoryName(item.getBaseCategoryName());
            return baseCategoryDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BaseCategoryDto> getByIdList(List<Integer> categoryIdList, List<BaseCategoryDto> existingCategoryList) {
        if (!CollectionUtils.isEmpty(categoryIdList)) {
            List<BaseCategory> baseCategoryList = tokeBaseCategoryMapper.findByBaseCategoryIds(categoryIdList.stream()
                    .distinct()
                    .collect(Collectors.toList()));
            // 按照parentId进行分组(过滤掉历史的，已被删除的分类)
            Map<Integer, List<BaseCategory>> parentMap = baseCategoryList.stream().filter(item -> item.getIsDeleted() == 0).collect(Collectors.groupingBy(BaseCategory::getParentId));

            List<BaseCategory> completedCategory = new ArrayList<>();
            List<BaseCategory> needMoreQueryCategory = new ArrayList<>();

            // 避免在循环中查询，因此先全部查出来
            List<Integer> parentIds = new ArrayList<>(parentMap.keySet());
            if (!CollectionUtils.isEmpty(parentIds)) {
                // 查询被选中的分类的父级分类下的所有子集分类（未被删除的）
                Map<Integer, List<BaseCategory>> parentListMap = tokeBaseCategoryMapper.getByParentIdList(parentIds).stream().collect(Collectors.groupingBy(BaseCategory::getParentId));
                // 查询这些父级分类的信息（由parentIds可知这些都是未被删除的）
                Map<Integer, BaseCategory> byParentIdMap = tokeBaseCategoryMapper.findByBaseCategoryIds(parentIds).stream().collect(Collectors.toMap(BaseCategory::getBaseCategoryId, Function.identity()));
                parentMap.forEach((key, value) -> {
                    List<BaseCategory> parentList = parentListMap.get(key);
                    if (CollUtil.isNotEmpty(parentList) && value.size() >= parentList.size()) {
                        // 若当前父id下的所有子分类都选中，则将这些子分类集合替换为 他们的 父级分类（用大于等于兼容分类被删除的场景）
                        BaseCategory parentCategory = byParentIdMap.get(key);
                        needMoreQueryCategory.add(parentCategory);
                    } else {
                        completedCategory.addAll(value);
                    }
                });

                if (!needMoreQueryCategory.isEmpty()) {
                    existingCategoryList.addAll(getByIdList(needMoreQueryCategory.stream().map(BaseCategory::getBaseCategoryId).collect(Collectors.toList()), new ArrayList<>()));
                }
                existingCategoryList.addAll(completedCategory.stream().map(item -> {
                            BaseCategoryDto temp = new BaseCategoryDto();
                            temp.setBaseCategoryId(item.getBaseCategoryId());
                            temp.setParentId(item.getParentId());
                            temp.setBaseCategoryName(item.getBaseCategoryName());
                            return temp;
                        }
                ).collect(Collectors.toList()));
            }
        }
        return existingCategoryList;
    }

    @Override
    public void synchronousTaxCode(Integer baseCategoryId, String taxCode) {
        Integer userId = CurrentUser.getCurrentUser().getId();
        log.info("三级分类同步税收编码,synchronousTaxCode,baseCategoryId:{},taxCode:{},userId:{}", baseCategoryId, taxCode, userId);
        if (Objects.isNull(baseCategoryId) || StrUtil.isBlank(taxCode)) {
            return;
        }
        coreSpuMapper.updateTaxClassificationCodeByCategoryIdIn(taxCode, Collections.singletonList(baseCategoryId), userId);
    }

    @Override
    public PageInfo<CategoryResultDto> getCategorybyKeyword(CategoryQueryDto categoryQueryDto) {
        PageParam<CategoryQueryDto> categoryQueryPageParam = new PageParam<>();
        categoryQueryPageParam.setPageNum(categoryQueryDto.getPageNum());
        categoryQueryPageParam.setPageSize(categoryQueryDto.getPageSize());
        categoryQueryPageParam.setParam(categoryQueryDto);
        return PageHelper.startPage(categoryQueryPageParam)
                .doSelectPageInfo(() -> tokeBaseCategoryMapper.queryThreeCategoryByKeyword(categoryQueryPageParam.getParam()));
    }

    @Override
    public Integer getCategoryIdByFullPath(String fullCategoryPath) {
        return tokeBaseCategoryMapper.getCategoryIdByFullPath(fullCategoryPath);
    }

    @Override
    public String getFullPathNameById(Integer categoryId) {
        if (categoryId == null) {
            return null;
        }
        return tokeBaseCategoryMapper.getAllLevelCategoryNameById(categoryId);
    }
}
