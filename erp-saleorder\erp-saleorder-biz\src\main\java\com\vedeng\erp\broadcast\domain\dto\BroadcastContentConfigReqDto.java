package com.vedeng.erp.broadcast.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 播报内容配置请求DTO
 */
@Getter
@Setter
public class BroadcastContentConfigReqDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图片名称
     */
    private String picName;

    /**
     * 专属类型：1=个人，2=团队，3=项目
     */
    private Integer exclusiveType;

    /**
     * 专属目标值（用于精确查询）
     */
    private String exclusiveTargetValues;

    /**
     * 专属目标标签（用于模糊搜索）
     */
    private String exclusiveTargetLabels;

    /**
     * 创建人id列表
     */
    private List<String> creatorList;

    /**
     * 创建时间 起 2025-01-01 00:00:00
     */
    private String startAddTime;

    /**
     * 创建时间 止 2025-01-01 23:59:59
     */
    private String endAddTime;
}
