package com.vedeng.erp.system.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 业务流转单主表
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FlowOrderDto extends BaseDto {
    /**
     * 主键
     */
    private Long flowOrderId;

    /**
     * 业务流转单编号
     */
    private String flowOrderNo;

    /**
     * 基础业务订单ID
     */
    private Integer baseOrderId;

    /**
     * 基础业务订单编号
     */
    private String baseOrderNo;

    /**
     * 基础业务类型 1.采购 2.销售
     */
    private Integer baseBusinessType;

    /**
     * 审核状态，0:未审核, 1:已审核
     */
    private Integer auditStatus;

    /**
     * 流转单详细
     */
    List<FlowOrderDetailDto> flowOrderDetailList;

    /**
     * 流转单节点
     * 交易者信息
     * 款票信息
     */
    private List<FlowNodeDto> flowNodeDto;

    /**
     * 审核人id
     */
    private Integer auditUserId;

    /**
     * 审核人
     */
    private String auditUsername;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 末级合同状态：0-无需上传（末级节点为贝登子公司），1-未上传（末级节点非贝登子公司且未上传合同），2-已上传（末级节点非贝登子公司且已上传合同）
     */
    private Integer contractStatus;

    /**
     * 推送方向：1-金蝶, 2-ERP
     */
    private Integer pushDirection;

    /**
     * 来源ERP系统编码
     */
    private String sourceErp;

    /**
     * 来源ERP系统名称
     */
    private String sourceErpName;
}