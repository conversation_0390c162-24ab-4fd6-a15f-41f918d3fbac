package com.newtask.broadcast;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.broadcast.statistics.project.BroadcastMonth;
import com.vedeng.erp.broadcast.statistics.project.BroadcastMonthAed;
import com.vedeng.erp.broadcast.statistics.project.BroadcastMonthVd;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;


/**
 * 播报到款排行榜定时任务（10点后，每小时执行一次）
 * @ClassName:  BroadcastRankTask   
 * @author: <PERSON>.yang
 * @date:   2025年7月22日 下午2:04:51    
 * @Copyright:
 */
@JobHandler(value = "broadcastRankTask")
@Component
public class BroadcastRankTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(BroadcastRankTask.class);
    
    @Autowired
    private BroadcastMonth broadcastMonth;
    
    @Autowired
    private BroadcastMonthAed broadcastMonthAed;
    
    @Autowired
    private BroadcastMonthVd broadcastMonthVd;
    
    @Override
    public ReturnT<String> doExecute(String params) throws Exception {
        XxlJobLogger.log("播报到款排行榜定时任务 start-----------");
        logger.info("播报到款排行榜定时任务  start-----------");
        try {
        	broadcastMonth.invocation(null, false);
        	broadcastMonthAed.invocation(null, false);
        	broadcastMonthVd.invocation(null, false);
        }catch(Exception e) {
        	XxlJobLogger.log("播报到款排行榜定时任务失败");
        	logger.error("播报到款排行榜定时任务失败");
        	return FAIL;
        }
        logger.info("播报到款排行榜定时任务 end-----------");
        XxlJobLogger.log("播报到款排行榜定时任务 end-----------");
        return SUCCESS;
    }
}
