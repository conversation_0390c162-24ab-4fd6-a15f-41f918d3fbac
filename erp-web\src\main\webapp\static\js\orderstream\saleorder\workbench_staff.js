$(function () {
//待办事项
    //待审核订单
    $.ajax({
        url: page_url + '/orderstream/saleorder/getNoVerifyOrderInfo.do',
        dataType: 'json',
        type: 'post',
        success: function (res) {
            if (res.code === 0) {
               $("#noVerifyTotal").text(res.data.noVerifyTotal);
               $("#noVerifyOnLine").text(res.data.noVerifyOnLine);
               $("#noVerifyOffLine").text(res.data.noVerifyOffLine);
            }else {
                layer.alert(res.message)
            }
        }
    });

    // //未处理商机
    // $.ajax({
    //     url: page_url + '/orderstream/saleorder/getUnHandleBusinessChance.do',
    //     dataType: 'json',
    //     type: 'post',
    //     success: function (res) {
    //         if (res.code === 0) {
    //             if (res.data.unHandleNum === 0){
    //                 $("#unHandleNum").attr("disabled",true).css("pointer-events","none");
    //             }else {
    //                 $("#unHandleNum").text(res.data.unHandleNum);
    //             }
    //             if (res.data.waitCommunicateNum === 0){
    //                 $("#waitCommunicateNum").attr("disabled",true).css("pointer-events","none");
    //             }else {
    //                 $("#waitCommunicateNum").text(res.data.waitCommunicateNum);
    //             }
    //         }
    //     }
    // });


    //未收款/部分收款订单
    $.ajax({
        url: page_url + '/orderstream/saleorder/getNoPaymentOrderInfo.do',
        dataType: 'json',
        type: 'post',
        success: function (res) {
            if (res.code === 0) {
                $("#noPaymentAll").text(res.data.noPaymentAll);
                $("#noPaymentSome").text(res.data.noPaymentSome);
                $("#noPaymentMoeny").text(res.data.noPaymentMoeny);
            }else {
                layer.alert(res.message)
            }
        }
    });

    //未开票订单
    $.ajax({
        url: page_url + '/orderstream/saleorder/getNoInvoiceOrderInfo.do',
        dataType: 'json',
        type: 'post',
        success: function (res) {
            if (res.code === 0) {
                $("#noInvoiceTotal").text(res.data.noInvoiceTotal);
                $("#noInvoiceMoney").text(res.data.noInvoiceMoney);
            }else {
                layer.alert(res.message)
            }
        }
    });

    //用户职级
    var usetLevel = $("#userLevelType").val();
    if (usetLevel == 1) {
        //待合同回传订单
        $.ajax({
            url: page_url + '/orderstream/saleorder/getNoContractOrderInfo.do',
            dataType: 'json',
            type: 'post',
            success: function (res) {
                if (res.code === 0) {
                    $("#noContractTotal").text(res.data.noContractTotal);
                    $("#noContractMoney").text(res.data.noContractMoney);
                }else {
                    layer.alert(res.message)
                }
            }
        })
    }

// 待跟踪事项
    //审核中订单
    $.ajax({
        url: page_url + '/orderstream/saleorder/getVerifyingOrderInfo.do',
        dataType: 'json',
        type: 'post',
        success: function (res) {
            if (res.code === 0) {
                $("#verifyingTotal").text(res.data.verifyingTotal);
                $("#verifyingOnLine").text(res.data.verifyingOnLine);
                $("#verifyingOffLine").text(res.data.verifyingOffLine);
            }else {
                layer.alert(res.message)
            }
        }
    });

    //未采购/部分采购订单
    $.ajax({
        url: page_url + '/orderstream/saleorder/getNoPurchaseOrderInfo.do',
        dataType: 'json',
        type: 'post',
        success: function (res) {
            if (res.code === 0) {
                $("#noPurchaseAll").text(res.data.noPurchaseAll);
                $("#noPurchaseSome").text(res.data.noPurchaseSome);
            }else {
                layer.alert(res.message)
            }
        }
    });

    //未发货/部分发货订单
    $.ajax({
        url: page_url + '/orderstream/saleorder/getNoSendGoodsOrderInfo.do',
        dataType: 'json',
        type: 'post',
        success: function (res) {
            if (res.code === 0) {
                $("#noSendGoodsAll").text(res.data.noSendGoodsAll);
                $("#noSendGoodsSome").text(res.data.noSendGoodsSome);
                $("#noNoSend").text(res.data.noNoSend);
            }else {
                layer.alert(res.message)
            }
        }
    });

    //未完结售后单
    $.ajax({
        url: page_url + '/orderstream/saleorder/getNoCompletedAfterSaleInfo.do',
        dataType: 'json',
        type: 'post',
        success: function (res) {
            if (res.code === 0) {
                $("#noCompletedAfterSaleReadyVerify").text(res.data.noCompletedAfterSaleReadyVerify);
                $("#noCompletedAfterSaleVerifing").text(res.data.noCompletedAfterSaleVerifing);
                $("#noCompletedAfterSaleIng").text(res.data.noCompletedAfterSaleIng);
                $("#noCompletedAfterSaleRejected").text(res.data.noCompletedAfterSaleRejected);
            }else {
                layer.alert(res.message)
            }
        }
    });

    // 未回款订单
    $.ajax({
        url: "/orderstream/saleorder/uncollectedOrders.do",
        dataType: 'json',
        type: 'post',
        success: function (res) {
            if (res.code === 0) {
                $("#totalNum").text(res.data.totalNum);
                $("#amount").text(res.data.amount != null ?res.data.amount.toFixed(2):"");
                $("#notOverdue").text(res.data.notOverdue != null ?res.data.notOverdue.toFixed(2):"");
                $("#betweenZeroAndSixtyAmount").text(res.data.betweenZeroAndSixtyAmount != null ?
                    res.data.betweenZeroAndSixtyAmount.toFixed(2):"");
                $("#moreThanSixtyAmount").text(res.data.moreThanSixtyAmount !=null ?res.data.moreThanSixtyAmount.toFixed(2):"");
            }else {
                layer.alert(res.messages)
            }
        }
    });

    // 新业绩考核待办
    $.ajax({
        url: "/orderstream/saleorder/getPerformanceEvaluationInfo.do",
        dataType: 'json',
        type: 'post',
        success: function (res) {
            if (res.code === 0) {
                $("#noInvoiceOrderNum").text(res.data.noInvoiceOrderNum);
                $("#contractNotReviewedOrderNum").text(res.data.contractNotReviewedOrderNum);
                $("#confirmationNotApprovedOrderNum").text(res.data.confirmationNotApprovedOrderNum);
            }else {
                layer.alert(res.message)
            }
        }
    });

    // 营销任务
    $.ajax({
        url: "/market/sale/getMarketPlanNum.do",
        dataType: 'json',
        type: 'post',
        success: function (res) {
            if (res.code === 0) {
                $("#marketPlanNum").text(res.data.marketPlanNum);
            }else {
                layer.alert(res.message)
            }
        }
    });

    // 拜访计划数量
    $.ajax({
        url: "/visitrecord/profile/getVisitRecordNum.do",
        dataType: 'json',
        type: 'post',
        success: function (res) {
            if (res.code === 0) {
                if(res.data.visitrecordNum>0){
                    $("#visitrecordNum").text(res.data.visitrecordNum);
                }else{
                    $('#visitrecordNum').after("<span style='font-size:20px;'>0</span>");
                    $('#visitrecordNum').hide();
                    //$("#visitrecordNum").text("0").css("color","gray").css("text-decoration","none").css("pointer-events","none");
                }

            }else {
                layer.alert(res.message)
            }
        }
    });

    //当月毛利
    // $.ajax({
    //     url: page_url + '/orderstream/saleorder/getMaoli.do',
    //     dataType: 'json',
    //     type: 'post',
    //     success: function (res) {
    //         if (res.code === 0) {
    //             var maoli = res.data.grossProfitRate;
    //             if(maoli && maoli !='' ){
    //                 $("#maoli").text(maoli);
    //             }else{
    //                 $("#maoli").text("本月无符合条件订单");
    //                 $("#maoli").css("font-size","14px");
    //             }
    //         }else {
    //             layer.alert(res.message);
    //         }
    //     }
    // });


});