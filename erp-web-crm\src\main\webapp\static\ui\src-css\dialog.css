.vd-ui-dialog_wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  background: rgba(0, 0, 0, 0.6);
  z-index: 2000;
}
.vd-ui-dialog_wrapper .vd-ui-dialog {
  width: 480px;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 7px 14px;
  margin: 0px auto 50px;
  background-color: #ffffff;
  border-radius: 5px;
  position: relative;
  margin-top: 15vh;
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
}
.vd-ui-dialog_wrapper .vd-ui-dialog.noPadding {
  padding: 0;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head {
  position: relative;
  padding: 0 20px;
  background-color: #F5F7FA;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  border-bottom: 1px solid #e1e5e8;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head .vd-ui-dialog_head_title {
  font-size: 16px;
  line-height: 44px;
  display: block;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head .vd-ui-dialog_head_title_icon {
  width: 30px;
  height: 30px;
  margin-right: 5px;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head .vd-ui-dialog_head_title_tip {
  color: #999;
  font-size: 12px;
  margin-left: 10px;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head .vd-ui-dialog_head_headerBtn {
  position: absolute;
  padding: 0 10px;
  right: 0;
  top: 0;
  height: 44px;
  line-height: 44px;
  cursor: pointer;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head .vd-ui-dialog_head_headerBtn i {
  font-size: 24px;
  color: #999999;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head .vd-ui-dialog_head_headerBtn:hover i {
  color: #666666;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_content {
  position: relative;
  padding: 20px;
  padding-bottom: 0px;
  flex: 1;
  overflow: auto;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_content.noPadding {
  padding: 0;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_content.noScroll {
  overflow: hidden;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_footer {
  padding: 20px;
  padding-bottom: 0px;
  display: flex;
  justify-content: flex-end;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_footer .vd-ui-button {
  margin-right: 10px;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_footer .vd-ui-button:last-child {
  margin-right: 0;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in {
  max-height: 650px;
  top: 50%;
  margin-top: 0;
  transform: translateY(-50%);
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content {
  position: relative;
  flex: 1;
  overflow: auto;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.fade-wrapper-enter {
  opacity: 0;
}
.fade-wrapper-leave-to {
  opacity: 0;
}
.fade-wrapper-enter-active {
  transition: all 0.22s ease-out;
}
.fade-wrapper-leave-active {
  transition: all 0.19s ease-in;
}
.vd-ui-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  background-color: #000000;
  opacity: 0.6;
}
.vd-ui-modal-modal-enter {
  animation: modal-in 0.22s ease-out;
}
.vd-ui-modal-modal-leave {
  animation: modal-out 0.19s ease-in;
}
@keyframes modal-in {
  0% {
    opacity: 0;
  }
}
@keyframes modal-out {
  100% {
    opacity: 0;
  }
}
.ui-poper-wrap {
  position: fixed;
  z-index: 3000;
}
.ui-poper-wrap.hidden {
  opacity: 0;
  z-index: -1;
  pointer-events: none;
}
.vd-ui-list-option-tip {
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 2000;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vd-ui-list-option-tip .vd-ui-list-option-cnt {
  background: #fff;
  border-radius: 5px;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 7px 14px;
  padding: 44px 20px 20px 20px;
  position: relative;
  width: 480px;
}
.vd-ui-list-option-tip .vd-ui-list-option-cnt .vd-ui-list-option-close {
  cursor: pointer;
  position: absolute;
  font-size: 24px;
  color: #ccc;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0;
  right: 0;
}
.vd-ui-list-option-tip .vd-ui-list-option-cnt .vd-ui-list-option-close:hover {
  color: #333;
}
.vd-ui-list-option-tip .vd-ui-list-option-cnt .vd-ui-list-option-img {
  width: 440px;
  height: 160px;
  background-image: url(/static/image/common/list-option-tip.gif);
  background-size: 100% 100%;
  margin-bottom: 20px;
}
.vd-ui-list-option-tip .vd-ui-list-option-cnt .vd-ui-list-option-txt {
  text-align: center;
  margin-bottom: 40px;
}
.vd-ui-list-option-tip .vd-ui-list-option-cnt .vd-ui-list-option-btn {
  text-align: right;
}
