package com.vedeng.erp.broadcast.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

import com.common.dto.SelectDto;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.broadcast.domain.dto.BroadcastRelationConfigDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastRelationConfigReqDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastRelationDept;
import com.vedeng.erp.broadcast.domain.dto.BroadcastRelationUser;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptRErpDeptEntity;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptUserOverrideEntity;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptRErpDeptMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptUserOverrideMapper;
import com.vedeng.erp.broadcast.service.BroadCastDeptUserRelateService;
import com.vedeng.erp.broadcast.web.api.BroadCastDeptUserRelateApiController;
import com.vedeng.erp.system.dto.OrganizationDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.OrganizationApiService;
import com.vedeng.erp.system.service.UserApiService;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BroadCastDeptUserRelateServiceImpl implements BroadCastDeptUserRelateService {

    @Value("${b2b_business_division_id}")
    private Integer b2bBusinessDivisionId;
    @Resource
    OrganizationApiService organizationApiService;
    @Resource
    BroadcastDeptRErpDeptMapper broadcastDeptRErpDeptMapper;
    @Resource
    BroadcastDeptUserOverrideMapper broadcastDeptUserOverrideMapper;
    @Resource
    UserApiService userApiService;
    @Resource
    BroadcastDeptMapper broadcastDeptMapper;



    @Override
    public BroadcastRelationConfigDto init() {
    	BroadcastRelationConfigDto dto=new BroadcastRelationConfigDto();
    	try {
    		List<OrganizationDto> orgList= organizationApiService.getOrgAllLeafOrgByParentId(b2bBusinessDivisionId);
    		List<BroadcastDeptRErpDeptEntity> deptRelateList = broadcastDeptRErpDeptMapper.getAllBroadcastDeptRelate();
    		List<BroadcastDeptUserOverrideEntity> userRelateList = broadcastDeptUserOverrideMapper.getAllBroadcastUserRelate();
    		//转换成map
    		Map<Integer,BroadcastDeptRErpDeptEntity> deptMap=
    				deptRelateList.stream().collect(Collectors.toMap(BroadcastDeptRErpDeptEntity::getErpDeptId, item -> item));
    		List<BroadcastRelationDept> deptRelationList=new ArrayList<>();
    		orgList.forEach(item->{
    			BroadcastRelationDept relation=new  BroadcastRelationDept();
    			relation.setErpDeptFullName(item.getOrgName());
    			relation.setErpDeptId(item.getOrgId());
    			if(deptMap.containsKey(item.getOrgId())){
    				BroadcastDeptRErpDeptEntity deptRelate=deptMap.get(item.getOrgId());
    				//小组下拉框
    				BroadcastDeptEntity detpEnt=broadcastDeptMapper.selectByPrimaryKey(deptRelate.getBroadcastDeptId());
    				SelectDto groupSelect=new SelectDto(detpEnt.getId()+"",detpEnt.getDeptName());
    				relation.setBroadcastGroupSelect(groupSelect);
    				//部门下拉框
    				BroadcastDeptEntity detpEnt1=broadcastDeptMapper.selectByPrimaryKey(detpEnt.getParentId());
    				SelectDto deptSelect=new SelectDto(detpEnt1.getId()+"",detpEnt1.getDeptName());
    				relation.setBroadcastDeptSelect(deptSelect);
    			}
    			deptRelationList.add(relation);
    		});
    		dto.setDeptRelationList(deptRelationList);
    		
    		//初始化用户
    		List<BroadcastRelationUser> userResultList=new ArrayList<>();
    		if(CollUtil.isNotEmpty(userRelateList)){
    			userRelateList.forEach(item->{
    				BroadcastRelationUser dtoRelation=new BroadcastRelationUser();
    				UserDto user=userApiService.getUserBaseInfo(item.getErpUserId());
    				SelectDto userSelect=new SelectDto(user.getUserId()+"",user.getUsername());
    				//用户下拉框
    				dtoRelation.setErpUserSelect(userSelect);
    				//小组下拉框
    				BroadcastDeptEntity detpEnt=broadcastDeptMapper.selectByPrimaryKey(item.getBroadcastDeptId());
    				SelectDto groupSelect=new SelectDto(detpEnt.getId()+"",detpEnt.getDeptName());
    				dtoRelation.setBroadcastGroupSelect(groupSelect);
    				//部门下拉框
    				BroadcastDeptEntity detpEnt1=broadcastDeptMapper.selectByPrimaryKey(detpEnt.getParentId());
    				SelectDto deptSelect=new SelectDto(detpEnt1.getId()+"",detpEnt1.getDeptName());
    				dtoRelation.setBroadcastDeptSelect(deptSelect);
    				userResultList.add(dtoRelation);
    			});
    		}
    		dto.setUserRelationList(userResultList);
    	}catch(Exception e) {
    		log.error("播报部门管理初始化init失败",e);
    	}
        return dto;
    }

    @Override
    @Transactional
    public void save(BroadcastRelationConfigDto dto, CurrentUser user) {
        List<BroadcastRelationConfigReqDto> deptSaveList=dto.getDeptSaveList();
        List<BroadcastRelationConfigReqDto> userSaveList=dto.getUserSaveList();
        //先删除
        broadcastDeptRErpDeptMapper.deleteAllBroadcastDeptRelate(user.getId());
        if(CollUtil.isNotEmpty(deptSaveList)){
            //再新增
            List<BroadcastDeptRErpDeptEntity> deptRelateList=new ArrayList<>();
            deptSaveList.forEach(item->{
                BroadcastDeptRErpDeptEntity entity=new BroadcastDeptRErpDeptEntity();
                if(item.getErpDeptId()==null|| item.getBroadcastGroupId()==null){
                   return;
                }
                entity.setErpDeptId(item.getErpDeptId());
                entity.setBroadcastDeptId(item.getBroadcastGroupId());
                entity.setCreator(user.getId());
                entity.setAddTime(new Date());
                deptRelateList.add(entity);
            });
            if(CollUtil.isNotEmpty(deptRelateList)){
                broadcastDeptRErpDeptMapper.insertBatch(deptRelateList);
            }
        }
        broadcastDeptUserOverrideMapper.deleteAllBroadcastUserRelate(user.getId());
        if(CollUtil.isNotEmpty(userSaveList)){
            //再新增
            List<BroadcastDeptUserOverrideEntity> userRelateList=new ArrayList<>();
            userSaveList.forEach(item->{
                BroadcastDeptUserOverrideEntity entity=new BroadcastDeptUserOverrideEntity();
                if(item.getErpUserId()==null|| item.getBroadcastGroupId()==null){
                    return;
                }
                entity.setErpUserId(item.getErpUserId());
                entity.setBroadcastDeptId(item.getBroadcastGroupId());
                entity.setCreator(user.getId());
                entity.setAddTime(new Date());
                userRelateList.add(entity);
            });
            if(CollUtil.isNotEmpty(userRelateList)){
                broadcastDeptUserOverrideMapper.insertBatch(userRelateList);
            }
        }
    }

}
