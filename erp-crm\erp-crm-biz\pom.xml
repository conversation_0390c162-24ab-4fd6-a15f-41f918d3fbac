<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>erp-crm</artifactId>
        <groupId>com.vedeng.erp</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>erp-crm-biz</artifactId>


    <dependencies>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>5.1.1</version>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-common-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-common-trace</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-common-feign</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
            </exclusions>

        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-common-mybatis</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-infrastructure</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Goods -->
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-goods-biz</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-common-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.search</groupId>
                    <artifactId>search-api</artifactId>
                </exclusion>

            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-trader-biz</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-common-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.vedeng.erp</groupId>
                    <artifactId>erp-old</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-system-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>

        </dependency>


        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-system-biz</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.vedeng.framework</groupId>
                    <artifactId>vedeng-core-sqlmonitor</artifactId>
                </exclusion>

            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-crm-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>9.0.74</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>