package com.vedeng.order.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.common.constants.Contant;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.activiti.taskassign.TaskMessageListener;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.dao.UserDetailMapper;
import com.vedeng.authorization.model.*;
import com.vedeng.authorization.service.AuthService;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.annotation.MethodLockParam;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.*;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.PdfUtil;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.quote.service.AuthorizationApplyApiService;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.enums.LowPriceOrderTypeEnum;
import com.vedeng.erp.saleorder.service.OrderGoodsLowerPriceApiService;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.strategy.OrderTerminalContext;
import com.vedeng.erp.system.common.enums.ChangeRelatedTableEnums;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.dto.BaseCompanyInfoSimpleDto;
import com.vedeng.erp.system.dto.ChangeLogDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.erp.system.service.ChangeLogApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.api.CommunicateVoiceTaskApi;
import com.vedeng.erp.trader.constant.AiConstant;
import com.vedeng.erp.trader.dto.CommunicateVoiceTaskDto;
import com.vedeng.erp.trader.dto.SpecialSalesDto;
import com.vedeng.erp.trader.dto.VoiceFieldResultDto;
import com.vedeng.erp.trader.service.SpecialSalesApiService;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.model.Unit;
import com.vedeng.goods.service.*;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.order.dao.QuoteorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.enums.QuotedAlarmModeEnum;
import com.vedeng.order.model.*;
import com.vedeng.order.model.query.LabelQuery;
import com.vedeng.order.model.vo.*;
import com.vedeng.order.service.BussinessChanceService;
import com.vedeng.order.service.QuoteService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.order.service.impl.RemarkComponentServiceImpl;
import com.vedeng.order.strategy.QuoteOrderTerminalStrategy;
import com.vedeng.price.api.price.dto.price.PriceInfoResponseDto;
import com.vedeng.price.dto.GoodSalePrice;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.price.service.PriceInfoDealWithService;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.Tag;
import com.vedeng.system.service.*;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.RTraderGroupJTrader;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.dto.PriceCenterQueryDto;
import com.vedeng.trader.model.vo.TraderAddressVo;
import com.vedeng.trader.model.vo.TraderContactVo;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderCustomerService;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <b>Description:</b><br> 报价管理
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.order.controller
 * <br><b>ClassName:</b> QuoteorderController
 * <br><b>Date:</b> 2017年6月21日 上午10:03:38
 */
@Controller
@RequestMapping("/order/quote")
public class QuoteController extends BaseController{
	public static Logger logger = LoggerFactory.getLogger(QuoteController.class);

	@Autowired // 自动装载
	private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

	@Autowired
	@Qualifier("actionProcdefService")
	private ActionProcdefService actionProcdefService;

	@Autowired
	@Qualifier("quoteService")
	private QuoteService quoteService;

	@Autowired
	@Qualifier("orgService")
	private OrgService orgService;//部门

	@Autowired
	@Qualifier("userService")
	private UserService userService;//自动注入userService

	@Autowired
	@Qualifier("traderCustomerService")
	private TraderCustomerService traderCustomerService;//客户-交易者

	@Autowired
	@Qualifier("goodsService")
	private GoodsService goodsService;

	@Autowired
	@Qualifier("tagService")
	private TagService tagService;

	@Autowired
	private UnitService unitService;

	@Autowired
	@Qualifier("verifiesRecordService")
	private VerifiesRecordService verifiesRecordService;


	@Autowired
	@Qualifier("regionService")
	private RegionService regionService;// 自动注入regionService

	@Autowired
	@Qualifier("userDetailMapper")
	private UserDetailMapper userDetailMapper;


	@Autowired
	@Qualifier("goodsChannelPriceService")
	private GoodsChannelPriceService goodsChannelPriceService;

	@Autowired
	@Qualifier("goodsSettlementPriceService")
	private GoodsSettlementPriceService goodsSettlementPriceService;
	@Autowired
	@Qualifier("bussinessChanceService")
	private BussinessChanceService bussinessChanceService;

	@Autowired
	@Qualifier("companyService")
	private CompanyService companyService;

	@Autowired
	@Qualifier("paramsConfigValueService")
	private ParamsConfigValueService paramsConfigValueService;

	@Autowired
	private VgoodsService vGoodsService;

	@Autowired
	private BasePriceService basePriceService;

	@Autowired
	private PriceInfoDealWithService priceInfoDealWithService;

	@Autowired
	private AttachmentService attachmentService;

	@Autowired
	private SaleorderService saleorderService;

	@Autowired
	private QuoteorderMapper quoteorderMapper;

	@Autowired
	private SaleorderMapper saleorderMapper;

	@Autowired
	private RoleService roleService;

	@Autowired
	private OssUtilsService ossUtilsService;

	@Autowired
	private SkuAuthorizationService skuAuthorizationService;

	@Autowired
	private AuthService authService;
	@Autowired
	private RemarkComponentServiceImpl remarkComponentService;

	@Autowired
	private OrganizationMapper organizationMapper;

	@Autowired
	private OrderGoodsLowerPriceApiService orderGoodsLowerPriceApiService;

	@Autowired
	private ChangeLogApiService changeLogApiService;

	@Autowired
	private UserApiService userApiService;

	//申请中
	private static Integer AUTHORIZATION_REVIEW=1;
	//驳回
	private static Integer AUTHORIZATION_REJECT=2;
	//通过
	private static Integer AUTHORIZATION_PASS=3;
	//取消
	private static Integer AUTHORIZATION_CANCEL=4;
	//废弃
	private static Integer AUTHORIZATION_ABANDON=5;

	private static Integer AUTHORIZATION_STORAGE_FILE=1643;

	private static Integer AUTHORIZATION_APPLY_FILE=1644;

	private static Integer AUTHORIZATION_ABANDON_FILE=1645;

	@Value("${ORG_IDS}")
	protected String orgIds;
	@Value("${oss_http}")
	public String OSS_HTTP;
	@Value("${oss_url}")
	public String OSS_URL;
	@Value("${AUTHORIZATION_AUDIT_USER_IDS:522}")
	public String authorizationAuditUserIds;

	@Autowired
	private SpecialSalesApiService specialSalesApiService;

	@Autowired
	private OrderTerminalApiService orderTerminalApiService;

	@Autowired
	private QuoteOrderTerminalStrategy quoteOrderTerminalStrategy;

	@Autowired
	private CommunicateVoiceTaskApi communicateVoiceTaskApi;

	@Autowired
	private AuthorizationApplyApiService authorizationApplyApiService;
	@Autowired
	private TrackStrategyFactory trackStrategyFactory;


	@Autowired
	private BaseCompanyInfoApiService baseCompanyInfoApiService;
	/**
	 * 该方法的list返回给页面前端解析并展示
	 * @return
	 */
	public List<BaseCompanyInfoSimpleDto> getCompanyInfoWithSeq() {
		List<BaseCompanyInfoSimpleDto>  baseCompanyInfoSimpleDtos = new ArrayList<>();
		List<BaseCompanyInfoDto> allCompanyList =baseCompanyInfoApiService.findAll();
		List<BaseCompanyInfoDto> erpCompanyList = allCompanyList.stream()
				.filter(company -> company.getFrontEndSeq() != null)
				.sorted(Comparator.comparing(BaseCompanyInfoDto::getFrontEndSeq))
				.collect(Collectors.toList());

		for(BaseCompanyInfoDto baseCompanyInfoDto:erpCompanyList){
			if(baseCompanyInfoDto.getFrontEndSeq() != null){
				BaseCompanyInfoSimpleDto companyInfoSimpleDto = new BaseCompanyInfoSimpleDto();
				companyInfoSimpleDto.setCompanyName(baseCompanyInfoDto.getCompanyName());
				companyInfoSimpleDto.setFrontEndSeq(baseCompanyInfoDto.getFrontEndSeq());
				baseCompanyInfoSimpleDtos.add(companyInfoSimpleDto);
			}
		}
		return baseCompanyInfoSimpleDtos;
	}

	/**
	 * <b>Description:</b><br> 报价信息列表查询
	 * @param request
	 * @param pageNo
	 * @param pageSize
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月21日 上午10:04:12
	 */
	@ResponseBody
	@RequestMapping(value="index")
	public ModelAndView index(HttpServletRequest request,Quoteorder quote,HttpSession session,
							  @RequestParam(required = false, value="beginTime") String beginTime,
							  @RequestParam(required = false, value="endTime") String endTime,
							  @RequestParam(required = false, defaultValue = "1") Integer pageNo,@RequestParam(required = false) Integer pageSize){
		ModelAndView mv = new ModelAndView();

		Page page = getPageTag(request,pageNo,pageSize);

		Map<String, Object> map;
		try {
			if(StringUtils.isNotBlank(beginTime)){
				quote.setBeginDate(DateUtil.convertLong(beginTime + " 00:00:00",""));
			}
			if(StringUtils.isNotBlank(endTime)){
				quote.setEndDate(DateUtil.convertLong(endTime + " 23:59:59",""));
			}
			mv.addObject("beginTime", beginTime);mv.addObject("endTime", endTime);
			if(quote.getFollowOrderStatus()==null){
				quote.setFollowOrderStatus(0);//默认查询跟单中
			}else if(quote.getFollowOrderStatus().equals(-1)){
				quote.setFollowOrderStatus(null);//查询全部
			}
			//查询沟通记录
			if(quote.getTimeType()!=null && quote.getTimeType()==2){
				if(quote.getBeginDate()!=null || quote.getEndDate()!=null){//若都为空，则查询全部报价列表，不需要查询沟通记录
					//根据时间获取沟通记录中外键ID
					List<Integer> keyIds = quoteService.getCommunicateRecordByDate(quote.getBeginDate(),quote.getEndDate(),"" + SysOptionConstant.ID_245);
					if(null == keyIds){
						keyIds = new ArrayList<Integer>(){{add(-1);}};
					}else if(keyIds.size() == 0){
						keyIds.add(-1);
					}
					quote.setKeyIds(keyIds);
				}
			}


			//按需设置报价预警--销售端触发模式
			Integer quotedAlarmLevel = quote.getSalesmanAlarmLevel();
			if(quotedAlarmLevel!=null && quotedAlarmLevel>0) {
				quote.setQuotedAlarmMode(QuotedAlarmModeEnum.SALESMAN_MODE.getMode());
			}

			//商机来源
			List<SysOptionDefinition> bussSource=getSysOptionDefinitionList(365);
			mv.addObject("bussSource", bussSource);
			//客户性质
			List<SysOptionDefinition> customerNatureList = getSysOptionDefinitionList(464);
			mv.addObject("customerNatureList", customerNatureList);
			//关闭原因
			List<SysOptionDefinition> firstCloseReason = getSysOptionDefinitionList(SysOptionConstant.ID_1600);
			List<SysOptionDefinition> saleCloseReason = getSysOptionDefinitionList(SysOptionConstant.ID_1601);
			List<SysOptionDefinition> traderCloseReason = getSysOptionDefinitionList(SysOptionConstant.ID_1602);
			List<SysOptionDefinition> goodsCloseReason = getSysOptionDefinitionList(SysOptionConstant.ID_1603);
			mv.addObject("firstCloseReason", firstCloseReason);
			mv.addObject("saleCloseReason", saleCloseReason);
			mv.addObject("traderCloseReason", traderCloseReason);
			mv.addObject("goodsCloseReason", goodsCloseReason);

			User user = (User)session.getAttribute(Consts.SESSION_USER);
			//获取销售部门
			List<Organization> orgList = orgService.getSalesOrgList(SysOptionConstant.ID_310,user.getCompanyId());
			mv.addObject("orgList",orgList);


			quote.setCompanyId(user.getCompanyId());
			//获取当前销售用户下级职位用户
			//List<User> saleUserList = userService.getNextAllUserList(user.getUserId(), user.getCompanyId(), true, user.getPositLevel(), SysOptionConstant.ID_310);
			List<Integer> positionType = new ArrayList<>();
			positionType.add(SysOptionConstant.ID_310);
			List<User> userList = userService.getMyUserList(user, positionType, false);
			List<User> userListByOrgId = userService.getMyUserListByUserOrgsList(user,positionType,false);
			if(CollectionUtils.isNotEmpty(userListByOrgId)){

				Set<Integer> userIds = new HashSet<>();
				for (User u : userList) {
					userIds.add(u.getUserId());
				}
				// 遍历 userListByOrgId 并添加那些 userId 不在 userIds Set 中的 User 对象
				for (User newUser : userListByOrgId) {
					if (!userIds.contains(newUser.getUserId())) {
						userList.add(newUser);
						userIds.add(newUser.getUserId()); // 更新 userIds Set
					}
				}
				// 此时 userList 包含了原始用户以及新添加的，没有重复 userId 的用户

				// 使用自定义比较器进行排序，不区分大小写
				Collections.sort(userList, new Comparator<User>() {
					@Override
					public int compare(User u1, User u2) {
						return u1.getUsername().compareToIgnoreCase(u2.getUsername());
					}
				});
			}
//			quote.setSaleUserList(userList);
			mv.addObject("userList",userList);//归属销售人员
			mv.addObject("loginUser",user);//登陆用户
			if(quote.getOptUserId()!=null){
				User s_user = new User();
				s_user.setUserId(quote.getOptUserId());
				quote.setSaleUserList(new ArrayList(){{add(s_user);}});
			}else if(user.getPositType()!=null && user.getPositType().intValue()==SysOptionConstant.ID_310){
				quote.setSaleUserList(userList);
			}
			setQuoteCloseReason(quote);
			map = quoteService.getQuoteListPage(quote,page);

			List<Quoteorder> list = (List<Quoteorder>)map.get("quoteList");

			List<Integer> userIdList = new ArrayList<>();List<Integer> orgIdList = new ArrayList<>();
			List<Integer> traderIdList = new ArrayList<>();
			List<Integer> quoteIdList = new ArrayList<>();List<Integer> businessIdList = new ArrayList<>();
			if(list!=null && list.size()>0){
				int list_size = list.size();
				for(int i=0;i<list_size;i++){
					Quoteorder quoteorder1 = list.get(i);
					userIdList.add(quoteorder1.getUserId());orgIdList.add(quoteorder1.getOrgId());
					//客户ID
					traderIdList.add(quoteorder1.getTraderId());
					quoteIdList.add(quoteorder1.getQuoteorderId());
					businessIdList.add(quoteorder1.getBussinessChanceId());

					//审核人
					if(null != quoteorder1.getVerifyUsername()){
						List<String> verifyUsernameList = Arrays.asList(quoteorder1.getVerifyUsername().split(","));
						quoteorder1.setVerifyUsernameList(verifyUsernameList);
					}
					quoteorder1.setCustomerNatureStr(getSysOptionDefinition(quoteorder1.getCustomerNature()).getTitle());
					if(quoteorder1.getFollowOrderStatus().equals(2) &&StringUtil.isBlank(quoteorder1.getCloseReasonComment()) && quoteorder1.getCloseReasonId() != null){
						SysOptionDefinition sysOptionDefinition = getSysOptionDefinition(quoteorder1.getCloseReasonId());
						if(sysOptionDefinition != null){
							quoteorder1.setCloseReasonComment(sysOptionDefinition.getTitle());
						}
					}
				}
				List<CommunicateRecord> communicateNumList = quoteService.getCommunicateNumList(new ArrayList<>(), quoteIdList, new ArrayList<>());
				List<User> traderUserList = userService.getUserByTraderIdList(traderIdList,1);
				List<User> orderUserList = userService.getUserByUserIds(userIdList);
				List<Organization> OrgList = userService.getOrgNameByOrgIdList(orgIdList,user.getCompanyId());
				int communicateNum = 0;
				for(int i=0;i<list_size;i++){
					for(int a=0;a<traderUserList.size();a++){
						if(traderUserList.get(a).getTraderId().equals(list.get(i).getTraderId())){
							list.get(i).setOptUserName(traderUserList.get(a).getUsername());
						}
					}
					for(int b=0;b<orderUserList.size();b++){
						if(orderUserList.get(b).getUserId().equals(list.get(i).getUserId())){
							list.get(i).setSalesName(orderUserList.get(b).getUsername());
						}
					}
					for(int c=0;c<OrgList.size();c++){
						if(OrgList.get(c).getOrgId().equals(list.get(i).getOrgId())){
							list.get(i).setSalesDeptName(OrgList.get(c).getOrgName());
						}
					}

					for(int d=0;d<communicateNumList.size();d++){
						if(communicateNumList.get(d).getRelatedId() != null && communicateNumList.get(d).getRelatedId().equals(list.get(i).getQuoteorderId())){
							communicateNum = communicateNum + communicateNumList.get(d).getCommunicateCount();
						}
					}
					list.get(i).setCommunicateNum(communicateNum);communicateNum = 0;
				}
				//判断是否'特麦帮'报价
				list.forEach(l -> {
					List<SpecialSalesDto> saleList = specialSalesApiService.findByRelateIdInAndRelateTypeAndIsDelete(Collections.singletonList(l.getQuoteorderId()), SpecialSalesEnum.QUOTEORDER.getCode(), ErpConst.ZERO);
					if (CollUtil.isNotEmpty(saleList)){
						l.setTmh(Boolean.TRUE);
					}else l.setTmh(Boolean.FALSE);
				});


			}

			//客户分群信息
			List<Integer> traderIds = list.stream().map(item -> item.getTraderId()).collect(Collectors.toList());
			Map<Integer, RTraderGroupJTrader> traderGroupMap = getTraderGroup(traderIds);
			mv.addObject("traderGroupMap", traderGroupMap);


			mv.addObject("quoteList",list);
			mv.addObject("quote",quote);

			mv.addObject("page", (Page)map.get("page"));
		} catch (Exception e) {
			logger.error("quote index:", e);
		}

		mv.setViewName("order/quote/index_quote");
		return mv;
	}

	private void setQuoteCloseReason(Quoteorder quote) {
		if(quote.getCloseReasonId() == null){
			return;
		}
		if(quote.getCloseReasonId().equals(SysOptionConstant.ID_1601)){
			setCloseReason(quote, SysOptionConstant.ID_1601);
		}else if(quote.getCloseReasonId().equals(SysOptionConstant.ID_1602)){
			setCloseReason(quote, SysOptionConstant.ID_1602);
		}else if(quote.getCloseReasonId().equals(SysOptionConstant.ID_1603)){
			setCloseReason(quote, SysOptionConstant.ID_1603);
		}else if(quote.getCloseReasonId() != null){
			quote.setCloseReasonIdList(Collections.singletonList(quote.getCloseReasonId()));
		}
	}

	private void setCloseReason(Quoteorder quote, int i) {
		List<SysOptionDefinition> closeReason = getSysOptionDefinitionList(i);
		List<Integer> closeReasonIdList = closeReason.stream().map(e -> e.getSysOptionDefinitionId()).collect(Collectors.toList());
		quote.setCloseReasonIdList(closeReasonIdList);
	}


	/**
	 * <b>Description:</b><br> 新增报价前验证客户是否被禁用
	 * @param request
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月26日 下午5:10:06
	 */
	@ResponseBody
	@RequestMapping(value="/getTraderCustomerStatus")
	public ResultInfo<?> getTraderCustomerStatus(HttpServletRequest request,@RequestParam(value="traderCustomerId")Integer traderCustomerId){
		return quoteService.getTraderCustomerStatus(traderCustomerId);
	}
	/**
	 * <b>Description:</b><br> 添加报价信息初始化
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月26日 下午1:23:06
	 */
	@FormToken(save=true)
	@RequestMapping(value="/addQuote")
	//@RequestParam(value="traderId",required=false)required=false非必须项，默认true
	public ModelAndView addQuote(HttpServletRequest request,Quoteorder quote){
		ModelAndView mv = new ModelAndView();
		try {
			mv.addObject("quote", quote);

			//根据客户ID查询客户信息
			TraderCustomerVo customer = traderCustomerService.getCustomerBussinessInfo(quote.getTraderId());
			mv.addObject("customer", customer);

			//查询联系人和联系地址
			TraderContactVo traderContactVo=new TraderContactVo();
			traderContactVo.setTraderId(quote.getTraderId());
			traderContactVo.setTraderType(ErpConst.ONE);
			traderContactVo.setIsEnable(1);
			Map<String, Object> map = traderCustomerService.getTraderContactVoList(traderContactVo);
			String tastr = (String) map.get("contact");
			net.sf.json.JSONArray json = net.sf.json.JSONArray.fromObject(tastr);
			// 联系人
			List<TraderContactVo> userList = (List<TraderContactVo>) json.toCollection(json, TraderContactVo.class);
			List<TraderAddressVo> addressList = (List<TraderAddressVo>)map.get("address");
			mv.addObject("userList", userList);
			mv.addObject("addressList", addressList);

			//采购类型
			List<SysOptionDefinition> purchasingTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_404);
			mv.addObject("purchasingTypeList", purchasingTypeList);

			//付款条件
			List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_407);
			mv.addObject("paymentTermList", paymentTermList);

			//采购时间
			List<SysOptionDefinition> purchasingTimeList = getSysOptionDefinitionList(SysOptionConstant.ID_410);
			mv.addObject("purchasingTimeList", purchasingTimeList);
		} catch (Exception e) {
			logger.error("addQuote:", e);
		}
		mv.setViewName("order/quote/add_quote");
		return mv;
	}

	/**
	 * <b>Description:</b><br> 保存报价信息
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月29日 下午5:39:36
	 */
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value="/saveQuote")
	@SystemControllerLog(operationType = "add",desc = "保存报价信息")
	public ModelAndView saveQuote(HttpServletRequest request,Quoteorder quote,@RequestParam(value="quoteSource",required=false)String quoteSource){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		ModelAndView mv = new ModelAndView();
		try {
			if(quoteService.isExistBussinessChanceId(quote.getBussinessChanceId())>0){
				mv.addObject("message", "该商机已存在");
				return fail(mv);
			}
			if(user!=null){
				quote.setCompanyId(user.getCompanyId());
				quote.setCreator(user.getUserId());
				quote.setAddTime(DateUtil.sysTimeMillis());

				quote.setUpdater(user.getUserId());
				quote.setModTime(DateUtil.sysTimeMillis());

				quote.setUserId(user.getUserId());
				//销售部门（若一个多个部门，默认取第一个部门）
				Organization org = orgService.getOrgNameByUserId(user.getUserId());
				quote.setOrgId(org==null?null:org.getOrgId());
			}
		} catch (Exception e) {
			logger.error("saveQuote:", e);
		}
		SysOptionDefinition sysOptionDefinition = getSysOptionDefinition(Integer.valueOf(quote.getCustomerLevel()));
		if(sysOptionDefinition!=null){
			quote.setCustomerLevel(sysOptionDefinition.getTitle());
		}
		ResultInfo<Quoteorder> result = quoteService.saveQuote(quote);

		mv.addObject("refresh", "false_false_true");//是否关闭当前页，是否刷新当前页，是否刷新上层页面----三个参数为必传项
		if(result.getCode()==0){
			mv.addObject("url","./getQuoteDetail.do?quoteorderId="+result.getData().getQuoteorderId()+"&viewType=1&quoteSource="+quoteSource);
			return success(mv);
		}else{
			mv.addObject("message", result.getMessage());
			return fail(mv);
		}
	}

	/**
	 * <b>Description:</b><br> 查询报价基本信息（需添加产品、付款记录等其他信息）
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月29日 下午5:39:55
	 */
	@FormToken(save=true)
	@RequestMapping(value="/getQuoteDetail")
	public ModelAndView getQuoteDetail(HttpServletRequest request,Quoteorder quote,HttpSession hs
			,@RequestParam(value="viewType",required=false)Integer viewType
			,@RequestParam(value="quoteSource",required=false)String quoteSource){
		ModelAndView mv  = new ModelAndView();
		try {
			quote = quoteService.getQuoteInfoByKey(quote.getQuoteorderId());
			//日志
			mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(quote)));
			User sessionUser = getSessionUser(request);
			mv.addObject("curr_user", sessionUser);
			//判断是否是B2B的销售
			Boolean saleAndB2BFlagByUserId = userService.getSaleAndB2BFlagByUserId(sessionUser);
			int isSaleFlag = 0;
			if(saleAndB2BFlagByUserId){
				isSaleFlag = 1;
			}
			mv.addObject("isSaleFlag", isSaleFlag);
			//根据报价ID查询报价产品（全部）
			Map<String,Object> map = quoteService.getQuoteGoodsByQuoteId(quote.getQuoteorderId(),ErpConst.NJ_COMPANY_ID,hs,viewType,quote.getTraderId());
			List<QuoteorderGoods> quoteGoodsList = (List<QuoteorderGoods>)map.get("quoteGoodsList");

			mv.addObject("quoteToSaleOrderFlag", userService.handleOrgIds(sessionUser));

			//判断授权书申请状态为打印还是申请中还是查看，防止影响原来逻辑加入try...catch
			Integer flag=1;
			try {
				//判断界面有效的产品信息数量
				List<QuoteorderGoods> countQuoteGoodsList=quoteGoodsList.stream().filter(item->item.getIsDelete().equals(0) && item.getIsTemp().equals(0)).collect(Collectors.toList());

				int authorizationReviewSum=quoteService.getAuthorizationSum(quote.getQuoteorderId(),AUTHORIZATION_REVIEW);
				int authorizationRejectSum=quoteService.getAuthorizationSum(quote.getQuoteorderId(),AUTHORIZATION_REJECT);
				int authorizationPassSum=quoteService.getAuthorizationSum(quote.getQuoteorderId(),AUTHORIZATION_PASS);

				if (countQuoteGoodsList != null){
					//界面有效的产品信息数量
					int sumQuoteGoodsList=countQuoteGoodsList.size();
					if (sumQuoteGoodsList==0){
						flag=1;
					}else if ((authorizationRejectSum+authorizationReviewSum !=0) && (sumQuoteGoodsList==authorizationRejectSum+authorizationReviewSum+authorizationPassSum)){
						flag=2;
					}else if (sumQuoteGoodsList==authorizationPassSum){
						flag=3;
					}
					mv.addObject("flag",flag);
					mv.addObject("sqnum",sumQuoteGoodsList);

				}
			} catch (Exception e) {
				logger.error("授权书按钮显示页面出错",e);
			}


			//新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
			List<Integer> skuIds = new ArrayList<>();
			quoteGoodsList.stream().forEach(quoteGood -> {
				skuIds.add(quoteGood.getGoodsId());
			});
			List<Map<String,Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
			Map<String,Map<String,Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key->key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
			mv.addObject("newSkuInfosMap", newSkuInfosMap);
			//新商品流切换 VDERP-1970 add by brianna 2020/3/16 end


			//核价信息应用 add by brianna 2020/5/29 start
			List<GoodSalePrice> goodSalePriceList = quoteGoodsList.stream().map(quoteGood -> {
				return new GoodSalePrice(quoteGood.getSku(),quoteGood.getChannelPrice());
			}).collect(Collectors.toList());

			Map<String,String> skuNoAndPriceMap = priceInfoDealWithService.dealWithGoodsSalePrice(quote.getTraderId(),goodSalePriceList);

			mv.addObject("skuNoAndPriceMap", skuNoAndPriceMap);
			//核价信息应用 add by brianna 2020/5/29 end

			//根据客户ID查询客户信息
			TraderCustomerVo customer = traderCustomerService.getTraderCustomerInfo(quote.getTraderId());
			mv.addObject("customer", customer);
			//产品核算价
			quoteGoodsList = goodsChannelPriceService.getQuoteChannelPriceList(quote.getSalesAreaId(),quote.getCustomerNature(),customer.getOwnership(),quoteGoodsList);
			//产品结算价
			quoteGoodsList = goodsSettlementPriceService.getGoodsSettlePriceByGoodsList(sessionUser.getCompanyId(),quoteGoodsList);

			//查看产品的报价咨询情况
			quoteGoodsList = quoteService.getConsultResultOfQuoteorderGoods(quoteGoodsList,viewType);

			setAuthorizationInfo(quoteGoodsList);
			if (CollectionUtils.isNotEmpty(quoteGoodsList)) {
				for (QuoteorderGoods quoteorderGoods : quoteGoodsList) {
					List<String> skuNoList = new ArrayList<>();
					skuNoList.add(quoteorderGoods.getSku());
					LabelQuery labelQuery = new LabelQuery();
					labelQuery.setScene(RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode());
					labelQuery.setRelationId(quote.getQuoteorderId());
					labelQuery.setSkuNoList(skuNoList);
					String componentHtml = remarkComponentService.getComponentHtml(labelQuery);
					quoteorderGoods.setComponentHtml(componentHtml);
					// 判断是否改低价
					if(quoteorderGoods.getIsDelete() == 1)
						continue;
					BigDecimal checkPrice = orderGoodsLowerPriceApiService.getCheckPrice(quoteorderGoods.getQuoteorderId(),quoteorderGoods.getQuoteorderGoodsId(), LowPriceOrderTypeEnum.QUOTATION);
					if (ObjectUtil.isNotNull(checkPrice)){
						quoteorderGoods.setIsLowerGoods(ErpConst.ONE);
						quoteorderGoods.setCheckPrice(checkPrice);
					}
				}
			}
			mv.addObject("quoteGoodsList", quoteGoodsList);
			mv.addObject("userList", (List<User>)map.get("userList"));
			mv.addObject("loginUser",sessionUser);

			//销售人员名称
			User saleUser = userService.getUserByTraderId(quote.getTraderId(), ErpConst.ONE);
			if(saleUser != null){
				quote.setOptUserName(saleUser.getUsername());
			}

			//创建人员
			User creatorUser = userService.getUserById(quote.getCreator());
			quote.setCreatorName(StringUtil.isBlank(creatorUser.getUsername()) ? "" : creatorUser.getUsername());

			//销售部门名称(根据部门ID)
			quote.setSalesDeptName(orgService.getOrgNameById(quote.getOrgId()));

			String salesDeptName = quote.getSalesDeptName();

			boolean isRelationOrg = StringUtil.isNotBlank(salesDeptName) && (salesDeptName.contains(ErpConst.YXG_ORG_NAME) || salesDeptName.contains(ErpConst.B2B_BUSINESS_UNIT));
			mv.addObject("isRelationOrg", isRelationOrg);

			Boolean btoBflag = orgService.getBtoBOrgFlagByTraderId(quote.getTraderId());
			mv.addObject("btoBflag", btoBflag);
			//客户类型
			quote.setCustomerTypeStr(getSysOptionDefinition(quote.getCustomerType()).getTitle());
			//客户性质
			quote.setCustomerNatureStr(getSysOptionDefinition(quote.getCustomerNature()).getTitle());
			//采购方式
			quote.setPurchasingTypeStr(getSysOptionDefinition(quote.getPurchasingType()).getTitle());
			//付款条件
			quote.setPaymentTermStr(getSysOptionDefinition(quote.getPaymentTerm()).getTitle());
			//采购时间
			quote.setPurchasingTimeStr(getSysOptionDefinition(quote.getPurchasingTime()).getTitle());
			//终端类型
			quote.setTerminalTraderTypeStr(getSysOptionDefinition(quote.getTerminalTraderType()).getTitle());
			mv.addObject("quote", quote);

			OrderTerminalDto info = orderTerminalApiService.getTerminalInfoByBusinessIdAndBusinessType(quote.getQuoteorderId(), 2);
			mv.addObject("orderTerminalDto", info);

			//客户分群信息
			Map<Integer, RTraderGroupJTrader> traderGroupMap = getTraderGroup(Arrays.asList(quote.getTraderId()));
			mv.addObject("traderGroupMap", traderGroupMap);

			//付款条件列表
			List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
			mv.addObject("paymentTermList", paymentTermList);

			//发票类型
			List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_428);
			mv.addObject("invoiceTypeList", invoiceTypeList);

			//运费说明
			List<SysOptionDefinition> freightList = getSysOptionDefinitionList(SysOptionConstant.ID_469);
			mv.addObject("freightList", freightList);
			Map<String, Object> historicInfo=actionProcdefService.getHistoric(processEngine, "quoteVerify_"+ quote.getQuoteorderId());
			mv.addObject("taskInfo", historicInfo.get("taskInfo"));
			mv.addObject("startUser", historicInfo.get("startUser"));
			// 最后审核状态
			mv.addObject("endStatus",historicInfo.get("endStatus"));
			mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
			mv.addObject("commentMap", historicInfo.get("commentMap"));
			mv.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
			Map<String, Object> historicInfoLinkBd=actionProcdefService.getHistoric(processEngine, "quoteLinkBd_"+ quote.getQuoteorderId());
			mv.addObject("taskInfoLinkBd", historicInfoLinkBd.get("taskInfo"));
			mv.addObject("startUserLinkBd", historicInfoLinkBd.get("startUser"));
			// 最后审核状态
			mv.addObject("endStatusLinkBd",historicInfoLinkBd.get("endStatus"));
			mv.addObject("historicActivityInstanceLinkBd", historicInfoLinkBd.get("historicActivityInstance"));
			mv.addObject("commentMapLinkBd", historicInfoLinkBd.get("commentMap"));
			mv.addObject("candidateUserMapLinkBd", historicInfoLinkBd.get("candidateUserMap"));
			Map<String, Object> historicInfoClose=actionProcdefService.getHistoric(processEngine, "closeQuoteorderVerify_"+ quote.getQuoteorderId());
			Task taskInfoClose = (Task) historicInfoClose.get("taskInfo");
			mv.addObject("taskInfoClose", taskInfoClose);
			mv.addObject("startUserClose", historicInfoClose.get("startUser"));
			// 最后审核状态
			mv.addObject("endStatusClose",historicInfoClose.get("endStatus"));
			mv.addObject("historicActivityInstanceClose", historicInfoClose.get("historicActivityInstance"));
			mv.addObject("commentMapClose", historicInfoClose.get("commentMap"));
			mv.addObject("candidateUserMapClose", historicInfoClose.get("candidateUserMap"));
			Task taskInfo = (Task) historicInfo.get("taskInfo");
			//当前审核人
			String verifyUsers = null;
			List<String> verifyUserList = new ArrayList<>();
			String reason = null;
			if(null!=taskInfo){
				Map<String, Object> taskInfoVariables= actionProcdefService.getVariablesMap(taskInfo);
				verifyUsers = (String) taskInfoVariables.get("verifyUsers");
			}
			mv.addObject("verifyUsers", verifyUsers);


			String verifyUsersClose = null;
			if(null!=taskInfoClose){
				Map<String, Object> taskInfoVariablesClose= actionProcdefService.getVariablesMap(taskInfoClose);
				String verifyUser = (String) taskInfoVariablesClose.get("verifyUserList");
				if(null != verifyUser){
					verifyUserList = Arrays.asList(verifyUser.split(","));
				}
				verifyUsersClose = (String) taskInfoVariablesClose.get("verifyUsers");
			}
			Map<String, Object> taskInfoVariablesReason= actionProcdefService.getVariablesMap("closeQuoteorderVerify_"+ quote.getQuoteorderId());
			//VDERP-2553  报价单关闭三级原因
			String closeReason ="";
			String closeReasonComment="";
			if(taskInfoVariablesReason != null){
				reason = (String) taskInfoVariablesReason.get("reason");
				Integer	closeReasonId = (Integer) taskInfoVariablesReason.get("closeReasonId");
				closeReasonComment = (String) taskInfoVariablesReason.get("closeReasonComment");
				if(StringUtil.isBlank(closeReasonComment)){
					closeReasonComment="-";
				}
				closeReason = quoteService.getCloseReasonInfo(closeReasonId);
				if(StringUtil.isBlank(closeReason)){
					closeReason="未填写";
				}
			}
			//报价单关闭原因
			mv.addObject("verifyUserList", verifyUserList);
			mv.addObject("reason", reason);
			mv.addObject("closeReason", closeReason);
			mv.addObject("closeReasonComment",closeReasonComment);
			mv.addObject("verifyUsersClose", verifyUsersClose);

			//VDERP-13616 【ERP】客户下单流程优化 - 报价单
			ChangeRelatedTableEnums tQuoteorderGoods = ChangeRelatedTableEnums.T_QUOTEORDER;
			tQuoteorderGoods.setRelatedId(quote.getQuoteorderId());
			List<ChangeLogDto> quSaleHistList = changeLogApiService.getInfo(tQuoteorderGoods);
			mv.addObject("quSaleHistList",quSaleHistList);

			if(viewType != null && viewType != 1){//null添加完报价后转入，1报价列表转入
				//查询报价咨询记录
				List<QuoteorderConsult> consultList = quoteService.getQuoteConsultList(quote.getQuoteorderId());
				if(!consultList.isEmpty()){
					mv.addObject("consultList", consultList);
					List<Integer> userIds = new ArrayList<Integer>();
					for(QuoteorderConsult qc:consultList){
						userIds.add(qc.getCreator());
					}
					List<User> userList = userService.getUserByUserIds(userIds);
					mv.addObject("userList", userList);
				}
				if(viewType!=5){
					//查询沟通记录
					//沟通类型为商机和报价、订单
					CommunicateRecord cr = new CommunicateRecord();
					cr.setQuoteorderId(quote.getQuoteorderId());
					cr.setBussinessChanceId(quote.getBussinessChanceId());
					List<Saleorder> saleorderList = saleorderMapper.getSaleorderByQuoteorderId(quote.getQuoteorderId());
					if (!saleorderList.isEmpty()){
						cr.setSaleorderId(saleorderList.get(0).getSaleorderId());
					}
					List<CommunicateRecord> communicateList = traderCustomerService.getCommunicateRecordList(cr);
					if(!communicateList.isEmpty()){
						//沟通内容
						mv.addObject("communicateList", communicateList);
					}
				}
				if(viewType==2){//编辑报价后视图
					if (StringUtils.isNotEmpty(quote.getSaleorderId()) // 生成订单后
							// 或者订单生效，未成单，审核通过
							|| (quote.getFollowOrderStatus().equals(0) && quote.getValidStatus().equals(1) && quote.getVerifyStatus().equals(1))) {
						mv.setViewName("order/quote/view_quote_sale");
					} else {
						mv.setViewName("order/quote/edit_quote_detail_2");
					}
				}else if(viewType==3){//销售报价视图
					mv.setViewName("order/quote/view_quote_sale");
				}else if(viewType==4){//销售主管报价视图
					mv.setViewName("order/quote/view_quote_director");
				}else if(viewType==5){//报价咨询详细视图
					mv.setViewName("order/quote/view_quote_consult");
				}

				if(viewType == 2 || viewType == 5){
					//根据分类获取产品负责人
					if(quoteGoodsList != null && quoteGoodsList.size() > 0){
						List<Integer> categoryIdList = new ArrayList<>();
						for(int i=0;i<quoteGoodsList.size();i++){
							categoryIdList.add(quoteGoodsList.get(i).getCategoryId());
						}

						List<QuoteorderGoods> my_quoteGoodsList = new ArrayList<>();
						List<QuoteorderGoods> they_quoteGoodsList = new ArrayList<>();
						categoryIdList = new ArrayList<Integer>(new HashSet<Integer>(categoryIdList));
						List<User> categoryUserList = quoteService.getGoodsCategoryUserList(categoryIdList,quote.getCompanyId());
						if(categoryUserList != null && categoryUserList.size() > 0){
							mv.addObject("loginUserId", sessionUser.getUserId()+";");
							//默认报价咨询人
							User contsultantUser = paramsConfigValueService.getQuoteConsultant(sessionUser.getCompanyId(),107);
							if(contsultantUser == null){
								contsultantUser = new User();
							}
							mv.addObject("contsultantUserId", contsultantUser.getUserId()+";");
							for(int i=0;i<quoteGoodsList.size();i++){
								for(int j=0;j<categoryUserList.size();j++){
									if(categoryUserList.get(j).getCategoryId().equals(quoteGoodsList.get(i).getCategoryId())){
										quoteGoodsList.get(i).setGoodsUserIdStr((quoteGoodsList.get(i).getGoodsUserIdStr()==null?";":quoteGoodsList.get(i).getGoodsUserIdStr()) + categoryUserList.get(j).getUserId() + ";");
									}
								}
								//归属当前登录用户的商品排列在前
								if(quoteGoodsList.get(i).getGoodsUserIdStr() != null && quoteGoodsList.get(i).getGoodsUserIdStr().contains(sessionUser.getUserId()+";")){
									my_quoteGoodsList.add(quoteGoodsList.get(i));
								}else if(sessionUser.getUserId().equals(contsultantUser.getUserId())){//当前登录的用户是报价咨询默认人
									my_quoteGoodsList.add(quoteGoodsList.get(i));
								}else{
									they_quoteGoodsList.add(quoteGoodsList.get(i));
								}
							}
							my_quoteGoodsList.addAll(they_quoteGoodsList);
							quoteGoodsList.clear();
							quoteGoodsList.addAll(my_quoteGoodsList);
						}
					}
				}
			}else{//报价添加产品视图
				if(quote.getCustomerNature().intValue() == 465){//分销
					// 省级地区
					List<Region> provinceList = regionService.getRegionByParentId(1);
					mv.addObject("provinceList", provinceList);
					if(quote.getSalesAreaId() != null){
						// 地区
						List<Region> regionList = (List<Region>) regionService.getRegion(quote.getSalesAreaId(), 1);
						if (regionList != null && (!regionList.isEmpty())) {
							for (Region r : regionList) {
								switch (r.getRegionType()) {
									case 1:
										List<Region> cityList = regionService.getRegionByParentId(r.getRegionId());
										mv.addObject("provinceRegion", r);
										mv.addObject("cityList", cityList);
										break;
									case 2:
										List<Region> zoneList = regionService.getRegionByParentId(r.getRegionId());
										mv.addObject("cityRegion", r);
										mv.addObject("zoneList", zoneList);
										break;
									case 3:
										mv.addObject("zoneRegion", r);
										break;
									default:
										mv.addObject("countryRegion", r);
										break;
								}
							}
						}


					}
				}
				mv.setViewName("order/quote/edit_quote_detail_1");
			}

			mv.addObject("regions", regionService.getRegionByParentId(1));
			mv.addObject("terminalTypes", skuAuthorizationService.getAllTerminalTypes());
			mv.addObject("quoteSource", quoteSource);//标记报价来源
			mv.addObject("isShowLinkButton",isShowLinkButton(isRelationOrg, quote, sessionUser));
			User curr_user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
			User saler = userService.getUserById(userService.getUserByTraderId(quote.getTraderId(),1).getUserId());

			if (saler != null && saler.getParentId().equals(curr_user.getUserId())){
				//订单归属销售的直接上级
				mv.addObject("roleType",1);
			} else if (viewType != null && viewType == 5){
				if (roleService.getUserIdByRoleName("供应主管",1).contains(curr_user.getUserId())){
					//供应主管
					mv.addObject("roleType",3);
				} else {
					List<Integer> assignUserList = userService.selectAllAssignUser().stream().map(User::getUserId).collect(Collectors.toList());
					if (assignUserList.contains(curr_user.getUserId())){
						//供应链成员
						mv.addObject("roleType",2);
					}
				}
			} else {
				mv.addObject("roleType",0);
			}

		} catch (Exception e) {
			logger.error("getQuoteDetail:", e);
		}
		return mv;
	}


	private boolean isShowLinkButton(boolean isRelationOrg, Quoteorder quoteorder,User user){
		if (quoteorder == null || quoteorder.getUserId() == null){
			return false;
		}
		if(quoteorder.getBussinessChanceId()==null || quoteorder.getBussinessChanceId()==0){
			return false;
		}
		if (!ErpConst.ONE.equals(quoteorder.getValidStatus())){
			return false;
		}
		if (ErpConst.ONE.equals(quoteorder.getFollowOrderStatus()) || ErpConst.TWO.equals(quoteorder.getFollowOrderStatus())){
			return false;
		}
		if (!isRelationOrg){
			return false;
		}
		if (user == null || user.getUserId() == null){
			return false;
		}
		User userParentInfo = userService.getUserParentInfo(quoteorder.getUserId());
		if (!user.getUserId().equals(quoteorder.getUserId()) && !user.getUserId().equals(userParentInfo.getParentId())){
			return false;
		}
		return true;
	}

	/**
	 * 设置终端信息
	 *
	 * @param quoteGoodsList
	 */
	private void setAuthorizationInfo(List<QuoteorderGoods> quoteGoodsList) {
		if (CollectionUtils.isNotEmpty(quoteGoodsList)){
			for (QuoteorderGoods quoteorderGoods : quoteGoodsList) {
				if (quoteorderGoods.getGoodsId() == null || quoteorderGoods.getGoodsId().equals(0)){
					logger.info("终端产品非临时商品 quoteorderGoodsId:{}", quoteorderGoods.getQuoteorderGoodsId());
					continue;
				}
				CoreSkuGenerate coreSkuGenerate = goodsService.getSkuAuthotizationInfoBySku(quoteorderGoods.getGoodsId().longValue());
				if (coreSkuGenerate == null){
					logger.info("终端商品SKU不存在 quoteorderGoodsId:{}", quoteorderGoods.getQuoteorderGoodsId());
					continue;
				}
				quoteorderGoods.setIsNeedReport(coreSkuGenerate.getIsNeedReport());
				quoteorderGoods.setIsAuthorized(coreSkuGenerate.getIsAuthorized() == null ? 0 : coreSkuGenerate.getIsAuthorized());
				if (quoteorderGoods.getIsNeedReport() != null && quoteorderGoods.getIsNeedReport().equals(1) &&
						quoteorderGoods.getIsAuthorized().equals(1)){
					quoteorderGoods.setSkuAuthorizationVo(skuAuthorizationService.getSkuAuthorizationInfoBySkuId(quoteorderGoods.getGoodsId()));
				}
			}
		}
	}


	/**
	 * <b>Description:</b><br> 报价下一步信息加载初始化
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月29日 下午5:40:20
	 */
	@RequestMapping(value="/editQuote")
	public ModelAndView editQuote(HttpServletRequest request,Quoteorder quote){
		ModelAndView mv = new ModelAndView();
		try {
			quote = quoteService.getQuoteInfoByKey(quote.getQuoteorderId());
			mv.addObject("quote", quote);
			//日志
			mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(quote)));

			//查询联系人和联系地址
			TraderContactVo traderContactVo=new TraderContactVo();
			traderContactVo.setTraderId(quote.getTraderId());
			traderContactVo.setTraderType(ErpConst.ONE);
			Map<String, Object> map = traderCustomerService.getTraderContactVoList(traderContactVo);
			String tastr = (String) map.get("contact");
			net.sf.json.JSONArray json = net.sf.json.JSONArray.fromObject(tastr);
			// 联系人
			List<TraderContactVo> userList = (List<TraderContactVo>) json.toCollection(json, TraderContactVo.class);
			List<TraderAddressVo> addressList = (List<TraderAddressVo>)map.get("address");
			mv.addObject("userList", userList);
			mv.addObject("addressList", addressList);

			//采购类型
			List<SysOptionDefinition> purchasingTypeList = getSysOptionDefinitionList(404);
//			if(JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + 404)){
//				String json_result = JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + 404);
//				JSONArray jsonArray = JSONArray.fromObject(json_result);
//				purchasingTypeList = (List<SysOptionDefinition>) JSONArray.toCollection(jsonArray, SysOptionDefinition.class);
//			}
			mv.addObject("purchasingTypeList", purchasingTypeList);

			//付款条件
			List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(407);
//			if(JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + 407)){
//				String json_result = JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + 407);
//				JSONArray jsonArray = JSONArray.fromObject(json_result);
//				paymentTermList = (List<SysOptionDefinition>) JSONArray.toCollection(jsonArray, SysOptionDefinition.class);
//			}
			mv.addObject("paymentTermList", paymentTermList);

			//采购时间
			List<SysOptionDefinition> purchasingTimeList = getSysOptionDefinitionList(410);
//			if(JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + 410)){
//				String json_result = JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + 410);
//				JSONArray jsonArray = JSONArray.fromObject(json_result);
//				purchasingTimeList = (List<SysOptionDefinition>) JSONArray.toCollection(jsonArray, SysOptionDefinition.class);
//			}
			mv.addObject("purchasingTimeList", purchasingTimeList);
		} catch (Exception e) {
			logger.error("editQuote:", e);
		}
		mv.setViewName("order/quote/edit_quote");
		return mv;
	}

	/**
	 * <b>Description:</b><br> 修改报价中客户信息
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月29日 下午5:40:41
	 */
	@ResponseBody
	@RequestMapping(value="/updateQuoteCustomer")
	@SystemControllerLog(operationType = "edit",desc = "修改报价中客户信息")
	public ResultInfo<?> updateQuoteCustomer(HttpServletRequest request,String beforeParams,Quoteorder quote){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			quote.setUpdater(user.getUserId());
			quote.setModTime(DateUtil.sysTimeMillis());
		}
		return quoteService.updateQuoteCustomer(quote);
	}

	/**
	 * <b>Description:</b><br> 修改报价终端信息
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月29日 下午5:42:19
	 */
	@ResponseBody
	@RequestMapping(value="/updateQuoteTerminal")
	public ResultInfo<?> updateQuoteTerminal(HttpServletRequest request,Quoteorder quote){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			quote.setUpdater(user.getUserId());
			quote.setModTime(DateUtil.sysTimeMillis());
		}
		return quoteService.updateQuoteTerminal(quote);
	}

	/**
	 * <b>Description:</b><br> 添加报价产品信息初始化
	 * @param request
	 * @param searchContent
	 * @param quoteorderId
	 * @param pageNo
	 * @param pageSize
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月4日 上午10:17:22
	 */
	@FormToken(save=true)
	@RequestMapping(value="/addQuoteGoods")
	public ModelAndView addQuoteGoods(HttpServletRequest request,@RequestParam(value="searchContent",required=false)String searchContent,
									  @RequestParam(value="searchBrandStr",required=false)String searchBrandStr,
									  @RequestParam(value="searchModelSpec",required=false)String searchModelSpec,
									  @RequestParam(value="baseUnitId",required=false)Integer baseUnitId,
									  @RequestParam(value="quoteorderId")Integer quoteorderId,@RequestParam(required = false)Integer salesAreaId,HttpSession session,
									  @RequestParam(required = false, defaultValue = "1") Integer pageNo,
									  @RequestParam(required = false, defaultValue = "10") Integer pageSize,Integer traderId,String optType){
		ModelAndView mv = new ModelAndView();
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if((traderId == null || traderId == 0) &&"1".equals(optType)){
			mv.addObject("message","请先完善客户信息");
			return fail(mv);
		}
		try {
			if(StringUtils.isNoneBlank(searchContent) || StringUtils.isNoneBlank(searchBrandStr) || StringUtils.isNoneBlank(searchModelSpec) || Objects.nonNull(baseUnitId)){
				Page page = getPageTag(request,pageNo,pageSize);
				Goods goods = new Goods();
				goods.setCompanyId(user.getCompanyId());
				goods.setSearchContent(searchContent);goods.setUpdater(23);
				goods.setBrandName(searchBrandStr);
				goods.setSpecModel(searchModelSpec);
				goods.setBaseUnitId(baseUnitId);
				Map<String, Object> map = goodsService.queryGoodsListPage(goods,page,session);
				List<Goods> goodsList = (List<Goods>)map.get("list");
				if(quoteorderId != null) {
					Quoteorder quoteorder = quoteService.getQuoteInfoByKey(quoteorderId);
					//根据客户ID查询客户信息
					TraderCustomerVo customer = traderCustomerService.getTraderCustomerInfo(quoteorder.getTraderId());
					goodsList = goodsChannelPriceService.getGoodsChannelPriceList(quoteorder.getSalesAreaId() == 0 ? salesAreaId : quoteorder.getSalesAreaId(), quoteorder.getCustomerNature(), customer.getOwnership(), goodsList);
					for (Goods gd : goodsList){
						PriceCenterQueryDto priceCenter = this.getPriceCenter(customer, gd.getSku());
						gd.setCheckPrice(priceCenter.getCheckPrice());
						gd.setPrice(priceCenter.getPrice());
						gd.setCheckPriceStr(priceCenter.getCheckPriceStr());
					}
				}

				mv.addObject("goodsList",goodsList);
				mv.addObject("page", (Page)map.get("page"));
				mv.addObject("searchContent", searchContent);

			}

			//所有单位
			Unit unit = new Unit();
			unit.setCompanyId(user.getCompanyId());
			List<Unit> unitList = unitService.getAllUnitList(unit);
			mv.addObject("unitList",unitList);
			mv.addObject("searchBrandStr",searchBrandStr);
			mv.addObject("searchModelSpec",searchModelSpec);
			mv.addObject("baseUnitId",baseUnitId);


			mv.addObject("optType",optType);
			mv.addObject("traderId",traderId);
			mv.addObject("quoteorderId", quoteorderId);
		} catch (Exception e) {
			logger.error("addQuoteGoods:", e);
		}
		mv.setViewName("order/quote/add_quote_goods");
		return mv;
	}

	/**
	 * <b>Description:</b><br> 保存报价产品信息
	 * @param request
	 * @param quoteGoods
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月4日 上午10:17:49
	 */
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value="/saveQuoteGoods")
	@SystemControllerLog(operationType = "add",desc = "保存报价产品信息")
	public ResultInfo<?> saveQuoteGoods(HttpServletRequest request,QuoteorderGoods quoteGoods,Attachment ach){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
			List<User> userListByorderId = authService.getUserListByorderId(quoteGoods.getQuoteorderId(), authService.QUOTEORDER_TYPE);
			if (authService.existOrNot(user, userListByorderId)) {
				logger.info("销售越权操作:接口[order/quote/saveQuoteGoods],行为[编辑非自己及下属的报价单],操作人{}",user.getUsername());
			}
		}
		if(user!=null){
			quoteGoods.setCreator(user.getUserId());
			quoteGoods.setAddTime(DateUtil.sysTimeMillis());

			quoteGoods.setUpdater(user.getUserId());
			quoteGoods.setModTime(DateUtil.sysTimeMillis());

			ach.setAddTime(DateUtil.sysTimeMillis());
			ach.setCreator(user.getUserId());
		}
		return quoteService.saveQuoteGoods(quoteGoods,ach);
	}

	/**
	 * <b>Description:</b><br> 产品附件上传
	 * @param request
	 * @param response
	 * @param lwfile
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月18日 下午1:46:37
	 */
	@ResponseBody
	@RequestMapping(value = "/goodsImgUpload")
	@SystemControllerLog(operationType = "edit",desc = "产品附件上传")
	public FileInfo goodsImgUpload(HttpServletRequest request, HttpServletResponse response, @RequestParam("lwfile") MultipartFile lwfile) {
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			String path = "/upload/quote/goods";
			long size = lwfile.getSize();
			if(size > 2*1024*1024){
				return new FileInfo(-1,"图片大小应为2MB以内");
			}
			System.out.println(lwfile.getOriginalFilename());
			String fileName = lwfile.getOriginalFilename();
			String prefix=fileName.substring(fileName.lastIndexOf(".")+1);
			if(!prefix.equals("jpg")){
				return new FileInfo(-1,"请选择jpg格式图片");
			}
			/*if(!FileType.getFileHeader(lwfile.getOriginalFilename()).equals("jpg")){
				return new FileInfo(-1,"请选择jpg格式图片");
			}*/
			return ossUtilsService.upload2Oss(request,lwfile);
			/*Attachment ach = new Attachment();
			ach.setAttachmentType(SysOptionConstant.ID_343);
			ach.setAttachmentFunction(SysOptionConstant.ID_494);
			ach.setName(info.getFileName());
			ach.setDomain(info.getHttpUrl());
			ach.setUri(info.getFilePath());*/
		}else{
			return new FileInfo(-1,"登录用户不能为空");
		}
	}

	/**
	 * <b>Description:</b><br> 编辑报价产品信息初始化
	 * @param request
	 * @param quoteGoods
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月4日 上午10:18:31
	 */
	@ResponseBody
	@RequestMapping(value="/editQuoteGoodsInit")
	public ModelAndView editQuoteGoodsInit(HttpServletRequest request,QuoteorderGoods quoteGoods){
		ModelAndView mv = new ModelAndView();
		try {
			//根据报价产品ID获取对应产品信息
			quoteGoods = quoteService.getQuoteGoodsById(quoteGoods.getQuoteorderGoodsId(),request.getSession());
			Quoteorder quoteorder = quoteService.getQuoteInfoByKey(quoteGoods.getQuoteorderId());
			List<Goods> goodsLists = new ArrayList<>();
			Goods goods = new Goods();
			goods.setGoodsId(quoteGoods.getGoodsId());
			goodsLists.add(goods);
			//根据客户ID查询客户信息
			TraderCustomerVo customer = traderCustomerService.getTraderCustomerInfo(quoteorder.getTraderId());
			// 优先查询商品核价中心价格
			PriceCenterQueryDto priceCenter = this.getPriceCenter(customer, quoteGoods.getSku());
			quoteGoods.setCheckPrice(priceCenter.getCheckPrice());
			quoteGoods.setCheckPriceStr(priceCenter.getCheckPriceStr());

			if (Objects.isNull(quoteGoods.getCheckPrice())){
				log.info("{}未查询到商品中心核价价格，按原逻辑查询",quoteGoods.getSku());
				List<Goods> goodsList = goodsChannelPriceService.getGoodsChannelPriceList(quoteorder.getSalesAreaId() == 0?0:quoteorder.getSalesAreaId(), quoteorder.getCustomerNature(),customer.getOwnership(), goodsLists);
				for(Goods g:goodsList){
					quoteGoods.setChannelPrice(g.getChannelPrice());
				}
			}

			//日志
			mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(quoteGoods)));

			//新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
			Map<String,Object> newSkuInfo = vGoodsService.skuTip(quoteGoods.getGoodsId());
			mv.addObject("newSkuInfo", newSkuInfo);
			//新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

			if(quoteGoods!=null && quoteGoods.getIsTemp()==0){//非临时产品
				mv.setViewName("order/quote/edit_quote_formal_goods");
			}else{
				mv.setViewName("order/quote/edit_quote_temp_goods");
			}
			//根据条件查询產品附件表信息
			Attachment ach2 = new Attachment();
			ach2.setAttachmentType(SysOptionConstant.ID_343);
			ach2.setAttachmentFunction(SysOptionConstant.ID_494);
			ach2.setRelatedId(quoteGoods.getQuoteorderGoodsId());
			Attachment ach = quoteService.getQuoteGoodsAttachment(ach2);
			mv.addObject("ach", ach);

			List<String> skuNoList = new ArrayList<>();
			skuNoList.add(quoteGoods.getSku());
			LabelQuery labelQuery = new LabelQuery();
			labelQuery.setScene(RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode());//
			labelQuery.setRelationId(quoteGoods.getQuoteorderId());
			labelQuery.setSkuNoList(skuNoList);
			String componentHtml = remarkComponentService.getComponentHtml(labelQuery);
			quoteGoods.setComponentHtml(componentHtml);

			mv.addObject("quoteGoods", quoteGoods);
		} catch (Exception e) {
			logger.error("editQuoteGoodsInit:", e);
		}
		return mv;
	}

	/**
	 * 查询价格中心核价
	 * @param traderCustomer
	 * @param skuNo
	 * @return
	 */
	private PriceCenterQueryDto getPriceCenter(TraderCustomerVo traderCustomer, String skuNo){
		BigDecimal checkPrice = null;
		String checkPriceStr = "";
		BigDecimal price = null;
		SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = basePriceService.findSkuPriceInfoBySkuNo(skuNo);
		//已经核价就设置成本价
		if (skuPriceInfoDetailResponseDto != null && traderCustomer != null) {
			//分销商
			if(ErpConst.CUSTOME_RNATURE.equals(traderCustomer.getCustomerNature())){
				checkPrice = skuPriceInfoDetailResponseDto.getDistributionPrice();
				price = skuPriceInfoDetailResponseDto.getDistributionPrice();
				checkPriceStr += ("商品已核价(经销价:"+checkPrice+")");
			}
			//终端
			if(ErpConst.CUSTOME_INTERMIAL.equals(traderCustomer.getCustomerNature())){
				//客户类型 = 科研医疗 终端 取科研终端价(新增的)
				if (ErpConst.CUSTOME_TYPE_RESEARCH_MEDICAL.equals(traderCustomer.getCustomerType())){
					if (ObjectUtil.isNotNull(skuPriceInfoDetailResponseDto.getResearchTerminalPrice())){
						checkPrice = skuPriceInfoDetailResponseDto.getResearchTerminalPrice();
						price = skuPriceInfoDetailResponseDto.getResearchTerminalPrice();
						checkPriceStr += ("商品已核价(科研终端价:"+checkPrice+")");
					}
				}else {
					checkPrice = skuPriceInfoDetailResponseDto.getTerminalPrice();
					price = skuPriceInfoDetailResponseDto.getTerminalPrice();
					checkPriceStr += ("商品已核价(终端价:"+checkPrice+")");
				}
			}
		}
		PriceCenterQueryDto priceCenterQueryDto = new PriceCenterQueryDto();
		priceCenterQueryDto.setCheckPriceStr(checkPriceStr);
		priceCenterQueryDto.setPrice(price);
		priceCenterQueryDto.setCheckPrice(checkPrice);
		return priceCenterQueryDto;
	}

	/**
	 * <b>Description:</b><br> 编辑保存报价产品信息
	 * @param request
	 * @param quoteGoods
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月4日 上午10:18:56
	 */
	@ResponseBody
	@RequestMapping(value="/editQuoteGoods")
	@SystemControllerLog(operationType = "edit",desc = "保存编辑报价产品信息")
	public ResultInfo<?> editQuoteGoods(HttpServletRequest request,String beforeParams,QuoteorderGoods quoteGoods,Attachment ach){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			quoteGoods.setUpdater(user.getUserId());
			quoteGoods.setModTime(DateUtil.sysTimeMillis());

			ach.setAddTime(DateUtil.sysTimeMillis());
			ach.setCreator(user.getUserId());
		}
		//根据报价产品ID获取对应产品信息
		return quoteService.editQuoteGoods(quoteGoods,ach);
	}

	/**
	 * <b>Description:</b><br> 删除报价产品信息
	 * @param request
	 * @param quoteGoods
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月4日 上午10:19:35
	 */
	@ResponseBody
	@RequestMapping(value="/delQuoteGoodsById")
	@SystemControllerLog(operationType = "delete",desc = "删除报价产品信息")
	public ResultInfo<?> delQuoteGoodsById(HttpServletRequest request,QuoteorderGoods quoteGoods){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
			List<User> userListByorderId = authService.getUserListByorderId(quoteGoods.getQuoteorderId(), authService.QUOTEORDER_TYPE);
			if (authService.existOrNot(user, userListByorderId)) {
				logger.info("销售越权操作:接口[order/quote/delQuoteGoodsById],行为[删除非自己及下属的报价单中的商品],操作人{}",user.getUsername());
			}
		}
		if(user!=null){
			quoteGoods.setUpdater(user.getUserId());
			quoteGoods.setModTime(DateUtil.sysTimeMillis());
		}
		//根据报价产品ID获取对应产品信息
		return quoteService.delQuoteGoodsById(quoteGoods);
	}

	/**
	 * <b>Description:</b><br> 修改报价付款信息
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月6日 下午2:29:13
	 */
	@ResponseBody
	@RequestMapping(value="/editQuoteAmount")
	@SystemControllerLog(operationType = "edit",desc = "修改报价付款信息")
	public ModelAndView editQuoteAmount(HttpServletRequest request,String beforeParams,Quoteorder quote, OrderTerminalDto orderTerminalDto,
										@RequestParam(value="quoteSource",required=false)String quoteSource){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			quote.setUpdater(user.getUserId());
			quote.setModTime(DateUtil.sysTimeMillis());
		}
		if(quote.getPaymentType() != null && quote.getPaymentType().equals(419)){//419 : 100%预付--其他付款计划：均设置默认值
			BigDecimal bd = new BigDecimal(0.00);
			quote.setAccountPeriodAmount(bd);//账期支付金额
			quote.setPeriodDay(0);//账期天数
			quote.setLogisticsCollection(0);//物流代收0否 1是
			quote.setRetainageAmount(bd);//尾款
			quote.setRetainageAmountMonth(0);//尾款期限(月)
		}
		ResultInfo<?> result = quoteService.editQuoteAmount(quote);
		ModelAndView mv = new ModelAndView();
		if(StringUtils.isNotBlank(quoteSource)){
			mv.addObject("refresh", "false_true_false");//是否关闭当前页，是否刷新当前页，是否刷新上层页面----三个参数为必传项
		}else{
			mv.addObject("refresh", "true_false_true");//是否关闭当前页，是否刷新当前页，是否刷新上层页面----三个参数为必传项
		}
		mv.addObject("url","./getQuoteDetail.do?quoteorderId="+quote.getQuoteorderId()+"&viewType=2");
		if(result.getCode()==0){
			// VDERP-15595 更新终端信息
			orderTerminalDto.setBusinessId(quote.getQuoteorderId());
			orderTerminalDto.setBusinessNo(quote.getQuoteorderNo());
			OrderTerminalContext orderTerminalContext = new OrderTerminalContext();
			orderTerminalContext.setOrderTerminalStrategy(quoteOrderTerminalStrategy);
			orderTerminalContext.executeStrategy(orderTerminalDto);
			return success(mv);
		}else{
			return fail(mv);
		}
	}

	/**
	 * <b>Description:</b><br> 添加报价沟通记录
	 * @param quote
	 * @param traderCustomer
	 * @param request
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月7日 上午10:57:44
	 */
	@ResponseBody
	@RequestMapping(value="/addComrecord")
	public ModelAndView addComrecord(Quoteorder quote,TraderCustomer traderCustomer,HttpServletRequest request){
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		ModelAndView mav=new ModelAndView("order/quote/add_communicate");
		TraderContact traderContact = new TraderContact();
		// 联系人
		traderContact.setTraderId(traderCustomer.getTraderId());
		traderContact.setIsEnable(ErpConst.ONE);
		traderContact.setTraderType(ErpConst.ONE);
		List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);
		// 客户标签
		Tag tag = new Tag();
		tag.setTagType(SysOptionConstant.ID_32);
		tag.setIsRecommend(ErpConst.ONE);
		tag.setCompanyId(user.getCompanyId());

		//默认沟通时间
		Date now = new Date();
		String startTime = DateUtil.DateToString(now,"yyyy-MM-dd HH:mm:ss");
		Date endDate = new Date(now.getTime() + 120000);//当前时间添加2分钟
		String endTime = DateUtil.DateToString(endDate,"yyyy-MM-dd HH:mm:ss");
		mav.addObject("startTime", startTime);mav.addObject("endTime", endTime);

		Integer pageNo = 1;
		Integer pageSize = 10;

		Page page = getPageTag(request, pageNo, pageSize);
		Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

		mav.addObject("quote", quote);
		if (traderCustomer.getTraderId() != null && traderCustomer.getTraderId() > 0){
			mav.addObject("traderId",traderCustomer.getTraderId());
		} else {
			mav.addObject("traderId",quote.getTraderId());
		}

		mav.addObject("traderBaseInfo", getTraderBaseInfoByTraderId(quote.getTraderId(),  ErpConst.ONE));
		mav.addObject("contactList", contactList);

		List<SysOptionDefinition> communicateList = getSysOptionDefinitionList(SysOptionConstant.ID_23);

		addCommunicateGoalList2ModelView(mav);

		mav.addObject("communicateList", communicateList);
		mav.addObject("tagList", (List<Tag>) tagMap.get("list"));
		mav.addObject("page", (Page) tagMap.get("page"));

		return mav;
	}

	private void addCommunicateGoalList2ModelView(ModelAndView mv){
		List<SysOptionDefinition> communicateGoalList = new ArrayList<>();
		//VDERP-3323 沟通目的按照指定排序
		Map<Integer, SysOptionDefinition> communicateGoalMap = getSysOptionDefinitionList(SysOptionConstant.ID_24).stream().collect(Collectors.toMap(SysOptionDefinition::getSysOptionDefinitionId,item -> item));
		Integer[]  communicateGoalIdArray = new Integer[]{468,636,635,637,638,265};
		for (int i = 0; i < communicateGoalIdArray.length; i++) {
			if (communicateGoalMap.containsKey(communicateGoalIdArray[i])){
				communicateGoalList.add(communicateGoalMap.get(communicateGoalIdArray[i]));
			}
		}
		mv.addObject("communicateGoalList", communicateGoalList);
	}

	/**
	 * <b>Description:</b><br> 保存报价沟通记录
	 * @param request
	 * @param communicateRecord
	 * @param session
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月7日 下午1:12:34
	 */
	@ResponseBody
	@RequestMapping(value="/saveCommunicate")
	@SystemControllerLog(operationType = "add",desc = "保存报价沟通记录")
	public ResultInfo<?> saveCommunicate(HttpServletRequest request,CommunicateRecord communicateRecord,HttpSession session){
		Boolean record=false;
		if (communicateRecord.getBussinessChanceId() != null && communicateRecord.getBussinessChanceId() > 0) {
			communicateRecord.setCommunicateType(SysOptionConstant.ID_244);//报价
			communicateRecord.setRelatedId(communicateRecord.getBussinessChanceId());
		} else {
			communicateRecord.setCommunicateType(SysOptionConstant.ID_245);//报价
			communicateRecord.setRelatedId(communicateRecord.getQuoteorderId());
		}

		try {
			if(null != communicateRecord.getCommunicateRecordId() && communicateRecord.getCommunicateRecordId() > 0){
				record = traderCustomerService.saveEditCommunicate(communicateRecord, request, session);
			}else{
				record = traderCustomerService.saveAddCommunicate(communicateRecord, request, session);
			}
		} catch (Exception e) {
			logger.error("saveCommunicate:", e);
		}
		if(record){
			if (communicateRecord.getQuoteorderId()!=null) {
				//修改报价主表信息(有沟通记录0无 1有)
				Quoteorder quote = new Quoteorder();
				User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
				if(user!=null){
					quote.setUpdater(user.getUserId());
					quote.setModTime(DateUtil.sysTimeMillis());
				}
				quote.setQuoteorderId(communicateRecord.getQuoteorderId());
//			ResultInfo<?> resultInfo = quoteService.editQuoteHaveCommunicate(quote);
				return quoteService.editQuoteHaveCommunicate(quote);
			}
			return new ResultInfo<>(0,"操作成功");
		} else {
			return new ResultInfo<>(-1, "操作失败！");
		}
	}

	/**
	 * <b>Description:</b><br> 编辑报价沟通记录
	 * @param communicateRecord
	 * @param quote
	 * @param request
	 * @param session
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月7日 下午1:13:02
	 */
	@ResponseBody
	@RequestMapping(value = "/editCommunicate")
	public ModelAndView editCommunicate(CommunicateRecord communicateRecord, Quoteorder quote, HttpServletRequest request,HttpSession session) {
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		ModelAndView mv = new ModelAndView("order/quote/edit_communicate");
		try {
			CommunicateRecord communicate = traderCustomerService.getCommunicate(communicateRecord);
//		communicate.setTraderCustomerId(communicateRecord.getTraderCustomerId());
			communicate.setTraderId(communicateRecord.getTraderId());

			TraderContact traderContact = new TraderContact();
			// 联系人
			traderContact.setTraderId(communicateRecord.getTraderId());
			traderContact.setIsEnable(ErpConst.ONE);
			traderContact.setTraderType(ErpConst.ONE);
			List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);

			// 沟通方式
//		if (JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_23)) {
//			String strJson = JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_23);
//			JSONArray jsonArray = JSONArray.fromObject(strJson);
//			List<SysOptionDefinition> communicateList = (List<SysOptionDefinition>) JSONArray.toCollection(jsonArray, SysOptionDefinition.class);
//			mv.addObject("communicateList", communicateList);
//		}
			List<SysOptionDefinition> communicateList = getSysOptionDefinitionList(SysOptionConstant.ID_23);
			mv.addObject("communicateList", communicateList);
			// 客户标签
			Tag tag = new Tag();
			tag.setTagType(SysOptionConstant.ID_32);
			tag.setIsRecommend(ErpConst.ONE);
			tag.setCompanyId(user.getCompanyId());

			Page page = getPageTag(request, 1, 10);
			Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

			mv.addObject("communicateRecord", communicate);

			mv.addObject("contactList", contactList);

			mv.addObject("traderBaseInfo", getTraderBaseInfoByTraderId(quote.getTraderId(),  ErpConst.ONE));

			addCommunicateGoalList2ModelView(mv);

			mv.addObject("tagList", (List<Tag>) tagMap.get("list"));
			mv.addObject("page", (Page) tagMap.get("page"));
			quote.setQuoteorderId(communicateRecord.getQuoteorderId());
			mv.addObject("quote", quote);
			//日志
			mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(quote)));

			if(org.apache.commons.lang.StringUtils.isNotBlank(communicate.getCoidUri())){
				String voiceStatusGptSuccess = "9";//Gpt解析成功
				CommunicateVoiceTaskDto taskDto = communicateVoiceTaskApi.selectByCommunicateRecordIdAndSence(communicate.getCommunicateRecordId(),
						AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode());

				mv.addObject("communicateTypeName",AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getName());

				if(taskDto != null && voiceStatusGptSuccess.equals(taskDto.getVoiceStatus()) ){
					List<VoiceFieldResultDto> voiceFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
							communicate.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode(),
							AiConstant.CODE_GROUP_SUMMARY);
					mv.addObject("voiceFieldList", voiceFieldList);

					List<VoiceFieldResultDto> voiceToDoFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
							communicate.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode(),
							AiConstant.CODE_GROUP_TODOTASK);
					mv.addObject("voiceToDoFieldList", voiceToDoFieldList);
				}else{
					String voiceStatusIgnore = "-1";//忽略的状态
					if(taskDto == null){
						//如果解析任务不存在，即历史数据或忽略
					}else if(voiceStatusIgnore.equals(taskDto.getVoiceStatus())){
						//忽略，则不做任务数据展示
					} else{  //非以上情况，即如果解析任务存在，但是在解析中，则展示AI解析中...
						List<VoiceFieldResultDto> voiceFieldList = new ArrayList<>();
						VoiceFieldResultDto tipVoiceField = new VoiceFieldResultDto();
						tipVoiceField.setFieldName("提示");
						tipVoiceField.setFieldResult("AI解析中...");
						voiceFieldList.add(tipVoiceField);
						mv.addObject("voiceFieldList", voiceFieldList);

						List<VoiceFieldResultDto> voiceToDoFieldList = new ArrayList<>();
						VoiceFieldResultDto tipVoiceToDoField = new VoiceFieldResultDto();
						tipVoiceToDoField.setFieldName("提示");
						tipVoiceToDoField.setFieldResult("AI解析中...");
						voiceToDoFieldList.add(tipVoiceToDoField);
						mv.addObject("voiceToDoFieldList", voiceToDoFieldList);
					}
				}
			}
		} catch (Exception e) {
			logger.error("editCommunicate:", e);
		}
		return mv;
	}
	/**
	 *
	 * <b>Description:</b><br>
	 * @param request
	 * @param quote
	 * @param session
	 * @return
	 * @Note
	 * <b>Author:</b> Michael
	 * <br><b>Date:</b> 2017年11月15日 上午11:38:32
	 */
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value = "/editApplyValidQuoteorder")
	public ResultInfo<?> editApplyValidQuoteorder(HttpServletRequest request, Quoteorder quote,String taskId, HttpSession session) {

		// add by Randy.Xu 2021/4/9 15:38 .Desc: . begin
		//VDERP-5839 数据越权 可以提交自己及下属的报价单审核
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		if(authService.checkUserIsSale(user)) {
			List<User> userListByorderId = authService.getUserListByorderId(quote.getQuoteorderId(), authService.QUOTEORDER_TYPE);
			Boolean checkFlag = authService.existOrNot(user, userListByorderId);
			if (checkFlag) {
				logger.info("销售越权操作:接口[order/quote/editApplyValidQuoteorder],行为[提交非自己及下属的报价单审核],操作人{}",user.getUsername());
			}
		}
		// add by Randy.Xu 2021/4/9 15:38 .Desc: . end
		return 	quoteService.editApplyValidQuoteorder(request,quote,taskId,session);
	}

	/**
	 * <b>Description:</b><br> 修改报价是否生效
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月7日 下午1:19:21
	 */
	@ResponseBody
	@RequestMapping(value = "/editQuoteValIdSave")
	@SystemControllerLog(operationType = "edit",desc = "修改报价是否生效保存")
	public ModelAndView editQuoteValIdSave(HttpServletRequest request,String beforeParams,Quoteorder quote){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			quote.setUpdater(user.getUserId());
			quote.setModTime(DateUtil.sysTimeMillis());

			quote.setValidTime(DateUtil.sysTimeMillis());
		}
		ResultInfo<?> result = quoteService.editQuoteValIdSave(quote);
		ModelAndView mv = new ModelAndView();
		if(result.getCode()==0){
			mv.addObject("url","./getQuoteDetail.do?quoteorderId="+quote.getQuoteorderId()+"&viewType=3");
			return success(mv);
		}else{
			mv.addObject("message", result.getMessage());
			return fail(mv);
		}
	}

	/**
	 * <b>Description:</b><br> 撤销报价生效状态
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月7日 下午5:05:45
	 */
	@ResponseBody
	@RequestMapping(value = "/revokeValIdStatus")
	@SystemControllerLog(operationType = "edit",desc = "撤销报价生效状态")
	public ModelAndView revokeValIdStatus(HttpServletRequest request,String beforeParams,Quoteorder quote){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			quote.setUpdater(user.getUserId());
			quote.setModTime(DateUtil.sysTimeMillis());

			quote.setValidTime(DateUtil.sysTimeMillis());
		}
		BussinessChance bussinessChance = new BussinessChance();
		bussinessChance.setBussinessChanceId(quote.getBussinessChanceId());
		//撤销生效更改商机报价状态，改为报价中
		bussinessChance.setStatus(1);
		ResultInfo<?> res = bussinessChanceService.editBussinessChance(bussinessChance);
		ResultInfo<?> result = quoteService.editQuoteValIdSave(quote);
		ModelAndView mv = new ModelAndView();
		mv.addObject("url","./getQuoteDetail.do?quoteorderId="+quote.getQuoteorderId()+"&viewType=2");
		if(result.getCode()==0){
			return success(mv);
		}else{
			return fail(mv);
		}
	}

	/**
	 * <b>Description:</b><br> 保存报价咨询记录
	 * @param request
	 * @param quoteConsult
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月7日 下午2:30:27
	 */
	@ResponseBody
	@RequestMapping(value = "/addQuoteConsultSave")
	@SystemControllerLog(operationType = "edit",desc = "保存报价咨询记录")
	public ResultInfo<?> addQuoteConsultSave(HttpServletRequest request,String beforeParams,QuoteorderConsult quoteConsult){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			quoteConsult.setCreator(user.getUserId());
			quoteConsult.setAddTime(DateUtil.sysTimeMillis());
		}
		return quoteService.addQuoteConsultSave(quoteConsult,user);
	}

	/**
	 * <b>Description:</b><br> 失单原因填写初始化
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月10日 上午11:05:37
	 */
	@ResponseBody
	@RequestMapping(value="/reasonOfLostOrder")
	public ModelAndView reasonOfLostOrder(HttpServletRequest request,Quoteorder quote){
		ModelAndView mv = new ModelAndView();
		try {
			//根据报价产品ID获取对应产品信息
			mv.setViewName("order/quote/lose_order_reason");
			mv.addObject("quote", quote);
			//日志
			mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(quote)));
		} catch (Exception e) {
			logger.error("reasonOfLostOrder:", e);
		}
		return mv;
	}

	/**
	 * <b>Description:</b><br> 修改报价为失单状态（备注）
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月10日 上午11:04:36
	 */
	@ResponseBody
	@RequestMapping(value = "/editLoseOrderStatus")
	@SystemControllerLog(operationType = "edit",desc = "修改报价为失单状态（备注）")
	public ResultInfo<?> editLoseOrderStatus(HttpServletRequest request,String beforeParams,Quoteorder quote){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			quote.setUpdater(user.getUserId());
			quote.setModTime(DateUtil.sysTimeMillis());

			quote.setFollowOrderTime(DateUtil.sysTimeMillis());
		}
		return quoteService.editLoseOrderStatus(quote);
	}

	/**
	 * <b>Description:</b><br> 报价咨询列表（包括销售人员）
	 * @param request
	 * @param pageNo
	 * @param pageSize
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月10日 下午3:28:33
	 */
	@ResponseBody
	@RequestMapping(value="getQuoteConsultListPage")
	public ModelAndView getQuoteConsultListPage(HttpServletRequest request,@RequestParam(required = false, defaultValue = "1") Integer pageNo,
												@RequestParam(required = false) Integer pageSize,QuoteorderConsult quoteConsult,@RequestParam(required = false, value="searchBeginTime") String searchBeginTime,
												@RequestParam(required = false, value="searchEndTime") String searchEndTime,
												HttpSession session,
												@RequestParam(required = false) Boolean searchFlag){
		User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		ModelAndView mv = new ModelAndView();

		Page page = getPageTag(request,pageNo,pageSize);

		//判断报价单是否存在报价预警
		if (QuotedAlarmModeEnum.PURCHASER_MODE.allowLevel(quoteConsult.getPurchaserAlarmLevel())) {
			quoteConsult.setQuoteAlarmMode(QuotedAlarmModeEnum.PURCHASER_MODE.getMode());
			quoteConsult.setPurchaserAlarmLevel(quoteConsult.getPurchaserAlarmLevel());
		}

		try {
			mv.addObject("searchBeginTime", searchBeginTime);mv.addObject("searchEndTime", searchEndTime);
			if(StringUtils.isNotBlank(searchBeginTime)){
				quoteConsult.setBeginTime(DateUtil.convertLong(searchBeginTime + " 00:00:00",""));
			}
			if(StringUtils.isNotBlank(searchEndTime)){
				quoteConsult.setEndTime(DateUtil.convertLong(searchEndTime + " 23:59:59",""));
			}

			quoteConsult.setCompanyId(sessUser.getCompanyId());

			//产品部门--选择条件
			List<Organization> productOrgList = orgService.getOrgListByPositType(SysOptionConstant.ID_311,sessUser.getCompanyId());
			mv.addObject("productOrgList", productOrgList);
			//产品负责人
			//先从会话中查看有没有 产品负责人，没有再从数据库查 ，以减少数据库查询次数。
			List<User> productUserList = (List<User>)request.getSession().getAttribute("productUserList");
			if(null == productUserList || productUserList.size()==0){
				productUserList = userService.selectAllAssignUser();
				request.getSession().setAttribute("productUserList", productUserList);
			}

			if (searchFlag == null || !searchFlag){
				User currentUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
				List<Integer> productUserIdList = productUserList.stream().map(User::getUserId).collect(Collectors.toList());
				Integer productCurrentUser = productUserIdList.contains(currentUser.getUserId()) ? currentUser.getUserId() : 0;
				quoteConsult.setConsultReplier(productCurrentUser);
			}

			Map<String, Object> map = quoteService.getQuoteConsultListPage(quoteConsult,page,session);
			if(map != null && !map.isEmpty()){
				List<QuoteorderConsult> quoteConsultList = (List<QuoteorderConsult>)map.get("quoteConsultList");
				User user = null;
				for(int i=0;i<quoteConsultList.size();i++){
					//销售人员
					user = userService.getUserById(quoteConsultList.get(i).getSaleUserId());
					quoteConsultList.get(i).setSaleUserName(user==null?"":user.getUsername());
				}
				mv.addObject("quoteConsultList",(List<QuoteorderConsult>)map.get("quoteConsultList"));

				//销售人员选择条件
				List<User> quoteConsultUserList = userService.getUserByUserIds((List<Integer>)map.get("quoteConsultUserList"));
				mv.addObject("quoteConsultUserList",quoteConsultUserList);
			}

			mv.addObject("quoteConsult",quoteConsult);

			mv.addObject("page", (Page)map.get("page"));
		} catch (Exception e) {
			logger.error("getQuoteConsultListPage:", e);
		}

		mv.setViewName("order/quote/index_consult");
		return mv;
	}

	/**
	 * <b>Description:</b><br> 验证报价单中产品货期和报价是否为空
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月25日 下午5:23:22
	 */
	@ResponseBody
	@RequestMapping(value="getQuoteGoodsPriceAndCycle")
	public ResultInfo<?> getQuoteGoodsPriceAndCycle(HttpServletRequest request,Quoteorder quote){
		return quoteService.getQuoteGoodsPriceAndCycle(quote);
	}

	/**
	 * <b>Description:</b><br> 咨询答复内容保存
	 * @param request
	 * @param quoteConsult
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月11日 下午2:15:06
	 */
	@ResponseBody
	@FormToken(remove=true)
	@RequestMapping(value="saveReplyQuoteConsult")
	@SystemControllerLog(operationType = "edit",desc = "咨询答复内容保存")
	public ResultInfo<?> saveReplyQuoteConsult(HttpServletRequest request,String beforeParams,QuoteorderConsult quoteConsult,Quoteorder quote,
											   @RequestParam(required = false, value="referencePriceArr") String referencePriceArr,
											   @RequestParam(required = false, value="referenceDeliveryCycleArr") String referenceDeliveryCycleArr,
											   @RequestParam(required = false, value="reportStatusArr") String reportStatusArr,
											   @RequestParam(required = false, value="reportCommentsArr") String reportCommentsArr,
											   @RequestParam(required = false, value="consultOtherArr") String consultOtherArr,
											   @RequestParam(required = false, value="quoteorderConsultIdArr") String quoteorderConsultIdArr){
		ResultInfo resultInfo = new ResultInfo();
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);

		List<String> referencePriceList = JSON.parseArray(request.getParameter("referencePriceArr"),String.class);
		List<String> referenceDeliveryCycleList = JSON.parseArray(request.getParameter("referenceDeliveryCycleArr"),String.class);
		List<Integer> reportStatusList = JSON.parseArray(request.getParameter("reportStatusArr"),Integer.class);
		List<String> reportCommentsList = JSON.parseArray(request.getParameter("reportCommentsArr"),String.class);
		List<String> consultOtherList = JSON.parseArray(request.getParameter("consultOtherArr"),String.class);

		List<Integer> quoteorderConsultIdList = JSON.parseArray(request.getParameter("quoteorderConsultIdArr"),Integer.class);

		if (quoteorderConsultIdList.size() == 0) {
			resultInfo.setCode(-1);
			resultInfo.setMessage("回复内容为空");
			return resultInfo;
		}

		QuoteorderConsultContentVo consultContentVo = new QuoteorderConsultContentVo();
		consultContentVo.setConsultType(2);
		List<QuoteorderConsultContentVo.SkuConsult> skuConsultList = new ArrayList<>();
		for (int i = 0; i < quoteorderConsultIdList.size(); i++) {
			QuoteorderConsultContentVo.SkuConsult skuConsult = new QuoteorderConsultContentVo.SkuConsult();
			QuoteorderGoods quoteorderGoods = quoteorderMapper.getQuoteorderGoodsByQuoteorderGoodsId(quoteorderConsultIdList.get(i));
			if (quoteorderGoods != null){
				skuConsult.setQuoteorderGoodsId(quoteorderGoods.getQuoteorderGoodsId());
				skuConsult.setSku(quoteorderGoods.getSku());
				consultContentVo.setConsultRelatedId(quoteorderGoods.getQuoteorderId());
				if ((referencePriceList.size()-1) >= i &&  StringUtils.isNotBlank(referencePriceList.get(i))){
					skuConsult.setReferencePrice(referencePriceList.get(i));
				}
				if ((referenceDeliveryCycleList.size()-1) >= i && StringUtils.isNotBlank(referenceDeliveryCycleList.get(i))){
					skuConsult.setDeliveryCycle(referenceDeliveryCycleList.get(i));
				}
				if ((reportStatusList.size()-1) >= i && reportStatusList.get(i) >= -1){
					skuConsult.setReport(reportStatusList.get(i));
				}
				if ((reportCommentsList.size() - 1) >= i && reportCommentsList.get(i) != null) {
					skuConsult.setReportContet(reportCommentsList.get(i));
				}
				if ((consultOtherList.size()-1) >= i && StringUtils.isNotBlank(consultOtherList.get(i))){
					skuConsult.setConsultOtherReplyContent(consultOtherList.get(i));
				}
			}
			skuConsultList.add(skuConsult);
		}

		if (skuConsultList.size() == 0) {
			resultInfo.setCode(-1);
			resultInfo.setMessage("回复内容为空");
			return resultInfo;
		}
		consultContentVo.setSkuConsults(skuConsultList);
		quoteService.saveSupplyReplyOfQuoteorderConsult(consultContentVo,user.getUserId());

		resultInfo.setCode(0);
		resultInfo.setMessage("提交成功");
		return resultInfo;
	}

	/**
	 * <b>Description:</b><br> 修改报价咨询回复状态保存
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月11日 下午2:59:27
	 */
/*	@ResponseBody
	@RequestMapping(value="editConsultStatus")
	@SystemControllerLog(operationType = "edit",desc = "修改报价咨询回复状态保存")
	public ResultInfo<?> editConsultStatus(HttpServletRequest request,String beforeParams,Quoteorder quote){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			quote.setUpdater(user.getUserId());
			quote.setModTime(DateUtil.sysTimeMillis());
		}
		return quoteService.editConsultStatus(quote);
	}*/

	/**
	 * <b>Description:</b><br> 验证报价中新添加的产品是否重复
	 * @param request
	 * @param
	 * @return
	 * @Note
	 * <b>Author:</b> Administrator
	 * <br><b>Date:</b> 2017年9月11日 下午6:53:39
	 */
	@ResponseBody
	@RequestMapping(value="vailQuoteGoodsRepeat")
	public ResultInfo<?> vailQuoteGoodsRepeat(HttpServletRequest request,QuoteorderGoods quoteGoods){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			return quoteService.vailQuoteGoodsRepeat(quoteGoods);
		}
		return new ResultInfo<>(-1, "用户登录超时");
	}



	/**
	 * <b>Description:</b><br> 报价信息列表导出
	 * @param request
	 * @param quote
	 * @param response
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月24日 下午6:46:10
	 */
	@RequestMapping(value="quoteExport")
	public void quoteExport(HttpServletRequest request,Quoteorder quote,HttpSession session,HttpServletResponse response,
							@RequestParam(required = false, value="beginTime") String beginTime,
							@RequestParam(required = false, value="endTime") String endTime){

		OutputStream out = null;SXSSFWorkbook wb = null;
		try {
			//-------------------查询条件----------------------------------------

			if(StringUtils.isNoneBlank(beginTime)){
				quote.setBeginDate(DateUtil.convertLong(beginTime + " 00:00:00",""));
			}
			if(StringUtils.isNoneBlank(endTime)){
				quote.setEndDate(DateUtil.convertLong(endTime + " 23:59:59",""));
			}
			//查询沟通记录
			if(quote.getTimeType()!=null && quote.getTimeType()==2){
				if(quote.getBeginDate()!=null || quote.getEndDate()!=null){//若都为空，则查询全部报价列表，不需要查询沟通记录
					//根据时间获取沟通记录中外键ID
					quote.setKeyIds(quoteService.getCommunicateRecordByDate(quote.getBeginDate(),quote.getEndDate(), SysOptionConstant.ID_244 + "," + SysOptionConstant.ID_245));
				}
			}

			User user = (User)session.getAttribute(Consts.SESSION_USER);
			//List<User> userList = userService.getNextAllUserList(user.getUserId(), user.getCompanyId(), true, user.getPositLevel(), SysOptionConstant.ID_310);

			List<Integer> positionType = new ArrayList<>();
			positionType.add(SysOptionConstant.ID_310);
			List<User> userList = userService.getMyUserList(user, positionType, false);

			if(quote.getOptUserId()==null){
				//销售人员所属客户（即当前报价单操作人员）
				List<Integer> traderIdList = userService.getTraderIdListByUserList(userList, ErpConst.ONE + "," +ErpConst.TWO);
				quote.setTraderIdList(traderIdList);
			}else{
				//销售人员所属客户（即当前报价单操作人员）
				List<Integer> traderIdList = userService.getTraderIdListByUserId(quote.getOptUserId(), 1);//1客户，2供应商
				quote.setTraderIdList(traderIdList);
			}
			//------------------------------------------------------------------------------------



//			User user = (User)session.getAttribute(Consts.SESSION_USER);

			response.setHeader("Content-type", "text/html;charset=UTF-8");
			response.setContentType("multipart/form-data");
			String fileName = System.currentTimeMillis() + ".xlsx";
			response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);


			int sheetNum = 1;// 工作薄sheet编号
			int currentRowCount = 1;// 当前的行号
			int bodyRowCount = 1;// 正文内容行号

			out = response.getOutputStream();
			wb = new SXSSFWorkbook(Consts.EXP_TEM_NUM);//内存中保留 x 条数据，以免内存溢出，其余写入 硬盘
			Sheet sh = wb.createSheet("工作簿" + sheetNum);
			writeTitleContent(sh);

			Row row_value = null;
			Cell cel_value = null;

			// List<Action> list = reportService.selectList();
			List<Quoteorder> list = new ArrayList<>();


			CommunicateRecord cr = null;
			// ------------------定义表头----------------------------------------
			int page_size = Consts.EXP_SEARCH_SIZE;// 数据库中每次查询条数
			int list_count = quoteService.getQuoteListCount(quote);//查询报价记录数
			int export_times = list_count % page_size > 0 ? list_count / page_size + 1 : list_count / page_size;
			for (int i = 0; i < export_times; i++) {
				list = quoteService.getQuoteListSize(quote,page_size * i, page_size);//一次导出开始和结束记录行
				int len = list.size() < page_size ? list.size() : page_size;
				for (int j = 0; j < len; j++) {
					// Row row_value = sh.createRow(j * page_size + i + 1);
					row_value = sh.createRow(bodyRowCount);

					cel_value = row_value.createCell(0);//报价单号
					/*if (list.get(j).getQuoteorderNo() instanceof Integer) {// 判断对象类型
						cel_value.setCellType(XSSFCell.CELL_TYPE_NUMERIC);// 设置文本框类型
					}*/
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getQuoteorderNo());

					cel_value = row_value.createCell(1);//创建时间
					cel_value.setCellType(CellType.STRING);
					String addTime = DateUtil.convertString(list.get(j).getAddTime(), "yyyy-MM-dd HH:mm:ss");
					cel_value.setCellValue(addTime);


					cel_value = row_value.createCell(2);//客户名称
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getTraderName());

					cel_value = row_value.createCell(3);//客户类型
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getCustomerTypeStr());
					/*CellRangeAddress cra = new CellRangeAddress(j+1, (short)(j+1), 3, (short)4);//合并单元格：参数：起始行号，终止行号， 起始列号，终止列号
					sh.addMergedRegion(cra);*/

					cel_value = row_value.createCell(4);//客户性质
					cel_value.setCellValue(list.get(j).getCustomerNatureStr());

					cel_value = row_value.createCell(5);//客户地区
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getArea());

					cel_value = row_value.createCell(6);//新/老客户
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getIsNewCustomer()==0?"否":"是");

					cel_value = row_value.createCell(7);//客户等级
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getCustomerLevel());

					cel_value = row_value.createCell(8);//联系人
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getTraderContactName());

					cel_value = row_value.createCell(9);//座机
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getTelephone());

					cel_value = row_value.createCell(10);//手机号
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getMobile());

					cel_value = row_value.createCell(11);//联系地址
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getAddress());

					cel_value = row_value.createCell(12);//销售区域
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getSalesArea());

					cel_value = row_value.createCell(13);//终端名称
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getTerminalTraderName());

					cel_value = row_value.createCell(14);//终端类型
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getTerminalTraderTypeStr());

					cel_value = row_value.createCell(15);//报价金额
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(String.format("%.2f", list.get(j).getTotalAmount().doubleValue()));

					cel_value = row_value.createCell(16);//销售部门
					cel_value.setCellType(CellType.STRING);
					//销售部门
					cel_value.setCellValue(orgService.getOrgNameById(list.get(i).getOrgId()));

					cel_value = row_value.createCell(17);//销售人员
					cel_value.setCellType(CellType.STRING);
					//销售人员
					user = userService.getUserById(list.get(i).getUserId());
					cel_value.setCellValue(user==null?"":user.getUsername());

					cel_value = row_value.createCell(18);//沟通次数
					cel_value.setCellType(CellType.STRING);
					//沟通记录次数(参数使用List，多个参数，使方法能复用)
					cr = new CommunicateRecord();
					if(list.get(i).getQuoteorderId()!=null){
						cr.setQuoteorderId(list.get(i).getQuoteorderId());
					}
					if(list.get(i).getBussinessChanceId()!=null){
						cr.setBussinessChanceId(list.get(i).getBussinessChanceId());
					}
					//沟通类型为商机和报价
					cel_value.setCellValue(quoteService.getCommunicateRecordCount(cr,SysOptionConstant.ID_244,SysOptionConstant.ID_245));


					cel_value = row_value.createCell(19);//报价有效期
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getPeriod());

					cel_value = row_value.createCell(20);//审核状态
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue("--");

					cel_value = row_value.createCell(21);//生效状态
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getValidStatus()==0?"否":"是");

					cel_value = row_value.createCell(22);//咨询答复状态
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getConsultStatus()==0?"":(list.get(j).getConsultStatus()==1?"未处理":(list.get(j).getConsultStatus()==2?"处理中":"已处理")));

					cel_value = row_value.createCell(23);//跟单状态
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getFollowOrderStatus()==0?"跟单中":(list.get(j).getFollowOrderStatus()==1?"成单":"失单"));

					cel_value = row_value.createCell(24);//失单原因
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getFollowOrderStatusComments());

					cel_value = row_value.createCell(25);//转化订单号
					cel_value.setCellType(CellType.STRING);
					cel_value.setCellValue(list.get(j).getSaleorderNo());


					/*例如：每个工作簿5条，总共10条，会生成3个工作簿，因为第二页最后一次循环时，当页是5条，下面公式成立*/
					if (currentRowCount % Consts.EXP_PRE_NUM == 0) {// 每个工作薄显示50000条数据
						sh = null;
						sheetNum++;// 工作薄编号递增1
						bodyRowCount = 0;// 正文内容行号置位为0
						sh = wb.createSheet("工作簿" + sheetNum);// 创建一个新的工作薄
						// setSheetColumn(sh);//设置工作薄列宽
						writeTitleContent(sh);// 写入标题
					}
					currentRowCount++;// 当前行号递增1
					bodyRowCount++;
				}
				list.clear();
			}

			wb.write(out);
			out.close();
			wb.dispose();

		} catch (Exception e) {
			logger.error("quoteExport:", e);
		}finally {
			if(out != null){
				try {
					out.close();
				} catch (IOException e) {
					logger.error(Contant.ERROR_MSG, e);
				}
			}
			if(wb != null){
				wb.dispose();
			}
		}
	}

	public void writeTitleContent(Sheet sh) {
		Row row = sh.createRow(0);
		Cell cel = null;
//		CellRangeAddress cra = null;
		// --------------------------------------------------
		cel = row.createCell(0);
		sh.setColumnWidth(0, 3000);// 设置列宽度
		cel.setCellValue("报价单号");

		cel = row.createCell(1);
		sh.setColumnWidth(1, 5000);// 设置列宽度
		cel.setCellValue("创建时间");

		cel = row.createCell(2);
		sh.setColumnWidth(2, 6000);// 设置列宽度
		cel.setCellValue("客户名称");

		cel = row.createCell(3);
		cel.setCellValue("客户类型");
//		cra = new CellRangeAddress(0, (short)0, 3, (short)4);//合并单元格：参数：起始行号，终止行号， 起始列号，终止列号
//		sh.addMergedRegion(cra);

		cel = row.createCell(4);
		cel.setCellValue("客户性质");
//		cra = new CellRangeAddress(0, (short)0, 5, (short)6);//合并单元格：参数：起始行号，终止行号， 起始列号，终止列号
//		sh.addMergedRegion(cra);

		cel = row.createCell(5);
		cel.setCellValue("客户地区");

		cel = row.createCell(6);
		cel.setCellValue("新/老客户");

		cel = row.createCell(7);
		cel.setCellValue("客户等级");

		cel = row.createCell(8);
		cel.setCellValue("联系人");

		cel = row.createCell(9);
		sh.setColumnWidth(9, 3000);// 设置列宽度
		cel.setCellValue("座机");

		cel = row.createCell(10);
		sh.setColumnWidth(10, 3200);// 设置列宽度
		cel.setCellValue("手机号");

		cel = row.createCell(11);
		sh.setColumnWidth(11, 6500);// 设置列宽度
		cel.setCellValue("联系地址");

		cel = row.createCell(12);
		sh.setColumnWidth(12, 5500);// 设置列宽度
		cel.setCellValue("销售区域");

		cel = row.createCell(13);
		sh.setColumnWidth(13, 6000);// 设置列宽度
		cel.setCellValue("终端名称");

		cel = row.createCell(14);
		cel.setCellValue("终端类型");

		cel = row.createCell(15);
		cel.setCellValue("报价金额");

		cel = row.createCell(16);
		sh.setColumnWidth(16, 5500);// 设置列宽度
		cel.setCellValue("销售部门");

		cel = row.createCell(17);
		cel.setCellValue("销售人员");

		cel = row.createCell(18);
		cel.setCellValue("沟通次数");

		cel = row.createCell(19);
		sh.setColumnWidth(19, 3000);// 设置列宽度
		cel.setCellValue("报价有效期");

		cel = row.createCell(20);
		cel.setCellValue("审核状态");

		cel = row.createCell(21);
		cel.setCellValue("生效状态");

		cel = row.createCell(22);
		cel.setCellValue("咨询答复");

		cel = row.createCell(23);
		cel.setCellValue("跟单状态");

		cel = row.createCell(24);
		sh.setColumnWidth(24, 5000);// 设置列宽度
		cel.setCellValue("失单原因");

		cel = row.createCell(25);
		sh.setColumnWidth(25, 3000);// 设置列宽度
		cel.setCellValue("转化订单号");

	}
	/**
	 *
	 * <b>Description:</b><br>
	 * 报价单关闭页面
	 *
	 * @Note <b>Author:</b> Michael <br>
	 *       <b>Date:</b> 2017年11月10日 下午1:39:42
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "/closeQuote")
	public ModelAndView closeQuote(HttpSession session,Integer quoteorderId) throws IOException {
		ModelAndView mv = new ModelAndView();
		List<SysOptionDefinition> firstCloseReason = getSysOptionDefinitionList(SysOptionConstant.ID_1600);

		mv.addObject("firstCloseReason", firstCloseReason);
		mv.addObject("quoteorderId", quoteorderId);
		mv.setViewName("order/quote/closeQuote");
		return mv;
	}

	/**
	 * @description: 查询字典表子集
	 * @return:
	 * @author: Strange
	 * @date: 2020/6/10
	 **/
	@ResponseBody
	@RequestMapping("/getColseSecondReason")
	public ResultInfo getColseSecondReason(Integer parentId){
		ResultInfo resultInfo = new ResultInfo();
		if(parentId == null){
			return resultInfo;
		}
		List<SysOptionDefinition> closeReason = getSysOptionDefinitionList(parentId);
		resultInfo.setCode(0);
		resultInfo.setData(closeReason);
		resultInfo.setMessage("查询成功");
		return resultInfo;
	}
	/**
	 *
	 * <b>Description:</b><br> 报价关闭审核申请
	 * @param request
	 * @param quote
	 * @param session
	 * @return
	 * @Note
	 * <b>Author:</b> Michael
	 * <br><b>Date:</b> 2017年11月15日 上午11:38:32
	 */
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value = "/closeQuoteVerify")
	public ResultInfo<?> closeQuoteVerify(HttpServletRequest request, Quoteorder quote,String reason, HttpSession session) {
		try {
			Map<String, Object> variableMap = new HashMap<String, Object>();
			// 查询当前订单的一些状态
			Quoteorder quoteorderInfo = quoteService.getQuoteInfoByKey(quote.getQuoteorderId());
			User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			//开始生成流程(如果没有taskId表示新流程需要生成)
			// 设置当前审核人(订单归属人)
			User userInfo = userService.getUserByTraderId(quoteorderInfo.getTraderId(), 1);// 1客户，2供应商
			variableMap.put("quoteorder", quoteorderInfo);
			quoteorderInfo.setOptUserName(user==null?"":userInfo.getUsername());
			/**
			 * 流程名称quoteorder+variables+businessKey
			 */
			variableMap.put("quoteorder", quoteorderInfo);
			variableMap.put("currentAssinee", quoteorderInfo.getOptUserName());
			variableMap.put("processDefinitionKey","closeQuoteorderVerify");
			variableMap.put("businessKey","closeQuoteorderVerify_" + quoteorderInfo.getQuoteorderId());
			variableMap.put("relateTableKey",quoteorderInfo.getQuoteorderId());
			variableMap.put("relateTable", "T_QUOTEORDER");

			//设置审核完成监听器回写参数
			variableMap.put("tableName", "T_QUOTEORDER");
			variableMap.put("id", "QUOTEORDER_ID");
			variableMap.put("idValue", quoteorderInfo.getQuoteorderId());
			variableMap.put("key", "FOLLOW_ORDER_STATUS");
			variableMap.put("orderId", quoteorderInfo.getQuoteorderId());
			variableMap.put("reason", reason);
			variableMap.put("closeReasonId",quote.getCloseReasonId());
			variableMap.put("closeReasonComment",quote.getCloseReasonComment());
			//关闭
			variableMap.put("value", 2);
			//回写数据的表在db中
			variableMap.put("db", 2);
			actionProcdefService.createProcessInstance(request,"closeQuoteorderVerify","closeQuoteorderVerify_" + quoteorderInfo.getQuoteorderId(),variableMap);
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("quoteorderId", quote.getQuoteorderId());
			//默认申请人通过
			//根据BusinessKey获取生成的审核实例
			Map<String, Object> historicInfo=actionProcdefService.getHistoric(processEngine, "closeQuoteorderVerify_"+ quoteorderInfo.getQuoteorderId());
			if(historicInfo.get("endStatus") != "审核完成"){
				Task taskInfo = (Task) historicInfo.get("taskInfo");
				String taskId = taskInfo.getId();
				Authentication.setAuthenticatedUserId(user.getUsername());
				Map<String, Object> variables = new HashMap<String, Object>();
				variables.put("reason", reason);
				//默认审批通过
				ResultInfo<?> complementStatus = actionProcdefService.complementTask(request,taskId,"",quoteorderInfo.getOptUserName(),variables);
				//如果未结束添加审核对应主表的审核状态
				if(!complementStatus.getData().equals("endEvent")){
					verifiesRecordService.saveVerifiesInfo(taskId,0);
				}
				// 查询当前订单的一些状态
				Quoteorder quoteorderInfoNew = quoteService.getQuoteInfoByKey(quote.getQuoteorderId());
				Integer status = null;
				if(quoteorderInfoNew.getValidStatus() == 1){
					status = 1;
				}else{
					status = 0;
				}
				return new ResultInfo(0, "操作成功",status,data);
			}
			return new ResultInfo(0, "操作成功",1,data);
		} catch (Exception e) {
			logger.error("closeQuoteVerify:", e);
			return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
		}

	}

	/**
	 *
	 * <b>Description:</b><br>
	 * 报价并行审核操作
	 *
	 * @Note <b>Author:</b> Michael <br>
	 *       <b>Date:</b> 2017年11月10日 下午1:39:42
	 */
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value = "/complementTaskParallel")
	public ResultInfo<?> complementTaskParallel(HttpServletRequest request,Integer quoteorderId, String taskId, String comment, Boolean pass,
												HttpSession session) {
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		Map<String, Object> variables = new HashMap<String, Object>();
		variables.put("pass", pass);
		variables.put("updater", user.getUserId());
		// 审批操作
		try {
			Integer statusInfo = 0;
			if (pass) {
				// 如果审核通过
				statusInfo = 0;
			} else {
				// 如果审核不通过
				statusInfo = 2;
				verifiesRecordService.saveVerifiesInfo(taskId, statusInfo);
			}
			ResultInfo<?> complementStatus = null;
			if (pass) {
				TaskService taskService = processEngine.getTaskService();
				HistoryService historyService = processEngine.getHistoryService(); // 任务相关service
				String processInstanceId = null;
				HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
				processInstanceId = historicTaskInstance.getProcessInstanceId();
//				HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
//						.processInstanceId(processInstanceId).singleResult();
//				String businessKey = historicProcessInstance.getBusinessKey();
				List<HistoricVariableInstance> historicVariableInstanceList = historyService.createHistoricVariableInstanceQuery().processInstanceId(processInstanceId).list();
				// 把list转为Map
				Map<String, Object> variablesMap = new HashMap<String, Object>();
				;
				for (HistoricVariableInstance hvi : historicVariableInstanceList) {
					variablesMap.put(hvi.getVariableName(), hvi.getValue());
				}
				List<IdentityLink> candidateUserList = null;
				if (null == variablesMap.get("candidateUserList")) {
					// 待审核人员
					candidateUserList = taskService.getIdentityLinksForTask(taskId);
					// 存入全局变量里
					taskService.setVariable(taskId, "candidateUserList", candidateUserList);
				} else {
					// 待审核人员
					candidateUserList = (List<IdentityLink>) variablesMap.get("candidateUserList");
				}
				if (null == variablesMap.get("verifyUserList")) {
					// 存入全局变量里
					taskService.setVariable(taskId, "verifyUserList", user.getUsername());
					if (!candidateUserList.isEmpty()) {
						List<String> candidateUsers = new ArrayList<>();
						for (IdentityLink il : candidateUserList) {
							candidateUsers.add(il.getUserId());
						}
						String candidateUser = StringUtils.join(candidateUsers.toArray(), ",");
						if (candidateUser.equals(user.getUsername())) {
							complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
						}
					} else {
						complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
					}
				} else {
					String verifyUser = (String) variablesMap.get("verifyUserList") + "," + user.getUsername();
					List<String> verifyUserList = Arrays.asList(verifyUser.split(","));
					verifyUserList = new ArrayList(new HashSet(verifyUserList));
					verifyUser = StringUtils.join(verifyUserList.toArray(), ",");
					// 已经审核的人存入全局变量里
					taskService.setVariable(taskId, "verifyUserList", verifyUser);
					if (!candidateUserList.isEmpty()) {
						List<String> candidateUsers = new ArrayList<>();
						for (IdentityLink il : candidateUserList) {
							candidateUsers.add(il.getUserId());
						}
						String candidateUser = StringUtils.join(candidateUsers.toArray(), ",");
						if (candidateUser.equals(verifyUser)) {
							complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
						}
					} else {
						complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
					}
				}
			} else {
				complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
			}
			// 如果未结束添加审核对应主表的审核状态
			if (complementStatus != null && !complementStatus.getData().equals("endEvent")) {
				verifiesRecordService.saveVerifiesInfo(taskId, statusInfo);
			}
			Quoteorder quoteInfo = quoteService.getQuoteInfoByKey(quoteorderId);
			Integer status = null;
			Map<String, Object> data = new HashMap<String, Object>();
			if (quoteInfo.getValidStatus() == 1) {
				status = 1;
				data.put("quoteorderId", quoteorderId);
			} else {
				status = 0;
			}
			return new ResultInfo(0, "操作成功", status, data);
		} catch (Exception e) {
			logger.error("complementTaskParallel:", e);
			return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
		}

	}
	/**
	 *
	 * <b>Description:</b><br>
	 * 报价单审核页面
	 *
	 * @Note <b>Author:</b> Michael <br>
	 *       <b>Date:</b> 2017年11月10日 下午1:39:42
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "/complement")
	public ModelAndView complement(HttpSession session,Integer quoteorderId, String taskId, Boolean pass,Integer type) {
		ModelAndView mv = new ModelAndView();
		mv.addObject("taskId", taskId);
		mv.addObject("pass", pass);
		mv.addObject("quoteorderId", quoteorderId);
		mv.addObject("type", type);
		mv.setViewName("order/quote/complement");
		return mv;
	}

	/**
	 *
	 * <b>Description:</b><br>
	 * 报价单审核操作
	 *
	 * @Note <b>Author:</b> Michael <br>
	 *       <b>Date:</b> 2017年11月10日 下午1:39:42
	 */
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value = "/complementTask")
	public ResultInfo<?> complementTask(HttpServletRequest request,Integer quoteorderId, String taskId, String comment, Boolean pass,
										HttpSession session) {
		logger.info("================报价单完成节点开始===taskId:"+taskId+"===quoteorderId:"+quoteorderId+"===");
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		Map<String, Object> variables = new HashMap<String, Object>();
		variables.put("pass", pass);
		variables.put("updater",user.getUserId());
		//审批操作
		try {
			Integer statusInfo = 0;
			if(pass){
				//如果审核通过
				statusInfo = 0;
			}else{
				//如果审核不通过
				statusInfo = 2;
				verifiesRecordService.saveVerifiesInfo(taskId,statusInfo);
			}

			ResultInfo<?> complementStatus= actionProcdefService.complementTask(request,taskId,comment,user.getUsername(),variables);
			//如果未结束添加审核对应主表的审核状态
			if(!complementStatus.getData().equals("endEvent")){
				verifiesRecordService.saveVerifiesInfo(taskId,statusInfo);
			}
			if(quoteorderId != null && quoteorderId != 0){
				Quoteorder quoteInfo = quoteService.getQuoteInfoByKey(quoteorderId);
				Integer status = null;
				Map<String, Object> data = new HashMap<String, Object>();
				if(quoteInfo.getValidStatus() == 1){
					status = 1;
					data.put("quoteorderId", quoteorderId);
				}else{
					status = 0;
				}
				logger.info("================报价单完成节点结束===taskId:"+taskId+"===quoteorderId:"+quoteorderId+"===");
				return new ResultInfo(0, "操作成功",status,data);
			}else{
				return new ResultInfo(0, "操作成功");
			}
		} catch (Exception e) {
			logger.error("quote complementTask:", e);
			return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
		}

	}
	/**
	 *
	 * <b>Description:</b><br> 打印报价单
	 * @param request
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> scott
	 * <br><b>Date:</b> 2017年12月13日 上午9:12:08
	 */
	@RequestMapping(value = "/printOrder")
	@NoNeedAccessAuthorization
	public ModelAndView printOrder(HttpServletRequest request,Quoteorder quote,HttpSession hs) {

		CurrentUser currentUser = CurrentUser.getCurrentUser();
		ModelAndView mv = new ModelAndView();
		quote.setCompanyId(currentUser.getCompanyId());
		QuoteorderVo quoteVo = quoteService.getPrintInfo(quote);
		//归属人员信息
		UserDetail detail = userDetailMapper.getUserDetail(quoteVo.getUserId());
		String username = userService.getUserById(quoteVo.getUserId()).getUsername();

		User user = userService.getUserByTraderId(quoteVo.getTraderId(), ErpConst.ONE);
		UserDetail detailUser = userDetailMapper.getUserDetail(user.getUserId());

		//报价单下的产品
		Map<String,Object> map = quoteService.getQuoteGoodsByQuoteId(quote.getQuoteorderId(),user.getCompanyId(),hs,null,quote.getTraderId());
		mv.addObject("quoteGoodsList", (List<QuoteorderGoods>)map.get("quoteGoodsList"));

		Long currTime = DateUtil.sysTimeMillis();
		mv.addObject("currTime",DateUtil.convertString(currTime, "YYYY-MM-dd "));

		// 发票类型
		List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(428);
		mv.addObject("invoiceTypes", invoiceTypes);

		//运费类型
		List<SysOptionDefinition> yfTypes = getSysOptionDefinitionList(469);
		mv.addObject("yfTypes", yfTypes);

		//获取公司信息
		Company company = companyService.getCompanyByCompangId(user.getCompanyId());
		mv.addObject("company", company);
		String print = request.getParameter("print");
		String chapter = request.getParameter("chapter");

		//报价单归属部门
		Integer orgId = quoteVo.getOrgId();
		Integer belongOrg = analysisOrgBelong(orgId);

		mv.addObject("belongOrg", belongOrg);
		List<QuoteorderGoods> goodsList = (List<QuoteorderGoods>) map.get("quoteGoodsList");
		mv.addObject("totalAmount", goodsList.stream().filter(g -> ErpConst.ZERO.equals(g.getIsDelete()))
				.map(g -> g.getPrice().multiply(new BigDecimal(g.getNum()))).reduce(BigDecimal.ZERO,BigDecimal::add));
		mv.addObject("detail", detail);
		mv.addObject("username", username);
		mv.addObject("detailUser", detailUser);
		mv.addObject("quoteVo", quoteVo);
		mv.addObject("print", StringUtil.isNotBlank(print));
		mv.addObject("haveChapter", StringUtil.isEmpty(chapter));
		mv.setViewName("order/quote/bjOrderPrint");
		return mv;
	}

	private Integer analysisOrgBelong(Integer orgId) {
		List<Organization> allOrganizationOfCompany = organizationMapper.getAllOrganization();
		List<Integer> belongOrgList = new ArrayList<>();
		getBelongOrg(allOrganizationOfCompany,belongOrgList,orgId);

		if (belongOrgList.contains(38)){
			return 1;
		} else if (belongOrgList.contains(36)){
			return 2;
		} else if (belongOrgList.contains(39)){
			return 3;
		} else {
			return 1;
		}

	}

	private void getBelongOrg(List<Organization> allOrganizationOfCompany, List<Integer> belongOrgList, Integer orgId) {
		if(ObjectUtils.allNotNull(orgId)) {
			belongOrgList.add(orgId);
		}
		if(CollectionUtils.isNotEmpty(allOrganizationOfCompany)){
			Optional<Organization> first = allOrganizationOfCompany.stream().filter(e -> e.getOrgId().equals(orgId)).findFirst();
			if(first.isPresent()){
				Organization organization = first.get();
				getBelongOrg(allOrganizationOfCompany,belongOrgList,organization.getParentId());
			}
		}
	}

	//每3位中间添加逗号的格式化显示
	public static String getCommaFormat(BigDecimal value){
		return getFormat(",###.##",value);
	}

	//自定义数字格式方法
	public static String getFormat(String style,BigDecimal value){
		DecimalFormat df = new DecimalFormat();
		df.applyPattern(style);// 将格式应用于格式化器
		return df.format(value.doubleValue());
	}


	@RequestMapping(method = RequestMethod.GET, value = "/consult/managerPage")
	public ModelAndView consultManager(@RequestParam Integer consultRelatedId, @RequestParam Integer consultType){

		ModelAndView mv = new ModelAndView("order/quote/consult_manager");
		mv.addObject("consultRelatedId",consultRelatedId);
		mv.addObject("consultType",consultType);
		return mv;
	}


	/**
	 * 发起咨询供应链
	 * @param consultRelatedId 咨询相关id
	 * @param consultType 咨询类型，1订单，2报价单
	 * @return
	 */
	@RequestMapping(method = RequestMethod.GET, value = "/consult/supplyPage")
	public ModelAndView consultSupplyChain(@RequestParam Integer consultRelatedId, @RequestParam Integer consultType) {
		ModelAndView mv = new ModelAndView("order/quote/consult_supply");
		List<ConsultGooodsVo> consultGoodsList = quoteService.getConsultGoodsInfoBySaleorderOrQuoteorder(consultRelatedId, consultType);
		Quoteorder quoteInfo = quoteService.getQuoteInfoByKey(consultRelatedId);

		Integer isNeedReport = getIsNeedReport(consultGoodsList);
		mv.addObject("isNeedReport", isNeedReport);
		mv.addObject("consultGoodsList", consultGoodsList);
		mv.addObject("quoteInfo", quoteInfo);
		mv.addObject("consultRelatedId",consultRelatedId);
		mv.addObject("consultType",consultType);
		return mv;
	}

	/**
	 * 设置是否需要报备信息
	 * @param consultGoodsList
	 * @return
	 */
	private Integer getIsNeedReport(List<ConsultGooodsVo> consultGoodsList) {
		Integer isNeedReport = 0;
		if (CollectionUtils.isNotEmpty(consultGoodsList)){
			for (ConsultGooodsVo consultGooodsVo : consultGoodsList) {
				if (consultGooodsVo.getGoodsId().equals(0)){
					continue;
				}
				CoreSkuGenerate coreSkuGenerate = goodsService.getSkuAuthotizationInfoBySku(consultGooodsVo.getGoodsId().longValue());
				if (coreSkuGenerate.getIsNeedReport() != null && coreSkuGenerate.getIsNeedReport().equals(1)){
					isNeedReport = coreSkuGenerate.getIsNeedReport();
					break;
				}
			}
		}
		return isNeedReport;
	}


	@ResponseBody
	@RequestMapping(method = RequestMethod.POST, value = "/consult/save")
	public ResultInfo<?> saveQuoteorderConsult(@RequestBody QuoteorderConsultContentVo quoteorderConsultContentVo, HttpSession session){
		User user = (User) session.getAttribute(ErpConst.CURR_USER);

		quoteService.saveQuoteorderConsult(quoteorderConsultContentVo,user.getUserId());
		ResultInfo resultInfo = new ResultInfo();
		resultInfo.setMessage("咨询成功");
		resultInfo.setCode(0);
		return resultInfo;
	}



	@RequestMapping(method = RequestMethod.GET, value = "/consult/assign/replierPage")
	public ModelAndView assignUserOfSkuPage(@RequestParam Integer quoteorderGoodsId, @RequestParam Integer quoteorderId){
		ModelAndView mv = new ModelAndView("/order/quote/assign_consult_replier");
		List<User> assUser = userService.selectAllAssignUser();
		mv.addObject("userList",assUser);
		mv.addObject("quoteorderId",quoteorderId);
		mv.addObject("quoteorderGoodsId",quoteorderGoodsId);
		return mv;
	}

	/**
	 * 保存分配报价单产品处理人
	 * @param consultReply 分配信息
	 * @return 结果
	 */
	@RequestMapping(method = RequestMethod.POST, value = "/consult/assign/save")
	@ResponseBody
	public ResultInfo<Void> saveAssignUser(@RequestBody QuoteorderConsultReply consultReply){
		ResultInfo<Void> resultInfo = new ResultInfo<>();
		if (consultReply.getQuoteorderId() == null || consultReply.getQuoteorderId() <= 0 || consultReply.getQuoteorderGoodsId() == null || consultReply.getQuoteorderGoodsId() <= 0){
			resultInfo.setCode(-1);
			resultInfo.setMessage("参数错误");
			return resultInfo;
		}
		quoteService.saveAssignConsultReplier(consultReply);
		resultInfo.setCode(0);
		resultInfo.setMessage("分配处理人成功");
		return resultInfo;
	}


	@RequestMapping(value = "/consult/manager/reply", method = RequestMethod.POST)
	@ResponseBody
	public ResultInfo<?> saveManagerReplyOfQuoteorder(@RequestBody QuoteorderConsultContentVo quoteorderConsultContentVo, HttpSession session){
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		ResultInfo resultInfo = new ResultInfo<>();
		if (quoteorderConsultContentVo.getConsultRelatedId() == null || quoteorderConsultContentVo.getConsultType() == null || StringUtils.isBlank(quoteorderConsultContentVo.getConsultContent())){
			resultInfo.setCode(-1);
			resultInfo.setMessage("参数错误");
			return resultInfo;
		} else {
			quoteService.saveManagerReplyOfQuoteorderConsult(quoteorderConsultContentVo,user.getUserId());
		}
		resultInfo.setCode(0);
		resultInfo.setMessage("回复成功");
		return resultInfo;
	}

	/**
	 * 查询授权书申请
	 *
	 * @param applyNum
	 * @param addTime
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/queryForSaleMall")
	@NoNeedAccessAuthorization
	public ResultInfo<Map<String,Object>> getQuoteApplyByApplyNum(String applyNum,Long addTime){

		AuthorizationApply authorizationApply = quoteService.getAutnorizationApplyByNum(applyNum);
		if(authorizationApply == null || !addTime.equals(authorizationApply.getAddTime())){
			ResultInfo<Map<String,Object>> resultInfo  = ResultInfo.error("授权书不存在");
			return resultInfo;
		}else{
			Map<String,Object> map = new HashMap<>();
			map.put("applyNum",authorizationApply.getAuthorizationApplyNum());// SQ2024110034
			map.put("purchaseOrBidding",authorizationApply.getAuthorizedCompany());//被授权人 鹰潭市余江区总医院
			map.put("skuId",authorizationApply.getSkuId());//SKU_ID
			map.put("skuName",authorizationApply.getSkuName()); //SKU_NAME  Aeonmed谊安 麻醉机 Aeon7200基础版（七氟醚）
			map.put("beginDate",authorizationApply.getBeginTime());//授权开始日期 2024-11-04
			map.put("endTime",authorizationApply.getEndTime());//授权结束日期 2024-12-31
			map.put("applyStatus",authorizationApply.getApplyStatus());//状态 1审核中 2驳回 3审核通过 4已取消 5已作废
//			if(authorizationApply.getStandardTemplate() !=null && authorizationApply.getStandardTemplate()==0){//0表示标准模板
//				//StringUtils.isNotBlank(authorizationApply.getNonStandardAuthorizationSignUrl())?authorizationApply.getNonStandardAuthorizationSignUrl():authorizationApply.getNonStandardAuthorizationUrl();
//			}else{
				String fileUrl = authorizationApply.getNonStandardAuthorizationUrl();
				if(StringUtils.isNotBlank(fileUrl) && !fileUrl.startsWith("http")){
					fileUrl = OSS_HTTP+OSS_URL+fileUrl;
				}
				map.put("fileUrl",fileUrl);
//			}

			ResultInfo<Map<String,Object>> resultInfo  = ResultInfo.success(map);
			return resultInfo;
		}
	}

	/**
	 * 授权书申请申请页
	 *
	 * @param quoteorderId
	 * @return
	 * @throws Exception
	 */
	@FormToken(save = true)
	@ResponseBody
	@RequestMapping(value = "/apply")
	public ModelAndView apply(Integer quoteorderId) throws Exception {
		Date date = new Date();
		SimpleDateFormat dateYear = new SimpleDateFormat("yyyy");
		SimpleDateFormat dateMonth = new SimpleDateFormat("MM");
		SimpleDateFormat dateDay = new SimpleDateFormat("dd");
		ModelAndView mv = new ModelAndView();
		mv.addObject("canladerYear", dateYear.format(date));

		List<AuthorizationApply> authorizationApplies=quoteService.getAuthorizationApplyByQuoteId(quoteorderId);
		//已经提交的sku申请
		List<Integer> isNotChanceSku = authorizationApplies.stream().map(AuthorizationApply::getSkuId).collect(Collectors.toList());

		List<AuthorizationStorage> authorizationStorageList = quoteService.getAuthorizationStorageInfoListByQuoteOrderId(quoteorderId);//已草稿的商品
		//将authorizationStorageList转换成skuId为key，authorizationStorageList为value的Map
		Map<Integer, AuthorizationStorage> authorizationStorageMap = new HashMap<>();
		for (AuthorizationStorage authorizationStorage : authorizationStorageList) {
			if (!authorizationStorageMap.containsKey(authorizationStorage.getSkuId())) {
				authorizationStorageMap.put(authorizationStorage.getSkuId(),authorizationStorage);
			}
		}

		Quoteorder quoteorder = quoteService.getQuoteInfoByKey(quoteorderId);
		List<Integer> quoteorderGoodsIds = quoteService.getQuoteGoodsByQuoteOrderId(quoteorderId);//全部的商品
		List<AuthorizationSkuInfoShowDto> productList = new ArrayList();
		if (quoteorderGoodsIds != null && quoteorderGoodsIds.size() >0) {
			List<Map<String, Object>> skuList = vGoodsService.skuTipListInfo(quoteorderGoodsIds);
			if (CollectionUtils.isNotEmpty(skuList)) {
				for(Map<String, Object> map:skuList){
					String skuName =map.get("SHOW_NAME").toString();
					String brandName = map.get("BRAND_NAME").toString();
					String model = map.get("MODEL").toString();
					Integer skuId = Integer.parseInt(map.get("SKU_ID").toString());
					AuthorizationSkuInfoShowDto product = new AuthorizationSkuInfoShowDto();
					product.setSkuId(skuId);
					product.setSkuName(skuName);
					product.setBrandName(brandName);
					product.setSkuModel("");
					product.setCheck(0);
					AuthorizationStorage authorizationStorage = authorizationStorageMap.get(skuId);
					if (authorizationStorage != null) {
						product.setCheck(1);//设置已选中
						product.setProductCompany(authorizationStorage.getProductCompany());
						product.setDistributionsType(authorizationStorage.getDistributionsType());
						product.setNatureOfOperation(authorizationStorage.getNatureOfOperation());
					}
					if (isNotChanceSku.contains(skuId)) {
						product.setCheck(2);//设置为不可选
					}
					productList.add(product);
				}
			}
		}


		//根据前台传过来的授权申请id判断是显示保存的内容还是不显示

		if (authorizationStorageList != null && authorizationStorageList.size()>0) {
			AuthorizationStorage authorizationStorage = authorizationStorageList.get(0);
			mv.addObject("skuName", authorizationStorage.getSkuName());
			mv.addObject("brandName", authorizationStorage.getBrandName());
			mv.addObject("model", authorizationStorage.getSkuModel());
			mv.addObject("skuId", authorizationStorage.getSkuId());
			mv.addObject("authorizedCompany", authorizationStorage.getAuthorizedCompany());
			mv.addObject("authorizationStorage", authorizationStorage);
			mv.addObject("dateYear", authorizationStorage.getApplyYear());
			mv.addObject("dateMonth", authorizationStorage.getApplyMonth());
			mv.addObject("dateDay", authorizationStorage.getApplyDay());
			mv.addObject("nonStandardAuthorizationUrl", authorizationStorage.getNonStandardAuthorizationUrl());
			mv.addObject("nonStandardAuthorizationName", authorizationStorage.getNonStandardAuthorizationName());
			mv.addObject("whetherSign", authorizationStorage.getWhetherSign());
			List<Attachment> attachment = attachmentService.getAttachmentInfoByRelatedIdAndFunctionId(authorizationStorage.getTemporaryStorageId(), AUTHORIZATION_STORAGE_FILE);
			mv.addObject("attachmentList", attachment);
		} else {
			mv.addObject("dateYear", dateYear.format(date));
			mv.addObject("dateMonth", dateMonth.format(date));
			mv.addObject("dateDay", dateDay.format(date));
			if (quoteorderGoodsIds != null && quoteorderGoodsIds.size() == 1) {
				List<Map<String, Object>> skuList = vGoodsService.skuTipListInfo(quoteorderGoodsIds);
				if (CollectionUtils.isNotEmpty(skuList)) {
					String skuName = skuList.get(0).get("SHOW_NAME").toString();
					String brandName = skuList.get(0).get("BRAND_NAME").toString();
					String model = skuList.get(0).get("MODEL").toString();
					Integer skuId = Integer.parseInt(skuList.get(0).get("SKU_ID").toString());
					mv.addObject("skuName", skuName);
					mv.addObject("brandName", brandName);
					mv.addObject("model", model);
					mv.addObject("skuId", skuId);
				}
			}
			mv.addObject("authorizedCompany", quoteorder.getTraderName());
		}
		int maxId = quoteService.getAuthorizationMaxId();
		mv.addObject("productList", JSONObject.toJSONString(productList));
		mv.addObject("maxId", maxId);
		mv.setViewName("order/quote/authorization");
		mv.addObject("domain", domain);
		mv.addObject("quoteorderId", quoteorderId);
		List<BaseCompanyInfoSimpleDto> companyInfoListWithSeq = getCompanyInfoWithSeq();
		mv.addObject("companyInfoList", companyInfoListWithSeq);
		mv.addObject("companyInfoListWithSeq", JSONArray.toJSONString(companyInfoListWithSeq));

		return mv;
	}




	/**
	 *判断用户是否有申请书授权权限
	 *判断按钮状态为申请还是审核还是打印
	 * @param request
	 * @param quoteorderId
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/operationAuthority")
	public ResultInfo<?> operationAuthority(HttpServletRequest request,Integer quoteorderId,String optUserName){
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		Quoteorder quoteInfo = quoteService.getQuoteInfoByKey(quoteorderId);
		ResultInfo resultInfo=new ResultInfo();
		//如果登陆者为null或者报价单信息返回不到直接返回错误
		if (user==null && quoteInfo==null){
			return resultInfo;
		}
		//判断是否含有有效的产品信息
		//	审核中授权书数量
		int authorizationReviewSum=quoteService.getAuthorizationSum(quoteorderId,AUTHORIZATION_REVIEW);
		//  驳回授权书数量
		int authorizationRejectSum=quoteService.getAuthorizationSum(quoteorderId,AUTHORIZATION_REJECT);
		// 审核通过授权书
		int authorizationPassSum=quoteService.getAuthorizationSum(quoteorderId,AUTHORIZATION_PASS);
		int authorizationCancelSum=quoteService.getAuthorizationSum(quoteorderId,AUTHORIZATION_CANCEL);
		int authorizationAbandonSum=quoteService.getAuthorizationSum(quoteorderId,AUTHORIZATION_ABANDON);

		int sum=authorizationReviewSum+authorizationRejectSum+authorizationPassSum+authorizationCancelSum+authorizationAbandonSum;
		resultInfo.setData(sum);
		resultInfo.setMessage("您已提交"+sum+"个授权申请，是否继续申请?");
		resultInfo.setCode(0);
		return resultInfo;
	}

	/**
	 * 保存授权书信息
	 * @param authorizationStorage
	 * @return
	 */
	@MethodLock(className = AuthorizationStorage.class, field = "quoteorderId")
	@FormToken(remove = true)
	@ResponseBody
	@RequestMapping(value = "/authorizationStorage")
	public ResultInfo<?> preservation(HttpServletRequest request, AuthorizationStorage authorizationStorage,
									  @RequestParam(required = false, value = "fileName") String[] fileName,
									  @RequestParam(required = false, value = "fileUri") String[] fileUri,
									  @RequestParam(required=true,value="temporaryStorageIds") String temporaryStorageIds,
									  @RequestParam(required=true,value="draftSkuJson") String draftSkuJson) {
		//判断该授权书申请下有无保存的授权书申请
		try {
			logger.info("授权书保存,{},文件链接{},文件名称{}" + JSON.toJSONString(authorizationStorage), fileUri, fileName);
			User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			Long time = DateUtil.sysTimeMillis();
			List<AuthorizationStorage> authorizationStorageList = quoteService.getAuthorizationStorageInfoListByQuoteOrderId(authorizationStorage.getQuoteorderId());//已草稿的商品

			if(CollectionUtils.isNotEmpty(authorizationStorageList)){//如果原来已有草稿，不能将草稿直接删除，因为可能已经有授权书编号生成过了
				//循环temporaryStorageIds
				for(AuthorizationStorage old:authorizationStorageList){
					quoteService.delAuthorizationStorageById(old.getTemporaryStorageId(), user.getUserId(), time);
					attachmentService.delAuthorizationStorageInfo(old.getTemporaryStorageId(), AUTHORIZATION_STORAGE_FILE);
				}
			}
			//第一步，先获取提交的商品信息
			List<AuthorizationSkuInfo> draftList = JSONArray.parseArray(draftSkuJson,AuthorizationSkuInfo.class);

			for(AuthorizationSkuInfo draftSku:draftList){
				authorizationStorage.setSkuId(draftSku.getSkuId());
				authorizationStorage.setSkuName(draftSku.getSkuName());
				authorizationStorage.setBrandName(draftSku.getBrandName());
				authorizationStorage.setProductCompany(draftSku.getProductCompany());
				authorizationStorage.setSkuModel(draftSku.getSkuModel());
				authorizationStorage.setDistributionsType(draftSku.getDistributionsType());
				authorizationStorage.setNatureOfOperation(draftSku.getNatureOfOperation());

				// 确保公章类型字段有值，如果为空则设置默认值为贝登公章(1)
				if (authorizationStorage.getSealType() == null) {
					authorizationStorage.setSealType(1);
				}

				authorizationStorage.setAddTime(time);
				authorizationStorage.setModTime(time);
				authorizationStorage.setCreator(user.getUserId());
				authorizationStorage.setUpdator(user.getUserId());

				quoteService.insertAuthorizationStorageInfo(authorizationStorage);

				//保存上传的文件信息
				attachmentService.saveAuthorization(fileName, fileUri, domain, authorizationStorage.getTemporaryStorageId(), time, AUTHORIZATION_STORAGE_FILE);
			}

			if(StringUtils.isNotBlank(temporaryStorageIds)){//如果原来已有草稿，直接将草稿全部删除掉
				//循环temporaryStorageIds
				String[] ids=temporaryStorageIds.split(",");
				for(String id:ids){
					quoteService.delAuthorizationStorageById(Integer.parseInt(id), user.getUserId(), time);
					attachmentService.delAuthorizationStorageInfo(Integer.parseInt(id), AUTHORIZATION_STORAGE_FILE);
				}
			}

		} catch (Exception e) {
			logger.error("保存授权书申请出错", e);
		}
		return new ResultInfo<>();
	}

	public String getSq(String yearAndMonth){
		List<String> sqNum = quoteService.getSqNumByYearAndMonth(yearAndMonth);
		if (sqNum.size() == 0) {
			return "SQ" + yearAndMonth + "0001";
		} else {
			List<Integer> max = new ArrayList<>();
			for (String sq : sqNum) {
				max.add(Integer.parseInt(sq.substring(sq.length() - 4)));
			}
			Integer ymMax = Collections.max(max);
			ymMax += 1;
			int numberOfDigits = String.valueOf(ymMax).length();
			String sqNo = "";
			switch (numberOfDigits) {
				case 1:
					sqNo = "000" + ymMax;
					break;
				case 2:
					sqNo = "00" + ymMax;
					break;
				case 3:
					sqNo = "0" + ymMax;
					break;
				case 4:
					sqNo += ymMax;
					break;
				default:
					sqNo = "大于9999";
					break;
			}
			return ("SQ" + yearAndMonth + sqNo);
		}
	}


	/**
	 * 授权书申请提交（防止并发）
	 *
	 * @param authorizationApply
	 * @return
	 */
	@Transactional
	@FormToken(remove = true)
	@MethodLock(className = Integer.class)
	@ResponseBody
	@RequestMapping(value = "/authorizationApply")
	public ResultInfo<?> authorizationApply(HttpServletRequest request,
											AuthorizationApply authorizationApply,
											Integer temporaryStorageId,
											@RequestParam(required = false, value = "fileName") String[] fileName,
											@RequestParam(required = false, value = "fileUri") String[] fileUri,
											@MethodLockParam Integer maxId,
											@RequestParam(required=false,value="draftSkuJson") String draftSkuJson

													) {
		//判断该授权书申请是新提交还是驳回后继续提交，一个授权单下的一个sku只能被有效申请一次
		try {
			logger.info("授权书提交{},暂存id{},文件链接{},文件名称{}", JSON.toJSONString(authorizationApply), temporaryStorageId, fileUri, fileName);
			User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

			Quoteorder targetQuoteorder = quoteService.getQuoteOrderInfoById(authorizationApply.getQuoteorderId());

			if (targetQuoteorder == null) {
				logger.info("报价单不存在" + authorizationApply.getQuoteorderId());
			}
			User optUserInfo = userService.getUserByTraderId(targetQuoteorder.getTraderId(), 1);
			if (optUserInfo == null) {
				optUserInfo = user;
			}


			Long time = DateUtil.sysTimeMillis();
			authorizationApply.setApplyPerson(optUserInfo.getUsername());

			if (authorizationApply.getAuthorizationApplyNum() == null) {
				//根据年月生成递增的数
				Date date = new Date();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
				String yearAndMonth = sdf.format(date);
				//第一步，先获取提交的商品信息
				List<AuthorizationSkuInfo> draftList = JSONArray.parseArray(draftSkuJson,AuthorizationSkuInfo.class);

				for(AuthorizationSkuInfo draftSku:draftList) {
					authorizationApply.setSkuId(draftSku.getSkuId());
					authorizationApply.setSkuName(draftSku.getSkuName());
					authorizationApply.setBrandName(draftSku.getBrandName());
					authorizationApply.setProductCompany(draftSku.getProductCompany());
					authorizationApply.setSkuModel(draftSku.getSkuModel());
					authorizationApply.setDistributionsType(draftSku.getDistributionsType());
					authorizationApply.setNatureOfOperation(draftSku.getNatureOfOperation());

					String sq = getSq(yearAndMonth);
					authorizationApply.setAuthorizationApplyNum(sq);
					authorizationApply.setYearAndMonth(yearAndMonth);
					authorizationApply.setAddTime(time);
					authorizationApply.setModTime(time);
					authorizationApply.setCreator(user.getUserId());
					authorizationApply.setUpdator(user.getUserId());
					authorizationApply.setApplyStatus(AUTHORIZATION_REVIEW);

					// 确保公章类型字段有值，如果为空则设置默认值为贝登公章(1)
					if (authorizationApply.getSealType() == null) {
						authorizationApply.setSealType(1);
					}
					quoteService.insertAuthorizationApplyInfo(authorizationApply);
					attachmentService.saveAuthorization(fileName, fileUri, domain, authorizationApply.getAuthorizationApplyId(), time, AUTHORIZATION_APPLY_FILE);

					this.startProcessInstance(request, authorizationApply, optUserInfo, user);
				}
				List<AuthorizationStorage> authorizationStorageList = quoteService.getAuthorizationStorageInfoListByQuoteOrderId(authorizationApply.getQuoteorderId());//已草稿的商品
				for(AuthorizationStorage storage:authorizationStorageList){//提交后，要把草稿都删除掉
					quoteService.delAuthorizationStorageById(storage.getTemporaryStorageId(), user.getUserId(), time);
				}

			} else {//场景为被驳回，就只存在一条的情况。
				AuthorizationApply apply = quoteService.getAuthorizationApplyByNum(authorizationApply.getAuthorizationApplyNum());
				authorizationApply.setAuthorizationApplyId(apply.getAuthorizationApplyId());
				authorizationApply.setModTime(time);
				authorizationApply.setUpdator(user.getUserId());
				authorizationApply.setApplyStatus(AUTHORIZATION_REVIEW);
				quoteService.updateAuthorizationApplyInfo(authorizationApply);
				//删除保存的信息
				attachmentService.delAuthorizationStorageInfo(authorizationApply.getAuthorizationApplyId(), AUTHORIZATION_APPLY_FILE);
				// 再上传新的保存信息
				attachmentService.saveAuthorization(fileName, fileUri, domain, authorizationApply.getAuthorizationApplyId(), time, AUTHORIZATION_APPLY_FILE);
				//将暂存表状态改为已删除
				if (temporaryStorageId != null) {
					quoteService.delAuthorizationStorageById(temporaryStorageId, user.getUserId(), time);
				}
				this.startProcessInstance(request, authorizationApply, optUserInfo, user);
				addTrackApplyAuthorization(CurrentUser.getCurrentUser(),authorizationApply.getAuthorizationApplyNum(),targetQuoteorder.getTraderId());
			}
		} catch (Exception e) {
			logger.error("提交授权申请失败+++++++++++++++++++++++++++", e);
			//手动开启事务，可能与注解产生冲突
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return ResultInfo.error(false);
		}
		return ResultInfo.success(true);
	}

	private void addTrackApplyAuthorization(CurrentUser currentUser,String authorizationNo,Integer traderId) {
		EventTrackingEnum eventTrackingEnum = EventTrackingEnum.SALE_APPLY_AUTHORIZATION;
		try {
			User user = authService.getUserById(currentUser.getId());
			TrackParamsData trackParamsData = new TrackParamsData();
			Map<String, Object> trackParams = new HashMap<>();
			trackParams.put("traderId",traderId);
			trackParams.put("track_user", user);
			trackParams.put("authorizationNo",authorizationNo);
			TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(eventTrackingEnum);
			trackParamsData.setEventTrackingEnum(eventTrackingEnum);
			trackParamsData.setTrackParams(trackParams);
			trackParamsData.setTrackResult(ResultInfo.success());
			trackStrategy.track(trackParamsData);
		}catch(Exception e) {
			log.error("埋点：{}，失败，不影响正常业务",eventTrackingEnum.getArchivedName(),e);
		}
	}



	/**
	 * 授权书申请审核流程
	 */
	@ResponseBody
	@RequestMapping(value = "next")
	public ResultInfo<?> next(HttpServletRequest request,Integer authorizationApplyId, Boolean pass,String suggestion){
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();
		Map<String, Object> taskVaribles = new HashMap<>();
		taskVaribles.put("pass", pass);


		// 获取附件表中的url,调用是否可签章方法
		AuthorizationApply apply = quoteService.getAuthorizationApplyByKeyId(authorizationApplyId);
		// 默认可以签章
		boolean authorization = true;
		// 当非标模板
		if(apply.getStandardTemplate() == 1){
			authorization = Convert.toBool(apply.getWhetherSign(), false);
		}
		taskVaribles.put("authorization", authorization);

		try {
			// 获取当前活动节点
			Task taskInfo = processEngine.getTaskService()
					.createTaskQuery()
					.processInstanceBusinessKey("authorizationApply_"+authorizationApplyId)
					.singleResult();

			String taskName = taskInfo.getName();

			if ("授权专员".equals(taskName) && pass) {
				// 不可电子签章 非标准模板，增加溯源码
				if (apply.getStandardTemplate() == 1 && !authorization) {
					// 增加溯源码
					if (StrUtil.isNotEmpty(apply.getNonStandardAuthorizationUrl())) {
						String url = authorizationApplyApiService.createTraceCode(ossHttp + ossUrl + apply.getNonStandardAuthorizationUrl(), authorizationApplyId);
						if (StrUtil.isNotEmpty(url)) {
							String uri = ossHttp + ossUrl;
							if (url.startsWith(uri)) {
								url = url.substring(uri.length());
							}
							apply.setNonStandardAuthorizationUrl(url);
							quoteService.updateAuthorizationApplyInfo(apply);
						}
					}
				}

				if (authorization) {
					// 增加溯源码 调用电子签章
					authorizationApplyApiService.electronicSign(authorizationApplyId);
				}
			}

			//完成当前任务
			actionProcdefService.complementTask(request, taskInfo.getId(), suggestion, user.getUsername(), taskVaribles);

			if ("产品归属人".equals(taskName) && pass) {

				// 所有授权专员
				List<UserDto> roleUserDtoList = userApiService.getUserByRoleId("授权专员");
				roleUserDtoList = roleUserDtoList.stream().distinct().collect(Collectors.toList());
				// 获取当前报价单归属销售信息
				List<Integer> byQuoteorderIdGetUserId = quoteService.findByQuoteorderIdGetUserId(apply.getQuoteorderId());
				UserDto userById = userApiService.getUserByIdGetMainOrg(CollUtil.getFirst(byQuoteorderIdGetUserId));
				// 当归属销售一级部门等于授权专员中的一级部门，则该授权专员是候选人
				if (CollUtil.isNotEmpty(roleUserDtoList) && CollUtil.isNotEmpty(byQuoteorderIdGetUserId)) {
					// 筛选并添加候选授权专员
					List<UserDto> userList = roleUserDtoList.stream()
							.filter(userDto -> userDto.getMainOrgId().equals(userById.getMainOrgId())).collect(Collectors.toList());

					Task nextTask = processEngine.getTaskService()
							.createTaskQuery()
							.processInstanceBusinessKey("authorizationApply_" + authorizationApplyId)
							.singleResult();

					// 当没有授权专员时，终止任务
					if (CollUtil.isEmpty(userList)) {
						taskVaribles.put("pass", false);
						actionProcdefService.complementTask(request, nextTask.getId(), userById.getMainOrgName() + "部门没有设置授权专员", user.getUsername(), taskVaribles);
					} else {
						userList.forEach(userDto -> processEngine.getTaskService().addCandidateUser(nextTask.getId(), userDto.getUsername()));

						Integer messageTemplateId = 114;
						Map<String, String> map = new HashMap<>();
						map.put("AuthorizationNum", apply.getAuthorizationApplyNum());
						String url = "./order/quote/authorizationExamine.do?authorizationApplyId=" + apply.getAuthorizationApplyId();
						List<Integer> varifyUserList = userList.stream().map(UserDto::getUserId).collect(Collectors.toList());
						MessageUtil.sendMessage(messageTemplateId, varifyUserList, map, url);
					}
				}
			}

			Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "authorizationApply_" + authorizationApplyId);
			String preAssignee=null;
			if(historicInfo.get("startUser")!=null) {
				preAssignee = historicInfo.get("startUser").toString();
			}

			if (historicInfo.get("taskInfo") != null) {

				Task task = (Task) historicInfo.get("taskInfo");

				//获取审核人候选组
				Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
				//获取审核人候选组
				List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(task.getId());

				if(CollectionUtils.isNotEmpty(candidateUserList)){
					List<String> userNameList = candidateUserList.stream().map(IdentityLink::getUserId).collect(Collectors.toList());
					String verifyUsers=org.apache.commons.lang.StringUtils.join(userNameList,",");
					quoteService.updateAuthorizationApplyReviewer(authorizationApplyId,verifyUsers);
				}


			}

			if ("通过".equals(historicInfo.get("endStatus").toString())) {
				quoteService.updateAuthorizationApplyModTimeAndStatus(authorizationApplyId, user.getUserId(), time, AUTHORIZATION_PASS);
				quoteService.updateAuthorizationApplyReviewer(authorizationApplyId, null);
			}
			if ("驳回".equals(historicInfo.get("endStatus").toString())){
				quoteService.updateAuthorizationApplyModTimeAndStatus(authorizationApplyId,user.getUserId(),time,AUTHORIZATION_REJECT);
				quoteService.updateAuthorizationApplyReviewer(authorizationApplyId,preAssignee);
			}
		} catch (Exception e) {
			logger.error("授权书申请审核流程异常",e);
			throw new RuntimeException(e);
		}

		return new ResultInfo<>();
	}

	/**
	 * 查询可用的sku
	 *
	 * @param quoteorderId
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "selSkuByQuote")
	public ModelAndView getSkuByQuoteId(Integer quoteorderId){
		/*ResultInfo<List<AuthorizationSkuInfo>> resultInfo=new ResultInfo();*/
		//该授权书下所有有效的sku商品
		List<Integer> isChanceSku=quoteService.getQuoteGoodsByQuoteOrderId(quoteorderId);
		List<AuthorizationApply> authorizationApplies=quoteService.getAuthorizationApplyByQuoteId(quoteorderId);
		//已经提交的sku申请
		List<Integer> isNotChanceSku = authorizationApplies.stream().map(AuthorizationApply::getSkuId).collect(Collectors.toList());

		isChanceSku.removeAll(isNotChanceSku);

		List<Map<String, Object>> notChanceMap = vGoodsService.skuTipListInfo(isNotChanceSku);
		List<Map<String, Object>> chanceMap = vGoodsService.skuTipListInfo(isChanceSku);

		List<AuthorizationSkuInfo> authorizationSkuInfos=new ArrayList<>();


		for (int i = 0; i <chanceMap.size() ; i++) {
			AuthorizationSkuInfo authorizationSkuInfo=new AuthorizationSkuInfo();
			authorizationSkuInfo.setSkuId(Integer.parseInt(chanceMap.get(i).get("SKU_ID").toString()));
			authorizationSkuInfo.setSkuName(chanceMap.get(i).get("SHOW_NAME").toString());
			authorizationSkuInfo.setBrandName(chanceMap.get(i).get("BRAND_NAME").toString());
			authorizationSkuInfo.setSkuModel(chanceMap.get(i).get("MODEL").toString());
			authorizationSkuInfo.setIsChance(0);
			authorizationSkuInfos.add(authorizationSkuInfo);
		}

		for (int i = 0; i <notChanceMap.size() ; i++) {
			AuthorizationSkuInfo authorizationSkuInfo=new AuthorizationSkuInfo();
			authorizationSkuInfo.setSkuId(Integer.parseInt(notChanceMap.get(i).get("SKU_ID").toString()));
			authorizationSkuInfo.setSkuName(notChanceMap.get(i).get("SHOW_NAME").toString());
			authorizationSkuInfo.setBrandName(notChanceMap.get(i).get("BRAND_NAME").toString());
			authorizationSkuInfo.setSkuModel(notChanceMap.get(i).get("MODEL").toString());
			authorizationSkuInfo.setIsChance(1);
			authorizationSkuInfos.add(authorizationSkuInfo);
		}


		ModelAndView mv=new ModelAndView();
		mv.setViewName("order/quote/chanceSku");
		mv.addObject("authorizationSkuInfos",authorizationSkuInfos);
		return mv;
	}

	/**
	 * 校验该授权书能否 被提交
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "checkAuthorizationApply")
	public ResultInfo<?> checkAuthorizationApply(AuthorizationApply authorizationApply, @RequestParam(required = false, value = "draftSkuJson") String draftSkuJson) {
		ResultInfo<Boolean> resultInfo = new ResultInfo<>();

		// 如果采购项目编号和名称都为空，直接返回成功
		if (StrUtil.isEmpty(authorizationApply.getPurchaseProjectNum()) && StrUtil.isEmpty(authorizationApply.getPurchaseProjectName())) {
			resultInfo.setData(true);
			return resultInfo;
		}

		List<AuthorizationApply> authorizationApplyList = new ArrayList<>();
		List<String> errorMessages = new ArrayList<>();
		if(StringUtils.isNotBlank(draftSkuJson)){
			List<AuthorizationSkuInfo> draftList = JSONArray.parseArray(draftSkuJson, AuthorizationSkuInfo.class);
			for (AuthorizationSkuInfo draftSku : draftList) {
				AuthorizationApply authorization = new AuthorizationApply();
				authorization.setSkuId(draftSku.getSkuId());
				authorization.setSkuName(draftSku.getSkuName());
				authorization.setBrandName(draftSku.getBrandName());
				authorization.setProductCompany(draftSku.getProductCompany());
				authorization.setSkuModel(draftSku.getSkuModel());
				authorization.setDistributionsType(draftSku.getDistributionsType());
				authorization.setNatureOfOperation(draftSku.getNatureOfOperation());

				authorization.setQuoteorderId(authorizationApply.getQuoteorderId());
				authorization.setPurchaseProjectNum(authorizationApply.getPurchaseProjectNum());
				authorization.setPurchaseProjectName(authorizationApply.getPurchaseProjectName());
				authorization.setBeginTime(authorizationApply.getBeginTime());
				authorization.setEndTime(authorizationApply.getEndTime());
				authorizationApplyList.add(authorization);
			}
		}else{
			authorizationApplyList.add(authorizationApply);
		}



		// 循环检查每个授权申请
		for (AuthorizationApply dto : authorizationApplyList) {
			// 查询该授权书下的商品有无有效授权申请
			AuthorizationApply apply = quoteService.getAuthorizationApplyInfoBySkuIdAndQuoteId(dto.getQuoteorderId(), dto.getSkuId(), dto.getAuthorizationApplyId());
			if (apply != null) {
				errorMessages.add(apply.getSkuName() + "已提交过审核，请在授权书详情页査看");
				continue;
			}

			// 判断有没有被其他销售申请
			AuthorizationApply applyInfo = quoteService.getAuthorizationIsRepeat(dto);
			if (applyInfo != null) {
				Date date = new Date(applyInfo.getAddTime());
				SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");

				String pNum = (applyInfo.getPurchaseProjectNum() == null) ? "" : applyInfo.getPurchaseProjectNum();
				User user = userService.getUserById(applyInfo.getCreator());
				if (StringUtils.isEmpty(applyInfo.getPurchaseProjectName()) && StringUtils.isEmpty(applyInfo.getPurchaseProjectNum())) {
					errorMessages.add(applyInfo.getSkuName() + "已有" + user.getUsername() + "于" + df.format(date) + "提交申请；");
				} else if (StringUtils.isEmpty(applyInfo.getPurchaseProjectName())) {
					errorMessages.add("项目编号" + pNum + "," + applyInfo.getSkuName() + "，已有" + user.getUsername() + "于" + df.format(date) + "提交申请；");
				} else if (StringUtils.isEmpty(applyInfo.getPurchaseProjectNum())) {
					errorMessages.add(applyInfo.getPurchaseProjectName() + "项目," + applyInfo.getSkuName() + ",已有" + user.getUsername() + "于" + df.format(date) + "提交申请；");
				} else {
					errorMessages.add(applyInfo.getPurchaseProjectName() + "项目，项目编号" + pNum + "," + applyInfo.getSkuName() + "，已有" + user.getUsername() + "于" + df.format(date) + "提交申请；");
				}
			}
		}

		// 如果有错误信息，返回错误信息；否则返回成功
		if (!errorMessages.isEmpty()) {
			resultInfo.setData(false);
			resultInfo.setMessage(String.join("<br>", errorMessages));
		} else {
			resultInfo.setData(true);
		}

		return resultInfo;
	}


	/**
	 * 开始审核流程
	 * @param request
	 * @param authorizationApplyId
	 * @throws Exception
	 */
	@FormToken(save=true)
	@RequestMapping(value = "authorizationExamine")
	public ModelAndView examine(String authorizationApplyId,HttpServletRequest request){
		CurrentUser currentUser = CurrentUser.getCurrentUser();
		Date date = new Date();
		SimpleDateFormat dateYear = new SimpleDateFormat("yyyy");
		SimpleDateFormat dateMonth = new SimpleDateFormat("MM");
		SimpleDateFormat dateDay = new SimpleDateFormat("dd");

		ModelAndView mv=new ModelAndView();
		mv.addObject("dateYear",dateYear.format(date));
		mv.addObject("dateMonth",dateMonth.format(date));
		mv.addObject("dateDay",dateDay.format(date));
		mv.addObject("domain", domain);
		List<BaseCompanyInfoSimpleDto> companyInfoListWithSeq = getCompanyInfoWithSeq();
		mv.addObject("companyInfoList", companyInfoListWithSeq);
		mv.setViewName("order/quote/authorization_view");
		AuthorizationApply apply=quoteService.getAuthorizationApplyByKeyId(Integer.parseInt(authorizationApplyId));

		// 确保公章类型字段有值，如果为空则设置默认值为贝登公章(1)
		if (apply.getSealType() == null) {
			apply.setSealType(1);
		}

		mv.addObject("authorizationApply",apply);
		User user=userService.getUserById(apply.getUpdator());
		mv.addObject("whetherApplyUser",currentUser.getId().equals(apply.getCreator()));
		mv.addObject("userName",user.getUsername());
		mv.addObject("comment",apply.getComment());
		List<Attachment> attachments=attachmentService.getAttachmentInfoByRelatedIdAndFunctionId(Integer.parseInt(authorizationApplyId),AUTHORIZATION_ABANDON_FILE);
		mv.addObject("attachments",attachments);


		List<Attachment> attachmentList=attachmentService.getAttachmentInfoByRelatedIdAndFunctionId(Integer.parseInt(authorizationApplyId),AUTHORIZATION_APPLY_FILE);
		mv.addObject("attachmentList",attachmentList);
		//显示审核流程
		Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
				"authorizationApply_" + authorizationApplyId);


		mv.addObject("historicInfo", historicInfo);
		mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
		mv.addObject("commentMap", historicInfo.get("commentMap"));
		if (historicInfo.get("endStatus")!=null){
			mv.addObject("endSt",historicInfo.get("endStatus").toString());
		}
		//判断是不是流程走完通过且当前登陆者为财务印章专员
		List<String> userListByRole=userService.getUserListByRole("财务印章专员");
		User userAss = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		if (userAss!=null && CollectionUtils.isNotEmpty(userListByRole) && userListByRole.contains(userAss.getUsername()) && "通过".equals(historicInfo.get("endStatus"))){
			mv.addObject("isPrinting",true);
		}

		if (historicInfo.get("taskInfo") != null) {

			Task taskInfo = (Task) historicInfo.get("taskInfo");
			mv.addObject("taskId",taskInfo.getId());
			mv.addObject("taskInfo", taskInfo);

			//获取审核人候选组
			Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
			mv.addObject("candidateUserMap",candidateUserMap);

			//获取审核人候选组
			List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

			if(CollectionUtils.isNotEmpty(candidateUserList)){
				List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());
				mv.addObject("verifyUsers", org.apache.commons.lang.StringUtils.join(userNameList,","));
			}
		}


		return mv;
	}

	/**
	 * 授权书详情页
	 * @param request
	 * @throws Exception
	 */
	@FormToken(save=true)
	@RequestMapping(value = "authorizationView")
	public ModelAndView authorizationView(Integer quoteorderId, Integer authorizationApplyId, HttpServletRequest request) {
		logger.info("进入授权书申请详情页面,报价单id{},授权申请id{}", quoteorderId, authorizationApplyId);
		ModelAndView mv = new ModelAndView();
		try {
			User userAss = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			if (userAss != null) {
				mv.addObject("cuUserName", userAss.getUsername());
			}
			Date date = new Date();
			SimpleDateFormat dateYear = new SimpleDateFormat("yyyy");
			SimpleDateFormat dateMonth = new SimpleDateFormat("MM");
			SimpleDateFormat dateDay = new SimpleDateFormat("dd");
			int maxId = quoteService.getAuthorizationMaxId();


			// 根据报价获取所有的授权书申请
			List<AuthorizationApply> authorizationApplies = quoteService.getAuthorizationApplyListByQuoteId(quoteorderId);
			mv.addObject("maxId", maxId);
			mv.addObject("dateYear", dateYear.format(date));
			mv.addObject("dateMonth", dateMonth.format(date));
			mv.addObject("dateDay", dateDay.format(date));
			mv.addObject("authorizationApplies", authorizationApplies);
			mv.addObject("domain", domain);
			mv.addObject("whetherApplyUser",userAss.getUserId().equals(CollUtil.getFirst(authorizationApplies).getCreator()));

			// 获取暂存记录
			AuthorizationStorage authorizationStorage = quoteService.getTemporaryStorageByNum(CollUtil.getFirst(authorizationApplies).getAuthorizationApplyNum());

			AuthorizationApply authorizationApply = null;
			AuthorizationStorage authorizationStorage2 = null;
			if (authorizationApplyId != null) {
				authorizationApply = quoteService.getAuthorizationApplyByKeyId(authorizationApplyId);
				mv.addObject("whetherApplyUser",userAss.getUserId().equals(authorizationApply.getCreator()));
				authorizationStorage2 = quoteService.getTemporaryStorageByNum(authorizationApply.getAuthorizationApplyNum());
			}
			Integer authorizationId;
			//  第一次跳转驳回申请的保存页面
			if (CollectionUtils.isNotEmpty(authorizationApplies)
					&& authorizationApplies.get(0).getApplyStatus() == 2
					&& authorizationApplyId == null
					&& authorizationStorage != null) {
				List<Attachment> attachmentList = attachmentService.getAttachmentInfoByRelatedIdAndFunctionId(authorizationStorage.getTemporaryStorageId(), AUTHORIZATION_STORAGE_FILE);
				mv.addObject("attachmentList", attachmentList);
				authorizationId = authorizationApplies.get(0).getAuthorizationApplyId();


				Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
						"authorizationApply_" + authorizationId);


				mv.addObject("historicInfo", historicInfo);
				mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
				mv.addObject("commentMap", historicInfo.get("commentMap"));

				if (historicInfo.get("taskInfo") != null) {

					Task taskInfo = (Task) historicInfo.get("taskInfo");
					mv.addObject("taskId", taskInfo.getId());
					mv.addObject("taskInfo", taskInfo);

					//获取审核人候选组
					Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
					mv.addObject("candidateUserMap", candidateUserMap);

					//获取审核人候选组
					List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

					if (CollectionUtils.isNotEmpty(candidateUserList)) {

						List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

						mv.addObject("verifyUsers", org.apache.commons.lang.StringUtils.join(userNameList, ","));
					}
				}

				mv.addObject("authorizationStorageId", authorizationStorage.getTemporaryStorageId());
				mv.addObject("authorizationApply", authorizationStorage);
				mv.addObject("nonStandardAuthorizationUrl", authorizationStorage.getNonStandardAuthorizationUrl());
				mv.addObject("nonStandardAuthorizationName", authorizationStorage.getNonStandardAuthorizationName());
				mv.addObject("whetherSign", authorizationStorage.getWhetherSign());
				mv.addObject("authorizationApplyId", authorizationId);
				List<BaseCompanyInfoSimpleDto> companyInfoListWithSeq = getCompanyInfoWithSeq();
				mv.addObject("companyInfoList", companyInfoListWithSeq);
				mv.addObject("companyInfoListWithSeq", JSONArray.toJSONString(companyInfoListWithSeq));

				mv.setViewName("order/quote/authorization_view_bh");
				return mv;
			}

			//  非第一次跳转驳回申请的保存页面
			if (authorizationApplyId != null
					&& authorizationApply.getApplyStatus() == 2
					&& authorizationStorage2 != null) {
				List<Attachment> attachmentList = attachmentService.getAttachmentInfoByRelatedIdAndFunctionId(authorizationStorage2.getTemporaryStorageId(), AUTHORIZATION_STORAGE_FILE);
				mv.addObject("attachmentList", attachmentList);
				authorizationId = authorizationApply.getAuthorizationApplyId();


				Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
						"authorizationApply_" + authorizationId);


				mv.addObject("historicInfo", historicInfo);
				mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
				mv.addObject("commentMap", historicInfo.get("commentMap"));

				if (historicInfo.get("taskInfo") != null) {

					Task taskInfo = (Task) historicInfo.get("taskInfo");
					mv.addObject("taskId", taskInfo.getId());
					mv.addObject("taskInfo", taskInfo);

					//获取审核人候选组
					Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
					mv.addObject("candidateUserMap", candidateUserMap);

					//获取审核人候选组
					List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

					if (CollectionUtils.isNotEmpty(candidateUserList)) {

						List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

						mv.addObject("verifyUsers", org.apache.commons.lang.StringUtils.join(userNameList, ","));
					}
				}
				mv.addObject("authorizationStorageId", authorizationStorage2.getTemporaryStorageId());
				mv.addObject("authorizationApply", authorizationStorage2);
				mv.addObject("nonStandardAuthorizationUrl", authorizationStorage2.getNonStandardAuthorizationUrl());
				mv.addObject("nonStandardAuthorizationName", authorizationStorage2.getNonStandardAuthorizationName());
				mv.addObject("whetherSign", authorizationStorage2.getWhetherSign());
				mv.addObject("authorizationApplyId", authorizationId);
				List<BaseCompanyInfoSimpleDto> companyInfoListWithSeq = getCompanyInfoWithSeq();
				mv.addObject("companyInfoList", companyInfoListWithSeq);
				mv.addObject("companyInfoListWithSeq", JSONArray.toJSONString(companyInfoListWithSeq));
				mv.setViewName("order/quote/authorization_view_bh");
				return mv;
			}

			//第一次加载页面（非保存页面）
			if (CollectionUtils.isNotEmpty(authorizationApplies)
					&& authorizationApplyId == null) {
				AuthorizationApply firstApply = authorizationApplies.get(0);

				// 确保公章类型字段有值，如果为空则设置默认值为贝登公章(1)
				if (firstApply.getSealType() == null) {
					firstApply.setSealType(1);
				}

				mv.addObject("authorizationApply", firstApply);
				User user = userService.getUserById(firstApply.getUpdator());
				authorizationId = firstApply.getAuthorizationApplyId();

				//查询文件相关位置
				List<Attachment> attachment = attachmentService.getAttachmentInfoByRelatedIdAndFunctionId(authorizationId, AUTHORIZATION_APPLY_FILE);
				mv.addObject("attachmentList", attachment);


				Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
						"authorizationApply_" + authorizationId);


				mv.addObject("historicInfo", historicInfo);
				mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
				mv.addObject("commentMap", historicInfo.get("commentMap"));

				if (historicInfo.get("taskInfo") != null) {

					Task taskInfo = (Task) historicInfo.get("taskInfo");
					mv.addObject("taskId", taskInfo.getId());
					mv.addObject("taskInfo", taskInfo);

					//获取审核人候选组
					Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
					mv.addObject("candidateUserMap", candidateUserMap);

					//获取审核人候选组
					List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

					if (CollectionUtils.isNotEmpty(candidateUserList)) {

						List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

						mv.addObject("verifyUsers", org.apache.commons.lang.StringUtils.join(userNameList, ","));
					}
				}
				mv.addObject("userName", user.getUsername());
				AuthorizationApply apply = quoteService.getAuthorizationApplyByKeyId(authorizationId);
				List<Attachment> attach = attachmentService.getAttachmentInfoByRelatedIdAndFunctionId(authorizationId, AUTHORIZATION_ABANDON_FILE);
				mv.addObject("attachList", attach);
				mv.addObject("comment", apply.getComment());
				mv.addObject("nonStandardAuthorizationUrl", apply.getNonStandardAuthorizationUrl());
				mv.addObject("nonStandardAuthorizationName", apply.getNonStandardAuthorizationName());
				mv.addObject("whetherSign", apply.getWhetherSign());


				//如果为驳回状态，显示驳回界面
				if (authorizationApplies.get(0).getApplyStatus() == 2) {
					mv.addObject("authorizationApplyId", authorizationId);
					List<BaseCompanyInfoSimpleDto> companyInfoListWithSeq = getCompanyInfoWithSeq();
					mv.addObject("companyInfoList", companyInfoListWithSeq);
					mv.addObject("companyInfoListWithSeq", JSONArray.toJSONString(companyInfoListWithSeq));
					mv.setViewName("order/quote/authorization_view_bh");
					return mv;
				}
			}

			//非第一次加载页面（非保存页面）
			if (authorizationApplyId != null) {
				// 确保公章类型字段有值，如果为空则设置默认值为贝登公章(1)
				if (authorizationApply.getSealType() == null) {
					authorizationApply.setSealType(1);
				}

				mv.addObject("authorizationApply", authorizationApply);
				authorizationId = authorizationApply.getAuthorizationApplyId();


				Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
						"authorizationApply_" + authorizationId);

				//查询文件相关位置
				List<Attachment> attachment = attachmentService.getAttachmentInfoByRelatedIdAndFunctionId(authorizationApplyId, AUTHORIZATION_APPLY_FILE);
				mv.addObject("attachmentList", attachment);


				mv.addObject("historicInfo", historicInfo);
				mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
				mv.addObject("commentMap", historicInfo.get("commentMap"));

				if (historicInfo.get("taskInfo") != null) {

					Task taskInfo = (Task) historicInfo.get("taskInfo");
					mv.addObject("taskId", taskInfo.getId());
					mv.addObject("taskInfo", taskInfo);

					//获取审核人候选组
					Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
					mv.addObject("candidateUserMap", candidateUserMap);

					//获取审核人候选组
					List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

					if (CollectionUtils.isNotEmpty(candidateUserList)) {

						List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

						mv.addObject("verifyUsers", org.apache.commons.lang.StringUtils.join(userNameList, ","));
					}
				}
				User user = userService.getUserById(authorizationApply.getUpdator());
				mv.addObject("userName", user.getUsername());
				AuthorizationApply apply = quoteService.getAuthorizationApplyByKeyId(authorizationId);
				List<Attachment> attach = attachmentService.getAttachmentInfoByRelatedIdAndFunctionId(authorizationId, AUTHORIZATION_ABANDON_FILE);
				mv.addObject("attachList", attach);
				mv.addObject("comment", apply.getComment());
				mv.addObject("nonStandardAuthorizationUrl", apply.getNonStandardAuthorizationUrl());
				mv.addObject("nonStandardAuthorizationName", apply.getNonStandardAuthorizationName());
				mv.addObject("whetherSign", apply.getWhetherSign());
				//如果为驳回状态，显示驳回界面
				if (authorizationApply.getApplyStatus() == 2) {
					mv.addObject("authorizationApplyId", authorizationId);
					List<BaseCompanyInfoSimpleDto> companyInfoListWithSeq = getCompanyInfoWithSeq();
					mv.addObject("companyInfoList", companyInfoListWithSeq);
					mv.addObject("companyInfoListWithSeq", JSONArray.toJSONString(companyInfoListWithSeq));
					mv.setViewName("order/quote/authorization_view_bh");
					return mv;
				}
			}

		} catch (Exception e) {
			logger.error("授权书详情页异常" + quoteorderId + authorizationApplyId, e);
			throw new RuntimeException(e);
		}
		List<BaseCompanyInfoSimpleDto> companyInfoListWithSeq = getCompanyInfoWithSeq();
		mv.addObject("companyInfoList", companyInfoListWithSeq);
		mv.addObject("domain", domain);
		mv.addObject("companyInfoListWithSeq", JSONArray.toJSONString(companyInfoListWithSeq));
		mv.setViewName("order/quote/authorization_view");
		return mv;
	}



	/**
	 * 撤回请求方法
	 * @param request
	 * @throws Exception
	 */
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value = "authorizationWithdrow")
	public ResultInfo<?> authorizationWithdrow(HttpServletRequest request,Integer authorizationApplyId,String comments){
		ResultInfo<Boolean> resultInfo=new ResultInfo<>();
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		List<String> userNameList=new ArrayList<>();
		List<Integer> userIdList=new ArrayList<>();
		try {
			Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
					"authorizationApply_" + authorizationApplyId);

			// 获取当前活动节点
			Task taskInfo = processEngine.getTaskService()
					.createTaskQuery()
					.processInstanceBusinessKey("authorizationApply_"+authorizationApplyId)
					.singleResult();

			String preAssignee=null;
			if(historicInfo.get("startUser")!=null) {
				preAssignee = historicInfo.get("startUser").toString();
			}
			if(!user.getUsername().equals(preAssignee)){
				resultInfo.setData(false);
				resultInfo.setMessage("你没有权限操作");
				return resultInfo;
			}
			Long time = DateUtil.sysTimeMillis();
			quoteService.updateAuthorizationApplyModTimeAndStatus(authorizationApplyId,user.getUserId(),time,AUTHORIZATION_CANCEL);
			quoteService.updateAuthorizationApplyComments(authorizationApplyId,comments);
			quoteService.updateAuthorizationApplyReviewer(authorizationApplyId,null);
			Map<String,String> map=new HashMap<>();
			AuthorizationApply authorizationApply=quoteService.getAuthorizationApplyByKeyId(authorizationApplyId);
			//如果是驳回的状态下保存了数据也删除暂存表的数据
			quoteService.updateAuthorizationStorage(authorizationApply.getAuthorizationApplyNum());

			if(taskInfo != null){

				Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
				//获取审核人候选组
				List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

				if(CollectionUtils.isNotEmpty(candidateUserList)){
					userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());
				}
				userNameList.add(preAssignee);
				for (String userName: userNameList) {
					User assigneeInfo = userService.getByUsername(userName, user.getCompanyId());
					userIdList.add(assigneeInfo.getUserId());
				}
				//删除流程实例
				actionProcdefService.deleteProcessInstance(taskInfo.getId());
			}

			map.put("AuthorizationNum",authorizationApply.getAuthorizationApplyNum());
			String url="./order/quote/authorizationExamine.do?authorizationApplyId=" + authorizationApplyId;
			//消息发送失败不让触发事务回滚
			try {
				MessageUtil.sendMessage(133,userIdList,map,url,preAssignee);
			} catch (Exception e) {
				logger.error("撤回授权书发送消息失败"+userIdList.toString()+map.toString()+url+preAssignee+authorizationApplyId);
			}
		} catch (Exception e) {
			logger.error("撤回授权书出错"+authorizationApplyId+comments,e);
			throw new RuntimeException(e);
		}
		resultInfo.setCode(0);
		resultInfo.setData(true);
		return resultInfo;
	}


	/**
	 * 终止请求方法
	 * @param request
	 * @throws Exception
	 */
	@FormToken(remove=true)
	@Transactional
	@ResponseBody
	@RequestMapping(value = "authorizationTermination")
	public ResultInfo<?> authorizationTermination(HttpServletRequest request, Integer authorizationApplyId, String comments) {

		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		List<String> userNameList = new ArrayList<>();
		List<Integer> userIdList = new ArrayList<>();

		try {
			Long time = DateUtil.sysTimeMillis();
			quoteService.updateAuthorizationApplyModTimeAndStatus(authorizationApplyId, user.getUserId(), time, AUTHORIZATION_CANCEL);
			quoteService.updateAuthorizationApplyComments(authorizationApplyId, comments);
			quoteService.updateAuthorizationApplyReviewer(authorizationApplyId, null);
			AuthorizationApply authorizationApply = quoteService.getAuthorizationApplyByKeyId(authorizationApplyId);
			Map<String, String> map = new HashMap<>();
			//如果是驳回的状态下保存了数据也删除暂存表的数据
			quoteService.updateAuthorizationStorage(authorizationApply.getAuthorizationApplyNum());

			Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
					"authorizationApply_" + authorizationApplyId);

			// 获取当前活动节点
			Task taskInfo = processEngine.getTaskService()
					.createTaskQuery()
					.processInstanceBusinessKey("authorizationApply_" + authorizationApplyId)
					.singleResult();

			String preAssignee = null;
			if (historicInfo.get("startUser") != null) {
				preAssignee = historicInfo.get("startUser").toString();
			}
			if (taskInfo != null) {
				Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
				//获取审核人候选组
				List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

				if (CollectionUtils.isNotEmpty(candidateUserList)) {
					userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());
				}
				userNameList.add(preAssignee);
				for (String userName : userNameList) {
					User assigneeInfo = userService.getByUsername(userName, user.getCompanyId());
					userIdList.add(assigneeInfo.getUserId());
				}
				//删除流程实例
				actionProcdefService.deleteProcessInstance(taskInfo.getId());
			}

			map.put("AuthorizationNum", authorizationApply.getAuthorizationApplyNum());
			String url = "./order/quote/authorizationExamine.do?authorizationApplyId=" + authorizationApplyId;
			//消息发送错误不让触发事务回滚
			try {
				MessageUtil.sendMessage(133, userIdList, map, url, preAssignee);
			} catch (Exception e) {
				logger.error("终止授权书发送消息失败" + userIdList.toString() + map.toString() + url + preAssignee + authorizationApplyId);
			}
		} catch (Exception e) {
			logger.error("终止授权书出错" + authorizationApplyId + comments, e);
			throw new RuntimeException(e);
		}
		return ResultInfo.success();
	}


	/**
	 * 终止请求方法
	 * @param request
	 * @throws Exception
	 */
	@Transactional
	@ResponseBody
	@RequestMapping(value = "authorizationTerminationTemporarily")
	public ResultInfo<?> authorizationTerminationTemporarily(HttpServletRequest request,Integer authorizationApplyId,String comments){

		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		List<String> userNameList=new ArrayList<>();
		List<Integer> userIdList=new ArrayList<>();
		try {
			Long time = DateUtil.sysTimeMillis();
			quoteService.updateAuthorizationApplyModTimeAndStatus(authorizationApplyId,user.getUserId(),time,AUTHORIZATION_CANCEL);
			quoteService.updateAuthorizationApplyComments(authorizationApplyId,comments);
			quoteService.updateAuthorizationApplyReviewer(authorizationApplyId,null);
			Map<String,String> map=new HashMap<>();
			AuthorizationApply authorizationApply=quoteService.getAuthorizationApplyByKeyId(authorizationApplyId);
			//如果是驳回的状态下保存了数据也删除暂存表的数据
			quoteService.updateAuthorizationStorage(authorizationApply.getAuthorizationApplyNum());

			Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
					"authorizationApply_" + authorizationApplyId);

			// 获取当前活动节点
			Task taskInfo = processEngine.getTaskService()
					.createTaskQuery()
					.processInstanceBusinessKey("authorizationApply_"+authorizationApplyId)
					.singleResult();

			String preAssignee=null;
			if(historicInfo.get("startUser")!=null) {
				preAssignee = historicInfo.get("startUser").toString();
			}
			if(taskInfo != null){
				Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
				//获取审核人候选组
				List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

				if(CollectionUtils.isNotEmpty(candidateUserList)){
					userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());
				}
				userNameList.add(preAssignee);
				for (String userName: userNameList) {
					User assigneeInfo = userService.getByUsername(userName, user.getCompanyId());
					userIdList.add(assigneeInfo.getUserId());
				}
				//删除流程实例
				actionProcdefService.deleteProcessInstance(taskInfo.getId());
			}

			map.put("AuthorizationNum",authorizationApply.getAuthorizationApplyNum());
			String url="./order/quote/authorizationExamine.do?authorizationApplyId=" + authorizationApplyId;
			//消息发送错误不让触发事务回滚
			try {
				MessageUtil.sendMessage(133,userIdList,map,url,preAssignee);
			} catch (Exception e) {
				logger.error("终止授权书发送消息失败"+userIdList.toString()+map.toString()+url+preAssignee+authorizationApplyId);
			}
		} catch (Exception e) {
			logger.error("终止授权书出错"+authorizationApplyId+comments,e);
			throw new RuntimeException(e);
		}
		return ResultInfo.success();
	}


	/**
	 * 废弃请求方法
	 * @param request
	 * @throws Exception
	 */
	@FormToken(remove=true)
	@Transactional
	@ResponseBody
	@RequestMapping(value = "authorizationAbandon")
	public ResultInfo<?> authorizationAbandon(HttpServletRequest request,Integer authorizationApplyId,String comments,Attachment attachment){
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		try {
			Long time = DateUtil.sysTimeMillis();
			quoteService.updateAuthorizationApplyModTimeAndStatus(authorizationApplyId,user.getUserId(),time,AUTHORIZATION_ABANDON);
			quoteService.updateAuthorizationApplyComments(authorizationApplyId,comments);
			quoteService.updateAuthorizationApplyReviewer(authorizationApplyId,null);
			attachment.setDomain(domain);
			attachment.setAddTime(time);
			attachment.setAttachmentFunction(AUTHORIZATION_ABANDON_FILE);
			attachment.setRelatedId(authorizationApplyId);
			if (StringUtils.isNotEmpty(attachment.getName()) && StringUtils.isNotEmpty(attachment.getUri())){
				attachmentService.saveOrUpdateAttachment(attachment);
			}
			// 获取当前活动节点
			Task taskInfo = processEngine.getTaskService()
					.createTaskQuery()
					.processInstanceBusinessKey("authorizationApply_"+authorizationApplyId)
					.singleResult();
			if(taskInfo != null){
				//删除流程实例
				actionProcdefService.deleteProcessInstance(taskInfo.getId());
			}
			List<Integer> userIdList=new ArrayList<>();
			Map<String,String> map=new HashMap<>();
			AuthorizationApply authorizationApply=quoteService.getAuthorizationApplyByKeyId(authorizationApplyId);
			User userInfo=userService.getUserById(authorizationApply.getCreator());
			userIdList.add(userInfo.getUserId());
			map.put("AuthorizationNum",authorizationApply.getAuthorizationApplyNum());
			String url="./order/quote/authorizationExamine.do?authorizationApplyId=" + authorizationApplyId;
			//废弃授权申请发送消息错误不触发事务回滚
			try {
				MessageUtil.sendMessage(132,userIdList,map,url,userInfo.getUsername());
			} catch (Exception e) {
				logger.error("废弃授权书发送消息失败"+userIdList.toString()+map.toString()+url+authorizationApply.toString());
			}
		} catch (Exception e) {
			logger.error("废弃授权书失败"+authorizationApplyId+comments+attachment.toString(),e);
			throw new RuntimeException(e);
		}
		return new ResultInfo<>();
	}

	/**
	 * 授权书列表
	 */
	@FormToken(save = true)
	@RequestMapping(value = "authorizationIndex")
	public ModelAndView authorizationIndex(AuthorizationApplyVo authorizationApplyVo, HttpServletRequest request,
										   @RequestParam(required = false, defaultValue = "1") Integer pageNo,
										   @RequestParam(required = false) Integer pageSize) {
		Page page = getPageTag(request, pageNo, pageSize);
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

		authorizationApplyVo.setUserName(user.getUsername());
		List<Integer> allSubordinateByUserIdForVisit = userApiService.getAllSubordinateByUserIdForVisit(user.getUserId());
		authorizationApplyVo.setUserIds(allSubordinateByUserIdForVisit);

		if(StrUtil.isNotEmpty(authorizationAuditUserIds)){
			List<String> list = Arrays.asList(authorizationAuditUserIds.split(","));
			if(list.contains(user.getUserId().toString())){
				authorizationApplyVo.setUserIds(null);
			}
		}


		List<AuthorizationApplyDto> authorizationApplyList = quoteService.getAuthorizationApplylistpage(authorizationApplyVo, page);
		List<String> applyPersonList = quoteService.getApplyPreson();

		if (CollectionUtils.isNotEmpty(authorizationApplyList)) {
			authorizationApplyList.forEach(e -> e.setIsCurrentUserCanCheck(isCurrentUserCanCheck(e.getReviewer(), user.getUsername())));
		}
		List<BaseCompanyInfoDto> allCompanyList =baseCompanyInfoApiService.findAll();

		List<BaseCompanyInfoDto> erpCompanyList = allCompanyList.stream()
				.filter(company -> company.getFrontEndSeq() != null)
				.sorted(Comparator.comparing(BaseCompanyInfoDto::getFrontEndSeq))
				.collect(Collectors.toList());



		ModelAndView mv = new ModelAndView();
		mv.addObject("authorizationApplyVo", authorizationApplyVo);
		mv.addObject("authorizationApplyList", authorizationApplyList);
		mv.addObject("applyPersonList", applyPersonList);
		mv.addObject("page", page);
		mv.addObject("companyInfoList", erpCompanyList);

		mv.setViewName("order/quote/authorization_index");
		return mv;
	}

	/**
	 * 判断当前用户是否有审核权限
	 *
	 * @param reviewer 审核人列表（逗号分隔）
	 * @param username 当前用户名
	 * @return 是否有审核权限
	 */
	private boolean isCurrentUserCanCheck(String reviewer, String username) {
		if (StrUtil.isEmpty(reviewer)) {
			return false;
		}

		List<String> reviewerList = Arrays.asList(reviewer.split(","));

		return CollectionUtils.isNotEmpty(reviewerList) && reviewerList.contains(username);
	}
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value = "authorizationShenhe")
	public ResultInfo<?> shenhe(HttpServletRequest request,Integer authorizationApplyId){
		ResultInfo<Boolean> resultInfo=new ResultInfo<>();
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();
		Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
				"authorizationApply_" + authorizationApplyId);
		if (historicInfo.get("taskInfo") != null) {
			Task taskInfo = (Task) historicInfo.get("taskInfo");
			//获取审核人候选组
			Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
			//获取审核人候选组
			List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());
			if(CollectionUtils.isNotEmpty(candidateUserList)){
				List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());
				if (!userNameList.contains(user.getUsername())){
					resultInfo.setData(false);
					String str=org.apache.commons.lang.StringUtils.join(userNameList,",");
					resultInfo.setMessage("当前处理人是"+str+",你无权操作审核");
					return resultInfo;
				}else {
					//完成当前任务
					Map<String, Object> taskVariableMap = new HashMap<String, Object>();
					taskVariableMap.put("pass", true);
					actionProcdefService.complementTask(request, taskInfo.getId(), "", user.getUsername(), taskVariableMap);

					Map<String, Object> historic = actionProcdefService.getHistoric(processEngine, "authorizationApply_" + authorizationApplyId);

					if (historic.get("taskInfo") != null) {
						Task task = (Task) historic.get("taskInfo");
						//获取审核人候选组
						Map<String, Object> candidateUserMap2 = (Map<String, Object>) historic.get("candidateUserMap");
						//获取审核人候选组
						List<IdentityLink> candidateUserList2 = (List<IdentityLink>) candidateUserMap2.get(task.getId());
						if(CollectionUtils.isNotEmpty(candidateUserList2)){
							List<String> userNameList2 = candidateUserList2.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());
							String verifyUsers=org.apache.commons.lang.StringUtils.join(userNameList2,",");
							quoteService.updateAuthorizationApplyReviewer(authorizationApplyId,verifyUsers);
						}
					}
					quoteService.updateAuthorizationApplyModTimeAndStatus(authorizationApplyId,user.getUserId(),time,null);
				}
			}
		}
		resultInfo.setData(true);
		return resultInfo;
	}


	/**
	 * 终止
	 * @param authorizationApplyId
	 * @return
	 */
	@FormToken(save=true)
	@RequestMapping(value = "zzApply")
	public ModelAndView zzApply(Integer authorizationApplyId){
		ModelAndView mv=new ModelAndView();
		mv.setViewName("order/quote/authorization_zz");
		mv.addObject("authorizationApplyId",authorizationApplyId);
		return mv;
	}


	/**
	 * 撤销
	 * @param authorizationApplyId
	 * @return
	 */
	@FormToken(save=true)
	@RequestMapping(value = "chApply")
	public ModelAndView chApply(Integer authorizationApplyId){
		ModelAndView mv=new ModelAndView();
		mv.setViewName("order/quote/authorization_ch");
		AuthorizationApply apply = quoteService.getAuthorizationApplyByKeyId(authorizationApplyId);
		mv.addObject("authorizationApplyId",authorizationApplyId);
		mv.addObject("quoteorderId",apply.getQuoteorderId());
		return mv;
	}


	/**
	 * 作废
	 * @param authorizationApplyId
	 * @return
	 */
	@FormToken(save=true)
	@RequestMapping(value = "zfApply")
	public ModelAndView zfApply(Integer authorizationApplyId){
		ModelAndView mv=new ModelAndView();
		mv.setViewName("order/quote/authorization_zf");
		mv.addObject("authorizationApplyId",authorizationApplyId);
		return mv;
	}

	@FormToken(remove=true)
	@MethodLock(className = Integer.class)
	@ResponseBody
	@RequestMapping(value = "canApply")
	public ResultInfo<?> canApply(HttpServletRequest request,@MethodLockParam Integer authorizationApplyId,String endSt,Integer skuid){
		ResultInfo resultInfo=new ResultInfo();
		try {
			User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			if (user==null){
				resultInfo.setMessage("请重新登陆");
				return resultInfo;
			}

			// add by Randy.Xu 2021/4/9 17:48 .Desc: . begin
			// VDERP-5839 数据越权 只可以提交自己及下属的授权书审核。
			if(authService.checkUserIsSale(user)) {
				List<User> userListByorderId = authService.getUserListByorderId(authorizationApplyId, authService.AUTHORIZATION_APPLY_TYPE);
				Boolean checkFlag = authService.existOrNot(user, userListByorderId);
				if (checkFlag) {
					logger.info("销售越权操作:接口[order/quote/canApply],行为[提交非自己及下属的授权书审核],操作人{}",user.getUsername());
				}
			}
			// add by Randy.Xu 2021/4/9 17:48 .Desc: . end


			Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
					"authorizationApply_" + authorizationApplyId);


			if ("产品归属人".equals(endSt) && !"产品归属人".equals(historicInfo.get("endStatus").toString())){
				String assistant=userService.getAssistantBySkuId(skuid);
				String manager=userService.getManagerBySkuId(skuid);
				if (user.getUsername().equals(assistant)){
					resultInfo.setMessage(manager+"已处理");
					return resultInfo;
				}
				if (user.getUsername().equals(manager)){
					resultInfo.setMessage(assistant+"已处理");
					return resultInfo;
				}
			}

			if (historicInfo.get("taskInfo") == null){
				resultInfo.setMessage("该流程已经结束");
				return resultInfo;
			}else {
				Task taskInfo = (Task) historicInfo.get("taskInfo");
				//获取审核人候选组
				Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
				//获取审核人候选组
				List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());
				if(CollectionUtils.isNotEmpty(candidateUserList)){
					List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());
					if (!userNameList.contains(user.getUsername())){
						String str=org.apache.commons.lang.StringUtils.join(userNameList,",");
						resultInfo.setMessage("当前处理人是$"+str+"$,你无权操作审核");
						return resultInfo;
					}
				}
			}
			resultInfo.setCode(0);
			return resultInfo;
		} catch (Exception e) {
			logger.error("授权书审核流程错误"+authorizationApplyId,e);
			return  resultInfo;
		}
	}



	/**
	 * 申请时授权书预览页面
	 * @param authorizationStorage
	 * @return
	 */
	@RequestMapping(value = "authorizationPreview")
	public ModelAndView authorizationPreview(AuthorizationStorage authorizationStorage){
		// 如果公章类型为空，默认设置为贝登公章
		if (authorizationStorage.getSealType() == null) {
			authorizationStorage.setSealType(1);
		}
		ModelAndView mv=new ModelAndView("order/quote/authorization_preview");
		List<BaseCompanyInfoSimpleDto> companyInfoListWithSeq = getCompanyInfoWithSeq();
		mv.addObject("companyInfoList", companyInfoListWithSeq);

		return mv;
	}

	/**
	 * 打印授权书
	 *
	 * @param authorizationApplyId
	 * @return
	 */
	@RequestMapping(value = "printAuthorizationElectronicSign")
	public ModelAndView printAuthorizationElectronicSign(Integer authorizationApplyId) {
		ModelAndView mv = new ModelAndView("order/quote/authorization_preview");
		AuthorizationApply apply = quoteService.getAuthorizationApplyByKeyId(authorizationApplyId);

		// 确保公章类型字段有值，如果为空则设置默认值为贝登公章(1)
		if (apply.getSealType() == null) {
			apply.setSealType(1);
		}
		List<BaseCompanyInfoSimpleDto> companyInfoListWithSeq = getCompanyInfoWithSeq();
		mv.addObject("companyInfoList", companyInfoListWithSeq);
		mv.addObject("authorizationApply", apply);
		return mv;
	}

	//开始工作流
	private void startProcessInstance(HttpServletRequest request,AuthorizationApply authorizationApply,User targetUser,User sessionUser) throws Exception {

		//初始化工作流变量
		Map<String, Object> variableMap = new HashMap<String, Object>();


		variableMap.put("currentAssinee", targetUser.getUsername());
		variableMap.put("processDefinitionKey", "testshouquan");
		variableMap.put("authorizationApplyId", authorizationApply.getAuthorizationApplyId());
		variableMap.put("skuId", authorizationApply.getSkuId());
		variableMap.put("authorizationNum", authorizationApply.getAuthorizationApplyNum());
		variableMap.put("businessKey", "authorizationApply_" + authorizationApply.getAuthorizationApplyId());
		variableMap.put("standardTemplate", authorizationApply.getStandardTemplate());
		variableMap.put("sealType", authorizationApply.getSealType());

		actionProcdefService.createProcessInstance(request, variableMap.get("processDefinitionKey").toString(), variableMap.get("businessKey").toString(), variableMap);


		// 获取当前活动节点
		Task taskInfo = processEngine.getTaskService()
				.createTaskQuery()
				.processInstanceBusinessKey(variableMap.get("businessKey").toString())
				.singleResult();
		String comment=sessionUser.getUsername()+"代理";
		if(StringUtils.equalsIgnoreCase(sessionUser.getUsername(),targetUser.getUsername())){
			comment="";
		}

		//完成当前任务
		actionProcdefService.complementTask(request, taskInfo.getId(), comment, targetUser.getUsername(), variableMap);

		//更新sku的核价审核状态为审核中
		Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
				"authorizationApply_" + authorizationApply.getAuthorizationApplyId());
		if (historicInfo.get("taskInfo") != null) {

			Task task = (Task) historicInfo.get("taskInfo");

			//获取审核人候选组
			Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");
			//获取审核人候选组
			List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(task.getId());

			if (CollectionUtils.isNotEmpty(candidateUserList)) {
				List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());
				String verifyUsers = org.apache.commons.lang.StringUtils.join(userNameList, ",");
				//审核后的流程不能影响事务
				try {
					quoteService.updateAuthorizationApplyReviewer(authorizationApply.getAuthorizationApplyId(), verifyUsers);
				} catch (Exception e) {
					logger.error("授权书申请改变审核人出错" + authorizationApply.toString() + verifyUsers);
				}
			}
		}

	}

	/**
	 * 初始化维护报价单报备信息页面
	 *
	 * @param consultRelatedId
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/terminalInfoInit")
	public ModelAndView terminalInfoInit(Integer consultRelatedId){
		ModelAndView modelAndView = new ModelAndView("/order/quote/edit_quote_authorization");
		if (consultRelatedId == null){
			return modelAndView;
		}
		Quoteorder quoteorder = quoteService.getQuoteInfoByKey(consultRelatedId);

		if (quoteorder.getSalesAreaId() != null) {
			// 地区
			List<Region> regionList = (List<Region>) regionService.getRegion(quoteorder.getSalesAreaId(), 1);
			if (regionList != null && (!regionList.isEmpty())) {
				for (Region r : regionList) {
					switch (r.getRegionType()) {
						case 1:
							List<Region> cityList = regionService.getRegionByParentId(r.getRegionId());
							modelAndView.addObject("provinceRegion", r);
							modelAndView.addObject("cityList", cityList);
							break;
						case 2:
							List<Region> zoneList = regionService.getRegionByParentId(r.getRegionId());
							modelAndView.addObject("cityRegion", r);
							modelAndView.addObject("zoneList", zoneList);
							break;
						case 3:
							modelAndView.addObject("zoneRegion", r);
							break;
						default:
							modelAndView.addObject("countryRegion", r);
							break;
					}
				}
			}
		}


		// 省级地区
		List<Region> provinceList = regionService.getRegionByParentId(1);
		modelAndView.addObject("provinceList", provinceList);
		modelAndView.addObject("terminalTypes", skuAuthorizationService.getAllTerminalTypes());
		modelAndView.addObject("quoteorder", quoteorder);
		return modelAndView;
	}

	/**
	 * 保存报价单的终端信息
	 *
	 * @param quoteorder
	 * @param zone
	 * @param session
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/saveTerminalInfo")
	public ResultInfo saveTerminalInfo(Quoteorder quoteorder, Integer zone, HttpSession session){
		User user = (User)session.getAttribute(Consts.SESSION_USER);
		ResultInfo<Object> resultInfo = new ResultInfo<>();

		StringBuffer salesArea = new StringBuffer();
		if (zone != null){
			quoteorder.setSalesAreaId(zone);
			// 地区
			List<Region> regionList = (List<Region>) regionService.getRegion(quoteorder.getSalesAreaId(), 1);
			if (CollectionUtils.isNotEmpty(regionList)) {
				for (Region region : regionList) {
					salesArea.append(region.getRegionName())
							.append(" ");
				}
			}
			quoteorder.setSalesArea(salesArea.toString());
		} else {
			quoteorder.setSalesArea("未知");
		}

		if (quoteorder.getTerminalType() == null){
			quoteorder.setTerminalType(1);
		}
		if (quoteorder.getTerminalTraderName() == null){
			quoteorder.setTerminalTraderName("未知");
		}
		quoteorder.setUpdater(user.getUserId());
		quoteorder.setModTime(System.currentTimeMillis());


		try {
			quoteService.updateQuote(quoteorder);
		} catch (Exception e) {
			logger.error("保存报价终端信息error", e);
			resultInfo.setMessage("保存报价终端信息error");
			return resultInfo;
		}
		resultInfo.setCode(0);
		resultInfo.setMessage("保存报价终端信息成功!");
		return resultInfo;
	}
	@RequestMapping("/quoteLinkBd")
	@ResponseBody
	public ResultInfo quoteLinkBd(HttpServletRequest request,HttpSession session,QuoteLinkBdLog log){
		if(log==null||log.getQuoteId()==null||log.getBdOrderId()==null){
			return ResultInfo.error("必要参数不得为空");
		}
		Quoteorder quoteorder=quoteService.getQuoteInfoByKey(log.getQuoteId());
		if(StringUtil.isNotBlank(quoteorder.getSaleorderId())){
			return ResultInfo.error("关联失败，请刷新页面重新尝试");
		}
		QuoteLinkBdLog queryLog=new QuoteLinkBdLog();
		queryLog.setBdOrderId(log.getBdOrderId());
		queryLog.setIsEnable(ErpConst.ONE);
		QuoteLinkBdLog validateLog=quoteService.getQuoteLinkBdLogByInfo(queryLog);
		if(validateLog!=null){
			return ResultInfo.error("该BD订单正在关联审核中，无法被关联");
		}
		User user = getSessionUser(request);
		log.setCreator(user.getUserId());
		log.setAddTime(System.currentTimeMillis());
		log.setIsEnable(ErpConst.ONE);
		quoteService.saveQuoteLinkBdLog(log);
		startCheckQuoteLinkBd(request, log, user.getUserId());
		return ResultInfo.success();
	}

	/**
	 * 启动报价关联订单审核
	 *
	 * @param request
	 * @param log
	 * @param belongUserId
	 */
	private void startCheckQuoteLinkBd(HttpServletRequest request,QuoteLinkBdLog log, Integer belongUserId){
		if (belongUserId == null){
			logger.error("startCheckQuoteLinkBd error log:{}", JSON.toJSONString(log));
			return;
		}
		//初始化工作流变量
		Map<String, Object> variableMap = new HashMap<String, Object>();
		User user = userService.getUserById(belongUserId);
		variableMap.put("currentAssinee", user.getUsername());
		variableMap.put("creator", user.getUserId());
		variableMap.put("processDefinitionKey", "quoteLinkBDVerify");
		variableMap.put("quoteId", log.getQuoteId());
		variableMap.put("bdOrderId", log.getBdOrderId());
		variableMap.put("logId", log.getQuoteLinkBdLogId());
		variableMap.put("businessKey", "quoteLinkBd_" + log.getQuoteId());
		try{
			actionProcdefService.createProcessInstance(request, variableMap.get("processDefinitionKey").toString(), variableMap.get("businessKey").toString(), variableMap);
			// 获取当前活动节点
			Task taskInfo = processEngine.getTaskService()
					.createTaskQuery()
					.processInstanceBusinessKey(variableMap.get("businessKey").toString())
					.singleResult();

			//完成当前任务
			actionProcdefService.complementTask(request, taskInfo.getId(), "", user.getUsername(), variableMap);
			Quoteorder quoteorder=new Quoteorder();
			quoteorder.setQuoteorderId(log.getQuoteId());
			quoteorder.setLinkBdStatus(OrderConstant.QUOTE_LINK_BD_CHECKIND_STATUS);
			quoteService.updateQuote(quoteorder);
			String url = ErpConst.QUOTE_LINK_BD_URL + log.getQuoteLinkBdLogId();
			List<Integer> userIds=new ArrayList<>();
			User parentUser=userService.getUserParentInfo(user.getUsername(),user.getCompanyId());
			if(parentUser==null||parentUser.getParentId()==null){
				userIds.add(user.getUserId());
			}else{
				userIds.add(parentUser.getParentId());
			}
			Map<String,String> map=new HashMap<>();
			Saleorder saleorder=saleorderService.getsaleorderbySaleorderId(log.getBdOrderId());
			if(saleorder!=null){
				map.put("orderNo",saleorder.getSaleorderNo());
			}
			MessageUtil.sendMessage(140, userIds, map, url, null);
		}catch (Exception ex){
			logger.error("创建报价单关联bd订单审核流程出错，error:",ex);
		}

	}

	@Autowired
	private ExpressService expressService;
	@Autowired
	private InvoiceService invoiceService;
	@RequestMapping("/checkQuoteLinkBdPage")
	public ModelAndView checkQuoteLinkBdPage(HttpServletRequest request,HttpSession session,Integer quoteLinkBdLogId){
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		ModelAndView mv=new ModelAndView("order/quote/quote_link_bd_check");
		QuoteLinkBdLog linkBdLog=quoteService.getQuoteLinkBdLog(quoteLinkBdLogId);
		if(linkBdLog==null||linkBdLog.getQuoteId()==null||linkBdLog.getBdOrderId()==null){
			return fail(mv);
		}
		Quoteorder quoteorder=quoteService.getQuoteInfoByKey(linkBdLog.getQuoteId());

		if(quoteorder==null||quoteorder.getBussinessChanceId()==null){
			return fail(mv);
		}
		try {
			Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "quoteLinkBd_" + linkBdLog.getQuoteId());
			mv.addObject("taskInfo", historicInfo.get("taskInfo"));
			mv.addObject("startUser", historicInfo.get("startUser"));
			// 最后审核状态
			mv.addObject("endStatus", historicInfo.get("endStatus"));
			mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
			mv.addObject("commentMap", historicInfo.get("commentMap"));
			mv.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
			mv.addObject("quote", quoteorder);
			Page page = getPageTag(request, 1, 20);
			BussinessChance bussinessChance = new BussinessChance();
			bussinessChance.setBussinessChanceId(quoteorder.getBussinessChanceId());
			Map<String, Object> map = bussinessChanceService.getAfterSalesDetail(bussinessChance, page);
			if (map.containsKey("bussinessChanceVo")) {
				BussinessChanceVo bcv = (BussinessChanceVo) map.get("bussinessChanceVo");
				mv.addObject("bussinessChance", bcv);
			}
			Saleorder saleorder = new Saleorder();
			saleorder.setSaleorderId(linkBdLog.getBdOrderId());
			saleorder = saleorderService.getBaseSaleorderInfo(saleorder);

			User belongUser = userService.getUserByTraderId(saleorder.getTraderId(), 1);
			if(belongUser!=null){
				saleorder.setOptUserName(belongUser.getUsername());
			}
			Map<String, BigDecimal> moneyData = saleorderService.getSaleorderDataInfo(saleorder.getSaleorderId());
			if (moneyData != null && moneyData.get("realAmount") != null) {
				mv.addObject("realAmount", moneyData.get("realAmount"));
			}
			Saleorder sale = new Saleorder();
			sale.setSaleorderId(saleorder.getSaleorderId());
			sale.setTraderId(saleorder.getTraderId());
			sale.setCompanyId(user.getCompanyId());
			sale.setReqType(1);
			List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleorderGoodsById(sale);
			mv.addObject("saleorderGoodsList", saleorderGoodsList);
			// add by Tomcat.Hui 2019/11/28 15:40 .Desc: VDERP-1325 分批开票 已开票数量/开票中数量/该订单该sku的数量. start
			//已开票数量 已申请数量
			Map<Integer , Map<String, Object>> taxNumsMap = invoiceService.getInvoiceNums(saleorder);
			if (null != taxNumsMap) {
				saleorderGoodsList.stream().forEach(g -> {
					g.setAppliedNum(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("APPLY_NUM").toString()));
					g.setInvoicedNum(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("INVOICE_NUM").toString()));
					g.setInvoicedAmount(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("INVOICE_AMOUNT").toString()));
					g.setAppliedAmount(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("APPLY_AMOUNT").toString()));
				});
			}
			List<ExpressDetail> expresseList = expressService.getSEGoodsNum(saleorderGoodsList.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList()));
			mv.addObject("expresseList", expresseList);


			Map<String, BigDecimal> saleorderDataInfo = saleorderService.getSaleorderDataInfo(saleorder.getSaleorderId());
			mv.addObject("saleorderDataInfo", saleorderDataInfo);
			mv.addObject("saleorder", saleorder);
			Map<String, Object> qmap = quoteService.getQuoteGoodsByQuoteId(quoteorder.getQuoteorderId(), user.getCompanyId(), session, 3, quoteorder.getTraderId());
			List<QuoteorderGoods> quoteGoodsList = (List<QuoteorderGoods>) qmap.get("quoteGoodsList");
			mv.addObject("quoteGoodsList", quoteGoodsList);
			TraderCustomerVo customer = traderCustomerService.getCustomerBussinessInfo(saleorder.getTraderId());
			// 产品结算价
			saleorderGoodsList = goodsSettlementPriceService
					.getGoodsSettlePriceBySaleorderGoodsList(user.getCompanyId(), saleorderGoodsList);
			// 计算核价信息
			if (customer != null) {
				saleorderGoodsList = goodsChannelPriceService.getSaleChannelPriceList(saleorder.getSalesAreaId(),
						saleorder.getCustomerNature(), customer.getOwnership(), saleorderGoodsList);
			}


			//添加产品的成本价
			//goodsChannelPriceService.setGoodsReferenceCostPrice(saleorderGoodsList);
			mv.addObject("saleorderGoodsList", saleorderGoodsList);
			for (SaleorderGoods good : saleorderGoodsList) {
				CoreSkuGenerate sku = saleorderService.getSkuBySkuNo(good.getSku());
				if (sku == null) {
					continue;
				}
				CoreSpuGenerate spu = saleorderService.getSpuBySpuId(sku.getSpuId());
				if (spu == null) {
					continue;
				}
				if (spu.getSpuType().equals("1008") || spu.getSpuType().equals("316")) {
					//如果sku商品类型为器械设备或配件，产品信息的规格/型号取该sku的制造商型号
					good.setModel(sku.getModel());
				} else {
					//否则取该sku的规格
					good.setModel(sku.getSpec());
				}
			}
			mv.addObject("saleorderGoodsList", saleorderGoodsList);

			//新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
			List<Integer> skuIds = new ArrayList<>();
			saleorderGoodsList.stream().forEach(saleGood -> {
				skuIds.add(saleGood.getGoodsId());
			});
			List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
			Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
			mv.addObject("newSkuInfosMap", newSkuInfosMap);
			//新商品流切换 VDERP-1970 add by brianna 2020/3/16 end


			List<String> skuNos = saleorderGoodsList.stream().map(saleorderGood -> saleorderGood.getSku()).collect(Collectors.toList());

			//批量查看sku的价格信息
			List<PriceInfoResponseDto> priceInfoResponseDtos = this.basePriceService.batchFindPriceInfo(skuNos, saleorder.getTraderId());

			//核价信息应用 add by brianna 2020/5/29 start
			List<GoodSalePrice> goodSalePriceList = saleorderGoodsList.stream()
					.map(
							saleorderGood -> {
								return new GoodSalePrice(saleorderGood.getSku(), saleorderGood.getChannelPrice());
							}
					).collect(Collectors.toList());

			Map<String, String> skuNoAndPriceMap = priceInfoDealWithService.dealWithGoodsSalePrice(saleorder.getTraderId(),goodSalePriceList, priceInfoResponseDtos);
			mv.addObject("skuNoAndPriceMap", skuNoAndPriceMap);
			//核价信息应用 add by brianna 2020/5/29 end

			//处理销售单的成本价
			priceInfoDealWithService.dealWithCostPrice(saleorderGoodsList, priceInfoResponseDtos);



			List<Integer> qskuIds = new ArrayList<>();
			quoteGoodsList.stream().forEach(quoteGood -> {
				qskuIds.add(quoteGood.getGoodsId());
			});
			List<Map<String, Object>> qskuTipsMap = this.vGoodsService.skuTipList(qskuIds);
			Map<String, Map<String, Object>> qnewSkuInfosMap = qskuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
			mv.addObject("qnewSkuInfosMap", qnewSkuInfosMap);
			//新商品流切换 VDERP-1970 add by brianna 2020/3/16 end


			//核价信息应用 add by brianna 2020/5/29 start
			List<GoodSalePrice> qgoodSalePriceList = quoteGoodsList.stream().map(quoteGood -> {
				return new GoodSalePrice(quoteGood.getSku(), quoteGood.getChannelPrice());
			}).collect(Collectors.toList());

			Map<String, String> qskuNoAndPriceMap = priceInfoDealWithService.dealWithGoodsSalePrice(quoteorder.getTraderId(), qgoodSalePriceList);

			mv.addObject("qskuNoAndPriceMap", qskuNoAndPriceMap);
		}catch (Exception ex){
			logger.error("查看报价关联bd订单报错",ex);
		}
		return mv;
	}

	/**
	 * 关联订单操作
	 * @param request
	 * @param log
	 * @return
	 */
	@FormToken(remove = true)
	@RequestMapping("/quoteLinkOrder")
	@NoNeedAccessAuthorization
	@ResponseBody
	public ResultInfo quoteLinkOrder(HttpServletRequest request,QuoteLinkBdLog log){
		if(log==null||log.getQuoteId()==null||log.getBdOrderId()==null){
			return ResultInfo.error("必要参数不得为空");
		}
		Quoteorder quoteorder=quoteService.getQuoteOrderInfoById(log.getQuoteId());
		if(StringUtil.isNotBlank(quoteorder.getSaleorderId())){
			return ResultInfo.error("报价单已转订单，关联失败，请重新关联");
		}
		QuoteLinkBdLog queryLog=new QuoteLinkBdLog();
		queryLog.setBdOrderId(log.getBdOrderId());
		queryLog.setIsEnable(ErpConst.ONE);
		QuoteLinkBdLog validateLog=quoteService.getQuoteLinkBdLogByInfo(queryLog);
		if(validateLog!=null){
			return ResultInfo.error("订单已关联报价单，关联失败，请重新关联");
		}


		QuoteLinkBdLog quoteLinkBdLogByQuoteInfo = quoteService.getQuoteLinkBdLogByQuoteId(log.getQuoteId());
		if (quoteLinkBdLogByQuoteInfo != null){
			return ResultInfo.error("该报价单已提交关联订单，请确认。");
		}

		User user = getSessionUser(request);
		log.setCreator(user.getUserId());
		log.setAddTime(System.currentTimeMillis());
		log.setIsEnable(ErpConst.ONE);
		quoteService.saveQuoteLinkBdLog(log);
		startCheckQuoteLinkBd(request,log, quoteorder.getUserId());
		return ResultInfo.success();
	}

	@RequestMapping("/addHandleOpinion")
	public ModelAndView addHandleOpinion(Integer goodsLowerPriceId){
		ModelAndView mv = new ModelAndView();
		mv.addObject("goodsLowerPriceId",goodsLowerPriceId);
		mv.setViewName("vue/view/lowerPrice/addHandleOpinion");
		return mv;
	}

	@RequestMapping("/doAddHandleOpinion")
	@NoNeedAccessAuthorization
	@ResponseBody
	public ResultInfo doAddHandleOpinion(@RequestBody LowerPriceVo lowerPriceVo){
		Integer rows = orderGoodsLowerPriceApiService.addHandleOpinion(lowerPriceVo.getGoodsLowerPriceId(),lowerPriceVo.getHandleOpinion());
		if (rows.compareTo(ErpConst.ZERO) > 0){
			return ResultInfo.success();
		}
		return ResultInfo.error();
	}

	@RequestMapping("/toSaleOrderDetail")
	public ModelAndView toSaleOrderDetail(Integer saleorderId){
		return new ModelAndView("redirect:/orderstream/saleorder/detail.do?saleOrderId="+saleorderId+"&amp;scene=0");
	}

	@RequestMapping("/toQuoteOrderDetail")
	public ModelAndView toQuoteOrderDetail(Integer quoteOrderId){
		return new ModelAndView("redirect:/order/quote/getQuoteDetail.do?quoteorderId="+quoteOrderId+"&viewType=3");
	}


	@RequestMapping("/whetherIncludeText")
	@NoNeedAccessAuthorization
	@ResponseBody
	public ResultInfo whetherIncludeText(String url, @RequestParam(required = false) Integer sealType) {
		try {
			String ossUrl = OSS_HTTP + OSS_URL + url;
			boolean whetherIncludeText = false;

			// 根据公章类型选择不同的检查文本
			if (sealType != null && sealType >= 2) {
				List<BaseCompanyInfoDto> allCompanyList =baseCompanyInfoApiService.findAll();
				String companyName = allCompanyList.stream()
						.filter(company -> sealType.equals( company.getFrontEndSeq()))
						.findFirst()
						.map(BaseCompanyInfoDto::getCompanyName)
						.orElse("");

				// 医购优选公章
				whetherIncludeText = PdfUtil.whetherIncludeText(ossUrl, companyName);//"南京医购优选供应链管理有限公司"
			} else {
				// 贝登公章（默认）
				whetherIncludeText = PdfUtil.whetherIncludeText(ossUrl, "南京贝登医疗股份有限公司");
				if (!whetherIncludeText) {
					whetherIncludeText = PdfUtil.whetherIncludeText(ossUrl, "南京⻉登医疗股份有限公司");
				}
			}

			if (whetherIncludeText) {
				return ResultInfo.success();
			}
		} catch (Exception e) {
			logger.error("判断PDF是否包含公司名称报错", e);
			return ResultInfo.error();
		}
		return ResultInfo.error();
	}

}
