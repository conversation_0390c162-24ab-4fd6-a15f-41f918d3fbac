package com.vedeng.erp.trader.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecordParams;
import com.vedeng.erp.trader.dto.FollowBindingTelParams;
import com.vedeng.erp.trader.service.CommunicateRecordService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 沟通记录(CommunicateRecord)表控制层
 *
 * <AUTHOR>
 * @since 2022-07-12 15:44:47
 */
@ExceptionController
@RestController
@RequestMapping("/communicateRecord")
@Slf4j
public class CommunicateRecordApi {

    @Autowired
    private  CommunicateRecordService communicateRecordService;

    /**
     * 分页查询
     *
     * @param recordDtoPageParam 筛选条件
     * @return 查询结果
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public R<?> page(@RequestBody PageParam<CommunicateRecordDto> recordDtoPageParam) {
        return R.success(communicateRecordService.page(recordDtoPageParam));
    }

    /**
     * 沟通记录详情
     * @param communicateRecordDto
     * @return 结果
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getOne", method = RequestMethod.POST)
    public R<?> getOne(@RequestBody CommunicateRecordDto communicateRecordDto) {
        return R.success(communicateRecordService.getOne(communicateRecordDto));
    }


    /**
     * 新增数据
     *
     * @param communicateRecordDto 实体
     * @return 新增结果
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R<?> add(@RequestBody CommunicateRecordDto communicateRecordDto) {
        communicateRecordService.add(communicateRecordDto);
        return R.success();
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/update",method = RequestMethod.POST)
    public R<?> update(@RequestBody CommunicateRecordDto communicateRecordDto) {
        communicateRecordService.update(communicateRecordDto);
        return R.success();
    }

    
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getTelList",method = RequestMethod.POST)
    public R<?> getTelList(@RequestBody PageParam<CommunicateTelRecordParams> communicateTelRecordParams) {
        return R.success(communicateRecordService.getTelList(communicateTelRecordParams));
    }
    
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/followBindingTel",method = RequestMethod.POST)
    public R<List<Integer>> followBindingTel(@RequestBody FollowBindingTelParams followBindingTelParams) {
        return R.success(communicateRecordService.followBindingTel(followBindingTelParams));
    }
}

