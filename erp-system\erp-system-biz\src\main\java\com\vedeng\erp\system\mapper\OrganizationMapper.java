package com.vedeng.erp.system.mapper;

import com.vedeng.erp.system.domain.entity.OrganizationEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/8/29 13:19
 **/
@Repository("sysOrganizationMapper")
public interface OrganizationMapper {
    /**
     * delete by primary key
     * @param orgId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer orgId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(OrganizationEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(OrganizationEntity record);

    /**
     * select by primary key
     * @param orgId primary key
     * @return object by primary key
     */
    OrganizationEntity selectByPrimaryKey(Integer orgId);

    List<OrganizationEntity> getAllOrganizationOnly();

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(OrganizationEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(OrganizationEntity record);

    int batchInsert(@Param("list") List<OrganizationEntity> list);

    /**
     * 根据当前部门id查询上级部门id
     *
     * @param orgId 当前部门id
     * @return 上级部门id
     */
    Integer getParentIdById(@Param("orgId") Integer orgId);

    /**
     * 获取所有的父级部门(含当前部门ID),注意，通过递归的方式进行查询，最多只查10个层次，防止出现死循环的情况
     * @param orgId
     * @return
     */
    List<Integer> getParentIdsById(@Param("orgId")Integer orgId);

    /**
     * 查询子部门
     * @param parentId 父级id
     * @return List<Integer>
     */
    List<Integer> findIdByParentId(@Param("parentId")Integer parentId);
    List<OrganizationEntity> findByParentId(@Param("parentId")Integer parentId);

    List<String> getOrgNameList(@Param("userId")Integer userId);
}
