package com.vedeng.crm.web.controller;

import java.io.BufferedReader;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import com.alibaba.druid.util.StringUtils;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.crm.business.quote.domain.dto.ApproverAppChatDto;
import com.vedeng.crm.qw.util.AesException;
import com.vedeng.crm.qw.util.WXBizMsgCrypt;
import com.vedeng.crm.business.quote.facade.QuoteFacade;

import lombok.extern.slf4j.Slf4j;

/**
 * 企微应用回调地址控制器
 * @ClassName:  QwApplication   
 * @author: Neil.yang
 * @date:   2025年7月11日 下午5:14:28    
 * @Copyright:
 */
@Controller
@RequestMapping("/api")
@Slf4j
public class QwApplicationApi {
	
	@Value("${qw_receive_sToken:yQWsRYPkhfkKGWThPDWp9O0Vs5Q6}")
	private String sToken;
	
	@Value("${qw_receive_sEncodingAESKey:QQm5uWz94CXbTvG1zAAcXm3HUG2vWYY6E6jZBQVaUXs}")
	private String sEncodingAESKey;
	
	@Value("${qw_receive_sCorpID:ww877e627a6426776c}")
	private String sCorpID;
	
	@Autowired
	private QuoteFacade quoteFacade;
	
	@Value("${testCurrentId}")
    private Integer testCurrentId;
	    
    @Value("${testCurrentName}")
    private String testCurrentName;
    
    //测试环境是否开启
    @Value("${isTurnOnTest:0}")
    private String isTurnOnTest;
    
    //发布环境
    @Value("${PROJECT_ENV}")
    private String projectEnv; 
	
	/**
	 * 开启消息,用于企微认证（GET方式）,企微回调只会使用一次
	 * @param msg_signature
	 * @param timestamp
	 * @param nonce
	 * @param echostr
	 * @return
	 */
	@RequestMapping(value = "/turnOnAndGetMessage",method = RequestMethod.GET)
    @NoNeedAccessAuthorization
    @ResponseBody
    public String turnOnAndGetMessageGet(@RequestParam(value="msg_signature") String msg_signature ,@RequestParam(value="timestamp") String timestamp
    		,@RequestParam(value="nonce") String nonce,@RequestParam(value="echostr") String echostr) {
		log.info("开启消息,用于企微认证（GET方式）,企微回调只会使用一次,入参：msg_signature:{}，timestamp:{}，nonce:{}，echostr:{}",msg_signature,timestamp,nonce,echostr);
		WXBizMsgCrypt wxcpt;
		//需要返回的明文
		String sEchoStr = ""; 
		try {
			String sVerifyMsgSig = URLDecoder.decode(msg_signature, "UTF-8");
			String sVerifyTimeStamp = URLDecoder.decode(timestamp, "UTF-8");
			String sVerifyNonce = URLDecoder.decode(nonce, "UTF-8");
			String sVerifyEchoStr = URLDecoder.decode(echostr, "UTF-8");
			wxcpt = new WXBizMsgCrypt(sToken, sEncodingAESKey, sCorpID);
			sEchoStr = wxcpt.VerifyURL(sVerifyMsgSig, sVerifyTimeStamp,sVerifyNonce, sVerifyEchoStr);
			return sEchoStr;
		} catch (AesException e1) {
			log.error("解密失败",e1);
		} catch (UnsupportedEncodingException e) {
			log.error("urlcode失败",e);
		}
		return sEchoStr;
    }
	
	/**
	 * 接收消息(POST方式)，接收企微消息
	 * @param msg_signature
	 * @param timestamp
	 * @param nonce
	 * @param echostr
	 * @return
	 */
	@RequestMapping(value = "/turnOnAndGetMessage",method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    @ResponseBody
    public String turnOnAndGetMessagePost(@RequestParam(value="msg_signature") String msg_signature ,@RequestParam(value="timestamp") String timestamp
    		,@RequestParam(value="nonce") String nonce,HttpServletRequest request) {
		log.info("接收消息(POST方式)，接收企微消息,入参：msg_signature:{}，msg_signature:{}，timestamp:{}",msg_signature,timestamp,nonce);
		String responseCode = "";
		Integer toUserId = null;
		String parentBelongId = "";
		try {
			WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(sToken, sEncodingAESKey, sCorpID);
			String sVerifyMsgSig = URLDecoder.decode(msg_signature, "UTF-8");
			String sVerifyTimeStamp = URLDecoder.decode(timestamp, "UTF-8");
			String sVerifyNonce = URLDecoder.decode(nonce, "UTF-8");
			// 获取请求体中的 XML 数据
            StringBuilder sReqData = new StringBuilder();
            BufferedReader reader = request.getReader();
            String line;
            while ((line = reader.readLine()) != null) {
                sReqData.append(line);
            }
			String sMsg = wxcpt.DecryptMsg(sVerifyMsgSig, sVerifyTimeStamp, sVerifyNonce, sReqData.toString());
			log.info("after decrypt msg: {}",sMsg);
			
			// 解析 XML 字符串
	        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
	        DocumentBuilder db = dbf.newDocumentBuilder();
	        StringReader sr = new StringReader(sMsg);
	        InputSource is = new InputSource(sr);
	        Document document = db.parse(is);
	        
	        NodeList nodeList2 = document.getElementsByTagName("ResponseCode");
	        if (nodeList2.getLength() > 0) {
	        	responseCode = nodeList2.item(0).getTextContent();
	        }
	        
	        // 获取 <OptionId> 标签的值
	        NodeList nodeList = document.getElementsByTagName("EventKey");
	        if (nodeList.getLength() > 0) {
	            String optionId = nodeList.item(0).getTextContent();
	            String[] values = optionId.split("_");
	            if(values.length>0) {
	            	ApproverAppChatDto approverAppChatDto = new ApproverAppChatDto();
	            	//根据企微回调值，判读是审批通过还是驳回
	            	approverAppChatDto.setApproverStatus(Integer.parseInt(values[3]));
	            	approverAppChatDto.setQuoteorderId(Integer.parseInt(values[0]));
	            	parentBelongId = values[4];
	            	CurrentUser currentUser = new CurrentUser();
	            	if(!StringUtils.isEmpty(testCurrentName) || Objects.nonNull(testCurrentId)) {
	            		toUserId = testCurrentId;
	            		currentUser.setId(toUserId);
	            		currentUser.setUsername(testCurrentName);
	            	}else {
	            		toUserId = Integer.parseInt(values[1]);
	            		currentUser.setId(toUserId);
	            		currentUser.setUsername(values[2]);
	            	}
	            	//开发环境或者测试开启状态，执行
	            	if("PRO".equals(projectEnv) || "1".equals(isTurnOnTest)) {
	            		try {
	            			quoteFacade.approverAppChat(approverAppChatDto,currentUser);
	            			//更新按钮状态
	    	            	if(!StringUtils.isEmpty(responseCode) && Objects.nonNull(parentBelongId)) {
	    	            		quoteFacade.updateQwMessageButton(responseCode,parentBelongId);
	    	            	}
	            		}catch(ServiceException e) {
	            			log.error("群聊创建失败");
	            		}
	            	}
	            }
	        }
		} catch (Exception e) {
			log.error("解析失败",e);
		}
		//处理失败，大概率代码错误，且建群操作可通过人工手动创建，此处为了避免重复被调用，导致大量报错，无论成功都通知企微不需要再回调了
		return "200";
    }
	

}
