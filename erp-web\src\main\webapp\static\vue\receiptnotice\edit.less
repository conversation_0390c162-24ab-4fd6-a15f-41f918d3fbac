.scrollbar() {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background: #D7DADE;
        width: 6px;
        height: 6px;
        border-radius: 3px;

        &:hover {
            background: #BABFC2;
        }

        &:active {
            background: #969B9E;
        }
    }
}

.setting-form-wrap {
    min-width: 1240px;
    max-width: 1600px;
    margin: 0 auto;
    padding: 20px;
    padding-bottom: 64px;

    .setting-form-block {
        padding: 20px;
        background: #fff;
        margin-bottom: 20px;

        .setting-form-block-title {
            font-weight: 700;
            margin-bottom: 15px;
        }

        .form-item {
            .vd-ui-select {
                width: 100%;
            }

            .form-label {
                width: 150px;
            }
        }

        .top-broad-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;

            &:first-child {
                padding-top: 6px;
            }

            .vd-ui-input {
                width: 60px !important;
                text-align: center;
                margin: 0 10px;
                margin-top: -5px;
            }
        }
    }

    .project-table {
        .table-item {
            display: flex;

            .table-th {
                background: #f5f7fa;
                padding: 7px 10px;
                border: 1px solid #BABFC2;
                margin-top: -1px;
                display: flex;
                align-items: center;
            }

            .table-td {
                flex: 1;
                padding: 7px 10px;
                border: 1px solid #BABFC2;
                margin-left: -1px;
                margin-top: -1px;
                text-align: center;

                &:nth-child(2) {
                    flex: 1.3;
                }

                .vd-ui-input {
                    margin: -3px 0;
                    
                    .vd-ui-input__inner {
                        height: 28px;
                    }
                }
            }
        }
    }

    .target-table-wrap {
        overflow: auto;
        
        .scrollbar;
    }

    .target-table {
        min-width: 1315px;

        .table-tr {
            .table-th, .table-td {
                text-align: center;
                
                &:nth-child(1) {
                    width: 150px;
                }
                
                &:nth-child(2) {
                    width: 765px;
                }
                
                &:nth-child(3) {
                    width: 210px;
                }
                
                &:nth-child(4) {
                    min-width: 180px;
                    flex: 1;
                }
            }
        }
    }

    .depart-base-table {
        .table-th, .table-td {
            text-align: center;
            
            &:nth-child(1) {
                width: 120px;
            }
            
            &:nth-child(2) {
                flex: 1;
            }
            
            &:nth-child(3) {
                flex: 1;
            }
            
            &:nth-child(4) {
                flex: 1;
            }
        }
    }

    .add-wrap {
        display: flex;
        margin-bottom: 10px;

        .add-btn {
            color: #09f;
            cursor: pointer;
            margin-right: 10px;

            &:hover {
                color: #f60;
            }
        }

        .add-btn-tip {
            color: #999;
        }
    }

    .depart-custom-table {
        .table-th, .table-td {
            text-align: center;
            
            &:nth-child(1) {
                width: 220px;
            }
            
            &:nth-child(2) {
                flex: 1;
            }
            
            &:nth-child(3) {
                flex: 1;
            }
            
            &:nth-child(4) {
                width: 100px;
            }

            .link-btn {
                color: #09f;
                cursor: pointer;

                &.warn {
                    color: #e64545;
                }

                &:hover {
                    color: #f60;
                }
            }
        }
    }

    .setting-form-tip {
        color: #999;
        margin-bottom: 10px;
    }
}

.form-footer-wrap {
    text-align: center;
    background: #fff;
    padding: 15px;
    position: fixed;
    width: 100%;
    bottom: 0;
    left: 0;
    z-index: 5;
    border-top: 1px solid #BABFC2;
}

.form-table {
    .table-tr {
        display: flex;

        .table-th, .table-td {
            margin-left: -1px;
            border: 1px solid #BABFC2;
            padding: 7px 10px;

            &:first-child {
                margin-left: 0;
            }
        }

        .table-th {
            background: #f5f7fa;

            .must {
                color: #e64545;
            }
        }

        .table-td {
            margin-top: -1px;

            >.vd-ui-input {
                margin: -2px 0;
                
                .vd-ui-input__inner {
                    height: 28px;
                }
            }

            &.select-wrap {
                padding: 4px 10px;
                
                .vd-ui-select {
                    width: 100%;
                    
                    .vd-ui-input__inner {
                        height: 28px;
                    }

                    .icon {
                        top: 6px;
                    }
                }
            }
        }

    }

    .table-empty {
        border: 1px solid #BABFC2;
        padding: 60px;
        text-align: center;
        margin-top: -1px;
        color: #999;
    }
}

.form-error-wrap {
    color: #e64545;
    margin-top: 5px;
}