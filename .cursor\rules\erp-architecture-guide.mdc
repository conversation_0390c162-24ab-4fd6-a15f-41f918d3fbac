---
description: 
globs: 
alwaysApply: false
---
# ERP 系统架构指南

## 项目概述
这是一个基于 Maven 的多模块 Java ERP 系统，采用分层架构设计。主要包含销售、采购、库存、财务、客户关系管理等业务模块。

## 核心技术栈
- **后端框架**: Spring 4.1.9.RELEASE
- **移动端**: Spring Boot 2.1.5.RELEASE
- **ORM框架**: MyBatis 3.3.1
- **缓存**: Redis (Spring Data Redis 1.6.6)
- **消息队列**: RabbitMQ
- **数据库**: MySQL 5.1.47
- **工具包**: Hutool 5.7.15
- **对象映射**: MapStruct 1.4.2
- **分页**: PageHelper 5.3.0
- **Excel处理**: EasyExcel 3.1.1

## 主配置文件
- 根目录配置: [pom.xml](mdc:pom.xml)
- 项目文档: [README.md](mdc:README.md)

## 模块架构说明

### 公共模块 (erp-common)
位置: `erp-common/`
- **erp-common-core**: 公共工具类核心包
- **erp-common-redis**: 缓存相关封装
- **erp-common-mybatis**: MyBatis 扩展封装
- **erp-common-feign**: Feign 远程调用封装
- **erp-common-statemachine**: 状态机组件
- **erp-common-trace**: 链路追踪
- **erp-common-cat**: CAT 监控集成
- **erp-common-activiti**: 工作流引擎集成

### 核心业务模块

#### 系统管理 (erp-system)
- 用户管理
- 省市区数据
- 部门职位管理
- 字典表管理

#### 交易商管理 (erp-trader)
- 客户管理
- 供应商管理
- 厂家信息
- 贝登商城用户
- 商机管理
- 呼叫中心
- 沟通记录

#### 销售订单 (erp-saleorder)
- 订单管理
- 报价管理
- 授权书
- 销售售后

#### 采购订单 (erp-buyorder)
- 采购管理
- 采购售后

#### 商品管理 (erp-goods)
- 商品信息
- 库存管理
- 商品分类

#### 库存管理 (erp-wms)
- WMS 业务逻辑
- 库存转移
- 出入库记录
- 出库单打印

#### 财务管理 (erp-finance)
- 开票管理
- 账期管理
- 余额管理
- 支付处理
- 财务流水

#### 客户关系管理 (erp-crm)
- 客户关系维护
- 销售机会管理

#### 办公自动化 (erp-oa)
- 办公流程管理

#### 文档管理 (erp-doc)
- 资料库管理

#### 金蝶集成 (erp-kingdee)
- 与金蝶系统的数据同步

### Web 应用模块

#### 主 Web 应用 (erp-web)
- 主要的 Web 界面
- 消息队列处理
- 定时任务管理

#### 移动端 Web (erp-web-mobile)
- 掌上小贝移动端应用

#### CRM Web (erp-web-crm)
- CRM 专用 Web 界面

#### 移动端 API (erp-mobile)
- 移动端 API 服务

### 基础设施模块

#### 基础设施 (erp-infrastructure)
- 第三方服务集成
- OSS 对象存储
- 电子签章
- 短信服务
- 快递接口

#### 遗留系统 (erp-old)
- 原有 ERP 系统代码
- 仅做维护，不做新功能开发

## 模块间依赖原则

1. **依赖倒置**: 模块间通过 API 接口进行交互，避免直接依赖
2. **接口隔离**: 每个模块提供清晰的 API 接口给其他模块调用
3. **单一职责**: 每个模块负责特定的业务领域

## 代码分层结构

### API 模块结构
```
xxx-api/
├── dto/          # 数据传输对象
├── service/      # 外部模块调用接口
└── remoteapi/    # 远程调用服务接口
```

### 业务模块结构
```
xxx-biz/
├── common/       # 公共方法、工具类、常量
├── config/       # 配置文件
├── domain/       # 领域对象
│   ├── entity/   # 实体对象
│   └── dto/      # 业务对象
├── dao/          # 数据访问层
├── manager/      # 管理层（第三方调用、事务封装）
├── service/      # 业务接口层
└── web/          # 视图层
    ├── api/      # 前端接口
    └── controller/ # 路由控制
```

## 开发指导原则

1. **新功能开发**: 在对应的新模块中开发，避免修改 erp-old
2. **模块交互**: 通过 API 接口进行，不直接引用其他模块的实现类
3. **事务管理**: 在 manager 层处理大事务，service 层专注业务逻辑
4. **第三方集成**: 统一在 erp-infrastructure 模块中处理
