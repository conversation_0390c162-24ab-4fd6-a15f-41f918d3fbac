package com.vedeng.erp.system.dto;

import lombok.*;

import java.util.List;

/**
 * @description: 用户
 * <AUTHOR>
 * @date 2022/7/13 9:32
 * @version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDto {

    private Integer userId;

    private Integer companyId;

    private String username;

    private String number;

    private String password;

    private String salt;

    private Integer parentId;

    private Integer isDisabled;

    private String disabledReason;

    private Long lastLoginTime;

    private String lastLoginIp;

    private Long addTime;

    private Integer creator;

    private Long modTime;

    private Integer updater;

    private String ccNumber;

    private Integer isAdmin;

    private Integer positType;

    private Integer orgId;

    /**
     * 一级部门
     */
    private Integer mainOrgId;
    /**
     * 一级部门名称
     */
    private String mainOrgName;

    private List<Integer> positionTypes;

    private List<Integer> orgIds;

    private String orgIdsList;

    private String realName;

    private String mobile;

    private String telephone;

    private String orgName;

    private Integer traderId;

    private Integer sex;

    //ALIAS_HEAD_PICTURE
    private String aliasHeadPicture;//用户头像

    private String email;
    
    /**上级用户ID*/
    private Integer parentUserId;
    
    /**展示的名称*/
    private String displayName;
}
