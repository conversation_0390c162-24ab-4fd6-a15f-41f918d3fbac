void function () {
    new Vue({
        el: '#page',
        data() {
            return {
                dayList: {},
                monthList: {},
                aedMonthList: {},
                customMonthList: {},
                Navs: [
                    '今日 到款榜单', '月度 到款榜单', '月度 AED榜单', '月度 自有品牌榜单',
                ],
                navIndex: 0,
                navFixed: false,
                navScroll: '',
                isloading: true,
                scrolling: false,
            }
        },
        async created() {

        },
        mounted () {
            // this.pageScroll();
            window.addEventListener('scroll', this.pageScroll);
            this.getData();
        },
        methods: {
            getData() {
                this.isloading = true;

                this.$axios.post('/broadcast/statisticData').then(({ data }) => {
                    if(data.success) {
                        let resdata = data.data;

                        this.dayList = this.parseData(resdata.incomeDayDataList || []);
                        this.monthList = this.parseData(resdata.incomeMonthDataList || []);
                        this.aedMonthList = this.parseData(resdata.aedNumDataList || []);
                        this.customMonthList = this.parseData(resdata.vdAmountDataList || []);

                        this.isloading = false;

                        this.$nextTick(() => {
                            console.log(' BScroll  ===>', BScroll);
                            this.initBetterScroll();
                        })
                    } else {
                        this.$message({
                            message: data.message,
                            type: 'warn'
                        })
                    }
                })
            },
            parseData(list) {
                let resData = {
                    personal: {},
                    group: {},
                    department: {}
                };
                list.forEach(item => {
                    let typeEnums = {
                        1: 'personal',
                        2: 'group',
                        3: 'department'
                    };
                    
                    resData[typeEnums[item.type]] = {
                        picUrl: item.picUrl || '',
                        list: item.statisticsLineDataList || []
                    }
                });

                return resData;
            },
            pageScroll () {
                let navEle = this.$refs.nav;
                let scrollTop = window.scrollY;

                if (scrollTop >= navEle.offsetTop) {
                    if (!this.navFixed) {
                        this.navFixed = true; 
                    }
                } else {
                    if (this.navFixed) {
                        this.navFixed = false;
                    }
                }

                if(!this.scrolling) {
                    let _this = this;
                    $('.J-rank-card').each(function(i) {
                        if(scrollTop > $(this).offset().top - 200) {
                            _this.navIndex = i;
                        }

                         // nav横向滚动
                        _this.$nextTick(() => {
                            let moveDistance = $(window).width() * 0.36;
                            _this.navScroll.scrollToElement(document.querySelector('.J-nav-item.active'), 300, -(moveDistance), 0);
                        })
                    })
                }
            },
            scrollTo(index) {
                this.navIndex = index;
                let ele = this.$refs['card' + (index + 1)];
                this.scrolling = true;

                // 页面上下滚动
                $("html, body").animate({
                    scrollTop: ele.offsetTop - 41
                }, 300);
               
                // nav横向滚动
                this.$nextTick(() => {
                    let moveDistance = $(window).width() * 0.36;
                    this.navScroll.scrollToElement(document.querySelector('.J-nav-item.active'), 300, -(moveDistance), 0);
                })

                setTimeout(() => {
                    this.scrolling = false;
                }, 500)
            },
            initBetterScroll() {
                try {
                    console.log('==>');
                    let scrollDom = this.$refs['scroll-dom'];
                    this.navScroll = new BScroll(scrollDom, {
                        scrollY: false, // (默认值:false) 表示延Y轴滚动
                        scrollX: true, // (默认值:true) 表示延X轴滚动
                        click: true, // (默认值:false) 是否派发点击事件；
                        bounceTime: 200,
                        swipeBounceTime: 50
                    })
                } catch (err) {
                    console.log('errr:', err);
                }
            },
        }
    })
}.call(this);